{"name": "sdpbase-spa", "version": "16.5.0", "description": "An excellent product developed by shudongpo<www.sdongpo.com>", "author": "www.sdongpo.com", "private": true, "scripts": {"version": "webpack --version", "dev": "node --max_old_space_size=4096 node_modules/webpack/bin/webpack serve --mode development --inline --progress --profile --config build/webpack.dev.conf.js", "start": "npm run dev", "build": "node --max_old_space_size=4096 build/build.js --openlog=true --mode production --bundle=1", "build-test": "node build/build.js --oss=1", "build-check": "node build/build.js --bundle=1", "build-local:bundle": "node --max_old_space_size=4096 build/build.js --production-mode=local --openlog=true --oss=1 --bundle=1", "build-local": "node --max_old_space_size=4096 build/build.js --production-mode=local --openlog=true --oss=1", "dll": "webpack --config build/webpack.dll.config.js", "postinstall": "patch-package"}, "dependencies": {"@aliyun-sls/web-browser": "^0.3.8", "@sdp/event-bus-v2": "^1.0.2", "@sdp/library-v2": "^2.0.3-0", "@sdp/ui": "0.3.20", "@sdp/xiaodong-sdk": "^0.1.1-alpha.2", "@sdptest/base": "^0.3.43", "@sdptest/webprinter": "^1.1.0", "buffer": "^6.0.3", "countup.js": "^2.8.0", "crypto-js": "^4.2.0", "element-ui": "^2.15.13", "gbk.js": "^0.3.0", "js-md5": "^0.8.3", "odometer_countup": "^1.0.4", "prefetch-preload": "0.0.3", "qiankun": "^2.10.16", "quicklink": "^2.3.0", "view-design": "4.5.0", "vue-color": "^2.8.2", "vue-cropper": "^0.6.5", "vue-pdf": "^4.3.0", "vue-print-nb": "^1.7.5", "vue-virtual-scroll-list": "^2.3.5"}, "devDependencies": {"@babel/plugin-syntax-bigint": "^7.8.3", "@sdp/oss-config": "^1.0.0", "@sentry/webpack-plugin": "^1.15.1", "@webpack-cli/serve": "1.5.2", "autoprefixer": "^7.1.2", "axios": "^0.27.2", "babel-core": "^6.22.1", "babel-eslint": "^10.1.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-import": "^1.13.3", "babel-plugin-lodash": "^3.3.4", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-0": "^6.24.1", "babel-preset-stage-2": "^6.22.0", "cache-loader": "4.1.0", "chalk": "^2.0.1", "code-inspector-plugin": "^0.14.2", "copy-webpack-plugin": "^5.1.2", "cross-env": "^7.0.3", "css-loader": "^0.28.0", "css-minimizer-webpack-plugin": "^3.0.2", "eslint": "^8.57.0", "eslint-cli": "^1.1.1", "eslint-config-airbnb-base": "^14.0.0", "eslint-plugin-import": "^2.20.1", "eslint-plugin-vue": "^6.2.1", "extract-html-plugin": "^1.0.3", "extract-text-webpack-plugin": "3.0.0", "file-loader": "^1.1.4", "flex.css": "^1.1.7", "friendly-errors-webpack-plugin": "^1.6.1", "globs2": "^1.0.4", "html-webpack-plugin": "5.3.2", "husky": "^1.0.0-rc.13", "iview-loader": "^1.3.0", "less": "^2.7.2", "less-loader": "^4.0.4", "lint-staged": "^10.0.8", "lodash-webpack-plugin": "^0.11.6", "mini-css-extract-plugin": "1.3.6", "mockjs": "^1.0.1-beta3", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "normalize.css": "^7.0.0", "ora": "^1.2.0", "os": "^0.1.1", "patch-package": "^6.4.7", "path-browserify": "^1.0.1", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "rimraf": "^2.6.0", "sass-loader": "^6.0.6", "semver": "^5.3.0", "shelljs": "^0.7.6", "simple-progress-webpack-plugin": "^1.1.2", "speed-measure-webpack-plugin": "^1.5.0", "terser-webpack-plugin": "^5.3.1", "thread-loader": "^3.0.4", "unused-webpack-plugin": "^2.4.0", "url-loader": "^0.5.8", "vue-eslint-parser": "^8.3.0", "vue-loader": "14.2.4", "vue-style-loader": "^3.0.1", "vue-svg-loader": "^0.16.0", "vue-template-compiler": "2.6.12", "webpack": "^5.75.0", "webpack-alioss-plugin": "2.4.2", "webpack-bundle-analyzer": "4.4.2", "webpack-cli": "4.8.0", "webpack-dev-server": "3.11.2", "webpack-merge": "^4.1.0", "yargs": "^15.1.0"}, "lint-staged": {"*.{js,vue}": ["eslint", "npx prettier --single-quote --write"]}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"], "main": ".postcssrc.js", "repository": {"type": "git", "url": "git@101.201.72.41:linxiangjun/sdpbase-pro.git"}, "license": "ISC"}