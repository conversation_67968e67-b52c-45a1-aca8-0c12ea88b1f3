<!--
 * @Author: <PERSON>
 * @Date: 2021-08-17 11:24:46
 * @LastEditors: hgj
 * @LastEditTime: 2023-07-26 16:32:22
 * @Description: 由（报表 / 客户毛利）迁移至 报表 / 销售毛利 / 按客户统计
-->
<template>
  <div class="s-common">
    <div class="common-wrap">
      <SFilter
        ref="filter"
        :filter="filterList"
        :advanceFilter="advanceList"
        @on-change="filterChange"
        @on-search="fetchData"
      >
        <template #button>
          <ExportButtonMulti :data="exportBtnMultiData"></ExportButtonMulti>
        </template>
      </SFilter>

      <div class="blk">
        <div class="blk-hd">
          <h5>
            按客户统计列表<span
              >（{{ dateTypeStr() }}：{{ filters.start_date }}至{{
                filters.end_date
              }}）</span
            >
          </h5>
        </div>
        <div class="blk-bd">
          <STable
            ref="table"
            :columns="colsList"
            :filters="filters"
            :beforeFetch="beforeFetch"
            :afterFetch="afterFetch"
            :dataProvider="dataProvider"
            :height="getTableHeight() - 100"
          />
        </div>
      </div>
    </div>
    <!--
      引入iview中现成的自定义指令v-transfer-form，并设置data-transfer="true"使其生效
      transfer-dom 将弹出层渲染至body下
      tabs中含有transform属性，会使子级的fixed定位失效
      渲染至body下，避免弹出层渲染在transform的子级中
    -->
    <s-side-modal
      ref="sideModal"
      maskClose
      width="900"
      title="详情"
      okTxt="导出"
      quitTxt="关闭"
      v-transfer-dom
      :data-transfer="true"
      @ok="exportDetail"
    >
      <RadioGroup
        v-model="detailFilter.stat_type"
        type="button"
        @on-change="_changeDetailFilter"
        style="padding: 20px"
      >
        <Radio :label="1">按商品</Radio>
        <Radio :label="8">按分类</Radio>
      </RadioGroup>
      <STable
        ref="detailTable"
        :show-pagination="false"
        :columns="detailCols"
        :data-provider="detailDataProvider"
      />
    </s-side-modal>
  </div>
</template>

<script>
import AreaSelect from '@components/delivery/areaSelect_new';
import UserGroup from '@components/user/userGroup_new';
import SFilter, { RemoteSelect } from '@components/s-filter';
import UserTypeSelectMultiple from '@components/user/UserTypeSelectMultiple';
import STable from '@components/s-table';
import { SSideModal } from '@components/';
import storeSelectMultiple from '@components/common/storeSelectMultiple';
import user from '@api/user.js';
import TransferDom from 'view-design/src/directives/transfer-dom';
import LineSelect from '@components/delivery/lineSelect_new'; // 线路
import report from '@api/reports.js';
import userTagCheckboxGroup from '@components/userTagCheckboxGroup';
import TimeType from './components/time-type.vue';
import ExportButtonMulti from '@components/common/export-btn-multi';
import { exportLoop } from '@/components/common/export-btn/util.js';
import {
  getYesterDay,
  getWeekStartDate,
  getWeekEndDate,
  getLastWeekStartDate,
  getLastWeekEndDate,
  getMonthStartDate,
  getMonthEndDate,
  getLastMonthEndDate,
  getLastMonthStartDate,
} from '@assets/js/getDate.js';
import ConfigMixin from '@/mixins/config';
import orderTagMixin from './mixin';
import dateUtil from '@/util/date';
import ReportAlert from '../components/ReportAlert.vue';
import nGrossProfit from './../images/n-gross-profit.svg'
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const areaMap = {
  昨日: [getYesterDay(), getYesterDay()],
  本周: [getWeekStartDate(), getWeekEndDate()],
  上周: [getLastWeekStartDate(), getLastWeekEndDate()],
  本月: [getMonthStartDate(), getMonthEndDate()],
  上月: [getLastMonthStartDate(), getLastMonthEndDate()],
};

const defaultArea = '本周';

const radioMap = {
  按每日统计: '15',
  按汇总统计: '2',
};

export default {
  name: 'user-profit',
  mixins: [ConfigMixin, orderTagMixin],
  directives: { TransferDom },
  components: {
    SFilter,
    STable,
    SSideModal,
    ExportButtonMulti,
  },
  data() {
    console.log(11111, this.isOpenCentralPurchasePlatform)
    return {
      exportBtnMultiData: Object.freeze([
        {
          text: '导出客户明细',
          api: this.apiUrl.exportProfitList,
          offline: false,
          paramGetter: () => this.getParams('filter'),
        },
        {
          text: '按记账日期导出实际金额',
          api: this.apiUrl.exportProfitList,
          offline: false,
          offline: false,
          onClick: () => {
            return this.validateDate();
          },
          paramGetter: () => this.getParams('filter', 16),
        },
      ]),
      detailFilter: {
        stat_type: 1,
      },
      detailData: [],
      filters: {},
      exportUserId: '', // 导出时传递的user_id，因为每次搜索完成后，组件的 user_id 会清除，这里用另外一个变量保存
      filterList: [
        {
          label: '日期',
          type: 'RadioGroup',
          key: 'date_area',
          data: [
            { label: '昨日', value: 'lastDay' },
            { label: '本周', value: 'week' },
            { label: '上周', value: 'lastWeek' },
            { label: '本月', value: 'month' },
            { label: '上月', value: 'lastMonth' },
          ],
          defaultValue: defaultArea,
          props: {
            type: 'button',
          },
          onChange: (data) => {
            const date_type = this.$refs.filter.getValue('date_type');
            this.$refs.filter.setValue({
              'start_date,end_date,date_type': [
                areaMap[data][0],
                areaMap[data][1],
                date_type,
              ],
            });
          },
          stop: true,
        },
        {
          type: 'Input',
          label: '搜索',
          key: 'search_value',
          props: {
            placeholder: '输入店铺名称/编码名称',
          },
        },
      ],
      advanceList: [],
      colsList: [
        {
          title: '客户编码',
          key: 'user_code',
          fixed: 'left',
          width: 140,
          render: (h, params) => {
            let data = params.row;
            if (data.user_code === '所有页合计')
              return h('span', data.user_code);
            return h(
              'a',
              {
                class: {
                  tableLink: true,
                },
                on: {
                  click: () => {
                    this.getDetail(data);
                  },
                },
              },
              data.user_code,
            );
          },
        },
        {
          title: '客户名称',
          key: 'name',
          render(h, params) {
            return params.row.name === '所有页合计' ? '所有页合计' : <div>{params.row.profit_rate < 0 ? <img src={nGrossProfit} style={{marginRight: '6px',marginTop:'-2.1px'}} /> : null}{params.row.name}</div>
          }
        },
        {
          title: '所属集团',
          key: 'group_name',
        },
        {
          title: '当前业务员',
          key: 'sale_name',
        },
        {
          title: '发货日期',
          key: 'delivery_date_desc',
          width: 110,
        },
        {
          title: '发货金额',
          key: 'sale_price',
          align: 'right',
          tip: '已发货出库订单中商品的发货金额',
          width: 100,
        },
        {
          title: '发货成本',
          key: 'sale_cost',
          align: 'right',
          tip: '已发货出库订单对应出库单的出库金额',
        },
        {
          title: '退货金额',
          key: 'return_price',
          align: 'right',
        },
        {
          title: '退货成本',
          key: 'return_cost',
          align: 'right',
          tip: '已审核且包含该商品的退货单对应入库单的入库金额',
        },
        {
          title: '实际金额',
          key: 'actual_price',
          align: 'right',
          tip: '发货金额-退货金额',
          width: 120,
        },
        {
          title: '实际成本',
          key: 'actual_cost',
          align: 'right',
          tip: '发货成本-退货成本',
        },
        {
          title: '毛利',
          key: 'profit',
          align: 'right',
          width: 90,
          tip: '实际金额-实际成本',
          render(h, params) {
            const profit = params.row.profit;
            return <div style={{ color: profit < 0 ? 'red' : 'inherit' }}>{profit}</div>
          }
        },
        {
          title: '毛利率',
          key: 'profit_rate',
          width: 100,
          tip: '毛利/实际金额*100%',
          render(h, params) {
            const rate = params.row.profit_rate;
            return <div style={{ color: parseFloat(rate) < 0 ? 'red' : 'inherit' }}>{rate ? `${rate}%` : '--'}</div>
          },
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 120,
          actions: ({ row }) => {
            if (row.user_code === '所有页合计') return [];

            const date_type = this.$refs.filter.getValue('date_type');
            if (+date_type === 3) return [];

            return [
              {
                name: '详情',
                action: (params) => {
                  this.getDetail(params.row);
                },
              },
            ];
          },
        },
      ],
      detailCols: [],
      originDetailCols: [
        {
          title: '发货金额',
          key: 'actual_price',
          align: 'right',
        },
        {
          title: '发货成本',
          key: 'in_price',
          align: 'right',
          tip: '已发货出库订单对应出库单的出库金额',
        },
        {
          title: '退货金额',
          key: 'return_price',
          align: 'right',
        },
        {
          title: '退货成本',
          key: 'return_in_price',
          align: 'right',
          tip: '已审核且包含该商品的退货单对应入库单的入库金额',
        },
        {
          title: '毛利',
          key: 'profit',
          align: 'right',
          tip: '实际金额-实际成本',
          render(h, params) {
            const profit = params.row.profit;
            return <div style={{ color: parseFloat(profit) < 0 ? 'red' : 'inherit' }}>{profit}</div>
          },
        },
        {
          title: '毛利率',
          key: 'profit_rate',
          tip: '毛利/实际金额*100%',
          render(h, params) {
            const rate = params.row.profit_rate;
            return <div style={{ color: parseFloat(rate) < 0 ? 'red' : 'inherit' }}>{rate ? `${rate}%` : '--'}</div>
          },
        },
      ],
      curRow: {},
      totalData: {},
    };
  },
  created() {
    this.initFilter();
  },
  activated() {
    this.fetchData();
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      vm.$smodal({
        title: '客户毛利升级提醒',
        text: '「客户毛利」页面已移动至「销售毛利 / 按客户统计」，本页面将在下期更新时关闭，建议您前往该页面使用',
        type: 'info',
        btns: 2,
        okTxt: '立即前往',
        quitTxt: '取消',
        onOk() {
          vm.$router.push('/reports/profit/sale?statusType=customer');
        },
        onQuit() {},
        mask: true,
      });
    });
  },
  methods: {
    initFilter() {
      console.log(111, this.isOpenCustomerFieldCustomize);
      this.advanceList = [
        {
          type: 'custom',
          component: TimeType,
          defaultValue: [...areaMap[defaultArea], 1],
          props: {
            typeList: [
              {
                label: '记账日期',
                value: 1,
              },
              {
                label: '创建日期',
                value: 3,
              },
            ],
          },
          key: ['start_date', 'end_date', 'date_type'],
          onChange: (data) => {
            const [start_time, end_time] = data;
            let dataString = [start_time, end_time].toString();

            let result = '';
            for (let i in areaMap) {
              if (areaMap[i].toString() === dataString) {
                result = i;
              }
            }
            this.$refs.filter.setValue({ date_area: result });
          },
        },
        {
          type: 'custom',
          component: UserGroup,
          key: 'group_id',
          label: '集团',
        },
        {
          type: 'custom',
          defaultValue: [],
          component: UserTypeSelectMultiple,
          props: {
            filterable: true,
            maxTagCount: 1,
            params: {
              pageSize: 99999,
            },
          },
          key: 'receivable_style_id',
          label: '客户类型',
        },
        {
          type: 'custom',
          component: storeSelectMultiple,
          key: 'store_id',
          label: '库房',
          ctrl: 'isEnableMultiStore',
          onChange: (value) => {
            this.advanceList.find(
              (item) => item.key === 'area_id',
            ).props.storeId = value;
          },
          props: {
            filterable: true,
            'filter-by-label': true,
          },
        },
        {
          label: '区域',
          type: 'custom',
          component: AreaSelect,
          key: 'area_id',
          props: {
            storeId: '',
          },
        },
        {
          type: 'custom',
          label: '线路',
          // asyncComponent: true,
          defaultValue: '',
          component: LineSelect,
          key: 'line_id',
        },
        {
          type: 'custom',
          key: 'sale_id',
          component: RemoteSelect,
          label: '业务员',
          props: {
            placeholder: '请输入业务员',
            optionValue: 'id',
            remote: (query) => user.getSalesList({ key: query }),
            optionLabel: 'name',
          },
        },
        {
          show: this.isOpenCustomerFieldCustomize,
          checked: false,
          type: 'custom',
          label: '客户自定义字段',
          key: 'user_customize_field_select_config_ids',
          defaultValue: [],
          props: {
            formatValue: false,
            customizeType: '14',
            label: '',
          },
          component: CustomizeCascader,
        },
        {
          type: 'RadioGroup',
          defaultValue: '按每日统计',
          data: [
            {
              label: '按每日统计',
              value: 'daily',
            },
            {
              label: '按汇总统计',
              value: 'period',
            },
          ],
          key: 'type',
          label: '统计方式',
        },
        {
          show: true,
          label: '客户标签',
          key: 'user_tag',
          type: 'custom',
          defaultValue: [],
          props: {
            data: [],
            name: 'user_tag',
          },
          style: {
            minWidth: '1800px',
          },
          component: userTagCheckboxGroup,
          onChange: (value) => {
            return { value, stop: true };
          },
        },
      ];
    },
    dateTypeStr() {
      const ref = 'filter';
      const date_type =
        this.$refs[ref] && this.$refs[ref].getValue('date_type');
      return +date_type === 1 ? '记账日期' : '创建日期';
    },
    validateDate() {
      const { start_date, end_date } = this.filters;

      let startDate = new Date(start_date).getTime(),
        endDate = new Date(end_date).getTime();
      let threshold = 1000 * 60 * 60 * 24 * 30;
      if (endDate - startDate > threshold) {
        this.errorNotice({
          title: '操作失败',
          desc: '导出日期汇总仅支持31天',
        });
        return false;
      }
      return true;
    },
    initDetailCols() {
      const stat_type = this.detailFilter.stat_type;
      let detailCols = this.deepClone(this.originDetailCols);
      if (stat_type === 1) {
        detailCols = [
          {
            title: '商品',
            key: 'name',
            fixed: 'left',
            width: 200,
          },
          {
            title: '下单单位',
            key: 'unit',
          },
          {
            title: '发货单位',
            key: 'unit_sell',
          },
          {
            title: '发货数量',
            key: 'actual_amount',
            tip: '已发货出库订单中商品的发货数量',
          },
          ...detailCols,
        ];
      } else if (stat_type === 8) {
        detailCols = [
          {
            title: '商品分类',
            key: 'category1',
            fiexed: 'left',
          },
          ...detailCols,
        ];
      }
      this.detailCols = detailCols;
    },
    getDetail(data) {
      this.initDetailCols();
      this.curRow = data;
      this.$nextTick(() => {
        this.$refs.sideModal.open();
        this.$refs.detailTable.fetchData();
      });
    },
    detailDataProvider() {
      return user.getUserProfitDetail(this.getDetailParams());
    },
    dataProvider(params) {
      return report.getProfitListReport({
        stat_type: radioMap[params.type],
        ...params,
      });
    },
    // dataProvider(params) {
    //   params.type = radioMap[params.type];
    //   user.getUserProfitList({...params, is_total: 1}).then(res => {
    //     this.totalData = res.data
    //   })
    //   return user.getUserProfitList(params);
    // },
    beforeFetch(params) {
      if (!params.store_id) return false;
      params.user_customize_field_select_config_ids = (params.user_customize_field_select_config_ids || []).map(item => item[1]).filter(item => item).join(',');
      return params;
    },
    afterFetch(data, res) {
      data.map((item) => (item.key = item.id + item.delivery_date_desc));
      if (res.status && res.data.list.length) {
        data.push({
          name: '所有页合计',
          ...res.data.sum,
          id: res.data.list.length,
          user_id: res.data.list.length + 'cursom',
        });
      } else {
        if (!res.status) {
          this.$smessage({ type: 'error', text: res.message || '查询失败' });
        }
      }
      // if (data.length) {
      //   data.push({
      //     user_code: '所有页合计',
      //     ...this.totalData,
      //     id: 'user' + data.length
      //   });
      // }
      return data;
    },
    filterChange(filters) {
      this.handleFilters(filters);
      this.filters = filters;
    },
    fetchData() {
      let filters = this.$refs.filter.getValue();
      const searchValueChange =
        filters.search_value !== this.filters.search_value;
      this.filters = filters;
      this.handleFilters(filters);
      if (!searchValueChange) {
        this.$refs.table.fetchData(filters);
      }
    },
    getDetailParams() {
      const { user_id, delivery_date_desc } = this.curRow;
      const { type, start_date, end_date, store_id, date_type } = this.filters;
      let startDate = radioMap[type] === '15' ? delivery_date_desc : start_date;
      let endDate = radioMap[type] === '15' ? delivery_date_desc : end_date;
      return {
        user_id,
        date_type,
        start_date: startDate,
        end_date: endDate,
        store_id,
        ...this.detailFilter,
      };
    },
    offlineExportData(statType) {
      let params = this.getDetailParams();
      if (statType) {
        params.stat_type = statType;
      }
      report.exportProfitList(params).then((res) => {
        const { status, message, data } = res;
        if (status) {
          this.$refs.sideModal.close();
          // this.successMessage(message || '操作成功，导出任务执行中');
          this.$store.commit('showTaskCenter', true);
          exportLoop(data.task_no);
        } else {
          this.errorNotice(message || '导出失败');
        }
      });
    },
    exportDetail() {
      if (this.detailFilter.stat_type == 1) {
        this.offlineExportData();
      } else {
        report.exportProfitList(this.getDetailParams()).then((res) => {
          if (res.status) {
            window.location.href = res.data.file;
            this.$smessage({ type: 'success', text: '导出成功' });
          } else {
            this.$smessage({ type: 'error', text: '导出失败' });
          }
        });
      }
    },
    getParams(ref, stat_type) {
      const filters = this.$refs[ref].getValue();
      return {
        stat_type: stat_type || radioMap[filters.type],
        ...filters,
        user_customize_field_select_config_ids: (filters.user_customize_field_select_config_ids || []).map(item => item[1]).filter(item => item).join(','),
      };
    },
    _changeDetailFilter() {
      this.getDetail(this.curRow);
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .s-filter__item:not(.s-filter__item--fixed) {
  .s-filter__item-wrap {
    height: auto;
  }
}
// /deep/.ivu-radio-group-button .ivu-radio-wrapper{
//   padding: 0 16.3px;
// }
.ivu-radio-group-button .ivu-radio-wrapper {
  height: 30px;
  line-height: 28px;
}
</style>
