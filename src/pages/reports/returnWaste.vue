<template>
  <div class="collect-list list-padding-left list-padding-right alert-text-container">
    <report-alert alterTxt="按退货单的退货日期统计退货入库产生的报损数据，方便企业优化后续流程" />
    <ListTable
      class="pt24"
      ref="listTable"
      :filters="filters"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :data-provider="url"
      :columns="columns"
      :debounceOptions="{ leading: true, trailing: false }"
      :initParams="initParams"
      :beforeRequest="beforeRequest"
      :afterRequest="afterRequest"
      :height="getTableHeight() - 225"
      :outer-border="true"
    >
      <div slot="button">
        <Button @click="exportData">导出</Button>
      </div>
      <div slot="before-table">
        <div class="customRow">
          <div class="statistical-item w_128">
            <b><CountTo class="number-count"  :decimal="2" :end="orderSumList ? Number(orderSumList.total_actual_amount) : 0" /></b>
            <span>
              发货数量
              <Tooltip
                content="退货日期在查询日期内的退货报损出库但关联源订单的发货数量合计"
                placement="top"
              >
                <i class="sui-icon icon-tips" style="cursor: pointer;"></i>
              </Tooltip>
            </span>
          </div>
          <div class="statistical-item w_128">
            <b><CountTo class="number-count"  :decimal="2" :end="orderSumList ? Number(orderSumList.total_loss_num) : 0" /></b>
            <span>
             报损数量
              <Tooltip
                content="退货日期在查询日期内对应的退货报损出库单的出库数量合计"
                placement="top"
              >
                <i class="sui-icon icon-tips" style="cursor: pointer;"></i>
              </Tooltip>
            </span>
          </div>
          <div class="statistical-item w_128">
            <b><CountTo class="number-count"  :decimal="2" :end="orderSumList ? String(orderSumList.loss_rate).replace('%', '') : '0'" />%</b>
            <span>
             报损率
              <Tooltip
                content="报损数量/发货数量*100%"
                placement="top"
              >
                <i class="sui-icon icon-tips" style="cursor: pointer;"></i>
              </Tooltip>
            </span>
          </div>
          <div class="statistical-item w_128">
            <b><CountTo class="number-count" :decimal="2" :end="orderSumList ? Number(orderSumList.total_loss_price) : 0" /></b>
            <span>
             损耗总金额
              <Tooltip
                content="退货日期在查询日期内对应的退货报损出库单的出库金额合计"
                placement="top"
              >
                <i class="sui-icon icon-tips" style="cursor: pointer;"></i>
              </Tooltip>
            </span>
          </div>
        </div>
        <div class="title">
          <h5>
            商品退货列表<span
              >（退货日期：{{ requestParams.start_date }}至{{
                requestParams.end_date
              }}）</span
            >
          </h5>
        </div>
      </div>
    </ListTable>
    <SSideModal
      ref="sideModal"
      maskClose
      width="1000"
      title="按客户统计详情"
      v-transfer-dom
      :data-transfer="true"
      @ok="$refs.sideModal.close()"
    >
    <div style="padding:10px;">
  <div style="display:flex;padding:5px;">
    <div >{{commodity.name}}</div>
    <div style="margin-left:100px;">单位：<span>{{commodity.unit}}</span></div>
  </div>
      <Table 
        ref="detailTable" 
        :columns="detailColumns"
        :border="false"
        :data="tableData"
        :outer-border="true"
         class="detailTable-class"
      >
      </Table>
      </div>
    </SSideModal>
  </div>
</template>
<script>
import SSideModal from '@components/side-modal';
import ListTable from '@/components/list-table/index';
import { areaMap } from './@utils';
import ReportAlert from './components/ReportAlert.vue';
import Button from '@components/button';
// import storeSelectMultiple from '@components/common/storeSelectMultiple';
import storage from '@components/common/storeSelect_new.vue';
import goodsAutoCompleteSelect from '@components/common/goodsAutoCompleteSelect_new.vue';
import ConfigMixin from '@/mixins/config';
import goods from '@api/goods.js';
import report from '@api/reports.js';
import { api } from '@api/api.js';
import InputAutoComplete from '@/components/common/InputAutoComplete';
import common from '@api/order.js';
import CountTo from '@components/count-to';
const defaultArea = '本月';
const PURCHASE_TYPE_LIST = [
  {
    value: '0',
    label: '全部'
  },
  {
    value: '1',
    label: '市场自采',
    children: []
  },
  {
    value: '2',
    label: '供应商直供',
    children: []
  },
  {
    value: '3',
    label: '指定供应商',
    children: []
  },
  {
    value: '5',
    label: '供应商联营',
    children: []
  }
];
export default {
  mixins: [ConfigMixin],
  name: 'depositoryWaste',
  components: {
    CountTo,
    ListTable,
    ReportAlert,
    Button,
    InputAutoComplete,
    SSideModal
  },
  data() {
    return {
      filters:{user_id:''},
      commodity:{},
      tableData:[],
      commodity_id:'',
      initParams: {},
      requestParams: {},
      orderSumList: {
        loss_rate: '0%',
        total: 0,
        total_actual_amount: 0,
        total_loss_num: 0,
        total_loss_price: 0,
      },
      url: report.ReturnLoss,
      filterItems: [
        {
          label: '日期',
          type: 'RadioGroup',
          key: 'date_area',
          data: [
            { label: '昨日' },
            { label: '本周' },
            { label: '上周' },
            { label: '本月' },
            { label: '上月' }
          ],
          defaultValue: defaultArea,
          props: {
            type: 'button'
          },
          onChange: data => {
            this.$refs.listTable.setValue(
              ['start_date', 'end_date'],
              areaMap[data]
            );
            return false;
          },
          stop: true
        },
        {
          type: 'custom',
          component: goodsAutoCompleteSelect,
          key: 'commodity_id',
          label: '搜索',
          props: {
            placeholder: '商品名/商品编码'
          },
          onChange(value) {
            return {
              value
            };
          }
        }
      ],
      advanceItems: [
        {
          label: '日期',
          type: 'DatePicker',
          style: {
            width: '232px'
          },
          props: {
            type: 'daterange',
            clearable: false
          },
          key: ['start_date', 'end_date'],
          defaultValue: areaMap[defaultArea],
          onChange: data => {
            let result = [''];
            for (let i in areaMap) {
              if (areaMap[i].toString() === data.toString()) {
                result = i;
              }
            }
            let startTime = new Date(data[0]).getTime()
            let endTime = new Date(data[1]).getTime()
            let threshold = 1000 * 60 * 93 * 24 * 60;
            if((endTime - startTime) > threshold) {
              this.$smessage({
                type: 'error', text: '最多支持查看93天的数据'
              })
              this.$refs.listTable.setValue(['date_area'], [''], true);
            } else {
              this.$refs.listTable.setValue(['date_area'], result, true);
              return {
                value: data
              };
            }
          }
        },
        {
          type: 'custom',
          component: storage,
          style: {
            width: '232px'
          },
          defaultValue: this.storage.getLocalStorage('init_ware_house_id'),
          show: this.isEnableMultiStore,
          key: 'store_id',
          label: '库房',
          props: {
            placeholder: '全部',
            'default-first': true,
            'show-all': false,
            initNoOnchange: true
          }
        },
        {
          type: 'CategorySelect',
          key: ['category_id1', 'category_id2'],
          label: '分类'
        },
        // {
        //   label: '采购模式',
        //   type: 'Cascader',
        //   key: ['channel_type', 'agent_id'],
        //   props: {
        //     filterable: true,
        //     data: this.isOpenProviderDeliver
        //       ? PURCHASE_TYPE_LIST
        //       : PURCHASE_TYPE_LIST.slice(0, -1),
        //     placeholder: '全部'
        //   }
        // },
        {
            type: 'custom',
            component: InputAutoComplete,
            key: 'user_search',
            label: '客户',
            defaultValue: '',
            stop: true,
            props: {
              placeholder: '输入客户名称/编码搜索',
              dataProvider: common.getUserBySearch,
              valueKey: 'id',
              labelKey: 'email',
              on: {
                resetValue:()=>{
                  this.filters.user_id = ''
                },
                'on-enter': value => {
                  // this.$refs.listTable.setValue(['user_id'],value );
                  this.filters.user_id = value
                  this.$refs.listTable.fetchData()
                },
                'on-focus': () => {
                  // this.$refs.listTable.setValue(['user_id'],'', true );
                  this.filters.user_id = ''
                }
              }
            }
          },
      ],
      detailColumns:[
        {
          title: '客户',
          align: 'left',
          key: 'user_name',
          minWidth: 100,
        },
        {
          title: '客户编码',
          align: 'left',
          key: 'user_code'
        },
        {
          title: '客户类型',
          align: 'left',
          key: 'style_name'
        },
        {
          title: '所属集团',
          align: 'right',
          key: 'group_name'
        },
        {
          title: '发货数量',
          align: 'right',
          key: 'actual_amount'
        },
        {
          title: '损耗数量',
          align: 'right',
          key: 'loss_num'
        },
  
        {
          title: '报损金额',
          align: 'right',
          key: 'loss_price'
        },
        {
          title: '报损金额占比',
          align: 'right',
          minWidth: 80,
          key: 'loss_price_rate'
        }
      ],
      columns:[
        {
          title: '商品名称',
          align: 'left',
          key: 'name',
          width: 180,
          render: (h, params) => {
            const tooltipR = h('Tooltip', {
              props: {
                content: params.row.name,
                placement: 'bottom',
                transfer: true,
              }
            },[
              h('span', {
                style: {
                  "overflow" : "hidden",
                  "text-overflow": "ellipsis",
                  "display": "-webkit-box",
                  "-webkit-line-clamp":2,
                  "-webkit-box-orient": "vertical",
                  "color": "#03ac54",
                  "cursor": "pointer"
                },
                on: {
                  click: () => {
                    // 页面跳转的时候需要清除 Tooltip，以避免将 Tooltip 带到跳转的页面
                    let Tooltip = document.getElementsByClassName('ivu-tooltip-popper');
                    for (let i = 0; i < Tooltip.length; i++) {
                      let p = document.getElementsByClassName('ivu-tooltip-popper')[i];
                      p.style.display = 'none';
                    }
                    this.$router.push({
                      path: '/reports/returnWasteDetail',
                      query: {
                        commodity_id: params.row.commodity_id,
                        store_id: this.requestParams.store_id,
                        start_date: this.requestParams.start_date,
                        end_date: this.requestParams.end_date
                      }
                    });
                  }
                }, 
              }, params.row.name)
            ])
            return tooltipR
          }
        },
        {
          title: '分类',
          align: 'left',
          key: 'category1',
          minWidth: 120,
          render: (h, params) => {
            const { category1, category2 } = params.row;
            return (
              <span>
                {category1}/{category2}
              </span>
            );
          }
        },
        {
          title: '商品编码',
          align: 'left',
          key: 'commodity_code'
        },
        {
          title: '单位',
          align: 'left',
          key: 'unit'
        },
        {
          title: '描述',
          align: 'left',
          key: 'summary'
        },
        {
          title: '发货数量',
          align: 'right',
          key: 'actual_amount'
        },
        {
          title: '报损数量',
          align: 'right',
          key: 'loss_num'
        },
        {
          title: '报损率',
          align: 'right',
          key: 'loss_rate'
        },
        {
          title: '报损金额',
          align: 'right',
          key: 'loss_price'
        },
        {
          title: '报损金额占比',
          align: 'right',
          minWidth: 150,
          key: 'loss_price_rate'
        },
                {
          title: '操作',
          fixed: 'right',
          type: 'action',
          key: 'action',
          width:'90',
          actions: params => {
            let data = params.row;
            let actions = [];
            actions.push({
              name: '详情',
              action: () => {
                this.commodity = data
                this.commodity_id = data.commodity_id;
                this.getDetailList()
                setTimeout(() => {
                this.$refs.sideModal.open();
                })
              }
            })
            return actions;
          },
        }
      ],
    };
  },
  created() {
    this.init();
  },
  mounted() {
    this.getFilterData();
  },
  activated() {
    this.getFilterData();
  },
  deactivated() {
    
  },
  methods: {
    detailBeforeSetData (data) {
      return data;
    },
    getDetailList (query) {
      this.tableData = []
      let params = {
        commodity_id: this.commodity_id
      };
      this.$request.get('/superAdmin/report/ReturnLossGroupByUser', Object.assign({}, this.requestParams, params)).then(res=>{
        let data = []
        if(res.data&&res.data.list){
            data = res.data.list
            data.push(res.data.sum)
        }
        this.tableData = data||[]
      })
    },
    init() {
      this.initQueryParams();
    },
    fetchData() {
      this.$refs.listTable.fetchData();
    },
    afterRequest(res) {
      const status = res.status;
      if (status) {
        const { record } = res.data;
        this.orderSumList = record;
      }
      return res;
    },
    beforeRequest(params) {
      params.user_id = this.filters.user_id
      this.requestParams = params;
      return params;
    },
    // 初始化表格请求参数
    initQueryParams() {
      const storeId = this.storage.getLocalStorage('init_ware_house_id');
      const dateArea = defaultArea;
      const startDate = areaMap[defaultArea][0];
      const endDate = areaMap[defaultArea][1];
      this.initParams = {
        store_id: storeId,
        date_area: dateArea,
        start_date: startDate,
        end_date: endDate,
      };
    },
    // 获取所有采购员/供应商
    getPurchaseTypeArr() {
      goods.getPurchaseType().then(res => {
        let { status, data } = res;
        if (status) {
          PURCHASE_TYPE_LIST[1].children = res.data.agents.map(item => {
            return { value: item.id, label: item.name };
          });

          let providersArr = [];
          let jointProvidersArr = [];
          res.data.providers.forEach(item => {
            providersArr.push({ value: item.id, label: item.name });
            if (+item.provider_type === 2) {
              jointProvidersArr.push({ value: item.id, label: item.name });
            }
          });
          PURCHASE_TYPE_LIST[2].children = PURCHASE_TYPE_LIST[3].children = providersArr;
          PURCHASE_TYPE_LIST[4].children = jointProvidersArr;
        }
      });
    },
    // 加载分类数据
    getGoodsCategory() {
      goods.getGoodsCategory().then(res => {
        if (res.status) {
          this.categoryList = res.data.map(item => {
            return {
              value: item.id,
              label: item.name,
              children: [],
              loading: false
            };
          });
          this.categoryList.unshift({ value: 0, label: '全部分类' });
        }
      });
    },
    // 获取筛选项数据
    getFilterData() {
      this.getPurchaseTypeArr();
      this.getGoodsCategory();
    },
    // 导出
    exportData() {
      let params = this.requestParams;
      params.export = '1';
      window.location.href = this.delGetParams(api.ReturnLoss, params);
    }
  },
};
</script>
<style lang="less" scoped>
.collect-list {
  .marginb24 {
    margin-bottom: 24px;
  }
  /deep/.ivu-radio-group-button .ivu-radio-wrapper {
    height: 30px;
    line-height: 28px;
    padding: 0 10.5px;
  }
  .list-table .before-table {
    height: auto;
  }
  .data-pane-box {
    width: 100%;
    height: 120px;
    display: flex;
    justify-content: space-between;
    .num {
      font-family: AvenirNextCondensed-DemiBold;
      font-size: 32px;
      color: rgba(0, 0, 0, 0.85);
      letter-spacing: 0;
      line-height: 34px;
      position: absolute;
      top: 26.67%;
      .num-unit {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.5);
        letter-spacing: 0;
        line-height: 16px;
        margin-left: -3px;
        position: relative;
        bottom: 1px;
      }
    }
    .label {
      position: absolute;
      top: 61.67%;
      font-family: PingFangSC-Regular;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.7);
      letter-spacing: 0;
      line-height: 16px;
    }
    .purchase {
      height: 100%;
      width: 11.47%;
      position: relative;
      display: flex;
      justify-content: center;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
    }
    .return-goods {
      height: 100%;
      width: 19%;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      .return-border {
        position: absolute;
        left: 56.13%;
        height: 60%;
        width: 1px;
        background: #e8e8e8;
        transform: scaleX(0.5);
      }
      .return-left {
        display: flex;
        width: 56.13%;
        justify-content: center;
        // left: 20.75%;
        left: 0;
      }
      .return-right {
        // left: 71.23%;
        display: flex;
        width: 43%;
        justify-content: center;
        // left: 20.75%;
        right: 0;
      }
      .return-right-label {
        left: 72.64%;
      }
      .num-unit {
        // position: absolute;
        // bottom: 0;
        // right: 0;
        // margin-left: 0;
        // margin-top: 32px;
      }
    }
    .purchase-price {
      height: 100%;
      width: 15.77%;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
      position: relative;
      display: flex;
      justify-content: center;
      .num {
        color: #03ac54;
        letter-spacing: 0;
        line-height: 34px;
      }
    }
    .store-goods {
      height: 100%;
      width: 19%;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      .store-border {
        position: absolute;
        left: 56.13%;
        height: 60%;
        width: 1px;
        background: #e8e8e8;
        transform: scaleX(0.5);
      }
      .store-left {
        // left: 24.06%;
        display: flex;
        width: 56.13%;
        justify-content: center;
        left: 0;
      }
      .store-right {
        // left: 71.23%;
        display: flex;
        width: 43%;
        justify-content: center;
        right: 0;
      }
      .store-right-label {
        left: 72.64%;
      }
    }
    .return-price {
      height: 100%;
      width: 13.8%;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
      display: flex;
      justify-content: center;
      position: relative;
    }
    .total {
      height: 100%;
      width: 15.77%;
      background: #ffffff;
      border: 1px solid #e8e8e8;
      border-radius: 1px;
      display: flex;
      justify-content: center;
      position: relative;
    }
  }
  .table-title-box {
    margin-top: 20px;
    margin-bottom: -4px;
    width: 100%;
    height: 15px;
    position: relative;
    display: flex;
    align-items: center;
    .table-title-box-border {
      height: 11px;
      width: 3px;
      background: rgba(0, 0, 0, 0.7);
      display: inline-block;
    }
    .table-title-box-content {
      display: inline-flex;
      height: 100%;
      margin-left: 6px;
      align-items: center;
      .table-title-box-content-tip {
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 15px;
      }
      .table-title-box-content-time {
        margin-left: 2px;
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.7);
        line-height: 15px;
      }
    }
  }
}
/deep/.ivu-tooltip-inner {
  max-width: inherit;
}
.customRow {
  text-align: center;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: auto;
  margin-top: 20px;
}
.statistical-item {
  height: 120px;
  border-radius: 1px;
  border: 1px solid #e8e8e8;
  text-align: center;
  cursor: default;
  padding: 32px 0 0;
  white-space: nowrap;
  &:not(:first-of-type) {
    margin-left: 12px;
  }
  b {
    font-size: 32px;
    font-family: AvenirNextCondensed-DemiBold, AvenirNextCondensed;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    line-height: 34px;
    display: block;
  }
  span:not(.number-count) {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    line-height: 16px;
    display: block;
    margin-top: 8px;
  }
  .help-tip {
    color: rgba(0, 0, 0, 0.5);
    margin-top: 2px;
  }
}
.w_128 {
  flex: 1;
  flex-basis: 128px;
}

.w_178 {
  flex: 2;
  flex-basis: 178px;
}
.icon-tips {
  font-size: 12px;
  vertical-align: 0px;
}
/deep/.ivu-tooltip-inner {
  max-width: initial;
}
.title {
  margin-top: 20px;
  margin-bottom: -4px;
  h5 {
    height: 15px;
    font-size: 13px;
    color: rgba(0, 0, 0, 0.85);
    display: inline-block;
    span {
      color: rgba(0, 0, 0, 0.7);
      font-weight: 500;
    }
  }
  h5::before {
    content: '';
    display: inline-block;
    width: 3px;
    height: 11px;
    background: rgba(0, 0, 0, 0.7);
    margin-right: 6px;
    margin-bottom: -1px;
  }
}
</style>