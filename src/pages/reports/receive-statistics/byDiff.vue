<template>
  <div>
    <ListTable
      ref="table"
      :border="false"
      :outer-border="true"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :data-provider="apiUrl.getReceivingStatisticsByDiff"
      :columns="columns"
      :height="getTableHeight() - 104 + 54 - 16"
      :before-set-data="afterRequest"
      :show-summary="true"
      :summary-method="() => summaryRow"
    >
      <template #button>
        <Button  @click="exportData()" >
          导出
        </Button>
      </template>
    </ListTable>
  </div>
</template>
<script>
import ListTable from '@components/list-table';
import { exportLoop } from '@components/common/export-btn/util';
import { areaMap } from '../@utils'
import GoodsAutoComplete from "@/components/common/goodsAutoComplete_new.vue";
import common from "@/api/order";
import {RemoteSelect} from "@/components/s-filter";
import { api } from '@api/api.js';
import {
  group,
  store,
  goodsCategory
} from '@/components/standard/sdp-filter-items'

export default {
  name: 'receiveByDiff',
  data () {
    return {
      summaryRow: {},
      filterItems: [
        {
          type: 'RadioGroup',
          key: 'date_area',
          label:'日期',
          data: [
            { label: '昨日', value: 'yesterday' },
            { label: '本周', value: 'week' },
            { label: '上周', value: 'lastWeek' },
            { label: '本月', value: 'month' },
            { label: '上月', value: 'lastMonth' },
          ],
          defaultValue: '本月',
          props: {
            type: 'button'
          },
          onChange: data => {
            this.$refs.table.setValue(
              ['start_date', 'end_date'],
              areaMap[data]
            );
            return false;
          }
        },
        {
          type: 'custom',
          label: '搜索',
          defaultValue:'',
          key: 'commodity_search',
          component: GoodsAutoComplete,
          onChange(value) {
            return {
              value,
              stop: true
            };
          },
        },
      ],
      advanceItems: [
        {
          items: [
            {
              type: 'DatePicker',
              props: {
                type: 'daterange',
                clearable: false,
                options: {
                  disabledDate (date) {
                    // 不能选择今天
                    return  Date.now() - 86400000 <=  date.valueOf() && date.valueOf() <= Date.now();
                  }
                }
              },
              defaultValue: areaMap['本月'],
              key: ['start_date', 'end_date'],
              label: '记账日期',
              onChange: data => {
                // 限制最多90天
                let start_date = new Date(data[0].replace(/-/g, '/')).getTime();
                let end_date = new Date(data[1].replace(/-/g, '/')).getTime();
                let day93 = 1000 * 60 * 60 * 24 * 93;
                if (end_date - start_date > day93) {
                  this.errorNotice({
                    title: '最多选择93天'
                  });
                  data = areaMap['本月'];
                  this.$refs.table.setValue(['start_date', 'end_date'], data);
                }
                let result = [''];
                for (let i in areaMap) {
                  if (areaMap[i].toString() === data.toString()) {
                    result = i;
                  }
                }
                this.$refs.table.setValue(['date_area'], result, true);
                return {
                  value: data
                };
              }
            },
            {
              label: '集团',
              type: 'custom',
              key: 'group_id',
              component: group
            },
            {
              type: 'custom',
              component: store,
              key: 'storage_id',
              label: '仓库',
            },
            {
              type: 'custom',
              key: ['category_id', 'category_id2', 'category_id3'],
              component: goodsCategory,
              label: '商品分类'
            },
            {
              type: 'custom',
              key: 'user_id',
              component: RemoteSelect,
              label: '客户',
              props: {
                placeholder: '输入客户名称/编码搜索',
                optionValue: 'id',
                remote: common.getUserBySearch,
                optionLabel: 'email'
              }
            },
          ]
        }
      ],
      columns: [
        {
          type: 'titleCfg',
          width: 64,
          align: 'center',
          fixed: 'left',
          titleType: 'modify_item_reason_summary'
        },
        {
          title: '收货差异原因',
          key: 'modify_item_reason_text',
          width: 120,
          fixed: 'left'
        },
        {
          title: '下单数量',
          key: 'order_amount',
          width: 120
        },
        {
          title: '下单金额',
          key: 'order_price',
          width: 120
        },
        {
          title: '发货数量',
          key: 'sale_amount',
          width: 120
        },
        {
          title: '发货金额',
          key: 'sale_price',
          width: 120
        },
        {
          title: '实收数量',
          key: 'receive_amount',
          width: 120
        },
        {
          title: '实收金额',
          key: 'receive_price',
          width: 120
        },
        {
          title: '差异数量',
          key: 'diff_amount',
          width: 120
        },
        {
          title: '差异金额',
          key: 'diff_price',
          width: 120
        },
        {
          title: '实收损耗率',
          key: 'receive_loss_rate',
          width: 120
        }
      ],
    }
  },
  components: {
    ListTable,
  },
  created() {
  },
  activated() {
    this.$refs.table.fetchData();
  },
  methods: {
    afterRequest(params, res) {
      const sum = {
        modify_item_reason_text: {key: 'modify_item_reason_text', value: '合计'},
      };
      Object.keys(res.data.sum).forEach(key => {
        sum[key] = {key, value: res.data.sum[key]};
      });
      this.summaryRow = sum;
      return params;
    },
    exportData() {
      let params = this.$refs.table.getParams();
      this.$request.get(api.exportReceiveByReason, params).then(res => {
        if (res.status) {
          exportLoop(res.data.task_no);
          this.$store.commit('showTaskCenter', true);
        } else {
          this.errorNotice(res.message || '导出失败');
        }
      });
    },
  }
}
</script>
<style lang="less" scoped>
.blk-hd h5::before {
  content: '';
  display: inline-block;
  width: 3px;
  height: 11px;
  background: rgba(0, 0, 0, 0.7);
  margin-right: 6px;
  margin-bottom: -1px;
}
/deep/.ivu-radio-group-button .ivu-radio-wrapper {
  padding: 0 10.5px;
}
</style>
