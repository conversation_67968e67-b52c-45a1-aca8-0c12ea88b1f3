<template>
  <div class="s-common alert-text-container">
    <report-alert alterTxt="订单发货出库后统计客户销量，为企业展示客户的购买情况（待审核、待发货、已关闭的不在统计范围内）" />
    <div class="common-wrap">
      <SFilter
        ref="filter"
        :filter="filter"
        :advance-filter="advanceFilter"
        :showSearch="false"
        :filterToggleable="true"
        :exportData="exportData"
        @on-change="filterChange"
        @on-ready="filterChange"
      />
      <SFlexBox class="statistics">
        <SFlexBoxItem
          class="statistics__item"
          :key="index"
          v-for="(item, index) in statistics"
        >
          <div class="statistics__value">
            <em>
              <CountTo :decimal="item.decimal" :end="item.value" />
            </em>
          </div>
          <div class="statistics__label">
            <div class="statistics__label-text">
              <img v-if="item.imgUrl" :src="item.imgUrl" alt="">
              <span>{{ item.label }}</span
              ><s-icon-tip v-if="item.tip" :content="item.tip" :size="12" />
            </div>
            <p class="statistics__label-explain">{{ item.explain }}</p>
          </div>
        </SFlexBoxItem>
      </SFlexBox>
    </div>
    <div class="blk divider" style="background-color: #ffffff;">
      <div class="blk-hd">
        <h5>
          按客户统计前五排行<span
            >（记账时间：{{ filters.start_time }}至{{
              filters.end_time
            }}）</span
          >
        </h5>
      </div>
      <div class="blk-bd">
        <SFlexBox class="charts" :spacing="16">
          <SFlexBoxItem class="charts__item">
            <div class="title">
              <img src="../../../assets/images/reports/chart-title-l.png" alt=""/>
              <span class="ml10 mr10">客户发货金额排行</span>
              <img src="../../../assets/images/reports/chart-title-r.png" alt=""/>
            </div>
            <div class="chart" ref="sale"></div>
          </SFlexBoxItem>
          <SFlexBoxItem class="charts__item">
                        <div class="title">
              <img src="../../../assets/images/reports/chart-title-l.png" alt=""/>
              <span class="ml10 mr10">客户退货金额排行</span>
              <img src="../../../assets/images/reports/chart-title-r.png" alt=""/>
            </div>
            <div class="chart" ref="return"></div>
          </SFlexBoxItem>
        </SFlexBox>
      </div>
    </div>
    <div class="blk divider" style="background-color: #ffffff;">
      <div class="blk-hd">
        <h5>
          按客户统计排行列表<span
            >（记账时间：{{ filters.start_time }}至{{
              filters.end_time
            }}）</span
          >
        </h5>
      </div>
      <div class="blk-bd" style="padding-bottom: 0">
        <STable
          ref="table"
          :columns="columns"
          :filters="filters"
          :beforeFetch="beforeFetch"
          :afterFetch="afterFetch"
          :dataProvider="dataProvider"
          :height="getTableHeight() - 10"
        />
      </div>
    </div>
    <GoodsModal
      @on-close="GoodsModalShow = false"
      :filters="filters"
      :userId="activeUserId"
      :show="GoodsModalShow"
    />
  </div>
</template>

<script>
import AreaSelect from '@components/delivery/areaSelect_new';
import echarts from '@/common/init-echarts.js'
import GoodsModal from './GoodsModal';
import SFilter, { RemoteSelect } from '@components/s-filter';
import STable from '@components/s-table';
import { SIconTip } from '@components';
import { SFlexBox, SFlexBoxItem } from '@components/s-flex-box';
import { api } from '@api/api.js';
import user from '@api/user.js';
import { exportLoop } from '@components/common/export-btn/util';
import ConfigMixin from '@/mixins/config';
import { areaMap } from '../@utils'
import LineSelect from "@components/delivery/lineSelect_new"; // 线路
import ReportAlert from '../components/ReportAlert.vue';
import InputAutoComplete from '@/components/common/InputAutoComplete';
import userTagCheckboxGroup from '@components/userTagCheckboxGroup';
import UserGroupSelect from '@components/common/userGroupSelect';
import CountTo from '@components/count-to';
import StorageUtil from '@util/storage.js';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const defaultArea = '本周';

export default {
  name: 'user-statistics',
  mixins: [ConfigMixin],
  components: {
    SFilter,
    STable,
    SIconTip,
    SFlexBox,
    SFlexBoxItem,
    GoodsModal,
    ReportAlert,
    UserGroupSelect,
    CountTo,
  },
  data() {
    return {
      activeUserId: '',
      GoodsModalShow: false,
      statistics: [
        {
          label: '订单笔数',
          value: '0',
          explain: '',
          imgUrl: require('../../../assets/images/reports/sum/order_num.png'),
          tip: ''
        },
        {
          label: '退单笔数',
          value: '0',
          explain: '',
          imgUrl: require('../../../assets/images/reports/sum/return_order_num.png'),
          tip: ''
        },
        {
          decimal: 2,
          label: '发货金额',
          value: '0.00',
          imgUrl: require('../../../assets/images/reports/sum/goods_money.png'),
          tip: '已发货出库订单的发货金额'
        },
        {
          decimal: 2,
          label: '退货金额',
          value: '0.00',
          imgUrl: require('../../../assets/images/reports/sum/return_goods_money.png'),
          explain: '',
          tip: ''
        },
        {
          decimal: 2,
          label: '实际金额',
          value: '0.00',
          imgUrl: require('../../../assets/images/reports/sum/actual_money.png'),
          tip: '发货金额-退货金额',
          selected: true
        }
      ],
      filters: {},
      filter: [
        {
          label:'日期',
          type: 'RadioGroup',
          key: 'date_area',
          data: [
            { label: '昨日' },
            { label: '本周' },
            { label: '上周' },
            { label: '本月' },
            { label: '上月' }
          ],
          defaultValue: StorageUtil.getLocalStorage('home_redirect_select_date') ? '' : defaultArea,
          props: {
            type: 'button'
          },
          onChange: data => {
            this.$refs.filter.setValue({
              'start_time,end_time': [areaMap[data][0], areaMap[data][1]]
            });
          },
          stop: true
        },
        {
          type: 'DatePicker',
          props: {
            type: 'daterange',
            clearable: false
          },
          defaultValue: StorageUtil.getLocalStorage('home_redirect_select_date') || areaMap[defaultArea],
          key: ['start_time', 'end_time'],
          label: '记账日期',
          tooltip: {
            maxWidth: '200',
            content: '订单取发货日期，退货单取退货日期'
          },
          onChange: data => {
            let result = '';
            for (let i in areaMap) {
              if (areaMap[i].toString() === data.toString()) {
                result = i;
              }
            }
            this.$refs.filter.setValue({ date_area: result });
            if (result) {
              this.$refs.filter.setValue({ date_area: result });
            } else {
              this.$refs.filter.setValue({
              'start_time,end_time': [data[0], data[1]]
            });
            }


          }
        }
      ],
      advanceFilter: [],
      columns: [
        {
          title: '客户名称',
          fixed: 'left',
          key: 'user_name'
        },
        {
          title: '客户编码',
          key: 'user_code'
        },
        {
          title: '业务员',
          key: 'sales_name'
        },
        {
          title: '订单笔数',
          key: 'total',
          tip: '待发货、待收货与已完成的订单数量',
          render: (h, params) => {
            return (
              <span style="color: var(--primary-color);cursor:pointer;" onClick={() => {this.handleJumpToOrderList(params.row, 'total')}}>{ params.row.total }</span>
            )
          }
        },
        {
          title: '退单笔数',
          key: 'return_total',
          tip: '已审核的退货订单数，一张订单多次退货算1次',
          render: (h, params) => {
            return (
              <span style="color: var(--primary-color);cursor:pointer;" onClick={() => {this.handleJumpToOrderList(params.row, 'return_total')}}>{ params.row.return_total }</span>
            )
          }
        },
        {
          title: '发货金额',
          key: 'order_price',
          align: 'right',
          tip: '已发货出库订单的发货金额'
        },
        {
          title: '退货金额',
          key: 'return_price',
          align: 'right'
        },
        {
          title: '实际金额',
          key: 'sub_total',
          align: 'right',
          tip: '实际金额 = 发货金额 - 退货金额'
        },
        {
          title: '发货金额排名',
          key: 'rank',
          tip: '按客户的发货金额进行排序'
        },
        {
          title: '操作',
          type: 'action',
          actions: [
            {
              name: '详情',
              action: params => {
                this.activeUserId = params.row.user_id;
                this.showDetail(params.row);
              }
            },
            {
              name: '导出',
              action: params => {
                this.exportCommoditySaleList(params.row);
              }
            }
          ]
        }
      ],
      chartsDoms: null,
      tableParams: {},
    };
  },
  created() {
    this.initFilter();
    this.showServiceCharge();
  },
  mounted () {
    window.addEventListener('resize', () => {
      Array.isArray(this.chartsDoms)&&
      this.chartsDoms.forEach(item => {
        item.resize()
      })
    })
  },
  activated () {
    // 首页筛选参数
    const dateArr = StorageUtil.getLocalStorage('home_redirect_select_date')
    if (dateArr) {
      this.$refs.table.setValue(
        ['start_time', 'end_time'],
        dateArr,
        true,
      );
    }
    this.$refs.table.fetchData();
  },
  beforeDestroy() {
    Array.isArray(this.chartsDoms)&&
    this.chartsDoms.forEach(item => {
      echarts.dispose(item)
    })
    this.chartsDoms = null;
  },
  methods: {
    initFilter() {
      this.advanceFilter = [
        {
          label: '区域',
          type: 'custom',
          component: AreaSelect,
          key: 'area_id'
        },
        {
          type: "custom",
          label: "线路",
          // asyncComponent: true,
          component: LineSelect,
          defaultValue: '',
          key: "line_id"
        },
        {
          type: 'custom',
          key: 'refer_id',
          component: RemoteSelect,
          label: '业务员',
          props: {
            placeholder: '请输入业务员',
            optionValue: 'id',
            remote: query => user.getSalesList({ key: query }),
            optionLabel: 'name'
          }
        },
        {
          key: 'user_search_value',
          label: '客户',
          type: 'custom',
          defaultValue: '',
          props: {
            placeholder: '输入客户名称/编码搜索',
            dataProvider: this.apiUrl.getUserInfo,
            valueKey: 'id',
            labelKey: 'email',
            queryKey: 'user',
            on: {
              'on-enter': value => {
                this.$refs.filter.setValue({ user_id: value })
              },
            }
          },
          component: InputAutoComplete,
          stop: true,
        },
        {
          checked:false,
          label: '集团',
          type: 'custom',
          key: 'group_id',
          defaultValue: '',
          onChange(value) {
            return {
              value
            };
          },
          props: {
            placeholder: '请选择',
            showAll: false,
            showNo: true,
            clearable: true,
          },
          component: UserGroupSelect
        },
        {
          show: this.isOpenCustomerFieldCustomize,
          checked: false,
          type: 'custom',
          label: '客户自定义字段',
          key: 'user_customize_field_select_config_ids',
          defaultValue: [],
          props: {
            formatValue: false,
            customizeType: '14',
            label: '',
          },
          component: CustomizeCascader,
        },
        {
          show: true,
          label: '客户标签',
          key: 'user_tag',
          type: 'custom',
          defaultValue: [],
          props: {
            data: [],
            name: 'user_tag'
          },
          style: {
            width: '100%',
          },
          component: userTagCheckboxGroup,
          onChange: (value) => {
            return { value, stop: true }
          }
        }
      ];
    },
    handleJumpToOrderList(row, key) {
      const { user_id, user_name } = row
      const {
        start_time,
        end_time,
      } = this.filters;

      const query = {
        user_id,
        user_name,
        start_time,
        end_time
      }

      const routeUrl = this.$router.resolve({
        path: '/reports/sales/order-statistics',
        query,
      });
      window.open(routeUrl.href);
    },
    exportCommoditySaleList (row) {
      let params = {
        start_date: this.tableParams.start_time,
        end_date: this.tableParams.end_time,
        user_id: row.user_id
      }
      this.$request.post(this.apiUrl.exportCommoditySaleList, params).then(res => {
        let data = res.data || {}
        if (res.status) {
          this.successNotice(res.message || '导出提交成功')
          exportLoop(data.task_no);
        } else {
          this.errorNotice(res.message || '导出失败');
        }
      })
    },
    showDetail() {
      this.GoodsModalShow = true;
    },
    createCharts(doms) {
      return doms.map(dom => echarts.init(dom));
    },
    drawCharts(charts, options) {
      charts.forEach((chart, index) => chart.setOption(options[index]));
    },
    dataProvider(params) {
      return this.$request.get(api.getdata, params);
    },
    beforeFetch(params) {
      // if (!params.date_area) {
      //   return;
      // }
      if (params.user_id) params.user_search_value = ''
      params.user_customize_field_select_config_ids = (params.user_customize_field_select_config_ids || []).map(item => item[1]).filter(item => item).join(',');
      // 缓存列表的筛选条件值，导出时要用到
      this.tableParams = this.deepClone(params);
      return params;
    },
    afterFetch(data, res) {
      if (res.status === 1) {
        const {
          user_order_return_sum,
          order_total,
          return_order_total,
          order_toal_price,
          return_order_toal_price,
          sub_total,
          user_sum_pie,
          user_return_sum_pie
        } = res.data;
        data = user_order_return_sum;
        this.statistics[0].value = order_total;
        this.statistics[1].value = return_order_total;
        this.statistics[2].value = order_toal_price;
        this.statistics[3].value = return_order_toal_price;
        this.statistics[4].value = sub_total;
        this.chartsDoms ||
          (this.chartsDoms = this.createCharts([
            this.$refs.sale,
            this.$refs.return
          ]));
        this.drawCharts(this.chartsDoms, [
          {
            title: {
              text: user_sum_pie.seriesData.length
                ? '发货金额\n(前五名)'
                : '暂无数据\n',
              textStyle: {
                color: '#303030',
                fontSize: 14,
                lineHeight: 22,
                fontFamily: 'PingFangSC-Semibold,PingFang SC',
                fontWeight: 500
              },
              left: 'center',
              top: 175
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              left: 'center',
              top: 340,
              icon: 'rect',
              itemWidth: 13,
              itemHeight: 13,
              itemGap: 16,
              data: user_sum_pie.legendData.slice(0, 5),
              formatter: function (name) {
                return echarts.format.truncateText(name, 150, '14px Microsoft Yahei', '…');
              },
              tooltip: {
                 show: true
              }
            },
            series: [
              {
                name: '发货金额',
                type: 'pie',
                bottom: 40,
                radius: ['60', '100'],
                label: {
                  color: 'rgba(0, 0, 0, 0.7)'
                },
                top: 45,
                data: user_sum_pie.seriesData.slice(0, 5),
                color: ['#4752CE', '#4B6CE5', '#00C3FF', '#66C058', '#FFBC00']
              }
            ]
          },
          {
            title: {
              text: user_return_sum_pie.seriesData.length
                ? '退货金额\n(前五名)'
                : '暂无数据\n',
              textStyle: {
                color: '#303030',
                fontSize: 14,
                lineHeight: 22,
                fontFamily: 'PingFangSC-Semibold,PingFang SC',
                fontWeight: '500'
              },
              left: 'center',
              top: 175
            },
            tooltip: {
              trigger: 'item',
              formatter: '{a} <br/>{b}: {c} ({d}%)'
            },
            legend: {
              left: 'center',
              top: 340,
              icon: 'rect',
              itemWidth: 13,
              itemHeight: 13,
              itemGap: 16,
              orient: 'horizontal',
              data: user_return_sum_pie.legendData.slice(0, 5),
              formatter: function (name) {
                return echarts.format.truncateText(name, 150, '14px Microsoft Yahei', '…');
              },
              tooltip: {
                 show: true
              }
            },
            series: [
              {
                name: '退货金额',
                type: 'pie',
                bottom: 40,
                top: 45,
                radius: ['60', '100'],
                label: {
                  color: 'rgba(0, 0, 0, 0.7)'
                },
                data: user_return_sum_pie.seriesData.slice(0, 5),
                color: ['#4752CE', '#4B6CE5', '#00C3FF', '#66C058', '#FFBC00']
              }
            ]
          }
        ]);
      } else {
        this.$smessage({ type: 'error', text: res.message });
      }
      return data;
    },
    filterChange(filters) {
      this.filters = filters;
      console.log('fecth', filters);
    },
    fetchData() {
      this.$refs[this.statusType + 'Table'].fetchData();
    },
    exportData() {
      const params = this.deepClone(this.filters);
      params.user_customize_field_select_config_ids = (params.user_customize_field_select_config_ids || []).map(item => item[1]).join(',');
      this.$request.get(api.exports, params).then(res => {
        if (res.status === 1) {
          window.location.href = res.data;
          this.$smessage({ type: 'success', text: '导出成功' });
        } else {
          this.$smessage({ type: 'error', text: '导出失败' });
        }
      });
    },
    showServiceCharge() {
      if (this.isOpenServiceCharge) {
        let find = this.columns.find(item => item.title === '服务费');
        if (!find) {
          this.columns.splice(7, 0, {
            title: '服务费',
            show: false,
            key: 'service_charge',
            align: 'right',
            tip:
              '在应用中心开启服务费功能后，根据应用配置自动计算的服务费金额'
          });
        }
      }
    }
  },
  watch: {
    sysConfig: {
      handler: function() {
        this.showServiceCharge();
      }
    }
  }
};
</script>

<style lang="less" scoped>
/deep/ .s-table__pagination {
  text-align: right;
  padding: 12px 0 16px;
}
/deep/.ivu-radio-group-button .ivu-radio-wrapper {
  padding: 0 10.5px;
}
.charts__item {
  position: relative;
  .title {
    vertical-align: middle;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 24px;
    color: '#303030';
    font-size: 14px;
    line-height: 22px;
    font-family: 'PingFangSC-Semibold,PingFang SC';
    font-weight: 500;
    img {
      width: 14px;
      height: 8px;
      vertical-align: middle;
    }
  }
}
.divider {
  Padding-top:0 !important;
}
.blk-hd{
	padding-top:0 !important;
}
.blk {
	padding-top: 0 !important;
}
</style>
