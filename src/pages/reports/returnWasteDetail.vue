<template>
    <div v-if="show" class="content-wrap" style="padding-top:8px;">
      <div class="floatRightBtns" style="z-index: 15; right: 20px">
        <Button @click="exportData">导出</Button>
      </div>
      <!--筛选区-->
      <div class="selectRow mb10 flexRow" :class="showAdvance ? 'show-advance' : ''">
        <div class="row_item" style="width: 301px">
          <RadioGroup v-model="dateType" type="button" class="radioGroup" @on-change="changeDate">
            <Radio label="yesterday">昨日</Radio>
            <Radio label="week">本周</Radio>
            <Radio label="lastWeek">上周</Radio>
            <Radio label="month">本月</Radio>
            <Radio label="lastMonth">上月</Radio>
          </RadioGroup>
        </div>
        <div class="row_item">
          <DatePicker
            :transfer="true"
            type="daterange"
            @on-change="selectDate"
            v-model="queryDate"
            placeholder="选择日期" style="width: 100%"></DatePicker>
        </div>
        <div style="width: 250px" class="row_item">
          <store-select
            @input="storeSelectInput"
            :default-first="true"
            :value="storeSelectDedaultValue"
            @on-change="changeStore"></store-select>
        </div>
        <div v-if="false"  class="row_item">
          <Cascader :data="categoryList" change-on-select :load-data="loadSubCategoryData" clearable
                    placeholder="全部分类" @on-change="changeCategory" style="width: 100%"></Cascader>
        </div>
      </div>
      <!--标题-->
      <div style="padding:10px">{{prdInfo.name || ''}} {{prdInfo.commodity_code || ''}} {{prdInfo.unit || ''}} {{prdInfo.summary || ''}}</div>
      <!--图表-->
      <div style="width: 1000px;height:400px;" id="depositoryWasteDetailChart"></div>
      <!--内容区-->
      <div style="padding: 10px">
        <Table :loading="productTableLoading" :height="tableHeight" style="margin-top:50px" :columns="product" :border="true" :data="productList"></Table>
        <Page style="margin-top:10px; margin-bottom:40px" :total="totalPage" :current='currentPage' :page-size='pageSize'
              class="buttomPage"
              @on-change="changePage"
              @on-page-size-change="changePageSize"
              placement="top"
              :page-size-opts="pageSizeOpts"
              show-elevator
              show-total
              show-sizer>
        </Page>
      </div>
      <Row class="fixedBtns">
        <Col span="6" align="left">
          <Button @click="goBack">返 回</Button>&nbsp;&nbsp;
        </Col>
      </Row>
    </div>
</template>
<script>
import AdvanceFilter from '@components/common/advanceFilter'
import storeSelect from '@components/common/storeSelect';
import {api} from '@api/api.js';
import echarts from '@/common/init-echarts.js'
import goods from '@api/goods.js';
import report from '@api/reports.js';
import {
    getYesterDay,
    getWeekStartDate,
    getWeekEndDate,
    getLastWeekStartDate,
    getLastWeekEndDate,
    getMonthStartDate,
    getMonthEndDate,
    getLastMonthEndDate,
    getLastMonthStartDate
  } from '@assets/js/getDate.js';
export const areaMap = {
  yesterday: [getYesterDay(), getYesterDay()],
  week: [getWeekStartDate(), getWeekEndDate()],
  lastWeek: [getLastWeekStartDate(), getLastWeekEndDate()],
  month: [getMonthStartDate(), getMonthEndDate()],
  lastMonth: [getLastMonthStartDate(), getLastMonthEndDate()]
};
export default {
  data () {
    return {
      firstLoading: true,
      show: true,
      prdInfo: {},
      // 筛选数据相关
      showAdvance: false,
      dateType: '', // 日期选择
      queryDate: [getWeekStartDate(), getWeekEndDate()], // 时间控件日期选择
      queryDateCopy: [getWeekStartDate(), getWeekEndDate()],
      categoryList: [], // 分类
      advanceFilterConfig: {},
      providerType: '',
      providerTypeList: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
      ],
      provider: '',
      providerList: [
        { label: '1', value: '1' },
        { label: '2', value: '2' },
      ],
      category1: '',
      category2: '',
      search_key: '',
      store_id: '',
      storeSelectDedaultValue: false,
      // 图表相关
      chartOptions: {
        tooltip: {
            trigger: "axis",          
            backgroundColor: '#fff',  //背景色
            padding: [5, 15, 5, 15],   //边距
            borderColor: '#DDDDDF',  //边框颜色
            borderWidth: 1,    //边框线宽度
            textStyle: {     //文字样式
              color: '#6A6A6A',
              decoration: 'none',
              fontFamily: 'Verdana, sans-serif',
            },
            extraCssText:'text-align: left;',  //文字对齐方式
            formatter: function (params) {   //格式化函数
              return `${params[0].axisValue}</br>报损金额：${params[0].data}`
            },
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          data: [],
          type: 'line'
        }]
      },
      // 表格相关
      productTableLoading: false,
      tableHeight: this.getTableHeight() -220,
      product: [
        { title: '日期', key: 'return_date', align: 'left' },
        { title: '报损单号', key: 'no', align: 'left', minWidth: 110, render:(h, params) => {
          let data = params.row;
            return h('a', {
              class: {
                depositoryWasteatag: true
              },
              on: {
                click: () => {
                  let params = this.getParams();
                  this.$router.push({
                    path: '/storeRoom/reportLPDetail',
                    query: {
                      id: data.loss_id || '',
                    }
                  });
                }
              }
            }, data.no)
        } },
        { title: '退货单号', key: 'return_no', align: 'left', minWidth: 110, render:(h, params) => {
          let data = params.row;
            return h('a', {
              class: {
                depositoryWasteatag: true
              },
              on: {
                click: () => {
                  let params = this.getParams();
                  this.$router.push({
                    path: '/returnDetail',
                    query: {
                      id: data.return_id  || '',
                      isDetail: 'true'
                    }
                  });
                }
              }
            }, data.return_no)
        }  },
        { title: '客户', key: 'user_name', align: 'left' },
        { title: '单价', key: 'in_price', align: 'right' },
        { title: '报损数量', key: 'loss_num', align: 'right' },
        { title: '报损金额', key: 'loss_price', align: 'right' },
        { title: '商品备注', key: 'remark', align: 'left' }
      ],
      productList: [
      ],
      // 分页相关
      pageSizeOpts: [20, 30, 50, 100],
      currentPage: 1,
      pageSize: 20,
      totalPage: 0,
    }
  },
  components: {
    AdvanceFilter,
    storeSelect
  },
  mounted () {
    // 加载分类数据
    goods.getGoodsCategory().then((res) => {
      if (res.status) {
        this.categoryList = res.data.map((item) => {
          return {
            value: item.id,
            label: item.name,
            children: [],
            loading: false
          }
        });
        this.categoryList.unshift({'value': 0, 'label': '全部分类'});
      }
    });
  },
  activated () {
    // 用 v-if 的方法当前路由页面刷新
    this.show = false;
    this.firstLoading = true;
    setTimeout(() => {
      this.show = true;
    })
  },
  methods: {
    exportData () {
      let params = this.getParams();
      params.export = '1'
      window.location.href = this.delGetParams(api.ReturnLossDetail, params);
    },
    getList () {
      let params = this.getParams();
      if (!params || this.productTableLoading) {
        return;        
      }
      params.export = '0'
      this.productTableLoading = true;
      report.ReturnLossDetail(params).then((res) => {
        if (res.status) {
          let data = res.data || {};
          this.productList = data.list || [];
          this.chartOptions.xAxis.data = [];
          this.chartOptions.series[0].data = [];
          if (data.pageParams) {
            this.totalPage = +data.pageParams.count;
            this.pageSize = +data.pageParams.page_size;
          }
          // 处理表格数据
          if ((data.chart || {}).data) {
            data.chart.data.map(item => {
              this.chartOptions.xAxis.data.push(item.date || '');
              this.chartOptions.series[0].data.push(item.loss_price || '');
            })
          }
          this.prdInfo = (data.chart || {}).commodity || {};
          this.initCharts();
        } else {
          this.$Modal.error({
            title: '错误',
            content: res.message
          });
        }
        this.productTableLoading = false;
      });

    },
    getParams () {
      let commodity_id = this.$route.query.commodity_id;
      if (!commodity_id) {
        this.$Modal.error({
          title: '错误',
          content: '页面参数错误'
        });
        return;
      }
      let params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        start_date: this.queryDate[0],
        end_date: this.queryDate[1],
        store_id: this.store_id,
        // category_id1: this.category1,
        // category_id2: this.category2,
        commodity_id: commodity_id
      }
      // 如果日期是 Date 对象的实例，说明日期格式需要转换
      if (params.start_date instanceof Date) {
        params.start_date = this.formatDate(params.start_date);
      }
      if (params.end_date instanceof Date) {
        params.end_date = this.formatDate(params.end_date);
      }
      return params;
    },
    // 筛选列表更改时间
    changeDate (val) {
      switch (val) {
        case 'yesterday':
          this.queryDate = [getYesterDay(), getYesterDay()];
          break;
        case 'week':
          this.queryDate = [getWeekStartDate(), getWeekEndDate()];
          break;
        case 'lastWeek':
          this.queryDate = [getLastWeekStartDate(), getLastWeekEndDate()];
          break;
        case 'month':
          this.queryDate = [getMonthStartDate(), getMonthEndDate()];
          break;
        case 'lastMonth':
          this.queryDate = [getLastMonthStartDate(), getLastMonthEndDate()];
          break;
      }
      this.getList();
    },
    // 时间控件选择日期 
    selectDate () {
      let startDate = new Date(this.queryDate[0]).getTime(),
          endDate = new Date(this.queryDate[1]).getTime();

      for (const key in areaMap) {
        if (areaMap[key][0] === this.formatDate(startDate) && areaMap[key][1] === this.formatDate(endDate)) {
          this.dateType = key
          console.log('this.dateType', this.dateType)
        } else {
          this.dateType = ''
        }
      }
      // 选择时间间隔超过60天不支持导出
      let threshold = 1000 * 60 * 60 * 24 * 59;
      if ((endDate - startDate) > threshold) {
        this.errorNotice({
          title: '操作失败',
          desc: '最多支持查看60天的数据'
        })
        this.queryDate = this.queryDateCopy;
        return;
      } else {
        this.queryDateCopy = this.queryDate;
      }
      this.getList();
    },
    // 更改仓库
    changeStore () {

    },
    storeSelectInput (target) {
      this.store_id = target || '';
      // 初次载入时带入上个页面的筛选数据
      if (this.firstLoading) {
        let store_id = this.$route.query.store_id,
            start_date = this.$route.query.start_date,
            end_date = this.$route.query.end_date;
        if (store_id) {
          this.store_id = store_id;
          this.storeSelectDedaultValue = store_id;
        }
        for (const key in areaMap) {
          if (areaMap[key][0] === start_date && areaMap[key][1] === end_date) {
            this.dateType = key
            console.log('this.dateType', this.dateType)
          }
        }
        if (start_date && end_date) {
          start_date = start_date.replace('-', '/');
          end_date = end_date.replace('-', '/');
          this.queryDate = [
            new Date(start_date),
            new Date(end_date)
          ];
        }
        this.firstLoading = false;
      }
      this.getList();
    },
    // 加载分类数据
    loadSubCategoryData (item, callback) {
      item.loading = true;
      goods.getGoodsCategory(item.value).then((res) => {
        if (res.status) {
          item.children = res.data.map((_item) => {
            return {
              value: _item.id,
              label: _item.name,
            }
          });
          item.loading = false;
          callback();
        }
      });
    },
    // 更改分类数据
    changeCategory () {
      this.category1 = value[0];
      this.category2 = value[1] ? value[1] : '';
      this.getList();
    },
    // 分页
    changePage (pageNo) {
      this.currentPage = pageNo;
      this.getList();
    },
    changePageSize (size) {
      this.pageSize = size;
      this.getList();
    },
    // 绘制图表
    initCharts () {
      this.chart = echarts.init(document.getElementById('depositoryWasteDetailChart'));
      this.chart.setOption(this.chartOptions);
    }

    },
}
</script>
<style lang="scss" scoped>
  .show-advance {
    margin-bottom: 0px !important;
  }
  .radioGroup {
    width: 298px;
  }
  .advanced-search-btn {
    cursor: pointer;
    position: absolute;
    display: inline-block;
    width: 115px;
    text-align: center;
    height: 45px;
    line-height: 35px;
    top: 0;
    &.active {
      background: #f3f5f2;
    }
  }
  .ivu-table-row-hover {
    color: black !important;
  }
</style>
<style>
  .depositoryWasteatag {
    color: #03ac54 !important;
  }
  .depositoryWasteatag:hover {
    color: #03ac54 !important;
    text-decoration: underline;
  }
</style>