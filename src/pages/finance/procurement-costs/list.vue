<template>
  <div class="common purchase__list">
    <ListTable
      :filter-items="filterItems"
      data-provider="/superAdmin/PurchaseExpense/list"
      :border="false"
      :outer-border="true"
      :advance="true"
      :advance-items="advanceItems"
      :max-line="2"
      row-key="refer_no"
      :columns="columns"
      ref="table"
      :afterRequest="afterRequest"
      :show-summary="Object.keys(summaryRow).length !== 0"
      :summary-method="() => summaryRow"
    >
      <ExportButton
        slot="button"
        text="导 出"
        type="default"
        offline
        :api="apiUrl.purchaseExpenseExportList"
        :param-getter="getParams"
      ></ExportButton>
      <div
        slot="before-table"
        style="margin-bottom: 10px"
        class="order__list__operation"
      >
        <SButton styleType="btnStyleForAdd" @click="news">新增</SButton>
      </div>
    </ListTable>

    <prePayModal v-model="showDetail" :id="detailId"></prePayModal>
  </div>
</template>
<script>
import prePayModal from './components/prePayModal.vue';
import ListTable from '@components/list-table';
import SButton from '@components/button';
import { get } from '@/api/request';
import { api } from '@api/api.js';
import ConfigMixin from '@/mixins/config';
import DateUtil from '@/util/date.js';

export default {
  name: 'procurementCosts',
  mixins: [ConfigMixin],
  components: {
    ListTable,
    SButton,
    prePayModal,
  },
  data() {
    let DefaultPayDate = [this.getCurrentLastMonth(), this.getToday()];

    return {
      detailId: '',
      showDetail: false,
      advanceItems: [
        {
          items: [
            {
              type: 'Select',
              label: '单据状态',
              props: {
                placeholder: '单据状态',
                clearable: true,
              },
              key: 'audit_status',
              data: [
                {
                  value: '0',
                  label: '全部状态',
                },
                {
                  value: '1',
                  label: '待审核',
                },
                {
                  value: '2',
                  label: '已审核',
                },
                {
                  value: '3',
                  label: '已作废',
                },
              ],
            },
            {
              type: 'Select',
              key: 'settle_status',
              label: '结算状态',
              props: {
                placeholder: '结算状态',
                clearable: true,
              },
              data: [
                {
                  value: '',
                  label: '全部结算状态',
                },
                {
                  value: '1',
                  label: '未结算',
                },
                {
                  value: '2',
                  label: '已结算',
                },
                {
                  value: '3',
                  label: '部分结算',
                },
              ],
            },
            {
              type: 'Cascader',
              label: '采购员/供应商',
              key: ['purchase_type', 'purchase_person'],
              props: {
                placeholder: '请选择',
                data: [],
                clearable: true,
                filterable: true,
              },
            },
            {
              type: 'Select',
              key: 'fee_type',
              label: '单据类型',
              props: {
                placeholder: '单据类型',
                clearable: true,
              },
              data: [
                {
                  value: '',
                  label: '全部单据类型',
                },
                {
                  value: '1',
                  label: '应收/应付',
                },
                {
                  value: '2',
                  label: '预付账款',
                },
                {
                  value: '3',
                  label: '期初账款',
                },
              ],
            },
            {
              type: 'Select',
              key: 'bill_mode',
              label: '对账模式',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '不可对账',
                },
                {
                  value: '2',
                  label: '可对账',
                },
              ],
            },
            {
              type: 'DatePicker',
              label: '付款日期',
              key: ['start_pay_date', 'end_pay_date'],
              defaultValue: DefaultPayDate,
              props: {
                type: 'daterange',
                clearable: false,
              },
              onChange: (value) => {
                if (DateUtil.diffDay(value[0], value[1]) > 365) {
                  this.errorMessage('付款日期筛选不能超过一年');
                  this.$refs.table.setValue(
                    ['start_pay_date', 'end_pay_date'],
                    DefaultPayDate,
                  );
                  return { DefaultPayDate, stop: true };
                } else {
                  DefaultPayDate = value;
                }
                return { value };
              },
            },
            {
              label: '单据备注',
              type: 'Input',
              key: 'remark',
              props: {
                placeholder: '请输入',
                clearable: true,
              },
            },
          ],
        },
      ],
      filterItems: [
        {
          type: 'DatePicker',
          label: '创建日期',
          key: ['start_create_time', 'end_create_time'],
          defaultValue: [this.getCurrentLastMonth(), this.getToday()],
          props: {
            type: 'daterange',
          },
        },
        {
          label: '搜索',
          type: 'Input',
          key: 'search_value',
          props: {
            placeholder: '请输入费用单号',
            clearable: true,
          },
        },
      ],
      columns: [],
      originCols: [
        {
          title: '费用单号',
          key: 'fee_no',
          className: 'medium-items',
          width: 180,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h(
                'span',
                {
                  class: {
                    'hover--primary': true,
                  },
                  style: {
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.toOrderDetail(data);
                    },
                  },
                },
                data.fee_no,
              ),
              h(
                'div',
                {
                  style: {
                    color: '#999999',
                  },
                },
                data.create_time,
              ),
            ]);
          },
        },
        {
          title: '采购员/供应商',
          key: 'purchase_person',
          width: 100,
        },
        {
          title: '制单人',
          key: 'create_user',
          width: 120,
        },
        {
          title: '单据类型',
          key: 'fee_type_desc',
          width: 120,
        },
        {
          title: '对账模式',
          key: 'bill_mode_desc',
          width: 100,
        },
        {
          title: '单据金额',
          key: 'fee_price',
          width: 100,
        },
        {
          title: '单据状态',
          key: 'audit_status_desc',
          width: 100,
        },
        {
          title: '结算金额',
          key: 'settle_price',
          width: 100,
          render: (h, params) => {
            let data = params.row;
            if (data.is_can_display_pre_paid_list == 1) {
              return h(
                'a',
                {
                  class: 'tableLink',
                  on: {
                    click: () => {
                      this.lookDetail(data);
                    },
                  },
                },
                data.settle_price,
              );
            } else {
              return h('div', null, data.settle_price);
            }
          },
        },
        {
          title: '结算状态',
          key: 'settle_status_desc',
          width: 100,
        },
        {
          title: '备注',
          key: 'remark',
          width: 100,
        },
        {
          resizable: false,
          title: '操作',
          key: 'action',
          fixed: 'right',
          type: 'action',
          width: 210,
          actionCountLimit: 3,
          actions: (params) => {
            let { row } = params;
            let actions = [];
            if (row.is_can_edit == 1) {
              actions.push({
                name: '编辑',
                action: (params) => {
                  this.toEdit(params.row.id);
                },
              });
            }
            if (row.is_can_discard == 1) {
              actions.push({
                name: '作废',
                confirm: () => '确定作废？',
                action: (params) => {
                  this.invalid(params.row.id);
                },
              });
            }
            if (row.is_can_delete == 1) {
              actions.push({
                name: '删除',
                confirm: () => '确定删除？',
                action: (params) => {
                  this.delete(params.row.id);
                },
              });
            }
            return actions;
          },
        },
      ],
      summaryRow: {},
    };
  },
  watch: {
    $route: {
      handler() {
        // this.$refs.table.fetchData();
      },
    },
  },
  created() {
    this.getSearchConfig();
    this.columns = this.originCols;
  },
  methods: {
    getParams() {
      const params = this.$refs.table.getParams();
      return params;
    },
    lookDetail(item) {
      this.detailId = item.id;
      this.showDetail = true;
    },
    setAdvanceItemData(key, data) {
      this.advanceItems.forEach((row) => {
        row.items.forEach((col) => {
          if (col.key[0] === key) {
            if (key == 'purchase_type') {
              col.props.data = data;
            } else {
              col.data = data;
            }
          }
        });
      });
    },
    // 获取供应商 采购员
    async getSearchConfig() {
      let { data, status } = await get(this.apiUrl.batchSummary.searchConfig);
      if (status == 1) {
        let agents = data.agents.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
        let providers = data.providers.map((item) => {
          return {
            value: item.id,
            label: item.name,
            provider_type: item.provider_type,
          };
        });
        let purchaseType = [
          {
            value: '0',
            label: '全部分类',
          },
          {
            value: '1',
            label: '采购员',
            children: [...agents],
          },
          {
            value: '2',
            label: '供应商',
            children: [...providers],
          },
        ];
        // if (this.sysConfig.provider_supplier_pool_switch == 1) {
        //   purchaseType.push({
        //     value: '5',
        //     label: '联营供应商',
        //     children: providers.filter((item) => item.provider_type == 2),
        //   });
        // }
        this.setAdvanceItemData('purchase_type', purchaseType);
      }
    },
    toOrderDetail(val) {
      this.$router.push({ path: '/finance/details', query: { id: val.id } });
    },
    async delete(id) {
      let { status, message } = await get(api.purchaseExpenseDel, { id: id });
      if (status == 1) {
        this.$Notice.success({
          title: '操作成功',
        });
        this.$refs.table.fetchData();
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    async invalid(id) {
      let { status, message } = await get(api.purchaseExpenseDiscard, {
        id: id,
      });
      if (status == 1) {
        this.$Notice.success({
          title: '操作成功',
        });
        this.$refs.table.fetchData();
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    news() {
      this.$router.push({ path: '/finance/newProcurementCosts' });
    },
    toEdit(id) {
      this.$router.push({
        path: '/finance/editProcurementCosts',
        query: { id: id },
      });
    },
    afterRequest(res) {
      let summaryRow = {};
      const total = res.data.total;
      if (total) {
        Object.keys(total).forEach((key) => {
          summaryRow[key] = {
            key,
            value: total[key],
          };
        });
        const key = 'fee_no';
        summaryRow[key] = {
          key,
          value: '合计',
        };
      }
      this.summaryRow = summaryRow;
      return res;
    },
  },
};
</script>

<style lang="less" scoped>
.common {
  .freight {
    .ivu-input-wrapper {
      padding: 10px 0 5px 0;
    }
    .err-txt {
      font-size: 12px;
      height: 18px;
      color: #f13130;
    }
  }
  .more-btn {
    .sui-icon {
      font-size: 12px;
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover .sui-icon {
      transform: rotate(180deg);
    }
  }
}
</style>
