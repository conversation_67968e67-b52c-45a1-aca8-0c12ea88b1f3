<template>
  <div class="content-wrap basePadding">
    <div id="GoodsList">
      <Row
        v-show="selectionShow"
        :gutter="30"
        style="padding: 15px 0"
        type="flex"
      >
        <Col>
          <span style="margin-right= 5px;">发货日期</span>
          <DatePicker
            type="daterange"
            placeholder="请选择发货日期"
            style="width: 200px"
            :value="timedata"
            @on-change="DeliveryDateChange"
          ></DatePicker>
        </Col>
        <Col>
          <Select
            @on-change="OrderStatusChange"
            placeholder="订单状态"
            v-model="OrderStatus"
            style="width: 100px"
          >
            <Option
              v-for="item in OrderStatusData"
              :value="item.value"
              :key="item.value"
              >{{ item.label }}</Option
            >
          </Select>
        </Col>
        
        <Col>
          <Input
            v-model="SearchValue"
            placeholder="请输入客户名称 / 订单号"
            style="width: 300px"
          >
            <Button slot="append" icon="ios-search" @click="Searchs"> </Button>
          </Input>
        </Col>
      </Row>
      <Row v-show="selectionShow">
        <Col style="margin-top: -8px;margin-bottom: 10px;display: flex;" v-if="orderTagArr">
          <span style="flex-shrink: 0;padding-top: 6px;">订单标签：</span>
          <orderTag 
            @on-change="handleTagSelect"
            :isVisibleArea="false"
            :emitArray="true"
            :checkboxItems="orderTagArr"
          >
          </orderTag>
        </Col>
      </Row>
      <Row type="flex" v-show="selectionButton">
        <Col span="8"
          ><span style="line-height: 45px"
            >已选择{{ selectedOrderCount }}个订单</span
          ></Col
        >
        <Col span="8" offset="8" class="flex-con">
          <Button @click="selectAllSameUser" style="margin: 15px 0"
            >全选相同客户单据</Button
          >
          <Button
            type="primary"
            style="margin: 15px 20px"
            @click="batchMakeInvoice"
            >批量开具发票</Button
          >
        </Col>
      </Row>
      <Row>
        <Col>
          <div class="position--relative">
            <div class="table-check-all">
              <template v-if="isAllSameUser">
                <div class="tooltip" v-show="checkAll">
                  <div class="tooltip__arrow"></div>
                  <div class="tooltip__content">
                    已选择{{ allPage ? '全部' : '当前' }}页内容，
                    <span
                      @click="toggleAllPage"
                      style="color: #03ac54; cursor: pointer"
                      >点击勾选{{ allPage ? '当前' : '全部' }}页内容</span
                    >
                  </div>
                </div>
                <Checkbox @on-change="toggleCheckAll" v-model="checkAll" />
              </template>
            </div>
            <Table
              border
              ref="list"
              @on-selection-change="selectUser"
              :height="tableHeight"
              :columns="columns"
              :data="InvoiceListData"
            ></Table>
            <Page
              style="width: 100%"
              :total="page.count ? page.count : 0"
              :current.sync="nowPage"
              :page-size="page.pageSize ? page.pageSize : 10"
              class="buttomPage"
              @on-change="getCurrentPage"
              @on-page-size-change="modifyPage"
              placement="top"
              show-elevator
              show-total
              show-sizer
              :pageSizeOpts="[10, 20, 40, 100, 200]"
            >
            </Page>
          </div>
        </Col>
      </Row>
    </div>
    <Modal
      v-model="unitInformation"
      @on-visible-change="visibleChange"
      :mask-closable="false"
      :title="purchaser"
    >
      <Form
        label-position="right"
        :model="Information"
        v-if="unitInformation"
        ref="formInformations"
        :rules="formInformation"
        :label-width="100"
      >
        <FormItem label="名称：" prop="name">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.name"
            placeholder="请输入发票抬头信息（个人或公司名称），必填"
          ></Input>
        </FormItem>
        <FormItem label="税号：" prop="dutyParagraph">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.dutyParagraph"
            placeholder="15~ 20位(企业报销或开专票必填，个人客户可不填)"
          ></Input>
        </FormItem>
        <FormItem label="单位地址：" prop="site">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.site"
            placeholder="公司地址(企业报销或开专票必填)"
          ></Input>
        </FormItem>
        <FormItem label="电话号码：" prop="number">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.number"
            placeholder="公司电话(企业报销或开专票必填)"
          ></Input>
        </FormItem>
        <FormItem label="开户银行：" prop="bank">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.bank"
            placeholder="开户银行(企业报销或开专票必填)"
          ></Input>
        </FormItem>
        <FormItem label="银行账户：" prop="account">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.account"
            placeholder="企业开户行账户(企业报销或开专票必填)"
          ></Input>
        </FormItem>
        <FormItem label="邮箱地址：" prop="email">
          <Input
            clearable
            style="width: 300px"
            v-model="Information.email"
            placeholder="接收电子发票的邮箱"
          ></Input>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="unitInformation = false">取消</Button>
        <Button @click="Modify" type="primary">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { api } from '@api/api.js';
import { get, post } from '@/api/request';
import dateUtil from '@/util/date';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

export default {
  components: { orderTag },
  computed: {
    isAllSameUser() {
      if (!this.InvoiceListData || this.InvoiceListData.length === 0) {
        return false;
      }
      return this.InvoiceListData.every(
        (order) => order.user_id === this.InvoiceListData[0].user_id,
      );
    },
    selectedOrderCount() {
      if (this.checkAll && this.allPage) {
        return this.page.count;
      }
      return this.selectedOrders.length;
    },
  },
  data() {
    return {
      orderTagArr: [],
      selectedOrderTag: '',
      checkAll: false,
      allPage: false,
      selectionShow: true,
      unitInformation: false,
      purchaser: '',
      companyGetInvoiceDatas: [],
      selectionButton: false,
      timedata: [],
      InvoiceListData: [],
      tableHeight: this.getTableHeight() - 10,
      SearchValue: '', // 模糊搜索
      startTime: '',
      selectedOrders: '',
      CreateInvoice: '',
      invoiceData: '',
      invoiceDataUser_id: '',
      endTime: '',
      jumps: false,
      JumpInvoice: false,
      Information: {
        name: '',
        dutyParagraph: '',
        site: '',
        number: '',
        bank: '',
        account: '',
        email: '',
      },
      page: {
        count: 0,
        pageSize: 10,
      },
      nowPage: 1,
      OrderStatus: '',
      user_id: '',
      invoiceInformation: false,
      userInvoiceInformation: false,
      currentPage: '1',
      formInformation: {
        name: [
          { required: true, message: '请输入正确的名称', trigger: 'blur' },
        ],
        dutyParagraph: [
          {
            required: true,
            min: 15,
            max: 20,
            message: '请输入正确的税号，15~20位数字、字母',
            trigger: 'blur',
          },
        ],
        site: [
          {
            required: true,
            max: 128,
            message: '请输入正确的单位名称，128个字以内',
            trigger: 'change',
          },
        ],
        number: [
          {
            required: true,
            min: 1,
            max: 20,
            message: '请输入正确的电话号码，1~20位数字',
            trigger: 'change',
          },
        ],
        bank: [
          {
            required: true,
            max: 128,
            message: '请输入正确的开户银行，128个字以内',
            trigger: 'change',
          },
        ],
        account: [
          {
            required: true,
            max: 128,
            message: '请输入正确的银行账户，128个数字以内',
            trigger: 'change',
          },
        ],
        email: [
          {
            type: 'email',
            message: '请输入正确的电子邮箱地址',
            trigger: 'change',
          },
        ],
      },
      OrderStatusData: [
        {
          value: 1,
          label: '全部状态',
        },
        {
          value: 400,
          label: '已发货',
        },
        {
          value: 600,
          label: '已完成',
        },
      ],
      columns: [
        {
          type: 'selection',
          width: 60,
          align: 'center',
          // eslint-disable-next-line no-unused-vars
          renderHeader: (h, params) => {
            return h('span', 'touxuan');
          },
        },
        {
          title: '订单编号',
          key: 'order_no',
          render: (h, params) => {
            let data = params.row;
            return h('span', [
              h(
                'p',
                // {
                //   style: {
                //     display: 'inline-block'
                //   }
                // },
                data.order_no,
              ),
              h(
                'p',
                // {
                //   style: {
                //     display: 'inline-block'
                //   }
                // },
                data.delivery_date,
              ),
            ]);
          },
        },
        {
          title: '订单状态',
          key: 'mode',
          render: (h, params) => {
            let data = params.row.mode;
            return h('span', data == 600 ? '已完成' : '已发货');
          },
        },
        {
          title: '客户',
          key: 'user_name',
        },
        {
          title: '实际金额',
          key: 'actual_price',
        },
        {
          title: '销售实收',
          key: 'sale_price',
        },
        {
          title: '退货实收',
          key: 'return_price',
        },
        {
          title: '发货日期',
          key: 'delivery_date',
        },
        {
          title: '业务订单备注',
          key: 'remark',
        },
        {
          resizable: false,
          title: '操作',
          key: 'action',
          width: 1,
          render: (h, params) => {
            // eslint-disable-next-line no-unused-vars
            let data = params.row;
            return h(
              'ul',
              {
                class: {
                  'table-row-btn-wrap': true,
                },
              },
              [
                h(
                  'span',
                  {
                    class: {
                      dn: false,
                    },
                    on: {
                      click: () => {
                        this.MakeInvoice(params.row.order_no); // 创建发票单据
                        this.getuserInvoice(
                          params.row.user_id,
                          params.row.order_no,
                        ); // 获取用户发票配置信息
                        this.invoiceDataUser_id = params.row.user_id;
                        // this.getInvoice(params.row.user_id); // 获取公司配置信息
                      },
                    },
                  },
                  '开票',
                ),
              ],
            );
          },
        },
      ],
    };
  },
  created() {
    this.subTract(7);
    this.init(); // 列表初始化
    this.getSourceList();
    this.OrderStatus = 1;
  },
  methods: {
    handleTagSelect(tag) {
      this.selectedOrderTag = tag.join(',') || ''
      this.init(); 
    },
    getSourceList() {
      this.$request.get(this.apiUrl.orderListSearchConfig).then((res) => {
        let { status, data } = res;
        if (status) {
          this.orderTagArr = data.order_tag || [];
        }
      });
    },
    visibleChange() {
      this.$nextTick(() => {
        this.$refs.formInformations &&
          this.$refs.formInformations.resetFields();
      });
    },
    Modify() {
      this.$refs.formInformations.validate((valid) => {
        if (!valid) {
          this.$Notice.error({
            title: '请填写完整再提交',
          });
        } else {
          if (this.purchaser == '用户信息编辑') {
            let param = {
              user_id: this.invoiceDataUser_id ? this.invoiceDataUser_id : '',
              invoice_title: this.Information.name ? this.Information.name : '',
              tax_no: this.Information.dutyParagraph
                ? this.Information.dutyParagraph
                : '',
              bank: this.Information.bank ? this.Information.bank : '',
              account: this.Information.account ? this.Information.account : '',
              address: this.Information.site ? this.Information.site : '',
              tel: this.Information.number ? this.Information.number : '',
              email: this.Information.email ? this.Information.email : '',
            };
            this.$request
              .post(api.invoice.userSuperInvoiceUpdate, param)
              .then((res) => {
                // eslint-disable-next-line no-unused-vars
                let { data, status, message } = res;
                if (status == 1) {
                  this.$Notice.success({
                    title: '操作成功！',
                  });
                  this.unitInformation = false;
                  this.subTract(7);
                  this.selectedOrders = [];
                  this.selectionShow = true;
                  this.selectionButton = false;
                  this.init();
                } else {
                  this.$Notice.error({
                    title: message,
                  });
                  this.subTract(7);
                  this.unitInformation = false;
                  this.selectionShow = true;
                  this.selectionButton = false;
                  this.init();
                }
              });
          } else {
            let param = {
              invoice_title: this.Information.name ? this.Information.name : '',
              tax_no: this.Information.dutyParagraph
                ? this.Information.dutyParagraph
                : '',
              bank: this.Information.bank ? this.Information.bank : '',
              account: this.Information.account ? this.Information.account : '',
              address: this.Information.site ? this.Information.site : '',
              tel: this.Information.number ? this.Information.number : '',
              email: this.Information.email ? this.Information.email : '',
            };
            this.$request.post(api.invoice.invoiceUpdate, param).then((res) => {
              // eslint-disable-next-line no-unused-vars
              let { data, status, message } = res;
              if (status == 1) {
                this.$Notice.success({
                  title: '操作成功！',
                });
                this.unitInformation = false;
                this.subTract(7);
                this.selectionShow = true;
                this.selectionButton = false;
                this.init();
              } else {
                this.$Notice.error({
                  title: message,
                });
                this.subTract(7);
                this.unitInformation = false;
                this.selectionShow = true;
                this.selectionButton = false;
                this.init();
              }
            });
          }
        }
      });
    },
    salesInformation() {
      this.formInformation.dutyParagraph[0].required = true;
      this.formInformation.site[0].required = true;
      this.formInformation.number[0].required = true;
      this.formInformation.bank[0].required = true;
      this.formInformation.account[0].required = true;
      this.purchaser = '企业信息编辑';
      this.unitInformation = true;
      this.Information.name = this.invoiceData.sale_title;
      this.Information.dutyParagraph = this.invoiceData.sale_tax_no;
      this.Information.site = this.invoiceData.sale_address;
      this.Information.number = this.invoiceData.sale_tel;
      this.Information.bank = this.invoiceData.sale_bank;
      this.Information.account = this.invoiceData.sale_account;
      this.Information.email = this.invoiceData.email;
    },
    EditBuyerInformation() {
      this.formInformation.dutyParagraph[0].required = false;
      this.formInformation.site[0].required = false;
      this.formInformation.number[0].required = false;
      this.formInformation.bank[0].required = false;
      this.formInformation.account[0].required = false;
      this.unitInformation = true;
      this.purchaser = '用户信息编辑';
      this.Information.name = this.companyGetInvoiceDatas.invoice_title;
      this.Information.dutyParagraph = this.companyGetInvoiceDatas.tax_no;
      this.Information.site = this.companyGetInvoiceDatas.address;
      this.Information.number = this.companyGetInvoiceDatas.tel;
      this.Information.bank = this.companyGetInvoiceDatas.bank;
      this.Information.account = this.companyGetInvoiceDatas.account;
      this.Information.email = this.companyGetInvoiceDatas.email;
    },
    toggleAllPage() {
      this.allPage = !this.allPage;
    },
    toggleCheckAll(res) {
      if (res == true) {
        this.selectionShow = false;
        this.selectionButton = true;
      } else {
        this.selectionShow = true;
        this.selectionButton = false;
        this.allPage = false;
      }
      this.InvoiceListData = this.InvoiceListData.map((order) => {
        order._checked = this.checkAll;
        return order;
      });
      this.selectedOrders = this.checkAll ? this.InvoiceListData : [];
    },
    selectAllSameUser() {
      if (!this.selectedOrders || this.selectedOrders.length === 0) {
        this.modalError({
          content: '请先选择一个单据!',
        });
        return false;
      }
      let SelectedOrder = this.selectedOrders[0];
      let user_id = SelectedOrder.user_id * 1;
      let list = this.InvoiceListData.map((iem) => {
        if (iem.user_id * 1 === user_id && !iem._disabled) {
          iem._checked = true;
        }
        return iem;
      });
      this.InvoiceListData = list;
      let arr = [];
      this.InvoiceListData.forEach((iten) => {
        if (iten._checked == true) {
          arr.push(iten);
        }
      });
      this.selectedOrders = arr;
    },
    modifyPage(size) {
      this.page.pageSize = size;
      this.init();
    },
    getCurrentPage(page) {
      this.nowPage = page;
      this.init();
    },
    batchMakeInvoice() {
      let user_id = this.selectedOrders[0].user_id;
      this.invoiceDataUser_id = user_id;
      this.getuserInvoice(user_id);
      if (
        !this.allPage &&
        (!this.selectedOrders || this.selectedOrders.length === 0)
      ) {
        this.errorNotice('请选择要开票的订单');
        return false;
      }
      let params = {};
      if (!this.allPage) {
        params.order_no = this.selectedOrders
          .map((order) => order.order_no)
          .join(',');
      } else {
        // current = this.selectedOrders.slice(0, this.page.pageSize);
        // params.order_no = this.InvoiceListData.map(order => order.order_no).join(',');
        // 同步全部的时候,传筛选条件
        params = this.getFilterParams();
      }
      this.$request
        .post(this.apiUrl.invoice.createInvoice, params)
        .then((res) => {
          let { status, data } = res;
          if (status) {
            // this.successNotice('批量开票成功');
            this.$router.push({
              path: '/finance/DetailsInvoice/order',
              query: { uerid: data.invoice_no },
            });
          } else {
            this.errorNotice({
              title: '开票失败',
              desc: res.message,
            });
          }
        });
    },
    fillInformation(status, message) {
      this.$Notice.warning({
        title: message,
        render: (h) => {
          return h('span', [
            h(
              'span',
              {
                style: {
                  display: 'inline-block',
                  margin: '5px 0',
                  'font-size': '14px',
                },
              },
              status == 100003
                ? '请在商品税率规则模块为该客户指定税率规则后使用'
                : '需要填写开票抬头才能开票',
            ),
            h(
              'span',
              {
                style: {
                  display: 'inline-block',
                  color: '#f0d32d',
                  'text-decoration': 'underline',
                  margin: '5px 0',
                  'font-size': '14px',
                  cursor: 'pointer',
                },
                on: {
                  click: () => {
                    if (status == 100003) {
                      this.$router.push({
                        path: '/taxRate/rules/list',
                      });
                    } else if (status == 100001) {
                      this.salesInformation();
                    } else {
                      this.EditBuyerInformation();
                    }
                  },
                },
              },
              status == 100003 ? '前往为客户设置税率规则' : '点击填写>>',
            ),
          ]);
        },
      });
    },
    async MakeInvoice(datas) {
      let param = {
        order_no: datas,
      };
      let res = await post(api.invoice.createInvoice, param);
      let { status, data, errCode, message } = res;
      if (status == 1) {
        this.CreateInvoice = data.invoice_no;
        this.$router.push({
          path: '/finance/DetailsInvoice/order',
          query: { uerid: this.CreateInvoice },
        });
      } else {
        // this.$Notice.error({
        //   title: res.message
        // });
        this.fillInformation(errCode, message);
      }
    },
    Searchs() {
      this.nowPage = 1;
      this.init();
    },
    OrderStatusChange(res) {
      this.nowPage = 1;
      this.OrderStatus = res;
      this.init();
    },
    // eslint-disable-next-line no-unused-vars
    async getuserInvoice(datas, order) {
      let param = {
        user_id: datas,
      };
      let { data } = await get(api.invoice.userSuperGetInvoice, param);
      this.companyGetInvoiceDatas = data;
      // 获取公司信息
      let companyGetInvoiceDatas = await get(api.invoice.companyGetInvoice);
      this.invoiceData = companyGetInvoiceDatas.data;
      // if (status == 1) {
      // if (
      //   data.invoice_title == '' ||
      //   data.tax_no == '' ||
      //   data == false ||
      //   data == []
      // ) {
      // this.userInvoiceInformation = true;
      //   this.$Notice.info({
      //     title: '客户开票信息未填写',
      //     render: h => {
      //       return h('span', [
      //         h(
      //           'span',
      //           {
      //             style: {
      //               display: 'inline-block',
      //               margin: '5px 0',
      //               'font-size': '14px'
      //             }
      //           },
      //           '需要填写开票抬头、税号才能开票'
      //         ),
      //         h(
      //           'span',
      //           {
      //             style: {
      //               display: 'inline-block',
      //               color: 'blue',
      //               'text-decoration': 'underline',
      //               margin: '5px 0',
      //               'font-size': '14px',
      //               cursor: 'pointer'
      //             },
      //             on: {
      //               click: () => {
      //                 this.EditBuyerInformation();
      //               }
      //             }
      //           },
      //           '点击填写>>'
      //         )
      //       ]);
      //     }
      //   });
      // }

      // if (datas.status == 1) {
      //   if (
      //     datas.data.invoice_title == '' ||
      //     datas.data.tax_no == '' ||
      //     datas.data == false ||
      //     datas.data == []
      //   ) {
      //     this.invoiceInformation = true;
      //     this.$Notice.info({
      //       title: '企业开票信息未填写',
      //       render: h => {
      //         return h('span', [
      //           h(
      //             'span',
      //             {
      //               style: {
      //                 display: 'inline-block',
      //                 margin: '5px 0',
      //                 'font-size': '14px'
      //               }
      //             },
      //             '需要填写开票抬头、税号才能开票'
      //           ),
      //           h(
      //             'span',
      //             {
      //               style: {
      //                 display: 'inline-block',
      //                 color: 'blue',
      //                 'text-decoration': 'underline',
      //                 margin: '5px 0',
      //                 'font-size': '14px',
      //                 cursor: 'pointer'
      //               },
      //               on: {
      //                 click: () => {
      //                   this.salesInformation();
      //                 }
      //               }
      //             },
      //             '点击填写>>'
      //           )
      //         ]);
      //       }
      //     });
      //   }
      // }
      // if (datas.status == 1) {
      //   if (
      //     datas.data.invoice_title == '' ||
      //     datas.data.tax_no == '' ||
      //     datas.data == false ||
      //     datas.data == []
      //   ) {
      //     this.invoiceInformation = true;
      //     this.$Notice.info({
      //       title: '该客户未指定税率规则',
      //       render: h => {
      //         return h('span', [
      //           h(
      //             'span',
      //             {
      //               style: {
      //                 display: 'inline-block',
      //                 margin: '5px 0',
      //                 'font-size': '14px'
      //               }
      //             },
      //             '请在‘商品税率规则’模块为该客户指定税率规则后使用'
      //           ),
      //           h(
      //             'span',
      //             {
      //               style: {
      //                 display: 'inline-block',
      //                 color: 'blue',
      //                 'text-decoration': 'underline',
      //                 margin: '5px 0',
      //                 'font-size': '14px',
      //                 cursor: 'pointer'
      //               },
      //               on: {
      //                 click: () => {
      //                   this.$router.push({
      //                     path: '/taxRate/rules/list',
      //                   });
      //                 }
      //               }
      //             },
      //             '前往为客户设置税率规则'
      //           )
      //         ]);
      //       }
      //     });
      //   }
      // }
      // }
    },
    // eslint-disable-next-line no-unused-vars
    cancelSelectUser(row) {
      this.InvoiceListData.forEach((item) => {
        // eslint-disable-next-line no-undef
        if (item.user_id !== selection[0].user_id) {
          item._disabled = false;
        }
      });
      if (this.selectionShow == true) {
        this.InvoiceListData.forEach((item) => {
          item._disabled = false;
        });
      }
    },
    // 表格单选
    selectUser(selection) {
      this.selectedOrders = selection;
      if (selection.length > 0) {
        this.selectionShow = false;
        this.selectionButton = true;
      } else {
        this.selectionShow = true;
        this.selectionButton = false;
      }
      let allFlag = true;
      this.InvoiceListData = this.InvoiceListData.map((order) => {
        if (selection && selection.length > 0) {
          order._checked = selection.some(
            (selectOrder) => selectOrder.order_id === order.order_id,
          );
          if (!order._checked) allFlag = false;
          order._disabled = order.user_id !== selection[0].user_id;
        } else {
          allFlag = false;
          order._checked = false;
          order._disabled = false;
        }
        return order;
      });
      this.checkAll = allFlag;
    },
    subTract(days) {
      // 设置 最近7日期区间
      this.startTime = dateUtil.getBeforeDate(days);
      this.endTime = dateUtil.getTodayDate();
      this.timedata = [this.startTime, this.endTime];
    },
    getFilterParams() {
      return {
        start_delivery_date: this.startTime,
        end_delivery_date: this.endTime,
        mode: this.OrderStatus,
        search_value: this.SearchValue,
        order_tag: this.selectedOrderTag,
        page: this.nowPage,
        pageSize: this.page.pageSize ? this.page.pageSize : 10,
      };
    },
    async init() {
      let param = this.getFilterParams();
      if (param.mode == 1) {
        param.mode = '';
      }
      let res = await get(api.invoice.invoiceOrderList, param);
      let { status, data } = res;
      // eslint-disable-next-line no-unused-vars
      let arr = [];
      // eslint-disable-next-line no-unused-vars
      let list = [];
      if (status == 1) {
        this.checkAll = false;
        this.selectedOrders = [];
        this.allPage = false;
        this.selectionButton = false;
        this.selectionShow = true;
        let lists = data.list.map((item) => {
          item._disabled = false;
          item._checked = false;
          return item;
        });
        this.page = data.pageParams ? data.pageParams : '';
        this.page.count = parseInt(this.page.count);
        this.page.pageSize = parseInt(this.page.page_size);
        this.InvoiceListData = lists;
      } else if (status == 0) {
        this.$Notice.error({
          title: res.message,
        });
      }
    },
    // 日期 change
    DeliveryDateChange(res) {
      this.nowPage = 1;
      this.startTime = res[0] ? res[0] : '';
      this.endTime = res[1] ? res[1] : '';
      this.init();
    },
  },
};
</script>

<style lang="less" scoped>
.postion--relative {
  position: relative;
}
.tooltip {
  width: 260px;
  height: 25px;
  line-height: 25px;
  border: #fff;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  position: absolute;
  top: -16px;
  left: 8px;
  .tooltip__content {
    position: relative;
    z-index: 2;
    background: #fff;
    padding: 0 10px;
  }
  .tooltip__arrow {
    z-index: 1;
    position: absolute;
    bottom: -5px;
    width: 10px;
    height: 10px;
    transform: rotate(45deg);
    box-shadow: 1px 1px 4px #ccc;
    background: #fff;
    left: 20px;
  }
}
.table-check-all {
  position: absolute;
  z-index: 2;
  left: 0;
  top: 0;
  width: 65px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #f3f5f3;
}
#GoodsList {
  position: relative;
}
.buttomPage {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
