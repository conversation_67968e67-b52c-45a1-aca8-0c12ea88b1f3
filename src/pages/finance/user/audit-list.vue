<!--
 * @Descripttion: 财务/客户结算
 * @url: http://0.0.0.0:8089/#/finance/user/audit-list
 * @Author: lizhiwei 🙂
 * @Date: 2021-02-07 16:01:48
 * @LastEditors: hgj
 * @LastEditTime: 2023-09-05 15:00:02
-->

<template>
  <div class="common purchase__list">
    <ListTable
      tableId="audit_list_01"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :initParams="initParams"
      @on-selection-change="handleSelectionChange"
      :customSelect="true"
      :extra-selection-count="extraSelectionCount"
      :before-request="beforeRequest"
      :after-request="afterRequest"
      :before-set-data="beforeSetData"
      :advance="true"
      :advance-items="advanceItems"
      :filters="filter"
      :filter-items="filterItems"
      :data-provider="dataProvider"
      :border="false"
      :outer-border="true"
      :max-line="2"
      row-key="refer_no"
      :columns="columns"
      :height="getTableHeight() - 77"
      ref="table"
      :defaultPageSize="25"
      :pageSizeOpts="[25, 40, 50, 100, 200]"
      :keepScroll="true"
      @reset-change="handleResetChange"
      :isOpenCustom="true"
    >
      <Dropdown @on-click="handleExport" slot="button">
        <Button class="more-btn" type="default"
          >导出<span class="sui-icon icon-arrow-down"></span
        ></Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="exportType.all">对账单详情</DropdownItem>
          <DropdownItem :name="exportType.group">按集团导出</DropdownItem>
          <DropdownItem :name="exportType.user">按客户导出</DropdownItem>
          <DropdownItem :name="exportType.detail">对账明细导出</DropdownItem>
          <DropdownItem :name="exportType.user_detail"
            >按客户导出明细</DropdownItem
          >
        </DropdownMenu>
      </Dropdown>

      <div
        slot="before-table"
        class="common__operation"
        v-show="selectedOrders.length === 0"
      >
        <Button
          class="mr10"
          :loading="tableExpanded === undefined"
          @click="toggleExpand"
        >
          {{
            tableExpanded !== undefined
              ? tableExpanded
                ? '收起退货单'
                : '展开退货单'
              : ''
          }}
        </Button>
        <Tooltip
          placement="top"
          max-width="300"
          content="一键把当前筛选条件下所有客户对账单进行批量结算操作"
        >
          <Button @click="openBatchSettleModal">
            一键结算
            <i
              class="sui-icon icon-tips"
              style="color: inherit; font-size: 13px; vertical-align: baseline"
            ></i>
          </Button>
        </Tooltip>
        <Tooltip
          class="fr"
          :max-width="200"
          placement="left"
          :disabled="tooltipDis"
        >
          <div slot="content">
            <span>新版：按记账日期对账，退货金额按退货单退货日期记录</span
            ><br /><span
              >旧版：按发货日期对账，退货金额按发货单发货日期记录</span
            >
          </div>
          <div @mouseenter="showTooltip">
            <Button @click="jumpToAuditListNew">切换新版</Button>
          </div>
        </Tooltip>
      </div>
      <div slot="batch-operation" class="aic">
        <span class="mr10" style="margin-left: -25px; font-weight: 500"
          >（应收总金额:
          <span style="color: #ff6e00">{{ selectedShouldPrice }}</span
          >）
        </span>
        <Button class="mr10" @click="selectAllSameUser"
          >全选相同客户单据</Button
        >
        <Poptip
          class="mr10"
          title="将选中的订单的对账状态标记为已对账，同时将“待收货”状态订单变更为“已完成”"
          confirm
          placement="bottom"
          :width="240"
          @on-ok="$_onBatchMarkAsReconciled"
        >
          <Button>批量标记为已对账</Button>
        </Poptip>
        <Button class="mr10" type="primary" @click="batchPayment"
          >批量结算</Button
        >
        <Button
          class="mr10"
          @click="batchLockModal = true"
          v-if="isOpenCustomerBillLock"
          >批量锁定</Button
        >
        <ButtonGroup>
          <Button
            class="mr10"
            type="default"
            v-show="false"
            id="batchPrinting"
            :class="{
              'batchPrinting-ORDER': isUerAuditPrintUseOrderTemplate
                ? true
                : false,
              'batchPrinting-SOA': isUerAuditPrintUseOrderTemplate
                ? false
                : true,
            }"
            @click="_prints(true)"
            >批量打印</Button
          >
          <Button class="mr10" type="default" @click="_prints(true)"
            >批量打印</Button
          >
        </ButtonGroup>
        <Button
          class="mr10"
          v-if="isOpenAccountBillOrderPrint"
          @click="printOrderList"
          >打印订单记录</Button
        >
        <Button class="mr10" @click="printCategorySummaryPrint"
          >打印商品分类汇总</Button
        >
        <Button
          class="mr10"
          @click="_printsData(1, undefined, undefined, true)"
          :disabled="exportLoading"
          v-if="export_excel_via_print_template"
          >{{ exportLoading ? '导出中...' : '按打印模板导出' }}</Button
        >
        <Tooltip
          v-if="export_excel_via_print_template"
          max-width="300"
          placement="top"
          transfer
          content="按打印模板导出仅应用新版打印模版,请配置新版打印模板!"
        >
          <i class="ml8 sui-icon icon-tips"></i>
        </Tooltip>
      </div>
    </ListTable>
    <PrintTemplateExportChoose
      :type="printType"
      :filterOld="false"
      showPreview
      title="选择打印模板"
      ref="printTemplateBatchChoose"
    ></PrintTemplateExportChoose>
    <PrintTemplateChoose
      :type="printType"
      :filterOld="true"
      btnText="确定"
      title="选择打印模板"
      ref="exportPrintTemplateChoose"
    ></PrintTemplateChoose>
    <settlement
      :show-modal="settlement.show"
      :no-list="settlement.no_list"
      @on-cancel="closeSettlement"
      @on-ok="handleSettlementOk"
    ></settlement>
    <settlement-record
      :order-id="settlementRecordModal.orderId"
      :username="settlementRecordModal.username"
      :show-modal="settlementRecordModal.show"
      @on-cancel="closeSettlementRecord"
    ></settlement-record>

    <SModal ref="freight" mask class="freight" :btns="0">
      <div class="freight">
        <p>输入运费收款金额：</p>
        <Input v-model="freight_price" />
        <p class="err-txt">{{ freightErrTxt }}</p>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <Button class="mr10" @click="freightClose">取消</Button>
          <Button type="primary" @click="determine">确定</Button>
        </div>
      </template>
    </SModal>
    <SModal ref="ServiceChargeModalRef" mask class="freight" :btns="0">
      <div class="freight">
        <p>{{ serviceTipsTxt }}</p>
        <NumberInput
          :precision="2"
          :min="0"
          :maxlength="14"
          v-model="service_charge"
        />
        <p class="err-txt">{{ serviceChargeErrTxt }}</p>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <Button class="mr10" @click="serviceChargeModalClose">取消</Button>
          <Button type="primary" @click="confirmServiceChargeBill">确定</Button>
        </div>
      </template>
    </SModal>
    <exportExcels
      :new-type="exportExcels.hiddenDate"
      :show="exportExcels.show"
      :params="exportExcels.params"
      :title="exportExcels.title"
      @on-cancel="_onCancel"
      :type="exportExcels.type"
      :storage_key="exportExcels.storage_key"
      :export_type="export_type"
      :api="apiUrl.userAuditListExport"
      :showExportTotal="showExportTotal"
    >
      <div
        v-show="exportExcels.params.export_type === 'user'"
        slot="extra-fields"
        class="export-modal-extra"
      >
        <RadioGroup v-model="export_type">
          <Radio label="user">按商品导出</Radio>
          <Radio label="user_commodity_price">按商品+相同单价导出</Radio>
        </RadioGroup>
      </div>
    </exportExcels>
    <ExportAll
      :show="exportAll.show"
      :params="exportAll.params"
      @on-cancel="_onCancel"
    ></ExportAll>
    <!--  选择繁体简体 -->
    <Modal
      title="选择打印模式"
      v-model="showChooseChineseModal"
      @on-ok="_handlePrintAuditOrder"
      :width="416"
    >
      <RadioGroup v-model="printMode">
        <Radio label="simple">
          <span>简体</span>
        </Radio>
        <Radio label="traditional" style="margin: 0 20px">
          <span>繁体</span>
        </Radio>
      </RadioGroup>
    </Modal>
    <BatchSettlement ref="batchSettlement" :isNew="false"></BatchSettlement>
    <ExportExcel
      :show="exportModal.show"
      title="汇总详情导出"
      :api="apiUrl.purchaseAuditListExport"
      :params="exportModal.params"
      fieldsType="purchase_bill_export_summary"
      storeKey="purchase_bill_export_summary_column_store"
      :handleParams="handleExportParams"
      @on-cancel="exportModal.show = false"
    >
      <div
        v-show="!!summaryValue"
        slot="extra-fields"
        class="export-modal-extra"
      >
        <RadioGroup v-model="summaryValue">
          <Radio label="summary">按商品导出</Radio>
          <Radio label="summary_all">按商品+相同单价导出</Radio>
        </RadioGroup>
      </div>
    </ExportExcel>
    <Modal
      title="批量锁定"
      v-model="batchLockModal"
      @on-ok="onBatchLock"
      :width="416"
    >
      <div>
        锁定之后单据不能再对账，确定批量锁定多个对账单据？注意:锁定之后,对账单状态将自动变为"已对账"
      </div>
    </Modal>
  </div>
</template>

<script>
import PrintTemplateExportChoose from './components/main';
import Button from '@components/button';
import { SModal } from '@/components/modal';
import ListTable from '@/components/list-table';
import returnOrderList from './returnOrderList';
import settlement from './settlement';
import settlementRecord from './settlementRecord';
import CheckboxGroup from '@components/CheckboxGroup';
import { get, post } from '@/api/request';
import { api } from '@api/api.js';
import DateUtil from '@/util/date';
import common from '@api/user.js';
import orderService from '@api/order.js';
import { exportLoop } from '@components/common/export-btn/util';
import orderSerivce from '@api/order.js';
import UserAudit from '@api/UserAudit.js';
import DatePickers from './date-picker';
import configure from '@api/main.js';
import settings from '@api/settings';
import { toDecimal } from '@util/Number.js';

import TableHeadSortIcon from '@components/common/tableHeadSortIcon';
import printMixin from '@/mixins/orderPrint';
import exportExcels from './components/ExportExcel.vue';
import ExportAll from './components/ExportAll.vue';
import auditPrintMixin from '@/mixins/print/auditListPrint';
import auditListCategorySummaryPrint from '@/mixins/print/auditListCategorySummaryPrint';
import SoaPrintMixin from '@/mixins/print/soaPrint';
import { exportType, deepClone } from './utils';
import ConfigMixin from '@/mixins/config';
import LayoutMixin from '@/mixins/layout';
import TimeTypeSelect from '@/components/common/TimeTypeSelect.vue'; // 特殊时间筛选
import RadioGroup from '@components/RadioGroup';
import SelectAndInput from '@components/common/SelectAndInput';
import GroupFilter from '@components/common/GroupFilter';
import InputAutoComplete from '@/components/common/InputAutoComplete';
import { PAYMENT_STATISTICS } from '@/util/const';
import RelationNo from '@/components/relation-no';
import { EXCEL_SHEET_MULTI } from '@/util/print';
import storage from '@util/storage';
import AreaSelect from '@components/delivery/areaSelect_new';
import NumberInput from '@components/basic/NumberInput';
import BatchSettlement from './components/batchSettlement.vue';
import SplitRuleCascader from '@/pages/order/components/SplitRuleCascader';
import commonMixin from './mixin';
import StorageUtil from '@/util/storage';
import ExportExcel from '@components/common/export-excel';
import PrintTemplateChoose from '@components/print-template-choose-new';
import { BUSINESS_TYPE_LIST } from '@/util/const';
import CustomizeCascader from "@/components/customize-cascader/index.vue";
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

const AUDIT_LIST_FILTERS = 'audit-list_filters',
  SIMPLE = 'simple',
  TRADITIONAL = 'traditional';

export default {
  mixins: [
    printMixin,
    auditPrintMixin,
    SoaPrintMixin,
    ConfigMixin,
    LayoutMixin,
    auditListCategorySummaryPrint,
    commonMixin,
  ],
  name: 'finance-user-audit-list',
  components: {
    Button,
    SModal,
    ListTable,
    settlement,
    settlementRecord,
    exportExcels,
    ExportAll,
    PrintTemplateExportChoose,
    PrintTemplateChoose,
    NumberInput,
    BatchSettlement,
    ExportExcel,
  },
  provide() {
    return {
      foo: this.test,
    };
  },
  data() {
    let that = this;
    return {
      batchLockModal: false,
      dataProvider: '/superAdmin/accountBill/list',
      tooltipDis: false,
      requestParams: {},
      exportAll: {
        show: false,
        params: {},
      },
      exportExcels: {
        show: false,
        params: {},
        hiddenDate: false, // 是否显示日期选择
        type: undefined,
        storage_key: undefined,
      },
      flagss: false,
      tplId: '',
      currentid: 0,
      printTemplateSelectionBox: false,
      priorityTemplate: false,
      test: { v: false },
      // 选中的附属单据
      extraSelection: {},
      init: false,
      freightErrTxt: '',
      showfreight: false,
      service_charge: '',
      serviceTipsTxt: '输入服务费收款金额：',
      serviceChargeErrTxt: '',
      freight_price: '',
      bill_no: '',
      tableExpanded: undefined,
      selectedOrders: [],
      settlement: {
        no_list: '',
        show: false,
      },
      showOrderComplete: false,
      auditRow: {},
      showSelectPanel: false,
      driverList: '',
      salesList: '',
      exportType,
      settlementRecordModal: {
        username: '',
        orderId: '',
        show: false,
      },
      printTemplateData: [],
      filter: {
        user_id: '',
      },
      filterItems: [
        {
          required: true,
          checked: true,
          type: 'custom',
          component: TimeTypeSelect,
          key: ['start_date', 'end_date', 'date_type'],
          width: 'auto',
          defaultValue: [
            DateUtil.getBeforeDate(30),
            DateUtil.format(DateUtil.getTodayDate(), 'YYYY-MM-DD'),
            1,
          ],
          props: {
            clearable: false,
            data: [
              {
                label: '发货日期',
                value: 1,
              },
              {
                label: '出库日期',
                value: 2,
              },
              {
                label: '退货日期',
                value: 3,
              },
            ],
          },
          onChange: (value) => {
            value[1] = DateUtil.format(value[1], 'YYYY-MM-DD');
            return { value };
          },
        },
        {
          checked: true,
          required: true,
          label: '下单时间',
          type: 'DatePicker',
          props: {
            options: DateUtil.dateQuickOptions,
            type: 'datetimerange',
            format: 'yyyy-MM-dd HH:mm',
            placeholder: '请选择',
            separator: '~',
          },
          key: ['start_create_time', 'end_create_time'],
          class: 'datepicker-createtime',
        },
        {
          // required:true,
          checked: true,
          _label: '客户信息',
          type: 'custom',
          hasType: 'GroupFilter',
          style: {
            width: '299px',
          },
          key: ['no_type', 'unc_search'],
          defaultValue: ['1', ''],
          props: {
            selectData: [
              {
                label: this.sysEntityText('按客户信息'),
                placeholder: '输入客户名称/编码查询',
                value: '1',
                key: 'unc_search',
              },
              {
                label: '按单号',
                placeholder: '输入单号查询',
                value: '2',
                key: 'order_no',
              },
              {
                label: '按手机号',
                placeholder: '输入手机号查询',
                value: '3',
                key: 'user_tel',
              },
            ],
            customType: '1',
            customComp: () => InputAutoComplete,
            customBind: {
              dataProvider: orderService.getUserBySearch,
              valueKey: 'id',
              labelKey: 'email',
            },
            on: {
              'on-enter': (value) => {
                if (value[0] === '1') this.filter.user_id = value[1] || '';
                this.$refs.table && this.$refs.table.fetchData();
              },
              'on-clear': () => {
                this.filter.user_id = '';
              },
            },
          },
          component: GroupFilter,
          onChange: (value = '') => {
            this.filter.user_id = '';
            this.$refs.table.setValue('user_id', '', true);
            return { value, stop: true };
          },
        },
        {
          label: '订单制单人',
          key: 'order_op_user',
          type: 'Input',
          props: {
            placeholder: '请输入',
          }
        },
        {
          type: 'Select',
          key: 'user_business_type',
          label: '客户业态',
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
          data: [
            {
              label: '全部',
              value: '',
            },
            ...BUSINESS_TYPE_LIST
          ],
          props: {
            placeholder: '选择客户业态',
          },
        },
      ],
      advanceItems: [],
      showCols: [],
      columns: [],
      originCols: [
        {
          type: 'expand',
          width: 52,
          align: 'center',
          fixed: 'left',
          key: 'expand',
          titleType: 'account_bill',
          render: (h, params) => {
            let data = params.row;
            const rowKey = data.refer_no;
            // data._expanded = false;
            const title = this.$refs.table.getTitleList();
            const columns = deepClone(this.$refs.table.getFilterColumns());
            return h(returnOrderList, {
              props: {
                checkDisabled: data.checkDisabled,
                cancelChecked: data.cancelChecked,
                listData: data.detail,
                showCols: title,
                showColumnsDetail: columns,
              },
              on: {
                'on-settlement': (detail) => {
                  this.openSettlement(detail.refer_no);
                },
                'on-auditUnLock': (expendTableParams) => {
                  this.auditLock(expendTableParams, { is_lock: 0 });
                },
                'on-reconciliation': (reconciliation) => {
                  this.reconciliation(reconciliation);
                },
                'on-unreconciliation': (expendTableParams) => {
                  this.unaudit(expendTableParams);
                },
                'on-auditLock': (expendTableParams) => {
                  this.auditLock(expendTableParams);
                },
                'on-selection-change': (selection, data, row, checked) => {
                  if (!this.extraSelection[rowKey]) {
                    this.$set(this.extraSelection, rowKey, []);
                  }
                  this.extraSelection[rowKey] = selection;
                  this.changeSelection(selection);
                },
              },
            });
          },
        },
        {
          align: 'left',
          type: 'selection',
          key: 'selection',
          width: 25,
          fixed: 'left',
          style: {
            paddingLeft: 0,
          },
        },
        {
          title: '业务单号',
          key: 'refer_no',
          width: 200,
          fixed: 'left',
          render: (h, params) => {
            const row = params.row;
            if (params.row.refer_no.includes('合计')) {
              if (row.refer_no === '费用单合计') {
                return h('div', [
                  h(
                    'span',
                    {
                      on: {
                        click: () => {
                          if (row._summary) return;
                          this.auditDetail(params);
                        },
                      },
                    },
                    row.refer_no,
                  ),
                  h(
                    'Tooltip',
                    {
                      props: {
                        transfer: true,
                        placement: 'top',
                        maxWidth: 800,
                        content:
                          '该合计仅计算了已结算不可对账的费用单，可对账的费用单会和其他对账单一样在当前页以及所有页中进行统计',
                      },
                    },
                    [
                      h('Icon', {
                        style: {
                          fontSize: '14px',
                          color: 'rgba(0,0,0,.75)',
                          marginLeft: '5px',
                          cursor: 'pointer',
                        },
                        class: 'tip-icon',
                        props: {
                          type: 'ios-help-circle',
                        },
                      }),
                    ],
                  ),
                ]);
              }
              return h('span', params.row.refer_no);
            }
            let id = params.row.order_id;
            // 退货单/订单服务费退款
            if (
              Number(params.row.type) === 2 ||
              Number(params.row.type) === 8 ||
              Number(params.row.type) === 9
            ) {
              id = params.row.order_type_id;
            }
            return h(RelationNo, {
              props: {
                no: params.row.refer_no,
                id,
              },
            });
          },
        },
        {
          title: '出入库单号',
          key: 'store_in_out_no',
          width: 185,
        },
        {
          title: '订单标签',
          key: 'order_tag_text',
          width: 120,
        },
        {
          title: '仓库',
          key: 'store_name',
          width: 100,
        },
        {
          title: '客户名称',
          key: 'user_name',
          width: 120,
          poptip: true,
          render: (h, params) => {
            return params.row.user_name || '--';
          },
        },
        {
          title: '客户业态',
          key: 'user_business_type_desc',
          width: 120,
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
          width: 120,
        },
        {
          title: '客户类型',
          key: 'receivable_style_desc',
          width: 120,
        },
        {
          title: '手机号',
          key: 'account_tel',
          width: 120,
        },
        {
          title: '集团名称',
          key: 'group_name',
          width: 100,
        },
        {
          title: '客户商品别名编码',
          key: 'user_commodity_alias_code',
          width: 100,
        },
        {
          title: '单据类型',
          key: 'type_desc',
          width: 100,
        },
        {
          title: '应收金额',
          key: 'should_price',
          width: 100,
          align: 'right',
        },
        {
          title: '已收金额',
          key: 'pay_price',
          width: 100,
          align: 'right',
        },
        {
          title: '实收金额',
          key: 'actual_price',
          width: 100,
          align: 'right',
        },
        {
          title: '抹零金额',
          key: 'reduction_price',
          width: 100,
          align: 'right',
        },
        {
          title: '未收金额',
          key: 'unpaid_price',
          width: 100,
          align: 'right',
        },
        {
          // LZW_MARK(renderHeader)
          title: '发货日期',
          key: 'delivery_date',
          width: 120,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#deliveryDateSortIcon').click();
                      },
                    },
                  },
                  '发货日期',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.filters.delivery_sort_type,
                    id: 'deliveryDateSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      let sortMap = ['', 'desc', 'asc'];
                      this.filters.delivery_sort_type = e;
                      this.filters.settlement_sort_type = 0;
                      this.filters.sort_type = sortMap[e];
                      this.filters.sort_field = sortMap[e]
                        ? 'delivery_date'
                        : '';
                      this.storage.setLocalStorage(
                        AUDIT_LIST_FILTERS,
                        this.filters,
                      );
                      this.$refs.table.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '结算日期',
          key: 'settlement_date',
          width: 120,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#settlementDateSortIcon').click();
                      },
                    },
                  },
                  '结算日期',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.filters.settlement_sort_type,
                    id: 'settlementDateSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      let sortMap = ['', 'desc', 'asc'];
                      this.filters.settlement_sort_type = e;
                      this.filters.delivery_sort_type = 0;
                      this.filters.sort_type = sortMap[e];
                      this.filters.sort_field = sortMap[e]
                        ? 'settlement_date'
                        : '';
                      this.storage.setLocalStorage(
                        AUDIT_LIST_FILTERS,
                        this.filters,
                      );
                      this.$refs.table.fetchData();
                      console.log(JSON.stringify(this.filters), e);
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '线路名称',
          key: 'line_name',
          width: 100,
        },
        {
          title: '司机',
          key: 'driver',
          width: 100,
        },
        {
          title: '业务员',
          key: 'salesman',
          width: 100,
        },
        {
          title: '付款方式',
          key: 'pay_way_desc',
          width: 100,
        },
        {
          title: '对账单号',
          key: 'bill_no',
          width: 200,
          poptip: true,
          render(h, params) {
            const { row } = params;
            if (!row.bill_no) {
              return h('span', '--');
            }
            return h(RelationNo, {
              props: {
                no: row.bill_no,
                extraQuery: {
                  is_can_print: row.is_can_print,
                  refer_no: row.refer_no,
                },
              },
            });
          },
        },
        {
          title: '对账状态',
          key: 'is_bill_desc',
          width: 100,
          render: (h, params) => {
            let { row, index } = params,
              color = '',
              len = this.$refs.table.length;
            // 最后两行合计不显示对账状态
            let exceptRows = [len - 1, len - 2];
            if (exceptRows.includes(index)) {
              return false;
            }
            if (row.is_bill_desc !== '已对账') {
              color = 'red';
            }
            return h(
              'span',
              {
                attrs: {
                  style: `color: ${color}`,
                },
              },
              row.is_bill_desc,
            );
          },
        },
        {
          title: '对账人',
          key: 'bill_user',
        },
        {
          title: '结算状态',
          key: 'status_desc',
          render: (h, params) => {
            let { row } = params;
            let color = '';
            if (row.status_desc !== '已结算') {
              color = 'red';
            }
            return h(
              'span',
              {
                attrs: {
                  style: `color: ${color}`,
                },
              },
              row.status_desc,
            );
          },
        },
        {
          title: '时间账期',
          key: 'billing_period_remind',
          show: false,
          render: (h, params) => {
            let { row } = params;
            let color = '';
            if (row.billing_period_remind === '已逾期') {
              color = 'red';
            }
            return h(
              'span',
              {
                attrs: {
                  style: `color: ${color}`,
                },
              },
              row.billing_period_remind || '--',
            );
          },
        },
        {
          title: '锁定状态',
          key: 'lock_status',
          width: 100,
        },
        {
          title: '回单状态',
          key: 'is_receipt',
          width: 100,
          render: (h, { row }) => {
            return h('span', row.is_receipt_text);
          },
        },
        {
          title: '订单备注',
          key: 'remark',
          width: 100,
        },
        {
          title: '对账单备注',
          key: 'bill_remark',
          width: 100,
        },
        {
          title: '业务单据备注',
          key: 'transaction_remark',
          minWidth: 100,
        },
        {
          title: '同步状态',
          key: 'issue_order_sync_status_text',
          minWidth: 100,
        },
        {
          title: '支付方式',
          key: 'pay_method_desc',
        },
        {
          title: '授信额度',
          key: 'remind_price',
          width: 100,
          align: 'right',
        },
        {
          title: '账期到期',
          key: 'remind_desc',
          tip: '当前列表逾期展示为客户最新账期状态，黑色表示未到期、红色表示已逾期、黄色表示提醒、橙色表示已到期但未逾期',
          width: 140,
          render: (h, params) => {
            let data = params.row;
            let dayEl = '';
            let priceEl = '';
            // 1 是正常 2 是提醒 3 是逾期 4 表示到期
            const colors = {
              2: '#FF9F00', // 黄色
              3: '#F13130', // 已逾期：红色
              4: '#FF6E00', // 已到期
            };
            if (!data.remind_desc) {
              return '--';
            }
            let remindDescArr = params.row.remind_desc.split('/');
            if (data.remain_day !== '') {
              dayEl = h(
                'span',
                { style: { color: colors[data.day_remind_status] } },
                remindDescArr[0],
              );
            }
            if (data.remain_price !== '') {
              let priceDesc = '';
              if (remindDescArr.length > 1) {
                priceDesc = remindDescArr[1];
              }
              if (remindDescArr.length === 1) {
                priceDesc = remindDescArr[0];
              }
              priceEl = h(
                'span',
                { style: { color: colors[data.price_remind_status] } },
                priceDesc,
              );
            }

            let split = '';
            if (dayEl && priceEl) {
              split = '/';
            }
            return !dayEl && !priceEl
              ? '--'
              : h('div', [dayEl, split, priceEl]);
          },
        },
        {
          title: '确认状态',
          show: this.isOpenAccountBillConfirm,
          key: 'confirm_status_desc',
          width: 100,
        },
        {
          title: '订单制单人',
          key: 'order_op_user',
          width: 100,
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          actionCountLimit: 3,
          width: 228,
          actions: (params) => {
            const data = params.row;
            const actions = [
              {
                name: '详情',
                action: (params) => this.auditDetail(params),
                ctrl: 'is_can_detail',
              },
              {
                name: '反锁定',
                ctrl: 'is_can_unlock',
                action: (params) => this.auditLock(params, { is_lock: 0 }),
                confirm: `反锁定后，该对账单状态将会按照最后一次锁定的对账金额变更为未锁定状态，是否确认反锁定？`,
              },
              {
                name: '对账',
                confirm: () =>
                  data.is_tips_order_complete
                    ? '销售单处于“待收货”状态，点击「确定」进行对账，销售单状态将自动变为“已完成”状态'
                    : '',
                action: (params) => this.audit(params),
                ctrl: 'is_can_bill',
              },
              {
                name: '反对账',
                confirm:
                  '反对账后，该对账单状态将会变更为未对账状态，是否确认反对账？',
                action: (params) => this.unaudit(params),
                ctrl: 'is_can_anti_bill',
              },
              {
                name: '结算',
                action: (params) => this.payment(params),
                ctrl: 'is_can_settle',
              },
              {
                name: '锁定',
                ctrl: 'is_can_locked',
                action: (params) => this.auditLock(params),
                confirm: `<div>锁定之后单据不能再对账，是否确定锁定对账单据？<p>注意:锁定之后,对账单状态将自动变为"已对账"</p></div>`,
              },
              {
                attrs: (params) => {
                  const data = params.row;
                  let attrs = {
                    refer_no: data.refer_no, // 配合打印模块
                  };
                  return attrs; // Object
                },
                action: () => {
                  this.curPrintRow = params.row;
                  this._prints(false);
                },
                ctrl: 'is_can_print',
                name: '打印',
              },
              {
                name: '导出',
                ctrl: 'is_can_export',
                url: api.userAuditExport,
                param: 'refer_no',
                type: 'export',
              },
              {
                name: '结算记录',
                action: (params) => this.settlementRecord(params),
                ctrl: 'is_can_settle_detail',
              },
            ];
            return actions.filter((item) => data[item.ctrl]);
          },
        },
      ],
      requestPrams: {},
      filters: this.storage.getLocalStorage(AUDIT_LIST_FILTERS) || {
        sort_field: 'delivery_date', // 排序类型 settlement_date|delivery_date
        sort_type: 'desc', // 排序方式 ''默认, 'desc'倒序, 'asc'正序,
        settlement_sort_type: 0,
        delivery_sort_type: 1, // 默认按发货日期倒序排
      },
      showChooseChineseModal: false,
      printReferNos: '', // 打印订单订单记录订单号
      printMode: SIMPLE, //
      curPrintRow: {},
      exportModal: {
        show: false,
        title: '',
        params: {},
        fieldsType: '',
        storeKey: '',
      },
      summaryValue: 'summary',
      export_type: '',
      showExportTotal: true,
    };
  },
  created() {
    this.advanceItems = [
      {
        items: [
          {
            checked: false,
            type: 'Input',
            label: '订单备注',
            key: 'remark',
            props: {
              placeholder: '请输入订单备注',
            },
          },
          {
            // required:true,
            checked: true,
            type: 'DatePicker',
            props: {
              type: 'daterange',
              placeholder: '请选择',
            },
            key: ['settlement_start_date', 'settlement_end_date'],
            label: '结算日期',
          },
          {
            //  required:true,
            checked: true,
            type: 'custom',
            data: [],
            component: DatePickers,
            defaultValue: [],
            props: {
              type: 'daterange',
              placeholder: '请选择',
            },
            onChange: (value) => {
              let startDate = new Date(value[0]).getTime(),
                endDate = new Date(value[1]).getTime();
              if (endDate - startDate > 2592000000) {
                try {
                  this.test.v = true;
                } catch (error) {
                  console.log(error);
                }
                this.errorNotice({
                  title: '操作失败',
                  desc: '起始时间不能超过一个月',
                });
                setTimeout(() => {
                  this.test.v = false;
                }, 10);
                return {
                  value: [],
                  stop: true,
                };
              }
              return {
                value,
                stop: false,
              };
            },
            key: ['start_pay_time', 'end_pay_time'],
            label: '支付日期',
          },
          {
            //  required:true,
            checked: true,
            type: 'Select',
            data: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '未结算',
                value: 1,
              },
              {
                label: '部分结算',
                value: 2,
              },
              {
                label: '已结算',
                value: 3,
              },
            ],
            key: 'settlement_status',
            props: {
              multiple: true,
            },
            defaultValue:
              StorageUtil.getLocalStorage('finance-user-audit-list-state') ||
              '',
            label: '结算状态',
          },
        ],
      },
      {
        items: [
          {
            checked: true,
            type: 'Select',
            data: [],
            key: 'delivery_status',
            label: '发货状态',
            props: {
              clearable: true,
            },
          },
          {
            checked: false,
            type: 'Select',
            data: [],
            key: 'return_status',
            label: '退货状态',
          },
          {
            checked: false,
            type: 'Select',
            data: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '未锁定',
                value: 0,
              },
              {
                label: '已锁定',
                value: 1,
              },
            ],
            key: 'is_locked',
            label: '锁定状态',
          },
        ],
      },
      {
        items: [
          {
            checked: false,
            type: 'Select',
            data: [],
            key: 'line_id',
            label: '线路',
            props: {
              filterable: true,
            },
          },
          {
            checked: true,
            type: 'Select',
            data: [],
            key: 'storage_id',
            label: '仓库',
            props: {
              filterable: true,
              'filter-by-label': true,
            },
            onChange: (value) => {
              this.advanceItems[2].items.find(
                (item) => item.key === 'area_id',
              ).props.storeId = value;
              return {
                value,
              };
            },
          },
          {
            checked: true,
            label: '区域',
            type: 'custom',
            component: AreaSelect,
            key: 'area_id',
            defaultValue: '',
            props: {
              storeId: '',
              initChange: false,
            },
          },
          {
            checked: true,
            type: 'Select',
            data: [],
            key: 'group_id',
            label: '集团',
            props: {
              filterable: true,
            },
          },
        ],
      },
      {
        items: [
          {
            checked: false,
            type: 'Select',
            data: [],
            key: 'driver',
            label: '司机',
            props: {
              filterable: true,
            },
          },
          {
            checked: false,
            type: 'Select',
            data: [],
            key: 'salesman',
            label: '业务员',
            props: {
              filterable: true,
            },
          },
          {
            checked: true,
            type: 'Select',
            data: [],
            key: 'receivable_style_id',
            label: '客户类型',
            props: {
              multiple: true,
              clearable: true,
              'max-tag-count': 1,
            },
          },
        ],
      },
      {
        items: [
          {
            checked: false,
            type: 'custom',
            component: SelectAndInput,
            style: {
              width: '299px',
            },
            key: ['t_no_key', 't_no_value'],
            defaultValue: ['1', ''],
            props: {
              inputPlaceholder: '输入第三方流水号 /退货单号查询',
              selectDefaultValue: '1',
              inputDefaultValue: '',
              selectData: [
                {
                  label: '第三方流水号',
                  placeholder: '输入第三方流水号查询',
                  value: '1',
                },
                {
                  label: '退货单号',
                  placeholder: '输入退货单号查询',
                  value: '2',
                },
              ],
            },
          },
          // {
          //   checked:false,
          //   label: '第三方流水号',
          //   type: 'Input',
          //   key: 'third_flow_no',
          //   props: {
          //     placeholder: '第三方流水号'
          //   }
          // },
          // {
          //   checked:false,
          //   label: '退货单号',
          //   type: 'Input',
          //   key: 'return_no',
          //   props: {
          //     placeholder: '退货单号'
          //   }
          // },
          {
            //  required:true,
            checked: true,
            type: 'Select',
            data: [],
            key: 'bill_status',
            label: '对账状态',
            defaultValue: [1, 2],
            props: {
              clearable: true,
              multiple: true,
            },
            arrayToString: true,
          },
          {
            checked: false,
            type: 'Select',
            data: [
              {
                value: '-1',
                label: '全部',
              },
              {
                value: '0',
                label: '未回单',
              },
              {
                value: '1',
                label: '已回单',
              },
            ],
            key: 'is_receipt',
            label: '回单状态',
          },
          {
            checked: false,
            key: 'confirm_status',
            show: this.isOpenAccountBillConfirm,
            label: '确认状态',
            type: 'Select',
            data: [
              {
                value: '',
                label: '全部',
              },
              {
                value: '0',
                label: '未确认',
              },
              {
                value: '1',
                label: '已确认',
              },
            ],
          },
          {
            checked: true,
            type: 'Select',
            data: [
              {
                label: '全部',
                value: 0,
              },
              {
                label: '已同步',
                value: 1,
              },
              {
                label: '未同步',
                value: 2,
              },
            ],
            key: 'issue_order_sync_status',
            label: '同步状态',
          },
          {
            type: 'Select',
            label: '支付方式',
            key: 'pay_method',
            data: [],
            props: {
              clearable: true,
            },
            placeholder: '支付方式',
          },
          {
            checked: false,
            label: '拆单规则',
            type: 'custom',
            component: SplitRuleCascader,
            key: 'split_rule_id',
            show: this.isOpenSplitOrder,
            defaultValue: [],
            props: {
              splitProviderFormat: true,
              placeholder: '请选择拆单规则',
            },
          },
          {
            show: () => this.isOpenCustomerFieldCustomize,
            checked: false,
            width: 'auto',
            type: 'custom',
            name: '客户自定义字段',
            key: ['user_customize_id', 'user_customize_field_select_config_ids'],
            defaultValue: [],
            props: {
              customizeType: '14',
              label: '客户自定义字段',
            },
            component: CustomizeCascader,
          },
        ],
      },
      {
        items: [
          {
            checked: false,
            type: 'custom',
            data: [],
            component: CheckboxGroup,
            defaultValue: [],
            key: 'pay_way',
            label: '付款方式',
            style: {
              width: '900px',
            },
            props: {
              data: [],
            },
            onChange(value) {
              return {
                value,
                stop: true,
              };
            },
          },
        ],
      },
      {
        items: [
          {
            //  required:true,
            checked: true,
            block: true,
            relation: 'order_tag_filter',
            label: '标签筛选',
            type: 'custom',
            show: this.isOpenOrderTag,
            key: 'order_tag_filter',
            component: RadioGroup,
            props: {
              data: [
                {
                  label: '同时存在',
                  value: '1',
                },
                {
                  label: '存在一个',
                  value: '2',
                },
                {
                  label: '无标签',
                  value: '3',
                },
              ],
              on: {
                'on-reset': (value) => {
                  // 重置时恢复订单标签可选
                  this._setOrderTagDisabled(+value === 3);
                },
              },
            },
            defaultValue: '1',
            style: {
              width: '900px',
            },
            onChange: (value) => {
              // 选中标签筛选中的无标签之后，订单标签筛选项不可选择
              this._setOrderTagDisabled(+value === 3);
              return {
                value,
              };
            },
          },
        ],
      },
      {
        items: [
          {
            //  required:true,
            checked: true,
            block: true,
            tagTopStart: true,
            label: '订单标签',
            relation: 'order_tag_filter',
            type: 'custom',
            key: 'order_tag',
            show: this.isOpenOrderTag,
            attrs: {
              disabled: false,
              keyName: 'order_tag',
            },
            component: orderTag,
            onChange(value) {
              return {
                value,
                stop: true,
              };
            },
          },
        ],
      },
      {
        items: [
          {
            checked: false,
            required: false,
            show: false,
            block: true,
            label: '客户标签',
            key: 'user_tag',
            type: 'custom',
            defaultValue: [],
            props: {
              data: [],
            },
            style: {
              minWidth: '900px',
            },
            component: CheckboxGroup,
            onChange: (value) => {
              return { value, stop: true };
            },
          },
        ],
      },
    ];
    this.userAuditService = new UserAudit();
    this.initQueryParams();
    // this.getOrderTagList();
  },
  activated() {
    const filters = this.advanceItems.reduce((prev, next) => {
      prev = prev.concat(next.items);
      return prev;
    }, []);
    this.getUserTagList(filters);
    this.getSearchConfig(filters);
    this.getUserTypeList(filters);
    if (this.init) {
      this.refreshList();
    } else {
      this.init = true;
    }
  },
  deactivated() {
    this.tooltipDis = true;
    this.advanceItems[2].items[0].data = [];
    this.advanceItems[3].items[0].data = [];
    this.advanceItems[3].items[1].data = [];
  },
  destroyed() {
    this.tooltipDis = true;
  },
  watch: {
    'sysConfig.order_reminder_range_of_billing_period_exceeded'() {
      this.getColumns();
    },
  },
  mounted() {
    const filters = this.advanceItems.reduce((prev, next) => {
      prev = prev.concat(next.items);
      return prev;
    }, []);
    this.getColumns();
    let _this = this;
    configure.getConfig().then((config) => {
      this.printTemplateSelectionBox =
        parseInt(config.bill_print_no_pop_tpl_window) === 1;
      this.priorityTemplate =
        parseInt(config.bill_print_use_customer_tpl) === 1;
    });
    this.handleDefaultSearch();
    // 加载xslx相关资源
    if (typeof window.loadXSLX === 'function') {
      window.loadXSLX();
    }
  },
  computed: {
    openUseInvoiceTemplate() {
      return +this.sysConfig.open_bill_delivery_print === 1;
    },
    printType() {
      return this.isUerAuditPrintUseOrderTemplate ? 'ORDER' : 'SOA';
    },
    extraSelectionList() {
      let list = [];
      Object.keys(this.extraSelection).forEach((rowKey) => {
        list = [...list, ...this.extraSelection[rowKey]];
      });
      return list;
    },
    extraSelectionCount() {
      return this.extraSelectionList.length;
    },
    selectedShouldPrice() {
      const selectedRows = this.selectedOrders.concat(this.extraSelectionList);
      let price = 0;
      selectedRows.forEach((row) => {
        if (!row.refer_no.includes('合计')) {
          // 排除合计行
          price += Number(row.should_price);
        }
      });
      return toDecimal(price, 2);
    },
  },
  methods: {
    getColumns() {
      const billingPeriodRemindData = this.originCols.find(
        (item) => item.key === 'billing_period_remind',
      );
      if (
        this.sysConfig.order_reminder_range_of_billing_period_exceeded &&
        !this.sysConfig.order_reminder_range_of_billing_period_exceeded
          .split(',')
          .includes('2')
      ) {
        billingPeriodRemindData.show = false;
        billingPeriodRemindData.hide = true;
      } else {
        billingPeriodRemindData.show = true;
        billingPeriodRemindData.hide = false;
      }
      this.columns = this.originCols;
    },
    showTooltip() {
      this.tooltipDis = false;
    },
    freightClose() {
      this.$refs.freight.close();
      this.freightErrTxt = '';
    },
    jumpToAuditListNew() {
      storage.setLocalStorage('audit-list-type', 1);
      this.$router.push('audit-list-new');
    },
    changeSelection(selection) {
      const list = this.$refs.table ? this.$refs.table.getData() : [];
      list.forEach((item) => {
        if (item.detail && item.detail.length) {
          item.detail.forEach((d) => {
            const checked = !!this.extraSelectionList.find(
              (s) => s.refer_no === d.refer_no,
            );
            d._checked = checked;
          });
        }
      });
    },
    initQueryParams() {
      this.initParams = {
        start_date: DateUtil.getBeforeDate(30),
        end_date: DateUtil.format(DateUtil.getTodayDate(), 'YYYY-MM-DD'),
        date_type: 1,
      };
    },
    // 处理默认搜索条件
    handleDefaultSearch() {
      const query = this.$route.query;
      const { account_tel, end_date, from, start_date } = query;
      if (from !== PAYMENT_STATISTICS) return;
      // this._mxSetAdvanceItemsData('客户信息', ['defaultValue'],['3', account_tel])
      // this._mxSetAdvanceItemsData('客户信息', ['props', 'inputDefaultValue'], account_tel)
      setTimeout(() => {
        this.$refs.table.setValue(
          ['no_type', 'unc_search'],
          ['3', account_tel],
          true,
        );
        this.$refs.table.setValue('unc_search', '', true);
        this.$refs.table.setValue('user_tel', account_tel, true);
        this.$refs.table.setValue(
          ['start_date', 'end_date', 'date_type'],
          [start_date, end_date, 1],
          false,
        );
      }, 500);
    },
    printCategorySummaryPrint() {
      if (
        this.selectedOrders.length == 0 ||
        this.selectedOrders.filter((e) => e.is_can_print).length == 0
      ) {
        this.warningMessage('没有可打印的销售订单');
        return;
      }
      this.auditListCategorySummaryPrint({
        id: this.selectedOrders
          .filter((e) => e.is_can_print)
          .map((item) => item.order_id)
          .join(','),
        delivery_date: `${this.requestParams.start_date}-${this.requestParams.end_date}`,
        print_time: DateUtil.getTodayDate(),
      });
    },
    _onCancel() {
      this.exportExcels.show = false;
      this.exportAll.show = false;
    },
    /**
     * 打印订单流水
     */
    printOrderList() {
      let list = this.$refs.table.getData();
      let nos = [];
      this.selectedOrders.forEach((item) => {
        if (item.is_can_print) {
          nos.push(item);
        }
      });
      if (nos.length == 0) {
        this.warningMessage('没有可打印的销售订单');
        return;
      }
      if (
        nos.filter((item) => {
          return item.is_delivery === false;
        }).length !== 0
      ) {
        this.$Modal.confirm({
          title: '提示',
          content: '已选订单中包含未发货的订单，点击确定将打印已发货的订单',
          onOk: () => {
            this.printReferNos = nos
              .map((item) => {
                return item.refer_no;
              })
              .join(',');
            this._assistPrintAuditOrder();
          },
        });
      } else {
        this.printReferNos = nos
          .map((item) => {
            return item.refer_no;
          })
          .join(',');
        this._assistPrintAuditOrder();
      }
    },
    _assistPrintAuditOrder() {
      // 如果开启了汇率设置显示选择打印简体还是繁体的模态框
      if (this.isOpenExchangeRate) {
        this.showChooseChineseModal = true;
      } else {
        this._handlePrintAuditOrder();
      }
    },
    _handlePrintAuditOrder() {
      this._printAuditOrder({
        refer_no: this.printReferNos,
        is_traditional: this.printMode === TRADITIONAL,
      });
      // 还原选项
      this.printMode = SIMPLE;
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    async _saveTemplate(tplId, isMerge, isBatch) {
      let params = {
        soaTpl: this.isUerAuditPrintUseOrderTemplate ? '' : tplId,
        orderTpl: this.isUerAuditPrintUseOrderTemplate ? tplId : '',
      };
      let order_id = [];
      if (isBatch) {
        this.selectedOrders.forEach((item) => {
          if (item.is_can_print) {
            order_id.push(item.order_id);
          }
        });
        if (order_id.length == 0) {
          this.warningMessage('没有可打印的销售订单');
          return;
        }
      } else {
        order_id = this.curPrintRow.order_id;
      }

      if (this.isUerAuditPrintUseOrderTemplate) {
        this.$Message.success('操作成功！');
        this._printsData(0, isMerge, tplId, isBatch);
        // 对账单保存下 不然老板不支持传id 只查询默认模板
      } else {
        let { status, message } = await post(api.saveTplSel, params);
        if (status) {
          this.$Message.success('操作成功！');
          this._printsData(0, isMerge, tplId, isBatch);
        } else {
          this.$Message.error(message);
        }
      }
    },
    /**
     * @description: 用于修改优先使用客户指定模板配置以及不再弹出打印模板选择框配置
     * @author: Jeff
     */
    async _configurationModification() {
      const templates = [
        {
          key: 'bill_print_use_customer_tpl',
          value: this.priorityTemplate ? 1 : 0,
        },
        {
          key: 'bill_print_no_pop_tpl_window',
          value: this.printTemplateSelectionBox ? 1 : 0,
        },
      ];
      let params = {
        config_list: JSON.stringify(templates),
      };
      let { message, status } = await post(api.saveSystemConfig, params);
      if (status) {
        this.$Message.success('配置修改成功！');
      } else {
        this.$Message.error(message);
      }
    },
    async _printsData(isExport = 0, is_merge, tplId, isBatch) {
      let checked = [];
      let order_id = [];
      let list = this.$refs.table.getData();
      if (isBatch) {
        this.selectedOrders.forEach((item) => {
          if (item.is_can_print) {
            checked.push(item.refer_no);
            order_id.push(item.order_id);
          }
        });
        if (order_id.length == 0) {
          this.warningMessage('没有可导出的销售订单');
          return false;
        }
        if (isExport && checked.length > 20) {
          this.$smessage({
            type: 'error',
            text: '一次导出的数据不能超过20条',
          });
          return false;
        }
      } else {
        checked = [this.curPrintRow.refer_no];
        order_id = [this.curPrintRow.order_id];
      }

      if (isExport) {
        tplId = await this.$refs.exportPrintTemplateChoose.choose();
      }

      if (this.isUerAuditPrintUseOrderTemplate) {
        this._printOrder({
          is_bill: 1,
          id: order_id.toString(),
          type: 'ORDER',
          is_merge: is_merge || 0,
          is_preview: !isBatch,
          is_export_excel: isExport,
          template_id: tplId,
          exportSheetMode: EXCEL_SHEET_MULTI,
        });
      } else {
        // 适配新版打印,_mixin混入
        this._mxPrintSoaOrder({
          className: 'batchPrinting-SOA',
          referNo: checked.toString(),
          isExport: isExport,
          template_id: tplId,
          is_preview: !isBatch,
          is_merge: is_merge || 0,
        });
      }
    },
    /**
     * @description: 批量打印 printTemplateSelectionBox为选中状态则直接批量打印，反之弹出打印模版选择框
     * @author: Jeff
     */
    async _prints(isBatch) {
      if (isBatch) {
        if (this.selectedOrders.filter((e) => e.is_can_print).length == 0) {
          this.warningMessage('没有可打印的销售订单');
          return;
        }
      }
      // 调用用户信息接口获取用户指定的模板
      const res = await common.getUserDetail(this.curPrintRow.user_id);
      let defaultBillId = null;
      if (res.status && !this.openUseInvoiceTemplate) {
        const { bill_order_tpl_id } = res.data;
        if (+bill_order_tpl_id) defaultBillId = bill_order_tpl_id;
      }
      this.$refs.printTemplateBatchChoose.open(
        (template_id, preview, sheet, isMerge) => {
          this.$refs.printTemplateBatchChoose.closeModal();
          this._saveTemplate(template_id, isMerge, isBatch);
        },
        defaultBillId,
      );
    },
    /**
     * @description: 打开打印模版
     * @author: Jeff
     */
    // 获取订单标签列表
    // getOrderTagList() {
    //   orderSerivce.qryOrderTagList().then((res) => {
    //     let { status, data } = res;
    //     if (status) {
    //       if (data && Array.isArray(data)) {
    //         let orderTagList = res.data.map((item) => {
    //           return {
    //             label: item.name,
    //             value: item.id,
    //           };
    //         });
    //         this.advanceItems.forEach((row) => {
    //           row.items.forEach((col) => {
    //             if (col.key === 'order_tag') {
    //               col.props.data = orderTagList;
    //             }
    //           });
    //         });
    //       }
    //     }
    //   });
    // },
    async determine() {
      let params = {
        bill_no: this.bill_no,
        freight_price: this.freight_price,
      };
      let { status, message } = await get(
        api.freight.confirmFreightBill,
        params,
      );
      if (status == 1) {
        this.$Notice.success({ title: '操作成功' });
        this.refreshList();
        this.freightClose();
      } else {
        this.freightErrTxt = message;
        // this.$Notice.success({ title: message });
      }
    },
    serviceChargeModalClose() {
      this.$refs.ServiceChargeModalRef.close();
      this.service_charge = '';
    },
    async confirmServiceChargeBill() {
      let url = '/superAdmin/accountBill/confirmServiceChargeBill';
      let params = {
        bill_no: this.bill_no,
        service_charge: this.service_charge,
      };
      const { status, message } = await this.$request.post(url, params);
      if (status == 1) {
        this.$Notice.success({ title: '操作成功' });
        this.serviceChargeModalClose();
        this.refreshList();
      } else {
        this.serviceChargeErrTxt = message;
      }
    },
    reconciliation(data) {
      this.bill_no = data.bill_no;
      this.bill_no = data.bill_no;
      console.log('reconciliation', data);
      if (data.type === '3') {
        // 运费对账
        this.freight_price = data.freight_price;
        this.$refs.freight.open();
      }
      if (data.type === '7' || data.type === '9') {
        // 服务费对账
        console.log('data----', data);
        this.serviceTipsTxt =
          data.type === '9' ? '输入服务费退款金额：' : '输入服务费收款金额：';
        this.service_charge = Math.abs(data.should_price);
        this.serviceChargeErrTxt = '';
        this.$refs.ServiceChargeModalRef.open();
      }
      if (data.type === '2') {
        // 退货单对账
        this.toAudit(data);
      }
    },
    handleExport(exportType) {
      if (exportType === exportType.print) {
        return;
      }
      this.exportExcel(exportType);
    },
    /**
     * 操作优化
     * @description: 检查导出接口params参数
     * @param {Object}
     * @return {Boolean}
     */
    $_checkExportParams(params) {
      const { export_type, delivery_status, start_date, end_date } = params;
      const exportTypeS = ['group', 'detail'];
      if (exportTypeS.includes(export_type) && delivery_status !== 1) {
        this.warningMessage('请展开高级筛选, 将发货状态筛选项选中为【已发货】');
        return false;
      } else if (export_type === 'group' && start_date !== end_date) {
        this.warningMessage('请选择同一天发货日期');
        return false;
      }
      return true;
    },
    exportExcel(exportType) {
      const params = this.formatParams(this.$refs.table.getParams());
      if (exportType === 'user_detail' && !params.user_id) {
        this.errorNotice({
          title: '导出失败',
          desc: '按客户信息筛选项中必须指定客户查询',
        });
        return;
      }
      this.showExportTotal = true;
      this.export_type = '';
      this.exportExcels.type = 'account_bill_detail_export';
      this.exportExcels.storage_key = 'account_export_column';
      if (exportType === this.exportType.all) {
        this.exportAll.show = true;
        this.exportAll.params = params;
        this.exportAll.params.export_type = exportType;
        return;
      } else if (
        exportType === 'detail' ||
        exportType === 'user_detail' ||
        exportType === 'user'
      ) {
        this.exportExcels.show = true;
        this.exportExcels.params = params;
        this.exportExcels.params.export_type = exportType;
        this.exportExcels.hiddenDate = exportType !== 'detail';
        if (exportType === 'user_detail' || exportType === 'user') {
          this.exportExcels.type = this.exportExcels.storage_key =
            exportType === 'user_detail'
              ? 'account_bill_user_detail_export'
              : 'account_bill_user_export';
        }
        if (exportType === 'user_detail') {
          this.export_type = 'user_detail';
        }
        if (exportType === 'user') {
          this.export_type = 'user';
          this.showExportTotal = false;
        }
        this.exportExcels.title =
          exportType === 'detail'
            ? '对账明细导出'
            : exportType === 'user'
              ? '按客户导出'
              : '按客户导出明细';
        return;
      }
      let url = api.userAuditListExport;
      // console.log(this.filters);
      params.export_type = exportType;
      if (!this.$_checkExportParams(params)) {
        // 检查不通过
        return false;
      }
      this.$request.get(url, params).then((res) => {
        let { status, message, data } = res;
        if (status) {
          exportLoop(data.task_no);
        } else {
          this.$Message.warning({
            content: message || '导出失败',
            duration: 5,
          });
        }
      });
    },
    formatParams(params) {
      let requestPrams = this.deepClone(params);
      if (params.pay_way) {
        requestPrams.pay_way = params.pay_way.join(',');
      } else {
        requestPrams.pay_way = '';
      }
      if (Array.isArray(params.user_tag))
        requestPrams.user_tag = params.user_tag.join();
      params.t_no_key === '1' &&
        (requestPrams.third_flow_no = params.t_no_value);
      params.t_no_key === '2' && (requestPrams.return_no = params.t_no_value);
      delete requestPrams.t_no_key;
      delete requestPrams.t_no_value;
      if (requestPrams.user_id) requestPrams.unc_search = '';
      // 搜索框类型
      // params.no_type === '1' && (requestPrams.unc_search = params.no_value);
      // params.no_type === '2' && (requestPrams.order_no = params.no_value);
      // params.no_type === '3' && (requestPrams.user_tel = params.no_value);
      delete requestPrams.no_type;
      // delete requestPrams.no_value;
      requestPrams.is_receipt =
        requestPrams.is_receipt === '-1' ? '' : requestPrams.is_receipt;
      if (Array.isArray(requestPrams.split_rule_id))
        requestPrams.split_rule_id = requestPrams.split_rule_id.join(',');
      return { ...requestPrams, ...this.filters };
    },
    beforeRequest(params) {
      if (!params.start_date || !params.end_date) {
        params.end_date = '';
        params.start_date = '';
      }
      let result = this.formatParams(params);
      this.requestParams = result;
      this.tableExpanded = undefined;

      const settlement_status = StorageUtil.getLocalStorage(
        'finance-user-audit-list-state',
      );
      if (settlement_status) {
        result.settlement_status = settlement_status;
      }
      return result;
    },
    afterRequest(response) {
      if (response.status && response.data.list.length > 0) {
        response.data.list.forEach((row) => {
          // 有详情数据的默认展开
          if (row.detail && row.detail.length > 0) {
            row._expanded = true;
          }
        });
        this.tableExpanded = response.data.list
          .filter((row) => !row.refer_no.includes('合计'))
          .some((row) => row._expanded);
      } else {
        this.tableExpanded = false;
      }
      this.extraSelection = {};
      return response;
    },
    beforeSetData(params) {
      params.forEach((row) => {
        // 合计行不展示勾选和展开
        if (row.refer_no.includes('合计')) {
          row._summary = true;
        }
        row._empty = !row.detail || row.detail.length === 0;
      });
      this.setCheckAble(params);
      return params;
    },
    handleResizeColumn() {
      this.$forceUpdate();
    },
    getSearchConfig(filters) {
      this.$request
        .get(this.apiUrl.getDriverList, { pageSize: 9999 })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            const driver = filters.find((item) => item.key === 'driver');
            if (driver) {
              driver.data = data.list.map((item) => ({
                label: item.driver_name,
                value: item.driver_id,
              }));
              driver.data.unshift({ label: '全部', value: '' });
            }
          }
        });
      this.userAuditService.getAuditListSearchConfig().then((res) => {
        if (res.status) {
          let data = res.data;
          const keyMap = {
            status: 'settle_status',
            line_id: 'line',
            group_id: 'group',
            salesman: 'sales_list',
            storage_id: 'user_warehouse',
            bill_status: 'bill_status',
            pay_way: 'pay_way',
            delivery_status: 'delivery_status',
            return_status: 'return_status',
          };
          filters.forEach((item) => {
            if (keyMap[item.key]) {
              item.data = data[keyMap[item.key]].map((item) => ({
                label: item.name,
                value: item.id,
              }));
              if (item.key === 'pay_way') {
                item.props.data = item.data;
              }
              if (item.key === 'salesman') {
                item.data.unshift({
                  label: '未设置',
                  value: 0,
                });
                item.data.unshift({
                  label: '全部',
                  value: '',
                });
              }
              // 对账状态去掉已关闭
              if (item.key === 'bill_status') {
                item.data = item.data.filter(
                  (_dataItem) => +_dataItem.value !== 3,
                );
              }
            }
            if (item.key === 'pay_method') {
              item.data = data.settle_pay_way.map((item) => ({
                label: item.name,
                value: item.id,
              }));
            }
          });
        }
      });
    },
    async getUserTagList(filters) {
      let res = await common.getUserTagList();
      if (res.status && res.data && res.data.length) {
        let tagFilter = filters.find((item) => item.key === 'user_tag');
        if (tagFilter) {
          tagFilter.show = true;
          tagFilter.props.data = res.data.map((item) => ({
            value: item.id,
            label: item.name,
          }));
        }
      }
    },
    async getUserTypeList(filters) {
      let res = await common.getUserTypeList({ pageSize: 999 });
      let { status, data } = res;
      if (status) {
        const result = filters.find(
          (item) => item.key === 'receivable_style_id',
        );
        if (result) {
          result.data = data.list.map((item) => ({
            label: item.name,
            value: item.id,
          }));
        }
      }
    },
    handleColChange(cols) {
      this.showCols = cols;
      let showCols = this.initCols(cols, this.originCols);
      let expandCol = this.originCols.find((col) => col.type === 'expand');
      let selectCol = this.originCols.find((col) => col.type === 'selection');
      let actionCol = this.originCols.find((col) => col.key === 'action');

      showCols.unshift(selectCol);
      showCols.unshift(expandCol);
      if (!showCols.some((col) => col.key === 'action')) {
        showCols.push(actionCol);
      }

      this.cols = showCols;
    },
    auditLock(params, otherParams = {}) {
      const unlock = +otherParams.is_lock === 0;
      this.$request
        .post(
          unlock ? this.apiUrl.userAuditUnlock : this.apiUrl.userAuditLock,
          { refer_no: params.row.refer_no, ...otherParams },
        )
        .then((res) => {
          if (res.status === 1) {
            this.successMessage(unlock ? '反锁定成功' : '锁定成功');
            this.refreshList();
          } else {
            this.errorMessage(
              res.message || (unlock ? '反锁定失败' : '锁定失败'),
            );
          }
        });
    },
    unaudit(params) {
      this.$request
        .post(this.apiUrl.accountAntiBill, { bill_id: params.row.bill_id })
        .then((res) => {
          if (res.status === 1) {
            this.successMessage('反对账成功');
            this.refreshList();
          } else {
            this.errorMessage(res.message || '反对账失败');
          }
        });
    },
    auditDetail(params) {
      let { row } = params;
      // 未发货跳转订单详情
      if (!row.is_delivery) {
        this.$router.push({
          path: '/orderDetail',
          query: {
            keep_scroll: 1,
            id: row.order_id,
          },
        });
      } else {
        this.$router.push({
          path: '/finance/userAuditDetail',
          query: {
            keep_scroll: 1,
            is_can_print: row.is_can_print,
            refer_no: row.refer_no,
          },
        });
      }
    },
    auditCompleteOrder() {
      this.userAuditService
        .completeOrder({ order_id: this.auditRow.order_id })
        .then((res) => {
          let { status, message } = res;
          if (status) {
            // this.showOrderComplete = false;
            this.toAudit(this.auditRow);
          } else {
            this.modalError(message);
            this.auditRow = {};
          }
        });
    },
    audit(params) {
      let { row } = params;
      if (row.is_tips_order_complete) {
        this.auditRow = row;
        // this.showOrderComplete = true;
        this.auditCompleteOrder();
      } else {
        this.toAudit(row);
      }
    },
    toAudit(row) {
      this.$router.push({
        path: '/finance/userAudit',
        query: {
          keep_scroll: 1,
          refer_no: row.refer_no,
          type: row.type,
        },
      });
    },
    closeSettlementRecord() {
      this.settlementRecordModal.show = false;
    },
    settlementRecord(params) {
      let { row } = params;
      this.settlementRecordModal.username = row.user_name;
      this.settlementRecordModal.orderId = row.order_id;
      this.settlementRecordModal.show = true;
    },
    payment(params) {
      let { row } = params;
      let noArr = [];
      noArr.push(row.refer_no);
      if (row.detail) {
        row.detail.forEach((item) => {
          if (
            item.is_can_settle ||
            (item.is_can_check && item.refer_no.startsWith('RT')) // 退货单
          ) {
            noArr.push(item.refer_no);
          }
        });
      }
      this.openSettlement(noArr.join(','));
    },
    handleSettlementOk() {
      this.closeSettlement();
      this.refreshList();
      this.cancelCheckAll();
    },
    openSettlement(nos) {
      if (nos) {
        this.settlement.no_list = nos;
      }
      this.settlement.show = true;
    },
    closeSettlement() {
      this.settlement.show = false;
    },
    /**
     * @description: 批量标记为已对账
     */
    $_onBatchMarkAsReconciled() {
      console.log(this.selectedOrders);
      console.log(this.extraSelectionList);
      const selectedRows = this.selectedOrders.concat(this.extraSelectionList);
      let bill_no = selectedRows
        .filter((e) => e.is_can_bill)
        .map((item) => item.bill_no)
        .join(',');
      if (!bill_no) {
        this.warningMessage('没有可对账的单据！');
        return;
      }
      this.userAuditService
        .batchMarkAsReconciled({
          bill_no,
        })
        .then(({ status, message }) => {
          if (status) {
            this.successNotice('批量标记为已对账成功');
            this.refreshList();

            this.cancelCheckAll();
          } else {
            this.errorNotice(message);
          }
        });
    },
    batchPayment() {
      let list = this.$refs.table.getData();
      let nos = [];
      this.selectedOrders.forEach((item) => {
        if (item.is_can_settle) {
          console.log('1,', item);
          nos.push(item.refer_no);
        }
      });
      this.extraSelectionList.forEach((extraItem) => {
        if (extraItem.is_can_settle) {
          console.log('2,', extraItem);
          nos.push(extraItem.refer_no);
        }
      });
      if (!nos || nos.length == 0) {
        this.warningMessage('没有可结算的单据！');
        return false;
      }
      this.settlement.no_list = nos.join(',');
      console.log('no_list', this.settlement.no_list);
      this.openSettlement();
    },
    onBatchLock() {
      const selectBillIds = this.selectedOrders.map((item) => item.bill_id);
      const list = this.$refs.table ? this.$refs.table.getData() : [];
      const selectedList = list.filter((item) =>
        selectBillIds.includes(item.bill_id),
      );
      let refer_no =
        selectedList
          .filter((item) => item.is_can_locked)
          .map((item) => item.refer_no)
          .join(',') || [];
      if (!refer_no.length) {
        this.warningMessage('没有可锁定的单据！');
        return;
      }
      this.$request
        .post(this.apiUrl.batchLockCustomerBill, { refer_no })
        .then((res) => {
          const { status, message } = res;
          if (status) {
            this.successMessage('批量锁定成功！');
            this.refreshList(false);
          } else {
            this.warningMessage(message || '批量锁定失败');
          }
        });
    },
    selectAllSameUser() {
      let selectedOrder = null;
      const list = this.$refs.table.getData();
      if (this.selectedOrders && this.selectedOrders.length > 0) {
        selectedOrder = this.selectedOrders[0];
      }
      let ids = new Set();
      this.selectedOrders.forEach((res) => {
        ids.add(res.user_id);
      });
      this.extraSelectionList.forEach((itemDetail) => {
        ids.add(itemDetail.user_id);
      });
      if (ids.size == 0) {
        this.modalError({
          content: '请先选择一个客户!',
        });
        return false;
      }
      for (let key of ids.keys()) {
        list.forEach((item) => {
          if (item.user_id === key) {
            item._checked = true;
          }
          if (!item.detail) {
            return false;
          }
          // 选中附属单据
          item.detail.forEach((itemDetail) => {
            if (item.user_id === key) {
              itemDetail._checked = true;
            }
          });
          // console.log('selectedOrder', this.selectedOrders)
        });
      }
      let selectedOrders = [];
      // 更新选中数据
      list.forEach((item) => {
        if (item._checked) {
          selectedOrders.push(item);
        }
      });
      this.selectedOrders = selectedOrders;
    },
    toggleExpand() {
      const flag = !this.tableExpanded;
      const list = this.$refs.table.getData().map((item) => {
        item._expanded = flag;
        return item;
      });
      this.$refs.table.setData(list);
      this.tableExpanded = flag;
    },
    handleSelectionChange(selection, item, params) {
      if (!params) {
        return;
      }
      this.selectedOrders = selection;
      const list = this.$refs.table ? this.$refs.table.getData() : [];
      list.forEach((item) => {
        item._checked =
          selection.map((item) => item.refer_no).indexOf(item.refer_no) !== -1;
        if (item._checked && item.detail && item.detail.length) {
          item._expanded = true;
        }
        if (
          params.noall &&
          selection.length == 0 &&
          item.detail &&
          item.detail.length
        ) {
          item.detail.forEach((d) => {
            d._checked = false;
          });
        }
        if (
          params.all &&
          selection.length > 0 &&
          item.detail &&
          item.detail.length
        ) {
          item.detail.forEach((d) => {
            d._checked = true;
          });
        }
        if (
          params.row &&
          item.refer_no == params.row.refer_no &&
          item.detail &&
          item.detail.length
        ) {
          item.detail.forEach((d) => {
            d._checked = params.checked;
          });
        }
      });
      // this.setCheckAble(list);
    },
    /**
     * 设置表格中可选的选项
     */
    setCheckAble(list) {
      const selectList = this.selectedOrders.map((item) => item.refer_no);
      list.forEach((item) => {
        // item._disabled = !item.is_can_check;
        item._checked = selectList.indexOf(item.refer_no) !== -1;
      });
      // this.$refs.table.setData(list);
    },
    cancelCheckAll() {
      this.selectedOrders = [];
      const list = this.$refs.table ? this.$refs.table.getData() : [];
      this.$refs.table.selection = [];
      list.forEach((item) => {
        item._checked = false;
        if (item.detail && item.detail.length) {
          item.detail.forEach((d) => {
            d._checked = false;
          });
        }
      });
    },
    refreshList() {
      this.$refs.table.fetchData(false);
    },
    _setOrderTagDisabled(_disabled) {
      if (this.advanceItems[7] && this.advanceItems[7].items[0]) {
        this.advanceItems[7].items[0].attrs.disabled = _disabled;
        if (_disabled) {
          // 清空筛选项
          this.$refs.table.setValue('order_tag', [], true);
        }
      }
    },
    handleResetChange() {
      this.filter.user_id = '';
    },
    handleExportParams() {},
  },
};
</script>

<style lang="less" scoped>
.common {
  .freight {
    .ivu-input-wrapper {
      padding: 10px 0 5px 0;
    }
    .err-txt {
      font-size: 12px;
      height: 18px;
      color: #f13130;
    }
  }
  .more-btn {
    .sui-icon {
      font-size: 12px;
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover .sui-icon {
      transform: rotate(180deg);
    }
  }
  /deep/ .groupFilter-custom {
    width: 219px;
  }
  /deep/ .ivu-select-multiple .ivu-select-selection .ivu-select-placeholder {
    padding-left: 10px;
  }
}
.ml10 {
  margin-left: 10px;
}
.modelHeaders {
  height: 30px;
  display: flex;
  align-items: center;
}
.modelFooter {
  height: 30px;
}
.aic {
  display: flex;
  align-items: center;
}
.export-modal-extra {
  padding: 6px 0 0 24px;
  border-top: 1px solid #f0f2f0;
  transform: translateY(26px);
}
</style>
