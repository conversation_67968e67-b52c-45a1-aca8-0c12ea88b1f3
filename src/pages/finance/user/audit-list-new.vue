<!--
 * @Description: 财务/客户结算-新版
 * @url: http://0.0.0.0:8089/#/finance/user/audit-list-new
 * @Author: hgj 🙂
 * @Date: 2022-09-07 16:01:48
 * @LastEditTime: 2023-09-05 15:00:12
-->

<template>
  <div class="common purchase__list">
    <ListTable
      tableId="audit_list_02"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :initParams="initParams"
      @on-selection-change="handleSelectionChange"
      :customSelect="true"
      :before-request="beforeRequest"
      :after-request="afterRequest"
      :before-set-data="beforeSetData"
      :advance="true"
      :advance-items="advanceItems"
      :filters="filter"
      :filter-items="filterItems"
      :data-provider="dataProvider"
      :border="false"
      :outer-border="true"
      :max-line="2"
      row-key="refer_no"
      :columns="columns"
      :height="getTableHeight() - 77"
      ref="table"
      :defaultPageSize="25"
      :pageSizeOpts="[25, 40, 50, 100, 200]"
      :keepScroll="true"
      :virtualScroll="true"
      @reset-change="handleResetChange"
      :isOpenCustom="true"
    >
      <Dropdown @on-click="handleExport" slot="button">
        <Button class="more-btn" type="default"
          >导出<span class="sui-icon icon-arrow-down"></span
        ></Button>
        <DropdownMenu slot="list">
          <DropdownItem :name="exportType.all">对账单详情</DropdownItem>
          <DropdownItem :name="exportType.group">按集团导出</DropdownItem>
          <DropdownItem :name="exportType.user">按客户导出</DropdownItem>
          <DropdownItem :name="exportType.detail">对账明细导出</DropdownItem>
          <!-- <DropdownItem :name="exportType.user_detail">按客户导出明细</DropdownItem> -->
          <DropdownItem :name="exportType.user_category"
            >客户分类统计</DropdownItem
          >
          <DropdownItem :name="exportType.statement_detail_summary"
            >对账单+明细+分类汇总</DropdownItem
          >
        </DropdownMenu>
      </Dropdown>
      <div
        slot="before-table"
        v-show="selectedOrders.length === 0"
        style="display: flex"
      >
        <Tooltip
          placement="top"
          max-width="300"
          content="一键把当前筛选条件下所有客户对账单进行批量结算操作"
        >
          <Button @click="openBatchSettleModal">
            一键结算
            <i
              class="sui-icon icon-tips"
              style="color: inherit; font-size: 13px; vertical-align: baseline"
            ></i>
          </Button>
        </Tooltip>
        <Tooltip
          :max-width="200"
          placement="left"
          :disabled="tooltipDis"
          style="margin-left: auto"
        >
          <div slot="content">
            <span>新版：按记账日期对账，退货金额按退货单退货日期记录</span
            ><br /><span
              >旧版：按发货日期对账，退货金额按发货单发货日期记录</span
            >
          </div>
          <div @mouseenter="showTooltip">
            <Button class="check-btn" @click="jumpToAuditList">切换旧版</Button>
          </div>
        </Tooltip>
      </div>
      <div slot="batch-operation" class="aic">
        <span class="mr10" style="margin-left: -25px; font-weight: 500"
          >（应收总金额:
          <span style="color: #ff6e00">{{ selectedShouldPrice }}</span
          >）
        </span>
        <Button class="mr10" @click="selectAllSameUser"
          >全选相同客户单据</Button
        >
        <Poptip
          title="将选中的订单的对账状态标记为已对账，同时将“待收货”状态订单变更为“已完成”"
          confirm
          placement="bottom"
          :width="240"
          class="mr10"
          @on-ok="$_onBatchMarkAsReconciled"
        >
          <Button>批量标记为已对账</Button>
        </Poptip>
        <Button class="mr10" type="primary" @click="batchPayment"
          >批量结算</Button
        >
        <!--        <Button class="mr10" @click="batchLockModal=true" v-if="isOpenCustomerBillLock">批量锁定</Button>-->
        <Button class="mr10" @click="printCategorySummaryPrint"
          >打印商品分类汇总</Button
        >
        <Button class="mr10" type="default" @click="_prints(true)"
          >批量打印</Button
        >
      </div>
    </ListTable>
    <PrintTemplateExportChoose
      type="SOA"
      filterOld
      showPreview
      title="选择打印模板"
      ref="printTemplateBatchChoose"
    ></PrintTemplateExportChoose>
    <settlement
      :show-modal="settlement.show"
      :no-list="settlement.no_list"
      @on-cancel="closeSettlement"
      @on-ok="handleSettlementOk"
      page="new"
    ></settlement>
    <settlement-record
      :order-id="settlementRecordModal.orderId"
      :username="settlementRecordModal.username"
      :show-modal="settlementRecordModal.show"
      @on-cancel="closeSettlementRecord"
    ></settlement-record>

    <SModal ref="freight" mask class="freight" :btns="0">
      <div class="freight">
        <p>输入运费收款金额：</p>
        <Input v-model="freight_price" />
        <p class="err-txt">{{ freightErrTxt }}</p>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <Button class="mr10" @click="freightClose">取消</Button>
          <Button type="primary" @click="determine">确定</Button>
        </div>
      </template>
    </SModal>
    <SModal ref="ServiceChargeModalRef" mask class="freight" :btns="0">
      <div class="freight">
        <p>{{ serviceTipsTxt }}</p>
        <NumberInput
          :precision="2"
          :min="0"
          :maxlength="14"
          v-model="service_charge"
        />
        <p class="err-txt">{{ serviceChargeErrTxt }}</p>
      </div>
      <template #footer>
        <div style="display: flex; justify-content: flex-end">
          <Button class="mr10" @click="serviceChargeModalClose">取消</Button>
          <Button type="primary" @click="confirmServiceChargeBill">确定</Button>
        </div>
      </template>
    </SModal>
    <exportExcels
      :new-type="true"
      :show="exportExcels.show"
      :title="exportExcels.title"
      :params="exportExcels.params"
      @on-cancel="_onCancel"
      :type="exportExcels.type"
      :storage_key="exportExcels.storage_key"
      :api="exportExcels.api"
      :showExportTotal="exportExcels.showExportTotal"
    >
    </exportExcels>
    <ExportAll
      :new-type="true"
      :show="exportAll.show"
      :params="exportAll.params"
      @on-cancel="_onCancel"
    ></ExportAll>
    <!--  选择繁体简体 -->
    <Modal
      title="选择打印模式"
      v-model="showChooseChineseModal"
      @on-ok="_handlePrintAuditOrder"
      :width="416"
    >
      <RadioGroup v-model="printMode">
        <Radio label="simple">
          <span>简体</span>
        </Radio>
        <Radio label="traditional" style="margin: 0 20px">
          <span>繁体</span>
        </Radio>
      </RadioGroup>
    </Modal>
    <BatchSettlement ref="batchSettlement"></BatchSettlement>
    <ExportExcel
      :show="exportModal.show"
      title="汇总详情导出"
      :api="apiUrl.purchaseAuditListExport"
      :params="exportModal.params"
      fieldsType="purchase_bill_export_summary"
      storeKey="purchase_bill_export_summary_column_store"
      :handleParams="handleExportParams"
      @on-cancel="exportModal.show = false"
    >
      <div
        v-show="!!summaryValue"
        slot="extra-fields"
        class="export-modal-extra"
      >
        <RadioGroup v-model="summaryValue">
          <Radio label="summary"> 按商品导出 </Radio>
          <Radio label="summary_all"> 按商品+相同单价导出 </Radio>
        </RadioGroup>
      </div>
    </ExportExcel>
    <Modal
      title="批量锁定"
      v-model="batchLockModal"
      @on-ok="onBatchLock"
      :width="416"
    >
      <div>
        锁定之后单据不能再对账，确定批量锁定多个对账单据？注意:锁定之后,对账单状态将自动变为"已对账"
      </div>
    </Modal>
  </div>
</template>

<script>
import PrintTemplateExportChoose from './components/main';
import Button from '@components/button';
import { SModal } from '@components/modal';
import ListTable from '@components/list-table';
import settlement from './settlement';
import settlementRecord from './settlementRecord';
import CheckboxGroup from '@components/CheckboxGroup';
import { get, post } from '@/api/request';
import { api } from '@api/api.js';
import DateUtil from '@/util/date';
import common from '@api/user.js';
import orderService from '@api/order.js';
import { exportLoop } from '@components/common/export-btn/util';
import UserAudit from '@api/UserAudit.js';
import settings from '@api/settings';
import { toDecimal } from '@util/Number.js';

import TableHeadSortIcon from '@components/common/tableHeadSortIcon';
import printMixin from '@/mixins/orderPrint';
import exportExcels from './components/ExportExcel.vue';
import ExportAll from './components/ExportAll.vue';
import auditPrintMixin from '@/mixins/print/auditListPrint';
import auditListCategorySummaryPrint from '@/mixins/print/auditListCategorySummaryPrint';
import SoaPrintMixin from '@/mixins/print/soaPrint';
import { exportType } from './utils';
import ConfigMixin from '@/mixins/config';
import LayoutMixin from '@/mixins/layout';
import TimeTypeSelect from '@/components/common/TimeTypeSelect.vue'; // 特殊时间筛选
import RadioGroup from '@components/RadioGroup';
import GroupFilter from '@components/common/GroupFilter';
import RelationNo from '@/components/relation-no';
import storage from '@util/storage';
import AreaSelect from '@components/delivery/areaSelect_new';
import NumberInput from '@components/basic/NumberInput';
import InputAutoComplete from '@/components/common/InputAutoComplete';
import BatchSettlement from './components/batchSettlement.vue';
import commonMixin from './mixin';
import SplitRuleCascader from '@/pages/order/components/SplitRuleCascader';
import goodsAutoCompleteNew from '@components/common/goodsAutoComplete_new';
import StorageUtil from '@/util/storage';
import ExportExcel from '@components/common/export-excel';
import { BUSINESS_TYPE_LIST } from '@/util/const';
import CustomizeCascader from "@/components/customize-cascader/index.vue";
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

const AUDIT_LIST_FILTERS = 'audit-list_filters';
const SIMPLE = 'simple';
const TRADITIONAL = 'traditional';

export default {
  mixins: [
    printMixin,
    auditPrintMixin,
    SoaPrintMixin,
    ConfigMixin,
    LayoutMixin,
    auditListCategorySummaryPrint,
    commonMixin,
  ],
  name: 'finance-user-audit-list-new',
  components: {
    Button,
    SModal,
    ListTable,
    settlement,
    settlementRecord,
    exportExcels,
    ExportAll,
    PrintTemplateExportChoose,
    NumberInput,
    BatchSettlement,
    goodsAutoCompleteNew,
    ExportExcel,
  },
  provide() {
    return {
      foo: this.test,
    };
  },
  data() {
    let that = this;
    return {
      batchLockModal: false,
      dataProvider: '/superAdmin/accountBill/newList',
      tooltipDis: false,
      requestParams: {},
      exportAll: {
        show: false,
        params: {},
      },
      exportExcels: {
        api: '',
        showExportTotal: true,
        type: '',
        storage_key: '',
        show: false,
        params: {},
        title: '',
      },
      flagss: false,
      tplId: '',
      currentid: 0,
      printTemplateSelectionBox: false,
      priorityTemplate: false,
      test: { v: false },
      // 选中的附属单据
      extraSelection: {},
      init: false,
      freightErrTxt: '',
      showfreight: false,
      freight_price: '',
      service_charge: '',
      serviceTipsTxt: '输入服务费收款金额：',
      serviceChargeErrTxt: '',
      bill_no: '',
      selectedOrders: [],
      settlement: {
        no_list: '',
        show: false,
      },
      auditRow: {},
      showSelectPanel: false,
      driverList: '',
      salesList: '',
      exportType,
      settlementRecordModal: {
        username: '',
        orderId: '',
        show: false,
      },
      printTemplateData: [],
      filter: {
        user_id: '',
      },
      filterItems: [
        {
          required: true,
          checked: true,
          type: 'custom',
          component: TimeTypeSelect,
          key: ['start_date', 'end_date', 'date_type'],
          width: 'auto',
          defaultValue: [
            DateUtil.getBeforeDate(30),
            DateUtil.format(DateUtil.getTodayDate(), 'YYYY-MM-DD'),
            1,
          ],
          props: {
            clearable: false,
            data: [
              {
                label: '记账日期',
                value: 1,
              },
              {
                label: '创建日期',
                value: 2,
              },
            ],
          },
          onChange: (value) => {
            value[1] = DateUtil.format(value[1], 'YYYY-MM-DD');
            return { value };
          },
        },
        {
          // required:true,
          checked: true,
          _label: '客户信息',
          type: 'custom',
          style: {
            width: '299px',
          },
          key: ['search_value_type', 'search_value'],
          defaultValue: ['1', ''],
          props: {
            selectData: [
              {
                label: '按客户信息',
                placeholder: '输入客户名称/手机号/联系人',
                value: '1',
                key: 'search_value',
              },
              {
                label: '按对账单号',
                placeholder: '输入对账单号查询',
                value: '2',
                key: 'search_value',
              },
              {
                label: '按业务单号',
                placeholder: '输入业务单号查询',
                value: '3',
                key: 'search_value',
              },
            ],
            customType: '1',
            customComp: () => InputAutoComplete,
            customBind: {
              dataProvider: orderService.getUserBySearch,
              valueKey: 'id',
              labelKey: 'email',
            },
            on: {
              'on-enter': (value) => {
                if (value[0] === '1') this.filter.user_id = value[1] || '';
                this.$refs.table && this.$refs.table.fetchData();
              },
              'on-clear': () => {
                this.filter.user_id = '';
              },
            },
          },
          component: GroupFilter,
          onChange: (value = '') => {
            return { value, stop: true };
          },
        },
        {
          label: '子账号',
          key: 'sub_user_search',
          type: 'Input',
          props: {
            placeholder: '输入子账号查询',
          }
        },
        {
          label: '订单制单人',
          key: 'order_op_user',
          type: 'Input',
          props: {
            placeholder: '请输入',
          }
        },
        {
          label: '下单时间',
          key: ['order_start_time', 'order_end_time'],
          type: 'DatePicker',
          props: {
            type: 'daterange',
            placeholder: '请选择下单时间',
          },
        },
        {
          type: 'Select',
          key: 'user_business_type',
          label: '客户业态',
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
          data: [
            {
              label: '全部',
              value: '',
            },
            ...BUSINESS_TYPE_LIST
          ],
          props: {
            placeholder: '选择客户业态',
          },
        },
      ],
      advanceItems: [],
      showCols: [],
      columns: [],
      originCols: [
        {
          width: 52,
          type: 'titleCfg',
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'account_bill_new_list',
        },
        {
          align: 'left',
          type: 'selection',
          key: 'selection',
          width: 25,
          fixed: 'left',
          style: {
            paddingLeft: 0,
          },
        },
        {
          title: '对账单号',
          key: 'bill_no',
          width: 200,
          fixed: 'left',
          render: (h, params) => {
            const { row } = params;
            if (params.row.refer_no.includes('合计')) {
              if (row.refer_no === '费用单合计') {
                return h('div', [
                  h(
                    'span',
                    {
                      on: {
                        click: () => {
                          if (row._summary) return;
                          this.auditDetail(params);
                        },
                      },
                    },
                    row.refer_no,
                  ),
                  h(
                    'Tooltip',
                    {
                      props: {
                        transfer: true,
                        placement: 'top',
                        maxWidth: 800,
                        content:
                          '该合计仅计算了已审核不可对账的费用单，可对账的费用单会和其他对账单一样在当前页以及所有页中进行统计',
                      },
                    },
                    [
                      h('Icon', {
                        style: {
                          fontSize: '14px',
                          color: 'rgba(0,0,0,.75)',
                          marginLeft: '5px',
                          cursor: 'pointer',
                        },
                        class: 'tip-icon',
                        props: {
                          type: 'ios-help-circle',
                        },
                      }),
                    ],
                  ),
                ]);
              }
              return h('span', params.row.refer_no);
            }
            return h(
              'span',
              {
                class: ['text--primary'],
                style: {
                  cursor: 'pointer',
                },
                on: {
                  click: () => {
                    this.auditDetail(params);
                  },
                },
              },
              params.row.bill_no,
            );
          },
        },
        {
          title: '客户名称',
          key: 'user_name',
          width: 120,
        },
        {
          title: '客户业态',
          key: 'user_business_type_desc',
          width: 120,
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
        },
        {
          width: 140,
          minWidth: 140,
          title: '子账号',
          key: 'sub_user_name',
        },
        {
          title: '客户编码',
          key: 'user_code',
          width: 120,
        },
        {
          title: '客户类型',
          key: 'receivable_style_desc',
          width: 120,
        },
        {
          title: '记账日期',
          key: 'delivery_date',
          width: 110,
          renderHeader: (h) => {
            return h(
              'div',
              { style: { display: 'flex', alignItems: 'center' } },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#deliveryDateSortIcon').click();
                      },
                    },
                  },
                  '记账日期',
                ),
                h(
                  'Tooltip',
                  {
                    props: {
                      content:
                        '销售订单、订单运费、押金周转筐、订单服务费：取订单发货日期；订单退货：取退货日期；客户费用单：取付款日期',
                      placement: 'top',
                      transfer: true,
                      maxWidth: '260',
                    },
                  },
                  [
                    h('s-icon', {
                      props: { icon: 'tips', size: 12 },
                    }),
                  ],
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.filters.delivery_sort_type,
                    id: 'deliveryDateSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      let sortMap = ['', 'desc', 'asc'];
                      this.filters.settlement_sort_type = 0;
                      this.filters.delivery_sort_type = e;
                      this.filters.sort_type = sortMap[e];
                      this.filters.sort_field = sortMap[e]
                        ? 'delivery_date'
                        : '';
                      this.storage.setLocalStorage(
                        AUDIT_LIST_FILTERS,
                        this.filters,
                      );
                      this.$refs.table.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '业务单据类型',
          key: 'type_desc',
          width: 100,
        },
        {
          title: '应收金额',
          key: 'should_price',
          width: 100,
          align: 'right',
        },
        {
          title: '已收金额',
          key: 'pay_price',
          width: 100,
          align: 'right',
        },
        {
          title: '未收金额',
          key: 'unpaid_price',
          width: 100,
          align: 'right',
        },
        {
          title: '抹零金额',
          key: 'reduction_price',
          width: 100,
          align: 'right',
        },
        {
          title: '实收金额',
          key: 'actual_price',
          width: 100,
          align: 'right',
        },
        {
          title: '业务单号',
          key: 'refer_no',
          width: 200,
          render: (h, params) => {
            if (params.row.refer_no.includes('合计')) {
              return h('span', '--');
            }
            let id = params.row.order_id;
            // 退货单
            if (
              Number(params.row.type) === 2 ||
              Number(params.row.type) === 8 ||
              Number(params.row.type) === 9
            ) {
              id = params.row.order_type_id;
            }

            // 客户费用单
            if (Number(params.row.type) === 10) {
              id = params.row.order_type_id;
            }
            return h(RelationNo, {
              props: {
                no: params.row.refer_no,
                id,
              },
            });
          },
        },
        {
          title: '仓库',
          key: 'storage_name',
          width: 100,
        },
        {
          title: '集团名称',
          key: 'group_name',
          width: 100,
        },
        {
          title: '结算日期',
          key: 'settlement_date',
          width: 160,
        },
        {
          title: '创建日期',
          key: 'create_date',
          width: 130,
        },
        {
          title: '付款方式',
          key: 'pay_way_desc',
          width: 100,
        },
        {
          title: '对账状态',
          key: 'is_bill_desc',
          width: 100,
          render: (h, params) => {
            let { row, index } = params,
              color = '',
              len = this.$refs.table.length;
            // 最后两行合计不显示对账状态
            let exceptRows = [len - 1, len - 2];
            if (exceptRows.includes(index)) {
              return false;
            }
            if (row.is_bill_desc !== '已对账') {
              color = 'red';
            }
            return h(
              'span',
              {
                attrs: {
                  style: `color: ${color}`,
                },
              },
              row.is_bill_desc,
            );
          },
        },
        {
          title: '对账人',
          key: 'bill_user',
        },
        {
          title: '结算状态',
          key: 'status_desc',
          render: (h, params) => {
            let { row } = params;
            let color = '';
            if (row.status_desc !== '已结算') {
              color = 'red';
            }
            return h(
              'span',
              {
                attrs: {
                  style: `color: ${color}`,
                },
              },
              row.status_desc,
            );
          },
        },
        {
          title: '确认状态',
          key: 'confirm_status_desc',
          width: 100,
        },
        {
          title: '回单状态',
          key: 'is_receipt',
          width: 100,
          render: (h, { row }) => {
            return h('span', row.is_receipt_text);
          },
        },
        {
          title: '线路',
          key: 'line_name',
          width: 100,
        },
        {
          title: '业务员',
          key: 'salesman',
        },
        {
          title: '司机',
          key: 'driver_name',
        },
        {
          title: '锁定状态',
          key: 'lock_status',
          width: 100,
          render(h, { row }) {
            return h('span', row.is_locked_desc || row.lock_status)
          }
        },
        {
          title: '对账单备注',
          key: 'remark',
          width: 100,
        },
        {
          title: '授信额度',
          key: 'remind_price',
          width: 100,
          align: 'right',
        },
        {
          title: '账期到期',
          key: 'remind_desc',
          tip: '当前列表逾期展示为客户最新账期状态，黑色表示未到期、红色表示已逾期、黄色表示提醒、橙色表示已到期但未逾期',
          width: 140,
          render: (h, params) => {
            let data = params.row;
            let dayEl = '';
            let priceEl = '';
            // 1 是正常 2 是提醒 3 是逾期 4 表示到期
            const colors = {
              2: '#FF9F00', // 黄色
              3: '#F13130', // 已逾期：红色
              4: '#FF6E00', // 已到期
            };
            if (!data.remind_desc) {
              return '--';
            }
            let remindDescArr = params.row.remind_desc.split('/');
            if (data.remain_day !== '') {
              dayEl = h(
                'span',
                { style: { color: colors[data.day_remind_status] } },
                remindDescArr[0],
              );
            }
            if (data.remain_price !== '') {
              let priceDesc = '';
              if (remindDescArr.length > 1) {
                priceDesc = remindDescArr[1];
              }
              if (remindDescArr.length === 1) {
                priceDesc = remindDescArr[0];
              }
              priceEl = h(
                'span',
                { style: { color: colors[data.price_remind_status] } },
                priceDesc,
              );
            }

            let split = '';
            if (dayEl && priceEl) {
              split = '/';
            }
            return !dayEl && !priceEl
              ? '--'
              : h('div', [dayEl, split, priceEl]);
          },
        },
        {
          title: '订单制单人',
          key: 'order_op_user',
          width: 100,
        },
        {
          title: '订单标签',
          key: 'order_tag_text',
          width: 100,
        },
        {
          title: '所属供应商',
          key: 'split_provider_name',
          width: 100,
          show: () => {
            return this.sysConfig.is_open_split_order == 1 && this.sysConfig.split_order_mode == 3
          },
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          actionCountLimit: 3,
          width: 228,
          actions: (params) => {
            const data = params.row;
            const actions = [
              {
                name: '详情',
                action: (params) => this.auditDetail(params),
                ctrl: 'is_can_detail',
              },
              {
                name: '锁定',
                ctrl: 'is_can_lock',
                confirm: '<div>锁定之后单据不能再对账，是否确定锁定对账单据？<p>注意:锁定之后,对账单状态将自动变为"已对账"</p></div>',
                action: () => {
                  this.auditLock(params, {is_lock: 1});
                }
              },
              {
                name: '反锁定',
                ctrl: 'is_can_unlock',
                action: params => this.auditLock(params, {is_lock: 0}),
                confirm: `反锁定后，该对账单状态将会按照最后一次锁定的对账金额变更为未锁定状态，是否确认反锁定？`
              },
              {
                name: '对账',
                confirm: () =>
                  data.is_tips_order_complete
                    ? '销售单处于“待收货”状态，点击「确定」进行对账，销售单状态将自动变为“已完成”状态'
                    : '',
                action: (params) => this.audit(params),
                ctrl: 'is_can_bill',
              },
              {
                name: '反对账',
                confirm:
                  '反对账后，该对账单状态将会变更为未对账状态，是否确认反对账？',
                action: (params) => this.unaudit(params),
                ctrl: 'is_can_anti_bill',
              },
              {
                name: '结算',
                action: (params) => this.payment(params),
                ctrl: 'is_can_settle',
              },
              {
                attrs: (params) => {
                  const data = params.row;
                  let attrs = {
                    refer_no: data.refer_no, // 配合打印模块
                  };
                  return attrs; // Object
                },
                action: () => {
                  this.curPrintRow = params.row;
                  this._prints(false);
                },
                name: '打印',
                show: +data.type === 1 || +data.type === 2,
              },
            ];
            return actions.filter((item) => data[item.ctrl] || item.show);
          },
        },
      ],
      requestPrams: {},
      filters: this.storage.getLocalStorage(AUDIT_LIST_FILTERS) || {
        sort_field: '', // 排序类型 settlement_date|delivery_date
        sort_type: '', // 排序方式 ''默认, 'desc'倒序, 'asc'正序,
        settlement_sort_type: 0,
        delivery_sort_type: 0,
      },
      showChooseChineseModal: false,
      printReferNos: '', // 打印订单订单记录订单号
      printMode: SIMPLE, // 打印简体
      curPrintRow: {},
      exportModal: {
        show: false,
        title: '',
        params: {},
        fieldsType: '',
        storeKey: '',
      },
      summaryValue: 'summary',
    };
  },
  created() {
    console.log('确认状态', this.sysConfig.is_account_bill_confirm);
    this.advanceItems = [
      {
        checked: false,
        type: 'Input',
        label: '订单备注',
        key: 'remark',
        props: {
          placeholder: '请输入订单备注',
        },
      },
      {
        // required:true,
        checked: true,
        type: 'Select',
        data: [],
        key: 'bill_type',
        defaultValue: 0,
        label: '单据类型',
      },
      {
        // required:true,
        checked: true,
        type: 'Select',
        data: [],
        key: 'bill_status',
        props: {
          multiple: true,
        },
        defaultValue: [1, 2],
        arrayToString: true,
        label: '对账状态',
      },
      {
        //  required:true,
        checked: true,
        type: 'Select',
        data: [
          // {
          //   label: '全部',
          //   value: '',
          // },
          {
            label: '未结算',
            value: 1,
          },
          {
            label: '部分结算',
            value: 2,
          },
          {
            label: '已结算',
            value: 3,
          },
        ],
        props: {
          multiple: true,
          clearable: true,
        },
        defaultValue:
          StorageUtil.getLocalStorage('finance-user-audit-list-state') || '',
        key: 'settlement_status',
        label: '结算状态',
      },
      {
        checked: true,
        type: 'Select',
        data: [],
        key: 'receivable_style_id',
        defaultValue: '-1',
        label: '客户类型',
        props: {
          multiple: true,
          clearable: true,
          'max-tag-count': 1,
        },
      },
      {
        checked: false,
        type: 'Select',
        data: [],
        key: 'salesman',
        defaultValue: '',
        label: '业务员',
        props: {
          filterable: true,
          placeholder: '全部',
        },
      },
      {
        checked: true,
        type: 'Select',
        data: [],
        key: 'group_id',
        defaultValue: 0,
        label: '集团',
        props: {
          filterable: true,
        },
      },
      {
        checked: false,
        type: 'Select',
        defaultValue: '-1',
        data: [
          {
            value: '-1',
            label: '全部',
          },
          {
            value: '0',
            label: '未回单',
          },
          {
            value: '1',
            label: '已回单',
          },
        ],
        props: {
          filterable: true,
        },
        key: 'is_receipt',
        label: '回单状态',
      },
      {
        checked: true,
        type: 'Select',
        data: [],
        key: 'storage_id',
        defaultValue: 0,
        label: '仓库',
        onChange: (value) => {
          this.advanceItems.find(
            (item) => item.key === 'area_id',
          ).props.storeId = value;
          return {
            value,
          };
        },
        props: {
          filterable: true,
          'filter-by-label': true,
        },
      },
      {
        checked: true,
        label: '区域',
        type: 'custom',
        component: AreaSelect,
        key: 'area_id',
        props: {
          storeId: '',
        },
      },
      {
        checked: false,
        type: 'Select',
        defaultValue: '-1',
        data: [
          {
            value: '-1',
            label: '全部',
          },
          {
            value: '0',
            label: '未锁定',
          },
          {
            value: '1',
            label: '已锁定',
          },
        ],
        key: 'is_locked',
        label: '锁定状态',
        props: {
          filterable: true,
        },
      },
      {
        show: +this.sysConfig.is_account_bill_confirm === 1,
        checked: false,
        type: 'Select',
        defaultValue: '',
        data: [
          {
            value: '',
            label: '全部',
          },
          {
            value: '0',
            label: '未确认',
          },
          {
            value: '1',
            label: '已确认',
          },
        ],
        key: 'confirm_status',
        label: '确认状态',
        props: {
          filterable: true,
        },
      },
      {
        checked: false,
        type: 'Select',
        data: [],
        key: 'line_id',
        defaultValue: 0,
        label: '线路',
        props: {
          filterable: true,
        },
      },
      {
        checked: false,
        type: 'Select',
        data: [],
        key: 'driver_id',
        label: '司机',
        defaultValue: 0,
        props: {
          filterable: true,
        },
      },
      {
        // required:true,
        checked: true,
        type: 'DatePicker',
        props: {
          type: 'daterange',
          placeholder: '请选择',
        },
        key: ['settlement_start_date', 'settlement_end_date'],
        label: '结算日期',
      },
      {
        checked: false,
        type: 'custom',
        component: goodsAutoCompleteNew,
        key: 'commodity_name',
        label: '商品搜索',
        onChange(value) {
          return {
            value,
            stop: true,
          };
        },
      },
      {
        checked: false,
        label: '拆单规则',
        type: 'custom',
        component: SplitRuleCascader,
        key: 'split_rule_id',
        show: this.isOpenSplitOrder,
        defaultValue: [],
        props: {
          splitProviderFormat: true,
          placeholder: '请选择拆单规则',
        },
      },
      {
        show: () => this.isOpenCustomerFieldCustomize,
        checked: false,
        width: 'auto',
        type: 'custom',
        name: '客户自定义字段',
        key: ['user_customize_id', 'user_customize_field_select_config_ids'],
        defaultValue: [],
        props: {
          customizeType: '14',
          label: '客户自定义字段',
        },
        component: CustomizeCascader,
      },
      {
        checked: false,
        block: true,
        type: 'custom',
        data: [],
        component: CheckboxGroup,
        defaultValue: [],
        key: 'pay_way',
        label: '支付方式',
        style: {
          width: '900px',
        },
        props: {
          data: [],
        },
        onChange(value) {
          return {
            value,
            stop: true,
          };
        },
      },
      {
        // required:true,
        relation: 'order_tag',
        label: '标签筛选',
        type: 'custom',
        show: false,
        key: 'order_tag_filter',
        component: RadioGroup,
        props: {
          data: [
            {
              label: '同时存在',
              value: '1',
            },
            {
              label: '存在一个',
              value: '2',
            },
            {
              label: '无标签',
              value: '3',
            },
          ],
          on: {
            'on-reset': (value) => {
              // 重置时恢复订单标签可选
              this._setOrderTagDisabled(+value === 3);
            },
          },
        },
        defaultValue: '1',
        style: {
          width: '900px',
        },
        onChange: (value) => {
          // 选中标签筛选中的无标签之后，订单标签筛选项不可选择
          this._setOrderTagDisabled(+value === 3);
          return {
            value,
          };
        },
      },
      {
        // required:true,
        checked: true,
        block: true,
        tagTopStart: true,
        label: '订单标签',
        relation: 'order_tag',
        type: 'custom',
        key: 'order_tag',
        show: false,
        attrs: {
          disabled: false,
          keyName: 'order_tag',
        },
        component: orderTag,
        onChange(value) {
          return {
            value,
            stop: true,
          };
        },
      },
      {
        checked: false,
        block: true,
        required: false,
        show: false,
        label: '客户标签',
        key: 'user_tag',
        type: 'custom',
        defaultValue: [],
        props: {
          data: [],
        },
        style: {
          minWidth: '900px',
        },
        component: CheckboxGroup,
        onChange: (value) => {
          return { value, stop: true };
        },
      },
    ];
    this.userAuditService = new UserAudit();
    this.initQueryParams();
  },
  activated() {
    if (this.init) {
      this.refreshList();
    } else {
      this.init = true;
    }
    this.columns = this.originCols;
    const filters = this.advanceItems;
    this.getUserTagList(filters);
    this.getSearchConfig(filters);
    this.getUserTypeList(filters);
    let _this = this;
    _this.advanceItems.forEach((item) => {
      if (item.key === 'order_tag' || item.key === 'order_tag_filter') {
        item.show = this.isOpenOrderTag === true;
        item.show = this.isOpenOrderTag === true;
      }
      if (item.key === 'storage_id') {
        item.show = this.isEnableMultiStore === true;
      }
    });
    this.printTemplateSelectionBox = this.billPrintNoPopTplWindow;
    this.priorityTemplate = this.billPrintUseCustomerTpl;
    // 加载xslx相关资源
    if (typeof window.loadXSLX === 'function') {
      window.loadXSLX();
    }
  },
  deactivated() {
    this.tooltipDis = true;
  },
  mounted() {},
  destroyed() {
    this.tooltipDis = true;
  },
  computed: {
    openUseInvoiceTemplate() {
      return +this.sysConfig.open_bill_delivery_print === 1;
    },
    selectedShouldPrice() {
      const selectedRows = this.selectedOrders;
      let price = 0;
      selectedRows.forEach((row) => {
        if (!row.refer_no.includes('合计')) {
          // 排除合计行
          price += Number(row.should_price);
        }
      });
      return toDecimal(price, 2);
    },
  },
  methods: {
    showTooltip() {
      this.tooltipDis = false;
    },
    freightClose() {
      this.$refs.freight.close();
      this.freightErrTxt = '';
    },
    jumpToAuditList() {
      storage.setLocalStorage('audit-list-type', 0);
      this.$router.push('audit-list');
    },
    initQueryParams() {
      this.initParams = {
        bill_status: '1,2',
        start_date: DateUtil.getBeforeDate(30),
        end_date: DateUtil.format(DateUtil.getTodayDate(), 'YYYY-MM-DD'),
        split_rule_id: [],
      };
    },
    printCategorySummaryPrint() {
      this.auditListCategorySummaryPrintNew({
        id: this.selectedOrders.map((item) => item.id).join(','),
        accounting_date: `${this.requestParams.start_date}-${this.requestParams.end_date}`,
        print_time: DateUtil.getTodayDate(),
      });
    },
    _onCancel() {
      this.exportExcels.show = false;
      this.exportAll.show = false;
    },
    _assistPrintAuditOrder() {
      // 如果开启了汇率设置显示选择打印简体还是繁体的模态框
      if (this.isOpenExchangeRate) {
        this.showChooseChineseModal = true;
      } else {
        this._handlePrintAuditOrder();
      }
    },
    _handlePrintAuditOrder() {
      this._printAuditOrder({
        refer_no: this.printReferNos,
        is_traditional: this.printMode === TRADITIONAL,
      });
      // 还原选项
      this.printMode = SIMPLE;
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    async _saveTemplate(tplId, isMerge, isBatch) {
      // 新版暂时没有orderTpl先传空，后面有了使用下面的
      // let params = {
      //   soaTpl: this.isUerAuditPrintUseOrderTemplate ? '' : tplId,
      //   orderTpl: this.isUerAuditPrintUseOrderTemplate ? tplId : '',
      // };
      let params = {
        soaTpl: tplId,
        orderTpl: '',
      };
      let { status, message } = await post(api.saveTplSel, params);
      if (status) {
        let order_id = [];
        if (isBatch) {
          this.selectedOrders.forEach((item) => {
            if (+item.type === 1 || +item.type === 2) {
              order_id.push(item.order_id);
            }
          });
          if (order_id.length == 0) {
            this.warningMessage('没有可打印的销售订单');
            return;
          }
        }
        this.$Message.success('操作成功！');
        this._printsData(0, isMerge, tplId, isBatch);
      } else {
        this.$Message.error(message);
      }
    },
    /**
     * @description: 用于修改优先使用客户指定模板配置以及不再弹出打印模板选择框配置
     * @author: Jeff
     */
    async _configurationModification() {
      const templates = [
        {
          key: 'bill_print_use_customer_tpl',
          value: this.priorityTemplate ? 1 : 0,
        },
        {
          key: 'bill_print_no_pop_tpl_window',
          value: this.printTemplateSelectionBox ? 1 : 0,
        },
      ];
      let params = {
        config_list: JSON.stringify(templates),
      };
      let { message, status } = await post(api.saveSystemConfig, params);
      if (status) {
        this.$Message.success('配置修改成功！');
      } else {
        this.$Message.error(message);
      }
    },
    _printsData(isExport = 0, is_merge, tplId, isBatch) {
      let checked = [];
      let order_id = [];
      let list = this.$refs.table.getData();

      if (isBatch) {
        this.selectedOrders.forEach((item) => {
          if (+item.type === 1 || +item.type === 2) {
            checked.push(item.refer_no);
            order_id.push(item.order_id);
          }
        });
        if (order_id.length == 0) {
          this.warningMessage('没有可导出的销售订单');
        }
        if (isExport && checked.length > 20) {
          this.$smessage({
            type: 'error',
            text: '一次导出的数据不能超过20条',
          });
          return false;
        }
      } else {
        checked = [this.curPrintRow.refer_no];
        order_id = [this.curPrintRow.order_id];
      }

      if (order_id.length == 0) {
        this.warningMessage('没有可导出的销售订单');
      }
      if (isExport && checked.length > 20) {
        this.$smessage({
          type: 'error',
          text: '一次导出的数据不能超过20条',
        });
        return false;
      }
      // 适配新版打印,_mixin混入
      this._mxPrintSoaOrder({
        className: 'batchPrinting-SOA',
        referNo: checked.toString(),
        isExport: isExport,
        template_id: tplId,
        is_preview: !isBatch,
        is_merge: is_merge || 0,
      });
    },
    async determine() {
      let params = {
        bill_no: this.bill_no,
        freight_price: this.freight_price,
      };
      let { status, message } = await get(
        api.freight.confirmFreightBill,
        params,
      );
      if (status == 1) {
        this.$Notice.success({ title: '操作成功' });
        this.freightClose();
        this.refreshList();
      } else {
        this.freightErrTxt = message;
        // this.$Notice.success({ title: message });
      }
    },
    serviceChargeModalClose() {
      this.$refs.ServiceChargeModalRef.close();
      this.service_charge = '';
    },
    async confirmServiceChargeBill() {
      let url = '/superAdmin/accountBill/confirmServiceChargeBill';
      let params = {
        bill_no: this.bill_no,
        service_charge: this.service_charge,
      };
      const { status, message } = await this.$request.post(url, params);
      if (status == 1) {
        this.$Notice.success({ title: '操作成功' });
        this.serviceChargeModalClose();
        this.refreshList();
      } else {
        this.serviceChargeErrTxt = message;
      }
    },
    freightBill(data) {
      this.freight_price = data.freight_price;
      this.$refs.freight.open();
    },
    serverBill(data) {
      this.service_charge = Math.abs(data.should_price);
      this.serviceChargeErrTxt = '';
      this.$refs.ServiceChargeModalRef.open();
    },
    formatParams(params) {
      let requestPrams = this.deepClone(params);
      if (params.pay_way) {
        requestPrams.pay_way = params.pay_way.join(',');
      } else {
        requestPrams.pay_way = '';
      }
      if (Array.isArray(params.user_tag))
        requestPrams.user_tag = params.user_tag.join();
      requestPrams.is_receipt =
        requestPrams.is_receipt === '-1' ? '' : requestPrams.is_receipt;
      requestPrams.is_locked =
        requestPrams.is_locked === '-1' ? '' : requestPrams.is_locked;
      requestPrams.receivable_style_id =
        requestPrams.receivable_style_id === '-1'
          ? ''
          : requestPrams.receivable_style_id;
      if (requestPrams.user_id) requestPrams.search_value = '';
      if (Array.isArray(requestPrams.split_rule_id))
        requestPrams.split_rule_id = requestPrams.split_rule_id.join(',');
      return { ...requestPrams, ...this.filters };
    },
    beforeRequest(params) {
      // if (!params.start_date || !params.end_date) {
      //   return false;
      // }
      const auditListState =
        StorageUtil.getLocalStorage('finance-user-audit-list-state') || '';
      if (auditListState) {
        params.settlement_status = auditListState;
      }
      let result = this.formatParams(params);
      this.requestParams = result;
      return result;
    },
    afterRequest(response) {
      this.extraSelection = {};
      return response;
    },
    beforeSetData(params) {
      params.forEach((row) => {
        // 合计行不展示勾选和展开
        if (row.refer_no.includes('合计')) {
          row._summary = true;
        }
        row._empty = !row.detail || row.detail.length === 0;
      });
      // this.setCheckAble(params);
      return params;
    },
    handleResizeColumn() {
      this.$forceUpdate();
    },
    getSearchConfig(filters) {
      this.$request
        .get(this.apiUrl.getDriverList, { pageSize: 9999 })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            const driver = filters.find((item) => item.key === 'driver_id');
            if (driver) {
              driver.data = data.list.map((item) => ({
                label: item.driver_name,
                value: item.driver_id,
              }));
              driver.data.unshift({ label: '全部', value: 0 });
            }
          }
        });
      this.userAuditService.getAuditListSearchConfig().then((res) => {
        if (res.status) {
          let data = res.data;
          const keyMap = {
            settle_status: 'settle_status',
            line_id: 'line',
            group_id: 'group',
            salesman: 'sales_list',
            storage_id: 'user_warehouse',
            bill_status: 'bill_status',
            pay_way: 'pay_way',
            bill_type: 'bill_type',
          };
          filters.forEach((item) => {
            if (keyMap[item.key]) {
              item.data = data[keyMap[item.key]].map((item) => ({
                label: item.name,
                value: item.id,
              }));
              if (item.key === 'pay_way') {
                item.props.data = item.data;
              }
              if (item.key === 'bill_status' || item.key === 'storage_id') {
                item.data.unshift({
                  label: '全部',
                  value: 0,
                });
              }
              if (item.key === 'salesman') {
                item.data.unshift({
                  label: '未设置',
                  value: 0,
                });
                item.data.unshift({
                  label: '全部',
                  value: '',
                });
              }
            }
          });
        }
      });
    },
    async getUserTypeList(filters) {
      let res = await common.getUserTypeList({ pageSize: 999 });
      let { status, data } = res;
      if (status) {
        const result = filters.find(
          (item) => item.key === 'receivable_style_id',
        );
        if (result) {
          result.data = data.list.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          result.data.unshift({
            label: '全部',
            value: '-1',
          });
        }
      }
    },
    async getUserTagList(filters) {
      let res = await common.getUserTagList();
      if (res.status && res.data && res.data.length) {
        let tagFilter = filters.find((item) => item.key === 'user_tag');
        if (tagFilter) {
          tagFilter.show = true;
          tagFilter.props.data = res.data.map((item) => ({
            value: item.id,
            label: item.name,
          }));
        }
      }
    },
    handleColChange(cols) {
      this.showCols = cols;
      let showCols = this.initCols(cols, this.originCols);
      let expandCol = this.originCols.find((col) => col.type === 'expand');
      let selectCol = this.originCols.find((col) => col.type === 'selection');
      let actionCol = this.originCols.find((col) => col.key === 'action');

      showCols.unshift(selectCol);
      showCols.unshift(expandCol);
      if (!showCols.some((col) => col.key === 'action')) {
        showCols.push(actionCol);
      }

      this.cols = showCols;
    },
    auditLock(params, otherParams = {}) {
      const unlock = +otherParams.is_lock === 0;
      this.$request
        .post(
          unlock ? this.apiUrl.userAuditUnlock : this.apiUrl.userAuditLock,
          { refer_no: params.row.refer_no, ...otherParams },
        )
        .then((res) => {
          if (res.status === 1) {
            this.successMessage(unlock ? '反锁定成功' : '锁定成功');
            this.refreshList();
          } else {
            this.errorMessage(
              res.message || (unlock ? '反锁定失败' : '锁定失败'),
            );
          }
        });
    },
    unaudit(params) {
      this.$request
        .post(this.apiUrl.accountAntiBill, { bill_id: params.row.id })
        .then((res) => {
          if (res.status === 1) {
            this.successMessage('反对账成功');
            this.refreshList();
          } else {
            this.errorMessage(res.message || '反对账失败');
          }
        });
    },
    auditDetail(params) {
      let { row } = params;
      // 对账单详情
      this.$router.push({
        path: '/finance/userAuditDetail',
        query: {
          keep_scroll: 1,
          is_can_print: row.is_can_print,
          refer_no: row.refer_no,
          type: row.type,
          page_type: 'new',
          is_can_bill: row.is_can_bill
        },
      });
    },
    auditCompleteOrder() {
      this.userAuditService
        .completeOrder({ order_id: this.auditRow.order_id })
        .then((res) => {
          let { status, message } = res;
          if (status) {
            this.toAudit(this.auditRow);
          } else {
            this.modalError(message);
            this.auditRow = {};
          }
        });
    },
    audit(params) {
      let { row } = params;
      this.bill_no = row.bill_no;
      if (+row.type === 3) {
        // 订单运费单据 对账
        this.freightBill(row);
        return;
      }
      if (+row.type === 7) {
        // 服务费单据 对账
        this.serviceTipsTxt = '输入服务费收款金额：';
        this.serverBill(row);
        return;
      }
      if (+row.type === 9) {
        // 服务费单据 对账
        this.serviceTipsTxt = '输入服务费退款金额：';
        this.serverBill(row);
        return;
      }
      if (row.is_tips_order_complete) {
        this.auditRow = row;
        this.auditCompleteOrder();
      } else {
        this.toAudit(row);
      }
    },
    toAudit(row) {
      this.$router.push({
        path: '/finance/userAudit',
        query: {
          keep_scroll: 1,
          refer_no: row.refer_no,
          type: row.type,
          page_type: 'new'
        },
      });
    },
    closeSettlementRecord() {
      this.settlementRecordModal.show = false;
    },
    settlementRecord(params) {
      let { row } = params;
      this.settlementRecordModal.username = row.user_name;
      this.settlementRecordModal.orderId = row.order_id;
      this.settlementRecordModal.show = true;
    },
    payment(params) {
      let { row } = params;
      let noArr = [];
      noArr.push(row.refer_no);
      if (row.detail) {
        row.detail.forEach((item) => {
          if (
            item.is_can_settle ||
            (item.is_can_check && item.refer_no.startsWith('RT')) // 退货单
          ) {
            noArr.push(item.refer_no);
          }
        });
      }
      this.openSettlement(noArr.join(','));
    },
    handleSettlementOk() {
      this.closeSettlement();
      this.refreshList();
      this.cancelCheckAll();
    },
    openSettlement(nos) {
      if (nos) {
        this.settlement.no_list = nos;
      }
      this.settlement.show = true;
    },
    closeSettlement() {
      this.settlement.show = false;
    },
    /**
     * @description: 批量标记为已对账
     */
    $_onBatchMarkAsReconciled() {
      const selectedRows = this.selectedOrders;
      let bill_no = selectedRows
        .filter((e) => e.is_can_bill)
        .map((item) => item.bill_no)
        .join(',');
      if (!bill_no) {
        this.warningMessage('所选单据都已完成对账');
        return;
      }
      this.userAuditService
        .batchMarkAsReconciled({
          bill_no,
        })
        .then(({ status, message }) => {
          if (status) {
            this.successNotice('批量标记为已对账成功');
            this.refreshList();

            this.cancelCheckAll();
          } else {
            this.errorNotice(message);
          }
        });
    },
    batchPayment() {
      let nos = [];
      this.selectedOrders.forEach((item) => {
        if (item.is_can_settle) {
          nos.push(item.refer_no);
        }
      });
      if (!nos || nos.length == 0) {
        this.warningMessage('所选单据都已结算');
        return false;
      }
      this.settlement.no_list = nos.join(',');
      this.openSettlement();
    },
    onBatchLock() {
      let refer_no =
        this.selectedOrders
          .filter((item) => item.is_can_locked)
          .map((item) => item.refer_no)
          .join(',') || [];
      if (!refer_no.length) {
        this.warningMessage('没有可锁定的单据！');
        return;
      }
      this.$request
        .post(this.apiUrl.batchLockCustomerBill, { refer_no })
        .then((res) => {
          const { status, message } = res;
          if (status) {
            this.successMessage('批量锁定成功！');
            this.refreshList(false);
          } else {
            this.warningMessage(message || '批量锁定失败');
          }
        });
    },
    selectAllSameUser() {
      const list = this.$refs.table.getData();
      let ids = new Set();
      this.selectedOrders.forEach((res) => {
        ids.add(res.user_id);
      });
      if (ids.size == 0) {
        this.modalError({
          content: '请先选择一个客户!',
        });
        return false;
      }
      for (let key of ids.keys()) {
        list.forEach((item) => {
          if (item.user_id === key) {
            item._checked = true;
          }
          if (!item.detail) {
            return false;
          }
          // 选中附属单据
          item.detail.forEach((itemDetail) => {
            if (item.user_id === key) {
              itemDetail._checked = true;
            }
          });
        });
      }
      let selectedOrders = [];
      // 更新选中数据
      list.forEach((item) => {
        if (item._checked) {
          selectedOrders.push(item);
        }
      });
      this.selectedOrders = selectedOrders;
    },
    handleSelectionChange(selection, item, params) {
      if (!params) {
        return;
      }
      this.selectedOrders = selection;
    },
    /**
     * 设置表格中可选的选项
     */
    setCheckAble(list) {
      const selectList = this.selectedOrders.map((item) => item.refer_no);
      list.forEach((item) => {
        // item._disabled = !item.is_can_check;
        item._checked = selectList.indexOf(item.refer_no) !== -1;
      });
      // this.$refs.table.setData(list);
    },
    cancelCheckAll() {
      this.selectedOrders = [];
      const list = this.$refs.table ? this.$refs.table.getData() : [];
      this.$refs.table.selection = [];
      list.forEach((item) => {
        item._checked = false;
        if (item.detail && item.detail.length) {
          item.detail.forEach((d) => {
            d._checked = false;
          });
        }
      });
    },
    refreshList() {
      this.$refs.table.fetchData(false, true, 'bar');
    },
    _setOrderTagDisabled(_disabled) {
      this.advanceItems.forEach((item) => {
        if (item.key === 'order_tag') {
          item.attrs.disabled = _disabled;
          if (_disabled) {
            // 清空筛选项
            this.$refs.table.setValue('order_tag', [], true);
          }
        }
      });
    },
    handleExportParams(params) {
      if (params.export_type === this.exportType.user) {
        params.export_type = this.summaryValue;
      }
      return params;
    },
    handleExport(exportType) {
      if (exportType === exportType.print) {
        return;
      }
      // if (exportType === this.exportType.user) {
      //   this.exportModal.show = true;
      //   this.exportModal.params = this.requestParams;
      //   return;
      // }
      this.exportExcel(exportType);
    },
    async exportExcel(exportType) {
      const url = api.userAuditListExportNew;

      if (exportType === this.exportType.all) {
        this.exportAll.show = true;
        this.exportAll.params = this.formatParams(this.$refs.table.getParams());
        this.exportAll.params.export_type = exportType;
        return;
      } else if (exportType === 'detail' || exportType === 'user_detail' || exportType === this.exportType.user) {
        this.exportExcels.show = true;
        this.exportExcels.params = this.formatParams(
          this.$refs.table.getParams(),
        );
        this.exportExcels.params.export_type = exportType;
        if (exportType === this.exportType.user) {
          this.exportExcels.storage_key = 'new_account_bill_user_export';
          this.exportExcels.type = 'new_account_bill_user_export';
          this.exportExcels.title = '按客户导出'
          this.exportExcels.showExportTotal = false;
          this.exportExcels.api = url
        } else {
          this.exportExcels.showExportTotal = true;
          this.exportExcels.api = this.apiUrl.userAuditListExportNew
          this.exportExcels.storage_key = 'account_export_column_new';
          this.exportExcels.type = 'account_bill_detail_new_export';
          this.exportExcels.params.delivery_status = 1;
          this.exportExcels.title =
            exportType === 'detail' ? '对账明细导出' : '按客户导出明细';
        }
        return;
      }
      let params = this.formatParams(this.$refs.table.getParams());
      // 对账单+明细+分类汇总
      if (exportType === this.exportType.statement_detail_summary) {
        // 对账单详情字段由后台自己获取
        // 对账明细导出选中的字段
        let selectCols = StorageUtil.getLocalStorage(
          'account_export_column_new',
        );
        // 本地没缓存，取默认
        if (!selectCols) {
          const resp = await this.$request.get(
            this.apiUrl.userAuditListExportNew,
            { type: 'account_bill_detail_new_export' },
          );
          selectCols = resp.data.selected_column;
        }
        params.selected_columns = JSON.stringify(selectCols);
        params.ab_order_export_blank_line =
          StorageUtil.getLocalStorage('ab_order_export_blank_line') || 0; // 带客户每日小计，1-是，0-否
        params.is_user_date_total = StorageUtil.getLocalStorage(
          'is_user_date_total',
        )
          ? 1
          : 0;
        params.delivery_status = 1;
      }
      params.export_type = exportType;
      this.$request.get(url, params).then((res) => {
        let { status, message, data } = res;
        if (status) {
          this.$store.commit('showTaskCenter', true);
          exportLoop(data.task_no);
        } else {
          this.$Message.warning({
            content: message || '导出失败',
            duration: 5,
          });
        }
      });
    },
    handleResetChange() {
      this.filter.user_id = '';
    },
    /**
     * @description: 批量打印 printTemplateSelectionBox为选中状态则直接批量打印，反之弹出打印模版选择框
     * @author: Jeff
     */
    async _prints(isBatch) {
      if (isBatch) {
        if (
          !this.selectedOrders.filter((e) => +e.type === 1 || +e.type === 2)
            .length
        ) {
          this.warningMessage('没有可打印的销售订单');
          return;
        }
      }

      // 调用用户信息接口获取用户指定的模板
      const res = await common.getUserDetail(this.curPrintRow.user_id);
      let defaultBillId = null;
      if (res.status && !this.openUseInvoiceTemplate) {
        const { bill_order_tpl_id } = res.data;
        if (+bill_order_tpl_id) defaultBillId = bill_order_tpl_id;
      }
      this.$refs.printTemplateBatchChoose.open(
        (template_id, preview, sheet, isMerge) => {
          this.$refs.printTemplateBatchChoose.closeModal();
          this._saveTemplate(template_id, isMerge, isBatch);
        },
        defaultBillId,
      );
    },
  },
};
</script>

<style lang="less" scoped>
.common {
  .freight {
    .ivu-input-wrapper {
      padding: 10px 0 5px 0;
    }
    .err-txt {
      font-size: 12px;
      height: 18px;
      color: #f13130;
    }
  }
  .more-btn {
    .sui-icon {
      font-size: 12px;
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover .sui-icon {
      transform: rotate(180deg);
    }
  }
  /deep/ .icon-tips {
    margin-left: 4px;
    color: rgba(0, 0, 0, 0.85);
    vertical-align: baseline;
    line-height: 1;
    cursor: pointer;
  }
  /deep/ .groupFilter-custom {
    width: 219px;
  }
}
.ml10 {
  margin-left: 10px;
}
.modelHeaders {
  height: 30px;
  display: flex;
  align-items: center;
}
.modelFooter {
  height: 30px;
}
.aic {
  display: flex;
  align-items: center;
}
.export-modal-extra {
  padding: 6px 0 0 24px;
  border-top: 1px solid #f0f2f0;
  transform: translateY(26px);
}
</style>
