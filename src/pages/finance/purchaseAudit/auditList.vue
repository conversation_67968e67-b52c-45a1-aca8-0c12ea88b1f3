<template>
  <div :class="{'audit-alert': isOpenHelp && showTips}">
    <div class="audit-alert__tips">
      <alert-popup
        text="资金分账功能上线！客户支付后，账款按比例自动分给供应商"
        :link="true"
        link-text="点击了解更多"
        link-url="https://www.yuque.com/sdongpo/school/fz"
        :closeable="true"
        service-name="资金分账"
        @ok="onJustClose"
        @cancel="onNeverShow"
        v-if="isOpenHelp && showTips"
      >
      </alert-popup>
    </div>
    <div class="auditList-wrap">
      <ListTable
        tableId="purchase_audit_01"
        :auto-load-data="true"
        :debounceOptions="{leading: true, trailing: false}"
        :initParams = initParams
        ref="auditListTable"
        @on-selection-change="handleSelectionChange"
        :filters="filters"
        :border="false"
        :outer-border="true"
        :max-line="2"
        row-key="id"
        :before-request="beforeRequest"
        :before-set-data="beforeSetData"
        :after-request="afterRequest"
        :filter-items="filterItems"
        :advance="true"
        :advance-items="advanceItems"
        :keepScroll="true"
        :columns="columns"
        :data-provider="apiUrl.purchaseAuditList"
        :autoLoadData="true"
        :height="getTableHeight() - 50"
        :pageSizeOpts="[10,20,30,100,300]"
        @on-sort-change="handleSortChange"
        :isOpenCustom="true"
      >
        <Dropdown slot="button" @on-click="handleExport">
          <Button>
            导 出
            <Icon size="mini" icon="arrow-down" />
          </Button>
          <DropdownMenu slot="list">
            <DropdownItem :name="exportType.list.key">对账列表导出</DropdownItem>
            <DropdownItem :name="exportType.detail.key">对账详情导出</DropdownItem>
            <DropdownItem :name="exportType.summary.key">汇总详情导出</DropdownItem>
            <DropdownItem :name="exportType.provider.key">按采购员/供应商对账明细导出</DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <div slot="batch-operation" class="no_custom">
      <span class="mr10" style="margin-left: -25px; font-weight: 500">（应付总金额:
        <span style="color: #ff6e00;">{{selectedBillPriceTotal}}</span>）
      </span>
          <Button class="mr8" @click="selectAllSameUser">全选相同采购员/供应商单据</Button>
          <Poptip
            title="将选中的采购单的对账状态记为已对账？"
            confirm
            placement="bottom"
            :width="210"
            word-wrap
            @on-ok="$_onBatchMarkAsReconciled">
            <Button class="mr8">批量标记为已对账</Button>
          </Poptip>
          <Button class="mr8" @click="batchPayment">批量结算</Button>
          <Button class="mr8" @click="batchLockModal=true" v-if="isOpenPurchaseBillLock">批量锁定</Button>
          <Button class="mr8" @click="batchPrint">批量打印</Button>
          <Button class="mr8" @click="printCategorySummaryPrint">打印商品分类汇总</Button>
          <Button v-if="isPurchaseUseOrderCommodityTag" @click="showTagModal">修改订单商品标签</Button>
          <Button @click="bathPrintPurchasePayOrder">打印付款单</Button>
        </div>
      </ListTable>
      <settlement
        ref="settlement"
        :show-modal="settlement.show"
        :no-list="settlement.no_list"
        :purchase-type="+settlement.purchase_type"
        @on-ok="handleSettlementOk"
        @on-cancel="closeSettlement"
      ></settlement>
      <Modal
        v-model="tagModal.show"
        title="修改订单商品标签"
        width="400"
      >
        <Form
          inline
          label-colon
          :label-width="110"
          :model="tagModal.formData"
          :rules="tagModal.rules"
          ref="tagModalFormRef"
        >
          <FormItem v-if="isPurchaseUseOrderCommodityTag" label="订单商品标签" prop="tag_id" :rules="tagRules">
            <Select
              style="width: 230px;"
              v-model="tagModal.formData.tag_id"
              clearable
              transfer
              placeholder='请选择'
            >
              <Option v-for="tag in tagList" :value="tag.id" :key="tag.id" :label="tag.name">
              </Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button @click="tagModal.cancel">取消</Button>
          <Button type="primary" @click="tagModal.confirm">确定 </Button>
        </div>
      </Modal>
    </div>
    <ExportExcel
      :show="exportModal.show"
      :title="exportModal.title"
      :api="apiUrl.purchaseAuditListExport"
      :params="exportModal.params"
      :fieldsType="exportModal.fieldsType"
      :storeKey="exportModal.storeKey"
      :handleParams="handleExportParams"
      @on-cancel="exportModal.show = false;"
    >
    <div class="pl24 pr24 mb12" slot="header" v-if="exportModal.fieldsType === 'purchase_bill_export_summary'">
      <div class="export-excel-title">排序规则</div>
      <RadioGroup v-model="sortType">
        <Radio label="asc"> 按计划交货日期正序 </Radio>
        <Radio label="desc"> 按计划交货日期倒序 </Radio>
      </RadioGroup>
    </div>
    <div class="pl24 pr24 mb12" slot="header" v-if="exportModal.fieldsType === 'purchase_bill_export_list'">
      <div class="export-excel-title">小计规则</div>
      <Checkbox v-model="is_provider_or_agent_date_total" true-value="1" false-value="0">带采购员/供应商每日小计</Checkbox>
    </div>
    <div v-show="!!summaryValue" slot="extra-fields" class="export-modal-extra">
      <RadioGroup v-model="summaryValue">
        <Radio label="summary"> 按记账日期每日汇总导出 </Radio>
        <Radio label="summary_all"> 按查询结果汇总导出 </Radio>
        <Radio label="summary_price"> 按采购/供应商汇总（不同单价不同展示）</Radio>
      </RadioGroup>
    </div>
  </ExportExcel>
  <exportExcels
    :new-type="true"
    ref="exportExcels"
    :show="exportExcels.show"
    :title="exportExcels.title"
    :params="exportExcels.params"
    @on-cancel="exportExcels.show = false"
   />
  <Modal
    title="批量锁定"
    v-model="batchLockModal"
    @on-ok="onBatchLock"
    :width="416"
  >
    <div>锁定之后单据不能再对账，确定批量锁定多个对账单据？注意:锁定之后,对账单状态将自动变为"已对账"</div>
  </Modal>
</div>
</template>

<script>
import settlement from './settlement';
import Util from '@api/util.js';
import { api } from '@api/api.js';
import { get } from '@api/request.js';
import Goods from '@api/goods.js'
import PurchaseAudit from '@api/PurchaseAudit';
import LayoutMixin from '@/mixins/layout';
import DateUtil from '@/util/date';
import ListTable from '@components/list-table';
import InputTypeSearch from '@components/common/InputTypeSearch';
import StoreSelect from "@/components/common/storeSelect_new.vue"; // 仓库单选
import SettlementStatus from '@components/common/SettlementStatus';
import goodsAutoCompleteNew from '@components/common/goodsAutoComplete_new';
import purchaseCategorySummaryPrint from '@/mixins/print/purchaseCategorySummaryPrint';
import purchasePayOrderPrint from '@/mixins/print/purchasePayOrderPrint'
import PurchaseType from '@components/purchase-type/src/muti.vue';
import Icon from '@components/icon';
import CheckboxGroup from '@components/CheckboxGroup';
import ConfigMixin from '@/mixins/config.js'
import { Tooltip,Poptip } from 'view-design'
import TipIntro from './components/tip.vue'
import purchaseBillPrint from '@/mixins/print/purchaseBillPrint';
import PurchaseAgent from '@components/common/purchaseAgentSelect_new';
import ExportExcel from '@components/common/export-excel';
import AlertPopup from "@/components/alert-popup/index.vue";
import StorageUtil from '@util/storage.js'
import { toDecimal } from '@util/Number.js'
// 采购结算
import exportExcels from './components/ExportExcel.vue';
import GroupFilter from '@components/common/GroupFilter';
import UserSearch from '@/components/user/userSearch';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

let purchaseAuditService = new PurchaseAudit();

const exportType = {
  list: {
    key: 'list',
    title: '对账列表导出',
    type: 'purchase_bill_export_list',
  },
  detail: {
    key: 'detail',
    title: '对账详情导出',
    type: 'purchase_bill_export_detail',
  },
  summary: {
    key: 'summary',
    title: '汇总详情导出',
    type: 'purchase_bill_export_summary',
  },
  provider: {
    key: 'provider',
    title: '按采购员/供应商对账明细导出',
    type: 'provider_or_agent_detail',
  },
};
const PURCHASE_TYPE = {
  1: {
    value: '1',
    label: '市场自采',
    mode: 'agent'
  },
  2: {
    value: '2',
    label: '供应商直供',
    mode: 'direct_provider'
  },
  3: {
    value: '3',
    label: '指定供应商',
    mode: 'provider'
  },
  5: {
    value: '5',
    label: '联营供应商',
    mode: '-'
  }
}
export default {
  name: 'byInOrder',
  mixins: [
    LayoutMixin,
    purchaseCategorySummaryPrint,
    ConfigMixin,
    purchaseBillPrint,
    purchasePayOrderPrint
  ],
  components: {
    settlement,
    ListTable,
    StoreSelect,
    SettlementStatus,
    goodsAutoCompleteNew,
    Tooltip,
    Icon,
    TipIntro,
    Poptip,
    CheckboxGroup,
    ExportExcel,
    AlertPopup,
    exportExcels
  },
  data() {
    let _this = this
    return {
      showTips: false,
      batchLockModal: false,
      tagModal: {
        show: false,
        formData: {
          tag_id: '',
        },
        rules: {},
        cancel: () => {
          this.tagModal.show = false
        },
        confirm: () => {
          if(!this.tagModalFormCheck()) return
          this.batchAlterTag()
        }
      },
      tagRules: {
        required: false, message: '请选择订单商品标签', trigger: 'change'
      },
      tagList: [],
      summaryValue:'summary',
      sortType: 'desc',
      is_provider_or_agent_date_total: '0',
      visible:true,
      initParams: {},
      requestParams: {},
      searchConfig: {},
      filters: {},
      selectedOrders: [],
      exportType,
      settlement: {
        no_list: [],
        purchase_type: '',
        show: false
      },
      filterItems: [
        {
          required:true,
          checked:true,
          label: '创建日期',
          type: 'DatePicker',
          defaultValue: [DateUtil.getBeforeDate(89), DateUtil.getTodayDate()],
          key: ['start_time', 'end_time'],
          props: {
            type: 'daterange',
            placeholder: '请选择创建日期',
            clearable: true
          }
        },
        {
          // required:true,
          checked:true,
          component: InputTypeSearch,
          type: 'custom',
          key: ['searchType', 'searchValue'],
          defaultValue: ['source_no', ''],
          props: {
            placeholder: '请填写单号',
            data: [
              {
                label: '业务单号',
                value: 'source_no'
              },
              {
                label: '对账单号',
                value: 'bill_no'
              },
              {
                label: '出入库单号',
                value: 'refer_no'
              },
            ]
          },
          style: {
            width: '300px'
          },
        },
        {
          checked: false,
          required: false,
          type: 'custom',
          hasType: 'GroupFilter',
          style: {
            width: '299px',
          },
          key: ['no_type', 'link_order_user_name'],
          defaultValue: ['1', ''],
          props: {
            placeholder: '输入客户名称/编码 /订单号',
            selectData: [
              {
                label: '所属客户',
                placeholder: '输入客户名称/编码查询',
                value: '1',
                key: 'link_order_user_id',
              },
              {
                label: '所属订单号',
                placeholder: '输入订单号查询',
                value: '2',
                key: 'link_order_no',
              },
            ],
            customType: '1',
            customComp: () => UserSearch,
            customBind: {
              valueKey: 'id',
              labelKey: 'email',
              splitType: false,
              multiple: false,
              changeValueType: true,
            },
            on: {
              'on-enter': (value) => {
                if (value[0] === '1') this.filters.link_order_user_id = value[1] || '';
                if (value[0] === '2') this.filters.link_order_no = value[1] || '';
                this.$refs.auditListTable &&
                  this.$refs.auditListTable.fetchData();
              },
              'on-clear': () => {
                this.filters.link_order_user_id = '';
                this.filters.link_order_no = '';
              },
            },
          },
          component: GroupFilter,
          onChange: (value = '') => {
            // 输入框删除的是否, 也需要把link_order_user_id干掉
            if (value[0] === '1') {
              this.filters.link_order_user_id = value[1];
              this.filters.link_order_no = '';
            } else {
              this.filters.link_order_user_id = '';
              this.filters.link_order_no = value[1];
            }

            return { value };
          },
          show: () => {
            return this.sysConfig.tc_platform == 1 && this.sysConfig.is_open_split_order == 1 && this.sysConfig.split_order_mode == 3
          },
        },
      ],
      advanceItems: [{
        items: [
          {
            checked:true,
            type: "custom",
            component: StoreSelect,
            label: "仓库",
            key: "storage_id",
            props: {
              params: { type: 1 },
              showAll: true,
              defaultFirst: false,
              placeholder:'全部',
              defaultChange: false,
              // saveSession: true,
              // checkFoodProcess: false
            },
          },
          {
            checked:true,
            // required:true,
            type: 'custom',
            component: SettlementStatus,
            label: '结算状态',
            key: 'status',
            props: {
              placeholder: '请选择结算状态',
            },
            defaultValue: ''
          },
					{
						checked: true,
						label: '供应商/采购员',
						key: ['purchaseType2', 'purchaseValue2'],
						type: 'custom',
						muti: true,
						props: {
							data: [],
							mode: [
								'agent', // 市场自采
								'provider' // 指定供应商
							],
							customLabel: {
								agent: '采购员',
								provider: '供应商'
							},
							remote: true,
							placeholder: '请选择供应商/采购员',
						},
						component: PurchaseType,
						onChange: (value, selectedData, formatValue) => {
							return {
								value: [
									value[0],
									value[0] === '1'
										? formatValue.agent_id
										: formatValue.provider_id,
								],
								stop: false,
							};
						},
					},
					{
						checked: false,
						label: '采购模式',
						key: ["purchaseType", "purchaseValue"],
						type: 'custom',
						muti: true,
						component: PurchaseType,
						props: {
							placeholder: '请选择采购模式',
							mode: ["agent", "direct_provider"],
							params: {filter_disable_provider: 0, filter_disable_agent: 0}
						},
						onChange: (value, selectedData, formatValue) => {
							return { value:[value[0], value[0] === '1' ? formatValue.agent_id : formatValue.provider_id], stop: false }
						}
					},
          {
            checked:false,
            type: 'Select',
            label: '单据类型',
            defaultValue: '',
            key: 'type',
            props: {
              placeholder: '请选择单据类型'
            },
            data: [
              {
                value: '',
                label: '全部'
              },
              {
                value: 1,
                label: '采购收货'
              },
              {
                value: 2,
                label: '采购退货'
              },
							{
								value: 4,
								label: '采购费用'
							}
            ]
          },
          {
            // required:true,
            checked:true,
            label: '对账状态',
            type: 'Select',
            key: 'is_bill',
            defaultValue: '',
            props: {
              placeholder: '选择对账状态'
            },
            data: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '已对账',
                value: 'Y'
              },
              {
                label: '未对账',
                value: 'N'
              }
            ]
          },
          {
            checked:false,
            label: '锁定状态',
            type: 'Select',
            key: 'is_locked',
            defaultValue: '',
            props: {
              placeholder: '选择锁定状态'
            },
            data: [
              {
                label: '全部',
                value: ''
              },
              {
                label: '未锁定',
                value: 0
              },
              {
                label: '已锁定',
                value: 1
              }
            ]
          },
          {
            checked:false,
            type: 'custom',
            component: goodsAutoCompleteNew,
            key: 'commodity_name',
            label: '商品搜索',
            onChange(value) {
              return {
                value,
                stop: true
              };
            }
          },
          {
            checked:true,
            label: '记账日期',
            key: ['start_plan_date', 'end_plan_date'],
            type: 'DatePicker',
            props: {
              type: 'daterange',
              placeholder: '选择日期'
            },
            tooltip: {
              maxWidth: '200',
              content: '采购入库单：取计划交货日期\n采购退货单：取退货日期\n采购费用单：取付款日期'
            }
          },
          {
            // required:true,
            checked:true,
            label: '结算日期',
            key: ['settle_start_date', 'settle_end_date'],
            type: 'DatePicker',
            props: {
              type: 'daterange',
              placeholder: '选择日期'
            },
            onChange:(data) => {
              // 限制最多90天
              let settle_start_date = new Date(data[0].replace(/-/g, '/')).getTime();
              let settle_end_date = new Date(data[1].replace(/-/g, '/')).getTime();
              let day90 = 1000 * 60 * 60 * 24 * 90;
              if (settle_end_date - settle_start_date > day90) {
                this.errorNotice({
                  title: '采购结算日期最大支持筛选90天,请重新选择'
                });
                this.$refs.auditListTable.setValue(['settle_start_date', 'settle_end_date'], ['', '']);
                return {
                  value: ['', ''],
                  stop: true
                }
              } else {
                return {
                  value: data,
                  stop: false
                }
              }
            },
          },
          {
            checked:false,
            label: '制单人',
            type: 'Select',
            key: 'create_userid',
            defaultValue: '',
            props: {
              placeholder: '请选择制单人',
              filterable: true,
              clearable: true
            },
            data: []
          },
          {
              checked:false,
              label: '采购负责人',
              type: 'custom',
              key: 'provider_supervisor',
              component: PurchaseAgent,
              props: {
                data: [],
                placeholder: '全部',
                filterable: true,
                clearable: true
              }
          },
          {
            checked: true,
            required: true,
            label: '订单商品标签',
            key: 'tag_id',
            type: 'custom',
            show: false,
            noReset: true,
            component: CheckboxGroup,
            defaultValue: StorageUtil.getLocalStorage('purchaseAuditList-order-goods-tag-list') || [],
            props: {
              data: [],
            },
            style: {
              minWidth: '900px'
            },
            onChange: (value) => {
              StorageUtil.setLocalStorage('purchaseAuditList-order-goods-tag-list', value)
              return {
                value,
                stop: true
              };
            }
          },
          {
            checked:false,
            label: '备注',
            type: 'Input',
            key: 'remark',
            props: {
              placeholder: '搜索备注',
            }
          },
          {
            checked: false,
            label: '应付金额',
            type: 'RangeInput',
            defaultValue: ['', ''],
            key: ['min_price', 'max_price'],
          },
          {
            show: () => this.isOpenProviderFieldCustomize,
            checked: false,
            width: 'auto',
            type: 'custom',
            name: '供应商自定义字段',
            key: ['provider_customize_id', 'provider_customize_field_select_config_ids'],
            defaultValue: [],
            props: {
              customizeType: '7',
              label: '供应商自定义字段',
            },
            component: CustomizeCascader,
          },
        ]
      }],
      columns: [
        {
          type: 'titleCfg',
          width: 40,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'purchase_bill',
          style: {
            paddingRight: 0
          }
        },
        {
          type: 'selection',
          align: 'left',
          fixed: 'left',
          width: 60,
          className: 'table-select'
        },
        {
          title: '业务单号',
          key: 'source_no',
          fixed: 'left',
          width: 200,
          render: (h, params) => {
            let { row } = params;
						if (row.source_no === '费用单合计') {
							return h('div', [
								h('a', {
									class: 'tableLink',
									on: {
										click: () => {
											if (row._summary) return
											this.auditDetail(params)
										}
									}
								},row.source_no),
								h('Tooltip', {
									props: {
										transfer: true,
										placement: 'top',
										maxWidth: 800,
										content: '该合计仅计算了已审核不可对账的费用单，可对账的费用单会和其他对账单一样在当前页以及所有页中进行统计',
									}
								}, [
									h('Icon', {
										style: {
											fontSize: '14px',
											color: 'rgba(0,0,0,.75)',
											marginLeft: '5px',
											cursor: 'pointer'
										},
										class: 'tip-icon',
										props: {
											type: 'ios-help-circle',
										},
									})
								])
							])
						}
            return h('div', [
              h('a', {
                class: 'tableLink',
                on: {
                  click: () => {
                    if (row._summary) return
                    this.auditDetail(params)
                  }
                }
              },row.source_no),
              h(
                'p',
                {
                  style: {
                    color: '#666'
                  }
                },
                row.create_time
              )
            ]);
          }
        },
        {
          title: '仓库',
          key: 'store_name'
        },
        {
          title: '采购类型',
          key: 'purchase_type_text',
          minWidth: 90
        },
        {
          title: '计划交货日期',
          key: 'plan_date',
        },
        {
          title: '记账日期',
          key: 'accounting_date',
          sortable:true,
          width:100,
          renderHeader: (h, param,fun) => {
            return <div style="display:flex;align-items:center;" onClick={fun}>
               <div>记账日期</div>
                <Tooltip placement="top" transfer={true} class='sdp-table__tr-tip'>
                  <Icon size={12} style="cursor: pointer;margin-bottom: 3px;" icon="tips" />
                  <div slot="content" style="white-space: pre-wrap;width:170px;">
                    采购入库单：取计划交货日期
                    采购退货单：取退货日期
                  </div>
                </Tooltip>
            </div>
          },
        },
        {
          title: '采购员/供应商',
          key: 'agent_name',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              parseInt(row.purchase_type) === Util.purchaseType.typeAgent.value
                ? row.agent_name
                : row.provider_name
            );
          }
        },
				{
					title: '采购负责人',
					align: 'right',
					width: 150,
					key: 'provider_supervisor_name'
				},
        {
          title: '单据类型',
          key: 'type_text',
          minWidth: 90
        },
        {
          title: '应付金额',
          align: 'right',
          width: 150,
          key: 'bill_price'
        },
        {
          title: '已付金额',
          width: 120,
          align: 'right',
          key: 'settle_price'
        },
        {
          title: '实付金额',
          width: 120,
          align: 'right',
          key: 'settle_actual_price'
        },
        {
          title: '抹零金额',
          width: 120,
          align: 'right',
          key: 'reduction_price'
        },
        {
          title: '采购收货整单抹零',
          key: 'in_out_reduction_price',
          width: 120,
          align: 'right',
        },
        {
          title: '未付金额',
          width: 120,
          align: 'right',
          key: 'unpaid_price'
        },
        {
          title: '对账单号',
          key: 'bill_no',
          width: 180
        },
        {
          title: '对账状态',
          key: 'bill_status_desc',
          render: (h, params) => {
            let { row, index } = params,
              color = '',
              statusText = '',
              len = this.$refs.auditListTable.getData().length;
            // 最后三行合计不显示对账状态
            let exceptRows = [len - 1, len - 2, len - 3];
            if (exceptRows.includes(index)) {
              return false;
            }
            // 退货不显示对账状态
            // if (
            //   parseInt(params.row.type) ===
            //   PurchaseAudit.auditType.purchaseReturn
            // ) {
            //   return false;
            // }
            // 未结算或部分结算显示红色
            if (!row.bill_time) {
              color = 'red';
              statusText = '未对账';
            } else {
              statusText = '已对账';
            }
            return h(
              'span',
              {
                style: {
                  color: color
                }
              },
              statusText
            );
          }
        },
        {
          title: '对账人',
          key: 'bill_username',
          width: 90
        },
        {
          title: '制单人',
          key: 'create_username',
          width: 90
        },
        {
          title: '结算状态',
          key: 'status_text',
          render: (h, params) => {
            let { row } = params;
            let color = '';
            // 未结算或部分结算显示红色
            if (parseInt(row.status) !== PurchaseAudit.settleStatus.done) {
              color = 'red';
            }
            return h(
              'span',
              {
                style: {
                  color: color
                }
              },
              row.status_text
            );
          }
        },
				{
					title: '最后结算时间',
					align: 'right',
					width: 150,
					key: 'settlement_time'
				},
        {
          title: '锁定状态',
          key: 'lock_status'
        },
        {
          title: '仓库',
          key: 'storage_name',
          minWidth: 90
        },
        {
          title: '备注',
          key: 'remark',
          minWidth: 90
        },
        {
          title: '业务单据备注',
          key: 'transaction_remark',
          minWidth: 100
        },
        {
          title: '出入库单号',
          key: 'refer_no',
          width: 180,
          render: (h, params) => {
            let { row } = params;
            return h('div', [
              h('a', {
                class: 'tableLink',
                attrs: {
                  // 表明同一个标签同一个域会共享会话
                  rel: 'opener',
                  target: '_blank',
                  href: `#/${ +row.in_out_type ? 'storeRoom/outStoreDetail?keep_scroll=1&' : 'storeRoom/storeDetail?' }id=${row.in_out_id}`
                }
              }, row.refer_no)
            ]);
          }
        },
        {
          title: '采购员/供应商编码',
          key: 'purchase_person_code',
          minWidth: 100
        },
        {
          title: '订单商品标签', // 有表头设置，后端走配置项，前端可以不用走配置
          key: 'tag_name',
        },
        {
          title: '差异金额',
          key: 'diff_price',
        },
        {
          title: '所属客户',
          key: 'link_order_user_name',
          width: 100,
          show: () => {
            return this.sysConfig.tc_platform == 1 && this.sysConfig.is_open_split_order == 1 && this.sysConfig.split_order_mode == 3
          }
        },
        {
          resizable: false,
          type: 'action',
          key: 'action',
          fixed: 'right',
          width: 220,
          actions: (params) => {
            let actions = [];
            // to test
            if (params.row.is_can_locked) {
              actions.push({
                name: '锁定',
                confirm: '锁定之后单据不能再对账，是否确定锁定对账单据？（注意:锁定之后,对账单状态将自动变为"已对账）',
                action: () => {
                  this.purchaseAuditLock(params);
                }
              })
            }
            if (params.row.is_can_unlock) {
              actions.push({
                name: '反锁定',
                action: params => this.purchaseAuditLock(params, {is_lock: 0}),
                confirm: `反锁定后，该对账单状态将会按照最后一次锁定的对账金额变更为未锁定状态，是否确认反锁定？`
              })
            }
            // to test
            if (params.row.is_can_bill) {
              actions.push({
                name: '对账',
                action: () => {
                  this.audit(params);
                }
              })
            }
            if (params.row.is_can_anti_bill) {
              actions.push({
                name: '反对账',
                confirm: '反对账后，该对账单状态将会变更为未对账状态，是否确认反对账？',
                action: params => this.unaudit(params),
              })
            }
            // to test
            if (params.row.is_can_settle) {
              actions.push({
                name: '结算',
                action: () => {
                  this.openSettlement(params);
									this.$refs.settlement.date = DateUtil.getTodayDate();
									this.$refs.settlement.auditTime = DateUtil.getTodayDate();
                }
              })
            }
            actions.push({
              name: '导出',
              action: () => {
                this.purchaseAuditDetailExport(params);
              }
            })
            actions.push({
              action: () => {
                // mixin 混入
                this._mxPrintPurchaseBill({
                  ids: params.row.id
                });
              },
              name: '打印'
            })
            return actions;
          }
        },
      ],
      exportModal: {
        show: false,
        title: '',
        params: {},
        fieldsType: '',
        storeKey: ''
      },
      exportExcels: {
        show: false,
        params: {},
        title: '按供应商导出明细'
      },
    };
  },
  computed: {
    selectedBillPriceTotal () {
      let total = 0
      for (const item of this.selectedOrders.map(item => item.bill_price)) {
        total = total + Number(item)
      }
      return toDecimal(total, 2)
    }
  },
  created() {
    this.visible = window.localStorage.getItem('visibleDate')?false:true
    this.initAdvanceItems()
    // 处理activated导致的重复请求
    this.firstLoad = true
    this.initQueryParams()
  },
  activated() {
    console.log('activated-----1');
    this.showTips = localStorage.getItem('auditListTips') !== 'close';
    if (!this.firstLoad) {
      this.$refs.auditListTable.fetchData(false);
    }
  },
  mounted() {
    this.getSearchConfig();
    this.getOperatorList();
    this.getTagList()
    document.getElementsByTagName("body")[0].className="custom-body";
  },
  beforeDestroy(){
    document.body.removeAttribute("class","custom-body");
  },
  methods: {
    initAdvanceItems() {
      this.advanceItems = this.advanceItems.map(item => {
        item.items = item.items.map(_item => {
          if (_item.key === 'tag_id') {
            _item.show = this.isPurchaseUseOrderCommodityTag
          }
          return _item;
        })
        return item;
      });
    },
    showTagModal () {
      this.tagModal.show = true
    },
    getTagList () {
      if(!this.isPurchaseUseOrderCommodityTag) return
      this.tagRules.required = this.isOrderCommodityTagRequired
      Goods.getOrderGoodsTagList().then((data) => {
        this.tagList = data
        let defaultTag = this.tagList.find(item => item.is_default === '1')
        this.tagModal.formData.tag_id = defaultTag ? defaultTag.id : ''
        this.advanceItems = this.advanceItems.map(item => {
          item.items = item.items.map(_item => {
            if (_item.key === 'tag_id') {
              _item.props.data = this.tagList.map(item => {
                return {
                  label: item.name,
                  value: item.id
                }
              })
            }
            return _item;
          })
          return item;
        });
      })
    },
    tagModalFormCheck() {
      let valid = false
      this.$refs.tagModalFormRef.validate(formValid => {
      valid = formValid
      })
      return valid
    },
    async batchAlterTag() {
      const ids =  this.selectedOrders.filter(order => order.is_can_bill && order.type === '1').map(order => order.id)
      const params = {
        ids: ids.length>0 ? ids.join(',') : '',
        tag_id: this.tagModal.formData.tag_id
      }
      const res = await this.$request.post(this.apiUrl.purchaseBillBatchSetTag, params)
      if(res.status) {
        this.$smessage({
          type: 'success',
          text: '标签设置成功'
        })
        this.tagModal.show = false
        this.$refs.auditListTable.fetchData(false);
      } else {
        this.$smessage({
          type: 'error',
          text:res.message || '标签设置失败'
        })
      }
    },
    handleSortChange (columns, key, order) {
      let keyAlias = {
        plan_date: 'plan_date',
        accounting_date:'sign_date'
      }
      if (order) {
        this.filters.sort_field = keyAlias[key];
        this.filters.sort_type = order;
      } else {
        this.filters.sort_field = '';
        this.filters.sort_type  = '';
      }
      this.$refs.auditListTable.fetchData(false)
    },
    initQueryParams() {
      this.initParams = {
        start_time: DateUtil.getBeforeDate(89),
        end_time: DateUtil.getTodayDate(),
        date_type: 2
      }
    },
    batchPrint() {
      this._mxPrintPurchaseBill({
        ids: this.selectedOrders.map(item => item.id).join(','),
        isBatch:true
      });
    },
    bathPrintPurchasePayOrder() {
      let obj = {}
      this.selectedOrders.forEach(e=>{
        if(e.purchase_type==1){
          obj[e.agent_id] = e
        }else{
          obj[e.provider_id+'provider'] = e
        }
      })
      if(Object.keys(obj).length>1){
        this.warningMessage('仅支持同一个供应商/采购员打印！');
        return false
      }
      this._mxPrintPurchasePayOrder(this.selectedOrders.map(item => item.id).join(','))
    },
    printCategorySummaryPrint () {
      this.purchaseCategorySummaryPrint({
        id: this.selectedOrders.map(item => item.id).join(','),
        delivery_date: `${this.requestParams.start_time}-${this.requestParams.end_time}`,
        print_time: DateUtil.getTodayDate()
      })
    },
    // 批量结算
    batchPayment() {
      let nos = [];
      let purchase_type = ''
      let temporaryData = this.selectedOrders.filter(e=>(e.is_can_settle &&!e._summary))
      let obj = {}
      temporaryData.forEach(e=>{
        if(e.purchase_type==1){
          obj[e.agent_id] = e
        }else{
          obj[e.provider_id+'provider'] = e
        }
      })
      if(Object.keys(obj).length>1){
        this.warningMessage('仅支持同一个供应商/采购员结算！');
        return false
      }
      temporaryData.forEach(item => {
          nos.push(item.id);
          purchase_type = item.purchase_type
      });
      if (!nos || nos.length == 0) {
        this.warningMessage('没有可结算的单据！');
        return false;
      }
      this.settlement.no_list = nos;
      this.settlement.purchase_type = purchase_type
      this.openSettlement();
    },
    onBatchLock() {
      let refer_no =  this.selectedOrders.filter(item => item.is_can_locked).map(item => +item.type === 4? item.source_no : item.refer_no).join(',') || []
      if(!refer_no.length){
        this.warningMessage("没有可锁定的单据！")
        return
      }
      this.$request.post(this.apiUrl.batchLockPurchaseBill, { refer_no }).then(res => {
        const { status, message } = res;
        if (status) {
          this.successMessage('批量锁定成功！')
          this.refreshList(false)
        } else {
          this.warningMessage(message || '批量锁定失败')
        }
      });
    },
    openMore() {
      window.open('https://www.yuque.com/sdongpo/school/fz');
    },
    onJustClose() {
      this.showTips = false;
    },
    onNeverShow() {
      this.showTips = false;
      localStorage.setItem('auditListTips', 'close');
    },
    selectAllSameUser() {
      let obj = {}
      this.selectedOrders.forEach(e=>{
        if(e.purchase_type==1){
          obj[e.agent_id] = 1
        }else{
          obj[e.provider_id+'provider'] = 0
        }
      })
      if(Object.keys(obj).length>1){
        this.warningMessage("已选单据的采购员/供应商不能超过1个")
        return
      }
      let firstSelectedOrder = this.selectedOrders[0];
      let agentId = firstSelectedOrder.agent_id * 1;
      let providerId = firstSelectedOrder.provider_id * 1;
      let list = this.$refs.auditListTable.getData();
      list.forEach(item => {
        item._checked = false;
      });
      if (agentId) {
        list.forEach(item => {
          if (item.agent_id * 1 === agentId && !item._disabled) {
            item._checked = true;
          }
        });
      } else {
        list.forEach(item => {
          if (item.provider_id * 1 === providerId && !item._disabled) {
            item._checked = true;
          }
        });
      }
    },
    /**
     * @description: 批量标记为已对账
     */
    async $_onBatchMarkAsReconciled () {
      let ids =  this.selectedOrders.filter(e=>e.is_can_bill).map(item => item.id).join(',')
      if(!ids){
        this.warningMessage("没有可对账的单据！")
        return
      }
      const { status, message } = await this.$request.post(this.apiUrl.batchMarkBill, { ids })
      if (status) {
        this.successMessage(message || '批量标记为已对账成功！')
      } else {
        this.warningMessage(message || '批量标记为已对账失败')
      }
      this.refreshList(false)
    },
    handleExportParams (params) {
      if (params.export_type === 'summary') {
        params.export_type = this.summaryValue;
        params.sort_type = this.sortType;
        params.sort_field = 'plan_date';
      }

      if (params.export_type === 'list') {
        params.is_provider_or_agent_date_total = this.is_provider_or_agent_date_total;
      }
      return params
    },
    handleExport(type) {
      let params = this.requestParams;
      params.export_type = type;
      // 按供应商导出明细
      if (type === 'provider') {
        if (!params.provider_id && !params.agent_id) {
          this.errorNotice({ title: '导出失败', desc: '请选择采购员或供应商' });
          return
        }
        this.exportExcels.show = true;
        this.exportExcels.params = params;
        this.exportExcels.title = '按采购员/供应商对账明细导出'
        return
      }
      // 订单汇总详情导出弹窗
      this.summaryValue = type == 'summary' ? 'summary' : ''
      this.exportModal.params = params;
      this.exportModal.title = this.exportType[type].title;
      this.exportModal.fieldsType = this.exportType[type].type;
      this.exportModal.storeKey = this.exportType[type].type + '_column_store';
      this.exportModal.show = true
      return
    },
    handleSettlementOk () {
      let refreshPage = false;
      this.closeSettlement();
      // this.selectedOrders = [];
      this.refreshList(refreshPage);
    },
    closeSettlement() {
      this.settlement.show = false;
    },
    refreshList(refreshPage) {
      this.$refs.auditListTable.fetchData(refreshPage);
    },
    purchaseAuditDetailExport (params) {
      this.$request.post(this.apiUrl.purchaseAuditDetailExport, {id: params.row.id}).then(res => {
        if (res.status) {
          window.location.href = res.data[0]
        } else {
          this.errorNotice(res.message || '网络错误');
        }
      })
    },
    openSettlement(params) {
      if (params) {
        let { id, purchase_type } = params.row;
        this.settlement.no_list = [id];
        this.settlement.purchase_type = purchase_type
      }

      this.$request.get(this.apiUrl.getPurchaseSettlementData, { ids: this.settlement.no_list.join(',') }).then(res => {
        if (res.status) {
          this.settlement.show = true;
        } else {
          this.modalError(res.message);
        }
      });
    },
    audit(params) {
      let {
        row: { id, type }
      } = params;
      this.$router.push({
        path: '/finance/purchaseAudit',
        query: {
          id: id,
          type
        }
      });
    },
    purchaseAuditLock(params, otherParams = {}) {
      const unlock = +otherParams.is_lock === 0
			const no = +params.row.type === 4? params.row.source_no : params.row.refer_no
      this.$request.post(unlock ? this.apiUrl.purchaseAuditUnlock : this.apiUrl.purchaseAuditLock, {refer_no: no, ...otherParams}).then(res => {
        if (res.status) {
          this.successNotice(unlock ? '反锁定成功' : '锁定成功');
          this.$refs.auditListTable.fetchData(false);
        } else {
          this.errorNotice(res.message || (unlock ? '反锁定失败' : '锁定失败'));
        }
      })
    },
    unaudit(params) {
      this.$request
        .post(this.apiUrl.purchaseAntiBill, { bill_id: params.row.id })
        .then(res => {
          if (res.status === 1) {
            this.successMessage('反对账成功');
            this.refreshList();
          } else {
            this.errorMessage(res.message || '反对账失败');
          }
        });
    },
    auditDetail(params) {
      let {
        row,
        row: { id }
      } = params;
      if (parseInt(row.type) === PurchaseAudit.auditType.purchase) {
        this.$router.push({
          path: '/finance/purchaseAuditDetail',
          query: {
            id: id
          }
        });
      } else {
        this.$router.push({
          path: '/finance/purchaseReturnAuditDetail',
          query: {
            id: id
          }
        });
      }
    },
    beforeRequest (params) {
      // 处理搜索框入参数
      let searchType = params.searchType;
      let searchValue = params.searchValue;
      params[searchType] = searchValue;
      delete params.searchType;
      delete params.searchValue;
      // 处理采购模式入参
      if (+params.purchaseType2 === 0 && +params.purchaseType === 0) {
        params = Object.assign(params, {
					agent_id: '',
					provider_id: '',
					agent: '',
					provider: '',
					purchase_type: ''
        })
      } else {
        params.purchase_type = params.purchaseType;
				const purchaseType2 = params.purchaseType2;
        if (+purchaseType2 === 1) {
          params.agent_id = params.purchaseValue2
        }
        if (+purchaseType2 === 2 || +params.purchaseType2 === 5 || +params.purchaseType2 === 3) {
          params.provider_id = params.purchaseValue2
        }
				if (+params.purchaseType === 1) {
					params.agent = params.purchaseValue
				}
				if (+params.purchaseType === 2 || +params.purchaseType === 5 ) {
					params.provider = params.purchaseValue
				}
      }
      delete params.purchaseType;
      delete params.purchaseValue;
			delete params.purchaseType2;
			delete params.purchaseValue2;
      params.date_type = 2; // 和李昕沟通好固定好2
      if (Array.isArray(params.tag_id)) {
        if(params.tag_id.length>0) {
          params.tag_id = params.tag_id.join(',')
        } else {
          params.tag_id = ''
        }
      }

      const isNumber = (value) => {
        return typeof value === 'number' && !isNaN(value);
      }
      // 后台查询一个字段
      if (isNumber(params.min_price) && isNumber(params.max_price)) {
        params.bill_price_interval = `${ params.min_price }|${ params.max_price }`
      }

      this.requestParams = this.deepClone(params);
      return params;
    },
    beforeSetData (list) {
      list.forEach(item => {
        item._disabled = false;
        item._checked = false;
      })
      return list;
    },
    // 计算合计信息
    afterRequest (res) {
      let list = res.data.list || [];
      let sumRow = {
        id: Math.random(),
        source_no: '当前页合计',
        bill_price: 0,
        settle_price: 0,
        unpaid_price: 0,
        reduction_price: 0,
        settle_actual_price: 0,
        in_out_reduction_price: 0,
        _summary: true // 不显示勾选项
      };
      const expenseListTotalRow = {
        id: Math.random(),
        source_no: '费用单合计',
        _summary: true
      }
      let totalPageSumRow = {
        id: Math.random(),
        bill_price: 0,
        source_no: '所有页合计',
        // eslint-disable-next-line no-dupe-keys
        settle_price: 0,
        unpaid_price: 0,
        reduction_price: 0,
        settle_actual_price: 0,
        _summary: true
      };
      if (list.length === 0) {
        return res;
      }
      list.forEach(item => {
        sumRow.bill_price += item.bill_price * 1;
        sumRow.settle_price += item.settle_price * 1;
        sumRow.unpaid_price += item.unpaid_price * 1;
        sumRow.reduction_price += item.reduction_price * 1;
        sumRow.settle_actual_price += item.settle_actual_price * 1;
        sumRow.in_out_reduction_price += item.in_out_reduction_price * 1;
      });
      sumRow.bill_price = sumRow.bill_price.toFixed(2);
      sumRow.settle_price = sumRow.settle_price.toFixed(2);
      sumRow.unpaid_price = sumRow.unpaid_price.toFixed(2);
      sumRow.reduction_price = sumRow.reduction_price.toFixed(2);
      sumRow.settle_actual_price = sumRow.settle_actual_price.toFixed(2);
      sumRow.in_out_reduction_price = sumRow.in_out_reduction_price.toFixed(2)
      list.push(sumRow);
      list.push(expenseListTotalRow);
      list.push(totalPageSumRow);
      this.selectedOrders = [];
      // 获取所有页合计及费用单合计
      this.getAllPageAndExpenseListTotal();
      this.firstLoad = false;
      return res;
    },
     /**
     * @description: 获取所有页及费用单合计
     * @param {*}
     * @return {*}
     */
    getAllPageAndExpenseListTotal() {
      this.$request.get(this.apiUrl.purchaseAuditTotal, this.requestParams).then(res => {
        let { data, status } = res;
        let list = this.$refs.auditListTable.getData();
        let len = list.length;
        if (status && len) {
          let expenseListTotalRow = list[len - 2]
          expenseListTotalRow = { ...expenseListTotalRow, ...data.fee_sum }
          let lastRow = list[len - 1];
          lastRow = { ...lastRow, ...data.sum }
          list.splice(len - 2, 1, expenseListTotalRow);
          list.splice(len - 1, 1, lastRow);
        }
      });
    },
    handleSelectionChange (selection) {
      this.selectedOrders = selection.filter(e=>!e._summary);
    },
    /**
     * 设置表格中可选的选项
     */
    setCheckAble() {
      let firstSelectedOrder = this.selectedOrders[0];
      let list =this.$refs.auditListTable?this.$refs.auditListTable.getData():[];

      if (firstSelectedOrder) {
        let agentId = firstSelectedOrder.agent_id * 1;
        let providerId = firstSelectedOrder.provider_id * 1;
        list.forEach(item => {
          // 设置 已结算的 和 其他采购员/供应商的 单子为不可选状态
          if (!item.is_can_settle || (agentId ? item.agent_id * 1 !== agentId : item.provider_id * 1 !== providerId)) {
            item._disabled = true
          } else {
            item._disabled = false
          }
        });
      } else {
        list.forEach(item => {
          item._disabled = !item.is_can_settle;
        });
      }
    },
     /**
     * 设置表格中已选中的选项
     */
    setChecked() {
      let list =this.$refs.auditListTable?this.$refs.auditListTable.getData():[];
      let nos = this.selectedOrders.map(item => {return item.refer_no;});
      list.forEach(item => {
        if (nos && nos.length > 0 && nos.includes(item['refer_no'])){
          item._checked = true;
        } else {
          item._checked = false;
        }
      });
    },
    async getOperatorList () {
      let { data, status } = await get(api.getOperatorList, {
        page: 1,
        pageSize: 999999
      });
      if (status == 1) {
        let operatorData = data.list.map(item => {
          return {
            label: `${item.user_name}（${item.cn_name}）`,
            value: item.id
          };
        });
        operatorData.unshift({
          value: '',
          label: '全部'
        })
        this.advanceItems = this.advanceItems.map(item => {
          item.items = item.items.map(_item => {
            if (_item.key === 'create_userid') {
              _item.data = operatorData
            }
            return _item;
          })
          return item;
        });
      }
    },
    getSearchConfig() {
      purchaseAuditService.getAuditListSearchConfig().then(res => {
        if (res.status) {
          let data = res.data;
          let PurchaseSelect = this.advanceItems[0].items.find(item => item.label === '采购模式');
          this.searchConfig = data;
          const mode = []
          this.searchConfig.purchase_type.map(item => {
            const type = PURCHASE_TYPE[`${item.id}`]
            if (type) mode.push(type.mode)
          })
          PurchaseSelect.props.mode = mode
        }
      });
      Goods.getPurchaseType().then(res => {
        if (res.status) {
           this.advanceItems[0].items.forEach(col => {
                if (col.key === 'provider_supervisor') {
                  col.props.data = [
                {
                  id: '',
                  name: '全部'
                },
                ...(res.data.agents || [])
              ];
                }
            });
        }
      });
    },
  }
};
</script>
<style scoped lang="less">

.audit-alert {
  margin-top: 49px;
  &__tips {
    position: relative;
    bottom: 38px;
  }
  .auditList-wrap {
    margin-top: -58px;
  }
}

.auditList-wrap {
  padding: 24px 0 0;
}

/deep/ .list-table {
  .ivu-select-multiple .ivu-select-item-focus {
    background: rgba(246, 248, 249, 0.8);
  }
  .ivu-select-dropdown-list .ivu-select-item-selected {
    background: var(--primary-tr-hover);
  }
}
.no_custom {
  display: flex;
  align-items: center;
  /deep/.ivu-btn {
    font-weight: 400;
    padding: 0 14px;
  }
}
.export-modal-extra {
  padding: 6px 0 0 24px;
  border-top: 1px solid #f0f2f0;
  transform: translateY(26px);
}
.export-excel-title {
  font-size: 14px;
  color: #222127;
  font-weight: bold;
  margin-bottom: 10px;
}
/deep/ .groupFilter-custom {
  width: 219px;
}
</style>
