<template>
	<div>
		<baseDetailHead
			class="ml15"
			:title="!isCustomerExpense ? '退货详情' : '采购费用单对账详情'"
		/>
		<div id="user-audit-detail" class="content-wrap">
			<div class="base-info">
				<div class="title">
					<Icon type="record" color="#03ac54"></Icon> 基础信息
				</div>
				<div class="base-info-content">
					<Row type="flex" align="middle" :gutter="50">
						<Col span="6">{{ isCustomerExpense? '业务' : '退货' }}单号：{{ orderInfo.source_no }} </Col>
						<Col v-if="!isCustomerExpense" span="6"> 采购类型：{{ orderInfo.purchase_type_text }} </Col>
						<Col span="6">
							{{
								parseInt(orderInfo.purchase_type) === purchaseType.typeAgent.value
									? '采购员'
									: '供应商'
							}}
							：{{
								parseInt(orderInfo.purchase_type) === purchaseType.typeAgent.value
									? orderInfo.agent_name
									: orderInfo.provider_name
							}}
						</Col>
						<Col span="6"> 对账单号：{{ orderInfo.bill_no }} </Col>
						<Col v-if="!isCustomerExpense" span="6"> 源采购单：{{ orderInfo.purchase_no || '--' }} </Col>
						<Col span="6"> 创建时间：{{ orderInfo.create_time }} </Col>
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <Col span="6" v-if="orderInfo.link_order_user_name">所属客户：
                {{orderInfo.link_order_user_name || '-'}}
              </Col>
            </template>
            <!-- 采购退回对账单新增退货退款信息 -->
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <Col span="6" v-if="orderInfo.link_return_order_info && orderInfo.link_return_order_info.length">退货退款单号：
                <span v-for="(item, index) in orderInfo.link_return_order_info" :key="item.id">
                  <RelationNo :id='item.id' :no='item.return_no'></RelationNo>{{ orderInfo.link_return_order_info.length - 1 > index ? '、' : ''}}
                </span>
              </Col>
            </template>
						<Col span="6" v-if="isCustomerExpense">
							付款日期：{{ orderInfo.bill_date }}
						</Col>
						<Col span="6"> 结算状态：{{ orderInfo.status_text }} </Col>
						<Col span="6">
							对账状态：{{ orderInfo.bill_time ? '已对账' : '未对账' }}
						</Col>
						<Col span="6"> 对账人员：{{ orderInfo.bill_username }} </Col>
						<Col span="6"> 单据金额：{{ orderInfo.order_price }} </Col>
						<Col span="6"> 对账金额：{{ orderInfo.bill_price }} </Col>
						<Col span="6"> 差异金额：{{ orderInfo.diff_price }} </Col>
					</Row>
				</div>
			</div>
			<div class="order-list">
				<div class="title">
					<Icon type="record" color="#03ac54"></Icon>
					{{ isCustomerExpense ? '单据明细' : '退货商品清单' }}
				</div>
				<div class="order-list-content">
					<Table
						:columns="isCustomerExpense ? customerExpenseCol : cols"
						:data="orderInfo.commoditys"
					></Table>
				</div>
				<p class="remark" style="margin-top: 0">备注：{{ orderInfo.remark }}</p>
			</div>
			<div class="order-list">
				<div class="title">
					<Icon type="record" color="#03ac54"></Icon> 单据操作历史
				</div>
				<div class="order-list-content">
					<Table :columns="logCols" :data="orderInfo.logs"></Table>
				</div>
			</div>
			<Row class="fixedBtns" style="padding-left: 30px">
				<Col span="6" align="left">
					<Button @click="goBack">返 回</Button>&nbsp;&nbsp;
					<Button @click="_mxPrintPurchaseBill({ ids: orderInfo.id })"
					>打 印</Button
					>&nbsp;&nbsp;
				</Col>
			</Row>
		</div>
	</div>

</template>

<script>
import purchaseBillPrint from '@/mixins/print/purchaseBillPrint';
import Util from '@api/util.js';
import PurchaseAudit from '@api/PurchaseAudit';
import baseDetailHead from '@/components/base-detail-head';
import ConfigMixin from '@/mixins/config.js'
import RelationNo from '@/components/relation-no'

let purchaseAuditService = new PurchaseAudit();

export default {
  name: 'userAuditDetail',
  components: {
    baseDetailHead,
    RelationNo
  },
  mixins: [purchaseBillPrint, ConfigMixin],
  data() {
    return {
      customerExpenseCol: [
        {
          title: '序号',
          key: 'index',
        },
        {
          title: '付款方式',
          key: 'fee_pay_way_desc',
        },
        {
          title: '应收/应付',
          key: 'fee_payment_desc',
        },
        {
          title: '费用金额',
          key: 'receipt_total_price',
        },
        {
          title: '对账金额',
          key: 'bill_total_price',
        },
        {
          title: '备注',
          key: 'diff_remark',
        },
      ],
      logs: [],
      purchaseType: Util.purchaseType,
      cols: [
        {
          title: '序号',
          key: 'index',
          width: 80,
        },
        {
          title: '商品名称',
          key: 'name',
        },
        {
          title: '描述',
          key: 'summary',
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '退货数量',
          key: 'receipt_num',
          render: (h, param) => {
            let { row } = param;
            let text = [];
            // 合计
            if (param.index === this.orderInfo.commoditys.length - 1) {
              return h('span', row.bill_num);
            }
            if (row.receipt_num - row.bill_num == 0) {
              text = [h('p', row.receipt_num)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_num,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_num,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '退货单价',
          key: 'receipt_price',
          render: (h, param) => {
            let { row } = param;
            let text = [];
            if (row.receipt_price - row.bill_price == 0) {
              text = [h('p', row.receipt_price)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_price,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_price,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '退货金额',
          key: 'receipt_total_price',
          render: (h, param) => {
            let { row } = param;
            let text = [];
            // 合计
            if (param.index === this.orderInfo.commoditys.length - 1) {
              return h('span', row.bill_total_price);
            }
            if (row.receipt_total_price - row.bill_total_price == 0) {
              text = [h('p', row.receipt_total_price)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_total_price,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_total_price,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '差异数量',
          key: 'diff_num',
        },
        {
          title: '差异单价',
          key: 'diff_price',
        },
        {
          title: '差异金额',
          key: 'diff_total_price',
        },
        {
          title: '差异备注',
          key: 'diff_remark',
        },
      ],
      logCols: [
        {
          title: '操作人',
          key: 'name',
        },
        {
          title: '时间',
          key: 'time',
        },
        {
          title: '类型',
          key: 'type',
        },
        {
          title: '日志',
          key: 'content',
        },
      ],
      orderInfo: {},
    };
  },
  created() {},
  mounted() {
    this.getDetail();
  },
  computed: {
    isCustomerExpense() {
      return +this.orderInfo.type === 4;
    },
  },
  methods: {
    getLogDetail(purchase_return_id) {
      this.$request
        .get('/superAdmin/purchaseBill/PurchaseReturnLogs', {
          purchase_return_id,
        })
        .then((res) => {
          if (res.status) {
            this.logs = res.data;
          } else {
            this.logs = [];
          }
        });
    },
    getDetail() {
      purchaseAuditService
        .getAuditDetail({ id: this.$route.query.id })
        .then((res) => {
          if (res.status) {
            let data = res.data.order;
            let sumRow = {
              index: '合计：',
              bill_num: 0,
              bill_total_price: 0,
              diff_num: 0,
              diff_total_price: 0,
            };
            data.commoditys.forEach((item, index) => {
              item.index = index + 1;
              sumRow.bill_num += item.bill_num * 1;
              sumRow.bill_total_price += item.bill_total_price * 1;
              sumRow.diff_num += item.diff_num * 1;
              sumRow.diff_total_price += item.diff_total_price * 1;
            });
            sumRow.bill_num = sumRow.bill_num.toFixed(2);
            sumRow.bill_total_price = sumRow.bill_total_price.toFixed(2);
            sumRow.diff_num = sumRow.diff_num.toFixed(2);
            sumRow.diff_total_price = sumRow.diff_total_price.toFixed(2);
            data.commoditys.push(sumRow);
            this.orderInfo = data;
            // this.getLogDetail(data.purchase_return_id)
          } else {
            this.modalError(res.message);
          }
        });
    },
  },
};
</script>

<style lang="less"></style>
<style scoped lang="less">
@gutter: 15px;
#user-audit-detail {
  text-align: left;
  padding: 15px 0 60px;
  .base-info {
    padding: 0 @gutter;
    .ivu-row-flex {
      margin: @gutter;
    }
    .base-info-content {
      margin: @gutter;
    }
  }
  .order-list {
    padding: @gutter;
    .order-list-content {
      padding: @gutter;
    }
  }
  .remark {
    margin: 0 0 0 @gutter;
  }
}
.base-info-content {
  .ivu-col {
    height: 36px;
    line-height: 36px;
  }
}
/deep/ .ivu-col {
  padding-left: 16px!important;
  padding-right: 0px!important;
}
</style>
