<template>
  <div  >
    <div id="purchase-audit" class="content-wrap">
      <div class="base-info">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon> 基础信息
        </div>
        <div class="base-info-content">
          <Form
            inline
            label-colon
            :label-width="90"
            :model="formValidate"
            :rules="ruleValidate"
            ref="formValidate"
          >
            <FormItem :label="isReturn ? '退货单号' : '业务单号'">
              {{orderInfo.source_no}}
            </FormItem>
            <FormItem v-if="isReturn" label="采购类型">
              {{orderInfo.purchase_type_text}}
            </FormItem>
            <FormItem :label="parseInt(orderInfo.purchase_type) === purchaseType.typeAgent.value ? '采购员' : '供应商'">
              {{parseInt(orderInfo.purchase_type) === purchaseType.typeAgent.value ? orderInfo.agent_name : orderInfo.provider_name}}
            </FormItem>
            <FormItem label="对账单号">
              {{orderInfo.bill_no}}
            </FormItem>
            <FormItem v-if="isReturn" label="源采购单">
              {{orderInfo.purchase_no}}
            </FormItem>
            <FormItem label="对账人员">
              {{orderInfo.bill_username || '-'}}
            </FormItem>
            <FormItem label="创建日期">
              {{orderInfo.create_time}}
            </FormItem>
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <FormItem label="所属客户" v-if="orderInfo.link_order_user_name">
                {{orderInfo.link_order_user_name || '-'}}
              </FormItem>
            </template>
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && orderInfo.type == 1">
              <FormItem label="关联订单号">
                <template v-if="orderInfo.link_order_info && orderInfo.link_order_info.length">
                  <span v-for="(item, index) in orderInfo.link_order_info" :key="item.id">
                    <RelationNo :id='item.id' :no='item.order_no'></RelationNo>{{ orderInfo.link_order_info.length - 1 > index ? '、' : ''}}
                  </span>
                </template>
                <span v-else>-</span>
              </FormItem>
            </template>
            <!-- 采购退回对账单新增退货退款信息 -->
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <FormItem label="退货退款单号" v-if="orderInfo.link_return_order_info && orderInfo.link_return_order_info.length">
                <span v-for="(item, index) in orderInfo.link_return_order_info" :key="item.id">
                  <RelationNo :id='item.id' :no='item.return_no'></RelationNo>{{ orderInfo.link_return_order_info.length - 1 > index ? '、' : ''}}
                </span>
              </FormItem>
            </template>
            <FormItem label="结算状态">
              {{orderInfo.status_text}}
            </FormItem>
            <FormItem label="对账状态">
              {{orderInfo.bill_time ? '已对账' : '未对账'}}
            </FormItem>
            <FormItem label="单据金额">
              {{orderInfo.order_price}}
            </FormItem>
            <FormItem v-if="!isReturn && !isCustomerExpense" label="整单折扣">
              <div class="percent">
                <NumberInput
                  style="width: 198px;"
                  v-model="orderInfo.bill_discount"
                  @on-change="discountChange"
                  :max="999999999"
                  :min="0"
                  precision="2"
                />
                <span>%</span>
              </div>
            </FormItem>
            <FormItem label="对账金额">
              <NumberInput
								:disabled="isCustomerExpense"
                style="width: 198px;"
                v-model="orderInfo.bill_price"
                @on-change="billPriceChange"
                :min="0"
                precision="2"
              />
            </FormItem>
            <FormItem label="应付金额" v-if="!isCustomerExpense">
              {{ ((orderInfo.bill_price || 0) - (orderInfo.bill_reduction_price || 0)).toFixed(2) }}
            </FormItem>
            <FormItem label="差异金额">
              <Input style="width: 198px;" type="text" v-model="orderInfo.diff_price" :disabled="true"/>
            </FormItem>
            <FormItem v-if="isPurchaseUseOrderCommodityTag && !isReturn" prop="tag_id" :rules="tagRules">
              <div slot="label">
                订单商品标签
                <Tooltip
                  max-width="220"
                  placement="top"
                  content="修改后，不会变更采购单中的数据"
                >
                  <SIcon style="cursor: pointer" :size="1" icon="tishifu"></SIcon>
                </Tooltip>
                :
              </div>
              <Select
                style="width: 198px;"
                v-model="formValidate.tag_id"
                clearable
                transfer
                placeholder='请选择'
              >
                <Option v-for="tag in tagList" :value="tag.id" :key="tag.id" :label="tag.name">
                </Option>
              </Select>
            </FormItem>
						<FormItem v-if="isCustomerExpense" label="付款日期">
							{{ orderInfo.bill_date  }}
						</FormItem>
          </Form>
        </div>
      </div>
      <div class="order-list">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon>
					{{ isCustomerExpense? '单据明细' : isReturn ? '退货商品清单' : '收货商品清单' }}
        </div>
        <div class="order-list-content">
					<Table
						v-if="isCustomerExpense"
						:columns="customerExpenseCol"
						:data="orderInfo.commoditys"
					>

					</Table>
          <SVxeEditableTable
            v-if="cols.length && !isCustomerExpense"
            refs="table"
            ref="editTable"
            :data="orderInfo.commoditys"
            @on-cell-change="handleCellChange"
            @currentRow-index="saveCurrentRowIndex"
            :editIncell="true"
            :hover-show="false"
            :columns="cols"
            :tableId="+type === 1 ? 'purchase_receipt_reconciliation' : 'purchase_return_reconciliation'">
            <template #after-table><span></span></template>
          </SVxeEditableTable>
        </div>
        <div class="extra" v-if="!isReturn && !isCustomerExpense">
          对账抹零金额：<NumberInput style="width: 186px;" v-model="orderInfo.bill_reduction_price" precision="2" min="0" @on-change="handleBillReductionPrice" />
        </div>
        <div class="extra">
          <span style="display: inline-block;width: 94px;">备注：</span>
          <Input style="width: 80%" v-model="formData.remark"/>
        </div>
      </div>
    </div>
    <Row class="fixedBtns">
      <Col span="6" align="left">
        <Button type="primary" @click="save" :disabled="saving">{{saving ? '保存中...' : '保 存'}}</Button>&nbsp;&nbsp;
        <Button @click="goBack">取 消</Button>&nbsp;&nbsp;
      </Col>
    </Row>
  </div>
</template>

<script>
  import HotKey from '../../../util/HotKey';
  import Util from '@api/util.js';
  import Goods from '@api/goods.js'
  import ConfigMixin from '@/mixins/config.js'
  import PurchaseAudit from '@api/PurchaseAudit';
  import SIcon from '@components/icon';
  import NumberInput from '@components/basic/NumberInput';
  import { round, floor } from 'lodash-es'
  import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
  import RelationNo from '@/components/relation-no'

  let purchaseAuditService = new PurchaseAudit();
  export default {
    name: "userAudit",
    mixins: [ConfigMixin],
    components: {
      SVxeEditableTable,
      NumberInput,
      SIcon,
      RelationNo
    },
    computed: {
      isReturn () {
        return +this.$route.query.type === 2;
      },
			isCustomerExpense() {
				return +this.orderInfo.type === 4
			}
    },
    data() {
      return {
				customerExpenseCol: [
					{
						title: '序号',
						key: 'index',
					},
					{
						title: '付款方式',
						key: 'fee_pay_way_desc'
					},
					{
						title: '应收/应付',
						key: 'fee_payment_desc'
					},
					{
						title: '费用金额',
						key: 'bill_total_price',
					},
					{
						title: '对账金额',
						key: 'receipt_total_price',
						render: (h, params) => {
							const { row } = params
							if (row.index === '合计') {
								return h('span', row.receipt_total_price)
							}
							const limit = +row.fee_payment === 2? 'min' : 'max'
							const limitBest = limit === 'min'? 'max' : 'min'
							return h(NumberInput, {
								props: {
									value: +row.receipt_total_price,
									allowNegativeDecimal: true,
									precision: 2,
									[limitBest]: limitBest === 'min'? -9999999999.99 : 9999999999.99
								},
								on: {
									'on-change': (val) => {
										row.receipt_total_price = val
										this.handleCellChange(row, params.index, 'bill_total_price')
									}
								}
							})
						}
					},
					{
						title: '备注',
						key: 'diff_remark',
						render: (h, params) => {
							const { row, index } = params
							if (row.index === '合计') {
								return h('span', '--')
							}
							return h('Input', {
								style: {
									width: '120px'
								},
								props: {
									value: row.diff_remark,
									placeholder: '请输入备注',
									maxlength: 20,
								},
								on: {
									'on-change': (e) => {
										row.diff_remark = e.target.value
										this.orderInfo.commoditys[index]['diff_remark'] = row.diff_remark
									},
									'on-enter': () => {
										this.orderInfo.commoditys[index]['diff_remark'] = row.diff_remark
									}
								},
							})
						}
					}
				],
        oldSubPriceList:[],
        saving: false,
        formValidate: {
          tag_id: ''
        },
        ruleValidate: {},
        tagRules: {
          required: false, message: '请选择订单商品标签', trigger: 'change'
        },
        purchaseType: Util.purchaseType,
        cols: [],
        opCols: [],
        opList: [],
        orderInfo: {
          commoditys: []
        },
        formData: {
          id: [],
          commoditys: [],
          remark: '',
        },
        tagList: [],
        type: 0
      }
    },
    created ()  {
      this.formData.id = this.$route.query.id;
      this.initCols()
      this.getDetail();
      this.getTagList();
    },
    mounted () {
    },
    methods: {
      initCols () {
        /**
         *  1: '采购收货'  2: '采购退货'  4: '采购费用'
         */
        const type = this.type = this.$route.query.type
        let titleTypeData = []
        if (+type === 1 || +type === 2) {
          titleTypeData = [{
            type: 'titleCfg',
            titleType: +type === 1 ? 'purchase_receipt_reconciliation' : 'purchase_return_reconciliation',
            width: 32,
            fixed: 'left'
          }]
        }
        const cols = [
          ...(titleTypeData),
          {
            title: '序号',
            key: 'index',
            width: 65
          },
          {
            title: '商品名称',
            key: 'name',
            minWidth: 90,
            render(h, {row}) {
              return <span>{row.index == '合计'? '' : (row.name || '-')}</span>
            }
          },
          {
            title: '描述',
            key: 'summary',
            minWidth: 90,
            render(h, {row}) {
              return <span>{row.index == '合计'? '' : (row.summary || '-')}</span>
            }
          },
          {
            title: '单位',
            key: 'unit',
            width: 80,
            render(h, {row}) {
              return <span>{row.index == '合计'? '' : (row.unit || '-')}</span>
            }
          },
          {
            title: '转换系数',
            key: 'unit_convert_text',
            minWidth: 100,
            render(h, {row}) {
              return <span>{row.index == '合计'? '' : (row.unit_convert_text || '-')}</span>
            }
          },
          {
            title: this.isReturn ? '退货数量' : '入库数量',
            key: 'receipt_num',
            minWidth: 100,
            render: (h, params) => {
              let { row, index } = params;
              let text = [];
              // 合计
              if (params.index === this.orderInfo.commoditys.length - 1) {
                return h('p', {}, row.receipt_num)
              }
              text = [
                h('InputNumber', {
                  class: [HotKey.getHotKeyClass()],
                  style: {
                    marginTop: this.opList[index]._receipt_num == row.receipt_num? '0' : '10px'
                  },
                  props: {
                    value: (row.receipt_num * 1).toFixed(2),
                    min: 0
                  },
                  on: {
                    'on-change': res => {
                      row.receipt_num = res;
                      this.handleCellChange(row, params.index, 'receipt_num');
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
                h(
                    'p',
                    {
                      style: {
                        marginBottom: '10px',
                        color: 'red',
												// 划线
												textDecoration: 'line-through'
                      },
											class: {
												'dn': this.opList[index]._receipt_num == row.receipt_num
											}
                    },
                    `${this.opList[index] && this.opList[index]._receipt_num}`
                )
              ];
              return h('div', text);
            }
          },
          {
            title: this.isReturn ? '退货单价' : '入库单价',
            key: 'receipt_price',
            minWidth: 100,
            render: (h, params) => {
              let { row, index } = params;
              let text = [];
              // 合计
              if (params.index === this.orderInfo.commoditys.length - 1) {
                return h('span', '');
              }
              text = [
                h('InputNumber', {
                  class: [HotKey.getHotKeyClass()],
									style: {
										marginTop: this.opList[index]._receipt_price == row.receipt_price? '0' : '10px'
									},
                  props: {
                    value: (row.receipt_price * 1).toFixed(2),
                    min: 0
                  },
                  on: {
                    'on-change': res => {
                      row.receipt_price = res;
                      this.handleCellChange(row, params.index, 'receipt_price');
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
								h(
									'p',
									{
										style: {
											marginBottom: '10px',
											color: 'red',
											// 划线
											textDecoration: 'line-through'
										},
										class: {
											'dn': this.opList[index]._receipt_price == row.receipt_price
										}
									},
									`${this.opList[index] && this.opList[index]._receipt_price}`
								)
              ];
              return h('div', text);
            }
          },
          {
            title: this.isReturn ? '退货金额' : `入库金额${this.isOpenStoreNoTax ? '(含税)' : ''}`,
            key: 'receipt_total_price',
            minWidth: 120,
            render: (h, params) => {
              let { row, index } = params;
              let text = [];
              if (row.index !== '合计') {
                text = [
                  h('InputNumber', {
                    class: [HotKey.getHotKeyClass()],
										style: {
											marginTop: this.opList[index]._receipt_total_price == row.receipt_total_price? '0' : '10px'
										},
                    props: {
                      value: Number(row.receipt_total_price),
                      min: 0
                    },
                    on: {
                      'on-change': res => {
                        row.receipt_total_price = res;
                        this.handleCellChange(
                          row,
                          params.index,
                          'receipt_total_price'
                        );
                      },
                      'on-focus': event => {
                        event.target.select();
                      }
                    },
                    nativeOn: {
                      keyup: event => {
                        if (event.keyCode == 13) {
                          this.handleMoveLeftAndRight(event, params);
                        }
                      }
                    }
                  }),
									h(
										'p',
										{
											style: {
												marginBottom: '10px',
												color: 'red',
												// 划线
												textDecoration: 'line-through'
											},
											class: {
												'dn': this.opList[index]._receipt_total_price == row.receipt_total_price
											}
										},
										`${this.opList[index] && this.opList[index]._receipt_total_price}`
									)
                ];
              } else if (row.index == '合计') {
                text = [h('p', {}, row.receipt_total_price)];
              }
              return h('div', text);
            }
          },
          ...(!this.isReturn ? [{
            title: '对账抹零',
            key: 'item_bill_reduction_price',
            minWidth: 100,
            render: (h, params) => {
              let { row } = params;
              if (row.index !== '合计') {
                return h('div', [
                  h('InputNumber', {
                    class: [HotKey.getHotKeyClass()],
                    props: {
                      value: Number(row.item_bill_reduction_price),
                      min: 0
                    },
                    on: {
                      'on-change': val => {
                        row.item_bill_reduction_price = val;
                        this.handleReductionPriceCellChange(
                          row,
                          params.index,
                          'item_bill_reduction_price'
                        );
                      },
                      'on-focus': event => {
                        event.target.select();
                      }
                    },
                    nativeOn: {
                      keyup: event => {
                        if (event.keyCode == 13) {
                          this.handleMoveLeftAndRight(event, params);
                        }
                      }
                    }
                  }),
                ]);
              } else {
                return h('span', this.orderInfo.bill_reduction_price)
              }
            }
          },] : []),
          {
            title: '差异数量',
            key: 'diff_num',
            minWidth: 80,
            render: (h, params) => {
              let {row} = params;
              return h('span', (row.need_receipt_num - row.receipt_num).toFixed(2));
            }
          },
          {
            title: '差异单价',
            key: 'diff_price',
            minWidth: 80,
            render: (h, params) => {
              let {row, index} = params;
              if (index === this.orderInfo.commoditys.length - 1) {
                return '';
              }
              return h('span', (row.need_receipt_price - row.receipt_price).toFixed(2));
            }
          },
          {
            title: '差异金额',
            key: 'diff_total_price',
            minWidth: 80,
            render: (h, params) => {
              let {row} = params;
              return h('span', (row.need_receipt_total_price - row.receipt_total_price).toFixed(2));
            }
          },
					{
						title: '差异备注',
						width: 120,
						key: 'diff_remark',
						render: (h, params) => {
							const { row, index } = params
							if (row.index === '合计') {
								return h('span', '')
							}
							return h('i-input', {
								props: {
									value: row.diff_remark,
									placeholder: '最多输入20字',
									maxlength: 20,
									type: "textarea",
								},
								class: {
									remarks: true
								},
								on: {
									'on-change': (e) => {
										row.diff_remark = e.target.value
										this.orderInfo.commoditys[index]['diff_remark'] = row.diff_remark
									},
									'on-enter': () => {
										this.orderInfo.commoditys[index]['diff_remark'] = row.diff_remark
									}
								},
							})
						}
					}
        ]
        if (!this.isReturn) {
          const index = cols.findIndex(col => col.key === 'receipt_num')
          if (index) {
            cols.splice(index, 0, ...[
              {
                title: '收货数量',
                key: 'origin_receipt_num',
                minWidth: 80,
                render(h, {row}) {
                  return <span>{row.index == '合计'? '' : (row.origin_receipt_num || '-')}</span>
                }
              },
              {
                title: '收货单价',
                key: 'origin_receipt_price',
                minWidth: 80,
                render(h, {row}) {
                  return <span>{row.index == '合计'? '' : (row.origin_receipt_price || '-')}</span>
                }
              },
              {
                title: '收货金额',
                key: 'origin_receipt_total_price',
                minWidth: 80,
                render(h, {row}) {
                  return <span>{row.index == '合计'? '' : (row.origin_receipt_total_price || '-')}</span>
                }
              },
            ])
          }
        }
        this.cols = cols
      },
    handleMoveLeftAndRight(event, params) {
      let vm = this;
      let indexCursor = 1;
      vm.$nextTick(() => {
        let $tableRows = vm.$refs['editTable'].$el.querySelectorAll(
          '.ivu-table-tbody .ivu-table-row'
        );
        if (!$tableRows) {
          return false;
        }
        let $currentRow = $tableRows[params.index];
        let $hotKeyItems = $currentRow.querySelectorAll(
          `.${INPUT_HOT_KEY_CLASS} input`
        );
        let hotKeyItems = Array.from($hotKeyItems);
        let itemIndex = hotKeyItems.indexOf(event.target);
        let $nextTarget = hotKeyItems[itemIndex + indexCursor];
        if ($nextTarget) {
          $nextTarget.focus();
        }
      });
    },
      billPriceChange (val,isChangeDiscount) {
      const len = this.orderInfo.commoditys.length
      if (len>2) {
        let lastSubPrice = val
        this.orderInfo.commoditys.forEach((item,index) => {
          // 排除汇总数据
          if (index<len-1) {
            if (index < len-2) {
              item.receipt_total_price = parseFloat(
                Number(val).div(this.orderInfo.order_price).mul(this.oldSubPriceList[index])
              ).toFixed(2)
              this.handleCellChange(item,index,'receipt_total_price', false);
              lastSubPrice = lastSubPrice.sub(item.receipt_total_price);
            } else {
              item.receipt_total_price = lastSubPrice
              this.handleCellChange(item,index,'receipt_total_price', false);
            }
          }
        })
      } else {
        this.orderInfo.commoditys[0].receipt_total_price = val
        this.handleCellChange(this.orderInfo.commoditys[0],0,'receipt_total_price', false);
      }
      if(!isChangeDiscount){
        this.orderInfo.bill_discount = Number(this.orderInfo.order_price)==0?'100': this.orderInfo.bill_price.div(Number(this.orderInfo.order_price)).mul(100).toFixed(2)
      }
      },
      discountChange (val) {
        this.orderInfo.bill_price = Number(this.orderInfo.order_price).mul(val.div(100)).toFixed(2)
        this.billPriceChange(this.orderInfo.bill_price,true)
      },
      getTagList () {
        if(!this.isPurchaseUseOrderCommodityTag) return
          this.tagRules.required = this.isOrderCommodityTagRequired
          Goods.getOrderGoodsTagList().then((data) => {
          this.tagList = data
        })
      },
      handleBillReductionPrice(val) {
        let orderCount = this.orderInfo.commoditys.length;
        const reductionPrice = floor(val / (orderCount - 1), 2)
        const tmpList = [].concat(this.orderInfo.commoditys)
        let _otherTotal = 0
        tmpList.forEach((item, index) => {
          // 排除合计那行
          if (index < orderCount - 2) {
            _otherTotal += reductionPrice;
            item.item_bill_reduction_price = reductionPrice;
          }
          if (index === orderCount - 2) {
            // 最后一个用总的减前面所有的
            item.item_bill_reduction_price = round(val - _otherTotal, 2);
          }
        });
        this.orderInfo.commoditys = tmpList
      },
      handleReductionPriceCellChange(row, index) {
        this.orderInfo.commoditys.splice(index, 1, row);
        let orderCount =  this.orderInfo.commoditys.length;
        let sumReductionPrice = 0;
        this.orderInfo.commoditys.forEach((item, index) => {
          // 排除合计那行
          if (index <= orderCount - 2) {
            sumReductionPrice += item.item_bill_reduction_price * 1;
          }
        });
        this.orderInfo.bill_reduction_price = sumReductionPrice.toFixed(2)
      },
       handleCellChange (row, index, column, isUpdateBillPrice=true) {
        if (row.receipt_num && isNaN(row.receipt_num)) {
          this.modalError('请输入有效收货数量', 0);
          return false;
        }
        if (row.receipt_price && isNaN(row.receipt_price)) {
          this.modalError('请输入有效收货单价', 0);
          return false;
        }
        if (row.receipt_total_price && isNaN(row.receipt_total_price)) {
          this.modalError('请输入有效收货金额', 0);
          return false;
        }
        if (Number(row.receipt_num) == 0 &&  Number(row.receipt_total_price) == 0) {
          row.receipt_num = 0;
          // row.receipt_price = 0;
          row.receipt_total_price = 0;
        } else {
          // 修改收货数量, 重新计算收货金额和对账金额
          if (column === 'receipt_num') {
            row.calculate_way = 1;
            row.receipt_num = (row.receipt_num * 1).toFixed(2);
            row = this.computeReceiptTotalPrice(row);
          }
          // 修改收货单价, 重新计算收货金额和对账金额
          if (column === 'receipt_price') {
            row.calculate_way = 1;
            row.receipt_price = (row.receipt_price * 1).toFixed(2);
            row = this.computeReceiptTotalPrice(row);
          }
          // 修改收货金额, 重新计算收货单价和对账金额
          if (column === 'receipt_total_price') {
            row.calculate_way = 2;
            row.receipt_price = row.receipt_num > 0 ? Number(row.receipt_total_price).div(row.receipt_num).toFixed(2) : 0;
          }
        }
        // this.orderInfo.commoditys.splice(index, 1, row);
        this.computeSumPrice();
        this.computeSumReceiptTotalPrice(isUpdateBillPrice);
      },
      /**
       * 重新计算对账金额, 最后一行合计的发货数量，发货金额，差异数量，差异金额
       */
      computeSumPrice () {
        let orderCount = this.orderInfo.commoditys.length;
        this.orderInfo.diff_price = 0;
        let sumNeedReceiptNum = 0;
        let sumReceiptNum= 0;
        let sumTotalReceiptPrice = 0;
        let sumTotalNeedReceiptPrice = 0;
        let sumDiffAmount = 0;
        let sumDiffTotalPrice = 0;
        this.orderInfo.commoditys.forEach((item, index) => {
          // 排除合计那行
          if (index <= orderCount - 2) {
            sumReceiptNum += item.receipt_num * 1;
            sumNeedReceiptNum += item.need_receipt_num * 1;
            sumTotalReceiptPrice += item.receipt_total_price * 1;
            sumTotalNeedReceiptPrice += item.need_receipt_total_price * 1;
            sumDiffAmount += item.diff_num * 1;
            sumDiffTotalPrice += item.need_receipt_total_price - item.receipt_total_price;
          }
          // 更新合计数据
          if (index === orderCount - 1) {
            item.receipt_num = sumReceiptNum.toFixed(2);
            item.need_receipt_num = sumNeedReceiptNum.toFixed(2);
            item.receipt_total_price = sumTotalReceiptPrice.toFixed(2);
            item.need_receipt_total_price = sumTotalNeedReceiptPrice.toFixed(2);
            item.diff_num = sumDiffAmount.toFixed(2);
            item.diff_total_price = sumDiffTotalPrice.toFixed(2);
          }
        });
        this.orderInfo.diff_price = sumDiffTotalPrice.toFixed(2);
      },
      /**
       * 重新计算收货金额
       * @param row
       * @returns {*}
       */
      computeReceiptTotalPrice (row) {
        row.receipt_total_price = (Number(row.receipt_num).mul(row.receipt_price)) . toFixed(2);
        return row;
      },
      /**
       * 重新计算对账金额
       */
      computeSumReceiptTotalPrice (isUpdateBillPrice) {
        if (isUpdateBillPrice) {
          let orderCount = this.orderInfo.commoditys.length;
          this.orderInfo.bill_price = 0;
          this.orderInfo.commoditys.forEach((item, index) => {
            // 排除合计那行
            if (index <= orderCount - 2) {
              // 不用单价 * 数量重新算小计，避免没有修改的商品产生精度问题
              this.orderInfo.bill_price += Number(item.receipt_total_price);
            }
          });
          this.orderInfo.bill_price = this.orderInfo.bill_price.toFixed(2);
          this.orderInfo.bill_discount = Number(this.orderInfo.order_price)==0 ? '100' : this.orderInfo.bill_price.div(Number(this.orderInfo.order_price)).mul(100).toFixed(2)
        }
      },
      saveCurrentRowIndex () {
      },
      getDetail () {
        purchaseAuditService.getAuditDetail({id: this.formData.id}).then((res) => {
          if (res.status) {
            let data = res.data.order;
            this.formValidate.tag_id = data.tag_id;
            let sumItemBillReductionPrice = 0
            data.commoditys.forEach((item, index) => {
              item.index = index + 1;
              item._origin_receipt_total_price = item.receipt_total_price;
              item.need_receipt_num = item.receipt_num;
              item.need_receipt_price = item.receipt_price;
              item.need_receipt_total_price = item.receipt_total_price;
              item._receipt_total_price = item.receipt_total_price;
              item.receipt_total_price = item.bill_total_price;
              item._receipt_price = item.receipt_price;
              item.receipt_price = item.bill_price;
              item._receipt_num = item.receipt_num
              item.receipt_num = item.bill_num;
              item.item_bill_reduction_price = item.item_bill_reduction_price || 0;
              sumItemBillReductionPrice += item.item_bill_reduction_price
            });
            let sumRow = {
              index: '合计',
              receipt_num: 0,
              receipt_total_price: 0,
              need_receipt_num: 0,
              need_receipt_total_price: 0,
            };
            data.commoditys.push(sumRow);
            this.orderInfo = data;
						this.opList = JSON.parse(JSON.stringify(data.commoditys))
            this.orderInfo.bill_discount = data.bill_discount ? Number(data.bill_discount) : 100
            this.formData.remark = data.remark;
            this.computeSumPrice();
            this.oldSubPriceList = data.commoditys.map((item) => {
              return item._origin_receipt_total_price
            })
            this.oldSubPriceList.pop()
            if (sumItemBillReductionPrice !== +this.orderInfo.bill_reduction_price) {
              this.handleBillReductionPrice(this.orderInfo.bill_reduction_price)
            }
          } else {
            this.modalError(res.message);
          }
        });
      },
      getSaveData () {
        let data = this.cloneObj(this.formData);
        let orderCount = this.orderInfo.commoditys.length;
        data.bill_discount = this.orderInfo.bill_discount
        data.bill_reduction_price = this.orderInfo.bill_reduction_price
        data.commoditys = [];
        this.orderInfo.commoditys.forEach((goods, index) => {
          // 排除合计那行
          if (index <= orderCount - 2) {
            let item = {
              id: goods.commodity_id,
              num: goods.receipt_num,
              price: goods.receipt_price,
              total_price: goods.receipt_total_price,
              bill_record_id:goods.id,
              diff_remark: goods.diff_remark,
              item_bill_reduction_price: goods.item_bill_reduction_price,
              calculate_way: goods.calculate_way,
            }
            data.commoditys.push(item);
          }
        });
        data.commoditys = JSON.stringify(data.commoditys);
        data.tag_id = this.formValidate.tag_id;
        return data;
      },
      check() {
        let valid = false
        this.$refs.formValidate.validate(formValid => {
        valid = formValid
        })
        return valid
      },
      save () {
        if(!this.check()) return
        let data = this.getSaveData();
        purchaseAuditService.auditSave(data).then((res) => {
          if (res.status) {
            this.$Message.success('保存成功')
            this.goBack();
          } else {
            this.modalError(res.message);
          }
        });
      },
    }
  }
</script>

<style lang="less">
  #purchase-audit {
    .ivu-table-cell {
      white-space: normal !important;
    }
  }
  #purchase-audit {
    .ivu-table-row:nth-last-child(1) {
      .ivu-btn {
        display: none;
      }
    }
  }
</style>
<style scoped lang="less">
  @gutter: 15px;
  #purchase-audit {
    font-size: 14px;
    text-align: left;
    padding: 15px 15px 50px;

  }
     .content-wrap {
      // padding-bottom: @gutter;
      .base-info {
        .ivu-row-flex {
          margin: @gutter;
        }
        .base-info-content {
          margin: @gutter;
          .ivu-input-wrapper {
            width: 60%;
          }
        }
      }
      .order-list-content {
        padding: @gutter;
      }
      .extra {
        margin-bottom: @gutter;
        margin-left: @gutter;
      }
    }
  .ivu-input-number-handler-wrap {
    display: none ;
  }
  .toggleIcon span{
    cursor: pointer;
    display: inline-block;
    margin-top: 7px;
  }
  /deep/ .ivu-form {
    .ivu-form-item {
      width: 330px;
      margin: -6px 5px 10px -2px;
      &-label {
        font-size: 13px;
        padding: 0 12px 0 0 !important;
        height: 30px;
        line-height: 15px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
      }
      &-content{
        font-size: 13px;
        min-height: 30px;
        line-height: normal;
        color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        justify-content: center;
        .percent {
          display: flex;
          align-items: center;
          span {
            margin-left: 5px;
          }
        }
      }
    }

  }
  /deep/ .ivu-col {
    padding-left: 16px!important;
    padding-right: 0px!important;
  }
</style>
