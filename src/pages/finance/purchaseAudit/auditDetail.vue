<template>
  <DetailPage pageType="view" title="收货详细" name="xxx">
    <div id="user-audit-detail" class="content-wrap">
      <div class="base-info">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon> 基础信息
        </div>
        <div class="base-info-content">
          <Row type="flex" align="middle" :gutter="50">
            <Col span="6"> 业务单号：{{ orderInfo.source_no }} </Col>
            <Col span="6">
              {{
                parseInt(orderInfo.purchase_type) ===
                purchaseType.typeAgent.value
                  ? '采购员'
                  : '供应商'
              }}
              ：{{
                parseInt(orderInfo.purchase_type) ===
                purchaseType.typeAgent.value
                  ? orderInfo.agent_name
                  : orderInfo.provider_name
              }}
            </Col>
            <Col span="6"> 对账单号：{{ orderInfo.bill_no }} </Col>
            <Col span="6"> 对账人员：{{ orderInfo.bill_username || '-' }} </Col>
            <Col span="6"> 创建日期：{{ orderInfo.create_time }} </Col>
             <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <Col span="6" v-if="orderInfo.link_order_user_name">
                所属客户：{{orderInfo.link_order_user_name || '-'}}
              </Col>
            </template>
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && orderInfo.type == 1">
              <Col span="6">
                关联订单号：
                <template v-if="orderInfo.link_order_info && orderInfo.link_order_info.length">
                  <span v-for="(item, index) in orderInfo.link_order_info" :key="item.id">
                    <RelationNo :id='item.id' :no='item.order_no'></RelationNo>{{ orderInfo.link_order_info.length - 1 > index ? '、' : ''}}
                  </span>
                </template>
                <span v-else>-</span>
              </Col>
            </template>
            <template v-if="sysConfig.tc_platform == 1 && sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3">
              <Col span="6" v-if="orderInfo.link_return_order_info && orderInfo.link_return_order_info.length">
                退货退款单号：
                <span v-for="(item, index) in orderInfo.link_return_order_info" :key="item.id">
                  <RelationNo :id='item.id' :no='item.return_no'></RelationNo>{{ orderInfo.link_return_order_info.length - 1 > index ? '、' : ''}}
                </span>
              </Col>
            </template>
            <Col span="6"> 结算状态：{{ orderInfo.status_text }} </Col>
            <Col span="6">
              对账状态：{{ orderInfo.bill_time ? '已对账' : '未对账' }}
            </Col>
            <Col span="6"> 单据金额：{{ orderInfo.order_price }} </Col>
            <Col span="6"> 对账金额：{{ orderInfo.bill_price }} </Col>
            <Col span="6"> 差异金额：{{ orderInfo.diff_price }} </Col>
            <Col span="8" v-if="isPurchaseUseOrderCommodityTag">
              订单商品标签：{{ orderInfo.tag_name }}
            </Col>
          </Row>
        </div>
      </div>
      <div class="order-list">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon> 收货商品清单
        </div>
        <div class="order-list-content">
          <Table :columns="cols" :data="orderInfo.commoditys"></Table>
        </div>
        <p class="remark" style="margin-top: 0">备注：{{ orderInfo.remark }}</p>
      </div>
      <div class="order-list">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon> 单据操作历史
        </div>
        <div class="order-list-content">
          <Table :columns="opCols" :data="orderInfo.logs"></Table>
        </div>
      </div>
    </div>
    <template #button-after>
      <Button
        type="primary"
        @click="_mxPrintPurchaseBill({ ids: orderInfo.id })"
        >打印
      </Button>
    </template>
  </DetailPage>
</template>

<script>
import purchaseBillPrint from '@/mixins/print/purchaseBillPrint';
import DetailPage from '@/components/detail-page/index.js';
import ConfigMixin from '@/mixins/config.js';
import Util from '@api/util.js';
import PurchaseAudit from '@api/PurchaseAudit';
import { SIcon } from '@/components';
import RelationNo from '@/components/relation-no'

let purchaseAuditService = new PurchaseAudit();

export default {
  name: 'userAuditDetail',
  components: {
    DetailPage,
    RelationNo
  },
  mixins: [purchaseBillPrint, ConfigMixin],
  data() {
    return {
      purchaseType: Util.purchaseType,
      cols: [
        {
          title: '序号',
          key: 'index',
          width: 90,
          resizable: true,
        },
        {
          title: '商品名称',
          key: 'name',
          resizable: true,
          width: 160,
          render: (h, param) => {
            let { row } = param;
            return h('div',
              {
                style: {
                  'white-space': 'wrap'
                }
              },
              row.name
            );
          },
        },
        {
          title: '描述',
          key: 'summary',
          width: 90,
          resizable: true,
        },
        {
          title: '单位',
          key: 'unit',
          width: 90,
          resizable: true,
        },
        {
          title: '收货数量',
          key: 'origin_receipt_num',
          minWidth: 90,
          resizable: true,
        },
        {
          title: '收货单价',
          key: 'origin_receipt_price',
          minWidth: 90,
          resizable: true,
        },
        {
          title: '收货金额',
          key: 'origin_receipt_total_price',
          minWidth: 90,
          resizable: true,
        },
        {
          title: '入库数量',
          key: 'receipt_num',
          width: 120,
          resizable: true,
          render: (h, param) => {
            let { row } = param;
            let text = [];
            // 合计
            if (param.index === this.orderInfo.commoditys.length - 1) {
              return h('span', row.bill_num);
            }
            if (row.receipt_num - row.bill_num == 0) {
              text = [h('p', row.receipt_num)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_num,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_num,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '建议采购价',
          key: 'reference_price',
          width: 120,
          resizable: true,
          renderHeader(h) {
            return [
              h('span', '建议采购价'),
              h(
                'Tooltip',
                {
                  props: {
                    maxWidth: 300,
                    transfer: true,
                    content:
                      '新增采购单时的默认采购价（优先取创建时的协议价，无协议价时取创建的最近一次进价）',
                  },
                },
                [
                  h(SIcon, {
                    props: {
                      icon: 'help1',
                      size: 12,
                    },
                    style: {
                      marginLeft: '3px',
                      verticalAlign: 'baseline',
                    },
                    class: ['pointer'],
                  }),
                ],
              ),
            ];
          },
        },
        {
          title: '最近一次进价',
          width: 120,
          resizable: true,
          key: 'last_in_price',
        },
        {
          title: '入库单价',
          key: 'receipt_price',
          width: 120,
          resizable: true,
          render: (h, param) => {
            let { row } = param;
            let text = [];
            if (row.receipt_price - row.bill_price == 0) {
              text = [h('p', row.receipt_price)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_price,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_price,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '入库金额',
          width: 120,
          resizable: true,
          key: 'receipt_total_price',
          renderHeader: (h) => {
            return h(
              'span',
              `入库金额${this.isOpenStoreNoTax ? '(含税)' : ''}`,
            );
          },
          render: (h, param) => {
            let { row } = param;
            let text = [];
            // 合计
            if (param.index === this.orderInfo.commoditys.length - 1) {
              return h('span', row.bill_total_price);
            }
            if (row.receipt_total_price - row.bill_total_price == 0) {
              text = [h('p', row.receipt_total_price)];
            } else {
              text = [
                h(
                  'p',
                  {
                    style: {
                      textDecoration: 'line-through',
                    },
                  },
                  row.receipt_total_price,
                ),
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                    },
                  },
                  row.bill_total_price,
                ),
              ];
            }
            return h('div', text);
          },
        },
        {
          title: '对账抹零',
          key: 'item_bill_reduction_price',
          width: 100,
          resizable: true,
        },
        {
          title: '差异数量',
          key: 'diff_num',
          width: 100,
          resizable: true,
        },
        {
          title: '差异单价',
          key: 'diff_price',
          width: 100,
          resizable: true,
        },
        {
          title: '差异金额',
          key: 'diff_total_price',
          width: 100,
          resizable: true,
        },
        {
          title: '差异备注',
          key: 'diff_remark',
          width: 100,
          resizable: true,
        },
      ],
      opCols: [
        {
          title: '操作人',
          key: 'name',
        },
        {
          title: '时间',
          key: 'time',
        },
        {
          title: '类型',
          key: 'type',
        },
        {
          title: '日志',
          render: (h, params) => {
            let { row } = params;
            let content = [];
            if (row.content && Array.isArray(row.content)) {
              row.content.forEach((item) => {
                content.push(h('p', item));
              });
            } else {
              content = row.content;
            }
            return h('div', content);
          },
        },
      ],
      list: [],
      opList: [],
      orderInfo: {
        type: 0
      },
    };
  },
  created() {},
  mounted() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      purchaseAuditService
        .getAuditDetail({ id: this.$route.query.id })
        .then((res) => {
          if (res.status) {
            let data = res.data.order;
            let sumRow = {
              index: '合计：',
              bill_num: 0,
              bill_total_price: 0,
              diff_num: 0,
              diff_total_price: 0,
              item_bill_reduction_price: 0,
            };
            data.commoditys.forEach((item, index) => {
              item.index = index + 1;
              sumRow.bill_num += item.bill_num * 1;
              sumRow.bill_total_price += item.bill_total_price * 1;
              sumRow.diff_num += item.diff_num * 1;
              sumRow.diff_total_price += item.diff_total_price * 1;
              sumRow.item_bill_reduction_price +=
                item.item_bill_reduction_price * 1;
            });
            sumRow.bill_num = sumRow.bill_num.toFixed(2);
            sumRow.bill_total_price = sumRow.bill_total_price.toFixed(2);
            sumRow.diff_num = sumRow.diff_num.toFixed(2);
            sumRow.diff_total_price = sumRow.diff_total_price.toFixed(2);
            sumRow.item_bill_reduction_price =
              sumRow.item_bill_reduction_price.toFixed(2);
            data.commoditys.push(sumRow);
            this.orderInfo = data;
          } else {
            this.modalError(res.message);
          }
        });
    },
  },
};
</script>

<style lang="less"></style>
<style scoped lang="less">
@gutter: 15px;
#user-audit-detail {
  text-align: left;
  .base-info {
    .title {
      margin-top: @gutter;
    }
    .ivu-row-flex {
      margin: @gutter;
    }
    .base-info-content {
      margin: @gutter;
    }
  }
  .order-list-content {
    padding: @gutter;
    /deep/.ivu-table-body {
    overflow-x: auto!important;
  }
  }
  .remark {
    margin-bottom: @gutter;
    margin-left: @gutter;
  }
}
.base-info-content {
  .ivu-col {
    height: 36px;
    line-height: 36px;
  }
}
/deep/ .ivu-col {
  padding-left: 16px!important;
  padding-right: 0px!important;
}
</style>
