<!--
 * @Descripttion: 客户结算，点击对账，对账页面
 * @url: http://0.0.0.0:8089/#/finance/userAudit?keep_scroll=1&refer_no=DD21022000019&type=1
 * @Author: lizhiwei 🙂
-->

<template>
  <div>
    <div id="user-audit">
      <div class="content-wrap">
        <div class="base-info">
          <div class="title">
            <Icon custom="ricon iconfont sdpicon-dian--" color="#03ac54"></Icon>
            基础信息
          </div>
          <div class="base-info-content">
            <Row type="flex" align="middle" :gutter="50" class="info-content-row">
              <Col span="6"> 业务单号：{{ orderInfo.refer_no }} </Col>
              <Col span="6"> 客户名称：{{ orderInfo.user_name }} </Col>
              <Col span="6" v-if="sysConfig.tc_platform == 1"> 客户业态：{{ orderInfo.user_business_type_desc }} </Col>
              <Col span="6" v-if="page_type==='new'">子账号：{{orderInfo.sub_user_name || '-'}}</Col>
              <Col span="6"> 对账单号：{{ orderInfo.bill_no }} </Col>
              <Col span="6"> 对账人员：{{ orderInfo.bill_user || '-'}} </Col>
              <Col span="6"> 创建日期：{{ orderInfo.create_time }} </Col>
              <Col span="6"> {{isReturn? '退货': '发货'}}日期：{{ orderInfo.delivery_date }} </Col>
              <Col span="6"> 结算状态：{{ orderInfo.status_desc }} </Col>
              <!-- 开启订单按供应商拆单、团餐、销售订单 -->
              <template v-if="sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && $route.query.page_type == 'new'">
                <Col span="6" v-if="orderInfo.type == 1 || orderInfo.type == 2">
                  所属供应商：{{orderInfo.split_provider_name || '-'}}
                </Col>
              </template>
              <Col span="6"> 对账状态：{{ orderInfo.is_bill_desc }} </Col>
              <Col span="6"> 单据金额：{{ orderInfo.original_price }} </Col>
              <Col span="6" v-if="!isReturn && !isCustomerExpense">
                整单折扣：
                <NumberInput
                  v-model="orderInfo.bill_discount"
                  @on-change="discountChange"
                  precision="2"
                  style="width: 160px;"
                />
                <span>%</span>
              </Col>
              <Col span="6">
                对账金额：
                <NumberInput
									:disabled="isCustomerExpense"
                  v-model="orderInfo.bill_price"
                  @on-change="billPriceChange"
                  precision="2"
                />
              </Col>
              <Col span="6">
                差异金额：
                <Input :disabled="true" v-model="orderInfo.diff_price" />
              </Col>
              <Col span="6" v-if="!isReturn && !isCustomerExpense"> 应收金额：{{ ((orderInfo.bill_price || 0) - (orderInfo.bill_reduction_price || 0)).toFixed(2) }} </Col>
              <template v-if="sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && $route.query.page_type == 'new'">
                <Col span="24" v-if="sysConfig.tc_platform == 1 && (orderInfo.type == 1 || orderInfo.type == 2)"> {{orderInfo.type == 1 ? '采购对账单号' : '采购退回对账单号'}}：
                  <template v-if="orderInfo.purchase_bill_info && orderInfo.purchase_bill_info.length">
                    <span v-for="(item, index) in orderInfo.purchase_bill_info" :key="item.id">
                      <RelationNo :id='item.id' :no='item.bill_no'></RelationNo>{{ orderInfo.purchase_bill_info.length - 1 > index ? '、' : ''}}
                    </span>
                  </template>
                  <span v-else>-</span>
                </Col>
              </template>
            </Row>
            <Row type="flex" align="middle" :gutter="50">
              <Col span="6" :key="field.id" v-for="field in customizeFields" style="margin-bottom: 16px;">
                {{ field.name }}：
                <Input v-model="field.value"/>
              </Col>
            </Row>
          </div>
        </div>
        <div class="order-list">
          <div class="title">
            <Icon custom="ricon iconfont sdpicon-dian--" color="#03ac54"></Icon>
						{{ isCustomerExpense? '单据明细' : '订单商品清单' }}
          </div>
          <div class="order-list-content">
						<Table
							v-if="isCustomerExpense"
							:columns="customerExpenseCol"
							:data="list"
						>
						</Table>
            <SVxeEditableTable
							v-else
              refs="table"
              ref="editTable"
              :data="list"
              @on-cell-change="handleCellChange"
              :editIncell="true"
              :hover-show="true"
              :columns="cols"
              minWidth="1800"
              :tableId="orderInfo.type == 2 ? 'audit_return' : ('audit_' + ($route.query.page_type || 'old'))"
            >
            <template #after-table><span></span></template>
          </SVxeEditableTable>
          </div>
          <div v-if="!isReturn && !isCustomerExpense" class="extra">
            <span class="label" style="flex:unset;">对账抹零金额：</span>
            <NumberInput
              v-model="orderInfo.bill_reduction_price"
              precision="2"
              min="0"
              @on-change="handleBillReductionPrice"
            />
          </div>
          <div class="extra">
            <span class="label">备注：</span>
            <Input
              type="textarea"
              show-word-limit
              maxlength="512"
              :autosize = "{
                minRows: 2,
                maxRows: 8
              }"
              v-model="orderInfo.remark"
            />
          </div>
        </div>
      </div>
    </div>
    <Row class="fixedBtns">
      <Col span="6" align="left">
        <Button type="primary" @click="save" :disabled="saving">{{
          saving ? '对账并保存中...' : '对账并保存'
        }}</Button
        >&nbsp;&nbsp; <Button @click="goBack">取 消</Button>&nbsp;&nbsp;
      </Col>
    </Row>
  </div>

</template>

<script>
import NumberInput from '@components/basic/NumberInput';
import HotKey from '../../../util/HotKey';
import UserAudit from '@api/UserAudit.js';
import ConfigMixin from '@/mixins/config.js'
import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
import RelationNo from '@/components/relation-no'

let userAuditService = new UserAudit();
const INPUT_HOT_KEY_CLASS = 'input-hot-key';

export default {
  name: 'userAuditDetail',
  mixins: [ConfigMixin],
  components: {
    SVxeEditableTable,
    NumberInput,
    RelationNo
  },
	computed: {
		isCustomerExpense() {
			return +this.orderInfo.type === 10
		}
	},
  data() {
    const titleType = this.$route.query.page_type === 'new' ? 'customer_settlement_reconciliation_new' : 'customer_settlement_reconciliation';
    return {
			customerExpenseCol: [
				{
					title: '序号',
					key: 'index',
				},
				{
					title: '付款方式',
					key: 'fee_pay_way_desc'
				},
				{
					title: '应收/应付',
					key: 'fee_payment_desc'
				},
				{
					title: '费用金额',
					key: 'original_sub_price',
				},
				{
					title: '对账金额',
					key: 'sub_price',
					render: (h, params) => {
						const { row } = params
						if (row.index === '合计') {
							return h('span', row.sub_price)
						}
						const limit = +row.fee_payment === 1? 'min' : 'max'
						const limitBest = limit === 'min'? 'max' : 'min'
						return h(NumberInput, {
							props: {
								value: +row.sub_price,
								allowNegativeDecimal: true,
								precision: 2,
								[limitBest]: limitBest === 'min'? -9999999999.99 : 9999999999.99
							},
							on: {
								'on-change': (val) => {
									row.sub_price = val
									this.handleCellChange(row, params.index, 'sub_price')
								}
							}
						})
					}
				},
				{
					title: '备注',
					key: 'diff_remark',
					render: (h, params) => {
						const { row, index } = params
						if (row.index === '合计') {
							return h('span', '--')
						}
						return h('Input', {
							style: {
								width: '120px'
							},
							props: {
								value: row.diff_remark,
								placeholder: '请输入备注',
								maxlength: 20,
							},
							on: {
								'on-change': (e) => {
									row.diff_remark = e.target.value
									this.list[index]['diff_remark'] = e.target.value
								},
								'on-enter': () => {
									this.list[index]['diff_remark'] = row['diff_remark']
								}
							},
						})
					}
				}
			],
      customizeFields: [],
      newBillPrice: '',
      saving: false,
      diffUnitPrice: 0,
      cols: [
        ...(this.$route.query.type !=2 ?[{
          type: 'titleCfg',
          titleType,
          width: 28,
          fixed: 'left',
        },] : []),
        {
          title: '序号',
          key: 'index',
          width: 60
        },
        {
          title: '商品名称',
          key: 'commodity_name',
          minWidth: 140,
          render: (h, params) => {
            let { row } = params;
            if (row.index == '合计') {
              return ''
            }

            const toolTipTemp = row.commodity_name.length > 10 ? h('Tooltip', {
                props: {
                  content: row.commodity_name,
                  placement: 'bottom',
                  transfer: true,
                 'max-width': "auto"
                }
              },[h('p',{
                style: {
                  cursor: 'pointer',
                  width: '85%',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  marginBottom: '-8px'
                }
              }, row.commodity_name)]) : h('p', row.commodity_name)
            const toolTipTemp2 = h(
                'p',
                {
                  props: {
                    title: row.commodity_code
                  },
                  attrs: {
                    title: row.commodity_code
                  },
                  style: {
                    color: '#999',
                    width: '85%',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis'
                  }
                },
                row.commodity_code
              )
            return h('div', {
              class: 'commodity_name'
            }, [
              toolTipTemp,
              toolTipTemp2
            ]);
          }
        },
        {
          title: '客户商品别名',
          key: 'user_commodity_alias_name',
          minWidth: 100,
          showOverflow: 'title',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.user_commodity_alias_name || '-')}</span>
          }
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 120,
          showOverflow: 'title',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.summary || '-')}</span>
          }
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          showOverflow: 'title',
          minWidth: 80,
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.unit_convert_text || '-')}</span>
          }
        },
        {
          title: '发货单位',
          key: 'unit',
          minWidth: 80,
          showOverflow: 'title',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.unit || '-')}</span>
          }
        },
        {
          title: '发货数量',
          key: 'amount',
          minWidth: 90,
          render: (h, params) => {
            let { row, index } = params;
            let text = [];
            if (row.index !== '合计') {
              text = [
                h('InputNumber', {
									style: {
										marginTop: this.opList[index].original_amount == row.amount? '0' : '10px'
									},
                  class: [HotKey.getHotKeyClass()],
                  attrs: {
                    id: 'recg' + row.bill_item_id
                  },
                  props: {
                    value: Number(row.amount),
                    min: 0,
                    precision: 2,
                  },
                  on: {
                    'on-change': res => {
                      row.amount = res;
                      this.handleCellChange(
                        row,
                        params.index,
                        'amount'
                      );
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
								h(
									'div',
									{
										style: {
											marginBottom: '10px',
											color: 'red',
											// 划线
											textDecoration: 'line-through'
										},
										class: {
											'dn': this.opList[index].original_amount == row.amount
										}
									},
									`${this.opList[index].original_amount}`
								)
              ];
            } else {
              text = [h('p', {}, row.amount)];
            }
            return h('div', text);
          }
        },
        {
          title: '发货单价',
          key: 'unit_price',
          minWidth: 90,
          render: (h, params) => {
            let { row, index } = params;
            let text = [];
            // 合计
            if (params.index === this.list.length - 1) {
              return h('span', '');
            }
            text = [
              h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
								style: {
									marginTop: this.opList[index].original_unit_price == row.unit_price? '0' : '10px'
								},
                props: {
                  value: (row.unit_price * 1).toFixed(4),
                  min: 0,
                  precision: 4,
                },
                on: {
                  'on-change': res => {
                    row.unit_price = res;
                    // row.unit_price = res;
                    this.handleCellChange(row, params.index, 'unit_price');
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
                nativeOn: {
                  keyup: event => {
                    if (event.keyCode == 13) {
                      this.handleMoveLeftAndRight(event, params);
                    }
                  }
                }
              }),
							h(
								'p',
								{
									style: {
										marginBottom: '10px',
										color: 'red',
										// 划线
										textDecoration: 'line-through'
									},
									class: {
										'dn': this.opList[index].original_unit_price == row.unit_price
									}
								},
								`${this.opList[index].original_unit_price}`
							)
            ];
            return h('div', text);
          }
        },
        {
          title: '发货金额',
          key: 'sub_price',
          minWidth: 120,
          render: (h, params) => {
            let { row } = params;
            let text = [];
            if (row.index !== '合计') {
              text = [
                h('InputNumber', {
                  class: [HotKey.getHotKeyClass()],
									style: {
										marginTop: this.opList[params.index].original_sub_price == row.sub_price? '0' : '10px'
									},
                  props: {
                    value: Number(row.sub_price),
                    min: 0,
                    precision: 2,
                  },
                  on: {
                    'on-change': res => {
                      row.sub_price = res;
                      this.handleCellChange(
                        row,
                        params.index,
                        'sub_price'
                      );
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
								h(
									'p',
									{
										style: {
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
											marginBottom: '10px',
											color: 'red',
											// 划线
											textDecoration: 'line-through'
										},
										class: {
											'dn': this.opList[params.index].original_sub_price == row.sub_price
										}
									},
									`${this.opList[params.index].original_sub_price}`
								)
              ];
            } else if (row.index == '合计') {
              text = [h('p', {
                style: {
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                }
              }, row.sub_price)];
            }
            return h('div', text);
          }
        },
        {
          title: '对账抹零',
          key: 'item_bill_reduction_price',
          minWidth: 90,
          render: (h, params) => {
            let { row } = params;
            if (row.index !== '合计') {
              return h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: Number(row.item_bill_reduction_price),
                  min: 0,
                  precision: 2,
                },
                on: {
                  'on-change': res => {
                    row.item_bill_reduction_price = res;
                    this.handleReductionPriceCellChange(
                      row,
                      params.index,
                      'item_bill_reduction_price'
                    );
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
                nativeOn: {
                  keyup: event => {
                    if (event.keyCode == 13) {
                      this.handleMoveLeftAndRight(event, params);
                    }
                  }
                }
              });
            } else {
              return h('span', this.orderInfo.bill_reduction_price)
            }
          }
        },
        {
          title: '发货金额（港币）',
          minWidth: 150,
          key: 'sub_price_hkd',
          render: (h, params) => {
            let { row } = params;
            let text = [];
            if (row.index === '合计') {
              text = [h('p', {}, row.sub_price_hkd)];
              return h('div', text);
            }
            text = [
              h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: Number(row.sub_price_hkd),
                  min: 0,
                  disabled: (row.exchange_rate_hkd * 1) === 0,
                },
                on: {
                  'on-change': res => {
                    row.sub_price_hkd = res;
                    this.handleCellChange(
                      row,
                      params.index,
                      'sub_price_hkd'
                    );
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
                nativeOn: {
                  keyup: event => {
                    if (event.keyCode == 13) {
                      this.handleMoveLeftAndRight(event, params);
                    }
                  }
                }
              })
            ];
            return h('div', text);
          }
        },
        {
          title:'协议市场价小计',
          key: 'protocol_org_total_price',
          minWidth: 120
        },
        {
          title: '差异数量',
          key: 'diff_amount',
          minWidth: 80,
        },
        {
          title: '差异单价',
          minWidth: 80,
          key: 'diff_price',
          render: (h, params) => {
            let { row } = params;
            // 合计
            if (params.index === this.list.length - 1) {
              return false;
            }
            return h(
              'span',
              (row.unit_price - row.original_unit_price).toFixed(2)
            );
          }
        },
        {
          title: '差异金额',
          key: 'diff_sub_price',
          minWidth: 80,
        },
				{
					title: '差异备注',
					minWidth: 140,
					key: 'diff_remark',
					render: (h, params) => {
						const { row, index } = params
						if (row.index === '合计') {
							return h('span', '')
						}
						return h('i-input', {
							props: {
								value: row.diff_remark,
								placeholder: '最多输入20字',
								maxlength: 20,
								type: "textarea",
							},
							class: {
								remarks: true
							},
							on: {
								'on-change': (e) => {
									row.diff_remark = e.target.value
									this.list[index]['diff_remark'] = e.target.value
								},
								'on-enter': () => {
									this.list[index]['diff_remark'] = row['diff_remark']
								}
							},
						})
					}
				},
        {
          title: '订单商品备注',
          minWidth: 140,
          key: 'remark',
          render(h, params) {
            return <span>{ params.row.remark || '-'}</span>
          }
        }
      ],
      originCols: [],
      opCols: [
        {
          title: '操作人',
          key: ''
        },
        {
          title: '时间',
          key: ''
        },
        {
          title: '类型',
          key: ''
        },
        {
          title: '日志',
          key: ''
        }
      ],
      list: [],
      opList: [],
      orderInfo: {
        bill_reduction_price: '',
        refer_no: '',
        user_name: '',
        bill_no: '',
        create_time: '',
        delivery_date: '',
        status_desc: '',
        bill_price: '',
        bill_user: '',
        diff_price: '',
        original_price: '',
        remark: '',
      },
      returnCols: [
        {
          type: 'titleCfg',
          titleType: 'customer_settlement_reconciliation_return',
          width: 28,
          fixed: 'left',
        },
        {
          title: '序号',
          key: 'index',
          width: 70
        },
        {
          title: '商品名称',
          key: 'commodity_name',
          minWidth: 150,
          render: (h, params) => {
            let { row } = params;
            if (row.index == '合计') {
              return ''
            }
            const toolTipTemp = row.commodity_name.length > 10 ? h('Tooltip', {
                style: {
                   width: '100%',
                },
                props: {
                  content: row.commodity_name,
                  placement: 'bottom',
                  transfer: true,
                 'max-width': "auto"
                }
              },[h('p',{
                style: {
                  cursor: 'pointer',
                  width: '85%',
                  overflow: 'hidden',
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  marginBottom: '-8px'
                }
              }, row.commodity_name)]) : h('p', row.commodity_name)
            const toolTipTemp2 = h(
                'p',
                {
                  props: {
                    title: row.commodity_code
                  },
                  attrs: {
                    title: row.commodity_code
                  },
                  style: {
                    color: '#999',
                    width: '85%',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    textOverflow: 'ellipsis'
                  }
                },
                row.commodity_code
              )
            return h('div', {
              class: 'commodity_name'
            }, [
              toolTipTemp,
              toolTipTemp2
            ]);
          }
        },
        {
          title: '描述',
          key: 'summary',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.summary || '-')}</span>
          }
        },
        {
          title: '单位',
          key: 'unit',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.unit || '-')}</span>
          }
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          render(h, {row}) {
            return <span>{row.index == '合计'? '' : (row.unit_convert_text || '-')}</span>
          }
        },
        {
          title: '退货数量',
          key: 'amount',
          minWidth: 100,
          render: (h, params) => {
            let { row, index } = params;
            let text = [];
            if (row.index !== '合计') {
              text = [
                h('InputNumber', {
                  class: [HotKey.getHotKeyClass()],
                  attrs: {
                    id: 'recg' + row.bill_item_id
                  },
                  props: {
                    value: Number(row.amount),
                    min: 0,
                    precision: 2
                  },
                  on: {
                    'on-change': res => {
                      row.amount = res;
                      this.handleCellChange(
                        row,
                        params.index,
                        'amount'
                      );
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
                h(
                    'p',
                    {
                      style: {
                        marginBottom: '10px',
												color: 'red',
												// 划线
												textDecoration: 'line-through'
                      },
											class: {
												'dn': this.opList[index].original_amount == row.amount
											}
                    },
                    `${this.opList[index].original_amount}`
                )
              ];
            } else {
              text = [h('p', {}, row.amount)];
            }
            return h('div', text);
          }
        },
        {
          title: '退货单价',
          key: 'unit_price',
          minWidth: 100,
          render: (h, params) => {
            let { row, index } = params;
            let text = [];
            // 合计
            if (params.index === this.list.length - 1) {
              return h('span', '');
            }
            text = [
              h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: (row.unit_price * 1).toFixed(4),
                  min: 0,
                  precision: 4,
                },
                on: {
                  'on-change': res => {
                    row.unit_price = res;
                    // row.unit_price = res;
                    this.handleCellChange(row, params.index, 'unit_price');
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
                nativeOn: {
                  keyup: event => {
                    if (event.keyCode == 13) {
                      this.handleMoveLeftAndRight(event, params);
                    }
                  }
                }
              }),
              h(
                  'p',
                  {
                    style: {
                      marginBottom: '10px',
											color: 'red',
											// 划线
											textDecoration: 'line-through'
                    },
										class: {
											'dn': this.opList[index].original_unit_price == row.unit_price
										}
                  },
                  `${this.opList[index].original_unit_price}`
              )
            ];
            return h('div', text);
          }
        },
        {
          title: '退货金额',
          key: 'sub_price',
          minWidth: 100,
          render: (h, params) => {
            let { row, index } = params;
            let text = [];
            if (row.index !== '合计') {
              text = [
                h('InputNumber', {
                  class: [HotKey.getHotKeyClass()],
                  props: {
                    value: Number(row.sub_price),
                    min: 0
                  },
                  on: {
                    'on-change': res => {
                      row.sub_price = res;
                      this.handleCellChange(
                        row,
                        params.index,
                        'sub_price'
                      );
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
                h(
                    'p',
                    {
                      style: {
                        marginBottom: '10px',
												color: 'red',
												// 划线
												textDecoration: 'line-through'
                      },
											class: {
												'dn': this.opList[index].original_sub_price == row.sub_price
											}
                    },
                    `${this.opList[index].original_sub_price}`
                )
              ];
            } else if (row.index == '合计') {
              text = [h('p', {}, row.sub_price)];
            }
            return h('div', text);
          }
        },
        {
          title: '差异数量',
          key: 'diff_amount'
        },
        {
          title: '差异单价',
          key: 'diff_price',
          render: (h, params) => {
            let { row } = params;
            // 合计
            if (params.index === this.list.length - 1) {
              return false;
            }
            return h(
              'span',
              (row.unit_price - row.original_unit_price).toFixed(2)
            );
          }
        },
        {
          title: '差异金额',
          key: 'diff_sub_price',
          minWidth: 80,
        },
        {
          title: '差异备注',
          width: 120,
          key: 'diff_remark',
          render: (h, params) => {
            const { row, index } = params
            if (row.index === '合计') {
              return h('span', '')
            }
            return h('i-input', {
              props: {
                value: row.diff_remark,
                placeholder: '最多输入20字',
                maxlength: 20,
                type: "textarea",
              },
              class: {
                remarks: true
              },
              on: {
                'on-change': (e) => {
                  row.diff_remark = e.target.value
                  this.list[index]['diff_remark'] = e.target.value
                },
                'on-enter': () => {
                  this.list[index]['diff_remark'] = row['diff_remark']
                }
              },
            })
          }
        }
      ],
      isReturn: false, // 是否是退货单 - 对账
      bill_protocol_discount: 0,
      page_type: ''
    };
  },
  watch: {
  'orderInfo.is_show_hkd'(n) {
    if (+n === 0) {
      this._filterCols()
    } else {
      this.cols = this.deepClone(this.originCols)
    }
  }
  },
  created() {
    this.page_type = this.$route.query.page_type;
    this.originCols = this.deepClone(this.cols)
    this.getDetail();
    !this.orderInfo.is_show_hkd && this._filterCols()
  },
  methods: {
    billPriceChange (val, isChangeDiscount) {
      const len = this.list.length
      if (len>2) {
        let lastSubPrice = this.orderInfo.bill_price
        this.list.forEach((item,index) => {
          // 排除汇总数据
          if (index<len-1) {
            if (index < len-2) {
              // console.log('bill_price', this.orderInfo.bill_price)
              // console.log('比例', this.orderInfo.bill_price/this.orderInfo.original_price)
              item.sub_price = parseFloat(this.orderInfo.bill_price/this.orderInfo.original_price*this.oldSubPriceList[index]).toFixed(2)
              this.handleCellChange(item,index,'sub_price', false);
              // console.log('item', item.sub_price)
              lastSubPrice = lastSubPrice.sub(item.sub_price);
            } else {
              item.sub_price = lastSubPrice
              this.handleCellChange(item,index,'sub_price', false);
            }
          }
        })
      } else {
        this.list[0].sub_price = val
        this.handleCellChange(this.list[0],0,'sub_price', false);
      }
      if(!isChangeDiscount) {
        this.orderInfo.bill_discount = +this.orderInfo.original_price ? this.orderInfo.bill_price.div(Number(this.orderInfo.original_price)).mul(100).toFixed(2) : '100.00'
      }
    },
    discountChange (val) {
      this.orderInfo.bill_price = Number(this.orderInfo.original_price).mul(val.div(100)).toFixed(2)
      this.billPriceChange(this.orderInfo.bill_price, true)
    },
    _filterCols(){
      const SUB_PRICE_HKD = 'sub_price_hkd';
      this.cols = this.cols.filter(col => col.key !== SUB_PRICE_HKD)
    },
    handleMoveLeftAndRight(event, params) {
      let vm = this;
      let indexCursor = 1;
      vm.$nextTick(() => {
        let $tableRows = vm.$refs['editTable'].$el.querySelectorAll(
          '.vxe-body--row'
        );
        if (!$tableRows) {
          return false;
        }
        let $currentRow = $tableRows[params.index];
        let $hotKeyItems = $currentRow.querySelectorAll(
          `.${INPUT_HOT_KEY_CLASS} input`
        );
        let hotKeyItems = Array.from($hotKeyItems);
        let itemIndex = hotKeyItems.indexOf(event.target);
        let $nextTarget = hotKeyItems[itemIndex + indexCursor];
        if ($nextTarget) {
          $nextTarget.focus();
        }
      });
    },
    async initCols() {
      if(this.orderInfo.type === '1') {
          this.cols.splice(7, 0,         {
          title: '发货数量(原)',
          key: 'convert_actual_amount',
          minWidth: 100,
          render: (h, params) => {
            let { row, index } = params;
            let key = 'convert_actual_amount';
            let text = [];
            if (row.index !== '合计') {
              // if (row.unit_convert === 'Y' && Number(row.unit_num) - 1 !== 0) {
              //   row[key] = (row.amount / row.unit_num).toFixed(2);
              // } else {
              //   row[key] = row.amount;
              // }
              text = [
                h('InputNumber', {
									style: {
										marginTop: this.opList[index].original_convert_actual_amount == row.convert_actual_amount? '0' : '10px'
									},
                  class: [HotKey.getHotKeyClass()],
                  attrs: {
                    id: 'recg' + row.bill_item_id
                  },
                  props: {
                    value: Number(row[key]),
                    min: 0,
                    max: 999999999.99,
                    precision: 2,
                  },
                  on: {
                    'on-change': res => {
                      row.convert_actual_amount = res;
                      this.handleCellChange(
                        row,
                        params.index,
                        key
                      );
                    },
                    'on-focus': event => {
                      event.target.select();
                    }
                  },
                  nativeOn: {
                    keyup: event => {
                      if (event.keyCode == 13) {
                        this.handleMoveLeftAndRight(event, params);
                      }
                    }
                  }
                }),
								h(
									'div',
									{
										style: {
											marginBottom: '10px',
											color: 'red',
											// 划线
											textDecoration: 'line-through'
										},
										class: {
											'dn': this.opList[index].original_convert_actual_amount == row.convert_actual_amount
										}
									},
									// `${(this.opList[index].original_convert_actual_amount).toFixed(2)}`
									`${this.forMatNumToFloat(this.opList[index].original_convert_actual_amount)}`
								)
              ];
            } else {
              text = [h('p', {}, row.convert_actual_amount)];
            }
            return h('div', text);
          }
        },)
      }
      if(this.orderInfo.type == '2') {
        // 退货单对账
        this.isReturn = true
        this.cols = this.returnCols
        return
      }
      const config = await this.commonService.getConfig()
      let { bill_protocol_discount, provider_supplier_pool_switch } = config;
      this.bill_protocol_discount = bill_protocol_discount
      const protocolPriceCol = {
        title: '协议市场价',
        key: 'protocol_org_price',
        width: 100,
        render: (h, params) => {
          let { row } = params;
          let text = [];
          if (row.index !== '合计') {
            if (row.original_protocol_org_price === '-') {
              return h('p', '-')
            }
            text = [
              h('span', {
                style: { 'margin-left': '4px' }
              }, row.protocol_org_price),
              +row.original_protocol_org_price === +row.protocol_org_price ? null :
              h('div', {
                style: {
                  'text-decoration': 'line-through',
                   color: 'red',
                }
              }, row.original_protocol_org_price)
            ];
            return h('div', text);
          }
        }
      };
      const protocolDisCountCol = {
        title: '协议折扣',
        key: 'protocol_discount',
        minWidth: 120,
        render: (h, params) => {
          let { row } = params;
          let text = [];
          if (row.index !== '合计') {
            if (row.original_protocol_discount === '-') {
              return h('p', '-')
            }
            text = [
              h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: parseFloat(row.protocol_discount),
                  min: 0,
                  max: 1000,
                  precision: 2,
                },
                on: {
                  'on-change': val => {
                    row.protocol_discount = val || 0
                    row.protocol_org_price = val ? (row.unit_price / val * 100).toFixed(2) : '-'
                    this.handleCellChange(row, params.index, 'protocol_discount');
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
              }),
              h('span', {
                style: { 'margin-left': '4px' }
              }, '%'),
              +row.original_protocol_discount  === +row.protocol_discount ? null : h('div', {
                style: {
                  'text-decoration': 'line-through',
                   color: 'red',
                }
              }, row.original_protocol_discount === '-' ? row.original_protocol_discount : row.original_protocol_discount + '%')
            ];
            return h('div', text);
          }
        }
      };
      const poolProviderDesc = {
        title: '联营供应商',
        key: 'pool_provider_desc',
        width: 130,
        render(h, {row}) {
          return <span>{row.index == '合计'? '':(row.pool_provider_desc || '-')}</span>
        }
      }
      if (Number(provider_supplier_pool_switch) === 1) {
        let index = this.cols.findIndex(col => col.key === 'commodity_name') + 1;
        this.cols.splice(index, 0, poolProviderDesc);
      }
      if (Number(bill_protocol_discount) === 1) {
        let index = this.cols.findIndex(col => col.key === 'unit') + 1;
        if (!this.cols.find(col => col.key === protocolPriceCol.key)) {
          this.cols.splice(index, 0, protocolPriceCol);
        }
        if (!this.cols.find(col => col.key === protocolDisCountCol.key)) {
          this.cols.splice(index + 1, 0, protocolDisCountCol);
        }
      } else {
        if (this.cols.find(col => col.key === protocolPriceCol.key)) {
          this.cols.splice(
            this.cols.findIndex(col => col.key === protocolPriceCol.key),
            1
          );
        }
        if (this.cols.find(col => col.key === protocolDisCountCol.key)) {
          this.cols.splice(
            this.cols.findIndex(col => col.key === protocolDisCountCol.key),
            1
          );
        }
      }
      const bargainPriceColumn = {
        title: '结算价',
        key: 'bargain_price',
        minWidth: 90,
        render: (h, params) => {
          let { row } = params;
          let text = [];
          if (row.index !== '合计') {
            text = [
              h('InputNumber', {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: Number(row.bargain_price),
                  min: 0
                },
                on: {
                  'on-change': res => {
                    row.bargain_price = res;
                    this.handleCellChange(
                      row,
                      params.index,
                      'bargain_price',
                      false
                    );
                  },
                  'on-focus': event => {
                    event.target.select();
                  }
                },
                nativeOn: {
                  keyup: event => {
                    if (event.keyCode == 13) {
                      this.handleMoveLeftAndRight(event, params);
                    }
                  }
                }
              })
            ];
          } else if (row.index == '合计') {
            text = [h('p', {}, row.bargain_price)];
          }
          return h('div', text);
        }
      }
      const bargainSubPriceColumn = {
        title: '结算金额',
        key: 'sub_bargain_price',
        minWidth: 80,
        render: (h, params) => {
          let { row } = params;
          let text = [];
          if (row.index !== '合计') {
            text = [h('p', {}, row.sub_bargain_price)]
          } else if (row.index == '合计') {
            text = [h('p', {}, row.sub_bargain_price)];
          }
          return h('div', text);
        }
      }
      if (this.isOpenUserBargainPrice) { // user_bargain_price
        let preIndex = this.cols.findIndex(col => col.key === 'sub_price')
        let existIndex = this.cols.findIndex(col => col.key === 'bargain_price')
        if (preIndex && existIndex < 0) {
          // 发货金额后边
          this.cols.splice(preIndex + 1, 0, bargainPriceColumn);
          this.cols.splice(preIndex + 2, 0, bargainSubPriceColumn)
        }
      }
    },
    forMatNumToFloat(num) {
      let formattedNum = new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num);
      return formattedNum || 0
    },

    handleBillReductionPrice(val) {
      let orderCount = this.list.length;
      const reductionPrice = (val / (orderCount - 1)).toFixed(2)
      const tmpList = [].concat(this.list)
      let remain = val;
      tmpList.forEach((item, index) => {
        // 排除合计那行
        if (index <= orderCount - 2) {
          if (remain - reductionPrice >= 0) {
            item.item_bill_reduction_price = reductionPrice;
          } else {
            item.item_bill_reduction_price = remain < 0 ? 0 : remain;
          }
          remain = (remain - reductionPrice).toFixed(2);
        }
      });
      this.list = tmpList
    },
    handleReductionPriceCellChange(row, index) {
      this.list.splice(index, 1, row);
      let orderCount = this.list.length;
      let sumReductionPrice = 0;
      this.list.forEach((item, index) => {
        // 排除合计那行
        if (index <= orderCount - 2) {
          sumReductionPrice += item.item_bill_reduction_price * 1;
        }
      });
      this.orderInfo.bill_reduction_price = sumReductionPrice.toFixed(2)
    },
    handleCellChange(row, index, column, isUpdateBillPrice=true) {
      // let row = data
      if (row.amount && isNaN(row.amount)) {
        this.modalError('请输入有效发货数量', 0);
        return false;
      }
      if (row.unit_price && isNaN(row.unit_price)) {
        this.modalError('请输入有效发货单价', 0);
        return false;
      }
      if (row.sub_price && isNaN(row.sub_price)) {
        this.modalError('请输入有效发货金额', 0);
        return false;
      }
      // 修改发货金额, 重新计算发货单价和对账金额
      // console.log(column);
      if (column === 'sub_price_hkd') {
        row.sub_price = (row.sub_price_hkd * row.exchange_rate_hkd).toFixed(2)
        row.unit_price = row.amount > 0 ? row.sub_price / row.amount : 0;
        row.unit_price = (row.unit_price * 1).toFixed(4);
        // console.log(row.sub_price);
      }

      if (column === 'sub_price') {
        row.sub_price_hkd = (row.sub_price / row.exchange_rate_hkd).toFixed(2)
        row.unit_price = row.amount > 0 ? row.sub_price.div(row.amount) : 0;
        row.unit_price = (row.unit_price * 1).toFixed(4);
        row = this.updateProtocolOrgPrice(row)
        // 数量和小计为准计算单价
        row.calculate_way = 2;
      }

      // 修改发货数量, 重新计算对账金额,发货金额，差异数量，和差异金额，结算金额
      if (column === 'amount') {
        row.amount = (row.amount * 1).toFixed(2);
        if (row.unit_convert === 'Y' && Number(row.unit_num) - 1 !== 0) {
          row.convert_actual_amount = (row.amount / Number(row.unit_num)).toFixed(2);
        } else {
          row.convert_actual_amount = row.amount;
        }
        row = this.computeRowPrice(row);
        // 数量和单价为准计算小计
        row.calculate_way = 1;
      }
      // 修改发货数量（原）, 重新计算发货数量、对账金额,发货金额，差异数量，和差异金额，结算金额
      if (column === 'convert_actual_amount') {
        row.convert_actual_amount = (row.convert_actual_amount * 1).toFixed(2);
        row.amount = row.convert_actual_amount;
        // row = this.computeRowPrice(row);
        if (
          row.unit_convert === 'Y' &&
          Number(row.unit_num) - 1 !== 0
        ) {
          row.amount =
          Number(row.convert_actual_amount) * Number(row.unit_num);
        }
        row = this.computeRowPrice(row);
        // 数量和单价为准计算小计
        row.calculate_way = 1;
      }
      // 修改发货单价, 重新计算对账金额和差异金额
      if (column === 'unit_price') {
        row.unit_price = (row.unit_price * 1).toFixed(4);
        row = this.updateProtocolOrgPrice(row)
        row = this.computeRowPrice(row);
        row.calculate_way = 1;
      }
      // 修改结算价，重新计算结算金额
      if (column === 'bargain_price') {
        row.sub_bargain_price = (row.amount*row.bargain_price).toFixed(2)
      }

      row.diff_amount = (row.amount - row.original_amount).toFixed(2);
      row.diff_unit_price = (row.unit_price - row.original_unit_price).toFixed(
        2
      );
      row.diff_sub_price = (row.sub_price - row.original_sub_price).toFixed(2);
      // this.list.splice(index, 1, row);
      this.computeSumPrice(isUpdateBillPrice);
    },
    /**
     * 重新计算收货金额
     * @param row
     * @returns {*}
     */
    computeRowPrice(row) {
      row.sub_price = Number(row.amount).mul(Number(row.unit_price)).toFixed(2);
      if (
        row.unit_price - row.original_unit_price === 0 &&
        row.amount - row.original_amount === 0
      ) {
        row.sub_price = row.original_sub_price;
      }
      row.sub_price_hkd = (row.sub_price / row.exchange_rate_hkd).toFixed(2)
      row.diff_sub_price = (row.original_sub_price - row.sub_price).toFixed(2);
      row.sub_bargain_price = (row.bargain_price * row.amount).toFixed(2);
      return row;
    },
    /**
     * 重新计算对账金额, 最后一行合计的发货数量，发货金额，差异数量，差异金额
     */
    computeSumPrice(isUpdateBillPrice) {
      let orderCount = this.list.length;
      if(isUpdateBillPrice) {
        this.orderInfo.bill_price = 0;
      }
      this.orderInfo.diff_price = 0;
      let sumAmount = 0;
      let sumConvertActualAmount = 0;
      let sumPrice = 0;
      let sumSubPriceHkd = 0;
      let sumDiffAmount = 0;
      let sumUnitPrice = 0;
      let sumDiffPrice = 0;
      let sumBargainPrice = 0;
      let sumSubBargainPrice = 0;
      this.list.forEach((item, index) => {
        // 排除合计那行
        if (index <= orderCount - 2) {
          if (isUpdateBillPrice) {
            this.orderInfo.bill_price += item.sub_price * 1;
          }
          this.orderInfo.diff_price += item.diff_sub_price * 1;
          sumAmount += item.amount * 1;
          sumConvertActualAmount += item.convert_actual_amount * 1;
          sumPrice += item.sub_price * 1;
          sumSubPriceHkd += item.sub_price_hkd * 1;
          sumUnitPrice += item.unit_price * 1;
          sumDiffAmount += item.diff_amount * 1;
          sumDiffPrice += item.diff_sub_price * 1;
          sumBargainPrice += item.bargain_price *1;
          sumSubBargainPrice += item.sub_bargain_price * 1;
        }
        // 更新合计数据
        if (index === orderCount - 1) {
          item.amount = sumAmount.toFixed(2);
          item.convert_actual_amount = sumConvertActualAmount.toFixed(2);
          item.sub_price_hkd = sumSubPriceHkd.toFixed(2);
          item.sub_price = sumPrice.toFixed(2);
          item.unit_price = sumUnitPrice.toFixed(4);
          item.diff_amount = sumDiffAmount.toFixed(2);
          item.diff_sub_price = sumDiffPrice.toFixed(2);
          item.bargain_price = sumBargainPrice.toFixed(2);
          item.sub_bargain_price = sumSubBargainPrice.toFixed(2);
        }
      });
      if (isUpdateBillPrice) {
        this.orderInfo.bill_price = this.orderInfo.bill_price.toFixed(2);
        this.orderInfo.bill_discount = +this.orderInfo.original_price ? this.orderInfo.bill_price.div(Number(this.orderInfo.original_price)).mul(100).toFixed(2) : '100.00'
      }
      this.orderInfo.diff_price = this.orderInfo.diff_price.toFixed(2);
    },
    // update协议市场价
    updateProtocolOrgPrice (item) {
      if(!isNaN(Number(item.protocol_discount))){
        item.protocol_org_price = item.unit_price? (item.unit_price / (Number(item.protocol_discount)/100)).toFixed(2):0
      }
      return item
    },
    getDetail() {
      let data = {
        refer_no: this.$route.query.refer_no,
        type: this.$route.query.type
      };
      userAuditService.getAuditDetail(data).then(res => {
        let { status, data } = res;
        if (status) {
          let { main, detail } = data;
          // main.remark = '';
          this.orderInfo = main;
          this.orderInfo.bill_discount = main.bill_discount ? Number(main.bill_discount) : 100
          let sumAmount = 0;
          this.list = detail.map((item, index)=> {
            // 添加 发货数量(原)
            if (item.unit_convert === 'Y' && Number(item.unit_num) - 1 !== 0) {
              item.convert_actual_amount = (item.amount / item.unit_num).toFixed(2);
              item.original_convert_actual_amount = (item.original_amount / item.unit_num).toFixed(2);
            } else {
              item.convert_actual_amount = Number(item.amount);
              item.original_convert_actual_amount = Number(item.original_amount);
            }
            sumAmount += item.convert_actual_amount

            if(index === detail.length - 1) {
              item.convert_actual_amount = this.forMatNumToFloat((sumAmount - item.convert_actual_amount).toFixed(2));
            }
						return {
							...item,
              diff_remark: item.diff_remark || '',
              item_bill_reduction_price: item.item_bill_reduction_price || 0,
              original_protocol_discount: item.original_protocol_discount
						}
					})
					this.opList = JSON.parse(JSON.stringify(this.list))
          this.oldSubPriceList = this.list.map((item) => {
            return item.original_sub_price
          })
          this.customizeFields = main.customize_fields || [];
          this.oldSubPriceList.pop()
          if (+detail[detail.length - 1].item_bill_reduction_price !== +this.orderInfo.bill_reduction_price) {
            this.handleBillReductionPrice(this.orderInfo.bill_reduction_price)
          }
          this.initCols()
        } else {
          this.modalError(res.message);
        }
      });
    },
    getSaveData() {
      let data = {
        bill_no: this.orderInfo.bill_no,
        remark: this.orderInfo.remark,
        bill_reduction_price: this.orderInfo.bill_reduction_price,
        bill_discount: this.orderInfo.bill_discount,
        commodity_string: [],
        customize_fields: JSON.stringify(this.customizeFields)
      };
      let listCount = this.list.length;
      this.list.forEach((item, index) => {
        // 排除合计那行
        if (index <= listCount - 2) {
          let commodity = {
            bill_item_id: item.bill_item_id,
            amount: item.amount,
            convert_actual_amount: item.convert_actual_amount,
            sub_price: item.sub_price,
            bill_record_id:item.id,
            bargain_price: item.bargain_price,
            sub_bargain_price: item.sub_bargain_price,
            diff_remark: item.diff_remark,
            item_bill_reduction_price: item.item_bill_reduction_price,
            calculate_way: item.calculate_way,
            unit_price: item.unit_price,
          };
          if (this.bill_protocol_discount == 1) {
            commodity.protocol_discount = item.protocol_discount
          }
          data.commodity_string.push(commodity);
        }
      });
      data.commodity_string = JSON.stringify(data.commodity_string);
      return data;
    },
    save() {
      let unit_price = false;
      if (this.saving) {
        return false;
      }
      let data = this.getSaveData();
      // console.log(this.list);
      let find = this.list.find(items => {
        return (
          (Number(items.amount) == 0 && Number(items.unit_price) !== 0) ||
          (Number(items.amount) !== 0 && Number(items.unit_price) == 0)
        );
      });
      if (find) {
        unit_price = true;
      }
      // 与后端沟通，所有为 0 的限制，前端先去掉，留给后端判断
      // 这里先保存之前的代码，以免又要改回来
      unit_price = false;
      if (unit_price !== true) {
        userAuditService.auditSave(data).then(res => {
          this.saving = false;
          if (res.status) {
            this.modalSuccess({
              content: '对账成功',
              onOk: () => {
                this.goBack();
              }
            });
          } else {
            this.modalError(res.message);
          }
        });
      } else {
        this.$Notice.error({
          title: '发货数量或单价不能为0'
        });
      }
    },
  }
};
</script>
<style scoped lang="less">
@gutter: 15px;
#user-audit {
  font-size: 14px;
  text-align: left;
  padding: 15px 15px 50px;
  .content-wrap {
    padding-bottom: @gutter;
    .base-info {
      .ivu-row-flex {
        margin: @gutter;
      }
      .base-info-content {
        margin: @gutter;
        .ivu-input-wrapper {
          width: 60%;
        }
      }
    }
    .order-list-content {
      width: 100%;
      overflow: auto;
      margin: 10px 5px;
      /deep/ .ivu-table-cell {
        padding: 0 0px 0 10px;
      }
    }
    .extra {
      display: flex;
      margin-bottom: @gutter;
      margin-left: @gutter;
      margin-right: @gutter;
      .label {
        flex: 104px;
      }
    }
  }
  /deep/ .ivu-table-body {
    overflow-x: auto !important;
  }
}
.ivu-input-number-handler-wrap {
  display: none;
}
.toggleIcon span {
  cursor: pointer;
  display: inline-block;
  margin-top: 7px;
}
.info-content-row {
  .ivu-col {
    margin-bottom: 12px;
  }
}
/deep/ .commodity_name {
  .ivu-tooltip {
    width: 100%;
  }
}
/deep/ .ivu-col {
    padding-left: 16px!important;
    padding-right: 0px!important;
  }
</style>
