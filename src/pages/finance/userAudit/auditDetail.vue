<template>
<div>
  <baseDetailHead class="ml15" :title="isReturn ? '客户退货单对账详情' : '客户结算详情'"/>
  <div id="user-audit-detail" class="content-wrap">
    <div class="base-info pl15">
      <div class="title">
        <Icon type="record" color="#03ac54"></Icon> 基础信息
        <Button
          type="primary"
          class="print-btn"
          @click="_mxPrintSoaOrder({className: 'tpl_print_view_soa', referNo: orderInfo.refer_no, is_summary: 0})"
          v-if="isCanPrint && !isUerAuditPrintUseOrderTemplate"
        >
          打印
        </Button>
        <Button type="primary" v-if="isCanPrint && isUerAuditPrintUseOrderTemplate" class="print-btn" @click="printOrder(orderInfo.order_id)">打印</Button>
      </div>
      <div class="base-info-content">
        <Row type="flex" align="middle" :gutter="50">
          <Col span="6">
            业务单号：{{orderInfo.refer_no}}
          </Col>
          <Col span="6">
            客户名称：{{orderInfo.user_name}}
          </Col>
          <Col span="6" v-if="sysConfig.tc_platform == 1"> 客户业态：{{ orderInfo.user_business_type_desc }} </Col>
          <Col span="6" v-if="page_type==='new'">子账号：{{orderInfo.sub_user_name || '-'}}</Col>
          <Col span="6">
            对账单号：{{orderInfo.bill_no}}
          </Col>
          <Col span="6">
            对账人员：{{orderInfo.bill_user}}
          </Col>
          <Col span="6">
            创建日期：{{orderInfo.create_time}}
          </Col>
          <Col span="6">
            {{isCustomerExpense? '付款' : isReturn ? '退货': '发货'}}日期：{{orderInfo.delivery_date}}
          </Col>
          <Col span="6">
            结算状态：{{orderInfo.status_desc}}
          </Col>
          <!-- 开启订单按供应商拆单、团餐、销售订单 -->
          <template v-if="sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && $route.query.page_type == 'new'">
            <Col span="6" v-if="orderInfo.type == 1 || orderInfo.type == 2">
              所属供应商：{{orderInfo.split_provider_name || '-'}}
            </Col>
          </template>
          <Col span="6">
            对账状态：{{orderInfo.is_bill_desc}}
          </Col>
          <Col span="6">
            单据金额：{{orderInfo.original_price}}
          </Col>
          <Col span="6">
            对账金额：{{orderInfo.bill_price}}
          </Col>
          <Col span="6">
            差异金额：{{orderInfo.diff_price}}
          </Col>
          <Col v-if="!isReturn && !isCustomerExpense" span="6">
            支付优惠：{{orderInfo.pay_preferential_price}}
          </Col>
          <Col span="6" v-show="orderInfo.customize_fields" :key="field.id" v-for="field in orderInfo.customize_fields">
            {{ field.name }}：{{ field.value }}
          </Col>
          <template v-if="sysConfig.is_open_split_order == 1 && sysConfig.split_order_mode == 3 && $route.query.page_type == 'new'">
            <Col span="24" v-if="sysConfig.tc_platform == 1 && (orderInfo.type == 1 || orderInfo.type == 2)"> {{orderInfo.type == 1 ? '采购对账单号' : '采购退回对账单号'}}：
              <template v-if="orderInfo.purchase_bill_info && orderInfo.purchase_bill_info.length">
                <span v-for="(item, index) in orderInfo.purchase_bill_info" :key="item.id">
                  <RelationNo :id='item.id' :no='item.bill_no'></RelationNo>{{ orderInfo.purchase_bill_info.length - 1 > index ? '、' : ''}}
                </span>
              </template>
              <span v-else>-</span>
            </Col>
          </template>
        </Row>
      </div>
    </div>
    <div class="order-list pl15">
      <template v-if="!isMoneyReturn">
        <div class="title">
          <Icon type="record" color="#03ac54"></Icon>
					{{ isCustomerExpense? '单据明细' : '订单商品清单' }}
        </div>
        <div class="order-list-content">
          <Table
            v-if="orderInfo.type == '4' || orderInfo.type == '5'"
            :columns="specialCols"
            :data="list"
          >
          </Table>
          <Table
            v-else
            :columns="isCustomerExpense? customerExpenseCol : cols"
            :data="list"></Table>
        </div>
      </template>
      <p class="remark" style="margin-top: 0">备注：{{orderInfo.remark}}</p>
    </div>
    <div class="order-list pl15">
      <div class="title">
        <Icon type="record" color="#03ac54"></Icon> 单据操作历史
      </div>
      <div class="order-list-content">
        <Table
          :columns="opCols"
          :data="opList"></Table>
      </div>
    </div>
    <Row class="fixedBtns" style="padding-left: 15px">
      <Col span="6" align="left">
        <Button @click="goBack">返 回</Button>&nbsp;&nbsp;
        <Button type="primary" @click="handleSave"  v-if="(orderInfo.type == 1 || orderInfo.type == 2) && isCanBill">对账</Button>
      </Col>
    </Row>
  </div>
</div>
</template>

<script>
  import UserAudit from '@api/UserAudit.js';
  import soaPrintMixin from '@/mixins/print/soaPrint';
  import ConfigMixin from '@/mixins/config';
  import printMixin from '@/mixins/orderPrint';
  import Button from '@/components/button'
  import baseDetailHead from '@/components/base-detail-head'
  import RelationNo from '@/components/relation-no'

  let userAuditService = new UserAudit();
  const NUMERIC_COL_WIDTH = 100;
  export default {
    name: "userAuditDetail",
    mixins: [ soaPrintMixin, ConfigMixin, printMixin],
    components: {
      Button,
      baseDetailHead,
      RelationNo
    },
		computed: {
			isCustomerExpense() {
				return +this.orderInfo.type === 10
			}
		},
    data() {
      return {
				customerExpenseCol: [
					{
						title: '序号',
						key: 'index',
					},
					{
						title: '付款方式',
						key: 'fee_pay_way_desc'
					},
					{
						title: '应收/应付',
						key: 'fee_payment_desc'
					},
					{
						title: '费用金额',
						key: 'original_sub_price',
					},
					{
						title: '对账金额',
						key: 'sub_price',
					},
					{
						title: '备注',
						key: 'diff_remark',
					}
				],
        isCanPrint: false,
        cols: [
          {
            width: 80,
            title: '序号',
            key: 'index',
          },
          {
            title: '商品名称',
            key: 'commodity_name',
            render: (h, params) => {
              let {row} = params;
              return h('div', [
                h('p',
                  {
                    style: {
                      'white-space': 'nowrap',
                      'text-overflow': 'ellipsis',
                      'overflow': 'hidden'
                    },
                    attrs: {
                      title:row.commodity_name
                    }
                  },
                  row.commodity_name
                ),
                h('p', {
                  style: {
                    color: '#999'
                  }
                }, row.commodity_code),
              ]);
            }
          },
          {
            title: '描述',
            key: 'summary',
          },
          {
            title: '发货单位',
            key: 'unit',
            width: 120,
          },
          {
            title: '发货数量',
            key: 'amount',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', row.amount);
              }
              if (row.amount - row.original_amount == 0) {
                text = [
                  h('p', row.amount),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_amount),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.amount)
                ];
              }
              return h('div', text);
            },
          },
          // {
          //   title: '发货数量(原)',
          //   key: 'convert_actual_amount',
          //   width: NUMERIC_COL_WIDTH,
          //   render: (h, param) => {
          //     let {row} = param;
          //     let text = [];
          //     // 合计
          //     if (param.index === this.list.length - 1) {
          //       return h('span', row.convert_actual_amount);
          //     }
          //     if (row.convert_actual_amount - row.original_convert_actual_amount == 0) {
          //       text = [
          //         h('p', row.convert_actual_amount),
          //       ];
          //     } else {
          //       text = [
          //         h('p', {
          //           style: {
          //             textDecoration: 'line-through'
          //           }
          //         }, row.original_convert_actual_amount),
          //         h('p', {
          //           style: {
          //             color: 'red'
          //           }
          //         }, row.convert_actual_amount)
          //       ];
          //     }
          //     return h('div', text);
          //   },
          // },
          {
            title: '发货单价',
            key: 'unit_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', '');
              }
              if (row.unit_price - row.original_unit_price == 0) {
                text = [
                  h('p', (row.unit_price * 1).toFixed(2)),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_unit_price),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.unit_price)
                ];
              }
              return h('div', text);
            },
          },
          {
            title: '发货金额',
            key: 'sub_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', row.sub_price);
              }
              if (row.sub_price - row.original_sub_price == 0) {
                text = [
                  h('p', row.sub_price),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_sub_price),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.sub_price)
                ];
              }
              return h('div', text);
            },
          },
          {
            title: '对账抹零',
            key: 'item_bill_reduction_price',
            width: NUMERIC_COL_WIDTH,
          },
          {
            title: '差异数量',
            key: 'diff_amount',
            width: NUMERIC_COL_WIDTH,
          },
          {
            title: '差异单价',
            key: 'diff_unit_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, params) => {
              let {row, index} = params;
              // 合计那行
              if (index === this.list.length - 1) {
                return false;
              }
              return h('span', row.diff_unit_price);
            }
          },
          {
            title: '差异金额',
            key: 'diff_sub_price',
            width: NUMERIC_COL_WIDTH,
          },
          {
            title: '差异备注',
            key: 'diff_remark',
          }
        ],
        returnCols: [
          {
            width: 80,
            title: '序号',
            key: 'index',
          },
          {
            title: '商品名称',
            key: 'commodity_name',
            render: (h, params) => {
              let {row} = params;
              return h('div', [
                h('p',
                  {
                    style: {
                      'white-space': 'nowrap',
                      'text-overflow': 'ellipsis',
                      'overflow': 'hidden'
                    },
                    attrs: {
                      title:row.commodity_name
                    }
                  },
                  row.commodity_name
                ),
                h('p', {
                  style: {
                    color: '#999'
                  }
                }, row.commodity_code),
              ]);
            }
          },
          {
            title: '描述',
            key: 'summary',
          },
          {
            title: '单位',
            key: 'unit',
            width: 120,
          },
          {
            title: '退货数量',
            key: 'amount',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', row.amount);
              }
              if (row.amount - row.original_amount == 0) {
                text = [
                  h('p', row.amount),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_amount),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.amount)
                ];
              }
              return h('div', text);
            },
          },
          // {
          //   title: '发货数量(原)',
          //   key: 'convert_actual_amount',
          //   width: NUMERIC_COL_WIDTH,
          // },
          {
            title: '退货单价',
            key: 'unit_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', '');
              }
              if (row.unit_price - row.original_unit_price == 0) {
                text = [
                  h('p', (row.unit_price * 1).toFixed(2)),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_unit_price),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.unit_price)
                ];
              }
              return h('div', text);
            },
          },
          {
            title: '退货金额',
            key: 'sub_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', row.sub_price);
              }
              if (row.sub_price - row.original_sub_price == 0) {
                text = [
                  h('p', row.sub_price),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, row.original_sub_price),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, row.sub_price)
                ];
              }
              return h('div', text);
            },
          },
          {
            title: '差异数量',
            key: 'diff_amount',
            width: NUMERIC_COL_WIDTH,
          },
          {
            title: '差异单价',
            key: 'diff_unit_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, params) => {
              let {row, index} = params;
              // 合计那行
              if (index === this.list.length - 1) {
                return false;
              }
              return h('span', row.diff_unit_price);
            }
          },
          {
            title: '差异金额',
            key: 'diff_sub_price',
            width: NUMERIC_COL_WIDTH,
          },
          {
            title: '差异备注',
            key: 'diff_remark',
          }
        ],
        specialCols: [
          {
            width: 80,
            title: '序号',
            key: 'index',
          },
          {
            title: '押金筐名称',
            key: 'basket_name',
            render: (h, params) => {
              let {row, index} = params;
              if (index === this.list.length - 1) {
                return false;
              }
              return h('p',
                {
                  style: {
                    'white-space': 'nowrap',
                    'text-overflow': 'ellipsis',
                    'overflow': 'hidden'
                  },
                  attrs: {
                    title:row.basket_name
                  }
                },
                row.basket_name
              );
            }
          },
          {
            title: '关联商品',
            key: 'commodity_name',
            render: (h, params) => {
              let {row, index} = params;
              if (index === this.list.length - 1) {
                return false;
              }
              return h('div', [
                h('p',
                  {
                    style: {
                      'white-space': 'nowrap',
                      'text-overflow': 'ellipsis',
                      'overflow': 'hidden'
                    },
                    attrs: {
                      title:row.commodity_name
                    }
                  },
                  row.commodity_name
                ),
                h('p', {
                  style: {
                    color: '#999'
                  }
                }, row.commodity_code),
              ]);
            }
          },
          {
            title: '单位',
            key: 'unit',
            width: 100,
          },
          {
            title: '数量',
            key: 'basket_num',
            width: 120,
          },
          {
            title: '单价',
            key: 'basket_unit_price',
            width: NUMERIC_COL_WIDTH,
            render: (h, params) => {
              const { row, index } = params
              if (index === this.list.length - 1) {
                return false;
              }
              return (
                <span>{row.basket_unit_price}</span>
              )
            }
          },
          {
            title: '金额',
            key: 'basket_price',
            width: NUMERIC_COL_WIDTH,
          },
        ],
        opCols: [
          {
            title: '操作人',
            key: 'name',
            width: 200
          },
          {
            title: '时间',
            key: 'time',
            width: 200
          },
          {
            title: '类型',
            key: 'type',
            width: 120
          },
          {
            title: '日志',
            key: 'content',
            render: (h, params) => {
              let {row} = params;
              let content = [];
              if (row.content && Array.isArray(row.content)) {
                row.content.forEach((item) => {
                  content.push(h('p', item));
                });
              } else {
                content = row.content;
              }
              return h('div', content)
            }
          }
        ],
        list: [],
        opList: [],
        orderInfo: {
        	order_id: '',
          refer_no: '',
          user_name: '',
          bill_no: '',
          create_time: '',
          delivery_date: '',
          status_desc: '',
          bill_price: '',
          diff_price: '',
          pay_preferential_price: '',
        },
        isReturn: false,
        isMoneyReturn: false,
        page_type: '',
        isCanBill: false
      }
    },
    created ()  {
      this.isCanPrint = this.$route.query.is_can_print === 'true';
      this.isCanBill = this.$route.query.is_can_bill === 'true';
      this.page_type = this.$route.query.page_type || '';
      this.getDetail();
    },
    mounted () {
    },
    methods: {
      printOrder(id) {
        this._printOrder({
          id: id,
          is_bill: '1'
        })
      },
      async initCols() {
        if(this.orderInfo.type === '1') {
          this.cols.splice(5, 0, {
            title: '发货数量(原)',
            key: 'convert_actual_amount',
            width: NUMERIC_COL_WIDTH,
            render: (h, param) => {
              let {row} = param;
              let text = [];
              // 合计
              if (param.index === this.list.length - 1) {
                return h('span', this.forMatNumToFloat(row.convert_actual_amount));
              }
              if (row.convert_actual_amount - row.original_convert_actual_amount == 0) {
                text = [
                  h('p', this.forMatNumToFloat(row.convert_actual_amount)),
                ];
              } else {
                text = [
                  h('p', {
                    style: {
                      textDecoration: 'line-through'
                    }
                  }, this.forMatNumToFloat(row.original_convert_actual_amount)),
                  h('p', {
                    style: {
                      color: 'red'
                    }
                  }, this.forMatNumToFloat(row.convert_actual_amount))
                ];
              }
              return h('div', text);
            },
          })
        }
        if(this.orderInfo.type == '2') {
          // 退货单对账
          this.isReturn = true
          this.cols = this.returnCols
          return
        }
        if(this.orderInfo.type == '8') {
          // 客户退款模式 退货单 对账单
          this.isMoneyReturn = true

        }
        const config = await this.commonService.getConfig()
        let { bill_protocol_discount } = config;
        const protocolPriceCol = {
          title: '协议市场价',
          key: 'protocol_org_price',
          render:(h, param)=>{
            let {row} = param;
            let text = [];
            // 合计
            if (param.index === this.list.length - 1) {
              return h('span', '');
            }
            if (row.protocol_org_price - row.original_protocol_org_price == 0) {
              text = [
                h('p', (row.protocol_org_price * 1).toFixed(2)),
              ];
            } else if (row.protocol_discount === '-' && row.original_protocol_discount === '-') {
              text = [
                h('p', '-'),
              ];
            } else {
              text = [
                h('p', {
                  style: {
                    textDecoration: 'line-through'
                  }
                }, row.original_protocol_org_price),
                h('p', {
                  style: {
                    color: 'red'
                  }
                }, row.protocol_org_price)
              ];
            }
            return h('div', text);
          }
        };
        const protocolDisCountCol = {
          title: '协议折扣',
          key: 'original_protocol_discount',
          render:(h, param)=>{
            let {row} = param;
            let text = [];
            // 合计
            if (param.index === this.list.length - 1) {
              return h('span', '');
            }
            if (row.protocol_discount - row.original_protocol_discount == 0) {
              text = [
                h('p', (row.protocol_discount * 1).toFixed(2)),
              ];
            } else if (row.protocol_discount === '-' && row.original_protocol_discount === '-') {
              text = [
                h('p', '-'),
              ];
            } else {
              text = [
                h('p', {
                  style: {
                    textDecoration: 'line-through'
                  }
                }, row.original_protocol_discount  === '-' ? row.original_protocol_discount : row.original_protocol_discount + '%'),
                h('p', {
                  style: {
                    color: 'red'
                  }
                }, row.protocol_discount ==='-' ? row.protocol_discount : row.protocol_discount + '%')
              ];
            }
            return h('div', text);
          }
        };
        if (Number(bill_protocol_discount) === 1) {
          let index = this.cols.findIndex((col) => col.key === 'unit') + 1;
          if (!this.cols.find((col) => col.key === protocolPriceCol.key)) {
            this.cols.splice(index, 0, protocolPriceCol);
          }
          if (!this.cols.find((col) => col.key === protocolDisCountCol.key)) {
            this.cols.splice(index + 1, 0, protocolDisCountCol);
          }
        } else {
          if (this.cols.find((col) => col.key === protocolPriceCol.key)) {
            this.cols.splice(this.cols.findIndex((col) => col.key === protocolPriceCol.key), 1);
          }
          if (this.cols.find((col) => col.key === protocolDisCountCol.key)) {
            this.cols.splice(this.cols.findIndex((col) => col.key === protocolDisCountCol.key), 1);
          }
        }
        const bargainPriceColumn = {
          title: '结算价',
          key: 'bargain_price'
        }
        const bargainSubPriceColumn = {
          title: '结算金额',
          key: 'sub_bargain_price'
        }
        if (this.isOpenUserBargainPrice) {
          let preIndex = this.cols.findIndex(col => col.key === 'sub_price')
          let existIndex = this.cols.findIndex(col => col.key === 'bargain_price')
          if (preIndex && existIndex < 0) {
            // 发货金额后边
            this.cols.splice(preIndex + 1, 0, bargainPriceColumn);
            this.cols.splice(preIndex + 2, 0, bargainSubPriceColumn)
          }
        }
      },
      forMatNumToFloat(num) {
        let formattedNum = new Intl.NumberFormat('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(num);
        return formattedNum || 0
      },
      getDetail () {
        let data = {
          refer_no: this.$route.query.refer_no,
          type: this.$route.query.type,
        };
        userAuditService.getAuditDetail(data).then((res) => {
          let {status, data} = res;
          if (status) {
              let {main, detail, log} = data;
              this.orderInfo = main;
              this.opList = log;
              // this.list = detail;
              let sumAmount = 0;
              this.list = detail.map((item, index)=> {
              // 添加 发货数量(原)
              if (item.unit_convert === 'Y' && Number(item.unit_num) - 1 !== 0) {
                item.convert_actual_amount = (item.amount / item.unit_num).toFixed(2);
                item.original_convert_actual_amount = (item.original_amount / item.unit_num).toFixed(2);
              } else {
                item.convert_actual_amount = Number(item.amount);
                item.original_convert_actual_amount = Number(item.original_amount);
              }
              sumAmount += item.convert_actual_amount
              if(index === detail.length - 1) {
                item.convert_actual_amount = (sumAmount - item.convert_actual_amount).toFixed(2);
              }
              return { ...item }
            })
              this.initCols();
          } else {
            this.modalError(res.message);
          }
        });
      },
      handleSave () {
        this.$router.push({
          path: '/finance/userAudit',
          query: {
            keep_scroll: 1,
            refer_no: this.orderInfo.refer_no,
            type: this.orderInfo.type,
            page_type: 'new'
          },
        });
      },
    }
  }
</script>

<style lang="less">
</style>
<style scoped lang="less">
  @gutter: 15px;
  #user-audit-detail {
    position: relative;
    text-align: left;
    padding: 15px 0px 50px;
    .print-btn {
      position: absolute;
      right: 30px;
      top: 15px;
    }
    .base-info {
      .ivu-row-flex {
        margin: @gutter;
      }
      .base-info-content {
        margin: @gutter;
      }
    }
    .order-list-content {
      padding: @gutter;
    }
    .remark {
      margin-bottom: @gutter;
      margin-left: @gutter;
    }
    .ivu-table-cell {
      white-space: normal !important;
    }
  }
  .base-info-content {
    .ivu-col {
      height: 36px;
      line-height: 36px;
    }
  }
  /deep/ .ivu-col {
    padding-left: 16px!important;
    padding-right: 0px!important;
  }
</style>
