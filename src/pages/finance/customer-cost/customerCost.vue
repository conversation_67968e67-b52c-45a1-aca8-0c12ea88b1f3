<template>
  <div class="common purchase__list">
    <ListTable
      :expandFixedWidth="true"
      :filter-items="filterItems"
      data-provider="/superAdmin/AccountExpense/list"
      :border="false"
      :autoLoadData="true"
      :advance="true"
      :advance-items="advanceItems"
      :outer-border="true"
      :max-line="2"
      :height="getTableHeight() - 135"
      :columns="columns"
      ref="table"
    >
      <ExportButton
        slot="button"
        text="导 出"
        type="default"
        offline
        :api="apiUrl.accountExpenseExportList"
        :param-getter="getParams"
      ></ExportButton>
      <div slot="before-table" class="order__list__operation">
        <SButton styleType="btnStyleForAdd" @click="news">新增</SButton>
      </div>
    </ListTable>
  </div>
</template>

<script>
import ListTable from '@components/list-table';
import DateUtil from '@/util/date.js';
import SButton from '@components/button';
import { get } from '@/api/request';
import { api } from '@api/api.js';

export default {
  name: 'customer-cost',
  components: {
    ListTable,
    SButton,
  },
  data() {
    let DefaultPayDate = [this.getCurrentLastMonth(), this.getToday()];
    return {
      advanceItems: [
        {
          items: [
            {
              type: 'Select',
              label: '单据状态',
              props: {
                placeholder: '单据状态',
                clearable: true,
              },
              key: 'audit_status',
              data: [
                {
                  value: '0',
                  label: '全部状态',
                },
                {
                  value: '1',
                  label: '待审核',
                },
                {
                  value: '2',
                  label: '已审核',
                },
                {
                  value: '3',
                  label: '已作废',
                },
              ],
            },
            {
              type: 'Select',
              key: 'settle_status',
              label: '结算状态',
              props: {
                placeholder: '结算状态',
                clearable: true,
              },
              data: [
                {
                  value: '',
                  label: '全部结算状态',
                },
                {
                  value: '1',
                  label: '未结算',
                },
                {
                  value: '2',
                  label: '已结算',
                },
              ],
            },
            {
              type: 'Select',
              key: 'bill_mode',
              label: '对账模式',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '不可对账',
                },
                {
                  value: '2',
                  label: '可对账',
                },
              ],
            },
            {
              type: 'DatePicker',
              label: '付款日期',
              key: ['start_pay_date', 'end_pay_date'],
              defaultValue: DefaultPayDate,
              props: {
                type: 'daterange',
                clearable: false,
              },
              onChange: (value) => {
                if (DateUtil.diffDay(value[0], value[1]) > 365) {
                  this.errorMessage('付款日期筛选不能超过一年');
                  this.$refs.table.setValue(
                    ['start_pay_date', 'end_pay_date'],
                    DefaultPayDate,
                  );
                  return { DefaultPayDate, stop: true };
                } else {
                  DefaultPayDate = value;
                  return { value };
                }
              },
            },
            {
              label: '单据备注',
              type: 'Input',
              key: 'remark',
              props: {
                placeholder: '请输入',
                clearable: true,
              },
            },
          ],
        },
      ],
      filterItems: [
        {
          type: 'DatePicker',
          label: '创建日期',
          key: ['start_create_time', 'end_create_time'],
          defaultValue: [this.getCurrentLastMonth(), this.getToday()],
          props: {
            type: 'daterange',
          },
        },
        {
          label: '搜索',
          type: 'Input',
          key: 'search_value',
          props: {
            placeholder: '请输入客户名称、联系人、手机号',
            clearable: true,
          },
        },
      ],
      columns: [],
      originCols: [
        {
          title: '费用单号',
          key: 'fee_no',
          width: 180,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h(
                'span',
                {
                  class: {
                    'hover--primary': true,
                  },
                  style: {
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.toOrderDetail(data);
                    },
                  },
                },
                data.fee_no,
              ),
              h(
                'div',
                {
                  style: {
                    color: '#999999',
                  },
                },
                data.create_time,
              ),
            ]);
          },
        },
        {
          title: '客户名称',
          key: 'user_name',
          width: 120,
        },
        {
          title: '制单人',
          key: 'create_user',
          width: 120,
        },
        {
          title: '单据类型',
          key: 'fee_type_desc',
          width: 130,
        },
        {
          title: '单据金额',
          key: 'fee_price',
          width: 100,
        },
        {
          title: '对账模式',
          key: 'bill_mode_desc',
          width: 100,
        },
        {
          title: '单据状态',
          key: 'audit_status_desc',
          width: 100,
        },
        {
          title: '结算状态',
          key: 'settle_status_desc',
          width: 100,
        },
        {
          title: '备注',
          key: 'remark',
          width: 100,
        },
        {
          resizable: false,
          title: '操作',
          key: 'action',
          fixed: 'right',
          type: 'action',
          width: 210,
          actionCountLimit: 3,
          actions: (params) => {
            let { row } = params;
            let actions = [];
            if (row.is_can_edit == 1) {
              actions.push({
                name: '编辑',
                action: (params) => {
                  this.toEdit(params.row.id);
                },
              });
            }
            if (row.is_can_discard == 1) {
              actions.push({
                name: '作废',
                confirm: () => '确定作废？',
                action: (params) => {
                  this.invalid(params.row.id);
                },
              });
            }
            if (row.is_can_delete == 1) {
              actions.push({
                name: '删除',
                confirm: () => '确定删除？',
                action: (params) => {
                  this.delete(params.row.id);
                },
              });
            }
            return actions;
          },
          // actions: [
          //   {
          //     action: params => {
          //       this.toEdit(params.row.id);
          //     },
          //     name: '编辑',
          //     class: params => {
          //       return {
          //         dn: params.row.is_can_edit == 1 ? false : true
          //       };
          //     }
          //   },
          //   {
          //     name: '作废',
          //     confirm: () => '确定作废？',
          //     action: params => {
          //       this.invalid(params.row.id);
          //     },
          //     class: params => {
          //       return {
          //         dn: params.row.is_can_discard == 1 ? false : true
          //       };
          //     }
          //   },
          //   {
          //     name: '删除',
          //     confirm: () => '确定删除商品？',
          //     action: params => {
          //       this.delete(params.row.id);
          //     },
          //     class: params => {
          //       return {
          //         dn: params.row.is_can_delete == 1 ? false : true
          //       };
          //     }
          //   }
          // ]
        },
      ],
    };
  },
  watch: {
    $route: {
      handler() {
        // this.$refs.table.fetchData();
      },
    },
  },
  created() {
    this.columns = this.originCols;
  },
  methods: {
    getParams() {
      const params = this.$refs.table.getParams();
      return params;
    },
    toOrderDetail(val) {
      this.$router.push({ path: '/finance/detail', query: { id: val.id } });
    },
    async delete(id) {
      let { status, message } = await get(api.accountExpensedel, { id: id });
      if (status == 1) {
        this.$Notice.success({
          title: '操作成功',
        });
        this.$refs.table.fetchData();
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    async invalid(id) {
      let { status, message } = await get(api.accountExpenseDiscard, {
        id: id,
      });
      if (status == 1) {
        this.$Notice.success({
          title: '操作成功',
        });
        this.$refs.table.fetchData();
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    news() {
      this.$router.push({ path: '/finance/newBills' });
    },
    toEdit(id) {
      this.$router.push({ path: '/finance/edit', query: { id: id } });
    },
  },
};
</script>

<style lang="less" scoped>
.common {
  .freight {
    .ivu-input-wrapper {
      padding: 10px 0 5px 0;
    }
    .err-txt {
      font-size: 12px;
      height: 18px;
      color: #f13130;
    }
  }
  .more-btn {
    .sui-icon {
      font-size: 12px;
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover .sui-icon {
      transform: rotate(180deg);
    }
  }
  .sdp-table__tr:hover .hover--primary {
    color: var(--primary-color);
  }
}
</style>
