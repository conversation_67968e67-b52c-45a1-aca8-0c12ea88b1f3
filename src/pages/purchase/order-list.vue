<template>
  <div class="purchase__list">
    <ListTable
      @on-selection-change="handleSelectionChange"
      :debounceOptions="{ leading: true, trailing: false }"
      :initParams="initParams"
      :before-request="beforeRequest"
      :before-set-data="afterRequest"
      :advance="true"
      :filterLazy="true"
      tableId="purchase_list_01"
      :advance-items="advanceItems"
      :filter-items="filterItems"
      data-provider="/superAdmin/purchaseOrder/orderPage"
      :border="false"
      :outer-border="true"
      :max-line="2"
      :height="getTableHeight() - 115"
      row-key="id"
      :columns="columns"
      ref="table"
      :keepScroll="true"
      :isOpenCustom="true"
      :pageSizeCache="{ suffix: 'purchase-order-list' }"
      :auto-load-data="false"
    >
      <Row :gutter="12" type="flex" slot="page-left">
        <Col>合计金额：</Col>
        <Col
          >已收货金额：<span>{{ sum.in_total_price }}</span></Col
        >
        <Col> 计划采购金额合计：{{ sum.purchase_total_price }} </Col>
      </Row>
      <div slot="button">
        <Button @click="openExportModal">导出</Button>
        <Dropdown
          placement="bottom-end"
          trigger="click"
          @on-click="openExportModal(true)"
        >
          <Button style="margin-left: -10px" icon="ios-arrow-down"></Button>
          <DropdownMenu slot="list" style="width: 112px">
            <DropdownItem>自定义导出字段</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <div
        slot="before-table"
        class="common__operation"
        v-show="selectedRows.length === 0"
      >
        <Button @click="addNewOrder" styleType="btnStyleForAdd">新增</Button>
        <ImportBtn
          class="ml10"
          :download="download"
          title="导入采购单"
          :data="importParams"
          :post="post"
          :offline="true"
          @on-completed="importBtnCallBack"
        >
          <span slot="custom-area-left">计划交货日期：</span>
          <div slot="custom-area-right" class="date">
            <DatePicker style="width: 210px;margin-left: 12px" :value="delivery_date" type="date" format="yyyy-MM-dd" @on-change="onDeliveryDateChange"/>
            <!-- <p>下载的模板将自动填入选择的计划交货日期</p> -->
          </div>
          <!-- 多仓才显示 -->
          <div slot="extra-items">
            <div class="warehouse">
              <div class="warehouse__text">模板类型：</div>
              <div class="warehouse__select">
                <RadioGroup @on-change="changeImportType" v-model="import_type">
                  <Radio label="0">
                    <span>自带商品</span>
                  </Radio>
                  <Radio label="1">
                    <span>无商品</span>
                  </Radio>
                </RadioGroup>
              </div>
            </div>
            <div class="warehouse" v-if="+import_type === 0">
              <div class="warehouse__text">商品上架状态：</div>
              <div class="warehouse__select">
                <RadioGroup v-model="commodity_online">
                  <Radio label="0">
                    <span>全部</span>
                  </Radio>
                  <Radio label="Y">
                    <span>上架商品</span>
                  </Radio>
                </RadioGroup>
              </div>
            </div>
            <div class="warehouse" v-if="warehouseList.length > 1">
              <div class="warehouse__text">仓库：</div>
              <div class="warehouse__select">
                <Select style="width: 210px" v-model="import_store_id" filterable>
                  <Option
                    v-for="item in warehouseList"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
                <p v-show="!import_store_id" class="warehouse__select__error">
                  请选择仓库
                </p>
              </div>
            </div>
            <div class="warehouse">
              <div class="warehouse__text">导入模式(价格)：</div>
              <div class="warehouse__select">
                <Select style="width: 210px" v-model="import_price_type" filterable>
                  <Option
                    v-for="item in priceTypes"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
            <div v-if="+import_type === 1" class="warehouse">
              <div class="warehouse__text">导入模式(商品)：</div>
              <div class="warehouse__select">
                <Select style="width: 210px" v-model="import_goods_type" filterable>
                  <Option
                    v-for="item in goodsTypes"
                    :value="item.id"
                    :key="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </div>
            </div>
          </div>
        </ImportBtn>
      </div>
      <div slot="batch-operation">
        <Button @click="mergeOrder">合并采购单</Button>
        <Button @click="batchCloseOrder">批量关闭采购单</Button>
        <Button @click="_batchPrintOrderBefore()">批量单个打印</Button>
        <Button @click="_batchMergePrintOrderBefore()">批量合并打印</Button>
        <Button @click="openBatchReceiptModal">一键收货</Button>
        <Button
          @click="onUpdateTax"
          v-if="
            sysConfig.is_open_store_no_tax != 1 && sysConfig.input_tax_rate == 1
          "
          >更新商品税率</Button
        >
        <Button @click="openExportModal(true, 'batch')">导出</Button>
        <Dropdown
          placement="bottom-end"
          trigger="click"
          @on-click="openExportModal(true, 'batch')"
        >
          <Button style="margin-left: -10px" icon="ios-arrow-down"></Button>
          <DropdownMenu slot="list" style="width: 112px">
            <DropdownItem>自定义导出字段</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </ListTable>
    <!-- 这个div不能去掉 （会影响智能预采购生成采购单跳转到此页面时的合并弹框） -->
    <div v-if="checkMerge"></div>
    <PurchaseOrderMerge
      v-if="checkMerge && isOPenPurchaseOrderCombineListAutoPopup"
      @on-success="fetchData"
    />
    <exportModal
      :width="845"
      @complete="exportOrderList"
      :groupList="groupList"
      :defaultDataList="defaultDataList"
      :exportDataList="exportDataList"
      ref="modal"
      :exportTotalFields="exportTotalFields"
    />
    <Modal
      v-model="batchReceiptModal.show"
      title="提示"
      @on-ok="batchReceipt"
      @on-cancel="batchReceiptModal.show = false"
    >
      <div style="text-align: center">{{ batchReceiptModal.title }}</div>
    </Modal>
    <Modal
      v-model="batchReceiptErrorTipModal.show"
      title="提示"
      @on-cancel="closeBatchReceiptErrorTipModal"
    >
      <div style="margin-bottom: 5px">以下采购单未能收货成功：</div>
      <div style="text-align: center">
        <Table
          border
          outer-border
          :columns="batchReceiptErrorTipModal.columns"
          :data="batchReceiptErrorTipModal.errorTips"
        ></Table>
      </div>
      <template #footer>
        <Button type="primary" @click="closeBatchReceiptErrorTipModal"
          >确定</Button
        >
      </template>
    </Modal>
    <QrCodeModal
      ref="qrCodeModal"
      :isEnableMultiStore="isEnableMultiStore"
    ></QrCodeModal>
    <SModal
      title="确认"
      text="是否复制该采购单"
      ref="smodal"
      type="warning"
      @ok="copyConfirm"
      okTxt="继续"
    >
      <template #footer-left>
        <Checkbox
          v-show="isCopyInStorageDataShow"
          v-model="isCopyInStorageData"
          @on-change="isCopyInStorageDataOnChange"
        >
          <span>复制入库数据</span>
        </Checkbox>
      </template>
    </SModal>
    <PrintTemplateChooseNew
      ref="printTemplateChooseNewRef"
      :type="printType"
      @print="_print"
      filterOld
    />
    <Modal
      :mask-closable="false"
      :closable="false"
      :footer-hide="true"
      v-model="updateTax.show"
      title="正在更新商品税率"
    >
      <div style="text-align: center">
        <Progress
          :percent="(updateTax.current / updateTax.total) * 100"
          hide-info
        />
        <p class="mt30 mb20">请勿离开本页面, 否则会导致部分数据处理失败</p>
      </div>
    </Modal>
  </div>
</template>

<script>
import Button from '@components/button';
import { SModal } from '@/components/modal';
import ImportBtn from '@components/import-button';
import ListTable from '@components/list-table';
import { MINE_TYPE } from '@/util/const';
import { Progress } from '@components';
import goods from '@api/goods.js';
import purchase from '@api/purchase.js';
import { api } from '@api/api.js';
import { post } from '@/api/request';
import PurchaseOrderMerge from '@components/common/PurchaseOrderMergeCheck';
import printMixin from './mixins/print';
import ConfigMixin from '@/mixins/config';
import goodsAutoCompleteSelect from '@/components/common/goodsAutoComplete_new';
import { purchaseTypeArr as selectData } from './utils';
import storeRoomRes from '@/api/storeRoom.js';
import TableHeadSortIcon from '@components/common/tableHeadSortIcon';
import { exportLoop } from '@components/common/export-btn/util';
import exportModal from '@/components/common/exportModal';
import date from '@/util/date.js';
import Table from '@/components/table';
import CheckboxGroup from '@components/CheckboxGroup';
import QrCodeModal from './purchaseOrder_New/components/qrCodeModal.vue';
import PrintTemplateChooseNew from '@components/print-template-choose-new';
import StorageUtil from '@util/storage.js';
import PurchaseTypeMuti from '@components/purchase-type/src/muti.vue';
import CommodityCascader from '@/components/base-filter-components/commodityCascader/index.vue';
import {
  agents,
  goodsTag,
  operator,
  store
} from '@/components/standard/sdp-filter-items'
import moment from "moment";
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const STATUS_ALL_RECEIPTED = 5;
const STATUS_CLOSE = 6;
const purchaseList = [
  {
    value: '1',
    label: '采购员',
    children: [],
  },
  {
    value: '2',
    label: '供应商',
    children: [],
  },
];

const LocalKey = 'isCopyInStorageData';
const SORT_FIELD = 'purchase-order-list-sort-field';
const SORT_TYPE = 'purchase-order-list-sort-type';

// const LIST_KEY = 'id';
export default {
  name: 'purchaseOrderList',
  mixins: [printMixin, ConfigMixin],
  components: {
    Button,
    ImportBtn,
    ListTable,
    PurchaseOrderMerge,
    exportModal,
    Table,
    SModal,
    QrCodeModal,
    PrintTemplateChooseNew,
  },
  data() {
    return {
      initParams: {},
      QRCodeUrl: '',
      updateTax: {
        show: false,
        current: 0,
        total: 0,
      },
      batchReceiptErrorTipModal: {
        show: false,
        errorTips: [],
        columns: [
          {
            title: '采购单号',
            key: 'pur_no',
          },
          {
            title: '失败原因',
            key: 'msg',
          },
        ],
      },
      batchReceiptModal: {
        show: false,
        title: '',
      },
      checkMerge: false,
      init: false,
      batchEditOlineY: false,
      batchEditOlineN: false,
      batchDeleting: false,
      syncingAll: false,
      syncingSelected: false,
      sort_field: window.localStorage.getItem(SORT_FIELD) || '',
      sort_type: +window.localStorage.getItem(SORT_TYPE) || 0,
      selectedRows: [],
      type: 'purchase',
      titleCol: [],
      selectData,
      purchaseList,
      delivery_date: moment().format('YYYY-MM-DD'),
      filterItems: [
        {
          required: true,
          checked: true,
          label: '采购单号',
          type: 'Input',
          key: 'no',
          props: {
            placeholder: '输入采购单号',
          },
        },
        {
          required: true,
          checked: true,
          type: 'custom',
          component: goodsAutoCompleteSelect,
          key: 'commodity_name',
          label: '商品',
          props: {
            on: {
              'on-enter': (e) => {
                this.selectGoods(e);
              },
            },
            placeholder: '商品名/助记码/编码/别名/条形码/品牌',
          },
          onChange(value) {
            return {
              value,
              stop: true,
            };
          },
        },
      ],
      advanceItems: [],
      origin_dvanceItems: [
        {
          items: [
            {
              required: true,
              checked: true,
              type: 'DatePicker',
              props: {
                type: 'daterange',
                placeholder: '全部',
              },
              defaultValue: [date.getBeforeDate(30), date.getTodayDate()],
              key: ['start_time', 'end_time'],
              label: '创建日期',
            },
            {
              checked: false,
              label: '采购模式',
              type: 'Cascader',
              key: ['purchase_type', 'purchase_item_type'],
              props: {
                filterable: true,
                data: selectData,
                placeholder: '全部',
              },
            },
            {
              checked: true,
              label: '供应商/采购员',
              key: ['new_purchase_type', 'new_purchase_person'],
              type: 'custom',
              muti: true,
              props: {
                data: purchaseList,
                remote: false,
                placeholder: '请选择供应商/采购员',
              },
              component: PurchaseTypeMuti,
              onChange: (value, selectedData, formatValue) => {
                return {
                  value: [
                    value[0],
                    value[0] === '1'
                      ? formatValue.agent_id
                      : formatValue.provider_id,
                  ],
                  stop: false,
                };
              },
            },
            // {
            //   checked: true,
            //   label: '供应商/采购员',
            //   type: 'Cascader',
            //   key: ['new_purchase_type', 'new_purchase_item_type'],
            //   props: {
            // 		multiple: true,
            //     filterable: true,
            //     data: purchaseList,
            //     placeholder: '全部',
            //   },
            // },
            {
              checked: false,
              label: '负责人',
              type: 'custom',
              key: 'provider_supervisor',
              component: agents,
              attrs: {
                placeholder: '请选择负责人',
              },
            },
            {
              required: true,
              checked: true,
              type: 'DatePicker',
              props: {
                type: 'daterange',
                placeholder: '全部',
              },
              key: ['delivery_start_time', 'delivery_end_time'],
              label: '计划交货',
            },
            {
              checked: true,
              type: 'custom',
              component: store,
              key: 'store_id',
              label: '仓库',
            },
            {
              checked: true,
              require: true,
              width: 'auto',
              type: 'custom',
              name: '商品分类',
              key: ['category_id', 'category_id2', 'category_id3'],
              defaultValue: [],
              props: {
                noMaxHeight: true,
              },
              component: CommodityCascader,
            },
            {
              checked: false,
              type: 'Select',
              defaultValue: '0',
              key: 'is_confirm_supply',
              label: '供货确认',
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '已确认',
                  value: '1',
                },
                {
                  label: '未确认',
                  value: '2',
                },
              ],
            },
            {
              checked: false,
              type: 'Select',
              defaultValue: '0',
              key: 'purchase_source',
              label: '单据来源',
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '手动创建采购单',
                  value: '1',
                },
                {
                  label: '订单汇总生成采购单',
                  value: '2',
                },
                {
                  module: 'pre_purchase',
                  label: '预采购生成采购单',
                  value: '3',
                },
                {
                  label: '现场采购订单',
                  value: '4',
                },
                {
                  module: 'purchase_plan',
                  label: '采购任务生成采购单',
                  value: '5',
                },
                {
                  module: 'purchase_plan',
                  label: '采购任务生成采购单【联营】',
                  value: '6',
                },
                {
                  module: 'purchase_plan',
                  label: '导入生成采购单',
                  value: '9',
                },
              ],
            },
            {
              checked: false,
              label: '制单人',
              type: 'custom',
              key: 'operator_id',
              component: operator,
            },
            {
              checked: false,
              label: '打印状态',
              type: 'Select',
              key: 'print_status',
              defaultValue: 'all',
              data: [
                {
                  label: '全部',
                  value: 'all',
                },
                {
                  label: '未打印',
                  value: '2',
                },
                {
                  label: '已打印',
                  value: '1',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              required: false,
              checked: false,
              key: 'order_remark',
              label: '备注',
              type: 'Input',
              props: {
                placeholder: '输入采购单备注',
              },
            },
            {
              checked: false,
              label: '已收货金额',
              type: 'RangeInput',
              defaultValue: ['', ''],
              key: ['min_purchase_price', 'max_purchase_price'],
            },
            {
              checked: true,
              required: true,
              label: '采购状态',
              type: 'custom',
              key: 'status',
              noReset: true,
              props: {
                data: [],
              },
              block: true,
              defaultValue: this.getDefaultPurchaseStatus(),
              component: CheckboxGroup,
              onChange(value) {
                window.localStorage.setItem(
                  '_purchase_order_list_pur_statys',
                  value.join(','),
                );
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              required: true,
              checked: true,
              show: false,
              label: '销售订单号',
              type: 'Input',
              key: 'order_no',
              props: {
                placeholder: '输入销售订单号',
              },
            },
            {
              show: true,
              block: true,
              label: '商品标签',
              key: 'goods_tag_names',
              type: 'custom',
              attrs: {
                keyName: 'goods_tag_names',
              },
              component: goodsTag,
              onChange: (value) => {
                return { value, stop: true };
              },
            },
            {
              checked: true,
              required: true,
              label: '订单商品标签',
              key: 'tag_id',
              type: 'custom',
              show: false,
              noReset: true,
              component: CheckboxGroup,
              defaultValue:
                StorageUtil.getLocalStorage(
                  'purchase-order-list-order-goods-tag-list',
                ) || [],
              props: {
                data: [],
              },
              style: {
                minWidth: '1300px',
              },
              onChange: (value) => {
                StorageUtil.setLocalStorage(
                  'purchase-order-list-order-goods-tag-list',
                  value,
                );
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              checked: false,
              label: '计划采购金额',
              type: 'RangeInput',
              defaultValue: ['', ''],
              key: ['min_price', 'max_price'],
            },
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
            {
              show: () => this.isOpenProviderFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '供应商自定义字段',
              key: ['provider_customize_id', 'provider_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '7',
                label: '供应商自定义字段',
              },
              component: CustomizeCascader,
            },
          ],
        },
      ],
      columns: [
        {
          type: 'titleCfg',
          width: 52,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'purchase',
        },
        {
          align: 'left',
          type: 'selection',
          width: 25,
          fixed: 'left',
          style: {
            paddingLeft: 0,
          },
        },
        {
          title: '采购单号',
          key: 'pur_no',
          width: 220,
          minWidth: 160,
          resizable: true,
          fixed: 'left',
          render: (h, params) => {
            let data = params.row;
            return h(
              'div',
              {
                class: 'pur_no',
                style: {
                  display: 'flex',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
              },
              [
                h('div', { style: { maxWidth: '20px' } }, [
                  data.is_central_purchase == 1
                    ? h('span', { class: 'sui-icon icon-central mr4 f14' })
                    : [],
                  data.purchaseReturnStatus
                    ? h('span', { class: 'sui-icon icon-return mr4 f14' })
                    : [],
                ]),
                h('div', { class: 'pur-no__text' }, [
                  h(
                    'a',
                    {
                      class: ['detail-name'],
                      on: {
                        click: () => {
                          this.router.push({
                            path: 'purchaseOrderDetail',
                            query: {
                              keep_scroll: 1,
                              id: data.id,
                              pur_no: data.pur_no,
                            },
                          });
                        },
                      },
                    },
                    data.pur_no,
                  ),
                  h(
                    'div',
                    {
                      style: {
                        color: '#999999',
                      },
                    },
                    data.purchase_time,
                  ),
                ]),
              ],
            );
          },
        },
        {
          title: '更新时间',
          key: 'updated_time',
          width: 160,
          renderHeader: (h) => {
            console.log(111,this.sort_field);
            console.log(222,this.sort_type);
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#updateTimeSortIcon').click();
                      },
                    },
                  },
                  '更新时间',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort_field === 'updated_time' ? +this.sort_type : 0,
                    id: 'updateTimeSortIcon',
                  },
                  ref: 'TableHeadSortIcon',
                  on: {
                    onChange: (e) => {
                      this.sort_field = 'updated_time';
                      this.sort_type = +e;
                      window.localStorage.setItem(SORT_FIELD, this.sort_field);
                      window.localStorage.setItem(SORT_TYPE, this.sort_type);
                      this.$refs.table.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '采购类型',
          key: 'channel_type',
          width: 110,
          render: (h, params) => {
            let type = params.row.purchase_type;
            let typeText = '';
            switch (type) {
              case '1':
                typeText = '市场自采';
                break;
              case '2':
                typeText = '供应商直供';
                break;
              case '3':
                typeText = '指定供应商';
                break;
              case '5':
                typeText = '供应商联营';
                break;
            }
            return h('div', typeText);
          },
        },
        {
          title: '单据来源',
          key: 'pur_plan_no',
          minWidth: 140,
        },
        {
          title: '负责人',
          key: 'provider_supervisor_name',
          minWidth: 90,
        },
        {
          title: '采购员/供应商',
          key: 'multi_name',
          minWidth: 105,
        },
        {
          title: '联系方式',
          poptip: true,
          minWidth: 160,
          key: 'contact_phone',
        },
        {
          title: '供应商确认',
          key: 'confirm_supply',
          minWidth: 90,
        },
        {
          title: '集采同步',
          key: 'purchase_central_sync_status',
          minWidth: 90,
          render: (h, params) => {
            let rows = params.row;
            let status = '';
            if (rows.purchase_central_sync_status == 0) {
              status = h('span', '/');
            } else if (rows.purchase_central_sync_status == 1) {
              status = h('Icon', {
                props: {
                  type: 'md-checkmark-circle',
                  color: '#05B657',
                },
              });
            } else {
              status = h('span', '同步失败');
            }
            return status;
          },
        },
        {
          title: '商品种类',
          key: 'commodity_count',
          minWidth: 90,
        },
        {
          title: '计划采购金额',
          key: 'purchase_sub_price',
          minWidth: 100,
        },
        {
          title: '已收货金额',
          key: 'purchase_price',
          align: 'right',
          minWidth: 100,
        },
        {
          title: '退货金额',
          key: 'purchase_return_price'
        },
        {
          title: '实际金额',
          key: 'actual_price'
        },
        {
          title: '制单人',
          key: 'author',
          minWidth: 90,
        },
        {
          title: '状态',
          width: 90,
          minWidth: 90,
          key: 'purchase_status',
          render: (h, params) => {
            let color = 'blue';
            switch (params.row.purchase_status) {
              case '全部收货':
                color = 'status-success';
                break;
              case '待审核':
              case '待采购':
              case '部分收货':
                color = 'status-error';
                break;
              case '关闭':
                color = 'status-close';
                break;
            }
            return h('div', [
              h(
                'span',
                {
                  attrs: {
                    class: color,
                  },
                },
                params.row.purchase_status,
              ),
            ]);
          },
        },
        {
          title: '计划交货日期',
          key: 'plan_date',
          minWidth: 110,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#planDateSortIcon').click();
                      },
                    },
                  },
                  '计划交货日期',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort_field === 'plan_date' ? +this.sort_type : 0,
                    id: 'planDateSortIcon',
                  },
                  ref: 'TableHeadSortIcon',
                  on: {
                    onChange: (e) => {
                      this.sort_field = 'plan_date';
                      this.sort_type = +e;
                      window.localStorage.setItem(SORT_FIELD, this.sort_field);
                      window.localStorage.setItem(SORT_TYPE, this.sort_type);
                      this.$refs.table.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '备注',
          minWidth: 140,
          key: 'remark',
          poptip: true,
        },
        {
          title: '建议采购价',
          minWidth: 100,
          key: 'reference_price',
        },
        {
          title: '打印次数',
          key: 'print_num',
          minWidth: 90,
        },
        {
          title: '采购进度',
          key: 'procured_num',
          width: 100,
          render: (h, params) => {
            const { procured_num, purchase_num } = params.row;
            return <Progress value={procured_num} total={purchase_num} />;
          },
        },
        {
          title: '仓库',
          key: 'storage_name',
          minWidth: 100,
        },
        {
          title: '抹零金额合计',
          key: 'reduction_price',
          minWidth: 110,
        },
        {
          title: '编码',
          key: 'channel_type_code',
          minWidth: 105,
          render: (h, params) => {
            const { agent_code, provider_code } = params.row;
            return <span>{agent_code || provider_code || '--'}</span>;
          },
        },
        {
          title: '订单商品标签', // 有表头设置，后端走配置项，前端可以不用走配置
          key: 'tag_name',
          minWidth: 100,
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 196,
          style: {
            paddingRight: '0px',
          },
          actionCountLimit: 3,
          actions: (params) => {
            let { row } = params;
            let actions = [];

            if (
              Number(row.origin_purchase_status) !== STATUS_CLOSE &&
              Number(row.origin_purchase_status) !== STATUS_ALL_RECEIPTED &&
              row.purchase_central_control.can_receipt == 1 &&
              +row.is_hidden_receipt !== 1
            ) {
              actions.push({
                name: '收货',
                action: () => {
                  this.router.push({
                    path: '/purchase/purchaseReceipt',
                    query: { keep_scroll: 1, id: row.id },
                  });
                },
              });
            }
            if (
              row.is_can_edit == 1 &&
              row.purchase_central_control.can_edit == 1
            ) {
              actions.push({
                name: '编辑',
                action: () => {
                  this.router.push({
                    path: 'addPurchaseOrder',
                    query: { keep_scroll: 1, id: row.id },
                  });
                },
              });
            }
            actions.push({
              action: (params) => {
                this.curPrintRow = params.row;
                this.curPrintType = 'single';
                if (
                  params.row.purchase_status === '全部收货' ||
                  params.row.purchase_status === '部分收货'
                ) {
                  this.printType = 'PUR_TAKE';
                } else {
                  this.printType = 'PUR';
                }
                this.$nextTick(() => {
                  if (
                    +this.sysConfig
                      .open_choose_purchase_order_print_template === 0
                  ) {
                    // 直接打印
                    this._print();
                  } else {
                    this.showPrintTemplateChooseModal();
                  }
                });
              },
              name: '打印',
            });
            if (+row.is_hidden_copy !== 1) {
              //增加复制按钮
              actions.push({
                name: '复制',
                action: (params) => {
                  this.copyId = params.row.id;
                  this.copyOrderStatus = params.row.origin_purchase_status;
                  this.$refs.smodal.open();
                },
              });
            }

            actions.push({
              name: '导出',
              action: (params) => {
                this.useExportOrder(params.row.id);
              },
            });
            if (
              (row.purchase_status === '待采购' ||
                row.purchase_status === '待审核') &&
              row.purchase_central_control.can_close == 1 &&
              +row.is_hidden_close === 0
            ) {
              actions.push({
                confirm: '您是否确定关闭该订单？',
                name: '关闭',
                action: () => {
                  this.closeOrder(params.row.id, params.index);
                },
              });
            }
            // 集采增加审核
            if (row.purchase_central_control.can_audit == 1) {
              actions.push({
                name: '审核',
                confirm: '您是否确定审核该订单？',
                action: (params) => {
                  this.audit(params.row.id);
                },
              });
            }
            if (row.purchase_central_control.can_sync == 1) {
              actions.push({
                name: '同步',
                confirm: '您是否确定同步该订单？',
                action: (params) => {
                  this.synchronous(params.row.id);
                },
              });
            }
            if (row.channel_type != 5) {
              actions.push({
                action: (params) => {
                  this.router.push({
                    path: 'addReturnOrder',
                    query: {
                      pur_no: params.row.pur_no,
                      type: 0,
                      id: params.row.id,
                    },
                  });
                },
                name: '退货（部分）',
              });
              actions.push({
                action: (params) => {
                  this.router.push({
                    path: 'addReturnOrder',
                    query: {
                      pur_no: params.row.pur_no,
                      type: 1,
                      id: params.row.id,
                    },
                  });
                },
                name: '退货（整单）',
              });
            }
            actions.push({
              action: (params) => {
                this.$refs.qrCodeModal.open(params.row, true);
              },
              name: '分享',
            });
            return actions;
          },
        },
      ],
      data: [],
      download: [
        {
          text: '商品默认供应商',
          'before-download': (resolve, reject) =>
            this.exportOrderTemplate(resolve, reject, 1),
        },
        {
          text: '客户指定+商品默认',
          'before-download': (resolve, reject) =>
            this.exportOrderTemplate(resolve, reject, 2),
        },
      ],
      post: {
        url: '/superAdmin/purchaseOrder/OfflineImportPurchase',
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xlsx'],
      },
      orderList: [],
      purchaseTypeArr: null,
      import_store_id: '',
      sort: {
        plan_date: 0,
      },
      import_price_type: 0, // 选择单价或金额
      commodity_online: '0',
      import_goods_type: 1, // 选择单价或金额
      import_type: '0', // 导入模式
      priceTypes: [
        { id: 0, name: '采购单价' },
        { id: 1, name: '采购金额' },
      ],
      exportDataList: {},
      defaultDataList: [],
      groupList: [
        {
          title: '单据信息',
          keys: [
            'pur_no',
            'updated_time',
            'plan_date',
            'purchase_type',
            'purchase_type_code',
            'pur_order_remark',
            'purchase_time',
            'pur_plan_no',
            'storage_name',
            'author',
            'purchase_status_desc',
            'total_fee_cost_price',
            'tag_name',
            'provider_supervisor_name',
            'purchase_tel',
            'purchase_address',
          ],
        },
        {
          title: '商品明细',
          keys: [
            'commodity_id',
            'commodity_name',
            'category1_name',
            'category2_name',
            'category3_name',
            'commodity_code',
            'unit',
            'summary',
            'purchase_status_name',
            'all_need',
            'plan_purchase_price',
            'purchase_sub_price',
            'stage_num',
            'already_receipt_price',
            'total_price',
            'not_purchased',
            'reference_price',
            'remark',
            'order_info',
            'return_amount',
            'return_price',
            'actual_price',
            'input_tax_rate', // 税率
            'shelf_life',
            'is_purchase_agreement_price',
            'curr_stock_desc',
            'tax_class_code',
          ],
        },
      ],
      exportTotalFields: [
        {
          key: 'is_export_sub_total',
          label: '小计',
        },
        { key: 'is_export_total', label: '合计' },
      ],
      goodsTypes: [
        { id: 1, name: '商品名称' },
        { id: 2, name: '商品编码' },
      ],
      warehouseList: [],
      isCopyInStorageData: StorageUtil.getLocalStorage(LocalKey) || false,
      copyId: '',
      copyOrderStatus: '',
      sum: {
        in_total_price: 0,
      },
      printConfig: {
        single: (templateId) => {
          this.printPurchase(this.curPrintRow.id);
          this._printOrder({
            ...this.curPrintRow,
            template_id: templateId,
          });
        },
        batchSingle: (templateId) => {
          this._batchPrintOrder(templateId);
        },
        batchMerge: (templateId) => {
          this._batchMergePrintOrder(templateId);
        },
      },
      curPrintType: '',
      curPrintRow: {},
      printType: 'PUR',
      exportType: 'all',
      category_type: '2',
    };
  },
  watch: {
    $route: {
      handler() {
        if (this.$route.query.check_merge) {
          this.checkMerge = false;
          setTimeout(() => {
            this.checkMerge = true;
          });
        }
      },
    },
  },
  created() {
    this.getPurchaseType();
    this.getPurchaseOrderStatusMap();
    this.getTagList();
  },
  mounted() {
    this._filterEditionModule();
    this._reqGetUserWarehouse();
    if (this.$route.query.check_merge) {
      this.checkMerge = true;
    }
  },
  activated() {
    if (this.init) {
      this.fetchData(false, true, 'bar');
    } else {
      this.init = true;
    }
  },
  computed: {
    importParams() {
      let params = { cal_price_type: this.import_price_type };
      if (+this.import_type === 1) {
        params.import_mode = this.import_goods_type;
      }
      if (+this.import_type === 0) {
        params.commodity_online = this.commodity_online;
      }
      return params;
    },
    isCopyInStorageDataShow() {
      // 部分收货 || 全部收货
      let arr = ['4', '5'];
      return ~arr.indexOf(this.copyOrderStatus);
    },
  },
  methods: {
    getDefaultPurchaseStatus() {
      let defaultPurchaseStatus = window.localStorage.getItem(
        '_purchase_order_list_pur_statys',
      );
      if (defaultPurchaseStatus) {
        defaultPurchaseStatus = defaultPurchaseStatus.split(',');
      } else {
        defaultPurchaseStatus = [];
      }
      return defaultPurchaseStatus;
    },
    selectGoods(e) {
      this.$refs.table.fetchData();
    },
    isCopyInStorageDataOnChange(value) {
      StorageUtil.setLocalStorage(LocalKey, value);
    },
    getTagList() {
      if (!this.isPurchaseUseOrderCommodityTag) return;
      goods.getOrderGoodsTagList().then((data) => {
        const tagList = data.map((item) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
        this.origin_dvanceItems = this.origin_dvanceItems.map((item) => {
          item.items = item.items.map((_item) => {
            if (_item.key === 'tag_id') {
              _item.show = this.isPurchaseUseOrderCommodityTag;
              _item.props.data = tagList;
            }
            return _item;
          });
          return item;
        });
        this._initTableFilters();
      });
    },
    getPurchaseOrderStatusMap() {
      let purchaseOrderStatusMap = [
        { key: 1, name: '待审核' },
        { key: 2, name: '待采购' },
        { key: 4, name: '部分收货' },
        { key: 5, name: '全部收货' },
        { key: 6, name: '已关闭' },
      ];
      let statusCom = this.origin_dvanceItems[0].items.find(
        (item) => item.key === 'status',
      );
      statusCom.props.data = purchaseOrderStatusMap.map((item) => {
        return {
          label: item.name,
          value: item.key + '',
        };
      });
      let _storage = window.localStorage.getItem(
        '_purchase_order_list_pur_statys',
      );
      let defaultValue = [];
      purchaseOrderStatusMap.forEach((item) => {
        if (item.key !== 6) {
          defaultValue.push(item.key + '');
        }
      });

      statusCom.defaultValue = _storage ? _storage.split(',') : defaultValue;

      if (this.$route.query.status) {
        statusCom.defaultValue = this.$route.query.status.split(',');
      }
      this._initTableFilters();
      this.initParams = {
        start_time: date.getBeforeDate(30),
        end_time: date.getTodayDate(),
        status: statusCom.defaultValue,
        tag_id:
          StorageUtil.getLocalStorage(
            'purchase-order-list-order-goods-tag-list',
          ) || [],
      };
    },
    getPurchaseType() {
      goods.getPurchaseType().then((res) => {
        if (res.status) {
          this.purchaseTypeArr = res.data;
          purchaseList[0].children = res.data.agents.map((item) => {
            return { value: item.id, label: item.name };
          });
          purchaseList[1].children = res.data.providers.map((item) => {
            return { value: item.id, label: item.name };
          });
          selectData[1].children = res.data.agents.map((item) => {
            return { value: item.id, label: item.name };
          });
          let providersArr = [];
          let jointProvidersArr = [];
          res.data.providers.forEach((item) => {
            providersArr.push({ value: item.id, label: item.name });
            if (+item.provider_type === 2) {
              jointProvidersArr.push({ value: item.id, label: item.name });
            }
          });
          selectData[2].children = selectData[3].children = providersArr;
          selectData[4].children = jointProvidersArr;
        }
        // 更新采购负责人数据源
        this.origin_dvanceItems = this.origin_dvanceItems.map((item) => {
          item.items = item.items.map((_item) => {
            return _item;
          });
          return item;
        });
        this._initTableFilters();
        if (this.init) {
          this.fetchData(false, true, 'bar');
        } else {
          this.fetchData();
        }
      });
    },
    onDeliveryDateChange(val) {
      this.delivery_date = val;
    },
    changeImportType(value) {
      if (+value === 0) {
        this.download = [
          {
            text: '商品默认供应商',
            'before-download': (resolve, reject) =>
              this.exportOrderTemplate(resolve, reject, 1),
          },
          {
            text: '客户指定+商品默认',
            'before-download': (resolve, reject) =>
              this.exportOrderTemplate(resolve, reject, 2),
          },
        ];
      } else {
        this.download = {
          url: '/superAdmin/commoditySuper/exportExcel?is_process=0',
          text: '采购导入模板',
          'before-download': (resolve, reject) =>
            this.exportOrderTemplate(resolve, reject),
        };
      }
    },
    closeBatchReceiptErrorTipModal() {
      this.batchReceiptErrorTipModal.show = false;
      this.$refs.table.fetchData(false);
    },
    batchReceipt() {
      let ids = this.excludeStatus(this.selectedRows, [
        '待审核',
        '部分收货',
        '全部收货',
        '已关闭',
      ])
        .map((item) => item.id)
        .join(',');
      this.$request
        .post(this.apiUrl.purchaseOrder.batchReceipt, { ids })
        .then((res) => {
          if (res.status) {
            if (res.data && res.data.length > 0) {
              this.batchReceiptErrorTipModal.errorTips = res.data;
              this.batchReceiptErrorTipModal.show = true;
            } else {
              this.$Message.info('收货成功');
              this.$refs.table.fetchData(false);
            }
          } else {
            this.$Message.error(res.message || '网络错误');
          }
        });
    },
    openBatchReceiptModal() {
      this.batchReceiptModal.title = `确认对${this.selectedRows.length}笔采购单按本次收货数量和本次收货价进行批量收货`;
      this.batchReceiptModal.show = true;
    },
    onUpdateTax() {
      const ids = this.selectedRows.map((item) => item.id);
      if (!ids.length) return;
      this.updateTax.total = ids.length;
      let successNum = 0,
        failNum = 0;
      const update = (index) => {
        if (index > ids.length - 1) {
          this.updateTax.show = false;
          this.successNotice(
            failNum ? `更新成功${successNum}个，失败${failNum}个` : '更新成功',
          );
          return;
        }
        this.updateTax.current = index + 1;
        this.$request
          .post(this.apiUrl.updatePurchaseTax, { id: ids[index] })
          .then(
            (res) => {
              if (res.status) {
                successNum++;
              } else {
                failNum++;
              }
              update(index + 1);
            },
            () => {
              failNum++;
              update(index + 1);
            },
          );
      };
      this.updateTax.current = 0;
      this.updateTax.show = true;
      update(0);
    },
    _batchPrintOrderBefore() {
      if (+this.sysConfig.open_choose_purchase_order_print_template === 0) {
        // 直接打印
        this._batchPrintOrder();
      } else {
        this.printType = 'PUR';
        this.curPrintType = 'batchSingle';
        this.$nextTick(() => {
          this.showPrintTemplateChooseModal();
        });
      }
    },
    _batchMergePrintOrderBefore() {
      if (+this.sysConfig.open_choose_purchase_order_print_template === 0) {
        // 直接打印
        this._batchMergePrintOrder();
      } else {
        this.printType = 'PUR';
        this.curPrintType = 'batchMerge';
        this.$nextTick(() => {
          this.showPrintTemplateChooseModal();
        });
      }
    },
    // 批量打印
    _batchPrintOrder(templateId, params = {}) {
      const { isMerge = 0 } = params;
      let ids = this.selectedRows.map((item) => item.id).join(',');
      // 设置打印次数
      setTimeout(() => {
        this.$refs.table.fetchData(false);
      }, 1000);
      this._printOrder({
        id: ids,
        isBatch: true,
        isMerge,
        template_id: templateId,
      });
    },
    _batchMergePrintOrder(templateId) {
      this._batchPrintOrder(templateId, {
        isMerge: 1,
        template_id: templateId,
      });
    },
    _initTableFilters() {
      const advanceItems = this.deepClone(this.origin_dvanceItems);
      advanceItems[0].items[1].props.data = this.isOpenProviderDeliver
        ? selectData
        : selectData.slice(0, -1);
      advanceItems[0].items.forEach((item) => {
        if (item.label === '单据来源' && +this.spotPurchaseOrder === 2) {
          item.data.push({
            label: '订单直采单',
            value: '7',
          });
        }
        if (item.key === 'status') {
          item.defaultValue = this.getDefaultPurchaseStatus();
        }
        if (item.key === 'order_no') {
          item.show = this.isEnablePurchaseTask;
        }
      });
      this.advanceItems = advanceItems;
    },
    /**
     * 过滤当前项目版本没有的功能模块对应的数据
     */
    _filterEditionModule() {
      this.advanceItems = this.advanceItems.filter((advanceItem) => {
        advanceItem.items = advanceItem.items.filter((filterItem) => {
          if (filterItem.data && Array.isArray(filterItem.data)) {
            filterItem.data = filterItem.data.filter((dataItem) => {
              return this.$hasModule(dataItem.module);
            });
          }
          return true;
        });
        return true;
      });
    },
    openExportModal(forceShow, exportType) {
      this.exportType = exportType;
      this.$request
        .post(this.apiUrl.generalCustomExportColumn, { type: 'purchase_order' })
        .then((res) => {
          if (res.status) {
            this.exportDataList = res.data.all_column || {};
            this.defaultDataList = res.data.default_column || [];

            // 设置自定义字段, 先匹配到customize_field开头的字段
            let customizeField = Object.keys(this.exportDataList).filter(
              (item) => item.indexOf('customize_field') > -1,
            );
            customizeField.forEach((item) => {
              if (this.groupList[0].keys.indexOf(item) === -1) {
                this.groupList[0].keys.push(item);
              }
            });

            this.$refs.modal.open(forceShow);
          } else {
            this.$Message.error(res.message || '网络错误');
          }
        });
    },
    exportOrderList(result, opt) {
      let notSelected = result.notSelected;

      let params = this.$refs.table.getParams();
      params = this.handleFormatParams(params);

      params.hide_columns = JSON.stringify(notSelected);

      delete params.purchase_item_type;
      delete params.new_purchase_item_type;
      // if (
      //   !this.checkExportDate(
      //     params.delivery_start_time,
      //     params.delivery_end_time,
      //   )
      // ) {
      //   return;
      // }
      const idObj =
        this.exportType === 'batch'
          ? { id: this.selectedRows.map((item) => item.id).join(',') }
          : {};
      this.$request
        .post(this.apiUrl.ExportOrderBatch, { ...params, ...opt, ...idObj })
        .then((res) => {
          if (res.status) {
            // this.successNotice('导出任务提交成功！');
            this.$store.commit('showTaskCenter', true);
            exportLoop(res.data.task_no);
          } else {
            this.errorNotice(res.message || '导出失败');
          }
        });
    },
    checkExportDate(start, end) {
      let startDate = start;
      let endDate = end;
      this.exportErrTips = '';
      // if (!startDate || !endDate) {
      //   this.errorNotice('请筛选计划交货日期后才能导出，最多筛选七天。');
      //   return false;
      // }
      let dayAreaLimit = 30 * 24 * 60 * 60 * 1000;
      let startDateTimeStamp = new Date(startDate).getTime();
      let endDateTimeStamp = new Date(endDate).getTime();
      if (endDateTimeStamp - startDateTimeStamp > dayAreaLimit) {
        this.errorNotice('最多只能筛选31天的数据。');
        return false;
      }
      return true;
    },
    async audit(id) {
      let { status, message } = await post(api.AuditCentralPurchaseOrder, {
        id: id,
      });
      if (status) {
        this.$Notice.success({
          title: '审核成功',
        });
        this.fetchData(false);
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    async synchronous(id) {
      let { status, message } = await post(api.syncCentralPurchaseOrder, {
        id: id,
      });
      if (status) {
        this.$Notice.success({
          title: '同步成功',
        });
        this.fetchData(false);
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    fetchData(resetPage = false, keepScroll = true, loadingType = 'spinner') {
      if (this.$refs.table) {
        this.$refs.table.fetchData(resetPage, keepScroll, loadingType);
      }
    },
    closeOrder(id) {
      purchase.closeOrder({ id }).then((res) => {
        if (res.status) {
          this.successMessage('关闭成功！');
          this.fetchData(false);
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    addNewOrder() {
      this.router.push({ path: 'addPurchaseOrder', query: { keep_scroll: 1 } });
    },
    afterRequest(params, res) {
      if (res && res.data && res.data.sum) {
        this.sum = res.data.sum;
      }
      let newData;
      try {
        newData = params.map((item) => {
          let _key = 'agents',
            _id = 'agent_id';
          if (
            item.channel_type === '2' ||
            item.channel_type === '3' ||
            item.channel_type === '5'
          ) {
            _key = 'providers';
            _id = 'provider_id';
          }
          if (this.purchaseTypeArr[_key]) {
            this.purchaseTypeArr[_key].map((_item) => {
              if (_item.id === item[_id]) {
                item.contact_phone =
                  _key === 'providers'
                    ? `供应商:${_item.tel}`
                    : `采购员:${_item.phone}`;
              }
            });
          }

          item.multi_name = item.name || item.agent_name || item.provider_name;
          item._checked === undefined && (item._checked = false);
          return item;
        });
      } catch (error) {}
      return newData;
    },
    exportOrderTemplate(resolve, reject, template_type) {
      if (this.delivery_date) {
        this.showExportPickTimeModal = false;
        // const { store_id } = this.$refs.table.getParams();
        // 使用弹框上的仓库(单仓库的时候会隐藏选项，但是值还是有)
        this.$Message.loading({
          title: '提示',
          content: '模板下载中，请稍后。',
          loading: true,
          closable: false,
          duration: 0,
        });
        const store_id = this.import_store_id;
        const cal_price_type = this.import_price_type;
        const import_mode = this.import_goods_type;
        let params = {
          store_id,
          cal_price_type,
          delivery_date: this.delivery_date,
        };
        if (+this.import_type === 1) {
          params.import_mode = import_mode;
        }
        if (+this.import_type === 0) {
          params.commodity_online = this.commodity_online;
        }
        if (template_type) {
          params.template_type = template_type;
        }
        purchase.exportOrderTemplate(params).then((res) => {
          this.$Message.destroy();
          if (res.status) {
            resolve(res.data[0]);
          } else {
            reject(res.message);
            this.errorNotice(res.message || '下载失败');
          }
        });
      }
    },
    useExportOrder(id) {
      this.$request.get(this.apiUrl.exportPurchaseOrder, { id }).then((res) => {
        let { status, data, message } = res;
        if (status) {
          location.href = data;
        } else {
          this.modalError(message ? message : '导出失败');
        }
      });
    },
    printPurchase(id, index) {
      purchase.printPurchaseOrder({ id }).then((res) => {
        if (res.status) {
          this.$refs.table.fetchData(false);
        }
      });
    },
    excludeStatus(list, statusArr) {
      return list.filter((item) => !statusArr.includes(item.purchase_status));
    },
    batchCloseOrder() {
      const batchOrder = () => {
        let idList = this.excludeStatus(this.selectedRows, [
          '已关闭',
          '部分收货',
          '全部收货',
        ])
          .map((item) => item.id)
          .join(',');
        purchase.batchCloseOrder(idList).then((res) => {
          let { status, message } = res;
          if (status) {
            this.$snotice({
              type: 'success',
              title: '批量关闭采购单成功',
            });
            this.$refs.table.fetchData(false);
          } else {
            this.$snotice({
              type: 'error',
              title: '批量关闭采购单失败',
              text: message || '',
            });
          }
        });
      };
      this.$smodal({
        type: 'warning',
        title: '关闭确认',
        text: `
          <p style="margin-bottom:10px;">已选择<em style="color: #03ac54;font-weight: 600;margin: 0 2px;">${this.selectedRows.length}</em>个采购单同时关闭，关闭后的采购单无法恢复，确定关闭这些采购单？</p>
        `,
        onOk: () => {
          batchOrder();
        },
      });
    },
    mergeOrder() {
      if (this.selectedRows.length < 2) {
        this.errorMessage('至少选择2个采购单进行合并');
        return false;
      }
      const mergeOrder = () => {
        let orderNoStr = this.selectedRows.map((item) => item.pur_no).join(',');
        purchase.mergeOrder([orderNoStr]).then((res) => {
          let { status, message } = res;
          if (status) {
            this.$snotice({
              type: 'success',
              title: '采购单合并成功',
            });
            this.$refs.table.fetchData(false);
          } else {
            this.$snotice({
              type: 'error',
              title: '采购单合并失败',
              text: message || '',
            });
          }
        });
      };
      this.$smodal({
        type: 'warning',
        title: '提示',
        text: `
          <p style="margin-bottom:10px;">已选择<em style="color: #03ac54;font-weight: 600;margin: 0 2px;">${this.selectedRows.length}</em>个采购单，是否确定合并？</p>
          <span style="color: #999;">注意：只有同一个采购员或供应商相同的交货日期的采购单才能合并，并且只有待采购的单据才能合并，已收货的采购单无法合并！</span>
        `,
        onOk: () => {
          mergeOrder();
        },
      });
    },
    importBtnCallBack(importSuccess) {
      if (importSuccess) {
        this.successNotice('批量导入成功');
        this.fetchData();
      }
    },
    importSuccess(data) {
      this.router.push({
        path: 'add',
        query: { id: data },
      });
    },
    handleCancelSelect() {
      let list = this.$refs.table.getData();
      list = list.map((item) => {
        item._checked = false;
        return item;
      });
      this.$refs.table.setData(list);
      this.selectedRows = [];
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    handleFormatParams(params) {
      const {
        purchase_type,
        purchase_item_type,
        new_purchase_type,
        new_purchase_person,
      } = params;
      if (+purchase_type === 1) {
        params.agent = purchase_item_type;
      } else {
        params.provider = purchase_item_type;
      }
      if (+new_purchase_type === 1) {
        params.new_agent = new_purchase_person;
      } else {
        params.new_provider = new_purchase_person;
      }

      const sortMap = ['', 'desc', 'asc'];
      if (this.sort_field && +this.sort_type) {
        params.order_by = this.sort_field + ' ' +sortMap[+this.sort_type];
      }

      if (Array.isArray(params.status)) {
        params.status = params.status.join(',');
      }

      if (Array.isArray(params.tag_id)) {
        params.tag_id = params.tag_id.join(',');
      }
      return params;
    },
    beforeRequest(params) {
      params = this.handleFormatParams(params);

      // if (!params.store_id) {
      //   return false;
      // }

      //查询之前判断库房是否发生改变(单仓始终取第一个，多仓才进行切换)，如果改变，赋值给批量导入采购单上的库房
      if (this.warehouseList.length > 1) {
        if (params.store_id !== this.import_store_id) {
          this.import_store_id = params.store_id ? params.store_id : '';
        }
      }

      return params;
    },
    _reqGetUserWarehouse() {
      storeRoomRes.getUserWarehouse().then((res) => {
        if (res.status) {
          this.warehouseList = Object.freeze(res.data);
          if (this.warehouseList.length === 1) {
            //  如果是单仓，默认取第一个仓库，不让用户选择
            this.import_store_id = res.data[0].id;
          }
          // 更新筛选项库房数据
          this.origin_dvanceItems = this.origin_dvanceItems.map((item) => {
            item.items = item.items.map((_item) => {
              return _item;
            });
            return item;
          });
          this._initTableFilters();
        } else {
          this.errorNotice(res.message);
        }
      });
    },
    copyConfirm() {
      this.$router.push({
        path: '/purchase/addPurchaseOrder',
        query: {
          keep_scroll: 1,
          copyId: this.copyId,
          isCopyInStorageData: Number(this.isCopyInStorageData),
        },
      });
    },
    showPrintTemplateChooseModal() {
      this.$refs.printTemplateChooseNewRef.open();
    },
    closePrintTemplateChooseModal() {
      this.$refs.printTemplateChooseNewRef.closeModal();
    },
    _print(templateId) {
      this.printConfig[this.curPrintType](templateId);
      this.closePrintTemplateChooseModal();
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .table__selection-panel__selected {
  margin-left: 20px;
}

/deep/ .list-table__selection-panel {
  padding: 8px 20px;
}
/deep/ .list-table .sdp-table__header-top .ivu-btn {
  margin-right: 6px;
}
.purchase__list {
  .pur_no {
    /deep/.sui-icon {
      font-size: 14px !important;
      margin-right: 4px;
    }
  }
  .icon-return {
    color: #ffa001;
  }
  .icon-central {
    color: #0679ff;
  }
}

// .sdp-table__tr:hover {
//   .detail-name {
//     color: #03ac54;
//   }
// }
.ml10 {
  margin-left: 10px;
}
.warehouse {
  width: 100%;
  display: inline-flex;
  align-items: center;
  margin-top: 20px;
  &__text {
    display: inline-block;
    width: 23%;
    text-align: right;
  }
  &__select {
    margin-left: 12px;
    width: 209px;
    &__error {
      color: red;
      position: absolute;
      font-size: 12px;
    }

    label {
      height: 20px;
      line-height: 20px;
      font-size: 13px;
    }
  }
}
</style>
