<!--
 * @Author: <PERSON>
 * @Date: 2022-02-22 11:15:32
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-09-11 14:59:24
 * @Description: 新增采购单（新版）
-->
<template>
  <div class="purchase-order-add">
    <DetailPage
      pageType="add"
      title="新增采购单"
      :disabledSave="saveLoading"
      @on-save="$_onSave"
    >
      <Form
        ref="formValidate"
        :model="formValidate"
        :rules="ruleValidate"
        inline
        label-colon
        :label-width="110"
        :disabled="false"
      >
        <base-block title="基础信息" class="base-info">
          <FormItem label="采购" prop="purchase_type">
            <PurchaseSelect
              :value="purchaseSelectValue"
              :level="[1, 2]"
              :change-on-select="false"
              :params="{
                filter_disable_provider: 1,
                filter_disable_agent: 1,
              }"
              use-hotkey
              placeholder="请选择采购类型"
              @on-change="_changePurchase"
            ></PurchaseSelect>
          </FormItem>
          <FormItem
            label="负责人"
            prop="provider_supervisor"
            v-if="isEnablePurchaseTask"
          >
            <PurchaseAgent
              class="supervisor"
              v-model="formValidate.provider_supervisor"
              filterable
            ></PurchaseAgent>
          </FormItem>
          <FormItem label="计划交货日期" prop="plan_date">
            <DatePicker
              :value="formValidate.plan_date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              @on-change="formValidate.plan_date = $event"
            ></DatePicker>
          </FormItem>
          <FormItem
            label="计价日期"
            prop="valuation_date"
            v-if="modeValuationDate"
          >
            <DatePicker
              :value="formValidate.valuation_date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              @on-change="handleValuationDateChange"
            ></DatePicker>
          </FormItem>
          <FormItem label="仓库" prop="storeId">
            <StoreSelect
              v-model="formValidate.storeId"
              filterable
            ></StoreSelect>
          </FormItem>
          <FormItem label="排序方式" prop="sort_type">
            <RadioGroup v-model="formValidate.sort_type">
              <Radio :label="0">添加顺序</Radio>
              <Radio :label="1">商品分类</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="最近一次进货价" prop="update_in_price">
            <RadioGroup
              @on-change="changeUpdateInPrice"
              v-model="formValidate.update_in_price"
            >
              <Radio :label="1">更新</Radio>
              <Radio :label="2">不更新</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem
            v-if="isPurchaseUseOrderCommodityTag"
            label="订单商品标签"
            prop="tag_id"
            :rules="tagRules"
          >
            <Select
              v-model="formValidate.tag_id"
              clearable
              transfer
              placeholder="请选择"
            >
              <Option
                v-for="tag in tagList"
                :value="tag.id"
                :key="tag.id"
                :label="tag.name"
              >
              </Option>
            </Select>
          </FormItem>
        </base-block>
        <template
          v-if="
            formValidate.purchase_type &&
            (formValidate.agent_id !== '' || formValidate.provider_id !== '')
          "
        >
          <base-block title="商品清单">
            <SFullscreen @change="updateTableHeight">
              <Row :gutter="10" type="flex" align="middle" slot="action-left">
                <Col>
                  <InputSearch
                    v-model="searchValue"
                    placeholder="请输入商品名称/商品编码"
                    @on-change="_onSearchCommodity"
                  ></InputSearch>
                </Col>
              </Row>
              <Row
                style="color: var(--primary-color); cursor: pointer"
                :gutter="18"
                type="flex"
                align="middle"
                slot="action-right"
              >
                <Col class="mb-7">
                  <div style="margin-left: auto">
                    <span
                      style="vertical-align: middle; color: rgba(0, 0, 0, 0.85)"
                    >
                      参考信息
                      <Tooltip placement="top" content="鼠标选中商品后展示">
                        <s-icon
                          style="
                            cursor: pointer;
                            color: rgba(0, 0, 0, 0.8);
                            margin-top: -2px;
                          "
                          icon="tips"
                        ></s-icon>
                      </Tooltip>
                    </span>
                    <Switch
                      v-model="showReference"
                      class="switch-new"
                      @on-change="handleChangeReferStatus"
                    ></Switch></div
                ></Col>
                <Col class="mb-7" style="padding-left: 7px; margin-right: 0">
                  <div class="mr-12">
                    <Checkbox
                      v-model="isAllowAddSameGoods"
                      @on-change="_changeIsAllowAddSameGoods"
                      ><span class="ml3">同一商品重复录入</span></Checkbox
                    >
                  </div>
                </Col>
              </Row>
              <EditableTable
                ref="editableTable"
                :rowKey="rowKey"
                :loading="tableLoading"
                :data="dataList"
                :has-customize-field="true"
                :columns="columns"
                :isShowRecordEditor="true"
                :row-class-name="rowClassName"
                :max-height="tableHeight"
                :stickyTop="101"
                :row-config="{ useKey: true }"
                enterAsDown
                @on-insert="onInsert"
                @on-delete="onDelete"
                @on-row-click-only="handleRowClick"
                @on-sort-change="handleSortChange"
                @on-draggable-data="_onDraggableData"
              >
                <template #after-table-right>
                  <span>合计：</span>
                  <span>计划采购量：{{ calTotalPurchaseNum }}</span>
                  <span class="ml6">计划采购金额：{{ calTotalPrice }}</span>
                </template>
              </EditableTable>
              <ReferenceInfo
                ref="reference"
                :show="showReference"
                :noOrder="true"
              ></ReferenceInfo>
            </SFullscreen>
          </base-block>
          <base-block title="其他信息" class="base-info">
            <FormItem label="备注" :label-width="46">
              <Input
                v-model="purchaseOrderRemark"
                type="textarea"
                :rows="3"
                :maxlength="128"
                :autosize="{ minRows: 3, maxRows: 8 }"
                show-word-limit
                placeholder="请输入备注信息"
                style="width: 318px"
              ></Input>
            </FormItem>

            <FormItem label="附件" :label-width="46" style="width: 100%">
              <AttachmentUpload v-model="attachmentFiles" />
            </FormItem>
          </base-block>
        </template>
      </Form>

      <template #button-after>
        <Button
          type="primary"
          ghost
          :disabled="saveLoading"
          @click="$_onSave(1)"
          >保存并新增</Button
        >
      </template>
    </DetailPage>
  </div>
</template>

<script>
import DetailPage from '@/components/detail-page/index.js';
import EditableTable from '@/components/editable-table/index.js';
import PurchaseSelect from '@/components/common/purchaseSelect.vue';
import PurchaseAgent from '@components/common/PurchaseAgent';
import StoreSelect from '@components/common/storeSelect_new';
import CommoditySelect from '@/components/common/CommoditySelect';
import SelfIcon from '@/components/icon';
import TooltipECharts from '../tooltip-e-charts';
import InputSearch from './components/input-search.vue';
import settings from '@api/settings.js';
import purchase from '@/api/purchase';
import ConfigMixin from '@/mixins/config.js';
import CommonMixin from './mixin';
import Goods from '@api/goods.js';
import { cloneDeep } from 'lodash-es';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import ReferenceInfo from './components/reference-info.vue';
import { getEditTableHeight } from '@/util/common';
import { get } from '@/api/request';
import StorageUtil from '@util/storage.js';
import SFullscreen from '@/components/s-fullscreen';
import Color from '@/pages/shop/home/<USER>';
import { uniqueId } from 'lodash-es';
import {Icon} from 'view-design';

export default {
  name: 'PurchaseOrderAdd',
  mixins: [ConfigMixin, CommonMixin],
  components: {
    DetailPage,
    EditableTable,
    PurchaseSelect,
    PurchaseAgent,
    StoreSelect,
    InputSearch,
    AttachmentUpload,
    ReferenceInfo,
    SFullscreen,
    Color,
  },
  data() {
    return {
      isFullscreen: false,
      tableHeight: getEditTableHeight(),
      is_default: '',
      isAllowAddSameGoods: true,
      formValidate: {
        plan_date: this.getCustomToday(6),
        storeId: '',
        purchase_type: '',
        agent_id: '',
        provider_id: '',
        purchase_time: '',
        sort_type: 0,
        update_in_price: '',
        provider_supervisor: '',
        tag_id: '',
        valuation_date: this.getCustomToday(6),
      },
      ruleValidate: {
        plan_date: [
          {
            required: true,
            type: 'string',
            message: '请选择日期',
            trigger: 'change',
          },
        ],
        purchase_type: [
          { required: true, message: '请选择采购类型', trigger: 'blur' },
        ],
        provider_supervisor: [{ required: true, message: '请选择负责人' }],
      },
      tagRules: {
        required: false,
        message: '请选择订单商品标签',
        trigger: 'change',
      },
      searchValue: '', // 商品搜索
      isModified: false, // 标记页面数据是否被修改过
      rowKey: 'id', // 行唯一标识
      tableLoading: false,
      dataList: [],
      storeList: [],
      columns: [],
      purchaseOrderRemark: '',
      saveLoading: false,
      copyId: '',
      pageReady: false,
      pageIntroLoaded: false,
      attachmentFiles: [],
      tagList: [],
    };
  },
  computed: {
    // 按计价日期生效模式
    modeValuationDate() {
      return +this.sysConfig.purchase_agreement_price_mode === 2;
    },
    purchaseSelectValue() {
      const { purchase_type, agent_id, provider_id } = this.formValidate;
      const purchaseValue = agent_id || provider_id;
      this.$refs.formValidate &&
        this.$refs.formValidate.validateField('purchase_type');
      return purchase_type
        ? [String(purchase_type), String(purchaseValue)]
        : [];
    },
    calTotalPurchaseNum() {
      return this.storeList
        .reduce((prev, next) => prev.add(Number(next.purchase_num)), 0)
        .toFixed(2);
    },
    calTotalPrice() {
      return this.storeList
        .reduce((prev, next) => prev.add(Number(next.total_price)), 0)
        .toFixed(2);
    },
  },
  watch: {
    'formValidate.sort_type': {
      deep: true,
      immediate: true,
      handler() {
        this.getColumns()
      }
    }
  },
  created() {
    this.init();
    window.addEventListener('unload', this.setStoreData);
  },
  methods: {
    getColumns() {
      this.columns = [
        ...(this.formValidate.sort_type == 0 ? [{
          type: 'drag',
          width: 28,
          minWidth: 28,
          fixed: 'left',
          align: 'center',
          style: {
            paddingRight: 0,
            paddingLeft: '6px',
          },
          className: 'drag-row', // 重写样式覆盖left
          render: (h, params) => {
            return h(Icon, {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer'
              },
              props: {
                size: 13,
                icon: 'sort'
              },
            });
          },
        },] : []),
        {
          type: 'titleCfg',
          titleType: 'purchase_edit',
          minWidth: 52,
          width: 52,
          align: 'center',
          fixed: 'left',
          style: {
            paddingRight: 0,
            paddingLeft: '12px',
          },
        },
        {
          key: 'index',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px',
          },
        },
        {
          type: 'img',
          title: '图片',
          key: 'logo',
          width: 60,
          align: 'center',
          fixed: 'left',
          defaultValue:
            'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
          style: {
            padding: '0 10px',
          },
        },
        {
          title: '商品名称',
          key: 'name',
          width: 187,
          fixed: 'left',
          style: {
            paddingRight: '6px',
          },
          className: 'commodity-name',
          render: (h, params) => {
            const { row, index } = params;
            return (
              <CommoditySelect
                commodityName={row.name}
                dataProvider={this.apiUrl.getPurchaseCommoditys}
                params={{
                  unfilter_online: 1,
                  plan_date: this.formValidate.plan_date,
                  provider_id: this.formValidate.provider_id,
                  valuation_date: this.formValidate.valuation_date,
                }}
                trimInput={true}
                commodityIdKey="commodity_id"
                commodityNameKey="name"
                selectedData={this.dataList}
                onOn-change={(cid) => this._setCommodity(cid, row, index)}
                onOn-enter={() => this._insertAt(index)}
                style={{ width: '174px' }}
                inputProps={{
                  type: 'textarea',
                  rows: 1,
                  autosize: { minRows: 1, maxRows: 2.2 },
                }}
                slot-type="purchase"
              ></CommoditySelect>
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 105,
        },
        {
          title: '单位',
          key: 'unit',
          minWidth: 60,
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 100,
        },
        {
          title: '商品分类',
          key: 'category_name',
          minWidth: 100,
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 120,
        },
        {
          title: '税率',
          minWidth: 60,
          key: 'input_tax_rate',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              !row.input_tax_rate || row.input_tax_rate === '-'
                ? '--'
                : row.input_tax_rate + '%',
            );
          },
        },
        {
          title: '税额',
          minWidth: 70,
          key: 'input_tax',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              !row.input_tax || row.input_tax === '-' ? '--' : row.input_tax,
            );
          },
        },
        {
          title: '计划采购金额(不含税)',
          key: 'tax_exclusive',
          width: 160,
          minWidth: 160,
          align: 'right',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            const { row } = params;
            const tax_exclusive = Number(row.tax_exclusive).toFixed(4);
            return h('span', tax_exclusive);
          },
        },
        // 有两个保质期，因为有开启与不开启is_batch 配置项两种情况
        // shelf_life  保质期   开启 is_batch 配置项是这个
        // durability_period   保质期  没开 is_batch 配置项是这个
        {
          title: '保质期',
          key: 'shelf_life',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '保质期',
          key: 'durability_period',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '产地',
          key: 'product_place',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '近一次采购价',
          key: 'in_price',
          minWidth: 100,
          align: 'right',
          render: (h, params) => {
            const { row: data, index } = params;
            return h('div', [
              h('span', data.in_price),
              data.in_price &&
                h(
                  'Tooltip',
                  {
                    props: {
                      placement: 'top-start',
                      transfer: true,
                      theme: 'light',
                    },
                    nativeOn: {
                      mouseenter: () => {
                        this.$refs['TooltipECharts' + index].initEchart(
                          data,
                          this.formValidate.storeId,
                        );
                      },
                    },
                    scopedSlots: {
                      content: () => {
                        return this.$createElement(TooltipECharts, {
                          ref: 'TooltipECharts' + index,
                        });
                      },
                    },
                  },
                  [
                    h(SelfIcon, {
                      props: {
                        icon: 'jiagebodong_p',
                        size: '20px',
                      },
                      nativeOn: {
                        mouseenter: (e) => {
                          e.target.style.color = '#0aa251';
                        },
                        mouseleave: (e) => {
                          e.target.style.color = '#ddd';
                        },
                        click: ($event) => $event.stopPropagation(),
                      },
                      style: {
                        cursor: 'pointer',
                        color: '#ccc',
                        marginLeft: '10px',
                        verticalAlign: 'top',
                      },
                    }),
                  ],
                ),
            ]);
          },
        },
        {
          title: '供应商/采购员上一次入库价',
          key: 'last_in_store_price'
        },
        {
          title: '采购询价',
          key: 'inquiry_price',
          minWidth: 80,
        },
        {
          title: '仓内可用库存',
          width: 140,
          key: 'available_stock',
          tip: '现有库存 - 分拣占用库存，值为负数时，显示为0',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.available_stock || '-'}${data.stock_unit || '-'}`,
            );
          },
        },
        {
          title: '分拣占用库存',
          width: 140,
          key: 'sorting_occupy_inventory',
          tip: '已分拣未出库的商品库存数量，不含供应商联营的部分',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.sorting_occupy_inventory || '-'}${data.stock_unit || '-'}`,
            );
          },
        },
        {
          title: '市场参考价',
          key: 'cloud_reference_price',
          minWidth: 100,
          align: 'right',
        },
        {
          title: '市场价',
          key: 'market_price',
          minWidth: 100,
          align: 'right',
        },
        {
          title: '条形码',
          key: 'barcode',
          minWidth: 90,
          render: (h, { row }) => {
            return <span>{(row.barcode || []).join(',')}</span>;
          },
        },
        {
          title: '现有库存',
          key: 'curr_stock_desc',
          minWidth: 100,
        },
        {
          type: 'NumberInput',
          title: '计划采购量',
          key: 'purchase_num',
          width: 110,
          require: true,
          setTip:
            '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          tip: '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
          // renderHeader: () => {
          //   return <span class="column__required">计划采购量</span>
          // },
          defaultValue: 1,
          props: ({ row, index }) => ({
            class: {
              error: row.purchase_num__error,
            },
            attrs: {
              id: 'purchase_num-' + index,
            },
            value: row.purchase_num,
            min: 0,
            max: 999999999.99,
            precision: 2,
            on: {
              'on-change': (val) => {
                row.calculate_way = 1;
                row.purchase_num__error = false;
                row.purchase_num = val;
                row.total_price =
                  Number(row.purchase_price).mul(+val).toFixed(2) || 0;
                this.changes(row, index);
              },
              'on-enter': () => this._insertAt(index),
            },
          }),
        },
        {
          title: '计划采购价',
          width: 140,
          key: 'purchase_price',
          fixed: 'right',
          setTip:
            '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          tip: '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          require: true,
          sortable: true,
          style: {
            padding: '0 10px',
          },
          // renderHeader: () => {
          //   return <span class="column__required">计划采购价</span>
          // },
          render: (h, params) => {
            const { row, index } = params;
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                },
              },
              [
                h('NumberInput', {
                  style: {
                    flex: 1,
                  },
                  props: {
                    value: Number(row.purchase_price),
                    min: 0,
                    max: 999999999,
                  },
                  on: {
                    'on-change': (val) => {
                      row.calculate_way = 1;
                      row.purchase_price = val;
                      row.total_price =
                        Number(val).mul(row.purchase_num).toFixed(2) || 0;
                      this.changes(row, index);
                    },
                    'on-focus': (event) => {
                      event.target.select();
                    },
                    'on-enter': () => this._insertAt(index),
                  },
                }),
                h(row.price_type == 1 ? SelfIcon : '', {
                  class: 'ml5',
                  props: {
                    icon: 'xie',
                    size: 16,
                  },
                }),
              ],
            );
          },
        },
        {
          type: 'NumberInput',
          title: '计划采购金额',
          key: 'total_price',
          setTip: '计划采购价 * 计划采购量',
          tip: '计划采购价 * 计划采购量',
          minWidth: 120,
          width: 120,
          require: true,
          fixed: 'right',
          style: {
            padding: '0 0px',
          },
          props: ({ row, index }) => ({
            min: 0,
            max: 999999999,
            // precision: 2,
            value: row.total_price,
            on: {
              'on-change': (val) => {
                row.calculate_way = 2;
                row.total_price = val;
                if (Number(row.purchase_num) > 0) {
                  row.purchase_price = Number(val)
                    .div(row.purchase_num)
                    .toFixed(2);
                }
                this.changes(row, index);
              },
              'on-enter': () => this._insertAt(index),
            },
          }),
        },
        {
          type: 'Input',
          title: '备注',
          minWidth: 120,
          key: 'remark',
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
          props: ({ row, index }) => ({
            type: 'textarea',
            rows: 1,
            autosize: { minRows: 1, maxRows: 2.2 },
            on: {
              'on-change': (e) => {
                this.storeList[index].remark = e.target.value;
                row.remark = e.target.value;
              },
              'on-enter': () => this._insertAt(index),
              'on-keydown': (e) => {
                if (e.keyCode === 13) e.preventDefault();
              },
            },
          }),
        },
      ]
    },
    updateTableHeight(isFullscreen, screenHeight) {
      this.isFullscreen = isFullscreen;
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 94;
      }
    },
    handleValuationDateChange(date) {
      // 采购类型不为供应商、商品列表为空时，不触发
      const goodsIds = this.dataList.map((item) => item.commodity_id).join(',');
      if (!this.formValidate.provider_id || !goodsIds) {
        return (this.formValidate.valuation_date = date);
      }
      const lastDate = this.formValidate.valuation_date;
      this.$smodal({
        title: '确认',
        text: '修改计价日期后，计划采购价重新按匹配生效的采购协议单取价',
        type: 'warning',
        btns: 2,
        okTxt: '继续',
        quitTxt: '取消',
        onOk: () => {
          this.formValidate.valuation_date = date;
          // 状态为待采购、部分收货的商品，计划采购价重新按匹配生效的采购协议单取价
          this.batchUpdatePurchasePrice();
        },
        onQuit: () => {
          this.formValidate.valuation_date = lastDate;
        },
      });
      return (this.formValidate.valuation_date = date);
    },
    async batchUpdateLastCommodityInfo() {
      const goodsIds = this.dataList.filter(item => !!item.commodity_id).map((item) => item.commodity_id);
      if (!goodsIds.length) {
        return;
      }
      let { data } = await get('/superAdmin/purchaseOrder/latestCommodityInfo', {
        provider_id: this.formValidate.provider_id,
        agent_id: this.formValidate.agent_id,
        commodity_ids: goodsIds.join(','),
      });
      data = data || [];
      this.dataList.forEach((item, index) => {
        const lastCommodityInfo = data.find((d) => d.commodity_id === item.commodity_id);
        if (lastCommodityInfo) {
          this.$set(this.dataList, index, {
            ...item,
            ...lastCommodityInfo,
          });
        }
      });
    },
    // 根据接口数据批量更新计划采购价
    async batchUpdatePurchasePrice() {
      const goodsIds = this.dataList.map((item) => item.commodity_id);
      const params = {
        provider_id: this.formValidate.provider_id,
        valuation_date: this.formValidate.valuation_date,
        commodity_ids: goodsIds.join(','),
      };
      if (!params.commodity_ids) {
        return;
      }
      // 返回的商品都是有协议价的商品
      const { data: goodsList } = await get(
        '/superAdmin/purchaseAgreementPrice/getPriceByValuationDate',
        params,
      );
      this.dataList.forEach((item, index) => {
        const goodsItem = goodsList.find(
          (d) => d.commodity_id === item.commodity_id,
        );
        const storeItem = this.storeList[index] || {};
        if (goodsItem) {
          const { price: purchase_price } = goodsItem;
          const purchase_num = storeItem
            ? storeItem.purchase_num
            : item.purchase_num;
          this.$set(this.dataList, index, {
            ...item,
            ...storeItem,
            purchase_price,
            price_type: 1,
            total_price: Number(purchase_price).mul(purchase_num).toFixed(2),
          });
          this.changes(this.dataList[index], index);
        } else {
          this.$set(this.dataList, index, {
            ...item,
            ...storeItem,
            price_type: 0,
          });
        }
      });
    },
    changeUpdateInPrice(value) {
      this.formValidate.update_in_price = value;
      localStorage.setItem('purchaseOrder_updateInPrice', value);
    },
    handleRowClick(row) {
      this.handleCheckReference({ row });
    },
    handleSortChange(columns, key, order) {
      this.dataList = this.dataList.sort((a, b) => {
        if (order === 'asc') {
          if (isNaN(a[key])) {
            return a[key].localeCompare(b[key]);
          } else {
            return a[key] - b[key];
          }
        } else if (order === 'desc') {
          if (isNaN(a[key])) {
            return b[key].localeCompare(a[key]);
          } else {
            return b[key] - a[key];
          }
        } else {
          return a._sortNum - b._sortNum;
        }
      });
      this._syncStoreList();
    },
    async init() {
      this.formValidate.update_in_price = this.isOpenUpdateInPrice ? 1 : 2;
      const { copyId, isCopyInStorageData } = this.$route.query;
      await this.getTagList();
      const orderNewData = StorageUtil.getLocalStorage('PURCHASE_NEW_DATA');
      if (copyId) {
        this.copyId = copyId;
        this._getPurchaseOrderDetail(copyId, Number(isCopyInStorageData));
      } else if (orderNewData) {
        this.$Modal.confirm({
          title: '提示',
          content: '已保存本地离线数据，是否恢复？',
          onOk: () => {
            StorageUtil.removeLocalStorage('PURCHASE_NEW_DATA');
            orderNewData.commoditys.forEach((item) => {
              item.purchase_price = item.purchase_price || 0;
              item.tax_exclusive = item.tax_exclusive || 0;
            });
            this.getOrderNewData(orderNewData);
          },
          onCancel: () => {
            StorageUtil.removeLocalStorage('PURCHASE_NEW_DATA');
          },
        });
      }
      this.getCompanyInfo();
      this._initIsAddSameGoods();
    },
    getTagList() {
      if (!this.isPurchaseUseOrderCommodityTag) return;
      this.tagRules.required = this.isOrderCommodityTagRequired;
      Goods.getOrderGoodsTagList().then((data) => {
        console.log('tagList', data);
        this.tagList = data;
        this.formValidate.tag_id =
          this.tagList.find((item) => item.is_default === '1').id || '';
        console.log('tagList-tag_id', this.formValidate.tag_id);
      });
    },
    getCompanyInfo() {
      settings.getCompanyInfo().then((res) => {
        if (res.status) {
          let data = res.data;
          this.is_default = data.is_default;
        }
      });
    },
    resetForm() {
      this.dataList = [];
      this.searchValue = '';
      this.attachmentFiles = [];
      this.purchaseOrderRemark = '';
      this.$refs.formValidate.resetFields();
      this.formValidate = {
        plan_date: this.getCustomToday(6),
        storeId: this.storage.getLocalStorage('init_ware_house_id'),
        purchase_type: '',
        agent_id: '',
        provider_id: '',
        purchase_time: '',
        sort_type: 0,
        update_in_price: 1,
        provider_supervisor: '',
      };
    },
    getEditTableHeight,
    _initPageReady() {
      this.$nextTick(() => {
        let unwatchDataList = this.$watch(
          () => {
            return this.purchaseOrderRemark || this.storeList;
          },
          () => {
            this.isModified = true;
            unwatchDataList(); // 只需监听一次，标记已修改后移除监听
          },
          { deep: true },
        );

        this.pageReady = true;
      });
    },
    _initIsAddSameGoods() {
      const localConfig = localStorage.getItem(
        'purchaseOrder_isAllowAddSameGoods',
      );
      this.isAllowAddSameGoods =
        localConfig === null ? true : +localConfig === 1;
    },
    // 是否开启【重复商品自动拆单】配置，该配置保存在本地
    _changeIsAllowAddSameGoods() {
      localStorage.setItem(
        'purchaseOrder_isAllowAddSameGoods',
        +this.isAllowAddSameGoods,
      );
    },
    _changePurchase(value) {
      if (value && value.length) {
        const [purchaseType, purchaseValue] = value;
        this.formValidate.purchase_type = purchaseType;
        if (+purchaseType === 1) {
          this.formValidate.agent_id = purchaseValue;
          this.formValidate.provider_id = '';
          // 重置price_type
          this.dataList.forEach((item, index) => {
            this.$set(this.dataList, index, {
              ...item,
              price_type: 0,
            });
          });
        } else {
          this.formValidate.provider_id = purchaseValue;
          this.formValidate.agent_id = '';
          this.batchUpdatePurchasePrice();
        }
        this.batchUpdateLastCommodityInfo();
      } else {
        this.formValidate.purchase_type = '';
        this.formValidate.agent_id = '';
        this.formValidate.provider_id = '';
      }
      if (this.isEnablePurchaseTask && !this.formValidate.provider_supervisor) {
        this.changePurchase = true;
        this.$nextTick(() => {
          const supervisorSelect = document.querySelector(
            '.ivu-select.supervisor ',
          );
          supervisorSelect &&
            supervisorSelect.querySelector('input.ivu-select-input').focus();
          this.changePurchase = false;
        });
      }
    },
    /**
     * @description: 获取采购单详情
     * @param {String} id
     * @param {Boolean} isCopyInStorageData 是否复制入库数据
     */
    _getPurchaseOrderDetail(id, isCopyInStorageData) {
      this.tableLoading = true;
      purchase
        .getPurchaseOrderDetail({ id, is_copy: 1 })
        .then(({ status, data }) => {
          this.tableLoading = false;
          if (status && data && data.order) {
            const { order } = data;
            this.formValidate.purchase_type = String(order.purchase_type);
            if (+order.purchase_type === 1) {
              this.formValidate.agent_id = String(order.agent_id);
            } else {
              this.formValidate.provider_id = String(order.provider_id);
            }
            this.formValidate.plan_date = order.plan_date;
            this.formValidate.valuation_date = order.valuation_date;
            this.formValidate.purchase_time = order.purchase_time;
            this.formValidate.storeId = order.storage_id;
            this.formValidate.sort_type = +order.sort_type;
            this.formValidate.provider_supervisor =
              +order.provider_supervisor > 0
                ? String(order.provider_supervisor)
                : '';
            this.formValidate.update_in_price = +order.update_in_price;
            this.formValidate.tag_id = order.tag_id;

            this.dataList = order.commoditys.map((item) => {
              item['logo'] = item.logo + '!40x40';
              item['price'] = item.price || 0;
              if (isCopyInStorageData) {
                console.log('isCopyInStorageData-start', item);
                item['purchase_price'] = parseFloat(item.in_store_price) || 0; // 采购价
                item['total_price'] = item.in_total_price; // 采购金额
                item['purchase_num'] = item.in_store_num; // 待采购量
                console.log('isCopyInStorageData-end', item);
              } else {
                item['purchase_price'] = parseFloat(item.purchase_price) || 0;
                item['total_price'] = item.purchase_total_price;
              }
              return item;
            });
            this.storeList = cloneDeep(this.dataList);

            this.purchaseOrderRemark = order.remark;

            this._initPageReady();
          }
        });
    },
    /**
     * @description: 获取并设置商品相关数据
     * @param {String} cid 选择的商品id
     * @param {Object} row 当前行数据
     * @param {Number} index 当前行下标
     */
    async _setCommodity(cid, row, index) {
      if (cid === row.commodity_id) return; // 商品没有变更，return
      if (!this.isAllowAddSameGoods) {
        const findIndex = this.dataList.findIndex(
          (item) => item.commodity_id === cid,
        );
        if (~findIndex) {
          this.$refs.editableTable.clearRowData(index);
          this._syncStoreList();
          this._focusGoodsNumInput(findIndex);
          this.errorMessage('您已录入过该商品，请确认');
          return;
        }
      }
      // else if(row.central_purchase_flag==1 && this.is_default == 2){
      //      const findIndex = this.dataList.findIndex(item => item.commodity_id === cid)
      //   if (~findIndex) {
      //     this.$refs.editableTable.clearRowData(index)
      //     this._syncStoreList()
      //     this._focusGoodsNumInput(findIndex)
      //     this.errorMessage('您已录入过该商品，请确认')
      //     return
      //   }
      // }
      const { status, data } = await purchase.getCommodityInfo({
        provider_id: this.formValidate.provider_id,
        agent_id: this.formValidate.agent_id,
        commodity_id: cid,
        store_id: this.formValidate.storeId,
        plan_date: this.formValidate.plan_date,
        valuation_date: this.formValidate.valuation_date,
      });
      if (status && data) {
        const { commodity } = data;

        // 判断选中采购员/供应商与商品默认的是否相同，相同的也可以直接添加
        const { purchase_type, provider_id, agent_id } = commodity;
        // 是否满足商品的默认条件(采购员/供应商相同)
        const matchDefaultRules =
          +this.formValidate.purchase_type === +purchase_type &&
          (String(agent_id) === String(this.formValidate.agent_id) ||
            String(provider_id) === String(this.formValidate.provider_id));
        if (+commodity['allow_change_channel'] === 1 || matchDefaultRules) {
          this._setCommodityInfo(commodity, row, index);
        } else {
          this.$Modal.confirm({
            title: '提示',
            content: '该商品不能临时指定采购员/供应商，确定要继续添加吗',
            onOk: () => {
              this._setCommodityInfo(commodity, row, index);
            },
          });
        }
      }
    },
    _setCommodityInfo(commodity, row, index) {
      commodity['purchase_price'] =
        Number(commodity.purchase_price) || Number(commodity.in_price);
      if (
        commodity.purchase_agreement_price &&
        Number(commodity.purchase_agreement_price) > 0
      ) {
        commodity['purchase_price'] = commodity.purchase_agreement_price;
      }

      commodity['purchase_num'] = Number(row['purchase_num']);
      commodity['total_price'] = +Number(commodity['purchase_price'])
        .mul(row['purchase_num'])
        .toFixed(2);
      // 备注保留，不更新
      commodity['remark'] = row.remark || '';

      // 数据初始化时 计算 税额 以及小计
      if (commodity['input_tax_rate'] !== '-') {
        let input_tax_rate = Number(commodity['input_tax_rate'])
          .div(100)
          .add(1);
        commodity['input_tax'] = commodity['total_price']
          .div(input_tax_rate)
          .mul(commodity['input_tax_rate'])
          .div(100)
          .toFixed(4);
        commodity['tax_exclusive'] = commodity['total_price']
          .sub(commodity['input_tax'])
          .toFixed(4);
      } else {
        commodity.input_tax = '-';
        commodity['tax_exclusive'] = commodity['total_price'];
      }

      commodity['logo'] = commodity['logo'] + '!40x40';
      commodity[this.rowKey] = row[this.rowKey]; // 商品可重复，取组件内生成的唯一key
      commodity._sortNum = this.storeList[index]._sortNum;
      this.dataList[index] = this.storeList[index] = commodity;
      this._syncStoreList();
      this._focusGoodsNumInput(index);
      this.handleCheckReference({ row: commodity });
    },
    rowClassName(row) {
      return row._show === false ? 'dn' : '';
    },
    /**
     * @description: 前端本地按商品名搜索
     */
    _onSearchCommodity() {
      this.dataList = [].concat(this.storeList);
      const matchValue = String(this.searchValue);
      const dataListCopy = cloneDeep(this.dataList);
      dataListCopy.forEach((item) => {
        item._show =
          !matchValue ||
          item.name.includes(matchValue) ||
          item.commodity_code.includes(matchValue);
      });
      this.dataList = cloneDeep(dataListCopy);
    },
    _insertAt(index) {
      if (index === this.dataList.length - 1) {
        // 只在最后一行添加
        setTimeout(() => {
          this.$refs.editableTable.insertAt(index);
        });
      }
    },
    onInsert(insertedRow, index) {
      insertedRow.id = uniqueId('$unique-') + index;
      this.dataList[this.dataList.length - 1]._sortNum = index + 1;
      this._syncStoreList();
      if (!this.changePurchase) this._focusGoodsNameInput(index + 1);
      if (index === -1 && !this.copyId) {
        // 新增的情况
        this._initPageReady();
      }
    },
    onDelete() {
      this._syncStoreList();
    },
    // 聚焦至商品名称输入框
    _focusGoodsNameInput(index) {
      this.$nextTick(() => {
        const commodityInputTd =
          document.querySelectorAll('td.commodity-name')[index];
        commodityInputTd &&
          commodityInputTd.querySelector('input,textarea').focus();
      });
    },
    // 聚焦至目标商品的待采购量输入框
    _focusGoodsNumInput(index) {
      this.$nextTick(() => {
        const targetRowId = index;
        const inputObj = document.querySelector(
          `#purchase_num-${targetRowId} input`,
        );
        if (inputObj) {
          inputObj.focus();
          inputObj.select();
          inputObj.scrollIntoView({ block: 'center' });
        }
      });
    },
    // 税额 小计联动
    // 税额 小计联动
    changes(row, index) {
      const val = row.total_price;
      if (row.input_tax_rate !== '-' && row.input_tax_rate !== undefined) {
        let input_tax_rate = Number(row.input_tax_rate).div(100).add(1);
        row.input_tax = Number(val)
          .div(input_tax_rate)
          .mul(row.input_tax_rate)
          .div(100)
          .toFixed(4);
        row.tax_exclusive = Number(val).sub(row.input_tax).toFixed(2);
      } else {
        row.tax_exclusive = val;
      }
      this.$set(this.storeList, index, row);
      // this._syncStoreList();
    },
    /**
     * @description: 同步 storeList 和 dataList 数据
     */
    _syncStoreList() {
      const dataListCopy = cloneDeep(this.dataList);
      dataListCopy.forEach((goods) => {
        const storeGoods = this.storeList.find(
          (storeGoods) => storeGoods[this.rowKey] === goods[this.rowKey],
        );
        if (storeGoods) {
          // 显示状态以dataList为准
          storeGoods._show = goods._show;
          Object.assign(goods, storeGoods);
        }
      });
      this.dataList = dataListCopy;
      this.storeList = cloneDeep(dataListCopy);
    },
    _checkValid(postList) {
      let valid = true;
      this.$refs.formValidate.validate((formValid) => {
        valid = formValid;
      });
      if (valid === false) {
        return valid;
      }
      let firstErrorGoodsIndex = -1;

      if (postList.length === 0) {
        valid = false;
        this.errorMessage('请添加商品和数量');
        return valid;
      }

      // 检查采购单商品待采购量
      postList.forEach((goods, index) => {
        if (Number(goods.purchase_num) === 0) {
          valid = false;
          this.$set(goods, 'purchase_num__error', true); // 输入框变红
          if (firstErrorGoodsIndex < 0) {
            firstErrorGoodsIndex = index;
            this.errorMessage(
              `商品[${goods.name}]，单位[${goods.unit}]的采购数量为0，请修改后重试`,
            );
          }
        } else {
          goods.purchase_num__error = false;
        }
      });
      return valid;
    },
    _scrollToFirstError() {
      this.$nextTick(() => {
        const $firstRow = this.$refs.formValidate.$el.querySelector(
          '.ivu-form-item-error, .error',
        );
        if ($firstRow) {
          $firstRow.scrollIntoView({ block: 'center' });
        }
      });
    },
    getSubmitParams() {
      const {
        purchase_type,
        agent_id,
        provider_id,
        plan_date,
        storeId: store_id,
        sort_type,
        update_in_price,
        provider_supervisor,
        tag_id,
      } = this.formValidate;

      const params = {
        agent_id,
        provider_id,
        plan_date,
        store_id,
        sort_type,
        purchase_type,
        update_in_price,
        provider_supervisor,
        tag_id,
        remark: this.purchaseOrderRemark,
        type: 2,
        attachment_link: this.attachmentFiles.map((item) => item.url).join(','),
        valuation_date: this.formValidate.valuation_date,
      };
      return params;
    },
    $_onSave(continute) {
      this._syncStoreList();
      const postList = this.storeList.filter((goods) => goods.commodity_id);
      if (!this._checkValid(postList)) {
        this._scrollToFirstError();
        return;
      }
      const params = this.getSubmitParams();
      params.commoditys = JSON.stringify(
        postList.map((goods, index) => ({
          commodity_id: parseInt(goods.commodity_id),
          num: Number(goods.purchase_num).toFixed(2) || 0,
          price: goods.purchase_price || 0,
          sub_price: goods.total_price,
          remark: goods.remark,
          calculate_way: goods.calculate_way,
          sort_num: index + 1
        })),
      );

      this.saveLoading = true;
      purchase.addPurchaseOrder(params).then(({ status, message }) => {
        this.saveLoading = false;
        if (status) {
          this.successMessage('保存成功');
          this.isModified = false;
          if (continute == 1) {
            // 保存并继续新增
            this.resetForm();
          } else {
            this.$router.push({ path: 'purchaseOrderList' });
          }
        } else {
          this.errorNotice({ desc: message });
        }
      });
    },
    getOrderNewData(newData) {
      const order = newData;
      this.formValidate.purchase_type = String(order.purchase_type);
      if (+order.purchase_type === 1) {
        this.formValidate.agent_id = String(order.agent_id);
      } else {
        this.formValidate.provider_id = String(order.provider_id);
      }
      this.formValidate.plan_date = order.plan_date;
      this.formValidate.valuation_date = order.valuation_date;

      this.formValidate.storeId = order.store_id;
      this.formValidate.sort_type = +order.sort_type;
      this.formValidate.provider_supervisor =
        +order.provider_supervisor > 0 ? String(order.provider_supervisor) : '';
      this.formValidate.update_in_price = +order.update_in_price;
      this.formValidate.tag_id = order.tag_id;

      this.dataList = order.commoditys;
      this.storeList = cloneDeep(this.dataList);

      this.purchaseOrderRemark = order.remark;

      this._initPageReady();
    },
    setStoreData() {
      if (this.isModified) {
        const newData = this.getSubmitParams();
        const postList = this.storeList.filter((goods) => goods.commodity_id);
        newData.commoditys = postList.length ? postList : [{}];
        StorageUtil.setLocalStorage('PURCHASE_NEW_DATA', newData);
      }
    },
    _onDraggableData(data) {
      this.dataList = data;
      this.postList = this.deepClone(this.dataList);
    },
  },
  beforeRouteEnter(to, from, next) {
    next();
  },
  beforeRouteLeave(to, from, next) {
    if (this.isModified) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function () {
          this.pageIntroLoaded = true;
          next();
        },
        onCancel: function () {
          next(false);
        }
      });
    } else {
      // 页面进入后就跳转，但有keepAlive缓存时，接口请求未取消，还会继续显示intro引导，暂用变量控制不显示引导，后续应加入路由跳转时取消上一个页面的请求
      this.pageIntroLoaded = true;
      next();
    }
  },
  beforeDestroy() {
    this.dataList = [].concat(this.storeList);
    this.$Modal.remove();
  },
};
</script>

<style lang="less" scoped>
/deep/ .column__required::before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 13px;
  color: #f55654;
  vertical-align: middle;
}
.purchase-order-add {
  position: relative;
}
/deep/ .drag-row {
  left: 0!important;
}
</style>
