<!--
 * @Author: <PERSON>
 * @Date: 2022-02-22 11:15:32
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-09-13 15:51:20
 * @Description: 采购单详情（新版）
-->
<template>
  <div class="purchase-order-detail">
    <DetailPage pageType="view" title="查看采购单" :disabledSave="saveLoading">
      <Form
        ref="formValidate"
        inline
        label-colon
        :label-width="120"
        :disabled="false"
      >
        <base-block title="基础信息" class="base-info__detail">
          <FormItem label="采购"
            >{{ orderDetail.purchase_type_name }} /
            {{
              +orderDetail.purchase_type === 1
                ? orderDetail.agent_name
                : orderDetail.provider_name
            }}</FormItem
          >
          <FormItem label="负责人" v-if="isEnablePurchaseTask">{{
            orderDetail.provider_supervisor_name
          }}</FormItem>
          <FormItem label="计划交货日期">{{ orderDetail.plan_date }}</FormItem>
          <FormItem label="计价日期" v-if="modeValuationDate">{{
            orderDetail.valuation_date
          }}</FormItem>
          <FormItem label="仓库">{{ orderDetail.storage_name }}</FormItem>
          <FormItem label="列表排序方式">{{
            getSortType(orderDetail.sort_type)
          }}</FormItem>
          <FormItem label="最近一次进货价">{{
            Number(orderDetail.update_in_price) === 1 ? '更新' : '不更新'
          }}</FormItem>
          <FormItem label="创建时间">{{ orderDetail.purchase_time }}</FormItem>
          <FormItem label="单据来源">{{ orderDetail.pur_plan_no }}</FormItem>
          <FormItem label="单据状态">
            <span
              :class="{
                'status-info':
                  orderDetail.purchase_status === '待审核' ||
                  orderDetail.purchase_status === '待采购' ||
                  orderDetail.purchase_status === '部分收货',
                'status-success': orderDetail.purchase_status === '全部收货',
                'status-error': orderDetail.purchase_status === '关闭',
              }"
              >{{ orderDetail.purchase_status }}</span
            >
          </FormItem>
          <FormItem label="单号">{{ orderDetail.pur_no }}</FormItem>
          <FormItem label="制单人">{{ orderDetail.author }}</FormItem>
          <FormItem label="收货种数"
            >{{ orderDetail.procured_num }} /
            {{ orderDetail.purchase_num }}</FormItem
          >
          <FormItem
            v-if="isEnablePurchaseTask"
            label="删除商品后重置采购任务采购状态"
          >
            <div style="width: 300px" class="price-type-tooltip">
              <Switch v-model="isResetPurchaseStatus" :disabled="true" />
              <Tooltip max-width="500" placement="right">
                <div slot="content">
                  删除商品后，将采购任务分配状态重置为【采购中】之前的状态（系统分配、已分配）
                </div>
                <SelfIcon
                  style="
                    cursor: pointer;
                    color: rgba(0, 0, 0, 0.6);
                    font-size: 12px;
                  "
                  icon="help"
                ></SelfIcon>
              </Tooltip>
            </div>
          </FormItem>
          <FormItem v-if="isPurchaseUseOrderCommodityTag" label="订单商品标签">
            {{ orderDetail.tag_name }}
          </FormItem>
        </base-block>
        <!-- <base-block title="收货信息" v-if="formValidate.agent_id !== '' || formValidate.provider_id !== ''"> -->
        <base-block title="商品清单">
          <EditableTable
            ref="editableTable"
            :has-customize-field="true"
            rowKey="id"
            :storePath="tableStorePath"
            :data="filterDataList"
            :columns="columns"
            :loading="tableLoading"
            :isShowRecordEditor="false"
            :virtualScroll="true"
            :virtualScrollBuff="{ top: 200, bottom: 200 }"
            :max-height="getEditTableHeight()"
            :stickyTop="100"
            @on-cols-change="handleColsChange"
            @on-row-click-only="handleRowClick"
          >
            <template #before-table>
              <InputSearch
                v-model="searchValue"
                placeholder="请输入商品名称/商品编码"
                style="margin-left: 0"
              ></InputSearch>
              <div style="margin-left: auto">
                <span
                  style="vertical-align: middle; color: rgba(0, 0, 0, 0.85)"
                >
                  参考信息
                  <Tooltip placement="top" content="鼠标选中商品后展示">
                    <s-icon
                      style="
                        cursor: pointer;
                        color: rgba(0, 0, 0, 0.8);
                        font-size: 13px;
                        margin-top: -2px;
                      "
                      icon="tips"
                    ></s-icon>
                  </Tooltip>
                </span>
                <Switch
                  v-model="showReference"
                  class="switch-new"
                  @on-change="handleChangeReferStatus"
                ></Switch>
              </div>
            </template>
            <template #after-table-left>
              <!-- 不显示左侧信息 -->
              <div></div>
            </template>
            <template #after-table-right>
              <div v-show="isShowTotal">
                <span>合计：</span>
                <span v-if="showCols.purchase_num"
                  >计划采购量：{{ calTotalPurchaseNum }}</span
                >
                <span v-if="showCols.purchase_total_price" class="ml6"
                  >计划采购金额：{{ calTotalPrice }}</span
                >
                <span v-if="showCols.num" class="ml6"
                  >已收货量：{{ numTotal }}</span
                >
                <span v-if="showCols.total_price" class="ml6"
                  >已收货金额：{{ priceTotal }}</span
                >
                <span v-if="actualPrice > 0" class="ml6"
                  >实际金额：{{ actualPrice }}</span
                >
              </div>
            </template>
          </EditableTable>
        </base-block>
        <base-block title="其他信息" class="base-info__detail">
          <FormItem label="备注" :label-width="46" style="width: 100%">{{
            orderDetail.remark
          }}</FormItem>
          <FormItem label="附件" :label-width="46" style="width: 100%">
            <AttachmentUpload
              v-model="attachmentFiles"
              :add="false"
              :remove="false"
            />
          </FormItem>
        </base-block>
        <base-block title="费用分摊" v-if="isEnablePurchaseCost">
          <Table :columns="costsColumns" :data="costs" outer-border></Table>
        </base-block>
        <WipeOut
          isDetail
          :beforeReductionPrice="priceTotal"
          :list="reductionList"
        />
        <base-block title="收货历史">
          <Table :columns="columns2" :data="historyList" outer-border></Table>
        </base-block>
        <base-block title="单据操作历史" class="base-info__detail">
          <div style="overflow-x: auto">
            <table class="log-table">
              <colgroup>
                <col width="50" />
                <col width="90" />
                <col width="110" />
                <col width="160" />
                <col width="160" />
                <col width="160" />
                <col width="130" />
                <col width="130" />
              </colgroup>
              <thead>
                <th></th>
                <th>操作员</th>
                <th>操作类型</th>
                <th>变更内容</th>
                <th>变更前</th>
                <th>变更后</th>
                <th>操作IP</th>
                <th>时间</th>
              </thead>
              <tbody class="history_body" style="height: 100%">
                <tr v-show="showGetLogBtn">
                  <td class="get-log-btn-box" colspan="10">
                    <div class="get-log-btn ivu-btn" @click="getLogList()">
                      {{ loadingLog ? '加载中...' : '点击查看单据操作历史' }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colspan="10"
                    v-show="logList == '' && !showGetLogBtn"
                    style="color: #d0d0d0; text-align: center"
                  >
                    <SIcon class="sdp-table__empty__icon" icon="zanwushuju" />
                    <p class="sdp-table__empty__text">暂无数据</p>
                  </td>
                </tr>
                <tr v-for="(log, index) in logList" :key="index">
                  <td style="text-align: center">
                    <a
                      class="iconA"
                      @click="more(log.id)"
                      v-if="log.content_count > 3 && log.rowStatus == 0"
                    >
                      <Icon
                        style="color: #909090; font-size: 16px !important"
                        type="ios-arrow-forward"
                        size="15"
                    /></a>
                    <a
                      class="iconA"
                      @click="more(log.id)"
                      v-else-if="log.content_count > 3 && log.rowStatus == 1"
                    >
                      <Icon
                        type="ios-arrow-down"
                        size="15"
                        style="color: #909090; font-size: 16px !important"
                      />
                    </a>
                  </td>
                  <td>{{ log.operator_name }}</td>
                  <td>{{ log.opr_type_detail }}</td>
                  <td colspan="3" class="sp">
                    <div class="row" v-show="log.content == ''">
                      <div class="rightBorder w"></div>
                      <div class="rightBorder w"></div>
                      <div class="w"></div>
                    </div>
                    <div
                      class="row"
                      v-for="(content, index) in log.content"
                      :key="index"
                    >
                      <div class="rightBorder w">
                        {{ content.content || '-' }}
                      </div>
                      <div class="rightBorder w">
                        {{ content.before_update || '-' }}
                      </div>
                      <div class="w">{{ content.after_update || '-' }}</div>
                    </div>
                  </td>
                  <td>{{ log.ip }}</td>
                  <td>{{ log.opr_time }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </base-block>
      </Form>
      <template #button-after>
        <div :data-purchase_id="orderId" style="display: inherit">
          <Button v-show="showEditBtn" @click="edit">编辑</Button>
          <Button v-show="showReceiptBtn" @click="receipt">收货</Button>
          <Button @click="_printOrder(orderDetail)">打印</Button>
          <Button @click="excelExport">导出</Button>
          <Button @click="getQRCode">分享采购单</Button>
          <Button
            @click="audit"
            type="primary"
            v-if="
              authority.hasAuthority('A003002001005') &&
              orderDetail.is_can_audit == 1
            "
            >审核</Button
          >
        </div>
      </template>
    </DetailPage>
    <QrCodeModal
      ref="qrCodeModal"
      :isEnableMultiStore="isEnableMultiStore"
    ></QrCodeModal>
    <s-drawer ref="drawer" title="订单明细">
      <div class="drawer-box">
        <div class="drawer-line">
          <div>商品名称：{{ drawerActiveObj.name }}</div>
          <div>备注：{{ drawerActiveObj.remark }}</div>
        </div>
        <div class="drawer-line">
          <div>待采购量：{{ drawerActiveObj.purchase_num }}</div>
          <div>已收数：{{ drawerActiveObj.num }}</div>
          <div>未收数：{{ drawerActiveObj.unreceive }}</div>
        </div>
        <div class="drawer-table">
          <STable
            :height="getTableHeight() - 100"
            :columns="drawerColumns"
            :data-provider="apiUrl.commodityOrderList"
            :filters="drawerFilters"
          ></STable>
        </div>
      </div>
    </s-drawer>
    <SModal
      title="分拣记录（单位：斤）"
      ref="sModal"
      :width="450"
      @ok="modalClose"
      okTxt="知道了"
      :btns="1"
      @on-cancel="modalClose"
    >
      <Table
        :columns="modal.columns"
        :data="modal.data"
        outer-border
        style="margin: 20px 0"
      ></Table>
    </SModal>
    <ReferenceInfo
      ref="reference"
      :show="showReference"
      :noOrder="!isPurchasePlanOri"
    ></ReferenceInfo>
  </div>
</template>

<script>
import DetailPage from '@/components/detail-page/index.js';
import EditableTable from '@/components/editable-table/index.js';
import Table from '@/components/table';
import SelfIcon from '@/components/icon';
import InputSearch from './components/input-search.vue';
import common from '@api/order.js';
import purchase from '@/api/purchase';
import ConfigMixin from '@/mixins/config.js';
import PrintMixin from '../mixins/print';
import CommonMixin from './mixin';
import SIcon from '@components/icon';
import TooltipECharts from '../tooltip-e-charts'; // hover 图表
import STable from '@components/s-table';
import WipeOut from './components/wipeOut';
import Progress from '@components/progress';
import { SModal } from '@sdp/ui';
import authority from '@util/authority.js';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import ReferenceInfo from './components/reference-info.vue';
import QrCodeModal from './components/qrCodeModal.vue';
import { getEditTableHeight } from '@/util/common';
import { get } from '@/api/request';

export default {
  name: 'PurchaseOrderDetail',
  mixins: [PrintMixin, ConfigMixin, CommonMixin],
  components: {
    DetailPage,
    EditableTable,
    Table,
    STable,
    SelfIcon,
    InputSearch,
    WipeOut,
    Progress,
    SModal,
    AttachmentUpload,
    SIcon,
    QrCodeModal,
    ReferenceInfo,
  },
  data() {
    return {
      return_order_should_money: 0,
      actualPrice: 0,
      loadingLog: false,
      orderRecord: [],
      logList: [],
      authority,
      saveLoading: false,
      isResetPurchaseStatus: false,
      tableLoading: false,
      searchValue: '',
      originCols: [],
      selectLoading: false,
      showSelectPanel: false,
      input_tax_rate: 0,
      orderRemark: '',
      totalNum: 0,
      refundId: '',
      taxExclusive: 0,
      remoteList: '',
      orderId: this.$route.query.id,
      Refunds: true,
      RefundOnly: false,
      costs: [], // 费用分摊记录
      file: { name: 'ddd' },
      costsColumns: [
        {
          title: '分摊项目',
          render: (h, params) => {
            let { row } = params;
            return h('span', this.feeMap[row.item] || '--');
          },
        },
        {
          title: '分摊类型',
          render: (h, params) => {
            let { row } = params;
            return h('span', this.util.getCostsTypeDesc(row.type));
          },
        },
        {
          title: '分摊金额',
          key: 'price',
        },
        {
          title: '操作人',
          key: 'create_user',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      tableStorePath: '',
      columns: [],
      originalColumns: [
        {
          type: 'titleCfg',
          titleType: 'all_part_purchase_receipt',
          width: 52,
          align: 'center',
          fixed: 'left',
        },
        {
          key: 'index',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px 0 0',
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          width: 80,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            var data = params.row;
            return h('img', {
              attrs: {
                src: data.logo + '!40x40',
              },
              style: {
                width: '40px',
                height: '40px',
              },
            });
          },
        },
        {
          title: '商品',
          minWidth: 140,
          key: 'name',
          fixed: 'left',
          resizable: true,
          render: (h, params) => {
            let data = params.row;
            if (
              this.orderDetail.pur_plan_no &&
              +data.is_show_order_item === 1
            ) {
              return h('div', [
                h(SelfIcon, {
                  class: {
                    mr5: true,
                    dn: params.row.central_purchase_flag == 1 ? false : true,
                  },
                  props: {
                    icon: 'central',
                    size: 16,
                  },
                }),
                h(
                  'span',
                  {
                    style: {
                      color: 'var(--primary-color)',
                      cursor: 'pointer',
                      verticalAlign: 'middle',
                    },
                    on: {
                      click: ($event) => {
                        this.getDrawerData(data);
                        $event.stopPropagation();
                      },
                    },
                  },
                  data.name,
                ),
              ]);
            } else {
              return h('span', {}, data.name);
            }
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 105,
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 140,
        },
        {
          title: '单位',
          key: 'unit',
          minWidth: 60,
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '商品分类',
          key: 'category_name',
          minWidth: 100,
        },
        {
          title: '状态',
          key: 'status',
          minWidth: 90,
          render: (h, params) => {
            let color = 'blue';
            switch (params.row.status) {
              case '全部收货':
                color = 'status-success';
                break;
              case '待采购':
              case '部分收货':
                color = 'status-info';
                break;
              case '关闭':
                color = 'status-error';
                break;
            }
            return h('div', [
              h(
                'span',
                {
                  attrs: {
                    class: color,
                  },
                },
                params.row.status,
              ),
            ]);
          },
        },
        {
          title: '已收货均价',
          key: 'price',
          align: 'right',
          setTip: '已收货金额 / 已收货数量',
          tip: '已收货金额 / 已收货数量',
          minWidth: 110,
        },
        {
          title: '税率',
          key: 'input_tax_rate',
          align: 'right',
          minWidth: 80,
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.input_tax_rate == '-' ? '--' : row.input_tax_rate + '%',
            );
          },
        },
        {
          title: '税额',
          key: 'input_tax',
          align: 'right',
          minWidth: 80,
          render: (h, params) => {
            let { row } = params;
            return h('span', row.input_tax == '-' ? '--' : row.input_tax);
          },
        },
        {
          title: '已收货金额（不含税）',
          key: 'tax_exclusive',
          minWidth: 140,
          render: (h, params) => {
            let { row } = params;
            let tax_exclusive = Number(row.tax_exclusive).toFixed(4);
            return h('span', tax_exclusive ? tax_exclusive : '--');
          },
        },
        {
          title: '已收数',
          key: 'num',
          minWidth: 100,
          render: (h, params) => {
            let tag = 'Tag';

            const isReturnGoods = (goods) => {
              return goods.return_num && Number(goods.return_num) > 0;
            };

            if (!isReturnGoods(params.row)) {
              tag = '';
            }
            return h('div', [
              h('span', {}, params.row.num),
              h(
                tag,
                {
                  props: {
                    color: 'red',
                  },
                },
                '退',
              ),
            ]);
          },
        },
        {
          title: '未收数',
          key: 'unreceive',
          align: 'right',
          minWidth: 80,
        },
        {
          title: '已收货金额',
          key: 'total_price',
          align: 'right',
          setTip: '已收货金额合计',
          tip: '已收货金额合计',
          minWidth: 110,
        },
        // 采购单详情、导出新增退货数量、金额
        {
          title: '退货数量',
          minWidth: 80,
          key: 'return_num',
          align: 'right',
        },

        {
          title: '退货金额',
          minWidth: 80,
          key: 'return_price',
          align: 'right',
        },
        {
          title: '实际金额',
          minWidth: 80,
          key: 'real_price',
          align: 'right',
        },
        {
          title: '采购入库数量',
          minWidth: 100,
          key: 'in_store_num',
          align: 'right',
        },
        {
          title: '采购入库单价',
          minWidth: 100,
          key: 'in_store_price',
          align: 'right',
          tip: '入库总金额/入库数量'
        },
        {
          title: '采购入库总价',
          minWidth: 100,
          key: 'in_total_price',
          align: 'right',
        },
        {
          title: '供应商/采购员上一次入库价',
          key: 'last_in_store_price'
        },
        {
          title: '批次',
          minWidth: 120,
          key: 'batch_no',
          poptip: true,
          // render: (h, params) => {
          //   let { row } = params
          //   let data = row.batch_no.split(',')
          //   let arr = []
          //   data.forEach(item => {
          //     arr.push(h('span', {
          //       class: 'cal'
          //     }, item))
          //   })
          //   return arr
          // }
        },
        {
          title: '库区库位',
          minWidth: 120,
          poptip: true,
          key: 'area_location',
          // render: (h, params) => {
          //   let { row } = params
          //   let data = row.area_location.split(',')
          //   let arr = []
          //   data.forEach(item => {
          //     arr.push(h('span', {
          //       class: 'cal'
          //     }, item))
          //   })
          //   return arr
          // }
        },
        {
          title: '现有库存',
          key: 'curr_stock_desc',
          minWidth: 100,
        },
        // 有两个保质期，因为有开启与不开启is_batch 配置项两种情况
        // shelf_life  保质期   开启 is_batch 配置项是这个
        // durability_period   保质期  没开 is_batch 配置项是这个
        {
          title: '保质期',
          key: 'shelf_life',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '保质期',
          key: 'durability_period',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '产地',
          key: 'product_place',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '仓内可用库存',
          minWidth: 120,
          key: 'available_stock',
          tip: '现有库存 - 分拣占用库存，值为负数时，显示为0',
          render: (h, params) => {
            const data = params.row;
            return h('span', {}, `${data.available_stock}${data.stock_unit}`);
          },
        },
        {
          title: '分拣占用库存',
          minWidth: 120,
          key: 'sorting_occupy_inventory',
          tip: '已分拣未出库的商品库存数量，不含供应商联营的部分',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.sorting_occupy_inventory}${data.stock_unit}`,
            );
          },
        },
        {
          title: '市场价',
          key: 'market_price',
          minWidth: 90,
          align: 'right',
        },
        {
          title: '条形码',
          key: 'barcode',
          minWidth: 90,
          render: (h, { row }) => {
            return <span>{row.bar_code.join(',')}</span>;
          },
        },
        {
          title: '计划采购量',
          setTip:
            '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          tip: '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          key: 'purchase_num',
          width: 120,
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
        },
        {
          title: '备注',
          key: 'remark',
          minWidth: 140,
          fixed: 'right',
          poptip: true,
        },
      ],
      cols: [],
      originalCols: [
        {
          type: 'titleCfg',
          titleType: 'pending_purchase',
          width: 52,
          align: 'center',
          fixed: 'left',
        },
        {
          key: 'index',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px 0 0',
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          width: 90,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40',
              },
            });
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 105,
        },
        {
          title: '商品分类',
          key: 'category_name',
        },
        {
          title: '商品',
          width: 200,
          key: 'name',
          fixed: 'left',
          resizable: true,
          render: (h, params) => {
            let data = params.row;
            if (
              this.orderDetail.pur_plan_no &&
              +data.is_show_order_item === 1
            ) {
              return h('div', [
                h(SelfIcon, {
                  class: {
                    mr5: true,
                    dn: params.row.central_purchase_flag == 1 ? false : true,
                  },
                  props: {
                    icon: 'central',
                    size: 16,
                  },
                }),
                h(
                  'span',
                  {
                    style: {
                      color: 'var(--primary-color)',
                      cursor: 'pointer',
                      verticalAlign: 'middle',
                    },
                    on: {
                      click: ($event) => {
                        this.getDrawerData(data);
                        $event.stopPropagation();
                      },
                    },
                  },
                  data.name,
                ),
              ]);
            } else {
              return h('span', {}, data.name);
            }
          },
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 90,
        },
        {
          title: '单位',
          key: 'unit',
          width: 70,
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '现有库存',
          key: 'curr_stock_desc',
        },
        {
          title: '近一次采购价',
          key: 'in_price',
          width: 120,
          align: 'right',
          render: (h, params) => {
            let { row: data, index } = params;
            return h(
              'div',
              {
                ref: 'div',
              },
              [
                h('span', data.in_price),
                h(
                  'Tooltip',
                  {
                    props: {
                      placement: 'top-start',
                      transfer: true,
                      theme: 'light',
                      // always: true
                    },
                    // ref: 'toolTip',
                    nativeOn: {
                      mouseenter: () => {
                        this.$refs['TooltipECharts' + index].initEchart(
                          data,
                          this.orderDetail.storage_id,
                        );
                      },
                      click: ($event) => $event.stopPropagation(),
                    },
                    scopedSlots: {
                      content: () => {
                        return this.$createElement(TooltipECharts, {
                          ref: 'TooltipECharts' + index,
                        });
                      },
                    },
                  },
                  [
                    h(SelfIcon, {
                      props: {
                        icon: 'jiagebodong_p',
                        size: '20px',
                      },
                      nativeOn: {
                        mouseenter: (e) => {
                          e.target.style.color = '#0aa251';
                        },
                        mouseleave: (e) => {
                          e.target.style.color = '#ddd';
                        },
                      },
                      style: {
                        cursor: 'pointer',
                        color: '#ccc',
                        marginLeft: '10px',
                        verticalAlign: 'top',
                      },
                    }),
                  ],
                ),
              ],
            );
          },
        },
        {
          title: '供应商/采购员上一次入库价',
          key: 'last_in_store_price'
        },
        {
          title: '采购询价',
          key: 'inquiry_price',
          width: 100,
          align: 'right',
        },
        {
          title: '市场参考价',
          key: 'cloud_reference_price',
          width: 100,
          align: 'right',
        },
        {
          title: '税率',
          key: 'input_tax_rate',
          width: 70,
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.input_tax_rate == '-' ? '--' : row.input_tax_rate + '%',
            );
          },
        },
        {
          title: '税额',
          key: 'input_tax',
          width: 70,
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            return h('span', row.input_tax == '-' ? '--' : row.input_tax);
          },
        },
        {
          title: '计划采购金额(不含税)',
          key: 'tax_exclusive',
          width: 120,
          minWidth: 120,
          align: 'right',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            let { row } = params;
            let tax_exclusive = Number(row.tax_exclusive).toFixed(4);
            return h('span', tax_exclusive ? tax_exclusive : '--');
          },
        },
        // 有两个保质期，因为有开启与不开启is_batch 配置项两种情况
        // shelf_life  保质期   开启 is_batch 配置项是这个
        // durability_period   保质期  没开 is_batch 配置项是这个
        {
          title: '保质期',
          key: 'shelf_life',
          width: 80,
          align: 'right',
        },
        {
          title: '保质期',
          key: 'durability_period',
          width: 80,
          align: 'right',
        },
        {
          title: '品牌',
          key: 'brand',
          width: 80,
          align: 'right',
        },
        {
          title: '产地',
          key: 'product_place',
          width: 80,
          align: 'right',
        },
        {
          title: '仓内可用库存',
          width: 140,
          key: 'available_stock',
          tip: '现有库存 - 分拣占用库存，值为负数时，显示为0',
          render: (h, params) => {
            const data = params.row;
            return h('span', {}, `${data.available_stock}${data.stock_unit}`);
          },
        },
        {
          title: '分拣占用库存',
          width: 140,
          key: 'sorting_occupy_inventory',
          tip: '已分拣未出库的商品库存数量，不含供应商联营的部分',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.sorting_occupy_inventory}${data.stock_unit}`,
            );
          },
        },
        {
          title: '市场价',
          key: 'market_price',
          width: 100,
          align: 'right',
        },
        {
          title: '条形码',
          key: 'barcode',
          minWidth: 90,
          render: (h, { row }) => {
            return <span>{row.bar_code.join(',')}</span>;
          },
        },
        {
          title: '计划采购量',
          setTip:
            '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          tip: '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          key: 'purchase_num',
          width: 100,
          align: 'right',
          style: {
            padding: '0 10px',
          },
        },
        {
          title: '计划采购价',
          key: 'purchase_price',
          setTip:
            '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          tip: '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          width: 100,
          align: 'right',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            let data = params.row;
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                },
              },
              [
                h('span', params.row.purchase_price),
                h(+data.price_type === 1 ? SelfIcon : '', {
                  class: 'ml5',
                  props: {
                    icon: 'xie',
                    size: 16,
                  },
                }),
              ],
            );
          },
        },
        {
          title: '计划采购金额',
          setTip: '计划采购价 * 计划采购量',
          tip: '计划采购价 * 计划采购量',
          key: 'purchase_total_price',
          width: 120,
          minWidth: 110,
          align: 'right',
        },
        {
          title: '备注',
          key: 'remark',
          width: 100,
          fixed: 'right',
          poptip: true,
        },
      ],
      columns2: [
        {
          type: 'index',
          title: '序号',
          width: 70,
          align: 'center',
        },
        {
          title: '操作人',
          key: 'user_name',
        },
        {
          title: '时间',
          key: 'create_time',
        },
        {
          title: '操作历史',
          key: 'describe',
        },
      ],
      orderList: [],
      historyList: [],
      orderDetail: '',
      drawerColumns: [
        {
          title: '订单号',
          key: 'order_no',
          width: 120,
          render: (h, params) => {
            let data = params.row;
            if (data.order_no === '合计') {
              return h('span', '合计');
            } else {
              const tooltip = [
                h(
                  'div',
                  {
                    style: {
                      display: 'flex',
                    },
                  },
                  [
                    h(
                      'Tooltip',
                      {
                        props: {
                          content: '原订单已删除该商品',
                          transfer: true,
                          placement: 'right',
                        },
                      },
                      [h('span', { class: ['tag14', 'tag14--error'] }, '删')],
                    ),
                    h('span', data.order_no),
                  ],
                ),
              ];
              const element = +data.is_deleted
                ? tooltip
                : [h('span', {}, data.order_no)];
              return h(
                'span',
                {
                  style: {
                    color: 'var(--primary-color)',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      let routeUrl = this.$router.resolve({
                        path: '/orderDetail',
                        query: { id: data.order_id, orderNo: data.order_no },
                      });
                      window.open(routeUrl.href, '_blank');
                    },
                  },
                },
                element,
              );
            }
          },
        },
        {
          title: '客户名称',
          key: 'email',
        },
        {
          title: '客户编码',
          key: 'user_code',
        },
        {
          title: '下单数量',
          key: 'amount',
        },
        {
          title: '下单单价',
          key: 'unit_price',
        },
        {
          title: '下单小计',
          key: 'total_price',
        },
        {
          title: '发货数量',
          key: 'actual_amount',
        },
        {
          title: '分配量',
          key: 'purchase_amount',
        },
        {
          title: '当前供应商分拣量',
          key: 'provider_sort_amount',
        },
        {
          title: '发货单价',
          key: 'actual_unit_price',
        },
        {
          title: '发货小计',
          key: 'sub_total_price',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      memoryDatas: [],
      drawerData: [],
      drawerFilters: {},
      drawerActiveObj: {},
      reductionList: [],
      modal: {
        columns: [
          {
            title: '分配量',
            key: 'allot',
          },
          {
            title: '分拣量',
            key: 'sorting',
          },
          {
            title: '来源',
            key: 'name',
          },
        ],
        data: [],
      },
      attachmentFiles: [],
      showGetLogBtn: true,
      showCols: {
        num: true,
        total_price: true,
        purchase_num: true,
        purchase_total_price: true,
      },
    };
  },
  created() {
    this.getOrderDetail();
    this.isResetPurchaseStatus = !!+window.localStorage.getItem(
      'purchaseEdietPageResetPurchaseStatus',
    );
    this.getFeeList();
  },
  activated() {
    this.getOrderDetail();
  },
  mounted() {},
  filters: {
    round: function (value) {
      if (!value) return '';
      var res = Math.floor(value * 100) / 100;
      return res.toString().match('[.]', 'g') ? res : res + '.00';
    },
  },
  computed: {
    // 按计价日期生效模式
    modeValuationDate() {
      return +this.sysConfig.purchase_agreement_price_mode === 2;
    },
    feeMap() {
      return this.feeList.reduce((acc, cur) => {
        acc[cur.value] = cur.label;
        return acc;
      }, {});
    },
    filterDataList() {
      return this.orderList.filter(
        (goods) =>
          !this.searchValue ||
          goods.name.includes(this.searchValue) ||
          goods.commodity_code.includes(this.searchValue),
      );
    },
    // 合计不含税
    tax_exclusive() {
      let key = 'tax_exclusive';
      let totalNum = 0;
      this.orderList.map((d) => {
        if (d[key] !== undefined && d[key] !== null) {
          totalNum += parseFloat(d[key]);
        }
      });
      return parseFloat(totalNum).toFixed(2);
    },
    calTotalPurchaseNum() {
      return this.orderList
        .reduce((prev, next) => prev.add(Number(next.purchase_num)), 0)
        .toFixed(2);
    },
    calTotalPrice() {
      let key = 'purchase_total_price';
      // if (this.orderDetail.purchase_status === '待采购') {
      //   key = 'purchase_total_price'
      // }
      let totalNum = 0;
      this.orderList.map((d) => {
        totalNum += parseFloat(d[key]);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    isShowTotal() {
      return Object.values(this.showCols).some((val) => val);
    },
    numTotal() {
      return this.orderList
        .reduce((prev, next) => prev.add(Number(next.num)), 0)
        .toFixed(2);
    },
    priceTotal() {
      let t =  this.orderList
        .reduce((prev, next) => prev.add(Number(next.total_price)), 0)
        .toFixed(2);
        this.actualPrice = (Number(t-this.return_order_should_money)).toFixed(2);
        return t;

    },
    showReceiptBtn() {
      console.log('isCanReceipt', this.orderDetail.isCanReceipt);
      return this.orderDetail.isCanReceipt;
    },
    showEditBtn() {
      console.log('isCanEdit', this.orderDetail.isCanEdit);
      return this.orderDetail.isCanEdit;
    },
  },
  methods: {
    handleRowClick(row) {
      this.handleCheckReference({ row });
    },
    async getFeeList() {
      const { status, data } = await get('/superAdmin/feeCost/list', {
        available_document: 1, // 获取采购单分摊费用列表
      });
      if (status) {
        this.feeList = data.map((item) => ({
          label: item.name,
          value: item.id,
        }));
      } else {
        this.feeList = [];
      }
    },
    getLogList() {
      var that = this,
        params = {
          module: 102,
          order_id: this.$route.query.id,
          subject: this.$route.query.pur_no,
        };
      if (this.loadingLog) return;
      this.loadingLog = true;
      common
        .orderLog(params)
        .then(function (res) {
          if (res.status) {
            that.logListData = that.deepClone(res.data);
            Array.isArray(res.data) &&
              res.data.length > 0 &&
              res.data.forEach((log) => {
                log.rowStatus = 0;
                if (log.content.length > 3) {
                  log.content = log.content.slice(0, 3);
                }
                // that.logList.push(log);
              });
            that.logList = Array.isArray(res.data) ? res.data : [];
            // if (that.orderRecord.length > 0) {
            //   that.orderRecord.forEach(item => {
            //     let data = {
            //       content: [
            //         {
            //           after_update: '',
            //           before_update: '',
            //           content: item.content
            //         }
            //       ],
            //       operator_name: item.user_name,
            //       opr_time: item.update_time,
            //       opr_type_detail: item.describe,
            //       ip: item.ip
            //     };
            //     that.logList.push(data);
            //   });
            // }
          } else {
            that.modalError(res.message);
          }
          that.showGetLogBtn = false;
        })
        .finally(() => {
          that.loadingLog = false;
        });
    },
    getEditTableHeight,
    modalClose() {
      this.$refs.sModal.close();
    },
    _download(url) {
      window.open(url);
    },
    getDrawerData(data) {
      this.drawerActiveObj = data;
      // 改变 sTable 的参数会触发表格组件自动请求接口
      this.drawerFilters = {
        purchase_id: this.$route.query.id,
        commodity_id: data.commodity_id,
        purchase_order_com_id: data.id,
      };
      this.$refs.drawer.open();
    },
    more(id) {
      var that = this,
        flag = 0,
        count = 0;
      if (that.memoryDatas.length > 0) {
        that.memoryDatas.forEach((memoryData) => {
          that.logList.forEach((log) => {
            if (memoryData.id == id && log.id == id) {
              log.content = [];
              if (memoryData.rowStatus == 0) {
                log.content = memoryData.newContent;
                memoryData.rowStatus = 1;
                log.rowStatus = 1;
              } else {
                log.content = memoryData.content;
                memoryData.rowStatus = 0;
                log.rowStatus = 0;
              }
              count++;
            }
          });
        });
        if (count == 0) flag = 1;
      } else {
        flag = 1;
      }
      if (flag == 1) {
        // common.orderLogDetail({ log_id: id }).then(function(res) {
        // if (res.status) {
        that.logList.forEach((item) => {
          that.logListData.forEach((i) => {
            if (item.id == id && i.id == item.id) {
              var data = {
                id: item.id,
                content: item.content,
                newContent: i.content,
                rowStatus: 1,
              };
              item.rowStatus = 1;
              that.memoryDatas.push(data);
              item.content = i.content;
            }
          });
        });
        // } else {
        //   that.modalError(res.message);
        // }
        // });
      }
    },
    getSortType(value) {
      if (value === undefined) return '';
      value = parseInt(value);
      const typeDic = {
        0: '添加顺序',
        1: '商品分类',
      };
      return typeDic[value] ? typeDic[value] : '未知排序方式';
    },
    audit() {
      const params = { id: this.$route.query.id };
      console.log(purchase, '---/');
      purchase.auditPurchaseOrder(params).then(({ status, message }) => {
        this.saveLoading = false;
        if (status) {
          this.successMessage('保存成功');
          this.$router.push({ path: 'purchaseOrderList' });
        } else {
          this.errorNotice({ desc: message });
        }
      });
    },
    getQRCode() {
      this.$refs.qrCodeModal.open(this.orderDetail);
    },
    excelExport() {
      this.$request
        .get(this.apiUrl.exportPurchaseOrder, { id: this.orderId })
        .then((res) => {
          let { status, data, message } = res;
          if (status) {
            location.href = data;
          } else {
            this.modalError(message ? message : '导出失败');
          }
        });
    },
    getOrderDetail() {
      let params = { id: this.$route.query.id };
      this.tableLoading = true;
      purchase
        .getPurchaseOrderDetail(params)
        .then((res) => {
          if (res.status && res.data.order) {
            res.data.order.update_in_price = Number(
              res.data.order.update_in_price,
            );
            this.taxExclusive = res.data.order.total_tax_exclusive; // 不含税合计
            if (res.data.order.purchase_status === '待采购') {
              this.columns = this.deepClone(this.originalCols);
            } else {
              this.columns = this.deepClone(this.originalColumns);
            }
            const titleCfg =
              this.columns.find((k) => k.type === 'titleCfg') || {};
            if (titleCfg.titleType) {
              // 添加个_detail, 避免其他地方有用到
              this.tableStorePath = `${titleCfg.titleType}_detail`;
            }
            this.return_order_should_money = Number(res.data.order.return_order_should_money);
            // if(this.return_order_should_money) {
              // this.actualPrice = this.priceTotal - this.return_order_should_money;
            // }
            // console.log('return_order_should_money', this.return_order_should_money, this.priceTotal, this.actualPrice);
            this.orderRecord = res.data.order.logs;
            this.orderDetail = res.data.order;
            this.reductionList = res.data.order.reduction_list;
            this.orderList = res.data.order.commoditys.map((item) => {
              item['price'] = item.price || 0;
              item['purchase_price'] = parseFloat(item.purchase_price) || 0;
              // item['total_price'] = parseFloat(item.purchase_price * item.purchase_num).toFixed(2);
              return item;
            });
            this.historyList = res.data.order.logs;
            this.attachmentFiles = res.data.order.attach_url;

            let costs = res.data.order.costs ? res.data.order.costs : [];
            this.costs = costs;
            if (
              !this.isOpenProviderSortingPurchaseTask ||
              this.orderDetail.purchase_type_name === '市场自采'
            ) {
              let providerSortAmountIndex = this.drawerColumns.findIndex(
                (item) => item.key === 'provider_sort_amount',
              );
              if (providerSortAmountIndex !== -1) {
                this.drawerColumns.splice(providerSortAmountIndex, 1);
              }
            }
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    receipt() {
      this.router.push({
        path: '/purchase/purchaseReceipt',
        query: { keep_scroll: 1, id: this.orderDetail.id },
      });
    },
    edit() {
      this.router.push({
        path: 'addPurchaseOrder',
        query: { keep_scroll: 1, id: this.orderDetail.id },
      });
    },
    handleColsChange() {
      const showCols = {};
      const tableHead = document.querySelector('.sdp-table .sdp-table__thead');
      if (!tableHead) {
        this.showCols = {};
        return;
      }
      this.$nextTick(() => {
        let cols = [];
        tableHead.querySelectorAll('th .sdp-table__cell').forEach((th) => {
          const col = this.columns.find(
            (item) => item.title && item.title === th.innerText,
          );
          if (col) {
            cols.push(col.key);
          }
        });
        Object.keys(this.showCols).map((key) => {
          showCols[key] = cols.includes(key);
        });
        this.showCols = showCols;
      });
    },
  },
  beforeRouteEnter(to, from, next) {
    next();
  },
  beforeRouteLeave(to, from, next) {
    if (this.isModified) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function () {
          next();
        },
        onCancel: function () {
          next(false);
        },
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
};
</script>

<style lang="less" scoped>
.w {
  width: 100%;
  padding: 8px;
}
.table-button {
  color: #03ac54;
  background-color: #fff !important;
  border: none;
}

.ivu-table-row-hover .table-button {
  background-color: transparent !important;
}

.fl {
  float: left;
}
.table-div {
  max-height: 322px;
  overflow-y: auto;
  overflow-x: hidden;
  background-color: #fff;
}
.log-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}
.log-table tbody {
  height: 100%;
}
.log-table th {
  background-color: #f6f8f9;
  border: 0.5px solid #e8e8e8;
  color: rgba(0, 0, 0, 0.85);
  padding: 0 15px;
  height: 44px;
  font-weight: 500;
  text-align: left;
}
.log-table td {
  padding: 15px;
  border: 0.5px solid #e8e8e8;
  text-align: left;
}
.sp {
  padding: 0 !important;
}
.sp div:last-child {
  border-bottom: none;
}
.row {
  display: flex;
  border-bottom: 1px solid #f5f5f5;
}
.rightBorder {
  border-right: 1px solid #f5f5f5;
}
/deep/ .input-search {
  width: 272px;
  margin-left: auto;
  .ivu-input {
    border-radius: 2px !important;
    &-icon-clear {
      right: 40px;
    }
  }
  .ivu-input-search {
    width: 40px;
    background-color: #fff !important;
    border: solid 1px #d8d8d8 !important;
    color: #d8d8d8 !important;
    padding: 0 !important;
    display: flex;
    justify-content: center;
    align-items: center;
    &::before {
      background-color: #d8d8d8;
    }
  }
}

/deep/ .ivu-table-row-hover {
  .cal {
    display: block;
  }
}

.topRow {
  padding: 0;
  margin-bottom: 20px;
}

.botRow {
  margin-bottom: 30px;
}

.remark {
  line-height: 20px;
  margin-top: 40px;
}
.file-attachment {
  display: flex;
  justify-items: center;
}
.file-wrap {
  display: flex;
}
.file-name {
  margin-right: 5px;
  // width:320px;
  // overflow: hidden;
  // text-overflow:ellipsis;
  // white-space: nowrap;
}
.file-icon {
  margin-right: 10px;
  cursor: pointer;
}

.ivu-tabs > .ivu-tabs-bar .ivu-tabs-nav-container {
  font-size: 16px !important;
}

.ivu-tabs > .ivu-tabs-bar {
  border-bottom-color: #e2e2e2;
  margin-bottom: 20px;
}
.drawer-box {
  width: 1200px;
  padding: 20px;
  .drawer-line {
    margin-top: 20px;
    display: flex;
    div {
      margin-right: 80px;
      width: 300px;
    }
  }
  .drawer-table {
    margin-top: 20px;
  }
}
/deep/ .sdp-table__container {
  min-height: 100px;
}
.get-log-btn-box {
  height: 180px;
  .get-log-btn {
    cursor: pointer;
    margin: 0 auto;
    width: 300px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.purchase-order-detail {
  position: relative;
}
/deep/ .sdp-detail-page__ft {
  width: 105%;
  transform: translateX(-16px);
}
/deep/.sdp-table__container {
  margin-top: 12px !important;
}
</style>
