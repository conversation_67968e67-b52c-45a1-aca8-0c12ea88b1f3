<!--
 * @Author: <PERSON>
 * @Date: 2022-02-22 11:15:32
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-09-11 15:06:46
 * @Description: 编辑采购单（新版）
-->
<template>
  <div class="purchase-order-edit">
    <DetailPage
      pageType="edit"
      title="编辑采购单"
      :disabledSave="saveLoading"
      @on-save="$_onSave"
    >
      <Form
        ref="formValidate"
        :model="formValidate"
        :rules="ruleValidate"
        inline
        label-colon
        :label-width="125"
        :disabled="false"
      >
        <base-block title="基础信息" class="base-info">
          <FormItem label="采购" prop="purchase_type">
            <PurchaseSelect
              :value="purchaseSelectValue"
              :level="
                +formValidate.purchase_type === 5 &&
                +orderDetail.edit_purchase_type === 1
                  ? [1, 2, 3, 5]
                  : [1, 2, 3]
              "
              :change-on-select="true"
              :params="{
                filter_disable_provider: 1,
                filter_disable_agent: 1,
              }"
              use-hotkey
              placeholder="请选择采购类型"
              :disabled="+orderDetail.edit_purchase_type === 1"
              :shouldInitData="pageReady"
              @on-change="_changePurchase"
            ></PurchaseSelect>
          </FormItem>
          <FormItem
            label="负责人"
            prop="provider_supervisor"
            v-if="isEnablePurchaseTask"
          >
            <PurchaseAgent
              v-model="formValidate.provider_supervisor"
              filterable
              :shouldInitData="pageReady"
              :disabled="
                !orderDetail.purchase_status === '待采购' ||
                +orderDetail.edit_agent === 1
              "
            ></PurchaseAgent>
          </FormItem>
          <FormItem label="计划交货日期" prop="plan_date">
            <DatePicker
              :value="formValidate.plan_date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              :disabled="!orderDetail.plan_date_is_edit"
              @on-change="formValidate.plan_date = $event"
            ></DatePicker>
          </FormItem>
          <FormItem
            label="计价日期"
            v-if="modeValuationDate"
            prop="valuation_date"
          >
            <DatePicker
              :value="formValidate.valuation_date"
              type="date"
              placeholder="选择日期"
              style="width: 100%"
              :disabled="!caneEditValuationDate"
              @on-change="handleValuationDateChange"
            ></DatePicker>
          </FormItem>
          <FormItem label="仓库" prop="storeId">{{
            orderDetail.storage_name
          }}</FormItem>
          <FormItem label="排序方式" prop="sort_type">
            <RadioGroup v-model="formValidate.sort_type">
              <Radio :label="0">添加顺序</Radio>
              <Radio :label="1">商品分类</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="最近一次进货价" prop="update_in_price">
            <RadioGroup
              @on-change="changeUpdateInPrice"
              v-model="formValidate.update_in_price"
            >
              <Radio :label="1">更新</Radio>
              <Radio :label="2">不更新</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="创建时间">{{ orderDetail.purchase_time }}</FormItem>
          <FormItem label="单据来源">{{ orderDetail.pur_plan_no }}</FormItem>
          <FormItem label="单据状态">
            <span
              :class="{
                'status-info':
                  orderDetail.purchase_status === '待审核' ||
                  orderDetail.purchase_status === '待采购' ||
                  orderDetail.purchase_status === '部分收货',
                'status-success': orderDetail.purchase_status === '全部收货',
                'status-error': orderDetail.purchase_status === '关闭',
              }"
              >{{ orderDetail.purchase_status }}</span
            >
          </FormItem>
          <FormItem label="单号">{{ orderDetail.pur_no }}</FormItem>
          <FormItem label="制单人">{{ orderDetail.author }}</FormItem>
          <FormItem
            v-if="isEnablePurchaseTask"
            label="删除商品后重置采购任务采购状态"
          >
            <div style="width: 300px" class="price-type-tooltip">
              <Switch
                v-model="isResetPurchaseStatus"
                :disabled="orderDetail.purchase_type == 5"
                @on-change="changeResetPurchaseStatus"
              />
              <Tooltip max-width="500" placement="right">
                <div slot="content">
                  删除商品后，将采购任务分配状态重置为【采购中】之前的状态（系统分配、已分配）
                </div>
                <Icon style="cursor: pointer" type="ios-help-circle"></Icon>
              </Tooltip>
            </div>
          </FormItem>
          <FormItem
            v-if="isPurchaseUseOrderCommodityTag"
            prop="tag_id"
            :rules="tagRules"
          >
            <div slot="label">
              订单商品标签
              <Tooltip
                max-width="210"
                placement="top"
                content="修改后，订单中的数据不会变更"
              >
                <SelfIcon style="cursor: pointer" :size="1" icon="tishifu"></SelfIcon>
              </Tooltip>
              :
            </div>
            <Select
              v-model="formValidate.tag_id"
              clearable
              transfer
              placeholder="请选择"
            >
              <Option
                v-for="tag in tagList"
                :value="tag.id"
                :key="tag.id"
                :label="tag.name"
              >
              </Option>
            </Select>
          </FormItem>
        </base-block>
        <!-- <base-block title="收货信息" v-if="formValidate.agent_id !== '' || formValidate.provider_id !== ''"> -->
        <base-block title="商品清单">
          <SFullscreen @change="updateTableHeight">
            <Row :gutter="10" type="flex" align="middle" slot="action-left">
              <Col>
                <InputSearch
                  v-model="searchValue"
                  placeholder="请输入商品名称/商品编码"
                  @on-change="_onSearchCommodity"
                ></InputSearch>
              </Col>
            </Row>
            <Row
              style="color: var(--primary-color); cursor: pointer"
              :gutter="20"
              type="flex"
              align="middle"
              slot="action-right"
            >
              <Col class="mb-4">
                <div style="margin-left: auto">
                  <span
                    style="vertical-align: middle; color: rgba(0, 0, 0, 0.85)"
                  >
                    参考信息
                    <Tooltip placement="top" content="鼠标选中商品后展示">
                      <SelfIcon
                        style="
                          cursor: pointer;
                          color: rgba(0, 0, 0, 0.8);
                          margin-top: -2px;
                          font-size: 12px;
                        "
                        icon="tips"
                      ></SelfIcon>
                    </Tooltip>
                  </span>
                  <Switch
                    v-model="showReference"
                    class="switch-new"
                    @on-change="handleChangeReferStatus"
                  ></Switch>
                </div>
              </Col>
            </Row>
            <EditableTable
              ref="editableTable"
              :rowKey="rowKey"
              :loading="tableLoading"
              :data="dataList"
              :has-customize-field="true"
              :columns="columns"
              :isShowRecordEditor="true"
              :row-class-name="rowClassName"
              :max-height="tableHeight"
              :stickyTop="101"
              enterAsDown
              @on-insert="onInsert"
              @on-delete="onDelete"
              @on-row-click-only="handleRowClick"
              @on-sort-change="handleSortChange"
              @on-draggable-data="_onDraggableData"
            >
              <template #after-table-right>
                <span>合计：</span>
                <span>计划采购量：{{ calTotalPurchaseNum }}</span>
                <span class="ml6">计划采购金额：{{ calTotalPrice }}</span>
              </template>
            </EditableTable>
            <template v-if="showReference">
              <ReferenceInfo
                ref="reference"
                :show="showReference"
                :noOrder="!isPurchasePlanOri"
              ></ReferenceInfo>
            </template>
          </SFullscreen>
        </base-block>
        <base-block title="其他信息" class="base-info">
          <FormItem
            label="备注"
            :label-width="46"
            style="width: 100%; margin-right: 0"
          >
            <Input
              v-model="purchaseOrderRemark"
              type="textarea"
              :rows="3"
              :maxlength="128"
              :autosize="{ minRows: 3, maxRows: 8 }"
              show-word-limit
              placeholder="请输入备注信息"
              style="width: 318px"
            ></Input>
          </FormItem>

          <FormItem label="附件" :label-width="46" style="width: 100%">
            <AttachmentUpload v-model="attachmentFiles" />
          </FormItem>
        </base-block>
      </Form>
      <s-drawer v-if="isDrawerVisible" ref="drawer" title="订单明细">
        <div class="drawer-box">
          <div class="drawer-line">
            <div>商品名称：{{ drawerActiveObj.name }}</div>
            <div>备注：{{ drawerActiveObj.remark }}</div>
          </div>
          <div class="drawer-line">
            <div>待采购量：{{ drawerActiveObj.purchase_num }}</div>
            <div>已收数：{{ drawerActiveObj.num }}</div>
            <div>未收数：{{ drawerActiveObj.unreceive }}</div>
          </div>
          <div class="drawer-table">
            <STable
              :height="getTableHeight() - 100"
              :columns="drawerColumns"
              :data-provider="apiUrl.commodityOrderList"
              :filters="drawerFilters"
            ></STable>
          </div>
        </div>
      </s-drawer>
      <template #button-after>
        <!-- 【味美轩】, 临时过渡方案 -->
        <Button
          type="primary"
          @click="$_onSave({ audit: 1 })"
          v-if="
            authority.hasAuthority('A003002001005') &&
            orderDetail.is_can_audit == 1
          "
          >保存并审核</Button
        >
        <Button
          type="primary"
          v-if="orderDetail.is_can_receipt == 1"
          @click="$_onSave({ type: 'receipt' })"
          >保存并收货</Button
        >
      </template>
    </DetailPage>
  </div>
</template>

<script>
import DetailPage from '@/components/detail-page/index.js';
import EditableTable from '@/components/editable-table/index.js';
import PurchaseSelect from '@/components/common/purchaseSelect.vue';
import PurchaseAgent from '@components/common/PurchaseAgent';
import CommoditySelect from '@/components/common/CommoditySelect';
import SelfIcon from '@/components/icon';
import TooltipECharts from '../tooltip-e-charts';
import InputSearch from './components/input-search.vue';
import ReferenceInfo from './components/reference-info.vue';
import purchase from '@/api/purchase';
import ConfigMixin from '@/mixins/config.js';
import CommonMixin from './mixin';
import { cloneDeep } from 'lodash-es';
import Progress from '@components/progress';
import STable from '@components/s-table';
import authority from '@util/authority.js';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import settings from '@api/settings.js';
import Goods from '@api/goods.js';
import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
import SFullscreen from '@/components/s-fullscreen';
import { getEditTableHeight } from '@/util/common';
import { Icon } from 'view-design';
import { get } from '@/api/request';

export default {
  name: 'PurchaseOrderEdit',
  mixins: [ConfigMixin, CommonMixin],
  components: {
    SVxeEditableTable,
    DetailPage,
    EditableTable,
    PurchaseSelect,
    PurchaseAgent,
    SelfIcon,
    InputSearch,
    Progress,
    STable,
    AttachmentUpload,
    ReferenceInfo,
    SFullscreen,
  },
  data() {
    return {
      deleteTipsContent: `删除商品后，将采购任务分配状态重置为【采购中】之前的状态（系统分配、已分配)`,
      isFullscreen: false,
      tableHeight: getEditTableHeight(),
      isDrawerVisible: false,
      is_default: '',
      isAllowAddSameGoods: true,
      authority,
      isResetPurchaseStatus: false,
      drawerActiveObj: {},
      drawerFilters: {},
      drawerColumns: [
        {
          title: '订单号',
          key: 'order_no',
          width: 120,
          render: (h, params) => {
            let data = params.row;
            if (data.order_no === '合计') {
              return h('span', '合计');
            } else {
              const tooltip = [
                h(
                  'div',
                  {
                    style: {
                      display: 'flex',
                    },
                  },
                  [
                    h(
                      'Tooltip',
                      {
                        props: {
                          content: '原订单已删除该商品',
                          transfer: true,
                          placement: 'right',
                        },
                      },
                      [h('span', { class: ['tag14', 'tag14--error'] }, '删')],
                    ),
                    h('span', data.order_no),
                  ],
                ),
              ];
              const element = +data.is_deleted
                ? tooltip
                : [h('span', {}, data.order_no)];
              return h(
                'span',
                {
                  style: {
                    color: 'var(--primary-color)',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      let routeUrl = this.$router.resolve({
                        path: '/orderDetail',
                        query: { id: data.order_id, orderNo: data.order_no },
                      });
                      window.open(routeUrl.href, '_blank');
                    },
                  },
                },
                element,
              );
            }
          },
        },
        {
          title: '客户名称',
          key: 'email',
        },
        {
          title: '客户编码',
          key: 'user_code',
        },
        {
          title: '下单数量',
          key: 'amount',
        },
        {
          title: '下单单价',
          key: 'unit_price',
        },
        {
          title: '下单小计',
          key: 'total_price',
        },
        {
          title: '当前供应商分拣量',
          key: 'provider_sort_amount',
        },
        {
          title: '发货数量',
          key: 'actual_amount',
        },
        {
          title: '分配量',
          key: 'purchase_amount',
        },
        {
          title: '发货单价',
          key: 'actual_unit_price',
        },
        {
          title: '发货小计',
          key: 'sub_total_price',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      level: [1, 2],
      formValidate: {
        plan_date: '',
        storeId: '',
        purchase_type: '',
        agent_id: '',
        provider_id: '',
        purchase_time: '',
        sort_type: 0,
        update_in_price: 1,
        provider_supervisor: '',
        tag_id: '',
        valuation_date: '',
      },
      ruleValidate: {
        plan_date: [
          {
            required: true,
            type: 'string',
            message: '请选择日期',
            trigger: 'change',
          },
        ],
        purchase_type: [
          {
            required: true,
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!this.formValidate.purchase_type) {
                return callback(new Error('请选择采购类型'));
              }
              if (+this.formValidate.purchase_type === 1) {
                if (!this.formValidate.agent_id) {
                  return callback(new Error('请选择采购负员'));
                }
              } else {
                if (!this.formValidate.provider_id) {
                  return callback(new Error('请选择供应商'));
                }
              }
              callback();
            },
          },
        ],
        provider_supervisor: [{ required: true, message: '请选择负责人' }],
      },
      tagRules: {
        required: false,
        message: '请选择订单商品标签',
        trigger: 'change',
      },
      orderDetail: {},
      searchValue: '', // 商品搜索
      isModified: false, // 标记页面数据是否被修改过
      rowKey: 'id', // 行唯一标识
      tableLoading: false,
      dataList: [],
      originDataListCopy: [],
      deleteList: [],
      columns: [],
      purchaseOrderRemark: '',
      saveLoading: false,
      purchaseOrderId: '',
      pageReady: false,
      pageIntroLoaded: false,
      attachmentFiles: [],
      tagList: [],
    };
  },
  computed: {
    // 按计价日期生效模式
    modeValuationDate() {
      return +this.sysConfig.purchase_agreement_price_mode === 2;
    },
    // 待采购、部分收货允许修改计价日期
    caneEditValuationDate() {
      const status = +this.orderDetail.status;
      return [
        4, // 部分收货
        2, // 待采购
        1, // 待审核
      ].includes(status);
    },
    purchaseSelectValue() {
      const { purchase_type, agent_id, provider_id } = this.formValidate;
      const purchaseValue = agent_id || provider_id;
      return purchase_type
        ? [String(purchase_type), String(purchaseValue)]
        : [];
    },
    calTotalPrice() {
      return this.originDataListCopy
        .reduce((prev, next) => prev.add(Number(next.total_price)), 0)
        .toFixed(2);
    },
    calTotalPurchaseNum() {
      return this.originDataListCopy
        .reduce((prev, next) => prev.add(Number(next.purchase_num)), 0)
        .toFixed(2);
    },
    // 联营采购
    isJoinPurchase() {
      return Number(this.orderDetail.channel_type) === 5;
    },
  },
  watch: {
    'formValidate.sort_type': {
      deep: true,
      immediate: true,
      handler() {
        this.getColumns()
      }
    }
  },
  created() {
    const { id } = this.$route.query;
    if (id) {
      this.purchaseOrderId = id;
      this._getPurchaseOrderDetail(id);
      this.getTagList();
    }
    this.isResetPurchaseStatus = !!+window.localStorage.getItem(
      'purchaseEdietPageResetPurchaseStatus',
    );
    this._initIsAddSameGoods();
    this.getCompanyInfo();
  },
  methods: {
    getColumns() {
      this.columns = [
        ...(this.formValidate.sort_type == 0 ? [{
          type: 'drag',
          width: 28,
          minWidth: 28,
          fixed: 'left',
          align: 'center',
          style: {
            paddingRight: 0,
            paddingLeft: '6px',
          },
          className: 'drag-row', // 重写样式覆盖left
          render: (h, params) => {
            return h(Icon, {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer'
              },
              props: {
                size: 13,
                icon: 'sort'
              },
            });
          },
        }]: []),
        {
          type: 'titleCfg',
          titleType: 'purchase_edit',
          minWidth: 52,
          width: 52,
          align: 'center',
          fixed: 'left',
          style: {
            paddingRight: 0,
            paddingLeft: '12px',
          },
        },
        {
          key: 'index',
          type: 'index',
          width: 50,
          align: 'center',
          fixed: 'left',
          style: {
            padding: '0 10px',
          },
        },
        {
          type: 'img',
          title: '图片',
          key: 'logo',
          width: 60,
          align: 'center',
          fixed: 'left',
          defaultValue:
            'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
          style: {
            padding: '0 10px',
          },
        },
        {
          title: '商品名称',
          key: 'name',
          minWidth: 200,
          fixed: 'left',
          className: 'commodity-name',
          render: (h, params) => {
            const { row, index } = params;
            if (row._isNew) {
              return (
                <CommoditySelect
                  commodityName={row.name}
                  dataProvider={this.apiUrl.getPurchaseCommoditys}
                  params={{
                    unfilter_online: 1,
                    plan_date: this.formValidate.plan_date,
                    provider_id: this.formValidate.provider_id,
                    valuation_date: this.formValidate.valuation_date,
                    store_id: this.orderDetail.storage_id,
                  }}
                  trimInput={true}
                  commodityIdKey="commodity_id"
                  commodityNameKey="name"
                  selectedData={this.dataList}
                  onOn-change={(cid) => this._setCommodity(cid, row, index)}
                  onOn-enter={() => this._insertAt(index)}
                  style={{ width: '200px' }}
                  inputProps={{
                    type: 'textarea',
                    rows: 1,
                    autosize: { minRows: 1, maxRows: 2.2 },
                  }}
                  slot-type="purchase"
                ></CommoditySelect>
              );
            }
            let data = params.row;
            if (
              this.orderDetail.pur_plan_no &&
              +data.is_show_order_item === 1
            ) {
              this.isDrawerVisible = true;
              return h('div', [
                h(SelfIcon, {
                  class: {
                    mr5: true,
                    dn: params.row.central_purchase_flag == 1 ? false : true,
                  },
                  props: {
                    icon: 'central',
                    size: 16,
                  },
                }),
                h(
                  'span',
                  {
                    style: {
                      color: 'var(--primary-color)',
                      cursor: 'pointer',
                      verticalAlign: 'middle',
                    },
                    on: {
                      click: ($event) => {
                        this.getDrawerData(data);
                        $event.stopPropagation();
                      },
                    },
                  },
                  data.name,
                ),
              ]);
            } else {
              return h('span', {}, data.name);
            }
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 105,
        },
        {
          title: '单位',
          minWidth: 60,
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 100,
        },
        {
          title: '商品分类',
          key: 'category_name',
          minWidth: 100,
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 120,
        },
        {
          title: '税率',
          key: 'input_tax_rate',
          minWidth: 60,
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              row.input_tax_rate == '-' ? '--' : row.input_tax_rate + '%',
            );
          },
        },
        {
          title: '税额',
          key: 'input_tax',
          minWidth: 80,
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h('span', row.input_tax == '-' ? '--' : row.input_tax);
          },
        },
        {
          title: '计划采购金额(不含税)',
          key: 'tax_exclusive',
          width: 160,
          minWidth: 160,
          align: 'right',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            const { row } = params;
            const tax_exclusive = Number(row.tax_exclusive).toFixed(4);
            return h('span', tax_exclusive ? tax_exclusive : '--');
          },
        },
        // 有两个保质期，因为有开启与不开启is_batch 配置项两种情况
        // shelf_life  保质期   开启 is_batch 配置项是这个
        // durability_period   保质期  没开 is_batch 配置项是这个
        {
          title: '保质期',
          key: 'shelf_life',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '保质期',
          key: 'durability_period',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '产地',
          key: 'product_place',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '近一次采购价',
          key: 'in_price',
          minWidth: 100,
          align: 'right',
          render: (h, params) => {
            const { row: data, index, fixed } = params;
            if (fixed) {
              return null;
            }
            return h('div', [
              h('span', data.in_price),
              data.in_price &&
                h(
                  'Tooltip',
                  {
                    props: {
                      placement: 'top-start',
                      transfer: true,
                      theme: 'light',
                    },
                    nativeOn: {
                      mouseenter: () => {
                        this.$refs['TooltipECharts' + index].initEchart(
                          data,
                          this.orderDetail.storage_id,
                        );
                      },
                      click: ($event) => $event.stopPropagation(),
                    },
                    scopedSlots: {
                      content: () => {
                        return this.$createElement(TooltipECharts, {
                          ref: 'TooltipECharts' + index,
                        });
                      },
                    },
                  },
                  [
                    h(SelfIcon, {
                      props: {
                        icon: 'jiagebodong_p',
                        size: '20px',
                      },
                      nativeOn: {
                        mouseenter: (e) => {
                          e.target.style.color = '#0aa251';
                        },
                        mouseleave: (e) => {
                          e.target.style.color = '#ddd';
                        },
                      },
                      style: {
                        cursor: 'pointer',
                        color: '#ccc',
                        marginLeft: '10px',
                        verticalAlign: 'top',
                      },
                    }),
                  ],
                ),
            ]);
          },
        },
        {
          title: '供应商/采购员上一次入库价',
          key: 'last_in_store_price'
        },
        {
          title: '采购询价',
          key: 'inquiry_price',
          minWidth: 80,
          align: 'right',
        },
        {
          title: '仓内可用库存',
          minWidth: 120,
          key: 'available_stock',
          tip: '现有库存 - 分拣占用库存，值为负数时，显示为0',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.available_stock || '-'}${data.stock_unit || '-'}`,
            );
          },
        },
        {
          title: '分拣占用库存',
          minWidth: 120,
          key: 'sorting_occupy_inventory',
          tip: '已分拣未出库的商品库存数量，不含供应商联营的部分',
          render: (h, params) => {
            const data = params.row;
            return h(
              'span',
              {},
              `${data.sorting_occupy_inventory || '-'}${data.stock_unit || '-'}`,
            );
          },
        },
        {
          title: '市场参考价',
          key: 'cloud_reference_price',
          minWidth: 100,
          align: 'right',
        },
        {
          title: '市场价',
          key: 'market_price',
          minWidth: 100,
          align: 'right',
        },
        {
          title: '现有库存',
          key: 'curr_stock_desc',
          minWidth: 100,
        },
        {
          title: '条形码',
          key: 'barcode',
          minWidth: 90,
          render: (h, { row }) => {
            const { barcode, bar_code } = row;
            return <span>{(barcode || bar_code || []).join(',')}</span>;
          },
        },
        {
          type: 'NumberInput',
          title: '计划采购量',
          key: 'purchase_num',
          width: 110,
          tip: '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求',
          require: true,
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
          defaultValue: 1,
          editRender: { autofocus: 'input', autoselect: true },
          props: ({ row, index }) => ({
            class: {
              error: row.purchase_num__error,
            },
            attrs: {
              id: 'purchase_num-' + index,
            },
            // value: Number(row.purchase_num),
            min: 0,
            max: 999999999.99,
            precision: 2,
            disabled:
              +this.orderDetail.edit_pending_purchase === 1 &&
              row.is_purchase_type_pool == 1,
            on: {
              'on-change': (val) => {
                row.calculate_way = 1;
                row.purchase_num__error = false;
                // row.purchase_num = Number(val.toFixed(2));
                row.purchase_num = val;
                row.total_price = Number(row.purchase_price)
                  .mul(+val)
                  .toFixed(2);
                this.changes(row, index);
              },
              'on-enter': () => this._insertAt(index),
            },
          }),
        },
        {
          title: '计划采购价',
          width: 140,
          setTip:
            '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          tip: '新增、编辑采购单时，可供修改的价格，用于创建采购单时提前定价',
          require: true,
          sortable: true,
          key: 'purchase_price',
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
          render: (h, params) => {
            const { row, index } = params;
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                },
              },
              [
                h('NumberInput', {
                  props: {
                    value: Number(row.purchase_price),
                    min: 0,
                    max: 999999999.99,
                    precision: 2,
                    disabled: +this.orderDetail.edit_purchase_unit_price === 1,
                  },
                  style: {
                    width: '90px',
                  },
                  on: {
                    'on-change': (val) => {
                      row.calculate_way = 1;
                      row.purchase_price = val;
                      row.total_price = Number(val)
                        .mul(row.purchase_num)
                        .toFixed(2);

                      this.changes(row, index);
                    },
                    'on-focus': (event) => {
                      event.target.select();
                    },
                    'on-enter': () => this._insertAt(index),
                  },
                }),
                h(row.price_type == 1 ? SelfIcon : '', {
                  class: 'ml5',
                  props: {
                    icon: 'xie',
                    size: 16,
                  },
                }),
              ],
            );
          },
        },
        {
          type: 'NumberInput',
          title: '计划采购金额',
          key: 'total_price',
          setTip: '计划采购价 * 计划采购量',
          tip: '计划采购价 * 计划采购量',
          minWidth: 120,
          width: 120,
          require: true,
          fixed: 'right',
          style: {
            padding: '0 0px',
          },
          props: ({ row, index }) => ({
            min: 0,
            min: 0,
            max: 999999999.99,
            precision: 2,
            on: {
              'on-change': (val) => {
                row.calculate_way = 2;
                row.total_price = val;
                if (Number(row.purchase_num) > 0) {
                  row.purchase_price = Number(val)
                    .div(row.purchase_num)
                    .toFixed(2);
                }
                this.changes(row, index);
              },
              'on-enter': () => this._insertAt(index),
            },
          }),
        },
        {
          type: 'Input',
          title: '备注',
          width: 120,
          key: 'remark',
          fixed: 'right',
          style: {
            padding: '0 10px',
          },
          props: ({ row, index }) => ({
            type: 'textarea',
            rows: 1,
            autosize: { minRows: 1, maxRows: 2.2 },
            on: {
              'on-change': (e) => {
                this.originDataListCopy[index].remark = e.target.value;
                row.remark = e.target.value;
              },
              'on-enter': () => this._insertAt(index),
              'on-keydown': (e) => {
                if (e.keyCode === 13) e.preventDefault();
              },
            },
          }),
        },
      ]
    },
    updateTableHeight(isFullscreen, screenHeight) {
      this.isFullscreen = isFullscreen;
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 94;
      }
    },
    handleValuationDateChange(date) {
      // 采购类型不为供应商、商品列表为空时，不触发
      const goodsIds = this.dataList.map((item) => item.commodity_id).join(',');
      if (!this.formValidate.provider_id || !goodsIds) {
        return (this.formValidate.valuation_date = date);
      }
      const lastDate = this.formValidate.valuation_date;
      this.$smodal({
        title: '确认',
        text: '修改计价日期后，计划采购价重新按匹配生效的采购协议单取价',
        type: 'warning',
        btns: 2,
        okTxt: '继续',
        quitTxt: '取消',
        onOk: () => {
          this.formValidate.valuation_date = date;
          // 状态为待采购、部分收货的商品，计划采购价重新按匹配生效的采购协议单取价
          this.batchUpdatePurchasePrice();
        },
        onQuit: () => {
          this.formValidate.valuation_date = lastDate;
        },
      });
      return (this.formValidate.valuation_date = date);
    },
    async batchUpdateLastCommodityInfo() {
      const goodsIds = this.dataList.filter(item => !!item.commodity_id).map((item) => item.commodity_id);
      if (!goodsIds.length) {
        return;
      }
      let { data } = await get('/superAdmin/purchaseOrder/latestCommodityInfo', {
        provider_id: this.formValidate.provider_id,
        agent_id: this.formValidate.agent_id,
        commodity_ids: goodsIds.join(','),
      });
      data = data || [];
      this.dataList.forEach((item, index) => {
        const lastCommodityInfo = data.find((d) => d.commodity_id === item.commodity_id);
        const storeItem = this.originDataListCopy[index] || {}
        if (lastCommodityInfo) {
          this.$set(this.dataList, index, {
            ...item,
            ...storeItem,
            ...lastCommodityInfo,
          });
        }
      });
    },
    // 根据接口数据批量更新计划采购价
    async batchUpdatePurchasePrice() {
      const goodsIds = this.dataList.map((item) => item.commodity_id);
      const params = {
        provider_id: this.formValidate.provider_id,
        valuation_date: this.formValidate.valuation_date,
        commodity_ids: goodsIds.join(','),
      };
      // 返回的商品都是有协议价的商品
      const { data: goodsList } = await get(
        '/superAdmin/purchaseAgreementPrice/getPriceByValuationDate',
        params,
      );
      this.dataList.forEach((item, index) => {
        const goodsItem = goodsList.find(
          (d) => d.commodity_id === item.commodity_id,
        );
        const storeItem = this.storeList[index] || {};
        if (goodsItem) {
          const { price: purchase_price } = goodsItem;
          this.$set(this.dataList, index, {
            ...item,
            ...storeItem,
            purchase_price,
            price_type: 1,
            total_price: Number(purchase_price)
              .mul(item.purchase_num)
              .toFixed(2),
          });
          this.changes(this.dataList[index], index);
        } else {
          this.$set(this.dataList, index, {
            ...item,
            ...storeItem,
            price_type: 0,
          });
        }
      });
    },
    changeUpdateInPrice(value) {
      this.formValidate.update_in_price = value;
      localStorage.setItem('purchaseOrder_updateInPrice', value);
    },
    getTagList() {
      if (!this.isPurchaseUseOrderCommodityTag) return;
      this.tagRules.required = this.isOrderCommodityTagRequired;
      Goods.getOrderGoodsTagList().then((data) => {
        this.tagList = data;
      });
    },
    getCompanyInfo() {
      settings.getCompanyInfo().then((res) => {
        if (res.status) {
          let data = res.data;
          this.is_default = data.is_default;
        }
      });
    },
    _initPageReady() {
      this.$nextTick(() => {
        let unwatchDataList = this.$watch(
          () => {
            return this.purchaseOrderRemark || this.storeList;
          },
          () => {
            this.isModified = true;
            unwatchDataList(); // 只需监听一次，标记已修改后移除监听
          },
          { deep: true },
        );

        this.pageReady = true;
      });
    },
    _initIsAddSameGoods() {
      const localConfig = localStorage.getItem(
        'purchaseOrderEdit_isAllowAddSameGoods',
      );
      this.isAllowAddSameGoods =
        localConfig === null ? true : +localConfig === 1;
    },
    getEditTableHeight,
    // 跳转到收货页面
    navigatorReceive() {
      this.router.replace({
        path: '/purchase/purchaseReceipt',
        query: { keep_scroll: 1, id: this.$route.query.id },
      });
    },
    changeResetPurchaseStatus(value) {
      window.localStorage.setItem(
        'purchaseEdietPageResetPurchaseStatus',
        value ? 1 : 0,
      );
    },
    getDrawerData(data) {
      this.drawerActiveObj = data;
      // 改变 sTable 的参数会触发表格组件自动请求接口
      this.drawerFilters = {
        purchase_id: this.$route.query.id,
        commodity_id: data.commodity_id,
        purchase_order_com_id: data.id,
      };
      this.$refs.drawer.open();
    },
    _download(url) {
      window.open(url);
    },
    _changeIsAllowAddSameGoods() {
      localStorage.setItem(
        'purchaseOrderEdit_isAllowAddSameGoods',
        +this.isAllowAddSameGoods,
      );
    },
    _changePurchase(value) {
      if (value && value.length) {
        const [purchaseType, purchaseValue] = value;
        this.formValidate.purchase_type = purchaseType;
        if (+purchaseType === 1) {
          this.formValidate.agent_id = purchaseValue;
          this.formValidate.provider_id = '';
          // 重置price_type
          this.dataList.forEach((item, index) => {
            if (this.originDataListCopy[index]) {
              this.originDataListCopy[index].price_type = 0;
            } else {
              this.$set(this.dataList, index, {
                ...item,
                price_type: 0,
              });
            }
          });
        } else {
          this.formValidate.provider_id = purchaseValue;
          this.formValidate.agent_id = '';
          this.batchUpdatePurchasePrice();
        }
        this.batchUpdateLastCommodityInfo();
      } else {
        this.formValidate.purchase_type = '';
        this.formValidate.purchase_type = '';
        this.formValidate.agent_id = '';
        this.formValidate.provider_id = '';
      }
      // 校验purchase_type
      this.$refs['formValidate'].validateField('purchase_type');
    },
    /**
     * @description: 获取采购单详情
     * @param {String} id
     */
    _getPurchaseOrderDetail(id) {
      this.tableLoading = true;
      purchase.getPurchaseOrderDetail({ id }).then(({ status, data }) => {
        this.tableLoading = false;
        if (status && data && data.order) {
          const { order } = data;
          this.orderDetail = order;
          if (
            !this.isOpenProviderSortingPurchaseTask ||
            this.orderDetail.purchase_type_name === '市场自采'
          ) {
            let providerSortAmountIndex = this.drawerColumns.findIndex(
              (item) => item.key === 'provider_sort_amount',
            );
            if (providerSortAmountIndex !== -1) {
              this.drawerColumns.splice(providerSortAmountIndex, 1);
            }
          }
          this.formValidate.purchase_type = String(order.purchase_type);
          if (+order.purchase_type === 1) {
            this.formValidate.agent_id = String(order.agent_id);
          } else {
            this.formValidate.provider_id = String(order.provider_id);
          }
          this.formValidate.plan_date = order.plan_date;
          this.formValidate.purchase_time = order.purchase_time;
          this.formValidate.storeId = order.storage_id;
          this.formValidate.sort_type = +order.sort_type;
          this.formValidate.provider_supervisor =
            +order.provider_supervisor > 0
              ? String(order.provider_supervisor)
              : '';
          this.formValidate.update_in_price = +order.update_in_price;
          this.formValidate.tag_id = order.tag_id;
          this.formValidate.valuation_date = order.valuation_date || '';

          this.dataList = order.commoditys.map((item) => {
            item._disableDelete =
              !item.purchase_status || Number(item.purchase_status) === 1
                ? false
                : true;
            item['logo'] = item.logo + '!40x40';
            item['price'] = item.price || 0;
            item['total_price'] = item.purchase_total_price;
            item['purchase_price'] = Number(item.purchase_price) || 0;
            return item;
          });

          if (this.orderDetail.purchase_type == 5) {
            this.isResetPurchaseStatus = true;
          }
          this.originDataListCopy = cloneDeep(this.dataList);

          // 采购任务生成的采购单，默认勾选配置：删除商品后重置采购任务采购状态
          // if (this.orderDetail.pur_plan_no.includes('采购任务生成采购单')) {
          // 	this.isResetPurchaseStatus = true;
          // }

          this.purchaseOrderRemark = order.remark;
          this.attachmentFiles = order.attach_url;
          let unwatchDataList = this.$watch(
            () => {
              return this.purchaseOrderRemark || this.dataList;
            },
            () => {
              this.isModified = true;
              unwatchDataList(); // 只需监听一次，标记已修改后移除监听
            },
            { deep: true },
          );

          this.pageReady = true;
        }
      });
    },
    /**
     * @description: 获取并设置商品相关数据
     * @param {String} cid 选择的商品id
     * @param {Object} row 当前行数据
     * @param {Number} index 当前行下标
     */
    async _setCommodity(cid, row, index) {
      if (cid === row.commodity_id) return; // 商品没有变更，return
      const findIndex = this.dataList.findIndex(
        (item) => item.commodity_id === cid,
      );
      if (~findIndex && !this.isDuplicateCommodity) {
        const existingRow = this.dataList[findIndex];
        this._focusGoodsNumInput(existingRow.index);
        // 触发name更新，清空商品名称
        row.name = existingRow.name;
        this.$nextTick(() => {
          row.name = '';
        });
        this.errorMessage('您已经添加过该商品了');
        return;
      }
      // 前提保证没有更换过发货日期
      else if (
        this.deleteList.length &&
        this.orderDetail.plan_date === this.formValidate.plan_date
      ) {
        const findIndex = this.deleteList.findIndex(
          (goods) => goods.commodity_id === cid,
        );
        if (~findIndex) {
          this.$Modal.confirm({
            title: '提示',
            content: '<p>您已经删除过该商品了，现在是否确定恢复？</p>',
            onOk: () => {
              this.dataList.splice(index, 1, this.deleteList[findIndex]);
              this.deleteList.splice(findIndex, 1);
              this._syncStoreList();
            },
          });
          return;
        }
      }
      const { status, data } = await purchase.getCommodityInfo({
        provider_id: this.formValidate.provider_id,
        agent_id: this.formValidate.agent_id,
        commodity_id: cid,
        plan_date: this.formValidate.plan_date,
        valuation_date: this.formValidate.valuation_date,
        store_id: this.orderDetail.storage_id,
      });
      if (status && data) {
        const { commodity } = data;

        // 判断选中采购员/供应商与商品默认的是否相同，相同的也可以直接添加
        const { purchase_type, provider_id, agent_id } = commodity;
        // 是否满足商品的默认条件(采购员/供应商相同)
        const matchDefaultRules =
          +this.formValidate.purchase_type === +purchase_type &&
          (String(agent_id) === String(this.formValidate.agent_id) ||
            String(provider_id) === String(this.formValidate.provider_id));
        if (+commodity['allow_change_channel'] === 1 || matchDefaultRules) {
          this._setCommodityInfo(commodity, row, index);
        } else {
          this.$Modal.confirm({
            title: '提示',
            content: '该商品不能临时指定采购员/供应商，确定要继续添加吗',
            onOk: () => {
              this._setCommodityInfo(commodity, row, index);
            },
          });
        }
      }
    },
    /**
     * @description: 接口获取商品数据后设置到当前行
     * @param {Object} commodity 接口取到的商品数据
     * @param {Object} row 当前行已有数据（保留待采购量的输入值）
     * @param {Number} index 当前行下标
     */
    _setCommodityInfo(commodity, row, index) {
      // 计划采购价取值逻辑：采购协议价 > 近一次采购价
      commodity['purchase_price'] =
        Number(commodity.purchase_price) || Number(commodity.in_price);
      if (
        commodity.purchase_agreement_price &&
        Number(commodity.purchase_agreement_price) > 0
      ) {
        commodity['purchase_price'] = commodity.purchase_agreement_price;
      }

      commodity['purchase_num'] = Number(row['purchase_num']);
      commodity['total_price'] = +Number(commodity['purchase_price'])
        .mul(row['purchase_num'])
        .toFixed(2);
      commodity['remark'] = '';

      // 数据初始化时 计算 税额 以及小计
      if (commodity['input_tax_rate'] !== '-') {
        let input_tax_rate = Number(commodity['input_tax_rate'])
          .div(100)
          .add(1);
        commodity['input_tax'] = commodity['total_price']
          .div(input_tax_rate)
          .mul(commodity['input_tax_rate'])
          .div(100)
          .toFixed(4);
        commodity['tax_exclusive'] = commodity['total_price']
          .sub(commodity['input_tax'])
          .toFixed(4);
      } else {
        commodity.input_tax = '-';
        commodity['tax_exclusive'] = commodity['total_price'];
      }
      commodity['logo'] = commodity['logo'] + '!40x40';
      commodity[this.rowKey] = row[this.rowKey]; // 商品可重复，取组件内生成的唯一key
      commodity._sortNum = this.originDataListCopy[index]._sortNum;
      this.dataList[index] = this.originDataListCopy[index] = commodity;
      this._syncStoreList();
      this._focusGoodsNumInput(index);
      this.handleCheckReference({ row: commodity });
    },
    handleRowClick(row) {
      this.handleCheckReference({ row });
    },
    handleSortChange(columns, key, order) {
      this.dataList = this.dataList.sort((a, b) => {
        if (order === 'asc') {
          if (isNaN(a[key])) {
            return a[key].localeCompare(b[key]);
          } else {
            return a[key] - b[key];
          }
        } else if (order === 'desc') {
          if (isNaN(a[key])) {
            return b[key].localeCompare(a[key]);
          } else {
            return b[key] - a[key];
          }
        } else {
          return a._sortNum - b._sortNum;
        }
      });
      this._syncStoreList();
    },
    // 税额 小计联动
    changes(row, index) {
      const val = row.total_price;
      if (row.input_tax_rate !== '-' && row.input_tax_rate !== undefined) {
        let input_tax_rate = Number(row.input_tax_rate).div(100).add(1);
        row.input_tax = Number(val)
          .div(input_tax_rate)
          .mul(row.input_tax_rate)
          .div(100)
          .toFixed(4);
        row.tax_exclusive = Number(val).sub(row.input_tax).toFixed(2);
      } else {
        row.tax_exclusive = val;
      }
      this.$set(this.originDataListCopy, index, row);
      // this._syncStoreList();
    },

    rowClassName(row) {
      return row._show === false ? 'dn' : '';
    },
    _onSearchCommodity() {
      this.dataList = [].concat(this.originDataListCopy);
      const matchValue = String(this.searchValue);
      const dataListCopy = cloneDeep(this.dataList);
      dataListCopy.forEach((item) => {
        item._show =
          !matchValue ||
          item.name.includes(matchValue) ||
          item.commodity_code.includes(matchValue);
      });
      this.dataList = cloneDeep(dataListCopy);
    },
    _insertAt(index) {
      if (index === this.dataList.length - 1) {
        // 只在最后一行添加
        setTimeout(() => {
          this.$refs.editableTable.insertAt(index);
        });
      }
    },
    onInsert(insertedRow, index) {
      this.dataList[this.dataList.length - 1]._sortNum = index + 1;
      this._syncStoreList();
      if (!this.changePurchase) this._focusGoodsNameInput(index + 1);
      if (index === -1 && !this.copyId) {
        // 新增的情况
        this._initPageReady();
      }
    },
    onDelete(row) {
      this.deleteList.push(row);
      this._syncStoreList();
    },
    _syncStoreList() {
      const dataListCopy = cloneDeep(this.dataList);
      dataListCopy.forEach((goods) => {
        const storeGoods = this.originDataListCopy.find(
          (storeGoods) => storeGoods[this.rowKey] === goods[this.rowKey],
        );
        if (storeGoods) {
          // 显示状态以dataList为准
          storeGoods._show = goods._show;
          Object.assign(goods, storeGoods);
        }
      });
      this.dataList = dataListCopy;
      this.originDataListCopy = cloneDeep(dataListCopy);
    },
    // 聚焦至商品名称输入框
    _focusGoodsNameInput(index) {
      this.$nextTick(() => {
        const commodityInputTd =
          document.querySelectorAll('td.commodity-name')[index];
        commodityInputTd &&
          commodityInputTd.querySelector('input,textarea').focus();
      });
    },
    // 聚焦至目标商品的待采购量输入框
    _focusGoodsNumInput(index) {
      this.$nextTick(() => {
        const targetRowId = index;
        const inputObj = document.querySelector(
          `#purchase_num-${targetRowId} input`,
        );
        if (inputObj) {
          inputObj.focus();
          inputObj.select();
          inputObj.scrollIntoView({ block: 'center' });
        }
      });
    },
    _checkValid(postList) {
      let valid = true;
      this.$refs.formValidate.validate((formValid) => {
        valid = formValid;
      });
      if (valid === false) return valid;

      if (postList.length === 0) {
        valid = false;
        this.errorMessage('请添加商品和数量');
        return valid;
      }
      return valid;
    },
    _scrollToFirstError() {
      this.$nextTick(() => {
        const $firstRow = this.$refs.formValidate.$el.querySelector(
          '.ivu-form-item-error, .error',
        );
        if ($firstRow) {
          $firstRow.scrollIntoView({ block: 'center' });
        }
      });
    },
    $_onSave(options = {}) {
      // 获取全量数据
      this._syncStoreList();
      const postList = this.originDataListCopy.filter(
        (goods) => goods.commodity_id,
      );
      if (!this._checkValid(postList)) {
        this._scrollToFirstError();
        return;
      }
      const commodities = postList.map((goods, index) => ({
        sort_num: index + 1,
        commodity_id: parseInt(goods.commodity_id),
        num: Number(goods.purchase_num).toFixed(2) || 0,
        price: goods.purchase_price || 0,
        sub_price: goods.total_price,
        remark: goods.remark,
        purchase_order_com_id: goods._isNew ? '' : goods.id,
        calculate_way: goods.calculate_way,
      }));

      const {
        purchase_type,
        agent_id,
        provider_id,
        plan_date,
        storeId: store_id,
        sort_type,
        update_in_price,
        provider_supervisor,
        tag_id,
      } = this.formValidate;

      const params = {
        ...options,
        id: this.purchaseOrderId,
        commoditys: JSON.stringify(commodities),
        purchase_type,
        tag_id: tag_id ? tag_id : 0,
        agent_id,
        provider_id,
        plan_date,
        store_id,
        sort_type,
        update_in_price,
        provider_supervisor,
        remark: this.purchaseOrderRemark,
        type: 2,
        is_reset_purchase_status: this.isResetPurchaseStatus ? 1 : 0,
        del_ids: this.deleteList.map((goods) => goods[this.rowKey]).toString(),
        attachment_link: this.attachmentFiles.map((item) => item.url).join(','),
        valuation_date: this.formValidate.valuation_date,
      };

      this.saveLoading = true;
      purchase.editPurchaseOrder(params).then(({ status, message }) => {
        this.saveLoading = false;
        if (status) {
          this.successMessage('保存成功');
          this.isModified = false;
          if (options.type === 'receipt') return this.navigatorReceive();

          let path = 'purchaseOrderList';
          // 转变状态的时候, 也需要自动合并的弹窗
          if (options.audit === 1) path += '?check_merge=true';

          this.$router.push({ path });
        } else {
          this.errorNotice({ desc: message });
        }
      });
    },
    _onDraggableData(data) {
      this.dataList = data;
      this.postList = this.deepClone(this.dataList);
    },
  },
  beforeRouteLeave(to, from, next) {
    if (this.isModified) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: () => {
          this.pageIntroLoaded = true;
          next();
        },
        onCancel: () => {
          next(false);
        }
      });
    } else {
      // 页面进入后就跳转，但有keepAlive缓存时，接口请求未取消，还会继续显示intro引导，暂用变量控制不显示引导，后续应加入路由跳转时取消上一个页面的请求
      this.pageIntroLoaded = true;
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
};
</script>

<style lang="less" scoped>
.purchase-order-edit {
  position: relative;
}
/deep/ .column__required::before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 13px;
  color: #f55654;
  vertical-align: middle;
}
.drawer-box {
  width: 1200px;
  padding: 20px;
  .drawer-line {
    margin-top: 20px;
    display: flex;
    div {
      margin-right: 80px;
      width: 300px;
    }
  }
  .drawer-table {
    margin-top: 20px;
  }
}
/deep/ .drag-row {
  left: 0!important;
}
</style>
