<template>
  <div class="returnOrderDetail">
    <DetailPage pageType="view" title="查看采购退回">
      <Form
        ref="formValidate"
        inline
        label-colon
        :label-width="120"
        :disabled="false"
      >
        <base-block title="基础信息" class="base-info__detail">
          <FormItem label="单号">{{ returnOrder.no }}</FormItem>
          <FormItem label="仓库">{{ returnOrder.store_name }}</FormItem>
          <FormItem label="单据状态">{{ returnOrder.status_text }}</FormItem>
          <FormItem label="制单人">{{ returnOrder.author }}</FormItem>
          <FormItem label="单据日期">{{ returnOrder.create_time }}</FormItem>
          <FormItem label="退货金额">{{ returnOrder.price }}</FormItem>
          <FormItem label="采购员/供应商">{{ returnOrder.agent_name || returnOrder.provider_name }}</FormItem>
          <FormItem label="退回方式">{{ returnOrder.return_type === 'order' ? '关联原单' : '不关联原单' }}</FormItem>
          <FormItem label="源采购单号">
            <span class="link" @click="jump2PurchaseOrderDetail" v-if="returnOrder.pur_no">{{ returnOrder.pur_no }}</span>
            <span v-else>--</span>
          </FormItem>
          <FormItem label="退货日期">{{ returnOrder.return_date }}</FormItem>
          <FormItem label="关联退货退款单">
            <template v-if="returnOrder.link_return_order_info && returnOrder.link_return_order_info.length">
              <span v-for="(item, index) in returnOrder.link_return_order_info" :key="item.id">
                <RelationNo :id='item.id' :no='item.return_no'></RelationNo>{{ returnOrder.link_return_order_info.length - 1 > index ? '、' : ''}}
              </span>
            </template>
            <span v-else>-</span>
          </FormItem>
        </base-block>
        <base-block title="退货商品清单" class="base-info__detail"></base-block>
        <EditableTable
          ref="editableTable"
          :has-customize-field="true"
          rowKey="id"
          :data="returnOrder.commoditys"
          :columns="columns"
          :loading="tableLoading"
          :isShowRecordEditor="false"
          :virtualScroll="true"
          :virtualScrollBuff="{ top: 200, bottom: 200 }"
          :max-height="getEditTableHeight()"
          :stickyTop="100"
        >
          <template #after-table-left>
            <!-- 不显示左侧信息 -->
            <div></div>
          </template>
          <template #after-table-right>
            <div class="summary">
              <span>合计(含税)：</span>
              <span class="total1">{{ calTotal || 0 }}</span>
              <template v-if="input_tax_rate == 1">
                <span class="ml16">合计(不含税)：</span>
                <span class="total2">{{ taxExclusive || 0 }}</span>
              </template>
            </div>
          </template>
        </EditableTable>
        <base-block title="其他信息" class="base-info__detail mb16"></base-block>
        <div class="otherFormItem mb14">
            <div>备注：</div>
            <div>{{ returnOrder.remark || '暂无' }}</div>
          </div>
          <div class="otherFormItem">
            <div>附件：</div>
            <div>
              <AttachmentUpload v-model="purchaseFiles" :add="false" :remove="false" />
            </div>
          </div>
        <base-block title="单据操作历史" class="base-info__detail mb20"></base-block>
        <Table
          :columns="columns2"
          :data="historyList"
          outer-border
        ></Table>
      </Form>
      <template #button-after>
        <Button @click="_printOrder">打 印</Button>
        <Button @click="excelExport">导 出</Button>
      </template>
    </DetailPage>
  </div>
</template>

<script>
import purchase from '@api/purchase.js';
import webPrint from '@/util/print';
import settings from '@api/settings';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import DetailPage from '@/components/detail-page/index.js';
import EditableTable from '@/components/editable-table/index.js';
import { getEditTableHeight } from '@/util/common';
import Table from '@/components/table';
import RelationNo from '@/components/relation-no'
import ConfigMixin from '@/mixins/config';

export default {
  name: 'return-order-detail',
  mixins: [ConfigMixin],
  components: {
    Table,
    DetailPage,
    EditableTable,
    AttachmentUpload,
    RelationNo
  },
  data() {
    return {
      tableLoading: false,
      input_tax_rate: 0,
      taxExclusive: 0,
      returnOrder: {
        id: '',
        commoditys: []
      },
      agentDic: {},
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商品图片',
          key: 'logo',
          width: 90,
          align: 'center',
          fixed: 'left',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品',
          poptip: true,
          width: 220,
          key: 'name',
          fixed: 'left'
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          width: 140
        },
        {
          title: '单位',
          key: 'unit',
          width: 90
        },
        {
          title: '商品描述',
          key: 'summary',
          width: 120,
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          width: 120,
        },
        {
          title: '采购入库数量',
          key: 'in_store_num',
          minWidth: 110,
          align: 'right'
        },
        {
          title: '采购入库单价',
          key: 'in_store_price',
          minWidth: 110,
          align: 'right'
        },
        {
          title: '采购入库金额',
          key: 'in_total_price',
          minWidth: 110,
          align: 'right'
        },
        {
          title: '已退数量',
          key: 'origin_return_num',
          minWidth: 90,
          align: 'right'
        },
        {
          title: '退货数量',
          key: 'num',
          minWidth: 90,
          align: 'right'
        },
        {
          title: '退货单价',
          key: 'price',
          minWidth: 90,
          tip: '对应的采购单收货均价',
          align: 'right',
        },
        {
          title: '退货小计(元)',
          key: 'total_price',
          minWidth: 110,
          align: 'right'
        },
        {
          title: '税率',
          key: 'input_tax_rate',
          minWidth: 90,
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.input_tax_rate == '-' ? '--' : row.input_tax_rate + '%'
            );
          }
        },
        {
          title: '税额',
          key: 'input_tax',
          minWidth: 90,
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            return h('span', row.input_tax == '-' ? '--' : row.input_tax);
          }
        },
        {
          title: '退货小计（不含税）',
          key: 'tax_exclusive',
          minWidth: 140,
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            let tax_exclusive = Number(row.tax_exclusive).toFixed(4);
            return h('span', tax_exclusive ? tax_exclusive : '--');
          }
        },
        {
          title: '退货成本均价',
          key: 'return_in_price',
          minWidth: 110,
          align: 'right',
          tip: '采购退货审核时候对应的商品库存均价',
          render: (h, params) => {
            return h('span', params.row.return_in_price);
          }
        },
        {
          title: '退回出库金额',
          key: 'return_out_total_price',
          minWidth: 120,
          align: 'right'
        },
        {
          title: '批次号',
          key: 'batch_remark',
          minWidth: 115,
          render: (h, params) => {
            let { row } = params;
            let batch_remark =row.batch_remark? row.batch_remark.map(e=>e.batch_no).join(',') :'';
            return h('span', batch_remark ? batch_remark : '--');
          }
        },
        {
          title: '备注',
          key: 'remark',
          minWidth: 110
        }
      ],
      columns2: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center'
        },
        {
          title: '操作人',
          key: 'user_name'
        },
        {
          title: '时间',
          key: 'create_time'
        },
        {
          title: '操作历史',
          key: 'describe'
        }
      ],
      historyList: [],
      PurchaseType: [],
      purchaseFiles: [],
    };
  },
  created() {
    this.getConfig();
    this.returnOrder.id = this.$route.query.id;
    this.getPurchaseReturnDetail();
  },
  computed: {
    /**
     * 合计（不含税）
     */
    tax_exclusive() {
      let totalNum = 0;
      this.returnOrder.commoditys.map(d => {
        if (d.tax_exclusive !== undefined && d.tax_exclusive !== null) {
          totalNum += parseFloat(d.tax_exclusive);
        }
      });
      return parseFloat(totalNum).toFixed(2);
    },
    calTotal() {
      let totalNum = 0;
      this.returnOrder.commoditys.map(d => {
        totalNum += parseFloat(d.total_price);
      });
      return parseFloat(totalNum).toFixed(2);
    }
  },
  methods: {
    getEditTableHeight,
    jump2PurchaseOrderDetail () {
      let routeUrl = this.$router.resolve({
        path: 'purchaseOrderDetail',
        query: { id: this.returnOrder.purchase_id, back: 'close' }
      });
      window.open(routeUrl.href, '_blank');
    },
    /**
     * 打印单据
     * @param {object} params 表格当前行数据
     */
    async _printOrder() {
      const row = this.returnOrder;
      // 更新打印次数
      // this.printPurchase(row.id, params.index);
      const oldVersionPrint = () => {
        const printBtn = document.createElement('span');
        printBtn.setAttribute('data-purchase_id', row.id);
        printBtn.classList.add('tpl_print_view_pur_return');
        document.body.appendChild(printBtn);
        printBtn.click();
        document.body.removeChild(printBtn);
      };
      const template = await settings
        .getPrintTemplate({
          type: 'PUR_RETURN'
        })
        .catch(() => false);
      if (!template) {
        this.errorMessage('找不到打印模版');
        return;
      }
      // 新打印模版
      if (settings.isNewTemplate(template)) {
        purchase.getPurchaseReturnPrintData(row.id).then(res => {
          let { status, data, message } = res;
          if (status) {
            const printData = {
              ...data.order,
              items: data.item.map((item, index) => {
                item.no = index + 1;
                item.goods_remark = item.remark;
                item.goods_price = item.price;
                return item;
              })
            };
            const templateData = template.tpl_data;
            templateData.config.preview = true;
            printData.pur_return_no = printData.no;
            webPrint({
              template: templateData,
              data: printData
            });
          } else {
            this.errorMessage(message);
          }
        });
      } else {
        oldVersionPrint();
      }
    },
    // 获取进项税配置 是否展示 税率 税额 小计（不含税）合计（不含税）
    getConfig() {
      this.commonService.getConfig().then(config => {
        let { input_tax_rate,is_batch } = config;
        this.input_tax_rate = input_tax_rate;
        if (+input_tax_rate === 0) {
          let columns = this.columns.filter(item => {
            return (
              item.key !== 'input_tax_rate' &&
              item.key !== 'input_tax' &&
              item.key !== 'tax_exclusive'
            );
          });
          this.columns = columns;
        }
        if(is_batch==0){
            let columns = this.columns.filter(item => {
            return (
              item.key !== 'batch_remark'
            );
          });
          this.columns = columns;
        }
      });
    },
    excelPrint() {
      purchase.printPurchaseOrder({ id: this.returnOrder.id }).then(res => {
        // eslint-disable-next-line no-empty
        if (res.status) {
        }
      });
    },
    excelExport() {
      location.href =
        '/superAdmin/purchaseReturn/export?id=' + this.returnOrder.id;
    },
    getPurchaseReturnDetail() {
      this.tableLoading = true
      purchase.purchaseReturnDetail({ id: this.returnOrder.id }).then(res => {
        if (res.status) {
          let order = res.data.order;
          this.purchaseFiles = order.purchase_files_link || []
          console.log('purchaseFiles', this.purchaseFiles)
          this.taxExclusive = order.total_tax_exclusive;
          order.commoditys.forEach(item => {
            item.origin_return_num = item.return_num;
            item.return_num = item.num ? item.num : 0;
            item.return_price = item.price ? item.price : 0;
            // item.total_price = item.return_price * item.return_num;
          });
          this.returnOrder = order;
          this.historyList = res.data.order.logs;
        }
      }).finally(() => {
        this.tableLoading = false;
      });
    }
  }
};
</script>

<style lang="less" scoped>
.link {
  color: var(--primary-color);
  cursor: pointer;
}
.summary {
  display: flex;
  align-items: center;
  line-height: 17px;
  .total1 {
    font-family: Avenir, Avenir;
    font-weight: 550;
    font-size: 14px;
    color: #FF6E00;
  }
  .total2 {
    font-family: Avenir, Avenir;
    font-weight: 550;
    font-size: 14px;
    color: #F13130;
  }
}
.otherFormItem {
  font-size: 13px;
  display: flex;
  div {
    &:first-child {
      flex-shrink: 0;
    }
  }
}
</style>
