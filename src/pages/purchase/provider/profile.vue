<template>
  <div>
    <header class="tl mt24 js-before-table"></header>
    <section>
      <ListTable
        ref="orderListTable"
        :filters="filters"
        :border="false"
        :loading="listLoading"
        :before-request="beforeRequest"
        :outer-border="true"
        :max-line="2"
        row-key="id"
        :filter-items="filterItems"
        :columns="proCol"
        data-provider="/superAdmin/provider/list"
        :height="getTableHeight() - 120"
        keepScroll
        :before-set-data="afterRequest"
        @on-selection-change="handleSelectionChange"
        :extraSelectionCount="extraSelectionCount"
      >
        <Button
          v-if="!is_sys_supervisor"
          type="default"
          slot="button"
          @click="exportes"
        >导出</Button>
        <div
          v-if="!is_sys_supervisor"
          slot="before-table"
          class="common__operation"
        >
          <div class="slot-left">
            <Button
              styleType="btnStyleForAdd"
              ype="primary"
              @click="router.push('/purchase/provider/add')"
            >新增</Button>
            <base-import-button
              class="ml10"
              :title="title"
              @on-completed="importBtnCallBack"
              :download="download"
              :post="post"
            >批量导入</base-import-button>
          </div>
          <div class="slot-right">
            <Checkbox
              style="margin-right: 3px"
              @on-change="getData"
              :true-value="0"
              :false-value="3"
              v-model="filters.disable"
            >不显示禁用账号</Checkbox>
          </div>
        </div>
        <div slot="batch-checked" class="checked_button" @click="checkedChange()">
          {{checkedAll ? '勾选当前页内容' : '勾选所有页内容'}}
        </div>
        <div slot="batch-operation">
          <Button @click="showBatchEditModal">批量编辑</Button>
        </div>
      </ListTable>
    </section>
    <Modal
      :width="730"
      v-model="batchEditModal.show"
      :title="batchEditModal.title"
      @on-cancel="hideBatchEditModal"
    >
      <Form
        ref="batchEditRef"
        :model="batchEditModal.formData"
        label-colon
        :label-width="110"
      >
        <FormItem
          label="客户信息显示"
        >
          <RadioGroup v-model="batchEditModal.formData.show_info_config_type">
            <Radio :label="-1">不调整</Radio>
            <Radio label="0">与业务配置一致</Radio>
            <Radio label="1">独立配置</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem
          v-show="batchEditModal.formData.show_info_config_type === '1'"
        >
          <CheckboxGroup v-model="batchEditModal.formData.show_info_config">
            <Checkbox label="1">客户名称</Checkbox>
            <Checkbox label="4">联系人</Checkbox>
            <Checkbox label="2">客户地址</Checkbox>
            <Checkbox label="3">客户电话</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem
          label="自主管理商品"
        >
          <CheckboxGroup v-model="batchEditModal.formData.manageGoods">
            <Checkbox label="is_not_update" :disabled="batchEditModal.formData.manageGoods.length && !batchEditModal.formData.manageGoods.includes('is_not_update')" @on-change="onNoManageGoodsChange">不调整</Checkbox>
            <Checkbox label="is_open_provider_power" :disabled="batchEditModal.formData.manageGoods.includes('is_not_update')">编辑商品<Button style="margin-left: 10px" v-if="isShowManageEditGoods" @click="showAuthorityModal">修改权限</Button></Checkbox>
            <Checkbox label="is_open_add_commodity" :disabled="batchEditModal.formData.manageGoods.includes('is_not_update')">新增商品</Checkbox>
            <Checkbox label="is_open_upload_commodity_trace" :disabled="batchEditModal.formData.manageGoods.includes('is_not_update')" v-if="openCommodityTrace">上传溯源报告</Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="采购单拆单方式" v-if="sysConfig.is_open_purchase_task == 1">
          <RadioGroup v-model="batchEditModal.formData.purchase_task_gen_order_mode">
            <Radio :label="-1">不调整</Radio>
            <Radio label="0">与业务配置一致</Radio>
            <Radio label="1">按供应商</Radio>
            <Radio label="3">按客户</Radio>
            <Radio label="2">按订单号</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem
          v-show="isOpenProviderDeliver"
          label="联营供应商"
        >
          <Select
            v-model="batchEditModal.formData.provider_type"
            placeholder="请选择"
            style="width:232px"
          >
            <Option :value="-1">不调整</Option>
            <Option value="2">开启（开启后不可关闭）</Option>
          </Select>
        </FormItem>
        <FormItem :label-width="95" label="分拣：" >
          <RadioGroup v-model="batchEditModal.formData.is_open_sort">
            <Radio :label="-1">不调整</Radio>
            <Radio label="1">与业务配置一致</Radio>
            <Radio label="2">独立配置</Radio>
          </RadioGroup>
          <span v-show="+batchEditModal.formData.is_open_sort == 2">
            <Switch v-model="batchEditModal.formData.providerSortStatus"/>
            <span style="color: rgba(0, 0, 0, 0.5); margin-left: 8px;">开启后，供应商可进行分拣/重置操作</span>
          </span>
        </FormItem>
        <FormItem
          label="查看销售报表"
          v-if="+sysConfig.is_open_purchase_task === 0"
        >
          <RadioGroup v-model="batchEditModal.formData.is_open_sale_report">
            <Radio :label="-1">不调整</Radio>
            <Radio label="1">开启</Radio>
            <Radio label="0">关闭</Radio>
          </RadioGroup>
        </FormItem>
        <FormItem :label-width="95" label="资质范围：">
            <CheckboxGroup v-model="batchEditModal.formData.purchaseAptitude">
              <Checkbox label="is_not_update"
              :disabled="batchEditModal.formData.purchaseAptitude.length && !batchEditModal.formData.purchaseAptitude.includes('is_not_update')">不调整</Checkbox>
              <Checkbox
              :disabled="batchEditModal.formData.purchaseAptitude.includes('is_not_update')"
              label="is_show_mall_qualification"
              >
                商城
                <Tooltip
                  placement="right"
                  content="勾选后，可在商城商品详情内展示商品默认供应商的资质内容"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
              <Checkbox
              :disabled="batchEditModal.formData.purchaseAptitude.includes('is_not_update')"
              label="is_show_group_qualification"
              >
                集团后台
                <Tooltip
                  placement="right"
                  content="勾选后，可在集团后台展示该供应商的资质内容"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
              <Checkbox
              :disabled="batchEditModal.formData.purchaseAptitude.includes('is_not_update')"
              label="is_show_purchase_qualification"
              >
                供应商后台（可编辑）
                <Tooltip
                  placement="right"
                  content="勾选后，供应商后台展示自己的资质内容，可编辑"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
        <FormItem
          label="发货单模板"
        >
          <Select
            v-model="batchEditModal.formData.tpl_info_config_order"
            style="width:232px"
            placeholder="请选择"
          >
            <Option v-for="item in orderTemplate" :key="item.value" :value="item.value">
              {{ item.label }}
            </Option>
          </Select>
          <span style="color: rgba(0, 0, 0, 0.5);margin-left: 8px;">优先级高于打印配置页面选择的模板</span>
        </FormItem>
        <FormItem
          label="分拣单模板"
        >
          <Select
            v-model="batchEditModal.formData.tpl_info_config_pick"
            style="width:232px"
            placeholder="请选择"
          >
            <Option v-for="item in pickTemplate" :key="item.value" :value="item.value">
              {{ item.label }}
            </Option>
          </Select>
          <span style="color: rgba(0, 0, 0, 0.5);margin-left: 8px;">优先级高于打印配置页面选择的模板</span>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="hideBatchEditModal">取消</Button>
        <Button :loading="batchEditLoading" class="ml8" type="primary" @click="batchEdit">确定</Button>
      </div>
    </Modal>
    <AuthorityModal
      ref="authorityModalRef"
      @confirm="authorityConfirm"
      @cancel="authorityCancel"
    />
  </div>
</template>

<script>
import { api } from '@/api/api';
import settings from '@api/settings';
import { get, post } from '@/api/request';
import ListTable from '@components/list-table';
import Button from '@components/button';
import BaseImportButton from '@sdptest/base/lib/import-button';
import AuthorityModal from './components/authorityModal.vue';
import ConfigMixin from '@/mixins/config';
import StorageUtil from '@/util/storage'
import TableHeadSortIcon from '@components/common/tableHeadSortIcon'
import SIcon from "@/components/icon";
import FundAllocationMixin from '@/mixins/fund-allocation';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

export default {
  name: 'providerList',
  mixins: [ConfigMixin, FundAllocationMixin],
  components: {
    ListTable,
    Button,
    BaseImportButton,
    AuthorityModal,
  },
  data () {
    return {
      sort: {
        supplier_code: 0,
        create_time: 0
      },
      canRequest: true,
      title: '批量导入供应商基础资料',
      post: {
        url: '/superAdmin/provider/import',
        accept: 'csv, .xlsx',
        maxSize: 1000,
        format: ['csv', 'xlsx']
      },
      download: {
        'before-download': this.getDownloadUrl,
        text: '供应商基础资料模版'
      },
      filterItems: [
        {
          label: '搜索',
          type: 'Input',
          props: {
            placeholder: '供应商名称/联系人/联系方式/编码'
          },
          key: 'provider_name'
        },
        {
          label: '进件状态',
          key: 'account_status',
          type: 'Select',
          props: {
            placeholder: '全部状态',
          },
          data: [
            {
              label: '全部状态',
              value: ''
            },
            {
              label: '未提交',
              value: 0
            },
            {
              label: '进件成功',
              value: 1
            },
            {
              label: '进件失败',
              value: 2
            }
          ]
        },
        {
          label: '是否联营',
          key: 'provider_type',
          type: 'Select',
          props: {
            placeholder: '全部',
          },
          data: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '是',
              value: '2'
            },
            {
              label: '否',
              value: '1'
            }
          ]
        },
        {
          show: () => this.isOpenProviderFieldCustomize,
          checked: false,
          width: 'auto',
          type: 'custom',
          name: '供应商自定义字段',
          key: ['provider_customize_id', 'provider_customize_field_select_config_ids'],
          defaultValue: [],
          props: {
            customizeType: '7',
            label: '供应商自定义字段',
          },
          component: CustomizeCascader,
        },
      ],
      layoutExtraHeight: 40 + 15,
      queryId: '',
      listLoading: false,
      proList: [],
      filters: {
        disable: 0
      },
      pageParams: {
        page: 1,
        total_page: 0,
        page_size: 10
      },
      proColTemp: [
        {
          show: () => { return !this.is_sys_supervisor },
          align: 'left',
          fixed: 'left',
          type: 'selection',
          width: 36,
        },
        {
          title: '供应商名称',
          key: 'name',
          align: 'left',
          fixed: 'left',
          minWidth: 140,
          render: (h, params) => {
            const { name } = params.row;
            return h('div', {
              class:'highlight',
              on: {
                click: () => {
                  this.operate(params.row, 0);
                }
              }
            }, name)
          }
        },
        {
          title: '创建时间',
          key: 'create_time',
          resizable: true,
          minWidth: 160,
          renderHeader: (h) => {
            return h('div', {
              style: {display: 'flex',alignItems: 'center'}
            },[
              h('span',{
                style: {
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    $('#createTimeSortIcon').click();
                  }
                }
              },'创建时间'),
              h(TableHeadSortIcon,{
                props: {
                  sortRule: this.sort.create_time,
                  id: 'createTimeSortIcon'
                },
                on:{
                  onChange: (e) => {
                    this.handleSortKeyChange('create_time', e)
                    this.$refs.orderListTable.fetchData();
                  }
                }
              })
            ])
          },
        },
        {
          title: '供应商编码',
          key: 'provider_code',
          minWidth: 140,
          renderHeader: (h) => {
            return h('div', {
              style: {display: 'flex',alignItems: 'center'}
            },[
              h('span',{
                style: {
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    $('#shopNameSortIcon').click();
                  }
                }
              },'供应商编码'),
              h(TableHeadSortIcon,{
                props: {
                  sortRule: this.sort.shop_name,
                  id: 'shopNameSortIcon'
                },
                ref: "TableHeadSortIcon",
                on:{
                  onChange: (e) => {
                    this.handleSortKeyChange('supplier_code', e)
                    this.$refs.orderListTable.fetchData();
                  }
                }
              })
            ])
          },
        },
        {
          title: '网关uuid',
          key: 'account_uuid',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '联系人',
          minWidth: 100,
          key: 'company_name',
          align: 'left'
        },
        {
          title: '联系方式',
          key: 'account',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '地址',
          key: 'address_detail',
          poptip: true,
          align: 'left',
          minWidth: 160,
        },
        {
          title: '禁用状态',
          minWidth: 100,
          render: (h, params) => {
            const isDisable = Number(params.row.disable) === 1;
            return h(
              'span',
              {
                class: {
                  'text-gray': isDisable
                }
              },
              isDisable ? '禁用' : '正常'
            );
          }
        },
        {
          title: '进件状态',
          key: 'account_status_text',
          render: (h, params) => {
            const { row } = params;
            const children = [row.account_status_text];
            if (+row.account_status === 2) {
              children.push(h('Tooltip', {
                props: {
                  content: row.account_message,
                  transfer: true,
                  maxWidth: 200
                },
                style: {
                  marginLeft: '5px'
                }
              }, [
                h(SIcon,{
                  props: {
                    icon:"tips",
                    size: 12
                  },
                })
              ]));
            }
            return h('div', children);
          }
        },
        {
          resizable: false,
          title: '操作',
          key: 'action',
          fixed: 'right',
          type: 'action',
          width: 210,
          show: () => { return !this.is_sys_supervisor },
          actionCountLimit: 3,
          actions: params => {
            let { row } = params;
            let actions = [];

            if (row.isCanDetail !== 'N') {
              actions.push({
                name: '编辑',
                action: () => {
                  this.operate(row, 1);
                }
              });
            }
            if (+row.disable !== 1) {
              actions.push({
                name: '禁用',
                confirm: '确定禁用当前账号？',
                action: params => {
                  this.$request
                    .post(this.apiUrl.provierDisable, {
                      id: params.row.id,
                      disable: 1
                    })
                    .then(res => {
                      let { status, message } = res;
                      if (status) {
                        this.getData(true);
                        this.successMessage(message || '禁用成功！');
                      } else {
                        this.errorMessage(message || '禁用失败！');
                      }
                    });
                }
              });
            }

            if (row.isCanEdit !== 'N') {
              actions.push({
                name: '删除',
                confirm: '确定要删除本条记录吗？',
                action: () => {
                  this.removeItem(row);
                }
              });
            }

            if (Number(row.disable) === 1) {
              actions.push({
                name: '恢复',
                confirm: '确定恢复当前账号？',
                action: params => {
                  this.$request
                    .post(this.apiUrl.provierDisable, {
                      id: params.row.id,
                      disable: 0
                    })
                    .then(res => {
                      let { status, message } = res;
                      if (status) {
                        this.getData(true);
                        this.successNotice(message || '恢复成功！');
                      } else {
                        this.errorNotice(message || '恢复失败！');
                      }
                    });
                }
              });
            }
            return actions;
          }
        }
      ],
      selectedOrders: [],
      extraSelectionCount: 0,
      checkedAll:false,
      allIds: '',
      batchEditModal: {
        show: false,
        title: '批量编辑供应商功能设置',
        formData: {
          purchaseAptitude: ['is_not_update'],
          manageGoods: ['is_not_update'],
          show_info_config_type: -1,
          is_open_sale_report: -1,
          provider_type: -1,
          tpl_info_config_pick: -1,
          tpl_info_config_order: -1,
          show_info_config: [],
          is_open_sort: -1,
          providerSortStatus: false,
          purchase_task_gen_order_mode: -1
        },
      },
      commodity_power: {},
      pickTemplate: [],
      orderTemplate: [],
      batchEditLoading: false,
      proCol: []
    };
  },
  computed: {
    isShowManageEditGoods() {
      return this.batchEditModal.formData.manageGoods.includes('is_open_provider_power');
    },
  },
  watch: {
    sysConfig: {
      deep: true,
      handler() {
        this.setColumns();
      }
    }
  },
  activated () {
    this.setFilterItems();
    this.setColumns();
    this.getData(false);
  },
  mounted() {
    this.setFilterItems();
    this.setColumns();
    this.getPrintTemplateData()
  },
  methods: {
    onNoManageGoodsChange(e) {
      console.log(1, e);
    },
    handleSortKeyChange (sortKey, e) {
      console.log(sortKey, e, this.sort, 'ioio')
      Object.keys(this.sort).map(key => {
        this.sort[key] = sortKey === key ? e : 0
      })
      console.log(this.sort, 'kkk')
    },
    afterRequest(list) {
      StorageUtil.setLocalStorage('provider-list-sort-filters', this.sort)
      return list
    },
    /**
     * @param{obj} val 请求参数
     * 处理多次请求
     */
    beforeRequest (val) {
      this.params = val;
      if (!this.canRequest) {
        return false;
      }
      let sortMap = ['', 'desc', 'asc'];
      if (+this.sort.create_time) {
        val.sort_type = sortMap[this.sort.create_time];
        val.sort_field = 'create_time';
      }
      if (+this.sort.supplier_code) {
        val.sort_field = 'provider_code';
        val.sort_type = sortMap[this.sort.supplier_code];
      }
      this.canRequest = false;
      setTimeout(() => {
        this.canRequest = true;
      }, 100);
      val.status = val.status || '';
      this.params = val;
      return val;
    },
    /**
     * @param{Boolean} importSuccess 文件是否上传成功 true or false
     */
    importBtnCallBack (importSuccess) {
      if (importSuccess) {
        this.successMessage('批量导入成功');
        this.getData(true);
      }
    },
    /**
     * 导出
     * getParams() {obj} 获取列表请求参数
     */
    async exportes () {
      let params = this.$refs.orderListTable.getParams();
      console.log(`/superAdmin/provider/export?provider_name=${params.provider_name}&account_status=${params.account_status}&provider_type=${params.provider_type || ''}&provider_customize_field_select_config_ids=${params.provider_customize_field_select_config_ids}`)
      location.href = `/superAdmin/provider/export?provider_name=${params.provider_name}&account_status=${params.account_status}&provider_type=${params.provider_type || ''}&provider_customize_field_select_config_ids=${params.provider_customize_field_select_config_ids}`;
    },
    /**
     * @param{function} resolve 会在组件内部直接使用 window.location.href = url； url必须是支持直接打开即可下载的地址
     */
    async getDownloadUrl (resolve) {
      let { data, status, message } = await get(
        // '/superAdmin/provider/template'
        api.providerTemplate
      );
      if (status) {
        resolve(data.url);
        this.$Notice.success({
          title: message || '下载成功!'
        });
      } else {
        this.$Notice.error({
          title: message
        });
      }
    },
    setFilterItems() {
      const hideKeys = [];
      if (!this.isOpenProviderInvoke) {
        hideKeys.push('account_status');
      }
      if (!this.isOpenProviderDeliver) {
        hideKeys.push('provider_type');
      }
      this.filterItems = this.filterItems.filter(item => !hideKeys.includes(item.key));
    },
    setColumns() {
      const hideKeys = [];
      if (!this.isOpenProviderInvoke) {
        hideKeys.push('account_status_text');
      }
      // 开启分账功能,显示网关uuid
      if (!this.isFundAllocationEnabled) {
        hideKeys.push('account_uuid');
      }
      const proCol = [].concat(this.proColTemp)
      this.proCol = proCol.filter(item => !hideKeys.includes(item.key));
    },
    /**
     * @param{boolean} val 是否重置分页
     */
    getData (val) {
      this.$refs.orderListTable.fetchData(val);
    },
    /**
     * @param{obj} row 点击当前行的数据
     */
    async removeItem (row) {
      let { status, message } = await post('/superAdmin/provider/delete', {
        id: row.id
      });
      if (status) {
        this.successMessage('删除成功!');
        this.getData();
      } else {
        this.errorMessage(message);
      }
      // let me = this;
      // this.$Modal.confirm({
      //   title: '提示',
      //   content: '<p>确定要删除本条记录吗？</p>',
      //   loading: true,
      //   onOk: () => {
      //     post('/superAdmin/provider/delete', { id: row.id }).then(res => {
      //       if (res.status) {
      //         // me.$Modal.remove();
      //         me.modalSuccess('删除成功!');
      //         me.getData();
      //       } else {
      //         me.modalError(res.message);
      //         me.$Modal.remove();
      //       }
      //     });
      //   }
      // });
    },
    /**
     * @param{obj} row 所点击的那行数据
     * @param{Number} flag 区分 编辑还是详情 1/2
     */
    operate (row, flag) {
      if (flag) {
        this.$router.push({
          path: '/purchase/provider/baseInfo',
          query: { id: row.id }
        });
      } else {
        this.$router.push({
          path: '/purchase/provider/baseInfoView',
          query: { id: row.id }
        });
      }
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    getPrintTemplateData() {
      this.$request.get(this.apiUrl.getPrintTemplate,{
        type: 'PICK',
      }).then((res)=>{
        let data = res.data.list
        if(data&&data.length>0){
          data = data.filter(item=>{
            return this.isNewTemplate(item)
          }).map(item => {
            return {
              value: item.id,
              label: item.name
            }
          })
        }
        this.pickTemplate = data || []
        console.log('template-pick', this.pickTemplate)
        this.pickTemplate.unshift(
          {
            value: -1,
            label: '不调整',
          },
          {
            value: '0',
            label: '清空设置模板'
          }
        )
      })
      .catch(() => false);
      this.$request.get(this.apiUrl.getPrintTemplate,{
        type: 'ORDER',
      }).then((res)=>{
        let data = res.data.list
        if(data&&data.length>0){
          data = data.filter(item=>{
            return this.isNewTemplate(item)
          }).map(item => {
            return {
              value: item.id,
              label: item.name
            }
          })
        }
        this.orderTemplate = data || []
        this.orderTemplate.unshift(
          {
            value: -1,
            label: '不调整',
          },
          {
            value: '0',
            label: '清空设置模板'
          }
        )
        console.log('template-order', this.orderTemplate)
      })
      .catch(() => false);
    },
    handleSelectionChange(selection, data, obj) {
      this.selectedOrders = selection;
      this.allIds = this.selectedOrders.map(item => item.id).join(',')
      if((obj&&obj.noall) || selection.length==0) {
        this.extraSelectionCount = 0
        this.checkedAll = false
      }
      console.log('selectedOrders-ids', this.allIds)
    },
    checkedChange() {
      this.checkedAll = !this.checkedAll
      this.$refs.orderListTable.$refs.table.handleToggleCheckAll(true)
      if(this.checkedAll){
        if(this.$refs.orderListTable.curPagination.total>this.$refs.orderListTable.curPagination.pageSize){
          this.extraSelectionCount = this.$refs.orderListTable.curPagination.total - this.$refs.orderListTable.curPagination.pageSize
          if(this.$refs.orderListTable.data.length < this.$refs.orderListTable.curPagination.pageSize){
            this.extraSelectionCount = this.$refs.orderListTable.curPagination.total - this.$refs.orderListTable.data.length
          }
          let params =  this.$refs.orderListTable.getParams()
          params = {
            ...params,
            ...this.filters,
          }
          this.$request.post(this.apiUrl.getProviderAllIds, params).then(res=>{
            this.allIds = res.data
          })
        }else{
          this.allIds = ''
          this.extraSelectionCount = 0
        }
      }else{
       this.allIds = ''
       this.extraSelectionCount = 0
      }
    },
    showBatchEditModal() {
      this.batchEditModal.show = true
    },
    hideBatchEditModal() {
      this.batchEditModal.show = false
    },
    resetBatchEditModal() {
      this.batchEditModal.formData.manageGoods = ['is_not_update']
      this.batchEditModal.formData.purchaseAptitude = ['is_not_update']
      this.batchEditModal.formData.show_info_config_type = -1
      this.batchEditModal.formData.is_open_sale_report = -1
      this.batchEditModal.formData.tpl_info_config_pick = -1
      this.batchEditModal.formData.tpl_info_config_order = -1
      this.batchEditModal.formData.show_info_config = []
      this.batchEditModal.formData.is_open_sort = -1
      this.batchEditModal.formData.providerSortStatus = false
      this.batchEditModal.formData.purchase_task_gen_order_mode = -1
    },
    authorityConfirm(value) {
      this.commodity_power = value
    },
    authorityCancel(value) {
      this.commodity_power = value
    },
    async batchEdit() {
      const removeEmptyKeys = (obj) => {
        if (typeof obj !== 'object' || obj === null) {
          return obj;
        }

        if (Array.isArray(obj)) {
          return obj.map(removeEmptyKeys);
        }

        return Object.fromEntries(
          Object.entries(obj)
            .filter(([_, value]) => {
              if (value === null || value === undefined || value === '' || value === -1 || (Array.isArray(value) && value.length === 0)) {
                return false;
              }
              if (typeof value === 'object') {
                return Object.keys(value).length > 0;
              }
              return true;
            })
            .map(([key, value]) => [key, removeEmptyKeys(value)])
        );
      }
      const isEmptyObject = (obj) => {
        return Object.keys(obj).length === 0;
      }
      let formData = this.batchEditModal.formData
      if (+formData.is_open_sort === 2) {
        formData.is_open_sort = formData.providerSortStatus ? '2' : '0'
      }
      delete formData.providerSortStatus
      formData = removeEmptyKeys(formData)
      console.log('formData', formData)

      const {purchaseAptitude=[],manageGoods=[], ...others} = formData
      // const {manageGoods=[], ...others} = formData
      if(isEmptyObject(others) && manageGoods.includes('is_not_update') && purchaseAptitude.includes('is_not_update')) {
      // if(isEmptyObject(others) && manageGoods.includes('is_not_update')) {
        // 未做更改 不调接口
        this.batchEditModal.show = false
        return
      }
      if (manageGoods.includes('is_not_update')) {
        delete formData.manageGoods;
        delete formData.is_open_provider_power;
        delete formData.is_open_add_commodity;
        delete formData.is_open_upload_commodity_trace;
      } else {
        formData.is_open_provider_power = manageGoods.includes('is_open_provider_power') ? 1 : 0;
        formData.is_open_add_commodity = manageGoods.includes('is_open_add_commodity') ? 1 : 0;
        formData.is_open_upload_commodity_trace = manageGoods.includes('is_open_upload_commodity_trace') ? 1 : 0;
      }
      if (purchaseAptitude.includes('is_not_update')) {
        delete formData.purchaseAptitude;
        delete formData.is_show_mall_qualification;
        delete formData.is_show_group_qualification;
        delete formData.is_show_purchase_qualification
        delete formData.is_show_qualification
      } else {
        formData.is_show_mall_qualification = purchaseAptitude.includes('is_show_mall_qualification') ? 1 : 0;
        formData.is_show_group_qualification = purchaseAptitude.includes('is_show_group_qualification') ? 1 : 0;
        formData.is_show_purchase_qualification = purchaseAptitude.includes('is_show_purchase_qualification') ? 1 : 0;
        const tempArr = [] //后端需要字符串
        if (formData.is_show_mall_qualification===1) tempArr.push(1)
        if (formData.is_show_group_qualification===1) tempArr.push(2)
        if (formData.is_show_purchase_qualification===1) tempArr.push(3)
        formData.is_show_qualification = tempArr.join(',') || ''
      }
      formData.show_info_config = formData.show_info_config && formData.show_info_config.join(',')
      if(formData.tpl_info_config_pick || formData.tpl_info_config_order) {
        formData.tpl_info_config = JSON.stringify({
          pick: formData.tpl_info_config_pick,
          order: formData.tpl_info_config_order
        })
      }
      delete formData.tpl_info_config_order
      delete formData.tpl_info_config_pick
      let params = {
        id: this.allIds,
        ...formData,
        commodity_power: JSON.stringify(this.commodity_power)
      }
      console.log('batchEdit-params', params)
      this.batchEditLoading = true
      const { status, message } = await this.$request.post(this.apiUrl.batchUpdateProvider, params)
      if (status) {
        this.successMessage('批量编辑成功')
        this.batchEditModal.show = false
        this.resetBatchEditModal()
        this.$refs.authorityModalRef.init()
        this.getData()
      } else {
        this.errorMessage(message || '批量编辑失败')
      }
      this.batchEditLoading = false
    },
    showAuthorityModal() {
      this.$refs.authorityModalRef.show()
    },
  }
};
</script>

<style lang="scss" scoped>
.vt {
  vertical-align: top;
}
.headerContent {
  display: inline-block;
  align-items: center;
  width: 3px;
  height: 10px;
  background-color: black;
}
.items {
  display: flex;
  justify-content: flex-start;
}
.common__operation {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .slot-right {
    color: rgba(0, 0, 0, 0.85);
  }
}
.checked_button {
	margin-right: 10px;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
  cursor: pointer;
  color: #fff;
  background: #03ac54;
  padding: 0 5px;
  border-radius: 3px;
}
</style>
