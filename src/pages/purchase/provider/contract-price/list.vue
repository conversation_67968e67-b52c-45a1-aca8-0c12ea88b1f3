<!--
 * @Description:
 * @Autor: lizi
 * @Date: 2021-11-03 09:48:40
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-09 15:21:31
 * @FilePath: /sdpbase-pro/src/pages/purchase/provider/contract-price/list.vue
-->
<template>
  <div>
    <div class="mt24"></div>
    <Alert
      type="warning"
      show-icon
      class="out-of-date"
      closable
      v-show="expireList && expireList.length > 0"
    >
      <div slot="desc">
        有采购协议价
        <span
          class="orderNo"
          v-for="(expire, index) in expireList"
          @click="toOrderDetail(expire.id)"
          v-if="index == 0"
          :key="index"
          >{{ expire.agreement_no }}</span
        >
        <span
          class="orderNo"
          v-for="(expire, index) in expireList"
          @click="toOrderDetail(expire.id)"
          v-if="index > 0"
          :key="index"
          >，{{ expire.agreement_no }}</span
        >
        即将过期，请尽快处理！
      </div>
    </Alert>

    <ListTable
      ref="listTable"
      :border="false"
      :outer-border="true"
      :data-provider="apiUrl.providerContractPrice.list"
      :columns="columns"
      :filters="filters"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :before-request="beforeRequest"
      :height="getTableHeight() - 120"
      keepScroll
      @reset-change="handleResetChange"
      @on-selection-change="handleSelectionChange"
    >
      <div slot="button">
        <ExportButton
          type="default"
          slot="button"
          text="导出"
          :offline="true"
          :param-getter="getExportParams"
          api="/superAdmin/purchaseAgreementPrice/exportDetailList"
        ></ExportButton>
      </div>
      <div slot="before-table" v-show="!selectedList.length">
        <Button
          v-if="hasAgreeEdit"
          @click="$_onAddNew"
          styleType="btnStyleForAdd"
          class="mr8"
          >新增</Button
        >
        <ImportButton
          v-if="hasAgreeEdit"
          title="导入采购协议价"
          offline
          :post="{
            url: this.apiUrl.providerContractPrice.offlineImport,
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xls', 'xlsx'],
          }"
          :download="{
            'before-download': (resolve) => this.downloadTemplate(resolve),
            text: '采购协议价导入模板',
          }"
          :modalProps="{
            width: 800,
          }"
          :data="{
            import_mode: importParams.import_mode,
          }"
          @on-completed="_handleImportSuccess"
        >
          <template v-slot:custom-area>
            <div class="import-explain">
              <h6>使用说明：</h6>
              <p>
                标识的字段导入模板中必须包含对应的列，非必要字段导入模板中可没有对用列.
              </p>
              <p>
                上传导入模板后将引导您设置模板列与订单字段的对应关系来匹配数据。您需要导入的模板列都必须设置对应的字段，否则将不会导入该列数据。
              </p>

              <br />
              <h6>注意事项：</h6>
              <p>1.导入文件仅支持xlsx格式，大小不超过2M，数据不超过2000行</p>
              <p>2.导入文件不能包含"合并单元格"，否则无法导入</p>
              <p>
                3.导入协议价必须填写开始时间以及结束时间，该字段显示的为供应商协议单的生效时间段
              </p>
              <p>4.供应商名称，必填，必须与供应商档案中的名称一致</p>
              <p>5.单位：必填，必须与商品档案中已存在单位保持一致</p>
              <p>6.协议价，必填，是指该供应商协议的价格</p>
            </div>
            <div class="mode-box">
              <div class="label">导入模式(商品):</div>
              <div class="ml12">
                <RadioGroup v-model="importParams.import_mode">
                  <Radio label="commodity_name">商品名称</Radio>
                  <Radio label="commodity_code">商品编码</Radio>
                </RadioGroup>
              </div>
            </div>
          </template>
          导入
        </ImportButton>
      </div>
      <div slot="batch-operation">
        <Button
          type="primary"
          confirm
          title="确定操作？"
          @click="handleBatchAudit(0)"
          placement="bottom-start"
          poptipClass="batch-audit-poptip"
          >批量审核</Button
        >
      </div>
    </ListTable>
    <BatchAuditModal :list="auditErrList" ref="batchAudit"></BatchAuditModal>
  </div>
</template>

<script>
import ListTable from '@components/list-table';
import goodsAutoCompleteSelect from '@components/common/goodsAutoComplete_new';
import { get } from '@api/request.js';
import Button from '@components/button';
import { MINE_TYPE } from '@/util/const';
import authority from '@/util/authority';
import systemConfig from '@/pages/settings/SystemConfig.vue';
import GroupFilter from '@/components/common/GroupFilter';
import BatchAuditModal from './components/BatchAuditModal.vue';
import ExportButton from '@components/common/export-btn';
import { operator } from '@/components/standard/sdp-filter-items';
const { hasAuthority } = authority;

export default {
  mixins: [systemConfig],
  components: {
    ListTable,
    Button,
    BatchAuditModal,
    ExportButton,
  },
  computed: {
    hasAgreeEdit() {
      return hasAuthority('purchase_provider_agree_edit');
    },
    openPlanDate() {
      return +this.sysConfig.purchase_agreement_price_mode !== 0;
    },
  },
  data() {
    return {
      filters: {
        commodity_id: '',
        start_time: '',
        end_time: '',
        effective_start_date: '',
        effective_end_date: '',
      },
      filterItems: [
        {
          checked: true,
          required: true,
          type: 'custom',
          hasType: 'GroupFilter',
          style: {
            width: '299px',
          },
          key: ['search_type', 'multi_search_value'],
          defaultValue: ['1', ''],
          props: {
            selectData: [
              {
                label: '创建时间',
                placeholder: '选择创建时间',
                value: '1',
                key: 'create_time',
              },
            ],
            clearable: true,
            type: 'datePicker',
          },
          component: GroupFilter,
          onChange: (value = '') => {
            this.filters.start_time = '';
            this.filters.end_time = '';
            this.filters.effective_start_date = '';
            this.filters.effective_end_date = '';
            const type = value[0];
            const startTime = value[1] || '';
            const endTime = value[2] || '';
            if (type === '1') {
              this.filters.start_time = startTime;
              this.filters.end_time = endTime;
            } else {
              this.filters.effective_start_date = startTime;
              this.filters.effective_end_date = endTime;
            }
            return { value, stop: false };
          },
        },
        {
          label: '搜索',
          key: 'search_value',
          type: 'Input',
          props: {
            placeholder: '请输入供应商名称/单号',
          },
        },
      ],
      advanceItems: [
        {
          items: [
            {
              label: '状态',
              key: 'status',
              type: 'Select',
              props: {
                placeholder: '选择状态',
              },
              data: [],
            },
            {
              label: '商品',
              key: 'commodity_search',
              type: 'custom',
              props: {
                placeholder: '请输入商品名/编码/条形码',
                on: {
                  'on-enter': (e) => {
                    this.filters.commodity_id =
                      typeof e === 'object' ? e.commodity_id : '';
                    this._fetchData();
                  },
                  'on-focus': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.listTable.setValue('commodity_search', '', true);
                  },
                  'on-clear': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.listTable.setValue(
                      'commodity_search',
                      '',
                      false,
                    );
                  },
                },
              },
              component: goodsAutoCompleteSelect,
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              label: '制单人',
              key: 'create_user',
              type: 'custom',
              component: operator,
              attrs: {
                valueKey: 'user_name'
              }
            },
          ],
        },
      ],
      columns: [
        {
          show: () => {
            return hasAuthority('purchase_provider_batch_audit');
          },
          align: 'left',
          fixed: 'left',
          type: 'selection',
          width: 36,
        },
        {
          title: '单号',
          key: 'agreement_no',
          fixed: 'left',
          width: 200,
          render: (h, params) => {
            const { agreement_no, id, isCanDetail, status_desc } = params.row;
            return h(
              'div',
              {
                class: {
                  highlight: isCanDetail,
                },
                on: {
                  click: () => {
                    // 不可以点击不高亮，点击无反应
                    if (!isCanDetail) return;
                    this.$router.push({
                      path: '/purchase/provider/contract-price-detail',
                      query: {
                        id,
                        status_desc,
                      },
                    });
                  },
                },
              },
              agreement_no,
            );
          },
        },
        {
          title: '供应商名称',
          key: 'provider_name',
          render: (h, params) => {
            const text = params.row.provider_name || '';
            if (!text) return '-';
            return h(
              'Tooltip',
              {
                props: {
                  transfer: true,
                  maxWidth: '200',
                },
              },
              [
                h('div', { class: 'line-clamp-2' }, text),
                h(
                  'div',
                  {
                    slot: 'content',
                  },
                  text,
                ),
              ],
            );
          },
        },
        {
          title: '开始时间',
          key: 'start_date',
          width: 105,
        },
        {
          title: '终止时间',
          key: 'end_date',
          width: 105,
        },
        {
          title: '制单人',
          key: 'create_user',
        },
        {
          title: '状态',
          key: 'status_desc',
          render: (h, params) => {
            const text = params.row.status_desc || '';
            return h(
              'div',
              {
                attrs: {
                  // 状态色值（已失效 / 已关闭：#606060） 待审核：#FF6E00
                  style: `color: ${text === '待审核' ? '#FF6E00' : '#606060'}`,
                },
              },
              text,
            );
          },
        },
        {
          title: '备注',
          key: 'remark',
          render: (h, params) => {
            const text = params.row.remark || '';
            if (!text) return '-';
            return h(
              'Tooltip',
              {
                props: {
                  transfer: true,
                  maxWidth: '200',
                },
              },
              [
                h('div', { class: 'line-clamp-2' }, text),
                h(
                  'div',
                  {
                    slot: 'content',
                  },
                  text,
                ),
              ],
            );
          },
        },
        {
          title: '创建时间',
          key: 'create_time',
          width: 160,
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 260,
          actions: (params) => {
            const { id, isCanEdit, isCanClose } = params.row;
            let actions = [];

            if (isCanEdit && this.hasAgreeEdit) {
              actions.push({
                name: '编辑',
                action: () => {
                  this.$router.push({
                    path: '/purchase/provider/contract-price-edit',
                    query: {
                      id,
                    },
                  });
                },
              });
            }

            if (this.hasAgreeEdit) {
              actions.push({
                name: '导出',
                action: () => {
                  this._reqExport(id);
                },
              });
            }

            if (this.hasAgreeEdit) {
              actions.push({
                name: '复制协议价',
                action: () => {
                  this.$router.push({
                    path: '/purchase/provider/contract-price-add',
                    query: {
                      copy_id: id,
                    },
                  });
                },
              });
            }

            if (isCanClose && this.hasAgreeEdit) {
              actions.push({
                name: '关闭',
                confirm: '确定关闭此协议单?',
                action: () => {
                  this._reqClose(id);
                },
              });
            }
            return actions;
          },
        },
      ],
      importParams: {
        import_mode: 'commodity_name',
      },
      MINE_TYPE: Object.freeze(MINE_TYPE),
      expireList: [],
      selectedList: [],
      auditErrList: [],
    };
  },
  created() {
    this.initFilters();
    this._reqGetExpireList();
    this._reqGetSearchConfig();
  },
  activated() {
    this._fetchData(false);
  },
  methods: {
    getExportParams() {
      const params = { ...this.$refs.listTable.getParams() };
      return params;
    },
    initFilters() {
      if (this.openPlanDate) {
        const groupComp = this.filterItems.find((item) =>
          item.key.includes('search_type'),
        );
        groupComp.props.selectData.push({
          label: '开始结束时间',
          placeholder: '选择开始结束时间',
          value: '2',
          key: 'search_value',
        });
      }
    },
    _reqGetExpireList() {
      this.$request
        .get(this.apiUrl.providerContractPrice.expireList)
        .then((res) => {
          let { status, data } = res;
          if (status) {
            this.expireList = data;
          } else {
            this.expireList = [];
          }
        });
    },
    _reqGetSearchConfig() {
      this.$request
        .get(this.apiUrl.providerContractPrice.searchConfig)
        .then((res) => {
          let { status, data } = res;
          if (status) {
            const list = data.status.map((item) => {
              return {
                label: item.name,
                value: item.id,
              };
            });
            this.advanceItems[0].items[0].data = Object.freeze(list);
          }
        });
    },
    /**
     * @description: 关闭协议单
     * @param {*} _id
     * @return {*}
     * @author: lizi
     */
    _reqClose(_id) {
      get(this.apiUrl.providerContractPrice.close, { id: _id }).then((res) => {
        let { status, message } = res;
        if (status) {
          this.successMessage('关闭成功');
          this._fetchData();
        } else {
          this.errorMessage(message);
        }
      });
    },
    _fetchData(resetPage) {
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage);
    },
    /**
     * @description: 导出协议单
     * @param {*} _id
     * @return {*}
     * @author: lizi
     */
    _reqExport(_id) {
      get(this.apiUrl.providerContractPrice.exportDetail, { id: _id }).then(
        (res) => {
          let { status, message, data } = res;
          if (status) {
            let url = data.url || data;
            location.href = url;
          } else {
            this.errorMessage(message);
          }
        },
      );
    },
    $_onAddNew() {
      this.router.push('/purchase/provider/contract-price-add');
    },
    downloadTemplate(resolve) {
      this.$request
        .get(this.apiUrl.providerContractPrice.downloadImportTemplate)
        .then((res) => {
          let { status, data } = res;
          if (status) {
            resolve(data);
          } else {
            this.modalError('下载模版失败', 0);
          }
        });
    },
    _handleImportSuccess(isSuccess) {
      if (isSuccess) {
        this._fetchData(true);
      }
    },
    beforeRequest(params) {
      if (params.commodity_id) params.commodity_search = '';
      delete params.search_type;
      delete params['search_type,multi_search_value'];
      return params;
    },
    handleResetChange() {
      this.filters.commodity_id = '';
      this.filters.start_time = '';
      this.filters.end_time = '';
      this.filters.effective_start_date = '';
      this.filters.effective_end_date = '';
    },
    handleSelectionChange(selection) {
      this.selectedList = selection;
    },
    handleBatchAudit(index) {
      const selectedItem = this.selectedList[index] || {};
      if (index === 0) {
        this.auditErrList = [];
        this.$Message.loading({
          content: '加载中...',
          duration: 0,
        });
      }

      this.$request
        .post(this.apiUrl.providerContractPrice.audit, {
          id: selectedItem.id,
        })
        .then((res) => {
          if (res.status == 0) {
            this.auditErrList.push({
              agreement_no: selectedItem.agreement_no,
              reason: res.message,
            });
          }
        })
        .finally(() => {
          if (index === this.selectedList.length - 1) {
            this._fetchData();
            this.$Message.destroy();
            if (this.auditErrList.length) {
              this.$refs.batchAudit.open();
              return;
            }
            this.successMessage('审核成功');
            return;
          }
          this.handleBatchAudit(index + 1);
        });
    },
  },
};
</script>

<style lang="less" scoped>
.out-of-date {
  position: fixed;
  z-index: 999;
  top: 0;
  max-width: 50%;
  left: 50%;
  transform: translateX(-50%);
}
.orderNo {
  cursor: pointer;
  color: red;
}
.mode-box {
  display: flex;
  align-items: center;
  .label {
    width: 112px;
    text-align: right;
  }
}
.import-explain {
  padding: 12px 20px 16px 20px;
  margin: 20px 24px;
  background: #f5f6f8;
  color: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  font-size: 12px;
  h6 {
    color: rgba(0, 0, 0, 0.7);
    font-weight: 400;
    margin-bottom: 2px;
    line-height: 16px;
  }
  p {
    padding-top: 2px;
  }
}
.line-clamp-2 {
  display: -webkit-box;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
/deep/ .table__selection-panel {
  padding: 8px 20px;
  .table__selection-panel__selected {
    margin-left: 24px;
  }
  .ivu-btn {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
  }
}
</style>
<style lang="less">
.batch-audit-poptip {
  .ivu-poptip-arrow {
    display: none;
  }
  .sdp-button__poptip__title {
    text-align: left;
    justify-content: flex-start;
    margin-left: 5px;
  }
  .sdp-button__poptip__button__area {
    justify-content: center;
  }
}
</style>
