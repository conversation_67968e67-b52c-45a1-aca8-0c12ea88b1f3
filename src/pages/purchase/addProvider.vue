<template>
  <div class="provider_add">
    <DetailPage
      pageType="add"
      :title="id ? '编辑供应商' : '新增供应商'"
      :disabledSave="saveLoading"
      @on-save="save('form')"
    >
      <Tabs v-model="currentTab">
        <TabPane name="base" label="基础信息"> </TabPane>
        <TabPane
          name="in"
          label="进件信息"
          leave="切换后可能丢失数据，请确认保存后切换，是否切换？"
          v-if="isOpenProviderInvoke && id"
        ></TabPane>
      </Tabs>
      <Form
        ref="form"
        :label-width="78"
        :model="addParams"
        :rules="formRules"
        class="form_flex"
        v-show="currentTab === 'base'"
      >
        <base-block title="基础信息" class="base-info">
          <div class="custom_form">
            <div class="custom_form_item provider-name">
              <FormItem label="名称：" prop="name">
                <Input
                  ref="name"
                  v-model="addParams.name"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForName', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForName', true)
                  "
                  :show-word-limit="showWordLimitForName"
                  :maxlength="32"
                  type="textarea"
                  :rows="2"
                  style="width: 232px;"
                  placeholder="限32个字符"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="编码：" prop="provider_code">
                <Input
                  v-model="addParams.provider_code"
                  :show-word-limit="true"
                  :maxlength="30"
                  placeholder="限30个字符"
                  style="width: 232px"
                ></Input>
              </FormItem>
            </div>
          </div>
        </base-block>

        <base-block title="联系信息" class="base-info">
          <div class="custom_form">
            <div class="custom_form_item">
              <FormItem label="联系人：" prop="company_name">
                <Input
                  ref="showWordLimitForCompany"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForCompany', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForCompany', true)
                  "
                  :show-word-limit="showWordLimitForCompany"
                  :maxlength="30"
                  v-model="addParams.company_name"
                  type="text"
                  style="width: 232px"
                  placeholder="限30个字符"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="联系手机：" prop="account">
                <Input
                  ref="account"
                  v-model="addParams.account"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForAccount', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForAccount', true)
                  "
                  :show-word-limit="showWordLimitForAccount"
                  :maxlength="11"
                  type="text"
                  style="width: 232px"
                  placeholder="请输入11位手机号"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="电话：">
                <Input
                  v-model="addParams.tel2"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForTel2', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForTel2', true)
                  "
                  :show-word-limit="showWordLimitForTel2"
                  :maxlength="12"
                  type="text"
                  style="width: 232px"
                  placeholder="请输入座机号"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="详细地址：">
                <Input
                  v-model="addParams.address_detail"
                  @on-blur="
                    toggleWordLimitForAccount(
                      'showWordLimitForAddressDetail',
                      false,
                    )
                  "
                  @on-focus="
                    toggleWordLimitForAccount(
                      'showWordLimitForAddressDetail',
                      true,
                    )
                  "
                  :show-word-limit="showWordLimitForAddressDetail"
                  :maxlength="50"
                  placeholder="请输入地址"
                  type="text"
                  style="width: 232px"
                ></Input>
              </FormItem>
            </div>
          </div>
        </base-block>

        <base-block title="账号信息" class="base-info">
          <div class="custom_form">
            <div class="custom_form_item">
              <FormItem prop="tel" label="登录账号：">
                <Input
                  ref="tel"
                  v-model="addParams.tel"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForTel', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForTel', true)
                  "
                  :show-word-limit="showWordLimitForTel"
                  :maxlength="11"
                  type="text"
                  style="width: 232px"
                  placeholder="设置供应商登录账号"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <!-- @on-blur="
          toggleWordLimitForAccount('showWordLimitForPassword', false)
        "
        @on-focus="
          toggleWordLimitForAccount('showWordLimitForPassword', true)
        " -->
              <FormItem :key="id + 2" label="密码：" v-if="id">
                <Input
                  ref="password"
                  type="text"
                  v-model="addParams.password"
                  :show-word-limit="showWordLimitForPassword"
                  :maxlength="64"
                  style="width: 232px"
                  placeholder="设置登录密码"
                ></Input>
              </FormItem>
              <FormItem
                v-else
                key="************"
                prop="password"
                label="密码："
              >
                <Input
                  ref="password"
                  type="text"
                  v-model="addParams.password"
                  :show-word-limit="showWordLimitForPassword"
                  :maxlength="64"
                  style="width: 232px"
                  placeholder="设置登录密码"
                ></Input>
              </FormItem>
            </div>
          </div>
        </base-block>

        <base-block title="功能设置" class="base-info">
          <FormItem :label-width="95" v-if="openFloatRate" label="上浮率：">
            <div style="display: flex; align-items: center">
              <InputNumber
                :min="0"
                :step="0"
                :max="100"
                placeholder=""
                v-model="addParams.float_rate"
                style="width: 232px"
              />
              <span class="remark_detail"
                >上浮率可在协议价中快速计算协议折扣率，为该供应商默认供货商品进行调价</span
              >
            </div>
          </FormItem>
          <FormItem :label-width="95" label="客户信息显示：">
            <div>
              <RadioGroup v-model="addParams.show_info_config_type">
                <Radio :label="0">与业务配置一致</Radio>
                <Radio :label="1">独立配置</Radio>
              </RadioGroup>
            </div>
            <CheckboxGroup
              v-if="addParams.show_info_config_type == 1"
              v-model="addParams.showInfoConfig"
            >
              <Checkbox label="1">客户名称</Checkbox>
              <Checkbox label="4">联系人</Checkbox>
              <Checkbox label="2">客户地址</Checkbox>
              <Checkbox label="3">客户电话</Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem :label-width="95" label="自主管理商品：">
            <CheckboxGroup v-model="addParams.manageGoods">
              <Checkbox label="is_open_provider_power"
                >编辑商品<Button
                  style="margin-left: 10px"
                  v-show="isShowManageEditGoods"
                  @click="showAuthorityModal"
                  >修改权限</Button
                ></Checkbox
              >
              <Checkbox label="is_open_add_commodity">新增商品</Checkbox>
              <Checkbox label="is_open_upload_commodity_trace" v-if="openCommodityTrace">上传溯源报告</Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem :label-width="95" label="供货权限：">
            <CheckboxGroup v-model="addParams.providerPower">
              <Checkbox label="is_open_order_return">
                退货退款
                <Tooltip
                  placement="right"
                  content="开启供应商拆单时，供应商可以在供应商后台-供货中对客户发起的退货退款单进行审核操作"
                  transfer
                  max-width="400"
                >
                  <i class="icon-help1 iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
              <Checkbox label="is_open_order_modify">
                实收变更
                <Tooltip
                  placement="right"
                  content="开启供应商拆单时，供应商可以在供应商后台-供货中对客户发起的实收变更单进行编辑和审核操作"
                  transfer
                  max-width="400"
                >
                  <i class="icon-help1 iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem :label-width="95" label="采购单拆单方式：" v-if="sysConfig.is_open_purchase_task == 1">
            <RadioGroup v-model="addParams.purchase_task_gen_order_mode">
              <Radio label="0">与业务配置一致</Radio>
              <Radio label="1">按供应商</Radio>
              <Radio label="3">按客户</Radio>
              <Radio label="2">按订单号</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem :label-width="95" label="客户对账结算：">
            <RadioGroup v-model="addParams.account_bill_flag">
              <Radio label="1">不允许</Radio>
              <Radio label="2">仅允许对账</Radio>
              <Radio label="3">仅允许结算</Radio>
              <Radio label="4">允许对账结算</Radio>
            </RadioGroup>
            <span class="remark_detail"
              >开启供应商拆单时，供应商可以在供应商后台-客户对账单中的订单进行对应对账结算操作。</span
            >
          </FormItem>
          <FormItem
            :label-width="95"
            v-if="isOpenProviderDeliver"
            label="联营供应商："
            prop="providerType"
          >
            <Switch
              v-model="addParams.providerType"
              :disabled="providerTypeCantEdit"
            />
            <span class="remark_detail">开启后不可关闭</span>
            <!-- <Tooltip max-width="500" placement="right">
              <div slot="content">
                <p>开启后不可关闭，开启后：</p>
                <p>界面：</p>
                <p>采购任务：隐藏按量分配</p>
                <p>增加【分拣端隐藏联营供应商商品】开关</p>
                <p>业务逻辑：</p>
                <p>
                  【联营供应商】定义：供应商基础资料中，打开【联营供应商】开关的供应商，开关关闭的，称为【非联营供应商】
                </p>
                <p>采购任务：计算库存仅对分配给采购员、【非联营供应商】生效</p>
                <p>通过采购任务生成采购单：</p>
                <p>
                  1.
                  同一个商品同时存在分配给【联营供应商】的采购单与分配给采购员或【非联营供应商】的采购单，分为两种类型的采购单，前一种称为：【联营供应商采购单】
                </p>
                <p>【联营供应商采购单】：</p>
                <p>
                  1.
                  订单商品数量与采购单待采商品数量同步；可新增的商品，需要重新通过采购任务关联
                </p>
                <p>2. 待采购数量、无法通过采购单修改；</p>
                <p>3. 供应商确认供货功能，无法修改采购量</p>
                <p>配送商发货出库的订单中订单商品与【联营供应商采购单】关联：</p>
                <p>
                  1. 【联营供应商采购单】，收货数量读取关联订单商品的发货数量合计
                </p>
                <p>2. 生成入库单：实际入库数量 = 0；生成出库单：实际出库数量 = 0</p>
                <p>3. 发货出库后，订单商品对应的采购单所有商品价格不可修改</p>
                <p>报表：</p>
                <p>
                  1. 供应商直送采购单，发货出库不取库房成本，取采购单单价
                  以便报表成本取订单对应的采购单成本，而非库存均价
                </p>
              </div>
                 <i
                  style="cursor: pointer"
                  class="iconfont common_help_icon icon-help"
                ></i>
            </Tooltip> -->
          </FormItem>
          <FormItem
            v-if="+sysConfig.is_open_purchase_task === 0"
            :label-width="95"
            label="查看销售报表："
          >
            <Switch
              v-model="addParams.is_open_sale_report"
              :trueValue="1"
              :falseValue="0"
            />
            <span class="remark_detail">开启后，可查看当前商品默认供应商为该供应商的销量</span>
          </FormItem>
          <FormItem
            :label-width="95"
            label="隐藏客户价格："
          >
            <Switch
              v-model="addParams.is_hide_user_order_price"
              trueValue="1"
              falseValue="0"
            />
            <span class="remark_detail"
              >开启后，该供应商端将不显示客户的单价和金额</span
            >
          </FormItem>
          <FormItem :label-width="95" label="分拣：">
            <RadioGroup v-model="addParams.is_open_sort">
              <Radio label="1">与业务配置一致</Radio>
              <Radio label="2">独立配置</Radio>
            </RadioGroup>
            <span
              v-show="+addParams.is_open_sort !== 1"
              style="vertical-align: -2px"
            >
              <Switch v-model="addParams.providerSortStatus" />
              <span class="remark_detail"
                >开启后，供应商可进行分拣/重置操作</span
              >
            </span>
          </FormItem>
          <FormItem :label-width="95" label="发货单模板：">
            <Select
              placeholder="请选择打印模板"
              filterable
              filter-by-label
              clearable
              v-model="addParams.seleted_tpl_order"
              style="width: 232px"
            >
              <Option
                v-for="model in orderTemplate"
                :value="model.id"
                :key="model.id"
                :label="model.name"
                ><SIcon
                  class="icon--new"
                  icon="xin"
                  v-if="isNewTemplate(model)"
                />{{ model.name }}</Option
              >
            </Select>
            <span class="remark_detail">优先级高于打印配置页面选择的模板</span>
          </FormItem>
          <FormItem :label-width="95" label="分拣单模板：">
            <Select
              filterable
              clearable
              placeholder="请选择打印模板"
              v-model="addParams.seleted_tpl_pick"
              style="width: 232px"
            >
              <Option
                v-for="model in pickTemplate"
                :value="model.id"
                :key="model.id"
                ><SIcon
                  class="icon--new"
                  icon="xin"
                  v-if="isNewTemplate(model)"
                />{{ model.name }}</Option
              >
            </Select>
            <span class="remark_detail">优先级高于打印配置页面选择的模板</span>
          </FormItem>
          <FormItem
            :label-width="95"
            label="客户对账单模板："
            v-if="isOpenSplitOrderLockProvider"
          >
            <Select
              filterable
              clearable
              placeholder="请选择打印模板"
              v-model="addParams.seleted_tpl_soa"
              style="width: 232px"
            >
              <Option
                v-for="model in soaTemplate"
                :value="model.id"
                :key="model.id"
                ><SIcon
                  class="icon--new"
                  icon="xin"
                  v-if="isNewTemplate(model)"
                />{{ model.name }}</Option
              >
            </Select>
            <span class="remark_detail">优先级高于打印配置页面选择的模板</span>
          </FormItem>
        </base-block>

        <base-block title="财务信息" class="base-info">
          <div class="custom_form">
            <div class="custom_form_item">
              <FormItem label="开户名称：">
                <Input
                  v-model="addParams.bank_name"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForBankName', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForBankName', true)
                  "
                  :show-word-limit="showWordLimitForBankName"
                  :maxlength="30"
                  type="text"
                  style="width: 232px"
                  placeholder="限30个字符"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="开户银行：">
                <Input
                  v-model="addParams.bank"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForBank', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForBank', true)
                  "
                  :show-word-limit="showWordLimitForBank"
                  :maxlength="30"
                  type="text"
                  style="width: 232px"
                  placeholder="限30个字符"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="银行账号：">
                <Input
                  v-model="addParams.bank_account"
                  @on-blur="
                    toggleWordLimitForAccount(
                      'showWordLimitForBankAccount',
                      false,
                    )
                  "
                  @on-focus="
                    toggleWordLimitForAccount(
                      'showWordLimitForBankAccount',
                      true,
                    )
                  "
                  :show-word-limit="showWordLimitForBankAccount"
                  :maxlength="50"
                  type="text"
                  placeholder="请输入"
                  style="width: 232px"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="发票抬头：">
                <Input
                  v-model="addParams.invoice_title"
                  @on-blur="
                    toggleWordLimitForAccount('showWordLimitForInvoice', false)
                  "
                  @on-focus="
                    toggleWordLimitForAccount('showWordLimitForInvoice', true)
                  "
                  :show-word-limit="showWordLimitForInvoice"
                  :maxlength="32"
                  type="text"
                  style="width: 232px"
                  placeholder="限32个字符"
                ></Input>
              </FormItem>
            </div>
            <div class="custom_form_item">
              <FormItem label="统一社会信用代码：">
                <Input
                  v-model="addParams.invoice_number"
                  @on-blur="
                    toggleWordLimitForAccount(
                      'showWordLimitForInvoiceNumber',
                      false,
                    )
                  "
                  @on-focus="
                    toggleWordLimitForAccount(
                      'showWordLimitForInvoiceNumber',
                      true,
                    )
                  "
                  :show-word-limit="showWordLimitForInvoiceNumber"
                  :maxlength="30"
                  type="text"
                  style="width: 232px"
                  placeholder="限30个字符"
                ></Input>
              </FormItem>
            </div>
          </div>
        </base-block>
        <base-block title="文件上传" class="base-info">
          <FormItem :label-width="95" label="资质范围：">
          <!-- <FormItem :label-width="95" label="商城展示资质：">
            <Switch
              v-model="addParams.is_show_mall_qualification"
              trueValue="1"
              falseValue="0"
            /> -->
            <CheckboxGroup v-model="addParams.purchaseAptitude">
              <Checkbox label="is_show_mall_qualification">
                商城
                <Tooltip
                  placement="right"
                  content="勾选后，可在商城商品详情内展示商品默认供应商的资质内容"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
              <Checkbox label="is_show_group_qualification">
                集团后台
                <Tooltip
                  placement="right"
                  content="勾选后，可在集团后台展示该供应商的资质内容"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
              <Checkbox label="is_show_purchase_qualification">
                供应商后台（可编辑）
                <Tooltip
                  placement="right"
                  content="勾选后，供应商后台展示自己的资质内容，可编辑"
                  transfer
                  max-width="400"
                >
                  <i class="icon-tips iconfont icontip"></i>
                </Tooltip>
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <p class="admin-title2">
            资质信息
            <span class="remark_detail"
              >（
              支持上传图片和PDF，图片尺寸建议800*800px，支持JPG、PNG、JPEG格式。文件大小≤3M，最多上传50个文件
              ）</span
            >
          </p>

          <div
            style="padding: 10px 0; display: flex; flex-wrap: wrap"
            class="img-upload-list normal-img-list file-line-list"
          >
            <div
              v-for="(item, index) in credentialInfo"
              v-show="+item.is_delete !== 1"
              :key="item.file_url"
              style="position: relative"
              class="dib file-line"
            >
              <div class="file-icon">
                <img
                  class="pdf-icon"
                  v-if="isPDF(item.file_url)"
                  src="@/assets/images/provider/pdf_icon.png"
                  alt=""
                />
                <div class="img-con" v-else>
                  <img :src="item.file_url" alt="" />
                </div>
                <div class="file-cover">
                  <i
                    class="delete sui-icon icon-solid-close"
                    @click="removeFile(item, index)"
                  />
                  <i
                    class="icon-img-view sui-icon view-icon"
                    @click="previewFile(item.file_url, index)"
                  />
                </div>
              </div>
              <div class="file-info">
                <div class="name-info">
                  <p>{{ item.file_name }}</p>
                  <p class="info-op">
                    <span @click="downloadFile(item)">下载</span>
                    <span class="split"></span>
                    <span @click="renameFile(item)">重命名</span>
                  </p>
                </div>
                <div class="date-info">
                  <div class="info-title">到期时间:</div>
                  <DatePicker type="daterange" v-model="item.date"></DatePicker>
                </div>
              </div>
            </div>
            <UploadForAliyun
              v-if="credentialInfo.length < 50"
              :show-upload-list="false"
              @on-success="uploadSuccess"
              :format="['jpg', 'jpeg', 'png', 'pdf']"
              accept=".jpg,.jpeg,.png,.pdf"
              :max-size="51200"
              :on-format-error="formatImgError"
              :on-exceeded-size="maxSize"
              :maxCount="50"
              multiple
              type="drag"
              style="display: block; width: 90px"
            >
              <div class="change-upload-type">
                <div class="load_box">
                  <i
                    class="iconfont icon-add load_img"
                    style="color: #909090; font-size: 20px"
                  ></i>
                  <span class="load_text"> 上传</span>
                </div>
              </div>
            </UploadForAliyun>
          </div>
        </base-block>

        <base-block title="商城展示" class="base-info">
          <FormItem label="商城展示：">
            <Switch
              v-model="addParams.is_show_mall"
              :true-value="1"
              :false-value="0"
            />
            <!-- <div style="display: flex; align-items: center">

              <RadioGroup v-model="addParams.is_show_mall">
                <Radio :label="0">不展示</Radio>
                <Radio :label="1">展示</Radio>
              </RadioGroup>
              <Tooltip content="展示在商城端供应商列表" placement="right">
                <i
                  style="cursor: pointer"
                  class="iconfont common_help_icon icon-help"
                ></i>
              </Tooltip>
            </div> -->
          </FormItem>
          <FormItem v-if="addParams.is_show_mall == 1" label="排序号：">
            <div style="display: flex; align-items: center">
              <InputNumber
                :min="0"
                :step="0"
                :max="999999999999"
                placeholder="0"
                v-model="addParams.mall_show_sort_key"
                style="width: 232px"
              />
              <!-- <Tooltip content="数值越小排序越靠前" placement="right">
               <i class="icon-help1 iconfont icontip"></i>
              </Tooltip> -->
              <span class="remark_detail">数值越小排序越靠前</span>
            </div>
          </FormItem>

          <div
            v-if="addParams.is_show_mall == 1"
            class="provider-avatar"
            style="padding-bottom: 15px"
          >
            <Row type="flex" align="middle" :gutter="10">
              <Col>
                <div class="picture_title">
                  <div class="picture_show">列表展示：</div>
                </div>
                <div class="flex_end img-upload-list normal-img-list">
                  <div v-show="addParams.icon" class="demo-upload-list">
                    <div class="img"><img :src="addParams.icon" /></div>
                    <div class="demo-upload-list-cover">
                      <i
                        class="delete sui-icon icon-solid-close"
                        @click="addParams.icon = ''"
                      />
                      <i
                        class="icon-img-view sui-icon view-icon"
                        @click="viewImage([addParams.icon], 0)"
                      />
                    </div>
                  </div>
                  <UploadForAliyun
                    v-show="!addParams.icon"
                    :show-upload-list="false"
                    @on-success="handleAvatarListSuccess"
                    :format="['jpg', 'jpeg', 'png']"
                    :max-size="10240"
                    :on-format-error="handleImageUploadFormatError"
                    :on-exceeded-size="avatarMaxSize"
                    multiple
                    type="drag"
                    action="/superAdmin/upload/uploadImg"
                  >
                    <div class="change-upload-type">
                      <div class="load_box">
                        <i
                          class="iconfont icon-add load_img"
                          style="color: #909090; font-size: 20px"
                        ></i>
                        <span class="load_text"> 上传</span>
                      </div>
                    </div>
                  </UploadForAliyun>
                  <div class="remark_detail">
                    <p>
                      商城中展示为一行两个则建议图片大小为440*720px或者相同比例图片
                    </p>
                    <p>
                      商城中展示为一行一个则建议图片大小为702*394px或者相同比例图片
                    </p>
                  </div>
                </div>
              </Col>
              <Col style="margin-left: 50px">
                <div class="picture_title">
                  <div class="picture_show">详情展示：</div>
                </div>
                <div class="flex_end img-upload-list normal-img-list">
                  <div v-show="addParams.logo" class="demo-upload-list">
                    <div class="img"><img :src="addParams.logo" /></div>
                    <div class="demo-upload-list-cover">
                      <i
                        class="delete sui-icon icon-solid-close"
                        @click="addParams.logo = ''"
                      />
                      <i
                        class="icon-img-view sui-icon view-icon"
                        @click="viewImage([addParams.logo], 0)"
                      />
                    </div>
                  </div>
                  <UploadForAliyun
                    v-show="!addParams.logo"
                    :show-upload-list="false"
                    @on-success="handleAvatarDetailSuccess"
                    :format="['jpg', 'jpeg', 'png']"
                    :max-size="10240"
                    :on-format-error="handleImageUploadFormatError"
                    :on-exceeded-size="avatarMaxSize"
                    multiple
                    type="drag"
                    action="/superAdmin/upload/uploadImg"
                  >
                    <div class="change-upload-type">
                      <div class="load_box">
                        <i
                          class="iconfont icon-add load_img"
                          style="color: #909090; font-size: 20px"
                        ></i>
                        <span class="load_text"> 上传</span>
                      </div>
                    </div>
                  </UploadForAliyun>
                  <span class="remark_detail">
                    建议图片大小为960*270px或者相同比例图片</span
                  >
                </div>
              </Col>
            </Row>
          </div>
        </base-block>

        <base-block
          title="其他信息"
          v-if="providerQrcode || providerPreQrcode || isOpenProviderCustom"
          class="base-info"
          style="padding-bottom: 100px"
        >
          <template v-if="isOpenProviderCustom">
            <Form>
              <FormItem
                v-for="item in customFieldForm"
                :key="item.name"
                :label="item.name + ':'"
              >
                <Input
                  maxlength="512"
                  show-word-limit
                  type="textarea"
                  :placeholder="`请输入,最多512个字符`"
                  v-model="item.value"
                  v-if="item.type == 1"
                ></Input>
                <Select
                  :filterable="false"
                  clearable
                  placeholder="请选择"
                  v-model="item.value"
                  v-else
                >
                  <Option
                    v-for="info in item.select_config"
                    :value="info.id"
                    :key="info.id">
                    {{ info.name }}
                  </Option>
                </Select>
              </FormItem>
            </Form>
          </template>
          <template v-if="providerPreQrcode">
            <div class="picture_title">
              <div class="picture_show">实时备货参考：</div>
            </div>
            <div class="flex_end" style="padding-bottom: 5px">
              <img
                style="border: 1px solid #d8d8d8"
                :src="providerPreQrcode"
                width="166"
              />
              <span class="remark_detail">
                （供应商可提前查看所负责供应商的商品下单数量）</span
              >
            </div>
          </template>
          <template v-if="providerQrcode">
            <div class="picture_title">
              <div class="picture_show">供应商二维码：</div>
            </div>
            <div class="flex_end" style="padding-bottom: 5px">
              <img style="border: 1px solid #d8d8d8" :src="providerQrcode" />
              <span class="remark_detail">
                （采购小程序在创建采购单时可以扫码匹配供应商）</span
              >
            </div>
          </template>
        </base-block>
      </Form>
      <intake
        ref="intake"
        :invoke-type="invokeType"
        :invoke-status="invokeStatus"
        :invoke-message="invokeMessage"
        :account-info="accountInfo"
        @change="onInvokeChange"
        v-show="currentTab === 'in'"
      ></intake>
      <template #button-between v-if="currentTab !== 'base'">
        <Poptip trigger="hover" title="">
          <Button>邀请供应商填写</Button>
          <div class="api" slot="content">
            <img
              style="width: 162px; height: 162px"
              src="https://base-oss.shudongpoo.com/static/superadmin-local/img/provider-mini-app.33cc19e..jpg"
            />
          </div>
        </Poptip>
      </template>
    </DetailPage>
    <AuthorityModal
      ref="authorityModalRef"
      :authorityObj="authorityObj"
      @confirm="authorityConfirm"
      @cancel="authorityCancel"
    />
  </div>
</template>

<script>
import settings from '@api/settings';
import SIcon from '@components/icon';
import AuthorityModal from './provider/components/authorityModal.vue';
import QRCode from 'qrcode';
import DetailPage from '@/components/detail-page/index.js';
import { get, post } from '@/api/request';
import ConfigMixin from '@/mixins/config';
import Tabs from '@components/tabs';
import Intake from './components/intake';
import DateUtil from '@/util/date';

export default {
  mixins: [ConfigMixin],
  components: { DetailPage, SIcon, AuthorityModal, Tabs, Intake },
  data() {
    return {
      currentTab: 'base', // base基础信息/in进件信息
      invokeType: 1, // 进件类型：企业/个人
      invokeStatus: 0, // 进件状态：0未提交、1进件成功、2进件失败
      invokeMessage: '', // 进件失败信息
      accountInfo: {}, // 进件信息
      customFieldForm: [],
      orderTemplate: [],
      pickTemplate: [],
      soaTemplate: [],
      providerTypeCantEdit: false,
      id: '',
      providerQrcode: '',
      providerPreQrcode: '',
      saveLoading: false,
      showWordLimitForAccount: true,
      showWordLimitForName: true,
      showWordLimitForCompany: true,
      showWordLimitForTel2: true,
      showWordLimitForAddressDetail: true,
      showWordLimitForBankName: true,
      showWordLimitForBank: true,
      showWordLimitForBankAccount: true,
      showWordLimitForInvoice: true,
      showWordLimitForInvoiceNumber: true,
      showWordLimitForTel: true,
      showWordLimitForPassword: true,
      imgList: [],
      credentialInfo: [],
      formRules: {
        name: [
          {
            required: true,
            message: '名称不能为空！',
            trigger: 'blur',
          },
        ],
        company_name: [
          {
            required: true,
            message: '联系人不能为空！',
            trigger: 'blur',
          },
        ],
        account: [
          {
            required: true,
            message: '手机号不能为空！',
            trigger: 'blur',
          },
        ],
        tel: [
          {
            required: true,
            message: '登录账号不能为空！',
            trigger: 'blur',
          },
        ],
        password: [
          {
            required: true,
            message: '密码不能为空！',
            trigger: 'blur',
          },
        ],
      },
      addParams: {
        purchaseAptitude: [],
        manageGoods: [],
        providerPower: [],
        float_rate: '',
        showInfoConfig: [],
        show_info_config_type: 0,
        is_show_mall: 1,
        mall_show_sort_key: 0,
        company_name: '',
        name: '',
        tel: '',
        tel2: '',
        address_detail: '',
        bank_name: '',
        bank: '',
        bank_account: '',
        invoice_title: '',
        invoice_number: '',
        account: '',
        password: '',
        providerType: false,
        provider_code: '',
        logo: '',
        icon: '',
        is_hide_user_order_price: '0',
        is_show_mall_qualification: '1',
        is_open_provider_power: '0',
        is_open_sort: '1',
        providerSortStatus: false,
        account_bill_flag: '2',
        purchase_task_gen_order_mode: '0'
      },
      authorityObj: {},
      commodity_power: {},
    };
  },
  computed: {
    isShowManageEditGoods() {
      return this.addParams.manageGoods.includes('is_open_provider_power');
    },
    isOpenProviderCustom() {
      return +this.sysConfig.is_open_provider_customize_field === 1;
    },
    isOpenSplitOrderLockProvider() {
      return +this.sysConfig.is_open_split_order_lock_provider === 1;
    },
  },
  async mounted() {
    this.getData();
    let id = this.$route.query.id;
    this.id = id;
    await this.getCustomizeField();
    this.addParams.company_name = '';
    this.getInfo(id);
    this.addParams.password = '';
  },
  watch: {
    addParams: {
      handler: function () {
        setTimeout(() => {
          this.addParams.account = this.addParams.account.replace(/\D/, '');
          this.addParams.tel2 = this.addParams.tel2.replace(/\D/, '');
          this.addParams.bank_account = this.addParams.bank_account.replace(
            /\D/,
            '',
          );
        });
      },
      deep: true,
    },
  },
  methods: {
    // 下载文件到本地
    downloadFile(item) {
      window.open(item.file_url);
    },
    // 重命名文件
    renameFile(item) {
      let inputFileName = item.file_name;
      this.$Modal.confirm({
        title: '重命名',
        render: (h) => {
          return h('Input', {
            props: {
              value: inputFileName,
            },
            on: {
              input: (val) => {
                inputFileName = val;
              },
            },
          });
        },
        onOk: () => {
          item.file_name = inputFileName;
          this.$Message.success('重命名成功');
        },
      });
    },
    async getCustomizeField() {
      const { status, data } = await this.$request.get(this.apiUrl.customizeFieldList, {
        customize_type: '7',
      });
      if (status) {
        this.customFieldForm = data.map(item => ({
          ...item,
          value: '',
        }));
      }
    },
    initData() {
      if (this.id) {
        delete this.formRules.password;
      }
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    getData() {
      this.$request
        .get(this.apiUrl.getPrintTemplate, {
          type: 'PICK',
        })
        .then((res) => {
          let data = res.data.list;
          if (data && data.length > 0) {
            data = data.filter((item) => {
              return this.isNewTemplate(item);
            });
          }
          this.pickTemplate = data || [];
        })
        .catch(() => false);
      this.$request
        .get(this.apiUrl.getPrintTemplate, {
          type: 'ORDER',
        })
        .then((res) => {
          let data = res.data.list;
          if (data && data.length > 0) {
            data = data.filter((item) => {
              return this.isNewTemplate(item);
            });
          }
          this.orderTemplate = data || [];
        })
        .catch(() => false);

      this.$request
        .get(this.apiUrl.getPrintTemplate, {
          type: 'SOA',
        })
        .then((res) => {
          let data = res.data.list;
          if (data && data.length > 0) {
            data = data.filter((item) => {
              return this.isNewTemplate(item);
            });
          }
          this.soaTemplate = data || [];
        });
    },
    genProviderQrcode(encrypt_code, prepare_reference_url) {
      let qrCodeOpts = {
        errorCorrectionLevel: 'H',
        type: 'image/jpeg',
        rendererOpts: {
          quality: 0.3,
          margin: 0,
        },
      };
      encrypt_code &&
        QRCode.toDataURL(encrypt_code, qrCodeOpts, (err, url) => {
          if (err) {
            console.log(err);
            return false;
          }
          this.providerQrcode = url;
        });
      prepare_reference_url &&
        QRCode.toDataURL(prepare_reference_url, qrCodeOpts, (err, url) => {
          if (err) {
            console.log(err);
            return false;
          }
          this.providerPreQrcode = url;
        });
    },
    getInfo(id) {
      if (!id) {
        return;
      }
      let me = this;
      get('/superAdmin/provider/detail', { id: id }).then((res) => {
        if (res.status) {
          const {
            account_type = 0,
            account_status = 0,
            account_message = '',
            account_info = {},
            ...other
          } = res.data;
          this.invokeType = +account_type;
          this.invokeStatus = +account_status;
          this.invokeMessage = account_message;
          this.accountInfo =
            account_info && Array.isArray(account_info) ? {} : account_info;
          this.accountInfo.period_date = [
            this.accountInfo.period_start,
            this.accountInfo.period_end,
          ].filter((item) => !!item);
          this.accountInfo.legal_identity_period = [
            this.accountInfo.legal_identity_period_start,
            this.accountInfo.legal_identity_period_end,
          ].filter((item) => !!item);
          this.accountInfo.legal_identity_period = [
            this.accountInfo.legal_identity_period_start,
            this.accountInfo.legal_identity_period_end,
          ].filter((item) => !!item);
          this.accountInfo.contract_period = [
            this.accountInfo.contract_period_start,
            this.accountInfo.contract_period_end,
          ].filter((item) => !!item);
          this.accountInfo.identity_period = [
            this.accountInfo.identity_period_start,
            this.accountInfo.identity_period_end,
          ].filter((item) => !!item);
          const qualification_certificate = this.accountInfo
            .qualification_certificate
            ? JSON.parse(this.accountInfo.qualification_certificate)
            : [];
          if (qualification_certificate.length) {
            qualification_certificate.forEach((item) => {
              if (item.code === 'license_front') {
                this.accountInfo.license_front = [item.url];
              }
              if (item.code === 'profit_sharing_contract') {
                this.accountInfo.profit_sharing_contract = [item.url];
              }
              if (item.code === 'open_account_front') {
                this.accountInfo.open_account_front = [item.url];
              }
              if (item.code === 'person_identity_front') {
                this.accountInfo.person_identity_front = [item.url];
              }
            });
            this.accountInfo.qualification_certificate =
              qualification_certificate;
          }
          this.accountInfo.business_area = [
            this.accountInfo.province_code,
            this.accountInfo.city_code,
          ].filter((item) => !!item);
          this.accountInfo.bank_area = [
            this.accountInfo.bank_province_code,
            this.accountInfo.bank_city_code,
          ].filter((item) => !!item);
          const manageGoods = [];
          const providerPower = [];
          const purchaseAptitude = [];
          if (+other.is_open_provider_power === 1) {
            manageGoods.push('is_open_provider_power');
          }
          if (+other.is_open_add_commodity === 1) {
            manageGoods.push('is_open_add_commodity');
          }
          if (+other.is_open_upload_commodity_trace === 1) {
            manageGoods.push('is_open_upload_commodity_trace');
          }
          if (+other.is_open_order_modify === 1) {
            providerPower.push('is_open_order_modify');
          }
          if (+other.is_open_order_return === 1) {
            providerPower.push('is_open_order_return');
          }
          if (+other.is_show_mall_qualification === 1) {
            purchaseAptitude.push('is_show_mall_qualification');
          }
          if (+other.is_show_group_qualification === 1) {
            purchaseAptitude.push('is_show_group_qualification');
          }
          if (+other.is_show_purchase_qualification === 1) {
            purchaseAptitude.push('is_show_purchase_qualification');
          }
          this.addParams = {
            ...other,
            manageGoods,
            providerPower,
            purchaseAptitude,
          };
          this.authorityObj = res.data.commodity_power;
          this.addParams.float_rate = res.data.float_rate || '';
          this.addParams.providerType = +res.data.provider_type === 2;
          this.addParams.mall_show_sort_key = +res.data.mall_show_sort_key || 0;
          this.addParams.is_show_mall = res.data.is_show_mall
            ? +res.data.is_show_mall
            : 1;
          this.providerTypeCantEdit = this.addParams.providerType;
          this.$set(
            this.addParams,
            'showInfoConfig',
            this.addParams.show_info_config
              ? this.addParams.show_info_config.split(',')
              : [],
          );
          this.addParams.seleted_tpl_order = this.addParams.tpl_info_config
            ? this.addParams.tpl_info_config.order
            : '';
          this.addParams.seleted_tpl_pick = this.addParams.tpl_info_config
            ? this.addParams.tpl_info_config.pick
            : '';
          this.addParams.seleted_tpl_soa = this.addParams.tpl_info_config
            ? this.addParams.tpl_info_config.soa
            : '';
          this.addParams.show_info_config_type =
            +res.data.show_info_config_type || 0;
          const sort = res.data.is_open_sort;
          this.addParams.is_open_sort = +sort !== 1 ? '2' : sort + '';
          this.addParams.providerSortStatus = +sort > 0;
          this.addParams.purchase_task_gen_order_mode = res.data.purchase_task_gen_order_mode
          if (this.addParams.customize_fields && this.addParams.customize_fields.length) {
            this.customFieldForm.forEach(item => {
              const field = this.addParams.customize_fields.find(child => child.id === item.id);
              if (field) {
                item.value = field.value;
              }
            });
          }
          if (res.data.credential_info) {
            this.credentialInfo = res.data.credential_info.map((item) => {
              return {
                id: item.id,
                file_url: item.file_url,
                file_name: item.file_name,
                date: [item.valid_start_date, item.valid_end_date],
              };
            });
          }
          this.$watch(
            'addParams',
            function () {
              this.modified = true;
            },
            { deep: true },
          );
          this.genProviderQrcode(
            this.addParams.encrypt_code,
            this.addParams.prepare_reference_url,
          );
        } else {
          // this.modalError(res.message);
          this.errorNotice({
            title: res.message,
          });
        }
      });
    },
    handleImageUploadFormatError() {},
    toggleWordLimitForAccount(key, value) {
      // this[key] = value;
    },
    // 校验必填项
    inputCheck() {
      let result = true;
      if (!this.addParams.name) {
        this.$refs.name.focus();
        result = false;
      } else if (!this.addParams.company_name) {
        this.$refs.showWordLimitForCompany.focus();
        result = false;
      } else if (!this.addParams.account) {
        this.$refs.account.focus();
        result = false;
      } else if (!this.addParams.tel) {
        this.$refs.tel.focus();
        result = false;
      } else if (!this.addParams.password && !this.id) {
        this.$refs.password.focus();
        result = false;
      }

      return result;
    },
    save(name) {
      if (this.currentTab === 'in') {
        this.onSaveIntake();
        return;
      }
      this.inputCheck();
      this.$refs[name].validate((valid) => {
        if (valid) {
          let credential_info = this.credentialInfo.map((item) => {
            const { date } = item;
            if (Array.isArray(date)) {
              item.valid_start_date = date[0]
                ? DateUtil.formatDate(date[0])
                : '';
              item.valid_end_date = date[0] ? DateUtil.formatDate(date[1]) : '';
            }
            return {
              id: item.id,
              is_delete: item.is_delete,
              file_url: item.file_url,
              file_name: item.file_name,
              valid_start_date: item.valid_start_date,
              valid_end_date: item.valid_end_date,
            };
          });
          this.addParams.is_open_provider_power =
            this.addParams.manageGoods.includes('is_open_provider_power')
              ? 1
              : 0;
          this.addParams.is_open_add_commodity =
            this.addParams.manageGoods.includes('is_open_add_commodity')
              ? 1
              : 0;

          this.addParams.is_open_upload_commodity_trace =
            this.addParams.manageGoods.includes('is_open_upload_commodity_trace')
              ? 1
              : 0;

          this.addParams.credential_info = JSON.stringify(credential_info);
          this.addParams.quaImg = credential_info
            .map((item) => {
              if (!this.isPDF(item.file_url) && !Number(item.is_delete)) {
                return item.file_url;
              }
            })
            .filter((it) => it)
            .join(',');
          this.addParams.is_open_order_modify =
            this.addParams.providerPower.includes('is_open_order_modify')
              ? 1
              : 0;
          this.addParams.is_open_order_return =
            this.addParams.providerPower.includes('is_open_order_return')
              ? 1
              : 0;
          const tempArr = [] //后端需要字符串
          this.addParams.is_show_mall_qualification =
            this.addParams.purchaseAptitude.includes('is_show_mall_qualification')
              ? 1
              : 0;
              if (this.addParams.is_show_mall_qualification===1) tempArr.push(1)
          this.addParams.is_show_group_qualification =
            this.addParams.purchaseAptitude.includes('is_show_group_qualification')
              ? 1
              : 0;
              if (this.addParams.is_show_group_qualification===1) tempArr.push(2)
          this.addParams.is_show_purchase_qualification =
            this.addParams.purchaseAptitude.includes('is_show_purchase_qualification')
              ? 1
              : 0;
              if (this.addParams.is_show_purchase_qualification===1) tempArr.push(3)
          this.addParams.is_show_qualification = tempArr.join(',') || ''

          this.addParams.provider_type = +this.addParams.providerType + 1;
          this.addParams.show_info_config =
            this.addParams.showInfoConfig.join(',');
          this.addParams.tpl_info_config = {
            order: this.addParams.seleted_tpl_order,
            pick: this.addParams.seleted_tpl_pick,
            soa: this.addParams.seleted_tpl_soa,
          };
          this.addParams.commodity_power = JSON.stringify(this.commodity_power);
          if (+this.addParams.is_open_sort !== 1) {
            this.addParams.is_open_sort = this.addParams.providerSortStatus
              ? '2'
              : '0';
          }
          // 将数组转成键值对的对象
          this.addParams.customize_fields = this.customFieldForm.map((item) => {
            return {
              id: item.id,
              value: item.value,
            };
          });
          this.addParams.customize_fields = JSON.stringify(
            this.addParams.customize_fields,
          );
          let params = JSON.parse(JSON.stringify(this.addParams));
          params.float_rate = params.float_rate || 0;
          post('/superAdmin/provider/update', params).then((res) => {
            if (res.status == 1) {
              this.successNotice({
                title: '保存成功',
                onClose: () => {},
              });
              this.$router.push('/purchase/provider');
            } else {
              this.errorNotice({
                title: '保存失败',
                desc: res.message,
              });
              // this.modalError(res.message);
            }
          });
        } else {
          // this.modalError("格式不正确！");
          this.errorNotice({
            title: '部分必填项未填写，请返回填写',
          });
        }
      });
    },
    // saveEdit(name) {
    //   this.$refs[name].validate((valid) => {
    //     if (valid) {
    //       let quaimg = this.imgList.map((item) => {
    //         return item.cdn_url;
    //       });
    //       this.addParams.quaImg = quaimg.toString();
    //       this.addParams.provider_type = +this.addParams.providerType + 1;
    //       post('/superAdmin/provider/update', this.addParams).then((res) => {
    //         if (res.status) {
    //           this.successNotice({
    //             title: '提示',
    //             desc: '修改成功',
    //             onClose: () => {},
    //           });
    //           this.modified = false;
    //           this.$router.push('/purchase/provider');
    //         } else {
    //           // this.modalError(res.message);
    //           this.errorNotice({
    //             title: res.message,
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    uploadSuccess(res, meta, file) {
      if (+res.status === 1) {
        let imgObj = {
          file_url: res.data.upyun_url,
          file_name: file.name,
        };
        this.credentialInfo.push(imgObj);
        // this.imgList.push(res.data.upyun_url);
      } else {
        this.errorNotice({
          title: '图片上传失败',
          desc: res.message || '',
        });
      }
    },
    formatImgError(file) {
      this.errorNotice({
        title: '图片上传失败',
        desc: '仅支持jpg，jpeg，png格式',
      });
      // this.$Notice.warning({
      //     title: "The file format is incorrect",
      //     desc:
      //         "File format of " +
      //         file.name +
      //         " is incorrect, please select jpg or png."
      // });
    },
    maxSize(file) {
      this.errorNotice({
        title: '图片上传失败',
        desc: '仅支持小于50M文件',
      });
      // this.$Notice.warning({
      //     title: "Exceeding file size limit",
      //     desc: "File  " + file.name + " is too large, no more than 6M."
      // });
    },
    previewFile(url, _viewIndex) {
      // 如果是pdf文件,直接打开pdf文件
      if (this.isPDF(url)) {
        window.open(url);
        return;
      }
      const images = this.credentialInfo.map((item) => item.file_url);
      this.viewImage(images, _viewIndex);
    },
    // 根据文件链接判断是否是PDF
    isPDF(url) {
      return /\.pdf$/.test(url);
    },
    removeFile(item, i) {
      if (this.id) {
        this.$set(item, 'is_delete', 1);
      } else {
        this.credentialInfo.splice(i, 1);
      }
    },
    handleAvatarListSuccess(res) {
      if (!res.status) {
        this.$Message.error(res.message);
        return;
      }
      this.addParams.icon = res.data.upyun_url;
    },
    handleAvatarDetailSuccess(res) {
      if (!res.status) {
        this.$Message.error(res.message);
        return;
      }
      this.addParams.logo = res.data.upyun_url;
    },
    avatarMaxSize() {
      this.$Message.error('图片大小超过10MB');
    },
    showAuthorityModal() {
      this.$refs.authorityModalRef.show();
    },
    authorityConfirm(value) {
      this.commodity_power = value;
      this.authorityObj = value;
    },
    authorityCancel(value) {
      this.commodity_power = value;
    },
    onInvokeChange(type) {
      this.invokeType = type;
      console.log('change', type);
    },
    onSaveIntake() {
      this.$refs.intake.getFormData((data) => {
        const params = {
          provider_id: this.id,
          account_type: this.invokeType,
          account_info: JSON.stringify(data),
        };
        this.$request
          .post(this.apiUrl.saveProviderAccountInfo, params)
          .then((res) => {
            if (res.status) {
              this.successNotice({
                title: '保存成功',
                onClose: () => {},
              });
              this.getInfo(this.id);
              // this.$router.push('/purchase/provider');
            } else {
              this.errorNotice({
                title: '保存失败',
                desc: res.message,
              });
            }
          });
      });
    },
  },

  beforeRouteLeave(to, from, next) {
    if (this.modified && !this.id) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function () {
          next();
        },
        onCancel: function () {
          next(false);
        },
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
};
</script>

<style scoped lang="scss">
.custom_form {
  display: flex;
  flex-wrap: wrap;
}
.picture_title {
  height: 30px;
  line-height: 30px;
  display: flex;
}
.picture_show {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.85);
}
/deep/ .ivu-form-item-content {
  line-height: 30px;
}
/deep/ .ivu-form-item-label {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  font-size: 13px;
  padding: 0;
  height: 30px;
  font-weight: 400;
  line-height: 15px;
  color: rgba(0, 0, 0, 0.85);
}
.custom_form_item {
  width: 360px;
}
.provider-name {
  /deep/ .ivu-input-word-count {
    right: -30px;
  }
}
.common_help_icon {
  margin-left: 10px;
}
.demo-upload-list {
  margin: 0 22px 0 0;
}
/deep/ .ivu-upload-drag {
  border: none;
}
#addProvider >>> .ivu-tooltip-inner {
  max-width: 500px;
}
.ivu-input-wrapper >>> input::-webkit-input-placeholder {
  color: #b9b9b9;
}

.ivu-input-wrapper >>> input:-moz-placeholder {
  color: #b9b9b9;
}

.ivu-input-wrapper >>> input::-moz-placeholder {
  color: #b9b9b9;
}

.ivu-input-wrapper >>> input:-ms-input-placeholder {
  color: #b9b9b9;
}
.icontip {
  cursor: pointer;
  font-size: 12px;
  margin-left: 4px;
}
.ivu-input-wrapper >>> .ivu-input {
  /* height: 34px; */
  padding-left: 12px;
  border: 1px solid #dedede;
  padding-right: 38px;
}
.ivu-input-wrapper >>> .ivu-input-word-count {
  padding-left: 0px;
}

#addProvider {
  background-color: #fff;
}

.provider-title {
  padding: 10px 0;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 400;
  border-bottom: 1px solid #eee;
}

.provider-title > p {
  padding: 0 10px;
  line-height: 1;
  border-left: 4px solid #03ac54;
}

.provider-title > p .provider-title_small {
  font-size: 13px;
  margin-left: 10px;
}

.provider-title > p .redStar {
  color: red;
}
.admin-title2 {
  height: 16px;
  font-size: 13px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.7);
  line-height: 16px;
}
.remark_detail {
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  text-indent: 1em;
  display: inline-block;
}
.icon--new {
  font-size: 16px;
  margin-top: -2px;
  margin-right: 5px;
  color: #ff9f00;
}
.base-info {
  /* overflow: hidden; */
}
.form_flex {
  display: flex;
  flex-direction: column;
}
.flex_end {
  display: flex;
  align-items: flex-end;
}
.flex_end.img-upload-list.normal-img-list .demo-upload-list {
  margin-right: 0;
}

.file-line-list {
  display: flex;
  flex-direction: column;
  font-size: 13px;
  gap: 15px;
  .file-line {
    display: flex;
    > .file-icon {
      width: 90px;
      height: 90px;
      line-height: 90px;
      margin-right: 19px;
      padding: 2px;
      border: 1px solid rgba(216, 216, 216, 0.8);
      border-radius: 2px;
      position: relative;
      > .img-con {
        width: 100%;
        overflow: hidden;
        height: 100%;
        > img {
          width: 100%;
          position: relative;
          top: 50%;
          transform: translateY(-50%);
          z-index: 1;
        }
      }

      .pdf-icon {
        width: 42px;
        margin: 0 auto;
        display: block;
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
      }
      > .file-cover {
        position: absolute;
        width: 100%;
        height: 100%;
        left: -1px;
        right: -1px;
        top: -1px;
        bottom: -1px;
        opacity: 0;
        z-index: 2;
        background: rgba(0, 0, 0, 0.3);
        .view-icon {
          position: absolute;
          right: 12px;
          bottom: 20px;
          color: #fff;
          font-size: 22px;
          cursor: pointer;
        }
      }
      &:hover {
        .file-cover {
          opacity: 1;
        }
      }
      .delete {
        position: absolute;
        top: -8px;
        right: -8px;
        color: #b2b2b2;
        background: #fff;
        border-radius: 50%;
        cursor: pointer;
        z-index: 255;
      }
    }
    > .file-info {
      position: relative;
      gap: 16px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      .name-info {
        font-weight: 400;
        font-size: 13px;
        color: rgba(0, 0, 0, 0.85);
        display: flex;
        align-items: center;
        .info-op {
          display: flex;
          margin-left: 20px;
          align-items: center;
          font-weight: 400;
          font-size: 13px;
          color: #03ac54;
          span {
            cursor: pointer;
          }
          .split {
            display: block;
            width: 1px;
            height: 13px;
            margin: 0 10px;
            background: #e9e9e9;
          }
        }
      }
      .date-info {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.75);
        .info-title {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
