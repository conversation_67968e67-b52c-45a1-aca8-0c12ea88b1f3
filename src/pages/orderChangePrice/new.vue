<template>
  <div class="list-container">
    <ListTable
      row-key="order_commodity_id"
      :pageSizeOpts="[20, 30, 40, 50, 100]"
      :advance="true"
      :showAdvance="true"
      :outer-border="true"
      :advance-items="advanceItems"
      data-provider="/superAdmin/orderChangePrice/pageEditPriceNeedData"
      :max-line="5"
      :columns="columns"
      :height="getTableHeight() - 76"
      :beforeRequest="beforeRequest"
      :afterRequest="afterRequest"
      @reset-change="resetChange"
      ref="list">
      <template #button>
        <Button @click="handleExport">{{ exporting ? '导出中...' : '导出' }}</Button>
      </template>
      <Row slot="before-table" :gutter="15">
        <Col style="flex: 1">
          <Button type="primary" @click="isAbleEditPrice" >{{ isEditPrice ? '取消修改价格' : '修改价格' }}</Button>
          <CommonUpload
            style="display: inline-block;"
            @on-success="handleImportSuccess"
            @on-error="handleImportError"
            :before-upload="beforeImport"
            :manual-handle-response="true"
            :data="exportParams"
            action="/superAdmin/orderChangePrice/ExcelChangePrice"
          >
            <Button class="ml10" :disabled="uploading">{{ uploading ? '导入中...' : '导入' }}</Button>
          </CommonUpload>
          <Button class="ml10" @click="handleToggleBatchOpen">{{ batchOpen ? '收起客户类型' : '展开客户类型' }}</Button>
        </Col>
        <Col>
          <Checkbox v-model="specialChecked">本次刷价同步更新商品资料</Checkbox>
          <Tooltip content="请先点击“修改价格”再进行修改" v-if="!isEditPrice">
            <Checkbox
              v-model="isCalcByPer"
              disabled
              class="black-font"
              style="margin-left: 20px"
              >按百分比计算</Checkbox
            >
          </Tooltip>
          <Checkbox
            v-if="isEditPrice"
            v-model="isCalcByPer"
            class="black-font"
            style="margin-left: 20px"
            >按百分比计算</Checkbox
          >
          <Tooltip
            :max-width="200"
            placement="top-end"
            content="注意：商品没有最近一次进货价的时候,不能按百分比修改价格.勾选按钮时,可以根据最近一次进价按百分比计算售价."
          >
            <i class="f20 iconfont icon-help" style="font-size:14px;" ></i>
          </Tooltip>
        </Col>
      </Row>
    </ListTable>
    <InPriceModal ref="inPriceModal" />
    <RowModal ref="rowModal" :isEditPrice="isEditPrice" />
  </div>
</template>

<script>
import Table from '@components/table';
import ListTable from "@components/list-table";
import Icon from '@components/icon';
import Button from '@components/button';
import img from '@assets/images/icon/status_icon.png'
import PurchaseType from "@components/purchase-type";
import CommodityCascader from '@/components/base-filter-components/commodityCascader/index.vue';
import UserSearch from '@components/user/userSearch';
import GoodsAutoComplete from '@components/common/goodsAutoComplete_new';
import NumberInput from '@components/basic/NumberInput';
import arrowUp from '@assets/images/smartPricingNew/arrow-up.png';
import arrowDown from '@assets/images/smartPricingNew/arrow-down.png';
import { PURCHASE_TYPE } from '@/util/const';

import InPriceModal from '@/pages/orderChangePrice/components/InPriceModal.vue';
import RowModal from './components/RowModal.vue';

import {
  userType,
  group,
  store,
  selfPickup,
} from '@/components/standard/sdp-filter-items'
import date from '@util/date.js';
import { post, get } from '@/api/request';
const deliveryDate = date.getDefaultDeliveryDate();
const priceTypeList = [
  {
    label:'最近一次进价',
    value:'1',
    tip: '每次入库单审核过后都会更新，但是可以进行手动更改'
  },
  {
    label:'最近一次入库价',
    value:'2',
    tip: '每次入库单审核之后才可以更新'
  },
  {
    label:'库存均价',
    value:'3',
    tip: '现有库存中的库存均价'
  },
  {
    label:'最近一次采购价',
    value:'4',
    tip: '采购单收货之后更新的采购价'
  }
];
export default {
  components: {
    ListTable,
    Table,
    Button,
    InPriceModal,
    RowModal
  },
  computed: {
    exportParams() {
      return {
        is_async_commodity: this.specialChecked == true ? 1 : 0,
        delivery_start_date: this.requestFilters.delivery_start_date,
        delivery_end_date: this.requestFilters.delivery_end_date,
        storage_id: this.requestFilters.storage_id
      };
    },
  },
  data() {
    return {
      exporting: false,
      batchOpen: false,
      uploading: false,
      isCalcByPer: false, //按百分比计算
      isEditPrice: false,
      specialChecked: true,
      priceType: localStorage.getItem('order_change_price_base_price_mode'),
      selection: [],
      filters: {
        commodity_id: ''
      },
      requestFilters: {
        storage_id: '',
      },
      advanceItems: [
        {
          checked:true,
          required:true,
          label: '发货日期',
          type: 'DatePicker',
          defaultValue:[],
          props: {
            type: 'daterange',
            clearable:false,
            placeholder: '选择发货日期'
          },
           defaultValue: [deliveryDate, deliveryDate],
          key: ['delivery_start_date', 'delivery_end_date']
        },
        {
          checked:true,
          type: 'custom',
          // show:!!parseInt(sessionStorage.getItem('_muti_store')),
          component: store,
          key: 'storage_id',
          label: '仓库',
        },
        {
          type: 'custom',
          component: PurchaseType,
          key: ['purchase_type', 'purchase_type_value'],
          label: '采购类型',
          props: {
            placeholder: '选择采购类型',
            mode: ['agent','direct_provider']
          }
        },
        {
          type: 'custom',
          component: group,
          key: 'group_id',
          label: '集团',
        },
        // {
        //   label: '商品分类',
        //   type: 'custom',
        //   key: ['category_id', 'category_id2', 'category_id3'],
        //   component: CategorySelect,
        // },
				{
					width: 'auto',
					type: 'custom',
					name: '商品分类',
					key:  ['category_id', 'category_id2', 'category_id3' ],
					defaultValue: [],
					props: {
						noMaxHeight: true
					},
					component: CommodityCascader,
				},
        {
          label: '客户类型',
          type: 'custom',
          key: 'receivable_style',
          data: [],
          component: userType,
          attrs: {
            multiple: true,
            placeholder:'全部类型',
          },
          tooltip: {
            maxWidth: '200',
            content: '筛选客户类型之后只会显示该客户类型的改价窗口!'
          }
        },
        {
          label: '客户信息',
          type: 'custom',
          key: 'user_id',
          component: UserSearch,
          props:{
            changeValueType:'value'
          }
        },
        {
          type: 'custom',
          component: GoodsAutoComplete,
          key: 'search_value',
          label: '商品搜索',
          defaultValue: '',
          props: {
            placeholder: '输入商品名称/编码/助记码查询',
            filters: {
              op: 'orderChangePrice'
            },
            on: {
              'on-enter': e => {
                this.filters.commodity_id = e.commodity_id;
              },
              'on-focus': () => {
                this.filters.commodity_id = '';
              },
              'on-clear': () =>{
                this.filters.commodity_id = '';
              }
            }
          }
        },
       {
          checked:false,
          label: '商品类型',
          type: 'Select',
          key: 'is_time_price',
          data: [
            {
            value:'',
            label:'全部商品'
          },
             {
            value:'1',
            label:'时价商品'
          },
             {
            value:'0',
            label:'非时价商品'
          }
          ],
          props: {
            placeholder:"全部商品",
            filterable: true,
            clearable: true
          },
        },
        {
          checked: false,
          label: "核价状态",
          type: 'Select',
          key: 'change_price_status',
          defaultValue: '0',
          data: [
            {
              value: '0',
              label: '全部',
            },
            {
              value: '1',
              label: '未核价',
            },
            {
              value: '2',
              label: '已核价',
            },
            {
              value: '3',
              label: '部分核价',
            },
          ]
        },
        {
          checked: false,
          label: '配送方式',
          type: 'Select',
          key: 'delivery_method',
          data: [
            {
              value: '0',
              label: '全部'
            },
            {
              value: '1',
              label: '配送'
            },
            {
              value: '2',
              label: '自提'
            }
          ]
        },
        {
          checked: false,
          label: '自提点',
          type: 'custom',
          key: 'self_pickup_point_id',
          component: selfPickup,
          props: {
            filterable: true,
            clearable: true,
            'filter-by-label':true
          },
        },
      ],
      list: [],
      columns: [
        {
          width: 30,
          type: 'titleCfg',
          titleType: 'order_change_price',
          fixed: 'left',
        },
        {
          title: "商品名称",
          key: "name",
          sortable: true,
          width: 200,
          resizable: true,
          poptip: true,
          fixed: 'left'
        },
        {
          title: "下单数量",
          key: "total_amount",
          sortable: true,
        },
        {
          title: "单位",
          key: "unit",
          render: (_, { row }) => {
            return <div><p>{row.unit}</p><p style={{color: 'red'}}>{row.unit_convert_desc}</p></div>
          }
        },
        {
          title: "商品描述",
          poptip: true,
          key: "summary",
        },
        {
          title: "转换系数",
          key:'unit_convert_text',
        },
        {
          title: "库存均价",
          key:'average_price',
          show: () => this.requestFilters.storage_id,
        },
        {
          key: 'pricy_type',
          title: '最近一次进价',
          minWidth: 128,
          renderHeader: h => {
            let currentPriceType = priceTypeList.find(
              item => (item.value === this.priceType || item.value === this.priceType)
            );
            if (!currentPriceType) {
              currentPriceType = priceTypeList[0];
            }
            this.baseValue = currentPriceType.value;
            const dropDownList = priceTypeList.map(item => {
              return h(
                'DropdownItem',
                {
                  props: {
                    name: item.value
                  },
                  style: {
                    display: 'flex',
                    alignItems: 'center'
                  }
                },
                [
                  h('span', item.label),
                  !!item.tip && h(
                    'Tooltip',
                    {
                      props: {
                        content: item.tip,
                        placement: 'right',
                        transfer: true
                      },
                      on: {
                        'on-popper-show': () => {
                          $('.ivu-tooltip-popper').css('z-index', '99999');
                          $('.ivu-tooltip-inner').css('white-space', 'normal');
                        },
                        'on-popper-hide': () => {
                          $('.ivu-tooltip-popper').css('z-index', '99999');
                          $('.ivu-tooltip-inner').css('white-space', 'normal');
                        }
                      }
                    },
                    [
                      h(Icon, {
                        props: {
                          icon: 'tips',
                          size: '13'
                        },
                        style: {
                          marginLeft: '5px',
                          fontSize: '13px',
                        }
                      }),
                    ]
                  )
                ]
              );
            });
            const dropDown = h(
              'Dropdown',
              {
                props: {
                  trigger: 'click',
                  transfer: true
                },
                on: {
                  'on-click': name => {
                    this.priceType = name;
                    this.handleChangePriceType(name);
                  }
                }
              },
              [
                h('img', {
                  style: {
                    width: '10px',
                    height: '10px',
                    marginLeft: '4px',
                    marginBottom: '1px'
                  },
                  attrs: {
                    src: img
                  }
                }),
                h(
                  'DropdownMenu',
                  {
                    slot: 'list'
                  },
                  dropDownList
                )
              ]
            );
            return h('div', [h('span', currentPriceType.label), dropDown]);
          },
          render: (h, { row }) => {
            const icon = h(
              Icon,
              {
                props: { icon: 'xiadanjilu' },
                style: { fontSize: '14px', marginLeft: '5px', cursor: 'pointer', color: '#303030' },
                on: {
                  click: () => this.inPriceList(row.commodity_id),
                },
              }
            );

            // 第一行展示 in_price
            const firstLine = h('div', { class: 'mb5' }, [
              h(
                'span',
                {
                  style: { display: 'inline-block', lineHeight: '20px' },
                },
                row.in_price == 0 ? '无' : row.in_price
              ),
              this.priceType === '1' && !row.purchase_unit_last_in_price_desc
                ? icon : null,
            ]);

            // 第二行展示 purchase_unit_last_in_price_desc
            const secondLine =
              row.purchase_unit_last_in_price_desc &&
              h('div', [
                h(
                  'span',
                  {
                    style: { display: 'inline-block', lineHeight: '20px', color: 'red' },
                  },
                  [row.purchase_unit_last_in_price_desc, icon]
                ),
              ]);

            return h('div', [firstLine, secondLine]);
          }
        },
        {
          title: '核价状态',
          width: 100,
          key: 'change_price_status',
          tip: '状态更新有延迟，获取最新状态请手动刷新页面',
          render: (_, { row }) => {
            const statusMap = {
              1: '未核价',
              2: '已核价',
              3: '部分核价',
            }
            return statusMap[+row.change_price_status] || '-';
          }
        },
        {
          title: '设置客户类型价',
          key: 'receivable_style',
          tip: '红色字体代表该客户类型价在对应发货日期段有过改价记录；黑色字体代表该客户类型价在对应发货日期段没有改价记录',
          minWidth: 156,
          width: 840,
          render: (h, { row }) => {
            const DEFAULT_SHOW_ITEM = 8;
            const showTypeItems = row._expand_user_type ? row.receivable_style : row.receivable_style.slice(0, DEFAULT_SHOW_ITEM);

            const expandIcon = h('img', {
              style: {
                color: '#03ac54', // todo 已下单的才展示
                cursor: 'pointer',
                width: '18px',
                height: '14px',
                marginLeft: '6px',
                marginTop: '-4px'
              },
              attrs: {
                  src: row._expand_user_type ? arrowUp : arrowDown
                },
                class: {
                },
                on: {
                  click: () => {
                    row._expand_user_type = !row._expand_user_type;
                  }
                }
              }
            );

            const userTypeItems = showTypeItems.map((item, index) => {
              return h('Col', {
                style: {
                  width: '25%',
                  marginBottom: '12px',
                  paddingRight: '8px'
                }
              }, [
                h('Row', {
                  props: {
                    type: 'flex',
                    wrap: false
                  }
                }, [
                  +item.is_true === 1 ? h('Col', [
                    h('Tooltip', {
                      props: {
                        content: '有客户下单',
                        transfer: true
                      }
                    }, [
                      h(Icon, {
                        props: {
                          icon: 'dan',
                          size: 14
                        },
                        style: {
                          color: 'var(--primary-color)',
                          marginRight: '5px',
                          cursor: 'pointer'
                        }
                      })
                    ])
                  ]) : null,
                  h('Col', {
                    style: {
                      overflow: 'hidden'
                    }
                  }, [
                    h('Tooltip',{
                      style: {
                        width: '100%',
                      },
                      props: {
                        content: item.receivable_style_name,
                        placement: 'top',
                        transfer: true
                      },
                    }, [
                      h('div', {
                        style: {
                          width: '100%',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          color: item.change_price_status == '2' ? '#f33333' : '#505050'
                        }
                      }, item.receivable_style_name)
                    ])
                  ]),
                  index === 3 && row.receivable_style.length > DEFAULT_SHOW_ITEM ? h('Col', [expandIcon]) : null
                ]),
                this.isCalcByPer && row.in_price != 0 ? h('div', {
                  style: {
                    whiteSpace: 'nowrap',
                  }
                }, [
                  h(NumberInput, {
                    props: {
                      value: item.ratio,
                      intOnly: true
                    },
                    style: {
                      width: '91px',
                      marginBottom: '6px'
                    },
                    on: {
                      'on-change': (val) => {
                        item.ratio = val;
                      },
                      'on-enter': () => {
                        this.changeRatio(row, item);
                      },
                      'on-blur': () => {
                        this.changeRatio(row, item);
                      }
                    }
                  }),
                  h('span',' %')
                ]) : null,
                h('div', [
                  h(NumberInput, {
                    style: {
                      width: '91px'
                    },
                    props: {
                      precision: 2,
                      value: item.change_price,
                      disabled: !this.isEditPrice || this.isCalcByPer,
                    },
                    on: {
                      'on-change': (val) => {
                        item.change_price = val;
                      },
                      'on-blur': () => {
                        this.changePrice(row, item);
                      },
                      'on-enter': () => {
                        this.changePrice(row, item);
                      },
                    },
                  }),
                  item.price_warning ? h('Tooltip', {
                    props: {
                      content: item.price_warning,
                      placement: 'top',
                      transfer: true
                    },
                  }, [
                    h(Icon, {
                      props: {
                        icon: 'tips',
                        size: 14
                      },
                      style: {
                        color: 'red',
                        marginLeft: '5px'
                      }
                    })
                  ]) : null
                ]),
              ]);
            });
            return h('div', [
              h('Row', {
                props: {
                  type: 'flex',
                  align: 'middle',
                  justify: 'start'
                }
              }, userTypeItems),
              // row.receivable_style.length > DEFAULT_SHOW_ITEM && expandIcon
            ]);
          }
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',         
          width: 100,
          actions: ({ row }) => {
            const actions =  [
              {
                name: '订单核价',
                show: () => {
                  return true;
                },
                action: () => {
                  this.$refs.rowModal.open(row, this.getFilters());
                }
              },
            ];
            return actions.filter(item => {
              if (typeof item.show === 'function') {
                return item.show();
              }
              return true;
            });
          }
        }
      ],
    }
  },
  methods: {
    beforeRequest(params) {
      return this.dealParams(params);
    },
    afterRequest(res) {
      this.batchOpen = false;
      if (res.data.list) {
        res.data.list.forEach(item => {
          item._expand_user_type = false;
          item._price_saving = false;
          item._ratio_saving = false;
          if (item.receivable_style) {
            item.receivable_style.forEach(priceObj => {
              priceObj.old_change_price = priceObj.change_price;
              priceObj.price_warning = this.priceWarning(priceObj, item.average_price);
            });
          }
        });
      }
      return res;
    },
    dealParams(params) {
      if (params.sort_field === 'name') {
        params.sort_field = 'commodity_name';
      }
      if (this.filters.commodity_id) {
        params.commodity_id = this.filters.commodity_id;
        params.search_value = '';
      }
      if (+params.purchase_type === +PURCHASE_TYPE.agent) {
        params.agent_id = params.purchase_type_value;
        params.provider_id = '';
      } else if (
        +params.purchase_type === +PURCHASE_TYPE.direct_provider ||
        +params.purchase_type === +PURCHASE_TYPE.provider
      ) {
        params.provider_id = params.purchase_type_value;
        params.agent_id = '';
      } else {
        params.provider_id = '';
        params.agent_id = '';
      }
      this.requestFilters = params;
      return params;
    },
    resetChange() {
      this.filters.commodity_id = '';
    },
    handleToggleBatchOpen() {
      this.batchOpen = !this.batchOpen;
      const listData = this.$refs.list.getData();
      listData.forEach(item => {
        item._expand_user_type = this.batchOpen;
      });
    },
    isAbleEditPrice() {
      this.isEditPer = false;
      if (this.isEditPrice) {
        this.isCalcByPer = false;
      }
      this.isEditPrice = !this.isEditPrice;
    },
    getFilters() {
      let filters = this.$refs.list.getParams();
      delete filters.page;
      delete filters.currentPage;
      delete filters.pageSize;
      delete filters['delivery_start_date,delivery_end_date'];
      filters = this.dealParams(filters);
      return filters;
    },
    handleExport() {
      let filters = this.getFilters();
      if (!filters.commodity_id_search) {
        filters.commodity_id_search = 0;
      }
      filters.return_url = 1;
      if (this.exporting) {
        return;
      }
      this.exporting = true;
      get('/superAdmin/orderChangePrice/exportChangeCommodityNeed', filters).then(({
        status,
        message,
        data
      }) => {
        if (status && data.url) {
          // 将http:替换为https:
          data.url = data.url.replace('http:', 'https:');
          window.open(data.url);
        } else {
          this.$smessage({ type: 'error', text: message });
        }
      }).finally(() => {
        this.exporting = false;
      });
    },
    //上传excel
    beforeImport() {
      this.uploading = true;
    },
    handleImportError() {
      this.uploading = false;
    },
    handleImportSuccess(res) {
      this.uploading = false;
      if (res.status === 'success') {
        this.$smessage({ type: 'success', text: '导入成功！' });
        this.refreshList();
      } else {
        this.$smessage({ type: 'error', text: res.message });
      }
    },
    //修改百分比
    changeRatio(goods, priceObj) {
      if (priceObj.ratio <= 0) {
        this.$smessage({ type: 'error', text: '不能修改为0' });
        priceObj.ratio = priceObj.old_ratio;
        return;
      }
      if (priceObj.ratio == priceObj.old_ratio) {
        this.isEditPer = false;
        return;
      }

      const filters = this.getFilters();

      const data = {
        ...filters,
        is_async_commodity: this.specialChecked == true ? 1 : 0,
        info: [
          {
            commodity_id: goods.commodity_id,
            receivable_style_id: priceObj.receivable_style_id,
            ratio: priceObj.ratio
          }
        ]
      };

      if (priceObj._ratio_saving) {
        return;
      }
      priceObj._ratio_saving = true;
      post('/superAdmin/OrderChangePrice/pageChangeRatio', {
        data: data
      }).then(res => {
        if (res.status == 'success') {
          priceObj.old_ratio = priceObj.ratio;
          priceObj.change_price = (goods.in_price * (priceObj.ratio / 100)).toFixed(
            4
          );
          console.log(priceObj.change_price, priceObj.ratio, priceObj.old_change_price, priceObj, goods);
          this.changePrice(goods, priceObj);
        } else {
          this.$Modal.warning({
            title: '警告',
            content: res.message
          });
          priceObj.ratio = '';
        }
      }).finally(() => {
        priceObj._ratio_saving = false;
      });
    },
    //修改价格
    changePrice(goods, priceObj) {
      if (!priceObj.change_price) {
        this.$smessage({ type: 'error', text: '请输入正确价格！' });
        priceObj.change_price = priceObj.old_change_price;
        return;
      }
      if (priceObj.change_price == 0) {
        this.$smessage({ type: 'error', text: '不能修改为0' });
        priceObj.change_price = priceObj.old_change_price;
        return;
      }

      if (priceObj.change_price == priceObj.old_change_price) {
        return;
      }
      const filters = this.getFilters();
      var data = {
        ...filters,
        delivery_date: '',
        is_async_commodity: this.specialChecked == true ? 1 : 0,
        info: [
          {
            commodity_id: goods.commodity_id,
            receivable_style_id: priceObj.receivable_style_id,
            in_price: goods.in_price,
            change_price: priceObj.change_price
          }
        ]
      };
      if (priceObj._price_saving) {
        return;
      }
      priceObj._price_saving = true;
      post('/superAdmin/OrderChangePrice/pageChangePrice', {
        data: data
      }).then(res => {
        if (res.status == 1) {
          priceObj.old_change_price = priceObj.change_price;
          priceObj.ratio =
            goods.in_price != 0
              ? ((priceObj.change_price / goods.in_price) * 100).toFixed(0)
              : '';
          this.$smessage({ type: 'success', text: '改价成功' });
          priceObj.change_price_status = '2'
          priceObj.price_warning = this.priceWarning(
            priceObj,
            goods.average_price
          );
        } else {
          this.$smessage({ type: 'error', text: res.message });
          priceObj.change_price = priceObj.old_change_price;
        }
      }).finally(() => {
        priceObj._price_saving = false;
      });
    },
    priceWarning(item, avgPrice) {
      if (item.change_price - avgPrice < 0) {
        return '此类型客户销售价低于库存均价';
      }
      return '';
    },
    handleChangePriceType(_value){
      window.localStorage.setItem(
      'order_change_price_base_price_mode',
      _value
      );
      // 更新价格模式配置，然后刷新列表
      this.commonService.editSingleConfig('order_change_price_base_price_mode', _value).then(()=>{
        this.refreshList();
      })
    },
    inPriceList(id) {
      this.$refs.inPriceModal.open(id);
    },
    refreshList(resetPage = false, keepScroll, loadingType = 'spinner') {
      this.$refs.list.fetchData(resetPage, keepScroll, loadingType);
    },
  }
}
</script>

<style scoped lang="less">
.list-container {
  padding-top: 16px;
}
/deep/ .ivu-input-number-handler-wrap {
  display: block;
}
</style>