export <template>
  <Modal className="modal-no-footer" v-model="show" :width="800" title="订单核价">
    <Form inline label-colon :label-width="70" >
      <FormItem label="商品名称">
        {{ `${goods.name}（${goods.unit}）` }}
      </FormItem>
    </Form>
    <Table
      :outer-border="true"
      :columns="columns"
      :data="list"
      :max-height="getTableHeight() - 100"
    ></Table>
  </Modal>
</template>
<script>
import Table from '@components/table';
import NumberInput from '@components/basic/NumberInput';
import { get, post } from '@/api/request';
export default {
  components: {
    Table
  },
  props: {
    isEditPrice: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      saving: false,
      show: false,
      title: '',
      list: [],
      goods: {},
      columns: [
        {
          title: '发货日期',
          key: 'delivery_date',
          minWidth: 80,
        },
        {
          title: '订单号',
          key: 'order_no',
          width: 186,
        },
        {
          title: '客户名称',
          key: 'email',
          poptip: true
        },
        {
          title: '下单数量',
          key: 'amount',
          width: 90
        },
        {
          title: '客户类型',
          key: 'receivable_style_name',
          poptip: true,
        },
        {
          title: '价格',
          width: 110,
          render: (h, { row }) => {
            return h(NumberInput, {
              props: {
                precision: 2,
                value: row.unit_price,
                disabled: !this.isEditPrice,
              },
              on: {
                'on-change': (val) => {
                  row.unit_price = val;
                },
                'on-blur': () => {
                  this.userChangePrice(row);
                },
                'on-enter': () => {
                  this.userChangePrice(row);
                }
              }
            })
          }
        }
      ],
    };
  },
  methods: {
    open(goods, filters = {}) {
      this.goods = goods;
      this.show = true;
      this.getList(goods, filters);
    },
    //修改客户列表价格
    userChangePrice(item) {
      if (item.unit_price == item.old_unit_price) {
        return;
      }
      if (Number(item.unit_price) === 0) {
        this.$smessage({ type: 'error', text: '不能修改为0' });
        return;
      }
      let params = {
        ocid: item.ocid,
        order_id: item.order_id,
        user_id: item.user_id,
        unit_price: item.unit_price
      };
      if (this.saving) {
        return;
      }
      this.saving = true;
      post('/superAdmin/orderChangePrice/saveUserOrderCommodity', params).then(
        res => {
          if (res.status == 1) {
            item.old_unit_price = item.unit_price;
            this.$smessage({ type: 'success', text: '改价成功' });
          } else {
            this.$smessage({ type: 'error', text: res.message });
            item.unit_price = item.old_unit_price;
          }
        }
      ).finally(() => {
        this.saving = false;
      });
    },
    getList(goods, filters) {
      this.list = [];
      let params = { ...filters, commodity_id: goods.commodity_id };
      get('/superAdmin/orderChangePrice/getCommodityUserList', params).then(
        (res) => {
          let { status, data } = res;
          if (status) {
            data.forEach(item => {
              item.old_unit_price = item.unit_price;
            });
            this.list = data;
          }
        }
      );
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .ivu-form-inline .ivu-form-item  {
  margin-bottom: 16px;
}
</style>