<template>
  <div class="amap-page-container">
    <div class="amap-demo" id="position-amap" style="height: 100%"></div>
  </div>
</template>

<script>
import 'flex.css';
import '@/init/init-map.js';

export default {
  data() {
    return {
      map: null,
      zoom: 14,
      lng: 0,
      lat: 0
    };
  },
  props: {
    position: {
      type: Object,
      default: () => {
        return {};
      },
      required: false
    },
    isAll: <PERSON><PERSON><PERSON>,
    list: Array
  },
  watch: {
    position: {
      deep: true,
      handler(position) {
        if (this.isAll) return
        if (this.map) {
          this.map.clearMap();
        }
        if (this.hasPosition(position)) {
          this.lng = position.longitude;
          this.lat = position.latitude;
          this.setMarker(position)
          this.setCenter();
        }
      }
    },
    isAll(val) {
      if (this.map) {
        this.map.clearMap();
      }
      if (val) {
        this.setCenter();
      } else {
        if (this.hasPosition(this.position)) {
          this.lng = this.position.longitude;
          this.lat = this.position.latitude;
          this.setMarker(this.position)
          this.setCenter();
        }
      }
    }
  },
  created() {},
  activated() {
    this.judgeMap();
  },
  mounted() {
    this.judgeMap();
  },
  beforeDestroy() {
    this.map.destroy();
    this.map = null;
    if (this.timer) {
      clearTimeout(this.timer);
    }
  },
  methods: {
    // 判断地图是否加载
    judgeMap() {
      /*eslint-disable*/
      if (typeof AMap !== 'undefined') {
        this.initMap();
      } else {
        setTimeout(() => {
          this.initMap();
        }, 800);
      }
    },
    initMap() {
      this.map = new AMap.Map('position-amap', {
        zoom: this.zoom
      });
      this.map.clearMap()
      if (this.isAll) {
        this.setCenter();
      } else {
        if (this.lng && this.lng > 0 && this.lat && this.lat > 0) {
          this.setCenter();
        }
      }
    },
    hasPosition(position) {
      return (
        position &&
        position.latitude &&
        position.longitude &&
        parseInt(position.latitude) !== 0 &&
        parseInt(position.longitude) !== 0 &&
        !isNaN(position.latitude) &&
        !isNaN(position.longitude)
      );
    },
    setCenter(position) {
      position ||
        (position = this.position.longitude ? [this.position.longitude, this.position.latitude] : '');
      const positionList = []
      if (this.isAll) {
        this.list.map(item => {
          if (item.longitude) {
            positionList.push(item)
          }
        })
      }
      setTimeout(() => {
        if (!this.map) {
          return;
        }
        if (position) {
          this.map.setZoomAndCenter(this.zoom, position);
        } else {
          if (positionList[0].longitude) {
            this.map.setZoomAndCenter(12, [parseFloat(positionList[0].longitude), parseFloat(positionList[0].latitude)])
          }
        }
        if (this.isAll) {
          positionList.forEach(item => {
            this.setMarker(item)
          })
        }
      }, 500);
    },
    /**
     * @description 添加marker
     */
    setMarker: function(item) {
      console.log('setMarker', item);
      
      var customIcon = new AMap.Icon({
        // 图标尺寸
        size: new AMap.Size(40, 48),
        // 图标的取图地址
        image: require('./../../images/<EMAIL>'),
        // 图标所用图片大小
        imageSize: new AMap.Size(40, 48)
      });

      const marker = new AMap.Marker({
        icon: customIcon,
        // map: this.map,
        position: [item.longitude, item.latitude],
        // resizeEnable: true
      });

      // 创建信息窗体
      var infoWindow = new AMap.InfoWindow({
        isCustom: true, // 使用自定义窗体
        content: `<div class="cus_info_window">
              <div class="license_plate">${item.license_plate ||
                item.driver_name}</div>
              <div class="temperature_humidity"><span>温度：${
                (item.temperature || '').indexOf('/') !== -1 ? item.temperature : `${item.temperature || '-'}°C`
              }</span>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;</span></span><span>湿度：${
                 (item.humidity || '').indexOf('/') !== -1 ? item.humidity : `${item.humidity || '-'}%`
              }</span></div>
              <div class="date temperature_humidity">车辆状态：${item.online ? '车辆在线' : '车辆离线' || '--'} &nbsp;&nbsp;|&nbsp;&nbsp; ${item.state_str || '--'}</div>
              <div class="date">最后更新：${item.create_time || '-'}</div>
            </div>`, // 信息窗体的内容可以是任意 html 片段
        offset: new AMap.Pixel(10, -40)
      });

      const onMarkerClick = e => {
        infoWindow.open(this.map, e.target.getPosition()); // 打开信息窗体
      };

      marker.on('click', onMarkerClick);

      this.map.add(marker)
    }
  }
};
</script>

<style lang="less">
.cus_info_window {
  position: relative;
  min-width: 200px;
  // height: 90px;
  background: rgba(0, 0, 0, 0.7);
  box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
  border-radius: 4px;
  padding: 8px 10px;
}
// 由于.cus_info_window的高度和宽度都在变化，top和left都设置为%
.cus_info_window::after {
  position: absolute;
  top: 100%;
  left: 50%;
  content: '';
  width: 0;
  height: 0;
  border: 6px solid;
  border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
}
.cus_info_window .license_plate {
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
}
.cus_info_window .temperature_humidity {
  position: relative;
  display: flex;
  align-items: center;
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
.cus_info_window .temperature_humidity i.line {
  margin: 0 16px;
  display: inline-block;
  width: 1px;
  height: 13px;
  background: rgba(233, 233, 233, 0.6);
}
.cus_info_window .date {
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
</style>

<style lang="scss" scoped>
.amap-demo {
  .search-box {
    position: absolute;
    top: 25px;
    left: 20px;
  }
  .amap-page-container {
    position: relative;
  }
}

.search-container {
  position: absolute;
  top: 75px;
  left: 35px;
  .ivu-btn {
    z-index: 255;
    margin-left: 15px;
  }
}

.vl-notify .vl-notify-content {
  padding: 0 !important;
}

.amap-page-container {
  .search-box {
    height: 35px;
  }
  .search-box-wrapper {
    .search-btn {
      color: #495060 !important;
    }
  }
}
</style>
