<template>
  <div class="set-line-map">
    <Row :gutter="15" type="flex" align="middle" class="filters">
      <Col :span="16">
      <Row :gutter="15" type="flex" align="middle">
        <Col>
        <DatePicker type="date" :clearable="false" v-model="delivery_date" @on-change="handleDateChange" placeholder="请选择发货日期" style="width: 140px"></DatePicker>
        </Col>
        <Col>
        <Select v-model="filters.storage_id" placeholder="请选择仓库" @on-change="changeStore">
          <Option :value="item.id" :key="item.id" v-for="item in storageList">{{item.name}}</Option>
        </Select>
        </Col>
      </Row>
      </Col>
      <Col :span="8" style="text-align: right">
      <Button type="primary" icon="reply-all" @click="unbindAll">全部解绑</Button>
      </Col>
    </Row>
    <Row type="flex" class="set-line-main">
      <!--左侧区域开始-->
      <Col class="left-part">
      <div class="line-container">
        <div class="item line-info title">
          <span v-show="isChooseLine" @click="closeLineDetail">收起该线路</span>
          <span v-show="!isChooseLine">全部线路</span>
        </div>
        <div v-show="currentLine.line_name" style="position: relative" :style="{height: getTableHeight() - 20 + 'px', overflowY: 'auto'}">
          <div class="line-item one-line-wrap">
            <div class="line-info active" @click="closeLineDetail">
              <Row type="flex" justify="space-between">
                <Col class="line-name">
                <Icon class="color-light" type="md-arrow-dropdown"></Icon>
                {{currentLine.line_name}}
                </Col>
                <Col class="text-right"><span class="color-light">司机：</span>{{currentLine.driver_name}}</Col>
              </Row>
              <Row type="flex">
                <Col :span="9">
                <span class="color-light">下单:</span>
                <span>{{currentLine.price}}</span>
                </Col>
                <Col :span="7" align="left">
                <span class="color-light">分拣:</span>
                <span>{{currentLine.actual_price}}</span>
                </Col>
                <Col :span="8" align="right">
                <Tooltip :transfer="true" content="线路绑定客户数" placement="left">{{currentLine.line_user_total}}</Tooltip>/<Tooltip :transfer="true" content="线路订单数" placement="right">{{currentLine.order_total}}</Tooltip>
                </Col>
              </Row>
              <Row type="flex">
                <Col :span="9">
                <span class="color-light">体积:</span>
                <span>{{currentLine.total_volume}}立方</span>
                </Col>
                <Col :span="8" align="left">
                <span class="color-light">重量:</span>
                <span>{{currentLine.total_weight}}KG</span>
                </Col>
              </Row>
            </div>
            <div class="line-user">
              <div class="no-user" v-if="hasLineUserList.length === 0">暂无客户</div>
              <draggable element="div" :options="dragOptions" v-model="hasLineUserList">
                <Row class="user-item" :class="user.active ? 'active' : ''" v-for="(user, index) in hasLineUserList" :key="index" type="flex">
                  <Col class="user-sequence"><span class="user-index">{{(index + 1)}}</span></Col>
                  <Col class="user-name">
                  <span style="padding-left: 10px" @click="clickUserItem(user)">{{user.name}}</span>
                  </Col>
                  <Col class="text-right"><Icon type="document-text color-light" v-if="user.order_num > 0"></Icon></Col>
                  <Col class="unbind text-center"><span class="color-light" @click.stop="unBindOneUser(user)">{{hasLine(user) ? '解绑' : '删除'}}</span></Col>
                  <Col class="text-right"><Icon style="margin-right: 0" type="android-radio-button-on" :class="hasPosition(user) ? 'color-active' : 'color-light'"></Icon></Col>
                </Row>
              </draggable>
            </div>
            <Row type="flex" justify="space-between" class="line-user-op">
              <Col><Button icon="gear-b" @click.stop="showLineSetting">线路设置</Button></Col>
              <Col><Button icon="reply-all" @click.stop="batchUnBindUser">批量解绑</Button></Col>
              <Col><Button icon="reply" @click="cancelBind">取消</Button></Col>
              <Col><Button type="primary" icon="android-done" @click.stop="batchBindUser(0)">保存</Button></Col>
            </Row>
          </div>
        </div>
        <div id="line-list" v-show="!isChooseLine" style="position: relative" :style="{height: getTableHeight() - 20 + 'px', overflowY: 'auto'}">
          <div class="line-item" v-for="line in lineList">
            <!--<div class="line-item" :class="lineItemClass(line)" v-for="line in lineList" v-show="!isChooseLine || isCurrentLine(line)">-->
            <div class="line-info" :class="line.active ? 'active' : ''" @click="changeLine(line)">
              <Row type="flex" justify="space-between">
                <Col class="line-name">
                <Icon class="color-light" type="arrow-right-b"></Icon>
                {{line.line_name}}
                </Col>
                <Col class="text-right"><span class="color-light">司机：</span>{{line.driver_name}}</Col>
              </Row>
              <Row type="flex">
                <Col :span="9" align="left">
                <span class="color-light">下单:</span>
                <span>{{line.price}}</span>
                </Col>
                <Col :span="7" align="left">
                <span class="color-light">分拣:</span>
                <span>{{line.actual_price}}</span>
                </Col>
                <Col :span="8" align="right">
                <Tooltip :transfer="true" content="线路绑定客户数" placement="left">{{line.line_user_total}}</Tooltip>/<Tooltip :transfer="true" content="线路订单数" placement="right">{{line.order_total}}</Tooltip>
                </Col>
              </Row>
              <Row type="flex">
                <Col :span="9" align="left">
                <span class="color-light">体积:</span>
                <span>{{line.total_volume}}立方</span>
                </Col>
                <Col :span="8" align="left">
                <span class="color-light">重量:</span>
                <span>{{line.total_weight}}KG</span>
                </Col>
              </Row>
            </div>
          </div>
        </div>
      </div>
      </Col>
      <!--左侧区域结束-->
      <Col class="right-part">
      <!--右侧已分配线路用户开始-->
      <div class="right-part-wrap">
        <Row class="map-header" type="flex" justify="space-between" align="middle" style="margin-bottom: 5px">
          <Col :span="16">
          <Row :gutter="20" type="flex" align="middle">
            <Col><span class="title" style="font-weight: bolder">地图概况</span></Col>
            <Col>总客户数：<span class="text-active text-bold color-red">{{allUserList.length}}</span></Col>
            <Col>已排线：<span class="text-active text-bold color-red">{{userSum.has_line.length}}</span></Col>
            <Col>未排线：<span class="text-active text-bold color-red">{{userSum.no_line.length}}</span></Col>
            <Col>当前视野客户数：<span class="text-bold">{{userSum.in_view.length}}</span></Col>
            <Col style="position: relative">
            未定位客户：<span class="text-bold">{{userSum.no_position.length}}</span>
            <Button class="view-btn" size="small" @click="toggleNoPositionUser" v-if="noPositionUserList && noPositionUserList.length > 0">{{showNoPositionUser ? '关闭' : '查看'}}</Button>
            <div class="no-position-user" v-show="showNoPositionUser">
              <div class="user-item user-search">
                <Input v-model="noPositionUserFilters.query" placeholder="请输入客户名称搜索" />
              </div>
              <div class="user-list" :style="{maxHeight: getTableHeight() - 100 + 'px', overflow: 'auto'}">
                <Row class="user-item" type="flex" align="middle" justify="space-between" :key="index" v-for="(user, index) in noPositionUserList">
                  <Col>{{user.name}}</Col>
                  <Col class="text-right color-active"><span style="cursor: pointer" @click="showPositionModal(user)">打点</span></Col>
                </Row>
              </div>
            </div>
            </Col>
            <Col><Checkbox v-model="allUserFilters.hide_other_line_user" @on-change="filterAllUser(true)"><span>不显示其他线路客户</span></Checkbox></Col>
            <Col><Checkbox v-model="showUsername" @on-change="refresh"><span>显示客户名称</span></Checkbox></Col>
          </Row>
          </Col>
          <Col class="text-right" :span="8">
          <Button type="primary" icon="refresh" @click="refresh">刷新</Button>
          <Button type="primary" icon="close" @click="cancelDraw" v-show="drawing">取消圈选</Button>
          <Button type="primary" :icon="drawing ? 'android-done' : 'ios-pulse-strong'" @click="handleDraw">{{drawing ? '完成圈选' : '开始圈选'}}</Button>
          </Col>
        </Row>
        <div class="map-wrap">
          <Alert v-show="drawing" closable show-icon style="text-align: left; margin: 0; width: 550px;position: absolute;left: 5px; right: 5px; top: 5px; z-index: 255">
            温馨提示：地图开始圈选之后，双击鼠标左键或者单击鼠标右键结束圈选。
          </Alert>
          <div id="set-line-map" :style="{height: '100%'}"></div>
        </div>
      </div>
      </Col>
    </Row>
    <!--线路设置开始-->
    <Modal
      v-model="lineSettingModal.show"
      title="线路设置/更换司机"
      @on-cancel="lineSettingModal.show=false">
      <Select style="width: auto" v-model="lineSettingModal.driver_id" placeholder="请选择司机" filterable>
        <Option :value="item.driver_id" v-for="item in driverList" :key="item.driver_id">{{item.driver_name}}</Option>
      </Select>
      <div slot="footer" style="text-align: right">
        <Button @click="lineSettingModal.show=false">取消</Button>
        <Button type="primary" @click="changeDriver">确定</Button>
      </div>
    </Modal>
    <!--线路设置结束-->
    <!--打点开始-->
    <Modal
      v-model="positionModal.show"
      width="1000"
      class-name="vertical-center-modal set-position-modal"
      title="打点">
      <Row slot="header" :gutter="20" style="font-weight: bolder; color: #000" type="flex" align="middle">
        <Col>客户名称：{{positionModal.user.name}}</Col>
        <Col>详细地址：{{positionModal.user.address_detail}}</Col>
      </Row>
      <set-position
        :style="{height: getTableHeight() + 'px'}"
        :user="positionModal.user"
        @on-success="setPositionSuccess"></set-position>
    </Modal>
    <!--打点结束-->
  </div>
</template>
<script>
  import draggable from 'vuedraggable'
  import storage from '@components/common/storeSelect';
  import lineSelect from '@components/delivery/lineSelect';
  import driver from '@components/delivery/driverSelect';
  import userType from '@components/user/userTypeSelect';
  import setPosition from '@components/user/setPosition'
  import mapService from '@api/map';
  import '@/init/init-map.js';
  const BLUE_ICON = 'https://img.shudongpoo.com/common/amap/setLine/marker-blue.png';
  const RED_ICON = 'https://img.shudongpoo.com/common/amap/setLine/marker-red.png';
  const GRAY_ICON = 'https://img.shudongpoo.com/common/amap/setLine/marker-gray.png';
  const orderFilter = {
    all: 0,
    hasOrder: 1,
    noOrder: 2,
  };
  export default {
    name: "setLine",
    components: {
      storage,
      lineSelect,
      driver,
      userType,
      setPosition,
      draggable
    },
    computed: {
      noPositionUserList() {
        let query = this.noPositionUserFilters.query;
        query = query.trim();
        let userList = this.userSum.no_position.filter((user) => {
          if (!query) {
            return true;
          } else {
            return user.name.includes(query);
          }
        });
        return userList;
      },
      modified() {
        let currentLineUser = this.hasLineUserList.map((user) => user.user_id).join(',');
        let originalLineUser = this.originalLineUserList.map((user) => user.user_id).join(',');
        return currentLineUser !== originalLineUser;
      },
      isChooseLine () {
        return this.currentLine && this.currentLine.line_name;
      }
    },
    watch: {
      markers() {
        this.checkInViewUser();
      },
      hasLineUserList(newUserList) {
        this.updateMarkers();
      },
      allUserList(newValue, oldValue) {
        let newUserIds = newValue.map((user) => user.user_id);
        let oldUserIds = oldValue.map((user) => user.user_id);
        // 用户数据发生变化
        if (newUserIds.join(',') !== oldUserIds.join(',')) {
          this.setMarkers();
        }
      }
    },
    data() {
      return {
        map: null,
        currentLine: {
          line_name: '',
          line_id: '',
          driver_name: '',
          price: '',
          actual_price: '',
          user_total: '',
          user_line_total: '',
        },
        showUsername: true,  // 获取用户数据后是否自动设置地图中心
        mapAutoCenter: true,  // 获取用户数据后是否自动设置地图中心
        showNoPositionUser: false,
        drawing: false,
        mouseTool: null,
        drawEditor: null,
        drawObj: null,
        checkAll: true,
        unBindingAll: false,
        binding: false,
        unBinding: false,
        unBindingConfirm: false,
        unBindingUser: '',
        delivery_date: '',
        dragOptions: {
          dragClass: 'dragging'
        },
        positionModal: {
          show: false,
          user: {}
        },
        lineSettingModal: {
          show: false,
          line_id: 0,
          driver_id: 0,
        },
        noPositionUserFilters: {
          query: ''
        },
        allUserFilters: {
          hide_other_line_user: false,
        },
        filters: {
          delivery_date: '',
          storage_id: 0,
          line_id: 0,
          driver_id: 0,
        },
        storageList: [],
        areaList: [],
        lineList: [],
        driverList: [],
        originalLineUserList: [],
        hasLineUserList: [],
        allUserList: [],
        allApiUserList: [],
        userSum: {
          all: [],
          has_line: [],
          no_line: [],
          in_view: [],
          no_position: [],
        },
        markers: [],
      }
    },
    created() {
      let deliveryDate = this.$route.query.delivery_date;
      this.filters.line_id = this.$route.query.line_id;
      this.filters.line_id || (this.filters.line_id = '');
      // 转字符串
      this.filters.line_id += '';
      if (deliveryDate) {
        this.filters.delivery_date = deliveryDate;
        this.delivery_date = deliveryDate;
      }
      this.getLineList();
    },
    mounted() {
      this.judgeMap()
      this.initHotKey();
    },
    beforeRouteLeave(to, from , next) {
      if (this.modified) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
          onOk: function() {
            next();
          },
          onCancel: () => {
            next(false);
          },
        });
      } else {
        next();
      }
    },
    beforeDestroy() {
      this.$Modal.remove();
    },
    methods: {
      judgeMap(){
        /*eslint-disable*/
        if(typeof AMap !== 'undefined'){
          this.renderMap();
        }else{
          setTimeout(()=>{
            this.renderMap();
          }, 800)
        }
      },
      initHotKey() {
        document.addEventListener('keyup', (e) => {
          // 回车
          if (e.keyCode === 13) {
            // 解绑用户时回车确定
            if (this.unBindingConfirm) {
              this.$Modal.remove();
              this.resetUnBindingConfirm();
              this.unBindUser(this.unBindingUser);
            }
          }
        });
      },
      init(refresh = false) {
        this.getLineList();
        this.getHasLineUser();
        this.getAllUser(refresh);
      },
      showPositionModal(user) {
        user = this.cloneObj(user);
        user.id = user.user_id;
        this.positionModal.user = user;
        this.positionModal.show = true;
      },
      setPositionSuccess() {
        this.closePositionModal();
        this.markers = [];
        this.getAllUser();
      },
      closePositionModal() {
        this.positionModal.user = {};
        this.positionModal.show = false;
      },
      toggleNoPositionUser() {
        this.showNoPositionUser = !this.showNoPositionUser;
      },
      handleDateChange(date) {
        this.filters.delivery_date = date;
        this.mapAutoCenter = true;
        this.init();
      },
      changeStore() {
        this.getAllUser();
        this.getLineList();
      },
      showLineSetting() {
        let currentLine = this.getCurrentLine();
        this.lineSettingModal.driver_id = currentLine.driver_id;
        this.lineSettingModal.show = true;
      },
      closeLineSettingModal() {
        this.lineSettingModal.show = false;
      },
      refresh() {
        let map = this.map;
        map.clearMap();
        this.markers = [];
        let refresh = true;
        this.init(refresh);
      },
      renderMap() {
        /*
        this.map = new AMap.Map('set-line-map', {
          center: [116.397428, 39.90923],
          zoom: 14
        });
        // 缩放地图
        this.map.on('zoomchange', () => {
          this.updateMarkers();
          this.checkInViewUser();
        });
        // 移动地图
        this.map.on('moveend', (evt) => {
          this.updateMarkers();
          this.checkInViewUser();
        });
        */

        window.initMapOver = () => {
          mapService.initMap({
            selector: 'set-line-map',
            center: {
              lat: 28.189765,
              lng: 112.968983
            },
            zoom: 14
          });
          mapService.onZoomChange((evt) => {
            console.log(evt)
          });
          mapService.onMoveEnd((evt) => {
            console.log(evt)
          });
          this.getFilterConfig();
        };

        this.commonService.getConfig().then((config) => {
          let mapType = this.util.getMapType(config);
          mapService.use(mapType, 'initMapOver');
        });

        // this.getAllUser();
      },
      /**
       * 检查在视野范围内的客户
       */
      checkInViewUser() {
        if (!this.map) {
          return false;
        }
        let bounds = this.map.getBounds();
        this.userSum.in_view = [];
        this.markers.forEach((userMarker) => {
          if (bounds.contains(userMarker.marker.getPosition())) {
            this.userSum.in_view.push(userMarker);
          }
        });
      },
      setMapCenter(position) {
        this.map.setCenter(position);
      },
      /**
       *  判断marker是否在地图可视范围内
       */
      isMakerInView(marker) {
        let bounds = this.map.getBounds();
        return bounds.contains(marker.getPosition());
      },
      setMarkerTitle(marker, user) {
        let title = `地址：${user.address_detail}\n下单金额：${user.total_price}\n分拣金额：${user.actual_total_price}\n体积：${user.total_volume}立方\n重量：${user.total_weight}KG`;
        if (this.showUsername) {
          title = `客户名称：${user.name}\n${title}`;
        }
        mapService.setMarkerTitle(marker, title);
      },
      clickMarker(markerUser) {
        if (!this.isChooseLine) {
          this.modalError('请先在左侧选择线路', 0);
          return false;
        }
        // 确保用户信息是最新的
        let user = this.allApiUserList.find((userItem) => userItem.user_id === markerUser.user_id);
        this.hasLineUserList.forEach((listUser, index) => {
          listUser.active = false;
          if (listUser.user_id === user.user_id) {
            listUser.active = true;
            this.hasLineUserList.splice(index, listUser);
          }
        });
        if (this.isCurrentLineUser(user)) {
          this.unBindOneUser(user);
        } else {
          if (this.hasLine(user)) {
            this.addOtherLineUserToCurrentLine([user]);
          } else {
            this.addUserToCurrentLine(user);
          }
          let userMarker = this.markers.find((userMarker) => userMarker.user_id === user.user_id);
          if (userMarker) {
            let marker = userMarker.marker;
            if (!this.isMakerInView(marker)) {
              this.setMapCenter(marker.getPosition());
            }
          }
        }
      },
      animateMarker(marker) {
        marker.setAnimation('AMAP_ANIMATION_BOUNCE');
        setTimeout(() => {
          marker.setAnimation('AMAP_ANIMATION_NONE');
        }, 1000 * 3);
      },
      /**
       * 点击线路中的客户
       */
      clickUserItem(user) {
        let userMarker = this.markers.find((userMarker) => userMarker.user_id === user.user_id);
        if (userMarker) {
          let animateDelay = 0;
          if (!this.isMakerInView(userMarker.marker)) {
            this.map.setCenter(userMarker.marker.getPosition());
            animateDelay = 500;
          }
          setTimeout(() => {
            let userMarker = this.markers.find((userMarker) => userMarker.user_id === user.user_id);
            this.animateMarker(userMarker.marker);
          }, animateDelay);
        }
      },
      /**
       * 生成marker
       */
      setMarkers() {
        let position = {};
        this.markers = [];
        /*
        if (map.getAllOverlays('marker')) {
          map.remove(map.getAllOverlays('marker'));
        }
        */
        mapService.clearMarkers();
        this.allUserList.forEach((user) => {
          if (!this.hasPosition(user)) {
            return false;
          }
          position = {
            lat: Number(user.latitude),
            lng: Number(user.longitude)
          };
          let markerIcon = BLUE_ICON;
          // 已排线,并已保存
          if (this.hasLine(user)) {
            // 没有展开的线路
            if (!this.isChooseLine) {
              markerIcon = BLUE_ICON;
            } else {
              if (this.isCurrentLineUser(user)) {
                markerIcon = RED_ICON;
              } else {
                markerIcon = GRAY_ICON;
              }
            }
          }
          // 未排线或者未保存
          else {
            if (this.isCurrentLineUser(user)) {
              markerIcon = RED_ICON;
            } else {
              markerIcon = BLUE_ICON;
            }
          }

          let markerOptions = {
            icon: {
              image: markerIcon,
              size: [25, 25],
              imageSize: [25, 25]
            },
            position: position,
            offset: [-18, -33]
          };

          let marker = mapService.setMarker(markerOptions);
          this.setMarkerTitle(marker, user);
          mapService.onClickMarker(marker, (evt) => {
            this.clickMarker(user, marker);
          });
          let userMarker = this.cloneObj(user);
          userMarker.marker = marker;
          this.markers.push(userMarker);
        });
        if (this.mapAutoCenter) {
          this.mapAutoCenter = false;
          mapService.setCenter(position);
        }
        this.checkInViewUser();
      },
      /**
       * 更新已排线客户marker
       */
      updateMarkers() {
        //this.setMarkers();
        this.allUserList.forEach((user) => {
          let userMarker = this.markers.find((userMarker) => userMarker.user_id === user.user_id);
          if (!userMarker) {
            return false;
          }
          // 已排线
          if (this.hasLine(user)) {
            // 没有展开的线路
            if (!this.isChooseLine) {
              this.setNormalMarker(userMarker.marker);
            } else {
              if (this.isCurrentLineUser(user)) {
                this.setHighLightMarker(userMarker.marker);
              } else {
                this.setLightMarker(userMarker.marker);
              }
            }
          }
          // 未排线
          else {
            if (this.isCurrentLineUser(user)) {
              this.setHighLightMarker(userMarker.marker);
            } else {
              this.setNormalMarker(userMarker.marker);
            }
          }
          this.setMarkerTitle(userMarker.marker, user);
        });
        this.checkInViewUser();
      },
      /**
       * 设置高亮marker
       */
      setHighLightMarker(marker) {
        /*
        marker.setIcon(new AMap.Icon({
          image: RED_ICON,
          size: new AMap.Size(25, 25),
          imageSize: new AMap.Size(25, 25)
        }));
        */
      },
      /**
       * 设置暗色marker
       */
      setLightMarker(marker) {
        /*
        marker.setIcon(new AMap.Icon({
          image: GRAY_ICON,
          size: new AMap.Size(25, 25),
          imageSize: new AMap.Size(25, 25)
        }));
        */
      },
      /**
       * 设置普通marker
       */
      setNormalMarker(marker) {
        /*
        marker.setIcon(new AMap.Icon({
          image: BLUE_ICON,
          size: new AMap.Size(25, 25),
          imageSize: new AMap.Size(25, 25)
        }));
        */
      },
      /**
       * 点击开始圈选
       */
      handleDraw() {
        if (this.drawing) {
          // 取消圈选
          this.completeDraw();
        } else {
          if (!this.isChooseLine) {
            this.modalError('请选选择线路', 0);
            return false;
          }
          // 开始圈选
          this.drawPolygon();
        }
      },
      completeDraw() {
        if (this.drawObj && this.isChooseLine) {
          let otherLineUser = [];
          this.markers.forEach((userMarker) => {
            let marker = userMarker.marker;
            // 标注点在圈选范围内
            if (this.drawObj.contains(marker.getPosition())) {
              if (!this.hasLine(userMarker)) {
                this.addUserToCurrentLine(userMarker);
              }
              if (this.hasLine(userMarker) && !this.isCurrentLineUser(userMarker)) {
                otherLineUser.push(userMarker);
                return false;
              }
              this.setHighLightMarker(marker);
            }
          });
          // 圈选了其他线路的客户
          if (otherLineUser.length > 0) {
            this.addOtherLineUserToCurrentLine(otherLineUser);
          }
        }
        this.cancelDraw();
      },
      /**
       * 结束圈选
       */
      cancelDraw() {
        let map = this.map;
        this.drawing = false;
        if (this.drawEditor) {
          this.drawEditor.close();
        }
        if (this.mouseTool) {
          this.mouseTool.close();
        }
        if (map.getAllOverlays('polygon')) {
          map.remove(map.getAllOverlays('polygon'));
        }
      },
      /**
       * 开始圈选(当前采用多边形圈选)
       */
      drawPolygon () {
        let map = this.map;
        let mouseTool = new AMap.MouseTool(map);
        mouseTool.polygon({
          strokeColor: "#FF33FF",
          // strokeOpacity: 1,
          strokeWeight: 6,
          strokeOpacity: 0.2,
          fillColor: '#1791fc',
          fillOpacity: 0.4,
          // 线样式还支持 'dashed'
          strokeStyle: "solid",
          // strokeStyle是dashed时有效
          // strokeDasharray: [30,10],
          isOutline: true,
          borderWeight: 3,
          zIndex: 50,
        });
        this.drawing = true;
        mouseTool.on('draw', (event) => {
          mouseTool.close();
          // event.obj 为绘制出来的覆盖物对象
          let polygon = event.obj;
          polygon.setMap(map);
          // 缩放地图到合适的视野级别
          // map.setFitView([ polygon ]);
          let polyEditor = new AMap.PolyEditor(map, polygon);
          polyEditor.open();
          this.drawObj = polygon;
          this.drawEditor = polyEditor;
        });
        this.mouseTool = mouseTool;
      },
      /**
       * 添加其他线路的客户到当前线路
       */
      addOtherLineUserToCurrentLine(userList) {
        this.$Modal.confirm({
          render: (h) => {
            return h('div', [
              h('span', '您选择的客户:'),
              h('span',{
                style: {
                  fontWeight: 'bolder'
                }
              } , userList.map((user) => user.name).join('，')),
              h('span', '已经分配了线路，确定要更换为现在的线路吗？'),
            ]);
          },
          onOk: () => {
            userList.forEach((user) => {
              this.addUserToCurrentLine(user);
            });
          }
        });
      },
      /**
       * 把用户从当前线路解绑
       */
      removeUserFromCurrentLine(user) {
        this.hasLineUserList.forEach((lineUser, index) => {
          if (user.user_id === lineUser.user_id) {
            this.hasLineUserList.splice(index, 1);
          }
        });
      },
      /**
       * 把用户添加到当前线路
       */
      addUserToCurrentLine(user, line) {
        if (!this.hasLineUserList.find((lineUser) => lineUser.user_id === user.user_id)) {
          this.hasLineUserList.push(user);
        }
      },
      /**
       * 是否已打点
       */
      hasPosition(user) {
        return user && user.latitude
          && parseInt(user.latitude) !== 0
          && user.longitude
          && parseInt(user.longitude) !== 0
          && !isNaN(user.latitude)
          && !isNaN(user.longitude);
      },
      /**
       * 是否已排线
       */
      hasLine(checkUser) {
        // 确保用户信息是最新的
        let user = this.allApiUserList.find((userItem) => userItem.user_id === checkUser.user_id);
        // 没有订单的客户
        if (!user) {
          user = checkUser;
        }
        return user && user.line_id && parseInt(user.line_id) !== 0;
      },
      /**
       * 是否是当前线路的客户
       */
      isCurrentLineUser(checkUser) {
        let currentLine = this.getCurrentLine();
        // 确保用户信息是最新的
        let user = this.allApiUserList.find((userItem) => userItem.user_id === checkUser.user_id);
        // 没有订单的客户
        if (!user) {
          user = checkUser;
        }
        return (currentLine && user.line_id && currentLine.line_id === user.line_id) || (this.hasLineUserList.find((lineUser) => lineUser.user_id === user.user_id));
      },
      isCurrentLine(line) {
        let currentLine = this.getCurrentLine();
        return line && currentLine && currentLine.line_id === line.line_id;
      },
      getCurrentLine() {
        return this.currentLine;
      },
      resetCurrentLine() {
        this.currentLine = {
          line_name: '',
          line_id: '',
          driver_name: '',
          price: '',
          actual_price: '',
          user_total: '',
          user_line_total: '',
        };
        this.hasLineUserList = [];
      },
      closeLineDetail() {
        if (this.modified) {
          let currentLine = this.getCurrentLine();
          this.$Modal.confirm({
            okText: '保存',
            content: '检测到当前线路的客户发生了变化，是否确定保存?',
            onOk: () => {
              this.batchBindUser(currentLine.line_id);
            },
            onCancel: () => {
              this.resetCurrentLine();
            }
          });
        } else {
          this.resetCurrentLine();
          this.filterAllUser();
        }
      },
      /**
       * 切换线路
       */
      changeLine(line) {
        this.currentLine = line;
        this.lineList.forEach((lineItem) => {
          lineItem.active = false;
          if (lineItem.line_id === line.line_id) {
            lineItem.active = true;
          }
        });
        this.getHasLineUser();
      },
      /**
       * 切换司机
       */
      changeDriver() {
        if (!this.filters.delivery_date) {
          this.modalError('请选择发货日期', 0);
          return false;
        }
        let currentLine = this.getCurrentLine();
        let today = this.getToday();
        let tip = new Date(today).getTime() - new Date(this.filters.delivery_date).getTime() > 0 ? '当天' : '及之后';
        if (!this.isChooseLine) {
          this.modalError('请选线路', 0);
          return false;
        }
        this.$Modal.confirm({
          render: (h) => {
            return h('div', [
              h('p', `发货日期：${this.filters.delivery_date}`),
              h('p', `更换司机将更新${this.filters.delivery_date}${tip}所有未发货订单的司机!`),
            ]);
          },
          onOk: () => {
            let params = {
              delivery_date: this.filters.delivery_date,
              line_id: currentLine.line_id,
              driver_id: this.lineSettingModal.driver_id,
            };
            this.$request.post(this.apiUrl.delivery.setLine.updateDriver, params).then((res) => {
              let {status, message} = res;
              this.unBinding = false;
              if (status) {
                this.getLineList();
                this.modalSuccess({
                  content: message ? message : '司机切换成功',
                  onOk: () => {
                    this.closeLineSettingModal();
                  }
                });
              } else {
                this.modalError(message ? message : '切换失败', 0);
              }
            });
          },
          onCancel: () => {
          }
        });
      },
      getLineList() {
        let params = {
          source: 'map',
          page: 1,
          pageSize: 999,
          delivery_date: this.filters.delivery_date,
        };
        let activeLine = this.lineList.find((line) => line.active);
        this.$request.get(this.apiUrl.delivery.line.list, params).then((res) => {
          let {status, data} = res;
          if (status) {
            let lineList = data.list;
            lineList.forEach((line) => {
              line.active = false;
              if (line.line_id === this.currentLine.line_id) {
                this.currentLine = line;
                line.active = true;
              } else {
                if (activeLine && line.line_id === activeLine.line_id) {
                  line.active = true;
                }
              }
            });
            this.lineList = lineList;
          } else {
            this.lineList = [];
          }
        });
      },
      getFilterConfig() {
        let params = {
          store_id: this.filters.storage_id
        };
        this.$request.get(this.apiUrl.delivery.setLine.allotUserConfig, params).then((res) => {
          let {status, data} = res;
          if (status) {
            data.area_list.forEach((area) => {
              area.checked = true;
            });
            this.storageList = data.warehouse_list;
            this.driverList = data.driver_list;
            this.areaList = data.area_list;
            if (!this.delivery_date) {
              this.delivery_date = data.delivery_date;
              this.filters.delivery_date = data.delivery_date;
            }
            if (!this.filters.storage_id && this.storageList) {
              this.filters.storage_id = this.storageList[0].id;
            }
            this.getAllUser();
          } else {
            this.storageList = [];
          }
        });
      },
      /**
       * 获取未绑定线路的客户
       */
      getHasLineUser() {
        let currentLine = this.getCurrentLine();
        let params = {
          delivery_date: this.filters.delivery_date,
          line_id: '',
        };
        if (!currentLine) {
          this.hasLineUserList = [];
          this.originalLineUserList = [];
          return false;
        }
        params.line_id = currentLine.line_id;
        // 暂存未绑定线路还没有保存或者切换线路的客户
        let addUserList = this.hasLineUserList.filter((user) => !this.hasLine(user) || !this.isCurrentLineUser(user));
        this.$request.get(this.apiUrl.delivery.setLine.searchLineUser, params).then((res) => {
          let {status, data} = res;
          if (status) {
            data.items.forEach((item) => {
              item.active = false;
            });
            this.originalLineUserList = this.cloneObj(data.items);
            let hasLineUserList = this.cloneObj(data.items);
            if (addUserList && addUserList.length > 0) {
              addUserList.forEach((user) => {
                if (!hasLineUserList.find((lineUser) => lineUser.user_id === user.user_id)) {
                  hasLineUserList.push(user);
                }
              });
            }
            this.hasLineUserList = hasLineUserList;
            this.filterAllUser();
            this.updateMarkers();
          } else {
            this.originalLineUserList = [];
            this.hasLineUserList = [];
          }
        });
      },
      filterAllUser() {
        let userList = this.allApiUserList.filter((user) => {
          if (!this.allUserFilters.hide_other_line_user) {
            return true;
          } else {
            return (this.isCurrentLineUser(user) || !this.hasLine(user));
          }
        });
        if (!this.isChooseLine) {
          userList = this.cloneObj(this.allApiUserList);
        }
        this.allUserList = userList;
        this.setUserSum();
      },
      /**
       * 获取已绑定线路的客户
       */
      getAllUser(refresh = false) {
        let params = {
          order_user_type: orderFilter.hasOrder // 有单客户
        };
        params.delivery_date = this.filters.delivery_date;
        params.line_id = this.filters.line_id;
        params.store_id = this.filters.storage_id;
        params.page = 1;
        params.pageSize = 5000;
        if (refresh === true) {
          this.allUserList = [];
        }
        this.resetUserSum();
        if (!params.delivery_date) {
          this.allUserList = [];
          this.allApiUserList = [];
          return false;
        }
        this.$request.get(this.apiUrl.delivery.setLine.searchBeAllotUser, params).then((res) => {
          let {status, data} = res;
          if (status) {
            this.allApiUserList = data.list;
            this.filterAllUser();
            this.updateMarkers();
          } else {
            this.allUserList = [];
            this.filterAllUser();
            this.updateMarkers();
          }
        });
      },
      resetUserSum() {
        this.userSum = {
          all: [],
          has_line: [],
          no_line: [],
          in_view: [],
          no_position: [],
        };
      },
      setUserSum() {
        this.resetUserSum();
        this.allApiUserList.forEach((user) => {
          if (this.hasLine(user)) {
            this.userSum.has_line.push(user);
          } else {
            this.userSum.no_line.push(user);
          }
          if (!this.hasPosition(user)) {
            this.userSum.no_position.push(user);
          }
        });
      },
      unbindAll() {
        if (this.unBindingAll) {
          return false;
        }
        this.unBindingAll = true;
        this.$Modal.confirm({
          content: '确定解绑所有当前已绑定的客户？',
          onOk: () => {
            let params = {
              delivery_date: this.filters.delivery_date
            };
            this.$request.post(this.apiUrl.delivery.line.unbindAllLine, params).then((res) => {
              let {status, message} = res;
              this.unBindingAll = false;
              if (status) {
                this.init();
                this.modalSuccess(message ? message : '解绑成功', 300);
              } else {
                this.modalError(message ? message : '解绑失败', 300);
              }
            });
          },
          onCancel: () => {
            this.unBindingAll = false;
          }
        });
      },
      resetUnBindingConfirm() {
        this.unBindingConfirm = false;
      },
      setUnBindingConfirm(user) {
        this.unBindingConfirm = true;
        this.unBindingUser = user;
      },
      unBindOneUser(user) {
        if (!this.hasLine(user)) {
          this.removeUserFromCurrentLine(user);
          return false;
        }
        let currentLine = this.getCurrentLine();
        this.setUnBindingConfirm(user.user_id);
        this.$Modal.confirm({
          render: (h) => {
            return h('div', [
              h('span', '确定将客户'),
              h('span', {
                style: {
                  fontWeight: 'bolder'
                }
              }, user.name),
              h('span', '从线路'),
              h('span', {
                style: {
                  fontWeight: 'bolder'
                }
              }, currentLine.line_name),
              h('span', '解除吗？'),
            ]);
          },
          onOk: () => {
            this.resetUnBindingConfirm();
            this.unBindUser(user.user_id);
          },
          onCancel: () => {
            this.resetUnBindingConfirm();
          }
        });
      },
      cancelBind() {
        this.hasLineUserList = this.cloneObj(this.originalLineUserList);
      },
      batchUnBindUser() {
        if (!this.hasLineUserList || this.hasLineUserList.length === 0) {
          this.modalError('请先选择用户');
          return false;
        }
        let uid = this.hasLineUserList.map((user) => {
          return user.user_id;
        });
        this.$Modal.confirm({
          content: `确定解锁当前线路的所有客户？`,
          onOk: () => {
            let isBatch = true;
            this.unBindUser(uid.join(','), isBatch);
          }
        });
      },
      unBindUser(user_id, isBatch = false) {
        let params = {
          user_id,
          delivery_date: this.filters.delivery_date,
        };
        if (this.unBinding) {
          return false;
        }
        this.unBinding = true;
        this.$request.post(this.apiUrl.delivery.setLine.unbindUser, params).then((res) => {
          let {status, message} = res;
          this.unBinding = false;
          if (status) {
            if (isBatch) {
              this.hasLineUserList = [];
            }
            this.init();
            this.modalSuccess(message ? message : '解绑成功', 0);
          } else {
            this.modalError(message ? message : '解绑失败', 0);
          }
        });
      },
      batchBindUser(lineId) {
        if (!this.hasLineUserList || this.hasLineUserList.length === 0) {
          this.modalError('请先选择用户');
          return false;
        }
        let uid = this.hasLineUserList.map((user) => {
          return user.user_id;
        });
        this.bindUser(uid.join(','), lineId);
      },
      bindUser(user_id, lineId) {
        let currentLine = this.getCurrentLine();
        if (!this.isChooseLine && !lineId) {
          this.modalError('请先选择线路', 0);
          return false;
        }
        let params = {
          user_id,
          line_id: lineId ? lineId : currentLine.line_id,
          delivery_date: this.filters.delivery_date,
          sequence_user_id: user_id
        };
        if (this.binding) {
          return false;
        }
        this.binding = true;
        this.$request.post(this.apiUrl.delivery.setLine.bindUser, params).then((res) => {
          let {status, message} = res;
          this.binding = false;
          if (status) {
            this.modalSuccess({
              content: message ? message : '保存成功',
              onOk: () => {
                this.hasLineUserList = this.cloneObj(this.originalLineUserList);
                this.closeLineDetail();
                this.init();
              }
            }, 300);
          } else {
            this.modalError(message ? message : '保存失败', 300);
          }
        });
      },
      back() {
        this.$router.push({
          path: '/delivery/line/list'
        })
      }
    }
  }
</script>
<style lang="less">
  .set-position-modal {
    .ivu-modal-footer {
      display: none;
    }
  }
</style>
<style lang="less" scoped>
  @gutter: 15px;
  .set-line-map {
    text-align: left;
    font-size: 14px;
    .text-right {
      text-align: right;
    }
    .text-center {
      text-align: center;
    }
    .color-light {
      color: #999;
    }
    .color-active {
      color: #03ac54;
    }
    .color-red {
      color: red;
    }
    .text-bold {
      font-weight: bolder;
    }
    .dragging {
      background: #ccc !important;
      cursor: move;
    }
    .sortable-chosen {
      background: #ccc !important;
      cursor: move;
    }
    .ivu-select {
      min-width: 140px;
      text-align: center;
    }
    .card {
      cursor: pointer;
      margin-bottom: 5px;
      background: #f2f2f2;
      padding: 5px 10px;
    }
    .set-line-main {
      border: 1px solid #e3e3e3;
      margin-top: 10px;
      border-radius: 2px;
      .left-part {
        width: 365px;
      }
      .right-part {
        flex: 1;
        padding: 5px 5px 5px 0;
        .right-part-wrap {
          display: flex;
          flex-direction: column;
          height: 100%;
          .map-wrap {
            position: relative;
            flex: 1
          }
        }
        .map-header {
          .view-btn {
            height: auto;
            padding: 2px 7px;
            margin-left: 10px;
          }
          .no-position-user {
            position: absolute;
            top: 24px;
            right: 0;
            width: 300px;
            z-index: 255;
            padding: 5px;
            background: #fff;
            .user-item {
              .card;
            }
          }
        }
      }
      .line-container {
        padding: 5px;
        .title {
          .card;
          text-align: center;
          position: relative;
          .right {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .one-line-wrap {
          display: flex;
          flex-direction: column;
          height: 100%;
          overflow: hidden;
          .line-user {
            flex: 1;
            overflow: auto;
          }
          .line-user-op {
            margin-bottom: 0;
          }
        }
        .line-info {
          width: 100%;
          .card;
          .line-name {
            .ivu-icon {
              font-size: 20px;
              position: relative;
              top: 2px;
            }
          }
          &.active {
            color: #fff;
            .color-light {
              color: #fff;
            }
            background: #03ac54;
          }
        }
        .line-user {
          .user-item {
            .card;
            .user-sequence {
              width: 24px;
              font-weight: bold;
            }
            .ivu-icon {
              font-size: 18px;
              position: relative;
              top: 2px;
            }
            .user-name {
              position: relative;
              flex: 1;
              &:before {
                content: "";
                position: absolute;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 80%;
                background: #ccc;
              }
            }
            .no-order-user {
              margin-left: 10px;
            }
            .unbind {
              width: 40px;
            }
          }
          &.active {
            background: #ccc;
          }
        }
        .no-user {
          text-align: center;
          color: #777;
          margin-bottom: 5px;
          padding: 10px
        }
      }
      .line-user-op {
        width: 100%;
        margin-bottom: 5px;
      }
    }
  }
</style>
