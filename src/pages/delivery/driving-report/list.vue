<template>
    <div class="driving-report common">
      <!-- data-provider="/superAdmin/deliveryMap/AjaxGetGpsDeviceList" -->
      <ListTable
      data-provider="/superAdmin/deliveryMap/GetGpsTravelReport"
      table-id="driving-report_01"
      :filter-items="filterItems"
      :columns="columns"
      :border="false"
      :outer-border="true"
      :filters="filters"
      ref="listTable"
    >
    </ListTable>
    <Modal
      v-model="detailsModal"
      title="详情"
      width="1000"
      footer-hide
      @on-cancel="detailsModal = false;"
      class="details-modal"
    >
      <div
          :style="{maxHeight: getTableHeight()-150 + 'px', overflowY: 'auto', overflowX: 'hidden'}">
        <ListTable
          ref="detailTable"
          class="detail-table"
          :columns="detailColumns"
          :border="false"
          :outer-border="true"
          :auto-load-data="false"
          data-provider="/superAdmin/deliveryMap/GetGpsAlarmDetailReport"
          :filters="detailTableFilters"
        >
        </ListTable>
      </div>
    </Modal>
    </div>
</template>
<script>
import ListTable from '@components/list-table';
import DateUtil from '@/util/date';
import deliveryRes from '@/api/delivery';
import { debounce } from 'lodash-es';
import DriverSelect from '@/components/standard/sdp-filter-items/src/components/driverSelect.vue';

const format = 'YYYY-MM-DD HH:mm:ss';
const formatDate = 'YYYY-MM-DD';
const startTime = DateUtil.getTodayDate(formatDate) + ' 00:00:00';
const endTime = DateUtil.getTodayDate(formatDate) + ' 23:59:59';

export default {
  name: 'DrivingReport',
  components: {
    ListTable
  },
  data() {
    return {
      isFistLoad: true,
      detailsModal: false,
      detailTableFilters: {},
      filters: {},
      filterItems: [
        {
          checked: false,
          type: 'DatePicker',
          props: {
            type: 'datetimerange',
            placeholder: '请选择日期',
          },
          defaultValue: [startTime, endTime],
          key: ['start_time', 'end_time'],
          label: '行驶日期',
          onChange: value => {
            let start_date = new Date(value[0].replace(/-/g, '/')).getTime();
            let end_date = new Date(value[1].replace(/-/g, '/')).getTime();
            let day31 = 1000 * 60 * 60 * 24 * 31 -1;
            if (end_date - start_date > day31) {
              this.errorNotice({
                title: '最多选择31天',
              });
              this.$refs.listTable.setValue(['start_time', 'end_time'], [startTime, endTime]);
              return {value:[startTime, endTime]}
            }
            return {value}
          }
        },
        { 
          label: '车牌号',
          type: 'Select',
          key: 'vehicle_ids',
          data: [
            {
              label: this.$route.query.license_plate,
              value: this.$route.query.id,
            }
          ],
          defaultValue: this.$route.query.id,
          component: DriverSelect,
          // props:{
          //   // multiple: true,
          //   filterable:true,
          //   // 'filter-by-label':true,
          //   clearable: true,
          //   loading: false,
          //   'remote-method': this._getLicensePlateList,
          // },
          type: 'custom',
        }
      ],
      columns: [
        {
          title: '车牌号',
          key: 'license_plate',
          width: 200
        },
        {
          title: '关联司机',
          key: 'driver_name'
        },
        {
          title: '总里程数',
          key: 'distance'
        },
        {
          title: '行驶时长',
          key: 'duration'
        },
        {
          title: '最大速度',
          key: 'max_speed'
        },
        {
          title: '最小速度',
          key: 'min_speed'
        },
        {
          title: '报警时长',
          key: 'times'
        },
        {
          title: '报警次数',
          key: 'counts'
        },
        {
          title: '操作',
          type: 'action',
          actions: [
            {
              name: '报警明细',
              action: params => {
                // this.$_onEdit(params);
                this.getDetailList(params.row)
              }
            },
            {
              name: '行驶轨迹',
              action: params => {
                this.goToDeliveryMap(params.row);
              }
            }
          ]
        }
      ],
      detailColumns: [
        {
          title: '车牌号码',
          key: 'license_plate',
          width: 200
        },
        {
          title: '司机姓名',
          key: 'driver_name',
          poptip: true
        },
        {
          title: '报警类型',
          key: 'param',
          poptip: true
        },
        {
          title: '报警开始时间',
          key: 'recvtime',
        },
        {
          title: '报警结束时间',
          key: 'erecvtime',
        },
        {
          title: '报警时长',
          key: 'times'
        },
        {
          title: '报警状态',
          key: 'state',
          poptip: true
        },
        {
          title: '报警位置',
          key: 'posinfo',
          poptip: true
        },
      ],
    }
  },
  methods: {
    goToDeliveryMap(row) {
      const date = (this.$refs.listTable.getParams().end_time || '').split(' ')[0] || ''
      this.$router.push({ path: '/deliveryMap', query: { id: row.vehicle_id, date: date } })
    },
    _getLicensePlateList: debounce( function(licensePlate = '') {
      if (this.isFistLoad) {
        this.isFistLoad = false;
        return
      }
      this.filterItems[1].props.loading = true;
      deliveryRes.getLicensePlateList({ license_plate: licensePlate }).then(res => {
        const { data } = res
        const _data = data&&data.list || [];
        this.filterItems[1].data = _data.map(item => ({ value: item.vehicle_id, label: item.license_plate }))
        this.filterItems[1].props.loading = false;
      });
    },300
  ),
    getDetailList (data = {}) {
      this.detailsModal = true;
      this.detailTableFilters = this.$refs.listTable.getParams();
      this.detailTableFilters.vehicle_ids = data.vehicle_id;
      console.log('params', this.detailTableFilters);
      this.$refs.detailTable.fetchData(true)
    },
  },
  async mounted() {
    const id = this.$route.query.id;
    const license_plate = this.$route.query.license_plate;
    const date = this.$route.query.date;
    const endTime =  `${date} 23:59:59`
    const startTime = `${date} 00:00:00`
    let defaultStr = ''
    if (id) {
      this.$refs.listTable.setValue(['start_time', 'end_time'], [startTime, endTime]);
    }
    
    
  }
}
</script>
<style scoped>
/deep/ .ivu-date-picker {
  width: 300px !important;
}
</style>
<style scoped lang="less">
// /deep/ .filter__col {
//   display: flex;
//   align-items: center;
//   margin-right: 12px;
//   // & > :first-child {
//   //   margin-right: 0 !important;
//   //   width: 99px !important;
//   // }
// }
// /deep/ .before-table-head {
//   padding-top: 0px;
// }
/deep/ .ivu-modal .ivu-modal-body {
    padding: 8px 24px 30px;
}
</style>