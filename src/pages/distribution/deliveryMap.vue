<template>
  <div class="mapWrap basePadding delivery-map">
    <article>
      <Row type="flex" :gutter="10" align="middle" class="filter spacing">
        <Col>
          <DatePicker
            type="date"
            v-model="deliveryDate"
            @on-change="handleDateChange"
            placeholder="选择发货日期"
            style="width: 140px"
          ></DatePicker>
        </Col>
        <Col>
          <SelectAndInput
            inputPlaceholder="输入线路名称/司机号码 /客户名称/线路名称"
            selectDefaultValue="keywords"
            :selectData="[
              {
                label: '搜索',
                placeholder: '输入线路名称/司机号码',
                value: 'keywords',
              },
              {
                label: '司机名称',
                placeholder: '输入司机名称',
                value: 'driver_name',
              },
              {
                label: sysEntityText('客户名称'),
                placeholder: '输入客户名称',
                value: 'user_name',
              },
            ]"
            @on-change="selectAndInputChange"
            @on-enter="getMapList"
          />
        </Col>
        <Col>
          <div class="flex">
            <Button type="primary" icon="search" @click="getMapList"
              >查询</Button
            >
          </div>
        </Col>
      </Row>
      <div
        :style="{ height: this.getTableHeight() + 30 + 'px' }"
        class="map-container"
      >
        <LinePanel
          @on-select-change="handleUserSelectChange"
          @on-toggle-driver-marker="handleToggleDriverMarker"
          @on-toggle-tempature="handleTempatureClick"
          ref="linePannel"
          class="line-panel"
          :list="markers"
          :deliveryDate="formatDeliveryDate"
        />
        <div class="vehicle-trajectory" @click="handleShowPlay" v-show="lineArr.length && selectedLine">
          <iconShowEye class="iconShowEye" v-if="!isShowPlay"></iconShowEye>
          <iconHideEye class="iconHideEye" v-if="isShowPlay"></iconHideEye>
          {{isShowPlay ? '隐藏':'显示'}}车辆行驶轨迹
        </div>
        <div id="deliveryMap"></div>
        <play-control
          v-if="isShowPlay && selectedLine"
          :showTempEcharts="isShowEcharts"
          :progress="progress"
          :trackPoints="allPoints"
          :playing="isPlaying"
          @play="handlePlay"
          @multiple="handleMultiple"
          @marker-move="handleSlider"
        />
        <div class="temp_container" v-show="isShowEcharts">
          <Icon type="md-close" @click="isShowEcharts = false"  class="close_icon"/>
          <div class="temp_title">
            <h5>温湿度曲线图</h5>
          </div>
          <img v-if="!loadingChart && curDistributed" src="@/assets/images/icon/deliveryMap_export_icon.png" class="temp_title_icon" @click="exportTempAndHumidity">
          <div v-if="loadingChart" class="chart-loading">
            <Icon type="ios-loading" class="loading-icon" />
            <span>加载中...</span>
          </div>
          <div class="chart" style="height: 260px;" ref="distributed"></div>
          <SEmpty v-show="!curDistributed" />
        </div>
      </div>
    </article>
  </div>
</template>

<script>
import echarts from '@/common/init-echarts.js'
import SEmpty from '@components/s-empty';
import SelectAndInput from '@/components/common/SelectAndInput';

import { get } from '@/api/request';
import delivery from '@api/distribution.js';
import DateUtil from '@/util/date';
import LinePanel from './panel';
import { debounce } from 'lodash-es';
import '@/init/init-map.js';
import PlayControl from './components/play-control';
import iconShowEye from './images/icon-show-eye-inline.svg'
import iconHideEye from './images/icon-hide-eye-inline.svg'
import {uniformSampleOnPolyline, findPointInterval, distanceAlongPath} from './utils'
import { exportLoop } from '@/components/common/export-btn/util';

const hasPosition = (user) =>
  user.longitude &&
  user.latitude &&
  Number(user.latitude) > 0 &&
  Number(user.longitude) > 0;

export default {
  name: 'delivery-map',
  components: {
    LinePanel,
    SelectAndInput,
    SEmpty,
    PlayControl,
    iconShowEye,
    iconHideEye
  },
  data() {
    return {
      isShowEcharts: false,
      curDistributed: false,
      chartsDoms: null,
      loadingChart: false,
      searchParam: '',
      loading: false,
      deliveryDate: this.getCustomToday(),
      filter: {
        delivery_date: this.getCustomToday(),
        keywords: '',
        driver_name: '',
        user_name: '',
      },
      columns: [
        {
          title: '配送线路列表',
          key: 'name',
          align: 'center',
        },
        {
          title: '司机号码',
          key: 'tel',
          align: 'center',
        },
        {
          width: 90,
          title: '配送客户数',
          key: 'userNum',
          align: 'center',
        },
        {
          title: '定位',
          width: 70,
          align: 'center',
          render: (h, params) => {
            return h('Icon', {
              props: {
                type: 'ios-pin',
                size: 20,
              },
              class: [
                params.row.is_active ? 'text--primary' : 'text-color-secondary',
              ],
              style: {
                cursor: 'pointer',
              },
              nativeOn: {
                click: () => {
                  this.getTrace(params.row);
                  this.setActiveDriver(params);
                },
              },
            });
          },
        },
      ],
      markers: [],
      userMarkers: [],
      driverMarkers: [],
      driverPolylinePoint: [],
      initData: [],
      map: '',
      isShowPlay: false,
      lineArr: [],
      marker: null,
      passedPolyline: null,
      progress: 0,
      multiple: 1,
      timeCount: 0,
      isPlaying: false,
      traceList: [],
      allPoints: [],
      selectedLine: null,
      driverId: '',
      isFirstLoad: 0 
    };
  },
  created() {},
  computed: {
    formatDeliveryDate() {
      return DateUtil.format(this.deliveryDate, 'YYYY-MM-DD')
    },
  },
  mounted() {
    this.isFirstLoad++
    this.chartsDoms = this.createCharts([
      // this.$refs.profile,
      // this.$refs.trend,
      this.$refs.distributed
    ]);
    this.distributedDom = this.chartsDoms.pop();
    window.addEventListener('resize', () => {
      Array.isArray(this.chartsDoms)&&
      this.chartsDoms.forEach(item => {
        item.resize()
      })
      this.distributedDom.resize()
    })
    if(this.$route.query.id) {
      this.deliveryDate = this.$route.query.date
      this.filter.delivery_date = this.$route.query.date
      console.log('judgeMap',this.$route.query.id)
    }
    // 判断地图是否加载
    /*eslint-disable*/
    this.judgeMap();
    this.drawDistributed()
  },
  beforeDestroy() {
    Array.isArray(this.chartsDoms)&&
    this.chartsDoms.forEach(item => {
      echarts.dispose(item)
    })
    echarts.dispose(this.distributedDom)
    this.distributedDom = null;
    this.chartsDoms = null;
    this.map.destroy();
    this.map = null;
  },
  activated() {
    if(this.$route.query.id) {
      this.deliveryDate = this.$route.query.date
      this.filter.delivery_date = this.$route.query.date
      // this.$nextTick(() => {
      //   this.deliveryDate = this.$route.query.date
      //   this.handleDateChange(this.$route.query.date)
      // })
      console.log('activated',this.$route.query.id)
    }
    if(this.isFirstLoad !== 1) { //mounted 执行过一次getMapList，这里不再执行
        this.getMapList();
    }
    this.isFirstLoad++
  },
  methods: {
    exportTempAndHumidity: debounce(function() {
      const params = {
        delivery_date: this.formatDeliveryDate,
        driver_id: this.driverId
      }
      get('/superAdmin/deliveryMap/ExportGpsTraceInfo', params).then(
      // (res) => {
      //   if (res.status) {
      //     window.location.href = res.data.url
      //   } else {
      //      this.errorMessage(res.message)
      //   }
      // })
      res => {
          let { status, message, data } = res;
          if (status) {
              this.$store.commit('showTaskCenter', true);
              exportLoop(data.task_no);
          } else {
            if (message) {
              this.errorNotice({
                title: '导出失败',
                desc: message
              });
            } else {
              this.errorNotice({
                title: '导出失败'
              });
            }
          }
        },
        res => {
          let { message } = res;
          if (message) {
            this.errorMessage(message);
          } else {
            this.errorMessage('导出失败！');
          }
        }
      )
    }, 500),

    drawDistributed(value) {
      const distributedData = {
        date: [],
        temp1: [],
        temp2: [],
        temp3: [],
        temp4: [],
        humidity1: [],
        humidity2: [],
        humidity3: [],
        humidity4: [],
      };
      const data = value || []
      this.curDistributed = data.length;
      if (
        this.curDistributed > 0 &&
        !(
          'temp1' in data[0] ||
          'temp2' in data[0] ||
          'temp3' in data[0] ||
          'temp4' in data[0] ||
          'humidity1' in data[0] ||
          'humidity2' in data[0] ||
          'humidity3' in data[0] ||
          'humidity4' in data[0]
        )
      ) {
        this.curDistributed = 0
        return;
      }
      data.forEach((item) => {
        distributedData.date.push(item.create_time.split(' ')[1]);

        if (item.temp1 !== '-') {
          distributedData.temp1.push(parseFloat(item.temp1.replace('°C', '')));
        }
        if (item.temp2 !== '-') {
          distributedData.temp2.push(parseFloat(item.temp2.replace('°C', '')));
        }
        if (item.temp3 !== '-') {
          distributedData.temp3.push(parseFloat(item.temp3.replace('°C', '')));
        }
        if (item.temp4 !== '-') {
          distributedData.temp4.push(parseFloat(item.temp4.replace('°C', '')));
        }
        if (item.humidity1 !== '-') {
          distributedData.humidity1.push(parseFloat(item.humidity1.replace('%', '')));
        }
        if (item.humidity2 !== '-') {
          distributedData.humidity2.push(parseFloat(item.humidity2.replace('%', '')));
        }
        if (item.humidity3 !== '-') {
          distributedData.humidity3.push(parseFloat(item.humidity3.replace('%', '')));
        }
        if (item.humidity4 !== '-') {
          distributedData.humidity4.push(parseFloat(item.humidity4.replace('%', '')));
        }
      });
      const tempLegendData = []
      if(distributedData.temp1.length){
        tempLegendData.push('1路温度（°C）')
      }
      if(distributedData.temp2.length){
        tempLegendData.push('2路温度（°C）')
      }
      if(distributedData.temp3.length){
        tempLegendData.push('3路温度（°C）')
      }
      if(distributedData.temp4.length){
        tempLegendData.push('4路温度（°C）')
      }
      if(distributedData.humidity1.length){
        tempLegendData.push('1路湿度（%）')
      }
      if(distributedData.humidity2.length){
        tempLegendData.push('2路湿度（%）')
      }
      if(distributedData.humidity3.length){
        tempLegendData.push('3路湿度（%）')
      }
      if(distributedData.humidity4.length){
        tempLegendData.push('4路湿度（%）')
      }
      const option = {
        legend: {
          data: tempLegendData,
          // backgroundColor: '#ccc',
          // textStyle: {
          //   color: '#ccc'
          // },
          orient: 'horizontal',
          right: 80,
          top: 10,
        },
        tooltip: {
          trigger: 'axis',
          borderColor: '#333',
          borderWidth: 0,
          padding: 0,
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(3, 172, 84, 1)'
            }
          },
          formatter: (params) => {
            const nameMap = {
              '1路温度（°C）': '℃',
              '2路温度（°C）': '℃',
              '3路温度（°C）': '℃',
              '4路温度（°C）': '℃',
              '1路湿度（%）': '%',
              '2路湿度（%）': '%',
              '3路湿度（%）': '%',
              '4路湿度（%）': '%',
            };

            let tooltip = `<div style="padding: 6px 10px; border-radius:2px;background:rgba(0, 0, 0, 0.30);">`;
            let time = '';
            if(params.length) {
              time = params[0].axisValueLabel || '';
            }

            // 遍历每一条 series 的 tooltip 数据
            params.forEach(item => {
              if (item.value !== undefined && item.value !== null && item.value !== '-') {
                const unit = nameMap[item.seriesName] || '';
                tooltip += `
                  <div style="margin-top: -4px;">
                    <span style="font-size: 12px; font-family: PingFangSC-Regular; color: #fff;">${item.seriesName}:</span>
                    <span style="font-size: 12px; font-family: AvenirNextCondensed-Medium; color: #fff;">${item.value}${unit}</span>
                  </div>`;
              }
            });
            tooltip += `
              <div style="margin-top: -4px;">
                <span style="font-size: 12px; font-family: PingFangSC-Regular; color: #fff;">时间:</span>
                <span style="font-size: 12px; font-family: AvenirNextCondensed-Medium; color: #fff;">${time}</span>
              </div>
            </div>`;
            return tooltip;
          }
        },
        grid: {
          left: '1%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: [
          {
            type: 'category',
            boundaryGap: false,
            data: distributedData.date,
            axisLabel: { color: 'rgba(0, 0, 0, 0.65)' },
            axisLine: {
              lineStyle: {
                color: 'rgba(178, 178, 178, 0.85)',
                width: 0.5
              }
            },
            axisTick: {
              length: 0
            }
          }
        ],
        yAxis: [
          {
            name: '',
            type: 'value',
            axisLine: {
              lineStyle: {
                color: 'rgba(178, 178, 178, 0.85)',
                width: 0.5
              }
            },
            axisLabel: {
              show: true,
              interval: 'auto',
              formatter: '{value}',
              color: 'rgba(0, 0, 0, 0.65)'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(216, 216, 216, .2)'
              }
            },
            axisTick: {
              length: 0
            }
          }
        ],
        xisPointer: {
          link: { xAxisIndex: 'all' },
          label: {
            backgroundColor: '#777'
          }
        },
        graphic: [
          {
            type: 'text',
            left: 80,
            top: 10,
            z: -10,
            bounding: 'raw',
            origin: [75, 75],
            style: {
              text: '',
              width: 150,
              height: 150,
              font: `bolder 1em 'Microsoft YaHei', sans-serif`,
              opacity: 0.4
            }
          }
        ],
        series: [
          {
            type: 'line',
            name: '1路温度（°C）',
            data: distributedData.temp1,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 172, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 172, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: '#FF9F00FF'
              }
            },
            itemStyle: {
              normal: { color: '#FF9F00FF' }
            }
          },
          {
            type: 'line',
            name: '2路温度（°C）',
            data: distributedData.temp2,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: '#1CB465FF'
              }
            },
            itemStyle: {
              normal: { color: '#1CB465FF' }
            }
          },
          {
            type: 'line',
            name: '3路温度（°C）',
            data: distributedData.temp3,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(255, 99, 132, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(255, 99, 132, 1)' }
            }
          },
          {
            type: 'line',
            name: '4路温度（°C）',
            data: distributedData.temp4,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(54, 162, 235, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(54, 162, 235, 1)' }
            }
          },
          {
            type: 'line',
            name: '1路湿度（%）',
            data: distributedData.humidity1,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(255, 206, 86, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(255, 206, 86, 1)' }
            }
          },
          {
            type: 'line',
            name: '2路湿度（%）',
            data: distributedData.humidity2,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(153, 102, 255, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(153, 102, 255, 1)' }
            }
          },
          {
            type: 'line',
            name: '3路湿度（%）',
            data: distributedData.humidity3,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(201, 203, 207, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(201, 203, 207, 1)' }
            }
          },
          {
            type: 'line',
            name: '4路湿度（%）',
            data: distributedData.humidity4,
            smooth: true,
            z: 10,
            symbol: 'circle',
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(3, 272, 84, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(3, 272, 84, 0)'
                  }
                ])
              }
            },
            lineStyle: {
              normal: {
                color: 'rgba(9, 300, 16, 1)'
              }
            },
            itemStyle: {
              normal: { color: 'rgba(9, 300, 16, 1)' }
            }
          },
        ]
      };
      this.drawCharts(this.distributedDom, option);
    },
    createCharts(doms) {
      return doms.map(dom => echarts.init(dom));
    },
    drawCharts(charts, options) {
      if (Array.isArray(charts) && Array.isArray(options)) {
        charts.forEach((chart, index) => chart.setOption(options[index]));
      } else {
        charts.setOption(options);
      }
    },
    judgeMap() {
      /*eslint-disable*/
      if (typeof AMap !== 'undefined') {
        this.initMap();
      } else {
        setTimeout(() => {
          this.initMap();
        }, 800);
      }
    },
    initMap() {
      this.map = new AMap.Map('deliveryMap', {
        resizeEnable: true,
        zoom: 13,
      });
      this.getMapList();
    },
    handleUserSelectChange(selectedLine) {
      this.clearUserMarkers();
      this.clearMap();
      this.selectedLine = selectedLine
      if (selectedLine) {
        this.getTrace(selectedLine);
      } else {
        this.isShowPlay = false;
        // 没有选择线路展示所有司机
        this.setDriverMarkers();
        this.clearDriverPolyline();
      }
    },
    handleTempatureClick(item) {
      this.driverId = item.id;
      this.isShowEcharts = true;
      this.getTempAndhumidity(item);
       // 延迟调用 resize，确保容器已渲染到 DOM 中
      this.$nextTick(() => {
        if (this.distributedDom) {
          this.distributedDom.resize();
        }
      });

    },
    handleToggleDriverMarker(line) {
      let driverMarker = this.driverMarkers.find(
        (item) => item.shop_id === line.shop_id,
      );
      if (!driverMarker) {
        return false;
      }
      if (line.show_driver_marker) {
        driverMarker.show();
        driverMarker.setAnimation('AMAP_ANIMATION_BOUNCE');
        setTimeout(() => {
          driverMarker.setAnimation('AMAP_ANIMATION_NONE');
        }, 2000);
      } else {
        driverMarker.hide();
      }
    },
    setActiveDriver(params) {
      this.markers = this.markers.map((item, index) => {
        item.is_active = false;
        if (params.index === index) {
          item.is_active = true;
        }
        return item;
      });
    },
    handleDateChange(date) {
      this.filter.delivery_date = date;
      this.getMapList();  
    },
    clearMap() {
      // 清除所有覆盖物
      this.map && this.map.clearMap();
      this.polyline = null;
      this.userMarkers = [];
      this.driverMarkers = [];
    },
    clearDriverPolyline() {
      if (!this.map || !this.polyline) {
        return false;
      }
      this.map.remove(this.polyline);
      this.map.remove(this.driverPolylinePoint);
    },
    clearDriverMarkers() {
      if (!this.map || !this.driverMarkers || this.driverMarkers.length === 0) {
        return false;
      }
      this.map.remove(this.driverMarkers);
    },
    clearUserMarkers() {
      if (!this.map || !this.userMarkers || this.userMarkers.length === 0) {
        return false;
      }
      this.map.remove(this.userMarkers);
    },
    setUserMarker(userList = []) {
      let me = this,
        iconSize = 45;
      const errorIconSize = 40;
      const userIconError = new AMap.Icon({
        size: new AMap.Size(errorIconSize, errorIconSize),
        image: 'https://img.shudongpoo.com/common/amap/marker-red.svg',
        imageSize: new AMap.Size(errorIconSize, errorIconSize),
      });
      this.userMarkers = [];
      userList.forEach((user, index) => {
        let lat = Number(user.latitude),
          lng = Number(user.longitude);
        let mark = null;
        if (!hasPosition(user)) {
          return false;
        }
        const setMarkerLabel = (marker, user) => {
          marker.setLabel({
            offset: new AMap.Pixel(20, 20),
            content: `<div class="user-name-label">
              <p>客户名称：${user.email}</p>
              <p>收货手机：${user.tel}</p>
              <p>地址：${user.address_detail}</p>
            </div>`,
          });
        };
        const resetMarkerLabel = (marker) => {
          marker.setLabel({
            offset: new AMap.Pixel(20, 20),
            content: ``,
          });
        };
        const userIcon = new AMap.Icon({
          size: new AMap.Size(iconSize, iconSize),
          image: `https://img.shudongpoo.com/common/amap/marker-${
            user.sign_time ? 'blue' : 'outline-blue'
          }.svg`,
          imageSize: new AMap.Size(iconSize, iconSize),
        });
        // 送达异常
        if (Number(user.is_abnormal) === 1) {
          mark = new AMap.Marker({
            map: me.map,
            icon: userIconError,
            position: [lng, lat],
          });
          const driverLatitude = Number(user.driver_latitude);
          const driverLongitude = Number(user.driver_longitude);
          if (driverLatitude && driverLongitude) {
            let arriveMark = new AMap.Marker({
              map: me.map,
              icon: new AMap.Icon({
                size: new AMap.Size(40, 40),
                image:
                  'https://img.shudongpoo.com/common/amap/car-marker-red.svg',
                imageSize: new AMap.Size(40, 40),
              }),
              position: [driverLongitude, driverLatitude],
            });
            arriveMark.on('mouseover', () => {
              setMarkerLabel(arriveMark, user);
            });
            arriveMark.on('mouseout', () => {
              resetMarkerLabel(arriveMark);
            });
            this.userMarkers.push(arriveMark);
          }
        } else {
          mark = new AMap.Marker({
            map: me.map,
            icon: userIcon,
            position: [lng, lat],
          });
        }
        if (!mark) {
          return false;
        }
        mark.on('mouseover', () => {
          setMarkerLabel(mark, user);
        });
        mark.on('mouseout', () => {
          resetMarkerLabel(mark);
        });
        this.userMarkers.push(mark);
        if (index === 0) {
          this.map.setCenter([lng, lat]);
        }
      });
      // this.map.setFitView();
    },
    setDriverMarkers() {
      this.markers.forEach((item) => {
        this.setDriverMarker(item);
      });
    },
    setDriverMarker(driver) {
      if (!driver || !driver.show_driver_position) {
        return false;
      }
      let me = this;
      let lat = driver.latitude;
      let lng = driver.longitude;
      let iconSize = 38;
      if (!lat || !lng) {
        return false;
      }
      let driverIcon = new AMap.Icon({
        size: new AMap.Size(iconSize, iconSize),
        // image: 'https://img.sdongpo.com/common/driver-location.png',
        image:
          'https://base-image.shudongpoo.com/common/amap/car-marker-green-v2.svg',
        imageSize: new AMap.Size(iconSize, iconSize),
      });
      let marker = new AMap.Marker({
        map: me.map,
        icon: driverIcon,
        position: [lng, lat],
      });
      const setMarkerLabel = (marker, line) => {
        marker.setLabel({
          offset: new AMap.Pixel(20, 20),
          content: `<div class="user-name-label">
              <p>${line.name}</p>
            </div>`,
        });
      };
      const resetMarkerLabel = (marker) => {
        marker.setLabel({
          offset: new AMap.Pixel(20, 20),
          content: ``,
        });
      };
      marker.on('mouseover', () => {
        setMarkerLabel(marker, driver);
      });
      marker.on('mouseout', () => {
        resetMarkerLabel(marker);
      });
      marker.on('click', () => {
        this.$refs.linePannel.toggleItemExpand(driver, true);
      });
      // this.map.setFitView();
      if (lat && lng && typeof this.map.setCenter === 'function') {
        this.map.setCenter([lng, lat]);
      }
      marker.shop_id = driver.shop_id;
      marker.line = driver;
      this.driverMarkers.push(marker);
    },
    renderDriverTrace({ path, line }) {
      let map = this.map;
      // 创建折线实例
      var polyline = new AMap.Polyline({
        path: path,
        borderWeight: 2, // 线条宽度，默认为 1
        strokeColor: 'red', // 线条颜色
        lineJoin: 'round', // 折线拐点连接处样式
      });
      // 将折线添加至地图实例
      map.add(polyline);
      let startIcon = new AMap.Icon({
        size: new AMap.Size(40, 40),
        image: 'https://img.shudongpoo.com/common/amap/marker-start.svg',
      });
      let endIcon = new AMap.Icon({
        size: new AMap.Size(40, 40),
        image: 'https://img.shudongpoo.com/common/amap/marker-end.svg',
      });
      const startMarker = new AMap.Marker({
        map: map,
        icon: startIcon,
        offset: new AMap.Pixel(-25, -35),
        position: path[0],
      });
      const endMarker = new AMap.Marker({
        map: map,
        icon: endIcon,
        offset: new AMap.Pixel(-25, -38),
        position: path[path.length - 1],
      });
      new AMap.Marker({
          map: this.map,
          position: path[path.length - 1],
          icon: new AMap.Icon({
            size: new AMap.Size(24, 30),
            image: require('./images/<EMAIL>'),
            imageSize: new AMap.Size(24, 30),
            imageOffset: new AMap.Pixel(-0, -0)
          }),
          offset: new AMap.Pixel(-13, -26),
      });
      endMarker.setContent(`<div class="driver-marker">
        <div class="driver-marker--hover">
          <p>${line.name}</p>
          <img class="driver-marker-car" src="https://img.shudongpoo.com/common/amap/car.svg" />
        </div>
      </div>`);
      this.driverPolylinePoint = [startMarker, endMarker];
      this.polyline = polyline;
    },
    search(data, argumentObj) {
      let res = data;
      let dataClone = data;
      for (let argu in argumentObj) {
        if (argumentObj[argu].length > 0) {
          res = dataClone.filter((d) => {
            return d[argu].indexOf(argumentObj[argu]) > -1;
          });
          dataClone = res;
        }
      }
      return res;
    },
    handleSearch() {
      this.markers = this.initData;
      if (Number(this.searchParam)) {
        this.markers = this.search(this.markers, { tel: this.searchParam });
      } else {
        this.markers = this.search(this.markers, { name: this.searchParam });
      }
    },
    getTempAndhumidity(rowData) {
      // this.$Message.loading({
      //   content: '加载中...',
      //   duration: 0
      // });
      this.loadingChart = true;
      delivery
        .getTrace({ id: rowData.id, delivery_date: this.filter.delivery_date })
        .then((res) => {
          this.clearDriverPolyline();
          if (res.status) {
            if (rowData.position[0] && rowData.position[1]) {
              this.map.panTo(rowData.position);
            }
            if(res.data.length){
              let path = res.data.map((item) => {
                return new AMap.LngLat(
                  Number(item.longitude),
                  Number(item.latitude),
                );
              });
              this.renderDriverTrace({ path, line: rowData });
              this.drawDistributed(res.data)
            } else {
              this.drawDistributed([])
              // 没有轨迹显示客户坐标点
              this.errorMessage('该线路司机未上传路线轨迹');
            }
            // this.$Message.destroy();
            this.loadingChart = false;
          } else {
            // this.$Message.destroy();
            this.loadingChart = false;
            this.drawDistributed([])
            this.errorMessage('获取数据失败，请稍后再试');
          }
        })
        .catch(() => {
          this.loadingChart = false;
          // this.$Message.destroy();
          // this.errorMessage('获取数据失败，请稍后再试');
          this.drawDistributed([])
          this.clearDriverPolyline();
        })
        .finally(() => {
          this.setUserMarker(rowData.ordinates);
        });
    },
    getTrace(rowData) {
      // this.$Message.loading({
      //   content: '加载中...',
      //   duration: 0
      // });
      this.loadingChart = true;
      delivery
        .getTrace({ id: rowData.id, delivery_date: this.filter.delivery_date })
        .then((res) => {
          this.clearDriverPolyline();
          if (res.status) {
            if (rowData.position[0] && rowData.position[1]) {
              this.map.panTo(rowData.position);
            }
            this.isShowPlay = false;
            this.timeCount = 0;
            this.isMoveAlong = false;
            this.traceList = res.data;
            if(res.data.length){
              let path = res.data.map((item) => {
                return new AMap.LngLat(
                  Number(item.longitude),
                  Number(item.latitude),
                );
              });
              this.lineArr = path;
              this.renderDriverTrace({ path, line: rowData });
              this.drawDistributed(res.data)
            } else {
              this.lineArr = []
              this.drawDistributed([])
              // 没有轨迹显示客户坐标点
              this.errorMessage('该线路司机未上传路线轨迹');
            }
            // this.$Message.destroy();
            this.loadingChart = false;
          } else {
            this.drawDistributed([])
            // this.$Message.destroy();
            this.loadingChart = false;
            this.errorMessage('获取数据失败，请稍后再试');
          }
        })
        .catch((err) => {
          this.drawDistributed([])
          // this.$Message.destroy();
          this.loadingChart = false;
          // this.errorMessage('获取数据失败，请稍后再试');
          this.clearDriverPolyline();
        })
        .finally(() => {
          this.setUserMarker(rowData.ordinates);
        });
    },
    getMapList: debounce(function () {
      this.loading = true;
      delivery.getDeliveryMapList(this.filter).then((res) => {
        this.clearMap();
        this.loading = false;
        let { data, status } = res;
        const isFilterTody =
          DateUtil.getTodayDate() === this.filter.delivery_date;
        if (status && data && data.length > 0) {
          this.markers = this.initData = res.data.map((item) => {
            let lng = Number(item.longitude);
            let lat = Number(item.latitude);
            if (item.ordinates) {
              item.ordinates.forEach((userItem) => {
                if (userItem.sign_time) {
                  userItem.sign_time = DateUtil.format(
                    userItem.sign_time,
                    'H:mm',
                  );
                }
              });
            }
            item.show_driver_position = isFilterTody && hasPosition(item);
            item.is_active = false;
            item.position = [lng, lat];
            item.visible = true;
            item.animation = 'AMAP_ANIMATION_NONE';
            item.title = `路线名称: ${item.name}
              司机号码: ${item.tel}
              总单数: ${item.orderNum}`;
            return item;
          });
          if (isFilterTody) {
            this.setDriverMarkers();
          }
          setTimeout(() => {
            if( this.$route.query.id 
              && this.formatDeliveryDate === this.$route.query.date
            ) { //判断一下等于今天，就自动点开详情面板，不然选了其他的也会点开详情面板
              const id = this.$route.query.id || ''
              const line = this.markers.find(item => item.vehicle_id === id)
              this.$refs.linePannel.toggleItemExpand(line, true);
            }
          }, 500);
        } else {
          this.markers = [];
          this.clearMap();
        }
      });
    }, 300),
    selectAndInputChange(value) {
      this.filter.keywords = '';
      this.filter.driver_name = '';
      this.filter.user_name = '';
      this.filter[value[0]] = value[1];
    },
    handleShowPlay() {
      this.isShowPlay = !this.isShowPlay;
      this.progress = 0;
      this.timeCount = 0;
      if (!this.isShowPlay) {
        this.map && this.map.clearMap();
        this.handleUserSelectChange(this.selectedLine)
      } else {
        this.getMoveAnimation();
      }
    },
    getMoveAnimation() {
      if (this.lineArr.length === 0) {
        return;
      }
      // 清除所有覆盖物
      this.map && this.map.clearMap();
      this.linePoints = this.lineArr.map(item => ([item.lat, item.lng]));
      this.allPoints = (this.lineArr.length < 100 ? uniformSampleOnPolyline(this.linePoints, 100) : this.linePoints).map(arr => {
        return new AMap.LngLat(
          Number(arr[1]),
          Number(arr[0]),
        )
      });
      this.map.setCenter(this.lineArr[0]);
      this.map.setFitView();

      new AMap.Marker({
          map: this.map,
          position: this.lineArr[0],
          icon: new AMap.Icon({
            size: new AMap.Size(40, 40),
            image: 'https://img.shudongpoo.com/common/amap/marker-start.svg',
          }),
          offset: new AMap.Pixel(-13, -26),
      });

      new AMap.Marker({
          map: this.map,
          position: this.lineArr[this.lineArr.length - 1],
          icon: new AMap.Icon({
            size: new AMap.Size(24, 30),
            image: require('./images/<EMAIL>'),
            imageSize: new AMap.Size(24, 30),
            imageOffset: new AMap.Pixel(-0, -0)
          }),
          offset: new AMap.Pixel(-13, -26),
      });

      this.marker = new AMap.Marker({
          map: this.map,
          position: this.lineArr[0],
          icon: new AMap.Icon({
            size: new AMap.Size(50, 25),
            image: require('./images/<EMAIL>'),
            imageSize: new AMap.Size(50, 25),
            imageOffset: new AMap.Pixel(0, 0),
          }),
          offset: new AMap.Pixel(-43, -14),
          autoRotation: true,
          angle: -90,
      });

      // 绘制轨迹
      var polyline = new AMap.Polyline({
          map: this.map,
          path: this.allPoints, // this.lineArr,
          // showDir: true,
          strokeColor: "#03ac54",  //线颜色
          strokeWeight: 5,      //线宽
      });

      this.passedPolyline = new AMap.Polyline({
          map: this.map,
          strokeColor: "#03ac54",  //线颜色
          strokeWeight: 5,      //线宽
      });
      this.marker.on('moving', (e) => {
        this.timeCount++
        const position = e.passedPath[e.passedPath.length - 1];
        this.getInfoWindowData({position})
        if (this.currentIndex >= this.allPoints.length - 1) {
          this.isMoveAlong = false;
          this.isPlaying = false;
          this.marker.pauseMove();
          // this.progress = 0;
          // this.timeCount = 0;
        }
      });
    },
    getInfoWindowData({ position }) {
      if (!this.traceList.length) return;
      this.currentIndex = this.allPoints.findIndex(line => {
        return line.lat == position.lat && line.lng == position.lng;
      });
      if (this.currentIndex > 0) {
        this.progress = (this.currentIndex / (this.allPoints.length - 1)) * 100;
      }
      // this.map.setCenter(position, true,)
      let lineIdx = findPointInterval(this.lineArr, position)
      this.getInfoWindow(position, lineIdx)
    },
    getInfoWindow(position, index) {
      if (index < 0 || index >= this.traceList.length) return;
      const item = this.traceList[index];
      // 创建信息窗体
      var infoWindow = new AMap.InfoWindow({
        isCustom: true, // 使用自定义窗体
        content: `<div class="delivery_map_info_window">
              <div class="temperature_humidity"><div>温度：${
                (item.temperature || '').indexOf('/') !== -1 ? item.temperature : `${item.temperature || '-'}°C`
              }</div><div>湿度：${
                 (item.humidity || '').indexOf('/') !== -1 ? item.humidity : `${item.humidity || '-'}%`
              }</div></div>
              <div class="date">时间：${item.create_time || '-'}</div>
            </div>`, // 信息窗体的内容可以是任意 html 片段
        offset: new AMap.Pixel(10, -40)
      });
      infoWindow.open(this.map, position);
    },
    handlePlay({ multiple, isPlaying }) {
      this.isPlaying = isPlaying;
      if (this.currentIndex >= this.allPoints.length - 1) {
        this.progress = 0;
        this.timeCount = 0;
      }
      this.multiple = multiple;
      if (!isPlaying) {
        this.marker.pauseMove();
        return;
      }
      if (this.isMoveAlong && !this.isChangeSlider) {
        this.marker.resumeMove();
        return;
      }
      this.isChangeSlider = false;
      const progress = this.progress || 0;
      // 计算对应的路径点索引
      const pointIndex = Math.min(
        this.allPoints.length - 1,
        Math.floor((this.allPoints.length - 1) * (progress / 100))
      );
      // this.timeCount = 0;
      this.marker.moveAlong(this.allPoints.slice(pointIndex), 2000 * multiple, (e) => {
        this.isMoveAlong = true;
        return e;
      });
    },
    handleMultiple: debounce(function ({ multiple, isPlaying }) {
      this.isChangeSlider = false;
      this.isPlaying = isPlaying;
      this.multiple = multiple;
      this.marker.pauseMove();
      this.isChangeSlider = true;
      setTimeout(() => {
        this.handlePlay({ multiple, isPlaying: true });
      }, 300);
    }, 300),
    handleSlider(data) {
      if (!this.isShowPlay) {
        this.handleShowPlay();
      }
      // 确保进度在0-100之间
      const progress = Math.max(0, Math.min(100, data.value));

      // 计算对应的路径点索引
      const pointIndex = Math.min(
        this.allPoints.length - 1,
        Math.floor((this.allPoints.length - 1) * (progress / 100))
      );
      const point = this.allPoints[pointIndex];
      this.marker.setPosition(point);
      this.isChangeSlider = true;

      this.getInfoWindowData({position: point })
      this.map.setCenter(point, true)
      // 暂停动画（防止自动播放干扰）
      this.marker.pauseMove();
      this.isPlaying = false; // 确保动画状态同步
      // 更新进度状态
      this.progress = progress;
      this.timeCount = ((distanceAlongPath(this.allPoints) / 4.63 / this.multiple) * (progress / 100)).toFixed(0);
    },
  },
};
</script>

<style scoped lang="less">
.chart-loading {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #03ac54;
  font-size: 14px;
  z-index: 10;

  .loading-icon {
    font-size: 24px;
    margin-bottom: 8px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
.close_icon {
  position: absolute;
  right: 23px;
  top: 10px;
  cursor: pointer;
  color: #333;
  font-size: 20px;
  z-index: 1000;
}
.temp_container {
  margin-top: -250px;
  background-color: #ffffff;
  z-index: 300;
  position: relative;
  border-left: 1px #E8E8E8FF solid;
  border-right: 1px #E8E8E8FF solid;
  border-bottom: 1px #E8E8E8FF solid;
  border-top: 1px solid transparent;
  box-shadow: 0 -1px 3px rgba(0,0,0,0.25);
}
.temp_title{
  position: absolute;
  left: 10px;
  top: 13px;
  font-size: 16px;
}
.temp_title_icon {
  width: 18px;
  position: absolute;
  left: 110px;
  top: 13px;
  cursor: pointer;
  z-index: 300;
}
.temp_title h5:before {
    background: rgba(0, 0, 0, .7);
    content: "";
    display: inline-block;
    height: 11px;
    margin-bottom: -1px;
    margin-right: 6px;
    width: 3px;
}
.map-wrap {
  z-index: 1;
}
.map-container {
  position: relative;
  .line-panel {
    position: absolute;
    top: 20px;
    bottom: 20px;
    left: 20px;
    max-height: calc(~'100% - 280px');
    z-index: 2;
    overflow: hidden;
  }
}
.mapBtn {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
}
.mapWrap {
  margin-right: 20px;
  padding-top: 8px;
  height: 100%;
  background: #ffffff;
}
.marginB-15 {
  margin-bottom: 15px;
}
.ivu-table-wrapper {
  min-width: 200px;
}
#deliveryMap {
  height: 100%;
  width: 100%;
}
</style>
<style lang="less">
.delivery-map {
  .marker-driver__name {
    margin: -5px;
    padding: 5px;
    background: #fff;
    border: 1px solid #eee;
  }
  .driver-marker {
    position: relative;
    top: 5px;
    left: -7px;
    z-index: 999;
    &-icon {
      width: 45px;
    }
    &--hover {
      display: flex;
      position: relative;
      border-radius: 20px;
      height: 25px;
      white-space: nowrap;
      padding-left: 10px;
      padding-right: 5px;
      background-color: var(--primary-color);
      color: #fff;
      align-items: center;
      &::after {
        position: absolute;
        content: '';
        display: block;
        width: 10px;
        height: 20px;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid transparent;
        border-top: 10px solid var(--primary-color);
      }
    }
    &-car {
      height: 20px;
      margin-left: 5px;
    }
  }
  .user-name-label {
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.2);
    position: absolute;
    left: -1px;
    top: -1px;
    background: #fff;
    padding: 3px;
    border-radius: 2px;
  }
}
.vehicle-trajectory {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 158px;
  height: 42px;
  line-height: 42px;
  background: #FFFFFF;
  box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.1);
  border-radius: 4px;
  z-index: 9;
  font-size: 13px;
  color: rgba(0,0,0,0.85);
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  display: flex;
  align-items: center;
  padding: 0 16px;
  cursor: pointer;
  .iconShowEye, .iconHideEye {
    display: inline-block;
    width: 18px;
    height: 13px;
    background-size: 100% 100%;
    margin-right: 3px;
  }
  &:hover {
    .iconShowEye, .iconHideEye {
      color: var(--primary-color);
      path {
        fill: var(--primary-color);
        stroke: var(--primary-color);
        stroke-width: 2px;
      }
    }
  }
}
</style>
<style lang="less">
.delivery_map_info_window {
  position: relative;
  min-width: 200px;
  // height: 90px;
  background: rgba(0, 0, 0, 0.7);
  box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
  border-radius: 4px;
  padding: 8px 10px;
}
.delivery_map_info_window::after {
  position: absolute;
  top: 74px;
  left: 94px;
  content: '';
  width: 0;
  height: 0;
  border: 6px solid;
  border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
}
.delivery_map_info_window .license_plate {
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #ffffff;
}
.delivery_map_info_window .temperature_humidity {
  position: relative;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
.delivery_map_info_window .date {
  font-size: 13px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  color: #ffffff;
}
</style>
