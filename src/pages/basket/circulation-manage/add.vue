<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="handleCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="500">
      <Form ref="form" :model="formData" :rules="ruleValidate" :label-width="100">
        <FormItem label="借还类型" prop="borrow_type">
          <Select v-model="formData.borrow_type" placeholder="请选择类型">
            <Option v-for="(type, index) in typeList" :key="index" :value="type.value">{{type.label}}</Option>
          </Select>
        </FormItem>
        <FormItem label="客户名称" prop="user_id">
          <UserSearch v-model="formData.user_id"></UserSearch>
        </FormItem>
        <FormItem label="关联订单" prop="order_no">
          <Input v-model="formData.order_no" placeholder="输入关联订单"/>
        </FormItem>
        <FormItem label="周转筐名称" prop="basket_id">
          <Select v-model="formData.basket_id" placeholder="请输入周转筐名称" style="width: 240px">
            <Option v-for="(basket, index) in basketList" :key="index" :value="basket.id">{{basket.basket_name}}</Option>
          </Select>
        </FormItem>
        <FormItem label="数量" prop="borrow_num">
          <Input v-model="formData.borrow_num" :placeholder="borrowNumPlaceholder"/>
        </FormItem>
        <FormItem label="司机">
          <driver placeholder="输入司机名称" v-model="formData.driver_id"></driver>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button  @click="handleCancel">{{cancelText}}</Button>
        <Button type="primary"  @click="save">{{confirmText}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import UserSearch from '@components/user/userSearch'
  import Driver from '@components/delivery/driverSelect'
  const typeList = {
    borrow: {
      label: '借出',
      value: '1',
    },
    return: {
      label: '归还',
      value: '2',
    },
  };
  export default {
    components: {
      UserSearch,
      Driver
    },
    props: {
      show: {
        type: Boolean,
        default: false
      },
      closable: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: '新增'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      confirmText: {
        type: String,
        default: '保 存'
      }
    },
    watch: {
      show(newValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.initFormData();
          this.getBasketList();
        }
      },
      'formData.borrow_type'() {
        this.setBorrowNumPlaceholder();
      },
      'formData.user_id'() {
        this.setBorrowNumPlaceholder();
      },
      'formData.basket_id'() {
        this.setBorrowNumPlaceholder();
      }
    },
    data() {
      return {
        borrowNumPlaceholder: '请输入周转筐数量',
        typeList,
        basketList: [],
        modal: {
          show: false,
          className: 'vertical-center-modal basket-circulation-add'
        },
        saving: false,
        formData: {
        },
        ruleValidate: {
          borrow_type: [
            {
              required: true,
              message: '请选择借还类型',
              trigger: 'blur'
            }
          ],
          user_id: [
            {
              required: true,
              message: '请选择客户',
            }
          ],
          basket_id: [
            {
              required: true,
              message: '请选择周转筐',
              trigger: 'change'
            }
          ],
          borrow_num: [
            {
              required: true,
              message: '请输入周转筐数量',
              trigger: 'change'
            }
          ]
        }
      }
    },
    created() {
      this.modal.show = this.show;
    },
    methods: {
      setBorrowNumPlaceholder() {
        if (+this.formData.borrow_type === 2 && this.formData.user_id && this.formData.basket_id) {
          const params = {
            user_id: this.formData.user_id,
            basket_id: this.formData.basket_id
          };
          this.$request.get(this.apiUrl.basket.getMaxReturnNum, params).then((res) => {
            let {status, data} = res;
            if (status) {
              this.borrowNumPlaceholder = `最大可归还${data.max_return_num}个`;
            }
          });
        } else {
          this.borrowNumPlaceholder = `请输入周转筐数量`;
        }
      },
      initFormData() {
        this.formData = {
          basket_id: '',
          user_id: '',
          borrow_num: '',
          order_no: '',
          driver_id: '',
          borrow_type: '',
        };
        this.$refs.form.resetFields();
      },
      getBasketList() {
        this.$request.get(this.apiUrl.basket.record.selectList).then((res) => {
          let {status, data} = res;
          if (status) {
            this.basketList = data;
          }
        });
      },
      handleCancel() {
        this.$emit('on-cancel');
      },
      save() {
        let params = this.cloneObj(this.formData);
        this.$request.post(this.apiUrl.basket.circulationManage.add, params).then((res) => {
          let { status, message } = res;
          if (status) {
            this.modalSuccess({
              content: message ? message : '保存成功',
              onOk: () => {
                this.$emit('on-ok');
              }
            }, 0);
          } else {
            this.modalError(message ? message : '保存失败', 0);
          }
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  .basket-circulation-add {

  }
</style>
