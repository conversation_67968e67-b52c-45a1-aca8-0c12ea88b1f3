<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="handleCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1000"
    >
      <Row class="base-info" type="flex" align="middle" justify="space-between">
        <Col>
          <Row type="flex" :gutter="60" align="middle">
            <Col>客户名称：{{ info.email }}</Col>
            <Col>周转筐名称：{{ info.basket_name }}</Col>
            <Col>借出数量：{{ info.total_borrow_num }}</Col>
            <Col>借出金额：￥{{ info.total_borrow_price }}</Col>
            <Col>剩余归还数量：{{ info.total_borrow_num - info.total_return_num }}</Col>
          </Row>
        </Col>
        <!-- <Col align="right">
          <Button
            type="primary"
            v-if="!isDetail"
            :disabled="info.remain_amount === 0"
            @click="addRecord"
            >新增</Button
          >
        </Col> -->
      </Row>
      <Table
        v-if="modal.show"
        :height="getTableHeight() - 200"
        :columns="columns"
        :data="list"
        ref="printDom"
      ></Table>
      <div class="desc">
        <p>说明：</p>
        <p>1.周转筐归还操作可以多次进行，归还的数量没有达到借出的数量时，可以再次操作周转筐归还</p>
        <p>2.周转筐归还为不可逆操作，使用的时候请确认客户的周转筐已经实际归还</p>
        <p>3.客户周转筐不能归还超出本次借出的数量</p>
      </div>
    </Modal>
  </div>
</template>

<script>
import NumberInput from '@components/basic/NumberInput';
// const canAudit = (row) => {
//   return !row.audit_status || row.audit_status !== '已审核';
// };
export default {
  name: 'dispose',
  components: {
    NumberInput, // eslint-disable-line
  },
  props: {
    // recordId: {
    //   type: [String, Number],
    // },
    row: {
      type: Object,
      default: () => {}
    },
    show: {
      type: Boolean,
      default: false,
    },
    closable: {
      type: Boolean,
      default: true,
    },
    title: {
      type: String,
      default: '周转筐归还',
    },
    isDetail: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    show(newValue) {
      this.modal.show = newValue;
      if (newValue) {
        this.getData();
        this.initColumns();
      }
    },
  },
  data() {
    return {
      modal: {
        show: false,
        className: 'vertical-center-modal deposit-basket-detail hide-footer',
      },
      info: {},
      list: [],
      storeList: [],
      columns: [],
    };
  },
  created() {
    this.modal.show = this.show;
    if (this.show) {
      this.getData();
      this.initColumns();
    }
  },
  methods: {
    initColumns() {
      let columns = [
        {
          title: '单号',
          key: 'order_nos',
        },
        {
          title: '申请归还数量',
          key: 'apply_num',
        },
        {
          title: '审核归还数量',
          render: (h, params) => {
            let { row } = params;
            let key = 'audit_amount';
            return h(NumberInput, {
              style: {
                width: '100px',
                display: 'inline-block',
              },
              props: {
                value: row.borrow_num,
              },
              on: {
                'on-change': (val) => {
                  params.row[key] = val;
                  this.storeList[params.index][key] = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
          },
        },
        {
          title: '金额',
          key: 'total_price',
        },
        {
          title: '状态',
          key: 'status_desc',
        },
        {
          title: '创建时间',
          key: 'create_time',
        },
        {
          title: '审核时间',
          key: 'audit_time',
        },
      ];
      if (!this.isDetail) {
        let style = {
          color: '#03ac54',
          cursor: 'pointer',
        };
        columns.push({
          title: '操作',
          render: (h, params) => {
            let { row } = params;
            return h('div', [
              h(
                'span',
                {
                  style: {
                    ...style,
                    marginLeft: '15px',
                  },
                  on: {
                    click: () => {
                      this.$Modal.confirm({
                        content: '确定审核该记录？',
                        onOk: () => {
                          this.audit(params);
                        },
                      });
                    },
                  },
                },
                '审核'
              ),
            ]);
          },
        });
      }
      this.columns = columns;
    },
    syncList() {
      let list = this.cloneObj(this.list);
      list.forEach((item) => {
        let storeItem = this.storeList.find(
          (storeItem) => storeItem.borrow_id === item.borrow_id
        );
        if (storeItem) {
          item.audit_amount = storeItem.audit_amount;
        }
      });
      this.list = list;
      this.storeList = this.cloneObj(list);
    },
    audit(params) {
      let { row } = params;
      let postParams = {
        borrow_id: row.id,
        borrow_num: row.audit_amount,
        borrow_status: 2,
        is_audit: 1,
      };
      this.$request
        .post(this.apiUrl.basket.circulationManage.edit, postParams)
        .then((res) => {
          let { status, message } = res;
          if (status) {
            this.$Message.success(message ? message : '审核成功！');
            this.handleCancel()
          } else {
            this.$Message.error(message ? message : '审核失败！');
          }
        });
    },
    getData() {
      this.info = this.row;
      this.list = [{ ...this.row }];
      this.syncList()
    },
    handleCancel() {
      this.$emit('on-cancel');
    },
  },
};
</script>

<style lang="less">
.ivu-modal-wrap {
  &.hide-footer {
    .ivu-modal-footer {
      display: none;
    }
  }
}
.deposit-basket-detail {
}
</style>
<style lang="less" scoped>
.base-info {
  margin-bottom: 15px;
}
.desc {
  margin-top: 20px;
  line-height: 20px;
}
</style>
