<template>
  <div class="">
    <!-- 常用商品 -->
    <div class="usual-goods">
      <div class="user-list__operation">
        <div class="user-list__select-middle" sdp-no-replace-text>
          <Select
            @on-change="getUsualGoodsSecondCategory"
            class="category first-category"
            v-model="usualGoods.firstCategoryId"
            placeholder="一级分类"
          >
            <Option
              v-for="item in firstCategory"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
          <Select
            @on-change="getUsualGoods()"
            class="category second-category ml10"
            v-model="usualGoods.secondCategoryId"
            placeholder="二级分类"
          >
            <Option
              v-for="item in usualGoods.secondCategory"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
          <i-input
            placeholder="输入商品编码/名称/助记码/别名"
            v-model="usualGoods.searchValue"
            class="ml10"
          >
            <!-- <i-button slot="append" icon="ios-search" class="search-btn" @click=""></i-button> -->
          </i-input>
          <Select
            @on-change="getUsualGoods()"
            clearable
            v-model="isPrint"
            class="is_print is-print"
            placeholder="手工单打印"
          >
            <Option value="1">是</Option>
            <Option value="0">否</Option>
          </Select>
          <SButton
            @click="getUsualGoods"
            class="filter__button ml10"
            type="primary"
            >查询</SButton
          >
        </div>
        <div style="line-height: 35px; margin-bottom: 10px">
          <!-- <Button type="primary" class="c-green-btn" @click="exportGoods" icon="ios-paperplane">导出常用商品</Button> -->
          <SButton styleType="btnStyleForAdd" @click="userNewGoodsActive = true"
            >新增</SButton
          >
          <Dropdown trigger="click" @on-click="doMore($event)" class="ml10">
            <Button icon="arrow-down-b">批量操作手工单</Button>
            <Dropdown-menu slot="list" style="text-align: center">
              <Dropdown-item name="add">批量添加</Dropdown-item>
              <Dropdown-item name="cancel">批量取消</Dropdown-item>
              <Dropdown-item name="del" divided style="color: red"
                >批量删除</Dropdown-item
              >
            </Dropdown-menu>
          </Dropdown>
          <button class="ivu-btn ml10" @click="exportPdf()">导出手工单</button>
          <Dropdown trigger="click" placement="bottom-start" class="ml10">
            <Button>常用商品导入/导出</Button>
            <DropdownMenu slot="list" style="text-align: center">
              <Button @click="exportGoods">常用商品导出</Button>
              <Upload
                style="display: inline-block; margin-top: 10px"
                :show-upload-list="false"
                :on-success="excelImportSuccess"
                :format="['csv', 'xlsx']"
                :accept="mineType.excel.join(',')"
                :on-format-error="handleExcelUploadFormatError"
                :data="{ user_id: uid }"
                action="/superAdmin/userSuper/ImportCollectionList?is_api=1"
              >
                <Button>常用商品导入</Button>
              </Upload>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
      <i-table
        :columns="columns2"
        :data="usualGoodsList"
        :height="layoutTableHeight - 10"
        @on-selection-change="getCheckedUsualGoods"
      ></i-table>
      <Page
        :total="usualGoods.page.count"
        :current.sync="usualGoods.page.nowPage"
        :page-size="usualGoods.page.pageSize"
        @on-page-size-change="modifyUsualGoodsPage"
        class="js-after-table"
        @on-change="getUsualGoods"
        placement="top"
        show-elevator
        show-total
        show-sizer
      >
      </Page>
      <Modal
        v-model="userNewGoodsActive"
        width="1000"
        class-name="sdp-modal"
        title="新增"
      >
        <goods-select
          ref="goods"
          v-on:selectChange="selectGoods"
          :filter="addUsualGoods.goodsFilter"
        ></goods-select>
        <div slot="footer">
          <i-button type="success" @click="addGoods">保存</i-button>
        </div>
      </Modal>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import { mapMutations } from 'vuex';
import common from '@api/user.js';
import goods from '@api/goods.js';
import Bus from '@api/bus.js';
import { api } from '@api/api.js';
import newGoods from '@components/user/UserNewGoods';
import goodsSelect from '@components/common/goodsSelect';
import SButton from '@components/button';
import LayoutMixin from '@/mixins/layout';
import { MINE_TYPE } from '@/util/const';

export default {
  name: 'NewUser',
  mixins: [LayoutMixin],
  data() {
    return {
      mineType: MINE_TYPE,
      columns2: [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select',
        },
        {
          title: '图片',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40',
              },
            });
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left',
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '商品分类',
          key: 'category_name2',
          align: 'left',
        },
        {
          title: '单位',
          key: 'unit',
          align: 'left',
        },
        {
          title: '描述',
          key: 'summary',
          align: 'left',
        },
        {
          title: '别名',
          key: 'alias',
          align: 'left',
        },
        {
          title: '手工单打印',
          key: 'is_print',
          align: 'left',
          render: (h, params) => {
            var obj = params.row;
            return h('div', [
              h('div', {
                domProps: {
                  innerHTML: obj.is_print == '1' ? '是' : '否',
                },
                style: {
                  margin: '5px 0',
                },
              }),
            ]);
          },
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            let data = params.row,
              id = data.id;

            return h('div', [
              h(
                'PopButton',
                {
                  class: {
                    'table-button': true,
                  },
                  props: {
                    confirm: true,
                    title: '确定删除？',
                    type: 'error',
                    size: 'small',
                  },
                  on: {
                    'on-ok': () => {
                      this.delUsualGoods(id);
                    },
                  },
                },
                '删除',
              ),
              h(
                'i-button',
                {
                  class: {
                    'table-button': true,
                    dn: data.is_print == 0 ? false : true,
                  },
                  props: {
                    type: 'error',
                    size: 'small',
                  },
                  on: {
                    click: () => {
                      this.checkGoods = id;
                      this.usualGoodsPrint(id, this.uid, 1);
                    },
                  },
                },
                '加入手工单',
              ),
              h(
                'i-button',
                {
                  class: {
                    'table-button': true,
                    dn: data.is_print == 0 ? true : false,
                  },
                  props: {
                    type: 'error',
                    size: 'small',
                  },
                  on: {
                    click: () => {
                      this.checkGoods = id;
                      this.usualGoodsPrint(id, this.uid, 0);
                    },
                  },
                },
                '取消手工单',
              ),
            ]);
          },
        },
      ],
      account: '',
      password: '',
      userInfo: {
        account: '',
        password: '',
        name: '',
        code: '',
        type: '0',
        group: '0',
        sales: '0',
        status: '2',
        area: '',
        contact: '',
        tel: '',
        address: '',
        deliveryDate: '',
        printTemplate: '',
      },
      navActive: 0,
      userNewGoodsActive: false,
      uid: '', // 用户详情的ID
      isPrint: '', // 是否加入手工单打印
      usualGoodsList: [],
      usualGoods: {
        checkedGoods: [],
        page: '',
        pageCount: '',
        pageSize: '',
        nowPage: 1,
        searchValue: '',
        firstCategoryId: '',
        secondCategory: [],
        secondCategoryId: '',
        page: {
          count: 0,
          nowPage: 1,
          pageSize: 10,
        },
      },
      addUsualGoods: {
        goodsFilter: {},
        checkedAddGoods: [],
      },
      firstCategory: [],
      checkGoods: '',
    };
  },
  created() {
    this.uid = this.$route.query.uid;
    this.getFirstCategory();
    this.getUsualGoods();
  },
  updated() {},
  mounted() {},
  activated() {},
  computed: {
    ...mapState({}),
  },
  methods: {
    /**
     * @description 新增商品选择发生变化处理函数
     * @param checkedData 新选择的商品 {ids:, detail}
     */
    selectGoods(checkedData) {
      this.addUsualGoods.checkedAddGoods = checkedData.ids;
    },
    /**
     * @description 新增商品时设置不可选的商品
     * <AUTHOR>
     */
    setDisabledGoods() {
      let goodsIds = this.usualGoodsList.map((goods) => {
        return goods.commodity_id;
      });
      this.$refs.goods.setDisableGoods(goodsIds);
    },
    /**
     * @description 获取一级分类
     * <AUTHOR>
     */
    async getFirstCategory() {
      let res = await goods.getGoodsCategory();
      let defaultItem = {
        id: '',
        name: '一级分类',
      };
      if (res.status) {
        this.firstCategory = res.data;
      } else {
        this.firstCategory = [];
      }
      this.firstCategory.unshift(defaultItem);
    },
    /**
     * @description 获取常用商品二级分类
     * <AUTHOR>
     */
    async getUsualGoodsSecondCategory() {
      let defaultItem = {
        id: '',
        name: '二级分类',
      };
      let secondCategory = [];
      let res = await goods.getGoodsCategory(this.usualGoods.firstCategoryId);
      if (res.status) {
        secondCategory = res.data;
      }
      secondCategory.unshift(defaultItem);
      this.usualGoods.secondCategory = secondCategory;
      this.getUsualGoods();
    },
    async modifyUsualGoodsPage() {
      this.usualGoods.page.pageSize = arguments[0];
      this.getUsualGoods();
    },
    async getGoodsCategory() {
      let res = await goods.getGoodsData();
      if (res.status) {
        let data = res.data;
        for (let i = 0, length = data.categoryList.length; i < length; i++) {
          let item = {
            value: data.categoryList[i].id,
            label: data.categoryList[i].name,
            children: [],
          };
          if (data.categoryList[i].items) {
            for (let j = 0; j < data.categoryList[i].items.length; j++) {
              item.children.push({
                value: data.categoryList[i].items[j].id,
                label: data.categoryList[i].items[j].name,
              });
            }
          }
          this.goodsCategory.push(item);
        }
      }
    },

    getUsualGoodsParam() {
      let value = {
        uid: this.uid,
        isPrint: this.isPrint,
        searchValue: this.usualGoods.searchValue,
        firstCategoryId: this.usualGoods.firstCategoryId,
        secondCategoryId: this.usualGoods.secondCategoryId,
        page: this.usualGoods.page.nowPage,
        pageSize: this.usualGoods.page.pageSize,
      };

      return value;
    },
    excelImportSuccess(res) {
      let { status, message } = res;
      if (status) {
        this.successNotice(message || '上传商品成功');
      } else {
        this.errorNotice({
          title: '常用商品导入失败',
          desc: message,
        });
      }
      this.getUsualGoods();
    },
    async getUsualGoods() {
      let value = this.getUsualGoodsParam();
      let res = await common.getUsualGoods(value);
      this.usualGoods.checkedGoods = '';
      if (res.status) {
        let data = res.data.list;
        let page = res.data.pageParams;
        this.usualGoods.page.count = parseInt(page.count);
        this.usualGoodsList = data;
        this.setDisabledGoods();
      }
    },
    handleExcelUploadFormatError(file) {
      this.errorNotice({
        title: '上传失败',
        desc: '上传的excel文件格式不正确,请选择[csv,xlsx]文件.',
      });
    },
    exportGoods() {
      let params = {
        user_id: this.uid,
        searchKey: this.usualGoods.searchValue,
        category_id: this.usualGoods.firstCategoryId,
        category_id2: this.usualGoods.secondCategoryId,
        is_print: this.isPrint,
      };
      this.$request.get(this.apiUrl.exportUsualGoods, params).then((res) => {
        let { status, data, message } = res;
        if (status) {
          location.href = data;
        } else {
          this.modalError(message, 0);
        }
      });
    },
    doMore(name) {
      switch (name) {
        case 'add':
          this.bacthUsualGoodsPrint(1);
          break;
        case 'cancel':
          this.bacthUsualGoodsPrint(0);
          break;
        case 'del':
          this.batchDelUsualGoods();
          break;
      }
    },
    // 批量操作打印
    async bacthUsualGoodsPrint() {
      let value = arguments[0];
      let goodsIds = this.usualGoods.checkedGoods;
      if (!goodsIds || goodsIds.length === 0) {
        this.errorNotice('请先选择商品');
        return false;
      }
      this.usualGoodsPrint(goodsIds, this.uid, value);
    },
    async usualGoodsPrint(goodsIds, uid, value) {
      let res = await common.collectionAddPrint(goodsIds, uid, value);
      let { status, message } = res;
      if (status) {
        this.checkGoods = '';
        this.successNotice(message || '操作成功');
        this.getUsualGoods();
      } else {
        this.errorNotice({
          title: '操作失败',
          desc: message ? message : '操作失败',
        });
      }
    },

    /**
     * @description 批量删除常用商品
     * <AUTHOR>
     */
    batchDelUsualGoods() {
      let goodsIds = this.usualGoods.checkedGoods;
      let self = this;
      if (!goodsIds || goodsIds.length === 0) {
        this.errorNotice('请选择需要删除的商品');
        return false;
      }
      Bus.confirm({
        content: '确定删除选中的商品？',
        okCallback: () => {
          self.delUsualGoods(goodsIds);
        },
      });
    },
    /**
     * @description 删除常用商品
     * <AUTHOR>
     * @param goodsIds 商品id，以','隔开，如：1,23,333
     */
    async delUsualGoods(goodsIds) {
      let res = await common.delUsualGoods({ cid: goodsIds });
      let { status, message } = res;
      if (status) {
        this.successNotice('删除成功');
        this.getUsualGoods();
      } else {
        this.errorNotice({
          title: '删除失败',
          desc: message ? message : '删除失败',
        });
      }
    },
    //导出用户手工单模板
    exportPdf() {
      let params = {
        user_id: this.uid,
      };
      common.exportUserOrderPdfTemplate(params).then((res) => {
        let { status, message } = res;
        if (status) {
          window.location.href =
            '/superAdmin/userSuper/downUserOrderPdfTemplate?path=' + res.data;
        } else {
          this.errorNotice({
            title: '导出失败',
            desc: message ? message : '导出失败!',
          });
        }
      });
    },
    /**
     * @description 获取选中的常用商品
     * <AUTHOR>
     */
    getCheckedUsualGoods() {
      let value = arguments[0],
        array = [];

      if (Array.isArray(value)) {
        array = value.map(function (value, index, array) {
          return value.id;
        });
      }
      this.usualGoods.checkedGoods = array.join();
    },
    async addGoods() {
      let uid = this.uid,
        cid = this.addUsualGoods.checkedAddGoods.join(',');
      if (!cid) {
        this.errorNotice('请选择商品');
        return false;
      }
      let res = await common.setUsualGoods(uid, cid);
      let { message, status } = res;
      if (status) {
        this.successNotice(message || '保存成功');
        this.closeLayer();
        this.getUsualGoods();
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: message ? message : '保存失败',
        });
      }
    },
    closeLayer() {
      this.userNewGoodsActive = false;
    },
  },
  components: {
    goodsSelect,
    newGoods,
    SButton,
  },
};
</script>

<style lang="scss" scoped>
.upload-module .ivu-upload {
  float: none;
  margin-left: 0;
}
.v-transfer-dom {
  /deep/ .ivu-modal .ivu-modal-body {
    padding: 50px 0;
  }
}
.basic-info,
.unavailable-goods,
.usual-goods {
  position: relative;
  width: 100%;
  /* iview custom */
  .ivu-form {
    text-align: left;
  }
  .ivu-select-placeholder,
  .ivu-select-selected-value,
  .ivu-input-wrapper {
    text-align: left !important;
    width: 380px;
  }
  .ivu-form .ivu-form-item-label {
    text-align: center;
    font-size: 14px !important;
  }
  .ivu-btn.ivu-btn-primary {
    background-color: #03ac54;
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}

.special-price {
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}

.nav-items {
  display: inline-block;
  margin-right: 20px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}

.nav-items + .ivu-badge {
  margin-left: -15px;
  margin-right: 10px;
}

.nav-items:hover {
  cursor: pointer;
}

.new-content {
  opacity: 0;
  margin-left: 10px;
  color: #03ac54;
}

.admin-title {
  margin-top: 20px;
}

.admin-title > span {
  font-size: 14px;
  margin-left: 20px;
  color: #80848f;
}

.operation__item {
  // height: 34px;
  padding: 12px 0;
  text-align: left;
}

.original-input {
  width: 500px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 0;
  text-align: right;
  span {
    margin-right: 18px;
  }
  .ivu-cascader {
    display: inline-block;
  }
}

.original-select {
  @extend .original-input;
  width: 395px;
}

.ivu-select {
  width: 275px !important;
}

.ivu-select-placeholder,
.ivu-select-item {
  text-align: left;
}

.ivu-radio-wrapper {
  font-size: 14px;
}

.ivu-radio {
  margin-right: 0 !important;
}

.operation-wrap {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: left;
  button {
    margin: 0 10px;
  }
}

.user-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  /* iview custom */
  .ivu-input-wrapper {
    // float: left;
    margin-right: 10px;
    width: 275px;
  }
  .search-btn {
    /*background-color: #03ac54 !important;*/
  }
  .user-list__left {
    float: left;
  }
  .user-list__right-but {
    padding: 10px 0;
    float: right;
  }
  .ivu-btn {
    font-size: 13px;
  }
}

.user-list__select-middle {
  // margin-left: 10px;
  padding: 10px 0;
  text-align: left;
}

.upload-module {
  text-align: right;

  .upload-module__del {
    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
  button {
    float: none;
  }
}

.user-account {
  position: relative;
  height: 500px;
  .operation-wrap {
    position: absolute;
    bottom: 10px;
  }
  .account-input {
    padding-left: 30px;
  }
  .original-input {
    text-align: left;
  }
  .ivu-input-wrapper {
    width: 200px;
  }
  .account-unit {
    color: #bbbec4;
  }
}

.ivu-page {
  text-align: right;
}

.ivu-form-item {
  display: inline-block;
  width: 380px;
}

.unavailable-goods .ivu-btn.ivu-btn.ivu-btn-primary {
  background-color: #03ac54 !important;
}

$cRed: #ed3f14;
.required-pot {
  color: $cRed;
}
</style>
<style lang="scss">
.payment-list {
  .ivu-input-number {
    width: 200px;
  }
}

.ivu-cascader {
  display: inline-block;
}

.ivu-select-selected-value {
  text-align: left;
}

.ivu-select-placeholder {
  text-align: left;
}

.category.first-category,
.category.second-category,
.is_print.is-print {
  width: 120px !important;
}
</style>
