<template>
  <div>
    <div class="content-wrap basePadding">
      <Form>
        <FormItem label="屏蔽商品模式" class="blockProductModel">
          <RadioGroup v-model="mode" @on-change="onChangeMode">
            <Radio
              v-for="item in modeList"
              :key="item.value"
              :true-value="item.value"
              :label="item.value"
              >{{ item.label }}
              <Tooltip
                :content="item.tooltip"
                v-if="item.tooltip"
                max-width="400"
              >
                <i
                  class="icon-help1 iconfont icontip"
                  style="font-size: 13px;"
                ></i>
              </Tooltip>
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="商品选择模式">
          <RadioGroup :value="chooseType" v-if="hasRadio">
            <Radio
              v-for="item in chooseTypeList"
              @click.native="onChangeChooseType(item.value)"
              :key="item.value"
              :true-value="item.value"
              :label="item.value"
              >{{ item.label }}</Radio
            >
          </RadioGroup>
        </FormItem>
      </Form>
      <Row
        class="selectRow mb10"
        type="flex"
        justify="start"
        v-show="chooseType === 0"
      >
        <Col span="4">
          <category-select
            clearable
            placeholder="全部分类"
            :noThree="!isOpenThreeCategory"
            @on-change="changeCategory"
            style="width: 100%"
          ></category-select>
        </Col>
        <Col span="6" style="margin-left: 10px;">
          <Input
            style="width: 240px"
            placeholder="请输入商品名称/编码进行搜索"
            clearable
            v-model="searchValue"
            @on-enter="search"
          >
            <Button
              type="success"
              @click="search"
              slot="append"
              icon="ios-search"
            ></Button>
          </Input>
        </Col>
        <div class="fixed-60">
          <Button class="ml15" @click="downloadTemplate"> 下载模板</Button>
          <Upload
            :format="['csv', 'xlsx']"
            :on-success="uploadSuccess"
            :show-upload-list="false"
            :action="
              `/superAdmin/userSuper/ImportDisabledCommodity?style=1&user_id=${uid}`
            "
          >
            <Button class="ml15">导入</Button>
          </Upload>
          <Button
            class="ml15"
            :disabled="shieldList.length === 0"
            @click="exportDisabledGoods"
            >导出</Button
          >
          <Button type="primary" class="ml15" icon="md-add" @click="addGoods"
            >新增</Button
          >
          <PopButton
            :confirm="true"
            title="确定批量删除商品？"
            class="ml15"
            @on-ok="removeShieldGoods"
            :disabled="selectedRow.length === 0"
            >批量删除</PopButton
          >
        </div>
      </Row>
      <Table
        v-show="chooseType === 0"
        :columns="columns"
        :data="shieldList"
        :loading="loading"
        :height="layoutTableHeight - 60"
        @on-selection-change="selectRow"
      ></Table>
      <Page
        v-show="chooseType === 0"
        :total="totalPage"
        :current="currentPage"
        :page-size="pageSize"
        :page-size-opts="[20, 30, 40, 50]"
        class="js-after-table"
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        placement="top"
        show-elevator
        show-total
        show-sizer
        style="margin-top: 8px;"
      >
      </Page>
      <div class="unavailable-category" v-if="chooseType === 1">
        <Transfer
          :list-style="{
            width: '400px',
            height: '580px'
          }"
          :titles="['未选分类', '已选分类']"
          @on-change="handleChange"
          :filter-method="filterMethod"
          :data="transferData"
          :target-keys="selectTransferData"
          filterable
          :render-format="categoryRender"
        ></Transfer>
      </div>
      <div class="pageLeftBtn">
        <Button @click="goBack">返 回</Button>
      </div>
    </div>
    <goods-list-modal
      ref="goodsModal"
      :params="{ disable_add: 1 }"
      v-model="showGoodsListModal"
      modalType="shieldGoods"
      @on-add="handlerAdd"
      :selectedGoods="selectedGoods"
      :uid="uid"
      customer="userType"
    >
      <div></div>
      <Col slot="extra" style="text-align: right">
        <PopButton
          :title="
            '确定' +
               (mode == 0 ? (commodity_disabled_mode == 2 ? '售卖' : '屏蔽') : mode == 1 ? '屏蔽' : '售卖') +
              '所有当前筛选的商品？'
          "
          :confirm="true"
          type="primary"
          @on-ok="handleAddAll"
          >{{
            mode == 0 ? (commodity_disabled_mode == 2 ? '售卖筛选商品' : '屏蔽筛选商品') : mode == 1 ? '屏蔽筛选商品' : '售卖筛选商品'
          }}</PopButton
        >
      </Col>
    </goods-list-modal>
  </div>
</template>

<script>
import goods from '@api/goods.js';
import user from '@api/user.js';
import GoodsListModal from '@components/order/goodsListModal';
import LayoutMixin from '@/mixins/layout';
import ConfigMixin from '@/mixins/config';
import { get } from '@/api/request';
import api from '@/api/api';
import CategorySelect from '@components/common/categorySelect';

const chooseTypeList = [
  {
    label: '按商品',
    value: 0
  },
  {
    label: '按分类',
    value: 1
  }
];

const modeList = [
  {
    label: '跟随系统配置',
    value: 0,
    tooltip: '跟随系统屏蔽配置'
  },
  {
    label: '按客户类型屏蔽指定商品',
    value: 1,
    tooltip:
      '选择后，在客户类型中可以设置需要屏蔽的商品，未设置时默认全部商品可售卖。'
  },
  {
    label: '按客户类型设置指定售卖商品',
    value: 2,
    tooltip:
      '开启后，在客户类型中可以设置需要售卖的商品，未设置时默认全部商品不可售卖。'
  }
];

export default {
  name: 'shield-order-list',
  mixins: [LayoutMixin, ConfigMixin],
  data() {
    return {
      hasBlockProductRadio: 0, // 用于还原屏蔽商品模式 radio
      hasRadio: true,
      chooseTypeList,
      transferData: [],
      selectTransferData: [],
      chooseType: 0,
      showGoodsListModal: false,
      loading: false,
      tableHeight: this.getTableHeight(),
      categoryList: [],
      shieldList: [],
      pageSize: 20,
      currentPage: 1,
      totalPage: 0,
      searchValue: '',
      columns: [
        {
          type: 'selection',
          width: 70
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code'
        },
        {
          title: '商品名称',
          width: 210,
          key: 'name'
        },
        {
          title: '描述',
          key: 'summary'
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '商品分类',
          key: 'category_name',
          align: 'left',
          hasCategory: true,
          render: (h, params) => {
            const textArr = [params.row.category_name, params.row.category_name2];
            console.log(textArr)
            if (this.isOpenThreeCategory) {
              textArr.push(params.row.category_name3);
            }
            return h('div', textArr.filter((item) => item).join('/'));
          },
        },
      ],
      selectedRow: [],
      category_id: '',
      category_id2: '',
      category_id3: '',
      uid: this.$route.query.id,
      modeList,
      mode: 0
    };
  },
  computed: {
    selectedGoods() {
      let selectedGoods = this.shieldList.map(item => {
        let goodsItem = this.deepClone(item);
        goodsItem.id = item.commodity_id || item.id;
        return goodsItem;
      });
      return selectedGoods;
    }
  },
  watch: {
    mode(newVal, oldVal) {
       this.hasBlockProductRadio = oldVal
    }
  },
  created() {
    const { disableType } = this.$route.query || {};
    this.chooseType = Number(disableType) === 1 ? 1 : 0;
    this.getMode()
    if (this.chooseType === 1) {
      this.getTransferData();
    } else {
      this.getGoodsCategory();
      this.getShieldList();
    }

    this.$nextTick(() => {
      // 让类名ivu-transfer-list-content-item的样式替换为overflow: inherit;
      // white-space: normal;
      // display: -webkit-box;
      // display: -ms-flexbox;
      // display: flex;
      document
        .querySelectorAll('.ivu-transfer-list-content-item')
        .forEach(item => {
          item.style.overflow = 'inherit';
          item.style.whiteSpace = 'normal';
          item.style.display = '-webkit-box';
          item.style.display = '-ms-flexbox';
          item.style.display = 'flex';
        });
    });
  },
  methods: {
    async addDisabledCategory(newTargetKeys) {
      // 找出新增的分类
      const addCategory = newTargetKeys.filter(item => {
        return this.selectTransferData.indexOf(item) === -1;
      });
      await user.setDisableCommodity({
        rece_style: this.$route.query.id,
        type: 1,
        commodity_ids: addCategory.join(',')
      });
    },
    onChangeChooseType(value) {
      this.$Modal.confirm({
        content: '确认切换商品选择模式？',
        onOk: async () => {
          this.chooseType = value;
          await this.setSwitchType();
          if (value === 1) {
            this.getTransferData();
          } else {
            this.getGoodsCategory();
            this.getShieldList();
          }
        },
        onCancel: () => {
          // 重置radio
          this.hasRadio = false;
          setTimeout(() => {
            this.hasRadio = true;
          }, 10);
          return false;
        }
      });
    },
    async setSwitchType() {
      await get(api.setUnavailableType, {
        type: this.chooseType,
        id: this.$route.query.id,
        style: 1,
        mode: this.mode
      });
      this.$router.push({
        path: this.$route.path,
        query: {
          id: this.$route.query.id,
          disableType: this.chooseType,
          mode: this.mode
        }
      });
    },
    // 获取所有可屏蔽分类和已屏蔽分类
    async getTransferData() {
      const res = await user.showDisableCommodity({
        rece_style: this.$route.query.id,
        type: 1
      });
      if (res.status) {
        res.data.list.forEach(item => {
          item.key = item.category_id;
          item.label = item.category_name;
        });
        res.data.selected_list &&
          res.data.selected_list.forEach(item => {
            item.key = item.category_id;
            item.label = item.category_name;
          });
        this.transferData = [...res.data.list, ...res.data.selected_list];
        this.selectTransferData = res.data.selected_list.map(item => item.key);
      }
    },
    async handleChange(newTargetKeys) {
      // 增加和取消的情况
      if (newTargetKeys.length > this.selectTransferData.length) {
        await this.addDisabledCategory(newTargetKeys);
      } else {
        // 删除的情况, 获取selectTransferData中被删除的分类
        const delCategoryKey = this.selectTransferData.filter(item => {
          return newTargetKeys.indexOf(item) === -1;
        });
        // 根据key去transferData中找到对应的分类
        const delCategory = this.transferData.filter(item => {
          return delCategoryKey.indexOf(item.key) !== -1;
        });
        console.log(delCategory);
        await user.cancelDisableCommodity({
          type: 1,
          disable_ids: delCategory.map(item => item.id).join(',')
        });
      }
      await this.getTransferData();
      this.$Message.success('操作成功');
    },
    filterMethod(data, query) {
      if (data.label === null) {
        return true;
      }
      return data.label.indexOf(query) > -1;
    },
    categoryRender(item) {
      if (item.is_selected) {
        return `<span title="${
          item.category_name
        }" class="category-item" style="display: inline-block;line-height: 2.3;"><span>${
          item.category_name ? item.category_name : ''
        }</span><i style="margin: -1px 0 0 10px;color: #00B3A7;font-size: 18px;" class="sui-icon icon-yifenpei category-sorter"></i></span>`;
      } else {
        return `<div title="${
          item.category_name
        }" class="category-item" style="display: inline-block;line-height: 2.3;">${
          item.category_name ? item.category_name : ''
        }
								<div style="
									display: inline-block;
									margin-left: 10px;
									color: #03ac54;
									border: 1px solid #03ac54;
									padding: 0 3px;
									line-height: normal;
									border-radius: 3px;
								">未分配</div></div>`;
      }
    },
    uploadSuccess(response) {
      if (response.status == 0) {
        this.$Notice.error({
          title: response.message
        });
      } else {
        this.$Notice.success({
          title: '导入成功，本次导入' + response.data + '个商品'
        });
        this.getGoodsCategory();
        this.getShieldList();
      }
    },
    downloadTemplate() {
      window.location.href =
        '/superAdmin/userSuper/ImportDisabledCommodityTemplate';
    },
    handleAddAll() {
      let filters = this.$refs.goodsModal.getFilters();
      let params = filters;

      params || (params = {});
      params.rece_style = this.$route.query.id;
      user.addUserTypeDisabledGoods(params).then(res => {
        if (res.status) {
          this.showGoodsListModal = false;
          this.successNotice('保存成功');
          this.getShieldList();
        } else {
          this.errorNotice({
            title: '保存失败',
            desc: res.message
          });
        }
      });
    },
    selectRow(selection) {
      this.selectedRow = selection;
    },
    getGoodsCategory() {
      goods.getGoodsCategory().then(res => {
        if (res.status) {
          this.categoryList = res.data.map(item => {
            return {
              value: item.id,
              label: item.name,
              children: [],
              loading: false
            };
          });
          this.categoryList.unshift({ value: 0, label: '全部分类' });
        }
      });
    },
    removeShieldGoods() {
      let ids = this.selectedRow.map(item => {
        return item.id;
      });
      let params = {
        disable_ids: ids.toString()
      };
      user.cancelDisableCommodity(params).then(res => {
        if (res.status) {
          this.successNotice('批量删除成功');
          this.getShieldList();
        } else {
          this.errorNotice({
            title: '批量删除失败',
            desc: res.message
          });
        }
      });
    },
    addGoods() {
      this.showGoodsListModal = true;
    },
    handlerAdd(orders) {
      if (orders) {
        let commodity_ids = [];
        orders.forEach(item => {
          commodity_ids.push(item.id);
        });
        let params = {
          rece_style: this.$route.query.id,
          commodity_ids: commodity_ids.toString()
        };
        user.setDisableCommodity(params).then(res => {
          if (res.status) {
            this.getShieldList();
          }
        });
      }
    },
    goBack() {
      if (history.length && history.length <=1) {
        window.close();
      } else {
        this.$router.go(-1)
      }
    },
    search() {
      this.currentPage = 1;
      this.getShieldList();
    },
    loadSubCategoryData(item, callback) {
      item.loading = true;
      goods.getGoodsCategory(item.value).then(res => {
        if (res.status) {
          item.children = res.data.map(_item => {
            return {
              value: _item.id,
              label: _item.name
            };
          });
          item.loading = false;
          callback();
        }
      });
    },
    changeCategory(value) {
      this.category_id = value[0];
      this.category_id2 = value[1];
      this.category_id3 = value[2];
      this.getShieldList();
    },
    changePage(pageNo) {
      this.currentPage = pageNo;
      this.getShieldList();
    },
    changePageSize(size) {
      this.pageSize = size;
      this.getShieldList();
    },
    getShieldList() {
      this.loading = true;
      let params = {
        category_id: this.category_id,
        category_id2: this.category_id2,
        category_id3: this.category_id3,
        commodity_name: this.searchValue,
        page: this.currentPage,
        pageSize: this.pageSize,
        rece_style: this.uid
      };
      user.showDisableCommodity(params).then(res => {
        if (res.status) {
          this.loading = false;
          this.shieldList = res.data.list;
          this.totalPage = Number(res.data.pageParams.count);
        }
      });
    },
    exportDisabledGoods() {
      let params = {
        rece_style: this.uid,
        category_id: this.category_id,
        category_id2: this.category_id2,
        category_id3: this.category_id3,
        commodity_name: this.searchValue,
        user_id: this.uid
      };
      user.exportTypeDisableCommodity(params).then(res => {
        let { status, message } = res;
        if (status) {
          window.location.href = res.data.url;
        } else {
          this.errorNotice({
            title: '导出失败',
            desc: message || '导出失败!'
          });
        }
      });
    },
      onChangeMode() {
      this.$Modal.confirm({
				content: '确认切换屏蔽模式？',
				onOk: async () => {
          await this.setSwitchType();
          this.currentPage = 1
          if (this.chooseType === 1) {
            this.getTransferData();
          } else {
            this.getGoodsCategory();
            this.getShieldList();
          }
				},
				onCancel: () => {
          this.mode = this.hasBlockProductRadio;
					return false
				}
			})
      
    },
    getMode() {
      this.$request.get(this.apiUrl.disableMode, {user_id: this.uid,style: 1}).then(res => {
        this.mode = +res.data
      })
    }
  },
  components: {
    GoodsListModal,
    CategorySelect
  }
};
</script>

<style lang="scss" scoped>
.pageLeftBtn {
  position: fixed;
  bottom: 24px;
}
/deep/.unavailable-category {
  .ivu-transfer-list-content-item {
    overflow: inherit;
    white-space: normal;
    display: flex;
  }
}
.content-wrap {
  .ivu-form-item {
    margin-bottom: 0;
    &.blockProductModel {
      margin-top: 5px;
    }
  }
}
</style>
