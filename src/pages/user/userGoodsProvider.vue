<template>
  <div id="GoodsList" class="Swrap userGoodsProvider">
    <Form :label-width="100" class="mt10">
      <FormItem label="商品选择模式">
        <RadioGroup :value="chooseType" v-if="hasRadio">
          <Radio
            v-for="item in chooseTypeList"
            @click.native="onChangeChooseType(item.value)"
            :key="item.value"
            :true-value="item.value"
            :label="item.value"
            >{{ item.label }}</Radio
          >
        </RadioGroup>
      </FormItem>
    </Form>
    <div v-if="+chooseType === 0" class="goods-list">
      <ListTable
        :border="false"
        :outer-border="true"
        :columns="columns"
        :filterItems="filterItems"
        :data-provider="apiUrl.getUserGoodsProvider"
        @on-selection-change="handleSelectedGoodsList"
        :height="getTableHeight() - 215"
        :filters="{ type: undefined, uid }"
        tableId="table-goods-list"
        ref="table"
        :pageSizeOpts="[10, 20, 30, 40, 100]"
        :defaultPageSize="10"
        :extraSelectionCount="extraSelectionCount"
      >
        <template #button>
          <Button @click="$_onExport" class="ml10">导出</Button>
        </template>
        <div slot="before-table">
          <div style="display: flex" v-show="!selectedGoodsList.length">
            <SButton styleType="btnStyleForAdd" @click="addGoods">新增</SButton>
            <Button @click="downloadTemplate" class="ml10">下载模版</Button>
            <CommonUpload
              class="ml10"
              @on-success="handleUploadSuccess"
              :manual-handle-response="true"
              :data="uploadData"
              :accept="mineType.excel.join(',')"
              :format="['csv', 'xlsx']"
              :action="apiUrl.userProvider.importExcel"
            >
              <i-button>导入</i-button>
            </CommonUpload>
          </div>
        </div>
        <div slot="batch-checked" class="checked_button" @click="checkedChange()">
            {{checkedAll ? '勾选当前页内容' : '勾选所有页内容'}}
        </div>
        <div class="flex-con" slot="batch-operation">
          <PopButton
            title="确定批量删除选中？"
            :confirm="true"
            @on-ok="handleBatchDelete"
          >
            批量删除
          </PopButton>
          <Button class="ml10" @click="handleSetManyProvider" :loading="loading" :disabled="loading">
            批量设置供应商
          </Button>
        </div>
      </ListTable>
    </div>
    <div v-else class="classify-list">
      <ListTable
        :border="false"
        :outer-border="true"
        :columns="categoryColumns"
        :filterItems="filterItems.slice(0, -1)"
        :data-provider="apiUrl.getUserGoodsProvider"
        @on-selection-change="handleSelectedGoodsList"
        :height="getTableHeight() - 215"
        :filters="{ type: 1, uid }"
        tableId="table-classify-list"
        ref="table"
        :pageSizeOpts="[10, 20, 30, 40, 100]"
        :defaultPageSize="10"
        :extraSelectionCount="extraSelectionCount"
      >
        <div slot="before-table">
          <div style="display: flex" v-show="!selectedGoodsList.length">
            <SButton styleType="btnStyleForAdd" @click="addCategoryShow">
              新增
            </SButton>
          </div>
        </div>
        <div slot="batch-checked" class="checked_button" @click="checkedChange(1)">
            {{checkedAll ? '勾选当前页内容' : '勾选所有页内容'}}
        </div>
        <div class="flex-con" slot="batch-operation">
          <PopButton
            title="确定批量删除选中？"
            :confirm="true"
            @on-ok="handleBatchDelete"
          >
            批量删除
          </PopButton>
          <Button class="ml10" @click="handleSetManyProvider" :loading="loading" :disabled="loading">
            批量设置供应商
          </Button>
        </div>
      </ListTable>
    </div>
    <Modal
      v-model="goodsModal.show"
      width="1000"
      class-name="provider-goods-modal"
      title="新增"
    >
      <goods-select
        ref="goods"
        :uid="uid"
        v-on:selectChange="selectGoods"
        :filter="goodsFilter"
      ></goods-select>
      <Row slot="footer" type="flex" align="middle" justify="space-between">
        <Col style="flex: 1; text-align: left">
          <span v-show="isFundAllocationEnabled">不显示供应商分账的商品</span>
        </Col>
        <Col>
          <Button type="default" @click="goodsModal.show = false">取消</Button>
          <i-button
            class="button_primary"
            :loading="btnLoading"
            @click="saveUserGoodsAll"
            >导入筛选商品
          </i-button>
          <i-button type="success" :loading="btnLoading2" @click="saveUserGoods"
            >导入勾选商品
          </i-button>
        </Col>
      </Row>
    </Modal>
    <s-modal
      ref="batchSetProviderModal"
      type="default"
      title="批量设置供应商"
      @ok="handleSelectProvider"
      @quit="$_onQuit"
      @close="$_onQuit"
    >
      <div style="padding: 30px 0 30px 15px">
        <span>供应商：</span>
        <Select
          v-model="selectedId"
          :lable="selectedProvider"
          style="width: 200px"
          filterable
        >
          <Option
            v-for="provider in providers"
            :value="provider.id"
            :label="provider.name"
            :key="provider.id"
          ></Option>
        </Select>
      </div>
    </s-modal>
    <category-modal
      ref="categoryModal"
      :exclude-ids="alreadyExistCategory"
      :show="categoryModal.show"
      :selected-ids="categoryModal.selectedCategory"
      @save="saveUserCategories"
      @cancel="closeCategoryModal"
    ></category-modal>
  </div>
</template>

<script>
import '@assets/scss/pagesCommon.scss';
import { mapState } from 'vuex';
import goods from '@api/goods.js';
import userGoodsProvider from '@api/userGoodsProvider.js';
import goodsSelect from '@components/common/goodsSelect';
import LayoutMixin from '@/mixins/layout';
import { MINE_TYPE } from '@/util/const';
import { SModal } from '@sdp/ui';
import SButton from '@components/button';
import userRes from '@/api/user';
import FundAllocation from '@/mixins/fund-allocation';
import { post } from '@/api/request';
import api from '@/api/api';
import CategoryModal from '@/pages/coupon/category-modal.vue';
import ListTable from '@components/list-table';
import CategorySelect from '@components/common/categorySelect';
import ConfigMixin from "@/mixins/config";
import ProviderSelect from "@/components/common/providerSelect_new.vue";

const chooseTypeList = [
  {
    label: '按商品',
    value: 0,
  },
  {
    label: '按分类',
    value: 1,
  },
];

export default {
  name: 'userGoodsProvider',
  mixins: [LayoutMixin, FundAllocation, ConfigMixin],
  data() {
    return {
      alreadyExistCategory: [],
      categoryModal: {
        show: false,
        selectedCategory: [],
      },
      hasRadio: true,
      chooseTypeList,
      chooseType: 0,
      btnLoading: false,
      btnLoading2: false,
      mineType: MINE_TYPE,
      uploadData: {
        user_id: '',
      },
      goodsFilter: {
        is_base: 1,
      },
      columns: {},
      filterItems: [],
      uid: 0, // 用户id
      checkedAddGoods: [], // 选择的新增商品

      providers: [],
      category: {
        first: '',
        second: '',
      },
      selectedGoodsList: '', // 选中的商品
      goodsModal: {
        show: false,
        loading: true,
      }, // 选择商品模态框
      selectedProvider: '',
      selectedId: '',
      checkedAll: false,
      extraSelectionCount: 0,
      allIds: {},
      loading: false,
    };
  },
  created() {
    this.uid = this.$route.query.uid;
    this.chooseType = +this.$route.query.bindProviderType || 0;
    this.uploadData.user_id = this.uid;
    this.init();
    this.getProvider();
  },
  computed: {
    ...mapState({
      uploadId: (state) => state.upload.id,
    }),
    categoryColumns() {
      return this.columns.filter((item) => item.hasCategory);
    },
  },
  methods: {
    init() {
      this.filterItems = [
        {
          label: '商品分类',
          type: 'custom',
          key: ['category_id', 'category_id2', 'category_id3'],
          component: CategorySelect,
          props: {
            noThree: !this.isOpenThreeCategory,
          },
        },
        {
          label: '商品搜索',
          type: 'Input',
          key: 'query',
          props: {
            placeholder: '请输入商品编码\名称\助记码\别名',
            clearable: true,
          },
        },

        {
          label: '供应商',
          type: 'custom',
          key: 'provider_ids',
          component: ProviderSelect,
          props: {
            multiple: true,
            filterable: true,
            showNone: true,
            showAll: false,
            maxTagCount: 1
          },
        },
      ];
      this.columns = [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select',
          hasCategory: true,
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40',
              },
            });
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left',
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '商品分类',
          key: 'category_name',
          align: 'left',
          hasCategory: true,
          render: (h, params) => {
            const textArr = [params.row.category_name, params.row.category_name2];
            console.log(textArr)
            if (this.isOpenThreeCategory) {
              textArr.push(params.row.category_name3);
            }
            return h('div', textArr.filter((item) => item).join('/'));
          },
        },
        {
          title: '单位',
          width: 120,
          key: 'unit',
          align: 'left',
        },
        {
          title: '描述',
          width: 120,
          key: 'summary',
          align: 'left',
        },
        {
          title: '指定供应商',
          width: 200,
          key: 'provider_id',
          align: 'left',
          hasCategory: true,
          render: (h, params) => {
            const originProviderId = params.row.provider_id;
            let options = this.providers.map((provider) =>
              h('i-option', {
                props: {
                  label: provider.name,
                  value: provider.id,
                },
              }),
            );
            return h(
              'i-select',
              {
                props: {
                  transfer: true,
                  filterable: true,
                  placeholder: '选择供应商', // 设置placeholder
                  value: params.row.provider_id || '', // 使用空字符串作为默认值
                },
                on: {
                  'on-change': async (value) => {
                    if (
                      this.chooseType === 1 && // 分类模式
                      +this.sysConfig.sale_link_purchase_pricing === 2 && // 开启了联动定价
                      params.row.pricing_def
                    ) {
                      const confirm = await this.confirmChangeProvider(params.row.id);
                      if (!confirm) {
                        // 刷新列表还原数据
                        this.$refs.table.fetchData(false);
                        return;
                      }
                    }
                    value &&
                    this.handleBatchSaveProvider([
                      {
                        commodity_id: params.row.commodity_id,
                        provider_id: value || 0,
                      },
                    ]);
                  },
                },
              },
              options,
            );
          },
        },
        {
          title: '操作',
          type: 'action',
          width: 100,
          align: 'center',
          hasCategory: true,
          actions: (params) => {
            return [
              {
                name: '删除',
                confirm: '确定删除？',
                action: () => {
                  if (
                    this.chooseType === 1 && // 分类模式
                    +this.sysConfig.sale_link_purchase_pricing === 2 && // 开启了联动定价
                    params.row.pricing_def
                  ) {
                    this.$smodal({
                      title: '确认',
                      text: '该分类指定供应商已关联采销联动定价，删除后会联动删除相关定价公式，请确认',
                      type: 'warning',
                      btns: 2,
                      okTxt: '继续',
                      quitTxt: '取消',
                      onOk: () => {
                        this.handleDelete(params.row.id);
                      },
                    });
                  } else {
                    this.handleDelete(params.row.id);
                  }
                },
              },
            ];
          },
        },
      ];
    },
    async addCategoryShow() {
      this.categoryModal.selectedCategory = [];
      this.$refs.categoryModal.resetCheckStatus();
      await this.getSelectedCategory();
      this.categoryModal.show = true;
    },
    closeCategoryModal() {
      this.categoryModal.show = false;
    },
    onChangeChooseType(value) {
      const data = this.$refs.table.getData();
      // 开启了联动定价，切换到商品模式，提示
      if (+this.sysConfig.sale_link_purchase_pricing === 2 && value === 0 && data.length) {
        this.$Modal.confirm({
          content: '切换商品模式后，已关联的采销联动定价将失效，请确认',
          onOk: () => {
            this.chooseType = value;
            this.setSwitchType();
            this.$refs.table.fetchData();
            this.extraSelectionCount = 0;
            this.checkedAll = false
          },
          onCancel: () => {
            // 重置radio
            this.hasRadio = false;
            setTimeout(() => {
              this.hasRadio = true;
            }, 10);
            return false;
          },
        });
      } else {
        this.$Modal.confirm({
          content: '确认切换商品选择模式？',
          onOk: () => {
            this.chooseType = value;
            this.setSwitchType();
            this.$refs.table.fetchData();
            this.extraSelectionCount = 0;
            this.checkedAll = false
          },
          onCancel: () => {
            // 重置radio
            this.hasRadio = false;
            setTimeout(() => {
              this.hasRadio = true;
            }, 10);
            return false;
          },
        });
      }
    },
    async setSwitchType() {
      const res = await post(api.setNominatedSupplier, {
        type: this.chooseType,
        user_id: this.$route.query.uid,
      });
      if (res.status) {
        this.$router.push({
          path: this.$route.path,
          query: {
            ...this.$route.query,
            uid: this.$route.query.uid,
            bindProviderType: this.chooseType,
          },
        });
      } else {
        this.errorNotice({
          title: '切换失败',
          desc: res.message || '切换失败',
        });
        // 重置radio
        this.hasRadio = false;
        setTimeout(() => {
          this.hasRadio = true;
        }, 10);
      }
    },
    saveUserGoodsAll() {
      this.btnLoading = true;
      let params = this.$refs.goods.params;
      userGoodsProvider
        .batchAddCommodity(params)
        .then((res) => {
          if (res.status) {
            this.successMessage('保存成功');
            this.goodsModal.show = false;
            this.$refs.table.fetchData();
          } else {
            this.errorNotice({
              title: '保存失败',
              desc: res.message || '保存失败',
            });
          }
          this.btnLoading = false;
        })
        .catch((err) => {
          this.btnLoading = false;
        });
    },
    downloadTemplate() {
      this.$request
        .get(this.apiUrl.userProvider.downloadTemplate)
        .then(({ status, data, message }) => {
          if (status) {
            location.href = data;
          } else {
            this.modalError(message || '下载失败！', 0);
          }
        });
    },
    handleUploadSuccess({ status, message }) {
      if (status) {
        this.$refs.table.fetchData();
        this.successNotice(message || '导入成功！', 0);
      } else {
        this.errorNotice({
          title: '导入失败',
          desc: message ? message : '导入失败',
        });
      }
    },
    async getProvider() {
      let params = {
        filter_disable_provider: 1,
        exclude_account_provider: 1,
      };
      let { status, data } = await goods.getPurchaseType(params);
      if (status) {
        this.providers = data.providers;
      } else {
        this.providers = [];
      }
    },
    /**
     * 批量删除商品
     */
    async handleBatchDelete() {
      const ids = (this.checkedAll ? this.allIds.ids : this.selectedGoodsList).map((item) => item.id ? item.id : item).join(',')
      if (
        this.chooseType === 1 && // 分类模式
        +this.sysConfig.sale_link_purchase_pricing === 2 // 开启了联动定价
      ) {
        this.$smodal({
          title: '确认',
          text: '该分类指定供应商已关联采销联动定价，删除后会联动删除相关定价公式，请确认',
          type: 'warning',
          btns: 2,
          okTxt: '继续',
          quitTxt: '取消',
          onOk: () => {
            this.handleDelete(
              ids,
              '批量',
            );
          },
        });
      } else {
        this.handleDelete(
          ids,
          '批量',
        );
      }
    },
    async handleDelete(ids, text = '') {
      console.log(ids, 'ids')
      let { status, message } = await userGoodsProvider.delGoods({ ids });
      if (status) {
        this.successNotice(text + '删除成功');
        this.$refs.table.fetchData();
      } else {
        this.errorNotice({
          title: text + '删除失败',
          desc: message ? message : text + '删除失败',
        });
      }
    },
    handleSelectedGoodsList(selection, data, obj) {
      this.selectedGoodsList = selection;
      if((obj && obj.noall) || selection.length==0) {
        this.extraSelectionCount = 0
        this.checkedAll = false
      }
    },
    /**
     * @description 新增商品选择发生变化处理函数
     * @param checkedData 新选择的商品 {ids:, detail}
     */
    selectGoods(checkedData) {
      this.checkedAddGoods = checkedData.ids;
    },
    /**
     * @description 新增用户商品
     */
    async saveUserGoods() {
      if (this.checkedAddGoods.length === 0) {
        this.errorNotice('请选择商品');
        this.categoryModal.show = false;
        return;
      }
      this.btnLoading2 = true;
      this.goodsModal.loading = false;
      this.goodsModal.show = true;
      let res = await userGoodsProvider.saveGoods({
        uid: this.uid,
        data: JSON.stringify(
          this.checkedAddGoods.map((goodsId) => ({
            commodity_id: goodsId,
            provider_id: 0,
          })),
        ),
      });
      let { status, message } = res;
      if (status) {
        this.successNotice('保存成功');
        this.goodsModal.show = false;
        this.$refs.table.fetchData();
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: message ? message : '保存失败',
        });
      }
      this.btnLoading2 = false;
    },
    // 新增用户商品分类
    async saveUserCategories(categoryIds = []) {
      if (categoryIds.length === 0) {
        this.errorNotice('请选择分类');
        this.categoryModal.show = false;
        return;
      }
      const res = await userGoodsProvider.saveGoods({
        uid: this.uid,
        data: JSON.stringify(
          categoryIds.map((id) => ({
            commodity_id: id,
            provider_id: 0,
          })),
        ),
        type: 1,
      });
      if (res.status) {
        this.successNotice('保存成功');
        this.categoryModal.show = false;
        this.$refs.table.fetchData();
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: res.message ? res.message : '保存失败',
        });
        this.categoryModal.show = false;
      }
    },
    confirmChangeProvider(ids) {
      const $vm = this;
      return new Promise(resolve => {
        this.$smodal({
          title: '确认',
          text: '该分类指定供应商已关联采销联动定价，修改供应商后请确认联动操作',
          type: 'warning',
          btns: 2,
          okTxt: '继续',
          quitTxt: '取消',
          renderFooter(h) {
            return h('div', {
              style: {
                textAlign: 'right',
              }
            }, [
              h(SButton, {
                style: {
                  marginRight: '10px',
                },
                on: {
                  click: () => {
                    this.close();
                    resolve(false);
                  }
                }
              }, '取消'),
              h(SButton, {
                props: {
                  styleType: 'btnStyleForAdd'
                },
                style: {
                  marginRight: '10px',
                },
                on: {
                  click: async () => {
                    this.close();
                    const {status, message} = await post('/superAdmin/customProvider/setUserSaleLinkPurchasePricingDef', {
                      user_id: $vm.uid,
                      ids,
                      pricing_def: ''
                    });
                    if (status) {
                      resolve(true);
                    } else {
                      this.$smessage({
                        type: 'error',
                        text: message
                      });
                      resolve(false);
                    }
                  }
                }
              }, '清除定价'),
              h(SButton, {
                props: {
                  styleType: 'btnStyleForAdd'
                },
                on: {
                  click: () => {
                    this.close();
                    resolve(true);
                  }
                }
              }, '更新定价'),
            ])
          },
          onOk: () => {
            resolve(true);
          },
        })
      });
    },
    /**
     * @description 保存用户商品供应商
     * <AUTHOR>
     */
    async handleBatchSaveProvider(selectedList) {
      let res = await userGoodsProvider.saveGoods({
        uid: this.uid,
        data: JSON.stringify(selectedList),
        type: +this.chooseType === 1 ? 1 : undefined,
      });
      this.loading = false;
      if (res.status) {
        this.successNotice('保存成功');
        this.$refs.table.fetchData();
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: res.message ? res.message : '保存失败',
        });
      }
    },
    handleSetManyProvider() {
      this.$refs.batchSetProviderModal.open();
      document.body.style.overflow = 'hidden';
    },
    async handleSelectProvider() {
      const list = (this.checkedAll ? this.allIds.commodity_ids : this.selectedGoodsList).map((item) => ({
        commodity_id: item.commodity_id ? item.commodity_id : item,
        provider_id: this.selectedId,
      }))
      const ids = this.checkedAll ? this.allIds.ids : this.selectedGoodsList.map(item => item.id);
      this.loading = true;
      if (this.chooseType === 1 && +this.sysConfig.sale_link_purchase_pricing === 2) {
        let confirm = true;
        try {
          confirm = await this.confirmChangeProvider(ids.join(','));
        } catch(err) {
          this.loading = false;
          console.error(err);
        }
        if (!confirm) {
          this.loading = false
          return;
        }
      }
      this.handleBatchSaveProvider(list);
    },
    addGoods() {
      this.goodsModal.show = true;
      this.$refs.goods.loadGoodsList();
    },
    async getSelectedCategory() {
      const { data } = await userGoodsProvider.getGoods({
        is_only_category_id2: 1,
        type: 1,
        uid: this.uid,
      });
      this.alreadyExistCategory = data.split(',');
    },
    $_onExport() {
      let params = this.$refs.table.getParams();
      userRes.exportCommodityProviders(params).then((res) => {
        if (res.status) {
          window.location.href = res.data.url;
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    $_onQuit() {
      document.body.style.overflow = '';
    },
    checkedChange(type){
      this.checkedAll = !this.checkedAll
      this.$refs.table.$refs.table.handleToggleCheckAll(true)
      if(this.checkedAll){
        if(this.$refs.table.curPagination.total){
          this.extraSelectionCount = this.$refs.table.curPagination.total - this.$refs.table.curPagination.pageSize
          if(this.$refs.table.data.length < this.$refs.table.curPagination.pageSize){
            this.extraSelectionCount = this.$refs.table.curPagination.total - this.$refs.table.data.length
          }
          let params =  this.$refs.table.getParams()
          params.is_only_ids = 1
          params.user_id = params.uid
          this.$request.post(this.apiUrl.getUserGoodsProvider, params).then(res=>{
            this.allIds = res.data
          })
        }else{
          this.allIds = {}
          this.extraSelectionCount = 0
        }
      }else{
       this.allIds = {}
       this.extraSelectionCount = 0
      }
    },
  },
  components: {
    CategoryModal,
    goodsSelect,
    SModal,
    SButton,
    ListTable,
  },
};
</script>

<style lang="less" scoped>
.ivu-form-item {
  margin-bottom: 10px;
}
.goods-list {
  background-color: #fff;
}
.button_primary {
  border-color: var(--primary-color);
  color: var(--primary-color);
}
.userGoodsProvider {
  .checked_button {
    margin-right: 10px;
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    cursor: pointer;
    color: #fff;
    background: #03ac54;
    padding: 0 5px;
    border-radius: 3px;
  }
}
</style>
<style>
.provider-goods-modal .ivu-modal-body {
  padding: 0 16px !important;
}
.provider-goods-modal .ivu-modal-body #GoodsList > article {
  padding: 16px 5px !important;
}
</style>
