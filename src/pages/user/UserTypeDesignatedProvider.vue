<template>
  <div class="base-wrapper" style="padding-top: 10px">
		<Form :label-width="100">
			<FormItem label="商品选择模式">
				<RadioGroup  :value="chooseType" v-if="hasRadio">
					<Radio
						v-for="item in chooseTypeList"
						@click.native="onChangeChooseType(item.value)"
						:key="item.value"
						:true-value="item.value"
						:label="item.value"
					>{{ item.label }}</Radio>
				</RadioGroup>
			</FormItem>
		</Form>
    <article v-show="+chooseType === 0" class="goods-list">
      <div class="goods-list__operation">
        <div class="goods-list__left">
          <i-input
            placeholder="请输入商品编码\名称\助记码\别名"
            v-model="search_key"
            @on-enter="loadGoodsList()"
            clearable
          >
            <i-button slot="append" icon="ios-search" @click="loadGoodsList()" class="search-btn"></i-button>
          </i-input>
        </div>

        <div class="goods-list__select-middle" style="margin-top: -2px">
          <category-select
            clearable
            placeholder="全部分类"
            v-model="categoryData"
            :noThree="!isOpenThreeCategory"
            @on-change="loadGoodsList"
            style="width: 100%"
          >
          </category-select>
        </div>
        <div class="goods-list__select-middle" style="margin-top: -2px">
          <provider-select
            clearable
            placeholder="全部供应商"
            v-model="provider_ids"
            @on-change="loadGoodsList"
            style="width: 200px"
            multiple
            filterable
            showNone
            :showAll="false"
            :maxTagCount="1"
          >
          </provider-select>
        </div>

        <div class="fixed-60">
          <i-button class="ml15" @click="setManyProvider">批量设置供应商</i-button>
          <PopButton
            title="确定批量删除选中？"
            :disabled="!checkGoods"
            :confirm="true"
            @on-ok="batchDel"
            class="ml15"
          >批量删除</PopButton>
          <i-button type="success" @click="addGoods" class="ml15">
            <Icon type="md-add"></Icon>新增
          </i-button>
        </div>
      </div>

      <div class="goods-list__table">
        <i-table
          :columns="columns"
          :data="goodsList"
          :height="layoutTableHeight - 30"
          @on-selection-change="getCheckGoods"
        ></i-table>
        <Row align="middle" type="flex" style="padding: 10px 0">
          <Col>
            <i-button type="success" class="c-green-btn" @click="saveUserGoodsProvider">保存修改</i-button>
          </Col>
          <Col align="right" class="flex-con">
            <Page
              :total="page.count"
              :page-size-opts="[10, 20, 30, 40, 100]"
              :current.sync="nowPage"
              :page-size="page.page_size"
              @on-change="getCurrentPage"
              @on-page-size-change="modifyPage"
              placement="top"
              show-elevator
              show-total
              show-sizer
              class="js-after-table"
            ></Page>
          </Col>
        </Row>
      </div>
      <Modal v-model="goodsModal.show" width="1000" class-name="sdp-modal" title="新增">
        <goods-select ref="goods" :filter="{ receivable_style: userTypeId,is_base: 1}" @selectChange="selectGoods"></goods-select>
        <Row slot="footer" type="flex" align="middle" justify="space-between">
          <Col style="flex: 1; text-align: left;">
            <span v-show="isFundAllocationEnabled">不显示供应商分账的商品</span>
          </Col>
          <Col>
           <Button type="default" @click="goodsModal.show=false">取消</Button>
            <i-button   class="button_primary" @click="saveUserGoodsAll">导入筛选商品</i-button>
            <i-button type="success" @click="saveUserGoods">导入勾选商品</i-button>
          </Col>
        </Row>
      </Modal>
    </article>
		<div v-show="+chooseType === 1" class="good-list">
			<div class="goods-list__operation">

				<div class="goods-list__select-middle">
					<category-select
            clearable
            placeholder="全部分类"
            v-model="categoryData"
            :noThree="!isOpenThreeCategory"
            @on-change="loadGoodsList"
            style="width: 100%"
          >
          </category-select>
				</div>

				<div class="fixed-60">
					<i-button class="ml15" @click="setManyProvider">批量设置供应商</i-button>
					<PopButton
						title="确定批量删除选中？"
						:disabled="!checkGoods"
						:confirm="true"
						@on-ok="batchDel"
						class="ml15"
					>批量删除</PopButton>
					<i-button type="success" @click="addGoods" class="ml15">
						<Icon type="md-add"></Icon>新增
					</i-button>
				</div>
			</div>

			<div class="goods-list__table">
				<i-table
					:columns="categoryColumns"
          :loading="loading"
					:data="goodsList"
					:height="layoutTableHeight - 30"
					@on-selection-change="getCheckGoods"
				></i-table>
				<Row align="middle" type="flex" style="padding: 10px 0">
					<Col>
						<i-button type="success" class="c-green-btn" @click="saveUserGoodsProvider">保存修改</i-button>
					</Col>
					<Col align="right" class="flex-con">
						<Page
							:total="page.count"
							:page-size-opts="[10, 20, 30, 40, 100]"
							:current.sync="nowPage"
							:page-size="page.page_size"
							@on-change="getCurrentPage"
							@on-page-size-change="modifyPage"
							placement="top"
							show-elevator
							show-total
							show-sizer
							class="js-after-table"
						></Page>
					</Col>
				</Row>
			</div>

			<category-modal ref="category-modal" :exclude-ids="alreadyExistCategory" :show="categoryModal.show" :selected-ids="categoryModal.selectedCategory" @save="saveUserCategories" @cancel="closeCategoryModal"></category-modal>
		</div>

    <s-modal
      ref="batchSetProviderModal"
      type="default"
      title="批量设置供应商"
      @ok="selectProvider"
      @quit="$_onQuit"
      @close="$_onQuit"
    >
      <div style="padding: 30px 0 30px 15px">
        <span>供应商：</span>
        <Select
          v-model="selectedId"
          :lable="selectedProvider"
          style="width: 200px"
          filterable
        >
          <Option
            v-for="provider in providers"
            :value="provider.id"
            :label="provider.name"
            :key="provider.id"
          ></Option>
        </Select>
      </div>
    </s-modal>
  </div>
</template>

<script>
import goods from '@api/goods.js'
import userGoodsProvider from '@api/userGoodsProvider.js'
import goodsSelect from '@components/common/goodsSelect'
import LayoutMixin from '@/mixins/layout'
import { SModal } from '@sdp/ui'
import FundAllocation from '@/mixins/fund-allocation'
import CategoryModal from '@/pages/coupon/category-modal.vue';
import {post} from '@/api/request';
import api from '@/api/api';
import CategorySelect from '@components/common/categorySelect';
import ConfigMixin from "@/mixins/config";
import modifiedTips from '@/mixins/modified-tips';
import ProviderSelect from "@/components/common/providerSelect_new.vue";

const chooseTypeList = [
	{
		label: '按商品',
		value: 0
	},
	{
		label: '按分类',
		value: 1
	}
];

export default {
  name: 'userGoodsProvider',
  mixins: [LayoutMixin, FundAllocation, ConfigMixin, modifiedTips],
  computed: {
    categoryColumns () {
      return this.columns.filter(item => item.hasCategory)
    }
  },
  data () {
    return {
      loading: false,
      categoryData: [],
			alreadyExistCategory: [],
			categoryModal: {
				show: false,
				selectedCategory: [],
			},
			hasRadio: true,
			chooseTypeList,
			chooseType: 0,
      tableHeight: this.getTableHeight(),
      modified: false,
      columns: [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select',
					hasCategory: true,
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          width: 90,
          render: (h, params) => {
            var obj = params.row
            if (!obj.logo) {
              obj.logo =
                'https://img.sdongpo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg'
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            })
          }
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left'
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left'
        },
        {
          title: '商品分类',
          key: 'category_name',
          align: 'left',
					hasCategory: true,
          render: (h, params) => {
            const textArr = [params.row.category_name, params.row.category_name2];
            if (this.isOpenThreeCategory) {
              textArr.push(params.row.category_name3);
            }
            return h('div', textArr.filter((item) => item).join('/'));
          },
        },
        {
          title: '单位',
          width: 120,
          key: 'unit',
          align: 'left'
        },
        {
          title: '描述',
          width: 120,
          key: 'summary',
          align: 'left'
        },
				{
					title: '指定供应商',
					width: 200,
					key: 'provider_id',
					align: 'left',
					hasCategory: true,
					render: (h, params) => {
						let options = [];
						let self = this;
						let providers = this.providers;
						// 添加一个空选项，用于显示placeholder
						options.push(
							h('i-option', {
								props: {
									label: '选择供应商',
									value: '' // 空字符串作为占位符值
								}
							})
						);
						// 遍历供应商，创建选项
						if (Array.isArray(providers)) {
							providers.forEach(provider => {
								let option = h('i-option', {
									props: {
										label: provider.name,
										value: provider.id
									}
								});
								options.push(option);
							});
						}
						return h(
							'i-select',
							{
								attrs: {
									tabindex: 1000
								},
								props: {
									transfer: true,
									filterable: true,
									placeholder: '选择供应商', // 设置placeholder
									value: this.goodsList[params.index].provider_id || '' // 使用空字符串作为默认值
								},
								on: {
									'on-change': value => {
										self.goodsList[params.index].provider_id = value;
										self.modified = true;
									}
								}
							},
							options
						);
					}
				},
        {
          title: '操作',
          key: 'action',
          width: 200,
					hasCategory: true,
          align: 'center',
          render: (h, { row }) => {
            return h('div', [
              h('PopButton', {
                class: {
                  'table-button': true
                },
                props: {
                  confirm: true,
                  title: '确定删除？',
                  type: 'text',
                  size: 'small'
                },
                on: {
                  'on-ok': () => {
                    this.delGoods(row)
                  }
                }
              }, '删除')
            ])
          }
        }
      ],

      userTypeId: '', // 用户id
      checkedAddGoods: [], // 选择的新增商品

      providers: [],
      goodsList: [],
      page: {
        currentPage: 1,
        pageSize: 20
      },
      pageSize: '',
      nowPage: 1,
      category: {
        first: '',
        second: ''
      },
      checkGoods: '', // 选中的商品
      searchData: '',
      search_key: '', //搜索框输入字符
      goodsModal: {
        show: false,
      }, // 选择商品模态框
      selectedProvider: '',
      selectedId: '',
      provider_ids: []
    }
  },
  created () {
    this.userTypeId = this.$route.query.id
		this.chooseType = this.$route.query.bindProviderType? Number(this.$route.query.bindProviderType) : 0
    this.loadGoodsList()
    this.getProvider()
  },
  methods: {
		async addCategoryShow() {
			this.categoryModal.selectedCategory = [];
			this.$refs['category-modal'].resetCheckStatus();
			await this.getSelectedCategory();
			this.categoryModal.show = true;
		},
		closeCategoryModal() {
			this.categoryModal.show = false
		},
		onChangeChooseType(value) {
			this.$Modal.confirm({
				content: '确认切换商品选择模式？',
				onOk: () => {
					this.chooseType = value
					this.setSwitchType();
					this.loadGoodsList();
				},
				onCancel: () => {
					// 重置radio
					this.hasRadio = false
					setTimeout(() => {
						this.hasRadio = true
					}, 10)
					return false
				}
			})
		},
		async setSwitchType() {
			const res = await post(api.setNominatedSupplier, {
				type: this.chooseType,
				receivable_style: this.$route.query.id
			})
			if (res.status) {
				this.$router.push({
					path: this.$route.path,
					query: {
						...this.$route.query,
						uid: this.$route.query.uid,
						bindProviderType: this.chooseType,
					}
				})
				// 重置radio
				this.hasRadio = false
				setTimeout(() => {
					this.hasRadio = true
				}, 10)
			} else {
				this.errorNotice(res.message)
			}

		},
    async getProvider () {
      let params = {
        filter_disable_provider: 1,
        exclude_account_provider: 1
      }
      let purchaseReq = await goods.getPurchaseType(params)
      if (purchaseReq.status) {
        this.providers = purchaseReq.data.providers
      } else {
        this.providers = []
      }
    },
    // 修改一页展示的条数
    async modifyPage () {
      this.pageSize = arguments[0]
      this.loadGoodsList()
    },
    getCurrentPage () {
      var that = this
      if (that.modified) {
        this.$Modal.confirm({
          title: '提示',
          content:
            '<p>系统检测到您当前页发生了更改，离开当前页将不会保存您所做的更改，您确定放弃修改吗？</p>',
          onOk: function () {
            that.loadGoodsList()
          },
          onCancel: () => {
            that.nowPage = parseInt(that.page.page)
          }
        })
      } else {
        that.nowPage = arguments[0]
        that.loadGoodsList()
      }
    },
    /**
     * 批量删除商品
     */
    async batchDel () {
      if (this.checkGoods === '') {
        this.errorNotice('请选择商品或分类')
        return
      }
      const { status, message } = await userGoodsProvider.delGoods({ ids: this.checkGoods })
      if (status) {
        this.successMessage('批量删除成功')
				this.checkGoods = ''
        this.loadGoodsList()
      } else {
        this.errorNotice({
          title: '批量删除失败',
          desc: message || '批量删除失败'
        })
      }
    },
    async delGoods (row) {
      let res = await userGoodsProvider.delGoods({ ids: row.id })
      let { status, message } = res
      if (status) {
        this.successMessage('删除成功')
        this.checkGoods = ''
        this.loadGoodsList()
      } else {
        this.errorNotice({
          title: '删除失败',
          desc: message ? message : '删除失败'
        })
      }
    },
    getCheckGoods (goods) {
      let array = []

      if (Array.isArray(goods)) {
        array = goods.map(item => item.id)
      }

      this.checkGoods = array.join()
    },
    /**
     * @description 新增商品选择发生变化处理函数
     * @param checkedData 新选择的商品 {ids:, detail}
     */
    selectGoods (checkedData) {
      this.checkedAddGoods = checkedData.ids || []
    },
    /**
     * @description 新增用户商品
     */
    // 导出筛选全部
    saveUserGoodsAll(){
       let params =  this.$refs.goods.params
       userGoodsProvider.batchAddCommodity({
        user_id:'',
        search_value:params.search_value,
        category_id:params.category_id,
        category_id2:params.category_id2,
        receivable_style:params.receivable_style,
        is_base:params.is_base
        }).then(res=>{
        if(res.status){
           this.successMessage('保存成功')
          this.goodsModal.show = false
          this.loadGoodsList()
        }else{
           this.errorNotice({
           title: '保存失败',
          desc: res.message || '保存失败'
         })
        }

       })

    },
		// 新增用户商品分类
		async saveUserCategories(categoryIds = []) {
			if (categoryIds.length === 0) {
				this.errorNotice('请选择分类');
				this.categoryModal.show = false;
				return;
			}
			const data = []
			categoryIds.forEach(id => {
				data.push({
					commodity_id: id,
					provider_id: 0
				})
			})
			const res = await userGoodsProvider.saveGoods({
				receivable_style: this.userTypeId,
				data: JSON.stringify(data),
				type: 1
			})
			if (res.status) {
				this.successNotice('保存成功');
				this.categoryModal.show = false;
				await this.loadGoodsList();
			} else {
				this.errorNotice({
					title: '保存失败',
					desc: res.message ? res.message : '保存失败'
				});
				this.categoryModal.show = false;
			}
		},
    async saveUserGoods () {
      const goodsList = this.checkedAddGoods.map(goodsId => ({
        commodity_id: goodsId,
        provider_id: 0
      }))

      const { status, message } = await userGoodsProvider.saveGoods({
        receivable_style: this.userTypeId,
        data: JSON.stringify(goodsList)
      })
      if (status) {
        this.successMessage('保存成功')
        this.goodsModal.show = false
        this.loadGoodsList()
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: message || '保存失败'
        })
      }
    },

    /**
     * @description 保存用户商品供应商
     * <AUTHOR>
     */
    async saveUserGoodsProvider () {
      const goodsList = this.goodsList.map(goods => ({
        commodity_id: goods.commodity_id,
        provider_id: goods.provider_id || 0
      }))
      const { status, message } = await userGoodsProvider.saveGoods({
        receivable_style: this.userTypeId,
        data: JSON.stringify(goodsList),
				type: this.chooseType === 1 ? 1 : undefined
      })
      if (status) {
        this.successMessage('保存成功')
        this.loadGoodsList()
        // 清空勾选，防止保存后不选供应商也能批量设置供应商
        this.checkGoods = ''
      } else {
        this.errorNotice({
          title: '保存失败',
          desc: message || '保存失败'
        })
      }
    },
    setManyProvider () {
      if (this.checkGoods === '') {
        this.errorNotice('请先选择商品或者分类！')
        return
      }
      this._openBatchSetProviderModal()
      document.body.style.overflow = 'hidden'
    },
    _openBatchSetProviderModal () {
      this.$nextTick(() => this.$refs.batchSetProviderModal.open())
    },
    _closeBatchSetProviderModal () {
      this.$nextTick(() => this.$refs.batchSetProviderModal.close())
    },
    selectProvider () {
      var i,
        j,
        selectedGoods = this.checkGoods.split(',')
      this.providers.forEach(provider => {
        if (provider.id == this.selectedId) {
          this.selectedProvider = provider.name
        }
      })
      for (i = 0; i < this.goodsList.length; i++) {
        for (j = 0; j < selectedGoods.length; j++) {
          if (this.goodsList[i].id == selectedGoods[j]) {
            this.goodsList[i].provider_id = this.selectedId
            this.goodsList[i].provider_name = this.selectedProvider
            this.goodsList[i]._checked = true
          }
        }
      }
      this.modified = true
      this._closeBatchSetProviderModal()
      this.saveUserGoodsProvider()
    },
    addGoods () {
			if (+this.chooseType === 1) {
				this.addCategoryShow();
				return;
			}
      this.goodsModal.show = true
    },
    /**
     * @description 新增商品时设置不可选的商品
     * <AUTHOR>
     */
    setDisabledGoods () {
      let goodsIds = this.goodsList.map(goods => {
        return goods.commodity_id
      })
      this.$refs.goods.setDisableGoods(goodsIds)
    },

    getSearchPara() {
      let para = {
        // 获取商品列表的参数对象
        receivable_style: this.userTypeId,
        page: this.nowPage,
        pageSize: this.pageSize,
        query: this.search_key, //搜索关键词
        category_id: this.categoryData[0],
        category_id2: this.categoryData[1],
        category_id3: this.categoryData[2],
        provider_id: this.search_provider_selected, //供应商id
        sortName: '', //排序字段
        sortOrder: '', //排序方式
        provider_ids: this.provider_ids.length ? this.provider_ids.join(',') : ''
      }

      return para
    },
		async getSelectedCategory() {
			const res = await userGoodsProvider.getGoods({
				is_only_category_id2: 1,
				type: 1,
				receivable_style: this.userTypeId
			})
			this.alreadyExistCategory = res.data.split(',')
		},
    async loadGoodsList () {
      this.loading = true;
      let paras = this.getSearchPara()
			if (+this.chooseType === 1) {
				paras.type = 1;
			}
      let res = await userGoodsProvider.getGoods(paras)
      this.loading = false;
      if (!res.status) {
        this.errorNotice(res.message)
        return
      }

      let data = res.data
      this.goodsList = data.list
      this.page = data.pageParams
      this.page.count = parseInt(this.page.count)
      this.page.page_size = parseInt(this.page.page_size)
      this.modified = false
			if (+this.chooseType === 0) {
				this.setDisabledGoods()
			}
    },
    $_onQuit () {
      document.body.style.overflow = ''
    }
  },
  components: {
		CategoryModal,
    goodsSelect,
    SModal,
    CategorySelect,
    ProviderSelect
  },
};
</script>

<style lang="scss" scoped>
.goods-list {
  background-color: #fff;
}
.ivu-form-item {
	margin-bottom: 0;
}
.button_primary{
  border-color: var(--primary-color);
  color: var(--primary-color);
}
.goods-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  .ivu-input-wrapper {
    margin-top: -2px;
    width: 275px;
  }
  .goods-list__left {
    float: left;
    padding: 10px 0;
  }
}

/* iview custom */

.ivu-icon-ios-search {
  font-size: 20px;
  color: #fff;
}

.goods-list__select-middle {
  float: left;
  padding: 10px 0;
  margin-left: 10px;
  /deep/ .ivu-select-input {
    height: 28px!important;
    line-height: 28px!important;
  }
}

.goodslist__add-btn {
  position: absolute;
  top: 10px;
  right: 0;
}

.goods-list__btn {
  margin-right: 10px;
  float: left;
}

.ivu-input-hide-icon .ivu-input-icon {
  margin-right: 50px;
}

.fixed-bottom-op {
  text-align: left;
  position: fixed;
  z-index: 2;
  bottom: 25px;
  height: 40px;
  line-height: 40px;
  background-color: #fff;

  button {
    margin-right: 5px;
  }

  span {
    margin-right: 5px;
  }

  span:hover {
    cursor: pointer;
  }
}
.set-provider {
  position: absolute;
  padding-top: 10px;
  z-index: 2;
}
/deep/ .table-button {
  background-color: transparent !important;
  .ivu-btn {
    color: var(--primary-color);
  }
}
</style>
