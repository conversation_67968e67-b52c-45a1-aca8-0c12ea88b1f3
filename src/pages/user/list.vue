<template>
  <div class="common user__list" style="padding-top: 0">
    <Tabs
      class="goods__tab"
      style="margin-bottom: 24px"
      v-model="filters.consortium_search_type"
      @on-click="changeTab"
    >
      <TabPane label="系统客户" name="1" :index="1"></TabPane>
      <TabPane v-if="isSystemB" label="授权客户" name="2" :index="2"></TabPane>
      <TabPane v-if="!is_sys_supervisor" label="子账号" name="3" :index="3"></TabPane>
    </Tabs>
    <ListTable
      v-if="filters.consortium_search_type == 1"
      tableId="user_list_01"
      :key="222222"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      @advanceChange="showAdvance = $event"
      @on-selection-change="handleSelectionChange"
      :extra-selection-count="extraSelectionCount"
      :before-request="beforeRequest"
      :before-set-data="afterRequest"
      :advance="true"
      :show-advance="showAdvance"
      :advance-items="advanceItems"
      :filter-items="filterItems"
      :data-provider="apiUrl.getUserList"
      :border="false"
      :filters="filters"
      :outer-border="true"
      :max-line="2"
      :height="getTableHeight() - 120"
      row-key="id"
      :columns="columns"
      @on-sort-change="handleSortChange"
      ref="table"
      :pageSizeCache="{ suffix: 'user' }"
      :isOpenCustom="true"
    >
      <Button
        slot="batch-checked"
        type="primary"
        class="mr10"
        @click="handleCheckAllIds"
      >
        {{ checkedAll ? '勾选当前页内容' : '勾选所有页内容' }}
      </Button>

      <template slot="batch-operation">
        <Button
          class="mr10"
          @click="handleBatchEdit"
          styleType="btnStyleForAdd"
          v-if="hasAuthority('A006002005')"
          >批量编辑</Button
        >
        <Button
          class="mr10"
          @click="handleBatchDelete"
          styleType="btnStyleForAdd"
          v-if="hasAuthority('A006002003')"
          >批量删除</Button
        >
      </template>

      <ExportButtonMulti
        :data="exportBtnMultiData"
        slot="button"
        v-if="!is_sys_supervisor"
      ></ExportButtonMulti>
      <div
        slot="before-table"
        class="common__operation"
        v-if="!is_sys_supervisor"
        v-show="selectedRows.length === 0"
      >
        <Button @click="toSetNewUser" styleType="btnStyleForAdd">新增</Button>
        <ImportBtn
          class="ml10"
          modalClassName="user-type-import"
          :data="importData"
          @on-completed="importBtnCallBack"
          @downloadTemplate="downloadTemplate"
          ref="importbuttom"
          @click="handleDropdownClick"
        >
          <!-- 导入指定供应商 -->
          <div slot="custom-area">
            <template
              v-if="
                importKey == 'designated_supplier' ||
                importKey === 'purchase_task_designated_supplier'
              "
            >
              <div
                class="import-box__dialog__body__import__description marginT20"
              >
                <div class="import-box__dialog__body__download__text__left">
                  操作：
                </div>
                <div class="ml12">
                  <RadioGroup
                    v-model="template_type"
                    @on-change="handleTemplateTypeChange"
                  >
                    <Radio :label="1">新增</Radio>
                    <Radio :label="2" style="margin-left: 16px">编辑</Radio>
                  </RadioGroup>
                </div>
              </div>
              <div
                class="import-box__dialog__body__import__description"
                style="margin-top: 6px"
              >
                <div class="import-box__dialog__body__download__text__left">
                  指定方式：
                </div>
                <div class="ml12">
                  <RadioGroup v-model="type" @on-change="handleTypeChange">
                    <Radio :label="0">按商品</Radio>
                    <Radio :label="1" style="margin-left: 16px">按分类</Radio>
                  </RadioGroup>
                </div>
              </div>
            </template>
            <template v-if="importKey == 'protocol_enabled'">
              <div
                class="import-box__dialog__body__import__description marginT20"
              >
                <div class="import-box__dialog__body__download__text__left">
                  导入模式：
                </div>
                <div class="ml12">
                  <RadioGroup
                    v-model="template_type"
                    @on-change="handleTemplateTypeChange"
                  >
                    <Radio :label="0">导入新增</Radio>
                    <Radio :label="1" style="margin-left: 16px">导入修改</Radio>
                  </RadioGroup>
                </div>
              </div>
              <div
                v-if="template_type == 1"
                style="color: #999; margin-left: 50px"
              >
                使用导入修改模式，导入协议价的模版中会自动填充筛选结果下的客户数据。导入后会以最终导入模版的数据为准，如果客户所属协议单列为空，则保存时将删除所有协议单
              </div>
            </template>
            <template v-if="importKey == 'cycle_enabled'">
              <div class="import-explain">
                <h4>导入说明：</h4>
                <p>1.该导入仅针对客户账期开启时间账期设置。</p>
                <p>
                  2.所有的数据会以导入为准，已经选择周账期或者月账期的数据，在导入时不会变动账期时间。
                </p>
              </div>
            </template>
          </div>
        </ImportBtn>
      </div>
    </ListTable>
    <ListTable
      v-else-if="filters.consortium_search_type == 2"
      :key="111111"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :advance="true"
      :filter-items="filterItems2"
      :advance-items="advanceItems2"
      :show-advance="true"
      data-provider="/superAdmin/userSuper/list"
      :border="false"
      :filters="filters"
      :outer-border="true"
      :max-line="2"
      :height="getTableHeight() - 120"
      :columns="columns2"
      @on-sort-change="handleSortChange2"
      ref="table2"
    >
    </ListTable>
    <ListTable
      v-else
      :key="333333"
      :initParams="{ status: 0 }"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :advance="true"
      :filter-items="filterItems3"
      :advance-items="advanceItems3"
      :show-advance="true"
      data-provider="/superAdmin/userSuper/pageGetSubaccount"
      :border="false"
      :filters="filters"
      :outer-border="true"
      :max-line="2"
      :height="getTableHeight() - 120"
      :columns="columns3"
      ref="table3"
    >
      <div slot="before-table" class="common__operation">
        <Button @click="onAddAccount" styleType="btnStyleForAdd">新增</Button>
      </div>
    </ListTable>
    <Modal
      v-model="labelSet"
      @on-visible-change="cancelSetUp"
      :closable="false"
      width="900"
    >
      <Table :columns="labelColumns" :data="tagList"></Table>
      <div slot="footer">
        <Button @click="labelSet = false">取消</Button>
        <Button type="primary" @click="SetUp">确定</Button>
      </div>
    </Modal>
    <Modal v-model="bindingShow" title="提示" width="300">
      <p>确定要解绑企业微信账号？</p>
      <div slot="footer">
        <Button @click="bindingShow = false">取消</Button>
        <Button type="primary" @click="binding">确定</Button>
      </div>
    </Modal>
    <add-child-account-modal ref="addChildAccount" @save="onAccountChange" />
    <sdpPollingTask ref="sdpPollingTask" :fail-log="1"> </sdpPollingTask>
    <batchEditModal ref="batchEditModal" @confirm="handleTaskApi(2, $event)">
    </batchEditModal>
  </div>
</template>

<script>
import Button from '@components/button';
import ImportBtn from '@components/import-button-multi';
import ExportButtonMulti from '@components/common/export-btn-multi';
import ListTable from '@components/list-table';
import { MINE_TYPE } from '@/util/const';
import common from '@api/user.js';
import { api } from '@api/api.js';
import goodsAutoCompleteSelect from '@components/common/goodsAutoCompleteSelect_new';
import SCheckboxGroup from '@components/CheckboxGroup';
import ConfigMixin from '@/mixins/config';
import batchTableCheckedAll from '@/mixins/batch-table-checked-all';
import sdpPollingTask from '@/components/standard/sdp-polling-task/index';
import StorageUtil from '@/util/storage';
import AddChildAccountModal from './components/AddChildAccountModal';
import batchEditModal from './components/batch-edit-modal';
import authority from '@/util/authority.js';
import { exportLoop } from '@/components/common/export-btn/util';
import {
  circuit,
  group,
  saller,
  userType,
  userTag,
  area,
  selfPickup,
} from '@/components/standard/sdp-filter-items'
import { BUSINESS_TYPE_LIST } from '@/util/const';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const { hasAuthority } = authority;

export default {
  name: 'userList',
  mixins: [ConfigMixin, batchTableCheckedAll],
  components: {
    sdpPollingTask,
    batchEditModal,
    Button,
    ImportBtn,
    ListTable,
    ExportButtonMulti,
    AddChildAccountModal,
  },
  data() {
    return {
      filters: {
        consortium_search_type: '1',
      },
      showAdvance: false,
      ready: false,
      qy_weixin_app_status: '',
      cancelSetUpshow: false,
      currentSetTagUser: {},
      labelColumns: [],
      tagList: [],
      labelSet: false,
      bindingShow: false,
      userList: [],
      type: 'user',
      titleCol: [],
      delivery_date: new Date(),
      originCols: [
        {
          title: '客户账号',
          key: 'account_tel',
          align: 'left',
          render: (h, params) => {
            let data = params.row;
            let Icon = '';
            if (this.qy_weixin_app_status == 0) {
              Icon = '';
            } else {
              data.qw_external_userid ? (Icon = 'Icon') : (Icon = '');
            }
            return h('span', [
              h('span', data.account_tel),
              h(Icon, {
                props: {
                  type: 'ios-link-outline',
                },
                style: {
                  'font-size': '16px',
                  'margin-left': '5px',
                },
              }),
            ]);
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
          align: 'left',
        },
        {
          title: '客户名称',
          key: 'email',
          align: 'left',
        },
        {
          title: '联系人',
          key: 'name',
          align: 'left',
        },
        {
          title: '收货地址',
          key: 'address_detail',
          align: 'left',
        },
        {
          title: '区域',
          key: 'area_id',
          align: 'left',
        },
        {
          title: '是否支持自提点',
          minWidth: 140,
          key: 'is_self_pickup_point',
          render(h, params) {
            return h(
              'span',
              params.row.is_self_pickup_point == 1 ? '是' : '否',
            );
          },
        },
        {
          title: '送货方式',
          minWidth: 140,
          key: 'is_self_pickup_point_desc',
        },
        {
          title: '客户类型',
          key: 'receivable_style',
          align: 'left',
        },
        {
          title: '业务员',
          key: 'refer_id',
          align: 'left',
        },
        // {
        //   title: '客户账期',
        //   key: 'remind',
        // },
        {
          title: '账期',
          key: 'remind_day',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {
                style: {
                  color: row.day_remind_level === 'danger' ? 'red' : '',
                },
              },
              row.remind_day,
            );
          },
        },
        {
          title: '授信额度',
          key: 'remind_price',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {
                style: {
                  color: row.price_remind_level === 'danger' ? 'red' : '',
                },
              },
              row.remind_price,
            );
          },
        },
        {
          title: '客户标签',
          key: 'qw_tags',
          render: (h, params) => {
            let show = '';
            if (this.qy_weixin_app_status == 0) {
              show = '';
            } else {
              show = h(
                'div',
                params.row.qw_tags.map((tag) =>
                  h(
                    tag.tag_name == null ? '' : 'Tag',
                    {
                      props: {
                        type: 'border',
                        color: 'success',
                      },
                    },
                    tag.tag_name,
                  ),
                ),
              );
            }
            return show;
          },
          width: 150,
        },
        {
          title: '创建时间',
          key: 'create_time',
          align: 'left',
          width: 160,
        },
        {
          title: '状态',
          key: 'status',
          align: 'left',
          render: (h, params) => {
            let data = params.row,
              colorClass = '';
            // 正常状态
            if (data.status == '已禁用') {
              colorClass = 'c-gray-color';
            }
            // 禁用状态
            if (data.status == '待审核') {
              colorClass = 'c-red-color';
            }
            return h(
              'span',
              {
                class: colorClass,
              },
              data.status,
            );
          },
        },
        {
          title: '操作',
          key: 'action',
          render: (h, params) => {
            let data = params.row,
              id = data.id,
              site_id = data.site_id;
            let span = '';
            if (this.qy_weixin_app_status !== 0) {
              if (data.qw_external_userid !== undefined) {
                span = 'span';
              } else {
                span = '';
              }
            }
            return h(
              'ul',
              {
                class: {
                  btnsPanel: true,
                },
              },
              [
                h(
                  'span',
                  {
                    attrs: {
                      order_id: data.id, // 配合打印模块
                    },
                    on: {
                      click: () => {
                        this.delUser(id);
                      },
                    },
                  },
                  '删除',
                ),
                h(
                  'span',
                  {
                    on: {
                      click: () => {
                        this.toUserDetail(id, data.receivable_style_id);
                      },
                    },
                  },
                  '编辑',
                ),
                h(
                  'span',
                  {
                    on: {
                      click: () => {
                        let WxStatus = this.qy_weixin_app_status;
                        this.viewUserDetail(id, site_id, WxStatus);
                      },
                    },
                  },
                  '详情',
                ),
                h(
                  span,
                  {
                    on: {
                      click: () => {
                        this.userid = params.row.id;
                        this.bindingShow = true;
                      },
                    },
                  },
                  '解绑',
                ),
                h(
                  span,
                  {
                    on: {
                      click: () => {
                        this.CustomerLabel();
                        this.labelSetUp(params);
                      },
                    },
                  },
                  '设置',
                ), // 设置标签
              ],
            );
          },
        },
      ],
      filterItems: [
        {
          checked: false,
          label: '客户类型',
          type: 'custom',
          key: 'receStyle',
          component: userType,
        },
        {
          checked: true,
          required: true,
          label: '客户搜索',
          type: 'Input',
          key: 'searchKey',
          props: {
            placeholder: '客户名称 / 编码 / 账号 /联系人',
          },
        },
        {
          type: 'Select',
          key: 'business_type',
          label: '客户业态',
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
          data: [
            {
              label: '全部',
              value: '',
            },
            ...BUSINESS_TYPE_LIST
          ],
          props: {
            placeholder: '选择客户业态',
          },
        },
      ],
      filterItems2: [
        {
          checked: true,
          required: true,
          label: '客户搜索',
          type: 'Input',
          key: 'searchKey',
          props: {
            placeholder: '客户名称 / 编码 / 账号 /联系人',
          },
        },
        {
          checked: true,
          required: true,
          label: '区域',
          type: 'custom',
          key: 'area_id',
          component: area,
          attrs: {
            defaultOptions: [{ label: '未设置', value: '-1' }]
          }
        },
      ],
      advanceItems2: [
        {
          checked: true,
          required: true,
          label: '默认线路',
          key: 'line_id',
          type: 'custom',
          component: circuit,
        },
      ],
      filterItems3: [
        {
          checked: true,
          required: true,
          label: '子账号',
          type: 'Input',
          key: 'search_sub',
          props: {
            placeholder: '请输入子账号',
          },
        },
        {
          checked: true,
          required: true,
          label: '客户搜索',
          type: 'Input',
          key: 'search_client',
          props: {
            placeholder: '客户名称 / 编码 / 账号 /联系人',
          },
        },
      ],
      advanceItems3: [
        {
          checked: false,
          label: '客户类型',
          type: 'custom',
          key: 'receivable_style',
          component: userType,
        },
      ],
      advanceItems: [
        {
          items: [
            {
              checked: false,
              label: '区域',
              type: 'custom',
              key: 'area_id',
              component: area,
              attrs: {
                defaultOptions: [{ label: '未设置', value: '-1' }]
              }
            },
            {
              checked: false,
              label: '默认线路',
              key: 'line_id',
              type: 'custom',
              component: circuit,
            },
            {
              checked: true,
              required: true,
              label: '账户状态',
              type: 'Select',
              key: 'status',
              data: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '未审核',
                  value: '1',
                },
                {
                  label: '正常',
                  value: '2',
                },
                {
                  label: '被禁用',
                  value: '3',
                },
              ],
              defaultValue:
                StorageUtil.getLocalStorage('user-list-check-state') || '',
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: true,
              label: '业务员',
              type: 'custom',
              key: 'refer_id',
              component: saller,
              attrs: {
                apiName: this.apiUrl.getSalesList,
                params: {
                  in_list: 1, // 返回list结构
                },
                defaultOptions: [{ label: '未设置', value: '-1' }]
              }
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '集团',
              type: 'custom',
              key: 'group_id',
              component: group,
            },
            {
              checked: false,
              label: '协议价',
              type: 'Select',
              key: 'agreementPrice',
              data: [
                {
                  label: '全部',
                  value: 'all',
                },
                {
                  label: '有协议价',
                  value: 'y',
                },
                {
                  label: '无协议价',
                  value: 'n',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: false,
              label: '是否打点',
              type: 'Select',
              key: 'position',
              data: [
                {
                  label: '全部',
                  value: 'all',
                },
                {
                  label: '已打点',
                  value: 'y',
                },
                {
                  label: '未打点',
                  value: 'n',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: false,
              label: '默认自提点',
              type: 'custom',
              key: 'self_pickup_point_id',
              component: selfPickup,
            },
            {
              checked: false,
              label: '送货方式',
              type: 'Select',
              key: 'is_self_pickup_point',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '送货上门',
                  value: 0,
                },
                {
                  label: '门店自提',
                  value: 2,
                },
                {
                  label: '送货上门 + 门店自提',
                  value: 1,
                },
              ],
              props: {
                placeholder: '全部',
                filterable: true,
                clearable: true,
                'filter-by-label': true,
              },
            },
            {
              checked: false,
              label: '是否支持自提点',
              type: 'Select',
              key: 'is_self_pickup_point',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '支持',
                  value: 1,
                },
                {
                  label: '不支持',
                  value: 0,
                },
              ],
              props: {
                placeholder: '全部',
                filterable: true,
                clearable: true,
                'filter-by-label': true,
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '客户账期',
              key: 'remind_type',
              type: 'Cascader',
              props: {
                data: [
                  {
                    label: '全部',
                    value: '',
                  },
                  {
                    label: '无账期',
                    value: '0',
                  },
                  {
                    label: '时间账期',
                    value: '2',
                    children: [
                      {
                        label: '全部',
                        value: '',
                      },
                      {
                        label: '自定义账期',
                        value: '1',
                      },
                      {
                        label: '周账期',
                        value: '2',
                      },
                      {
                        label: '月账期',
                        value: '3',
                      },
                    ],
                  },
                  {
                    label: '金额账期',
                    value: '1',
                  },
                ],
              },
            },
            {
              checked: false,
              type: 'DatePicker',
              props: {
                type: 'daterange',
                placeholder: '请选择',
              },
              key: ['startTime', 'endTime'],
              label: '创建日期',
            },
            {
              label: '注册渠道',
              type: 'Select',
              key: 'source_from',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '其他',
                  value: 0,
                },
                {
                  label: '后台注册',
                  value: 1,
                },
                {
                  label: '商城注册',
                  value: 2,
                },
                {
                  label: '业务员注册',
                  value: 3,
                },
              ],
              props: {
                placeholder: '全部',
                filterable: true,
                clearable: true,
                'filter-by-label': true,
              },
            },
            {
              checked: false,
              label: '子账号',
              type: 'Input',
              key: 'sub_account',
              props: {
                placeholder: '请输入子账号',
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '时间账期',
              type: 'Select',
              key: 'day_remind_status',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '正常',
                  value: '1',
                },
                {
                  label: '时间账期提醒',
                  value: '2',
                },
                {
                  label: '时间账期逾期',
                  value: '3',
                },
                {
                  label: '时间账期到期',
                  value: '4',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: false,
              label: '授信额度',
              type: 'Select',
              key: 'price_remind_status',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '正常',
                  value: '1',
                },
                {
                  label: '授信额度提醒',
                  value: '2',
                },
                {
                  label: '授信额度超出',
                  value: '3',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: true,
              label: '屏蔽/售卖商品',
              type: 'custom',
              key: 'disabled_commodity_id',
              component: goodsAutoCompleteSelect,
              defaultValue: '',
            },
            {
              checked: false,
              label: '认证状态',
              type: 'Select',
              key: 'auth_status',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '已认证',
                  value: '1',
                },
                {
                  label: '未认证',
                  value: '0',
                },
              ],
              props: {
                placeholder: '全部',
              },
            },
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
            {
              checked: false,
              required: false,
              block: true,
              label: '重点关注',
              key: ' is_focus_on_sorting',
              type: 'custom',
              defaultValue: [],
              props: {
                data: [
                  {
                    label: '分拣',
                    value: 1,
                  },
                ],
              },
              style: {
                width: '90%',
              },
              component: SCheckboxGroup,
              onChange: (value) => {
                return { value: value ? value.join(',') : '', stop: true };
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              required: false,
              show: true,
              label: '客户标签',
              key: 'user_tag',
              type: 'custom',
              block: true,
              style: {
                width: '90%',
              },
              component: userTag,
              onChange: (value) => {
                return { value, stop: true };
              },
            },
          ],
        },
      ],
      columns2: [
        {
          title: '客户账号',
          key: 'account_tel',
          width: 180,
          fixed: 'left',
          align: 'left',
          render: (h, params) => {
            let data = params.row;
            let Icon = '';
            const { id, site_id } = data;
            if (this.qy_weixin_app_status == 0) {
              Icon = '';
            } else {
              data.qw_external_userid ? (Icon = 'Icon') : (Icon = '');
            }

            return h('div', [
              h(
                'span',
                {
                  style: {
                    margin: '0 3px 0',
                  },
                  class: {
                    dn: +params.row.is_real == 1 ? false : true,
                    'common-tag': true,
                    'tag-green': true,
                  },
                },
                '实',
              ),
              h('span', [
                h(
                  'span',
                  {
                    class: {
                      'common-link': true,
                    },
                    on: {
                      click: () =>
                        this.viewUserDetail(
                          id,
                          site_id,
                          this.qy_weixin_app_status,
                        ),
                    },
                  },
                  data.account_tel,
                ),
                h(Icon, {
                  props: {
                    type: 'ios-link-outline',
                  },
                  style: {
                    'font-size': '16px',
                    'margin-left': '5px',
                  },
                }),
              ]),
            ]);
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
          width: 140,
          sortable: true,
          poptip: true,
          asc: true,
          align: 'left',
        },
        {
          title: '客户名称',
          key: 'email',
          asc: true,
          resizable: true,
          sortable: true,
          poptip: true,
          popperClass: 'sdp-td_cell_content ',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '联系人',
          key: 'name',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '区域',
          key: 'area_id',
          minWidth: 140,
          align: 'left',
        },
        {
          title: '是否支持自提点',
          minWidth: 140,
          align: 'center',
          key: 'is_self_pickup_point',
          render(h, params) {
            return h(
              'span',
              params.row.is_self_pickup_point == 1 ? '是' : '否',
            );
          },
        },
        {
          title: '送货方式',
          minWidth: 140,
          align: 'center',
          key: 'is_self_pickup_point_desc',
        },
        {
          title: '客户类型',
          key: 'receivable_style',
          align: 'left',
          poptip: true,
          width: 120,
        },
        {
          title: '客户来源',
          key: 'consortium_platform_exterior_name',
          show: () => !this.isWuhanYuxin,
          minWidth: 140,
        },
        {
          title: '状态',
          key: 'status',
          align: 'left',
          width: 100,
          render: (h, params) => {
            let data = params.row;
            let color = '';
            // 正常状态
            if (data.status == '禁用') {
              color = '#F13130';
            }
            // 禁用状态
            if (data.status == '待审核') {
              color = '#FF9F00';
            }
            return h(
              'span',
              {
                style: { color },
              },
              data.status,
            );
          },
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 210,
          actions: (params) => {
            let actions = [
              {
                name: '详情',
                action: (params) => {
                  const { id, site_id } = params.row;
                  let WxStatus = this.qy_weixin_app_status;
                  this.viewUserDetail(id, site_id, WxStatus);
                },
              },
              {
                name: '编辑',
                action: (params) => {
                  this.toUserDetail(
                    params.row.id,
                    params.row.receivable_style_id,
                  );
                },
              },
            ];
            return actions;
          },
        },
      ],
      columns3: [
        {
          title: '子账号（手机号）',
          key: 'account_tel',
          minWidth: 140,
        },
        {
          title: '子账号名称',
          key: 'sub_name',
          minWidth: 140,
        },
        {
          title: '子账号联系人',
          key: 'contacts',
          minWidth: 140,
        },
        {
          title: '所属客户',
          key: 'user_name',
          minWidth: 140,
        },
        {
          title: '所属客户账号',
          key: 'user_tel',
          minWidth: 140,
        },
        {
          title: '商品价格显示',
          key: 'is_show_price',
          minWidth: 140,
          render: (h, params) => {
            let data = params.row;
            return h('span', data.is_show_price == 1 ? '是' : '否');
          },
        },
        {
          title: '子账号状态',
          key: 'is_lock',
          minWidth: 140,
          render: (h, params) => {
            let data = params.row;
            return h('span', data.is_lock == 1 ? '禁用' : '正常');
          },
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 210,
          actions: () => {
            let actions = [
              {
                name: '编辑',
                action: (params) => {
                  this.onAddAccount(params.row);
                },
              },
              {
                name: '删除',
                action: (params) => {
                  this.onDelChildAccount(params.row);
                },
                confirm: '确定删除该子账号',
              },
            ];
            return actions;
          },
        },
      ],
      columns: [
        {
          type: 'titleCfg',
          width: 52,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'user',
          style: {
            paddingRight: 0,
          },
        },
        {
          type: 'selection',
          key: 'selection',
          width: 36,
          fixed: 'left',
          style: {},
        },
        {
          title: '客户账号',
          key: 'account_tel',
          width: 180,
          fixed: 'left',
          align: 'left',
          render: (h, params) => {
            let data = params.row;
            let Icon = '';
            const { id, site_id } = data;
            if (this.qy_weixin_app_status == 0) {
              Icon = '';
            } else {
              data.qw_external_userid ? (Icon = 'Icon') : (Icon = '');
            }

            return h('div', [
              h(
                'span',
                {
                  style: {
                    margin: '0 3px 0',
                  },
                  class: {
                    dn: +params.row.is_real == 1 ? false : true,
                    'common-tag': true,
                    'tag-green': true,
                  },
                },
                '实',
              ),
              h('span', [
                h(
                  'span',
                  {
                    class: {
                      'common-link': true,
                    },
                    on: {
                      click: () =>
                        this.viewUserDetail(
                          id,
                          site_id,
                          this.qy_weixin_app_status,
                        ),
                    },
                  },
                  data.account_tel,
                ),
                h(Icon, {
                  props: {
                    type: 'ios-link-outline',
                  },
                  style: {
                    'font-size': '16px',
                    'margin-left': '5px',
                  },
                }),
              ]),
            ]);
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
          width: 140,
          sortable: true,
          poptip: true,
          asc: true,
          align: 'left',
        },
        {
          title: '客户名称',
          key: 'email',
          asc: true,
          resizable: true,
          sortable: true,
          poptip: true,
          popperClass: 'sdp-td_cell_content',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '客户业态',
          key: 'business_type_desc',
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
          align: 'left',
          minWidth: 100
        },
        {
          title: '联系人',
          key: 'name',
          align: 'left',
          minWidth: 120,
        },
        {
          title: '收货地址',
          key: 'address_detail',
          width: 200,
          poptip: true,
          align: 'left',
        },
        {
          title: '区域',
          key: 'area_id',
          minWidth: 140,
          align: 'left',
        },
        {
          title: '默认线路',
          key: 'line_id',
        },
        {
          title: '客户类型',
          key: 'receivable_style',
          align: 'left',
          poptip: true,
          popperClass: 'sdp-td_cell_content',
          width: 120,
        },
        {
          title: '业务员',
          key: 'refer_id',
          align: 'left',
        },
        {
          title: '账期',
          key: 'remind_day',
          width: 90,
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {
                style: {
                  color: row.day_remind_level === 'danger' ? 'red' : '',
                },
              },
              row.remind_day,
            );
          },
        },
        {
          title: '授信额度',
          key: 'remind_price',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {
                style: {
                  color: row.price_remind_level === 'danger' ? 'red' : '',
                },
              },
              row.remind_price,
            );
          },
        },
        {
          title: '账期到期',
          key: 'remind_desc',
          tip: '当前列表逾期展示为客户最新账期状态，黑色表示未到期、红色表示已逾期、黄色表示提醒、橙色表示已到期但未逾期',
          width: 140,
          render: (h, params) => {
            let data = params.row;
            let dayEl = '';
            let priceEl = '';
            // 1 是正常 2 是提醒 3 是逾期 4 表示到期
            const colors = {
              2: '#FF9F00', // 黄色
              3: '#F13130', // 已逾期：红色
              4: '#FF6E00', // 已到期
            };
            if (!data.remind_desc) {
              return '--';
            }
            let remindDescArr = params.row.remind_desc.split('/');
            if (data.remain_day !== '') {
              dayEl = h(
                'span',
                { style: { color: colors[data.day_remind_status] } },
                remindDescArr[0],
              );
            }
            if (data.remain_price !== '') {
              let priceDesc = '';
              if (remindDescArr.length > 1) {
                priceDesc = remindDescArr[1];
              }
              if (remindDescArr.length === 1) {
                priceDesc = remindDescArr[0];
              }
              priceEl = h(
                'span',
                { style: { color: colors[data.price_remind_status] } },
                priceDesc,
              );
            }

            let split = '';
            if (dayEl && priceEl) {
              split = '/';
            }
            return !dayEl && !priceEl
              ? '--'
              : h('div', [dayEl, split, priceEl]);
          },
        },
        {
          title: '客户标签',
          key: 'qw_tags',
          render: (h, params) => {
            let show = '';
            if (this.qy_weixin_app_status == 0) {
              return h('span', '--');
            } else {
              if (!params.row.qw_tags || params.row.qw_tags.length === 0) {
                return h('span', '--');
              }
              show = h(
                'div',
                params.row.qw_tags.map((tag) =>
                  h(
                    !tag.tag_name ? 'div' : 'Tag',
                    {
                      props: {
                        type: 'border',
                        color: 'success',
                      },
                    },
                    !tag.tag_name ? '--' : tag.tag_name,
                  ),
                ),
              );
            }
            return show;
          },
          width: 150,
        },
        {
          title: '创建时间',
          key: 'create_time',
          align: 'left',
          width: 160,
        },
        {
          title: '注册渠道',
          key: 'source_from_text',
          align: 'left',
          tip: '某一个客户通过何种方式进行注册的（2023年9月之前的客户注册数据均记录为其他）',
          width: 120,
        },
        {
          title: '状态',
          key: 'status',
          align: 'left',
          width: 100,
          render: (h, params) => {
            let data = params.row;
            let color = '';
            // 正常状态
            if (data.status == '禁用') {
              color = '#F13130';
            }
            // 禁用状态
            if (data.status == '待审核') {
              color = '#FF9F00';
            }
            return h(
              'span',
              {
                style: { color },
              },
              data.status,
            );
          },
        },
        {
          title: '客户标签',
          key: 'user_tag_text',
        },
        {
          title: '默认自提点',
          key: 'self_pickup_point_name',
        },
        {
          align: 'center',
          title: '送货方式',
          minWidth: 140,
          key: 'is_self_pickup_point_desc',
        },
        {
          align: 'center',
          title: '是否支持自提点',
          minWidth: 140,
          key: 'is_self_pickup_point',
          render(h, params) {
            return h(
              'span',
              Number(params.row.is_self_pickup_point) === 1 ? '支持' : '不支持',
            );
          },
        },
        {
          title: '客户ID',
          key: 'id',
          align: 'left',
          width: 100,
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          width: 210,
          actions: (params) => {
            let actions = [
              {
                name: '详情',
                action: (params) => {
                  const { id, site_id } = params.row;
                  let WxStatus = this.qy_weixin_app_status;
                  this.viewUserDetail(id, site_id, WxStatus);
                },
              },
              {
                name: '编辑',
                action: (params) => {
                  this.toUserDetail(
                    params.row.id,
                    params.row.receivable_style_id,
                  );
                },
              },
              {
                name: '删除',
                attrs: (params) => {
                  return {
                    order_id: params.row.id,
                  };
                },
                action: (params) => {
                  this.delUser(params.row.id);
                },
                confirm: '确定删除该客户',
              },
            ];
            const wxActions = [
              {
                name: '解绑',
                action: (params) => {
                  this.userid = params.row.id;
                  this.bindingShow = true;
                },
              },
              {
                name: '设置',
                action: (params) => {
                  this.CustomerLabel();
                  this.labelSetUp(params);
                },
              },
            ];
            if (
              this.qy_weixin_app_status !== 0 &&
              params.row.qw_external_userid !== undefined
            ) {
              actions = actions.concat(wxActions);
            }
            // 监管人员只返回详情按钮
            if (this.is_sys_supervisor) {
              return actions.filter((k) => k.name === '详情');
            }
            return actions;
          },
        },
      ],
      data: [],
      importData: [
        {
          title: '导入客户信息',
          download: {
            url: '/superAdmin/userSuper/userTemplate?is_process=0',
            text: '客户信息模版',
          },
          post: {
            url: '/superAdmin/userSuper/userImport?is_api=1&is_process=0',
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
          },
        },
        {
          title: '导入开票信息',
          key: 'invoice_enabled',
          download: {
            url: '/superAdmin/invoice/UserInvoiceConfigTemplate',
            text: '开票信息模版',
          },
          post: {
            url: '/superAdmin/invoice/ImportInvoiceInfo',
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
          },
        },
        {
          title: '导入指定供应商',
          key: 'designated_supplier',
          downloadUrl: '/superAdmin/customProvider/importTemplate',
          download: {
            text: '指定供应商导入模板',
          },
          offline: true,
          post: {
            url: `/superAdmin/customProvider/offlineImport`,
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
            extraParams: {},
          },
        },
        {
          title: '导入采购任务指定供应商',
          key: 'purchase_task_designated_supplier',
          downloadUrl: '/superAdmin/PurchaseTaskCustomProvider/importTemplate',
          download: {
            text: '采购任务指定供应商导入模板',
          },
          offline: true,
          post: {
            url: `/superAdmin/PurchaseTaskCustomProvider/offlineImport`,
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
            extraParams: {},
          },
        },
        {
          title: '导入协议价',
          key: 'protocol_enabled',
          downloadUrl: '/superAdmin/userSuper/importProtocolTemplate',
          download: {
            text: '导入协议价',
          },
          offline: true,
          post: {
            url: '/superAdmin/userSuper/importProtocol',
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
            extraParams: {},
          },
        },
        {
          title: '导入时间账期',
          key: 'cycle_enabled',
          downloadUrl: '/superAdmin/userSuper/remindTemplate',
          download: {
            text: '时间账期模板',
          },
          offline: true,
          post: {
            url: '/superAdmin/userSuper/importRemind',
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xlsx'],
            extraParams: {},
          },
        },
      ],
      orderList: [],
      exportBtnMultiData: [
        {
          text: '客户',
          api: '/superAdmin/userSuper/userExport',
          offline: true,
          paramGetter: () => this.getExportParams(),
        },
        {
          text: '指定供应商',
          api: '/superAdmin/userSuper/userExport',
          offline: true,
          paramGetter: () => this.getExportParams('bind_provider_com'),
        },
        {
          key: 'export_user_store_auth',
          text: '商户认证名单',
          api: '/superAdmin/userSuper/ExportUserStoreAuth',
          paramGetter: () => this.getExportParams(),
          onClick: () => {
            this.$Message.loading({
              title: '提示',
              content: '导出中...',
            });
            this.$request.get('/superAdmin/userSuper/ExportUserStoreAuth', this.getExportParams()).then(({ status, data, message }) => {
              if (status === 0) {
                this.errorNotice({ title: '导出失败', desc: message });
                return;
              }
              window.open(data.file || data.url || data, '_self');
              this.successNotice('导出成功');
            }).finally(() => {
              this.$Message.destroy();
            });
            return false; // 阻止默认的导出操作，因为我们已经在 onClick 中处理了导出逻辑
          }
        },
        {
          key: 'purchase_task_designated_supplier',
          text: '采购任务指定供应商',
          api: '/superAdmin/userSuper/userExport',
          offline: true,
          paramGetter: () =>
            this.getExportParams('bind_purchase_task_provider_com'),
        },
      ],
      template_type: 1,
      type: 0,
      importKey: '',
    };
  },
  created() {
    this._initEditionConfig();
  },
  mounted() {
    this.Customer();
  },
  activated() {
    if (this.ready) {
      this.$refs.table && this.$refs.table.fetchData(false);
      this.$refs.table2 && this.$refs.table2.fetchData(false);
    } else {
      this.ready = true;
    }
    this._initQueryParams();
  },
  deactivated() {},
  computed: {
    // 武汉常鑫玉鑫
    isWuhanYuxin() {
      const projectInfo = JSON.parse(this.sysConfig.project_info);
      return projectInfo.project_code === 'SDP06654';
    },
  },
  watch: {
    $route() {
      this._initQueryParams();
    },
  },
  methods: {
    changeTab() {},
    hasAuthority,
    handleCheckAllIds() {
      const params = this.beforeRequest(this.$refs.table.getParams());
      this.handleCheckedChange({ apiName: 'getUserList', params });
    },
    handleTaskApi(type, params = {}) {
      // 1 删除, 2 编辑
      const ids = this.handleGetRealId();
      const array = ids;
      const groupSize = 10;

      const result = Array.from(
        { length: Math.ceil(array.length / groupSize) },
        (_, i) =>
          array.slice(i * groupSize, i * groupSize + groupSize).join(','),
      );

      let tasks = [];
      result.forEach((id) => {
        tasks.push(() => {
          return this.$request.post(
            this.apiUrl[type === 1 ? 'batchDeleteUser' : 'batchEditUser'],
            {
              ...params,
              user_ids: id,
            },
          );
        });
      });
      this.$refs.sdpPollingTask
        .createRequest(tasks, 1, {
          array: result,
          label: type === 1 ? '批量删除' : '批量编辑',
          isContinue: (res) => {
            if (res.status === 0) {
              this.$Message.error(res.message);
              return false;
            }
            return true;
          },
        })
        .then((results) => {
          console.log('success->', results);
        })
        .catch((reason) => {
          console.log('fail->', reason);
        })
        .finally(() => {
          this.$refs.table.fetchData(true);
        });
    },
    // 批量编辑
    handleBatchEdit() {
      this.$refs.batchEditModal.open();
    },
    // 批量删除
    handleBatchDelete() {
      const ids = this.handleGetRealId();
      if (ids && ids.length === 0) {
        this.$Message.error('请先选择用户');
        return;
      }

      this.$smodal({
        type: 'warning',
        title: '提示',
        text: '确定删除所选客户',
        mask: true,
        onOk: () => {
          this.handleTaskApi(1);
        },
        onQuit: () => {},
      });
    },
    handleSortChange(columns, key, order) {
      let keyAlias = {
        user_code: 'user_code',
        email: 'user_name',
      };
      if (order) {
        this.filters.sort_field = keyAlias[key];
        this.filters.sort_type = order;
      } else {
        this.filters.sort_field = '';
        this.filters.sort_type = '';
      }
      this.$refs.table.fetchData(false);
    },
    handleSortChange2(columns, key, order) {
      let keyAlias = {
        user_code: 'user_code',
        email: 'user_name',
      };
      if (order) {
        this.filters.sort_field = keyAlias[key];
        this.filters.sort_type = order;
      } else {
        this.filters.sort_field = '';
        this.filters.sort_type = '';
      }
      this.$refs.table2.fetchData(false);
    },
    onAddAccount(row) {
      this.$refs.addChildAccount.show(true, row);
    },
    onDelChildAccount(row) {
      const params = {
        id: row.id,
        pid: row.pid,
      };
      this.$request.post(api.deleteSubaccount, params).then((res) => {
        let { status, message } = res;
        if (status == 1) {
          this.$Notice.success({
            title: '操作成功！',
          });
          this.$refs.table3.fetchData(false);
        } else {
          this.$Notice.error({
            title: message,
          });
        }
      });
    },
    onAccountChange() {
      this.$refs.table3.fetchData(false);
    },
    /**
     * @description: 初始化版本配置
     */
    _initEditionConfig() {
      // 没有开票功能
      if (!this.$hasModule('invoice_enabled')) {
        this.importData = this.importData.filter((item) =>
          this.$hasModule(item.key),
        );
      }
      if (+this.sysConfig.is_open_purchase_task_bind_provider !== 1) {
        this.importData = this.importData.filter(
          (item) => item.key !== 'purchase_task_designated_supplier',
        );
        this.exportBtnMultiData = this.exportBtnMultiData.filter(
          (item) => item.key !== 'purchase_task_designated_supplier',
        );
      }

      // 前置条件：项目必须在pms中同时开启【微信门店助手】【开启平台微信支付 - 10.微信B2B】
      if (!(+this.sysConfig.is_open_b2b_store_assistant === 1 && +this.sysConfig.platform_wechat_pay === 10)) {
        this.exportBtnMultiData = this.exportBtnMultiData.filter(
          (item) => item.key !== 'export_user_store_auth',
        );
      }
    },
    /**
     * @description: 路由传参时，初始化筛选条件
     */
    _initQueryParams() {
      this.$nextTick(() => {
        if (
          this.$route.query.day_remind_status ||
          this.$route.query.price_remind_status
        ) {
          this.showAdvance = true;
          this.$refs.table.$refs.filter.handleReset();
          this.$refs.table.setValue(
            'day_remind_status',
            this.$route.query.day_remind_status || '',
          );
          this.$refs.table.setValue(
            'price_remind_status',
            this.$route.query.price_remind_status || '',
          );
          this.$router.replace('/user/list');
        }
      });
    },
    open() {
      this.$refs.custom.open();
    },
    getUserList() {
      this.$refs.table.fetchData(false);
    },
    getExportParams(type) {
      let params = this.$refs.table.getParams();
      params.excel_type = 'data';
      params.slow_export = true;
      params.name = params.searchValue;
      if (type) params.export_type = type;
      const { remind_type } = params;
      if (remind_type) {
        params.remind_type = remind_type[0];
        params.day_period_type = remind_type[1];
      }
      // return this.dealParams(params);
      return params;
    },
    binding() {
      let param = {
        userid: this.userid,
        type: 2,
      };
      this.$request.post(api.unbundUser, param).then((res) => {
        let { status, message } = res;
        if (status == 1) {
          this.$Notice.success({
            title: '操作成功！',
          });
          this.bindingShow = false;
          this.getUserList();
        } else {
          this.$Notice.error({
            title: message,
          });
          this.bindingShow = false;
          this.getUserList();
        }
      });
    },
    cancelSetUp(res) {
      this.cancelSetUpshow = res;
    },
    getRemoveTag() {
      const currentUserTagIds = this.getCurrentUserTagIds();
      const removeTags = [];
      this.tagList.forEach((group) => {
        group.tag.forEach((tag) => {
          if (!tag.checked && currentUserTagIds.includes(tag.id)) {
            removeTags.push(tag.id);
          }
        });
      });
      return removeTags;
    },
    SetUp() {
      let param = {
        add_tag: this.getAddTag(),
        remove_tag: this.getRemoveTag,
        external_userid: this.currentSetTagUser.qw_external_userid,
      };
      this.$request.post(api.MarkTag, param).then((res) => {
        let { status, message } = res;
        if (status == 1) {
          this.$Notice.success({
            title: '操作成功！',
          });
          this.labelSet = false;
          this.getUserList();
          this.setUpLabelData = [];
        } else {
          this.labelSet = false;
          this.$Notice.error({
            title: message,
          });
        }
      });
    },
    getAddTag() {
      const tags = [];
      this.tagList.forEach((group) => {
        group.tag.forEach((tag) => {
          if (tag.checked) {
            tags.push(tag);
          }
        });
      });
      return tags;
    },
    async delUser(id) {
      let res = await common.delUser(id);
      if (res.status) {
        this.successMessage('删除成功');
        this.getUserList();
      } else {
        this.errorMessage(res.message);
      }
    },
    toUserDetail(id, receStyleId) {
      this.router.push({
        path: '/user/base-info',
        query: { uid: id, keep_scroll: 1, receStyleId },
      });
    },
    viewUserDetail(id, siteId, WxStatus) {
      this.router.push({
        path: '/user/base-info',
        query: {
          uid: id,
          is_view: 1,
          site_id: siteId,
          WxStatus: WxStatus,
          keep_scroll: 1,
        },
      });
    },
    Customer() {
      let labelColumn = [
        {
          title: '标签组名称',
          key: 'group_name',
          width: 300,
        },
        {
          title: '标签',
          key: 'tag',
          render: (h, params) => {
            let data = params.row;
            let list = data.tag.map((item, tagIndex) => {
              return h(
                'Button',
                {
                  props: {
                    type: item.checked == true ? 'primary' : '',
                    ghost: item.checked == true ? true : false,
                  },
                  style: {
                    position: 'relative',
                    margin: '5px 5px',
                  },
                  on: {
                    click: () => {
                      item.checked = !item.checked;
                      params.row.tag[tagIndex].checked = item.checked;
                      this.tagList[params.index].tag[tagIndex].checked =
                        item.checked;
                    },
                  },
                },
                [
                  h('span', item.name),
                  item.checked
                    ? h('i', {
                        class: ['iconfont', 'sdpicon-xuanzhongjiaobiao'],
                        style: {
                          position: 'absolute',
                          bottom: '-14px',
                          right: '-13px',
                          'font-size': '26px',
                          color: 'rgb(33, 236, 43)',
                        },
                      })
                    : '',
                ],
              );
            });
            return list;
          },
        },
      ];
      this.labelColumns = labelColumn;
      return this.labelColumns;
    },
    CustomerLabel() {
      this.$request.get(api.getCorpTagList).then((res) => {
        let { data } = res;
        // 获取标签信息  并且加入Check
        data.list.forEach((item) => {
          item.tag.forEach((tag) => {
            tag.checked = false;
          });
        });
        this.tagList = data.list;
      });
    },
    labelSetUp(params) {
      this.currentSetTagUser = params.row;
      this.labelSet = true;
      let tagList = this.tagList;
      tagList = tagList.map((group) => {
        group.tag = group.tag.map((tag) => {
          tag.checked = false;
          if (params.row.qw_tags.find((userTag) => userTag.tag_id === tag.id)) {
            tag.checked = true;
          }
          return tag;
        });
        return group;
      });
      this.tagList = tagList;
    },
    getCurrentUserTagIds() {
      if (!this.currentSetTagUser.qw_tags) {
        return [];
      }
      return this.currentSetTagUser.qw_tags.map((tag) => tag.tag_id);
    },
    ShowCustomerLabel() {
      if (this.qy_weixin_app_status == 0) {
        this.originCols.forEach((item, index) => {
          if (item.key === 'qw_tags') {
            this.originCols.splice(index, 1);
          }
        });
      }
    },
    toSetNewUser() {
      this.router.push({ path: '/user/base-info', query: { keep_scroll: 1 } });
    },
    afterRequest(params, res = {}) {
      if (res.status === 1) {
        this.qy_weixin_app_status = Number(res.data.qy_weixin_app_status);
        // localStorage.setItem('qy_weixin_app_status',this.qy_weixin_app_status);
      }
      this.ShowCustomerLabel();
      return params.map((item) => {
        item.qw_tags.map((val) => {
          val.Check = false;
        });
        return item;
      });
    },
    beforeRequest(params) {
      const accountStatus =
        StorageUtil.getLocalStorage('user-list-check-state') || '';
      if (accountStatus) params.status = accountStatus;
      const { remind_type } = params;
      if (remind_type) {
        params.remind_type = remind_type[0];
        params.day_period_type = remind_type[1];
      }
      if (this.sysConfig.tc_platform == 1) {
        params.exclude_business_type = 2;
      }
      return params;
    },
    downloadTemplate(url, option) {
      if (
        option.key === 'designated_supplier' ||
        option.key === 'purchase_task_designated_supplier' ||
        option.key === 'protocol_enabled' ||
        option.key === 'cycle_enabled'
      ) {
        this.$Message.loading({
          title: '提示',
          content: '模板下载中，请稍后。',
          loading: true,
          closable: false,
          duration: 0,
        });
        const listParams = this.$refs.table.getParams();
        const remindType = listParams.remind_type;
        if (remindType) {
          listParams.remind_type = remindType[0];
          listParams.day_period_type = remindType[1];
        }
        this.$request
          .post(option.downloadUrl, {
            user_type: 1,
            template_type: this.template_type,
            type: this.type,
            ...listParams,
          })
          .then((res) => {
            let { status, message, data } = res;
            if (status) {
              if (data.task_no) {
                this.$store.commit('showTaskCenter', true);
                exportLoop(data.task_no);
                this.$refs.importbuttom.cancel();
                return;
              }
              window.open(data.replace('http:', 'https:'), '_blank');
            } else {
              this.modalError(message);
            }
          })
          .finally(() => {
            this.$Message.destroy();
          });
      }
    },
    importBtnCallBack(importSuccess) {
      if (importSuccess) {
        this.successNotice('导入成功');
        this.$refs.table.fetchData();
      }
    },
    importSuccess(data) {
      this.router.push({
        path: 'add',
        query: { id: data },
      });
    },
    handleDropdownClick(index) {
      const curData = this.importData[index];
      this.template_type = 1;
      this.type = 0;
      if (curData.key == 'protocol_enabled') {
        this.template_type = 0;
      }
      this.importData[index].post.extraParams = {
        user_type: 1,
        template_type: this.template_type,
        type: this.type,
        ...this.$refs.table.getParams(),
      };
      this.importKey = curData.key;
    },
    handleTypeChange(val) {
      const index = this.importData.findIndex(
        (item) => item.key === this.importKey,
      );
      this.importData[index].post.extraParams.type = val;
    },
    handleTemplateTypeChange(val) {
      const index = this.importData.findIndex(
        (item) => item.key === this.importKey,
      );
      this.importData[index].post.extraParams.template_type = val;
    },
    handleTypeChange(val) {
      const index = this.importData.findIndex(
        (item) => item.key === this.importKey,
      );
      this.importData[index].post.extraParams.type = val;
    },
  },
};
</script>

<style lang="less">
.ml10 {
  margin-left: 10px;
}
</style>
<style>
.user-type-import .import-box__dialog__body__import__description:first-child,
.user-type-import .import-box__dialog__body__download__description {
  margin-top: 10px !important;
}
.user-type-import .import-box__dialog__body__error__description {
  margin-top: -12px !important;
  margin-bottom: 0px !important;
}
.user-type-import
  .import-box__dialog__body__error__description
  span:nth-child(2) {
  font-weight: normal;
  margin-left: 0px;
}
</style>
