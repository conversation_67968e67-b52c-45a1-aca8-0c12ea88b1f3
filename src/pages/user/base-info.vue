<template>
  <div class="s-common">
    <div class="detail__wrap" :style="{ height: height + 46 + 'px' }">
      <div class="detail__hd">
        <base-detail-head backTo="/user/list" :title="title" backTxt="返回" />
      </div>
      <div class="detail__bd" ref="bd" @scroll="handleScroll">
        <EditPageMenu
          v-if="(uid && !isView) || isSyncPlatform"
          :goodDiscountDis="goodDiscountDis"
        ></EditPageMenu>
        <Form
          ref="userInfo"
          class="detail__form"
          :model="userInfo"
          :rules="isView ? null : ruleValidate"
          :label-width="200"
          :disabled="!!isView"
          @submit.native.prevent
        >
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="基础信息0">基础信息</h5>
            </div>

            <FormItem class="account" label="客户账号" prop="account">
              <Input
                :maxlength="sysConfig.customer_account_format == 0 ? 11 : 16"
                v-model="userInfo.account"
                placeholder="请填写手机号"
                :disabled="accountDisable || isSyncPlatform"
                :show-word-limit="(!uid && !isView) || isSyncPlatform"
                @on-change="accountChange"
              />
              <SIcon
                @click.native="accountDisable = false"
                :size="18"
                v-if="uid && !isView"
                class="account__edit"
                :class="{ done: !accountDisable }"
                icon="bianji"
              />
            </FormItem>
            <FormItem label="登录密码" prop="password">
              <Input
                v-model="userInfo.password"
                :disabled="isSyncPlatform"
                :placeholder="isView ? '' : '客户登录商城所使用的密码'"
              />
            </FormItem>
            <FormItem label="客户名称" prop="name">
              <Input
                maxlength="40"
                :disabled="isSyncPlatform"
                v-model="userInfo.name"
                placeholder="请填写客户名称 / 客户店铺名称"
              />
            </FormItem>
            <FormItem
              label="客户类型"
              prop="type"
              tip="为客户选中客户类型后，将使用对应客户类型价格及运营时段；客户类型可在客户/客户类型页面配置"
            >
              <Select
                filterable
                placeholder="请选择客户类型"
                v-model="userInfo.type"
              >
                <Option
                  :value="info.id"
                  v-for="info in userTypeList"
                  :key="info.id"
                  >{{ info.name }}</Option
                >
              </Select>
            </FormItem>
            <FormItem label="状态" prop="status">
              <RadioGroup v-model="userInfo.status">
                <Radio label="2" :disabled="isSyncPlatform">正常</Radio>
                <Radio
                  label="1"
                  :disabled="
                    (isMultiSite && userInfo.status !== '1') || isSyncPlatform
                  "
                  >待审核</Radio
                >
                <Radio label="3" :disabled="isSyncPlatform">禁用</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              label="客户标签"
              prop="user_tag"
              v-if="userTagList.length"
            >
              <CheckboxGroupCustom
                :disabled="isSyncPlatform"
                v-model="userInfo.user_tag"
                :data="userTagList"
                @on-change="handleSelectTag"
              />
            </FormItem>
            <FormItem label="重点关注" prop="focus_on_sorting ">
              <CheckboxGroup v-model="userInfo.focus_on_sorting">
                <Checkbox label="sorting">
                  分拣
                  <Tooltip
                    :transfer="true"
                    :delay="0"
                    :maxWidth="246"
                    content="分拣端增加标识，便于客户分拣时重点关注"
                    placement="top"
                  >
                    <SIcon icon="help1" :size="12" />
                  </Tooltip>
                </Checkbox>
              </CheckboxGroup>
            </FormItem>
            <FormItem
              label="业务员"
              tip="为客户选中业务员后，可从业务员纬度查看该客户销售收入"
            >
              <Select
                filterable
                :disabled="isSyncPlatform"
                placeholder="请选择业务员"
                v-model="userInfo.sales"
              >
                <Option
                  :value="info.id"
                  v-for="info in salesList"
                  :key="info.id"
                  >{{ info.name }}</Option
                >
              </Select>
            </FormItem>
            <FormItem label="客户编码" tip="客户名称的简称，用于快速识别客户">
              <Input
                v-model="userInfo.code"
                maxlength="32"
                placeholder="请输入不超过32个字符"
                :show-word-limit="!isView"
              />
            </FormItem>
            <FormItem label="所属集团">
              <!-- :disabled="isSyncPlatform" -->
              <Select
                filterable
                placeholder="请选择所属集团"
                v-model="userInfo.group"
              >
                <Option
                  :value="info.id"
                  v-for="info in userGroupList"
                  :key="info.id"
                  >{{ info.group_name }}</Option
                >
              </Select>
            </FormItem>
            <FormItem label="营业执照/门头照">
              <base-upload
                v-model="userInfo.picture"
                :max="3"
                :disabled="!!isView || isSyncPlatform"
              />
            </FormItem>
            <FormItem label="统一社会信用代码" prop="license_no">
              <Input
                v-model="userInfo.license_no"
                maxlength="50"
                :disabled="isSyncPlatform"
                placeholder="请输入统一社会信用代码"
              />
            </FormItem>
            <FormItem label="企业认证" prop="auth_status">
              <RadioGroup v-model="userInfo.auth_status">
                <Radio label="0">未认证</Radio>
                <Radio label="1">已认证</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem
              v-if="isEnableUserTrace"
              label="溯源二维码"
              v-show="trace_qrcode"
            >
              <a
                :href="trace_qrcode"
                download="qrcode.png"
                class="qrcode-download"
              >
                <img :src="trace_qrcode" />
                <span>下载到电脑</span>
              </a>
            </FormItem>
            <FormItem
              label="客户性质"
              prop="buyer_type"
              v-if="traceability_platform_e_code"
            >
              <Select
                :clearable="false"
                :disabled="isSyncPlatform"
                placeholder="请选择客户性质"
                v-model="userInfo.buyer_type"
              >
                <Option
                  :value="item.id"
                  v-for="(item, index) in buyerType"
                  :key="index"
                  >{{ item.name }}</Option
                >
              </Select>
            </FormItem>
          </div>
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="收货信息0">收货信息</h5>
            </div>
            <div class="blk-bd">
              <FormItem label="区域" prop="area">
                <Select filterable placeholder="请选择" v-model="userInfo.area">
                  <Option
                    :value="info.id"
                    v-for="info in areaList"
                    :key="info.id"
                    >{{ info.name }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem
                label="默认线路"
                prop="line_id"
                v-if="!is_line_decision_table_mode"
              >
                <line-select
                  v-model="userInfo.line_id"
                  :show-all="false"
                  :defaultValue="defaults"
                  placeholder="请选择"
                  @on-change="changeLine"
                ></line-select>
              </FormItem>
              <FormItem label="联系人" prop="contact">
                <Input
                  v-model="userInfo.contact"
                  maxlength="20"
                  :disabled="isSyncPlatform"
                  placeholder="请填写联系人姓名"
                  :show-word-limit="!isView"
                />
              </FormItem>
              <FormItem label="收货手机" prop="tel">
                <Input
                  v-model="userInfo.tel"
                  maxlength="11"
                  :disabled="isSyncPlatform"
                  placeholder="请填写手机号码"
                  :show-word-limit="!isView"
                />
              </FormItem>
              <FormItem label="收货地址" prop="address">
                <Input
                  v-model="userInfo.address"
                  type="textarea"
                  maxlength="128"
                  :disabled="isSyncPlatform || userRegisterUseGdLocation"
                  placeholder="请输入收货地址…"
                  :rows="4"
                  :show-word-limit="!isView"
                />
              </FormItem>
              <FormItem label="详细地址" prop="address_attach">
                <Input
                  v-model="userInfo.address_attach"
                  type="textarea"
                  maxlength="128"
                  :disabled="isSyncPlatform"
                  placeholder="请输入详细地址，例如1栋101"
                  :rows="4"
                  :show-word-limit="!isView"
                />
              </FormItem>
              <FormItem
                label="定位"
                prop="longitudeAndLatitude"
                :rules="[
                  {
                    required: userRegisterUseGdLocation,
                    message: '必须定位地址',
                    trigger: 'change',
                  },
                ]"
              >
                <Input
                  v-model="userInfo.longitudeAndLatitude"
                  maxlength="128"
                  :disabled="isSyncPlatform"
                  @on-focus="openPosition"
                  placeholder="请选择"
                />
              </FormItem>
              <FormItem
                label="送货时间"
                tip="在该时段为客户送货；送货时间可在配置/商城运营/送货时间页面配置"
              >
                <Select
                  filterable
                  placeholder="请选择"
                  :disabled="isSyncPlatform"
                  v-model="userInfo.deliveryDate"
                >
                  <Option
                    :value="info.id"
                    v-for="info in deliveryDateList"
                    :key="info.id"
                    >{{ info.start_time }} ~ {{ info.end_time }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem label="收货周期">
                <InputNumber
                  style="width: 292px"
                  :disabled="isSyncPlatform"
                  v-model="userInfo.receipt_cycle"
                  :max="999"
                  :min="0"
                  ddd
                  :precision="0"
                />
              </FormItem>
              <template v-if="sysConfig.is_open_self_pickup_point == 1">
                <FormItem label="送货方式">
                  <RadioGroup
                    v-model="userInfo.is_self_pickup_point"
                    style="width: 400px"
                  >
                    <Radio :disabled="isSyncPlatform" label="0">送货上门</Radio>
                    <Radio :disabled="isSyncPlatform" label="2">门店自提</Radio>
                    <Radio :disabled="isSyncPlatform" label="1"
                      >送货上门 + 门店自提</Radio
                    >
                  </RadioGroup>
                </FormItem>
                <FormItem
                  v-if="userInfo.is_self_pickup_point != 0"
                  label="默认自提点"
                >
                  <Select
                    filterable
                    placeholder="请选择"
                    :disabled="isSyncPlatform"
                    v-model="userInfo.self_pickup_point_id"
                  >
                    <Option
                      :value="info.id"
                      v-for="info in pickUpList"
                      :key="info.id"
                      >{{ info.name }}</Option
                    >
                  </Select>
                </FormItem>
              </template>
            </div>
          </div>
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="订单设置0">订单设置</h5>
            </div>
            <div class="blk-bd">
              <FormItem
                label="货到付款"
                prop="is_cod"
                tip="如不支持货到付款，请确保系统配置中开启了在线支付；"
              >
                <RadioGroup v-model="userInfo.is_cod">
                  <Radio :disabled="isSyncPlatform" label="1">支持</Radio>
                  <Radio :disabled="isSyncPlatform" label="2">不支持</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem
                label="在线支付"
                prop="is_support_online_pay"
                tip="如果配置支持需要【系统配置 - 支付配置】中开启【支持在线支付】"
              >
                <RadioGroup v-model="userInfo.is_support_online_pay">
                  <Radio :disabled="isSyncPlatform" label="1">支持</Radio>
                  <Radio :disabled="isSyncPlatform" label="0">不支持</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem
                label="余额支付"
                prop="is_support_balance_pay"
                tip="不支持时，商城端不展示客户余额，也不能使用余额支付；"
              >
                <RadioGroup v-model="userInfo.is_support_balance_pay">
                  <Radio :disabled="isSyncPlatform" label="1">支持</Radio>
                  <Radio :disabled="isSyncPlatform" label="2">不支持</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="支持对公支付" prop="is_public_account_pay">
                <RadioGroup v-model="userInfo.is_public_account_pay">
                  <Radio :disabled="isSyncPlatform" label="1">支持</Radio>
                  <Radio :disabled="isSyncPlatform" label="0">不支持</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem
                label="货币类型"
                prop="currency_type"
                v-if="isOpenExchangeRate"
              >
                <Select
                  :clearable="false"
                  :disabled="isSyncPlatform"
                  placeholder="请选择货币类型"
                  v-model="userInfo.currency_type"
                >
                  <Option value="1">人民币</Option>
                  <Option value="2">港币</Option>
                </Select>
              </FormItem>
              <FormItem
                label="客户起订价"
                :slotContent="true"
                tip="客户起订价为空或者0时，代表不限制。<br/>客户起订价优先于参数设置中起订价。<br/>客户起订价模式与参数设置中保持一致。"
              >
                <InputNumber
                  style="width: 292px"
                  :disabled="isSyncPlatform"
                  v-model="userInfo.lowest_order_price"
                  size="default"
                  :precision="2"
                  :min="0"
                  :max="99999999.99"
                  placeholder="请输入客户起订价"
                />
              </FormItem>
              <FormItem
                label="客户折扣率"
                tip="客户折扣率主要应用于计算商品折前价, 填写后当前客户下单的商品将会按照该折扣率通过销售价格计算折前价;该折前价优先级高于客户类型价设置的折前价"
              >
                <InputNumber
                  style="width: 292px"
                  :disabled="isSyncPlatform"
                  v-model="userInfo.user_discount"
                  :min="0.0"
                  :precision="2"
                  placeholder="请输入≥0数值"
                />
              </FormItem>
              <FormItem label="订单审核">
                <RadioGroup
                  v-model="userInfo.order_audit_mode"
                  style="width: 400px"
                  @on-change="handleOrderAuditModeChange"
                >
                  <Radio label="0">跟随系统配置</Radio>
                  <Radio label="1">开启</Radio>
                  <Radio label="2">关闭</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem
                v-if="isEnablePurchasePlan && isSplitOrderByProvider && !isOpenOfferBindProvider && userInfo.order_audit_mode == 1"
                label="审核后按询价结果拆单"
                tip="开启后,订单审核后才拆单,按供应商询价结果拆单,且所有订单商品都询价后才能审核通过"
              >
                <div style="line-height: 30px">
                  <Switch
                    :disabled="isSyncPlatform"
                    v-model="userInfo.order_audit_inquiry_split"
                  />
                </div>
              </FormItem>
            </div>
          </div>
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="打印模版0">打印模版</h5>
            </div>
            <div class="blk-bd">
              <FormItem
                v-has-module="'user_kg_print'"
                label="公斤打印"
                prop="status"
                tip="开启后，同时开启发货单模板中公斤打印配置，送给客户的发货单，商品数量将自动除以2，转换成公斤"
              >
                <div style="line-height: 30px">
                  <Switch
                    :disabled="isSyncPlatform"
                    v-model="userInfo.kgConversion"
                  />
                </div>
              </FormItem>
              <FormItem
                v-if="deliveryOrderPrintTotalPriceMode"
                label="发货单打印发货金额向下取整"
                prop="is_floor_total_price"
                tip="向下取整开启后发货单打印时发货金额、发货合计金额做抹零处理。当发货单设置显示为2位小数时，抹零不生效；当设置为1位小数时，对打印的发货单发货金额、发货合计金额第二位小数进行抹零；当设置为整数时，对打印的发货单发货金额、发货合计金额第一位小数进行抹零"
              >
                <Switch
                  :disabled="isSyncPlatform"
                  v-model="userInfo.is_floor_total_price"
                />
              </FormItem>
              <FormItem label="发货单打印模板">
                <Select
                  filterable
                  placeholder="请选择"
                  v-model="userInfo.printTemplate"
                >
                  <Option
                    :value="info.id"
                    v-for="info in printTemplateList"
                    :key="info.id"
                    ><SIcon
                      class="icon--new"
                      icon="xin"
                      :size="16"
                      v-if="isNewTemplate(info)"
                    />{{ info.name }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem label="发货单打印商品排序方式">
                <RadioGroup
                  style="width: 600px"
                  v-model="userInfo.is_invoice_print_sort"
                >
                  <Radio label="">跟随系统配置</Radio>
                  <Radio label="0">按下单顺序排序</Radio>
                  <Radio label="1">按商品分类创建时间排序</Radio>
                  <Radio label="3">按商品分类名称排序</Radio>
                  <Radio label="2">按商品编码排序</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem label="订单退货单打印模板">
                <CheckPrintTemplate
                  placeholder="请选择"
                  :params="{ type: 'ORDER_RETURN' }"
                  v-model="userInfo.return_order_tpl_id"
                  filterable
                  clearable
                >
                  <template slot-scope="{ item }">
                    <SIcon
                      class="icon--new"
                      :size="16"
                      icon="xin"
                      v-if="isNewTemplate(item)"
                    />{{ item.name }}
                  </template>
                </CheckPrintTemplate>
              </FormItem>
              <FormItem
                v-has-module="'inspection_reports'"
                label="检测单打印模板"
              >
                <CheckPrintTemplate
                  placeholder="请选择"
                  :params="{ type: 'ORDER_CHECK' }"
                  v-model="userInfo.inspection_tpl_id"
                  filterable
                  clearable
                >
                  <template slot-scope="{ item }">
                    <SIcon
                      class="icon--new"
                      :size="16"
                      icon="xin"
                      v-if="isNewTemplate(item)"
                    />{{ item.name }}
                  </template>
                </CheckPrintTemplate>
              </FormItem>
              <FormItem label="标签打印模板">
                <CheckPrintTemplate
                  placeholder="请选择"
                  :params="{ type: 'PICK' }"
                  v-model="userInfo.sort_order_tpl_id"
                  filterable
                  clearable
                >
                  <template slot-scope="{ item }">
                    <SIcon
                      class="icon--new"
                      :size="16"
                      icon="xin"
                      v-if="isNewTemplate(item)"
                    />{{ item.name }}
                  </template>
                </CheckPrintTemplate>
              </FormItem>
              <FormItem label="对账单打印模板">
                <CheckPrintTemplate
                  placeholder="请选择"
                  :params="{ type: 'SOA' }"
                  :onlyNew="true"
                  v-model="userInfo.bill_order_tpl_id"
                  filterable
                  clearable
                >
                  <template slot-scope="{ item }">
                    <SIcon
                      class="icon--new"
                      :size="16"
                      icon="xin"
                      v-if="isNewTemplate(item)"
                    />{{ item.name }}
                  </template>
                </CheckPrintTemplate>
              </FormItem>
              <FormItem v-if="enableFloatRice" label="下浮率">
                <number-input
                  :disabled="isSyncPlatform"
                  v-model="userInfo.price_float_rate"
                  placeholder="请输入下浮率"
                  :int-only="true"
                  filterable
                ></number-input>
              </FormItem>
            </div>
          </div>
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="财务设置0">财务设置</h5>
            </div>
            <div class="blk-bd">
              <template v-if="sysConfig.order_send_auto_bill_mode === '1'">
                <FormItem
                  class="account"
                  label="对账数量默认取下单数量"
                  prop="bill_amount_default_order_amount "
                  tip="开启后：生成对账单时，付款方式为货到付款类型的订单，对账数量默认对账为下单数量，操作日志操作员为：发货的人员"
                >
                  <Switch
                    :disabled="isSyncPlatform"
                    v-model="userInfo.bill_amount_default_order_amount"
                  />
                </FormItem>
              </template>
              <template v-if="sysConfig.order_send_auto_bill_mode === '2'">
                <FormItem
                  class="account"
                  label="对账小计小数点三位抹零"
                  prop="bill_amount_default_order_amount "
                  tip="开启配置后，付款方式为货到付款类型的订单，发货出库自动生成对账单且对账小计默认为小数点后三位抹零，对账单价保持不变"
                >
                  <Switch
                    :disabled="isSyncPlatform"
                    v-model="userInfo.bill_amount_default_order_amount"
                  />
                </FormItem>
              </template>
            </div>
          </div>
          <div class="blk">
            <div class="blk-hd scroll-top">
              <h5 id="其他信息0">其他信息</h5>
              <FormItem
                label="商城商品展示为时价"
                prop="mall_commodity_show_time_price"
              >
                <Switch v-model="userInfo.mall_commodity_show_time_price" />
              </FormItem>
              <FormItem
                label="特殊备注"
                tip="填写特殊备注后该内容会在订单新增/编辑/核算/详情界面展示，未填写则不展示，文本框支持换行"
              >
                <Input
                  :disabled="isSyncPlatform"
                  v-model="userInfo.special_remark"
                  type="textarea"
                  placeholder="请输入特殊备注信息"
                  :maxlength="512"
                  show-word-limit
                  :autosize="{
                    minRows: 2,
                    maxRows: 8,
                  }"
                />
              </FormItem>
              <FormItem
                class="account"
                label="辅助核算编码"
                prop="aux_acc_code"
              >
                <Input
                  :maxlength="64"
                  show-word-limit
                  v-model="userInfo.aux_acc_code"
                  placeholder="请填写辅助核算编码"
                />
              </FormItem>
              <FormItem
                class="account"
                label="辅助核算名称"
                prop="aux_acc_name"
              >
                <Input
                  :maxlength="64"
                  show-word-limit
                  v-model="userInfo.aux_acc_name"
                  placeholder="请填写辅助核算名称"
                />
              </FormItem>
              <FormItem
                class="account"
                :label="item.name"
                prop="aux_acc_name"
                v-for="(item, index) in customizeFieldList"
                :key="item.id"
              >
                <Input
                  show-word-limit
                  :maxlength="64"
                  v-model="customizeFieldList[index].value"
                  :placeholder="'请填写' + item.name"
                  v-if="item.type == 1"
                />
                <Select
                  :filterable="false"
                  placeholder="请选择"
                  v-model="customizeFieldList[index].value"
                  v-else
                >
                  <Option
                    v-for="info in item.select_config"
                    :value="info.id"
                    :key="info.id">
                    {{ info.name }}
                  </Option>
                </Select>
              </FormItem>
            </div>
          </div>
          <div class="blk" v-if="is_open_user_store">
            <div class="blk-hd scroll-top">
              <h5 id="客户进销存0">客户进销存</h5>
              <FormItem
                label="盘点类型"
                tip="盘点配置生效范围【商城-库存管理-盘点】和【智能收货秤-库存管理-盘点单】"
                prop="take_stock_type"
              >
                <RadioGroup v-model="userInfo.take_stock_type">
                  <Radio :disabled="isSyncPlatform" label="0">明盘</Radio>
                  <Radio :disabled="isSyncPlatform" label="1">盲盘</Radio>
                </RadioGroup>
              </FormItem>
              <FormItem
                class="account"
                label="出入库审核"
                prop="in_out_audit"
                tip="关闭后，商城/智能收货称相关进出库单据仅支持保存，不支持保存并审核"
              >
                <Switch
                  :disabled="isSyncPlatform"
                  v-model="userInfo.in_out_audit"
                />
              </FormItem>
            </div>
          </div>
          <div class="blk" v-if="isOpenNewRecipe">
            <div class="blk-hd scroll-top">
              <h5 id="就餐信息0">就餐信息</h5>
              <FormItem label="关联平台菜谱" prop="recipe_id">
                <Select v-model="userInfo.recipe_id" :disabled="isSyncPlatform">
                  <Option
                    v-for="item in recipeList"
                    :key="item.id"
                    :value="item.id"
                    >{{ item.name }}</Option
                  >
                </Select>
              </FormItem>
              <div class="meal-number-box">
                <Table
                  stripe
                  :columns="mealNumberColumns"
                  :data="userInfo.number_of_diners"
                ></Table>
                <p class="mt16" style="color: rgba(0, 0, 0, 0.6)">
                  单餐次的默认就餐人数，按菜谱下单时可自动基于此计算数量
                </p>
              </div>
            </div>
          </div>
        </Form>
      </div>
      <div class="aside-box">
        <div
          style="transition: all 0.5s"
          v-for="(group, index) in sections"
          :class="[
            group.isMain ? 'aside-2-box' : 'aside-1-box',
            activeStep == index ? 'active' : '',
          ]"
          v-show="group.show !== false"
          :key="group.title"
        >
          <span
            class="txt"
            @click="jumpTo(group.title + Number(group.isMain), index)"
            v-if="group.title"
          >
            {{ group.title }}
          </span>
        </div>
      </div>
      <div class="detail__ft fixed">
        <SButton @click="quit">{{ isView ? '返回' : '取消' }}</SButton
        ><SButton type="primary" @click="save" v-if="!isView">{{ saving ? '保存中...' : '保存' }}</SButton>
      </div>
    </div>
    <SModal width="1000" title="打点" type="default" ref="position" :btns="0">
      <set-position
        :ban-dot="userRegisterUseGdLocation"
        :user="userInfo"
        style="padding: 14px 0"
        :style="{ height: height - 100 + 'px' }"
        @on-success="updateLongitudeAndLatitude"
      ></set-position>
    </SModal>
  </div>
</template>

<script>
import EditPageMenu from './components/EditPageMenu.vue';
import { SModal, SButton } from '@sdp/ui';
import BaseDetailHead from '@components/base-detail-head';
import BaseUpload from '@components/base-upload';
import Select from '@components/base-select';
import FormItem from '@components/base-form-item';
import lineSelect from '@components/delivery/lineSelect';
import setPosition from '@components/user/setPosition_new';
import NumberInput from '@components/basic/NumberInput';
import CheckPrintTemplate from '@/components/print/template/index.vue';
import CheckboxGroupCustom from '@components/CheckboxGroup';
import SIcon from '@components/icon';
import QRCode from 'qrcode';
import common from '@api/user.js';
import settings from '@api/settings';
import { debounce, throttle } from 'lodash-es';
import { getMinHeight } from '../../util';
import ConfigMixin from '@/mixins/config';
import deliveryMixins from '@/mixins/delivery/config.js';
import httpNewRecipe from '@/api/newRecipe.js';

const areaValidator = (rule, value, callback) => {
  if (!Number(value)) {
    callback(new Error('请选择区域'));
  }
  callback();
};
const lineValidator = (rule, value, callback) => {
  if (!Number(value)) {
    callback(new Error('请选择线路'));
  } else {
    callback();
  }
};
const isCodeValidator = (rule, value, callback) => {
  if (!Number(value)) {
    callback(new Error('请选择货到付款'));
  } else {
    callback();
  }
};

const checkNumber = (rule, value, callback) => {
  if (!/^[0-9]+$/.test(value)) {
    callback(new Error('请输入数字'));
  } else {
    callback();
  }
};

export default {
  name: 'user-base-info',
  mixins: [ConfigMixin, deliveryMixins],
  components: {
    EditPageMenu,
    BaseDetailHead,
    BaseUpload,
    FormItem,
    lineSelect,
    setPosition,
    SModal,
    SButton,
    SIcon,
    CheckPrintTemplate,
    NumberInput,
    Select,
    CheckboxGroupCustom,
  },
  data() {
    return {
      saving: false,
      isSyncPlatform: false,
      defaults: true,
      ready: false,
      original_line_id: '',
      enableFloatRice: false,
      accountDisable: false,
      today: '',
      config: {},
      userInfo: {
        is_invoice_print_sort: '',
        longitudeAndLatitude: '',
        aux_acc_code: '',
        aux_acc_name: '',
        is_cod: '1',
        take_stock_type: '0',
        auth_status: '0',
        license_no: '',
        buyer_type: '',
        is_self_pickup_point: '0',
        self_pickup_point_id: '',
        is_support_online_pay: '1',
        is_support_balance_pay: '1',
        is_public_account_pay: '1',
        status: '2',
        user_discount: '',
        address_attach: '',
        picture: [],
        account: '',
        password: '',
        name: '',
        code: '',
        type: '',
        group: '0',
        sales: '0',
        area: '',
        line_id: '',
        contact: '',
        tel: '',
        address: '',
        deliveryDate: '',
        printTemplate: '',
        price_float_rate: '',
        inspection_tpl_id: '',
        return_order_tpl_id: '',
        bill_order_tpl_id: '',
        sort_order_tpl_id: '',
        kgConversion: false,
        order_audit_inquiry_split: false,
        in_out_audit: true,
        bill_amount_default_order_amount: false,
        is_floor_total_price: false,
        mall_commodity_show_time_price: false,
        lowest_order_price: '',
        currency_type: 1,
        receipt_cycle: 0,
        special_remark: '',
        recipe_id: '',
        number_of_diners: [],
        user_tag: [],
        focus_on_sorting: [],
        order_audit_mode: '0',
      },
      height: 'auto',
      salesList: [],
      areaList: [],
      buyerType: [],
      userTypeList: [],
      userGroupList: [],
      userTagList: [],
      deliveryDateList: [],
      pickUpList: [],
      printTemplateList: [],
      uid: null,
      isView: false,
      ruleValidate: {
        account: [
          {
            required: true,
            message: '',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('客户账号为必填项'));
              } else {
                checkNumber(rule, value, callback);
              }
            },
          },
          {
            trigger: 'change',
            validator: checkNumber,
          },
        ],
        password: [
          {
            required: true,
            message: '登录密码为必填项',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!this.userInfo.id && !value) {
                callback(new Error('登录密码为必填项'));
              } else {
                callback();
              }
            },
          },
        ],
        name: [
          { required: true, message: '客户名称为必填项', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '客户类型为必填项', trigger: 'blur' },
        ],
        contact: [
          { required: true, message: '联系人为必填项', trigger: 'blur' },
        ],
        tel: [{ required: true, message: '收货手机为必填项', trigger: 'blur' }],
        address: [
          { required: true, message: '收货地址为必填项', trigger: 'change' },
        ],
        area: [
          {
            required: true,
            message: '区域为必填项',
            trigger: 'change',
            validator: areaValidator,
          },
        ],
        line_id: [
          {
            required: true,
            message: '线路为必填项',
            trigger: 'change',
            validator: lineValidator,
          },
        ],
        is_cod: [
          {
            required: true,
            message: '货到付款为必填项',
            trigger: 'change',
            validator: isCodeValidator,
          },
        ],
      },
      modified: false,
      trace_qrcode: '',
      longitudeAndLatitude: '',
      mealNumberColumns: [
        {
          title: '餐次',
          key: 'meal_type_name',
        },
        {
          title: '就餐人数(个)',
          key: 'number',
          render: (h, params) => {
            const { row, index } = params;
            return h(NumberInput, {
              props: {
                value: row.number,
                min: 0,
                precision: 0,
                disabled: !!this.isView,
              },
              on: {
                'on-change': (value) => {
                  row.number = value;
                  this.userInfo.number_of_diners[index].number = value;
                },
                'on-blur': () => {},
                'on-enter': () => {},
              },
            });
          },
        },
      ],
      recipeList: [],
      sections: [],
      customizeFieldList: [],
      activeStep: 0,
    };
  },
  beforeRouteLeave(to, from, next) {
    if (this.modified) {
      this.$Modal.confirm({
        title: '这是进行一项操作时必须了解的重要信息',
        content: '<p>您刚刚修改的内容尚未保存，是否放弃修改？</p>',
        onOk: function () {
          next();
        },
        onCancel: function () {
          next(false);
        },
        okText: '放弃修改',
      });
    } else {
      next();
    }
  },
  created() {
    this.uid = this.$route.query.uid;
    this.isView = this.uid && this.$route.query.is_view;
    this.height = getMinHeight();
    this.accountDisable = !!this.uid;
    this.getUserTagList();
    this.getUserAjaxList();
    this.getUserGroupList();
    this.getSalesList();
    this.getAreaList();
    if (this.traceability_platform_e_code) {
      this.getBuyerType();
    }
    this.getDeliveryDateList();
    this.getPickUpList();
    this.getPrintTemplateList();
    this.getMealNumberNameList();
    this.getRecipeList();
    if (+this.sysConfig.is_open_customer_customize_field === 1) {
      this.getCustomizeFieldList();
    }
    if (
      +this.sysConfig.is_open_accompanying_of_diners === 1 &&
      +this.sysConfig.is_new_recipe === 1
    ) {
      this.mealNumberColumns.push({
        title: '陪餐人数(个)',
        key: 'aNumber',
        render: (h, params) => {
          const { row, index } = params;
          return h(NumberInput, {
            props: {
              value: row.aNumber,
              min: 0,
              precision: 0,
              disabled: !!this.isView,
            },
            on: {
              'on-change': (value) => {
                row.aNumber = value;
                this.userInfo.number_of_diners[index].aNumber = value;
              },
              'on-blur': () => {},
              'on-enter': () => {},
            },
          });
        },
      });
    }
    if (this.uid) {
      this.getUserDetail();
    }
    this.commonService.commonData().then((res) => {
      let { status, data } = res;
      if (status) {
        this.today = data.sys_time.split(' ')[0];
      }
    });
    this.commonService.getConfig().then((res) => {
      let { open_user_float_price, commodity_disabled_mode } = res;
      this.config = res;
      this.enableFloatRice = parseInt(open_user_float_price) === 1;
    });
  },
  watch: {
    sysConfig: {
      immediate: true,
      handler() {
        if (!this.uid) {
          this.userInfo.is_cod = this.register_open_cash ? '1' : '2';
        }
        this.getSections()
      },
    },
    userInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        if (this.ready) {
          this.modified = true;
        } else if (val.type) {
          this.ready = true;
        }
      },
    },
  },
  computed: {
    userRegisterUseGdLocation() {
      return +this.sysConfig.user_register_use_gd_location === 1;
    },
    isEnableGoogleMap() {
      return this.util.isEnableGoogleMap(this.config);
    },
    title() {
      return `${this.isView ? '查看' : this.uid ? '编辑' : '新增'}用户`;
    },
    goodDiscountDis() {
      return this.is_open_receivable_rate;
    },
  },
  methods: {
    getSections() {
      this.sections = [
        {
          isMain: false,
          title: '基础信息',
        },
        {
          isMain: false,
          title: '收货信息',
        },
        {
          isMain: false,
          title: '订单设置',
        },
        {
          isMain: false,
          title: '打印模版',
        },
        {
          isMain: false,
          title: '财务设置',
        },
        {
          isMain: false,
          title: '其他信息',
        },
        {
          isMain: false,
          show: this.is_open_user_store,
          title: '客户进销存',
        },
        {
          isMain: false,
          show: this.isOpenNewRecipe,
          title: '就餐信息',
        },
      ]
    },
    handleOrderAuditModeChange(val) {
      if (+val !== 1) {
        this.userInfo.order_audit_inquiry_split = false
      }
    },
    formatActualPriceDecimal(apDecimal) {
      if (apDecimal) {
        return apDecimal.split(',');
      }
      return [];
    },
    updateLongitudeAndLatitude(info) {
      const { poi } = info || {};
      const { id: address_poi_id } = poi || {};
      this.$set(
        this.userInfo,
        'longitudeAndLatitude',
        info.longitude + ',' + info.latitude,
      );
      this.userInfo.address = info.address;
      this.userInfo.address_poi_id = address_poi_id;
      this.$refs.position.close();
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    accountChange() {
      this.userInfo.tel = this.userInfo.account;
    },
    getUserParam() {
      const {
        account,
        password,
        name,
        code,
        type,
        group,
        sales,
        status,
        area,
        line_id,
        contact,
        tel,
        address,
        deliveryDate,
        printTemplate,
        is_cod,
        take_stock_type,
        auth_status,
        license_no,
        buyer_type,
        is_support_online_pay,
        is_self_pickup_point,
        self_pickup_point_id,
        is_support_balance_pay,
        is_public_account_pay,
        price_float_rate,
        inspection_tpl_id,
        return_order_tpl_id,
        bill_order_tpl_id,
        sort_order_tpl_id,
        picture,
        kgConversion,
        order_audit_inquiry_split,
        in_out_audit,
        bill_amount_default_order_amount,
        lowest_order_price,
        currency_type,
        is_floor_total_price,
        actual_price_decimal_point, // 实际金额小数点设置
        mall_commodity_show_time_price,
        receipt_cycle,
        user_discount,
        address_attach,
        special_remark,
        number_of_diners,
        user_tag,
        focus_on_sorting,
        aux_acc_code,
        aux_acc_name,
        address_poi_id,
        longitudeAndLatitude,
        is_invoice_print_sort,
        order_audit_mode,
      } = this.userInfo;
      const [longitude, latitude] = longitudeAndLatitude
        ? longitudeAndLatitude.split(',')
        : ['', ''];

      console.log(
        'actual_price_decimal_point',
        actual_price_decimal_point,
        is_floor_total_price,
      );
      return {
        account_tel: account,
        password,
        user_discount,
        address_attach,
        email: name.trim(),
        user_code: code,
        receivable_style: type,
        group_id: group,
        refer_id: sales,
        status,
        area_id: area,
        line_id,
        name: contact,
        tel,
        address_detail: address,
        delivery_time: deliveryDate,
        prt_tpl_id: printTemplate ? printTemplate : '',
        bill_order_tpl_id,
        is_cod,
        take_stock_type,
        auth_status,
        license_no,
        buyer_type,
        is_support_online_pay,
        is_self_pickup_point,
        self_pickup_point_id,
        is_support_balance_pay,
        is_public_account_pay,
        price_float_rate,
        inspection_tpl_id: inspection_tpl_id || '',
        return_order_tpl_id: return_order_tpl_id || '',
        sort_order_tpl_id: sort_order_tpl_id || '',
        business_license: picture
          .map((item) => item.replace(/.*?com\//, ''))
          .join(','),
        is_kg_print: kgConversion == true ? 1 : 0,
        order_audit_inquiry_split: order_audit_inquiry_split === true ? 1 : 0,
        in_out_audit: in_out_audit === true ? 1 : 0,
        bill_amount_default_order_amount:
          bill_amount_default_order_amount === true ? 1 : 0,
        lowest_order_price,
        currency_type,
        is_floor_total_price: is_floor_total_price == true ? 1 : 0,
        actual_price_decimal_point: actual_price_decimal_point,
        mall_commodity_show_time_price: mall_commodity_show_time_price ? 1 : 0,
        receipt_cycle,
        special_remark,
        // number_of_diners,
        user_tag: user_tag.join(),
        // 目前只有一个, 后续后续会有多个, 先用复选框
        focus_on_sorting: focus_on_sorting.length ? 1 : 0,
        longitude,
        latitude,
        aux_acc_code,
        aux_acc_name,
        address_poi_id,
        is_invoice_print_sort,
        order_audit_mode,
        customize_fields: this.customizeFieldList,
      };
    },
    async getMealNumberNameList() {
      if (this.uid || this.isView == '1') return;
      const params = {
        customize_type: 2,
      };
      const { status, message, data } = await this.$request.get(
        this.apiUrl.getCustomizeFieldList,
        params,
      );
      if (status) {
        this.userInfo.number_of_diners = data.map((item) => {
          return {
            meal_type_id: item.id,
            meal_type_name: item.name,
            number: 0,
            aNumber: 0,
          };
        });
      } else {
        this.errorMessage(message || '获取餐次列表失败');
      }
    },
    async getRecipeList() {
      if (!this.isOpenNewRecipe) return;
      const params = {
        pageSize: 9999,
      };
      const { status, message, data } =
        await httpNewRecipe.getRecipeList(params);
      if (status) {
        this.recipeList = data.list;
      } else {
        this.errorMessage(message || '获取菜谱列表失败');
      }
    },
    async getCustomizeFieldList() {
      const { status, data } = await this.$request.get(this.apiUrl.customizeFieldList, {
        customize_type: '14',
      });
      if (status) {
        this.customizeFieldList = data.map(item => ({
          ...item,
          id: item.id,
          name: item.name,
          value: '',
        })) || [];
      }
    },
    quit() {
      this.$router.go(-1);
    },
    save: debounce(function () {
      this.$refs.userInfo.validate((valid) => {
        if (valid) {
          const userInfo = this.getUserParam();
          if (+this.sysConfig.is_open_customer_customize_field !== 1) {
            delete userInfo.customize_fields;
          }
          const number_of_diners = [];
          const accompanying_of_diners = [];
          this.userInfo.number_of_diners.map((item) => {
            number_of_diners.push({
              meal_type_id: item.meal_type_id,
              number: +item.number,
            });
            accompanying_of_diners.push({
              meal_type_id: item.meal_type_id,
              number: +item.aNumber,
            });
          });
          const recipe_id = this.userInfo.recipe_id;
          const request = this.uid
            ? common.modifyUser(
                this.uid,
                userInfo,
                number_of_diners,
                accompanying_of_diners,
                recipe_id,
              )
            : common.setNewUser(
                userInfo,
                number_of_diners,
                accompanying_of_diners,
                recipe_id,
              );

          if (this.saving) {
            return;
          }
          this.saving = true;
          request.then((res) => {
            const { status, message } = res;
            if (status === 1) {
              this.modified = false;
              this.$smessage({ type: 'success', text: '保存成功' });
              this.router.push({ path: '/user/list' });
            } else {
              const endIndex = message.indexOf('是集团管理员');
              if (endIndex !== -1) {
                this.errorNotice({
                  desc: `客户[${message.slice(0, endIndex)}]是当前集团的集团管理员，请前往集团管理中，解绑客户后继续`,
                  goto: '/groupManage',
                });
              } else {
                this.showError(message);
              }
            }
          }).finally(() => {
            this.saving = false;
          });
        } else {
          this.scrollToError();
        }
      });
    }, 300),
    scrollToError() {
      this.$nextTick(() => {
        let firstError = document.querySelector('.ivu-form-item-error');
        if (firstError) {
          firstError.scrollIntoView();
        }
      });
    },
    showError(msg) {
      this.scrollToError();
      this.$snotice({
        type: 'error',
        title: '保存失败',
        text: msg,
        btnTxt: '返回修改',
      });
    },
    openPosition() {
      this.$refs.position.open();
    },
    async getUserAjaxList() {
      let params = {
        pageSize: 99999,
      };
      let res = await common.getUserAjaxList(params);
      if (res.status) {
        let data = res.data;
        this.userTypeList = data.list;
        // 新增客户
        if (!this.uid) {
          let defaultUserType = this.userTypeList.find(
            (item) => Number(item.is_default) === 1,
          );
          if (defaultUserType) {
            this.userInfo.type = defaultUserType.id;
          }
        }
      }
    },
    async getUserGroupList() {
      let res = await common.getGroupList();
      let defaultItem = {
        id: '0',
        group_name: '无',
      };
      if (res.status) {
        let data = res.data;
        this.userGroupList = data;
      }
      this.userGroupList.unshift(defaultItem);
    },
    async getSalesList() {
      let params = {
        type: 1, // 过滤冻结帐号
      };
      let res = await common.getSalesList(params);
      let defaultItem = {
        id: '0',
        name: '无',
      };
      if (res.status) {
        let data = res.data;
        this.salesList = data;
      }
      this.salesList.unshift(defaultItem);
    },
    async getAreaList() {
      let defaultItem = {
        id: '0',
        name: '未设置',
      };
      let res = await common.getAreaList();
      this.areaList = [];
      if (res.status) {
        let data = res.data;
        if (!this.uid) {
          const dataItem = data.find((item) => +item.is_default === 1) || {};
          this.userInfo.area = dataItem.area_id;
        }
        this.areaList = data;
      }
      this.areaList.unshift(defaultItem);
    },
    async getBuyerType() {
      let res = await common.getBuyerType();
      this.buyerType = [];
      if (res.status) {
        this.buyerType = res.data;
      }
    },
    async getUserTagList() {
      let res = await common.getUserTagList();
      if (res.status) {
        this.userTagList =
          res.data.map((item) => ({
            value: item.id,
            label: item.name,
            disabled: false,
          })) || [];
      }
    },
    async getPickUpList() {
      const { status, message, data } = await this.$request.get(
        this.apiUrl.SelfPickupPointList,
      );
      if (status && data && data.length) {
        this.pickUpList = data;
      }
    },
    async getDeliveryDateList() {
      let defaultItem = {
        id: '0',
        start_time: '未设置',
        end_time: '未设置',
      };
      let res = await common.getDeliveryDateList();
      if (res.status) {
        let data = res.data;
        this.deliveryDateList = data;
      }
      this.deliveryDateList.unshift(defaultItem);
    },
    async getPrintTemplateList() {
      let res = await common.getPrintTemplateList();
      if (res.status) {
        let data = res.data;
        this.printTemplateList = data;
      }
    },
    async getUserDetail() {
      let res = await common.getUserDetail(this.uid);
      if (res.status) {
        let data = res.data;
        this.isSyncPlatform = data.is_sync_from_consortium_platform == 1;
        this.userInfo = {
          bind_provider_type: data.bind_provider_type,
          disable_type: data.disable_type,
          account: data.account_tel,
          password: '',
          user_discount: data.user_discount ? +data.user_discount : '',
          address_attach: data.address_attach,
          name: data.email,
          code: data.user_code,
          type: data.receivable_style,
          group: data.group_id,
          sales: data.refer_id,
          area: data.area_id,
          line_id: data.line_id,
          contact: data.name,
          tel: data.tel,
          address: data.address_detail,
          deliveryDate: data.delivery_time,
          printTemplate: data.prt_tpl_id,
          latitude: data.latitude,
          longitude: data.longitude,
          bill_order_tpl_id: data.bill_order_tpl_id,
          is_cod: data.is_cod,
          take_stock_type: data.take_stock_type,
          auth_status: data.auth_status,
          license_no: data.license_no,
          buyer_type: data.buyer_type,
          is_support_online_pay: data.is_support_online_pay || '1',
          self_pickup_point_id: data.self_pickup_point_id,
          is_self_pickup_point: data.is_self_pickup_point,
          is_support_balance_pay: data.is_support_balance_pay,
          is_public_account_pay: data.is_public_account_pay,
          status: data.status + '',
          id: this.uid,
          inspection_tpl_id: data.inspection_tpl_id
            ? data.inspection_tpl_id
            : '',
          return_order_tpl_id: data.return_order_tpl_id
            ? data.return_order_tpl_id
            : '',
          sort_order_tpl_id: data.sort_order_tpl_id
            ? data.sort_order_tpl_id
            : '',
          user_trace_url: data.user_trace_url,
          picture: data.business_license == null ? [] : data.business_license,
          price_float_rate: data.price_float_rate ? data.price_float_rate : '',
          kgConversion: data.is_kg_print == 1 ? true : false,
          order_audit_inquiry_split: data.order_audit_inquiry_split == 1 ? true : false,
          in_out_audit: data.in_out_audit == 1 ? true : false,
          bill_amount_default_order_amount:
            data.bill_amount_default_order_amount == 1 ? true : false,
          is_floor_total_price: data.is_floor_total_price == 1 ? true : false,
          actual_price_decimal_point: data.actual_price_decimal_point,
          mall_commodity_show_time_price:
            data.mall_commodity_show_time_price == 1,
          lowest_order_price: data.lowest_order_price
            ? Number(data.lowest_order_price)
            : '',
          currency_type: data.currency_type,
          receipt_cycle: +data.receipt_cycle,
          special_remark: data.special_remark,
          number_of_diners: data.number_of_diners.map((item) => {
            const ad =
              (data.accompanying_of_diners || []).find(
                (ad) => ad.meal_type_id === item.meal_type_id,
              ) || {};
            return {
              ...item,
              aNumber: ad.number || 0,
            };
          }),
          recipe_id: data.recipe_id,
          user_tag: data.user_tag ? data.user_tag.split(',') : [],
          // 目前只有一个, 后台入参暂未1或0
          focus_on_sorting: data.focus_on_sorting == 1 ? ['sorting'] : [],
          aux_acc_code: data.aux_acc_code,
          aux_acc_name: data.aux_acc_name,
          address_poi_id: data.address_poi_id,
          is_invoice_print_sort: data.is_invoice_print_sort,
          order_audit_mode: data.order_audit_mode,
        };
        if (data.customize_fields && data.customize_fields.length) {
          data.customize_fields.forEach(item => {
            if (item.value) {
              const customizeFieldIndex = this.customizeFieldList.findIndex(item2 => item2.id === item.id);
              if (customizeFieldIndex > -1) {
                this.customizeFieldList[customizeFieldIndex].value = item.value;
              }
            }
          })
        }
        // 将disableType添加到路由栏的query中
        this.$router.replace({
          query: {
            disableType: data.disable_type,
            bindProviderType: data.bind_provider_type,
            purchaseTaskBindProviderType: data.purchase_task_bind_provider_type,
            // 原有的query
            ...this.$route.query,
          },
        });

        if (data.line_id) {
          this.defaults = false;
        }
        this.original_line_id = data.line_id;
        if (+data.longitude && +data.latitude) {
          this.userInfo.longitudeAndLatitude =
            data.longitude + ',' + data.latitude;
        }
        this.$nextTick(() => {
          this.genTraceCode();
          this.modified = false;
        });
      }
    },
    genTraceCode() {
      let qrCodeOpts = {
        errorCorrectionLevel: 'H',
        type: 'image/png',
        rendererOpts: {
          quality: 0.3,
          margin: 0,
        },
      };
      let url = this.userInfo.user_trace_url;
      if (!url) {
        return false;
      }
      QRCode.toDataURL(url, qrCodeOpts, (err, url) => {
        if (err) return false;
        this.trace_qrcode = url;
      });
    },
    changeLine() {
      // 切换客户的线路
      if (
        this.userInfo.line_id &&
        this.original_line_id * 1 &&
        this.userInfo.line_id !== this.original_line_id &&
        this.uid
      ) {
        this.$smodal({
          text: `<p>切换线路将会改动今天(${this.today})及今天以后的未发货订单的线路和司机, 确定切换？</p> <p>注意：保存之后才生效！</p>`,
          okTxt: '切换',
          onOk: () => {
            this.original_line_id = this.userInfo.line_id;
          },
          onQuit: () => {
            this.userInfo.line_id = this.original_line_id;
          },
        });
      }
    },
    handleSelectTag(val) {
      this.userInfo.user_tag = val;
      this.userTagList = this.userTagList.map((tag) => {
        if (
          this.userInfo.user_tag.length >= 3 &&
          !this.userInfo.user_tag.includes(tag.value)
        )
          tag.disabled = true;
        else if (tag.disabled) tag.disabled = false;
        return tag;
      });
    },
    handleScroll: throttle(function (e) {
      this.handleHighLight(e);
    }, 100),
    handleHighLight(e) {
      let scrollItems = document.querySelectorAll('.scroll-top');
      // 判断滚动条是否滚动到底部
      for (let i = scrollItems.length - 1; i >= 0; i--) {
        // 判断滚动条滚动距离是否大于当前滚动项可滚动距离
        let judge =
          e.target.scrollTop >=
          scrollItems[i].offsetTop - scrollItems[0].offsetTop;
        if (judge) {
          this.activeStep = i;
          break;
        }
      }
    },
    jumpTo(id, index) {
      this.activeStep = index;
      let scrolls = document.getElementById(id);
      scrolls &&
        scrolls.scrollIntoView({ block: 'start', behavior: 'instant' });
    },
  },
};
</script>

<style lang="less" scoped>
.s-common {
  .position-btn {
    position: absolute;
    right: 0;
    // top: -4px;
    // width: 80px;
    text-align: center;
    padding-left: 10px;
    padding-right: 10px;
  }
  .qrcode-download {
    position: relative;
    img {
      width: 128px;
      border: 1px solid #d8d8d8;
      vertical-align: middle;
    }
    span {
      display: inline-block;
      vertical-align: bottom;
      margin: 0 0 0 14px;
      color: #03ac54;
    }
  }
  .account {
    position: relative;
    &__edit {
      position: absolute;
      right: -26px;
      top: 50%;
      transform: translateY(-50%);
      color: rgb(158, 167, 180);
      &.done {
        color: rgba(158, 167, 180, 0.5);
      }
    }
  }
  .meal-number-box {
    margin-right: -134px;
    margin-left: 134px;
  }
}
</style>
<style lang="less">
.aside-box {
  position: fixed;
  overflow-y: auto;
  top: 126px;
  right: 25px;
  width: 130px;
  z-index: 100;
  height: calc(~'100vh - 188px');
  border-left: 1px solid #e8e8e8;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.75);
  .aside-1-box {
    margin-bottom: 20px;
    font-size: 12px;
    line-height: 12px;
    margin-left: 10px;
    position: relative;
    .txt {
      cursor: pointer;
    }
    .txt::before {
      position: absolute;
      top: 0px;
      left: -11px;
      content: '';
      width: 3px;
      height: 12px;
      background: #d8d8d8;
    }
  }
  .aside-2-box {
    margin-top: 12px;
    margin-left: 18px;
    font-size: 12px;
    line-height: 12px;
    .txt {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: relative;
    }
    .txt::before {
      position: absolute;
      top: 0px;
      left: -19px;
      content: '';
      width: 3px;
      height: 12px;
      opacity: 0;
    }
  }
  .aside-1-box:not(:first-child) {
    margin-top: 20px;
  }
  .active {
    > .txt {
      color: #03ac54;
    }
    > .txt::before {
      background: #03ac54;
      opacity: 1;
    }
  }
}
.aside-box&::-webkit-scrollbar {
  width: 1;
}
</style>
