<template>
  <div class="common orderState">
    <ListTable
      ref="listTable"
      :border="false"
      :outer-border="true"
      :data-provider="apiUrl.getSingleCase"
      :columns="columns"
      :filter-items="filterItems"
      :before-request="beforeRequest"
      :advance-items="advanceItems"
      :height="getTableHeight() - 35"
      :after-request="afterRequest"
      @on-selection-change="handleSelectionChange"
    >
      <ExportButton
        v-if="!is_sys_supervisor"
        type="default"
        slot="button"
        text="导出"
        :offline="true"
        :param-getter="_getParams"
        :api="apiUrl.statUserOffLineOrderExport"
      ></ExportButton>
      <!-- 这里用空的before-table占位，不然多选之后样式会有问题 -->
      <template v-if="selectedRows.length!==0">
        <div slot="before-table"></div>
      </template>
      <div slot="batch-operation">
        <Button
          @click="$_onReminder(false)"
          class="operation__item"
        >
          {{reminding ? '短信发送中...' : '发送催单短信(选中客户)'}}
        </Button>

        <Button
          @click="$_onReminder(true)"
          class="operation__item"
        >
          {{reminding ? '短信发送中...' : '发送催单短信(全部客户)'}}
        </Button>
        <Button @click="batchDel" :loading="batchDelLoading">
          删除客户
        </Button>
      </div>
    </ListTable>

    <Modal
      :title="modalTitle"
      v-model="modal9"
      width="800"
      class="order-detail-modal"
    >
      <ListTable
        v-if="modal9"
        ref="detailListTable"
        :border="false"
        :outer-border="true"
        :data-provider="apiUrl.singleHistory"
        :columns="columns2"
        :filters="filters"
        :height="getTableHeight() - 160"
      >
      </ListTable>
    </Modal>
  </div>
</template>

<script>
import ListTable from '@components/list-table';
import TimeTypeSelect from '@components/common/TimeTypeSelect';
import dateUtil from '@/util/date';
import Button from "@components/button";
import ConfigMixin from '@/mixins/config';
import RelationNo from '@/components/relation-no'
import StorageUtil from '@util/storage.js';
import {
  group,
  userTag,
  area,
} from '@/components/standard/sdp-filter-items'

const start = dateUtil.getBeforeDate(89);
const end = dateUtil.getTomorrow();

export default {
  name:'orderState',
  components: {
    ListTable,
    Button
  },
  mixins: [ConfigMixin],
  data () {
    const otherParams = StorageUtil.getLocalStorage('home_redirect_other_params') || {}
    const homeRedirectSelectDate = StorageUtil.getLocalStorage('home_redirect_select_date')
    return {
      modalTitle: '',
      modal9: false,
      areaList: [],
      reminding: false,
      count: 0,
      selectedRows: [],
      // 详情列表检索条件
      filters: {
        user_id: '',
        start_day: '',
        end_day: '',
        date_type: ''
      },
      filterItems: [{
        type: 'custom',
        component: TimeTypeSelect,
        width: 299,
        // 开始时间、结束时间、默认筛选前缀(下单日期)
        defaultValue: homeRedirectSelectDate ? [...homeRedirectSelectDate, 1] : [start, end, 1],
        key: ['start_day', 'end_day', 'date_type'],
        props: {
          data: [{
            label: '下单日期',
            value: 1
          },
          {
            label: '发货日期',
            value: 2
          }],
          clearable: false
        }
      },
      {
        label: '搜索',
        type: 'Input',
        key: 'search_key',
        props: {
          placeholder: '输入客户名称/客户账号/客户编码'
        }
      }],
      advanceItems: [{
        items: [{
          label: '下单状态',
          type: 'Select',
          key: 'is_ordered',
          data: [{
            label: '全部',
            value: '0'
          }, {
            label: '已下单',
            value: '1'
          }, {
            label: '未下单',
            value: '2'
          }],
          defaultValue: otherParams.is_ordered || ''
        }, 
        {
          label: '区域',
          type: 'custom',
          key: 'area_id',
          component: area
        }, 
        {
          label: '账户状态',
          type: 'Select',
          key: 'user_status',
          data: [
            {
              label: '全部',
              value: '0'
            },
            {
              label: '未审核',
              value: '1'
            },
            {
              label: '正常',
              value: '2'
            },
            {
              label: '被禁用',
              value: '3'
            }
          ]

        }, 
        {
          type: 'custom',
          label: '集团',
          key: 'group_id',
          component: group
        }, 
        {
          label: '客户标签',
          key: 'user_tag',
          type: 'custom',
          block: true,
          attrs: {
            keyName: 'user_tag',
          },
          component: userTag,
          onChange: (value) => {
            return { value, stop: true };
          },
				}]
      }],
      columns: [
        {
          type: 'selection',
          show: () => { return !this.is_sys_supervisor },
          width: 60,
          fixed: 'left'
        },
        {
          title: '客户账号',
          key: 'account_tel',
          align: 'left',
          fixed: 'left',
          width: '120'
        },
        {
          title: '客户编码',
          key: 'user_code',
          align: 'left'
        },
        {
          title: '客户名称',
          key: 'email',
          align: 'left',
          poptip: true,
          render: (h, params) => {
            return params.row.email || '--'
          },
        },
        {
          title: '详细地址',
          key: 'address_detail',
          align: 'left',
          width: 150,
          poptip: true,
          render: (h, params) => {
            return params.row.address_detail || '--'
          },
        },
        {
          title: '业务员',
          key: 'refer_id',
          align: 'left'
        },
        {
          title: '最后下单时间',
          key: 'lastTime',
          align: 'left',
          width: 160,
          tip: '某一个客户在所有时间段下的最后一次下单时间'
        },
        {
          title: '最近一次下单时间',
          key: 'recent_order_time',
          align: 'left',
          width: 170,
          tip: '某一个客户在当前页面的筛选时间段下的最近一次下单时间',
          render: (h, params) => {
            return h(
                'div',
                params.row.recent_order_time
              )
          }
        },
        {
          title: '日活数',
          key: 'order_days',
          tip: '某一个客户在当前页筛选条件时间下有多少天存在下单行为',
          align: 'right'
        },
        {
          title: '注册时间',
          key: 'create_time',
          tip: '某一个客户的注册时间',
          minWidth: 110
        },
        {
          title: '注册渠道',
          key: 'source_from_text',
          minWidth: 100,
          tip: '某一个客户通过何种方式进行注册的（2023年9月之前的客户注册数据均记录为其他）',
        },
        {
          title: '成交周期',
          key: 'first_order_day',
          tip: '某一个客户从注册到第一次下单间隔了多少天（往上取）',
          minWidth: 100,
          align: 'right'
        },
        {
          title: '客单价',
          key: 'user_price',
          tip: '某一个客户某一个时间段内的下单总金额数/总订单笔数',
          align: 'right'
        },
        {
          title: '下单次数',
          key: 'orderCount',
          align: 'left'
        },
        {
          title: '下单金额',
          key: 'totalPrice',
          align: 'right',
          width: 100,
        },
        {
          title: '发货金额',
          key: 'actualPrice',
          align: 'right',
          width: 100,
        },
        {
          title: '下单历史',
          type: 'action',
          key: 'action',
          width: 100,
          align: 'right',
          fixed: 'right',
          actions: [{
            name: '详情',
            action: params => {
              const data = params.row;
              const { start_day, end_day, date_type } = this._getParams();
              if (start_day) {
                this.modal9 = true;
                this.nowPage1 = 1;
                this.modalTitle = data.email;
                this.filters = {
                  start_day, end_day, date_type,
                  user_id: data.id
                }
                this.$refs.detailListTable.fetchData();
              } else {
                this.modalError('必须选择下单时间');
              }
            }
          }]
        }],
      columns2: [
        {
          title: '订单编号',
          key: 'order_no',
          render: (h, params) => {
            return h(RelationNo, {
              props: {
                no: params.row.order_no,
                id: params.row.order_id
              }
            });
          }
        },
        {
          title: '发货日期',
          key: 'delivery_date'
        },
        {
          title: '下单金额',
          key: 'price'
        },
        {
          title: '发货金额',
          key: 'actual_price'
        }
      ],
      batchDelLoading: false,
    }
  },
  created () {
  },
  activated() {
    // 首页筛选参数
    const otherParams = StorageUtil.getLocalStorage('home_redirect_other_params')
    const homeRedirectSelectDate = StorageUtil.getLocalStorage('home_redirect_select_date')
    if (otherParams) {
      this.$refs.listTable.setValue(
        'is_ordered',
        otherParams.is_ordered,
        true,
      );
      this.$refs.listTable.setValue(
        ['start_day', 'end_day', 'date_type'],
        [...homeRedirectSelectDate, 1],
        true,
      );
      this._fetchData(false)
    }
  },
  methods: {
    beforeRequest(params) {
      if (!params.end_day) {
        return false;
      } else {
        return params
      }
    },
    _getParams () {
      return this.$refs.listTable && this.$refs.listTable.getParams();
    },
    _fetchData (resetPage) {
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage)
    },
    /**
     * @description: 发送催单短信
     * @param {Boolean} _isAll 是否全部发送(false为选中项发送)
     * @author: lizi
     */
    $_onReminder (_isAll = false) {
      if (this.reminding) {
        return false;
      }
      const reminder = () => {
        let params = {}
        if (_isAll) {
          params = this._getParams()
        } else {
          let userIds = this.selectedRows.map(user => user.id);
          if (!userIds || userIds.length === 0) {
            this.modalError('请选择客户');
            return false;
          }
          params = {
            user_ids: JSON.stringify(userIds)
          };

        }
        this.reminding = true;
        this.$request.post(this.apiUrl.user.reminder, params).then(
          res => {
            this.reminding = false;
            let { status, message } = res;
            if (status) {
              this._fetchData(true);
              this.$Message.success(message || '发送成功');
            } else {
              this.modalError(message || '发送失败, 请重试');
            }
          },
          () => {
            this.reminding = false;
          }
        );
      }

      this.$Modal.confirm({
        title: '提示',
        render: h => {
          return h(
            'div',
            {
              style: {
                marginTop: '20px'
              }
            },
            [
              h('span', '共选择'),
              h(
                'span',
                {
                  style: {
                    color: 'red'
                  }
                },
                _isAll ? this.count : this.selectedRows.length
              ),
              h('span', '个客户，确定批量发送催单短信？')
            ]
          );
        },
        onOk: () => {
          reminder();
        }
      });

    },
    afterRequest (params) {
      if (params.status === 0) {
        this.errorMessage(params.message)
        return;
      }
      this.count = params.data.pageParams.count || 0;
      return params;
    },
    handleSelectionChange (selection) {
      this.selectedRows = selection;
    },
    async batchDel () {
      let userIds = this.selectedRows.map(user => user.id);
      if (!userIds || userIds.length === 0) {
        this.modalError('请选择客户');
        return false;
      }
      this.batchDelLoading = true
      const params = {
        user_id: userIds.join(',')
      }
      const { status, message, data } = await this.$request.post(this.apiUrl.delUser, params)
      if (status) {
        this.successMessage(`已删除成功${data}个客户，失败${userIds.length-data}个客户`);
        this._fetchData()
      } else {
        console.log('message', message)
        this.errorMessage(message)
      }
      this.batchDelLoading = false
    },
  }
}
</script>

<style lang="less" >
.orderState .sdp-table__cell .ivu-tooltip-rel {
  vertical-align: 0.5px;
}
.order-detail-modal .ivu-modal-body {
  padding: 0 20px!important;
}
</style>
