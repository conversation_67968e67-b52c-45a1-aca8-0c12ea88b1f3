<template>
  <div class="user-unavailable-goods">
    <Form :label-width="100">
      <FormItem label="屏蔽商品模式" class="blockProductModel">
        <RadioGroup v-model="mode" @on-change="onChangeMode">
          <Radio
            v-for="item in modeList"
            :key="item.value"
            :true-value="item.value"
            :label="item.value"
            >{{ item.label }}
            <Tooltip
              :content="item.tooltip"
              v-if="item.tooltip"
              max-width="400"
            >
              <i
                class="icon-help1 iconfont icontip"
                style="font-size: 13px"
              ></i>
            </Tooltip>
          </Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="商品选择模式" v-if="showGoods">
        <RadioGroup :value="chooseType" v-if="hasRadio">
          <Radio
            v-for="item in chooseTypeList"
            @click.native="onChangeChooseType(item.value)"
            :key="item.value"
            :true-value="item.value"
            :label="item.value"
            >{{ item.label }}</Radio
          >
        </RadioGroup>
      </FormItem>
    </Form>
    <template v-if="showGoods">
      <div class="unavailable-goods" v-if="chooseType === 0">
        <ListTable
          :columns="columns1"
          :data-provider="apiUrl.getUnavailableGoods"
          :border="false"
          :outer-border="true"
          :height="getTableHeight() - 240"
          row-key="commodity_id"
          :auto-load-data="true"
          @on-selection-change="getCheckedUnavailableGoods"
          :filter-items="filterItems"
          :before-request="beforeRequest"
          :after-request="afterRequest"
          :filters="{ uid }"
          ref="table"
          :extraSelectionCount="extraSelectionCount"
        >
          <div slot="before-table">
            <div
              style="display: flex"
              v-show="!selectedUnavailableGoods.length"
            >
              <SButton styleType="btnStyleForAdd" @click="showAddGoods"
                >新增</SButton
              >
              <Button class="ml8" @click="downloadTemplate">下载模板</Button>
              <Upload
                :format="['csv', 'xlsx']"
                :on-success="uploadSuccess"
                :show-upload-list="false"
                :action="`/superAdmin/userSuper/ImportDisabledCommodity?style=0&user_id=${uid}`"
              >
                <Button class="ml8">导入</Button>
              </Upload>
              <Button
                class="ml8"
                :disabled="disabled"
                @click="exportDisabledGoods"
                >导出</Button
              >
              <Button
                class="ml8"
                @click="onGoModeGoods"
                v-if="mode == 0 && modeDesc.button_desc"
                >{{ modeDesc.button_desc }}</Button
              >
            </div>
          </div>
          <div slot="batch-checked" class="checked_button" @click="checkedChange()">
             {{checkedAll ? '勾选当前页内容' : '勾选所有页内容'}}
          </div>
          <div class="flex-con" slot="batch-operation">
            <PopButton
              :confirm="true"
              title="确认批量删除？"
              placement="bottom"
              @on-ok="batchDelUnavailableGoods"
              >批量删除</PopButton
            >
          </div>
        </ListTable>
      </div>
      <div class="unavailable-category" v-show="chooseType === 1">
        <Transfer
          :list-style="{
            width: '400px',
            height: '580px',
          }"
          :titles="['未选分类', '已选分类']"
          @on-change="handleChange"
          :filter-method="filterMethod"
          :data="transferData"
          :target-keys="selectTransferData"
          filterable
          :render-format="categoryRender"
        ></Transfer>
      </div>
    </template>
    <Button
      style="margin-top: 8px"
      @click="onGoModeGoods"
      v-if="!showGoods && mode == 0 && modeDesc.button_desc"
      >{{ modeDesc.button_desc }}</Button
    >
    <goods-list-modal
      ref="goodsModal"
      :params="{ disable_add: 1 }"
      v-model="showGoodsListModal"
      modalType="shieldGoods"
      :uid="uid"
      @on-add="handlerAdd"
    >
      <div></div>
      <Col slot="extra" style="text-align: right; flex: 1">
        <PopButton
          :confirm="true"
          :title="
            '确定' +
            (mode == 0 ? (commodity_disabled_mode == 2 ? '售卖' : '屏蔽') : mode == 1 ? '屏蔽' : '售卖') +
            '所有当前筛选的商品？'
          "
          type="primary"
          @on-ok="handleAddAll"
          >{{
            mode == 0 ? (commodity_disabled_mode == 2 ? '售卖筛选商品' : '屏蔽筛选商品') : mode == 1 ? '屏蔽筛选商品' : '售卖筛选商品'
          }}</PopButton
        >
      </Col>
    </goods-list-modal>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import common from '@api/user.js';
import { api } from '@api/api.js';
import { get } from '@api/request';
import newGoods from '@components/user/UserNewGoods';
import goodsSelect from '@components/common/goodsSelect';
import GoodsListModal from '@components/order/goodsListModal';
import LayoutMixin from '@/mixins/layout';
import SButton from '@components/button';
import ConfigMixin from '@/mixins/config';
import user from '@/api/user';
import ListTable from '@components/list-table';
import CategorySelect from '@components/common/categorySelect';

const chooseTypeList = [
  {
    label: '按商品',
    value: 0,
  },
  {
    label: '按分类',
    value: 1,
  },
];

export default {
  name: 'NewUser',
  mixins: [LayoutMixin, ConfigMixin],
  data() {
    return {
      hasBlockProductRadio: 0, // 用于还原屏蔽商品模式 radio
      hasRadio: true,
      transferData: [],
      selectTransferData: [],
      chooseTypeList,
      chooseType: 0,
      showGoodsListModal: false,
      columns1: [],
      filterItems: [],
      addUnavailableGoods: {
        goodsFilter: {},
        checkedAddGoods: [],
      },
      account: '',
      password: '',
      userInfo: {
        account: '',
        password: '',
        name: '',
        code: '',
        type: '0',
        group: '0',
        sales: '0',
        status: '2',
        area: '',
        contact: '',
        tel: '',
        address: '',
        deliveryDate: '',
        printTemplate: '',
      },
      navActive: 0,
      userNewGoodsActive: false,
      uid: '', // 用户详情的ID
      addAllGoods: false,
      disabled: true,
      checkGoods: '',
      modeList: [],
      mode: 0,
      modeDesc: {
        disable_mode: 0, // 默认给个0是为了页面加载计算表格高度，如果showGoods一开始是false，那么计算高度就不对
      },
      selectedUnavailableGoods: [],
      checkedAll: false,
      extraSelectionCount: 0,
      allIds: ''
    };
  },
  created() {
    this.init();
    this.uid = this.$route.query.uid;
    const { disableType } = this.$route.query || {};
    this.chooseType = Number(disableType) === 1 ? 1 : 0;
    this.getMode();
    this.getModeText();
    if (this.chooseType === 1) {
      this.getTransferData();
    }
  },
  computed: {
    ...mapState({}),
    bottomDesc() {
      const modeOption = this.modeList.find((item) => item.value == this.mode);
      return `选择【${modeOption.label}】后，客户仅生效该配置下指定【${modeOption.value == 1 ? '屏蔽' : '售卖'}】的商品`;
    },
    showGoods() {
      // 如果是上级也是跟随系统配置，那么客户档案切换成跟随配置项后，还是可以独立设置分类或商品；如果上级是指定售卖/屏蔽，那么客户档案切换成跟随系统配置后，则不展示任何数据，需求里红框框起来的操作和列表都隐藏
      return !(this.modeDesc.disable_mode != 0 && this.mode == 0);
    },
  },
  watch: {
    mode(newVal, oldVal) {
       this.hasBlockProductRadio = oldVal
    }
  },

  methods: {
    init() {
      this.filterItems = [
        {
          label: '商品分类',
          type: 'custom',
          key: ['category_id', 'category_id2', 'category_id3'],
          component: CategorySelect,
          props: {
            noThree: !this.isOpenThreeCategory,
          },
        },
        {
          label: '商品搜索',
          type: 'Input',
          key: 'searchKey',
          props: {
            placeholder: '输入商品编码/名称/助记码/别名',
          },
        },
      ];
      this.columns1 = [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select',
        },
        {
          title: '图片',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40',
              },
            });
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left',
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
        },
        {
          title: '商品分类',
          key: 'category_name2',
          align: 'left',
          render: (h, params) => {
            const textArr = [params.row.category_name, params.row.category_name2];
            console.log(textArr)
            if (this.isOpenThreeCategory) {
              textArr.push(params.row.category_name3);
            }
            return h('div', textArr.filter((item) => item).join('/'));
          },
        },
        {
          title: '单位',
          key: 'unit',
          align: 'left',
        },
        {
          title: '描述',
          key: 'summary',
          align: 'left',
        },
        {
          title: '别名',
          key: 'alias',
          align: 'left',
        },
        {
          title: '操作',
          type: 'action',
          width: 100,
          align: 'center',
          actions: (params) => {
            return [
              {
                name: '删除',
                confirm: '确定删除？',
                action: () => {
                  this.delUnavailableGoods(params.row.id);
                },
              },
            ];
          },
        },
      ];
    },
    async addDisabledCategory(newTargetKeys) {
      // 找出新增的分类
      const addCategory = newTargetKeys.filter((item) => {
        return this.selectTransferData.indexOf(item) === -1;
      });
      const res = await user.setUnavailableGoods(
        this.uid,
        addCategory.join(','),
        1,
      );
    },
    beforeRequest(params) {
      params.user_id = this.uid;
      return params;
    },
     afterRequest(res) {
      if(res.status) {
        this.disabled = res.data.pageParams.count === 0
      }
      return res
    },
    onChangeChooseType(value) {
      this.$Modal.confirm({
        content: '确认切换商品选择模式？',
        onOk: () => {
          this.chooseType = value;
          this.setSwitchType();
          if (value === 1) this.getTransferData();
          else {
            this.$refs.table.fetchData();
          }
        },
        onCancel: () => {
          // 重置radio
          this.hasRadio = false;
          setTimeout(() => {
            this.hasRadio = true;
          }, 10);
          return false;
        },
      });
    },
    async setSwitchType() {
      await get(api.setUnavailableType, {
        mode: this.mode,
        type: this.chooseType,
        id: this.$route.query.uid,
      });
      this.$router.push({
        path: this.$route.path,
        query: {
          ...this.$route.query,
          uid: this.$route.query.uid,
          disableType: this.chooseType,
          mode: this.mode,
        },
      });
    },
    // 获取所有可屏蔽分类和已屏蔽分类
    async getTransferData() {
      const res = await get(api.getUnavailableGoods, {
        type: 1,
        user_id: this.$route.query.uid,
      });
      if (res.status) {
        res.data.list.forEach((item) => {
          item.key = item.category_id;
          item.label = item.category_name;
        });
        res.data.selected_list &&
          res.data.selected_list.forEach((item) => {
            item.key = item.category_id;
            item.label = item.category_name;
          });
        this.transferData = [...res.data.list, ...res.data.selected_list];
        this.selectTransferData = res.data.selected_list.map(
          (item) => item.key,
        );
      }
    },
    async handleChange(newTargetKeys) {
      // 增加和取消的情况
      if (newTargetKeys.length > this.selectTransferData.length) {
        await this.addDisabledCategory(newTargetKeys);
      } else {
        // 删除的情况, 获取selectTransferData中被删除的分类
        const delCategoryKey = this.selectTransferData.filter((item) => {
          return newTargetKeys.indexOf(item) === -1;
        });
        // 根据key去transferData中找到对应的分类
        const delCategory = this.transferData.filter((item) => {
          return delCategoryKey.indexOf(item.key) !== -1;
        });
        await user.delUnavailableCategories(delCategory, 1);
      }
      await this.getTransferData();
      this.$Message.success('操作成功');
    },
    filterMethod(data, query) {
      if (data.label === null) {
        return true;
      }
      return data.label.indexOf(query) > -1;
    },
    categoryRender(item) {
      if (item.is_selected) {
        return `<span title="${item.category_name}" class="category-item" style="line-height: 2.3;"><span>${item.category_name ? item.category_name : ''}</span><i class="sui-icon icon-yifenpei category-sorter"></i></span>`;
      } else {
        return `<div title="${item.category_name}" class="category-item" style="line-height: 2.3;">${item.category_name ? item.category_name : ''}<div class="category-sorter-unshare">未分配</div></div>`;
      }
    },
    uploadSuccess(response) {
      if (response.status == 0) {
        this.$Notice.error({
          title: response.message,
        });
      } else {
        this.$Notice.success({
          title: '导入成功，本次导入' + response.data + '个商品',
        });
        this.$refs.table.fetchData();
      }
    },
    downloadTemplate() {
      window.location.href =
        '/superAdmin/userSuper/ImportDisabledCommodityTemplate';
    },
    /**
     * @description 批量删除不可用商品
     * <AUTHOR>
     */
    batchDelUnavailableGoods() {
      let goodsIds = this.checkedAll ? this.allIds : this.selectedUnavailableGoods;
      if (!goodsIds || goodsIds.length === 0) {
        this.errorNotice('请选择需要删除的商品');
        return false;
      }
      this.delUnavailableGoods(goodsIds.join(','));
    },
    onGoModeGoods() {
      const commodityDisabledType = {
        0: '/shieldOrderList',
        1: '/delivery/area/unavailableGoods',
        2: '/group-manage/unavailableGoods',
      };
      const newLink = this.$router.resolve({
        path: commodityDisabledType[this.commodity_disabled_type],
        query: {
          id: this.modeDesc.style_id,
          uid: this.modeDesc.style_id,
          disableMode: this.modeDesc.disable_mode,
          disableType: this.modeDesc.disable_type,
        },
      });
      window.open(newLink.href, '_blank');
    },
    /**
     * @description 删除不可用商品
     * <AUTHOR>
     * @param goodsIds 商品id，以','隔开，如：1,23,333
     */
    async delUnavailableGoods(goodsIds) {
      let res = await common.delUnavailableGoods({ cid: goodsIds });
      let { status, message } = res;
      if (status) {
        this.successNotice(message || '删除成功');
        this.$refs.table.fetchData();
      } else {
        this.errorNotice({
          title: goodsIds.length > 1 ? '批量删除失败' : '删除失败',
          desc: message ? message : '删除失败',
        });
      }
    },
    cancel() {
      history.go(-1);
    },
    /**
     * @description 获取选中的不可用商品
     * <AUTHOR>
     */
    getCheckedUnavailableGoods(selection,data,obj) {
      this.selectedUnavailableGoods = selection.map((value) => {
        return value.id;
      });

      if((obj && obj.noall) || selection.length==0) {
        this.extraSelectionCount = 0
        this.checkedAll = false
      }
    },
    showAddGoods() {
      this.showGoodsListModal = true;
    },
    handleAddAll() {
      const label = +this.commodity_disabled_mode === 2 ? '售卖' : '屏蔽';

      let filters = this.$refs.goodsModal.getFilters();
      let params = filters;
      params || (params = {});
      params.user_id = this.uid;
      common.addDisabledGoods(params).then((res) => {
        let { status, message } = res;
        if (status) {
          this.showGoodsListModal = false;
          this.closeLayer();
          this.successNotice(label + '筛选商品成功');
          this.$refs.table.fetchData();
        } else {
          this.errorNotice({
            title: label + '筛选商品失败',
            desc: message ? message : label + '筛选商品失败',
          });
        }
      });
    },
    async handlerAdd(orders) {
      // 添加当前页勾选的商品
      if (orders) {
        let cid = [];
        orders.forEach((item) => {
          cid.push(item.id);
        });
        let uid = this.uid;
        cid = cid.toString();
        let res = await common.setUnavailableGoods(uid, cid);
        let { status, message } = res;
        if (status) {
          this.closeLayer();
          this.successNotice('保存成功');
          this.$refs.table.fetchData();
        } else {
          this.errorNotice({
            title: '保存失败',
            desc: message ? message : '保存失败',
          });
        }
      }
    },
    closeLayer() {
      this.userNewGoodsActive = false;
    },
    exportDisabledGoods() {
      const opt = this.$refs.table.getParams()
      let params = {
        user_id: this.uid,
        searchKey: opt.searchKey,
        category_id: opt.category_id,
        category_id2: opt.category_id2,
        category_id3: opt.category_id3,
        rece_style_id: this.$route.query.receStyleId,
      };
      common.disabledCommodityExport(params).then((res) => {
        let { status, message } = res;
        if (status) {
          window.location.href = res.data.url;
        } else {
          this.errorNotice({
            title: '导出失败',
            desc: message || '导出失败!',
          });
        }
      });
    },
     onChangeMode() {
        this.$Modal.confirm({
				content: '确认切换屏蔽模式？',
				onOk: async () => {
          await this.setSwitchType();
          if (this.chooseType === 1) {
            this.getTransferData();
          } else {
            this.$refs.table.fetchData()
          }
				},
				onCancel: () => {
          this.mode = this.hasBlockProductRadio;
					return false
				}
			})
    },
    getModeText() {
      this.$request
        .get(this.apiUrl.disableModesDesc, { user_id: this.uid })
        .then((res) => {
          this.modeDesc = res.data;
          this.modeList = [
            {
              label: res.data.desc,
              value: 0,
            },
            {
              label: '独立指定商品屏蔽',
              value: 1,
            },
            {
              label: '独立指定商品售卖',
              value: 2,
            },
          ];
        });
    },
    getMode() {
      this.$request
        .get(this.apiUrl.disableMode, { user_id: this.uid, style: 0 })
        .then((res) => {
          this.mode = +res.data;
        });
    },
    checkedChange() {
      this.checkedAll = !this.checkedAll
      this.$refs.table.$refs.table.handleToggleCheckAll(true)
      if (this.checkedAll){
        if (this.$refs.table.curPagination.total){
          this.extraSelectionCount = this.$refs.table.curPagination.total - this.$refs.table.curPagination.pageSize
          if(this.$refs.table.data.length < this.$refs.table.curPagination.pageSize){
            this.extraSelectionCount = this.$refs.table.curPagination.total - this.$refs.table.data.length
          }
          let params =  this.$refs.table.getParams()
          params.is_only_ids = 1
          params.user_id = params.uid
          this.$request.post(this.apiUrl.getUnavailableGoods, params).then(res=>{
            this.allIds = res.data
          })
        }else{
          this.allIds = ''
          this.extraSelectionCount = 0
        }
      }else{
       this.allIds = ''
       this.extraSelectionCount = 0
      }
    },
  },
  components: {
    newGoods,
    goodsSelect,
    GoodsListModal,
    SButton,
    ListTable,
  },
};
</script>

<style lang="scss" scoped>
.user-unavailable-goods {
  background-color: #fff;
  .checked_button {
    margin-right: 10px;
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    cursor: pointer;
    color: #fff;
    background: #03ac54;
    padding: 0 5px;
    border-radius: 3px;
  }
}
.basic-info,
.unavailable-goods,
.usual-goods {
  position: relative;
  width: 100%;
  /* iview custom */
  .ivu-form {
    text-align: left;
  }
  .ivu-select-placeholder,
  .ivu-select-selected-value,
  .ivu-input-wrapper {
    text-align: left !important;
    /* width: 380px; */
  }
  .ivu-form .ivu-form-item-label {
    text-align: center;
    font-size: 14px !important;
  }
  .ivu-btn.ivu-btn-primary {
    /*background-color: #03ac54;*/
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}
.special-price {
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}

.user-unavailable-goods-nav {
  /* display: table; */
  text-align: left;
  border-bottom: 1px solid rgba(228, 228, 228, 1);
  background-color: #fff;
}
.user-unavailable-goods-nav ul li {
  display: inline-block;
  margin-right: 5px;
}
.nav-items {
  display: inline-block;
  margin-right: 20px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}
.nav-items + .ivu-badge {
  margin-left: -15px;
  margin-right: 10px;
}
.nav-items:hover {
  cursor: pointer;
}
.user-unavailable-goods-nav .active {
  color: #03ac54;
  border-bottom: 2px solid #03ac54;
}
.new-content {
  opacity: 0;
  margin-left: 10px;
  color: #03ac54;
}

.admin-title {
  margin-top: 20px;
}
.admin-title > span {
  font-size: 14px;
  margin-left: 20px;
  color: #80848f;
}
.operation__item {
  // height: 34px;
  padding: 12px 0;
  text-align: left;
}
.original-input {
  width: 500px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 0;
  text-align: right;
  span {
    margin-right: 18px;
  }
  .ivu-cascader {
    display: inline-block;
  }
}
.original-select {
  @extend .original-input;
  width: 395px;
}
.ivu-select {
  width: 275px !important;
}
.ivu-select-placeholder,
.ivu-select-item {
  text-align: left;
}
.ivu-radio {
  margin-right: 0 !important;
}
.operation-wrap {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: left;
  button {
    margin: 0 10px;
  }
}

.user-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  &--list {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    line-height: 35px;
  }
  /* iview custom */
  .ivu-input-wrapper {
    // float: left;
    margin-right: 10px;
    width: 275px;
  }
  .search-btn {
    /*background-color: #03ac54 !important;*/
  }
  .user-list__left {
    float: left;
  }
  .user-list__right-but {
    padding: 10px 0;
    float: right;
  }
  .ivu-btn {
    font-size: 13px;
  }
}
.user-list__bottom {
  display: flex;
  align-items: center;
  margin-top: 10px;
  .page {
    margin-left: auto;
  }
}
.user-list__select-middle {
  padding: 0 0 10px;
  text-align: left;
}

.upload-module {
  .upload-module__del {
    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
}

.user-account {
  position: relative;
  height: 500px;
  .operation-wrap {
    position: absolute;
    bottom: 10px;
  }
  .account-input {
    padding-left: 30px;
  }
  .original-input {
    text-align: left;
  }
  .ivu-input-wrapper {
    width: 200px;
  }
  .account-unit {
    color: #bbbec4;
  }
}

.ivu-page {
  text-align: right;
}
.ivu-form-item {
  /* display: flex;
  align-items: center; */
  margin-bottom: 5px;
  /* width: 380px; */
  &.blockProductModel {
    margin-bottom: 0;
    margin-top: 10px;
  }
}
.unavailable-goods .ivu-btn.ivu-btn.ivu-btn-primary {
  background-color: #03ac54 !important;
}

$cRed: #ed3f14;
.required-pot {
  color: $cRed;
}
</style>
<style lang="scss">
.category-item {
  display: inline-block;
  overflow: unset;
}
.unavailable-category {
  .ivu-transfer-list .ivu-transfer-list-content-item {
    overflow: inherit;
    white-space: normal;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .ivu-transfer-list-with-footer {
    padding-bottom: 0;
  }
  .ivu-transfer-list-body {
    border-radius: 0 0 6px 6px !important;
  }
  .category-sorter-unshare {
    display: inline-block;
    margin-left: 10px;
    color: #03ac54;
    border: 1px solid #03ac54;
    padding: 0 3px;
    line-height: normal;
    border-radius: 3px;
  }
  .category-sorter {
    margin: -1px 0 0 10px;
    color: #00b3a7;
    font-size: 18px;
  }
}
</style>
