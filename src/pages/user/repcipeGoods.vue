<template>
  <div class="user-repcipe-goods">
    <div class="repcipe-goods">
      <div class="user-list__operation">
        <div class="user-list__select-middle">
          <Select
            @on-change="getRepcipeGoodsSecondCategory"
            class="category first-category"
            v-model="repcipeGoods.firstCategoryId"
            placeholder="一级分类"
          >
            <Option
              v-for="item in firstCategory"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
          <Select
            @on-change="getRepcipeGoods()"
            class="category second-category ml10"
            v-model="repcipeGoods.secondCategoryId"
            placeholder="二级分类"
          >
            <Option
              v-for="item in repcipeGoods.secondCategory"
              :value="item.id"
              :key="item.id"
              >{{ item.name }}</Option
            >
          </Select>
          <i-input
            placeholder="输入商品编码/名称/助记码/别名"
            v-model="repcipeGoods.searchValue"
            class="ml10"
          >
          </i-input>
          <SButton
            @click="getRepcipeGoods"
            class="filter__button"
            type="primary"
            >查询</SButton
          >
        </div>
        <div class="user-list__operation--list">
          <SButton styleType="btnStyleForAdd" @click="showAddGoods"
            >新增</SButton
          >
          <Button class="ml10" @click="downloadTemplate">下载模板</Button>
          <Upload
            :format="['csv', 'xlsx']"
            :on-success="uploadSuccess"
            :show-upload-list="false"
            :action="uploadUrl"
          >
            <Button class="ml10">导入</Button>
          </Upload>
          <Button
            class="ml10"
            :disabled="repcipeGoodsList.length === 0"
            @click="exportRepcipeGoods"
            >导出</Button
          >
          <PopButton
            class="ml10"
            :confirm="true"
            title="确认删除"
            placement="bottom"
            @on-ok="batchDelRepcipeGoods"
            :disabled="!repcipeGoods.checkedGoods.length"
            >批量删除
          </PopButton>
        </div>
      </div>

      <Table
        :columns="columns1"
        :data="repcipeGoodsList"
        :height="layoutTableHeight - 15"
        @on-selection-change="getCheckedrepcipeGoods"
      ></Table>

      <Page
        :total="repcipeGoods.page.count"
        :current.sync="repcipeGoods.page.nowPage"
        :page-size="repcipeGoods.page.pageSize"
        @on-page-size-change="modifyGooodsPage"
        @on-change="getRepcipeGoods"
        class="js-after-table"
        placement="top"
        show-elevator
        show-total
        show-sizer
        style="margin-top: 6px;"
      >
      </Page>
    </div>
    <goods-list-modal
      ref="goodsModal"
      :params="{ filter_raw_recipe_pre_delivery: 1 }"
      v-model="showGoodsListModal"
      modalType="shieldGoods"
      :uid="uid"
      @on-add="handlerAdd"
      :selectedGoods="selectedGoods"
    >
      <div></div>
      <Col slot="extra" style="text-align: right; flex: 1">
        <PopButton
          :confirm="true"
          :title="'确定全部当前筛选的商品？'"
          type="primary"
          @on-ok="handleAddAll"
        >
          全部筛选商品
        </PopButton>
      </Col>
    </goods-list-modal>
  </div>
</template>
<script>
import { mapState } from 'vuex';
import goods from '@api/goods.js';
import GoodsListModal from '@components/order/goodsListModal';
import LayoutMixin from '@/mixins/layout';
import SButton from '@components/button';
import ConfigMixin from '@/mixins/config';

export default {
  name: 'repcipeGoods',
  mixins: [LayoutMixin, ConfigMixin],
  data() {
    return {
      hasRadio: true,
      transferData: [],
      selectTransferData: [],
      chooseType: 0,
      showGoodsListModal: false,
      columns1: [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select'
        },
        {
          title: '图片',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left'
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left'
        },
        {
          title: '商品分类',
          key: 'category2',
          align: 'left',
          render(h, params) {
            return h('span', params.row.category1 + '/' +params.row.category2)
          }
        },
        {
          title: '单位',
          key: 'unit',
          align: 'left'
        },
        {
          title: '描述',
          key: 'summary',
          align: 'left'
        },
        {
          title: '别名',
          key: 'alias',
          align: 'left'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            let data = params.row,
              id = data.id;

            return h('div', [
              h(
                'PopButton',
                {
                  class: {
                    'table-button': true
                  },
                  props: {
                    confirm: true,
                    title: '确定删除？',
                    type: 'error',
                    size: 'small'
                  },
                  on: {
                    'on-ok': () => {
                      this.delRepcipeGoods(id);
                    }
                  }
                },
                '删除'
              )
            ]);
          }
        }
      ],
      addrepcipeGoods: {
        goodsFilter: {},
        checkedAddGoods: []
      },
      userNewGoodsActive: false,
      uid: '', // 用户详情的ID
      addAllGoods: false,
      repcipeGoodsList: [],
      repcipeGoods: {
        checkedGoods: [],
        pageSize: '',
        searchValue: '',
        firstCategoryId: '',
        secondCategory: [],
        secondCategoryId: '',
        page: {
          count: 0,
          nowPage: 1,
          pageSize: 10
        }
      },
      firstCategory: [],
      checkGoods: '',
      mode: 0,
      uploadUrl: `/superAdmin/userSuper/importPreDelivery?style=0&user_id=${this.$route.query.uid}`
    };
  },
  created() {
    this.uid = this.$route.query.uid;
    this.getRepcipeGoods();
    this.getFirstCategory();
  },
  computed: {
    ...mapState({}),
    selectedGoods() {
      return this.repcipeGoodsList.map(item => {
        return {
          commodity_id: item.commodity_id
        };
      });
    }
  },
  methods: {
    async addDisabledCategory(newTargetKeys) {
      // 找出新增的分类
      const addCategory = newTargetKeys.filter(item => {
        return this.selectTransferData.indexOf(item) === -1;
      });
      await this.$request.get(this.apiUrl.addPreDeliveryCommodity, {
        user_id: this.uid,
        commodity_id: addCategory.join(',')
      });
    },
    uploadSuccess(response) {
      if (response.status == 0) {
        this.$Notice.error({
          title: response.message
        });
      } else {
        this.$Notice.success({
          title: `处理完成，${response.data.success_count}个商品导入成功，${response.data.failure_count}个商品导入失败`// '导入成功，本次导入' + response.data + '个商品'
        });
        this.getRepcipeGoods();
        this.getFirstCategory();
      }
    },
    downloadTemplate() {
      this.$request.get(this.apiUrl.importPreDeliveryTemplate).then(res => {
        if (res.status) {
          window.location.href = res.data;
        } else {
          this.errorNotice({
            title: '下载模板失败',
            desc: res.message || '下载模板失败!'
          });
        }
      });
    },
    /**
     * @description 新增商品选择发生变化处理函数
     * @param checkedData 新选择的商品 {ids:, detail}
     */
    selectGoods(checkedData) {
      this.addrepcipeGoods.checkedAddGoods = checkedData.ids;
    },
    async getFirstCategory() {
      let res = await goods.getGoodsCategory();
      let defaultItem = {
        id: '',
        name: '一级分类'
      };
      if (res.status) {
        this.firstCategory = res.data;
      } else {
        this.firstCategory = [];
      }
      this.firstCategory.unshift(defaultItem);
    },
    async getRepcipeGoodsSecondCategory() {
      let defaultItem = {
        id: '',
        name: '二级分类'
      };
      let secondCategory = [];
      let res = await goods.getGoodsCategory(this.repcipeGoods.firstCategoryId);
      if (res.status) {
        secondCategory = res.data;
      }
      secondCategory.unshift(defaultItem);
      this.repcipeGoods.secondCategory = secondCategory;
      this.getRepcipeGoods();
    },
    async modifyGooodsPage() {
      this.repcipeGoods.page.pageSize = arguments[0];
      this.getRepcipeGoods();
    },
    getRepcipeGoodsParam() {
      return {
        user_id: this.uid,
        search_value: this.repcipeGoods.searchValue,
        category_id: this.repcipeGoods.firstCategoryId,
        category_id2: this.repcipeGoods.secondCategoryId,
        page: this.repcipeGoods.page.nowPage,
        pageSize: this.repcipeGoods.page.pageSize
      };
    },
    async getRepcipeGoods() {
      let params = this.getRepcipeGoodsParam();
      let res = await this.$request.get(this.apiUrl.preDeliverylist, params);
      this.repcipeGoods.checkedGoods = '';
      if (res.status) {
        let data = res.data.list;
        let pageParams = res.data.pageParams;
        this.repcipeGoodsList = data;
        // this.setDisabledGoods();
        this.repcipeGoods.page.count = parseInt(pageParams.count);
      }
    },
    /**
     * @description 批量删除不可用商品
     */
    batchDelRepcipeGoods() {
      let goodsIds = this.repcipeGoods.checkedGoods;
      if (!goodsIds || goodsIds.length === 0) {
        this.errorNotice('请选择需要删除的商品');
        return false;
      }
      this.delRepcipeGoods(goodsIds);
    },
    /**
     * @param goodsIds 商品id，以','隔开，如：1,23,333
     */
    async delRepcipeGoods(goodsIds) {
      let res = await this.$request.post(this.apiUrl.deletePreDelivery, {
        ids: goodsIds
      });
      let { status, message } = res;
      if (status) {
        this.successNotice(message || '删除成功');
        this.getRepcipeGoods();
      } else {
        this.errorNotice({
          title: goodsIds.split(',').length > 1 ? '批量删除失败' : '删除失败',
          desc: message ? message : '删除失败'
        });
      }
    },
    /**
     * @description 获取选中的不可用商品
     */
    getCheckedrepcipeGoods() {
      let value = arguments[0],
        array = [];

      if (Array.isArray(value)) {
        // eslint-disable-next-line no-unused-vars
        array = value.map(function(value, index, array) {
          return value.id;
        });
      }
      this.repcipeGoods.checkedGoods = array.join();
    },
    showAddGoods() {
      this.showGoodsListModal = true;
    },
    async handleAddAll() {
      let filters = this.$refs.goodsModal.getFiltersAll();
      console.log(filters);
      const res = await this.$request.get(this.apiUrl.getGoodsList, {
        ...filters,
        need_only_id: 1
      });
      this.$request
        .post(this.apiUrl.addPreDeliveryCommodity, {
          user_id: this.uid,
          commodity_id: res.data
        })
        .then(res => {
          let { status, message } = res;
          if (status) {
            this.showGoodsListModal = false;
            this.successNotice('全部筛选商品成功');
            this.getRepcipeGoods();
          } else {
            this.errorNotice({
              title: '全部筛选商品失败',
              desc: message ? message : '全部筛选商品失败'
            });
          }
        });
    },
    async handlerAdd(orders) {
      // 添加当前页勾选的商品
      if (orders) {
        let cid = [];
        orders.forEach(item => {
          cid.push(item.id);
        });
        let uid = this.uid;
        cid = cid.toString();
        let res = await this.$request.post(
          this.apiUrl.addPreDeliveryCommodity,
          { user_id: uid, commodity_id: cid }
        );
        let { status, message } = res;
        if (status) {
          this.successNotice('保存成功');
          this.getRepcipeGoods();
        } else {
          this.errorNotice({
            title: '保存失败',
            desc: message ? message : '保存失败'
          });
        }
      }
    },
    exportRepcipeGoods() {
      let params = {
        user_id: this.uid,
        search_value: this.repcipeGoods.searchValue,
        category_id: this.repcipeGoods.firstCategoryId,
        category_id2: this.repcipeGoods.secondCategoryId
      };
      this.$request.get(this.apiUrl.exportPreDelivery, params).then(res => {
        let { status, message } = res;
        if (status) {
          window.location.href = res.data;
        } else {
          this.errorNotice({
            title: '导出失败',
            desc: message || '导出失败!'
          });
        }
      });
    }
  },
  components: {
    GoodsListModal,
    SButton
  }
};
</script>

<style lang="scss" scoped>
.user-repcipe-goods {
  background-color: #fff;
}
.basic-info,
.repcipe-goods,
.usual-goods {
  position: relative;
  width: 100%;
  /* iview custom */
  .ivu-form {
    text-align: left;
  }
  .ivu-select-placeholder,
  .ivu-select-selected-value,
  .ivu-input-wrapper {
    text-align: left !important;
    /* width: 380px; */
  }
  .ivu-form .ivu-form-item-label {
    text-align: center;
    font-size: 14px !important;
  }
  .ivu-btn.ivu-btn-primary {
    /*background-color: #03ac54;*/
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}
.special-price {
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}

.user-repcipe-goods-nav {
  /* display: table; */
  text-align: left;
  border-bottom: 1px solid rgba(228, 228, 228, 1);
  background-color: #fff;
}
.user-repcipe-goods-nav ul li {
  display: inline-block;
  margin-right: 5px;
}
.nav-items {
  display: inline-block;
  margin-right: 20px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}
.nav-items + .ivu-badge {
  margin-left: -15px;
  margin-right: 10px;
}
.nav-items:hover {
  cursor: pointer;
}
.user-repcipe-goods-nav .active {
  color: #03ac54;
  border-bottom: 2px solid #03ac54;
}
.new-content {
  opacity: 0;
  margin-left: 10px;
  color: #03ac54;
}

.admin-title {
  margin-top: 20px;
}
.admin-title > span {
  font-size: 14px;
  margin-left: 20px;
  color: #80848f;
}
.operation__item {
  // height: 34px;
  padding: 12px 0;
  text-align: left;
}
.original-input {
  width: 500px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 0;
  text-align: right;
  span {
    margin-right: 18px;
  }
  .ivu-cascader {
    display: inline-block;
  }
}
.original-select {
  @extend .original-input;
  width: 395px;
}
.ivu-select {
  width: 275px !important;
}
.ivu-select-placeholder,
.ivu-select-item {
  text-align: left;
}
.ivu-radio {
  margin-right: 0 !important;
}
.operation-wrap {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: left;
  button {
    margin: 0 10px;
  }
}

.user-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  &--list {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    line-height: 35px;
  }
  /* iview custom */
  .ivu-input-wrapper {
    // float: left;
    margin-right: 10px;
    width: 275px;
  }
  .search-btn {
    /*background-color: #03ac54 !important;*/
  }
  .user-list__left {
    float: left;
  }
  .user-list__right-but {
    padding: 10px 0;
    float: right;
  }
  .ivu-btn {
    font-size: 13px;
  }
}
.user-list__select-middle {
  // margin-left: 10px;
  padding: 10px 0;
  text-align: left;
}

.upload-module {
  .upload-module__del {
    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
}

.ivu-page {
  text-align: right;
}
.ivu-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  /* width: 380px; */
  &.blockProductModel {
    margin-bottom: 0;
    margin-top: 10px;
  }
}
.repcipe-goods .ivu-btn.ivu-btn.ivu-btn-primary {
  background-color: #03ac54 !important;
}

$cRed: #ed3f14;
.required-pot {
  color: $cRed;
}
</style>
<style lang="scss">
.user-repcipe-goods {
  .ivu-form-item-content {
    margin-left: 0 !important;
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }

  .payment-type-default {
    pre {
      padding-left: 30px;
      text-align: left;
    }
  }
}
.payment-list {
  .ivu-input-number {
    width: 200px;
  }
}
.ivu-cascader {
  display: inline-block;
}
.ivu-select-selected-value {
  text-align: left;
}
.ivu-select-placeholder {
  text-align: left;
}

.category.first-category,
.category.second-category {
  width: 120px !important;
}
.category-item {
  display: inline-block;
  overflow: unset;
}
.unavailable-category {
  .ivu-transfer-list .ivu-transfer-list-content-item {
    overflow: inherit;
    white-space: normal;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
  }
  .ivu-transfer-list-with-footer {
    padding-bottom: 0;
  }
  .ivu-transfer-list-body {
    border-radius: 0 0 6px 6px !important;
  }
  .category-sorter-unshare {
    display: inline-block;
    margin-left: 10px;
    color: #03ac54;
    border: 1px solid #03ac54;
    padding: 0 3px;
    line-height: normal;
    border-radius: 3px;
  }
  .category-sorter {
    margin: -1px 0 0 10px;
    color: #00b3a7;
    font-size: 18px;
  }
}
</style>
