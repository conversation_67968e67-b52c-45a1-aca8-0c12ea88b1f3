<template>
  <div class="user-unavailable-goods">
    <DetailPage
      pageType="add"
      :showFooter="false"
      title="屏蔽/售卖商品"
    >
      <Form :label-width="100">
        <FormItem label="屏蔽商品模式" class="blockProductModel">
          <RadioGroup v-model="mode" @on-change="onChangeMode">
            <Radio
              v-for="item in modeList"
              :key="item.value"
              :true-value="item.value"
              :label="item.value"
            >{{ item.label }}
              <Tooltip :content="item.tooltip" v-if="item.tooltip" max-width="400">
                <i class="icon-help1 iconfont icontip" style="font-size: 13px;"></i>
              </Tooltip>
            </Radio>
          </RadioGroup>
        </FormItem>
        <FormItem label="商品选择模式">
          <RadioGroup  :value="chooseType" v-if="hasRadio">
            <Radio
              v-for="item in chooseTypeList"
              @click.native="onChangeChooseType(item.value)"
              :key="item.value"
              :true-value="item.value"
              :label="item.value"
            >{{ item.label }}</Radio>
          </RadioGroup>
        </FormItem>
      </Form>
      <!-- 不可用商品 -->
      <div class="unavailable-goods" v-show="chooseType === 0">
        <div class="user-list__operation">
          <div class="user-list__select-middle">
            <CategorySelect
              :noThree="!isOpenThreeCategory"
              v-model="categoryData"
              style="display: inline-block;width: 200px"
              @on-change="getUnavailableGoods"
            />
            <i-input
              placeholder="输入商品编码/名称/助记码/别名"
              v-model="unavailableGoods.searchValue"
              class="ml10"
            >
            </i-input>
            <SButton @click="getUnavailableGoods" class="filter__button" type="primary">查询</SButton>
          </div>
          <div class="user-list__operation--list">
            <SButton styleType="btnStyleForAdd" @click="showAddGoods">新增</SButton>
            <Button class="ml10" @click="downloadTemplate">下载模板</Button>
            <Upload
              :format="['csv', 'xlsx']"
              :on-success="uploadSuccess"
              :show-upload-list="false"
              :action="
              `/superAdmin/userSuper/ImportDisabledCommodity?style=4&user_id=${uid}`
            "
            >
              <Button class="ml10"> 导入</Button>
            </Upload>
            <Button class="ml10" :disabled="unavailableGoodsList.length === 0" @click="exportDisabledGoods">导出</Button>
            <PopButton
              class="ml10"
              :confirm="true"
              title="确认删除"
              placement="bottom"
              @on-ok="batchDelUnavailableGoods"
            >批量删除</PopButton
            >
          </div>
        </div>

        <i-table
          :columns="columns1"
          :data="unavailableGoodsList"
          :height="layoutTableHeight - 10"
          @on-selection-change="getCheckedUnavailableGoods"
        ></i-table>

        <Page
          :total="unavailableGoods.page.count"
          :current.sync="unavailableGoods.page.nowPage"
          :page-size="unavailableGoods.page.pageSize"
          @on-page-size-change="modifyUnavailableGooodsPage"
          @on-change="getUnavailableGoods"
          class="js-after-table"
          placement="top"
          show-elevator
          show-total
          show-sizer
        >
        </Page>
      </div>
      <div class="unavailable-category" v-show="chooseType === 1">
        <Transfer
          :list-style="{
					width: '400px',
					height: '580px'
				}"
          :titles="['未选分类', '已选分类']"
          @on-change="handleChange"
          :filter-method="filterMethod"
          :data="transferData"
          :target-keys="selectTransferData"
          filterable
          :render-format="categoryRender"
        ></Transfer>
      </div>
      <goods-list-modal
        ref="goodsModal"
        :params="goodsParams"
        v-model="showGoodsListModal"
        modalType="shieldGoods"
        @on-add="handlerAdd"
        :selectedGoods="selectedGoods"
        :showUsed="false"
      >
        <div></div>
      </goods-list-modal>
    </DetailPage>
  </div>
</template>
<script>
import { mapState } from 'vuex';
// eslint-disable-next-line no-unused-vars
import { mapMutations } from 'vuex';
import common from '@api/user.js';
import goods from '@api/goods.js';
// eslint-disable-next-line no-unused-vars
import { api } from '@api/api.js';
// eslint-disable-next-line no-unused-vars
import { get, post } from '@api/request';
// eslint-disable-next-line no-unused-vars
import Bus from '@api/bus.js';
import newGoods from '@components/user/UserNewGoods';
import goodsSelect from '@components/common/goodsSelect';
import GoodsListModal from '@components/order/goodsListModal';
import LayoutMixin from '@/mixins/layout';
import SButton from '@components/button';
import ConfigMixin from '@/mixins/config';
import user from '@/api/user';
import DetailPage from "@/components/detail-page";
import CategorySelect from '@components/common/categorySelect';

const chooseTypeList = [
  {
    label: '按商品',
    value: 0
  },
  {
    label: '按分类',
    value: 1
  }
];

const modeList = [
  {
    label: '跟随系统配置',
    value: 0,
  },
  {
    label: '独立指定商品屏蔽',
    value: 1,
  },
  {
    label: '独立指定商品售卖',
    value: 2,
  }
]

export default {
  name: 'NewUser',
  mixins: [LayoutMixin, ConfigMixin],
  data() {
    return {
      hasBlockProductRadio: 0, // 用于还原屏蔽商品模式 radio
			hasRadio: true,
			transferData: [],
			selectTransferData: [],
      chooseTypeList,
      chooseType: 0,
      showGoodsListModal: false,
      columns1: [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select'
        },
        {
          title: '图片',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left'
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left'
        },
        {
          title: '商品分类',
          key: 'category2',
          align: 'left',
          render: (h, params) => {
            const textArr = [params.row.category_name, params.row.category_name2];
            console.log(textArr)
            if (this.isOpenThreeCategory) {
              textArr.push(params.row.category_name3);
            }
            return h('div', textArr.filter((item) => item).join('/'));
          },
        },
        {
          title: '单位',
          key: 'unit',
          align: 'left'
        },
        {
          title: '描述',
          key: 'summary',
          align: 'left'
        },
        {
          title: '别名',
          key: 'alias',
          align: 'left'
        },
        {
          title: '操作',
          key: 'action',
          width: 200,
          align: 'center',
          render: (h, params) => {
            let data = params.row,
              id = data.id;

            return h('div', [
              h(
                'PopButton',
                {
                  class: {
                    'table-button': true
                  },
                  props: {
                    confirm: true,
                    title: '确定删除？',
                    type: 'error',
                    size: 'small'
                  },
                  on: {
                    'on-ok': () => {
                      this.delUnavailableGoods(id);
                    }
                  }
                },
                '删除'
              )
            ]);
          }
        }
      ],
      addUnavailableGoods: {
        goodsFilter: {},
        checkedAddGoods: []
      },
      account: '',
      password: '',
      userInfo: {
        account: '',
        password: '',
        name: '',
        code: '',
        type: '0',
        group: '0',
        sales: '0',
        status: '2',
        area: '',
        contact: '',
        tel: '',
        address: '',
        deliveryDate: '',
        printTemplate: ''
      },
      navActive: 0,
      userNewGoodsActive: false,
      style: 4,
      uid: '', // 用户详情的ID
      addAllGoods: false,
      unavailableGoodsList: [],
      unavailableGoods: {
        checkedGoods: [],
        pageSize: '',
        searchValue: '',
        firstCategoryId: '',
        secondCategory: [],
        secondCategoryId: '',
        page: {
          count: 0,
          nowPage: 1,
          pageSize: 10
        }
      },
      firstCategory: [],
      categoryData: [],
      checkGoods: '',
      modeList,
      mode: 0
    };
  },
  created() {
    // 注意！！！这里屏蔽/售卖商品从【客户编辑-屏蔽/售卖商品】那copy过来的，有问题可以查看那边问题
    this.uid = this.$route.query.uid;
    this.mode = +this.$route.query.disableMode || 0;
    this.chooseType = +this.$route.query.disableType || 0;
    this.receStyleId= this.$route.query.receStyleId;
		if (this.chooseType === 1) {
			this.getTransferData();
		} else {
			this.getUnavailableGoods();
			this.getFirstCategory();
		}
  },
  watch: {
    mode(newVal, oldVal) {
       this.hasBlockProductRadio = oldVal
    }
  },
  computed: {
    ...mapState({}),
    selectedGoods () {
      return this.unavailableGoodsList.map(item => {
        return {
          commodity_id: item.commodity_id
        }
      });
    },
    goodsParams() {
      return {
        disable_style_id: this.uid,
        disable_style: this.style,
        disable_add: 1,
      }
    }
  },
  methods: {
		async addDisabledCategory(newTargetKeys) {
			// 找出新增的分类
			const addCategory = newTargetKeys.filter(item => {
				return this.selectTransferData.indexOf(item) === -1
			});
			const res = await user.setUnavailableGoods(this.uid, addCategory.join(','), 1, 4);
		},
    async updateModeAndType() {
      await get(api.setUnavailableType, {
        mode: this.mode,
        type: this.chooseType,
        id: this.uid,
        style: this.style,
      }).then(() => {
        this.$router.push({
          path: this.$route.path,
          query: {
            ...this.$route.query,
            uid: this.$route.query.uid,
            disableType: this.chooseType,
            disableMode: this.mode,
          }
        })
      });
    },
		onChangeChooseType(value) {
			this.$Modal.confirm({
				content: '确认切换商品选择模式？',
				onOk: () => {
					this.chooseType = value
          this.updateModeAndType()
					if (value === 1) this.getTransferData()
					else {
						this.getUnavailableGoods();
						this.getFirstCategory();
					}
				},
				onCancel: () => {
					// 重置radio
					this.hasRadio = false
					setTimeout(() => {
						this.hasRadio = true
					}, 10)
					return false
				}
			})
		},
		// 获取所有可屏蔽分类和已屏蔽分类
		async getTransferData () {
			const res = await get(api.getUnavailableGoodsByGroupArea, {
				id: this.uid,
				style: this.style
			})
			if (res.status) {
				res.data.list.forEach(item => {
					item.key = item.category_id
					item.label = item.category_name
				})
				res.data.selected_list && res.data.selected_list.forEach(item => {
					item.key = item.category_id
					item.label = item.category_name
				})
				this.transferData = [...res.data.list, ...res.data.selected_list]
				this.selectTransferData = res.data.selected_list.map(item => item.key)
			}
		},
		async handleChange (newTargetKeys) {
			// 增加和取消的情况
			if (newTargetKeys.length > this.selectTransferData.length) {
				await this.addDisabledCategory(newTargetKeys)
			} else {
				// 删除的情况, 获取selectTransferData中被删除的分类
				const delCategoryKey = this.selectTransferData.filter(item => {
					return newTargetKeys.indexOf(item) === -1
				});
				// 根据key去transferData中找到对应的分类
				const delCategory = this.transferData.filter(item => {
					return delCategoryKey.indexOf(item.key) !== -1
				});
				await user.delUnavailableCategories(delCategory, 1)
			}
			await this.getTransferData()
			this.$Message.success('操作成功')
		},
		filterMethod(data, query) {
			if (data.label === null) {
				return true;
			}
			return data.label.indexOf(query) > -1;
		},
    categoryRender(item) {
      if (item.is_selected) {
        return `<span title="${item.category_name}" class="category-item" style="line-height: 2.3;"><span>${item.category_name ? item.category_name : ''}</span><i class="sui-icon icon-yifenpei category-sorter"></i></span>`;
      } else {
        return `<div title="${item.category_name}" class="category-item" style="line-height: 2.3;">${item.category_name ? item.category_name : ''}<div class="category-sorter-unshare">未分配</div></div>`;
      }
    },
    uploadSuccess(response) {
      if (response.status == 0) {
        this.$Notice.error({
          title: response.message
        });
      } else {
        this.$Notice.success({
          title: '导入成功，本次导入' + response.data + '个商品'
        });
        this.getUnavailableGoods();
        this.getFirstCategory();
      }
    },
    downloadTemplate() {
      window.location.href =
        '/superAdmin/userSuper/ImportDisabledCommodityTemplate';
    },
    /**
     * @description 新增商品选择发生变化处理函数
     * @param checkedData 新选择的商品 {ids:, detail}
     */
    selectGoods(checkedData) {
      this.addUnavailableGoods.checkedAddGoods = checkedData.ids;
    },
    /**
     * @description 获取一级分类
     * <AUTHOR>
     */
    async getFirstCategory() {
      let res = await goods.getGoodsCategory();
      let defaultItem = {
        id: '',
        name: '一级分类'
      };
      if (res.status) {
        this.firstCategory = res.data;
      } else {
        this.firstCategory = [];
      }
      this.firstCategory.unshift(defaultItem);
    },
    /**
     * @description 获取常用商品二级分类
     * <AUTHOR>
     */
    async getUnavailableGoodsSecondCategory() {
      let defaultItem = {
        id: '',
        name: '二级分类'
      };
      let secondCategory = [];
      let res = await goods.getGoodsCategory(
        this.unavailableGoods.firstCategoryId
      );
      if (res.status) {
        secondCategory = res.data;
      }
      secondCategory.unshift(defaultItem);
      this.unavailableGoods.secondCategory = secondCategory;
      this.getUnavailableGoods();
    },
    async modifyUnavailableGooodsPage() {
      this.unavailableGoods.page.pageSize = arguments[0];
      this.getUnavailableGoods();
    },
    getUnavailableGoodsParam() {
      let value = {
        id: this.uid,
        style: 4,
        search_value: this.unavailableGoods.searchValue,
        category_id: this.categoryData[0],
        category_id2: this.categoryData[1],
        category_id3: this.categoryData[2],
        page: this.unavailableGoods.page.nowPage,
        pageSize: this.unavailableGoods.page.pageSize
      };

      return value;
    },
    async getUnavailableGoods() {
      let value = this.getUnavailableGoodsParam();
      let res = await this.$request.get('/superAdmin/commodityDisabled/disabledList', value);
      this.unavailableGoods.checkedGoods = '';
      if (res.status) {
        let data = res.data.list;
        let pageParams = res.data.pageParams;
        this.unavailableGoodsList = data;
        // this.setDisabledGoods();
        this.unavailableGoods.page.count = parseInt(pageParams.count);
      }
    },
    /**
     * @description 批量删除不可用商品
     * <AUTHOR>
     */
    batchDelUnavailableGoods() {
      let goodsIds = this.unavailableGoods.checkedGoods;
      if (!goodsIds || goodsIds.length === 0) {
        this.errorNotice('请选择需要删除的商品');
        return false;
      }
      this.delUnavailableGoods(goodsIds);
    },
    /**
     * @description 删除不可用商品
     * <AUTHOR>
     * @param goodsIds 商品id，以','隔开，如：1,23,333
     */
    async delUnavailableGoods(goodsIds) {
      let res = await common.delUnavailableGoods({ cid: goodsIds });
      let { status, message } = res;
      if (status) {
        this.successNotice(message || '删除成功');
        this.getUnavailableGoods();
      } else {
        this.errorNotice({
          title: goodsIds.split(',').length > 1 ? '批量删除失败' : '删除失败',
          desc: message ? message : '删除失败'
        });
      }
    },
    cancel() {
      history.go(-1);
    },
    /**
     * @description 获取选中的不可用商品
     * <AUTHOR>
     */
    getCheckedUnavailableGoods() {
      let value = arguments[0],
        array = [];

      if (Array.isArray(value)) {
        // eslint-disable-next-line no-unused-vars
        array = value.map(function(value, index, array) {
          return value.id;
        });
      }
      this.unavailableGoods.checkedGoods = array.join();
    },
    showAddGoods() {
      this.showGoodsListModal = true;
    },
    handleAddAll() {
      const label = +this.commodity_disabled_mode === 2 ? '售卖' : '屏蔽'

      let filters = this.$refs.goodsModal.getFilters();
      let params = filters;
      params || (params = {});
      params.user_id = this.uid;
      common.addDisabledGoods(params).then(res => {
        let { status, message } = res;
        if (status) {
          this.showGoodsListModal = false;
          this.closeLayer();
          this.successNotice(label + '筛选商品成功');
          this.getUnavailableGoods();
        } else {
          this.errorNotice({
            title: label + '筛选商品失败',
            desc: message ? message : label + '筛选商品失败'
          });
        }
      });
    },
    async handlerAdd(orders) {
      // 添加当前页勾选的商品
      if (orders) {
        let cid = [];
        orders.forEach(item => {
          cid.push(item.id);
        });
        let uid = this.uid;
        cid = cid.toString();
        let res = await common.setUnavailableGoods(uid, cid, 0, 4);
        let { status, message } = res;
        if (status) {
          this.closeLayer();
          this.successNotice('保存成功');
          this.getUnavailableGoods();
        } else {
          this.errorNotice({
            title: '保存失败',
            desc: message ? message : '保存失败'
          });
        }
      }
    },
    closeLayer() {
      this.userNewGoodsActive = false;
    },
    exportDisabledGoods() {
      let params = {
        id: this.uid,
        search_value: this.unavailableGoods.searchValue,
        category_id: this.categoryData[0],
        category_id2: this.categoryData[1],
        category_id3: this.categoryData[2],
        style: 4,
      };
      this.$request.get(api.areaAndGroupDisabledCommodityExport, params).then(res => {
        let { status, message } = res;
        if (status) {
          window.location.href = res.data;
        } else {
          this.errorNotice({
            title: '导出失败',
            desc: message || '导出失败!'
          });
        }
      })
    },
      onChangeMode() {
      this.$Modal.confirm({
				content: '确认切换屏蔽模式？',
				onOk: async () => {
          await this.updateModeAndType();
          this.unavailableGoods.page.nowPage = 1
          if (this.chooseType === 1) {
            this.getTransferData()
          } else {
            this.getUnavailableGoods();
            this.getFirstCategory();
          }
				},
				onCancel: () => {
          this.mode = this.hasBlockProductRadio;
					return false
				}
			})
     
    },
  },
  components: {
    DetailPage,
    // eslint-disable-next-line vue/no-unused-components
    newGoods,
    // eslint-disable-next-line vue/no-unused-components
    goodsSelect,
    GoodsListModal,
    SButton,
    CategorySelect,
  }
};
</script>

<style lang="scss" scoped>
.user-unavailable-goods {
  background-color: #fff;
}
.basic-info,
.unavailable-goods,
.usual-goods {
  position: relative;
  width: 100%;
  /* iview custom */
  .ivu-form {
    text-align: left;
  }
  .ivu-select-placeholder,
  .ivu-select-selected-value,
  .ivu-input-wrapper {
    text-align: left !important;
    /* width: 380px; */
  }
  .ivu-form .ivu-form-item-label {
    text-align: center;
    font-size: 14px !important;
  }
  .ivu-btn.ivu-btn-primary {
    /*background-color: #03ac54;*/
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}
.special-price {
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }
}

.user-unavailable-goods-nav {
  /* display: table; */
  text-align: left;
  border-bottom: 1px solid rgba(228, 228, 228, 1);
  background-color: #fff;
}
.user-unavailable-goods-nav ul li {
  display: inline-block;
  margin-right: 5px;
}
.nav-items {
  display: inline-block;
  margin-right: 20px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}
.nav-items + .ivu-badge {
  margin-left: -15px;
  margin-right: 10px;
}
.nav-items:hover {
  cursor: pointer;
}
.user-unavailable-goods-nav .active {
  color: #03ac54;
  border-bottom: 2px solid #03ac54;
}
.new-content {
  opacity: 0;
  margin-left: 10px;
  color: #03ac54;
}

.admin-title {
  margin-top: 20px;
}
.admin-title > span {
  font-size: 14px;
  margin-left: 20px;
  color: #80848f;
}
.operation__item {
  // height: 34px;
  padding: 12px 0;
  text-align: left;
}
.original-input {
  width: 500px;
  height: 32px;
  line-height: 32px;
  display: inline-block;
  margin-right: 20px;
  margin-bottom: 0;
  text-align: right;
  span {
    margin-right: 18px;
  }
  .ivu-cascader {
    display: inline-block;
  }
}
.original-select {
  @extend .original-input;
  width: 395px;
}
.ivu-select {
  width: 275px !important;
}
.ivu-select-placeholder,
.ivu-select-item {
  text-align: left;
}
.ivu-radio {
  margin-right: 0 !important;
}
.operation-wrap {
  width: 100%;
  height: 50px;
  line-height: 50px;
  text-align: left;
  button {
    margin: 0 10px;
  }
}

.user-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  &--list {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    line-height: 35px;
  }
  /* iview custom */
  .ivu-input-wrapper {
    // float: left;
    margin-right: 10px;
    width: 275px;
  }
  .search-btn {
    /*background-color: #03ac54 !important;*/
  }
  .user-list__left {
    float: left;
  }
  .user-list__right-but {
    padding: 10px 0;
    float: right;
  }
  .ivu-btn {
    font-size: 13px;
  }
}
.user-list__select-middle {
  // margin-left: 10px;
  padding: 10px 0;
  text-align: left;
}

.upload-module {
  .upload-module__del {
    margin-right: 10px;
    &:hover {
      cursor: pointer;
    }
  }
}

.user-account {
  position: relative;
  height: 500px;
  .operation-wrap {
    position: absolute;
    bottom: 10px;
  }
  .account-input {
    padding-left: 30px;
  }
  .original-input {
    text-align: left;
  }
  .ivu-input-wrapper {
    width: 200px;
  }
  .account-unit {
    color: #bbbec4;
  }
}

.ivu-page {
  text-align: right;
}
.ivu-form-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  /* width: 380px; */
  &.blockProductModel {
    margin-bottom: 0;
    margin-top: 10px;
  }
}
.unavailable-goods .ivu-btn.ivu-btn.ivu-btn-primary {
  background-color: #03ac54 !important;
}

$cRed: #ed3f14;
.required-pot {
  color: $cRed;
}
</style>
<style lang="scss">
.user-unavailable-goods {
  .ivu-form-item-content {
    margin-left: 0 !important;
  }
  .ivu-icon-ios-search {
    font-size: 20px;
    color: #fff;
  }

  .payment-type-default {
    pre {
      padding-left: 30px;
      text-align: left;
    }
  }
}
.payment-list {
  .ivu-input-number {
    width: 200px;
  }
}
.ivu-cascader {
  display: inline-block;
}
.ivu-select-selected-value {
  text-align: left;
}
.ivu-select-placeholder {
  text-align: left;
}

.category.first-category,
.category.second-category {
  width: 120px !important;
}
.category-item {
	display: inline-block;
	overflow: unset;
}
.unavailable-category {
	.ivu-transfer-list .ivu-transfer-list-content-item {
		overflow: inherit;
		white-space: normal;
		display: -webkit-box;
		display: -ms-flexbox;
		display: flex;
	}
	.ivu-transfer-list-with-footer {
		padding-bottom: 0;
	}
	.ivu-transfer-list-body {
		border-radius: 0 0 6px 6px !important;
	}
	.category-sorter-unshare {
		display: inline-block;
		margin-left: 10px;
		color: #03ac54;
		border: 1px solid #03ac54;
		padding: 0 3px;
		line-height: normal;
		border-radius: 3px;
	}
	.category-sorter {
		margin: -1px 0 0 10px;
		color: #00B3A7;
		font-size: 18px;
	}
}
</style>
