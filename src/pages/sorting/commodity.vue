<!--
 * @Description: 库房/商品分拣-分拣打印-新版
 * @url: http://0.0.0.0:8089/#/sorting/commodity
-->
<template>
  <div class="sorting-commodity">
    <ListTable
      :filters="extraFilters"
      tableId="sorting_commodity_01"
      row-key="order_commodity_id"
      :selectionOtherPage="true"
      :rowClassName="rowClassName"
      :auto-load-data="true"
      :initParams="initParams"
      :before-request="beforeRequest"
      :before-set-data="afterRequest"
      :advance="true"
      :advance-items="advanceItems"
      :filter-items="filterItems"
      :columns="allColumns"
      data-provider="/superAdmin/sortSuper/OrderCommodityList"
      :border="false"
      :outer-border="true"
      :max-line="2"
      :showAdvance="showAdvance"
      @on-selection-change="handleSelectionChange"
      @advanceChange="advanceChange"
      @filter-change="filterChange"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
      @paginationReset="paginationReset"
      :defaultPageSize="page.pageSize"
      :pageSizeOpts="page.pageSizeOpts"
      :customPagination="page"
      :virtualScroll="true"
      :virtualScrollBuff="{ top: 200, bottom: 200 }"
      :height="getTableHeight() - 120"
      ref="table"
      :isOpenCustom="true"
      :pageSizeCache="{ suffix: 'list' }"
    >
      <div slot="batch-operation">
        <Button @click="batchSetProductDate">设置生产日期</Button>
        <Button class="ml10" @click="batchSetCustomField" v-if="sysConfig.is_open_order_customize_field == 1">
          设置自定义字段值
          <Tooltip
            :maxWidth="200"
            content="取关联订单创建时间，纯新开单据取单据创建时间"
            placement="top"
          >
            <i class="sui-icon icon-tips"></i>
          </Tooltip>
        </Button>
        <Button class="ml10" @click="$_onBatchMarkOutOfStock"
          >批量标记缺货</Button
        >
        <Button @click="batchReset" class="ml10">重置</Button>
      </div>
      <div slot="button">
        <Button @click="openExportModal(false)">导出</Button>
        <Dropdown
          placement="bottom-end"
          trigger="click"
          @on-click="openExportModal(true)"
        >
          <Button style="margin-left: -10px" icon="ios-arrow-down"></Button>
          <DropdownMenu slot="list" style="width: 112px">
            <DropdownItem>导出设置</DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
      <div
        slot="before-table"
        class="common__operation"
        v-show="selectedRows.length === 0"
      >
        <div class="slot-left">
          <ButtonGroup class="more-btn-group">
            <Button class="main-btn" type="default" @click="batchPrint"
              >{{ isBatchSorting ? '执行中...' : '一键分拣' }}</Button
            >
            <Dropdown
              placement="bottom-end"
              @on-click="showRecoveryModal"
              trigger="click"
            >
              <Button class="more-btn" icon="ios-arrow-down"></Button>
              <DropdownMenu slot="list" style="width: 112px">
                <DropdownItem :name="1">一键恢复</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </ButtonGroup>
          <Button class="ml10" @click="onlyOneTouchPrint">一键打印</Button>
          <div class="dropdown-btn-box ml10">
            <Dropdown placement="bottom-end" @on-click="summaryPick">
              <Button class="dropdown-btn">
                打印分拣汇总
                <SIcon size="mini" icon="arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem v-if="enableStoreArea" name="detail"
                  >商品明细拣货单</DropdownItem
                >
                <DropdownItem name="group">商品汇总拣货单</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <Button class="ml10" @click="$_onBatchMarkOutOfStock"
            >批量标记缺货</Button
          >
          <ImportButton
            class="ml10"
            v-if="isOpenSortImport"
            title="导入下单明细"
            :post="post"
            :data="importParams"
            @on-completed="_onImportBtnCompleted"
            >导入</ImportButton
          >
          <div
            id="batch-print"
            class="tpl_print_pick"
            style="visibility: hidden; display: inline-block"
          ></div>
          <div
            id="print-pick-sum"
            is_merge="1"
            style="visibility: hidden; display: inline-block"
            v-if="enableStoreArea"
          ></div>
          <Button @click="batchSetProductDate">设置生产日期</Button>
          <Button class="ml10" @click="batchSetCustomField" v-if="sysConfig.is_open_order_customize_field == 1">
            设置自定义字段值
            <Tooltip
              transfer="true"
              max-width="200"
              content="仅支持“订单明细自定义字段”的内容设置，设置成功后会同步修改订单明细中的自定义字段信息"
              placement="top"
            >
              <SIcon icon="help1" :size="12" />
            </Tooltip>
          </Button>
          <Button
            @click="batchReset"
            v-if="hasAuthority('A004001001002')"
            class="ml10"
            >重置分拣</Button
          >
        </div>
        <div class="slot-right">
          <Checkbox
            style="margin-right: 3px"
            @on-change="handleChangePrint"
            v-model="disablePrint"
            >&nbsp;分拣时不打印标签</Checkbox
          >
          <Tooltip
            :transfer="true"
            content="开启后，分拣时不打印分拣小票"
            placement="left"
          >
            <s-icon
              :size="12"
              style="margin-top: -2px; vertical-align: middle"
              icon="tips"
            />
          </Tooltip>
        </div>
      </div>
      <div
        slot="before-table-head"
        class="process-list"
        v-show="sortProcessList.length"
      >
        <div
          class="process-item"
          :key="index"
          v-for="(item, index) in sortProcessList"
        >
          <s-circle-rate
            :rate="
              (item.sortedAmount / (item.sortedAmount + item.sortAmount)) * 100
            "
          />
          <h6>
            {{ item.commodityName }}：已分拣<span class="sorted">{{
              item.sortedAmount
            }}</span
            >{{ item.commodityUnit }} 未分拣<span class="sort">{{
              item.sortAmount
            }}</span
            >{{ item.commodityUnit }}
          </h6>
        </div>
      </div>
    </ListTable>
    <exportModal
      ref="modal"
      :exportDataList="exportDataObj"
      :isOpenSortImport="isOpenSortImport"
      @complete="exportSortSum"
    />
    <!--批量设置采购员供应商弹窗-->
    <Modal
      :title="sortManyModal.title"
      :mask-closable="false"
      @on-visible-change="visibleChange"
      v-model="sortManyModal.show"
      class-name="vertical-center-modal sort-many-modal"
    >
      <div style="margin-bottom: 10px">
        备注：{{
          sortManyModal.rowParams.row ? sortManyModal.rowParams.row.remark : ''
        }}
      </div>
      <Tabs :value="TabsValue" :animated="false" @on-click="TabsChange">
        <TabPane label="不定值" name="name1" style="padding-top: 10px">
          <Form
            label-position="left"
            onsubmit="return false"
            ref="sortManyValidate"
            :rules="sortManyValidates"
            :model="sortManyModal"
            :label-width="80"
            style="text-align: left"
          >
            <FormItem label="分拣数：" prop="amount">
              <Tooltip
                v-if="TabsValue === 'name1'"
                :disabled="!sortManyModal.amount_warning"
                :always="true"
                :theme="danger"
                placement="right"
                :content="sortManyModal.amountWarningContent"
              >
                <Input
                  type="text"
                  @on-enter="saveSortMany"
                  class="sort-many-input"
                  v-model="sortManyModal.amount"
                  style="width: 120px; display: inline-block"
                ></Input>
              </Tooltip>

              <span class="many-sort-tips">输入数量，按回车</span>
            </FormItem>
            <FormItem label="分拣记录：">
              <div
                ref="record"
                :style="{
                  maxHeight: getTableHeight() - 50 + 'px',
                  overflow: 'auto',
                }"
              >
                <p v-for="(item, index) in sortManyModal.list" :key="index">
                  {{ Number(item)
                  }}{{ item ? sortManyModal.rowParams.row.unit : '' }}
                </p>
              </div>
            </FormItem>
          </Form>
        </TabPane>
        <TabPane label="定值" name="name2" style="padding-top: 10px">
          <p style="color: #ccc">
            定值多次分拣：会根据定值一次分拣多份。分拣总数 = 定值 * 份数
          </p>
          <Form
            label-position="left"
            :model="ConstantValue"
            onsubmit="return false"
            :rules="ruleValidate"
            ref="formValidate"
            :label-width="100"
            style="text-align: left; margin-top: 10px"
          >
            <FormItem label="每份分拣：" prop="NumberOfSorting">
              <Input
                type="text"
                @on-enter="saveSortMany"
                class="sort-many-input"
                v-model="ConstantValue.NumberOfSorting"
                style="width: 120px; display: inline-block"
              ></Input>
              <span class="many-sort-tips">{{ units }}</span>
            </FormItem>
            <FormItem label="分拣数：" prop="AdditionalCopies">
              <Input
                type="text"
                @on-enter="saveSortMany"
                class="sort-many-input"
                v-model="ConstantValue.AdditionalCopies"
                style="width: 120px; display: inline-block"
              ></Input>
              <span>输入数量，按回车</span>
            </FormItem>
          </Form>
        </TabPane>
      </Tabs>
      <div slot="footer">
        <Row type="flex" align="middle">
          <Col style="flex: 1" align="left">
            <span v-if="sortManyModal.show">订购数量：{{ orderNumber }}</span>
          </Col>
          <Col style="flex: 1" align="left" v-if="TabsValue !== 'name2'">
            <span v-if="sortManyModal.show"
              >分拣累计：{{ sortManyTotal
              }}{{ sortManyModal.rowParams.row.unit }}</span
            >
          </Col>
          <Col style="flex: 1" align="left" v-if="TabsValue == 'name2'">
            <span v-if="sortManyModal.show"
              >分拣累计：{{ NumberOfSortings
              }}{{ sortManyModal.rowParams.row.unit }}</span
            >
          </Col>
          <Col align="right">
            <Button type="primary" @click="quitSort">取消</Button>
            <Button type="primary" @click="doneSort">完成</Button>
          </Col>
        </Row>
      </div>
      <order-exception
        v-if="deliveryDate"
        :deliveryDate="deliveryDate"
      ></order-exception>
    </Modal>
    <CheckSorterTask />
    <MultiPrint
      postUrl="/superAdmin/sortSuper/AuthorizedPrintMultipleLabels"
      @on-ok="itemSortComplete"
      :goods="multiPrintModal.goods"
      v-model="multiPrintModal.show"
    />
    <Drawer width="650" :closable="false" v-model="recoveryModal.show">
      <Row
        slot="header"
        class="order-modal-title"
        align="middle"
        justify="space-between"
        type="flex"
      >
        <Col style="font-size: 14px; font-weight: bold">一键恢复</Col>
        <Col>
          <Icon
            style="font-size: 20px"
            @click.native="closeRecoveryModal"
            class="pointer"
            type="md-close"
          />
        </Col>
      </Row>
      <Table :data="recoveryList" :columns="recoveryColumns"></Table>
    </Drawer>
    <!--查看分拣记录-->
    <RecordModal
      :is-show="showSortRecord"
      :record-id="recordId"
      @close="showSortRecord = false"
    ></RecordModal>
    <SModal
      title="确认"
      :text="isHasNoGoods == 1 ? modalTitle : modalTitle2"
      ref="smodal"
      type="info"
      @ok="confirmSortOrPrint"
      okTxt="确认"
    >
      <template #footer-left>
        <Checkbox
          @on-change="setIsHasNoGoods"
          v-model="isHasNoGoods"
          true-value="1"
          false-value="0"
        >
          <span>含缺货商品</span>
        </Checkbox>
      </template>
    </SModal>
    <BatchSetProductDate ref="batchSetProductDate" />
    <BatchReset ref="batchReset" />

    <PrintTemplateExportChoose
      showPreview
      :filterOld="false"
      type="PICK"
      btnText="打印"
      title="选择打印模板"
      ref="printTemplateBatchChoose"
    />
    <Modal
      :mask-closable="false"
      :closable="false"
      :footer-hide="true"
      v-model="scheduleDialog"
      title="正在处理"
    >
      <div style="text-align: center">
        <Progress
          :percent="(progressData.current / progressData.total) * 100"
          hide-info
        />
        <!--			<s-d-p-progress :value="progressData.current" :total="progressData.total"></s-d-p-progress>-->
        <p class="mt30 mb20">请勿离开本页面, 否则会导致部分数据处理失败</p>
      </div>
    </Modal>
    <BatchSetCustomField ref="batchSetCustomField" @success="onCustomFieldSuccess"/>
  </div>
</template>

<script>
import { SModal } from '@/components/modal';
import Button from '@components/button';
import SIcon from '@components/icon';
import ListTable from '@/components/list-table';
import SCircleRate from '@components/circle-rate';
import CheckSorterTask from '@components/store/CheckSorterTask';
import storeSelect from '../../components/common/storeSelect';
import providerSelect from '../../components/common/providerSelect_new';
import purchaseAgentSelect from '@components/common/purchaseAgentSelect_new';
import categorySelect from '@components/common/categorySelect';
import moveeSelect from '@components/basic/moveeSelect';
import Progress from '@components/progress';
import RecordModal from '@components/sort-record-modal/record-modal';
import { MINE_TYPE } from '@/util/const';
import NumberInput from '@components/basic/NumberInput';
import goodsAutoComplete from '@components/common/goodsAutoComplete_new';
import UserGroup from '@components/user/userGroup_new';
import UserTypeSelect from '@components/user/userTypeSelect';
import orderException from '@components/common/orderException';
import LineSelect from '../../components/delivery/lineSelect_new';
import MultiPrint from '@components/sort/MultiPrint';
import sort from '@api/sort.js';
import bus from '@api/bus.js';
import store from '@api/storeRoom.js';
import SortMixin from '@components/sort/mixins';
import { mapState } from 'vuex';
import { api } from '@api/api.js';
import { exportLoop } from '@components/common/export-btn/util';
import BatchSetCustomField from "./components/BatchSetCustomField.vue";
import apiUtil from '@/api/util';
import {
  MIN_PAGE_SIZE,
  MAX_PAGE_SIZE,
  checkIsNumber,
  checkNumber,
  checkZeroNumber,
  checkInt,
} from './utils';
import ConfigMixin from '@/mixins/config';
import PickPrint from '@/mixins/print/pickPrint';
import SummaryPrint from '@/mixins/print/summaryPrint';
import SummaryGroupPrint from '@/mixins/print/summaryGroupPrint';
import CheckboxGroup from '@components/CheckboxGroup';
import OrderPriceFilter from '@components/order-price-filter';
import orderSerivce from '@api/order.js';
import RadioGroup from '@components/RadioGroup';
import exportModal from './components/exportModal';
import date from '@util/date.js';
import StorageUtil from '@util/storage.js';
import PurchaseType from '@components/purchase-type';
import DeliveryTime from '@components/delivery/deliveryTimeSelect';
import MutiCascader from '@/components/basic/mutiCascader';
import UserSearch from '@/components/user/userSearch';
import GroupFilter from '@/components/common/GroupFilter';
import SDPProgress from '@/components/progress';
import BatchSetProductDate from './components/BatchSetProductDate';
import BatchReset from './components/BatchReset';
import moment from 'moment';
import GoodsTagCheckboxGroup from '@/components/goodsTagCheckboxGroup';
import TableHeadSortIcon from '@/components/common/tableHeadSortIcon/index.vue';
import { getHasInAllFilterConfig } from '@/util/common';
import PrintTemplateExportChoose from '@/components/print-template-export-choose';
import authority from '@/util/authority.js';
import CommodityCascader from '@/components/base-filter-components/commodityCascader/index.vue';
import {
  reservoirAreaLocation,
} from '@/components/standard/sdp-filter-items'
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const LOCALSORTINGGOODSCUSTOMER = 'sorting_goods_customer';
const { hasAuthority } = authority;
export default {
  name: 'sorting-commodity',
  mixins: [SortMixin, ConfigMixin, PickPrint, SummaryPrint, SummaryGroupPrint],
  components: {
    SIcon,
    BatchReset,
    BatchSetProductDate,
    SModal,
    RecordModal,
    SCircleRate,
    Button,
    ListTable,
    SIcon,
    storeSelect,
    categorySelect,
    moveeSelect,
    NumberInput,
    orderException,
    CheckSorterTask,
    MultiPrint,
    OrderPriceFilter,
    exportModal,
    SDPProgress,
    PrintTemplateExportChoose,
    BatchSetCustomField,
  },
  computed: {
    ...mapState({
      sysConfig: 'sysConfig',
    }),
    // 是否开启分拣单选择打印模板
    isOpenChoosePrintTemplate() {
      return +this.sysConfig.open_choose_sort_order_print_template === 1;
    },
    sortManyTotal() {
      let sortManyList = this.sortManyModal.list;
      return sortManyList.length > 0
        ? this.fixedNum(
            sortManyList.reduce(
              (total, currentValue) => total * 1 + currentValue * 1,
            ),
          )
        : 0;
    },
    NumberOfSortings() {
      let NumberOfSortingLIst = this.NumberOfSortingsData;
      return NumberOfSortingLIst.length > 0
        ? this.fixedNum(
            NumberOfSortingLIst.reduce(
              (total, currentValue) => total * 1 + currentValue * 1,
            ),
          )
        : 0;
    },
    isOpenMultiMfgDate() {
      return this.sysConfig.is_multi_mfg_date == 1;
    },
    options() {
      return {
        disabledDate: (date) => {
          if (
            this.curItem.mfg_date &&
            this.curItem.mfg_date.split(',').length >= 3
          ) {
            return !this.curItem.mfg_date
              .split(',')
              .includes(moment(date).format('YYYY-MM-DD'));
          }
          let timestamp = Date.parse(new Date(this.deliveryDate));
          return date && date.valueOf() > timestamp;
        },
      };
    },
    allColumns() {
      const columns = this.deepClone(this.columns);
      const lastIndex = columns.findIndex(item => item.key === 'action');
      columns.splice(lastIndex, 0, ...this.customFieldList);
      return columns;
    }
  },
  data() {
    const sysEntityText = this.sysEntityText;
    return {
      isBatchSorting: false,
      sort: StorageUtil.getLocalStorage('sorting-list-sort-filters') || {
        sortingTime: 0,
      },
      scheduleDialog: false,
      progressData: {
        total: 0,
        current: 0,
        success: 0,
      },
      selectedRows: [],
      params: {},
      modalTitle: '确认打印？',
      modalTitle2: '',
      isHasNoGoods: '1',
      initParams: {},
      importParams: { delivery_date: '' },
      curFilters: '',
      rowData: '',
      noclearGoods: false,
      orderAmount: '',
      goods: {},
      curGoodsId: null,
      pageTotal: 0,
      showAdvance: true,
      showSortRecord: false, // 是否显示分拣记录
      recordId: '', // 显示分拣记录的商品id
      recoveryModal: {
        show: false,
      },
      filterItems: [
        {
          checked: true,
          required: true,
          label: '发货日期',
          type: 'DatePicker',
          key: 'delivery_date',
          props: {
            editable: false,
            clearable: false,
          },
          defaultValue: date.getDefaultDeliveryDate(),
          noreset: true,
          onChange: (value) => {
            this.deliveryDate = value;
            return { value };
          },
        },
        {
          checked: false,
          label: '收货时间',
          type: 'custom',
          key: 'delivery_time',
          props: {
            placeholder: '请选择',
            label: '全部收货时间',
          },
          onChange(label, selectedItem) {
            return {
              value: selectedItem ? selectedItem.id : '',
            };
          },
          component: DeliveryTime,
        },
      ],
      advanceItems: [
        {
          items: [
            {
              checked: true,
              type: 'Select',
              data: [],
              key: 'store_id',
              defaultValue: '',
              label: '仓库',
              props: {
                placeholder: '全部',
                filterable: true,
                'filter-by-label': true,
              },
              noreset: true,
              onChange: (value) => {
                this.advanceItems[0].items[2].props.storeId = value;
                if (this.$refs.table) {
                  this.$refs.table.setValue('store_area_id', '', true);
                  this.$refs.table.setValue('store_location_id', '', true);
                }
                return { value };
              },
            },
            {
              required: false,
              checked: true,
              type: 'custom',
              component: goodsAutoComplete,
              key: 'commodity_name',
              label: '商品搜索',
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
              props: {
                on: {
                  'on-enter': (e) => {
                    this.selectGoods(e);
                  },
                  'on-focus': (e) => {
                    this.handleSearchFocus(e);
                  },
                },
              },
            },
            {
              show: apiUtil.getIsOpenStoreMGT(),
              checked: true,
              width: 'auto',
              type: 'custom',
              label: '库区库位',
              key: ['store_area_id', 'store_location_id'],
              defaultValue: [],
              props: {
                storeId: '',
              },
              component: reservoirAreaLocation,
            },
            {
              checked: false,
              type: 'Select',
              data: [],
              key: 'area_id',
              label: '区域',
              props: {
                filterable: true,
                placeholder: '全部',
              },
            },
            {
              checked: false,
              type: 'Select',
              data: [
                {
                  value: 0,
                  label: '全部',
                },
                {
                  value: 1,
                  label: '未装车',
                },
                {
                  value: 2,
                  label: '已装车',
                },
              ],
              key: 'loading_status',
              label: '装车状态',
              defaultValue: 0,
              props: {
                filterable: true,
                // placeholder: '全部'
              },
            },
            {
              checked: true,
              require: true,
              width: 'auto',
              type: 'custom',
              name: '商品分类',
              key: ['category_id', 'category_id2'],
              defaultValue: [],
              props: {
                noMaxHeight: true,
                needThreeLevel: false,
              },
              component: CommodityCascader,
              // onChange: (value) => {
              //   value = value.filter((item) => item);
              //   this.extraFilters.category_id = value[0];
              //   // 数组剩下的所有值,数量不确定
              //   this.extraFilters.category_id2 = value.slice(1).join(',');
              //   this.getList();
              //   return { stop: false };
              // },
            },
            // {
            //   required: false,
            //   checked:true,
            //   type: 'custom',
            //   component: MutiCascader,
            //   key: ['category_id', 'category_id2'],
            //   label: '商品分类',
            //   props: {
            //     data: [],
            //     clearable: true,
            //     'change-on-select': true,
            //     placeholder: '全部',
            //     filterable: true
            //   },
            // 	onChange: (value) => {
            // 		this.extraFilters.category_id = value[0];
            // 		// 数组剩下的所有值,数量不确定
            // 		this.extraFilters.category_id2 = value.slice(1).join(',');
            // 		this.getList();
            // 		return { stop: false }
            // 	}
            // },
            {
              checked: true,
              type: 'custom',
              component: LineSelect,
              key: 'line_id',
              label: '线路',
              props: {
                data: [],
                remote: false,
                multiple: true,
                maxTagCount: 1,
                placeholder: '全部',
              },
            },
            {
              checked: false,
              type: 'Select',
              data: [],
              key: 'standard_status',
              label: '是否标品',
              props: {
                placeholder: '全部',
              },
            },
            {
              required: false,
              checked: true,
              type: 'Select',
              data: [],
              key: 'sort_status',
              defaultValue:
                StorageUtil.getLocalStorage('sorting-commodity-sort-status') ||
                '',
              label: '分拣状态',
              props: {
                placeholder: '全部',
              },
            },
            {
              required: false,
              checked: true,
              type: 'custom',
              component: PurchaseType,
              key: ['purchase_type', 'purchase_type_value'],
              label: '供应商/采购员',
              props: {
                placeholder: '全部',
                providerName: '供应商',
                agentName: '采购员',
                mode: ['agent', 'direct_provider'],
              },
            },
            // {
            //    required: false,
            //   checked:true,
            //   type: 'custom',
            //   component: providerSelect,
            //   key: 'provider_id',
            //   label: '默认供应商',
            //   props: {
            //     data: [],
            //     remote: false,
            //     filterable: true,
            //     placeholder: '全部'
            //   }
            // },
            // {
            //   checked:false,
            //   type: 'custom',
            //   component: purchaseAgentSelect,
            //   key: 'agent_id',
            //   label: '采购员',
            //   props: {
            //     filterable: true
            //   }
            // },
            {
              checked: false,
              type: 'Select',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '不缺货',
                  value: 1,
                },
                {
                  label: '缺货',
                  value: 2,
                },
              ],
              key: 'is_stockout',
              label: '是否缺货',
              props: {
                placeholder: '全部',
              },
            },
            {
              checked: false,
              type: 'Select',
              data: [
                {
                  label: '全部',
                  value: 0,
                },
                {
                  label: '有备注',
                  value: 1,
                },
                {
                  label: '无备注',
                  value: 2,
                },
              ],
              key: 'order_commodity_remark',
              label: '是否备注',
              defaultValue: 0,
              props: {
                placeholder: '全部',
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              type: 'custom',
              component: UserGroup,
              key: 'group_id',
              label: '集团',
            },
            {
              checked: false,
              type: 'custom',
              component: UserTypeSelect,
              key: 'receivable_style',
              label: '客户类型',
            },
            {
              checked: true,
              required: false,
              type: 'custom',
              hasType: 'GroupFilter',
              style: {
                width: '299px',
              },
              key: ['no_type', 'no_value'],
              defaultValue: StorageUtil.getLocalStorage(
                LOCALSORTINGGOODSCUSTOMER,
              )
                ? StorageUtil.getLocalStorage(LOCALSORTINGGOODSCUSTOMER)
                : ['1', ''],
              props: {
                placeholder: '输入客户名称/编码查询',
                selectData: [
                  {
                    label: sysEntityText('客户名称'),
                    placeholder: '输入客户名称查询',
                    value: '1',
                    key: 'user_id',
                  },
                  {
                    label: sysEntityText('客户名称模糊'),
                    placeholder: '输入客户名称查询',
                    value: '2',
                    key: 'uname',
                  },
                  {
                    label: sysEntityText('客户编码'),
                    placeholder: '输入客户编码查询',
                    value: '3',
                    key: 'user_code',
                  },
                  {
                    label: '订单号',
                    placeholder: '输入订单号查询',
                    value: '4',
                    key: 'order_no_like',
                  },
                ],
                customType: '1',
                customComp: () => UserSearch,
                customBind: {
                  valueKey: 'id',
                  labelKey: 'email',
                  splitType: false,
                  multiple: true,
                  changeValueType: true,
                },
                on: {
                  'on-enter': (value) => {
                    if (value[0] === '1')
                      this.extraFilters.user_id = value[1] || '';
                    this.$refs.table && this.$refs.table.fetchData();
                  },
                  'on-clear': () => {
                    this.filters.user_id = '';
                  },
                },
              },
              component: GroupFilter,
              onChange: (value = '') => {
                // 勾选状态存在本地
                StorageUtil.setLocalStorage(LOCALSORTINGGOODSCUSTOMER, [
                  value[0],
                  '',
                ]);
                if (value[0] === '1') {
                  this.extraFilters.user_id = value[1].join(',');
                } else {
                  this.extraFilters.user_id = '';
                }
                return { value };
              },
            },
            // {
            //   type: 'Input',
            //   key: 'user_name',
            //   label: '客户',
            //   props: {
            //     placeholder: '请输入客户名称/编码'
            //   }
            // },
            {
              checked: false,
              show: 'isEnableSortCode',
              type: 'Input',
              key: 'sort_code',
              label: '客户临时编码',
              props: {
                placeholder: '请输入临时编码',
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              type: 'Select',
              data: [
                {
                  label: '全部商品',
                  value: 0,
                },
                {
                  label: '正常分拣商品',
                  value: 1,
                },
                {
                  label: '超过阈值',
                  value: 2,
                },
                {
                  label: '低于阈值',
                  value: 3,
                },
              ],
              key: 'sort_threshold_status',
              label: '分拣阈值',
              defaultValue: 0,
              props: {},
            },
            {
              checked: false,
              type: 'Cascader',
              key: ['business_type', 'pool_provider_id'],
              label: '经营方式',
              defaultValue: 0,
              show: false,
              style: {
                width: '232px',
              },
              props: {
                filterable: true,
                clearable: true,
                'change-on-select': true,
                data: [],
                placeholder: '全部',
              },
            },
            {
              checked: false,
              label: '制单人',
              type: 'Input',
              key: 'op_user',
              props: {
                placeholder: '请输入制单人',
              },
            },
            {
              checked: false,
              label: '分拣员',
              type: 'Input',
              key: 'sorter_name',
              props: {
                placeholder: '请输入分拣员',
              },
            },
            {
              checked: false,
              label: '订购数量',
              type: 'custom',
              defaultValue: ['', ''],
              key: ['min_amount', 'max_amount'],
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
              component: OrderPriceFilter,
            },
            {
              checked: false,
              label: '配送方式',
              type: 'Select',
              key: 'delivery_method',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '配送',
                },
                {
                  value: '2',
                  label: '自提',
                },
              ],
            },
            {
              checked: false,
              label: '库存',
              type: 'Select',
              key: 'is_record',
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: 'Y',
                  label: '有库存',
                },
                {
                  value: 'N',
                  label: '无库存',
                },
              ],
            },
            {
              checked: false,
              label: '自提点',
              type: 'Select',
              key: 'self_pickup_point_id',
              props: {
                filterable: true,
                clearable: true,
                'filter-by-label': true,
              },
              data: [],
            },
            {
              required: false,
              checked: true,
              label: '商品备注',
              type: 'Input',
              key: 'commodity_remark',
              props: {
                placeholder: '请输入商品备注搜索',
              },
            },
            {
              label: '是否有内部备注',
              key: 'is_has_inner_remark',
              show: () => this.open_order_commodity_inner_remark,
              type: 'Select',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '是',
                },
                {
                  value: '2',
                  label: '否',
                },
              ],
            },
            {
              required: false,
              checked: true,
              label: '生产日期设置',
              type: 'Select',
              key: 'is_produce_date',
              show: false,
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: 'N',
                  label: '未设置',
                },
                {
                  value: 'Y',
                  label: '已设置',
                },
              ],
              onChange: (value) => {
                StorageUtil.setLocalStorage('SORTING_IS_PRODUCE_DATE', value);
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              required: false,
              checked: true,
              label: '商品品牌',
              type: 'Input',
              key: 'brand',
              props: {
                placeholder: '商品品牌',
              },
            },
            {
              required: false,
              checked: true,
              label: '商品产地',
              type: 'Input',
              key: 'product_place',
              props: {
                placeholder: '商品产地',
              },
            },
            {
              required: false,
              checked: false,
              label: '子账号',
              type: 'Input',
              key: 'sub_user_search',
              props: {
                placeholder: '子账号',
              },
            },
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
          ],
        },
        {
          items: [
            {
              relation: 'order_tag',
              required: false,
              checked: true,
              label: '标签筛选',
              show: false,
              type: 'custom',
              key: 'order_tag_filter',
              component: RadioGroup,
              props: {
                data: [
                  {
                    label: '同时存在',
                    value: '1',
                  },
                  {
                    label: '存在一个',
                    value: '2',
                  },
                  {
                    label: '无标签',
                    value: '3',
                  },
                ],
                on: {
                  'on-reset': (value) => {
                    // 重置时恢复订单标签可选
                    this._setOrderTagDisabled(+value === 3);
                  },
                },
              },
              defaultValue: '1',
              block: true,
              onChange: (value) => {
                // 为了解决切换无效和报错的问题,直接更改默认值,这是个历史遗漏问题
                let orderTagFilter =
                  this.advanceItems[3] &&
                  this.advanceItems[3].items &&
                  this.advanceItems[3].items.find(
                    (item) => item.key === 'order_tag_filter',
                  );
                orderTagFilter.defaultValue = value;
                // 选中标签筛选中的无标签之后，订单标签筛选项不可选择
                this._setOrderTagDisabled(+value === 3);
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              relation: 'order_tag',
              required: false,
              checked: true,
              label: '订单标签',
              type: 'custom',
              key: 'order_tag',
              show: false,
              props: {
                data: [],
                disabled: false,
              },
              style: {
                width: '1300px',
              },
              defaultValue: [],
              component: CheckboxGroup,
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              checked: false,
              required: false,
              label: '订单商品标签',
              key: 'order_commodity_tag',
              type: 'custom',
              show: false,
              tagTopStart: true,
              component: CheckboxGroup,
              defaultValue: [],
              props: {
                data: [],
              },
              style: {
                minWidth: '900px',
              },
              onChange: (value) => {
                StorageUtil.setLocalStorage(
                  'sorting-order-goods-tag-list',
                  value,
                );
                return {
                  value,
                  stop: true,
                };
              },
            },
            {
              checked: false,
              required: false,
              show: false,
              label: '客户标签',
              key: 'user_tag',
              tagTopStart: true,
              type: 'custom',
              defaultValue: [],
              props: {
                key: 'user_tag',
              },
              style: {
                minWidth: '1800px',
              },
              component: CheckboxGroup,
              onChange: (value) => {
                return { value, stop: true };
              },
            },
            {
              show: true,
              label: '商品标签',
              key: 'goods_tag_names',
              type: 'custom',
              block: true,
              defaultValue: [],
              props: {
                data: [],
                name: 'goods_tag_names',
              },
              component: GoodsTagCheckboxGroup,
              onChange: (value, labels) => {
                return { value: labels, stop: true };
              },
            },
            {
              label: '业务员',
              checked: false,
              key: 'refer_id',
              type: 'Select',
              data: [],
              props: {
                filterable: true,
                clearable: true,
                placeholder: '全部',
              },
            },
          ],
        },
      ],
      columns: [],
      sortManyValidates: {
        amount: [
          { required: true, message: '分拣数不能为空', trigger: 'blur' },
          { validator: checkIsNumber, trigger: 'blur' },
          { validator: checkZeroNumber, trigger: 'blur' },
          {
            validator: (rule, value, callback) => {
              const row = this.sortManyModal.rowParams.row;
              if (
                Number(value) +
                  Number(this.sortManyTotal) +
                  (row ? Number(row.actual_amount) : 0) <
                0
              ) {
                callback(new Error('分拣累计值不能小于0'));
              } else callback();
            },
            trigger: 'blur',
          },
        ],
      },
      ruleValidate: {
        NumberOfSorting: [
          { required: true, message: '每份分拣不能为空', trigger: 'blur' },
          { validator: checkNumber, trigger: 'blur' },
          {
            validator: checkZeroNumber,
            message: '分拣数为0，请分拣>0的数量',
            trigger: 'blur',
          },
        ],
        AdditionalCopies: [
          { required: true, message: '分拣数不能为空', trigger: 'blur' },
          { validator: checkInt, trigger: 'blur' },
        ],
      },
      single: false,
      orderNumber: '',
      ConstantValue: {
        NumberOfSorting: '',
        AdditionalCopies: '',
      },
      TabsValue: 'name1',
      sortManyValid: '',
      sortTimes: '',
      name1Valids: '',
      SortingsData: '',
      NumberOfSortingsData: [],
      tableHeight: this.getTableHeight() * 1 + 100,
      enableStoreArea: apiUtil.getIsOpenStoreMGT(), // 开启库区
      enableSortCode: false,
      isMultiStorage: false,
      areaList: [],
      deliveryDate: '',
      danger: 'danger',
      units: '',
      area: '',
      loading: true,
      PrintConfiguration: '',
      disablePrint: false,
      showSelectPanel: false,
      noDataText: '数据加载中...',
      currentStore: {},
      extraFilters: {
        // category_id: '', // 一级分类id
        // category_id2: '', // 二级分类id
      },
      stockStatusList: [
        {
          label: '是否缺货',
          value: '',
        },
        {
          label: '缺货',
          value: '2',
        },
        {
          label: '不缺货',
          value: '1',
        },
      ],
      countTask: {
        task_no: '',
        timer: 0,
        loop: true,
        time: 0,
        frequency: 1000,
        maxLoopTime: 2 * 60 * 1000,
      },
      filters: {
        order_commodity_remark: 0,
        commodity_id: '',
        delivery_date: '', // 发货日期
        store_id: '', // 仓库id
        store_area_id: '', // 库区id
        store_location_id: '', // 库位id
        loading_status: 0, // 装车状态
        standard_status: '', // 是否标品id
        commodity_code: '', // 商品编码
        commodity_name: '', // 商品名称
        user_name: '', // 客户名称
        line_id: '', // 线路id
        category_id: '', // 一级分类id
        category_id2: '', // 二级分类id
        pageSize: MIN_PAGE_SIZE, // 每页大小
        page: '', // 当前页
        search_value: '', // 组合搜索值
        sort_status: '', // 分拣状态id
        delivery_time: '', // 收货时间id
        is_stockout: '',
        area_id: '',
        goods: {},
      },
      recoveryList: [],
      recoveryColumns: [
        {
          title: '分拣时间',
          key: 'create_time',
        },
        {
          title: '操作员',
          key: 'create_user',
        },
        {
          title: '商品条数',
          key: 'commodity_count',
          renderHeader: (h) => {
            return h('div', [
              h('span', '商品条数'),
              h(
                'Tooltip',
                {
                  props: {
                    content: '订单商品条数',
                    transfer: true,
                    placement: 'top',
                  },
                },
                [
                  h('s-icon', {
                    style: {
                      marginLeft: '3px',
                      verticalAign: 'baseline',
                    },
                    props: {
                      icon: 'tips',
                      size: 12,
                    },
                  }),
                ],
              ),
            ]);
          },
        },
        {
          title: '商品',
          render: (h, params) => {
            return h('span', params.row.commodity_list.join(','));
          },
        },
        {
          width: 90,
          title: '操作',
          render: (h, params) => {
            return h(
              'span',
              {
                class: ['text-green', 'pointer'],
                on: {
                  click: () => {
                    this.recovery(params.row);
                  },
                },
              },
              '一键恢复',
            );
          },
        },
      ],
      searchConfig: {
        sort_status: [],
        standard_status: [],
      },
      sortManyModal: {
        // 多次分拣模态框
        title: '多次分拣',
        amount_warning: false,
        show: false,
        amount: '',
        list: [],
        rowParams: {},
        amountWarningContent: '',
      },
      sortProcessList: [],
      activeRow: {
        index: 0,
        event: null,
      },
      pageSizeCacheKey: `pageSize_${this.$route.path}_list`,
      page: {
        pageSizeOpts: [MIN_PAGE_SIZE, 200, MAX_PAGE_SIZE],
        pageSize: MIN_PAGE_SIZE,
        totalPage: 0,
        total: 0,
        currentPage: 1,
      },
      list: [],
      postList: [],
      customFieldList: [],
      cols: [],
      originCols: [
        {
          type: 'title',
          width: 40,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'sort',
          style: {
            paddingRight: 0,
          },
          render: (h, params) => {
            return h(
              'span',
              {
                class: {
                  dn: true,
                  tpl_print_pick: true,
                },
                attrs: {
                  'data-id': params.row.order_commodity_id, // 配合打印模块
                },
              },
              '打印',
            );
          },
        },
        {
          type: 'selection',
          key: 'selection',
          width: 36,
          fixed: 'left',
        },
        {
          minWidth: 140,
          fixed: 'left',
          title: '商品名称',
          key: 'commodity_name',
          resizable: true,
          poptip: true,
          render: (h, params) => {
            let { row } = params;
            let tag = [];
            const isFirstOrder = Number(params.row.is_first_order) === 1;
            let firstOrderTag = '';
            if (isFirstOrder) {
              firstOrderTag = h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'top',
                    content: '该客户第一次下单',
                  },
                },
                [
                  h('i', {
                    style: {
                      color: 'var(--primary-color)',
                      fontSize: '14px',
                      marginTop: '-2px',
                      marginRight: '4px',
                    },
                    class: ['sui-icon', 'icon-first-order'],
                  }),
                ],
              );
            }
            // 赠品
            if (row.isGift) {
              tag = h(
                'span',
                {
                  style: {
                    color: 'red',
                  },
                },
                '[赠品]',
              );
            }
            let stockOutTag = this.getStockOutTag(h, row);
            return h(
              'div',
              {
                style: {
                  position: 'relative',
                },
              },
              [firstOrderTag, stockOutTag, h('span', row.commodity_name), tag],
            );
          },
        },
        {
          title: '商品编码',
          minWidth: 120,
          key: 'commodity_code',
          width: (this.getWindowWidth() - 286) / 9,
        },
        {
          width: 120,
          title: '客户商品别名',
          key: 'oc_user_commodity_alias_name',
        },
        {
          width: 120,
          title: '联营供应商',
          key: 'pool_provider_desc',
        },
        {
          width: 120,
          title: '客户临时编码',
          key: 'sort_code',
        },
        {
          minWidth: 160,
          title: '库区/库位',
          key: 'shelf_code',
        },
        {
          minWidth: 140,
          title: '客户名称',
          key: 'shop_name',
          render: (h, params) => {
            const childrens = params.row.focus_on_sorting == 1 ? [
              [
                h('span', {
                  style: {
                    background: 'rgb(255, 103, 79)',
                    color: 'rgb(255, 255, 255)',
                    padding: '2px 4px',
                    'font-size': '12px',
                    'margin-right': '4px',
                    'border-radius': '2px'
                  },
                }, '重要'),
              ]
            ] : [];
            childrens.push(params.row.shop_name);
            return h('div', null, childrens);
          }
        },
        {
          width: 140,
          minWidth: 140,
          title: '子账号',
          key: 'sub_user_name',
        },
        {
          width: 100,
          title: '订购数量',
          key: 'order_amount_desc',
        },
        {
          width: 120,
          title: '实际数量',
          key: 'actual_amount',
          render: (h, params) => {
            let data = params.row,
              me = this;
            let sortedEl = h(
              'span',
              {
                class: ['table-number-input'],
              },
              data.actual_amount,
            );
            let el =
              +data.sort_status === 1
                ? sortedEl
                : h('input', {
                    attrs: {
                      min: 0,
                      class: 'table-number-input',
                      // disabled: data.sort_status == '1' ? true : false
                    },
                    domProps: {
                      value:
                        data.sort_status == '1'
                          ? data.actual_amount
                          : data.actual_amount || '',
                    },
                    style: {
                      width: '100%',
                    },
                    on: {
                      input(event) {
                        params.row.actual_amount = event.target.value || '';
                      },
                      change(event) {
                        let newRow = me.cloneObj(params.row);
                        newRow.input_amount = event.target.value;
                        me.postList.splice(params.index, 1, newRow);
                      },
                      focus(event) {
                        event.target.select();
                        // me.hightlightRow(params.index, 'red', 'bolder', '16px');
                        params.row._row_focused = true;
                        me.activeRow.index = params.index;
                        me.activeRow.event = event;
                      },
                      blur() {
                        params.row._row_focused = false;
                      },
                      keyup: (event) => {
                        if (event.keyCode == 13) {
                          params.row.amount_warning = false;
                          params.row.warning_msg = '';
                          // }
                          event.target.select();
                          if (isNaN(event.target.value)) {
                            this.$smessage({
                              type: 'warning',
                              text: '分拣数量必须是数字',
                            });
                            // this.errorMessage('分拣数量必须是数字');
                            return false;
                          }
                          if (event.target.value < 0) {
                            this.$smessage({
                              type: 'warning',
                              text: '分拣数量必须大于0',
                            });
                            // this.errorMessage('分拣数量必须是数字');
                            return false;
                          }
                          // if (!this.openExceededThresholdCannotSort) {
                          me.setFocus();
                          // }
                          me.saveSortInfo(
                            event.target.value,
                            params,
                            event,
                            true,
                          );
                        }
                      },
                    },
                  });
            if (!data.amount_warning) {
              return el;
            } else {
              return h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    always: true,
                    // transfer: true,
                    placement: 'right',
                    content: data.warning_msg,
                  },
                },
                [el],
              );
            }
          },
        },
        {
          width: 160,
          title: '生产日期',
          key: 'mfg_date',
          render: (h, params) => {
            let data = params.row,
              me = this;
            if (this.loading) {
              return null;
            }
            return h('DatePicker', {
              props: {
                multiple: this.isOpenMultiMfgDate,
                clearable: true,
                value: params.row.mfg_date,
                disabled: params.row.is_can_edit_mfg_date != 1,
              },
              attrs: {
                options: this.options,
                transfer: true,
              },
              on: {
                'on-change': (date) => {
                  if (date && date.split(',').length > 3) {
                    this.$Message.warning('最多可选择3个，请取消选中后重试');
                    return;
                  }
                  me.saveMfgDate(data.order_commodity_id, date);
                  let newRow = me.cloneObj(params.row);
                  newRow.mfg_date = date;
                  me.postList.splice(params.index, 1, newRow);
                  this.curItem = newRow;
                  // 更新当前行的数据
                  this.$refs.table.setRowData(params.index, newRow)
                },
                'on-open-change': () => {
                  this.curItem = params.row;
                },
              },
            });
          },
        },
        {
          minWidth: 90,
          title: '分拣单位',
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          width: 120,
        },
        {
          title: '分拣进度',
          key: 'sorting_progress',
          render: (h, params) => {
            let row = params.row;
            return h(Progress, {
              props: {
                value: row.sort_progress.sorting || 0,
                total: row.sort_progress.all || 0,
                showLine: true,
              },
              style: {
                width: '52px',
              },
              on: {
                'on-click-text': () => {
                  this.recordId = params.row.order_commodity_id;
                  this.showSortRecord = true;
                },
              },
            });
          },
        },
        {
          minWidth: 120,
          title: '描述',
          key: 'summary',
        },
        {
          minWidth: 100,
          title: '订单标签',
          key: 'order_tag',
          render: (h, params) => {
            let row = params.row;
            let result = '';
            if (row.order_tag && row.order_tag.length) {
              row.order_tag.map((item) => {
                result += item.name + '</br>';
              });
            }
            return h('span', {
              domProps: {
                innerHTML: result,
              },
            });
          },
        },
        {
          width: 140,
          title: '备注',
          poptip: true,
          key: 'remark',
        },
        {
          minWidth: 120,
          title: '内部备注',
          key: 'inner_remark',
        },
        {
          title: '库存',
          key: 'record',
          width: 90,
        },
        {
          width: 90,
          title: '分拣状态',
          key: 'sort_status_desc',
          render: (h, params) => {
            let data = params.row;
            return h(
              'span',
              {
                attrs: {
                  class: [
                    data.sort_status_desc == '未分拣' ? 'warning' : 'light',
                  ],
                },
              },
              data.sort_status_desc,
            );
          },
        },
        {
          title: '分拣占用库存',
          key: 'sorting_occupy_inventory',
        },
        {
          title: '仓内可用库存',
          key: 'available_stock',
        },
        {
          width: 80,
          title: '分拣员',
          key: 'sort_name',
        },
        {
          width: 90,
          title: '装车状态',
          key: 'loading_status_desc',
          render: (h, params) => {
            let data = params.row;
            let color = '';
            if (
              data.loading_status_desc == '未装车' ||
              data.loading_status_desc == '部分装车'
            ) {
              color = 'warning';
            } else {
              color = '';
            }
            return h(
              'span',
              {
                class: [color],
              },
              data.loading_status_desc,
            );
          },
        },
        {
          width: 160,
          minWidth: 110,
          title: '分拣时间',
          key: 'sort_time',
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#shopNameSortIcon').click();
                      },
                    },
                  },
                  '分拣时间',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.sortingTime,
                    id: 'shopNameSortIcon',
                  },
                  ref: 'TableHeadSortIcon',
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('sortingTime', e);
                      this.$refs.table._fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
        },
        {
          title: '线路',
          key: 'line_name',
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          actionCountLimit: 3,
          width: 298,
          actions: (params) => {
            let data = params.row;
            let isEnableMultiPrint = this.isEnableMultiPrint;
            const actions = [
              {
                name: '打印多份标签',
                ctrl: isEnableMultiPrint,
                // ctrl: true,
                action: () => this.showMultiPrint(data),
              },
              {
                name: '打印',
                ctrl: true,
                action: () => this.print(params),
              },
              {
                name: '多次分拣',
                ctrl: 'isCanManySort',
                show: hasAuthority('A004001001002'),
                action: () => {
                  this.openSortManyModal(params);
                  this.units = data.unit;
                  this.orderNumber = data.order_amount_desc;
                  this.orderAmount = params.row.order_amount;
                  this.rowData = params;
                  // this.defaultValues(params);
                },
              },
              {
                name: '标记缺货',
                ctrl: 'isCanMarkStockOut',
                action: () => this._markStockOut(data, () => this.getList()),
              },
              {
                name: '部分缺货',
                ctrl: 'isCanPartMarkStockOut',
                action: () =>
                  this.$_onMarkPartStockOut(data, () => this.getList()),
              },
              {
                name: '重置',
                ctrl: 'isCanReset',
                show: hasAuthority('A004001001002'),
                action: () => {
                  this.$smodal({
                    type: 'info',
                    title: '提示',
                    text: `<span>确定重置商品<em>${data.commodity_name}</em>分拣状态？</span>`,
                    onOk: () => {
                      this.resetSort(params);
                    },
                  });
                },
              },
            ];
            return actions.filter(
              (item) =>
                (item.ctrl === true || data[item.ctrl]) && item.show !== false,
            );
          },
        },
      ],
      post: {
        url: '/superAdmin/sortSuper/ImportSortOrderCommodity',
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xlsx'],
      },
      exportDataObj: null,
      curItem: {},
      isFirstEnter: true,
    };
  },
  created() {
    const cachedPageSize = this.pageSizeCacheKey
      ? localStorage.getItem(this.pageSizeCacheKey)
      : null;
    this.page.pageSize = cachedPageSize
      ? parseInt(cachedPageSize, 10)
      : MAX_PAGE_SIZE;
    this.initQueryParams();
    // 生产日期设置
    this.advanceItems[2].items[
      this.advanceItems[2].items.findIndex(
        (item) => item.key === 'is_produce_date',
      )
    ].show = !this.isEnableBatch;
  },
  mounted() {
    this.disablePrint =
      localStorage.getItem('commodityIsprint') == 1 ? true : false;
    this.getSearchConfig();
    this.getAreaList();
    // this.setTableHeight();
    this.columns = this.originCols;
    store.checkIsMultiStorage().then((res) => {
      if (res.status) {
        this.isMultiStorage = res.data;
      }
    });
    this.getPickUpList();
    this.getOrderTagList();
    this.getUserTagList();
    bus.$on('update-sort-data', this.getList);
    this.showAdvance =
      localStorage.getItem(this.$route.name + '-showAdvance') !== 'false';

    // 初次加载时设为 true
    this.isFirstEnter = true;
  },
  activated() {
    this.getFieldColumns();
    if (this.isFirstEnter) {
      // 第一次进入的逻辑
      this.isFirstEnter = false; // 标记已经进入过一次
    } else {
      // 第二次及之后的逻辑
      this.getList(false);
    }
  },
  beforeDestroy() {
    this.advanceItems[0].items[5].props.data = [];
    this.advanceItems[0].items[6].props.data = [];
    this.advanceItems[0].items[9].props.data = [];
    this.endCountLoop();
    bus.$off('update-sort-data');
  },
  deactivated() {
    bus.$off('update-sort-data', this.getList);
  },
  methods: {
    hasAuthority,
    handleSortKeyChange(sortKey, e) {
      Object.keys(this.sort).map((key) => {
        this.sort[key] = sortKey === key ? e : 0;
      });
    },
    getFieldColumns() {
      this.$request.get(this.apiUrl.customizeFieldList, {customize_type: 4}).then(res => {
        const {data=[], status=0} = res;
        if (status) {
          this.customFieldList = data.map(item => ({
            key: `customize_field_${item.id}`,
            title: item.name,
            width: 120,
            render: (h, params) => {
              const text = params.row[`customize_field_${item.id}`];
              return h('Tooltip', {
                props: {
                  content: text,
                  placement: 'bottom',
                  transfer: true,
                  maxWidth: '240'
                }
              },[h('p',{
                class: 'line-clamp-2'
              }, text)]);
            },
          }));
        }
      })
    },
    getUserTagList() {
      this.$request.get(this.apiUrl.getUserTagList).then((res) => {
        if (res.status && res.data && res.data.length) {
          const tagFilter = this.advanceItems.find((arr) =>
            arr.items.find((item) => item.key === 'user_tag'),
          );
          const userTagItem = tagFilter.items.find(
            (item) => item.key === 'user_tag',
          );
          if (userTagItem) {
            userTagItem.show = true;
            userTagItem.props.data = res.data.map((item) => ({
              value: item.id,
              label: item.name,
            }));
          }
        }
      });
    },
    rowClassName(row) {
      if (row._row_focused) {
        return 'row-focused';
      } else {
        return '';
      }
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    batchSetProductDate() {
      const selectedRows = this.selectedRows.filter(
        (item) => +item.is_can_edit_mfg_date === 1,
      );
      this.$refs.batchSetProductDate
        .open({
          params: this.listParams,
          selection: selectedRows,
        })
        .then(() => {
          this.getList(false);
          this.$refs.table.handleCancelSelect();
        });
    },
    batchSetCustomField() {
      this.$refs.batchSetCustomField.showModal({
        params: this.listParams,
        selection: this.selectedRows,
      });
    },
    batchReset() {
      this.$refs.batchReset
        .open({
          params: this.listParams,
          selection: this.selectedRows,
        })
        .then(() => {
          this.getList(false);
          this.$refs.table.handleCancelSelect();
        });
    },
    onCustomFieldSuccess() {
      this.getList(false);
      this.$refs.table.handleCancelSelect();
    },
    setIsHasNoGoods(e) {
      localStorage.setItem('isHasNoGoods', e);
    },
    // 打印方法
    printPickOrder(params = {}) {
      params.params = {
        ...params.params,
        is_one_touch_sort_print: 1,
      };
      params.postUrl = '/superAdmin/sortSuper/authorizedPrintData';
      // 多次分拣用其他接口
      if (params.mulitPick) {
        params.postUrl = '/superAdmin/sortSuper/ManyPrintData';
      }
      if (params.notChoose || !this.isOpenChoosePrintTemplate) {
        this._mxPrintPickOrder(params);
        return;
      }
      if (this.isOpenChoosePrintTemplate) {
        this.$refs.printTemplateBatchChoose.open((template_id, preview) => {
          this._mxPrintPickOrder({
            ...params,
            templateId: template_id,
            preview,
          });
          setTimeout(() => {
            // 关闭打印模板选择框
            this.$refs.printTemplateBatchChoose.closeModal();
          }, 500);
        });
      }
    },
    confirmSortOrPrint() {
      this.params.contain_stock_out = this.isHasNoGoods;
      sort.batchPrint(this.params).then((res) => {
        if (res.status) {
          let orderCommodityIds = res.data.order_commodity_id;
          if (res.data.sort_print == sort.canPrintTickt && !this.disablePrint) {
            this.printPickOrder({
              dataId: orderCommodityIds.join(','),
              idName: 'batch-print',
              className: 'tpl_print_pick',
            });
          }
          bus.$emit('update-sort-data');
        } else {
          this.$smessage({ type: 'error', text: res.message });
        }
      });
    },
    initQueryParams() {
      this.isHasNoGoods = localStorage.getItem('isHasNoGoods') == 0 ? '0' : '1';
      const storeId = this.storage.getLocalStorage('init_ware_house_id');
      const delivery_date = date.getDefaultDeliveryDate();
      this.initParams = {
        store_id: storeId,
        delivery_date: delivery_date,
      };
    },
    _setOrderTagDisabled(_disabled) {
      try {
        let orderTag =
          this.advanceItems[3] &&
          this.advanceItems[3].items &&
          this.advanceItems[3].items.find((item) => item.key === 'order_tag');
        if (orderTag) {
          orderTag.props.disabled = _disabled;
          _disabled && this.$refs.table.setValue('order_tag', [], true);
        }
      } catch (e) {
        console.log(e);
      }
    },
    // 获取订单标签列表
    getOrderTagList() {
      orderSerivce.qryOrderTagList().then((res) => {
        let { status, data } = res;
        if (status) {
          if (data && Array.isArray(data)) {
            let orderTagList = res.data.map((item) => {
              return {
                label: item.name,
                value: item.id,
              };
            });
            let orderTag = this.advanceItems[3].items.find(
              (item) => item.key === 'order_tag',
            );
            if (orderTag) {
              orderTag.props.data = orderTagList;
            }
          }
        }
      });
    },
    // 获取自提点列表
    async getPickUpList() {
      const { status, message, data } = await this.$request.get(
        this.apiUrl.SelfPickupPointList,
      );
      if (status && data && data.length) {
        let target = this.advanceItems[2].items.find(
          (item) => item.key === 'self_pickup_point_id',
        );
        if (target) {
          target.data = data.map((item) => ({
            value: item.id,
            label: item.name,
          }));
        }
      }
    },
    _onImportBtnCompleted(importSuccess) {
      importSuccess && this.getList();
    },
    requestSortRecord() {
      this.showSortRecord = true;
    },
    // 定值 辅助单位 并且勾选了按基础单位分拣的商品 那么每次分拣数以及分拣数就会自动填值
    defaultValues(val) {
      if (
        val.row.unit_convert == 'Y' &&
        val.row.commodity_id !== val.row.parent_id &&
        !this.openOrderUnitSorting
      ) {
        this.ConstantValue.NumberOfSorting = val.row.unit_num.toString();
        this.ConstantValue.AdditionalCopies = val.row.order_amount.toString();
      } else {
        this.ConstantValue.NumberOfSorting = this.orderAmount.toString();
        this.ConstantValue.AdditionalCopies = '1';
      }
    },
    filterChange() {
      this.filters.goods = {};
    },
    fixedNum(n) {
      return parseFloat(Number(n).toFixed(10));
    },
    recovery(row) {
      this.$Modal.confirm({
        title: '一键恢复',
        render: (h) => {
          return h('div', [
            h('span', '本次恢复商品共'),
            h(
              'span',
              {
                class: ['text-red'],
              },
              row.commodity_count,
            ),
            h('span', '个，确定一键恢复？'),
            h('p', '注意：此功能会重置商品分拣数量，请谨慎操作！'),
          ]);
        },
        onOk: () => {
          this.$request
            .post(this.apiUrl.sortOneTouch.recovery, {
              id: row.id,
            })
            .then((res) => {
              let { status, message } = res;
              if (status) {
                this.getRecoveryList();
                this.$refs.table.fetchData();
                this.successNotice(message || '恢复成功！');
              } else {
                this.errorNotice({
                  title: '失败',
                  desc: message || '恢复失败！',
                });
              }
            });
        },
      });
    },
    advanceChange(e) {
      localStorage.setItem(this.$route.name + '-showAdvance', e);
    },
    handleExport() {},
    handleSorting() {},
    printAll() {},
    _markStockOut(goods, callback) {
      const markStockOut = () => {
        this.$request
          .post(this.apiUrl.markStockOut, {
            order_commodity_id: goods.order_commodity_id,
            is_part: 2,
          })
          .then((res) => {
            let { status, message } = res;
            if (status) {
              callback && typeof callback === 'function' && callback();
              this.$smessage({
                type: 'success',
                text: '标记缺货成功',
              });
            } else {
              this.$smessage({
                type: 'error',
                text: message || '标记缺货失败',
              });
            }
          });
      };
      if (this.isSorted(goods)) {
        this.$smodal({
          type: 'info',
          title: '提示',
          text: `<div><p>这个商品已经分拣，是否继续标记缺货？</p><p>注意：</p><p>1、标记缺货之后默认这个商品全部缺货状态</p><p>2、发货出库时，缺货状态的商品不会发货</p></div>`,
          onOk: () => {
            markStockOut();
          },
        });
      } else {
        this.$smodal({
          type: 'info',
          title: '确认',
          text: '确定标记该商品为缺货？',
          onOk: () => {
            markStockOut();
          },
        });
      }
    },
    /**
     * 标记部分缺货
     * @param {goods} 订单中的商品对象
     * @param {callback} 回调函数
     */
    $_onMarkPartStockOut(goods, callback) {
      const markPartStockOut = () => {
        let params = {
          order_commodity_id: goods.order_commodity_id,
          is_part: 1,
        };
        sort.markStockOut(params).then((res) => {
          let { status, message } = res;
          if (status) {
            callback && typeof callback === 'function' && callback();
            this.$smessage({
              type: 'success',
              text: '标记部分缺货成功',
            });
          } else {
            this.$smessage({
              type: 'error',
              text: message || '标记部分缺货失败',
            });
          }
        });
      };
      this.$smodal({
        type: 'info',
        title: '确认',
        text: '确定标记该商品为部分缺货？',
        onOk: () => {
          markPartStockOut();
        },
      });
    },
    /**
     * 批量标记缺货
     */
    $_onBatchMarkOutOfStock() {
      console.log(this.curFilters);
      // 须进行商品或客户筛选后才能批量标记缺货
      if (
        !this.curFilters ||
        (!(this.curFilters.commodity_id || this.curFilters.commodity_name) &&
          !this.curFilters.uname &&
          !this.curFilters.user_id &&
          !this.selectedRows.length)
      ) {
        this.$smessage({
          type: 'warning',
          text: '批量标记缺货前，请输入商品搜索或客户名称进行筛选！',
        });
        return;
      }

      // 如果是先勾选了商品, 可直接进行批量标记缺货环节
      if (this.$refs.table) {
        const hasSelectedRows = Boolean(this.selectedRows.length);
        let tableList = hasSelectedRows
          ? this.selectedRows
          : this.$refs.table.data;
        let order_commodity_ids = tableList
          .filter((item) =>
            hasSelectedRows
              ? item.isCanMarkStockOut || item.isCanPartMarkStockOut
              : item.isCanMarkStockOut,
          )
          .map((item) => item.order_commodity_id);
        if (!order_commodity_ids.length) {
          this.$smessage({
            type: 'warning',
            text: '当前列表没有能标记缺货的商品。',
          });
        } else {
          this.$smodal({
            type: 'info',
            title: '请确认是否批量标记缺货？',
            text: hasSelectedRows
              ? `即将标记缺货${order_commodity_ids.length}个商品,此操作仅针对未分拣商品或者实际数量<订单数量时`
              : `共${tableList.length}个包裹，其中${order_commodity_ids.length}条可以标记缺货，确定之后这${order_commodity_ids.length}个商品将会标记缺货（注意：不包括部分缺货）。`,
            onOk: async () => {
              if (hasSelectedRows) {
                const goodList = JSON.parse(JSON.stringify(this.selectedRows));
                const canMarkStockOutList = goodList.filter(
                  (item) =>
                    item.isCanMarkStockOut || item.isCanPartMarkStockOut,
                );
                this.progressData.current = 0;
                this.progressData.success = 0;
                this.progressData.total = canMarkStockOutList.length;
                this.scheduleDialog = true;
                await this.queueMarkOutOfStock(canMarkStockOutList);
              } else {
                this.$_batchMarkOutOfStock(order_commodity_ids.join(','), () =>
                  this.getList(),
                );
              }
            },
          });
        }
      }
    },
    $_batchMarkOutOfStock(order_commodity_ids, callback) {
      this.$request
        .post(this.apiUrl.allMarkStockOut, {
          order_commodity_ids: order_commodity_ids,
        })
        .then(({ status, message }) => {
          if (status) {
            callback && typeof callback === 'function' && callback();
            this.$smessage({
              type: 'success',
              text: '批量标记缺货成功！',
            });
          } else {
            this.$smessage({
              type: 'error',
              text: message || '批量标记缺货失败',
            });
          }
        });
    },
    // 队列标记缺货任务
    async queueMarkOutOfStock(goodsList) {
      const goods = goodsList[0];
      const res = await this.$request.post(this.apiUrl.markStockOut, {
        order_commodity_id: goods.order_commodity_id,
        is_part: goods.isCanMarkStockOut ? 2 : 1,
      });

      // 去除goodsList中第一项
      goodsList.shift();
      this.progressData.current = this.progressData.total - goodsList.length;
      if (res.status) {
        this.progressData.success++;
      }
      if (goodsList.length <= 0) {
        goodsList.shift();
        this.progressData.current = this.progressData.total - goodsList.length;
        this.scheduleDialog = false;
        if (this.progressData.success === this.progressData.total) {
          this.$smessage({
            type: 'success',
            text: `${this.progressData.success}条数据处理成功`,
          });
        } else {
          this.$smessage({
            type: 'error',
            text: `${this.progressData.success}条数据处理成功, ${this.progressData.total - this.progressData.success}条处理失败, 请重试`,
          });
        }
        this.selectedRows = [];
        this.$refs.table.handleCancelSelect();
        this.getList();
        return;
      }
      await this.queueMarkOutOfStock(goodsList);
    },
    getStockOutTag(h, goods) {
      let stockOutTag = '';
      if (this.isStockOut(goods)) {
        stockOutTag = h('i', {
          style: {
            color: '#F13031',
            fontSize: '14px',
            marginTop: '-2px',
            marginRight: '4px',
          },
          class: ['sui-icon', 'icon-out-stock'],
        });
      }
      return stockOutTag;
    },
    showRecoveryModal() {
      this.getRecoveryList();
      this.recoveryModal.show = true;
    },
    closeRecoveryModal() {
      this.recoveryModal.show = false;
    },
    getRecoveryList() {
      this.$request
        .get(this.apiUrl.sortOneTouch.getOneTouchSortRecord, {
          pageSize: 999,
        })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            this.recoveryList = data.list;
          } else {
            this.recoveryList = [];
          }
        });
    },
    /**
     * @description: 点击一键分拣
     */
    batchPrint() {
      if (this.isBatchSorting) {
        return;
      }
      let params = this.getFilters();
      if (!params.store_id) {
        this.errorNotice('网络异常，请刷新页面后重试！');
        return;
      }
      params.is_print = this.disablePrint ? 0 : 1;
      this.isBatchSorting = true;
      sort.batchPrintCheck(params).then((res) => {
        if (res.status) {
          this.modalTitle2 = res.data.message_not_stock_out;
          this.modalTitle = res.message ? res.message : '确定执行一键打印？';
          params.contain_stock_out = this.isHasNoGoods;
          this.params = params;
          this.$refs.smodal.open();

          // this.$smodal({
          //   type: 'info',
          //   title: '确认',
          //   text: res.message ? res.message : '确定执行一键打印？',
          //   onOk: () => {
          //     sort.batchPrint(params).then(res => {
          //       if (res.status) {
          //         let orderCommodityIds = res.data.order_commodity_id;
          //         if (
          //           res.data.sort_print == sort.canPrintTickt &&
          //           !this.disablePrint
          //         ) {
          //           // prckPrint  mixin混入函数
          //           this._mxPrintPickOrder({
          //             dataId: orderCommodityIds.join(','),
          //             idName: 'batch-print',
          //             className: 'tpl_print_pick'
          //           })
          //         }
          //         bus.$emit('update-sort-data');
          //       } else {
          //         this.$smessage({ type: 'error', text: res.message });
          //       }
          //     });
          //   }
          // });
        } else {
          this.$smessage({ type: 'warning', text: res.message });
        }
      }).finally(() => {
        this.isBatchSorting = false;
      });
    },
    /**
     * @description: 点击一键打印
     */
    onlyOneTouchPrint() {
      let params = this.getFilters();
      params.op_type = 1;
      params.delivery_date = this.deliveryDate;
      params.commodity_id = this.curGoodsId;
      params.is_print = this.disablePrint ? 0 : 1;
      params.is_one_touch_sort_print = 1;
      sort.batchPrintCheck(params).then((res) => {
        if (res.status) {
          this.$smodal({
            type: 'info',
            title: '确认',
            text: res.message ? res.message : '确定执行一键打印？',
            onOk: () => {
              sort.onlyOneTouchPrint(params).then((res) => {
                if (res.status) {
                  let orderCommodityIds = res.data.order_commodity_id;
                  // prckPrint  mixin混入函数
                  if (this.isOpenChoosePrintTemplate) {
                    this.$refs.printTemplateBatchChoose.open(
                      (template_id, preview) => {
                        this._mxPrintPickOrder({
                          dataId: orderCommodityIds.join(','),
                          idName: 'batch-print',
                          oneClickPrinting: true,
                          className: 'tpl_print_pick',
                          templateId: template_id,
                          preview,
                          postUrl: '/superAdmin/sortSuper/authorizedPrintData',
                          params: {
                            is_one_touch_sort_print: 1,
                          },
                        });
                        setTimeout(() => {
                          // 关闭打印模板选择框
                          this.$refs.printTemplateBatchChoose.closeModal();
                        }, 500);
                      },
                    );
                    return;
                  }
                  this._mxPrintPickOrder({
                    dataId: orderCommodityIds.join(','),
                    idName: 'batch-print',
                    oneClickPrinting: true,
                    className: 'tpl_print_pick',
                    params: {
                      is_one_touch_sort_print: 1,
                    },
                    postUrl: '/superAdmin/sortSuper/authorizedPrintData'
                  });
                } else {
                  this.$message({ type: 'error', text: res.message });
                }
                // btnDom = null;
              });
            },
          });
        } else {
          // btnDom = null;
          this.$smessage({
            type: 'error',
            text: res.message,
          });
        }
      });
    },
    summaryPick(type = 'detail') {
      const params = this.getFilters();
      params.commodity_id = this.curGoodsId;
      sort.summaryPick(params).then((res) => {
        if (res.status) {
          let data = res.data;
          if (type === 'group') {
            this.printSummaryGroupOrder({
              dataId: data.order_commodity_id.join(','),
            });
            return;
          }
          this._mxPrintSummaryOrder({
            className: 'tpl_print_view_summary',
            isMerge: '1',
            dataId: data.order_commodity_id.join(','),
          });
          // let btnDom = document.querySelector('#print-pick-sum');
          // btnDom.setAttribute('data-id', data.order_commodity_id.join(','));
          // btnDom.click();
        } else {
          this.$smessage({
            type: 'error',
            text: res.message,
          });
        }
        // btnDom = null;
      });
    },
    openExportModal(isOpen) {
      this.$refs.modal.open(isOpen);
    },
    exportSortSum(res) {
      let params = this.getFilters();
      params.in_type = res.exportType;
      // 如果设置了带备注下单数量且备注单独一列
      if (res.exportType === '2' && res.remarkSet === '2') {
        params.in_type = 4;
      }
      if (res.exportType === '4') {
        params.in_type = 5;
        params.remark_show_type = res.remarkSet;
        params.customer_export_fields = JSON.stringify(res.selected);
      }
      if (res.remarkSet === '3') {
        params.remark_show_type = '3';
      }
      if (!this.isOpenSortImport && res.exportType === '3') {
        params.in_type = '0';
      }
      params.sequence_field = res.sequence_field;
      this.$request.post(this.apiUrl.exportSortSum, params).then((res) => {
        let { status, message, data } = res;
        if (status) {
          // this.$smessage({ text: '导出任务已开始，请稍候' });
          this.$store.commit('showTaskCenter', true);
          exportLoop(data.task_no);
        } else {
          this.$smessage({ type: 'error', text: message });
        }
      });
    },
    beforeRequest(params) {
      const showFilterConfig = this.$refs.table.$refs.filter.allFilters;
      // 判断订单标签\订单商品标签\客户标签\商品标签是否在筛选条件中
      const paramsToCheck = [
        'order_tag',
        'order_commodity_tag',
        'user_tag',
        'goods_tag_names',
      ];
      paramsToCheck.forEach((param) => {
        if (!getHasInAllFilterConfig(param, showFilterConfig)) {
          this.$refs.table.$refs.filter.filterData[param] = '';
          params[param] = '';
        }
      });
      if (!params.delivery_date) {
        return false;
      }
      this.loading = true;
      if (!params.store_id) {
        params.store_id = this.storage.getLocalStorage('init_ware_house_id');
      }
      let { purchase_type, purchase_type_value } = params;
      if (Number(purchase_type) === 1) {
        params.agent_id = purchase_type_value;
        delete params.provider_id;
      } else {
        params.provider_id = purchase_type_value;
        delete params.agent_id;
      }
      delete params.purchase_type_value;
      this.importParams.delivery_date = params.delivery_date;
      params.order_delivery_date = params.delivery_date;
      this.noclearGoods && (this.goods = this.noclearGoods);
      if (this.goods && this.goods.commodity_id) {
        params.commodity_id = this.curGoodsId = this.goods.commodity_id;
        params.commodity_name = '';
      } else {
        this.curGoodsId = null;
        this.resetSortProgress();
      }
      if (params.order_tag) {
        params.order_tag = params.order_tag.join(',');
      }
      if (params.user_tag) {
        params.user_tag = params.user_tag.join(',');
      }
      if (Array.isArray(params.order_commodity_tag))
        params.order_commodity_tag = params.order_commodity_tag.join();
      this.listParams = params;

      let sortMap = ['', 'desc', 'asc'];
      if (+this.sort.sortingTime) {
        params.sequence_field = 'sort_time';
        params.sequence_type = sortMap[this.sort.sortingTime];
      }
      return params;
    },
    afterRequest(list, res) {
      this.loading = false;
      if (!res) {
        return list;
      }
      this.curFilters = JSON.parse(JSON.stringify(this.getFilters()));
      if (this.goods && this.goods.commodity_id) {
        this.getSortProgress();
      }

      StorageUtil.setLocalStorage('sorting-list-sort-filters', this.sort);

      this.goods = {};
      this.noclearGoods = false;
      // this.getSortProgress();
      if (res && res.status === 1) {
        const { data } = res;
        const { pageParams, sort_customer_export } = data;
        if (!this.exportDataObj) this.exportDataObj = sort_customer_export;
        this.resetActiveRow();

        list.forEach((item) => {
          item.input_amount = '';
          item.amount_warning = false;
          item.warning_msg = '';
          item._row_focused = false;
          if (item.mfg_date) {
            item.mfg_date = item.mfg_date.replace(/、/g, ',');
          }
        });
        if (data.sort_alert) {
          if (this.sortAlertModal) {
            this.sortAlertModal.$el.remove();
            this.sortAlertModal.$destroy();
          }
          this.sortAlertModal = this.$smodal({
            type: 'info',
            title: '提醒',
            text: data.sort_alert,
            mask: false,
            btns: 1,
          });
          // this.sortAlertModal.show = true;
          // this.sortAlertModal.content = data.sort_alert;
        }
        // 获取分拣总条数
        if (data.refresh_count) {
          this.genCountTask();
        } else {
          this.page.total = parseInt(pageParams.count);
          this.endCountLoop();
        }
      } else {
        switch (res.errCode) {
          // 客户未设置区域
          case sort.errorCode.user_no_area:
            this.$snotice({
              title: res.message,
              btnTxt: '前往设置',
              btnClick: () => {
                this.router.push({
                  path: '/userList',
                  query: {
                    area_id: '0',
                  },
                });
              },
            });
            break;
          // 客户区域被删除
          case sort.errorCode.user_area_deleted:
            // this.modalError({
            //   content: res.message,
            //   onOk: () => {}
            // });
            this.$snotice({
              title: res.message,
            });
            break;
          // 客户对应的区域未分配到线路
          case sort.errorCode.area_no_match_line:
            // this.modalError({
            //   content: res.message,
            //   onOk: () => {}
            // });
            this.$snotice({
              title: res.message,
            });
            break;
          default:
            break;
        }
      }
      return list;
    },
    TabsChange(res) {
      this.TabsValue = res;
      if (res == 'name1') {
        this.$refs.formValidate.resetFields();
      } else {
        this.defaultValues(this.rowData);
        this.$refs.sortManyValidate.resetFields();
      }
    },
    getAreaList() {
      this.$request.get(api.getAreaList).then((res) => {
        let defaultItem = {
          id: '',
          name: '全部区域',
        };
        this.areaList = [];
        if (res.status) {
          this.areaList = res.data;
        }
        this.areaList.unshift(defaultItem);
        this.advanceItems[0].items[3].data = this.areaList.map(
          ({ id, name }) => ({ label: name, value: id }),
        );
      });
    },
    getUserList() {
      this.getList();
    },
    handleChangePrint(val) {
      localStorage.setItem('commodityIsprint', val ? '1' : '0');
      bus.$emit('updatePrint', val);
    },
    itemSortComplete(row, sortAmount) {
      row.sort_status = '1';
      row.sort_status_desc = '已分拣';
      row.actual_amount = sortAmount;
      row.isCanReset = true;
    },
    genCountTask() {
      let filters = this.getFilters();
      sort.genSortCount(filters).then((res) => {
        let { status, data } = res;
        if (status) {
          this.countTask.task_no = data.task_no;
          this.getCount();
        } else {
          this.countTask.task_no = '';
        }
      });
    },
    pageChange(page) {
      this.page.currentPage = page;
    },
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    paginationReset() {
      this.page.totalPage = 0;
      this.page.total = 0;
      this.page.currentPage = 1;
    },
    getCount() {
      let taskNo = this.countTask.task_no;
      sort.getSortCount(taskNo).then((res) => {
        let { status, data } = res;
        if (status) {
          // this.pageTotal = parseInt(data.sort_count);
          this.page.total = parseInt(data.sort_count);
          this.endCountLoop();
        } else {
          this.startCountLoop();
        }
      });
    },
    startCountLoop() {
      clearTimeout(this.countTask.timer);
      this.countTask.timer = setTimeout(() => {
        this.countTask.time += this.countTask.frequency;
        this.getCount();
      }, this.countTask.frequency);
    },
    endCountLoop() {
      if (this.countTask.timer) {
        clearTimeout(this.countTask.timer);
      }
      this.countTask.time = 0;
      this.countTask.timer = 0;
    },
    handlerChange(cols) {
      this.cols = this.initCols(cols, this.originCols);
    },
    setTableHeight() {
      if (this.filters.commodity_id) {
        // this.tableHeight = this.getTableHeight() - 177;
      } else {
        // this.tableHeight = this.getTableHeight() - 130;
      }
    },
    checkAmountWarning(rowParams, amount) {
      let warningRate = parseFloat(this.searchConfig.sort_num_threshold);
      let row = rowParams.row;
      let orderAmount = parseFloat(row.order_amount);
      return sort.checkAmountWarning(orderAmount, amount, warningRate, row);
    },
    openSortManyModal(rowParams) {
      console.log(this.sortManyModal.rowParams);
      this.sortManyModal.rowParams = rowParams;
      this.sortManyModal.show = true;
      this.sortManyModal.amount_warning = false;
      this.sortManyModal.title = `${rowParams.row.commodity_name}-多次分拣`;
      this.sortManyFocus();
    },
    sortManyFocus() {
      this.$nextTick(function () {
        document.querySelector('.sort-many-input input').focus();
      });
    },
    quitSort() {
      this.noclearGoods = this.filters.goods;
      this.sortManyModal.show = false;
    },
    doneSort() {
      this.noclearGoods = this.filters.goods;
      this.saveSortMany(1);
    },
    visibleChange(data) {
      if (data == false) {
        this.closeSortManyModal(data);
      }
    },
    closeSortManyModal(data) {
      if (data == 1 || data == false) {
        this.sortManyModal.show = false;
        this.$refs.formValidate.resetFields();
        this.$refs.sortManyValidate.resetFields();
        this.sortManyModal.list = [];
        this.ConstantValue.NumberOfSorting = '';
        this.ConstantValue.AdditionalCopies = '';
        this.sortManyModal.rowParams = {};
        // this.sortManyModal.show = false;
        this.sortManyModal.amount = '';
        this.NumberOfSortingsData = [];
        this.ConstantValue.NumberOfSorting = '';
        this.ConstantValue.AdditionalCopies = '';
        this.orderNumber = '';
        this.TabsValue = 'name1';
        this.getList();
      }
      // if (data == 1) {
      //   this.getList();
      // }
    },
    storeChange(storeId) {
      if (storeId) {
        let selectedStore = this.searchConfig.store_list.find(
          (item) => item.id === storeId,
        );
        this.currentStore = selectedStore || {};
      } else {
        this.currentStore = {};
      }
      this.getList();
    },
    changeDeliveryDate(date) {
      this.deliveryDate = date;
      this.getList();
    },
    handleSearchFocus() {
      this.resetCommodityName();
    },
    selectGoods(e) {
      this.filters.goods = this.goods = e;
      this.$refs.table.setValue('commodity_name', '', true);
      this.getList();
    },
    searchGoods() {
      this.page.pageSize = MAX_PAGE_SIZE;
      this.setTableHeight();
      this.getList();
      this.getSortProgress();
    },
    changeCategory(category) {
      this.filters.category_id = category[0];
      this.filters.category_id2 = category[1];
      this.getList();
    },
    /**
     * 保存多次分拣
     * @param amount 分拣数量
     * @param order_commodity_id 订单商品id
     */
    saveSortMany(event) {
      let rowParams = this.sortManyModal.rowParams;
      let row = rowParams.row;
      let NumberOfSorting = '';
      // let sort_times = this.ConstantValue.AdditionalCopies
      // let checkAmountWarningResult = this.checkAmountWarning(
      //   this.sortManyModal.rowParams,
      //   this.sortManyModal.amount
      // )
      // if (
      //   checkAmountWarningResult !== true
      // ) {
      //   this.sortManyModal.amountWarningContent = checkAmountWarningResult;
      //   this.sortManyModal.amount_warning = true;
      // } else {
      //   this.sortManyModal.amount_warning = false;
      // }
      let params = {
        amount: this.sortManyModal.amount
          ? this.sortManyModal.amount
          : NumberOfSorting,
        sort_times: this.ConstantValue.AdditionalCopies,
        order_commodity_id: row.order_commodity_id,
      };
      if (this.TabsValue == 'name2') {
        this.$refs.formValidate.validate((valid) => {
          if (
            valid &&
            this.ConstantValue.NumberOfSorting !== '' &&
            this.ConstantValue.AdditionalCopies !== ''
          ) {
            this.name1Valids = valid;
            this.sortManyModal.amount = '';
            params.amount =
              Number(this.ConstantValue.NumberOfSorting) *
              Number(this.ConstantValue.AdditionalCopies);
            this.SortingsData =
              Number(this.ConstantValue.NumberOfSorting) *
              Number(this.ConstantValue.AdditionalCopies);
          } else {
            this.sortManyModal.show = true;
          }
        });
      } else {
        delete params.sort_times;
        this.$refs.sortManyValidate.validate((valid) => {
          if (valid) {
            this.sortManyValid = valid;
          } else {
            this.sortManyModal.show = true;
          }
        });
      }
      if (this.name1Valids == true || this.sortManyValid == true) {
        sort.saveSortMany(params).then((res) => {
          const { status, data } = res;
          if (status) {
            this.ConstantValue.NumberOfSorting = '';

            this.NumberOfSortingsData.push(this.SortingsData);
            this.sortManyModal.list.push(this.sortManyModal.amount);
            this.sortManyModal.amount = '';
            // 允许打印
            if (rowParams.row.isCanPrint && !this.disablePrint) {
              this.printTicket(
                row.order_commodity_id,
                true,
                1, // 这个地方给1.按之前逻辑打印份数由后端控制.
                {
                  total_actual_amount:
                    this.TabsValue === 'name2'
                      ? this.NumberOfSortings
                      : this.sortManyTotal,
                },
                // this.ConstantValue.AdditionalCopies
              );
              this.ConstantValue.AdditionalCopies = '';
            }
            // if (event !== 1) {
            //   event.target.focus();
            // }
            this.$nextTick(() => {
              let sortRecordDom = this.$refs.record;
              sortRecordDom.scrollTop = sortRecordDom.scrollHeight;
            });
            this.sortManyFocus();
            this.name1Valids = false;
            this.sortManyValid = false;

            this.closeSortManyModal(event);
            let amountWarningMessage = '';
            if (+data.sort_threshold_status === 2) {
              amountWarningMessage = '您设置的数量已超过阈值';
            } else if (+data.sort_threshold_status === 3) {
              amountWarningMessage = '您设置的数量已低于阈值';
            }
            this.sortManyModal.amountWarningContent = amountWarningMessage;
            this.sortManyModal.amount_warning = !!amountWarningMessage;
          } else {
            this.name1Valids = false;
            this.sortManyValid = false;
            this.modalError({
              content: res.message,
              onOk: () => {
                if (event !== 1) {
                  event.target.focus();
                }
                this.sortManyModal.amount = '';
              },
            });
            this.closeSortManyModal(event);
          }
        });
      }
    },
    setAMountTooltip(message, rowParams) {
      this.$nextTick(() => {
        let tableData = this.$refs.table.getData();
        rowParams.row.amount_warning = true;
        rowParams.row.warning_msg = message;
        Object.keys(tableData[rowParams.index]).forEach((key) => {
          item[key] = rowParams.row[key];
        });
        this.$refs.table.setData(tableData, {}, true);
      });
    },
    /**
     * 保存分拣信息
     * @param amount 分拣数量
     * @param rowParams table中的每行数据
     * @param event 输入框事件对象
     * @param notChoose 是否总是不选择模板
     */
    // eslint-disable-next-line no-unused-vars
    saveSortInfo(amount, rowParams, event, notChoose = false) {
      amount = amount.trim();
      let row = rowParams.row;
      let params = {
        order_commodity_id: row.order_commodity_id,
        amount: amount,
      };
      if (isNaN(amount)) {
        this.$smessage({ type: 'warning', text: '分拣数量必须是数字' });
        return false;
      }
      sort.saveSortInfo(params).then((res) => {
        const { status, data } = res;
        if (status) {
          // 更新已选择项的数据
          this.selectedRows.forEach((item, index) => {
            if (item.order_commodity_id === row.order_commodity_id) {
              console.log('被纠正');
              this.$set(this.selectedRows, index, rowParams.row);
            }
          });
          // 允许打印
          if (row.isCanPrint && !this.disablePrint) {
            this.printTicket(
              row.order_commodity_id,
              undefined,
              undefined,
              undefined,
              true,
            );
          }

          rowParams.row.sort_status = '1';
          rowParams.row.sort_status_desc = '已分拣';
          rowParams.row.actual_amount = amount;
          rowParams.row.isCanReset = true;
          rowParams.row.isCanMarkStockOut = false;
          if (rowParams.row.is_can_cumulative) {
            rowParams.row.sort_progress.sorting =
              Number(rowParams.row.sort_progress.sorting) + 1;
          }
          this.cancelStockOut(rowParams.row);
          if (
            rowParams.row.base_unit !== rowParams.row.unit &&
            rowParams.row.unit_convert.toUpperCase() === 'Y'
          ) {
            if (
              Number(rowParams.row.order_amount) *
                Number(rowParams.row.unit_num) -
                Number(rowParams.row.actual_amount) >
              0
            ) {
              rowParams.row.isCanPartMarkStockOut = true;
            }
          } else {
            if (
              Number(rowParams.row.order_amount) -
                Number(rowParams.row.actual_amount) >
              0
            ) {
              rowParams.row.isCanPartMarkStockOut = true;
            }
          }

          this.postList.splice(
            rowParams.index,
            1,
            this.cloneObj(rowParams.row),
          );

          this.getSortProgress();
          if (+data.sort_threshold_status === 2) {
            this.setAMountTooltip('您设置的数量已高于阈值', rowParams);
          } else if (+data.sort_threshold_status === 3) {
            this.setAMountTooltip('您设置的数量已低于阈值', rowParams);
          }
        } else {
          // 阈值相关的提示不采用全局提示
          if (+res.errCode === 50001 || +res.errCode === 50002) {
            this.setAMountTooltip(res.message, rowParams);
          } else {
            rowParams.row.actual_amount = '';
            this.$smessage({
              type: 'warning',
              text: res.message
                ? res.message
                : res.errMsg
                  ? res.errMsg
                  : '未知错误',
            });
          }
        }
      });
    },
    print(params) {
      let row = params.row;
      const data = this.$refs.table.getData();
      let amount = data[params.index]['input_amount'];
      console.log('input_amount', amount);
      if (row.sort_status == sort.sortStatus.sorted) {
        this.printTicket(row.order_commodity_id);
      } else {
        // let warningCheck = this.checkAmountWarning(params, amount);
        // if (warningCheck !== true) {
        //   params.row.amount_warning = true;
        //   params.row.warning_msg = warningCheck;
        // } else {
        //   params.row.amount_warning = false;
        //   params.row.warning_msg = '';
        // }
        this.saveSortInfo(amount, params);
      }
    },
    /**
     * 打印标签
     * @param orderCommodityId 订单商品id
     */
    printTicket(
      orderCommodityId,
      isSortMany,
      additionalCopies = 1,
      params,
      notChoose = false,
    ) {
      this.printPickOrder({
        className: 'tpl_print_pick',
        printNumber: additionalCopies,
        mulitPick: isSortMany ? '1' : '',
        dataId: orderCommodityId,
        preview: false,
        params,
        notChoose,
      });
    },
    setFocus(index, refresh) {
      let nextRowIndex =
        index === undefined ? this.activeRow.index * 1 + 1 : index * 1 + 1;
      if (
        // 不走这个逻辑
        // eslint-disable-next-line no-constant-condition
        false &&
        refresh !== false &&
        ((this.activeRow.index == this.list.length - 1 &&
          this.list.length > 1) || // 最后一条纪录
          (this.list.length > 0 &&
            nextRowIndex != 1 &&
            this.list[nextRowIndex] &&
            this.list[nextRowIndex]['sort_status'] == '1')) // 下一个商品已分拣
      ) {
        this.getList();
      } else {
        const tr = document.querySelectorAll('.list-table__tr');
        const idx = index ? index : this.activeRow.index + 1;
        const input = tr[idx] && tr[idx].querySelector('.table-number-input');
        if (input && !input.disabled) {
          return input.focus();
        }
      }
    },
    /**
     * 获取分拣搜索配置
     */
    async getSearchConfig() {
      try {
        let res = await sort.getSortSearchConfig();
        if (res.status) {
          let data = res.data;
          this.searchConfig = res.data;
          this.deliveryDate = data.default_delivery_date;
          this.currentStore = data.store_list[0];
          this.filterItems[0].defaultValue = data.default_delivery_date;
          this.advanceItems[3].items[0].show = this.isOpenOrderTag;
          this.advanceItems[3].items[1].show = this.isOpenOrderTag;
          this.advanceItems[0].items.forEach((item) => {
            if (data[item.key]) {
              item.data = data[item.key].map(({ id, name }) => ({
                label: name,
                value: id,
              }));
            }
          });
          this.advanceItems[0].items[0].data = data.store_list.map(
            ({ id, name }) => ({
              label: name,
              value: id,
            }),
          );
          this.advanceItems[0].items[2].props.storeId = data.store_list[0].id;
          // 商品分类联级数据, 给最后一级的数据添加multiple属性以实现多选
          this.advanceItems[0].items[5].props.data =
            data.commodity_category.map((item) => {
              if (item.children && item.children.length) {
                item.children.forEach((child) => {
                  child.multiple = true;
                });
              }
              return item;
            });
          // this.advanceItems[0].items[5].props.data = data.commodity_category;
          this.advanceItems[0].items[6].props.data = data.line;
          this.advanceItems[0].items[9].props.data = data.provider;
          this.advanceItems[1].items[1].show =
            this[this.advanceItems[1].items[1].show];
          this.advanceItems[2].items.forEach((item) => {
            if (item.label === '经营方式') {
              if (this.isOpenProviderDeliver) {
                item.show = true;
              }
              item.props.data = [
                {
                  value: '1',
                  label: '自营',
                  children: [],
                },
                {
                  value: '2',
                  label: '联营',
                  children: data.pool_provider.map((item) => {
                    return {
                      value: item.id,
                      label: item.name,
                    };
                  }),
                },
              ];
            }
          });
          this.advanceItems[3].items.forEach((item) => {
            if (item.key === 'refer_id') {
              let referOptions = data.sale_list.map((item) => {
                return {
                  value: item.id,
                  label: item.name,
                };
              });
              referOptions = [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: '-1',
                  label: '未设置',
                },
                ...referOptions,
              ];
              item.data = referOptions;
            }
          });
          let orderGoodsTagFilter = this.advanceItems[3].items.find(
            (item) => item.key === 'order_commodity_tag',
          );
          if (orderGoodsTagFilter) {
            orderGoodsTagFilter.show = this.isOpenOrderCommodityTag;
            orderGoodsTagFilter.props.data = data.order_commodity_tag_list.map(
              (item) => {
                return { value: item.id, label: item.name };
              },
            );
          }
          if (this.$refs.table) {
            // 异常订单检查
            this.$refs.table.setValue(
              'delivery_date',
              data.default_delivery_date,
              true,
            );
            this.$refs.table.setValue('store_id', data.store_list[0].id, true);
            let arr =
              StorageUtil.getLocalStorage('sorting-order-goods-tag-list') || [];
            let sortingList = arr.filter((r) => {
              return (
                data.order_commodity_tag_list.findIndex((e) => e.id == r) > -1
              );
            });
            if (sortingList.length) {
              this.$refs.table.setValue('order_commodity_tag', sortingList);
            }
            let is_produce_date =
              StorageUtil.getLocalStorage('SORTING_IS_PRODUCE_DATE');
            if (is_produce_date) {
              this.$refs.table.setValue('is_produce_date', is_produce_date);
            }
          }
          // this.getList();
        }
      } catch (e) {
        console.log(e);
      }
    },
    /**
     * 重置分拣
     */
    resetSort(params) {
      let row = params.row;
      let order_commodity_id = row.order_commodity_id;
      sort.resetSort({ order_commodity_id }).then((res) => {
        if (res.status) {
          this.getSortProgress();
          params.row.sort_status = '0';
          params.row.sort_status_desc = '未分拣';
          params.row.actual_amount = '';
          params.row.amount_warning = false;
          params.row.warning_msg = '';
          params.row.isCanReset = false;
          params.row.isCanMarkStockOut = true;
          params.row.isCanPartMarkStockOut = false;
          params.row.sort_progress.sorting = 0;
          this.cancelStockOut(params.row);
          this.postList.splice(params.index, 1, this.cloneObj(params.row));
          this.$nextTick(() => {
            this.setFocus(params.index, false);
          });
          this.$smessage({ type: 'success', text: '重置成功！' });
        } else {
          this.$smessage({ type: 'warning', text: res.message });
        }
      });
    },
    /**
     * 获取分拣进度
     */
    getSortProgress() {
      // this.resetSortProgress();
      const { delivery_date, store_id } = this.$refs.table.getParams();
      let params = {
        commodity_id: this.curGoodsId,
        delivery_date,
        store_id,
      };
      sort.getSortProgress(params).then((res) => {
        if (res.status) {
          this.sortProcessList = res.data;
        } else {
          this.resetSortProgress();
        }
      });
    },
    /**
     * 重置分拣进度数据
     */
    resetSortProgress() {
      this.sortProcessList = [];
    },
    resetActiveRow() {
      this.activeRow = {
        index: 0,
        event: null,
      };
    },
    showLoading() {
      this.loading = true;
      this.noDataText = '数据加载中...';
    },
    endLoading() {
      this.loading = false;
      this.noDataText = '暂无数据';
    },
    /**
     * 商品名称搜索词变化
     */
    queryChange(query) {
      if (!this.filters.commodity_id) {
        this.filters.commodity_name = query;
      } else {
        this.filters.commodity_name = '';
      }
    },
    resetCommodityName() {
      this.$refs.table.setValue('commodity_name', '', true);
      // this.$refs.searchGoods.setQuery('');
    },
    saveMfgDate(id, date) {
      let par = {
        order_commodity_id: id,
        produce_date: date.replace(/,/g, '、'),
      };
      sort.submitDate(par).then((res) => {
        if (res.status) {
          this.$smessage({ type: 'success', text: '保存生产日期成功！' });
        } else {
          this.$smessage({ type: 'warning', text: res.message });
        }
      });
    },
    getList(resetPage) {
      if (this.$refs.table) {
        this.$refs.table.fetchData(resetPage);
      }
    },
    getFilters() {
      let params = this.$refs.table.getParams();
      if (!params.store_id) {
        params.store_id = this.storage.getLocalStorage('init_ware_house_id');
      }

      if (params.user_tag) {
        params.user_tag = params.user_tag.join(',');
      }

      let { purchase_type, purchase_type_value } = params;
      if (Number(purchase_type) === 1) {
        params.agent_id = purchase_type_value;
        delete params.provider_id;
      } else {
        params.provider_id = purchase_type_value;
        delete params.agent_id;
      }
      params.page = this.page.currentPage;
      params.pageSize = this.page.pageSize;
      params.order_delivery_date = params.delivery_date;
      params.commodity_id =
        this.filters.goods && this.filters.goods.commodity_id
          ? this.filters.goods.commodity_id
          : '';
      if (Array.isArray(params.order_commodity_tag))
        params.order_commodity_tag = params.order_commodity_tag.join();
      return params;
    },
    resetPage() {
      this.page.totalPage = 0;
      this.page.total = 0;
      this.page.currentPage = 1;
      this.filters.page = 1;
    },
    changePage(page) {
      this.page.currentPage = page;
      this.filters.page = page;
      this.getList(false);
    },
    changePageSize(pageSize) {
      this.filters.pageSize = pageSize;
      this.page.pageSize = pageSize;
      this.getList();
    },
  },
};
</script>

<style lang="less" scoped>
@deep: ~'>>>';
// 产品要求改 Tooltip 组件样式
/deep/ .ivu-tooltip .ivu-tooltip-content .ivu-tooltip-inner {
  background: #fff5e5;
  color: #ff9f00;
}
/deep/ .ivu-tooltip .ivu-tooltip-content .ivu-tooltip-arrow {
  border-right-color: #fff5e5;
}
@deep: ~'>>>';
.dropdown-btn-box {
  height: 30px;
  vertical-align: top;
  display: inline-block;
  @{deep} .dropdown-btn {
    overflow: hidden;
    display: inline-block;
    .sui-icon {
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover {
      .sui-icon {
        transform: rotate(180deg);
      }
    }
  }
}
.sorting-commodity@{deep} {
  .slot-right {
    line-height: 14px;
    .ivu-checkbox-wrapper {
      height: auto;
      line-height: normal;
    }
  }
  .common__operation {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .slot-right {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .more-btn-group {
    font-size: 0;
    .main-btn {
      .ivu-btn {
        border-radius: 2px 0 0 2px;
        border-right-width: 0;
      }
    }
    .more-btn {
      .ivu-btn {
        border-radius: 0 2px 2px 0;
      }
    }
  }
  .table-number-input {
    height: 28px;
    border-radius: 2px;
  }
  .sdp-table__tr.row-focused {
    .sdp-table__td,
    .warning,
    .light {
      color: #f13130;
      background: #fef6f5;
      font-size: 13px;
      font-weight: 500;
    }
    input {
      border-color: #f13130;
    }
  }
  .warning {
    color: #FF6E00;
  }
  .light {
    color: rgba(0, 0, 0, 0.5);
  }
  .process-list {
    height: 52px;
    line-height: 52px;
    background: rgba(250, 251, 252, 0.5);
    border-radius: 1px 1px 0px 0px;
    border: solid #e8e8e8;
    border-width: 1px 1px 0 1px;
    padding: 0 18px;
    .process-item {
      display: flex;
      align-items: center;
    }
    h6 {
      font-size: 13px;
      margin-left: 12px;
    }
    .sorted {
      color: #03ac54;
      margin: 0 4px;
    }
    .sort {
      color: #ff9f00;
      margin: 0 4px;
    }
  }
}
#sorting-commodity {
  .ivu-form-inline {
    .ivu-form-item-content {
      padding-right: 0 !important;
    }
  }
  .filters {
    .search-goods {
      width: 240px !important;
    }
  }
  .ivu-table-cell {
    white-space: normal !important;
    padding-left: 10px;
    padding-right: 10px;
  }
  .btnsPanel_muti span {
    font-size: 14px;
    font-weight: normal;
  }
  td {
    position: relative;
  }
}
</style>
<style scoped lang="less">
.ml10 {
  margin-left: 5px !important;
}
.common {
  background-color: #fff;
}
#sorting-commodity {
  .ivu-form-inline {
    .ivu-form-item {
      margin-bottom: 15px;
    }
  }
  .filters {
    .current-store {
      margin-left: 15px;
      font-size: 16px;
      font-weight: bold;
    }
    .filter-row-1 {
      .ivu-select {
        width: 140px;
      }
    }
    .filter-row-2 {
      .ivu-select {
        width: 120px;
      }
    }
    .filter-row-3 {
      .ivu-select {
        width: 140px;
      }
      margin-bottom: 15px;
    }
    .sort-progress {
      font-size: 16px;
      font-weight: bold;
      .remain-amount {
        color: red;
      }
      .sorted-amount {
        color: #03ac54;
      }
    }
  }
}
/deep/ .search__type--remoteSelect .ivu-select-selection > div {
  display: flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
  .ivu-tag {
    flex-shrink: 0;
  }
  .ivu-select-input {
    flex: 1 1;
    width: inherit !important;
    padding-right: 5px;
    padding-left: 10px;
    line-height: 28px;
  }
}
</style>
