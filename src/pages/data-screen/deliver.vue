<template>
  <div class="data-screen wrapper">
    <div class="top-area">
      <Top 
        @refresh="refresh" 
        @fetchData="init" 
        @close="closePage" 
        :showSettingField="true"
        @setting="handleOpenSetting"
        subTitle=" • 分拣大屏"  />
    </div>
    <div class="main-area">
      <div class="line-1">
        <div class="column-1">
          <div class="top"><span></span>总分拣进度</div>
          <div class="pr-box-warp">
            <div v-if="deliverDegree.show" class="pr-box">
              <PrDegree
                textFontSize="15"
                :lineColor="deliverDegree.lineColor"
                :text="deliverDegree.text"
                :degree="deliverDegree.degree"
                :barWidth="deliverDegree.deliverDegreeBarWith"
                :radius="deliverDegree.radius"
                v-show="deliverDegree.show"
              />
            </div>
          </div>
          <div class="content-box">
            <div @click="showSettingBox" class="content-item">
              发货日期：{{ selectDate || getTodayOrTomorrow()
              }}<img :src="calendarIcon" />
            </div>
            <div class="content-item">单位（个）</div>
            <div class="content-item">
              总数：{{ infor.sort_process.sort_count }}
            </div>
            <div class="content-item">
              未分拣：{{ infor.sort_process.unsort_count }}
            </div>
            <div class="content-item">
              已分拣：{{ infor.sort_process.sorted_count }}
            </div>
          </div>
        </div>
        <div class="column-2">
          <div class="top"><span></span>TOP10分拣绩效</div>
          <div class="tip-box">
            <span v-for="(item, index) in chart1Options.series" :key="index">
              <i :style="{ background: item.color }"></i>
              {{ item.name }}
            </span>
          </div>
          <div class="bottom">
            <div class="chart1" id="chart1"></div>
          </div>
        </div>
      </div>
      <div class="line-2">
        <div class="column-1">
          <div class="top"><span></span>分拣员分拣排名  
          <Tooltip :content="`按${selectDate || getTodayOrTomorrow()}实时分拣数据排序`" max-width="400">
            <i class="icon-help1 iconfont icontip" style="font-size: 13px;margin-left: 8px;"></i>
          </Tooltip></div>
          <div class="bottom">
            <div class="bottom-item">
              <span>排名</span>
              <span>姓名</span>
            </div>
            <div
              v-for="(item, index) in infor.sorter_rank_list"
              :key="index"
              class="bottom-item content-item"
              :class="{
                yellow: index === 0,
                green: index === 1,
                blur: index === 2
              }"
            >
              <span>{{ index + 1 }}</span>
              <span>{{ item.name }}</span>
            </div>
          </div>
        </div>
        <div class="column-2">
          <div class="top"><span></span>客户分拣进度</div>
          <div class="bottom">
            <div
              v-for="(item, index) in commodityStat"
              :key="index"
              class="bottom-item bottom-item--customer-sort"
            >
              <div class="pr-box-warp">
                <div class="pr-box">
                  <PrDegree
                    textFontSize="14px"
                    lineColor="#00DCA3"
                    :text="item.tempo + '%'"
                    :degree="item.tempo"
                    v-show="item.show"
                  />
                </div>
              </div>
              <div class="pr-text line-clamp-3">{{ item.user_name }}</div>
            </div>
          </div>
        </div>
        <div class="column-3">
          <div class="top"><span></span>商品分拣进度</div>
          <div class="bottom">
            <div
              v-for="(item, index) in sortOrderCommodityStat"
              :key="index"
              class="bottom-item"
            >
              <div class="top-text">{{ item.commodity_name }}</div>
              <div class="progress-line-bg"></div>
              <div
                :style="{ width: item.width }"
                class="progress-line"
                :class="{ 'width-zero': item.width === '0%' }"
              >
                <div class="progress-line-degree">{{ item.tempo }}%</div>
              </div>
            </div>
          </div>
        </div>
        <div class="column-4">
          <div class="item1">
            <div class="top"><span></span>订单差异分拣</div>
            <div v-if="sortOrderCommodity.length" class="bottom">
              <div
                v-for="(item, index) in sortOrderCommodity"
                :key="index"
                class="bottom-item"
              >
                <div class="content-box">
                  <span class="img-box">
                    <img v-if="+item.diff_type === 1" :src="editIcon" />
                    <img v-else :src="deleteIcon" />
                  </span>
                  {{ item.diff_type_desc }}
                </div>
              </div>
            </div>
            <div v-else class="bottom">
              <div
                class="bottom-item"
              >
                <div class="content-box">-</div>
              </div>
            </div>
          </div>
          <div class="item2">
            <div class="top"><span></span>缺货商品</div>
            <div class="bottom">
              <div class="bottom-item">
                <span>商品</span>
                <span>数量</span>
              </div>
              <div
                v-for="(item, index) in sortOrderCommodityForStockout"
                :key="index"
                class="bottom-item bottom-content"
              >
                <span>{{ item.name }}</span>
                <span>{{ item.order_amount }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="other-screen">
      <span class="other-screen__item" v-for="item in otherScreenList" :key="item.path" @click="onGoOtherScreen(item)">{{item.name}}</span>
    </div>
    
    <Calendar @on-change="changeDate" ref="Calendar" />

    <setting 
      v-if="showSetting"
      @on-change="handleChangeSetting"
      @on-close="() => showSetting = false"
      :default-legend="show_field"
      :default-sort-field="sort_field"
      ref="setting" />
  </div>
</template>
<script>
import { get } from '../../api/request';
import echarts from '@/common/init-echarts.js'

import calendarIcon from '../../assets/images/deliverScreen/calendar.png';
import deleteIcon from '../../assets/images/deliverScreen/delete.png';
import editIcon from '../../assets/images/deliverScreen/edit.png';
import Top from './components/Top';
import PrDegree from './components/PrDegree';
import Calendar from './components/Calendar'
import setting from './components/setting'
import storage from '@/util/storage';

export default {
  data() {
    return {
      showSetting: false,
      sort_field: storage.getLocalStorage('deliver-screen-sort_field') || 'pack_num',
      show_field: storage.getLocalStorage('deliver-screen-show_field') ||['order_num', 'pack_num', 'total_price'],
      interval1: '',
      interval2: '',
      interval3: '',
      interval4: '',
      refreshInterval: '',
      infor: {
        sort_process: {},
        sorter_rank_list: []
      },
      calendarIcon,
      deleteIcon,
      editIcon,
      // 其他大屏
      otherScreenList: [
        {
          name: '数据大屏',
          path: '/data-screen',
        },
        {
          name: '分拣大屏',
          path: '/deliver-screen',
        },
        {
          name: '配送大屏',
          path: '/distribution-screen',
        }
      ],
      // 总分拣进度相关属性
      deliverDegree: {
        text: '',
        degree: 0,
        lineColor: '#00DCDC',
        show: false,
        deliverDegreeBarWith: 12,
        radius: '170%'
      },
      sortProgressData: {},
      // TOP10分拣绩效相关属性
      chart1: {},
      chart1Options: {},
      // 客户分拣进度
      commodityStat: [],
      // 商品分拣速度
      sortOrderCommodityStat: [],
      // 订单差异分拣
      sortOrderCommodity: [],
      sortOrderCommodityBak: [],
      sortOrderCommodityIndex: -1,
      // 缺货商品
      sortOrderCommodityForStockoutIndex: -1,
      sortOrderCommodityForStockout: [],
      selectDate: '',
      miniMode: false,
    };
  },
  components: {
    Top,
    PrDegree,
    Calendar,
    setting
  },
  destroyed() {
    clearInterval(this.interval1);
    clearInterval(this.interval2);
    clearInterval(this.interval3);
    clearInterval(this.interval4);
    clearInterval(this.refreshInterval);
  },
  mounted() {
    window.addEventListener('resize', () => {
      this.charts1 && this.charts1.resize();
    });
    // this.init();
    // 每隔3个小时刷新一遍页面，改为5分钟
    this.refreshInterval = setInterval(() => {
      this.refresh();
    }, 5 * 60 * 1000);
  },
  methods: {
    onGoOtherScreen(item) {
      this.$router.replace(item.path);
    },
    toggleMiniMode (mode) {
      this.miniMode = mode;
    },
    closePage () {
      clearInterval(this.interval1);
      clearInterval(this.interval2);
      clearInterval(this.interval3);
      clearInterval(this.interval4);
      clearInterval(this.refreshInterval);
    },
    refresh () {
      clearInterval(this.interval1);
      clearInterval(this.interval2);
      clearInterval(this.interval3);
      clearInterval(this.interval4);
      this.clearData();
      this.init();
    },
    changeDate (year, month, day) {
      clearInterval(this.interval1);
      clearInterval(this.interval2);
      clearInterval(this.interval3);
      clearInterval(this.interval4);
      this.selectDate = `${year}-${month}-${day}`;
      this.init();
    },
    showSettingBox () {
      // this.$refs.Calendar.showSettingBox(this.selectDate || this.getToday(false));
      this.$refs.Calendar.showSettingBox(this.selectDate || this.getTodayOrTomorrow());
    },
    init() {
      // this.getDeliverTop10Data();
      this.getTodayOrTomorrow();
      this.getSortStatistics();
      this.getUserOrderCommodityStat(1);
      setTimeout(() => {
        this.getSortOrderCommodityStat(1);
        this.getSortOrderCommodity(1);
      }, 1000);
      // this.getSortOrderCommodityForStockout(1);
    },
    handleChangeSetting(sort_field, show_field) {
      this.sort_field = sort_field
      this.show_field = show_field
      this.getSortStatistics()
      this.showSetting = false
    },
    handleOpenSetting() {
      this.showSetting = true
    },
    handleSetChart1() {
      let legend = [
        { name: '分拣订单数', key: 'order_num', color: '#29B0FF' },
        { name: '分拣包裹数', key: 'pack_num', color: '#00DCA3' },
        { name: '分拣金额', key: 'total_price', color: '#FFB42C' },
        { name: '分拣数量', key: 'sort_num', color: '#FD5A4B' },
        { name: '分拣重量', key: 'weight_num', color: '#2678FF' },
      ]

      legend = legend.filter(k => this.show_field.indexOf(k.key) > -1)

      const series = legend.map(val => {
        return {
          name: val.name,
          type: 'bar',
          barGap: '125%',
          barWidth: '8px',
          itemStyle: {
            barBorderRadius: [1, 1, 1, 1]
          },
          color: val.color,
          label: {
            show: false, //开启显示
            position: 'top', //在上方显示
            textStyle: {
              //数值样式
              color: '#C9C9C9',
              fontSize: 8,
              fontFamily: 'PingFangSC-Regular,PingFang SC',
              fontWeight: '400'
            }
          },
          data: this.infor.sorter_rank_list.map(e => +e[val.key])
        }
      })

      this.chart1Options = {
        xAxis: [
          {
            type: 'category',
            data: this.infor.sorter_rank_list.map(val => val.name),
            axisLine: {
              lineStyle: {
                color: 'rgba(0,220,220,0.3)'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(201,201,201,1)',
                fontSize: '11px',
                fontFamily: 'PingFangSC-Regular,PingFang SC',
                fontWeight: '400',
                align: 'left'
              },
              interval: 0,
              formatter: function (value) {
                // 超过5个字符则截断并添加...
                return value.length > 5 ? value.substring(0, 5) + '...' : value
              }
            }
          }
        ],
        grid: {
          top: 13,
          left: 0,
          right: 0,
          bottom: 0,
          containLabel: true
        },
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              fontStyle: 16,
              color: '#529cd1'
            },
            axisTick: {
              show: false
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(0,220,220,0.3)'
              }
            },
            axisLine: {
              lineStyle: {
                color: 'rgba(0,220,220,0.3)'
              }
            },
            axisLabel: {
              show: true,
              textStyle: {
                color: 'rgba(201,201,201,1)',
                fontSize: '11px',
                fontFamily: 'PingFangSC-Regular,PingFang SC',
                fontWeight: '400'
              }
            }
          }
        ],
        series,
      }

      this.charts1 = echarts.init(document.getElementById('chart1'));
      this.charts1.clear();
      this.charts1.setOption(this.chart1Options);
    },
    // 根据当前是否超过12点，超过12点获取明日时间，不超过就获取今日时间
    getTodayOrTomorrow() {
      let now = new Date();
      let hours = now.getHours();
      let isAfterNoon = hours >= 12;
      if (isAfterNoon) {
        now.setDate(now.getDate() + 1); // 将日期设置为明日
      }
      let m = now.getMonth() + 1 >= 10 ? now.getMonth() + 1 : '0' + (now.getMonth() + 1);
      let d = now.getDate() >= 10 ? now.getDate() : '0' + now.getDate();
      return `${now.getFullYear()}-${m}-${d}`;
    },

    // 获取今日时间
    getToday({ trans }) {
      let now = new Date();
      if (trans) {
        let weekMap = {
          1: '一',
          2: '二',
          3: '三',
          4: '四',
          5: '五',
          6: '六',
          0: '天'
        };
        return {
          day: `${now.getFullYear()}年${now.getMonth() +
            1}月${now.getDate()}日`,
          week: `星期${weekMap[now.getDay()]}`
        };
      }
      let m =
        +now.getMonth() + 1 >= 10
          ? +now.getMonth() + 1
          : '0' + (+now.getMonth() + 1);
      let d = +now.getDate() >= 10 ? +now.getDate() : '0' + +now.getDate();
      return `${now.getFullYear()}-${m}-${d}`;
    },
    // 切换站点的时候清空数据
    clearData () {
      this.infor = {
        sort_process: {},
        sorter_rank_list: []
      }
      this.deliverDegree = {
        text: '',
        degree: 0,
        lineColor: '#00DCDC',
        show: false,
        deliverDegreeBarWith: 18,
        radius: '170%'
      }
      this.sortProgressData = {};
      this.commodityStat = [];
      this.sortOrderCommodityStat = [];
      this.sortOrderCommodity = [];
      this.sortOrderCommodityBak = [];
      this.sortOrderCommodityIndex = -1;
      this.sortOrderCommodityForStockoutIndex = -1;
      this.sortOrderCommodityForStockout = [];
    },
    getSortStatistics() {
      const options = {
        // delivery_date: this.selectDate || this.getToday(false),
        delivery_date: this.selectDate || this.getTodayOrTomorrow(),
        sort_type: 'desc',
        sort_field: this.sort_field
      }

      get(this.apiUrl.dataScreen.sortStatistics, options).then(res => {
        if (res.status) {

          this.infor = res.data || {};
          // 给默认数据
          if (this.infor.sorter_rank_list.length === 0) {
            this.infor.sorter_rank_list = [{
              index: 0,
              name: '-'
            }]
          }
          if (this.infor.stock_out_commodity_list.length === 0) {
            this.infor.stock_out_commodity_list = [{
              name: '-',
              order_amount: '-',
            }]
          }
          let degree = +this.infor.sort_process.sort_percent.replace('%', '');
          this.deliverDegree.degree = degree;
          this.deliverDegree.text = degree + '%';
          this.deliverDegree.show = true;
          if (this.infor.sorter_rank_list) {
            this.handleSetChart1();
          }
          if (this.infor.stock_out_commodity_list.length) {
            this.infor.stock_out_commodity_list.map(item => {
              if (item.name.length >= 9) {
                item.name = item.name.substr(0, 7) + '...';
              }
            });
            // this.sortOrderCommodityForStockout = this.infor.stock_out_commodity_list;
            this.autoSetSortOrderCommodityForStockout();
            if (this.infor.stock_out_commodity_list.length > 8) {
              this.interval2 = setInterval(() => {
                this.autoSetSortOrderCommodityForStockout();
              }, 20 * 1000);
            }
          }
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    autoSetSortOrderCommodityForStockout() {
      this.sortOrderCommodityForStockout = [];
      this.sortOrderCommodityForStockoutIndex += 1;
      let max = Math.ceil(this.infor.stock_out_commodity_list.length / 8) - 1;
      if (this.sortOrderCommodityForStockoutIndex > max) {
        this.sortOrderCommodityForStockoutIndex = 0;
      }
      let start = this.sortOrderCommodityForStockoutIndex * 7;
      for (var i = 0; i <= 7; i++) {
        if (this.infor.stock_out_commodity_list[start + i]) {
          this.sortOrderCommodityForStockout.push(
            this.infor.stock_out_commodity_list[start + i]
          );
        }
      }
    },
    // 获取客户分拣进度
    getUserOrderCommodityStat(page, looping) {
      get(this.apiUrl.getUserOrderCommodityStat, {
        // delivery_date: this.selectDate || this.getToday(false),
        delivery_date: this.selectDate || this.getTodayOrTomorrow(),
        page: page,
        pageSize: '8',
        check_sort_result: '0'
      }).then(res => {
        if (res.status) {
          let commodityStatCopy = this.deepClone(res.data.list);
          res.data.list.forEach(item => {
            item.show = false;
            item.tempo = 0;
            // if (item.user_name.length >= 5) {
            //   item.user_name = item.user_name.substr(0, 4) + '...';
            // }
          });
          this.commodityStat = res.data.list || [];
          let pageParams = res.data.pageParams || {};
          let maxPage = Math.ceil(+pageParams.count / 8);
          if (maxPage > 1 && !looping) {
            this.setLoop(
              'getUserOrderCommodityStat',
              maxPage,
              5000,
              'interval3'
            );
          }
          setTimeout(() => {
            // 给默认数据
            if (this.commodityStat.length === 0) {
              for (let i = 0; i < 8; i++) {
                this.commodityStat.push({
                  user_name: '-',
                  tempo: '0'
                })
              }
              this.commodityStatCopy = this.deepClone(this.commodityStat);
            }
            this.commodityStat.forEach((item, index) => {
              item.show = true;
              item.tempo = commodityStatCopy[index].tempo;
              // 必须改变 tempo 的值，才能渲染图表
              if (commodityStatCopy[index].tempo === 0) {
                item.tempo = 1;
                setTimeout(() => {
                  item.tempo = 0;
                });
              }
            });
          }, 100);
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    // 获取商品分拣速度
    getSortOrderCommodityStat(page, looping) {
      get(this.apiUrl.getSortOrderCommodityStat, {
        // delivery_date: this.selectDate || this.getToday(false),
        delivery_date: this.selectDate || this.getTodayOrTomorrow(),
        page: page,
        pageSize: '20',
        diff_type: '',
        commodity_id: '',
        store_id: '',
        store_area_id: '',
        standard_status: '',
        commodity_code: '',
        commodity_name: '',
        user_name: '',
        line_id: '',
        category_id: '',
        category_id2: '',
        search_value: '',
        sort_status: '',
        delivery_time: '',
        check_sort_result: '0',
        // order_delivery_date: this.getToday(false)
        order_delivery_date: this.getTodayOrTomorrow()
      }).then(res => {
        if (res.status) {
          // sortOrderCommodityStatCopy = this.deepClone(res.data.list);
          res.data.list.forEach(item => {
            item.width = '0%';
          });
          this.sortOrderCommodityStat = res.data.list || [];
          if (this.sortOrderCommodityStat.length === 0) {
            for (let i = 0; i < 20; i++) {
              this.sortOrderCommodityStat.push({
                commodity_name: '-',
                tempo: '0',
              })
            }
          }
          let pageParams = res.data.pageParams || {};
          let maxPage = Math.ceil(+pageParams.count / 20);
          if (maxPage > 1 && !looping) {
            this.setLoop(
              'getSortOrderCommodityStat',
              maxPage,
              20 * 1000,
              'interval4'
            );
          }
          setTimeout(() => {
            this.sortOrderCommodityStat.forEach(item => {
              item.width = item.tempo + '%';
            });
          }, 100);
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    // 获取订单差异分拣
    getSortOrderCommodity(page) {
      get(this.apiUrl.getSortOrderCommodity, {
        // delivery_date: this.selectDate || this.getToday(false),
        delivery_date: this.selectDate || this.getTodayOrTomorrow(),
        page: page,
        pageSize: '999',
        diff_type: '0',
        commodity_id: '',
        store_id: '',
        store_area_id: '',
        standard_status: '',
        commodity_code: '',
        commodity_name: '',
        user_name: '',
        line_id: '',
        category_id: '',
        category_id2: '',
        search_value: '',
        sort_status: '',
        delivery_time: '',
        // order_delivery_date: this.getToday(false)
        order_delivery_date: this.getTodayOrTomorrow()
      }).then(res => {
        if (res.status) {
          this.sortOrderCommodityBak = res.data.list || [];
          let pageParams = res.data.pageParams || {};
          let maxPage = Math.ceil(+pageParams.count / 4);
          this.autoSortOrderCommodity(maxPage);
          if (maxPage > 1) {
            this.interval1 = setInterval(() => {
              this.autoSortOrderCommodity(maxPage);
            }, 20 * 1000);
          }
        } else {
          this.errorMessage(res.message);
        }
      });
    },
    // 定时刷新订单差异分拣列表
    autoSortOrderCommodity(maxPage) {
      this.sortOrderCommodity = [];
      this.sortOrderCommodityIndex += 1;
      let start = this.sortOrderCommodityIndex * 4;
      if (this.sortOrderCommodityIndex >= maxPage - 1) {
        this.sortOrderCommodityIndex = -1;
      }
      for (var i = 0; i <= 3; i++) {
        if (this.sortOrderCommodityBak[start + i]) {
          this.sortOrderCommodity.push(this.sortOrderCommodityBak[start + i]);
        }
      }
    },
    // 定时刷新数据
    setLoop(funcKey, maxPage, time, intervalKey) {
      let currentPage = 1;
      this[intervalKey] = setInterval(() => {
        currentPage += 1;
        if (currentPage > maxPage) {
          currentPage = 1;
        }
        this[funcKey](currentPage, true);
      }, time || 20 * 1000);
    }
  }
};
</script>
<style lang="less" scoped>
// 占据全屏
.data-screen {
  position: absolute;
  left: 0px;
  top: 0px;
  padding-top: 0px !important;
  z-index: 500;
  margin: 0px;

  .main {
    margin-right: 0px;
  }
}
.wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  margin: 0 !important;
  background: url('https://img.shudongpoo.com/supertest/upload_pic/com_thumb_20200630140217ca7780285efad56925854.png')
    no-repeat;
  background-size: cover;
  .top-area {
    height: 9%;
    width: 100%;
    // background: pink;
    padding: 0 3%;
    display: flex;
    justify-content: center;
    position: relative;
  }
  .main-area {
    width: 94%;
    height: 83.6%;
    margin: 0 3%;
    margin-top: 2%;
    display: flex;
    flex-direction: column;
    .top {
      font-size: 16rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(201, 201, 201, 1);
      line-height: 22px;
      position: absolute;
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: linear-gradient(
          305deg,
          rgba(12, 21, 48, 1) 0%,
          rgba(0, 220, 220, 1) 100%
        );
        opacity: 0.8;
        border-radius: 8px;
        margin-right: 10px;
      }
    }
    .line-1 {
      width: 100%;
      height: 25.5%;
      display: flex;
      margin-bottom: 20px;
      .column-1 {
        background: rgba(0, 27, 54, 0.7);
        border-radius: 10px;
        border: 1px solid rgba(82, 255, 255, 0.3);
        width: 35.6%;
        height: 100%;
        margin-right: 20px;
        position: relative;
        .top {
          top: 8.7%;
          left: 3.1%;
        }
        .pr-box-warp {
          position: absolute;
          width: 21.9%;
          height: 1;
          padding-bottom: 21.9%;
          left: 21.3%;
          top: 26.1%;
          .pr-box {
            position: absolute;
            width: 100%;
            height: 100%;
          }
        }
        .content-box {
          width: 31.1%;
          height: 82.6%;
          position: absolute;
          top: 8.7%;
          left: 59.5%;
          display: flex;
          justify-content: space-between;
          flex-direction: column;
          .content-item {
            font-size: 16rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: rgba(255, 255, 255, 1);
            line-height: 16px;
            display: flex;
            align-items: center;
            img {
              height: 40%;
              width: auto;
              margin-left: 10px;
              cursor: pointer;
            }
          }
        }
      }
      .column-2 {
        flex: 1;
        background: rgba(0, 27, 54, 0.7);
        border-radius: 10px;
        border: 1px solid rgba(82, 255, 255, 0.3);
        width: 100%;
        height: 100%;
        position: relative;
        .top {
          top: 8.7%;
          left: 1.8%;
        }
        .tip-box {
          width: 50%;
          height: 6%;
          position: absolute;
          top: 8.7%;
          right: 1.8%;
          font-size: 10rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(201, 201, 201, 1);
          line-height: 10px;
          text-align: right;
          span {
            display: inline-block;
            margin-left: 3%;
            i {
              display: inline-block;
              width: 8px;
              height: 8px;
              border-radius: 1px;
              margin-right: 3px;
            }
          }
        }
        .bottom {
          width: 96.4%;
          height: 62.6%;
          position: absolute;
          left: 1.8%;
          top: 28.7%;
          #chart1 {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
    .line-2 {
      flex: 1;
      height: 100%;
      width: 100%;
      display: flex;
      .column-1 {
        width: 16.7%;
        height: 100%;
        background: rgba(0, 27, 54, 0.7);
        border-radius: 10px;
        border: 1px solid rgba(82, 255, 255, 0.3);
        margin-right: 20px;
        position: relative;
        .top {
          top: 3%;
          left: 6.7%;
        }
        .bottom {
          position: absolute;
          width: 86%;
          left: 7%;
          height: 80%;
          top: 12.6%;
          display: flex;
          flex-direction: column;
          .bottom-item {
            border-left: 1px solid #002541;
            border-right: 1px solid #002541;
            height: 7.6%;
            width: 100%;
            display: flex;
            span {
              flex: 1;
              display: inline-flex;
              justify-content: center;
              align-items: center;
              font-size: 12rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(201, 201, 201, 1);
              line-height: 17px;
            }
          }
          .content-item:nth-child(odd) {
            background: #001b36;
          }
          .yellow span {
            color: #ffb42c;
          }
          .green span {
            color: #00dcdc;
          }
          .blur span {
            color: #29b0ff;
          }
          .bottom-item:first-child {
            background: rgba(0, 51, 90, 1);
          }
        }
      }
      .column-2 {
        width: 17.8%;
        height: 100%;
        background: rgba(0, 27, 54, 0.7);
        border-radius: 10px;
        border: 1px solid rgba(82, 255, 255, 0.3);
        margin-right: 20px;
        position: relative;
        .top {
          top: 3%;
          left: 12.5%;
        }
        .bottom {
          height: 81.2%;
          width: 100%;
          position: absolute;
          top: 12.5%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          .bottom-item {
            height: 25%;
            width: 34.2%;
            position: relative;
            .pr-box-warp {
              width: 100%;
              height: 1px;
              padding-bottom: 100%;
              position: relative;
              .pr-box {
                width: 100%;
                height: 100%;
                position: absolute;
              }
            }
            .pr-text {
              width: 100%;
              position: absolute;
              top: 60%;
              padding: 0 10px;
              text-align: center;
              font-size: 16rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
            }
          }
          .bottom-item--customer-sort {
            width: 50%;
            .pr-box-warp {
              .pr-box {
                width: 50%;
                height: 50%;
                left: 25%;
              }
            }
          }
        }
      }
      .column-3 {
        width: 45.6%;
        height: 100%;
        background: rgba(0, 27, 54, 0.7);
        border-radius: 10px;
        border: 1px solid rgba(82, 255, 255, 0.3);
        margin-right: 20px;
        position: relative;
        .top {
          top: 3%;
          left: 2.4%;
        }
        .bottom {
          height: 84.6%;
          width: 85.4%;
          position: absolute;
          top: 9.5%;
          left: 4.8%;
          line-height: 0;
          .bottom-item {
            display: inline-block;
            height: 6.7%;
            width: 40%;
            margin-bottom: 2.9%;
            position: relative;
            .top-text {
              position: absolute;
              top: 0;
              left: 0;
              font-size: 14rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
              line-height: 14px;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              width: 100%;
            }
            .progress-line-bg {
              width: 100%;
              background: rgba(0, 220, 220, 0.1);
              border-radius: 13px;
              height: 37.8%;
              position: absolute;
              bottom: 0;
            }
            .progress-line {
              transition: all 2s;
              background: linear-gradient(
                270deg,
                rgba(0, 220, 220, 1) 0%,
                rgba(41, 176, 255, 1) 100%
              );
              border-radius: 13px;
              height: 37.8%;
              position: absolute;
              bottom: 0;
              width: 50%;
            }
            .progress-line-degree {
              height: 100%;
              display: flex;
              align-items: center;
              position: absolute;
              bottom: 0;
              // right: -15%;
              right: -40px;
              font-size: 12rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(201, 201, 201, 1);
              line-height: 12px;
            }
            .width-zero {
              .progress-line-degree {
                left: 5%;
                right: 0;
              }
            }
          }
          .bottom-item:nth-child(odd) {
            margin-right: 19%;
          }
        }
      }
      .column-4 {
        flex: 1;
        width: 100%;
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        .item1 {
          width: 100%;
          height: 43%;
          margin-bottom: 20px;
          background: rgba(0, 27, 54, 0.7);
          border-radius: 10px;
          border: 1px solid rgba(82, 255, 255, 0.3);
          position: relative;
          .top {
            top: 7.1%;
            left: 6.7%;
          }
          .bottom {
            top: 21.8%;
            left: 6.7%;
            position: absolute;
            height: 67.9%;
            width: 86.6%;
            overflow: hidden;
            .bottom-item {
              width: 100%;
              // height: 22.9%;
              // margin-bottom: 20px;
              margin-bottom: 6px;
              // overflow: hidden;
              position: relative;
              .img-box {
                display: inline-block;
                width: 12px;
                height: 12px;
                // padding-bottom: 6.7%;
                position: relative;
                margin-bottom: -2px;
                img {
                  width: 100%;
                  height: 100%;
                  position: absolute;
                }
              }
              .content-box {
                // position: absolute;
                // width: 88.5%;
                // top: 0;
                // right: 0;
                font-size: 16rem;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(255, 255, 255, 1);
                line-height: 22px;
                margin-top: -3px;
              }
            }
          }
        }
        .item2 {
          width: 100%;
          height: 100%;
          flex: 1;
          background: rgba(0, 27, 54, 0.7);
          border-radius: 10px;
          border: 1px solid rgba(82, 255, 255, 0.3);
          position: relative;
          .top {
            top: 5.7%;
            left: 6.7%;
          }
          .bottom {
            width: 86.6%;
            height: 77.1%;
            position: absolute;
            top: 17.1%;
            left: 6.7%;
            .bottom-item {
              border-left: 1px solid #002541;
              border-right: 1px solid #002541;
              font-size: 14rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: rgba(255, 255, 255, 1);
              line-height: 14px;
              display: flex;
              height: 11.1%;
              span {
                display: inline-flex;
                justify-content: center;
                align-items: center;
                width: 50%;
              }
            }
            .bottom-item:first-child {
              background: #00335a;
              span {
                color: #c9c9c9;
              }
            }
            .bottom-content:nth-child(odd) {
              background: #002541;
            }
          }
        }
      }
    }
  }
}
.other-screen {
  margin-top: 2px;
  text-align: center;
  color: white;
  &__item {
    margin-right: 50px;
    font-weight: 400;
    cursor: pointer;
    font-size: 18rem;
    font-family: PingFangSC-Regular, PingFang SC;
    border-bottom: 1px solid white;
  }
}
</style>
