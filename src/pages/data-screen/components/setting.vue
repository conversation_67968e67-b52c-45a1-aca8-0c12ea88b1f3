<template>
  <div class="setting-box-wrap">
    <div class="setting-box">
      <div class="form">
        <div class="items">
          <div class="name">分拣排名方式</div>
          <div class="value">
            <RadioGroup v-model="sort_field" @on-change="handleRadioChange">
              <Radio :label="item.key" v-for="(item, index) in radioItems" :key="index">
                <span>{{ item.name }}</span>
                <Tooltip
                  v-if="item.tip"
                  :delay="0"
                  :maxWidth="246"
                  :content="item.tip"
                  placement="top"
                >
                  <SIcon
                    icon="help1"
                    :size="12"
                  />
                </Tooltip>
              </Radio>
          </RadioGroup>
          </div>
        </div>
        <div class="items">
          <div class="name">显示设置</div>
          <div class="value">
            <CheckboxGroup v-model="show_field">
              <Checkbox :label="item.key" v-for="(item, index) in checkBoxItems" :key="index" :disabled="item.disabled">
                <span>{{ item.name }}</span>
                <Tooltip
                  v-if="item.tip"
                  :delay="0"
                  :maxWidth="246"
                  :content="item.tip"
                  placement="top"
                >
                  <SIcon
                    icon="help1"
                    :size="12"
                  />
                </Tooltip>
              </Checkbox>
          </CheckboxGroup>
          </div>
        </div>
      </div>
      <div class="bottom-btn">
        <div @click="handleCancel" class="cancel">取消</div>
        <div @click="handleConfirm" class="confirm">确认</div>
      </div>
    </div>
  </div>
</template>
<script>
import SIcon from '@components/icon';
import storage from '@/util/storage';

export default {
  data () {
    return {
      sort_field: this.defaultSortField,
      show_field: this.defaultLegend,
      radioItems: [
        { name: '分拣包裹数', key: 'pack_num' },
        { name: '分拣数量', key: 'sort_num', tip: '实际分拣的数量' },
        { name: '分拣重量', key: 'weight_num', tip: '分拣数量*对应分拣单位重量' },
      ],
    }
  },
  computed: {
    checkBoxItems() {
      const arr =  [
        { name: '分拣订单数', key: 'order_num' },
        { name: '分拣包裹数', key: 'pack_num' },
        { name: '分拣金额', key: 'total_price' },
        { name: '分拣数量', key: 'sort_num', tip: '实际分拣的数量' },
        { name: '分拣重量', key: 'weight_num', tip: '分拣数量*对应分拣单位重量' },
      ]
      return arr.map(val => {
        return {
          ...val,
          disabled: this.sort_field === val.key
        }
      })
    }
  },
  props: {
    defaultSortField: {
      type: String,
    },
    defaultLegend: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  components: {
    SIcon
  },
  mounted () {
  },
  methods: {
    handleRadioChange(e) {
      this.show_field = [e]
    },
    handleConfirm () {
      storage.setLocalStorage('deliver-screen-sort_field', this.sort_field)
      storage.setLocalStorage('deliver-screen-show_field', this.show_field)

      this.$emit('on-change', this.sort_field, this.show_field)
    },
    handleCancel() {
      this.$emit('on-close')
    }
  }
}
</script>
<style lang="less" scoped>
.setting-box-wrap {
  width: 44.8%;
  height: 1px;
  padding-bottom: 22.7%;
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 999;
  margin: auto;
  .setting-box {
    position: absolute;
    width: 100%;
    height: 100%;
    background:rgba(21,44,74,1);
    border-radius:6px;
    padding: 4.6%;
    .form {
      color: #fff;
      line-height: 1;
      .items {
        border-bottom: 1px solid #1D6376;
        padding-bottom: 4%;
        &:last-child {
          border-bottom: 0 none;
        }
        & + div {
          padding-top: 4%;
        }
        .name {
          font-size: 18rem;
          margin-bottom: 3%;
        }
        .ivu-radio-group {
          width: 100%;
        }
        /deep/ .ivu-tooltip-inner {
          background-color: #1F3B5F;
          box-shadow: unset;
        }
        /deep/ .ivu-tooltip-arrow {
          border-top-color: #1F3B5F;
        }
        .ivu-checkbox-wrapper-disabled  {
          opacity: 0.7;
        }
        .ivu-radio-wrapper, .ivu-checkbox-wrapper {
          font-size: 16rem;
          margin-right: 4%;
          /deep/ .ivu-radio-inner, /deep/ .ivu-checkbox-inner {
            border-color: #fff;
            background-color: transparent;
          }
          /deep/ .ivu-radio-checked .ivu-radio-inner {
            border-color: #00DCDC;
            &:after {
              background-color: #00DCDC;
            }
          }
          /deep/ .ivu-checkbox-checked .ivu-checkbox-inner {
            border-color: #00DCDC;
            background-color: #00DCDC;
            &:after {
              background-color: #00DCDC;
              border-color: #152C4A;
            }
          }
          /deep/ .ivu-radio-checked ~ span, /deep/ .ivu-checkbox-checked ~ span {
            color: #00DCDC;
          }
        }
      }
    }
    .bottom-btn {
      height: 9.28%;
      width: 36%;
      position: absolute;
      bottom: 9.1%;
      right: 4.6%;
      display: flex;
      justify-content: space-between;
      font-size:20rem;
      font-family:PingFangSC-Regular,PingFang SC;
      font-weight:400;
      color:rgba(255,255,255,1);
      line-height:28px;
      .cancel {
        width: 45.83%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius:4px;
        border:1px solid rgba(0,220,220,1);
        cursor: pointer;
        color: #00DCDC;
      }
      .confirm {
        width: 45.83%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background:rgba(0,220,220,0.8);
        border-radius:4px;
        cursor: pointer;
        color: #152C4A;
      }
    }  
  }
  
}
</style>