<template>
  <div class="data-screen wrapper">
    <div class="top-area">
      <Top
        @refresh="refresh"
        @close="closePage"
        @fetchData="getDistributionStatistics"
        :showDeliverDateSetting="true"
        subTitle=" • 配送大屏"
        :deliveryTime="selectDate || getCustomToday(12)"
      />
    </div>
    <div class="main-area">
      <div class="left">
        <div class="left-line-2">
          <div class="top"><span></span>下单客户数（个）</div>
          <div class="value-box">{{infor.today.order_user_num || 0}}</div>
          <div class="ratio-box">
            <div class="ratio-item">
              <div class="ratio-item-title">周同比</div>
              <div v-if="infor.today.order_user_num_on_time_rate_for_week" :class="{
                  'arrow-up':
                    infor.today.order_user_num_on_time_rate_for_week.indexOf(
                      '-'
                    ) < 0,
                  'arrow-down':
                    infor.today.order_user_num_on_time_rate_for_week.indexOf(
                      '-'
                    ) >= 0
                }" class="ratio-item-arrow-up"></div>
              <div class="ratio-item-title">{{infor.today.order_user_num_on_time_rate_for_week}}</div>
            </div>
            <div v-if="(infor.today.order_user_num_relative_rate_for_day + '').length + (infor.today.order_user_num_on_time_rate_for_week + '').length < 9" class="ratio-item">
              <div class="ratio-item-title">日环比</div>
              <div v-if="infor.today.order_user_num_relative_rate_for_day" :class="{
                  'arrow-up':
                    infor.today.order_user_num_relative_rate_for_day.indexOf(
                      '-'
                    ) < 0,
                  'arrow-down':
                    infor.today.order_user_num_relative_rate_for_day.indexOf(
                      '-'
                    ) >= 0
                }" class="ratio-item-arrow-down"></div>
              <div class="ratio-item-title">{{infor.today.order_user_num_relative_rate_for_day}}</div>
            </div>
          </div>
          <div class="bg-img-wrap">
            <div class="bg-img-box">
              <img :src="customerIcon">
            </div>
          </div>
        </div>
        <div class="left-line-2">
          <div class="top"><span></span>订单金额（元）</div>
          <div class="value-box">{{infor.today.total_price || 0}}</div>
          <div class="ratio-box">
            <div class="ratio-item">
              <div class="ratio-item-title">周同比</div>
              <div v-if="infor.today.total_price_on_time_rate_for_week" :class="{
                  'arrow-up':
                    infor.today.total_price_on_time_rate_for_week.indexOf(
                      '-'
                    ) < 0,
                  'arrow-down':
                    infor.today.total_price_on_time_rate_for_week.indexOf(
                      '-'
                    ) >= 0
                }" class="ratio-item-arrow-up"></div>
              <div class="ratio-item-title">{{infor.today.total_price_on_time_rate_for_week}}</div>
            </div>
            <div v-if="(infor.today.total_price_relative_rate_for_day + '').length + (infor.today.total_price_on_time_rate_for_week + '').length < 9" class="ratio-item">
              <div class="ratio-item-title">日环比</div>
              <div v-if="infor.today.total_price_relative_rate_for_day" :class="{
                  'arrow-up':
                    infor.today.total_price_relative_rate_for_day.indexOf(
                      '-'
                    ) < 0,
                  'arrow-down':
                    infor.today.total_price_relative_rate_for_day.indexOf(
                      '-'
                    ) >= 0
                }" class="ratio-item-arrow-down"></div>
              <div class="ratio-item-title">{{infor.today.total_price_relative_rate_for_day}}</div>
            </div>
          </div>
          <div class="bg-img-wrap">
            <div class="bg-img-box">
              <img :src="priceIcon">
            </div>
          </div>
        </div>
        <div class="left-line-1">
          <div class="top-box">
            <div class="top"><span></span>订单配送进度</div>
            <div class="content">
              <div class="degree-box">
                <PrDegree
                  bgColor="rgba(0, 220, 220, 0.1)"
                  v-if="deliverDegree.show"
                  :lineColor="deliverDegree.lineColor"
                  :text="deliverDegree.text"
                  :degree="deliverDegree.degree"
                  :barWidth="deliverDegree.deliverDegreeBarWith"
                  :radius="deliverDegree.radius"
                  :textFontSize="deliverDegree.fontSize"
                />
              </div>
              <div class="detail-box">
                <!-- <div class="detail-item">单位（{{infor.sort_process.unit}}）</div> -->
                <div class="detail-item blue">已完成：{{infor.sum_received_order || 0}}</div>
                <div class="detail-item yellow">未完成：{{(infor.sum_order - infor.sum_received_order) || 0}}</div>
              </div>
            </div>
          </div>
          <div class="bottom-box">
            <div class="top"><span></span>订单配送进度(司机)</div>
            <div class="middle">
              <template v-for="(item, index) in topPrSatus">
                <div v-if="item.show" :key="index" class="pr-box-warp">
                  <div class="pr-box">
                    <div class="title">
                      {{item.title}}
                    </div>
                    <Progress :percent="item.degree" />
                  </div>
                </div>
              </template>
            </div>
            <div v-if="topPrSatus.length" class="bottom">
              <template v-for="(item, index) in topPrSatus">
                <div :key="index"  v-if="index % 4 === 0" class="dot-box">
                  <span
                    :key="index"
                    :class="{ active: item.active }"
                  ></span>
                </div>
              </template>
            </div>
          </div>
        </div>
        <div class="left-line-4">

        </div>
      </div>
      <div class="right">
        <div class="top-box">
          <SSelect
            ref="SSelectRef"
            url="/superAdmin/dataScreen/getAccountList"
            :isOpenInterval="true"
            :requestData="{
              delivery_date: selectDate || getCustomToday()
            }"
            @on-change="selectDriver"
          >
          </SSelect>
        </div>
        <div class="map">
          <div class="top"><span></span>配送地图</div>
          <div class="content">
            <div class="mapArea" id="mapArea"></div>
          </div>
          <div class="tip-box">
            <div class="tip-item">
              <img src="https://base-image.shudongpoo.com/common/amap/redPoint.png" alt="" srcset="">
              <span>未送达</span>
            </div>
            <div class="tip-item">
              <img src="https://base-image.shudongpoo.com/common/amap/bluePoint.png" alt="" srcset="">
              <span>已送达</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="other-screen">
      <span class="other-screen__item" v-for="item in otherScreenList" :key="item.path" @click="onGoOtherScreen(item)">{{item.name}}</span>
    </div>
    <Calendar @on-change="changeDate" ref="Calendar" />
  </div>
</template>
<script>
import '@/init/init-map.js';
import echarts from '@/common/init-echarts.js'
import Top from './components/Top';
import Calendar from './components/Calendar';
import PrDegree from './components/PrDegree';
import SSelect from './components/Select.vue'
import customerIcon from '../../assets/images/deliverScreen/customerIcon.png';
import priceIcon from '../../assets/images/deliverScreen/priceIcon.png';
import delivery from '@api/distribution.js';
import common from '@api/basic.js';
import { get } from '../../api/request';


export default {
  data () {
    return {
      miniMode: false,
      customerIcon,
      priceIcon,
      deliverDegree: {
        fontSize: '16',
        text: '',
        degree: 0,
        lineColor: new echarts.graphic.LinearGradient(1, 0, 0, 0, [
          {
            offset: 0,
            color: 'rgba(41,176,255,1)'
          },
          {
            offset: 1,
            color: 'rgba(0,220,220,1)'
          }
        ]),
        deliverDegreeBarWith: 15,
        radius: '170%',
        show: false,
        timer: ''
      },
      map: {},
      selectDate: '',
      topPrSatus: [
      ],
      infor: {
        sort_process: {},
        sorter_rank_list: [],
        today: {}
      },
      singleSiteLoopTimer: '',
      userMarkers: [],
      driverMarkers: [],
      polyline: [],
      // 其他大屏
      otherScreenList: [
        {
          name: '数据大屏',
          path: '/data-screen',
        },
        {
          name: '溯源大屏',
          path: '/trace-screen',
        },
        {
          name: '分拣大屏',
          path: '/deliver-screen',
        },
      ],
    }
  },
  components: {
    Top,
    Calendar,
    PrDegree,
    SSelect
  },

  mounted () {
    if (window.location.href.indexOf('from=sideAdmin') === -1) {
      setTimeout(() => {
        this.initMap();
      }, 1000)
      this.singleSiteLoopTimer = setInterval(() => {
        this.getCustomToday(12);
        this.getDistributionStatistics();
      }, 60 * 1000)
    }
  },
  destroyed() {
    clearInterval(this.timer);
    clearInterval(this.singleSiteLoopTimer);
  },
  methods: {
    onGoOtherScreen(item) {
      this.$router.replace(item.path);
    },
    toggleMiniMode (mode) {
      this.miniMode = mode;
    },
    closePage () {
      clearInterval(this.timer);
    },
    refresh() {
      clearInterval(this.timer);
      this.initMap();
      this.getDistributionStatistics();
    },
    getDistributionStatistics() {
      get(this.apiUrl.dataScreen.distributionStatistics, {
        delivery_date: this.selectDate || this.getToday(false)
      }).then(res => {
        if (res.status) {
          console.log('res----',res.data)
          this.infor = res.data || {};
          let degree = this.infor.sum_received_order / this.infor.sum_order;
          this.deliverDegree.degree = degree;
          this.deliverDegree.text = `${this.infor.sum_received_order}/${this.infor.sum_order}`;
          this.deliverDegree.show = false;
          setTimeout(() => {
            this.deliverDegree.show = true;
          }, 100);
          if (this.infor.driver_list && this.infor.driver_list.length) {
            this.topPrSatus = [];
            this.infor.driver_list.forEach((item, index) => {
              this.topPrSatus.push({
                degree: ((item.received_order_total / item.order_total) * 100).toFixed(0),
                text: `${item.received_order_total}/${item.order_total}`,
                bgColor: '#002841',
                lineColor: '#ffb42c',
                show: false,
                title: item.driver_name,
                active: false,
                radius: '175%',
                fontSize: '14'
              })
            })
            this.autoloop();
          } else {
            this.topPrSatus = [];
          }
        } else {
          this.errorMessage(res.message);
        }
      })
    },
    autoloop () {
      clearInterval(this.timer);
      let loopFunForTopStatus = () => {
        let nowIndex = this.topPrSatus.findIndex(item => {
          return item.show;
        })
        if (nowIndex === -1) {
          nowIndex = -4;
        }
        let nextIndex = nowIndex + 4;
        if (nextIndex >= this.topPrSatus.length) {
          nextIndex = 0;
        }
        this.topPrSatus.forEach((item, index) => {
          if (nextIndex <= index && index < nextIndex + 4) {
            item.show = true;
          } else {
            item.show = false;
          }
          if (index === nextIndex) {
            item.active = true;
          } else {
            item.active = false;
          }
        })
      };
      loopFunForTopStatus();
      this.timer = setInterval(() => {
        loopFunForTopStatus();
      }, 5000)
    },
    changeDate (year, month, day) {
      this.selectDate=`${year}-${month}-${day}`;
      this.getDistributionStatistics();
      this.initMap();
      this.$nextTick(() => {
        this.$refs.SSelectRef.getList()
      })
    },
    // 初始化地图
    initMap() {
      this.map = new AMap.Map('mapArea', {
        zoom: 13, //级别
      });
    },
    setUserMarkers(users) {
      this.usersMarkers = []
      users.forEach(item => {
        if(this.setUserMarker(item)) {
          this.usersMarkers.push(this.setUserMarker(item))
        };
      });
    },
    setUserMarker(user) {
      if (!user) {
        return false;
      }
      let lat = user.driver_latitude || user.latitude;
      let lng = user.driver_longitude || user.longitude;
      let iconSizeW = 24;
      let iconSizeH = 30;
      if (!lat || !lng) {
        return false;
      }
      let imageUrl = 'https://base-image.shudongpoo.com/common/amap/bluePoint.png'
      if(user.is_abnormal === '2') {
        imageUrl = 'https://base-image.shudongpoo.com/common/amap/redPoint.png'
      }
      let userIcon = new AMap.Icon({
        size: new AMap.Size(iconSizeW, iconSizeH),
        image: imageUrl,
        imageSize: new AMap.Size(iconSizeW, iconSizeH)
      });
      let marker = new AMap.Marker({
        map: this.map,
        icon: userIcon,
        position: [lng, lat],
      });
      return marker
    },
    clearDriverMarkers() {
      if (!this.map || !this.driverMarkers || this.driverMarkers.length === 0) {
        return false;
      }
      this.map.remove(this.driverMarkers);
    },
    clearMap() {
      this.map && this.map.clearMap(); // 清除所有覆盖物
      this.userMarkers = []; // 清除所有客户点
      this.driverMarkers = []; // 清除所有司机点
    },
    clearDriverPolyline() {
      if (!this.map || !this.polyline) {
        return false;
      }
      this.map.remove(this.polyline);
    },
    renderDriverTrace({ driver, driver_trace, customer_list }) {
      // 排序，已送达的排到最前面
      Array.isArray(customer_list) && customer_list.sort((a, b) => {
        if (a.is_abnormal === 2 && b.is_abnormal !== 2) {
          return -1; // 将is_abnormal等于2的项排在前面
        } else if (a.is_abnormal !== 2 && b.is_abnormal === 2) {
          return 1; // 将is_abnormal等于2的项排在后面
        } else {
          return 0; // 保持原始顺序
        }
      });
      const driver_trace_path = driver_trace.map(item => {
        return new AMap.LngLat(
          Number(item.longitude),
          Number(item.latitude)
        );
      })
      const customer_list_path = customer_list.map(item => {
        return new AMap.LngLat(
          Number(item.driver_longitude || item.longitude),
          Number(item.driver_latitude || item.latitude)
        );
      })
      if(driver_trace_path.length<=0){
        this.errorMessage('该线路司机未上传路线轨迹')
        return
      }

      var polyline = new AMap.Polyline({
        path: driver_trace_path,
        borderWeight: 1, // 线条宽度，默认为 1
        lineJoin: 'round', // 折线拐点连接处样式
        strokeColor: 'rgba(33, 106, 255, 1)' // 设置默认线段的颜色
      });

      let map = this.map;

      map.add(polyline);

      let startIcon = new AMap.Icon({
        size: new AMap.Size(40, 48),
        image: 'https://base-image.shudongpoo.com/common/amap/driverStartPoint.png!40',
        imageSize: new AMap.Size(40, 48) // 图片显示尺寸
      });
      let endIcon = new AMap.Icon({
        size: new AMap.Size(40, 48),
        image: 'https://base-image.shudongpoo.com/common/amap/curDriverPoint.png!40',
        imageSize: new AMap.Size(40, 48) // 图片显示尺寸
      });
      const startMarker = new AMap.Marker({
        map: map,
        icon: startIcon,
        offset: new AMap.Pixel(-25, -35),
        position: driver_trace_path[0]
      });
      const endMarker = new AMap.Marker({
        map: map,
        icon: endIcon,
        offset: new AMap.Pixel(-25, -88),
        position: driver_trace_path[driver_trace.length - 1]
      });
      endMarker.setContent(`<div class="driver-marker">
        <div class="driver-marker--hover">
          <p>${driver.driver_name}</p>
           <img class="driver-marker-img" src="https://base-image.shudongpoo.com/common/amap/curDriverPoint.png!40" />
        </div>
      </div>`);

      const driverTraceMap = driver_trace[driver_trace.length - 1]
      let lat = driverTraceMap.latitude;
      let lng = driverTraceMap.longitude;
      if (lat && lng) {
        this.map.setCenter([lng, lat]);
      }
    },
    // 获取今日时间
    getToday({ trans }) {
      let now = new Date();
      if (trans) {
        let weekMap = {
          1: '一',
          2: '二',
          3: '三',
          4: '四',
          5: '五',
          6: '六',
          0: '天'
        };
        return {
          day: `${now.getFullYear()}年${now.getMonth() +
            1}月${now.getDate()}日`,
          week: `星期${weekMap[now.getDay()]}`
        };
      }
      let m =
        +now.getMonth() + 1 >= 10
          ? +now.getMonth() + 1
          : '0' + (+now.getMonth() + 1);
      let d = +now.getDate() >= 10 ? +now.getDate() : '0' + +now.getDate();
      return `${now.getFullYear()}-${m}-${d}`;
    },
    selectDriver(shop_id, info) {
      if(!shop_id) return
      this.getTrace(info)
    },
    // 获取客户、司机线路轨迹
    async getTrace(info) {
      const { shop_id, id } = info
      const url = '/superAdmin/dataScreen/getDeliveryCustomer'
      const params = {
        shop_id,
        id,
        delivery_date: this.selectDate || this.getCustomToday()
      }
      const { status, message, data } = await this.$request.get(url, params)
      if(status) {
        this.clearMap()
        if(data.customer_list.length) {
          this.setUserMarkers(data.customer_list)
        }
        this.renderDriverTrace({driver: info, driver_trace: data.driver_trace, customer_list:  data.customer_list});

      } else {
        this.errorMessage(message)
        this.clearMap()
      }
    },
  }
}
</script>
<style lang="less" scoped>
// 去掉高德logo
.data-screen {
  .amap-logo {
    right: 0 !important;
    left: auto !important;
    display: none !important;
  }
  .driver-marker {
    position: relative;
    top: 5px;
    left: -7px;
    z-index: 999;
    &--hover {
      display: flex;
      position: relative;
      border-radius: 20px;
      height: 25px;
      white-space: nowrap;
      padding-left: 10px;
      padding-right: 5px;
      background-color: rgba(33, 106, 255, 1);
      color: #fff;
      align-items: center;
      &::after {
        position: absolute;
        content: '';
        display: block;
        width: 10px;
        height: 20px;
        bottom: -20px;
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 10px solid transparent;
        border-top: 10px solid rgba(33, 106, 255, 1);
      }
    }
    &-img {
      position: absolute;
      bottom: -60px;
      left: 13px;
    }
  }
}
</style>
<style lang="less" scoped>
// 占据全屏
.data-screen {
  position: absolute;
  left: 0px;
  top: 0px;
  padding-top: 0px !important;
  z-index: 500;
  margin: 0px;
}
.wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  margin: 0 !important;
  background: url('../../assets/images/deliverScreen/bg.png')
    no-repeat;
  background-size: cover;
  .top-area {
    height: 7%;
    width: 100%;
    // background: pink;
    padding: 0 3%;
    display: flex;
    justify-content: center;
    position: relative;
    /deep/ .top-area-center {
      .title {
        font-size: 32rem;
      }
      .title-bottom-line {
        bottom: -8px;
      }
    }
  }
  .main-area {
    width: 94%;
    height: 85.6%;
    margin: 0 3%;
    margin-top: 1.5%;
    display: flex;
    .top {
      font-size: 16rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: rgba(201, 201, 201, 1);
      line-height: 22px;
      position: absolute;
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: linear-gradient(
          305deg,
          rgba(12, 21, 48, 1) 0%,
          rgba(0, 220, 220, 1) 100%
        );
        opacity: 0.8;
        border-radius: 8px;
        margin-right: 10px;
      }
    }
    .left {
      width: 22.44%;
      height: 100%;
      margin-right: 20px;
      display: flex;
      flex-direction: column;
      .left-line-1 {
        width: 100%;
        height: 55%;
        background:rgba(14, 26, 84, 1);
        opacity: 0.8;
        border-radius:10px;
        border: 2px solid rgba(82,200,255,0.3);

        .top-box {
          position: relative;
          height: 35%;
          .top {
            top: 7.89%;
            left: 5.94%;
          }
          .content {
            height: 77.23%;
            width: 77.23%;
            left: 10%;
            position: absolute;
            top: 28.95%;
            display: flex;
            .degree-box {
              height: 100%;
              width: 50.06%;
              // width: 100%;
            }
            .detail-box {
              height: 100%;
              padding-top: 9%;
              padding-bottom: 12%;
              padding-left: 16.59%;
              flex: 1;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              .detail-item {
                font-size:16rem;
                font-family:PingFangSC-Regular,PingFang SC;
                font-weight:400;
                color:rgba(255,255,255,1);
                line-height:22px;
              }
              .blue {
                color:rgba(0,220,220,1);
              }
              .yellow {
                color:rgba(255,180,44,1);
              }
            }
          }
        }
        .bottom-box {
          width: 100%;
          height: 65%;
          flex: 1;
          position: relative;
          border: none;
          .top {
            top: 6%;
            left: 5.94%;
          }
          .middle {
            position: absolute;
            height: 32.9%;
            width: 80%;
            top: 18%;
            left: 10%;
            display: flex;
            flex-direction: column;
            gap: 8%;
            .pr-box-warp {
              width: 100%;
              .pr-box {
                .title {
                  font-size: 14px;
                  font-family: PingFangSC-Regular, PingFang SC;
                  color: #FFFFFF;
                }
                /deep/.ivu-progress {

                  .ivu-icon {
                    font-size: 16px;
                  }
                  .ivu-progress-text {
                    margin-left: 25px;
                    .ivu-progress-text-inner {
                      font-size: 12px;
                      font-family: PingFangSC-Regular, PingFang SC;
                      font-weight: 400;
                      color: #C9C9C9;
                    }
                  }
                  .ivu-progress-inner {
                    background-color: rgba(0, 220, 220, 0.1);
                  }
                }
              }
            }
            .pr-box-warp:last-child {
              margin-bottom: 0;
            }
            .pr-box-warp:nth-of-type(1) {
              /deep/ .ivu-progress-bg {
                background-color: #00D7E4;
              }
              /deep/ .ivu-icon {
                color: #00D7E4;
              }
            }
            .pr-box-warp:nth-of-type(2) {
              /deep/ .ivu-progress-bg {
                background-color: #FFB42C;
              }
              /deep/ .ivu-icon {
                color: #FFB42C;
              }
            }
            .pr-box-warp:nth-of-type(3) {
              /deep/ .ivu-progress-bg {
                background-color: #0997FF;
              }
              /deep/ .ivu-icon {
                color: #0997FF;
              }
            }
            .pr-box-warp:nth-of-type(4) {
              /deep/ .ivu-progress-bg {
                background-color: #0CB95F;
              }
              /deep/ .ivu-icon {
                color: #0CB95F;
              }
            }
          }
          .bottom {
            position: absolute;
            width: 100%;
            height: 6px;
            top: 88%;
            display: flex;
            justify-content: center;
            .dot-box {
              display: inline-block;
              span {
                display: inline-block;
                width: 6px;
                height: 6px;
                background: rgba(82, 255, 255, 1);
                opacity: 0.2;
                margin-right: 10px;
                border-radius: 6px;
              }
              span:last-child {
                // margin-right: 0;
              }
              .active {
                background: rgba(0, 220, 220, 0.8);
                opacity: 1;
              }
            }
          }
        }

      }
      .left-line-2 {
        width: 100%;
        height: 20%;
        margin-bottom: 20px;
        background:rgba(14, 26, 84, 1);
        border-radius:10px;
        border: 2px solid rgba(82,200,255,0.3);
        opacity: 0.8;
        position: relative;
        .top {
          top: 10%;
          left: 5.94%;
        }
        .value-box {
          font-size:40rem;
          font-family:Avenir-Heavy,Avenir;
          font-weight:800;
          color:rgba(0,220,220,1);
          line-height:42px;
          position: absolute;
          top: 33.33%;
          left: 10.40%;
          z-index: 999;
        }
        .ratio-box {
          width: 62%;
          font-size:14rem;
          font-family:PingFangSC-Regular,PingFang SC;
          font-weight:400;
          color:rgba(201,201,201,1);
          line-height:20px;
          position: absolute;
          top: 74.44%;
          left: 9.4%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .ratio-item {
            display: flex;
            align-items: center;
            .ratio-item-arrow-up {
              margin-left: 10px;
              margin-right: 4px;
              display: inline-block;
              width: 0;
              height: 0;
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              border-bottom: 8px solid #18d3a0;
              margin-right: 4px;
            }
            .ratio-item-arrow-down {
              margin-left: 10px;
              margin-right: 4px;
              display: inline-block;
              width: 0;
              height: 0;
              border-top: 8px solid #ff0048;
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              margin-right: 4px;
            }
          }
        }
        .bg-img-wrap {
          width: 22.28%;
          height: 1px;
          padding-bottom: 22.28%;
          position: absolute;
          top: 12.78%;
          right: 7.92%;
          .bg-img-box {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
    .right {
      flex: 1;
      display: flex;
      flex-direction: column;
      .top-box {
        height: 37px;
      }
      .map {
        flex: 1;
        border-radius:0 10px 10px 10px;
        background:rgba(14, 26, 84, 1);
        border: 2px solid #224E87;
        position: relative;
        .top {
          top: 2%;
          left: 2.9%;
        }
        .content {
          width: 94.2%;
          height: 89.33%;
          position: absolute;
          left: 2.9%;
          top: 6.33%;
          .mapArea {
            height: 100%;
            width: 100%;
            opacity: 0.95;
          }
        }
        .tip-box {
          position: absolute;
          left: 6%;
          bottom: 8%;
          height: 45px;
          background: #FFFFFF;
          box-shadow: 0px 2px 6px 0px rgba(0,0,0,0.1);
          border-radius: 4px;
          padding: 14px 24px;
          display: flex;
          gap: 16px;
          align-items: center;
          justify-content: center;
          .tip-item {
            border-right: 1px dotted rgba(220, 222, 226, 1);
            padding-right: 16px;
            img {
              width: 15px;
              height: 18px;
              vertical-align: bottom;
            }
            span {
              font-size: 12px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: rgba(0,0,0,0.6);
            }
          }
          .tip-item:last-of-type{
            border-right: none;
            padding-right: 0;
          }
        }
      }

    }
  }
}
.other-screen {
  margin-top: 6px;
  text-align: center;
  color: white;
  &__item {
    margin-right: 50px;
    font-weight: 400;
    cursor: pointer;
    font-size: 18rem;
    font-family: PingFangSC-Regular, PingFang SC;
    border-bottom: 1px solid white;
  }
}
</style>
