<template>
  <div
    :style="{
      height: layoutMainContentHeight + 'px',
      overflowY: 'auto',
      overflowX: 'hidden'
    }"
    class="new-order basePadding"
  >
    <article class="add-order">
      <p class="newOrder-title">
        <strong class="newOrder-title-strong">·</strong>基本信息
      </p>

      <!-- customer information begin -->
      <div class="new-order-main">
        <Form :label-width="70">
          <Row :gutter="50" type="flex">
            <Col>
              <Form-item ref="error.user" :error="error.user" label="客户">
                <div style="position: relative">
                  <Input
                    ref="userInput"
                    :clearable="true"
                    placeholder="请输入客户名称/客户编号"
                    v-model="user.name"
                    @on-change="searchUser"
                    @on-enter="enterUser"
                    @on-focus="resetUserError"
                    @on-blur="userDropDown = false"
                    style="width: 300px;"
                  ></Input>
                  <transition name="dropdown-fade">
                    <div class="newOrder-dropdown" v-show="userDropDown">
                      <ul>
                        <li
                          class="dropdown-items"
                          v-for="info in userList"
                          :key="info.id"
                          :class="{ active: user.uid === info.uid }"
                          @click="addUser(info)"
                        >
                          <strong class="dropdown-items-strong">{{
                            info.email
                          }}</strong>
                          <p class="dropdown-items-p">{{ info.user_code }}</p>
                        </li>
                      </ul>
                    </div>
                  </transition>
                  <transition name="dropdown-fade">
                    <div
                      class="newOrder-dropdown-content"
                      v-show="
                        !userDropDown && user.name && userList.length === 0
                      "
                    >
                      <p class="dropdown-empty">暂无数据</p>
                    </div>
                  </transition>
                </div>
                <p class="text-red" v-if="showContractPriceTips">
                  客户不存在生效的协议单，不会同步协议单价格
                </p>
              </Form-item>
            </Col>
            <Col>
              <Form-item
                ref="error.delivery_date"
                :error="error.delivery_date"
                label="发货日期"
              >
                <Row>
                  <i-col span="11">
                    <Date-picker
                      @on-change="handleChangeDate"
                      style="width: 140px"
                      type="date"
                      placeholder="选择日期"
                      :disabled="disabledEverything"
                      v-model="date"
                      format="yyyy-MM-dd"
                      :editable="false"
                    ></Date-picker>
                  </i-col>
                </Row>
              </Form-item>
            </Col>
            <Col>
              <Form-item label="发货时间" style="width: 230px;">
                <Row>
                  <i-col span="11">
                    <Select
                      v-model="selectedDeliveryTime"
                      :disabled="disabledEverything"
                      style="width:150px"
                    >
                      <Option
                        v-for="item in deliveryTimeList"
                        :value="item.id"
                        :key="item.id"
                        >{{ item.timeDu }}</Option
                      >
                    </Select>
                  </i-col>
                </Row>
              </Form-item>
            </Col>
          </Row>
          <Row v-if="userMessage" style="margin-top: 10px">
            <Col>
              <span style="color: red; margin-left: 70px;">{{
                userMessage
              }}</span>
            </Col>
          </Row>
        </Form>
      </div>
      <div v-if="orderTagList.length > 0" class="new-order-main">
        <Row type="flex" justify="start" align="middle">
          <Col style="width: 70px" span="2">
            <span>订单标签</span>
          </Col>
          <Col span="22">
            <CheckboxGroup @on-change="checkTag" v-model="selectedOrderTag">
              <!-- __MARK HTML -->
              <Checkbox
                :disabled="
                  disabledEverything ||
                    (only_tag === '1' &&
                      selectedOrderTag.length > 0 &&
                      !selectedOrderTag.includes(item.id))
                "
                v-for="item in orderTagList"
                :key="item.id"
                :label="item.id"
              >
                <span>{{ item.name }}</span>
              </Checkbox>
            </CheckboxGroup>
          </Col>
        </Row>
      </div>
      <!-- customer information end -->
      <p class="newOrder-title">
        <strong class="newOrder-title-strong">·</strong>收货信息
      </p>

      <!-- address information begin -->
      <div class="newOrder-address">
        <span>收货人：{{ user.detail.name }}</span>
        <span>{{ user.detail.tel }}</span>
        <span>{{ user.detail.address_detail }}</span>
        <!-- <strong><Icon type="edit"></Icon>编辑收货地址</strong> -->
      </div>
      <!-- address information end -->

      <template v-if="isCanOrder">
        <div class="newOrder-title">
          <strong class="newOrder-title-strong">·</strong>订购商品清单
          <div class="newOrder-operation">
            <span @click="goodsPackageModalShow" v-if="isCommodityPackage && goodsPackagePriceModeCombos && !isEnableAddSameGoods">添加套餐</span>
            <span @click="activeUserHandmade">智能录单</span>
            <span @click="activeHistoryAddOrder">从历史订单复制新增</span>
          </div>
        </div>
        <!-- begin -->
        <div style="border-bottom: none" class="new-order-main">
          <i-form :label-width="40">
            <Row :gutter="15" type="flex">
              <Col>
                <Form-item ref="error.goods" :error="error.goods" label="商品">
                  <div style="position: relative" class="goods-input">
                    <i-input
                      v-model="commodity.name"
                      id="commodity-input"
                      placeholder="请输入商品名称/编码/别名/关键字"
                      @on-change="getCommodity"
                      @on-enter="enterCommodity"
                      @on-focus="isUserInput"
                      @on-blur="commodityDropDown = false"
                      style="width: 300px;"
                      clearable
                    >
                      <Button
                        type="success"
                        @click="addGoods"
                        slot="append"
                        icon="md-add"
                      ></Button>
                    </i-input>
                    <transition name="dropdown-fade">
                      <div class="newOrder-dropdown" v-show="commodityDropDown">
                        <ul class="commodity-list">
                          <li
                            class="dropdown-items"
                            v-for="info in commodityList"
                            :key="info.cid"
                            :class="{ active: commodity.id === info.id }"
                            @click="addCommodity(info)"
                          >
                            <span
                              class="tag-protocol-price"
                              v-if="isProtocolGoods(info)"
                              >协</span
                            >
                            <strong class="dropdown-items-strong">{{
                              info.name
                            }}</strong>
                            <span class="dropdown-items-span"
                              >({{ info.unit }})</span
                            >
                            <p class="dropdown-items-p">
                              <span>{{ info.commodity_code }}</span>
                              <StepPricingPoptip v-if="isStepPricingGoods(info)" :goods="info"></StepPricingPoptip>
                              <Rate
                                style="float: right; font-size: 15px;"
                                :value="info.star"
                                disabled
                              ></Rate>
                            </p>
                          </li>
                        </ul>
                      </div>
                    </transition>
                    <transition name="dropdown-fade">
                      <div
                        class="newOrder-dropdown-content"
                        v-show="
                          commodityDropDown &&
                            commodity.name &&
                            commodityList.length === 0
                        "
                      >
                        <p class="dropdown-empty">暂无数据</p>
                      </div>
                    </transition>
                  </div>
                </Form-item>
              </Col>
              <Col>
                <Form-item label="数量">
                  <i-input
                    style="width: 100px"
                    ref="amountInput"
                    placeholder="输入订购数"
                    v-model="commodity.amount"
                    @on-change="isNumber"
                    @on-focus="isUserInput"
                    @on-enter="enterAmount"
                    id="order-amount-input"
                  ></i-input>
                </Form-item>
              </Col>
              <template v-if="isShowSyncContractPrice">
                <Col>
                  <Form-item label="单价">
                    <NumberInput
                      ref="priceInput"
                      style="width: 100px"
                      @on-change="handleChangePrice(commodity)"
                      @on-enter="handelEnterPrice"
                      v-model="commodity.price"
                    ></NumberInput>
                  </Form-item>
                </Col>
                <template v-if="isEnableUserContractPriceDiscountRatio">
                  <Col>
                    <Form-item :label-width="60" label="折扣率">
                      <NumberInput
                        ref="discountInput"
                        style="width: 100px"
                        @on-change="handleChangeDiscount(commodity)"
                        @on-enter="handleEnterDiscount"
                        v-model="commodity.discount"
                      ></NumberInput>
                      %
                    </Form-item>
                  </Col>
                  <Col>
                    <Form-item :label-width="90" label="协议市场价">
                      <NumberInput
                        ref="marketPriceInput"
                        style="width: 100px"
                        @on-change="handleChangeMarketPrice(commodity)"
                        @on-enter="handleEnterMarketPrice"
                        v-model="commodity.org_price"
                      ></NumberInput>
                    </Form-item>
                  </Col>
                </template>
              </template>
              <Col>
                <i-button
                  type="success"
                  icon="md-add"
                  @click="addCommodityToList()"
                  >添加</i-button
                >
              </Col>
              <Col class="flex-con" align="right">
                <SelectColsPanel
                  type="order_edit"
                  :default-trigger="true"
                  @on-change="handleSetCols"
                />
              </Col>
            </Row>
          </i-form>
        </div>

        <div class="newOrder-lists">
          <Table
            :row-class-name="rowClassName"
            @on-row-click="handleRowClick"
            class="table-normal"
            highlight-row
            :columns="goodsColumns"
            :data="newOrderList"
            ref="orderGoodsTable"
          ></Table>
        </div>
        <div v-show="isOpenOrderCombine && hasStepPricingGoods" class="text--error" style="padding: 10px 0 0 22px">当前订单包含阶梯定价商品，不支持合并订单。</div>

        <div class="newOrder-amount">
          <span class="c6">下单数量：</span>
          <span class="c6 mr10">{{ orderTotalNum }}</span>
          <span>合计金额：¥</span>
          <span class="order-total-amount">{{ calTotal || 0 }}</span>
        </div>
        <!-- end -->
        <div class="newOrder-remarks">
          <i-form :label-width="70">
            <Form-item label="订单备注">
              <i-input
                maxlength="64"
                show-word-limit
                placeholder="输入订单备注"
                v-model="remarks"
              ></i-input>
            </Form-item>

             <Form-item label="附件">
              <AttachmentUpload v-model="attachmentFiles" />
            </Form-item>
          </i-form>
        </div>
      </template>

      <!-- operation begin -->
      <div class="newOrder-feature">
        <!-- <span @click="showNewOrderReview">核对订单</span> -->
        <i-button
          type="success"
          @click="showNewOrderReview"
          :disabled="disabledEverything"
          >核对订单</i-button
        >
        <i-button
          type="success"
          @click="createOrder(1)"
          :disabled="createDisabled || disabledEverything"
          >保存并回到列表页</i-button
        >
        <i-button
          type="success"
          @click="createOrder(2)"
          :disabled="createDisabled || disabledEverything"
          >保存并继续新增</i-button
        >
        <i-button @click="cancel">取消</i-button>
      </div>
      <!-- operation end -->
      <!--<transition name="slide-fade">-->
      <usual v-if="usualOrderActive" @close="activeUsualOrder"></usual>
      <!--</transition>-->

      <!--<transition name="slide-fade">-->
      <userHandMade
        v-if="userHandmadeActive"
        :userId="user.id"
        @addRecgData="addUserHandmade"
        @addOrder="handlerAdd"
        @close="activeUserHandmade"
      ></userHandMade>
      <!--</transition>-->

      <!--<transition name="slide-fade">-->
      <history
        :user="user"
        :goods="newOrderList"
        v-if="historyAddOrderActive"
        @close="activeHistoryAddOrder"
        @history="addHistoryGoods"
      ></history>
      <!--</transition>-->

      <Modal
        v-model="newOrderReviewActive"
        title="核对订单"
        width="1000"
        :mask-closable="false"
        :closable="false"
      >
        <i-table
          highlight-row
          :columns="checkColumns"
          :data="checkOrderList"
        ></i-table>
        <div slot="footer">
          <Button @click="newOrderReviewActive = false">取 消</Button>
          <Button type="primary" @click="confirmReview">确 认</Button>
        </div>
      </Modal>

      <goods-list-modal
        :params="{
          is_online: createOrderShowOfflineGoods ? '' : 'Y',
          delivery_date: delivery_date,
        }"
        v-model="showGoodsListModal"
        :uid="user.id"
        :selectedGoods="isEnableAddSameGoods ? [] : newOrderList"
        @on-add="handlerAdd"
      >
      <div></div>
      </goods-list-modal>
      <GoodsPackageModal
        ref="goodsPackageModal"
        :show-mode-filter="true"
        :show="goodsPackageModal.show"
        :columns="goodsPackageModal.columns"
        :defaultValue="true"
        @on-cancel="$_closeGoodsPackageModal"
        @on-ok="$_onSelectGoodsPackage"
      />
    </article>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import GoodsPackageModal from '@components/packageGoods/PackageGoodsModal';
import NumberInput from '@components/basic/NumberInput';
// import editNumberInput from "@components/basic/editNumberInput";
import history from '@components/order/NewOrderHistory';
import userHandMade from '@components/order/NewOrderUserHandMade';
import usual from '@components/order/NewOrderUsual';
// import review from "@components/order/Review";
import GoodsListModal from '@components/order/goodsListModal';
import common from '@api/order.js';
// import { orderMode } from "@assets/js/common.js";
import LayoutMixin from '@/mixins/layout';
import Goods from '@api/goods.js';
// import Bus from "@api/bus.js";
import '@assets/scss/mixin.scss';
import HotKey from '../../util/HotKey';
import SIcon from '@components/icon';
import DateUtil from '@/util/date';
import { get } from '@api/request.js';
import ConfigMixin from '@/mixins/config';
import HeaderFilter from './components/header-filter.vue';
import settings from '@/api/settings';
import store from '@/vuex'
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import StepPricingPoptip from './components/StepPricingPoptip.vue'
import coreEfficiencyIndexLogger, { INDEX_TYPE } from '@/util/coreEfficiencyIndexLogger';
import { debounce } from 'lodash-es'

const filterList = [
  {
    name:'最近一次进价',
    value:'1',
    tip: '每次入库单审核过后都会更新，但是可以进行手动更改'
  },
  {
    name:'最近一次入库价',
    value:'2',
    tip: '每次入库单审核之后才可以更新'
  },
  {
    name:'库存均价',
    value:'3',
    tip: '现有库存中的库存均价'
  },
  {
    name:'最近一次采购价',
    value:'4',
    tip: '采购单收货之后更新的采购价'
  }
]
export default {
  name: 'new-order',
  mixins: [LayoutMixin, ConfigMixin],
  watch: {
    userHasContractPriceOrder() {
      this.updateCols();
    },
    isNewOrderInputVersion (value) {
      if (value) {
        this.$router.replace(({ path: '/order/new', query: this.$route.query }))
      }
    }
  },
  data() {
    return {
      colsCopy: [],
      goodsPackageList: [],
      goodsPackage: [],
      goodsPackageModal: {
        show: false,
        columns: [
          {
            width: 60,
            type: 'selection'
          },
          {
            title: '图片',
            render: (h, params) => {
              let { row } = params;
              let key = 'pic_url';
              return h('img', {
                style: {
                  width: '40px'
                },
                attrs: {
                  src: row[key]
                }
              });
            }
          },
          {
            title: '套餐名称',
            key: 'name'
          },
          {
            title: '单位',
            key: 'unit'
          },
          {
            title: '描述',
            key: 'summary'
          },
          {
            title: '下单数量',
            render: (h, params) => {
              const key = 'amount';
              return h('NumberInput', {
                props: {
                  value: params.row.amount,
                },
                on: {
                  'on-change': value => {
                    params.row[key] = value;
                  },
                  'on-blur': () => {
                    const list = this.$refs.goodsPackageModal.getList();
                    list.forEach(item => {
                      if (item.id === params.row.id) {
                        item.amount = params.row.amount;
                      }
                    });
                    this.$refs.goodsPackageModal.setList(list);
                  },
                  'on-click': (e) => {
                    window.event? window.event.cancelBubble = true : e.stopPropagation();
                  }
                }
              });
            }
          }
        ],
      },
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      idGeneratorIndex: 1,
      userMessage: '',
      selectedOrderTag: [],
      orderTagList: [],
      error: {
        user: '',
        delivery_date: '',
        goods: ''
      },
      date: '',
      delivery_date: '',
      userHasContractPriceOrder: true,
      showContractPriceTips: false,
      syncContractPrice: false, // 是否同步协议价
      showGoodsListModal: false,
      usualOrderActive: false, // 常用清单
      userHandmadeActive: false, // 客户手工单
      historyAddOrderActive: false, //  从历史订单复制新增
      newOrderReviewActive: false,
      commodityDropDown: false, // 商品下拉框
      userDropDown: false,
      totalAmount: '',
      orderTotalAmount: '',
      tUser: '', // * 追求最好的用户体验，暂时缓存用户信息
      isClose: '', // 判断是否关闭页面
      deliveryTimeList: [],
      selectedDeliveryTime: '',
      commodity: {},
      commodityList: '',
      user: {
        id: '',
        uid: '',
        name: '',
        detail: ''
      },
      userList: [],
      newOrderList: [],
      postList: [],
      remarks: '',
      createDisabled: false,
      isInputNumber: '', // 正在加入商品
      previousValue: '',
      checkOrderList: [],
      inputCheckOrderList: [],
      goodsPackagePriceModeCombos: false,
      isCommodityPackage: false,
      checkColumns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '商品单位',
          key: 'unit',
          align: 'center'
        },
        {
          title: '描述',
          key: 'summary',
          align: 'center'
        },
        {
          title: '订购数',
          key: 'amount',
          width: 100,
          align: 'center',
          props: {
            amountArr: ''
          },
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              class: [HotKey.getHotKeyClass()],
              props: {
                value: parseFloat(data.amount),
                min: 0
                // precision: 2,
              },
              style: {
                width: '100%'
              },
              nativeOn: {
                click: () => {
                  this.isFocus = true;
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              },
              on: {
                'on-change': val => {
                  this.inputCheckOrderList[params.index]['check_amount'] = val;
                },
                'on-focus': event => {
                  event.target.select();
                }
              }
            });
          }
        },
        {
          title: '订购单价（元）',
          key: 'price',
          align: 'center'
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('i-input', {
              props: {
                value: data.remark
              },
              class: {
                remarks: true,
                [HotKey.getHotKeyClass()]: true
              },
              nativeOn: {
                click: $event => {
                  $event.stopPropagation();
                },
                change: $event => {
                  let value = $event.target.value;
                  params.row.remark = value;
                  this.inputCheckOrderList[params.index].remark = value;
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              }
            });
          }
        }
      ],
      activeRowIndex: 0,
      goodsColumns: [],
      quickClick: true,
      ifFromCopy: false,
      originCols: [
        {
          title: '序号',
          align: 'center',
          width: 60,
          key: 'index',
          render: (h, params) => {
            const template = [];
            // 预售商品
            if (params.row.expected_arrival_date) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 20
                  },
                  style: {
                    color: 'var(--primary-color)',
                    marginRight: '5px'
                  }
                })
              );
            }
            template.push(h('span', params.index + 1));
            return template;
          }
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          minWidth: 80,
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          minWidth: 120,
          className: 'commodity-name-wrap',
          render: (h, params) => {
            var obj = params.row;
            let activityIcon = Goods.getActivityIcon(obj);
            return h(
              'div',
              {
                style: {
                  position: 'relative',
                  paddingLeft: activityIcon ? '40px' : '0'
                }
              },
              [
                h(
                  'span',
                  {
                    class: {
                      'new-logo': obj.new,
                      dn: !obj.new
                    }
                  },
                  '新'
                ),
                h(
                  'img',
                  {
                    attrs: {
                      src: activityIcon
                    },
                    style: {
                      height: '18px',
                      position: 'absolute',
                      left: 0,
                      top: '1px'
                    },
                    class: {
                      dn: !obj.activity_type_desc
                    }
                  },
                  `[${obj.activity_type_desc}]`
                ),
                h(
                  'span',
                  {
                    class: {
                      'commodity-name': true
                    }
                  },
                  obj.name
                )
              ]
            );
          }
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
          ellipsis: true,
          render: (h, params) => {
            const { row: { category_name, category_name2 } } = params
            return <span>{category_name ? `${category_name}${category_name2 ? `/${category_name2}` : ''}` : '--'}</span>
          }
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'center',
          minWidth: 120,
        },
        {
          title: '单位',
          key: 'unit',
          align: 'center',
          minWidth: 90,
        },
        {
          title: '现有库存',
          key: 'stock',
          minWidth: 100,
          render: (h, params) => {
            let data = params.row;
            return h('span', data.stock + '/' + data.unit_sell)
          }
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'center',
          minWidth: 100,
        },
        {
          title: '下单数量',
          key: 'order_amount',
          align: 'center',
          width: 150,
          className: 'column__order-amount',
          render: (h, params) => {
            const { row: data, index } = params,
              me = this;
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0
            this.newOrderList.forEach(item => {
              if (data.commodity_id === item.commodity_id) {
                otherAmount += Number(item.amount);
              }
            })
            let template = h('div', {}, [
              h('div', {
                display: 'flex',
                align: 'center'
              }, [
                h(NumberInput, {
                  class: [HotKey.getHotKeyClass(), 'order-amount'],
                  props: {
                    value: Number(data.amount),
                    min: 0,
                    precision: 2
                  },
                  style: {
                    width: '80px',
                    flexShrink: 0
                  },
                  nativeOn: {
                    click: $event => {
                      $event.stopPropagation();
                      this.isFocus = true;
                    },
                    keyup: event => {
                      HotKey.handleTableRowsHotKey({
                        event,
                        params,
                        tableRef: 'orderGoodsTable',
                        vm: this
                      });
                    }
                  },
                  on: {
                    'on-focus': event => {
                      params.row.amount_warning = '';
                      me.postList[params.index]['amount_warning'] = '';
                      me.newOrderList[params.index]['amount_warning'] = '';

                      event.target.select();
                    },
                    'on-change': val => {
                      params.row.amount = val;
                      me._updateStepPricing(data, index)
                    },
                    'on-blur': () => {
                      params.row['total'] = parseFloat(
                        data.amount * params.row.price
                      ).toFixed(2);
                      me.postList[params.index]['amount'] = data.amount;
                      me.newOrderList[params.index]['amount'] = data.amount;
                      me.newOrderList[params.index]['price'] = data.price;
                    }
                  }
                }),
                this.isStepPricingGoods(data) ? <StepPricingPoptip goods={data}></StepPricingPoptip> : null
              ]),
              h(
                'span',
                {
                  class: {
                    dn:
                      !data.is_sell_stock_alert ||
                      (otherAmount) <= Number(data.sell_stock)
                  },
                  style: {
                    color: 'red'
                  }
                },
                '库存不足'
              )
            ]);
            if (params.row.amount_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'top',
                    content: '输入订购数量',
                    always: true,
                    transfer: true
                  }
                },
                [template]
              );
            }
            return template;
          }
        },
        {
          title: '下单单价',
          key: 'unit_price',
          align: 'center',
          minWidth: 100,
          render: (h, params) => {
            let { row } = params;
            let key = 'price';
            if (this.isShowSyncContractPrice) {
              return h(NumberInput, {
                class: [HotKey.getHotKeyClass()],
                props: {
                  value: params.row[key]
                },
                on: {
                  'on-change': value => {
                    params.row[key] = value;
                    this.postList[params.index][key] = value;
                    this.handleChangePrice(params.row);
                    this.updateStoreListPrice(params);
                  }
                },
                nativeOn: {
                  keyup: event => {
                    HotKey.handleTableRowsHotKey({
                      event,
                      params,
                      tableRef: 'orderGoodsTable',
                      vm: this
                    });
                  }
                }
              });
            }
            let template = [h('span', row[key])];
            if (Goods.isProtocolGoods(row)) {
              template.push(
                h(
                  'span',
                  {
                    class: {
                      'tag-protocol-price': true
                    }
                  },
                  '协'
                )
              );
            }
            if (params.row.in_price - params.row[key] > 0) {
              template.push(
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                      fontSize: '12px'
                    }
                  },
                  '低于最近一次进货价！'
                )
              );
            }
            return h(
              'div',
              {
                style: {
                  padding: '5px 0'
                }
              },
              template
            );
          }
        },
        {
          title: '下单小计',
          key: 'sub_total_price',
          align: 'center',
          minWidth: 100,
          render: (h, params) => {
            return h('span', {
              class: {
                'total-amount': true
              },
              domProps: {
                innerHTML: (params.row.amount * params.row.price).toFixed(2)
              }
            });
          }
        },
        {
          minWidth: 100,
          title: '折扣率(%)',
          key: 'discount',
          render: (h, params) => {
            let key = 'discount';
            return h(NumberInput, {
              class: [HotKey.getHotKeyClass()],
              props: {
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  this.postList[params.index][key] = value;
                  this.handleChangeDiscount(params.row);
                  this.updateStoreListPrice(params);
                }
              },
              nativeOn: {
                click: $event => {
                  $event.stopPropagation();
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              }
            });
          }
        },
        {
          minWidth: 100,
          title: '协议市场价',
          key: 'org_price',
          render: (h, params) => {
            let key = 'org_price';
            return h(NumberInput, {
              class: [HotKey.getHotKeyClass()],
              props: {
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  this.postList[params.index][key] = value;
                  this.handleChangeMarketPrice(params.row);
                  this.updateStoreListPrice(params);
                }
              },
              nativeOn: {
                click: $event => {
                  $event.stopPropagation();
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              }
            });
          }
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          filterMultiple: false,
          minWidth: 120,
          renderHeader: h => {
          const defaultFilter = this.priceType;
          return h(HeaderFilter,{
              props:{
                filterList,
                defaultFilter
              },
              on:{
                'on-change':(_value)=>{
                  this.handleChangePriceType(_value);
                }
              }
            })
          },
          render:(h,params)=>{
            const {row} = params;
            let key = '';
            switch(this.priceType){
              case '1':
                key = 'in_price';
                break;
              case '2':
                key = 'store_in_price';
                break;
              case '3':
                key = 'average_price';
                break;
              case '4':
                key = 'last_receipt_price';
                break;
              default:
                break;
            }
            // 根据选择的价格模式显示不同的数据
            const val = row[key];
            return h('span',val)
          }
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          minWidth: 120,
        },
        {
          minWidth: 80,
          title: '税率',
          key: 'tax_rate_desc',
          render: (h, params) => h('span', params.row.tax_rate)
        },
        {
          minWidth: 80,
          title: '税额',
          key: 'tax_rate_price',
          render: (h, params) => {
            let data = params.row;
            let taxRatePrice =
              ((
                (params.row.amount * params.row.price) /
                (1 + data.tax_rate / 100)
              ).toFixed(2) *
                Number(data.tax_rate)) /
              100;
            return h('span', {}, taxRatePrice.toFixed(2));
          }
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            let data = params.row;
            return h('i-input', {
              props: {
                value: data.remark
              },
              class: {
                remarks: true,
                [HotKey.getHotKeyClass()]: true
              },
              nativeOn: {
                click: $event => {
                  $event.stopPropagation();
                },
                change: $event => {
                  let value = $event.target.value;
                  params.row.remark = value;
                  this.postList[params.index].remark = value;
                  this.newOrderList[params.index]['remark'] = value;
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              }
            });
          }
        },
        {
          title: '内部备注',
          className: 'InternalNote',
          key: 'inner_remark',
          align: 'center',
          minWidth: 120,
          render: (h, params) => {
            let data = params.row;
            return h('i-input', {
              props: {
                value: data.inner_remark
              },
              class: {
                remarks: true,
                [HotKey.getHotKeyClass()]: true
              },
              nativeOn: {
                click: $event => {
                  $event.stopPropagation();
                },
                change: $event => {
                  let value = $event.target.value;
                  params.row.inner_remark = value;
                  this.postList[params.index].inner_remark = value;
                  this.newOrderList[params.index]['inner_remark'] = value;
                },
                keyup: event => {
                  HotKey.handleTableRowsHotKey({
                    event,
                    params,
                    tableRef: 'orderGoodsTable',
                    vm: this
                  });
                }
              }
            });
          }
        }
      ],
      hasGetCustomFieldKeys: false,
      priceType:window.localStorage.getItem('order_base_price_mode') || filterList[0].value,
      attachmentFiles:[]
    };
  },
  created() {
    this.getCommodity = debounce(this.searchCommodity,200)
    this.initCommodity();
    this.$store.state.isToOrder = '';
    this.getDate();
    this.getDeliveryTimeList();
    this.focusUserInput();

    //获取列表页复制来的id
    let id = this.$route.query.copyId;
    if (id != undefined) {
      this.getOrderDetail(id);
      this.ifFromCopy = true;
    }
    this.getOrderTagList();
    // 得到标签配置，是否可以添加多个标签，是否只能添加一个标签
    this.commonService.getConfig().then(config => {
      const {
        is_open_order_tag_required: must_tag,
        is_open_order_tag_only_one: only_tag
      } = config;
      this.isCommodityPackage = +config.is_commodity_package === 1;
      this.goodsPackagePriceModeCombos = +config.commodity_package_mode === 1;
      this.must_tag = must_tag;
      this.only_tag = only_tag;
    });
  },
  beforeRouteEnter(to, from, next) {
    const cfg = localStorage.getItem('use_new_order_placement')
    const isLocalNewVersion = cfg !== null && (!!+cfg) // 本地有配置且是新版
    if (isLocalNewVersion) {
      const configList = [
        {
          key: 'order_input_version',
          value: 2
        }
      ]
      settings.saveSystemConfig(configList).then(({ status }) => {
        if (status) {
          localStorage.removeItem('use_new_order_placement')
        }
      })
      next({ path: '/order/new', query: to.query })
    } else if (+store.state.sysConfig.order_input_version === 2) {
      next({ path: '/order/new', query: to.query })
    } else {
      next()
    }
  },
  beforeRouteLeave: function(to, from, next) {
    //      if (this.newOrderList.length || this.user.id && !this.isClose && !this.isToOrder) {
    if (this.newOrderList.length || (this.user.id && this.isClose)) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function() {
          next();
        },
        onCancel: function() {
          next(false)
        }
      });
      // if (confirm('系统可能不会保存您所做的更改。')) {
      //   next();
      // }
    } else {
      this.isClose = '';
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  mounted() {
    getWidth();
    let date = document.querySelectorAll('.ivu-date-picker')[0],
      input = date.getElementsByClassName('ivu-input')[0];
    input.readOnly = true;
  },
  computed: {
    ...mapState({
      isOrderClose: state => state.isOrderClose,
      isToOrder: state => state.isToOrder
    }),
    disabledEverything() {
      return this.user.name === '';
    },
    calTotal() {
      let totalNum = 0;
      this.postList.map(d => {
        totalNum += parseFloat(d.amount * d.price).toFixed(2);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    orderTotalNum() {
      let num = 0;
      this.postList.map(e => {
        num += Number(e.amount);
      });
      num = num.toFixed(2);
      return num;
    },
    isShowSyncContractPrice() {
      return (
        this.isEnableOrderSyncContractPrice && this.userHasContractPriceOrder
      );
    },
    isCanOrder() {
      if (!this.user.id) {
        return false;
      }
      // 超出账期允许后台下单
      if (this.OrdersBeyondPaymentDaysAreAllowed) {
        return true;
      }
      // 超出账期不允许后台下单，且当前客户超出了账期
      if (!this.OrdersBeyondPaymentDaysAreAllowed && this.userMessage) {
        return false;
      }
      return true;
    },
    // 是否包含阶梯定价商品
    hasStepPricingGoods () {
      return this.newOrderList.find(goods => this.isStepPricingGoods(goods))
    },
    // 是否允许添加相同商品
    isEnableAddSameGoods () {
      return this.isAddSameCommoditySplitOrder || this.is_open_order_add_same_commodity
    }
  },
  methods: {
    goodsPackageModalShow () {
      this.syncPostList();
      this.goodsPackageModal.show = true;
    },
    $_closeGoodsPackageModal () {
      this.goodsPackageModal.show = false;
    },
    dataProcessing (val,res) {
      const goodsList = [];
      const addGoodsToGoosdList = ({ goods, amount }) => {
        const existGoods = goodsList.find(
          existGoods => +existGoods.commodity_id === +goods.commodity_id
        );
        if (!existGoods) {
          goods = this.deepClone(goods);
          goods.amount = Number(amount);
          goodsList.push(goods);
        } else {
          existGoods.amount += Number(amount).toFixed(2);
        }
      };
      if (res === 1) {
        addGoodsToGoosdList({goods: val, amount: val.amount});
      } else {
        val.forEach(ite => {
          const packageNum = ite.amount || 0;
          ite.item.forEach(vals => {
            vals.id = vals.commodity_id;
            if (vals.item) {
              vals.item.forEach( ites => {
                ites.id = ites.commodity_id
              addGoodsToGoosdList(
                {
                  goods: ites,
                  amount: ((packageNum * vals.num) * ites.num).toFixed(2)
                })
              })
            }
            else {
              addGoodsToGoosdList({goods:vals, amount: (packageNum * vals.num).toFixed(2)})
            }
          })
        });
      }
      let newOrderList = this.deepClone(this.newOrderList)
      newOrderList.forEach(item => {
        if (item.commodity_id === undefined && item.id) {
          item.commodity_id = item.id
        }
      })
      if (newOrderList.length > 0) {
        newOrderList.forEach(items => {
          let find = goodsList.find( val => +items.commodity_id === +val.commodity_id);
          if (find) {
            items.amount = (Number(find.amount) + Number(items.amount)).toFixed(2);
          }
        })
        let arr = []
        goodsList.forEach( value => {
          let find = newOrderList.find(ites => Number(ites.commodity_id) === Number(value.commodity_id))
          if (!find) {
            arr.push(value)
          }
        });
        // newOrderList.unshift(...arr);
        newOrderList.splice(this.activeRowIndex,0,...arr)
      } else {
        // newOrderList.push(...goodsList);
        newOrderList.splice(this.activeRowIndex,0,...goodsList)
      }
      this.activeRowIndex = 0;
      this.newOrderList = newOrderList;
      this.postList = this.deepClone(newOrderList);
    },
    async $_onSelectGoodsPackage (value) {
      let commodity_id = [];
      let goodsPackage = this.deepClone(value);
      // this.dataProcessing(goodsPackage)
      goodsPackage.forEach(ite => {
        ite.item.forEach(val => {
          if(val.commodity_id !== undefined) {
            commodity_id.push(val.commodity_id);
          }
          if (val.item) {
            val.item.forEach(v => {
              if (v.commodity_id !== undefined) {
                commodity_id.push(v.commodity_id);
              }
            })
          }
        })
      });
      let id = Array.from(new Set(commodity_id));
      let params = {
        user_id: this.user.id,
        commodity_id: id.toString(),
        delivery_date: this.delivery_date
      }
      let { data, status, message} = await get(this.apiUrl.orderSuperAjaxGetMultiOrderCommodity, params);
        if (status) {
          goodsPackage.forEach(ite => {
            ite.item.forEach(val => {
              if(val.commodity_id !== undefined) {
                this._newPackageDataProcessing(data,val)
              }
              if (val.item) {
                val.item.forEach(i => {
                  this._newPackageDataProcessing(data,i);
                })
              }
            })
          });
          this.dataProcessing(goodsPackage);
          this.$_closeGoodsPackageModal();
        } else {
          this.modalError(message);
        }
    },
    _newPackageDataProcessing (data, val) {
      let find = data.find(v => +v.id === +val.commodity_id);
      if (find) {
        val.price = find.price;
        val.logo = find.logo;
        val.stock = find.stock;
        val.discount = find.discount;
        val.org_price = find.org_price;
        val.in_price = find.in_price;
        val.tax_rate = find.tax_rate;
        val.tax_rate_price = find.tax_rate_price;
        val.remark = find.remark;
        val.inner_remark = find.inner_remark;
        val.unit_sell = find.unit_sell;
      }
      return val
    },
    async handleSetCols(cols) {
      if (!this.hasGetCustomFieldKeys) {
        this.hasGetCustomFieldKeys = true;
        let customFieldKeys = await this.setCustomizeFieldKeys();
        this.originCols = this.originCols.concat(customFieldKeys);
      }
      // 因为有必选项，cols 为 0 时是异常情况
      // 这里如果 cols 为空，取上一次的值，不为空则保存此次的值
      if (cols.length === 0) {
        cols = this.deepClone(this.colsCopy);
      } else {
        this.colsCopy = this.deepClone(cols);
      }
      let columns = this.initCols(cols, this.originCols);
      columns = this.deepClone(columns);
      const defaultCols = [
        {
          title: ' ',
          key: 'notice',
          width: 1,
          render: (h, params) => {
            if (params.index !== this.activeRowIndex) {
              return false;
            }
            let template = h(
              'Tooltip',
              {
                props: {
                  always: true,
                  transfer: true,
                  placement: 'left'
                }
              },
              [
                h('p', {
                  style: {
                    marginLeft: '-20px'
                  }
                }),
                h(
                  'div',
                  {
                    slot: 'content'
                  },
                  [h('p', '新增商品将添加'), h('p', '到当前商品之前')]
                )
              ]
            );
            return template;
          }
        },
        {
          title: ' ',
          width: 70,
          resizable: false,
          align: 'center',
          render: (h, params) => {
            return h(
              'Poptip',
              {
                props: {
                  title: '确认删除此商品？',
                  confirm: true,
                  transfer: true,
                  placement: 'right'
                },
                on: {
                  'on-ok': () => {
                    this.delCommodity(params);
                  }
                },
                // nativeOn: {
                //   click: $event => {
                //     $event.stopPropagation();
                //   }
                // }
              },
              [
                h(
                  'i',
                  {
                    class: {
                      'del-commodity': true
                    },
                    style: {
                      color: 'rgba(237,63,20,.6)'
                    }
                  },
                  '删除'
                )
              ]
            );
          }
        }
      ];
      columns.splice(0, 0, ...defaultCols);
      if (!this.userHasContractPriceOrder) {
        if (columns.some(col => col.key === 'org_price')) {
          columns.splice(
            columns.findIndex(col => col.key === 'org_price'),
            1
          );
        }
        if (columns.some(col => col.key === 'discount')) {
          columns.splice(
            columns.findIndex(col => col.key === 'discount'),
            1
          );
        }
      }
      this.goodsColumns = columns;
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys () {
      return new Promise((resolve, reject) => {
        this.$request.get(this.apiUrl.customizeFieldKeys).then(res => {
          if(res.status && res.data && res.data.length) {
            let keys = [];
            res.data.forEach((item) => {
              keys.push({
                title: item.name,
                key: item.key
              })
            })
            resolve(keys)
          } else {
            resolve([]);
          }
        })
      })

    },
    updateCols(cols) {
      cols =
        cols ||
        this.originCols
          .filter(col =>
            this.goodsColumns.some(showCol => showCol.key === col.key)
          )
          .map(col => col.key);
      this.handleSetCols(cols);
    },
    // 订单标签限制
    checkTag() {
      // __MARK setTimeout(() => {
      if (this.orderTagList.length === 0) return true;

      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.$Message.warning('一个订单最多存在3个标签');
        return false;
      }

      if (this.only_tag === '1' && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.$Message.warning('一个订单只能选择一个标签');
        return false;
      }

      if (this.must_tag === '1' && this.selectedOrderTag.length === 0) {
        this.$Message.warning('请至少选择一个订单标签');
        return false;
      }
      return true;
      // });
    },
    // 获取订单标签列表
    getOrderTagList() {
      common.qryOrderTagList().then(res => {
        if (+res.status === 1) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = res.data;
          }
        }
      });
    },
    rowClassName(row, index) {
      return index === this.activeRowIndex ? 'table-row-active' : '';
    },
    handleRowClick(row, index) {
      this.activeRowIndex = index;
    },
    resetUser() {
      this.user = {
        id: '',
        uid: '',
        name: '',
        detail: ''
      };
    },
    initCommodity() {
      this.commodity = {
        id: '', // 序号
        cid: '', // 商品id
        name: '',
        amount: '',
        detail: '',
        price: 0,
        discount: 100,
        org_price: 0
      };
    },
    // 订购数量单可以直接加入商品到列表
    enterAmount: function(event) {
      event.target.blur();
      // 传1代表是enter加入商品
      if (this.isShowSyncContractPrice) {
        this.focusPriceInput();
      } else {
        window.ez.debounce(this.addCommodityToList(1), 1000);
      }
      this.commodityList = [];
    },
    handelEnterPrice() {
      if (this.isEnableUserContractPriceDiscountRatio) {
        this.focusDiscountInput();
      } else {
        this.addCommodityToList();
      }
    },
    handleEnterDiscount() {
      this.focusMarketPriceInput();
    },
    handleEnterMarketPrice() {
      this.addCommodityToList();
    },
    focusPriceInput() {
      this.$nextTick(() => {
        let dom = this.$refs.priceInput.$el.querySelector('.number_input');
        if (dom) {
          dom.focus();
        }
      });
    },
    focusDiscountInput() {
      this.$nextTick(() => {
        let dom = this.$refs.discountInput.$el.querySelector('.number_input');
        if (dom) {
          dom.focus();
        }
      });
    },
    focusMarketPriceInput() {
      this.$nextTick(() => {
        let dom = this.$refs.marketPriceInput.$el.querySelector(
          '.number_input'
        );
        if (dom) {
          dom.focus();
        }
      });
    },
    handleChangePrice(goods) {
      if (!this.isEnableUserContractPriceDiscountRatio) {
        return false;
      }
      let discount = goods.discount || 100;
      discount = discount / 100;
      goods.org_price = (goods.price / discount).toFixed(2);
    },
    handleChangeDiscount(goods) {
      this.handleChangePrice(goods);
    },
    handleChangeMarketPrice(goods) {
      let marketPrice = goods.org_price || 0;
      let discount = goods.discount || 100;
      discount = discount / 100;
      goods.price = (marketPrice * discount).toFixed(2);
    },
    updateStoreListPrice(params) {
      this.postList[params.index].price = params.row.price;
      this.postList[params.index].discount = params.row.discount;
      this.postList[params.index].org_price = params.row.org_price;
    },
    isProtocolGoods(goodsInfo) {
      return Goods.isProtocolGoods(goodsInfo);
    },
    isGoodsExists() {
      return this.newOrderList.some(goods => goods.commodity_id === this.commodity.cid);
    },
    //获取复制的商品详情
    getOrderDetail(id) {
      common.getModifyOrderDetail(id, true).then(res => {
        if (res.status) {
          var data = res.data.data,
            list = data.commodityList.reverse(),
            detail = data.order;

          list.forEach((e, i) => {
            e.amount = e.order_amount;
            if (
              this.isShowSyncContractPrice &&
              this.isEnableUserContractPriceDiscountRatio
            ) {
              e.discount = e.discount || 100;
              if (e.org_price === '') {
                this.handleChangePrice(e);
              }
            }
            this._updateStepPricing(e, i)
          });

          // 复制订单时过滤积分商品
          if (this.ifFromCopy) {
            list = list.filter(goods => !Goods.isCreditGoods(goods));
          }

          this.user.id = detail.user_id;
          this.user.name = detail.user_name;
          this.user.detail = detail;
          this.user.detail.name = detail.receive_name
          this.user.detail.tel = detail.receive_tel
          this.remarks = detail.remark;
          this.newOrderList = list;
          this.postList = this.deepClone(this.newOrderList);
          this.userList = [1];
          this.selectedDeliveryTime = detail.delivery_time_id;
          this.checkContractPrice();
        }
      });
    },
    handlerAdd(orders) {
      if (orders) {
        orders.forEach(item => {
          item.id += `$_${++this.idGeneratorIndex}`
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +item.is_price_type === 2 &&
            +item.price === 0
          ) {
            this.$Message.warning(`${item.name}未设置客户类型价`);
          }
          item.discount = item.discount || 100;
          if (!item.inner_remark) {
            item.inner_remark = '';
          }
          if (item.org_price === '') {
            this.handleChangePrice(item);
          }
        });
        this.newOrderList.splice(this.activeRowIndex, 0, ...orders.reverse());
        // if (this.goodsPackagePriceModeCombos || this.isOpenSplitOrders) {
          this.syncPostList();
          // this.dataProcessing(...orders.reverse(),1);
        // }
        this.activeRowIndex = 0;
      }
    },
    // 批量新增订单
    addGoods() {
      if (this.user.name) {
        this.showGoodsListModal = true;
      } else {
        this.modalError('请选择正确的客户');
      }
    },
    // 检测是否输入用户
    isUserInput: function() {
      if (!this.user.id) {
        this.modalError('请选择正确的客户');
        this.focusUserInput();
      }
    },
    handleChangeDate(date) {
      this.delivery_date = date;
      // 协议单按发货日期模式生效
      if (
        this.newOrderList &&
        this.newOrderList.length > 0 &&
        this.isContractPriceModeDelivery
      ) {
        this.$Modal.warning({
          title: '提示',
          content:
            '协议价按发货日期生效时,更改订单发货期日,商品价格以下单时价格为准!'
        });
      }
      this.syncPostList();
      this.checkContractPrice();
    },
    // 获取现在的日期
    getDate: function() {
      this.date = DateUtil.getTomorrow();
      this.delivery_date = this.date;
    },
    getDeliveryTimeList: function() {
      var self = this;
      common.getDeliveryTimeList().then(res => {
        if (res.status) {
          res.data.list.forEach(time => {
            let timeJson = {
              id: time.id,
              timeDu: time.start_time + '-' + time.end_time
            };
            self.deliveryTimeList.push(timeJson);
          });
        }
      });
    },
    setScrollTop: function(id) {
      var dropdown = document.getElementsByClassName('newOrder-dropdown')[id];
      setTimeout(function() {
        if (dropdown) {
          dropdown.scrollTop = 0;
        }
      }, 0);
    },
    userKeyControll: function() {
      var self = this,
        length = self.userList.length,
        first = self.userList[0].uid,
        last = self.userList[length - 1].uid,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0];

      document.onkeydown = function(event) {
        var e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        // self.user.uid = first;
        self.user.uid !== '' ? (checkId = self.user.uid) : (checkId = -1);
        // 如果点击向上按钮
        if (e && e.keyCode === 38 && self.userDropDown) {
          if (checkId !== -1 && checkId > first) {
            self.user.uid = self.user.uid - 1;
            // 控制滚动条的滚动，50为单个选项的高度
            // if (checkId !== 0 && checkId )
            dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 50);
          }
        }

        // 如果点击向下按钮
        if (e && e.keyCode === 40 && self.userDropDown) {
          // 如果是首次点击向下
          if (checkId === -1) {
            self.user.uid = first;
          }
          if (checkId !== -1 && checkId < last) {
            self.user.uid = self.user.uid + 1;
            // 获取下面的内容
            if ((checkId !== 0 && checkId % 3 === 0) || checkId > 3) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 50);
            }
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          if (self.user.uid === 0 || self.user.uid) {
            self.user.id = self.userList[self.user.uid].id;
            self.user.name = self.userList[self.user.uid].email;
            self.user.detail = self.userList[self.user.uid];
            // 处理左侧栏样式问题
            if (self.user.id) {
              self.$store.state.isOrderClose = 1;
            } else {
              self.$store.state.isOrderClose = '';
            }
          }
        }
      };
    },
    commodityKeyControll: function() {
      var self = this,
        length = self.commodityList.length,
        first = self.commodityList[0].id,
        last = self.commodityList[length - 1].id,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[1];

      document.onkeydown = function(event) {
        var e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        self.commodity.id !== ''
          ? (checkId = self.commodity.id)
          : (checkId = -1);

        // 如果点击向上按钮
        if (e && e.keyCode === 38 && self.commodityDropDown) {
          if (checkId !== -1 && checkId > first) {
            self.commodity.id = self.commodity.id - 1;
          }
          // 控制滚动条的滚动，62为单个选项的高度
          dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 62);
        }
        // 如果点击向下按钮
        if (e && e.keyCode === 40 && self.commodityDropDown) {
          // 如果是首次点击向下
          if (checkId === -1) {
            self.commodity.id = first;
          }
          if (checkId !== -1 && checkId < last) {
            self.commodity.id = self.commodity.id + 1;
            // if ((checkId !== 0 && checkId % 3 === 0) || checkId > 3) {

            // }
            dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 62);
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          // 如果已经加入购物车，则无法选择
          if (self.commodity.id === 0 || self.commodity.id) {
            self.commodity.cid =
              self.commodityList[self.commodity.id].commodity_id;
            self.commodity.name = self.commodityList[self.commodity.id].name;
          }
        }
      };
    },
    addUserSave(value) {
      var self = this,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0];

      self.historyAddOrderActive = false;
      // 如果已经选择商品，则提示
      if (self.newOrderList.length) {
        self.$Modal.confirm({
          title: '修改客户',
          // content: '<p>修改客户之后将清空已加入的商品列表</p>',
          content: '<p>确定修改客户？</p>',
          onOk: function() {
            self.user.uid = value.uid;
            self.user.id = value.id;
            self.user.name = value.email;
            self.user.detail = value;

            self.checkContractPrice();

            /*
               self.newOrderList = [];
               self.postList = [];
               */
            // 刷新商品价格
            self.refreshGoodsList();

            // 将搜索出来的商品列表置顶
            dropdown.scrollTop = 0;
            self.focusGoodsInput();
          },
          onCancel: function() {}
        });
      } else {
        self.user.uid = value.uid;
        self.user.id = value.id;
        self.user.name = value.email;
        self.user.detail = value;
        self.selectedDeliveryTime = value.delivery_time;
        // 将搜索出来的商品列表置顶
        dropdown.scrollTop = 0;
        // 处理左侧栏样式问题
        if (self.user.id) {
          self.$store.state.isOrderClose = 1;
        } else {
          self.$store.state.isOrderClose = '';
        }
        this.checkContractPrice();
        self.focusGoodsInput();
      }
    },
    addUser: function(value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeStart(INDEX_TYPE.createOrder);
      } catch(error) {
        console.log(error)
      }
      // 客户异常提示文案展示
      this.userMessage = '';
      common.accountRemind({ user_id: value.id }).then(res => {
        if (res.status) {
          this.userMessage = res.message;
        }
      });
      //判断是否货到付款
      if (!value.is_cod) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>该客户不支持货到付款是否继续下单？</p>',
          onOk: () => {
            setTimeout(() => {
              this.addUserSave(value);
            }, 300)
          },
          onCancel: () => {
            this.user.id = '';
            this.user.uid = '';
            this.user.name = '';
            this.user.detail = '';
          }
        });
      } else {
        this.addUserSave(value);
      }
    },
    addCommodity: function(value) {
      // 如果已经加入订单列表，则无法再次加入
      if (value.is_selected && !this.isEnableAddSameGoods) {
        this.autoFocus(value);
        // this.modalError('此商品已加入订单列表');
        return;
      }
      this.commodity.cid = value.commodity_id;
      this.commodity.name = value.name;
      this.commodity.price = value.type_price || '';
      this.commodity.discount = value.discount || 100;
      this.commodity.org_price = value.org_price || '';
      // 将搜索出来的商品列表置顶
      var dropdown = document.getElementsByClassName('newOrder-dropdown')[1];
      dropdown.scrollTop = 0;
      document.querySelector('#order-amount-input .ivu-input').focus();
      if (!this.isEnableAddSameGoods && this.isGoodsExists()) {
        this.modalError({
          content: '商品已存在',
          onOk: () => {
            this.quickClick = true;
            this.focusGoodsInput();
          }
        });
        return false;
      }
    },
    searchUser: function() {
      var self = this;
      self.user.id = self.user.uid = ''; // 每次重新搜索必须清空
      // var dropdown = document.getElementsByClassName("newOrder-dropdown")[0];
      // 如果删除客户搜索信息，则清空客户信息
      // self.user = { id: '', uid: '', name: '', detail: '' };
      common.getUserFull(self.user.name).then(function(res) {
        if (res.status) {
          var data = res.data;
          self.userList = data;
          // 加上排序
          for (var i = 0, length = self.userList.length; i < length; i++) {
            if (!self.userList[i].uid) {
              self.userList[i].uid = i;
            }
          }
          i = null;
          // // 修改客户之后隐藏右侧栏
          // if (self.userHandmadeActive) {
          //   self.userHandmadeActive = false;
          //   document.getElementById('sdp-history-add-order').style.display = 'none';
          // }

          if (data && data.length) {
            self.userDropDown = true;
            self.focusUserInput();
            self.userKeyControll();
          } else {
            self.userDropDown = false;
          }
          let firstUser = data[0] || {};
          // 下拉列表默认选中第一个用户
          self.user.default_item = firstUser;
          self.user.uid = firstUser.uid;

          self.setScrollTop(0);
        }
      });
    },
    searchCommodity: function() {
      var self = this;
      this.resetGoodsError();
      self.commodity.id = self.commodity.cid = ''; // 每次重新搜索必须清空
      let param = {
        query: self.commodity.name,
        user_id: self.user.id,
        delivery_date: this.delivery_date
      };

      if (!this.createOrderShowOfflineGoods) {
        param.is_online = 'Y';
      }

      common.getCommodity(param).then(function(res) {
        if (res.status) {
          if (param.query != self.commodity.name) {
            return false;
          }
          var data = res.data.commodities;
          self.commodityList = data;

          // 加上排序
          for (var i = 0, length = self.commodityList.length; i < length; i++) {
            if (!self.commodityList[i].id) {
              self.commodityList[i].id = i;
            }
          }
          i = length = null;

          setTimeout(function() {
            self.setStyle();
          }, 0);

          if (data && data.length) {
            self.commodityDropDown = true;
            self.focusGoodsInput();
            self.commodityKeyControll();
          } else {
            self.commodityDropDown = false;
          }
          if (!data.length) {
            return false;
          }
          let firstCom = data[0];
          self.commodity.cid = firstCom.commodity_id; // 默认选中第一个商品
          self.commodity.id = firstCom.id; // 默认选中第一个商品
          self.commodity.default_item = firstCom; // 默认选中第一个商品
          self.setScrollTop(1);
        }
      });
    },
    // 遍历查询商品，如果已经被选中，则设置为不可选择
    setStyle: function() {
      var self = this,
        list = document.getElementsByClassName('commodity-list')[0],
        goodsEls = list ? list.getElementsByClassName('dropdown-items') : [];

      //已经加入订单列表的商品设置属性区分
      if (self.newOrderList.length) {
        self.commodityList.forEach(goods => {
          const findIndex = self.newOrderList.findIndex(item => item.commodity_id === goods.commodity_id);
          if (~findIndex) {
            goods.is_selected = true;
            goodsEls[findIndex] ? (goodsEls[findIndex].className = 'dropdown-items selected') : undefined;
          }
        })
        // for (
        //   var k = 0, newLength = self.newOrderList.length;
        //   k < newLength;
        //   k++
        // ) {
        //   for (var i = 0, length = self.commodityList.length; i < length; i++) {
        //     if (
        //       self.commodityList[i].commodity_id === self.newOrderList[k].id
        //     ) {
        //       self.commodityList[i].is_selected = true;
        //       goods[i]
        //         ? (goods[i].className = 'dropdown-items selected')
        //         : undefined;
        //     }
        //   }
        // }
      }
    },
    // 添加商品
    addCommodityToList() {
      // 避免连续点击多次
      if (this.quickClick) {
        this.quickClick = false;
      } else {
        return false;
      }

      let amount = this.commodity.amount;

      if (this.isGoodsExists() && !this.isEnableAddSameGoods) {
        this.errorNotice('商品已存在');
        this.focusGoodsInput();
        this.quickClick = true;
        return false;
      }

      // 必须选择客户和正确的商品
      if (!this.user.id) {
        this.modalError('请输入客户');
        this.quickClick = true;
        return;
      }
      if (!this.commodity.cid) {
        this.modalError('请输入正确的商品');
        this.quickClick = true;
        // 再次跳转商品选择
        this.focusGoodsInput();
        return;
      }
      if (!amount || amount < 0) {
        this.modalError({
          content: '请输入商品订购数量',
          onOk: () => {
            this.focusAmountInput();
          }
        });
        this.quickClick = true;
        return;
      }

      if (this.isShowSyncContractPrice) {
        if (!this.commodity.price) {
          this.modalError({
            content: '请输入单价',
            onOk: () => {
              this.focusPriceInput();
            }
          });
          this.quickClick = true;
          return false;
        }
        if (this.isEnableUserContractPriceDiscountRatio) {
          if (!this.commodity.discount) {
            this.modalError({
              content: '请输入折扣率',
              onOk: () => {
                this.focusDiscountInput();
              }
            });
            this.quickClick = true;
            return false;
          }
          if (!this.commodity.org_price) {
            this.modalError({
              content: '请输入市场价',
              onOk: () => {
                this.focusMarketPriceInput();
              }
            });
            this.quickClick = true;
            return false;
          }
        }
      }
      common
        .getOrderCommodity(this.user.id, this.commodity.cid, this.delivery_date)
        .then(res => {
          let { data, status, message } = res;
          if (status) {
            if (
              !this.userStylePriceIsZeroDefaultMarkPrice &&
              +data.is_price_type === 2 &&
              +data.price === 0
            ) {
              this.$Message.warning(`${data.name}未设置客户类型价`);
            }
            if (this.isShowSyncContractPrice) {
              data.org_price = this.commodity.org_price;
              data.discount = this.commodity.discount;
              data.price = this.commodity.price;
              if (this.isEnableUserContractPriceDiscountRatio) {
                data.discount = data.discount || 100;
                if (data.org_price === '') {
                  this.handleChangePrice(data);
                }
              }
            }
            data.cid = this.commodity.cid;
            data.commodity_id = this.commodity.cid;
            data.amount = this.commodity.amount;
            data.amount_warning = '';
            // ID先标记为不一样，提交的时候，再去掉
            data.id += `$_${++this.idGeneratorIndex}`;
            data.inner_remark = '';
            this._updateStepPricing(data)
            let activeRowIndex = this.activeRowIndex;
            if (activeRowIndex === 0) {
                this.newOrderList.unshift(this.deepClone(data));
              } else {
                this.newOrderList.splice(activeRowIndex, 0, this.deepClone(data));
                this.activeRowIndex = 0;
              }
            // if (this.goodsPackagePriceModeCombos || this.isOpenSplitOrders) {
              // if (activeRowIndex === 0) {
              //   this.newOrderList.unshift(this.deepClone(data));
              // } else {
              //   this.newOrderList.splice(activeRowIndex, 0, this.deepClone(data));
              //   this.activeRowIndex = 0;
              // }
              // 重新跟新列表数据，防止重新渲染填些的数量和备注被覆盖
              this.syncPostList();
            // }
            // 加入商品之后初始化商品
            this.initCommodity();
            // 再次跳转商品选择
            // if (value === 1) { commodity.focus() };
            this.quickClick = true;
            this.focusGoodsInput();
            this.commodity.amount = '';
            this.isInputNumber = '';
          } else {
            this.modalError(message || '商品不存在', 0);
          }
        });
    },
    // 将历史订单中的商品加入到订单商品列表中
    addHistoryGoods: function(value) {
      var self = this;
      // 必须选择客户和正确的商品
      if (!self.user.id) {
        self.modalError('请输入客户');
        return;
      }
      if (!value || !value.length) {
        self.modalError('请选择正确的历史订单商品');
        return;
      }

      if (
        this.isShowSyncContractPrice &&
        this.isEnableUserContractPriceDiscountRatio
      ) {
        value.forEach(item => {
          item.price = item.curr_price;
          item.discount = item.discount || 100;
          if (item.org_price === '') {
            this.handleChangePrice(item);
          }
        });
      }
      value = value.reverse();
      // 清空已经存在的订单商品列表
      self.newOrderList = [];
      self.newOrderList = value.concat(self.newOrderList);
      this.postList = this.deepClone(this.newOrderList);
    },
    // 获取核对的商品
    getReview: function(value) {
      var self = this;
      self.newOrderList = [];
      self.newOrderList = value.concat(self.newOrderList);
      // for (var i = 0, length = value.length; i < length; i++) {
      //   this.newOrderList.push(value[i]);
      // }
      this.postList = this.deepClone(this.newOrderList);
    },
    addUserHandmade(value) {
      value.commodity_arr.forEach(e => {
        e.amount_pic = '';
      });

      if (!this.user.id) {
        this.user.id = value.user_info.id;
        this.user.detail = {
          name: value.user_info.name,
          tel: value.user_info.tel,
          address_detail: value.user_info.address_detail
        };

        this.newOrderList = [...this.newOrderList, ...value.commodity_arr];
        this.postList = this.deepClone(this.newOrderList);
        return;
      }

      if (this.user.id == value.user_info.id) {
        value.commodity_arr.forEach(e => {
          this.newOrderList.forEach(se => {
            if (e.id == se.id) {
              se.amount = parseFloat(se.amount) + parseFloat(e.amount);
            }
          });
        });

        let diffArr = this.diff(value.commodity_arr, this.newOrderList);
        this.newOrderList = [...this.newOrderList, ...diffArr];
        this.postList = this.deepClone(this.newOrderList);
      }
    },
    //复合数组求差集
    diff(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) {
        return false;
      }
      let result = a;
      for (let i = 0; i < b.length; i++) {
        result = result.filter(e => e.id != b[i].id);
      }

      return result;
    },
    // 按enter之后跳转到订购数的输入框
    enterUser: function() {
      var self = this,
        value = '',
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0],
        userValue = JSON.parse(JSON.stringify(self.user));

      self.tUser ? (value = JSON.parse(JSON.stringify(self.tUser))) : undefined;
      self.historyAddOrderActive = false;

      self.userList.forEach(item => {
        if (item.uid == self.user.uid) {
          this.addUser(item);
        }
      });

      if (self.newOrderList.length) {
        self.$Modal.confirm({
          title: '修改客户',
          // content: '<p>修改客户之后将清空已加入的商品列表</p>',
          content: '<p>确定修改客户？</p>',
          onOk: function() {
            dropdown.scrollTop = 0;
            self.userDropDown = false;
            self.tUser = userValue;
            // 刷新商品价格
            self.checkContractPrice();
            self.refreshGoodsList();
          },
          onCancel: function() {
            self.user = value;
          }
        });
      } else {
        self.tUser = userValue;
        this.focusGoodsInput();
        dropdown.scrollTop = 0;
        self.userDropDown = false;
      }
    },
    checkContractPrice() {
      // 没有启用订单同步客户协议价
      this.showContractPriceTips = false;
      if (!this.isEnableOrderSyncContractPrice) {
        this.syncContractPrice = false;
        return false;
      }
      this.syncContractPrice = true;
      common
        .checkValidUserContractPriceOrder(this.user.id, this.delivery_date)
        .then(res => {
          let { data } = res;
          if (!data) {
            this.userHasContractPriceOrder = false;
            this.syncContractPrice = false;
            this.showContractPriceTips = true;
          } else {
            this.userHasContractPriceOrder = true;
          }
        });
    },
    focusUserInput() {
      this.$nextTick(() => {
        this.$refs.userInput.$el.querySelector('input').focus();
      });
    },
    focusGoodsInput() {
      this.$nextTick(() => {
        document
          .querySelector('#commodity-input')
          .querySelector('input')
          .focus();
      });
    },
    focusAmountInput() {
      this.$nextTick(() => {
        document
          .querySelector('#order-amount-input')
          .querySelector('input')
          .focus();
      });
    },
    // 按enter之后跳转到订购数的输入框
    enterCommodity: function() {
      // 获取订购数的输入框
      var self = this,
        // amount = document
        //   .querySelector('#order-amount-input')
        //   .querySelector('input'),
        dropdown = document.getElementsByClassName('newOrder-dropdown')[1];
        // value = self.commodity;

      // if (
      //   value.default_item &&
      //   value.default_item.name &&
      //   value.name != value.default_item.name
      // ) {
      //   self.commodity.name = value.default_item.name;
      // }

      self.commodityList.forEach(item => {
        if (item.id == self.commodity.id) {
          self.addCommodity(item);
        }
      });

      // var isSelected = self.commodityList.filter(function(item) {
      //   if (item.commodity_id === value.cid && item.is_selected) {
      //     return true;
      //   }
      // });
      // if (isSelected.length) {
      //   self.autoFocus(value);
      // } else {
      //   amount.focus();
      // }
      dropdown.scrollTop = 0;
      self.commodityDropDown = false;
    },
    showNewOrderReview: function() {
      // 有商品才能够打开订单核价
      if (!this.newOrderList.length) {
        this.modalError('您还未选择任何商品');
        return;
      }
      let newList = this.deepClone(this.postList);
      newList.reverse();
      newList.forEach(item => {
        item.check_amount = parseFloat(item.amount);
      });
      this.checkOrderList = newList;
      this.inputCheckOrderList = this.deepClone(newList);
      this.newOrderReviewActive = true;
    },
    /**
     * 刷新商品列表
     */
    refreshGoodsList() {
      let params = {
        user_id: this.user.id,
        commodity_type: 1,
        commodity_string: '',
        showAll: true,
        page: 1,
        pageSize: 99999
      };
      let goodsIdArr = this.postList.map(goods => goods.id);
      params.commodity_string = JSON.stringify(goodsIdArr);
      this.$request.post(this.apiUrl.getGoodsList, params).then(res => {
        let { status, data } = res;
        if (status) {
          let newOrderList = this.newOrderList.filter(goods =>
            data.list.find(retGoods => goods.id === retGoods.id)
          );
          newOrderList.forEach(goods => {
            let retGoods = data.list.find(retGoods => goods.id === retGoods.id);
            if (goods) {
              goods.price = retGoods.price;
              // 刷新相关价格
              goods.in_price = retGoods.in_price;
              goods.store_in_price = retGoods.store_in_price;
              goods.average_price = retGoods.average_price;
              goods.last_receipt_price = retGoods.last_receipt_price;
            }
          });
          this.postList = [];
          this.newOrderList = newOrderList;
          this.syncPostList();
        }
      });
    },
    /**
     * 同步postList 和 newOrderList数据
     */
    syncPostList() {
      let newOrderList = this.deepClone(this.newOrderList);
      newOrderList.forEach(goods => {
        let storeGoods = this.postList.find(
          storeGoods => storeGoods.id === goods.id
        );
        if (storeGoods) {
          Object.keys(goods).forEach(key => {
            goods[key] = storeGoods[key];
            // console.log(key);
          });
        }
      });
      this.newOrderList = newOrderList;
      this.postList = this.deepClone(newOrderList);
    },
    confirmReview() {
      this.inputCheckOrderList.map(checkGoods => {
        this.postList.forEach((goods, index) => {
          if (checkGoods.id == goods.id) {
            goods.amount = checkGoods.check_amount;
            goods.remark = checkGoods.remark;
            let amountInputList = document.querySelectorAll(
              '.newOrder-lists .amount input'
            );
            if (amountInputList && amountInputList.length > 0) {
              amountInputList[index].value = goods.amount;
            }
            this._updateStepPricing(goods);
          }
        });
      });
      this.syncPostList();
      this.newOrderReviewActive = false;
    },
    createOrder: function(value) {
      if (!this.checkData()) {
        return false;
      }

      if (!this.date) {
        this.modalError('请选择日期');
        return;
      }
      // 转换中国时区
      let date = new Date(this.date.valueOf() + 28800000);
      date = date.toISOString().slice(0, 10);

      let self = this,
        userId = self.user.id,
        remarks = self.remarks,
        selectedDeliveryTime = self.selectedDeliveryTime;

      !selectedDeliveryTime && (selectedDeliveryTime = 0);

      if (!self.postList.length) {
        this.errorNotice('请先选择商品再创建订单');
        return;
      }

      let commodityList = this.deepClone(this.postList);

      // 需要降序传给后台。详情等其他地方排序和新增编辑是相反的
      commodityList = commodityList.reverse().map((item, index) => {
        return {
          sort_num: index,
          commodity_id: item.id ? item.id.replace(/\$_.*$/, '') : item.commodity_id,
          amount: item.amount,
          remark: item.remark,
          inner_remark: item.inner_remark,
          price: item.price,
          mutate_price: item.price,
          in_price: item.in_price,
          org_price: item.org_price,
          discount: item.discount
        };
      });

      let orderParams = {
        sync_protocol: this.syncContractPrice ? 1 : 0,
        user_id: userId,
        commodity_list: commodityList,
        delivery_date: date,
        remark: remarks,
        delivery_time: selectedDeliveryTime,
        tag_ids: JSON.stringify(this.selectedOrderTag),
        attachment_link: this.attachmentFiles.map(item=>item.url).join(',')
      };
      orderParams.commodity_list = JSON.stringify(orderParams.commodity_list);

      //判断是否开启订单合并
      if (this.isOpenOrderCombine) {
        if (this.createDisabled) {
          return false;
        }
        self.createDisabled = true;
        //检查订单是否满足合并条件
        common.orderCombineCheck(orderParams).then(res => {
          const { status, message, data } = res;
          const { can_order_combine, combine, order: submitOrder } = data;
          if (status) {
            //1.满足合并条件，提示用户是否进行合并
            if (can_order_combine) {
              this.$Modal.confirm({
                title: '确定要合并到之前的订单吗？',
                content: `<div>
                  <p>订单号:${combine.order_no}</p>
                  <p>下单时间:${combine.create_time}</p>
                  <p>发货日期:${combine.delivery_date}</p>
                  <p>注意:如果已经生成采购单或者已经分拣,请谨慎操作</p>
                </div>`,
                onOk: () => {
                  orderParams.is_order_combine = 1;
                  orderParams.order_no = combine.order_no;
                  this.orderCombine(orderParams, value, true);
                },
                onCancel: () => {
                  orderParams.is_order_combine = 0;
                  orderParams.order_no = combine.order_no;
                  this.orderCombine(orderParams, value, false);
                }
              });
            } else {
              //2.不满足合并条件，后台已默认创建订单，不用手动创建，这里需要清除前端信息
              self.clearInfo(submitOrder, value);
            }
          } else {
            self.errorNotice(message);
          }
        }).finally(() => {
          self.createDisabled = false;
        });
      } else {
        //未开启订单合并，正常提交
        this.confirmAddOrder(orderParams, value);
      }
    },
    /**
     * 确认提交订单
     * @param(params) 提交参数
     */
    confirmAddOrder(params, value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeEnd(INDEX_TYPE.createOrder, {
          goods_count: JSON.parse(params.commodity_list).length,
          page: location.href
        });
      } catch(error) {
        console.log(error)
      }
      let self = this;
      if (this.createDisabled) {
        return false;
      }
      self.createDisabled = true;
      common
        .createOrderV2(params)
        .then(res => {
          let { status, message } = res;
          if (status) {
            self.successNotice(message || '订单创建成功');
            self.clearInfo(res, value);
          } else {
            if (!message) {
              self.errorNotice('订单创建失败！');
            }
            self.errorNotice({
              title: '订单创建失败!',
              desc: message
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    /**
     * 订单提交之后，清除信息
     */
    clearInfo(res, value) {
      let self = this;
      self.newOrderList = [];
      self.postList = [];
      self.newOrderReviewActive = false;
      self.user = { id: '', uid: '', name: '', detail: '' };
      self.commodity = {
        id: '',
        cid: '',
        name: '',
        amount: '',
        detail: ''
      };
      self.remarks = '';
      self.$store.state.isOrderClose = '';
      self.selectedOrderTag = [];
      if (value === 1) {
        setTimeout(function() {
          self.back();
        }, 1000);
      } else if (value === 2) {
        console.log(value);
      } else {
        setTimeout(function() {
          self.back();
        }, 1000);
      }
    },
    /**
     *进行订单合并
     *@param(params) 请求参数
     *@param(value) 区分是哪个点击
     *@param(ifCombine) 是否进行合并
     */
    orderCombine(params, value, ifCombine) {
      let self = this;
      if (this.createDisabled) {
        return false;
      }
      this.createDisabled = true;
      common
        .orderCombine(params)
        .then(res => {
          let { status, message } = res;
          self.createDisabled = false;
          message = message
            ? message
            : ifCombine
            ? '订单合并成功'
            : '订单创建成功';
          if (status) {
            self.successNotice(message);
            self.newOrderList = [];
            self.postList = [];
            self.newOrderReviewActive = false;
            self.user = { id: '', uid: '', name: '', detail: '' };
            self.commodity = {
              id: '',
              cid: '',
              name: '',
              amount: '',
              detail: ''
            };
            self.remarks = '';
            self.$store.state.isOrderClose = '';
            self.selectedOrderTag = [];
            if (value === 1) {
              setTimeout(function() {
                self.back();
              }, 1000);
            } else if (value === 2) {
              console.log(value);
            } else {
              setTimeout(function() {
                self.back();
              }, 1000);
            }
          } else {
            if (!message) {
              self.errorNotice('订单合并失败！');
            }
            self.errorNotice({
              title: '订单合并失败!',
              desc: message
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    checkGoodsAmount(goods) {
      if (!goods.amount || Number(goods.amount) === 0) {
        goods.amount_warning = '请输入订购数量';
        return false;
      }
      return true;
    },
    resetError() {
      this.error = {
        user: '',
        delivery_date: '',
        goods: ''
      };
    },
    resetGoodsError() {
      this.error.goods = '';
    },
    resetUserError() {
      this.error.user = '';
    },
    /**
     * @param {scrollToError} options
     * @returns {boolean}
     */
    checkBaseData(options = {}) {
      let { scrollToError } = options;
      let valid = true;
      let firstErrorDom = null;

      if (!this.user.id) {
        valid = false;
        this.error.user = '请选择客户';
        firstErrorDom = this.$refs['error.user'].$el;
      } else {
        this.error.user = '';
      }
      if (!this.date) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.delivery_date = '请选择发货日期';
      } else {
        this.error.delivery_date = '';
      }
      if (firstErrorDom && scrollToError) {
        firstErrorDom.scrollIntoView();
      }
      return valid;
    },
    checkData() {
      let valid = true;
      let firstErrorGoodsIndex = null;

      if (!this.postList || this.postList.length === 0) {
        valid = false;
        this.error.goods = '请添加商品和数量';
      } else {
        this.error.goods = '';
      }

      // 检查订单标签设置
      if (!this.checkTag()) {
        valid = false;
      }

      // 检查订单商品订购数量
      this.postList.forEach((item, index) => {
        if (!this.checkGoodsAmount(item)) {
          valid = false;
          item.amount_warning = '请输入订购数量';
          if (firstErrorGoodsIndex === null) {
            firstErrorGoodsIndex = index;
          }
        } else {
          item.amount_warning = '';
        }
      });
      this.syncPostList();
      let baseDataValid = this.checkBaseData({
        scrollToError: true
      });

      if (baseDataValid && firstErrorGoodsIndex !== null) {
        this.$nextTick(() => {
          let $firstRow = this.$refs.orderGoodsTable.$el.querySelector(
            `.ivu-table-body .ivu-table-row:nth-child(${firstErrorGoodsIndex})`
          );
          if ($firstRow) {
            $firstRow.scrollIntoView();
          }
        });
      }
      return valid && baseDataValid;
    },
    back() {
      this.router.push({
        path: '/order'
      });
    },
    activeUsualOrder: function() {
      this.usualOrderActive = !this.usualOrderActive;
    },
    activeUserHandmade: function() {
      this.userHandmadeActive = !this.userHandmadeActive;
    },
    activeHistoryAddOrder: function() {
      if (!this.user.id) {
        this.modalError('请先选择客户');
        return;
      }
      this.historyAddOrderActive = !this.historyAddOrderActive;
    },
    cancel() {
      let self = this;
      self.isClose = 1;
      self.router.push({
        path: 'order'
      });
      return true;

      // eslint-disable-next-line no-unreachable
      self.$Modal.confirm({
        title: '确定',
        content: '<p>系统可能不会保存您所做的更改,确定返回列表页面?</p>',
        onOk() {
          self.isClose = 1;
          self.router.push({
            path: 'order'
          });
        },
        onCancel: () => {}
      });
    },
    delCommodity: function(value) {
      let arr = JSON.parse(JSON.stringify(this.newOrderList));
      arr.splice(value.row._index, 1);
      this.newOrderList = arr;
      this.syncPostList();
      this.successNotice(value.row.name + '已删除');
    },
    // 导出订单
    useExportOrder: function(value) {
      // var self = this,
      var id = value.id;

      location.href = '/superAdmin/orderSuper/AjaxExport?order_id=' + id;
    },
    // 已加入列表的商品自动聚焦
    autoFocus: function(value) {
      let goodsId = value.commodity_id ? value.commodity_id : value.cid;
      // let inputObj = document.querySelector(`.amount-${goodsId} input`);
      const amountEls = document.querySelectorAll('.order-amount');
      const findIndex = this.newOrderList.findIndex(goods => goods.commodity_id === goodsId)
      if (amountEls.length && ~findIndex) {
        const inputObj = amountEls[findIndex].querySelector('input');
        inputObj.focus();
        inputObj.select();
        inputObj.scrollIntoView();
      }
      this.commodity.cid = this.commodity.name = '';
    },
    isNumber: function() {
      let goodsAmount = this.$refs.amountInput.$el.querySelector(
        'input[type="text"]'
      );
      if (isNaN(this.commodity.amount)) {
        this.commodity.amount = '';
        setTimeout(function() {
          goodsAmount.value = '';
        }, 0);
      } else {
        // 将正确的数字存储，避免快速操作造成数量不正确的问题
        this.isInputNumber = this.commodity.amount;
      }
    },
    handleChangePriceType(_value){
      window.localStorage.setItem(
      'order_base_price_mode',
      _value
      );
      this.priceType = _value;
      // 更新价格模式配置
      this.commonService.editSingleConfig('order_base_price_mode', _value)
    },
    /**
     * @description: 判断是否为阶梯定价商品
     * @param {*} goods
     */
    isStepPricingGoods (goods) {
      return Goods.isStepPricingGoods(goods)
    },
    /**
     * @description: 若商品配置了阶梯定价，根据商品下单数量，动态计算更新阶梯定价的商品价格
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing (row, index = -1) {
      const { amount } = row
      if (this.isStepPricingGoods(row)) {
        const stepPriceItem = row.price_grads_list.find(item => +amount >= +item.min_order_num && (item.max_order_num ? +amount < +item.max_order_num : true))
        if (stepPriceItem) {
          row.price = stepPriceItem.price
          if (index >= 0 && this.postList[index]) { // 作修改时, 需要更新至postList
            this.postList[index].price = stepPriceItem.price
          }
        }
      }
    }
  },
  components: {
    history,
    NumberInput,
    // editNumberInput,
    userHandMade,
    usual,
    // review,
    GoodsListModal,
    GoodsPackageModal,
    AttachmentUpload,
    StepPricingPoptip
  }
};

window.onresize = function() {
  getWidth();
};
function getWidth() {
  var app = document.getElementsByClassName('new-order')[0],
    feature = document.getElementsByClassName('newOrder-feature')[0];

  feature ? (feature.style.width = app.clientWidth + 'px') : undefined;
}
</script>

<style lang="less" scoped>
.newOrder-dropdown-content {
  left: 0;
}
.newOrder-dropdown {
  left: 0;
}
.newOrder-lists {
  /deep/ .column__order-amount {
    .ivu-table-cell {
      padding-left: 7px;
      padding-right: 0;
    }
  }
}
</style>
<style lang="scss">
.InternalNote {
  color: #3399ff;
}
.ivu-form {
  text-align: left;
}

.add-order {
  padding-bottom: 60px;
  background-color: #fff;
  .amount {
    .ivu-input {
      width: 100px;
    }
  }
}

.newOrder-title {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.newOrder-title-strong {
  margin-right: 5px;
  color: #03ac54;
}

.new-order-main {
  position: relative;
  border-bottom: 8px solid #f5f5f5;
  width: 100%;
  padding: 15px 10px;
  .goods-input {
    .ivu-input-group-append {
      background-color: #03ac54 !important;
    }
  }
}

.new-order-main .ivu-form-item {
  margin-bottom: 0;
}

.new-order-main .ivu-icon-arrow-down-b {
  right: 50px;
}

.new-order-main .ivu-form .ivu-form-item-label {
  text-align: left;
  font-size: 14px;
}

.newOrder-address {
  width: 100%;
  padding: 10px;
  text-align: left;
  border-bottom: 8px solid #f5f5f5;
}

.newOrder-address > span {
  margin-right: 10px;
}

.newOrder-address strong:last-child {
  color: #03ac54;
}

.newOrder-address strong:last-child i {
  margin-right: 5px;
}

.newOrder-address strong:last-child:hover {
  cursor: pointer;
}

.newOrder-operation {
  float: right;
  margin-right: 10px;
  vertical-align: middle;
  font-size: 14px;
  color: #03ac54;
}

.newOrder-operation span:not(:last-child) {
  margin-right: 10px;
}

.newOrder-operation span:hover {
  cursor: pointer;
}

.newOrder-lists {
  width: 100%;
  background-color: #fff;
}

.newOrder-lists input {
  text-align: center;
}

.newOrder-lists img {
  max-width: 50px;
  height: auto;
}

.newOrder-amount {
  padding: 10px;
  text-align: right;
  font-size: 14px;
  color: #ed3f14;
}

.newOrder-remarks {
  margin-top: 10px;
  width: 100%;
  padding: 0 10px;
  border-bottom: 8px solid #f5f5f5;
  .ivu-form-item {
    width: 100%;
  }
}

.newOrder-other {
  padding: 10px;
  font-size: 14px;
}

.newOrder-other strong {
  color: #03ac54;
}

.newOrder-other strong:hover {
  cursor: pointer;
}

.newOrder-other i {
  margin-right: 5px;
  vertical-align: middle;
  font-size: 18px;
}

.newOrder-other span {
  font-size: 12px;
  color: #bbbec4;
}

.newOrder-feature {
  position: fixed;
  z-index: 2;
  bottom: 0;
  padding: 15px;
  text-align: left;
  border-top: 1px solid #eee;
  background-color: #fff;
}

.newOrder-feature button {
  margin-right: 5px;
}

.newOrder-feature > span {
  color: #03ac54;
  margin-right: 5px;
}

.newOrder-feature span:hover {
  cursor: pointer;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
  .slide-fade-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */

 {
  transform: translateX(100px);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
  .fade-leave-to
    /* .fade-leave-active below version 2.1.8 */

 {
  opacity: 0;
}

.dropdown-fade-enter-active {
  transition: all 0.3s ease;
}

.dropdown-fade-leave-active {
  transition: all 0.2s ease;
}

.dropdown-fade-enter,
  .dropdown-fade-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */

 {
  transform: translateY(-20px);
  opacity: 0;
}

.ivu-upload {
  display: inline-block;
}

.newOrder-dropdown,
.newOrder-dropdown-content {
  position: absolute;
  z-index: 199;
  left: 80px;
  margin-top: 5px;
  width: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  max-height: 200px !important;
  box-shadow: 1px 2px 1px #ccc;
  border-radius: 2px;
}

.newOrder-dropdown .active {
  p {
    color: #03ac54;
  }
  color: #03ac54;
  background-color: #ebf7ff;
}

.newOrder-dropdown .selected {
  color: inherit !important; // color: #c3cbd6;
  /* background-color: #c3cbd6; */
}

.newOrder-dropdown .selected:hover {
  // color: #c3cbd6;
  /* background-color: #c3cbd6; */
}

.dropdown-items {
  width: 100%;
  padding: 5px 10px !important;
}

.dropdown-items:hover {
  cursor: pointer;
  background-color: #ebf7ff;
  p {
    color: #03ac54;
  }
  color: #03ac54;
} // .dropdown-items:first-child{

.dropdown-items-strong {
  font-weight: 500;
  font-size: 13px;
  margin-right: 10px;
  line-height: 19.2px;
}

.dropdown-items-span {
  font-size: 12px;
  color: #aaa;
}

.dropdown-items-p {
  line-height: 20px;
  font-size: 13px;
  color: #aaa;
}

.ivu-icon-trash-a:hover {
  cursor: pointer;
  color: rgba(237, 63, 20, 1) !important;
}

.del-commodity:hover {
  cursor: pointer;
  color: rgba(237, 63, 20, 1) !important;
}

.commodity-name {
  display: inline-block !important;
}

.new-logo {
  display: inline-block !important;
  margin-right: 2px;
  padding: 0 5px;
  color: #fff;
  background-color: #3399ff;
  border-radius: 2px;
}

.ivu-date-picker {
  .ivu-input {
    &:hover {
      cursor: pointer;
    }
  }
}

.dropdown-empty {
  padding: 10px;
  color: #80848f;
  text-align: center;
}
.ivu-rate-star-half .ivu-rate-star-content:before,
.ivu-rate-star-full:before {
  // color: #03ac54;
}
.ivu-rate-star-half:hover .ivu-rate-star-content:before,
.ivu-rate-star-full:hover:before {
  // color: #03ac54;
}
</style>
<style lang="scss" scoped>
.ivu-input {
  font-size: 13px;
}
/deep/ .ivu-table {
  overflow-x: auto;
  .ivu-table-body {
    overflow-x: auto !important;
  }
}
</style>
