<template>
  <div class="base-wrapper" v-if="statusList.length">
    <Tabs v-model="filters.status" @on-click="_fetchData">
      <TabPane
        v-for="(status, index) in statusList"
        :key="index"
        :label="status.name"
        :name="status.id"
      ></TabPane>
    </Tabs>
    <ListTable
      ref="listTable"
      outer-border
      :filters="filters"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :extra-selection-count="extraSelectionCount"
      :columns="columns"
      :data-provider="apiUrl.orderModify.list"
      :autoLoadData="false"
      :height="getTableHeight() - 115"
      @on-selection-change="handleSelectionChange"
      :before-request="_beforeRequest"
      keepScroll
      @reset-change="handleResetChange"
    >
      <Button
        slot="batch-checked"
        type="primary"
        v-show="+filters.status === 1"
        class="mr10"
        @click="checkedChange"
      >
        {{ checkedAll ? '勾选当前页内容' : '勾选所有页内容' }}
      </Button>
      <div v-show="selectedOrders.length === 0" slot="before-table">
        <Button
          styleType="btnStyleForAdd"
          @click="router.push('/order-modify/add')"
          >新增</Button
        >
        <ExportButton
          class="ml10"
          text="导出"
          :offline="true"
          :param-getter="getExportParams"
          api="/superAdmin/orderModify/conditionExport"
        ></ExportButton>
      </div>
      <div class="flex-con" slot="batch-operation">
        <div v-show="+filters.status === 1">
          <Button @click="batchAuditHandle" :loading="batchAuditLoading"
            >批量审核</Button
          >
        </div>
      </div>
    </ListTable>
    <Modal title="审核结果" v-model="batchAudit.dialog">
      <Progress
        :value="batchAudit.dealNum"
        :total="batchAudit.total"
      ></Progress>
      <p class="mt10" v-if="batchAuditLoading">批量审核中</p>
      <p class="mt10" v-else>
        审核完成，批量审核成功{{ batchAudit.successNum }}条，失败{{
          batchAudit.failNum
        }}条
      </p>
      <template #footer>
        <Button
          @click="closeBatchAudit"
          type="primary"
          :loading="batchAuditLoading"
          >确认</Button
        >
      </template>
    </Modal>
  </div>
</template>

<script>
import Tabs from '@/components/tabs';
import ListTable from '@/components/list-table';
import Button from '@/components/button';
import KeepAliveMixin from '@/mixins/keepAlive';
import printMixin from '@/mixins/print/orderModifyPrint.js';
import GoodsAutoComplete from '@/components/common/goodsAutoComplete_new.vue';
import { get, post } from '@/api/request';
import api from '@/api/api';
import { Progress } from '@/components';
import DateUtil from '@/util/date';
import authority from '@/util/authority';
import { Icon } from '@components';
import { BUSINESS_TYPE_LIST } from '@/util/const';
import ConfigMixin from '@/mixins/config';
import {
  provider,
  operator,
  store
} from '@/components/standard/sdp-filter-items'

const { hasAuthority } = authority;

const startTime = DateUtil.getBeforeDate(30);
const endTime = DateUtil.getTodayDate();
const defaultFilterTime = [startTime, endTime];

export default {
  name: 'ModifyOrderList',
  mixins: [KeepAliveMixin, printMixin, ConfigMixin],
  components: {
    Tabs,
    ListTable,
    Button,
    Progress,
  },
  data() {
    return {
      extraSelectionCount: 0,
      allIds: '',
      checkedAll: false,
      statusList: [],
      filters: {
        status: '',
      },
      filterItems: Object.freeze([
        {
          type: 'DatePicker',
          key: ['start_time', 'end_time'],
          label: '申请时间',
          defaultValue: defaultFilterTime,
          props: {
            type: 'daterange',
            placeholder: '请选择申请时间',
          },
          onChange: (value) => {
            return {
              value: value,
            };
          },
        },
        {
          type: 'Input',
          key: 'search_value',
          label: '搜索',
          props: {
            placeholder: '输入客户名称/客户账号/源订单号',
          },
        },
      ]),
      advanceItems: Object.freeze([
        {
          items: [
            {
              type: 'custom',
              component: store,
              label: '库房',
              key: 'storage_id',
              attrs: {
                defaultFirst: true,
                clearable: false
              },
            },
            {
              type: 'DatePicker',
              key: ['start_delivery_date', 'end_delivery_date'],
              label: '发货日期',
              props: {
                type: 'daterange',
                placeholder: '请选择发货日期',
              },
            },
            {
              type: 'DatePicker',
              key: ['start_audit_time', 'end_audit_time'],
              label: '审核时间',
              props: {
                type: 'daterange',
                placeholder: '请选择审核时间',
              },
            },
            {
              checked: false,
              label: '审核人',
              type: 'custom',
              component: operator,
              key: 'audit_user',
              attrs: {
                defaultOptions: [{ label: 'system(system)', value: 'system' }],
                valueKey: 'user_name'
              }
            },
            {
              required: false,
              checked: true,
              label: '商品',
              type: 'custom',
              key: 'commodity_name',
              defaultValue: '',
              props: {
                on: {
                  'on-enter': (e) => {
                    this.filters.commodity_id =
                      typeof e === 'object' ? e.commodity_id : '';
                    this.$refs.listTable && this.$refs.listTable.fetchData();
                  },
                  'on-focus': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.listTable.setValue('commodity_name', '', true);
                  },
                  'on-clear': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.listTable.setValue('commodity_name', '', false);
                  },
                },
              },
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
              component: GoodsAutoComplete,
            },
            {
              checked: false,
              type: 'Select',
              label: '差异状态',
              defaultValue: '',
              key: 'price_has_diff',
              show: true,
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '有差异',
                },
                {
                  value: '0',
                  label: '无差异',
                },
              ],
            },
            {
              label: '供应商',
              type: 'custom',
              key: 'split_provider_ids',
              component: provider,
              defaultValue: '',
              attrs: {
                multiple: true,
                clearable: true,
                defaultOptions: [
                  {
                    label: '空',
                    value: '0'
                  }
                ]
              }
            },
            {
              type: 'Select',
              key: 'user_business_type',
              label: '客户业态',
              show: () => {
                return this.sysConfig.tc_platform == 1
              },
              data: [
                {
                  label: '全部',
                  value: '',
                },
                ...BUSINESS_TYPE_LIST
              ],
              props: {
                placeholder: '选择客户业态',
              },
            },
          ],
        },
      ]),
      columns: Object.freeze([
        {
          type: 'titleCfg',
          width: 60,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'order_modify_list',
          style: {
            paddingRight: 0,
          },
        },
        {
          type: 'selection',
          key: 'selection',
          width: 40,
          fixed: 'left',
          style: {
            marginLeft: '-14px',
          },
        },
        {
          title: '变更单号',
          key: 'modify_no',
          minWidth: 180,
          fixed: 'left',
          render: (h, params) => {
            const { row } = params;
            let tagArr = [];
            if (row.source_type == 20) {
              // 同步
              let isAsynOrderTag = h(Icon, {
                props: {
                  icon: 'tongbudingdan',
                  size: 14,
                },
              });
              tagArr.push(isAsynOrderTag);
            }
            return h(
              'div',
              {
                style: {
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
              },
              [
                h(
                  'div',
                  {
                    style: {
                      display: 'flex',
                      alignItems: 'center',
                    },
                  },
                  [
                    h(
                      'a',
                      {
                        class: row.is_can_detail ? 'tableLink' : '',
                        on: {
                          click: () => {
                            row.is_can_detail &&
                              this.router.push({
                                path: 'detail',
                                query: { id: row.id },
                              });
                          },
                        },
                      },
                      tagArr.length === 1
                        ? [
                            tagArr[0],
                            h(
                              'span',
                              { style: { marginLeft: '4px' } },
                              row.modify_no,
                            ),
                          ]
                        : row.modify_no,
                    ),
                  ],
                ),
                h(
                  'div',
                  {
                    style: {
                      color: '#999999',
                    },
                  },
                  row.order_create_time,
                ),
              ],
            );
          },
        },
        {
          title: '变更前金额',
          key: 'original_price',
          align: 'right',
          minWidth: 110,
        },
        {
          title: '变更后金额',
          key: 'price',
          align: 'right',
          minWidth: 110,
        },
        {
          title: '差异金额',
          key: 'diff_price',
          align: 'right',
          minWidth: 100,
        },
        {
          title: '客户',
          key: 'user_name',
          minWidth: 150,
          poptip: true,
          render: (h, params) => {
            return params.row.user_name || '--'
          },
        },
        {
          title: '客户业态',
          key: 'user_business_type_desc',
          width: 120,
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
        },
        {
          title: '客户账号',
          key: 'account_tel',
          minWidth: 120,
        },
        {
          title: '发货日期',
          key: 'delivery_date',
          minWidth: 120,
        },
        {
          title: '审核时间',
          key: 'audit_time',
          minWidth: 110,
        },
        {
          title: '审核人',
          key: 'audit_user',
          minWidth: 120,
        },
        {
          title: '源订单',
          key: 'order_no',
          minWidth: 190,
          render: (h, params) => {
            const { row } = params;
            return h(
              'a',
              {
                class: 'tableLink',
                on: {
                  click: () => {
                    this.router.push({
                      path: '/orderDetail',
                      query: {
                        keep_scroll: 1,
                        id: row.order_id,
                        orderNo: row.order_no,
                        isCanEdit: false,
                        isCanApproval: false,
                        needCut: 1,
                      },
                    });
                  },
                },
              },
              row.order_no,
            );
          },
        },
        {
          title: '所属供应商',
          key: 'split_provider_name',
          minWidth: 120,
          poptip: true,
        },
        {
          title: '单据状态',
          key: 'status_desc',
          minWidth: 100,
          render: (h, { row }) => {
            const { status_desc } = row;
            return (
              <span
                class={{ 'status-warning': ['待审核'].includes(status_desc) }}
              >
                {status_desc}
              </span>
            );
          },
        },
        {
          title: '来源',
          key: 'source',
          minWidth: 100,
        },
        {
          type: 'action',
          title: '操作',
          key: 'action',
          width: 195,
          fixed: 'right',
          actions: ({ row }) => {
            const actions = [
              {
                name: '编辑',
                ctrl: 'is_can_edit',
                action: () => {
                  this.$router.push({
                    path: 'edit',
                    query: { id: row.id },
                  });
                },
              },
              {
                name: '打印',
                ctrl: true,
                action: () => {
                  this._mxPrintOrderModify({
                    id: row.id,
                  });
                },
              },
              {
                name: '审核',
                ctrl: 'is_can_audit',
                confirm: '审核通过后源订单的发货数量会改变，是否审核通过?',
                action: this.apiUrl.orderModify.audit,
                error: '审核失败',
                param: 'id',
                success: '审核成功',
              },
              {
                name: '反审核',
                ctrl:
                  hasAuthority('order-modify_anti-audit') &&
                  row.is_can_re_audit,
                action: () => {
                  this.$router.push({
                    path: 'edit',
                    query: { id: row.id, anti: 1 },
                  });
                },
              },
              {
                name: '关闭',
                ctrl: 'is_can_close',
                confirm:
                  '关闭后实收变更单据不会变更数量，若需重新变更数量需要重新发起，是否确定?',
                action: this.apiUrl.orderModify.close,
                param: 'id',
                success: '关闭成功',
              },
            ];
            return actions.filter(
              (action) => action.ctrl === true || row[action.ctrl],
            );
          },
        },
      ]),
      selectedOrders: [],
      batchAuditLoading: false,
      batchAudit: {
        dialog: false,
        total: 0,
        dealNum: 0,
        successNum: 0,
        failNum: 0,
        // 每次处理的数量
        lotNum: 20,
      },
    };
  },
  created() {
    this.getSearchConfig();
  },
  methods: {
    _setAdvanceItemsData(itemKey, key, value) {
      const index = this.advanceItems[0].items.findIndex(
        (item) => item.key === key,
      );
      this.advanceItems[0].items[index][itemKey] = value;
    },
    getRealId() {
      let ids = this.selectedOrders.map((row) => row.id).join(',');
      if (this.checkedAll && this.allIds) {
        let arr = this.allIds.split(',');
        let all = this.$refs.listTable.data.map((row) => row.id);
        let check = this.selectedOrders.map((row) => row.id);
        let noCheck = [];
        let realids = [];
        all.forEach((e) => {
          if (!check.includes(e)) {
            noCheck.push(e);
          }
        });
        if (noCheck.length > 0) {
          arr.forEach((res) => {
            if (!noCheck.includes(res)) {
              realids.push(res);
            }
          });
          ids = realids.join(',');
        } else {
          return this.allIds;
        }
      }
      return ids;
    },
    checkedChange() {
      this.checkedAll = !this.checkedAll;
      this.$refs.listTable.$refs.table.handleToggleCheckAll(true);
      if (this.checkedAll) {
        if (
          this.$refs.listTable.curPagination.total >
          this.$refs.listTable.curPagination.pageSize
        ) {
          this.extraSelectionCount =
            this.$refs.listTable.curPagination.total -
            this.$refs.listTable.curPagination.pageSize;
          if (
            this.$refs.listTable.data.length <
            this.$refs.listTable.curPagination.pageSize
          ) {
            this.extraSelectionCount =
              this.$refs.listTable.curPagination.total -
              this.$refs.listTable.data.length;
          }
          let params = this.$refs.listTable.getParams();
          params.is_only_ids = 1;
          console.log(params, 'jkjk');
          get(api.orderModify.list, params).then((res) => {
            this.allIds = res.data.ids;
          });
        } else {
          this.allIds = '';
          this.extraSelectionCount = 0;
        }
      } else {
        this.allIds = '';
        this.extraSelectionCount = 0;
      }
    },
    /**
     * author: 夏金
     * time: 2021/3/8 11:39
     * use: ->设置导出参数
     */
    getExportParams() {
      let params = this.$refs.listTable.getParams();
      return params;
    },
    /**
     * @description: 加载列表数据（mixin中有用到）
     * @param {Boolean} resetPage
     */
    _fetchData(resetPage) {
      if (!this.filters.status) this.filters.status = '';
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage);
    },
    getSearchConfig() {
      this.$request.get(this.apiUrl.orderModify.searchConfig).then((res) => {
        let { status, data } = res;
        if (status) {
          this.statusList = data.status.map((item) => {
            item.id = String(item.id);
            return item;
          });
          this.filters.status = data.status[0].id;
        }
      });
    },
    handleResetChange() {
      this.filters.commodity_id = '';
    },
    handleSelectionChange(selection) {
      this.selectedOrders = selection;
    },
    _beforeRequest(params) {
      if (!params.storage_id) return false

      if (params.commodity_id) {
        params.commodity_name = '';
      }
      return params;
    },
    closeBatchAudit() {
      this.batchAudit.dialog = false;
      this.batchAudit.total = 0;
      this.batchAudit.dealNum = 0;
      this.batchAudit.successNum = 0;
      this.batchAudit.failNum = 0;
    },
    async batchAuditHandle() {
      const ids = this.getRealId();
      if (!ids || ids.length === 0) {
        this.errorMessage('请选择订单');
        return false;
      }
      this.batchAudit.dialog = true;
      this.batchAuditLoading = true;

      // 将数据按照每次处理的数量分组
      let idsArr = ids.split(',');

      this.batchAudit.total = idsArr.length;

      while (idsArr.length > 0) {
        let dealIds = idsArr.splice(0, this.batchAudit.lotNum);
        let params = {
          id: dealIds.join(','),
        };
        const { status, message, data } = await this.$request.post(
          '/superAdmin/orderModify/audit',
          params,
        );
        if (status) {
          this.batchAudit.successNum += data;
        } else {
          this.batchAudit.failNum += dealIds.length;
        }
        this.batchAudit.dealNum += dealIds.length;
      }
      this.batchAuditLoading = false;
      await this.$refs.listTable._fetchData(true);
    },
  },
};
</script>

<style lang="less" scoped>
.checked_button {
  margin-right: 10px;
  height: 26px;
  line-height: 26px;
  font-size: 12px;
  cursor: pointer;
  color: #fff;
  background: #03ac54;
  padding: 0 5px;
  border-radius: 3px;
}
/deep/ .sdp-table__th {
  background: #f2f4f5;
  .sdp-table__cell {
    background: #f2f4f5;
    height: 40px;
    min-height: 40px;
  }
}
</style>
