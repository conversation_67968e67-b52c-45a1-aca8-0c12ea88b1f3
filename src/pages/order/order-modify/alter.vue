<template>
  <div class="order-alter">
    <DetailPage
      pageType="edit"
      title="追加修改"
      :backTo="
        () => {
          this.$router.go(-1);
        }
      "
      :customSaveBtn="true"
    >
      <s-block title="基础信息" class="base-info__detail">
        <Form inline label-colon :label-width="70" :disabled="false">
          <FormItem label="客户">{{ orderInfo.shop_name }}</FormItem>
          <FormItem label="客户业态" v-if="sysConfig.tc_platform == 1">{{ orderInfo.user_business_type_desc }}</FormItem>
          <FormItem label="发货日期">
            <Date-picker
              @on-change="$_onChangeDate"
              type="date"
              placeholder="选择日期"
              :value="orderInfo.delivery_date"
              format="yyyy-MM-dd"
              :editable="false"
              style="width: 232px"
            ></Date-picker>
          </FormItem>
          <FormItem label="送货时间">
            <Select style="width: 232px" v-model="orderInfo.delivery_time">
              <Option
                v-for="item in deliveryTimeList"
                :value="item.id"
                :key="item.id"
                >{{ item.timeDu }}</Option>
            </Select>
          </FormItem>
          <FormItem label="订单号">{{ orderInfo.order_no }}</FormItem>
          <FormItem label="提交时间">{{ orderInfo.create_time }}</FormItem>
          <FormItem label="付款状态"
            >{{ orderInfo.is_pay_text }}({{ orderInfo.pay_way_text }})</FormItem
          >
          <FormItem label="订单状态">{{ orderInfo.mode_text }}</FormItem>
          <FormItem label="配送方式">{{
            orderInfo.delivery_method_text
          }}</FormItem>
          <FormItem label="收货信息"
            >{{ orderInfo.receive_name }} {{ orderInfo.receive_tel }}
            {{ orderInfo.address_detail }}</FormItem
          >
          <FormItem label="回单状态">
            <RadioGroup v-model="orderInfo.is_receipt">
              <Radio :label="0">未回单</Radio>
              <Radio :label="1">已回单</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="子账号">{{orderInfo.sub_user_name || '-'}}</FormItem>
          <FormItem label="物流单号" v-if="isOpenExpress100">
            <Input
              style="width: 200px"
              maxlength="32"
              v-model="orderInfo.express_no"
            />
          </FormItem>
          <FormItem
            v-if="orderTagList && orderTagList.length > 0"
            label="订单标签"
            style="width: 100%"
          >
            <orderTag
              v-model="orderInfo.tag"
              @on-change="_checkTagLengs"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return only_tag === '1' &&
                  orderInfo.tag.length > 0 &&
                  !orderInfo.tag.includes(item.id)
              }
              ">
            </orderTag>
          </FormItem>
          <FormItem label="外部系统编号" v-if="isOpenTraceabilityPlatform4 || sysConfig.open_openapi == 1">
            {{orderInfo.third_party_order_no || '-'}}
          </FormItem>
          <FormItem
            :label="item.name"
            :key="item.key"
            v-for="item in orderCustomizeField"
          >
            <Input style="width: 256px" v-model="item.value" />
          </FormItem>
        </Form>
      </s-block>
      <base-block title="商品清单">
        <SFullscreen @change="updateTableHeight">
          <Row :gutter="10" type="flex" align="middle" slot="action-left">
            <Col v-if="orderInfo.source != 26 && orderInfo.has_audit_modify_order != 1">
              <goods-list-modal
                :params="{
                  is_online: createOrderShowOfflineGoods ? '' : 'Y',
                  delivery_date: orderInfo.delivery_date,
                  order_id: orderId,
                  query_setting: JSON.stringify(excludeName()),
                  is_sell_independent_from_user_id:
                    superCreateOrderUnsellCommodity,
                }"
                :uid="user.id"
                :selectedGoods="
                  is_open_order_add_same_commodity ? [] : newOrderCommodityList
                "
                @on-add="handlerAdd"
              />
            </Col>
            <Col>
              <SearchInputCommodity
                :label="false"
                :originData="newOrderCommodityList"
                @toScroll="toScroll"
              />
            </Col>
          </Row>
          <EditableTable
            :max-height="tableHeight"
            ref="orderGoodsTable"
            :loading="tableLoading"
            :row-class-name="rowClassName"
            :columns="goodsColumns"
            :data="newOrderCommodityList"
            @on-draggable-data="_onDraggableData"
            @on-row-click="handleRowClick"
            @on-cols-change="handleColsChange"
          >
            <template #after-table-left>
							<div class="after-table-left-hotkey">
								<SIcon icon="tips" :size="12" class="mr6" />
								<span>支持键盘操作，</span>
								<Icon type="ios-arrow-round-back" />
								<Icon type="ios-arrow-round-forward" />
								<span>左右切换，</span>
								<Icon type="ios-arrow-round-up" />
								<Icon type="ios-arrow-round-down" />
								<span>上下换行</span>
								<span v-show="orderInfo.source != 26">，Enter 键新增一行</span>
							</div>
						</template>
            <template #after-table-right>
              <div class="newOrder-amount" v-show="orderTotalAmount">
                <span class="c6">下单数量：</span>
                <span class="c6 mr10 order-total-num">{{ orderTotalNum }}</span>
                <span>合计金额：¥</span>
                <span class="newOrder-amount-total mr10">{{
                  orderTotalAmount
                }}</span>
                <span>发货金额：¥</span>
                <span class="newOrder-amount-total mr10">{{
                  deliveryAmount
                }}</span>
                <span v-show="totalReferenceProfitShow">参考毛利总额：¥</span>
                <span
                  v-show="totalReferenceProfitShow"
                  class="newOrder-amount-total"
                  >{{ totalReferenceProfit }}</span
                >
              </div>
            </template>
          </EditableTable>
        </SFullscreen>
      </base-block>

      <base-block title="其他信息">
        <div class="newOrder-remarks">
          <Form :label-width="70">
            <FormItem label="订单备注">
              <Input
                v-model="remarks"
                type="textarea"
                :maxlength="512"
                show-word-limit
                placeholder="输入订单备注"
              ></Input>
            </FormItem>

            <FormItem label="附件" style="margin-top: 10px">
              <AttachmentUpload v-model="attachmentFiles" />
            </FormItem>
          </Form>
        </div>
      </base-block>
      <template #button-after>
        <Button
          v-if="
            isEnableOrderSyncContractPrice &&
            isHasSyncContractPriceRoot &&
            hasAuthority('A002001004')
          "
          @click="syncPrice"
          :disabled="!isAllOrderState"
          type="primary"
          >刷价</Button
        >
        <Tooltip placement="top" maxWidth="200" :content="brushTips">
          <SIcon icon="help" :size="16" style="cursor: pointer" />
        </Tooltip>
        <Button type="primary" @click="alterOrder" :loading="saveLoading"
          >仅保存本单</Button
        >
        <template
          v-if="
            isEnableOrderSyncContractPrice &&
            isHasSyncContractPriceRoot &&
            hasAuthority('A002001004')
          "
        >
          <Button
            type="success"
            @click="alterOrderAndSync"
            :loading="saveLoading"
            >保存并同步协议单</Button
          >
          <template v-if="is_open_order_add_same_commodity">
            <Tooltip
              placement="top"
              maxWidth="200"
              content="订单中若存在重复商品下单单价、折扣率（订）、协议市场价（订）不一致时，该商品不会同步到协议单中。"
            >
              <SIcon icon="help" :size="16" style="cursor: pointer" />
            </Tooltip>
          </template>
        </template>
      </template>
    </DetailPage>

    <SModal title="提示" ref="smodal" type="warning" @ok="confirm" okTxt="继续">
      <div>
        1.增删改订单普通商品发货数量，保存后会冲销订单所关联的出库单并重新执行出库操作，请谨慎操作
      </div>
      <div>
        2.删改订单联营商品发货数量，保存后会更新订单所关联的库房出入库单以及出入库流水信息，请谨慎操作
      </div>
    </SModal>
    <Modal
      v-model="modal1"
      title="提示"
      ok-text="一键报溢"
      @on-ok="reportLossAndProfit"
      width="600"
    >
      <Table
        :border="false"
        :outer-border="true"
        :columns="negativeOutColumns"
        :data="negativeOutColumnsData"
        :height="getViewportHeight() * 0.75 - 300"
      />
    </Modal>
    <AgreementPriceSelectModal
      :show="agreementPriceSelectModal.show"
      :tableData="agreementPriceSelectModal.data"
      @confirm="agreementPriceSelectModal.confirm"
      @cancel="agreementPriceSelectModal.cancel"
    >
    </AgreementPriceSelectModal>
    <AlterGoodsChooseBatch :storeId="orderInfo.storage_id" ref="chooseBatch" />
  </div>
</template>
<script>
import StepPricingPoptip from '../components/StepPricingPoptip.vue';
import GoodsListModal from '@components/order/goodsListModal';
import Input from '@/components/base-input';
import { Rate } from 'view-design';
import CommoditySelect from '@/components/common/CommoditySelectTable';
import EditableTable from '@/components/editable-table/index.js';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import { getEditTableHeight } from '@/util/common';
import NumberInput from '@components/basic/NumberInput';
import StorageUtil from '@util/storage.js';
import SIcon from '@components/icon';
import common from '@api/order.js';
import Goods from '@api/goods.js';
import ware from '@api/storeRoom.js';
import SButton from '@components/button';
import { getViewportHeight } from '@/util/common';
import AgreementPriceSelectModal from '../../order/components/agreementPrice-select-modal/index.vue';
import SearchInputCommodity from '../../order/components/SearchInputCommodity/index.vue';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue';
import ConfigMixin from '@/mixins/config';
import CalcPrice from '../mixins/calcContractPrice';
import Table from '@components/table';
import { SModal } from '@/components/modal';
import { Modal } from 'view-design';
import intro from '@mixins/intro';
import { uniqueId } from 'lodash-es';
import DetailPage from '@/components/detail-page/index.js';
import authority from '@/util/authority.js';
import AlterGoodsChooseBatch from '../components/AlterGoodsChooseBatch.vue';
import SBlock from '@/components/s-block';
import SFullscreen from '@/components/s-fullscreen';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'
import SdpTableStaticFormWrap from '@/components/standard/sdp-table-static-form-wrap';
import SdpTableStaticPoptip from '@/components/standard/sdp-table-static-poptip';

const { hasAuthority } = authority;
const isGoodsEnableBatch = (row) => {
  return Number(row.is_batch) === 1;
};
const discountRatioKey = 'discount';
const marketPriceKey = 'org_price';
const agreementPriceKey = 'unit_price';

export default {
  name: 'alter',
  mixins: [ConfigMixin, intro, CalcPrice],
  components: {
    SdpTableStaticFormWrap,
    SdpTableStaticPoptip,
    SFullscreen,
    SBlock,
    AlterGoodsChooseBatch,
    AttachmentUpload,
    EditableTable,
    GoodsListModal,
    Modal,
    Input,
    Rate,
    SButton,
    NumberInput,
    // AddGoods,
    DetailPage,
    Table,
    SModal,
    SIcon,
    AgreementPriceSelectModal,
    SearchInputCommodity,
    orderTag,
  },
  data() {
    return {
      orderGoodsTagList: [],
      tableLoading: false,
      saveLoading: false,
      is_brush_price: 0,
      modal1: false,
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      attachmentFiles: [], // 附件
      goodsColumns: [],
      orderInfo: {}, // 订单详情
      newOrderCommodityList: [], // 修改的商品列表数据
      oldOrderCommodityList: [], // 原商品列表数据
      saveData: {}, // 保存的数据
      showGoodsListModal: false,
      isWaringOrderAlter: StorageUtil.getLocalStorage('is_waring_order_alter'),
      user: {
        id: '',
        uid: '',
        name: '',
        detail: '',
      },
      remarks: '',
      authority,
      dataCopy: {}, // 备份远程数据，用于在最后提交时做数据比对
      loading: false,
      orderCustomizeField: [], // 订单自定义字段
      goodsCustomizeField: [], // 订单明细自定义字段
      userHasContractPriceOrder: true,
      syncContractPrice: false, // 是否同步协议价
      deliveryTimeList: [],
      orderTagList: [],
      tableHeight: '',
      addGoodsInfo: {
        id: '',
        num: '',
        price: '',
        org_price: '',
        discount: '',
        unit_price: '',
      },
      current: 0,
      orderInfoData: [],
      orderId: this.$route.query.id,
      originCols: [
        {
          type: 'drag',
          width: 50,
          fixed: 'left',
          render: (h, parmas) => {
            return h('Icon', {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer',
              },
            });
          },
        },
        {
          type: 'titleCfg',
          titleType: 'order_approval',
          width: 52,
          align: 'center',
          fixed: 'left',
          key: 'title',
          render: (h, params) => {
            const { row, index } = params;
            const actions = [];
            // 追加修改根据order->source = 26,锁定下单数量+商品增删操作
            if (this.orderInfo.source == 26 || this.orderInfo.has_audit_modify_order == 1) return ''
            if (row.is_can_reverse_audit != 0) {
              actions.push(
                h(
                  SdpTableStaticPoptip,
                  {
                    props: {
                      poptipProps: {
                        title: '确认删除此商品？',
                        confirm: true,
                        transfer: true,
                        placement: 'right',
                        disabled: this.newOrderCommodityList.length === 1,
                      },
                    },
                    on: {
                      'on-ok': () => {
                        this._deleteGoods(row, index);
                      },
                    },
                  },
                  [
                    h(SIcon, {
                      props: {
                        icon: 'jian',
                        size: 16,
                      },
                      class:
                      'icon-record-editor icon-record-editor--delete custom-teleport',
                    }),
                  ],
                ),
              );
            }
            actions.push(
              h(SIcon, {
                props: {
                  icon: 'jia1',
                  size: 16,
                },
                class: 'pointer icon-record-editor icon-record-editor--delete',
                style: {
                  marginTop: '5px',
                },
                on: {
                  click: (event) => {
                    event.stopPropagation();
                    this._addGoods(index);
                  },
                },
              }),
            );
            return h('div', actions);
          },
        },
        {
          title: '序号',
          align: 'center',
          width: 70,
          key: 'index',
          fixed: 'left',
          render: (h, params) => {
            const template = [];
            // 预售商品
            if (Number(params.row.date_type) > 0) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 20,
                  },
                  style: {
                    color: 'var(--primary-color)',
                    marginRight: '5px',
                  },
                }),
              );
            }
            template.push(h('span', params.index + 1));
            return template;
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          width: 90,
          fixed: 'left',
          render: (h, params) => {
            const { row } = params;
            return h('img', {
              attrs: {
                src:
                  (row.logo ||
                    'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png') +
                  '!40x40',
              },
            });
          },
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          width: 236,
          resizable: true,
          fixed: 'left',
          cellContentStyle: {
            width: '100%'
          },
          render: (h, params) => {
            const { row, index } = params;
            let activityIcon = Goods.getActivityIcon(row);
            const inputProps = {
              type: 'textarea',
              rows: 1,
              autosize: { minRows: 1, maxRows: 2.2 }
            }
            return (
              <div style="display: flex; align-items: center">
                {(row.new && <span class="new-logo">新</span>) ||
                  (row.activity_type_desc && (
                    <img
                      style={{height: '18px', marginRight: '10px'}}
                      src={activityIcon}
                      >{row.activity_type_desc}</img>
                  ))}
                {row.activity_type_desc !== '赠品' ? (
                  <div class="commodity-select">
                    <SdpTableStaticFormWrap
                      displayValue={{
                        value: row.name,
                        placeholder: '商品名/编码/别名/关键字',
                        disabled: row.is_can_reverse_audit == 0 || this.orderInfo.has_audit_modify_order == 1,
                        ...inputProps
                      }}
                      scopedSlots={{
                        default: ({
                        }) => (
                          <CommoditySelect
                            commodityName={row.name}
                            disabled={row.is_can_reverse_audit == 0 || this.orderInfo.has_audit_modify_order == 1}
                            params={{
                              user_id: this.user.id,
                              delivery_date: this.delivery_date,
                              is_online: this.createOrderShowOfflineGoods ? '' : 'Y',
                              order_id: this.orderId,
                              pageSize: 30,
                              query_setting: this.excludeName(),
                            }}
                            dataProvider={common.getCommodity}
                            selectedData={this.newOrderCommodityList}
                            inputProps={inputProps}
                            commodityIdKey="commodity_id"
                            commodityNameKey="commodity_name"
                            onOn-change={(cid, com) => {
                              row.name = com.origin_commodity_name;
                              this._setCommodity(cid, com, row, index)
                            }}
                            onOn-enter={() => this._addGoods(index)}
                            style={{ width: '100%' }}
                            slot-type="order"
                            selectType="table"
                          ></CommoditySelect>
                        ),
                      }}
                    ></SdpTableStaticFormWrap>
                  </div>
                ) : (
                  <span style="margin-left: 8px;">{row.name}</span>
                )}
                {row.is_pool_commodity == 1 && (
                  <span style="margin-left: 8px;color: red">联</span>
                )}
              </div>
            );
          },
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'center',
          minWidth: 120,
        },
        {
          title: '现有库存',
          key: 'stock',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              row.stock ? row.stock + '/' + row.unit_sell : '--',
            );
          },
        },
        {
          title: '单位',
          key: 'unit',
          align: 'center',
          width: 80,
        },
        {
          title: '下单数量',
          key: 'order_amount',
          align: 'left',
          width: 110,
          style: {
            paddingRight: 0,
          },
          render: (h, params) => {
            const { row, index } = params;
            if (row.activity_type_desc == '赠品') {
              return h('span', row.order_amount);
            }
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0;
            this.newOrderCommodityList.forEach((item) => {
              if (row.commodity_id === item.commodity_id) {
                otherAmount += Number(item.order_amount);
              }
            });
            let template = h('div', {}, [
              h(
                'div',
                {
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                  },
                },
                [
                  h(NumberInput, {
                    class: [
                      'order-amount-input',
                      row.amount_warning && 'amount_warning',
                      +row.order_amount === 0 && 'emphasis-tip',
                    ],
                    style: {
                      width: '80px',
                      flexShrink: 0,
                    },
                    props: {
                      disabled:
                        Goods.isCreditGoods(row) ||
                        row.is_can_reverse_audit == 0 || this.orderInfo.source == 26 || this.orderInfo.has_audit_modify_order == 1,
                      value: row.order_amount,
                      min: 0,
                      max: 999999999.99,
                      precision: 2,
                    },
                    on: {
                      'on-change': (val) => {
                        this.newOrderCommodityList[index]['order_amount'] =
                          row.order_amount = val;
                        this._updateStepPricing(row, index); // 更新阶梯定价价格
                        this.computeTotalPrice(params);
                      },
                      'on-blur': () => {
                        this.newOrderCommodityList[index]['order_amount'] =
                          row.order_amount;
                        this.newOrderCommodityList[index]['unit_price'] =
                          row.unit_price;
                      },
                      'on-focus': () => {
                        this.newOrderCommodityList[index]['amount_warning'] =
                          row['amount_warning'] = false;
                      },
                      'on-enter': () => {
                        this.newOrderCommodityList[index]['order_amount'] =
                          row.order_amount;
                        this._addGoods(index);
                      },
                    },
                  }),
                  row._isNewGoods && this.isStepPricingGoods(row) ? (
                    <StepPricingPoptip goods={row}></StepPricingPoptip>
                  ) : null,
                ],
              ),
              h(
                'div',
                {
                  class: {
                    dn:
                      !row.is_sell_stock_alert ||
                      otherAmount <= Number(row.sell_stock),
                  },
                  style: {
                    color: 'red',
                  },
                },
                '库存不足',
              ),
              h(
                'div',
                {
                  style: {
                    color: '#ff6e00',
                    fontSize: '12px',
                    display: +row.order_amount === 0 ? 'block' : 'none',
                  },
                },
                '请注意特殊下单数量！',
              ),
            ]);
            return template;
          },
        },
        {
          title: '下单单价',
          key: 'unit_price',
          align: 'right',
          width: 160,
          render: (h, params) => {
            const { row, index } = params;
            const key = 'unit_price';
            const protocol = h(SIcon, {
              class: '',
              props: {
                icon: 'xie',
                size: 16,
              },
            });
            const tip = h(
              'p',
              {
                style: {
                  color: 'red',
                  fontSize: '12px',
                  // display:'inline-block'
                },
              },
              '低于最近一次进货价',
            );
            const showWaring = +row.in_price <= 0 && +row[key] === 0;
            const warningTip = h(
              'div',
              {
                style: { color: '#ff6e00', fontSize: '12px' },
              },
              '请注意特殊下单单价！',
            );
            let template = [];
            if (Goods.isDiscountGoods(row)) {
              const discountIcon = h(SIcon, {
                class: 'mr5',
                props: {
                  icon: 'zhe',
                  size: 16,
                },
                style: 'color: #ff6600;',
              });
              template.push(discountIcon);
            }
            // 下单时改价的权限
            if (
              hasAuthority('A002001012') &&
              row.activity_type_desc !== '赠品'
            ) {
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'mr5';
                template.push(protocol);
              }
              template.push(
                h(NumberInput, {
                  style: {
                    width: '95px',
                  },
                  class: {
                    'emphasis-tip': showWaring,
                  },
                  props: {
                    value: row[key],
                    min: 0,
                    max: 999999999.99,
                    precision: 2,
                    disabled: row.is_can_reverse_audit == 0,
                  },
                  on: {
                    'on-change': (value) => {
                      this.newOrderCommodityList[index][key] = row[key] = value;
                      this.computeUserContractPrice(row, agreementPriceKey);
                      this.updateNewOrderCommodityList(params);
                      this.computeTotalPrice(params);
                    },
                    'on-enter': () => {
                      this.newOrderCommodityList[index][key] = row[key];
                      this.updateNewOrderCommodityList(params);
                      this._addGoods(index);
                    },
                  },
                }),
              );
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              if (showWaring) {
                template.push(warningTip);
              }
              return template;
            } else {
              template = [h('span', row[key] || '--')];
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'ml5';
                template.push(protocol);
              }
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              return h(
                'div',
                {
                  style: {
                    padding: '5px 0',
                  },
                },
                template,
              );
            }
          },
        },
        {
          title: '下单小计',
          key: 'sub_total_price',
          align: 'right',
          minWidth: 120,
          render: (h, params) => {
            const { row, index } = params;
            const key = 'sub_total_price';
            // 如果开启了小计支持修改且有下单改下权限，可以输入
            if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
              return h(NumberInput, {
                props: {
                  value: params.row[key],
                  min: 0,
                  precision: 4,
                  disabled: this.orderInfo.has_audit_modify_order == 1
                },
                on: {
                  'on-change': (val) => {
                    params.row[key] = val;
                    this.computeOfSubtotalModify(params, false);
                  },
                  'on-blur': (event) => {
                    this.computeOfSubtotalModify(params, false, true);
                  },
                  'on-enter': () => {
                    this.newOrderCommodityList[index][key] = row[key];
                    this._addGoods(index);
                  },
                },
              });
            }
            return h(
              'span',
              {
                class: {
                  'total-amount': true,
                },
              },
              isNaN(row[key]) ? 0 : row[key],
            );
          },
        },
        {
          title: '发货数量',
          width: 130,
          key: 'actual_amount',
          render: (h, params) => {
            let me = this;
            let { row, index } = params;
            let key = 'amount';
            let template = h(NumberInput, {
              props: {
                disabled:
                  Goods.isCreditGoods(row) || row.is_can_reverse_audit == 0 || this.orderInfo.has_audit_modify_order == 1,
                value: isNaN(Number(row[key])) ? 0 : Number(row[key]),
                min: 0,
                max: 999999999.99,
                precision: 2,
              },
              style: {
                width: '80px',
                display: 'inline-block',
              },
              on: {
                'on-focus': (event) => {
                  event.target.select();
                },
                'on-change': (val) => {
                  row[key] = val;
                  me.newOrderCommodityList[index][key] = val;
                  me.computeTotalPrice(params);
                  me.computeSortingActualDiffAmount(params);
                },
                'on-enter'() {
                  me._addGoods(index);
                },
              },
            });

            if (row.activity_type_desc == '赠品') {
              template = h(
                'span',
                isNaN(Number(row[key])) ? 0 : Number(row[key]),
              );
            }
            return h('div', [template]);
          },
        },
        {
          title: '发货单位',
          key: 'unit_sell',
          width: 120,
        },
        {
          title: '发货数量(原)',
          key: 'convert_actual_amount',
          width: 140,
          render: (h, params) => {
            let me = this;
            let { row, index } = params;
            let _isNewGoods = row._isNewGoods;
            let key = 'convert_actual_amount';
            if (row.unit_convert === 'Y' && Number(row.unit_num) - 1 !== 0) {
              row[key] = (row.amount / row.unit_num).toFixed(2);
            } else {
              row[key] = row.amount;
            }
            let template = h(NumberInput, {
              props: {
                value: isNaN(row[key]) ? 0 : row[key],
                min: 0,
                max: 999999999.99,
                precision: 2,
                disabled: row.is_can_reverse_audit == 0 || this.orderInfo.has_audit_modify_order == 1,
              },
              style: {
                width: '80px',
                display: 'inline-block',
              },
              on: {
                'on-focus': (event) => {
                  setTimeout(() => {
                    event.target.select();
                  });
                },
                'on-blur': () => {},
                'on-change': (val) => {
                  row[key] = val;
                  me.newOrderCommodityList[index][key] = val;
                  me.computeAmount(params);
                  me.computeTotalPrice(params);
                },
                'on-enter'() {
                  me._addGoods(index);
                },
              },
            });
            if (row.activity_type_desc == '赠品') {
              template = h('span', isNaN(row[key]) ? 0 : row[key]);
            }
            return h('div', [
              template,
              h(
                'span',
                {
                  style: {
                    'margin-left': '5px',
                    'vertical-align': 'middle',
                  },
                },
                row.unit,
              ),
            ]);
          },
        },
        {
          title: '发货金额',
          key: 'delivery_price',
          width: 120,
          render: (h, params) => {
            let price = 0;
            let row = params.row;
            const key = 'delivery_price';
            if (row.unit_convert === 'Y') {
              price +=
                (Number(row.amount) * Number(row.unit_price)) /
                Number(row.unit_num);
            } else {
              price += Number(row.amount) * Number(row.unit_price);
            }
            price = price.toFixed(2);

            // 如果开启了小计支持修改且有下单改价权限，可以输入
            if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
              return h(NumberInput, {
                props: {
                  value: row[key],
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                  disabled: this.orderInfo.has_audit_modify_order == 1
                },
                on: {
                  'on-change': (value) => {
                    params.row[key] = value;
                    this.computeOfSubtotalModify(params, true);
                  },
                  'on-blur': (event) => {
                    this.computeOfSubtotalModify(params, true, true);
                  },
                  'on-enter': () => {
                    this._addGoods(params.index);
                  },
                },
              });
            }
            return h('span', isNaN(price) ? 0 : price);
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          align: 'center',
          minWidth: 90,
          render: (h, params) => {
            const { row, index } = params;
            let template = h(Input, {
              props: {
                value: row.remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': () => {
                  this.newOrderCommodityList[index]['remark'] = row['remark'];
                  this._addGoods(index);
                },
                'on-change': (e) => {
                  let value = e.target.value;
                  this.newOrderCommodityList[index]['remark'] = value;
                },
              },
            });
            if (row.activity_type_desc == '赠品') {
              template = h('span', row.remark);
            }
            return template;
          },
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
          render: (h, params) => {
            const {
              row: { category_name },
            } = params;
            return <span>{category_name ? category_name : '--'}</span>;
          },
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '实收状态',
          key: 'un_confirm_receive',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {},
              +row.un_confirm_receive === 1 ? '待实收' : '已实收',
            );
          },
        },
        {
          title: '订单商品标签',
          key: 'order_commodity_tag',
          show: () => this.isOpenOrderCommodityTag,
          editRender: { autofocus: '.ivu-select' },
          type: 'select',
          render: (h, { row, index }) => {
            const key = 'order_commodity_tag';
            const getDefaultTag = () => {
              let defaultOption = this.orderGoodsTagList.find(
                (item) => item.is_default === '1',
              );
              let tagId = defaultOption ? defaultOption.id : '';
              row[key] = tagId;
              return tagId;
            };
            return h(mvSelect, {
              attrs: {
                clearable: true,
                placeholder: '请选择',
                transfer: true,
              },
              class: {
                'required-tip':
                  row[key] === '' && this.isOrderCommodityTagRequired,
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal:
                  row.order_commodity_tag !== undefined
                    ? row.order_commodity_tag
                    : getDefaultTag(),
              },
              on: {
                'on-change': (tagId, item) => {
                  row[key] = tagId || '';
                  this.newOrderCommodityList[index][key] = tagId || '';
                },
                'on-open-change': (isOpen) => {
                  setTimeout(() => {
                    row.isSelectOpen = isOpen;
                  }, 100);
                },
                'on-enter-key-up': () => {
                  if (!row.isSelectOpen) this._addGoods(index);
                },
              },
              nativeOn: {
                keydown: (e) => {
                  if ('EnterArrowDownArrowUp'.includes(e.code)) {
                    e.stopPropagation();
                  }
                },
              },
            });
          },
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'right',
        },
        {
          title: '分拣数量',
          key: 'sorting_amount',
        },
        {
          title: '发货差异',
          key: 'sorting_actual_diff_amount',
          render: (h, { row, index }) => {
            return h(
              'span',
              {
                style: {
                  color:
                    Number(row.sorting_actual_diff_amount) !== 0
                      ? '#FF0000'
                      : '#303030',
                },
              },
              row.sorting_actual_diff_amount,
            );
          },
        },
        {
          title: '批次号',
          key: 'batch_no',
          width: 130,
          render: (h, params) => {
            let { row } = params;
            let key = 'batch_no';
            if (
              !isGoodsEnableBatch(row) ||
              (!row[key] && row.diff_batch === undefined)
            ) {
              return h('span', '-');
            }
            let batchNoList = row[key].split(',');
            if (row.diff_batch !== undefined) {
              batchNoList = batchNoList.concat(row.diff_batch.split(','));
            }
            // 去重
            batchNoList = [...new Set(batchNoList)].filter((item) => !!item);
            const batchText =
              batchNoList.length > 0 ? batchNoList.join(',') : '设置批次';
            return h(
              'span',
              {
                style: {
                  color: 'var(--primary-color)',
                  cursor: row.diff_batch !== undefined ? 'pointer' : 'default',
                },
                on: {
                  click: async () => {
                    // 需要选择差异批次
                    if (row.diff_batch !== undefined) {
                      const filterGoodsList = this.diffBatchGoodsList.filter(
                        (item) => item.commodity_id === row.commodity_id,
                      );
                      filterGoodsList.forEach((item) => {
                        item.new = row.amount;
                        item.num = item.new - item.old;
                      });
                      const selectedBatchGoods =
                        await this.$refs.chooseBatch.open(filterGoodsList);
                      const batchNo = selectedBatchGoods[0].batch_no;
                      if (batchNo !== undefined) {
                        row.diff_batch = batchNo;
                      }
                      if (!this.reverse_batch_commodities) {
                        this.reverse_batch_commodities = [];
                      }
                      const diffBatchGoodsIndex =
                        this.reverse_batch_commodities.findIndex(
                          (item) => item.commodity_id === row.commodity_id,
                        );
                      if (diffBatchGoodsIndex > -1) {
                        this.reverse_batch_commodities[diffBatchGoodsIndex] =
                          selectedBatchGoods[0];
                      } else {
                        this.reverse_batch_commodities.push(
                          selectedBatchGoods[0],
                        );
                      }
                    }
                  },
                },
              },
              batchText,
            );
          },
        },
        {
          title: '商品重量(kg)',
          minWidth: 100,
          align: 'right',
          key: 'commodity_weight',
        },
        {
          title: '内部备注',
          width: 150,
          key: 'inner_remark',
          render: (h, params) => {
            let { row, index } = params;
            let template = h(Input, {
              props: {
                value: row['inner_remark'],
              },
              on: {
                'on-change': (e) => {
                  let value = e.target.value;
                  params.row['inner_remark'] = value;
                  this.newOrderCommodityList[index]['inner_remark'] = value;
                },
              },
              nativeOn: {
                click: ($event) => {
                  $event.stopPropagation();
                },
              },
            });
            if (row.activity_type_desc == '赠品') {
              template = h('span', row.inner_remark);
            }
            return template;
          },
        },
        {
          width: 160,
          title: '折扣率(订)%',
          key: 'discount',
          align: 'left',
          render: (h, params) => {
            const { row, index } = params;
            const key = 'discount';
            let template = h(NumberInput, {
              props: {
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.newOrderCommodityList[index][key] = row[key] = value;
                  this.computeUserContractPrice(row, discountRatioKey);
                  this.updateNewOrderCommodityList(params);
                  this.computeTotalPrice(params);
                },
                'on-enter': () => {
                  this.newOrderCommodityList[index][key] = row[key];
                  this._addGoods(index);
                },
              },
            });
            if (row.activity_type_desc == '赠品') {
              template = h('span', row[key]);
            }
            return template;
          },
        },
        {
          width: 100,
          title: '折扣率',
          key: 'protocol_discount',
          render: (h, params) => {
            let key = 'protocol_discount';
            let style = {
              color: params.row[key] - params.row.discount === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          width: 120,
          title: '协议价',
          key: 'protocol_price',
          render: (h, params) => {
            let key = 'price';
            let style = {
              color: params.row[key] - params.row.unit_price === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          width: 130,
          title: '协议市场价(订)',
          key: 'org_price',
          render: (h, params) => {
            const { row, index } = params;
            const key = 'org_price';
            let template = h(NumberInput, {
              props: {
                value: row[key],
                precision: 2,
              },
              on: {
                'on-change': (value) => {
                  this.newOrderCommodityList[index][key] = row[key] = value;
                  this.computeUserContractPrice(row, marketPriceKey);
                  this.updateNewOrderCommodityList(params);
                  this.computeTotalPrice(params);
                },
                'on-enter': () => {
                  this.newOrderCommodityList[index][key] = row[key];
                  this._addGoods(index);
                },
              },
            });
            if (row.activity_type_desc == '赠品') {
              template = h('span', row[key]);
            }
            return template;
          },
        },
        {
          width: 120,
          title: '协议市场价',
          key: 'protocol_org_price',
          render: (h, params) => {
            let key = 'protocol_org_price';
            let style = {
              color: params.row[key] - params.row.org_price === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          width: 140,
          align: 'right',
        },
        {
          width: 80,
          title: '税率',
          key: 'tax_rate_desc',
          align: 'right',
          render: (h, params) => h('span', params.row.tax_rate || 0),
        },
        {
          width: 80,
          title: '税额',
          key: 'tax_rate_price',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            const taxRatePrice = +row.tax_rate
              ? (((row.order_amount * row.unit_price) /
                  (1 + row.tax_rate / 100)) *
                  Number(row.tax_rate)) /
                100
              : 0;
            return h('span', {}, taxRatePrice.toFixed(2));
          },
        },
        {
          title: '客户商品别名',
          key: 'user_commodity_alias_name',
        },
        {
          title: '绑定农批市场',
          minWidth: 140,
          key: 'bind_wholesale_market',
        },
        {
          title: '条形码',
          key: 'barcode',
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 140,
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          width: 120,
        },
        {
          title: '参考毛利',
          key: 'reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row.in_price
            ).toFixed(2);
            return h('span', Number.isNaN(profit) ? '0' : profit);
          },
        },
        {
          title: '参考毛利总额',
          key: 'total_reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row.in_price
            ).toFixed(2);
            // 参考毛利总额=下单数量*参考毛利
            const total = Number(
              +(Number.isNaN(profit) ? '0' : profit) * +params.row.order_amount,
            ).toFixed(2);
            return h('span', Number.isNaN(total) ? '0' : total);
          },
        },
        {
          title: '参考毛利率',
          key: 'reference_profit_rate',
          width: 100,
          render: (h, params) => {
            // 参考毛利率=参考毛利/下单单价*100%
            if (!+params.row.unit_price) return h('span', '0%');
            let profit = params.row.unit_price - params.row.in_price;
            let rate = ((profit / params.row.unit_price) * 100).toFixed(2);
            return h('span', rate + '%');
          },
        },
        {
          title: '实时进货价',
          key: 'curr_in_price',
          width: 120,
        },
        {
          title: '服务费',
          key: 'service_charge',
          width: 70,
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 100,
        },
        {
          title: '基准单位',
          key: 'standard_unit_name',
          show: () => this.is_open_standard_unit,
        },
        {
          title: '基准单位数量',
          key: 'standard_unit_sort_amount',
          show: () => this.is_open_standard_unit,
          render: (h, { row }) => {
            if (!row.standard_unit_name || !row.standard_unit_num) {
              return h('span', '--');
            }
            const amount = row.amount || 0;
            return h(
              'span',
              Number(
                (amount * (row.unit_convert === 'Y' ? 1 : row.unit_num)) /
                  row.standard_unit_num,
              ).tofixed(2),
            );
          },
        },
        {
          title: '商品状态',
          key: 'is_online',
          render: (h, { row }) => {
            return h(
              'span',
              row.is_online === 'Y' ? '上架' : '下架',
            );
          },
        },
        {
          title: '实时采购员/供应商',
          key: 'now_agent_provider',
          minWidth: 160,
          tip: '实时读取当前客户对应商品 默认的采购员/供应商信息',
        },
      ],
      negativeOutColumns: [
        {
          title: '商品',
          key: 'name',
        },
        {
          title: '分拣单位',
          key: 'unit',
        },
        {
          title: '改前数量',
          key: 'old',
        },
        {
          title: '改后数据',
          key: 'new',
        },
        {
          title: '库存量',
          key: 'existing',
        },
        {
          title: '差异',
          key: 'num',
          render: (h, params) => {
            return h(
              'span',
              {
                class: ['c-red-color'],
              },
              params.row.num,
            );
          },
        },
      ],
      negativeOutColumnsData: [],
      agreementPriceSelectModal: {
        show: false,
        data: [],
        confirm: (selectedId) => {
          this.alterOrder({ sync_protocol_id: selectedId });
          this.agreementPriceSelectModal.show = false;
        },
        cancel: (selectedId) => {
          this.agreementPriceSelectModal.show = false;
        },
      },
      totalReferenceProfitShow: false
    };
  },
  created() {
    this.changeAgreementPriceKey(agreementPriceKey);
    this.getOrderDetail();
    this._getDeliveryTimeList();
    this.getOrderGoodsTagList();
    this._initColumns();
    this.commonService.getConfig().then((config) => {
      const {
        is_open_order_tag_required: must_tag,
        is_open_order_tag_only_one: only_tag,
      } = config;
      this.must_tag = must_tag;
      this.only_tag = only_tag;
    });
  },
  computed: {
    // 小计修改计算模式
    totalPriceUpdateMode() {
      return +this.sysConfig.total_price_update_mode;
    },
    isAllOrderState() {
      return +this.sysConfig.batch_brush_order_mode === 0;
    },
    isGoodsNewestPrice() {
      return +this.sysConfig.batch_brush_order_price_type === 0;
    },
    brushTips() {
      return this.isGoodsNewestPrice
        ? '会刷新订单商品下单单价为最新价格（协议价>客户类型价>市场价）'
        : '会刷新订单商品下单单价为当前客户生效的协议价，没有生效的协议价则不进行刷价';
    },
    deliveryAmount() {
      let prices = 0;
      this.newOrderCommodityList.forEach((item) => {
        if (!item.amount) item.amount = 0;
        prices += +item.delivery_price || 0;
      });
      return prices.toFixed(2);
    },
    totalReferenceProfit() {
      let allTotal = 0;
      this.newOrderCommodityList.forEach((item) => {
        // 参考毛利=下单单价-参考成本价
        const profit = (item.unit_price - item.in_price).toFixed(2);
        // 参考毛利总额=下单数量*参考毛利
        const total = Number(
          +(Number.isNaN(profit) ? '0' : profit) * +item.order_amount,
        ).toFixed(2);
        allTotal = Number(
          allTotal + +(Number.isNaN(total) ? '0' : total),
        ).toFixed(2);
      });
      return allTotal;
    },
    isShowSyncContractPrice() {
      return (
        this.isEnableOrderSyncContractPrice && this.userHasContractPriceOrder
      );
    },
    isHasSyncContractPriceRoot() {
      return this.authority.hasAuthority('A002001010');
    },
    orderTotalAmount() {
      return this.newOrderCommodityList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add(+next.sub_total_price || 0), 0)
        .toFixed(2);
    },
    orderTotalNum() {
      return this.newOrderCommodityList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add(next.order_amount), 0)
        .toFixed(2);
    },
  },
  methods: {
    /**
     * 获取送货时间数据
     */
     _getDeliveryTimeList() {
      common.getDeliveryTimeList({ pageSize: 9999 }).then((res) => {
        if (res.status) {
          this.deliveryTimeList = res.data.list.map((time) => {
            return {
              id: time.id,
              timeDu: time.name + ' ' + time.start_time + '-' + time.end_time,
            };
          });
        }
      });
    },
    handleColsChange(cols) {
      this.totalReferenceProfitShow = cols.includes('total_reference_profit');
    },
    $_onChangeDate(date) {
      this.orderInfo.delivery_date = date;
    },
    getOrderGoodsTagList() {
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data;
      });
    },
    updateTableHeight(isFullscreen, screenHeight) {
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 94;
      }
    },
    excludeName() {
      let arr = ['commodity_code', 'bar_code'];
      let goodNameSearch = StorageUtil.getLocalStorage('goodNameSearch')
        ? StorageUtil.getLocalStorage('goodNameSearch')
        : ['commodity_code', 'bar_code'];
      goodNameSearch.forEach((res) => {
        let index = arr.findIndex((ee) => ee == res);
        if (index > -1) {
          arr.splice(index, 1);
        }
      });
      return {
        query_exclude: arr.join(','),
        only_bar_code: StorageUtil.getLocalStorage('isRightMatch')
          ? StorageUtil.getLocalStorage('isRightMatch')
          : false,
      };
    },
    hasAuthority,
    getViewportHeight,
    getEditTableHeight,
    // 一键报溢
    reportLossAndProfit() {
      let stocks = [];
      this.negativeOutColumnsData.forEach((item, idx) => {
        stocks.push({
          id: item.id,
          num: item.num,
          price: item.price,
          remark: '',
        });
      });

      let params = {
        type: 2,
        store_id: this.orderInfo.storage_id,
        order_id: this.$route.query.id,
        stocks: JSON.stringify(stocks),
      };
      ware.createReportLpOrder(params).then((res) => {
        if (res.status) {
          this.$smodal({
            type: 'success',
            title: '提示',
            text: '报溢成功，请审核',
            btns: 1,
            onOk: () => {
              let routeUrl = this.$router.resolve({
                path: '/storeRoom/reportLPAudit',
                query: { id: res.data.id, type: 'diff', isClose: true },
              });
              window.open(routeUrl.href, '_blank');
            },
          });
        } else {
          this.modalError(res.message);
        }
      });
    },
    setIsWaringOrderAlter(val) {
      this.isWaringOrderAlter = val;
      StorageUtil.setLocalStorage('is_waring_order_alter', val);
    },
    async confirm() {
      let saveData = this.saveData;
      this.saveLoading = true;
      try {
        let reverseBatchCommodities = [];
        if (this.reverse_batch_commodities) {
          reverseBatchCommodities = this.reverse_batch_commodities.map(
            (item) => ({
              commodity_id: item.commodity_id,
              batch_data: item.batch_data
                ? item.batch_data.map((batch) => ({
                    id: batch.id,
                    num: batch.outStockNum,
                  }))
                : [],
            }),
          );
        }
        const res = await common.reverseAudit({
          reverse_batch_commodities: JSON.stringify(reverseBatchCommodities),
          reverse_order_commodities: JSON.stringify(
            saveData.reverse_order_commodities,
          ),
          reverse_order: JSON.stringify(saveData.reverse_order),
          order_id: this.$route.query.id,
          del_order_commodity_ids: JSON.stringify(
            saveData.del_order_commodity_ids,
          ),
          sync_protocol: saveData.sync_protocol || 0,
          is_brush_price: saveData.is_brush_price || 0,
          sync_protocol_id: saveData.sync_protocol_id || '',
        });
        this.saveLoading = false;

        if (res.status) {
          this.$smessage({ type: 'success', text: '订单追加修改成功' });
          this.$router.go(-1);
        } else {
          if (res.errCode == 1000101) {
            const diffBatchGoodsList = res.data
              .filter((item) => !!Number(item.is_batch))
              .map((item) => ({
                ...item,
                commodity_id: String(item.id),
              }));
            // 接口返回需要选择批次的商品时，弹出选择批次弹窗
            if (diffBatchGoodsList.length > 0 && !this.canOverFlow) {
              // 选择过批次后再保存，不再弹出选择批次弹窗，直接报溢
              this.canOverFlow = true;

              // 批次库存不足的商品可以选择差异库存
              this.newOrderCommodityList.forEach((item) => {
                if (
                  diffBatchGoodsList.some(
                    (batchGoodsItem) =>
                      batchGoodsItem.commodity_id === item.commodity_id,
                  )
                ) {
                  this.$set(item, 'diff_batch', '');
                }
              });

              this.diffBatchGoodsList = diffBatchGoodsList;

              const selectedBatchGoods =
                await this.$refs.chooseBatch.open(diffBatchGoodsList);
              this.reverse_batch_commodities = selectedBatchGoods.map(
                (item) => ({
                  commodity_id: item.commodity_id,
                  batch_data: item.batch_data,
                }),
              );
              this.newOrderCommodityList = this.newOrderCommodityList.map(
                (item) => {
                  const batchGoods = selectedBatchGoods.find(
                    (goods) => goods.commodity_id === item.commodity_id,
                  );
                  return {
                    ...item,
                    diff_batch:
                      batchGoods && batchGoods.batch_no
                        ? batchGoods.batch_no
                        : item.diff_batch,
                  };
                },
              );
              return;
            }
            // 一键报溢, 过滤批次商品
            this.negativeOutColumnsData = res.data.filter(
              (item) => !Number(item.is_batch),
            );
            if (this.negativeOutColumnsData.length > 0) {
              // 开启报溢弹窗
              this.modal1 = true;
            } else {
              this.$smessage({ type: 'error', text: res.message });
            }
          } else {
            this.$smessage({ type: 'error', text: res.message });
          }
        }
      } catch (error) {
        this.saveLoading = false;
        console.error(error);
      }
    },
    // 修改发货数量（原），同步变动发货数量
    computeAmount(params) {
      if (!params) {
        return;
      }
      params.row.amount = params.row.convert_actual_amount;
      if (
        params.row.unit_convert === 'Y' &&
        Number(params.row.unit_num) - 1 !== 0
      ) {
        params.row.amount =
          params.row.convert_actual_amount * params.row.unit_num;
      }
      this.newOrderCommodityList[params.index]['amount'] = params.row.amount;
    },
    /**
     * @description 开启小计支持修改后的计算逻辑
     * @param {Object} params 计算参数
     * @param {Boolean} isDeliver 是否是发货小计
     * @param {Boolean} onBlur 是否是失焦事件
     */
    computeOfSubtotalModify(params, isDeliver = false, onBlur = false) {
      const totalKey = isDeliver ? 'delivery_price' : 'sub_total_price';
      const anotherTotalKey = isDeliver ? 'sub_total_price' : 'delivery_price';
      const amountKey = isDeliver ? 'amount' : 'order_amount';
      const anotherAmountKey = isDeliver ? 'order_amount' : 'amount';

      // 若单价或数量或小计其中一个数值为0，不论其他两个数值输入多少，均默认为0
      if (onBlur) {
        if (this.totalPriceUpdateMode === 1) {
          if (+params.row.unit_price === 0) {
            params.row[totalKey] = 0;
            params.row[amountKey] = 0;
          }
        } else {
          if (+params.row[amountKey] === 0) {
            this.$nextTick(() => {
              params.row[totalKey] = 0;
              params.row.unit_price = 0;
              params.row[anotherTotalKey] = (
                params.row[anotherAmountKey] * params.row.unit_price
              ).toFixed(2);
            });
          }
        }
        return;
      }
      // 如果是固定单价
      if (this.totalPriceUpdateMode === 1) {
        if (+params.row.unit_price === 0) return;
        params.row[amountKey] = (
          params.row[totalKey] / params.row.unit_price
        ).toFixed(2);
      } else {
        if (+params.row[amountKey] === 0) return;
        if (params.row.unit_convert === 'Y') {
          if (isDeliver) {
            // 修改发货金额时候， 需要考虑是否按基础单位分拣
            params.row.unit_price = (+params.row[totalKey])
              .div(params.row[amountKey])
              .mul(params.row.unit_num)
              .toFixed(2);
            params.row[anotherTotalKey] = (
              params.row[anotherAmountKey] * params.row.unit_price
            ).toFixed(2);
          } else {
            params.row.unit_price = (+params.row[totalKey])
              .div(params.row[amountKey])
              .toFixed(2);
            params.row[anotherTotalKey] = (
              params.row[anotherAmountKey] * params.row.unit_price
            )
              .div(params.row.unit_num)
              .toFixed(2);
          }
        } else {
          params.row.unit_price = (
            params.row[totalKey] / params.row[amountKey]
          ).toFixed(2);
          params.row[anotherTotalKey] = (
            params.row[anotherAmountKey] * params.row.unit_price
          ).toFixed(2);
        }
      }
      this.computeUserContractPrice(params.row, agreementPriceKey);
      Object.keys(this.newOrderCommodityList[params.index]).forEach((key) => {
        this.newOrderCommodityList[params.index][key] = params.row[key];
      });
    },
    //同步发货金额
    computeTotalPrice(params, onlySubtotal = false) {
      const { row } = params;
      if (params && !onlySubtotal) {
        params.row.original_total_price = (
          params.row.amount * params.row.unit_price
        ).toFixed(2);
        this.newOrderCommodityList[params.index].original_total_price =
          params.row.original_total_price;

        params.row.sub_total_price = (
          params.row.order_amount * params.row.unit_price
        ).toFixed(2);

        // 计算发货小计
        let price = 0;
        if (row.unit_convert === 'Y') {
          price += (+row.amount).mul(row.unit_price).div(row.unit_num);
        } else {
          price += (+row.amount).mul(row.unit_price);
        }
        price = price.toFixed(2);
        params.row.delivery_price = price;

        this.newOrderCommodityList[params.index].delivery_price =
          params.row.delivery_price;
        this.newOrderCommodityList[params.index].sub_total_price =
          params.row.sub_total_price;
      }
    },
    computeSortingActualDiffAmount(params) {
      if (params) {
        params.row.sorting_actual_diff_amount = (
          params.row.amount - params.row.sorting_amount || 0
        ).toFixed(2);
        this.newOrderCommodityList[params.index].sorting_actual_diff_amount =
          params.row.sorting_actual_diff_amount;
      }
    },
    // 批量添加商品
    handlerAdd(orders) {
      if (orders) {
        orders.forEach((item, index) => {
          item.id += uniqueId('$unique-');
          item.un_confirm_receive = 0;
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +item.is_price_type === 2 &&
            +item.price === 0
          ) {
            this.$Message.warning(`${item.name}未设置客户类型价`);
          }
          item.order_amount = item.amount; // 添加商品填写的数量写入商品详情中
          if (item.unit_convert === 'Y') {
            item.amount = item.amount.mul(item.unit_num);
          }
          item.unit_price = item.price;
          item.amount_warning = false;
          item._isNewGoods = true;
          if (item.org_price === '') {
            this._computeMarketPrice(item);
          }
          item.protocol_org_price = item.org_price;
          item.batch_data = {};
          item.batch_no = '';
          // if (item.discount == 100) {
          //   item.discount = 0
          // }
          item.protocol_discount = item.discount;
        });

        if (
          this.newOrderCommodityList[this.activeRowIndex] &&
          !this.newOrderCommodityList[this.activeRowIndex].commodity_id
        ) {
          console.log(orders, 'iiiiiiiiii');
          this.newOrderCommodityList.splice(this.activeRowIndex, 1, ...orders);
          this.activeRowIndex += orders.length - 1;
        } else {
          console.log(orders, 'iiiiiiiiii');
          this.newOrderCommodityList.splice(
            this.activeRowIndex + 1,
            0,
            ...orders,
          );
          this.activeRowIndex += orders.length;
        }
        this.newOrderCommodityList.forEach((item, index) => {
          this.computeTotalPrice({ row: item, index });
        });
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    // 添加商品
    _setCommodity(cid, com, row, index) {
      com.cid = com.commodity_id;
      com.price = com.type_price || '';
      com.discount = com.discount || 0;
      com.org_price = com.org_price || '';
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        this.errorMessage('请输入客户');
        return;
      }
      if (!com.cid) {
        this.errorMessage('请输入正确的商品');
        return;
      }
      // 根据is_open_order_add_same_commodity【是否开启订单录入重复商品】
      // 关闭时如果已经加入订单列表，则无法再次加入
      const findIndex = this.newOrderCommodityList.findIndex(
        (goods) => goods.commodity_id === cid,
      );
      if (
        !this.is_open_order_add_same_commodity &&
        ~findIndex &&
        findIndex !== index
      ) {
        this.newOrderCommodityList[index] = {
          // 清空当前行商品
          id: uniqueId('$unique-'), // 生成唯一key
          commodity_id: '',
          order_commodity_tag: undefined,
          actual_amount: 1,
          un_confirm_receive: 1,
          amount: 1,
          convert_actual_amount: 1,
          name: '',
          order_amount: row.order_amount,
          unit_price: '',
        };
        this._focusAmountInput(findIndex);
        return this.warningMessage('商品已存在');
      }
      // 这里通过客户id和商品id获取订单商品数据
      common.getOrderCommodity(this.user.id, com.cid).then((res) => {
        const { status, data, message } = res;
        if (status) {
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +data.is_price_type === 2 &&
            +data.price === 0
          ) {
            this.warningMessage(`${data.name}未设置客户类型价`);
          }
          data.unit_price = data.price;
          data.commodity_id = data.id;
          // 保证id即rowKey唯一即可
          data.id += uniqueId('$unique-');
          data.order_amount = row.order_amount;
          data.amount_warning = false;
          data.un_confirm_receive = 0;
          data.actual_amount = 1;
          data.un_confirm_receive = 1;
          data.amount = 1;
          data.is_online = data.is_online || 'Y';
          if (data.unit_convert === 'Y') {
            data.amount = data.amount.mul(data.unit_num);
          }
          data.convert_actual_amount = 1;
          if (data.org_price === '') {
            this._computeMarketPrice(data);
          }
          data.protocol_org_price = data.org_price;
          // if (data.discount == 100) {
          //   data.discount = 0
          // }
          data.protocol_discount = data.discount;
          data.batch_data = {};
          data.batch_no = '';

          // 如果是从获取出来的商品中删除，则记录, 接口需要传参删除的[订单商品id]
          let _isNewGoods = true;
          data._isNewGoods = _isNewGoods;
          this._updateStepPricing(data);
          this.newOrderCommodityList.splice(index, 1, {
            ...data,
          });
          // 计算发货金额和下单小计
          this.newOrderCommodityList.forEach((item, index) => {
            this.computeTotalPrice({ row: item, index });
          });
          this._updateStepPricingOrderAmountColumnWidth();
          this._focusAmountInput(index);
        } else {
          this.modalError(message || '商品不存在', 0);
        }
      });
    },
    _focusGoodsInput(index) {
      this.activeRowIndex = index;
      this.$nextTick(() => {
        const commodityInput =
          document.getElementsByClassName('commodity-select')[index];
        commodityInput && commodityInput.querySelector('textarea').focus();
      });
    },
    // 添加一行
    _addGoods(index) {
      if (this.orderInfo.source == 26) return;
      this.newOrderCommodityList.splice(index + 1, 0, {
        id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
        commodity_id: '',
        name: '',
        unit_price: '',
        order_commodity_tag: undefined,
        un_confirm_receive: 1,
        amount: 1,
        order_amount: 1,
        convert_actual_amount: 1,
        discount: 0,
        protocol_discount: 0,
        delivery_price: 0,
        is_online: 'Y',
      });
      this._focusGoodsInput(index + 1);
    },
    // 删除一行
    _deleteGoods(row, index) {
      if (row.activity_type_desc == '赠品') {
        return;
      }
      if (this.newOrderCommodityList.length > 1) {
        this.newOrderCommodityList.splice(index, 1);
        this.infoMessage(row.name + '已删除');
      }
    },
    _focusAmountInput(index) {
      setTimeout(() => {
        const $currentRow =
          this.$refs.orderGoodsTable.$el.querySelectorAll('tbody tr')[index];
        $currentRow
          .querySelector('.order-amount-input')
          .querySelector('input')
          .focus();
      });
    },
    rowClassName(row, index) {
      return index === this.activeRowIndex ? 'sdp-table__tr-highlight' : '';
    },

    _onDraggableData(data) {
      // 重新排序
      data.forEach((item, index) => {
        item.sequence = index + 1;
        item.sortBy = index + 1;
      });
      this.newOrderCommodityList = data;
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '0,4',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                // 商品自定义字段，只需要展示
                if (+item.customize_type === 0) {
                  return {
                    title: item.name,
                    key: item.key,
                  };
                }
                // 订单自定义字段，只需要展示
                if (+item.customize_type === 4) {
                  this.goodsCustomizeField.push(item);
                  return {
                    title: item.name,
                    key: item.key,
                    render: (h, { row, index }) => {
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        nativeOn: {
                          change: ($event) => {
                            let value = $event.target.value;
                            this.newOrderCommodityList[index][item.key] = value;
                          },
                        },
                      });
                    },
                  };
                }
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
    handleSetCols() {
      const columns = this.deepClone(this.originCols);
      if (!this.userHasContractPriceOrder) {
        if (columns.some((col) => col.key === 'org_price')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'org_price'),
            1,
          );
        }
        if (columns.some((col) => col.key === 'discount')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'discount'),
            1,
          );
        }
      }
      if (!this.isEnableBatch) {
        if (columns.some((col) => col.key === 'batch_no')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'batch_no'),
            1,
          );
        }
      }
      this.goodsColumns = columns;
    },
    async _initColumns() {
      const customFieldKeys = await this.setCustomizeFieldKeys();
      this.originCols = this.originCols.concat(customFieldKeys);
      this.handleSetCols();
    },
    _resetAddGoodsInfo() {
      this.addGoodsInfo = {
        id: '',
        num: '',
        price: '',
        org_price: '',
        discount: '',
        unit_price: '',
      };
    },
    /**
     * 获取订单标签列表
     */
    _getOrderTagList(user_id) {
      const params = {
        user_id,
      };
      common.qryOrderTagList(params).then((res) => {
        if (res.status) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = res.data;
          }
        }
      });
    },
    /**
     * 限制一个订单最多使用3个标签
     */
    _checkTagLengs() {
      let flag = true;
      if (this.orderInfo.tag.length > 3) {
        this.orderInfo.tag.pop(this.orderInfo.tag.length - 1);
        this.warningMessage('一个订单最多存在3个标签');
      }
      if (this.orderInfo.tag.length === 0) {
        flag = true;
      }
      if (this.only_tag === '1' && this.orderInfo.tag.length > 1) {
        this.orderInfo.tag.pop(this.orderInfo.tag.length - 1);
        this.warningMessage('一个订单只能选择一个标签');
        flag = false;
      }

      if (this.must_tag === '1' && this.orderInfo.tag.length === 0) {
        this.warningMessage('请至少选择一个订单标签');
        flag = false;
      }
      return flag;
    },
    handleChangeDiscount(goods) {
      this.handleChangePrice(goods);
    },
    updateNewOrderCommodityList(params) {
      this.newOrderCommodityList[params.index].unit_price =
        params.row.unit_price;
      this.newOrderCommodityList[params.index].discount = params.row.discount;
      this.newOrderCommodityList[params.index].org_price = params.row.org_price;
    },
    /**
     * @description 格式化传递到后端的订单商品项
     * @param orderCommodityItem 订单商品项
     */
    formatApprovalData(orderCommodityItem, index) {
      // console.log('orderCommodityItem', orderCommodityItem)
      let formatData = {
        commodity_id: orderCommodityItem.id.replace(/\$unique-.*$/, ''),
        order_commodity_id: orderCommodityItem.orderCommodity_id || '',
        unit_price: orderCommodityItem.unit_price,
        amount: orderCommodityItem.order_amount,
        actual_amount: orderCommodityItem.amount,
        org_price: orderCommodityItem.org_price,
        discount: orderCommodityItem.discount,
        remark: orderCommodityItem.remark,
        inner_remark: orderCommodityItem.inner_remark,
        pp_detail_id: orderCommodityItem.pp_detail_id,
        mutate_total_price: orderCommodityItem.sub_total_price,
        mutate_sub_total_price: orderCommodityItem.delivery_price,
        sequence: index + 1,
        order_commodity_tag: orderCommodityItem.order_commodity_tag,
        is_price_type: orderCommodityItem.is_price_type,
        batch_data: orderCommodityItem.batch_data,
      };
      formatData.customize_fields = this.goodsCustomizeField.map((field) => {
        return {
          key: field.key,
          name: field.name,
          value: orderCommodityItem[field.key],
        };
      });
      return formatData;
    },
    getOrderDetail: function () {
      this.tableLoading = true;
      let self = this,
        id = self.$route.query.id,
        orderNo = self.$route.query.orderNo;
      this.loading = true;
      common
        .getOrderDetail(id, orderNo, '1', '1')
        .then(function (res) {
          self.tableLoading = false;
          if (res.status) {
            let data = res.data;
            let order = data.order;
            data.commodityList.forEach((item, index) => {
              item.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
              //  接口把值弄反了，需要反过来。
              let discount = item.discount;
              let orgPrice = item.org_price;
              item.discount =
                item.protocol_discount === undefined
                  ? 100
                  : item.protocol_discount;
              item.org_price =
                item.protocol_org_price === undefined
                  ? 100
                  : item.protocol_org_price;

              item.protocol_discount = discount || 0;
              item.protocol_org_price = orgPrice || '';
              item.sub_total_price = item.original_total_price;
              item.delivery_price = item.actual_total_price;
              item.price_warning = '';
              item.sequence = +index + 1;
            });
            // 记录原始商品列表数据，用于后续判断商品数量是否发生变更
            self.newOrderCommodityList = self.deepClone(data.commodityList);
            self.oldOrderCommodityList = self.deepClone(data.commodityList);
            order.delivery_time = order.delivery_time_id;
            order.tag = order.tag ? order.tag.split(',') : [];
            self.orderInfoData = data.amountRecord;
            if (self.isNewOrderInputVersion) {
              console.log('===================================')
              self.orderCustomizeField = order.customize_fields;
            }
            self.dataCopy = self.deepClone(data);
            self.user = {
              id: order.user_id,
              name: order.receive_name,
              tel: order.receive_tel,
              address: order.address_detail,
            };
            // order.source = 26 // 订单类型为开放api
            self.orderInfo = order;
            self.remarks = order.remark;
            self.attachmentFiles = order.attach_url || [];
            self._getOrderTagList(self.orderInfo.user_id);
          } else {
            self.modalError(res.message);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，输入下单数量改变时，需要动态计算阶梯定价的商品价格
     * !! 只对新增的商品生效
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing(row, index) {
      const { order_amount: amount } = row;
      if (row._isNewGoods && this.isStepPricingGoods(row)) {
        // 仅对新增的商品生效阶梯定价
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true),
        );
        if (stepPriceItem) {
          row.unit_price = stepPriceItem.price;
          if (index) {
            this.newOrderCommodityList[index].unit_price = stepPriceItem.price;
          }
        }
      }
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      if (
        this.newOrderCommodityList.some(
          (goods) => goods._isNewGoods && this.isStepPricingGoods(goods),
        )
      ) {
        const newWidth = 200;
        const order_amount_column = this.goodsColumns.find(
          (item) => item.key === 'order_amount',
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
    handleRowClick(row, index) {
      this.activeRowIndex = index;
    },
    syncPrice() {
      if (!hasAuthority('DDHSSJ1121')) {
        this.errorMessage('您的权限不足，无法进行当前操作！');
        return;
      }
      const refreshPrice = () => {
        this.is_brush_price = 1;
        this.newOrderCommodityList.forEach((item, index) => {
          // 如果是按协议价, 非协议价的商品不刷价
          if (!this.isGoodsNewestPrice && item.new_price_type !== 1) {
            return;
          }
          item.unit_price = item.price || '';
          // 原有商品 new_price_type 对应 新增商品里的 is_price_type 字段
          if (item._isNewGoods) {
            item.org_price = item.org_price || '';
            item.discount = item.discount || 100;
          } else {
            item.is_price_type = item.new_price_type;
            item.org_price = item.protocol_org_price || '';
            item.discount = item.protocol_discount || 100;
          }
          this.computeTotalPrice({ row: item, index });
        });
      };
      this.$smodal({
        title: '确认',
        text: '确定执行刷价？',
        type: 'warning',
        btns: 2,
        okTxt: '确定',
        quitTxt: '取消',
        onOk: () => {
          refreshPrice();
        },
      });
    },
    // 仅保存本单
    alterOrder(extraParams) {
      if (!this._checkTagLengs()) {
        return;
      }
      // 开启订单商品标签必填
      if (this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        for (const good of this.newOrderCommodityList.filter(
          (item) => item.commodity_id,
        )) {
          if (!good.order_commodity_tag) {
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`);
            return;
          }
        }
      }
      let saveData = {
        reverse_order_commodities: [],
        reverse_order: {
          express_no: this.orderInfo.express_no,
          delivery_date: this.orderInfo.delivery_date,
          remark: '',
          tag_ids: '',
          attachment_link: '',
          delivery_time: this.orderInfo.delivery_time,
          is_receipt: this.orderInfo.is_receipt,
          customize_fields: this.orderCustomizeField,
        },
        isWarning: false,
        is_brush_price: this.is_brush_price,
        ...extraParams,
      };
      this.newOrderCommodityList.forEach((item, index) => {
        if (item.commodity_id) {
          saveData.reverse_order_commodities.push(
            this.formatApprovalData(item, index),
          );
        }
      });
      saveData.reverse_order.remark = this.remarks;
      saveData.reverse_order.tag_ids = this.orderInfo.tag.join(',');
      saveData.reverse_order.attachment_link = this.attachmentFiles
        .map((item) => item.url)
        .join(',');
      saveData.del_order_commodity_ids = [];
      let newIds = [];
      let oldIds = [];
      saveData.reverse_order_commodities.forEach((item) => {
        newIds.push(item.order_commodity_id);
        this.oldOrderCommodityList.forEach((item2) => {
          if (
            item2.orderCommodity_id === item.order_commodity_id &&
            item.actual_amount !== item2.actual_amount
          ) {
            saveData.isWarning = true;
          }
        });
      });
      if (
        saveData.reverse_order_commodities.length !==
        this.oldOrderCommodityList.length
      ) {
        saveData.isWarning = true;
      }
      this.oldOrderCommodityList.forEach((item) => {
        oldIds.push(item.orderCommodity_id);
      });
      oldIds.forEach((item) => {
        if (newIds.indexOf(item) == -1) {
          saveData.del_order_commodity_ids.push(item);
        }
      });
      this.saveData = saveData;
      if (saveData.isWarning && !this.isWaringOrderAlter) {
        this.$refs.smodal.open();
      } else {
        this.$refs.smodal.ok();
      }
    },
    alterOrderAndSync() {
      let sync_protocol = 1;
      this.saveButtonDisabled = true;
      common
        .checkValidUserContractPriceOrder(
          this.orderInfo.user_id,
          this.orderInfo.delivery_date,
        )
        .then((res) => {
          let { data, status, message } = res;
          if (status) {
            if (!data || (Array.isArray(data) && data.length < 1)) {
              this.$Modal.confirm({
                title: '提示',
                render: (h) => {
                  return h('div', [
                    h(
                      'p',
                      {
                        style: {
                          marginTop: '15px',
                        },
                      },
                      '客户没有已生效的协议单，确定要继续操作吗？',
                    ),
                    h(
                      'p',
                      {
                        style: {
                          marginTop: '15px',
                          color: '#777',
                        },
                      },
                      '注意：因为系统中不存在协议单该客户已生效的协议单，继续操作，订单中的商品不会同步至协议单中！',
                    ),
                  ]);
                },
                onOk: () => {
                  let sync_protocol = 0;

                  this.alterOrder({ sync_protocol: sync_protocol });
                },
              });
            } else {
              const notAgreementPriceGoods = this.newOrderCommodityList.filter(
                (item) => !Goods.isProtocolGoods(item),
              );
              if (
                Array.isArray(data) &&
                data.length > 1 &&
                notAgreementPriceGoods.length > 0 &&
                this.isOpenSyncProtocolIsAddCommodity
              ) {
                let repeatGoods = []; // 非协议价商品 - 重复商品组数
                let sameRepeatGoods = []; // 下单单价、折扣率（订）和协议市场价（订）一样的商品数组
                for (
                  let index = 0;
                  index < notAgreementPriceGoods.length - 1;
                  index++
                ) {
                  const item = notAgreementPriceGoods[index];
                  for (
                    let index2 = index + 1;
                    index2 < notAgreementPriceGoods.length;
                    index2++
                  ) {
                    const item2 = notAgreementPriceGoods[index2];
                    if (
                      item.commodity_id === item2.commodity_id &&
                      repeatGoods.indexOf(item) === -1
                    ) {
                      repeatGoods.push(item);
                      repeatGoods.push(item2);
                      if (
                        +item.unit_price === +item2.unit_price &&
                        +item.org_price === +item2.org_price &&
                        +item.discount === +item2.discount &&
                        sameRepeatGoods.indexOf(item2) === -1
                      ) {
                        sameRepeatGoods.push(item2);
                      }
                    }
                  }
                }
                if (
                  (repeatGoods.length > 0 && sameRepeatGoods.length > 0) ||
                  repeatGoods.length < notAgreementPriceGoods.length
                ) {
                  this.agreementPriceSelectModal.show = true;
                  this.agreementPriceSelectModal.data = data;
                } else {
                  this.alterOrder({ sync_protocol: sync_protocol });
                }
              } else {
                this.alterOrder({ sync_protocol: sync_protocol });
              }
            }
          } else {
            this.$smessage({ type: 'error', text: message });
            this.alterOrder({ sync_protocol: sync_protocol });
          }
        });
    },
    toScroll(curRow) {
      this.resetPreElement();
      const scrollBox = this.$refs.orderGoodsTable.$el.querySelector(
        '.sdp-table__content',
      );
      const curIndex = this.newOrderCommodityList.findIndex(
        (item) => item.id === curRow.id,
      );
      this.currentIndex = curIndex;
      const allElement = scrollBox.querySelectorAll('.sdp-table__tr');
      allElement.forEach((item) => {
        item.style.backgroundColor = '#fff';
      });
      const curElement = this.$refs.orderGoodsTable.$el.querySelector(
        `[data-key="body_tr_${curIndex}"]`,
      );
      const childrenElement = curElement.querySelectorAll('.sdp-table__td');
      childrenElement.forEach((item) => {
        item.style.backgroundColor = '#e6f7ec';
      });
      scrollBox.scrollTo(0, curElement.offsetTop);
    },
    resetPreElement() {
      const curElements =
        this.$refs.orderGoodsTable.$el.querySelectorAll('.sdp-table__tr');
      curElements.forEach((item) => {
        const childrenElements = item.querySelectorAll('.sdp-table__td');
        childrenElements.forEach((childrenElement) => {
          childrenElement.style.backgroundColor = '#fff';
        });
      });
    },
  },
  filters: {
    round: function (value) {
      if (!value) return '';
      var res = Math.floor(value * 100) / 100;
      // eslint-disable-next-line
      return res.toString().match('[\.]', 'g') ? res : res + '.00';
    },
  },
};
</script>

<style lang="less">
.newOrder-remarks {
  margin-top: 10px;
  width: 100%;
  padding: 0 10px;
  border: none;
  .ivu-form-item {
    width: 100%;
  }
}
.approval {
  .protocol {
    @pw: 20px;
    font-weight: bolder;
    color: red;
    border: 1px solid;
    border-radius: 50%;
    display: inline-block;
    width: @pw;
    height: @pw;
    line-height: @pw;
    font-size: 12px;
    margin-left: 5px;
    text-align: center;
  }
}
</style>
<style lang="less" scoped>
/deep/ .editable-table {
  .sdp-table__cell > div {
    display: block !important;
  }
  .new-logo {
    margin-right: 2px;
    padding: 0 5px;
    color: #fff;
    background-color: #3399ff;
    border-radius: 2px;
  }

  .InternalNote {
    color: #3399ff;
  }

  .order-amount-input.amount_warning input {
    border-color: red;
  }
  .emphasis-tip input {
    color: #ff6e00;
    border-color: currentColor;
  }
}
/deep/ .goods-form .ivu-form-item {
  margin-bottom: 14px !important;
}
/deep/ .goods-search-row {
  margin-bottom: 18px;
  .ivu-form-item {
    margin-bottom: 0 !important;
  }
}
.special {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  span {
    display: inline-block;
    box-sizing: border-box;
    padding-right: 12px;
    width: 120px;
    text-align: right;
  }
}

/deep/.commodity-select {
  width: 100%;
  .ivu-dropdown {
    width: 100%;
  }
}
.commodity-table {
  position: relative;
  .custom-column {
    position: absolute;
    top: -44px;
    right: 0;
  }
  /deep/ .column__unit-price .ivu-table-cell {
    padding-right: 8px;
  }
}
.order-info {
  margin-top: 15px;
  /deep/ .ivu-col {
    margin-bottom: 15px;
  }
}

.order-alter {
  background-color: #fff;
}

.order-alter-nav {
  /* display: table; */
  border-bottom: 1px solid rgba(228, 228, 228, 1);
  text-align: left;
  background-color: #fff;
}

.order-alter-nav ul li {
  display: inline-block;
}
.order-alter-nav button {
  float: right;
}

.nav-items {
  display: inline-block;
  margin-right: 25px;
  /* width: 125px; */
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}

.nav-items:hover {
  cursor: pointer;
}
.order-alter-nav .active {
  color: #03ac54;
  border-bottom: 2px solid #03ac54;
}

.order-alter-info {
  padding: 20px 20px 10px 20px;
}
.order-alter-info .ivu-steps {
  margin-left: 60px;
  width: 900px;
}
.order-alter-info .ivu-steps .ivu-steps-tail > i {
  height: 2px;
}
.order-alter-info .ivu-steps-status-process .ivu-steps-title {
  color: #03ac54;
}
.order-alter-info .ivu-steps .ivu-steps-title {
  margin-top: 10px;
  margin-left: -5px;
  display: block;
}

.table-button {
  color: #03ac54;
  background-color: #fff !important;
  border: none;
}

.ivu-table-row-hover .table-button {
  background-color: transparent !important;
}

.table-button:hover {
  background-color: transparent !important;
  color: var(--primary-color);
}

.ivu-page {
  margin: 10px;
  text-align: right;
}

.order-alter-title {
  width: 100%;
  height: 50px;
  padding: 10px 0 15px 0;
  font-size: 16px;
  text-align: left;
  border-bottom: none;
}
.order-alter-title span:hover {
  cursor: pointer;
}
.order-alter-title-strong {
  margin-right: 5px;
  color: #03ac54;
}
.order-alter-title button {
  float: right;
  margin-right: 10px;
  color: #fff;
  background-color: #03ac54;
}
.order-alter-title button:hover {
  color: #fff;
  border-color: #03ac54 !important;
  background-color: #03ac54;
}

.order-alter-feature {
  text-align: left;
  position: fixed;
  z-index: 99;
  bottom: 0;
  padding: 15px;
  border-top: 1px solid #eee;
  background-color: #fff;
  width: 100%;
  transform: translateX(-15px);
}

.order-alter-amount {
  padding: 10px;
  text-align: right;
}
.order-alter-amount span:last-child {
  font-size: 20px;
  color: #f33333;
}
.order-alter__price {
  margin-right: 15px;
  font-size: 14px;
  color: #495060;
}
.slide-fade-enter-active {
  transition: all 0.2s ease;
}
.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}
/* 切换订单详情内容的渲染 */
.slide-enter-active {
  transition: all 0.2s ease;
}

.slide-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-enter,
.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
.commodity-table {
  min-height: 89px;
}

.ivu-table-wrapper img {
  max-width: 50px;
  height: auto;
}
.ivu-col-span-4,
.ivu-col-span-8 {
  text-align: left;
}
.fl {
  float: left;
}
.order-amount-input.amount_warning input {
  border-color: red;
}
.approval {
  .ivu-input-number {
    width: 100%;
  }

  .sdp-modal {
    .ivu-modal-footer {
      display: none;
    }
  }
  .protocol {
    @pw: 25px;
    font-weight: bolder;
    color: red;
    border: 1px solid;
    border-radius: 50%;
    display: inline-block;
    width: @pw;
    height: @pw;
    line-height: @pw;
    text-align: center;
  }
}
</style>
