<!--
 * @Author: <PERSON>
 * @Date: 2021-12-08 18:11:44
 * @LastEditors: hgj
 * @LastEditTime: 2023-09-05 14:23:47
 * @Description: 订单编辑 -- 新版
-->
<template>
  <div class="order-edit">
    <DetailPage pageType="edit" title="编辑订单" customSaveBtn>
      <Form ref="form" inline label-colon :label-width="98" :disabled="false">
        <s-block title="基础信息" class="base-info">
          <FormItem label="客户名称">{{ orderDetail.user_name }}</FormItem>
          <FormItem label="客户业态" v-if="sysConfig.tc_platform == 1">{{ orderDetail.user_business_type_desc }}</FormItem>
          <FormItem
            label="发货日期"
            ref="error.delivery_date"
            :error="error.delivery_date"
          >
            <Date-picker
              v-model="date"
              type="date"
              placeholder="选择日期"
              :editable="false"
              @on-open-change="handleOpenDeliveryDate"
              @on-change="handleChangeDeliveryDate"
              style="width: 232px"
            ></Date-picker>
          </FormItem>
          <FormItem label="送货时间段">
            <Select v-model="selectedDeliveryTime" style="width: 232px">
              <Option
                v-for="item in deliveryTimeList"
                :value="item.id"
                :key="item.id"
                >{{ item.timeDu }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="配送方式">
            <RadioGroup v-model="orderDetail.delivery_method">
              <Radio :label="1" disabled>配送</Radio>
              <Radio :label="2" disabled>自提</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="子机构" v-if="isOpenSubsidiary">
            <SuborganizationCascader
              v-model="formData.subsidiary_id"
              :userId="user.id"
            ></SuborganizationCascader>
          </FormItem>
          <FormItem label="餐次" v-if="isNewGoodsPackageRecipe">
            <MealtimeSelect v-model="formData.meal_type"></MealtimeSelect>
          </FormItem>
          <FormItem v-show="isSplitOrderByProvider && !(userInfo.order_audit_inquiry_split == 1 && orderDetail.mode == 100)" label="所属供应商">
            <Select
              style="width: 232px"
              :disabled="isAccountOrder || isFundAllocationEnabled"
              filterable
              clearable
              v-model="formData.split_provider_id"
              placeholder="请选择"
            >
              <Option
                :value="info.id"
                v-for="info in initData.providers"
                :key="info.id"
                >{{ info.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="物流单号" v-if="isOpenExpress100">
            <Input
              style="width: 232px"
              maxlength="32"
              v-model="formData.express_no"
            />
          </FormItem>
          <FormItem label="子账号">{{formData.sub_user_name || '-'}}</FormItem>
          <FormItem
            label="订单标签"
            v-if="orderTagList.length > 0"
            style="width: 100%"
          >
            <orderTag 
              v-model="selectedOrderTag"
              @on-change="checkTag"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return (only_tag === '1' &&
                  selectedOrderTag.length > 0 &&
                  !selectedOrderTag.includes(item.id))
              }
              ">
            </orderTag>
          </FormItem>
          <FormItem label="外部系统编号" v-if="isOpenTraceabilityPlatform4 || sysConfig.open_openapi == 1">
            {{orderDetail.third_party_order_no || '-'}}
          </FormItem>
          <FormItem
            style="margin-top: -4px !important"
            :label="item.name"
            :key="item.key"
            v-for="item in orderCustomizeField"
          >
            <Input style="width: 232px" maxlength="256" v-model="item.value" />
          </FormItem>
          <div v-if="orderDetail.special_remark">
            <FormItem>
              <template #label>
                <span style="color: #f33333">特殊备注:</span>
              </template>
              <div style="white-space: pre">
                {{ orderDetail.special_remark }}
              </div>
            </FormItem>
          </div>
        </s-block>
        <base-block
          title="收货信息"
          class="base-info receive_goods"
          style="margin-top: 22px"
        >
          <template v-if="orderDetail.delivery_method == 1">
            <FormItem label="收货人"><SText :text="user.name" /></FormItem>
            <FormItem label="联系电话">{{ user.tel }}</FormItem>
            <FormItem label="地址"><SText :text="user.address" /></FormItem>
          </template>
          <template v-else>
            <FormItem label="自提点"
              ><SText :text="orderDetail.self_pickup_point_name"
            /></FormItem>
            <FormItem label="联系电话">{{ user.tel }}</FormItem>
            <FormItem label="地址"><SText :text="user.address" /></FormItem>
          </template>
        </base-block>
        <base-block title="商品清单" style="margin-top: 24px">
          <s-fullscreen @change="updateTableHeight">
            <Row :gutter="10" type="flex" align="middle" slot="action-left">
              <Col>
                <Button
                  v-if="!orderDetail.consortium_forbid_change_item_num"
                  styleType="btnStyleForAdd"
                  @click="_batchAddGoods"
                  >批量添加</Button
                >
              </Col>
              <Col
                v-if="
                  isEnableGoodsPackage &&
                  commodityPackageMode &&
                  !is_open_order_add_same_commodity
                "
              >
                <Button
                  @click="goodsPackageModal.show = true"
                  styleType="btnStyleForAdd"
                  >添加套餐</Button
                >
              </Col>
              <Col>
                <SearchInputCommodity
                  :label="false"
                  :originData="newOrderList"
                  @toScroll="toScroll"
                />
              </Col>
            </Row>
            <EditableTable
              :loading="tableLoading"
              rowKey="id"
              :max-height="tableHeight"
              ref="orderGoodsTable"
              stickyTop="102"
              :row-class-name="rowClassName"
              :columns="goodsColumns"
              :data="newOrderList"
              @on-draggable-data="_onDraggableData"
              @on-row-click="handleRowClick"
              @on-sort-change="handleSortChange"
              @on-cols-change="handleColsChange"
              :supportEnterAdd="!orderDetail.consortium_forbid_change_item_num"
              :virtualScroll="true"
              :virtualScrollBuff="{ top: 2100, bottom: 2100 }"
            >
              <template #after-table-right>
                <div class="newOrder-amount" v-show="orderTotalAmount">
                  <span class="num-label">下单数量：</span>
                  <span class="mr16 order-total-num num-font text--primary">{{
                    orderTotalNum
                  }}</span>
                  <span v-show="totalReferenceProfitShow" class="num-label"
                    >参考毛利总额：</span
                  >
                  <span
                    v-show="totalReferenceProfitShow"
                    class="c6 mr16 order-total-num num-font text--primary"
                    >{{ totalReferenceProfit }}</span
                  >
                  <span class="num-label">合计金额：</span>
                  <span class="newOrder-amount-total num-font"
                    >¥{{ orderTotalAmount }}</span
                  >
                </div>
              </template>
            </EditableTable>
            <goods-list-modal
              v-model="showGoodsListModal"
              :modalProps="{ transfer: false }"
              :params="{
                is_online: createOrderShowOfflineGoods ? '' : 'Y',
                delivery_date,
                order_id: this.orderId,
                query_setting: JSON.stringify(excludeName()),
                is_sell_independent_from_user_id:
                  superCreateOrderUnsellCommodity,
              }"
              :uid="user.id"
              :splitProviderId="splitProviderId"
              :selectedGoods="
                is_open_order_add_same_commodity ? [] : newOrderList
              "
              @on-add="handlerAdd"
            >
              <div></div>
            </goods-list-modal>
            <GoodsPackageModal
              ref="goodsPackageModal"
              :modalProps="{ transfer: false }"
              :show-mode-filter="true"
              :show="goodsPackageModal.show"
              :columns="goodsPackageModal.columns"
              :defaultValue="true"
              @on-cancel="$_closeGoodsPackageModal"
              @on-ok="$_onSelectGoodsPackage"
            />
          </s-fullscreen>
        </base-block>

        <base-block title="其他信息">
          <div class="newOrder-remarks">
            <FormItem label="订单备注">
              <Input
                style="width: 418px"
                v-model="remarks"
                type="textarea"
                :maxlength="512"
                show-word-limit
                placeholder="输入订单备注"
              ></Input>
            </FormItem>

            <FormItem label="附件" style="margin-top: 10px">
              <AttachmentUpload v-model="attachmentFiles" />
            </FormItem>
            <FormItem
              label="客户签名图片"
              v-if="orderDetail.signature_pic"
              style="width: 100%"
            >
              <ThumbnailList :images="orderDetail.signature_pic ? [orderDetail.signature_pic] : []" />
            </FormItem>
            <FormItem
              label="货物送达图片"
              v-if="orderDetail.delivery_pic && orderDetail.delivery_pic.length"
              style="width: 100%"
            >
              <ThumbnailList :images="orderDetail.delivery_pic" />
            </FormItem>
          </div>
        </base-block>
      </Form>

      <template #button-after>
        <Button type="success" :disabled="submitting" @click="saveOrderTags">{{
          submitting ? '保存中...' : '保存修改'
        }}</Button>
      </template>
    </DetailPage>
    <ActionRemindModal ref="actionRemind" @on-ok="handleOk" @on-cancel="handleCancelActionRemindModal"></ActionRemindModal>
  </div>
</template>
<script>
import DetailPage from '@/components/detail-page/index.js';
import SuborganizationCascader from '@/components/common/SuborganizationCascader.vue';
import MealtimeSelect from '@/components/common/mealTimeSelect.vue';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue';
import EditableTable from '@/components/editable-table/index.js';
import CommoditySelect from '@/components/common/CommoditySelectTable';
import common from '@api/order.js';
import userApi from '@api/user.js'
import GoodsPackageModal from '@components/packageGoods/PackageGoodsModal';
import GoodsListModal from '@components/order/goodsListModal';
import NumberInput from '../../components/basic/NumberInput.vue';
import StepPricingPoptip from './components/StepPricingPoptip.vue';
import Icon from 'view-design/src/components/icon';
import { get } from '@api/request.js';
import Goods from '@api/goods.js';
import LayoutMixin from '@/mixins/layout';
import '@assets/scss/mixin.scss';
import SIcon from '@components/icon';
import ConfigMixin from '@/mixins/config';
import HeaderFilter from './components/header-filter.vue';
import { uniqueId, uniqBy } from 'lodash-es';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import authority from '@/util/authority.js';
import { getEditTableHeight } from '@/util/common';
import SearchInputCommodity from './components/SearchInputCommodity/index.vue';
import ThumbnailList from '@components/thumbnail-list';
const { hasAuthority } = authority;
import StorageUtil from '@util/storage.js';
import SFullscreen from '@/components/s-fullscreen';
import Button from '@components/button';
import SBlock from '@/components/s-block';
import SText from '@/components/s-text';
import FundAllocationMixin from '@/mixins/fund-allocation';
import ActionRemindModal from './components/ActionRemindModal'
import modifiedTips from '@/mixins/modified-tips';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'
import SdpTableStaticFormWrap from '@/components/standard/sdp-table-static-form-wrap';
import SdpTableStaticPoptip from '@/components/standard/sdp-table-static-poptip';

const filterList = [
  {
    name: '最近一次进价',
    value: '1',
    tip: '每次入库单审核过后都会更新，但是可以进行手动更改',
  },
  {
    name: '最近一次入库价',
    value: '2',
    tip: '每次入库单审核之后才可以更新',
  },
  {
    name: '库存均价',
    value: '3',
    tip: '现有库存中的库存均价',
  },
  {
    name: '最近一次采购价',
    value: '4',
    tip: '采购单收货之后更新的采购价',
  },
];
const priceTypeMap = {
  1: 'in_price',
  2: 'store_in_price',
  3: 'average_price',
  4: 'last_receipt_price',
};

const defaultImg = require('@/assets/empty.png');

export default {
  name: 'OrderEdit',
  mixins: [LayoutMixin, ConfigMixin, FundAllocationMixin, modifiedTips],
  components: {
    SdpTableStaticFormWrap,
    SdpTableStaticPoptip,
    SText,
    SBlock,
    Button,
    SFullscreen,
    DetailPage,
    SuborganizationCascader,
    MealtimeSelect,
    EditableTable,
    GoodsListModal,
    GoodsPackageModal,
    AttachmentUpload,
    Icon,
    SearchInputCommodity,
    ThumbnailList,
    ActionRemindModal,
    orderTag
  },
  data() {
    return {
      totalReferenceProfitShow: false,
      tableHeight: getEditTableHeight(),
      initData: {
        providers: [],
      },
      userInfo: {},
      orderId: this.$route.query.id,
      tableLoading: false,
      orderCustomizeField: [], // 订单自定义字段
      goodsCustomizeField: [], // 订单明细自定义字段
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      isOpenScanLoading: '', // 是否开启了分拣装框功能
      goodsPackageModal: {
        show: false,
        columns: [
          {
            width: 60,
            type: 'selection',
          },
          {
            title: '图片',
            render: (h, params) => {
              let { row } = params;
              let key = 'pic_url';
              return h('img', {
                style: {
                  width: '40px',
                },
                attrs: {
                  src: row[key],
                },
              });
            },
          },
          {
            title: '套餐名称',
            key: 'name',
          },
          {
            title: '单位',
            key: 'unit',
          },
          {
            title: '描述',
            key: 'summary',
          },
          {
            title: '下单数量',
            render: (h, params) => {
              const key = 'order_amount';
              return h('NumberInput', {
                props: {
                  value: params.row.order_amount,
                },
                on: {
                  'on-change': (value) => {
                    params.row[key] = value;
                  },
                  'on-blur': () => {
                    const list = this.$refs.goodsPackageModal.getList();
                    list.forEach((item) => {
                      if (item.id === params.row.id) {
                        item.order_amount = params.row.order_amount;
                      }
                    });
                    this.$refs.goodsPackageModal.setList(list);
                  },
                  'on-click': (e) => {
                    window.event
                      ? (window.event.cancelBubble = true)
                      : e.stopPropagation();
                  },
                },
              });
            },
          },
        ],
      },
      error: {
        delivery_date: '',
      },
      delivery_date: '',
      submitting: false,
      date: '',
      originalDate: '',
      deliveryTimeList: [],
      delivery_method: '',
      selectedDeliveryTime: '',
      originalOrderCommodity: [],

      user: {
        id: '',
        uid: '',
        name: '',
        detail: '',
      },
      formData: {
        subsidiary_id: '',
        meal_type: '',
      },
      orderDetail: '',
      storeGoodsList: [],
      newOrderList: [],
      oldOrderList: [], // 原有的订单商品 √
      delOrderList: [], // 删除的订单商品 √
      remarks: '',
      activeRowIndex: 0,
      goodsColumns: [],
      showGoodsListModal: false,
      selectedOrderTag: [], // 存储id 1,2,3
      selectedOrderTagList: [], // 存储已经选中的标签 [{id:1, name:'dd'}]
      orderTagList: [],
      syncContractPrice: false,
      userHasContractPriceOrder: true,
      originCols: [
        {
          type: 'drag',
          width: 28,
          minWidth: 28,
          fixed: 'left',
          align: 'center',
          style: {
            paddingRight: 0,
            paddingLeft: '6px',
          },
          render: (h, parmas) => {
            return h('Icon', {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer',
                color: '#909090',
              },
            });
          },
        },
        {
          type: 'titleCfg',
          titleType: 'order_edit',
          minWidth: 32,
          width: 32,
          style: {
            paddingRight: 0,
            paddingLeft: '12px',
          },
          align: 'center',
          fixed: 'left',
          key: 'title',
          render: (h, params) => {
            const { row, index } = params;
            const operation = h('div', [
              h(
                SdpTableStaticPoptip,
                {
                  props: {
                    poptipProps: {
                      title: '确认删除此商品？',
                      confirm: true,
                      transfer: true,
                      placement: 'right',
                      disabled: this.newOrderList.length === 1,
                    },
                  },
                  on: {
                    'on-ok': () => {
                      this._deleteGoods(row, index);
                    },
                  },
                },
                [
                  h(SIcon, {
                    class:
                      'icon-record-editor icon-record-editor--delete custom-teleport',
                    props: {
                      icon: 'jian',
                      size: 16,
                    },
                  }),
                ],
              ),
              h(SIcon, {
                props: {
                  icon: 'jia1',
                  size: 16,
                },
                class: 'icon-record-editor icon-record-editor--insert mt7',
                on: {
                  click: (event) => {
                    event.stopPropagation();
                    this._addGoods(index);
                  },
                },
              }),
            ]);
            return !this.orderDetail.consortium_forbid_change_item_num ? operation : null;
          },
        },
        {
          title: '序号',
          align: 'center',
          width: 70,
          key: 'index',
          style: {
            paddingRight: 0,
            paddingLeft: '18px',
          },
          fixed: 'left',
          render: (h, params) => {
            const template = [];
            // 预售商品
            if (Number(params.row.date_type) > 0) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 20,
                  },
                  style: {
                    color: 'var(--primary-color)',
                    marginRight: '5px',
                  },
                }),
              );
            }
            template.push(h('span', params.index + 1));
            return template;
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'left',
          minWidth: 70,
          width: 70,
          fixed: 'left',
          style: {
            paddingRight: 0,
            paddingLeft: '18px',
          },
          render: (h, params) => {
            const { row } = params;
            return h(
              'div',
              { class: 'demo-upload-list', style: { border: 'none' } },
              [
                <img style="width: 36px;" src={row.logo || defaultImg} />,
                h(
                  'div',
                  {
                    class: 'demo-upload-list-cover',
                  },
                  [
                    h(
                      'Icon',
                      {
                        props: {
                          type: 'ios-eye-outline',
                        },
                        on: {
                          click: () => this.previewImage(row.logo + '!400x400'),
                        },
                      },
                      [],
                    ),
                  ],
                ),
              ],
            );
          },
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          sortable: true,
          width: 266,
          fixed: 'left',
          resizable: true,
          style: {
            paddingLeft: '28px',
          },
          cellContentStyle: {
            width: '100%'
          },
          render: (h, params) => {
            const { row, index } = params;
            let activityIcon = Goods.getActivityIcon(row);
            let iconStyle = {
              color: '#cc4916',
              marginRight: '6px',
              border: '1px solid #cc4916',
              borderRadius: '3px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
            };
            const inputProps = {
              type: 'textarea',
              rows: 1,
              autosize: { minRows: 1, maxRows: 2.2 }
            }
            return (
              <div style="display: flex; align-items: center;">
                {(row.new && <span class="new-logo">新</span>) ||
                  (row.activity_type_desc && (
                    <img
                      style="height: 18px; padding-right: 10px"
                      src={activityIcon}
                    >
                      {row.activity_type_desc}
                    </img>
                  )) ||
                  (row.unit !== row.unit_sell && (
                    <div style={iconStyle}>多规格</div>
                  ))}
                  <div class="commodity-select">
                    <SdpTableStaticFormWrap
                      displayValue={{
                        value: row.name,
                        placeholder: '商品名/编码/别名/关键字',
                        disabled: this.orderDetail.consortium_forbid_change_item_num,
                        ...inputProps
                      }}
                      scopedSlots={{
                        default: ({
                          handleUpdateActiveStatus,
                          handleDeactivate,
                        }) => (
                          <CommoditySelect
                            commodityName={row.name}
                            disabled={this.orderDetail.consortium_forbid_change_item_num}
                            params={{
                              user_id: this.user.id,
                              delivery_date: this.delivery_date,
                              is_online: this.createOrderShowOfflineGoods ? '' : 'Y',
                              provider_id: this.splitProviderId,
                              order_id: this.orderId,
                              pageSize: 30,
                              query_setting: this.excludeName(),
                            }}
                            dataProvider={common.getCommodity}
                            selectedData={this.newOrderList}
                            inputProps={inputProps}
                            commodityIdKey="commodity_id"
                            commodityNameKey="commodity_name"
                            onOn-show-dropdown={(show, createGoodsModal) => {
                              if (!createGoodsModal) {
                                const flag = show ? 2 : 1;
                                handleUpdateActiveStatus(flag);
                                if (flag === 1) {
                                  handleDeactivate();
                                }
                              }
                            }}
                            onOn-change={(cid, com) => {
                              row.name = com.origin_commodity_name;
                              this._setCommodity(cid, com, row, index)
                            }}
                            onCreateGood={(newGood) => {
                              row.name = newGood.origin_commodity_name;
                              this._setCommodity(
                                newGood.commodity_id,
                                newGood,
                                row,
                                index,
                              )
                            }}
                            onOn-enter={(event) => this._addGoods(index)}
                            selectType="table"
                            slot-type="order-is_online"
                            isOpenCreated={true}
                          ></CommoditySelect>
                        ),
                      }}
                    ></SdpTableStaticFormWrap>
                  </div>
              </div>
            );
          },
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'center',
          minWidth: 120,
        },
        {
          title: '现有库存',
          key: 'stock',
          align: 'left',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              {
                class: 'max-text-2',
              },
              row.stock ? row.stock + ' ' + row.unit_sell : '--',
            );
          },
        },
        {
          title: '单位',
          key: 'unit',
          align: 'center',
        },
        {
          title: '下单数量',
          key: 'order_amount',
          align: 'left',
          sortable: true,
          width: 140,
          style: {
            paddingRight: 0,
          },
          render: (h, params) => {
            const { row, index } = params;
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0;
            this.newOrderList.forEach((item) => {
              if (row.commodity_id === item.commodity_id) {
                otherAmount += Number(item.order_amount);
              }
            });

            let template = h('div', {}, [
              h(
                'div',
                {
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                  },
                },
                [
                  h(NumberInput, {
                    class: [
                      'order-amount-input',
                      row.amount_warning && 'amount_warning',
                      +row.order_amount === 0 && 'emphasis-tip',
                    ],
                    props: {
                      disabled: Goods.isCreditGoods(row),
                      value: row.order_amount,
                      min: 0,
                      max: 999999999.99,
                      precision: 2,
                    },
                    style: {
                      width: '80px',
                      flexShrink: 0,
                    },
                    on: {
                      'on-change': (val) => {
                        this.storeGoodsList[index]['order_amount'] =
                          row.order_amount = val;
                        this._updateStepPricing(row, index); // 更新阶梯定价价格
                        this.handleChangeNum(row, index);
                      },
                      'on-blur': () => {
                        this.newOrderList[index]['order_amount'] =
                          row.order_amount;
                        this.newOrderList[index]['unit_price'] = row.unit_price;
                      },
                      'on-focus': () => {
                        this.storeGoodsList[index]['amount_warning'] = row[
                          'amount_warning'
                          ] = false;
                        this.newOrderList[index]['amount_warning'] = false;
                      },
                      'on-enter': (event) => {
                        this.newOrderList[index]['order_amount'] =
                          row.order_amount;
                        this._addGoods(index);
                      },
                    },
                  }),
                  row._isNewGoods && this.isStepPricingGoods(row) ? (
                    <StepPricingPoptip goods={row}></StepPricingPoptip>
                  ) : null,
                ],
              ),
              h(
                'div',
                {
                  style: { color: '#ff6e00', fontSize: '12px' },
                  class: { dn: +row.order_amount !== 0 },
                },
                '请注意特殊下单数量！',
              ),
              h(
                'div',
                {
                  class: {
                    dn:
                      !row.is_sell_stock_alert ||
                      otherAmount <= Number(row.sell_stock),
                  },
                  style: {
                    color: 'red',
                  },
                },
                '库存不足',
              ),
            ]);
            return template;
          },
        },
        {
          title: '下单单价',
          key: 'unit_price',
          sortable: true,
          align: 'left',
          width: 160,
          render: (h, params) => {
            const { row, index } = params;
            const key = 'unit_price';
            let protocol = h(SIcon, {
              class: '',
              props: {
                icon: 'xie',
                size: 16,
              },
            });
            const { desc } = row.price_type || {};
            if (desc === '~协') {
              protocol = h(SIcon, {
                props: { icon: 'xieyijiabeigai', size: 16 },
                class: 'mr5',
                style: {
                  color: '#ff6600',
                },
              });
            }
            const tip = h(
              'p',
              {
                style: {
                  color: 'red',
                  fontSize: '12px',
                  // display:'inline-block'
                },
              },
              '低于最近一次进货价',
            );
            const showWaring = +row.in_price <= 0 && +row[key] === 0;
            const warningTip = h(
              'p',
              {
                style: { color: '#ff6e00', fontSize: '12px' },
              },
              '请注意特殊下单单价！',
            );
            let template = [];
            if (Goods.isDiscountGoods(row)) {
              const discountIcon = h(SIcon, {
                class: 'mr5',
                props: {
                  icon: 'zhe',
                  size: 16,
                },
                style: 'color: #ff6600;',
              });
              template.push(discountIcon);
            }
            // 下单时改价的权限
            if (hasAuthority('A002001012')) {
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'mr5';
                template.push(protocol);
              }
              template.push(
                h(NumberInput, {
                  style: {
                    width: '95px',
                  },
                  class: {
                    'emphasis-tip': showWaring,
                  },
                  props: {
                    value: row[key],
                    min: 0,
                    max: 999999999.99,
                    precision: 2,
                  },
                  on: {
                    'on-change': (value) => {
                      this.newOrderList[index][key] = row[key] = value;
                      this.storeGoodsList[index][key] = row[key] = value;
                      this.handleChangePrice(row);
                      this.updateStoreListPrice(params);
                      this.handleChangeNum(row, index);
                    },
                    'on-enter': () => {
                      this.newOrderList[index][key] = row[key];
                      this._addGoods(index);
                    },
                  },
                }),
              );
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              if (showWaring) {
                template.push(warningTip);
              }
              return template;
            } else {
              template = [h('span', row[key] || '--')];
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'ml5';
                template.push(protocol);
              }
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              return h(
                'div',
                {
                  style: {
                    padding: '5px 0',
                  },
                },
                template,
              );
            }
          },
        },
        {
          title: '下单小计',
          key: 'sub_total_price',
          sortable: true,
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            const key = 'sub_total_price';
            return h(
              'span',
              {
                class: {
                  'total-amount': true,
                },
              },
              row.original_total_price,
              // (+row.order_amount).mul(+row.unit_price).toFixed(2),
            );
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          align: 'center',
          minWidth: 90,
          render: (h, params) => {
            const { row, index } = params;
            return h('i-input', {
              props: {
                value: row.remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': (event) => {
                  this.newOrderList[index]['remark'] = row['remark'];
                  this._addGoods(index, event);
                },
              },
              nativeOn: {
                change: ($event) => {
                  let value = $event.target.value;
                  row.remark = value;
                  this.storeGoodsList[index].remark = value;
                  this.syncStoreList();
                },
              },
            });
          },
        },
        {
          title: '条形码',
          key: 'barcode',
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 140,
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
        },
        {
          title: '联营供应商',
          key: 'provider_pool_name',
        },
        {
          title: '采购单号',
          key: 'pur_no',
          render: (h, params) => {
            const { row } = params;
            if (!row.pur_no) return '--';
            return h('div', [
              h(
                'a',
                {
                  class: 'tableLink',
                  attrs: {
                    // 表明同一个标签同一个域会共享会话
                    rel: 'opener',
                    target: '_blank',
                    href: `#/purchase/detail?keep_scroll=1&id=${row.purchase_id}&pur_no=${row.pur_no}`,
                  },
                },
                row.pur_no,
              ),
            ]);
          },
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'right',
        },
        {
          title: '实收状态',
          key: 'un_confirm_receive',
          minWidth: 120,
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            let selectItem = [
              {
                label: '待实收',
                value: 1,
              },
              {
                label: '已实收',
                value: 0,
              },
            ];
            return h(
              'Select',
              {
                props: {
                  value: row.un_confirm_receive,
                  transfer: true,
                  disabled: +row.is_sorting === 1,
                },
                on: {
                  'on-change': (val) => {
                    this.storeGoodsList[index]['un_confirm_receive'] =
                      row.un_confirm_receive = val;
                  },
                },
              },
              selectItem.map((item) => {
                return h('Option', {
                  props: {
                    value: item.value,
                    label: item.label,
                  },
                });
              }),
            );
          },
        },
        {
          title: '商品重量(kg)',
          minWidth: 100,
          align: 'right',
          key: 'commodity_weight',
        },
        {
          title: '订单商品标签',
          width: 150,
          key: 'order_commodity_tag',
          render: (h, { row, index }) => {
            const key = 'order_commodity_tag';
            const getDefaultTag = () => {
              let defaultOption = this.orderGoodsTagList.find(
                (item) => item.is_default === '1',
              );
              let tagId = defaultOption ? defaultOption.id : '';
              this.storeGoodsList[index][key] =
                this.newOrderList[index][key] =
                row[key] =
                  tagId;
              return tagId;
            };
            return h(mvSelect, {
              attrs: {
                clearable: true,
                placeholder: '请选择',
                transfer: true,
              },
              style: {
                width: '100px !important',
              },
              class: {
                'required-tip':
                  row[key] === '' && this.isOrderCommodityTagRequired,
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal:
                  row.order_commodity_tag !== undefined
                    ? row.order_commodity_tag
                    : this.showCreateOrderLastOcGag
                      ? ''
                      : getDefaultTag(),
              },
              on: {
                'on-change': (tagId, item) => {
                  this.storeGoodsList[index][key] =
                    this.newOrderList[index][key] =
                    row[key] =
                      tagId || '';
                },
                'on-open-change': (isOpen) => {
                  setTimeout(() => {
                    this.newOrderList[index].isSelectOpen = isOpen;
                  }, 100);
                },
                'on-enter-key-up': (event) => {
                  if (!this.newOrderList[index].isSelectOpen)
                    this._addGoods(index, event);
                },
              },
            });
          },
        },
        {
          width: 100,
          title: '折扣率(%)',
          key: 'discount',
          align: 'right',
        },
        {
          width: 100,
          title: '协议市场价',
          key: 'org_price',
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          filterMultiple: false,
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            const defaultFilter = this.priceType;
            return h(HeaderFilter, {
              props: {
                filterList,
                defaultFilter,
              },
              on: {
                'on-change': (_value) => {
                  this.handleChangePriceType(_value);
                },
              },
            });
          },
          render: (h, params) => {
            const { row } = params;
            let key = '';
            switch (this.priceType) {
              case '1':
                key = 'in_price';
                break;
              case '2':
                key = 'store_in_price';
                break;
              case '3':
                key = 'average_price';
                break;
              case '4':
                key = 'last_receipt_price';
                break;
              default:
                break;
            }
            // 根据选择的价格模式显示不同的数据
            const val = row[key];
            return h('span', val);
          },
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          width: 140,
          align: 'right',
        },
        {
          title: '参考毛利',
          key: 'reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row[priceTypeMap[this.priceType]]
            ).toFixed(2);
            return h('span', Number.isNaN(profit) ? '0' : profit);
          },
        },
        {
          title: '参考毛利总额',
          key: 'total_reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row[priceTypeMap[this.priceType]]
            ).toFixed(2);
            // 参考毛利总额=下单数量*参考毛利
            const total = Number(
              +(Number.isNaN(profit) ? '0' : profit) * +params.row.order_amount,
            ).toFixed(2);
            return h('span', Number.isNaN(total) ? '0' : total);
          },
        },
        {
          title: '参考毛利率',
          key: 'reference_profit_rate',
          width: 100,
          render: (h, params) => {
            // 参考毛利率=参考毛利/下单单价*100%
            if (!+params.row.unit_price) return h('span', '0%');
            let profit =
              params.row.unit_price - params.row[priceTypeMap[this.priceType]];
            let rate = ((profit / params.row.unit_price) * 100).toFixed(2);
            return h('span', rate + '%');
          },
        },
        {
          width: 130,
          title: '税率',
          key: 'tax_rate_desc',
          align: 'left',
          sortable: true,
          render: (h, params) => {
            const { index, row } = params;
            const key = 'tax_rate';
            return h(NumberInput, {
              props: {
                precision: 0,
                min: 0,
                max: 99,
                placeholder: '',
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.storeGoodsList[index][key] = row[key] = value;
                  this.newOrderList[index][key] = row[key];
                },
                'on-enter': (event) => {
                  this.storeGoodsList[index]['editable'] = row.editable;
                  this.newOrderList[index]['editable'] = row.editable;
                  this._addGoods(index, event);
                },
              },
            });
          },
        },
        {
          width: 80,
          title: '税额',
          sortable: true,
          key: 'tax_rate_price',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            const taxRatePrice = +row.tax_rate
              ? (((row.order_amount * row.unit_price) /
                  (1 + row.tax_rate / 100)) *
                  Number(row.tax_rate)) /
                100
              : 0;
            return h('span', {}, taxRatePrice.toFixed(2));
          },
        },
        {
          title: '内部备注',
          key: 'inner_remark',
          align: 'center',
          minWidth: 90,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '内部备注',
            );
          },
          render: (h, params) => {
            const { row, index } = params;
            return h('i-input', {
              props: {
                value: row.inner_remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': (event) => {
                  this.newOrderList[index]['inner_remark'] =
                    row['inner_remark'];
                  this._addGoods(index, event);
                },
              },
              nativeOn: {
                change: ($event) => {
                  let value = $event.target.value;
                  row.inner_remark = value;
                  this.storeGoodsList[index].inner_remark = value;
                  this.syncStoreList();
                },
              },
            });
          },
        },
        {
          title: '客户商品别名',
          key: 'user_commodity_alias_name',
        },
        {
          title: '绑定农批市场',
          minWidth: 140,
          key: 'bind_wholesale_market',
        },
        {
          title: '加单数量',
          key: 'changes_num',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单数量',
            );
          },
          render: (h, params) => {
            const { row, index } = params;
            let key = 'changes_num';
            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.storeGoodsList[index][key] = row[key] = value;
                  this.handleChangeNum(row, index);
                },
                'on-enter': () => {
                  this.newOrderList[index][key] = row[key];
                  this._addGoods(index);
                },
              },
            });
          },
        },
        {
          title: '加单金额',
          key: 'changes_price',
          minWidth: 140,
          align: 'right',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单金额',
            );
          },
        },
        {
          title: '销售数量',
          key: 'sales_num',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售数量',
            );
          },
        },
        {
          title: '销售单价',
          key: 'sales_unit_price',
          minWidth: 100,
          align: 'right',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售单价',
            );
          },
        },
        {
          title: '销售金额',
          key: 'sales_price',
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售金额',
            );
          },
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 100,
        },
        {
          title: '分账供应商',
          key: 'account_provider_name',
        },
        {
          title: '分账比例',
          key: 'account_ratio',
        },
        {
          title: '菜谱套餐',
          key: 'raw_recipe_package_names',
        },
        {
          title: '采购负责人',
          key: 'provider_supervisor_name',
        },
        {
          title: '客户商品别名编码',
          key: 'user_commodity_alias_code',
        },
        {
          title: '商品状态',
          key: 'is_online',
          render: (h, { row }) => {
            return h(
              'span',
              row.is_online === 'Y' ? '上架' : '下架',
            );
          },
        },
        {
          title: '实时采购员/供应商',
          key: 'now_agent_provider',
          width: 160,
          tip: '实时读取当前客户对应商品 默认的采购员/供应商信息',
        },
      ],
      priceType:
        window.localStorage.getItem('order_base_price_mode') ||
        filterList[0].value,
      attachmentFiles: [],
      splitProviderId: '',
      orderGoodsTagList: [],
    };
  },
  watch: {
    sysConfig() {
      this.isOpenScanLoading =
        +this.sysConfig.open_scan_loading_unloading === 1;
    },
    userHasContractPriceOrder() {
      this.handleSetCols();
    },
  },
  created() {
    this.getOrderGoodsTagList();
    this.init();
  },
  activated() {
    this.init('active');
  },
  updated() {
    this.detectModified();
  },
  mounted() {
    this.detectModified();
    this.getInitData();
  },
  computed: {
    isLastRow() {
      return this.newOrderList.length === this.activeRowIndex + 1;
    },
    isAccountOrder() {
      return Number(this.orderDetail.is_account) === 1;
    },
    orderTotalAmount() {
      return this.storeGoodsList
        .filter((goods) => goods.commodity_id)
        .reduce(
          (prev, next) => prev.add(+next.original_total_price).toFixed(2),
          0,
        )
        .toFixed(2);
    },
    orderTotalNum() {
      return this.storeGoodsList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add(next.order_amount), 0)
        .toFixed(2);
    },
    totalReferenceProfit() {
      let allTotal = 0;
      this.storeGoodsList
        .filter((goods) => goods.commodity_id)
        .forEach((item) => {
          // 参考毛利=下单单价-参考成本价
          const profit = (
            item.unit_price - item[priceTypeMap[this.priceType]]
          ).toFixed(2);
          // 参考毛利总额=下单数量*参考毛利
          const total = Number(
            +(Number.isNaN(profit) ? '0' : profit) * +item.order_amount,
          ).toFixed(2);
          allTotal = Number(
            allTotal + +(Number.isNaN(total) ? '0' : total),
          ).toFixed(2);
        });
      return allTotal;
    },
    showCreateOrderLastOcGag() {
      return Number(this.sysConfig.show_create_order_last_oc_tag) === 1;
    },
  },
  methods: {
    handleCancelActionRemindModal() {
      this.submitting = false;
    },
    handleColsChange(cols) {
      this.totalReferenceProfitShow = cols.includes('total_reference_profit');
    },
    updateTableHeight(isFullscreen, screenHeight) {
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 94;
      }
    },
    excludeName() {
      let arr = ['commodity_code', 'bar_code'];
      let goodNameSearch = StorageUtil.getLocalStorage('goodNameSearch')
        ? StorageUtil.getLocalStorage('goodNameSearch')
        : ['commodity_code', 'bar_code'];
      goodNameSearch.forEach((res) => {
        let index = arr.findIndex((ee) => ee == res);
        if (index > -1) {
          arr.splice(index, 1);
        }
      });
      return {
        query_exclude: arr.join(','),
        only_bar_code: StorageUtil.getLocalStorage('isRightMatch')
          ? StorageUtil.getLocalStorage('isRightMatch')
          : false,
      };
    },
    // 批量新增订单
    _batchAddGoods() {
      if (this.user.id) {
        this.showGoodsListModal = true;
      } else {
        this.modalError('请选择正确的客户');
      }
    },
    handleSortChange(columns, key, order) {
      console.log('newOrderList-sort-before', this.newOrderList);
      if (key === 'tax_rate_desc') {
        // 特殊字段替换
        key = 'tax_rate';
      }
      this.newOrderList = this.newOrderList.sort((a, b) => {
        if (key === 'sub_total_price') {
          // 特殊字段替换 表格本身没有这个字段，前端运算展示
          a[key] = (+a.order_amount).mul(+a.unit_price).toFixed(4);
          b[key] = (+b.order_amount).mul(+b.unit_price).toFixed(4);
        }

        if (key === 'tax_rate_price') {
          a[key] = +a.tax_rate
            ? (
                (((a.order_amount * a.unit_price) / (1 + a.tax_rate / 100)) *
                  Number(a.tax_rate)) /
                100
              ).toFixed(2)
            : 0;
          b[key] = +b.tax_rate
            ? (
                (((b.order_amount * b.unit_price) / (1 + b.tax_rate / 100)) *
                  Number(b.tax_rate)) /
                100
              ).toFixed(2)
            : 0;
        }
        if (order === 'asc') {
          if (isNaN(a[key])) {
            return a[key].localeCompare(b[key]);
          } else {
            return a[key] - b[key];
          }
        } else if (order === 'desc') {
          if (isNaN(a[key])) {
            return b[key].localeCompare(a[key]);
          } else {
            return b[key] - a[key];
          }
        } else {
          return a._sortNum - b._sortNum;
        }
      });
      console.log('newOrderList-sort-after', this.newOrderList);
      // console.log('newOrderList', this.newOrderList)
      this.syncStoreList();
      // console.log('columns', columns)
      // console.log('key', key)
      // console.log('order', order)
    },
    getInitData() {
      common.getPurchaseType({}).then((res) => {
        if (res.status) {
          this.initData = res.data || {};
        }
      });
    },
    previewImage(image, _images, _viewIndex = 0) {
      this.viewImage(image, _viewIndex);
    },
    init(active) {
      this.delOrderList = [];
      this._initColumns(active);
      this.isOpenScanLoading =
        +this.sysConfig.open_scan_loading_unloading === 1;
      this.modified = false;
      this.orderTagList = [];
      this.selectedOrderTag = [];
      this.getOrderDetail();
      this.getDeliveryTimeList();
      // 得到标签配置，是否可以添加多个标签，是否只能添加一个标签
      this.commonService.getConfig().then((config) => {
        const {
          is_open_order_tag_required: must_tag,
          is_open_order_tag_only_one: only_tag,
        } = config;
        this.must_tag = must_tag;
        this.only_tag = only_tag;
      });
    },
    _onDraggableData(data) {
      this.newOrderList = data;
      this.setSortNum();
      this.syncStoreList();
    },
    async _initColumns(active) {
      if (active) {
        return;
      }
      const customFieldKeys = await this.setCustomizeFieldKeys();
      this.originCols = this.originCols.concat(customFieldKeys);
      this.handleSetCols();
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '0,4',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                // 商品自定义字段，只需要展示
                if (+item.customize_type === 0) {
                  return {
                    title: item.name,
                    key: item.key,
                  };
                }
                // 订单自定义字段，需要可编辑
                if (+item.customize_type === 4) {
                  this.goodsCustomizeField.push(item);
                  return {
                    title: item.name,
                    key: item.key,
                    render: (h, params) => {
                      let row = params.row;
                      let index = params.index;
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        on: {
                          'on-enter': () => {
                            this.newOrderList[index][item.key] = row[item.key];
                          },
                        },
                        nativeOn: {
                          change: ($event) => {
                            let value = $event.target.value;
                            row[item.key] = value;
                            this.storeGoodsList[index][item.key] = value;
                            this.syncStoreList();
                          },
                        },
                      });
                    },
                  };
                }
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
    handleSetCols() {
      const columns = this.deepClone(this.originCols);

      if (!this.userHasContractPriceOrder) {
        if (columns.some((col) => col.key === 'org_price')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'org_price'),
            1,
          );
        }
        if (columns.some((col) => col.key === 'discount')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'discount'),
            1,
          );
        }
      }
      this.goodsColumns = columns;
      console.log({ columns }, this.goodsColumns);
    },
    _addGoods(index) {
      if (this.orderDetail.consortium_forbid_change_item_num) return


      this.newOrderList.splice(index + 1, 0, {
        id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
        commodity_id: '',
        name: '',
        order_amount: 1,
        unit_price: '',
        un_confirm_receive: 0,
        sales_num: 1,
        sales_price: 0,
        changes_price: 0,
        changes_num: 0,
        _sortNum: index + 1,
        editable: false,
        is_online: 'Y',
      });
      console.log('newOrderList-addGoods', this.newOrderList);
      this.syncStoreList();
      this._focusGoodsInput(index);
    },
    /**
     * @description: 删除商品并标记，需要传给接口
     * @return {Boolean} 返回标记结果，是否为删除的商品
     */
    //
    _signDelGoods(orderCommodity_id) {
      // 如果是从获取出来的商品中删除，则记录
      const ocid = orderCommodity_id;
      if (ocid) {
        const findGoods = this.oldOrderList.find(
          (goods) => goods.orderCommodity_id === ocid,
        );
        if (findGoods) {
          this.delOrderList.push(ocid);
          return true;
        }
      }
      return false;
    },
    _deleteGoods(row, index) {
      if (this.newOrderList.length > 1) {
        this._signDelGoods(row.orderCommodity_id);
        this.newOrderList.splice(index, 1);
        this.syncStoreList();
        this.infoMessage(row.name + '已删除');
      }
    },
    _focusGoodsInput(index) {
      this.activeRowIndex = index;
      console.log(index);
      const curTr = this.$refs.orderGoodsTable.$el.querySelector(
        `[data-key="body_tr_${index}"]`,
      );
      this.$nextTick(() => {
        let nextTr = curTr.nextElementSibling;
        if (nextTr) {
          nextTr
            .querySelector('.commodity-select')
            .querySelector('textarea')
            .focus();
          this.activeRowIndex = index + 1;
        }
      });
    },
    _focusAmountInput(index) {
      this.$nextTick(() => {
        const $currentRow =
          this.$refs.orderGoodsTable.$el.querySelectorAll('tbody tr')[index];
        if ($currentRow) {
          this.currentIndex = index;
          $currentRow.classList.add('sdp-table__tr-highlight');
          $currentRow.scrollIntoView({ block: 'center' });
          // 使用 MutationObserver 监控变化
          const observer = new MutationObserver((mutationsList, observer) => {
            // 检查是否有子节点变化
            for (const mutation of mutationsList) {
              if (mutation.type === 'childList') {
                const input = $currentRow.querySelector(
                  '.order-amount-input input',
                );
                if (input) {
                  input.focus();
                  observer.disconnect(); // 任务完成，停止观察
                }
              }
            }
          });

          // 开始观察row的子节点
          observer.observe($currentRow, { childList: true, subtree: true });

          // 如果已经渲染完毕，则立即focus
          const input = $currentRow.querySelector('.order-amount-input input');
          if (input) {
            input.focus();
            observer.disconnect(); // 如果已经有input，则停止观察
          }
        }
      });
    },
    dataProcessing(val) {
      const goodsList = [];
      const addGoodsToGoodsList = ({ goods, order_amount }) => {
        const existGoods = goodsList.find(
          (existGoods) => +existGoods.commodity_id === +goods.commodity_id,
        );
        if (!existGoods) {
          goods = this.deepClone(goods);
          goods.order_amount = Number(order_amount);
          goodsList.push(goods);
        } else {
          existGoods.order_amount += Number(order_amount);
        }
      };

      val.forEach((ite) => {
        const packageNum = ite.order_amount || 0;
        ite.item.forEach((vals) => {
          vals.id = vals.commodity_id;
          if (vals.item) {
            vals.item.forEach((ites) => {
              ites.id = ites.commodity_id;
              addGoodsToGoodsList({
                goods: ites,
                order_amount: (packageNum * vals.num * ites.num).toFixed(4),
              });
            });
          } else {
            addGoodsToGoodsList({
              goods: vals,
              order_amount: (packageNum * vals.num).toFixed(4),
            });
          }
        });
      });

      let newOrderList = this.deepClone(this.newOrderList);
      if (newOrderList.length === 1 && !newOrderList[0].commodity_id)
        newOrderList = [];
      if (newOrderList.length > 0) {
        newOrderList.forEach((items) => {
          let find = goodsList.find(
            (val) => +items.commodity_id === +val.commodity_id,
          );
          if (find) {
            items.order_amount = (
              Number(find.order_amount) + Number(items.order_amount)
            ).toFixed(4);
          }
        });
        let arr = [];
        goodsList.forEach((value) => {
          let find = newOrderList.find(
            (ites) => Number(ites.id) === Number(value.commodity_id),
          );
          if (!find) {
            arr.push(value);
          }
        });
        newOrderList.splice(this.activeRowIndex + 1, 0, ...arr);
        this.activeRowIndex += arr.length;
      } else {
        newOrderList.splice(0, 0, ...goodsList);
        this.activeRowIndex += goodsList.length;
      }
      this.newOrderList = newOrderList;
      this.storeGoodsList = this.deepClone(newOrderList);
    },
    _newPackageDataProcessing(data, val) {
      let find = data.find((v) => +v.id === +val.commodity_id);
      if (find) {
        val.unit_price = find.price;
        val.logo = find.logo;
        val.stock = find.stock;
        val.discount = find.discount;
        val.org_price = find.org_price;
        val.in_price = find.in_price;
        val.tax_rate = find.tax_rate;
        val.tax_rate_price = find.tax_rate_price;
        val.remark = find.remark;
        val.inner_remark = find.inner_remark;
        val.unit_sell = find.unit_sell;
        val.un_confirm_receive = 0;
      }
      return val;
    },
    async $_onSelectGoodsPackage(value) {
      let commodity_id = [];
      let goodsPackage = this.deepClone(value);

      goodsPackage.forEach((ite) => {
        ite.item.forEach((val) => {
          if (val.commodity_id !== undefined) {
            commodity_id.push(val.commodity_id);
          }
          if (val.item) {
            val.item.forEach((v) => {
              if (v.commodity_id !== undefined) {
                commodity_id.push(v.commodity_id);
              }
            });
          }
        });
      });
      let id = Array.from(new Set(commodity_id));
      let params = {
        user_id: this.user.id,
        commodity_id: id.toString(),
        delivery_date: this.delivery_date,
      };
      let { data, status, message } = await get(
        this.apiUrl.orderSuperAjaxGetMultiOrderCommodity,
        params,
      );
      if (status) {
        goodsPackage.forEach((ite) => {
          ite.item.forEach((val) => {
            this._newPackageDataProcessing(data, val);
            if (val.item) {
              val.item.forEach((i) => {
                this._newPackageDataProcessing(data, i);
              });
            }
          });
        });
        this.dataProcessing(goodsPackage);
        this.$_closeGoodsPackageModal();
      } else {
        this.modalError(message);
      }
    },
    $_closeGoodsPackageModal() {
      this.goodsPackageModal.show = false;
    },

    handleChangePrice(goods) {
      // if (!this.isEnableUserContractPriceDiscountRatio) {
      //   return false;
      // }

      // 为0.00时出现无穷大
      let discount = Number(goods.discount) || 100;
      discount = discount / 100;
      goods.org_price = (goods.unit_price / discount).toFixed(2);
    },
    handleChangeDiscount(goods) {
      this.handleChangePrice(goods);
    },
    handleChangeMarketPrice(goods) {
      let marketPrice = goods.org_price || 0;
      let discount = Number(goods.discount) || 100;
      discount = discount / 100;
      goods.unit_price = (marketPrice * discount).toFixed(2);
    },
    handleChangeNum(goods, index) {
      // 计算下单小计
      this.storeGoodsList[index].original_total_price = (+goods.order_amount)
        .mul(+goods.unit_price)
        .toFixed(2);
      // 固定单价
      if (this.issue_order_data_contact_way) {
        if (!this.isCommoditySorted(goods)) {
          // 销售数量=下单数量+加单数量
          this.storeGoodsList[index].sales_num = goods.sales_num = (
            Number(goods.order_amount) + Number(goods.changes_num || 0)
          ).toFixed(2);
          // 销售金额=销售单价*销售数量
          this.storeGoodsList[index].sales_price = goods.sales_price =
            (+goods.sales_num).mul(+goods.sales_unit_price).toFixed(4);
          let sub_total_price = (+goods.order_amount)
            .mul(+goods.unit_price)
            .toFixed(2);
          this.storeGoodsList[index].sub_total_price = sub_total_price;
          // 加单金额=销售金额-下单金额
          this.storeGoodsList[index].changes_price = goods.changes_price = (
            +goods.sales_price - sub_total_price
          ).toFixed(2);
        } else {
          // 销售数量=发货数量+加单数量
          this.storeGoodsList[index].sales_num = goods.sales_num = (
            Number(goods.actual_amount) + Number(goods.changes_num || 0)
          ).toFixed(2);
          // 销售金额=销售单价*销售数量
          this.storeGoodsList[index].sales_price = goods.sales_price =
            (+goods.sales_num).mul(+goods.sales_unit_price).toFixed(4);
          // 加单金额=销售金额-发货金额
          this.storeGoodsList[index].changes_price = goods.changes_price = (
            +goods.sales_price - goods.actual_total_price
          ).toFixed(2);
        }

        this.syncStoreList();
        return;
      }
      // 商品未分拣时
      if (!this.isCommoditySorted(goods)) {
        // 销售数量=下单数量+加单数量
        this.storeGoodsList[index].sales_num = goods.sales_num = (
          Number(goods.order_amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=下单金额+加单金额
        this.storeGoodsList[index].sales_price = goods.sales_price =
          (+goods.order_amount).mul(+goods.unit_price).toFixed(4) +
          Number(goods.changes_price);
      } else {
        // 销售数量=发货数量+加单数量
        this.storeGoodsList[index].sales_num = goods.sales_num = (
          Number(goods.actual_amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=发货金额+加单金额
        this.storeGoodsList[index].sales_price = goods.sales_price = (
          Number(goods.actual_total_price) + Number(goods.changes_price)
        ).toFixed(4);
      }
      // 销售单价=销售金额/销售数量
      this.storeGoodsList[index].sales_unit_price = goods.sales_unit_price = (
        goods.sales_price / goods.sales_num
      ).toFixed(2);
      this.syncStoreList();
    },
    updateStoreListPrice(params) {
      this.storeGoodsList[params.index].unit_price = params.row.unit_price;
      this.storeGoodsList[params.index].discount = params.row.discount;
      this.storeGoodsList[params.index].org_price = params.row.org_price;
    },
    checkContractPrice(uid) {
      // 没有启用订单同步客户协议价
      if (!this.isEnableOrderSyncContractPrice) {
        this.syncContractPrice = false;
        return false;
      }
      this.syncContractPrice = true;
      common
        .checkValidUserContractPriceOrder(uid, this.delivery_date)
        .then((res) => {
          let { data } = res;
          if (!data) {
            this.userHasContractPriceOrder = false;
            this.syncContractPrice = false;
          } else {
            this.userHasContractPriceOrder = true;
          }
        });
    },
    // 订单标签限制
    checkTag() {
      if (this.orderTagList.length === 0) return true;

      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单最多存在3个标签');
        return false;
      }

      if (this.only_tag === '1' && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单只能选择一个标签');
        return false;
      }

      if (this.must_tag === '1' && this.selectedOrderTag.length === 0) {
        this.warningMessage('请至少选择一个订单标签');
        return false;
      }
      return true;
    },
    // 获取订单标签列表
    getOrderTagList(user_id) {
      const params = {
        user_id,
      };
      common.qryOrderTagList(params).then((res) => {
        if (+res.status === 1) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = uniqBy(
              [...res.data, ...this.selectedOrderTagList],
              'id',
            );
          }
        } else {
          // 没开启标签时不需要提示
          // this.modalError(res.message);
        }
      });
    },
    // 获取当前下单用户的信息
    handleGetUserInfo(id) {
      userApi.getUserDetail(id).then(res => {
        if (res.status) {
          this.userInfo = res.data
        }
      })
    },
    handleRowClick(row, index) {
      this.activeRowIndex = index;
    },
    rowClassName(row, index) {
      return index === this.activeRowIndex ? 'sdp-table__tr-highlight' : '';
    },
    detectModified() {
      let inputList = document.querySelectorAll("input:not([type='hidden'])");
      Array.from(inputList).map((input) => {
        input.onclick = () => {
          this.modified = true;
        };
      });
    },
    handleOpenDeliveryDate(isOpen) {
      if (isOpen) {
        this.originalDate = this.date;
      }
    },
    handleChangeDeliveryDate(date) {
      this.delivery_date = date;
      if (
        this.date !== this.originalDate &&
        this.isOpenScanLoading &&
        this.storeGoodsList.some((commodity) =>
          this.isCommoditySorted(commodity),
        )
      ) {
        this.errorNotice(
          '该订单已经有商品分拣装框，不能修改发货日期，请将分拣商品重置之后再进行修改',
        );
        this.date = this.originalDate;
        this.delivery_date = this.originalDate;
      }
      if (this.date !== this.originalDate && this.isContractPriceModeDelivery) {
        this.$Modal.confirm({
          content:
            '本次保存不会更改商品价格，如果发货日期存在不同的协议价请重新下单或者使用复制订单功能！',
          onCancel: () => {
            this.date = this.originalDate;
            this.delivery_date = this.originalDate;
          },
        });
      }
    },
    syncStoreList() {
      let list = this.deepClone(this.newOrderList);
      list = list.map((item) => {
        let storeItem = this.storeGoodsList.find(
          (findItem) => findItem.id === item.id,
        );
        if (storeItem) {
          return storeItem;
        }
        return item;
      });
      this.newOrderList = list;
      this.storeGoodsList = this.deepClone(list);
    },
    isProtocolGoods(goodsInfo) {
      return Goods.isProtocolGoods(goodsInfo);
    },
    handlerAdd(orders) {
      if (orders) {
        orders.forEach((item) => {
          item.id += uniqueId('$unique-');
          item.un_confirm_receive = 0;
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +item.is_price_type === 2 &&
            +item.price === 0
          ) {
            this.$Message.warning(`${item.name}未设置客户类型价`);
          }
          item.order_amount = item.amount; // 添加商品填写的数量写入商品详情中
          item.unit_price = item.price;
          item.changes_price = 0;
          item.changes_num = 0;
          item.sales_num = item.order_amount;
          item.sales_unit_price = item.unit_price;
          item.sales_price = (+item.order_amount)
            .mul(+item.unit_price)
            .toFixed(4);
          item.amount_warning = false;
          item._isNewGoods = true;
          item.editable = false;
          // 计算下单小计
          item.original_total_price = (+item.order_amount)
            .mul(+item.unit_price)
            .toFixed(2);
        });
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(this.activeRowIndex, 1, ...orders);
          this.activeRowIndex += orders.length - 1;
        } else {
          this.newOrderList.splice(this.activeRowIndex + 1, 0, ...orders);
          this.activeRowIndex += orders.length;
        }
        this.setSortNum();
        this.syncStoreList();
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    setSortNum() {
      this.newOrderList.forEach((item, index) => {
        item._sortNum = index;
      });
      console.log('_sortNum', this.newOrderList);
    },
    // 批量新增订单
    addGoods() {
      this.showGoodsListModal = true;
    },
    getOrderDetail() {
      this.tableLoading = true;
      var self = this,
        id = this.$route.query.id;
      common.getModifyOrderDetail(id).then((res) => {
        this.tableLoading = false;
        if (res.status) {
          var data = res.data.data,
            list = data.commodityList,
            detail = data.order;
          // 检查客戶是否有协议单
          this.checkContractPrice(detail.user_id);

          // 订单自定义字段
          this.orderCustomizeField = detail.customize_fields;

          // 获取已选中的标签
          let orderTagList = (data.order || {}).order_tag;
          this.selectedOrderTagList = orderTagList || [];
          self.selectedOrderTag = [];
          if (Array.isArray(orderTagList)) {
            orderTagList.map((item) => {
              self.selectedOrderTag.push(item.id);
            });
          }
          // if (this.isSplitOrderLockProvider && detail.split_provider_name) this.splitProviderId = detail.split_provider_id

          self.remarks = detail.remark;
          this.getOrderTagList(detail.user_id);
          // 获取当前用户信息
          this.handleGetUserInfo(detail.user_id)
          self.user = {
            id: detail.user_id,
            name: detail.receive_name,
            tel: detail.receive_tel,
            address: detail.address_detail + detail.address_attach,
          };
          self.orderDetail = data.order;
          this.formData.subsidiary_id = data.order.subsidiary_id;
          this.formData.meal_type = data.order.meal_type;
          this.formData.express_no = data.order.express_no;
          this.formData.sub_user_name = data.order.sub_user_name;
          this.formData.split_provider_id = data.order.split_provider_id;
          // 将修改的订单打上标记
          list.forEach((item, index) => {
            item.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
            item.amount_warning = '';
            item.is_modify = 1;
            item._sortNum = index;
            item.editable = false;
          });
          self.date = detail.delivery_date;
          self.delivery_date = self.date;
          self.selectedDeliveryTime = detail.delivery_time_id;
          self.oldOrderList = self.newOrderList = list;
          self.originalOrderCommodity = self.deepClone(list);
          self.syncStoreList();
          self.activeRowIndex = self.newOrderList.length - 1;
          self.attachmentFiles = detail.attach_url || [];
        }
      });
    },
    getDeliveryTimeList: function () {
      var self = this;
      common.getDeliveryTimeList({ pageSize: 9999 }).then((res) => {
        if (res.status) {
          res.data.list.forEach((time) => {
            let timeJson = {
              id: time.id,
              timeDu: time.name + ' ' + time.start_time + '-' + time.end_time,
            };
            self.deliveryTimeList.push(timeJson);
          });
        }
      });
    },
    // 获取现在的日期
    getDate() {
      var date = new Date();
      this.date =
        date.getFullYear() + '-' + date.getMonth() + 1 + '-' + date.getDate();
    },
    // 添加商品
    _setCommodity(cid, com, row, index) {
      com.cid = com.commodity_id;
      com.price = com.type_price || '';
      com.discount = com.discount || 100;
      com.org_price = com.org_price || '';
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        this.errorMessage('请输入客户');
        return;
      }
      if (!com.cid) {
        this.errorMessage('请输入正确的商品');
        return;
      }

      // 根据is_open_order_add_same_commodity【是否开启订单录入重复商品】
      // 关闭时如果已经加入订单列表，则无法再次加入
      const findIndex = this.newOrderList.findIndex(
        (goods) => goods.commodity_id === cid,
      );
      if (
        !this.is_open_order_add_same_commodity &&
        ~findIndex &&
        findIndex !== index
      ) {
        this.newOrderList[index] = {
          // 清空当前行商品
          id: uniqueId('$unique-'), // 生成唯一key
          commodity_id: '',
          name: '',
          order_amount: row.order_amount,
          unit_price: '',
          un_confirm_receive: 0,
          _sortNum: index,
          editable: false,
        };
        this.syncStoreList();
        this._focusAmountInput(findIndex);
        this._signDelGoods(row.orderCommodity_id);
        this.$Message.destroy();
        return this.warningMessage('商品已存在');
      }
      let extraParams = {
        order_id: this.orderId,
      };
      // 这里通过客户id和商品id获取订单商品数据
      common
        .getOrderCommodity(
          this.user.id,
          com.cid,
          this.delivery_date,
          extraParams,
        )
        .then((res) => {
          const { status, data, message } = res;
          if (status) {
            if (
              !this.userStylePriceIsZeroDefaultMarkPrice &&
              +data.is_price_type === 2 &&
              +data.price === 0
            ) {
              this.warningMessage(`${data.name}未设置客户类型价`);
            }
            data.unit_price = data.price;
            data.commodity_id = data.id;
            // 保证id即rowKey唯一即可
            data.id += uniqueId('$unique-');
            data.editable = false;
            data._sortNum = index;
            data.order_amount = row.order_amount;
            data.amount_warning = false;
            data.un_confirm_receive = 0;
            data.changes_num = 0;
            data.changes_price = 0;
            data.sales_num = row.sales_num;
            data.is_online = data.is_online || 'Y';
            if (!data.sales_price)
              data.sales_price = (+data.order_amount)
                .mul(+data.unit_price)
                .toFixed(2);
            if (!data.sales_unit_price) data.sales_unit_price = data.unit_price;

            // 如果是从获取出来的商品中删除，则记录, 接口需要传参删除的[订单商品id]
            let _isNewGoods = true;
            if (row.orderCommodity_id) {
              _isNewGoods = !this._signDelGoods(row.orderCommodity_id);
            }

            // 详情接口和新增单个商品查询接口 category_name 返回不一致的问题
            data.category_name1 = data.category_name;
            data.category_name =
              data.category_name1 +
              (data.category_name2 ? '/' + data.category_name2 : '') +
              (data.category_name3 ? '/' + data.category_name3 : '');
            data._isNewGoods = true; // 商品有变更就是true
            this._updateStepPricing(data);

            this.newOrderList[index] = this.deepClone(data);
            this.syncStoreList();
            this.handleChangeNum(data, index);
            this._updateStepPricingOrderAmountColumnWidth();
            this._focusAmountInput(index);
          } else {
            this.modalError(message || '商品不存在', 0);
          }
        });
    },
    isCommoditySorted(commodity) {
      return Number(commodity.is_sorting) === 1;
    },
    checkCommodity(flag) {
      return new Promise((resolve, reject) => {
        let diffCommodity = [];
        this.originalOrderCommodity.forEach((commodity) => {
          let modifiedCommodity = this.newOrderList.find(
            (findCommodity) =>
              findCommodity.orderCommodity_id === commodity.orderCommodity_id,
          );
          // 删除了已分拣的商品
          if (this.isCommoditySorted(commodity)) {
            if (!modifiedCommodity) {
              diffCommodity.push(commodity.name);
              return false;
            }
            if (commodity.order_amount - modifiedCommodity.order_amount !== 0) {
              diffCommodity.push(commodity.name);
              return false;
            }
          }
        });
        if (diffCommodity.length > 0 && !flag) {
          this.$Modal.confirm({
            content: `商品：${diffCommodity.join('，')}已分拣，确定继续保存？`,
            onOk: () => {
              resolve();
            },
            onCancel: () => {
              reject();
            },
          });
        } else {
          resolve();
        }
      });
    },
    checkData() {
      let valid = true;
      let firstErrorDom = null;
      let firstErrorGoodsIndex = null;

      const filterStoreGoodsList = this.storeGoodsList.filter(
        (goods) => goods.commodity_id,
      );
      if (filterStoreGoodsList.length === 0) {
        // valid = false
        this.errorMessage('请添加商品和数量');
        return false;
      }

      // 检查订单商品订购数量
      filterStoreGoodsList.forEach((item, index) => {
        if (!item.order_amount && Number(item.order_amount) !== 0) {
          valid = false;
          item.amount_warning = true;
          if (firstErrorGoodsIndex === null) {
            this.errorMessage('请输入订购数量');
            firstErrorGoodsIndex = index;
          }
        } else {
          item.amount_warning = false;
        }
      });
      this.syncStoreList();
      // 开启订单商品标签必填
      if (this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        for (const [index, good] of filterStoreGoodsList.entries()) {
          if (!good.order_commodity_tag) {
            console.log('commodity', good);
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`);
            const scrollBox = this.$refs.orderGoodsTable.$el.querySelector(
              '.sdp-table__content',
            );
            const curElement = this.$refs.orderGoodsTable.$el.querySelector(
              `[data-key="body_tr_${index}"]`,
            );
            scrollBox.scrollTo(0, curElement.offsetTop);
            return false;
          }
        }
      }

      if (!this.date) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.delivery_date = '请选择发货日期';
      } else {
        this.error.delivery_date = '';
      }

      if (!firstErrorDom && firstErrorGoodsIndex !== null) {
        this.$nextTick(() => {
          let $firstRow = this.$refs.orderGoodsTable.$el.querySelector(
            `.editable-table tbody tr:nth-child(${firstErrorGoodsIndex + 1})`,
          );
          if ($firstRow) {
            $firstRow.scrollIntoView({ block: 'center' });
          }
        });
      } else if (firstErrorDom) {
        firstErrorDom.scrollIntoView({ block: 'center' });
      }
      return valid;
    },
    // 保存订单标签
    saveOrderTags() {
      if (!this.checkTag()) {
        return false;
      }
      let orderId = this.$route.query.id;
      // 防止重复传参
      let tagIds = JSON.stringify([...new Set(this.selectedOrderTag)]);
      if (this.submitting) {
        return false;
      }
      this.submitting = true;
      common.saveOrderTags(orderId, tagIds).finally(() => {
        // 为了对正常保存流程无影响，这里不管成不成功，都直接进行下一步
        let commodityList = this.deepClone(
          this.storeGoodsList.filter((goods) => goods.commodity_id),
        );
        const change_ocs = []
        this.originalOrderCommodity.forEach(item => {
          const obj = commodityList.find(c => item.id === c.id)
          if (obj && obj.order_amount != item.order_amount) {
            change_ocs.push({
              order_commodity_id: obj.orderCommodity_id,
              amount: obj.order_amount
            })
          }
        })
        if (this.delOrderList.length || change_ocs.length) {
          // this.submitting = false;
          this.$refs.actionRemind.open({
            order_id: orderId,
            del_oc_ids: this.delOrderList.join(','),
            change_ocs: JSON.stringify(change_ocs)
          })
          this.modified = false
          return
        }
        this.saveOrder();
      });
    },
    handleOk() {
      this.saveOrder(true);
    },
    saveOrder(flag) {
      if (!this.checkData()) {
        this.submitting = false;
        return false;
      }
      this.checkCommodity(flag)
        .then(() => {
          this.saveModifyOrderV2();
        })
        .catch(() => {
          this.submitting = false;
        });
    },
    saveModifyOrderV2() {
      this.submitting = true;
      // 转换中国时区
      let dateValue = new Date(this.date).valueOf() + 28800000;
      dateValue = new Date(dateValue);
      let self = this;
      let orderId = self.$route.query.id;
      let delOrderList = self.delOrderList;
      let selectedDeliveryTime = self.selectedDeliveryTime;

      let date = dateValue.toISOString().slice(0, 10);
      let remarks = self.remarks;

      let commodityList = this.deepClone(
        this.storeGoodsList.filter((goods) => goods.commodity_id),
      );
      commodityList = commodityList.map((item, index) => {
        let result = {
          sort_num: index,
          commodity_id: item.id
            ? item.id.replace(/\$unique-.*$/, '')
            : item.commodity_id,
          order_commodity_id: item.orderCommodity_id,
          remark: item.remark,
          inner_remark: item.inner_remark,
          amount: item.order_amount,
          unit_price: item.unit_price,
          in_price: item.in_price,
          org_price: item.org_price,
          discount: item.discount,
          un_confirm_receive: item.un_confirm_receive,
          pp_detail_id: item.pp_detail_id,
          price_type: item.is_price_type,
          changes_num: item.changes_num,
          changes_price: item.changes_price,
          sales_unit_price: item.sales_unit_price,
          order_commodity_tag: item.order_commodity_tag,
          tax_rate: item.tax_rate,
        };
        // 订单自定义字段
        result.customize_fields = this.goodsCustomizeField.map((field) => {
          return {
            key: field.key,
            name: field.name,
            value: item[field.key],
          };
        });
        // result.customize_fields = JSON.stringify(result.customize_fields);
        return result;
      });

      common
        .saveModifyOrderV2(
          {
            sync_protocol: this.syncContractPrice ? 1 : 0,
            items: commodityList,
            delivery_date: date,
            delivery_time: selectedDeliveryTime,
            remark: remarks,
            orderId,
            delItems: delOrderList,
            customize_fields: this.orderCustomizeField,
            ...this.formData,
          },
          {
            attachment_link: this.attachmentFiles
              .map((item) => item.url)
              .join(','),
          },
        )
        .then((res) => {
          if (res.status) {
            self.successMessage('订单修改成功');
            self.modified = false;
            self.back();
          } else {
            self.errorNotice(res.message);
          }
        })
        .finally(() => {
          self.submitting = false;
        });
    },
    back() {
      this.router.go(-1);
    },
    handleChangePriceType(_value) {
      window.localStorage.setItem('order_base_price_mode', _value);
      this.priceType = _value;
      // 更新价格模式配置
      this.commonService.editSingleConfig('order_base_price_mode', _value);
    },
    /**
     * @description: 判断是否为阶梯定价商品
     * @param {*} goods
     */
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，输入下单数量改变时，需要动态计算阶梯定价的商品价格
     * !! 只对新增的商品生效
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing(row, index = -1) {
      const { order_amount: amount } = row;
      if (row._isNewGoods && this.isStepPricingGoods(row)) {
        // 仅对新增的商品生效阶梯定价
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true),
        );
        if (stepPriceItem) {
          row.unit_price = stepPriceItem.price;
          if (index >= 0 && this.storeGoodsList[index]) {
            this.storeGoodsList[index].unit_price = stepPriceItem.price;
          }
        }
      }
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      if (
        this.storeGoodsList.some(
          (goods) => goods._isNewGoods && this.isStepPricingGoods(goods),
        )
      ) {
        const newWidth = 155;
        const order_amount_column = this.goodsColumns.find(
          (item) => item.key === 'order_amount',
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
    getOrderGoodsTagList() {
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data;
      });
    },
    toScroll(curRow) {
      console.log('toScroll', curRow);
      this.resetPreElement();
      const scrollBox = this.$refs.orderGoodsTable.$el.querySelector(
        '.sdp-table__content',
      );
      const curIndex = this.newOrderList.findIndex(
        (item) => item.id === curRow.id,
      );
      this.currentIndex = curIndex;
      const curElement = this.$refs.orderGoodsTable.$el.querySelector(
        `[data-key="body_tr_${curIndex}"]`,
      );
      const childrenElement = curElement.querySelectorAll('.sdp-table__td');
      childrenElement.forEach((item) => {
        item.style.backgroundColor = '#e6f7ec';
      });
      scrollBox.scrollTo(0, curElement.offsetTop);
    },
    resetPreElement() {
      const curElements =
        this.$refs.orderGoodsTable.$el.querySelectorAll('.sdp-table__tr');
      curElements.forEach((item) => {
        const childrenElements = item.querySelectorAll('.sdp-table__td');
        childrenElements.forEach((childrenElement) => {
          childrenElement.style.backgroundColor = '#fff';
        });
      });
    },
  },
};
</script>
<style lang="less" scoped>
.order-edit {
  /deep/.ivu-form-item {
    margin: 0 36px 12px -2px !important;
  }
  /deep/.receive_goods {
    .ivu-form-item {
      margin: -2px 36px 0px -2px !important;
    }
    .base-block__hd {
      padding-bottom: 6px;
    }
  }
}
/deep/.demo-upload-list {
  text-align: center;
  border: 1px solid transparent;
  border-radius: 2px;
  overflow: hidden;
  height: 36px;
  width: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0);
  position: relative;
  box-shadow: none;
  margin-right: 0px;
}

/deep/.demo-upload-list img {
  width: 100%;
  height: 100%;
}
/deep/.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}
/deep/.demo-upload-list-cover {
  display: none;
  position: absolute;
  color: #fff;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
/deep/ .editable-table {
  .sdp-table__cell > div {
    display: block !important;
  }
  .new-logo {
    margin-right: 2px;
    padding: 0 5px;
    color: #fff;
    background-color: #3399ff;
    border-radius: 2px;
  }

  .InternalNote {
    color: #3399ff;
  }

  .order-amount-input.amount_warning input {
    border-color: red;
  }
  .emphasis-tip input {
    color: #ff6e00;
    border-color: currentColor;
  }
  .required-tip .ivu-select-selection {
    border-color: rgb(243, 51, 51);
  }
}

.newOrder-operation {
  vertical-align: middle;
  color: #03ac54;

  span {
    cursor: pointer;
  }

  span:not(:last-child) {
    margin-right: 10px;
  }
}

.newOrder-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #303030;
  text-align: right;

  &-total {
    color: #f13130;
  }
}

.num-label {
  color: rgba(0, 0, 0, 0.85);
}

.num-font {
  font-family: Avenir;
}

.newOrder-remarks {
  margin-top: 10px;
  width: 100%;
  padding: 0 10px;
  border: none;
  .ivu-form-item {
    width: 100% !important;
  }
}
/deep/.commodity-select {
  width: 100%;
  .ivu-dropdown {
    width: 100%;
  }
}
.com-list-table-con {
  &.sdp-table__th {
    background-color: blue !important;
  }
}
</style>
