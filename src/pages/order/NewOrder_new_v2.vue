<!--
 * @Author: <PERSON>
 * @Date: 2021-12-03 14:41:58
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-31 10:21:11
 * @Description: 新增订单 -- 新版
-->
<template>
  <div class="order-new">
    <DetailPage
      pageType="add"
      title="新增订单"
      :disabledSave="createDisabled || disabledEverything"
      @on-save="createOrderBefore(1)"
    >
      <Form ref="form" inline label-colon :label-width="82" :disabled="false">
        <base-block title="基础信息" class="base-info">
          <FormItem label="客户" ref="error.user" :error="error.user">
            <div style="position: relative; width: 232px">
              <Input
                ref="userInput"
                :clearable="true"
                placeholder="请输入客户名称/客户编号"
                v-model="user.name"
                @on-change="getSearchUser"
                @on-enter="enterUser"
                @on-focus="resetUserError"
                @on-blur="userDropDown = false"
              ></Input>
              <transition name="dropdown-fade">
                <div class="dropdown-user" v-show="userDropDown">
                  <ul>
                    <li
                      class="dropdown-items"
                      v-for="info in userList"
                      :key="info.id"
                      :class="{ active: user.uid === info.uid }"
                      @click.stop="addUser(info)"
                      @mousedown.prevent
                    >
                      <strong class="dropdown-items-strong">{{
                        info.email
                      }}</strong>
                      <p class="dropdown-items-p">{{ info.user_code }}</p>
                    </li>
                  </ul>
                </div>
              </transition>
              <transition name="dropdown-fade">
                <div
                  class="dropdown-user-content"
                  v-show="!userDropDown && user.name && userList.length === 0"
                >
                  <p class="dropdown-empty">暂无数据</p>
                </div>
              </transition>
              <div
                v-if="showContractPriceTips || userMessage"
                style="padding-top: 6px; font-size: 12px"
              >
                <p class="text-red" v-if="showContractPriceTips">
                  客户不存在生效的协议单，不会同步协议单价格
                </p>
                <p class="text-red" v-if="userMessage">{{ userMessage }}</p>
              </div>
            </div>
          </FormItem>
          <FormItem
            label="发货日期"
            ref="error.delivery_date"
            :error="error.delivery_date"
          >
            <Date-picker
              @on-change="handleChangeDate"
              type="date"
              placeholder="选择日期"
              :disabled="disabledEverything"
              v-model="date"
              format="yyyy-MM-dd"
              :editable="false"
              style="width: 232px"
            ></Date-picker>
          </FormItem>
          <FormItem label="送货时间段">
            <Select
              v-model="selectedDeliveryTime"
              :disabled="disabledEverything"
              style="width: 232px"
            >
              <Option
                v-for="item in deliveryTimeList"
                :value="item.id"
                :key="item.id"
                >{{ item.timeDu }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="配送方式">
            <RadioGroup v-model="delivery_method">
              <Radio label="1" :disabled="disabledEverything">配送</Radio>
              <Radio label="2" :disabled="disabledEverything || !hasPickUpList.length || !is_can_self_pickup_point" v-if="sysConfig.self_pickup_point_apply_scope.indexOf('1') > -1">
                自提
                <Tooltip
                  v-if="!hasPickUpList.length || !is_can_self_pickup_point"
                  :transfer="true"
                  :delay="0"
                  :maxWidth="246"
                  :content="!is_can_self_pickup_point ? '当前客户暂不支持自提配送方式' : '当前未创建自提点，无法选择自提模式，请创建后重新操作'"
                  placement="top"
                >
                  <SIcon icon="help1" :size="12"/>
                </Tooltip>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="子机构" v-if="isOpenSubsidiary">
            <SuborganizationCascader
              v-model="formData.subsidiary_id"
              :userId="user.id"
              :disabled="!user.id"
            ></SuborganizationCascader>
          </FormItem>
          <FormItem label="餐次" v-if="isNewGoodsPackageRecipe">
            <MealtimeSelect
              v-model="formData.meal_type"
              style="width: 232px"
            ></MealtimeSelect>
          </FormItem>
          <FormItem label="拆单规则" v-if="isSplitOrderByProvider">
            <Select v-model="formData.split_provider_rule" style="width: 232px">
              <Option
                v-for="item in splitRule"
                :value="item.value"
                :key="item.value"
                >{{ item.name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem
            v-show="isSplitOrderByProvider && formData.split_provider_rule == 2"
            label="所属供应商"
          >
            <Select
              style="width: 232px"
              filterable
              clearable
              v-model="formData.split_provider_id"
              placeholder="请选择"
            >
              <Option
                :value="info.id"
                v-for="info in initData.providers"
                :key="info.id"
                >{{ info.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="物流单号" v-if="isOpenExpress100">
            <Input style="width: 200px" maxlength="32" v-model="formData.express_no" />
          </FormItem>
          <FormItem
            label="订单标签"
            v-if="orderTagList.length > 0"
            style="width: 100%"
          >
            <CheckboxGroup @on-change="checkTag" v-model="selectedOrderTag">
              <Checkbox
                :disabled="
                  disabledEverything ||
                  (only_tag === '1' &&
                    selectedOrderTag.length > 0 &&
                    !selectedOrderTag.includes(item.id))
                "
                v-for="item in orderTagList"
                :key="item.id"
                :label="item.id"
              >
                <span>{{ item.name }}</span>
              </Checkbox>
            </CheckboxGroup>
          </FormItem>
          <FormItem
            :label="item.name"
            :key="item.key"
            v-for="item in orderCustomizeField"
          >
            <Input style="width: 200px" maxlength="256" v-model="item.value" />
          </FormItem>
          <div v-if="user.id && user.special_remark">
            <FormItem>
              <template #label>
                <span style="color: #f33333">特殊备注:</span>
              </template>
              <div style="white-space: pre">{{ user.special_remark }}</div>
            </FormItem>
          </div>
        </base-block>
        <base-block class="base-info">
          <div class="base-block__hd" slot="title">
            <h5>收货信息</h5>
            <span class="newOrder-operation" style="margin-left: 12px" v-if="delivery_method == 2" @click="handleShowPick">切换自提点</span>
          </div>
          <template v-if="delivery_method == 1">
            <FormItem label="收货人">{{ user.detail.name }}</FormItem>
            <FormItem label="联系电话">{{ user.detail.tel }}</FormItem>
            <FormItem label="地址">{{ user.detail.address_detail }}</FormItem>
          </template>
          <!-- 自提点 -->
          <template v-else>
            <FormItem label="自提点">{{ pickInfo.name }}</FormItem>
            <FormItem label="联系电话">{{ pickInfo.mobile }}</FormItem>
            <FormItem label="地址">{{ pickInfo.address }}</FormItem>
          </template>
        </base-block>
        <base-block title="商品清单" v-show="isCanOrder">
          <EditableTable
            :max-height="getEditTableHeight()"
            ref="orderGoodsTable"
            :row-class-name="rowClassName"
            outer-border
            :columns="goodsColumns"
            :data="newOrderList"
            @on-draggable-data="_onDraggableData"
            @on-row-click="handleRowClick"
            @on-sort-change="handleSortChange"
          >
            <template #before-table>
          <goods-list-modal
            :params="{
              is_online: createOrderShowOfflineGoods ? '' : 'Y',
              delivery_date: delivery_date,
              is_show_activity_unit_price: 1,
              query_setting:JSON.stringify(excludeName()),
              is_sell_independent_from_user_id: superCreateOrderUnsellCommodity
            }"
            :uid="user.id"
            :selectedGoods="isEnableAddSameGoods ? [] : newOrderList"
            @on-add="handlerAdd"
            >
          </goods-list-modal>
              <div class="newOrder-operation">
                <span
                  v-if="
                    isEnableGoodsPackage &&
                    commodityPackageMode &&
                    !isEnableAddSameGoods
                  "
                  @click="goodsPackageModalShow"
                  >添加套餐</span
                >

                <span @click="searchSet">录入设置</span>
                <span @click="showImportModal">商品导入</span>
                <ImportButton
                  ref="importBtnRef"
                  modalWidth="800"
                  @beforeUpload="beforeUpload"
                  @on-completed="importCompletedHandel"
                  v-show="false"
                  title="商品导入"
                  :post="importPost"
                  :data="{
                    type: importDownParams.template_type === '1' ? '0' : importDownParams.type,
                    price_mode: importDownParams.price_mode,
                    template_type: importDownParams.template_type,
                    user_id: user.id,
                    is_show_activity_unit_price: '1',
                    delivery_date: delivery_date,
                  }"

                >
                  <template #custom-area>
                    <div class="import-explain">
                      <h6>文本导入说明：</h6>
                      <p>1. * 标识的字段导入模板中必须包含对应列,非必要字段则可没有对应列.</p>
                      <p>2. 导入模板后,将引导设置“模板列”与“订单字段”的对应关系来匹配数据,需要导入的模版列都必须设置对应订单字段,否则将无法导入该列数据.</p>
                      <p>3. 导入文件支持xls、xlsx格式，大小不超过2M，数据不超过200行.</p>
                      <p>4. 导入文件不能包含“合并单元格”,否则无法导入.</p>
                      <p>5. 导入时只要有一行数据不正确,则导入失败.</p>
                      <p>6. 导入填写订单标签时不能填写错误,请务必将同一个订单的商品订单标签保持一致.</p>
                    </div>
                    <div class="import-mode-box">
                      <div class="label">选择导入模板:</div>
                      <div class="ml12">
                        <Select v-model="importDownParams.template_type" style="width: 230px;">
                          <Option value="0">
                            系统模板
                          </Option>
                          <Option value="1">
                            客户常用商品
                          </Option>
                        </Select>
                      </div>
                    </div>
                    <template v-if="importDownParams.template_type == '0'">
                      <div class="import-mode-box">
                        <div class="label">导入模式(商品):</div>
                        <div class="ml12">
                          <RadioGroup v-model="importDownParams.type">
                            <Radio label="0">商品编码</Radio>
                            <Radio label="1">商品名称</Radio>
                            <Radio v-if="!isAliasAllowRepeat" label="2">商品别名</Radio>
                          </RadioGroup>
                        </div>
                      </div>
                      <div class="import-mode-box">
                        <div class="label">价格:</div>
                        <div class="ml12">
                          <RadioGroup v-model="importDownParams.price_mode">
                            <Radio label="0">保持不变</Radio>
                            <Radio v-if="hasAuthority('A002001012')" label="1">导入时修改</Radio>
                          </RadioGroup>
                        </div>
                      </div>
                      <div class="import-mode-box">
                        <div class="label">下载模版:</div>
                        <div class="ml12">
                          <span class="bold">点击下载
                              <span class="ml16" style="color: #03ac54; cursor: pointer;" @click="exportOrderTemplate">
                                订单商品导入模版
                              </span>
                            </span
                          >
                        </div>
                      </div>

                    </template>

                  </template>
                  <template slot="custom-error-tip" slot-scope="{ errorTable }">
                    <p v-show="importError" style="margin: -20px 0 0 130px; color: #f33333;">
                      {{importError}}
                    </p>
                    <Table
                      v-show="errorTable.length>0"
                      :border="false"
                      outerBorder
                      class="mt15"
                      :columns="importCol"
                      :data="errorTable"
                      height="300"
                      max-height="300"
                    >
                    </Table>
                  </template>
                  <span>导入</span>
                </ImportButton>
                <span @click="activeUserHandmade">智能录单</span>
                <span @click="activeHistoryAddOrder">从历史订单复制新增</span>
              </div>
            </template>
            <template #after-table-right>
              <div class="newOrder-amount">
                <span class="c6">下单数量：</span>
                <span class="c6 mr10">{{ orderTotalNum }}</span>
                <span>合计金额：¥</span>
                <span class="newOrder-amount-total">{{ calTotal || 0 }}</span>
              </div>
            </template>
          </EditableTable>
          <div
            v-show="isOpenOrderCombine && hasStepPricingGoods"
            class="text--error"
            style="padding: 10px 0 0 22px"
          >
            当前订单包含阶梯定价商品，不支持合并订单。
          </div>
        </base-block>

        <base-block title="其他信息" v-show="isCanOrder">
          <div class="newOrder-remarks">
            <FormItem label="订单备注" style="width: 100%">
              <Input
                style="width: 418px"
                v-model="remarks"
                type="textarea"
                :maxlength="512"
                show-word-limit
                placeholder="输入订单备注"
              ></Input>
            </FormItem>

            <FormItem label="附件" style="margin-top: 10px; width: 100%">
              <AttachmentUpload v-model="attachmentFiles" />
            </FormItem>
          </div>
        </base-block>
      </Form>
      <template #button-between>
        <Button
          type="primary"
          ghost
          :disabled="disabledEverything"
          @click="showNewOrderReview"
          >核对订单</Button
        >
      </template>
      <template #button-after>
        <Button
          type="primary"
          ghost
          :disabled="createDisabled || disabledEverything"
          @click="createOrderBefore(2)"
          >保存并新增</Button
        >
        <Button
          v-if="isOpenSyncProtocolIsAddCommodity && isEnableOrderSyncContractPrice && hasAuthority('A002001003001')"
          type="primary"
          ghost
          :disabled="createDisabled || disabledEverything"
          @click="createOrderBefore(1, {sync_protocol: 1})"
          >保存并同步协议单</Button
        >
      </template>
    </DetailPage>

    <userHandMade
      v-if="userHandmadeActive"
      :userId="user.id"
      :deliveryDate="delivery_date"
      @addRecgData="addUserHandmade"
      @addOrder="handlerAdd"
      @close="activeUserHandmade"
    ></userHandMade>

    <history
      :user="user"
      :goods="newOrderList"
      v-if="historyAddOrderActive"
      @close="activeHistoryAddOrder"
      @history="addHistoryGoods"
    ></history>

    <Modal
      v-model="newOrderReviewActive"
      title="核对订单"
      width="1000"
      :mask-closable="false"
      :closable="false"
    >
      <i-table
        highlight-row
        :columns="checkColumns"
        :data="checkOrderList"
      ></i-table>
      <div slot="footer">
        <Button @click="newOrderReviewActive = false">取 消</Button>
        <Button type="primary" @click="confirmReview">确 认</Button>
      </div>
    </Modal>
    <Modal
      v-model="isShowSet"
      title="录入设置"
      @on-ok="confirmSet"
      @on-cancel="isShowSet = false"
    >   <Form
      ref="form"
      :label-width="140"
    >
         <FormItem label="商品条码精准匹配：" style="margin-bottom:0px;">
          <Switch
          v-model="isRightMatch"
        />
      </FormItem>
      <div  class="tips_line" style="padding-bottom:10px;">开启后，仅支持条码精准匹配，需要输入完整条码才能录入。配置将影响订单新增、编辑、核算、追加修改页面</div>
        <FormItem v-if="!isRightMatch" label="商品模糊匹配：" style="margin-bottom:0px;">
        <CheckboxGroup class="checkbox" v-model="goodNameSearch">
          <Checkbox label="commodity_code">商品编码</Checkbox>
          <Checkbox label="bar_code">商品条码</Checkbox>
        </CheckboxGroup>
      </FormItem>
    <span v-if="!isRightMatch" class="tips_line">配置将影响订单新增、编辑、核算、追加修改页面</span>
    </Form>
    </Modal>

    <GoodsPackageModal
      ref="goodsPackageModal"
      :show-mode-filter="true"
      :show="goodsPackageModal.show"
      :columns="goodsPackageModal.columns"
      :defaultValue="true"
      @on-cancel="$_closeGoodsPackageModal"
      @on-ok="$_onSelectGoodsPackage"
    />
    <AgreementPriceSelectModal
      :show="agreementPriceSelectModal.show"
      :tableData="agreementPriceSelectModal.data"
      @confirm="agreementPriceSelectModal.confirm"
      @cancel="agreementPriceSelectModal.cancel"
    >

    </AgreementPriceSelectModal>

    <pickUpModal :selectId="pickInfo.id" ref="pickUpModal" @change="handleChangePick"></pickUpModal>
  </div>
</template>

<script>
import DetailPage from '@/components/detail-page/index.js';
import SuborganizationCascader from '@/components/common/SuborganizationCascader.vue';
import MealtimeSelect from '@/components/common/mealTimeSelect.vue';
import EditableTable from '@/components/editable-table-v2/index.js';
import Table from '@/components/table';
import CommoditySelect from '@/components/common/CommoditySelect';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue'
import { mapState } from 'vuex';
import GoodsPackageModal from '@/components/packageGoods/PackageGoodsModal.vue';
import NumberInput from '@/components/basic/NumberInput.vue';
import history from '@/components/order/NewOrderHistory.vue';
import userHandMade from '@/components/order/NewOrderUserHandMade.vue';
import GoodsListModal from '@/components/order/goodsListModal.vue';
import StepPricingPoptip from './components/StepPricingPoptip.vue';
import { debounce } from 'lodash-es';
import common from '@/api/order.js';
import Goods from '@/api/goods.js';
import '@/assets/scss/mixin.scss';
import SIcon from '@/components/icon';
import DateUtil from '@/util/date.js';
import HeaderFilter from './components/header-filter.vue';
import { uniqueId } from 'lodash-es';
import LayoutMixin from '@/mixins/layout';
import ConfigMixin from '@/mixins/config.js';
import CalcPrice from './mixins/calcContractPrice'
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import authority from '@/util/authority.js';
import { getEditTableHeight } from '@/util/common';
import StorageUtil from '@util/storage.js';
import AgreementPriceSelectModal from './components/agreementPrice-select-modal/index.vue'
import pickUpModal from './components/pickUpModal.vue'
import { MINE_TYPE } from '@/util/const';
import Tooltip from "@components/base-tooltip";

import coreEfficiencyIndexLogger, {
  INDEX_TYPE,
} from '@/util/coreEfficiencyIndexLogger';
const { hasAuthority } = authority;
const filterList = [
  {
    name: '最近一次进价',
    value: '1',
    tip: '每次入库单审核过后都会更新，但是可以进行手动更改',
  },
  {
    name: '最近一次入库价',
    value: '2',
    tip: '每次入库单审核之后才可以更新',
  },
  {
    name: '库存均价',
    value: '3',
    tip: '现有库存中的库存均价',
  },
  {
    name: '最近一次采购价',
    value: '4',
    tip: '采购单收货之后更新的采购价',
  },
];
const priceTypeMap = {
  '1': 'in_price',
  '2': 'store_in_price',
  '3': 'average_price',
  '4': 'last_receipt_price'
}
const discountRatioKey = 'discount';
const marketPriceKey = 'org_price';
const agreementPriceKey = 'price';

export default {
  name: 'OrderNew',
  components: {
    DetailPage,
    SuborganizationCascader,
    MealtimeSelect,
    EditableTable,
    history,
    userHandMade,
    GoodsListModal,
    GoodsPackageModal,
    AttachmentUpload,
    AgreementPriceSelectModal,
    pickUpModal,
    Table,
    Tooltip,
    SIcon
  },
  mixins: [LayoutMixin, ConfigMixin, CalcPrice],
  data() {
    return {
      isRightMatch:StorageUtil.getLocalStorage('isRightMatch')?StorageUtil.getLocalStorage('isRightMatch'):false,
      goodNameSearch:StorageUtil.getLocalStorage('goodNameSearch')?StorageUtil.getLocalStorage('goodNameSearch'):['commodity_code', 'bar_code'],
      isShowSet:false,
      hasPickUpList: [], // 是否有自提仓库
      pickInfo: {},
      is_can_self_pickup_point: true, // 选择的客户是否支持自提配送
      initData: {
        providers: [],
      },
      splitRule: [
        {
          name: '按供应商拆单',
          value: '1',
        },
        {
          name: '指定订单所属供应商',
          value: '2',
        },
      ],
      orderCustomizeField: [], // 订单自定义字段
      goodsCustomizeField: [], // 订单明细自定义字段
      originUser: {},
      user: {
        id: '',
        uid: '',
        name: '',
        detail: '',
        special_remark: '',
      },
      formData: {
        subsidiary_id: '',
        meal_type: '',
        split_provider_rule: '1',
        split_provider_id: '',
      },
      goodsPackageModal: {
        show: false,
        columns: [
          {
            width: 60,
            type: 'selection',
          },
          {
            title: '图片',
            render: (h, params) => {
              let { row } = params;
              let key = 'pic_url';
              return h('img', {
                style: {
                  width: '40px',
                },
                attrs: {
                  src: row[key],
                },
              });
            },
          },
          {
            title: '套餐名称',
            key: 'name',
          },
          {
            title: '单位',
            key: 'unit',
          },
          {
            title: '描述',
            key: 'summary',
          },
          {
            title: '下单数量',
            render: (h, params) => {
              const { row } = params;
              const key = 'amount';
              return h('NumberInput', {
                props: {
                  value: row.amount,
                },
                on: {
                  'on-change': (value) => {
                    row[key] = value;
                  },
                  'on-blur': () => {
                    const list = this.$refs.goodsPackageModal.getList();
                    list.forEach((item) => {
                      if (item.id === row.id) {
                        item.amount = row.amount;
                      }
                    });
                    this.$refs.goodsPackageModal.setList(list);
                  },
                  'on-click': (e) => {
                    window.event
                      ? (window.event.cancelBubble = true)
                      : e.stopPropagation();
                  },
                },
              });
            },
          },
        ],
      },
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      idGeneratorIndex: 1,
      userMessage: '',
      selectedOrderTag: [],
      orderTagList: [],
      error: {
        user: '',
        delivery_date: '',
        goods: '',
      },
      date: '',
      delivery_date: '',
      delivery_method: '1',
      userHasContractPriceOrder: true,
      showContractPriceTips: false,
      userHandmadeActive: false, // 客户手工单
      historyAddOrderActive: false, //  从历史订单复制新增
      newOrderReviewActive: false,
      userDropDown: false,
      tUser: '', // * 追求最好的用户体验，暂时缓存用户信息
      deliveryTimeList: [],
      selectedDeliveryTime: '',

      userList: [],
      newOrderList: [],
      remarks: '',
      createDisabled: false,
      previousValue: '',
      checkOrderList: [],
      inputCheckOrderList: [],

      checkColumns: [
        {
          type: 'index',
          width: 60,
          align: 'center',
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'center',
        },
        {
          title: '商品单位',
          key: 'unit',
          align: 'center',
        },
        {
          title: '描述',
          key: 'summary',
          align: 'center',
        },
        {
          title: '订购数',
          key: 'amount',
          width: 100,
          align: 'center',
          props: {
            amountArr: '',
          },
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.amount),
                min: 0,
                // precision: 2,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  this.inputCheckOrderList[params.index]['check_amount'] = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
          },
        },
        {
          title: '订购单价（元）',
          key: 'price',
          align: 'center',
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('i-input', {
              props: {
                value: data.remark,
              },
              class: {
                remarks: true,
              },
              nativeOn: {
                click: ($event) => {
                  $event.stopPropagation();
                },
                change: ($event) => {
                  let value = $event.target.value;
                  params.row.remark = value;
                  this.inputCheckOrderList[params.index].remark = value;
                },
              },
            });
          },
        },
      ],
      activeRowIndex: 0,
      goodsColumns: [],
      ifFromCopy: false,
      originCols: [
        {
          type: 'drag',
          width: 50,
          fixed: 'left',
          render: (h, parmas) => {
            return h('Icon', {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer',
              },
              props: {
                size: 13,
              },
            });
          },
        },
        {
          type: 'titleCfg',
          titleType: 'order_edit',
          width: 52,
          align: 'center',
          fixed: 'left',
          key: 'title',
          render: (h, params) => {
            const { row } = params;
            const operation = h('div', [
              h(SIcon, {
                class: 'icon-record-editor',
                props: {
                  icon: 'jian',
                  size: 16,
                },
                on: {
                  click: () => {
                    const index = this.newOrderList.indexOf(row);
                    this._deleteGoods(row, index);
                  },
                },
              }),
              h(SIcon, {
                props: {
                  icon: 'jia1',
                  size: 16,
                },
                class: 'pointer icon-record-editor icon-record-editor--insert',
                on: {
                  click: (event) => {
                    const index = this.newOrderList.indexOf(row);
                    event.stopPropagation();
                    this._addGoods(index);
                  },
                },
              }),
            ]);
            return operation;
          },
        },
        {
          title: '序号',
          align: 'center',
          width: 70,
          key: 'index',
          fixed: 'left',
          render: (h, params) => {
            const template = [];
            const index = this.getRowIndex(params.row);
            // 预售商品
            if (params.row.expected_arrival_date) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 20,
                  },
                  style: {
                    marginRight: '5px',
                    verticalAlign: 'middle',
                  },
                })
              );
            }
            template.push(
              h(
                'span',
                {
                  style: {
                    verticalAlign: 'middle',
                  },
                },
                index + 1
              )
            );
            return template;
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          width: 90,
          fixed: 'left',
          // render: (h, params) => {
          //   const { row } = params
          //   return h('img', {
          //     attrs: {
          //       src: (row.logo || 'https://base-image.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg') + '!40x40'
          //     }
          //   })
          // }
          render: (h, params) => {
            const { row } = params;
            return h('div', { class: 'demo-upload-list' }, [
              <img
                src={
                  (row.logo ||
                    'https://base-image.sdongpo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg') +
                  '!40x40'
                }
              />,
              h(
                'div',
                {
                  class: 'demo-upload-list-cover',
                },
                [
                  h(
                    'Icon',
                    {
                      props: {
                        type: 'ios-eye-outline',
                      },
                      on: {
                        click: () => this.previewImage(row.logo + '!400x400'),
                      },
                    },
                    []
                  ),
                ]
              ),
            ]);
          },
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          width: 236,
          fixed: 'left',
          sortable: true,
          render: (h, params) => {
            const { row } = params;
            const activityIcon = Goods.getActivityIcon(row);
            let iconStyle = {
              color: '#cc4916',
              marginRight: '6px',
              border: '1px solid #cc4916',
              borderRadius: '3px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
            };
            return (
              <div style="display: flex; align-items: center">
                {(row.new && <span class="new-logo">新</span>) ||
                  (row.activity_type_desc && (
                    <img
                      style="height: 18px; padding-right: 10px"
                      src={activityIcon}
                    >
                      {row.activity_type_desc}
                    </img>
                  )) ||
                  (row.unit !== row.unit_sell && (
                    <div style={iconStyle}>多规格</div>
                  ))}
                <CommoditySelect
                  class="commodity-select"
                  commodityName={row.name}
                  params={{
                    is_show_activity_unit_price: 1,
                    user_id: this.user.id,
                    delivery_date: this.delivery_date,
                    is_online: this.createOrderShowOfflineGoods ? '' : 'Y',
                    query_setting: this.excludeName()
                  }}
                  dataProvider={common.getCommodity}
                  commodityIdKey="commodity_id"
                  commodityNameKey="commodity_name"
                  onOn-focus={() => this.isUserInput()}
                  onOn-change={(cid, com) =>
                    this._setCommodity(cid, com, row, this.getRowIndex(row))
                  }
                  onCreateGood={
                    (newGood) => this._setCommodity(newGood.commodity_id, newGood, row, this.getRowIndex(row))
                  }
                  onOn-enter={() => this._addGoods(this.getRowIndex(row))}
                  style={{ width: '200px' }}
                  slot-type="order-is_online"
                  selectType="table"
                  isOpenCreated={true}
                ></CommoditySelect>
              </div>
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 140,
        },
        {
          title: '条形码',
          key: 'barcode'
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
          render: (h, params) => {
            const {
              row: { category_name, category_name2, category_name3 },
            } = params;
            return (
              <span>
                {category_name
                  ? `
                    ${category_name}
                    ${
                      category_name2 ? `/${category_name2}` : ''
                    }
                    ${category_name3 ? `/${category_name3}` : ''}
                    `
                  : '--'}
              </span>
            );
          },
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'center',
          minWidth: 120,
        },
        {
          title: '单位',
          key: 'unit',
          align: 'center',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120
        },
        {
          title: '现有库存',
          key: 'stock',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              row.stock ? row.stock + ' ' + row.unit_sell : '--'
            );
          },
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'right',
        },
        {
          title: '下单数量',
          key: 'order_amount',
          align: 'left',
          width: 140,
          sortable: true,
          style: {
            paddingRight: 0,
          },
          render: (h, params) => {
            const { row } = params;
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0;
            // 重复渲染问题先
            /*
            this.newOrderList.forEach((item) => {
              if (row.commodity_id === item.commodity_id) {
                otherAmount += Number(item.amount);
              }
            });
            */
            let template = h('div', {}, [
              h(
                'div',
                {
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                  },
                },
                [
                  h(NumberInput, {
                    class: [
                      'order-amount-input',
                      row.amount_warning && 'amount_warning',
                      +row.amount === 0 && 'emphasis-tip',
                    ],
                    props: {
                      value: Number(row.amount),
                      min: 0,
                      precision: 4,
                    },
                    style: {
                      maxWidth: '80px',
                      minWidth: '50px',
                    },
                    on: {
                      'on-focus': () => {
                        row.amount_warning = false;
                      },
                      'on-change': (val) => {
                        row.amount = val;
                        this.handleChangeNum(row);
                        this._updateStepPricing(row); // 更新阶梯定价价格
                      },
                      'on-enter': () => {
                        const index = this.getRowIndex(row);
                        this._addGoods(index);
                      },
                    },
                  }),
                  this.isStepPricingGoods(row) ? (
                    <StepPricingPoptip goods={row}></StepPricingPoptip>
                  ) : null,
                ]
              ),
              h('div', {
                style: { color: '#ff6e00', fontSize: '12px' },
                class: { dn: +row.amount !== 0 }
              }, '请注意特殊下单数量！'),
              h(
                'span',
                {
                  class: {
                    dn:
                      !row.is_sell_stock_alert ||
                      otherAmount <= Number(row.sell_stock),
                  },
                  style: {
                    color: 'red',
                  },
                },
                '库存不足'
              ),
            ]);
            return template;
          },
        },
        {
          title: '实收状态',
          width: 200,
          key: 'un_confirm_receive',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            let selectItem = [
              {
                label: '待实收',
                value: 1,
              },
              {
                label: '已实收',
                value: 0,
              },
            ];
            return h(
              'Select',
              {
                props: {
                  value: row.un_confirm_receive,
                  transfer: true,
                },
                on: {
                  'on-change': (val) => {
                      row.un_confirm_receive = val;
                  },
                },
              },
              selectItem.map((item) => {
                return h('Option', {
                  props: {
                    value: item.value,
                    label: item.label,
                  },
                });
              })
            );
          },
        },
        {
          title: '下单单价',
          key: 'unit_price',
          align: 'left',
          width: 160,
          render: (h, params) => {
            const { row } = params;
            const key = 'price';
            const protocol = h(SIcon, {
              class: '',
              props: {
                icon: 'xie',
                size: 16,
              },
            });
            const tip = h(
              'p',
              {
                style: {
                  color: 'red',
                  fontSize: '12px',
                },
              },
              '低于最近一次进货价！'
            );
            const showWaring = +row.in_price <= 0 && +row[key] === 0
            const warningTip = h('div', {
              style: { color: '#ff6e00', fontSize: '12px' },
            }, '请注意特殊下单单价！')
            let template = [];
            if (Goods.isDiscountGoods(row)) {
              const discountIcon = h(SIcon, {
                class: 'mr5',
                props: {
                  icon: 'zhe',
                  size: 16,
                },
                style: "color: #ff6600;"
              });
              template.push(discountIcon)
            }
            // if (this.isShowSyncContractPrice) {
            // 下单时改价的权限
            if (this.hasAuthority('A002001012')) {
              if (Goods.isProtocolGoods(row) && !this.showContractPriceTips) {
                protocol.data.class = 'mr5';
                template.push(protocol);
              }
              template.push(
                h(NumberInput, {
                  style: { width: '95px' },
                  class: {
                    'emphasis-tip': showWaring,
                  },
                  props: {
                    value: row[key],
                    precision: 2
                  },
                  on: {
                    'on-change': (value) => {
                      const index = this.getRowIndex(row);
                      row[key] = value;
                      this.computeUserContractPrice(row, agreementPriceKey, 4);
                      this.handleChangeNum(row, index);
                    },
                    'on-enter': () => {
                      const index = this.getRowIndex(row);
                      this._addGoods(index);
                    },
                  },
                })
              );
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              if (showWaring) {
                template.push(warningTip)
              }
              return template;
            } else {
              template = [h('span', row[key] || '--')];
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'ml5';
                template.push(protocol);
              }
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              return h(
                'div',
                {
                  style: {
                    padding: '5px 0',
                  },
                },
                template
              );
            }
          },
        },
        {
          title: '下单小计',
          key: 'sub_total_price',
          align: 'right',
          sortable: true,
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              {
                class: {
                  'total-amount': true,
                },
              },
              (+row.amount).mul(+row.price).toFixed(2)
            );
          },
        },
        {
          title: '订单商品标签',
          key: 'order_commodity_tag',
          render: (h, {row}) => {
            const key = 'order_commodity_tag'
            const getDefaultTag = () => {
              let defaultOption = this.orderGoodsTagList.find(item => item.is_default === '1')
              let tagId = defaultOption ? defaultOption.id : ''
              row[key] = tagId
              return tagId
            }
            return h(mvSelect, {
              attrs: {
                clearable: true,
                placeholder: '请选择',
                transfer: true
              },
              class: {
                'required-tip': (row[key] === '' && this.isOrderCommodityTagRequired)
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal: row[key] !== undefined ? row[key] : getDefaultTag()
              },
              on: {
                'on-change': (tagId) => {
                 row[key] = tagId || ''
                },
                'on-open-change': (isOpen) => {
                  setTimeout(() => {
                    row.isSelectOpen = isOpen
                  }, 100)
                },
                'on-enter-key-up': () => {
                  const index = this.getRowIndex(row);
                  if (!this.newOrderList[index].isSelectOpen) this._addGoods(index)
                },
              }
            })
          }
        },
        {
          width: 100,
          title: '折扣率(%)',
          key: 'discount',
          align: 'right',
          render: (h, params) => {
            let { row } = params;
            const key = 'discount';
            return h(NumberInput, {
              props: {
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  row[key] = value;
                  this.computeUserContractPrice(row, discountRatioKey, 4)
                },
                'on-enter': () => {
                  const index = this.getRowIndex(row);
                  this._addGoods(index);
                },
              },
            });
          },
        },
        {
          width: 110,
          title: '协议市场价',
          key: 'org_price',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            const key = 'org_price';

            return h(NumberInput, {
              props: {
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  row[key] = value;
                  this.computeUserContractPrice(params.row, marketPriceKey);
                },
                'on-enter': () => {
                  const index = this.getRowIndex(row);
                  this._addGoods(index);
                },
              },
            });
          },
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          filterMultiple: false,
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            const defaultFilter = this.priceType;
            return h(HeaderFilter, {
              props: {
                filterList,
                defaultFilter,
              },
              on: {
                'on-change': (_value) => {
                  this.handleChangePriceType(_value);
                },
              },
            });
          },
          render: (h, params) => {
            const { row } = params;
            let key = '';
            switch (this.priceType) {
              case '1':
                key = 'in_price';
                break;
              case '2':
                key = 'store_in_price';
                break;
              case '3':
                key = 'average_price';
                break;
              case '4':
                key = 'last_receipt_price';
                break;
              default:
                break;
            }
            // 根据选择的价格模式显示不同的数据
            return h('span', row[key] || '--');
          },
        },
        {
          title: '参考毛利',
          key: 'reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (params.row.price - params.row[priceTypeMap[this.priceType]]).toFixed(2)
            return h('span', Number.isNaN(profit) ? '0' : profit)
          }
        },
        {
          title: '参考毛利率',
          key: 'reference_profit_rate',
          width: 100,
          render: (h, params) => {
            // 参考毛利率=参考毛利/下单单价*100%
            if (!+params.row.price) return h('span', '0%')
            let profit = (params.row.price - params.row[priceTypeMap[this.priceType]])
            let rate = ((profit / params.row.price) * 100).toFixed(2)
            return h('span', rate + '%')
          }
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          align: 'right',
        },
        {
          width: 80,
          title: '税率',
          key: 'tax_rate_desc',
          sortable: true,
          align: 'right',
          render: (h, params) => h('span', params.row.tax_rate || 0),
        },
        {
          width: 80,
          title: '税额',
          key: 'tax_rate_price',
          sortable: true,
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            let taxRatePrice = +row.tax_rate
              ? (
                  (((row.amount * row.price) / (1 + row.tax_rate / 100)) *
                    Number(row.tax_rate)) /
                  100
                ).toFixed(2)
              : 0;
            return h('span', {}, taxRatePrice.toFixed(2));
          },
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          minWidth: 90,
          render: (h, params) => {
            const { row } = params;
            return h('i-input', {
              props: {
                value: row.remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': () => {
                  const index = this.getRowIndex(row);
                  this.newOrderList[index]['remark'] = row['remark'];
                  this._addGoods(index);
                },
              },
              nativeOn: {
                change: ($event) => {
                  const value = $event.target.value;
                  row.remark = value;
                  console.log('ffff', this.newOrderList, row)
                },
              },
            });
          },
        },
        {
          title: '内部备注',
          key: 'inner_remark',
          align: 'center',
          minWidth: 90,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '内部备注'
            );
          },
          render: (h, params) => {
            const { row } = params;
            return h('i-input', {
              props: {
                value: row.inner_remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': () => {
                  const index = this.getRowIndex(row);
                  this.newOrderList[index]['inner_remark'] =
                    row['inner_remark'];
                  this._addGoods(index);
                },
              },
              nativeOn: {
                change: ($event) => {
                  const value = $event.target.value;
                  row.inner_remark = value;
                },
              },
            });
          },
        },
        {
          title: '客户商品别名',
          key: 'user_commodity_alias_name',
        },
        {
          title: '加单数量',
          key: 'changes_num',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单数量'
            );
          },
          render: (h, params) => {
            const { row } = params;
            let key = 'changes_num';
            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  const index = this.getRowIndex(row);
                  row[key] = value;
                  this.handleChangeNum(row, index);
                },
              },
            });
          },
        },
        {
          title: '加单金额',
          key: 'changes_price',
          minWidth: 140,
          align: 'right',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单金额'
            );
          },
        },
        {
          title: '销售数量',
          key: 'sales_num',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售数量'
            );
          },
        },
        {
          title: '销售单价',
          key: 'sales_unit_price',
          align: 'right',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售单价'
            );
          },
        },
        {
          title: '销售金额',
          key: 'sales_price',
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售金额'
            );
          },
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 100
        },
        {
          title: '菜谱套餐',
          key: 'raw_recipe_package_names'
        },
        {
          title: '实时采购员/供应商',
          key: 'now_agent_provider',
          width: 160,
          tip: '实时读取当前客户对应商品 默认的采购员/供应商信息',
        },
      ],
      priceType:
        window.localStorage.getItem('order_base_price_mode') ||
        filterList[0].value,
      attachmentFiles: [],
      agreementPriceSelectModal: {
        show: false,
        data: [],
        confirm: (selectedId) => {
          this.createOrder(this.addMode, { sync_protocol_id: selectedId, sync_protocol: 1 },)
          this.agreementPriceSelectModal.show = false
        },
        cancel: (selectedId) => {
          this.agreementPriceSelectModal.show = false
        },
      },
      addMode: 1,
      orderGoodsTagList: [],
      importPost: {
        url: '/superAdmin/orderSuper/ImportOrderCommodity',
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xls', 'xlsx']
      },
      importDownParams: {
        template_type: '0',
        type: '0',
        price_mode: '0',
      },
      importCol: [
        {
          title: '错误提示',
          key: 'content',
          width: 200,
          render: (h, params) => {
            var data = params.row;
            return h(
              'span',
              {
                style: {
                  color: 'red',
                  'white-space': 'normal !important'
                }
              },
              data.content
            );
          }
        },
        {
          title: '错误行数',
          key: 'line',
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'commodity_name'
        },
        {
          title: '用户编码',
          key: 'user_code',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.user_name !== '' ? row.user_name : row.user_code
            );
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code'
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '备注',
          key: 'remark'
        }
      ],
      importError: '',
    };
  },
  watch: {
    userHasContractPriceOrder() {
      this.handleSetCols();
    },
  },
  created() {
    // 1220暂时不上 客户推荐
    // if(!this.$route.query.copyId){
    //   this.searchUser()
    // }
    this.changeAgreementPriceKey(agreementPriceKey)
    this.getSearchUser = debounce(this.searchUser, 300);
    this.getDate();
    this.getDeliveryTimeList();
    this.getOrderGoodsTagList();

    this._initColumns();
    //获取列表页复制来的id
    let id = this.$route.query.copyId;
    if (id != undefined) {
      this.getOrderDetail(id);
      this.ifFromCopy = true;
    } else {
      this._initTableData();
      this.getOrderCustomizeFieldKeys();
    }
    // 得到标签配置，是否可以添加多个标签，是否只能添加一个标签
    this.commonService.getConfig().then((config) => {
      const {
        is_open_order_tag_required: must_tag,
        is_open_order_tag_only_one: only_tag,
      } = config;
      this.must_tag = must_tag;
      this.only_tag = only_tag;
    });
  },
  mounted() {
    this.$refs.userInput.focus();
    this.getInitData();
    // 获取自提列表, 是否有数据
    this.getPickUpList();
  },
  beforeRouteLeave(to, from, next) {
    if (
      this.user.id ||
      this.newOrderList.filter((item) => item.commodity_id).length
    ) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: () => next(),
        onCancel: () => {
          next(false);
        },
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  computed: {
    ...mapState({
      isOrderClose: (state) => state.isOrderClose,
    }),
    indexMap() {
      return this.newOrderList.reduce((prev, next, index) => {
        prev[next.id] = index;
        return prev;
      }, {});
    },
    disabledEverything() {
      return this.user.id === '';
    },
    calTotal() {
      return this.newOrderList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add((+next.amount).mul(+next.price)), 0)
        .toFixed(2);
    },
    orderTotalNum() {
      return this.newOrderList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add(next.amount), 0)
        .toFixed(2);
    },
    isShowSyncContractPrice() {
      return (
        this.isEnableOrderSyncContractPrice && this.userHasContractPriceOrder
      );
    },
    isCanOrder() {
      if (!this.user.id) {
        return false;
      }
      // 超出账期允许后台下单
      if (this.OrdersBeyondPaymentDaysAreAllowed) {
        return true;
      }
      // 超出账期不允许后台下单，且当前客户超出了账期
      if (!this.OrdersBeyondPaymentDaysAreAllowed && this.userMessage) {
        return false;
      }
      return true;
    },
    // 是否包含阶梯定价商品
    hasStepPricingGoods() {
      return this.newOrderList.find((goods) => this.isStepPricingGoods(goods));
    },
    // 是否允许添加相同商品
    isEnableAddSameGoods() {
      return (
        this.isAddSameCommoditySplitOrder ||
        this.is_open_order_add_same_commodity
      );
    },
  },
  methods: {
    getRowIndex(row) {
      return this.indexMap[row.id] || 0;
    },
    handleShowPick() {
      this.$refs.pickUpModal.open()
    },
    // 获取自提点列表
    async getPickUpList() {
      const { status, message, data } = await this.$request.get(this.apiUrl.SelfPickupPointList)
      if (status && data && data.length) {
        this.hasPickUpList = data
      }
    },
    handleChangePick(item) {
      this.pickInfo = item
    },
    excludeName() {
      let arr =  ['commodity_code', 'bar_code']
      this.goodNameSearch.forEach(res=>{
        let index = arr.findIndex(ee=>ee==res)
        if(index>-1){
          arr.splice(index,1)
        }
      })
      return {
        query_exclude:arr.join(','),
        only_bar_code:this.isRightMatch
      }
    },
    confirmSet(value) {
      StorageUtil.setLocalStorage('isRightMatch',this.isRightMatch)
      StorageUtil.setLocalStorage('goodNameSearch',this.goodNameSearch)
    },
    searchSet() {
      this.isShowSet = true
    },
    hasAuthority,
    getInitData() {
      common.getPurchaseType({}).then((res) => {
        if (res.status) {
          this.initData = res.data || {};
        }
      });
    },
    previewImage(image, _images, _viewIndex = 0) {
      this.viewImage(image, _viewIndex);
    },
    getEditTableHeight,
    _onDraggableData(data) {
      this.newOrderList = data;
      this.setSortNum()
    },
    async _initColumns() {
      const customFieldKeys = await this.setCustomizeFieldKeys();
      this.originCols = this.originCols.concat(customFieldKeys);
      this.handleSetCols();
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '0,4',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                // 商品自定义字段，只需要展示
                if (+item.customize_type === 0) {
                  return {
                    title: item.name,
                    key: item.key,
                  };
                }
                // 订单自定义字段，需要可编辑
                if (+item.customize_type === 4) {
                  this.goodsCustomizeField.push(item);
                  return {
                    title: item.name,
                    key: item.key,
                    render: (h, params) => {
                      let row = params.row;
                      let index = params.index;
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        on: {
                          'on-enter': () => {
                            this.newOrderList[index][item.key] = row[item.key];
                          },
                        },
                        nativeOn: {
                          change: ($event) => {
                            const value = $event.target.value;
                            this.newOrderList[index][item.key] = value;
                          },
                        },
                      });
                    },
                  };
                }
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
    // 获取订单自定义字段
    getOrderCustomizeFieldKeys() {
      this.$request
        .get(this.apiUrl.customizeFieldKeys, {
          customize_type: '3',
        })
        .then(({ status, data }) => {
          if (status) {
            data = data || [];
            data.forEach((item) => {
              '';
            });
            this.orderCustomizeField = data;
          }
        });
    },
    _initTableData(list = []) {
      if (list.length === 0) {
        this.newOrderList = [];
        this._addGoods(0);
      } else {
        this.newOrderList = list;
      }
    },
    // MARK: 删除商品有bug
    _deleteGoods(row, index) {
      if (this.newOrderList.length > 1) {
        this.newOrderList.splice(index, 1);
        this.infoMessage(row.name + '已删除');
      }
    },
    _addGoods(index) {
      this.newOrderList.splice(index + 1, 0, {
        id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
        commodity_id: '',
        name: '',
        amount: 1,
        price: '',
        un_confirm_receive: 0,
        changes_num: 0,
        changes_price: 0,
        sales_num: 1,
        sales_price: 0,
        _sortNum: index+1
      });
      this._focusGoodsInput(index + 1);
    },
    setSortNum() {
      this.newOrderList.forEach((item, index) => {
        item._sortNum = index
      })
      console.log('_sortNum', this.newOrderList)
    },
    handleSortChange(columns, key, order) {
      console.log('newOrderList-sort-before', this.newOrderList)
      if(key === 'order_amount') { // 特殊字段替换
        key = 'amount'
      }
      if(key === 'tax_rate_desc') { // 特殊字段替换
        key = 'tax_rate'
      }

      this.newOrderList = this.newOrderList.sort((a, b) => {
        console.log('sort-item', a[key])
        if(key === 'sub_total_price') { // 特殊字段替换 表格本身没有这个字段，前端运算展示
          a[key] = (+a.amount).mul(+a.price).toFixed(4)
          b[key] = (+b.amount).mul(+b.price).toFixed(4)
        }
        if(key === 'tax_rate_price') {
          a[key] = +a.tax_rate ? (
              (((a.amount * a.price) / (1 + a.tax_rate / 100)) * Number(a.tax_rate)) / 100
            ).toFixed(2)
            : 0
          b[key] = +b.tax_rate ? (
              (((b.amount * b.price) / (1 + b.tax_rate / 100)) * Number(b.tax_rate)) / 100
            ).toFixed(2)
            : 0
        }
        if(order === 'asc') {
          if(isNaN(a[key])) {
            return a[key].localeCompare(b[key])
          } else {
            return a[key] - b[key]
          }

        } else if (order === 'desc') {
          if(isNaN(a[key])) {
            return b[key].localeCompare(a[key])
          } else {
            return b[key] - a[key]
          }
        } else {
          return  a._sortNum - b._sortNum
        }
      })
    },
    goodsPackageModalShow() {
      this.goodsPackageModal.show = true;
    },
    $_closeGoodsPackageModal() {
      this.goodsPackageModal.show = false;
    },
    dataProcessing(val) {
      const goodsList = [];
      const addGoodsToGoodsList = ({ goods, amount }) => {
        const existGoods = goodsList.find(
          (existGoods) => +existGoods.commodity_id === +goods.commodity_id
        );
        if (!existGoods) {
          goods = this.deepClone(goods);
          goods.amount = Number(amount);
          goodsList.push(goods);
        } else {
          existGoods.amount += Number(amount).toFixed(4);
        }
      };
      val.forEach((ite) => {
        const packageNum = ite.amount || 0;
        ite.item.forEach((vals) => {
          vals.id = vals.commodity_id;
          if (vals.item) {
            vals.item.forEach((ites) => {
              ites.id = ites.commodity_id;
              addGoodsToGoodsList({
                goods: ites,
                amount: (packageNum * vals.num * ites.num).toFixed(4),
              });
            });
          } else {
            addGoodsToGoodsList({
              goods: vals,
              amount: (packageNum * vals.num).toFixed(4),
            });
          }
        });
      });
      let newOrderList = this.deepClone(this.newOrderList);
      if (newOrderList.length === 1 && !newOrderList[0].commodity_id)
        newOrderList = [];
      if (newOrderList.length > 0) {
        newOrderList.forEach((items) => {
          let find = goodsList.find(
            (val) => +items.commodity_id === +val.commodity_id
          );
          if (find) {
            items.amount = (Number(find.amount) + Number(items.amount)).toFixed(
              4
            );
          }
        });
        let arr = [];
        goodsList.forEach((value) => {
          let find = newOrderList.find(
            (ites) => Number(ites.commodity_id) === Number(value.commodity_id)
          );
          if (!find) {
            arr.push(value);
          }
        });
        newOrderList.splice(this.activeRowIndex + 1, 0, ...arr);
        this.activeRowIndex += arr.length;
      } else {
        newOrderList.splice(0, 0, ...goodsList);
        this.activeRowIndex += goodsList.length;
      }
      this.newOrderList = newOrderList;
    },
    async $_onSelectGoodsPackage(value) {
      let commodity_id = [];
      let goodsPackage = this.deepClone(value);
      goodsPackage.forEach((ite) => {
        ite.item.forEach((val) => {
          if (val.commodity_id !== undefined) {
            commodity_id.push(val.commodity_id);
          }
          if (val.item) {
            val.item.forEach((v) => {
              if (v.commodity_id !== undefined) {
                commodity_id.push(v.commodity_id);
              }
            });
          }
        });
      });
      let id = Array.from(new Set(commodity_id));
      let params = {
        user_id: this.user.id,
        commodity_id: id.toString(),
        delivery_date: this.delivery_date,
      };
      let { data, status, message } = await this.$request.get(
        this.apiUrl.orderSuperAjaxGetMultiOrderCommodity,
        params
      );
      if (status) {
        goodsPackage.forEach((ite) => {
          ite.item.forEach((val) => {
            if (val.commodity_id !== undefined) {
              this._newPackageDataProcessing(data, val);
            }
            if (val.item) {
              val.item.forEach((i) => {
                this._newPackageDataProcessing(data, i);
              });
            }
          });
        });
        this.dataProcessing(goodsPackage);
        this.$_closeGoodsPackageModal();
      } else {
        this.modalError(message);
      }
    },
    _newPackageDataProcessing(data, val) {
      let find = data.find((v) => +v.id === +val.commodity_id);
      if (find) {
        val.price = find.price;
        val.logo = find.logo;
        val.stock = find.stock;
        val.discount = find.discount;
        val.org_price = find.org_price;
        val.in_price = find.in_price;
        val.tax_rate = find.tax_rate;
        val.tax_rate_price = find.tax_rate_price;
        val.remark = find.remark;
        val.inner_remark = find.inner_remark;
        val.unit_sell = find.unit_sell;
        val.un_confirm_receive = 0;
      }
      return val;
    },
    handleSetCols() {
      const columns = this.deepClone(this.originCols);

      if (!this.userHasContractPriceOrder) {
        if (columns.some((col) => col.key === 'org_price')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'org_price'),
            1
          );
        }
        if (columns.some((col) => col.key === 'discount')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'discount'),
            1
          );
        }
      }
      console.log('handleSetCols');
      this.goodsColumns = columns;
    },
    // 订单标签限制
    checkTag() {
      if (this.orderTagList.length === 0) return true;

      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单最多存在3个标签');
        return false;
      }

      if (this.only_tag === '1' && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单只能选择一个标签');
        return false;
      }

      if (this.must_tag === '1' && this.selectedOrderTag.length === 0) {
        this.warningMessage('请至少选择一个订单标签');
        return false;
      }
      return true;
    },
    // 获取订单标签列表
    getOrderTagList(user_id) {
      const params = {
        user_id,
      };
      common.qryOrderTagList(params).then((res) => {
        if (+res.status === 1) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = res.data;
            // this.selectedOrderTag中只保留this.orderTagList存在的标签
            this.selectedOrderTag = this.selectedOrderTag.filter((item) => {
              return this.orderTagList.some((tag) => tag.id === item.id);
            });
          }
        }
      });
    },
    handleRowClick(row, index) {
      this.activeRowIndex = index;
    },
    rowClassName(row, index) {
      return index === this.activeRowIndex ? 'sdp-table__tr-highlight' : '';
    },
    resetUser() {
      this.user = {
        id: '',
        uid: '',
        name: '',
        detail: '',
      };
    },
    handleChangeNum(goods) {
      // 数据联动模式(false固定销售金额、true固定销售单价)
      if (this.issue_order_data_contact_way) {
        // 销售数量=下单数量+加单数量
        goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=销售单价*销售数量
        goods.sales_price =
          (+goods.sales_num).mul(+goods.sales_unit_price).toFixed(4);
        let sub_total_price = (+goods.amount).mul(+goods.price);
        // 加单金额=销售金额-下单金额
        goods.changes_price = (
          +goods.sales_price - sub_total_price
        ).toFixed(2);
      } else {
        // 销售数量=下单数量+加单数量
        goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=下单金额+加单金额
        goods.sales_price =
          (+goods.amount).mul(+goods.price).toFixed(4) +
          Number(goods.changes_price);
        // 销售单价=销售金额/销售数量
        goods.sales_unit_price = (
          goods.sales_price / goods.sales_num
        ).toFixed(2);
      }
    },
    isProtocolGoods(goodsInfo) {
      return Goods.isProtocolGoods(goodsInfo);
    },
    // 获取复制的商品详情
    getOrderDetail(id) {
      common.getModifyOrderDetail(id, true).then((res) => {
        if (res.status) {
          var data = res.data.data,
            list = data.commodityList,
            detail = data.order || {};
          this.formData.subsidiary_id = detail.subsidiary_id;
          this.formData.meal_type = detail.meal_type;
          this.formData.split_provider_rule = detail.split_provider_rule || '1';
          this.orderCustomizeField = detail.customize_fields || [];
          list.forEach((e, index) => {
            e.amount = e.order_amount;
            e.changes_price = 0;
            e.changes_num = 0;
            e.sales_num = e.amount;
            e.sales_unit_price = e.price;
            e._sortNum = index
            e.sales_price = (+e.amount).mul(+e.price).toFixed(4);
            if (
              this.isShowSyncContractPrice &&
              this.isEnableUserContractPriceDiscountRatio
            ) {
              e.discount = e.discount || 100;
              if (e.org_price === '') {
                this._computeMarketPrice(e, 4);
              }
            }
            // 复制订单勾选是否同步订单商品价格？ 不需要根据下单数量更新阶梯定价
            if (StorageUtil.getLocalStorage('is_copy_sync_good_price')) {
              e.price = e.unit_price;
            } else {
              this._updateStepPricing(e);
            }
            e.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
          });

          // 复制订单时过滤积分商品
          if (this.ifFromCopy) {
            list = list.filter((goods) => !Goods.isCreditGoods(goods));
          }

          this.user.id = detail.user_id;
          this.user.name = detail.user_name;
          this.user.detail = detail;
          this.user.detail.name = detail.receive_name;
          this.user.detail.tel = detail.receive_tel;
          this.user.special_remark = detail.special_remark;
          this.originUser = this.deepClone(this.user);
          this.remarks = detail.remark;
          this.newOrderList = list;
          this._updateStepPricingOrderAmountColumnWidth();
          this.activeRowIndex = list.length - 1;
          this.userList = [1];
          this.selectedDeliveryTime = detail.delivery_time_id;
          this.checkContractPrice();
          this.getOrderTagList(this.user.id);
        }
      });
    },
    handlerAdd(orders) {
      if (!orders || orders.length<=0) {
        this.warningMessage('没有可下单的商品')
        return
      }
      const removeDuplicatesByCommodityId = (arr1, arr2) => {
        let result = []
        const uniqueIds = (arr) => {
          const uniqueIds = {}; // 辅助对象，用于记录已经出现过的 commodity_id
          return arr.filter(item => {
            if (!uniqueIds[item.commodity_id]) {
              uniqueIds[item.commodity_id] = true; // 将当前的 commodity_id 记录为已出现
              return true; // 保留该元素
            }
            return false; // 删除重复的元素
          });

        }
        arr1 = uniqueIds(arr1)
        result = arr1.filter(item1 => {
          const hasDuplicate = arr2.some(item2 => item2.commodity_id === item1.commodity_id);
          return !hasDuplicate;
        });
        return result
      }
      if (orders) {
        if(!this.is_open_order_add_same_commodity) {
          orders = removeDuplicatesByCommodityId(orders, this.newOrderList)
        }
        console.log('orders---', orders)
        orders.forEach((item) => {
          item.id += uniqueId('$unique-');
          item.un_confirm_receive = 0;
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +item.is_price_type === 2 &&
            +item.price === 0
          ) {
            this.$Message.warning(`${item.name}未设置客户类型价`);
          }
          item.discount = item.discount || 100;
          if (!item.inner_remark) {
            item.inner_remark = '';
          }
          if (item.org_price === '') {
            this._computeMarketPrice(item, 4);
          }
          item.amount_warning = false;
          item.changes_price = 0;
          item.changes_num = 0;
          item.sales_num = item.amount;
          item.sales_unit_price = item.price;
          item.sales_price = (+item.amount).mul(+item.price).toFixed(4);
        });
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(this.activeRowIndex, 1, ...orders);
          this.activeRowIndex += orders.length - 1;
        } else {
          this.newOrderList.splice(this.activeRowIndex + 1, 0, ...orders);
          this.activeRowIndex += orders.length;
        }
        this.setSortNum()
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    // 批量新增订单
    _batchAddGoods() {
      if (this.user.id) {
      } else {
        this.modalError('请选择正确的客户');
      }
    },
    // 检测是否输入用户
    isUserInput() {
      if (!this.user.id) {
        this.modalError('请选择正确的客户');
        this.$refs.userInput.focus();
      }
    },
    handleChangeDate(date) {
      this.delivery_date = date;
      // 协议单按发货日期模式生效
      if (
        this.newOrderList &&
        this.newOrderList.length > 0 &&
        this.isContractPriceModeDelivery
      ) {
        this.$Modal.warning({
          title: '提示',
          content:
            '协议价按发货日期生效时,更改订单发货期日,商品价格以下单时价格为准!',
        });
      }
      this.checkContractPrice();
    },
    // 获取现在的日期
    getDate() {
      let defaultEndTime = DateUtil.getTodayDate();
      const filterConfig = StorageUtil.getLocalStorage('order_list_01_filter_config')
      let endDate = '00:00'
      if(filterConfig && filterConfig.length>0) {
        const deliverDateFilterConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date'
        })
        endDate = deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue
      }
      console.log('localEndDate-----', endDate)
      const isTomorrow = DateUtil.isTimeAfterSixPM(endDate)
      if(isTomorrow) {
        defaultEndTime = DateUtil.getTomorrow()
      }
      this.date = defaultEndTime;
      this.delivery_date = this.date;
    },
    getDeliveryTimeList() {
      common.getDeliveryTimeList().then(({ status, data }) => {
        if (status && data && Array.isArray(data.list)) {
          data.list.forEach((time) => {
            const timeJson = {
              id: time.id,
              timeDu: time.name + ' ' + time.start_time + '-' + time.end_time
            };
            this.deliveryTimeList.push(timeJson);
          });
        }
      });
    },
    setUserScrollTop() {
      const dropdown = document.getElementsByClassName('dropdown-user')[0];
      setTimeout(() => {
        dropdown.scrollTop = 0;
      }, 0);
    },
    userKeyControll() {
      const length = this.userList.length,
        first = this.userList[0].uid,
        last = this.userList[length - 1].uid,
        dropdown = document.getElementsByClassName('dropdown-user')[0];

      document.onkeydown = (event) => {
        if (!this.userDropDown) return;
        let e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        this.user.uid !== '' ? (checkId = this.user.uid) : (checkId = -1);

        // 如果点击向上按钮
        if (e && e.keyCode === 38) {
          if (checkId !== -1 && checkId > first) {
            this.user.uid = this.user.uid - 1;
            // 控制滚动条的滚动，60为单个选项的高度
            if (checkId < last - 1) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 60);
            }
          }
        }

        // 如果点击向下按钮
        if (e && e.keyCode === 40) {
          // 如果是首次点击向下
          if (checkId === -1) {
            this.user.uid = first;
          }
          if (checkId !== -1 && checkId < last) {
            this.user.uid = this.user.uid + 1;
            // 获取下面的内容
            if (checkId > 1) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 60);
            }
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          if (this.user.uid === 0 || this.user.uid) {
            this.user.name = this.userList[this.user.uid].email;
            this.user.detail = this.userList[this.user.uid];
            // 处理左侧栏样式问题
            if (this.user.id) {
              this.$store.state.isOrderClose = 1;
            } else {
              this.$store.state.isOrderClose = '';
            }
          }
        }
      };
    },
    addUserSave(value) {
      let dropdown = document.getElementsByClassName('dropdown-user')[0];
      this.historyAddOrderActive = false;
      // 如果已经选择商品，则提示
      if (this.newOrderList.length && this.newOrderList[0].commodity_id) {
        setTimeout(
          () => {
            this.$Modal.confirm({
              title: '修改客户',
              content: '<p>确定修改客户？</p>',
              onOk: () => {
                this._setUser(value);
                this.formData.subsidiary_id = '';
                this.checkContractPrice();

                // 刷新商品价格
                this.refreshGoodsList();

                // 将搜索出来的商品列表置顶
                // dropdown.scrollTop = 0
                this._focusGoodsInput(0);
                this.getOrderTagList(value.id);
              },
              onCancel: () => {
                this.user = this.deepClone(this.originUser);
              },
            });
          },
          !value.is_cod ? 301 : 0
        );
      } else {
        this._setUser(value);
        this.selectedDeliveryTime = value.delivery_time;

        this.formData.subsidiary_id = '';
        this.formData.meal_type = '';

        // 将搜索出来的商品列表置顶
        dropdown.scrollTop = 0;
        // 处理左侧栏样式问题
        if (this.user.id) {
          this.$store.state.isOrderClose = 1;
        } else {
          this.$store.state.isOrderClose = '';
        }
        this.checkContractPrice();
        this._focusGoodsInput(0);
        this.getOrderTagList(value.id);
      }
    },
    addUser(value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeStart(INDEX_TYPE.createOrder);
      } catch (error) {
        console.log(error);
      }
      // 客户异常提示文案展示
      this.userMessage = '';
      common.accountRemind({ user_id: value.id }).then((res) => {
        if (res.status) {
          this.userMessage = res.message;
        }
      });
      //判断是否货到付款
      if (!value.is_cod) {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>该客户不支持货到付款是否继续下单？</p>',
          onOk: () => {
            setTimeout(() => {
              this.addUserSave(value);
            }, 500);
          },
          onCancel: () => {
            this.user.id = '';
            this.user.uid = '';
            this.user.name = '';
            this.user.detail = '';
            this.user.special_remark = '';
            this.originUser = this.deepClone(this.user);
          },
        });
      } else {
        this.addUserSave(value);
      }
    },
    _setUser(value) {
      this.user.uid = value.uid;
      this.user.id = value.id;
      this.user.name = value.email;
      this.user.detail = value;
      this.originUser = this.deepClone(this.user);
      this.user.special_remark = value.special_remark;

      // 重置先
      this.delivery_method = '1'

      // 当前客户是否支持自提
      this.is_can_self_pickup_point = value.is_can_self_pickup_point
      if (this.hasPickUpList.length) {
        // 当前客户绑定的默认自提点
        const defalutIndex = this.hasPickUpList.findIndex(k => k.id === value.self_pickup_point_id)
        this.pickInfo = this.hasPickUpList[~defalutIndex ? defalutIndex : 0]
      }
    },
    // 获取并设置商品相关数据
    _setCommodity(cid, com, row, index) {
      com.cid = com.commodity_id;
      com.price = com.type_price || '';
      com.discount = com.discount || 100;
      com.org_price = com.org_price || '';

      // 如果已经加入订单列表，则无法再次加入
      const findIndex = this.newOrderList.findIndex(
        (goods) => goods.commodity_id === cid
      );
      if (!this.isEnableAddSameGoods && ~findIndex && findIndex !== index) {
        Object.assign(row, {
          // 清空当前行商品
          id: uniqueId('$unique-'), // 生成唯一key
          commodity_id: '',
          name: '',
          amount: row.amount,
          price: '',
          un_confirm_receive: 0,
        })
        this._focusAmountInput(findIndex);
        return this.warningMessage('商品已存在');
      }
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        return this.errorMessage('请输入客户');
      }
      if (!com.cid) {
        this._focusGoodsInput(index);
        return this.errorMessage('请输入正确的商品');
      }

      common
        .getOrderCommodity(this.user.id, com.cid, this.delivery_date, {
          is_show_activity_unit_price: 1
        })
        .then((res) => {
          const { status, data, message } = res;
          if (status) {
            if (
              !this.userStylePriceIsZeroDefaultMarkPrice &&
              +data.is_price_type === 2 &&
              +data.price === 0
            ) {
              this.warningMessage(`${data.name}未设置客户类型价`);
            }

            if (this.isShowSyncContractPrice) {
              data.org_price = com.org_price;
              data.discount = com.discount;
              data.un_confirm_receive = 0;
              if (this.isEnableUserContractPriceDiscountRatio) {
                data.discount = data.discount || 100;
                if (!Number(data.org_price)) {
                  this._computeMarketPrice(data, 4);
                }
              }
            }
            data.commodity_id = data.id;
            data.amount = row.amount;
            data.amount_warning = false;
            data.changes_num = 0;
            data.changes_price = 0;
            data.sales_unit_price = data.price; // 初始化销售单价为下单单价

            // 可能是重复添加的商品，ID先标记为不一样，提交的时候，再去掉
            // 直接标记，不用判断开关，因为可能有批量添加进来的和单个添加的，都直接标记为唯一值，提交的时候去掉标记即可
            data.id += uniqueId('$unique-');
            data.inner_remark = '';
            data.un_confirm_receive = 0;
            data._sortNum = index;
            this.handleChangeNum(data, index);
            this._updateStepPricing(data);
            Object.assign(row, data)
            this._updateStepPricingOrderAmountColumnWidth();
            this._focusAmountInput(index);
          } else {
            this.modalError(message || '商品不存在', 0);
          }
        });
    },
    searchUser() {
      this.user.id = this.user.uid = ''; // 每次重新搜索必须清空
      const consortium_search_type = this.consortium_pricing_type == 3 || this.platform_4_b ? '' : 1
      common.getUserBySearch(this.user.name, consortium_search_type).then((res) => {
        if (res.status) {
          var data = res.data;
          this.userList = data;
          // 加上排序
          for (let i = 0; i < this.userList.length; i++) {
            if (!this.userList[i].uid) {
              this.userList[i].uid = i;
            }
          }

          if (data && data.length) {
            this.userDropDown = true;
            this.$refs.userInput.focus()
            this.userKeyControll();
          } else {
            this.userDropDown = false;
          }
          let firstUser = data[0] || {};
          // 下拉列表默认选中第一个用户
          this.user.default_item = firstUser;
          this.user.uid = firstUser.uid;
          this.setUserScrollTop();
        }
      });
    },
    // 将历史订单中的商品加入到订单商品列表中
    addHistoryGoods(value) {
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        this.modalError('请输入客户');
        return;
      }
      if (!value || !value.length) {
        this.modalError('请选择正确的历史订单商品');
        return;
      }

      if (
        this.isShowSyncContractPrice &&
        this.isEnableUserContractPriceDiscountRatio
      ) {
        value.forEach((item) => {
          item.price = item.curr_price;
          item.discount = item.discount || 100;
          if (item.org_price === '') {
            this._computeMarketPrice(item, 4);
          }
        });
      }
      value.forEach((item) => {
        item.id += uniqueId('$unique-');
        item.un_confirm_receive = 0;
        item.changes_price = 0;
        item.changes_num = 0;
        item.sales_num = item.amount;
        item.sales_unit_price = item.price;
        item.sales_price = (+item.amount).mul(+item.price).toFixed(4);
      });
      // 清空已经存在的订单商品列表
      this.newOrderList = [];
      this.newOrderList = value.concat(this.newOrderList);
    },
    addUserHandmade(value) {
      value.commodity_arr.forEach((e) => {
        (e.amount_pic = ''), (e.commodity_id = e.id);
      });

      if (!this.user.id) {
        this.user.id = value.user_info.id;
        this.user.detail = {
          name: value.user_info.name,
          tel: value.user_info.tel,
          address_detail: value.user_info.address_detail,
        };
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(
            this.activeRowIndex,
            1,
            ...value.commodity_arr
          );
          this.activeRowIndex += value.commodity_arr.length - 1;
        } else {
          this.newOrderList.splice(
            this.activeRowIndex + 1,
            0,
            ...value.commodity_arr
          );
          this.activeRowIndex += value.commodity_arr.length;
        }
        this._updateStepPricingOrderAmountColumnWidth();
        return;
      }

      if (this.user.id == value.user_info.id) {
        value.commodity_arr.forEach((e) => {
          this.newOrderList.forEach((se) => {
            if (e.id == se.id) {
              se.amount = parseFloat(se.amount) + parseFloat(e.amount);
            }
          });
        });

        let diffArr = this.diff(value.commodity_arr, this.newOrderList);
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(this.activeRowIndex, 1, ...diffArr);
          this.activeRowIndex += diffArr.length - 1;
        } else {
          this.newOrderList.splice(this.activeRowIndex + 1, 0, ...diffArr);
          this.activeRowIndex += diffArr.length;
        }
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    //复合数组求差集
    diff(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) {
        return false;
      }
      let result = a;
      for (let i = 0; i < b.length; i++) {
        result = result.filter((e) => e.id != b[i].id);
      }

      return result;
    },
    // 按enter之后跳转到列表第一个商品输入框
    enterUser() {
      this.historyAddOrderActive = false;
      this.user.uid !== '' && this.addUser(this.userList[this.user.uid]);
    },
    checkContractPrice() {
      // 没有启用订单同步客户协议价
      this.showContractPriceTips = false;
      if (!this.isEnableOrderSyncContractPrice) {
        return false;
      }
      common
        .checkValidUserContractPriceOrder(this.user.id, this.delivery_date)
        .then((res) => {
          let { data } = res;
          if (!data) {
            this.userHasContractPriceOrder = false;
            this.showContractPriceTips = true;
          } else {
            this.userHasContractPriceOrder = true;
          }
        });
    },
    selectAgreementPriceOrder(value, extraParams) {
      common
        .checkValidUserContractPriceOrder(this.user.id, this.delivery_date)
        .then((res) => {
          let { status, message, data } = res;
          if(status) {
            const notAgreementPriceGoods =  this.newOrderList.filter( item => !Goods.isProtocolGoods(item))
            if(
              (Array.isArray(data) && data.length>1) &&
              notAgreementPriceGoods.length > 0 &&
              this.isOpenSyncProtocolIsAddCommodity
            ) {
              let repeatGoods = [] // 非协议价商品 - 重复商品数组
              let sameRepeatGoods = [] // 下单单价、折扣率（订）和协议市场价（订）一样的商品数组
              for (let index = 0; index < notAgreementPriceGoods.length-1; index++) {
                const item = notAgreementPriceGoods[index]
                for (let index2 = index+1; index2 < notAgreementPriceGoods.length; index2++) {
                  const item2 = notAgreementPriceGoods[index2];
                  if(
                    item.commodity_id === item2.commodity_id &&
                    repeatGoods.indexOf(item) === -1
                  ) {
                    repeatGoods.push(item)
                    repeatGoods.push(item2)
                    if(
                      +item.price === +item2.price &&
                      +item.org_price === +item2.org_price &&
                      +item.discount === +item2.discount &&
                      sameRepeatGoods.indexOf(item2) === -1
                    ) {
                      sameRepeatGoods.push(item2)
                    }
                  }
                }
              }
              console.log('repeatGoods-notAgreementPriceGoods', notAgreementPriceGoods)
              console.log('repeatGoods', repeatGoods)
              console.log('repeatGoods-sameRepeatGoods', sameRepeatGoods)
              if(
                repeatGoods.length > 0 &&
                sameRepeatGoods.length > 0 ||
                repeatGoods.length < notAgreementPriceGoods.length
              ) {
                this.agreementPriceSelectModal.show = true
                this.agreementPriceSelectModal.data = data
              } else {
                this.createOrder(value, extraParams);
              }
            } else {
              this.createOrder(value, extraParams);
            }
          } else {
            this.$smessage({ type: 'error', text: message });
            this.createOrder(value, extraParams);
          }

        });
    },
    _focusGoodsInput(index) {
      this.activeRowIndex = index;
      this.$nextTick(() => {
        const commodityInput =
          document.getElementsByClassName('commodity-select')[index];
        commodityInput && commodityInput.querySelector('input').focus();
      });
    },
    _focusAmountInput(index) {
      setTimeout(() => {
        const $currentRow =
          this.$refs.orderGoodsTable.$el.querySelectorAll('tbody tr')[index];
        $currentRow
          .querySelector('.order-amount-input')
          .querySelector('input')
          .focus();
      });
    },
    showNewOrderReview() {
      // 有商品才能够打开订单核价
      if (!this.newOrderList.length) {
        this.modalError('您还未选择任何商品');
        return;
      }
      let newList = this.deepClone(
        this.newOrderList.filter((goods) => goods.commodity_id).map(item => ({
          ...item,
          _rowParams: undefined
        }))
      );
      newList.forEach((item) => {
        item.check_amount = parseFloat(item.amount);
      });
      this.checkOrderList = newList;
      this.inputCheckOrderList = this.deepClone(newList);
      this.newOrderReviewActive = true;
    },
    /**
     * 刷新商品列表
     */
    refreshGoodsList() {
      let params = {
        user_id: this.user.id,
        commodity_type: 1,
        commodity_string: '',
        showAll: true,
        page: 1,
        pageSize: 99999,
        is_sell_independent_from_user_id: this.superCreateOrderUnsellCommodity
      };
      let goodsIdArr = this.newOrderList.map((goods) => goods.id);
      params.commodity_string = JSON.stringify(goodsIdArr);
      this.$request.post(this.apiUrl.getGoodsList, params).then((res) => {
        let { status, data } = res;
        if (status) {
          let newOrderList = this.newOrderList.filter((goods) =>
            data.list.find(
              (retGoods) => goods.commodity_id === retGoods.commodity_id
            )
          );
          newOrderList.forEach((goods) => {
            let retGoods = data.list.find(
              (retGoods) => goods.commodity_id === retGoods.commodity_id
            );
            if (goods) {
              goods.price = retGoods.price;
              // 刷新相关价格
              goods.in_price = retGoods.in_price;
              goods.store_in_price = retGoods.store_in_price;
              goods.average_price = retGoods.average_price;
              goods.last_receipt_price = retGoods.last_receipt_price;
              goods.is_price_type = retGoods.is_price_type; // 更新价格类型
            }
          });
          // this.newOrderList = newOrderList
          this._initTableData(newOrderList);
        }
      });
    },
    confirmReview() {
      this.inputCheckOrderList.map((checkGoods) => {
        this.newOrderList.forEach((goods) => {
          if (checkGoods.id == goods.id) {
            goods.amount = checkGoods.check_amount;
            goods.remark = checkGoods.remark;
            this._updateStepPricing(goods);
          }
        });
      });
      this.newOrderReviewActive = false;
    },
    createOrderBefore(value, extraParams) {
      this.addMode = value
      this.selectAgreementPriceOrder(value, extraParams)
    },
    createOrder(value, extraParams) {
      console.log('addMode', value)
      if (!this.checkData()) {
        return false;
      }

      if (!this.date) {
        this.modalError('请选择日期');
        return;
      }
      // 转换中国时区
      let date = new Date(this.date.valueOf() + 28800000);
      date = date.toISOString().slice(0, 10);

      let userId = this.user.id,
        remarks = this.remarks,
        selectedDeliveryTime = this.selectedDeliveryTime;

      !selectedDeliveryTime && (selectedDeliveryTime = 0);

      let commodityList = this.deepClone(
        this.newOrderList.filter((goods) => goods.commodity_id)
      );
      if (!commodityList.length) {
        this.errorNotice('请先选择商品再创建订单');
        return;
      }

      commodityList = commodityList.map((item, index) => {
        let result = {
          sort_num: index,
          commodity_id: item.id
            ? item.id.replace(/\$unique-.*$/, '')
            : item.commodity_id,
          amount: item.amount,
          remark: item.remark,
          inner_remark: item.inner_remark,
          price: item.price,
          mutate_price: item.price,
          in_price: item.in_price,
          org_price: item.org_price,
          discount: item.discount,
          un_confirm_receive: item.un_confirm_receive,
          changes_num: item.changes_num,
          changes_price: item.changes_price,
          sales_unit_price: item.sales_unit_price,
          order_commodity_tag: item.order_commodity_tag,
        };
        // 订单自定义字段
        result.customize_fields = this.goodsCustomizeField.map((field) => {
          return {
            key: field.key,
            name: field.name,
            value: item[field.key],
          };
        });
        // result.customize_fields = JSON.stringify(result.customize_fields);
        return result;
      });

      let orderParams = {
        user_id: userId,
        commodity_list: commodityList,
        delivery_date: date,
        delivery_method: this.delivery_method,
        self_pickup_point_id: this.pickInfo.id,
        remark: remarks,
        delivery_time: selectedDeliveryTime,
        tag_ids: JSON.stringify(this.selectedOrderTag),
        ...extraParams,
        ...this.formData,
      };
      orderParams.commodity_list = JSON.stringify(orderParams.commodity_list);
      // 订单自定义字段
      orderParams.customize_fields = JSON.stringify(this.orderCustomizeField);
      orderParams.attachment_link = this.attachmentFiles
        .map((item) => item.url)
        .join(',');

      //判断是否开启订单合并
      if (this.isOpenOrderCombine && !this.hasStepPricingGoods) {
        if (this.createDisabled) {
          return false;
        }
        this.createDisabled = true;
        //检查订单是否满足合并条件
        common
          .orderCombineCheck(orderParams)
          .then((res) => {
            const { status, message, data } = res;
            const { can_order_combine, combine } = data;
            if (status) {
              //1.满足合并条件，提示用户是否进行合并
              if (can_order_combine) {
                this.$Modal.confirm({
                  title: '确定要合并到之前的订单吗？',
                  content: `<div>
                  <p>订单号:${combine.order_no}</p>
                  <p>下单时间:${combine.create_time}</p>
                  <p>发货日期:${combine.delivery_date}</p>
                  <p>注意:如果已经生成采购单或者已经分拣,请谨慎操作</p>
                </div>`,
                  onOk: () => {
                    orderParams.is_order_combine = 1;
                    orderParams.order_no = combine.order_no;
                    this.orderCombine(orderParams, value, true);
                  },
                  onCancel: () => {
                    orderParams.is_order_combine = 0;
                    orderParams.order_no = combine.order_no;
                    this.orderCombine(orderParams, value, false);
                  },
                });
              } else {
                //2.不满足合并条件，后台已默认创建订单，不用手动创建，这里需要清除前端信息
                this.clearInfo(value);
              }
            } else {
              this.errorNotice(message);
            }
          })
          .finally(() => {
            this.createDisabled = false;
          });
      } else {
        //未开启订单合并，正常提交
        if (this.createDisabled) {
          return false;
        }
        this.confirmAddOrder(orderParams, value);
      }
    },
    /**
     * 确认提交订单
     * @param(params) 提交参数
     */
    confirmAddOrder(params, value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeEnd(INDEX_TYPE.createOrder, {
          goods_count: JSON.parse(params.commodity_list).length,
          page: location.href,
        });
      } catch (error) {
        console.log(error);
      }
      this.createDisabled = true;
      common
        .createOrderV2(params)
        .then((res) => {
          const { status, message } = res;
          if (status) {
            this.successMessage(message || '订单创建成功');
            this.clearInfo(value);
          } else {
            this.errorNotice({
              title: '订单创建失败!',
              desc: message || '订单创建失败！',
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    /**
     * 订单提交之后，清除信息
     */
    clearInfo(value) {
      this._initTableData();
      this.newOrderReviewActive = false;
      this.user = { id: '', uid: '', name: '', detail: '' };
      this.$refs.userInput && this.$refs.userInput.focus();

      this.remarks = '';
      this.$store.state.isOrderClose = '';
      this.selectedOrderTag = [];
      if (value === 1) {
        this.back();
      } else if (value === 2) {
        console.log(value);
      } else {
        this.back();
      }
    },
    /**
     * 进行订单合并
     * @param (params) 请求参数
     * @param (value) 区分是哪个点击
     * @param (ifCombine) 是否进行合并
     */
    orderCombine(params, value, ifCombine) {
      if (this.createDisabled) {
        return false;
      }
      this.createDisabled = true;
      common
        .orderCombine(params)
        .then((res) => {
          let { status, message } = res;
          this.createDisabled = false;
          message = message
            ? message
            : ifCombine
            ? '订单合并成功'
            : '订单创建成功';
          if (status) {
            this.successMessage(message);
            this.clearInfo(value);
          } else {
            this.errorNotice({
              title: '订单合并失败!',
              desc: message || '订单合并失败！',
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    checkGoodsAmount(goods) {
      if (!goods.amount && Number(goods.amount) !== 0) {
        return false;
      }
      return true;
    },
    resetError() {
      this.error = {
        user: '',
        delivery_date: '',
        goods: '',
      };
    },
    resetUserError() {
      this.error.user = '';
    },
    /**
     * @param {scrollToError} options
     * @returns {boolean}
     */
    checkBaseData(options = {}) {
      let { scrollToError } = options;
      let valid = true;
      let firstErrorDom = null;

      if (!this.user.id) {
        valid = false;
        this.error.user = '请选择客户';
        firstErrorDom = this.$refs['error.user'].$el;
      } else {
        this.error.user = '';
      }
      if (!this.date) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.delivery_date = '请选择发货日期';
      } else {
        this.error.delivery_date = '';
      }
      if (firstErrorDom && scrollToError) {
        firstErrorDom.scrollIntoView({ block: 'center' });
      }
      return valid;
    },
    checkData() {
      let valid = true;
      let firstErrorGoodsIndex = null;

      const filterList = this.newOrderList.filter(
        (goods) => goods.commodity_id
      );

      if (filterList.length === 0) {
        // valid = false
        this.errorMessage('请添加商品和数量');
        this._focusGoodsInput(0);
        return false;
      }

      // 检查订单标签设置
      if (!this.checkTag()) {
        valid = false;
      }

      // 检查订单商品订购数量
      filterList.forEach((item, index) => {
        if (!this.checkGoodsAmount(item)) {
          valid = false;
          item.amount_warning = true;
          if (firstErrorGoodsIndex === null) {
            this.errorMessage('请输入订购数量');
            firstErrorGoodsIndex = index;
          }
        } else {
          item.amount_warning = false;
        }
      });

      // 开启订单商品标签必填
      if(this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        for (const [index, good] of filterList.entries()) {
          if(!good.order_commodity_tag) {
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`)
            const scrollBox = this.$refs.orderGoodsTable.$el.querySelector('.sdp-table__content')
            const curElement = this.$refs.orderGoodsTable.$el.querySelector(`[data-key="body_tr_${index}"]`)
            scrollBox.scrollTo(0, curElement.offsetTop)
            return false
          }
        }
      }
      let baseDataValid = this.checkBaseData({
        scrollToError: true,
      });

      if (baseDataValid && firstErrorGoodsIndex !== null) {
        this.$nextTick(() => {
          const $firstRow = this.$refs.orderGoodsTable.$el.querySelector(
            `.editable-table tbody tr:nth-child(${firstErrorGoodsIndex + 1})`
          );
          if ($firstRow) {
            $firstRow.scrollIntoView({ block: 'center' });
          }
        });
      }
      return valid && baseDataValid;
    },
    back() {
      this.router.push({
        path: '/order',
      });
    },
    activeUserHandmade() {
      this.userHandmadeActive = !this.userHandmadeActive;
    },
    activeHistoryAddOrder() {
      if (!this.user.id) {
        this.modalError('请先选择客户');
        return;
      }
      this.historyAddOrderActive = !this.historyAddOrderActive;
    },
    handleChangePriceType(_value) {
      window.localStorage.setItem('order_base_price_mode', _value);
      this.priceType = _value;
      // 更新价格模式配置
      this.commonService.editSingleConfig('order_base_price_mode', _value);
    },
    /**
     * @description: 判断是否为阶梯定价商品
     * @param {*} goods
     */
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，根据商品下单数量，动态计算更新阶梯定价的商品价格
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing(row) {
      const { amount } = row;
      if (this.isStepPricingGoods(row)) {
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true)
        );
        if (stepPriceItem) {
          row.price = stepPriceItem.price;
        }
      }
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      if (this.newOrderList.some((goods) => this.isStepPricingGoods(goods))) {
        const newWidth = 155;
        // 接口请求时间不一定，两个都更新就行
        const origin_order_amount_column = this.originCols.find(
          (item) => item.key === 'order_amount'
        );
        if (origin_order_amount_column) {
          origin_order_amount_column.width = newWidth;
        }
        const order_amount_column = this.goodsColumns.find(
          (item) => item.key === 'order_amount'
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
    getOrderGoodsTagList () {
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data
      })
    },
    showImportModal () {
      this.$refs.importBtnRef.showModal()
      console.log('showImportModal', this.$refs.importBtnRef)
    },
    beforeUpload() {
      this.importError = ''
    },
    importCompletedHandel(status, data) {
      if(status) {
        this.handlerAdd(data)
      } else {
        this.importError = data
      }
    },
    exportOrderTemplate() {
      let url = `/superAdmin/orderSuper/BatchImportOrderCommodityTemplate?type=${this.importDownParams.type}&price_mode=${this.importDownParams.price_mode}`;
      window.location.href = url;
    },
  },
};
</script>

<style lang="less" scoped>
/deep/.demo-upload-list {
  display: inline-block;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  height: 40px;
  width: 40px;
  line-height: 40px;
  background: rgba(0, 0, 0, 0);
  position: relative;
  box-shadow: none;
  margin-right: 0px;
}

/deep/.demo-upload-list img {
  width: auto;
  height: auto;
}
.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}
.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
.dropdown-user,
.dropdown-user-content {
  position: absolute;
  z-index: 199;
  left: 0;
  margin-top: 5px;
  width: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  max-height: 200px !important;
  box-shadow: 1px 2px 1px #ccc;
  border-radius: 2px;

  .dropdown-items {
    width: 100%;
    padding: 5px 10px !important;

    &-strong {
      font-weight: 500;
      font-size: 13px;
      margin-right: 10px;
      line-height: 19.2px;
    }
    &-span {
      font-size: 12px;
      color: #aaa;
    }
    &-p {
      line-height: 20px;
      font-size: 13px;
      color: #aaa;
    }
    &.active {
      p {
        color: #03ac54;
      }
      color: #03ac54;
      background-color: #ebf7ff;
    }
    &:hover {
      cursor: pointer;
      background-color: #ebf7ff;
      p {
        color: #03ac54;
      }
      color: #03ac54;
    }
  }
}

/deep/ .editable-table {
  .sdp-table__cell > div {
    display: block !important;
  }
  .new-logo {
    margin-right: 2px;
    padding: 0 5px;
    color: #fff;
    background-color: #3399ff;
    border-radius: 2px;
  }

  .InternalNote {
    color: #3399ff;
  }

  .order-amount-input.amount_warning input {
    border-color: red;
  }
  .emphasis-tip input {
    color: #ff6e00;
    border-color: currentColor;
  }
  .required-tip .ivu-select-selection {
    border-color: rgb(243, 51, 51);
  }
}

.newOrder-operation {
  align-self: flex-end;
  line-height: 15px;
  color: #03ac54;

  span {
    cursor: pointer;
  }

  span:not(:last-child) {
    margin-right: 10px;
  }
}

.newOrder-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #303030;
  text-align: right;

  &-total {
    color: #ed3f14;
  }
}

.newOrder-remarks {
  width: 100%;
  border: none;
  .ivu-form-item {
    width: 100%;
  }
}
.tips_line{
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 140px;
}
/deep/ .dropdown-empty {
  padding: 10px;
  color: #80848f;
  text-align: center;
}
/deep/ .ivu-poptip-rel {
  line-height: 1;
}
.order-new {
  content-visibility: auto;
}
</style>
