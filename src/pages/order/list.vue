<template>
  <div class="order__list list-padding-left list-padding-right">
    <Tabs
      @on-click="changeOrderMode"
      v-model="filters.modeStatus"
      class="order__list__tab"
    >
      <TabPane label="全部" :name="orderMode.all + ''" :index="1"></TabPane>
      <!-- v-if="is_order_audit" -->
      <TabPane
        label="待审核"
        :name="orderMode.unaudit + ''"
        :index="2"
      >
      </TabPane>
      <TabPane
        label="待发货"
        :name="orderMode.undelivery + ''"
        :index="3"
      ></TabPane>
      <TabPane
        label="待收货"
        :name="orderMode.unreceived + ''"
        :index="4"
      ></TabPane>
      <TabPane
        label="已完成"
        :name="orderMode.completed + ''"
        :index="5"
      ></TabPane>
      <TabPane
        v-if="hasAuthority('A002001014')"
        label="已关闭"
        :name="orderMode.closed + ''"
        :index="6"
      ></TabPane>
      <TabPane
        v-if="sysConfig.group_order_audit == 1"
        label="待集团审核"
        :name="orderMode.groupUnaudit + ''"
        :index="7"
      ></TabPane>
    </Tabs>
    <ListTable
      ref="orderListTable"
      :sumConfig="{ title: '合计金额', data: sum }"
      :autoLoadData="true"
      :smartOnChange="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :initParams="initParams"
      :filters="filters"
      :border="false"
      :outer-border="true"
      :max-line="2"
      row-key="order_no"
      tableId="order_list_01"
      :before-request="beforeRequest"
      :before-set-data="afterRequest"
      :customGetPageParams="_customGetPageParams"
      :filter-items="filterItems"
      :advance="true"
      :advance-items="advanceItems"
      :columns="columns"
      :data-provider="apiUrl.getOrderList"
      :height="getTableHeightNew()"
      :keepScroll="true"
      maxLine="initial"
      :pageSizeCache="{ suffix: filters.modeStatus }"
      @reset-change="handleResetChange"
      @on-selection-change="handleSelectionChange"
      @filterConfigChange="filterConfigChange"
      @first-data-render="firstTimeDataRendered"
      @on-finish-data-load="handleFinishDataLoad"
      :filterConfig="filterConfig"
      :isOpenCustom="true"
    >
      <template #button>
        <ExportButtonMulti
          :data="exportBtnMultiData"
          v-if="!is_sys_supervisor"
        ></ExportButtonMulti>
      </template>
      <div
        slot="before-table"
        class="order__list__operation"
        v-if="!is_sys_supervisor"
        v-show="selectedOrders.length === 0"
      >
        <Button styleType="btnStyleForAdd" @click="toAdd">新增</Button>
        <Button
          class="ml8"
          v-if="isEnableSpotPurchaseOrder && $store.state.canAddSceneOrder"
          @click="toAddSceneOrder"
          >新增现场采购单</Button
        >
        <Button class="ml8" @click="openImportOrder">导入订单</Button>
        <ImportButton
          v-if="commodityPackageMode"
          title="按套餐导入订单"
          :download="{
            url: apiUrl.packageRecipe.exportTemplate,
            text: '套餐导入模板',
          }"
          :post="{
            url: apiUrl.packageRecipe.import,
            accept: MINE_TYPE.excel.join(','),
            format: ['csv', 'xls', 'xlsx'],
          }"
          class="ml8"
          :modalProps="{
            width: 600,
          }"
          @on-completed="(isSuccess) => isSuccess && this.fetchData()"
        >
          <span>按套餐导入订单</span>
          <template #custom-area>
            <div class="import-explain">
              <h4>导入说明：</h4>
              <p>
                1.<span style="color: #f13130">*</span
                >标识的字段导入模板中必须包含对应的列，非必要字段导入模板中可没有对用列
              </p>
              <p>2.导入文件仅支持xlsx格式，大小不超过2M，数据不超过500行</p>
              <p>3.导入文件不能包含"合并单元格"，否则无法导入</p>
              <p>4.导入时只要有一行数据不正确,则导入失败</p>
            </div>
          </template>
        </ImportButton>
        <Button
          v-if="isEnableOrderListBehalf"
          class="ml8"
          @click="toBehalfOrder"
          >单据管理新增</Button
        >
        <!-- <Button v-if="isNewGoodsPackageRecipe" class="ml10" @click="_toAddRecipeOrder">按食谱下单</Button> -->
        <Button
          v-if="
            is_open_order_add_same_commodity &&
            isOpenOrderCombineNoAuto &&
            hasAuthority('A002001013')
          "
          class="ml8"
          @click="jumpMergeOrder"
          >合并订单</Button
        >

        <template
          v-if="
            isEnableOrderSyncContractPrice &&
            !isClosed &&
            (isAllOrderState ||
              (!isAllOrderState && ['100', '300'].includes(filters.modeStatus)))
          "
        >
          <!-- 待发货 开启刷价 -->
          <template v-if="disSyncAllBtn">
            <Tooltip
              content="订单刷价不支持发货日期筛选超过31天"
              placement="top"
            >
              <SyncAllBtn
                :disabled="disSyncAllBtn"
                class="ml8"
                @expandClick="openRecovers"
                width="90px"
                styleType="primary"
                :isGroup="true"
                :allIdsObj="{
                  ids: '',
                  url: apiUrl.getOrderList,
                  params: {
                    ...getParams(),
                    only_id: 1,
                  },
                }"
                :syncObj="{
                  url: '/superAdmin/OrderSuper/BatchBrushPrice',
                  reqMethod: order.batchBrushPrice,
                  params: {
                    is_brush_price: 1,
                  },
                }"
                @done="() => fetchData()"
              >
                <i
                  slot="tips"
                  class="ml-1 mt-2 mr1 sui-icon icon-tips"
                  style="font-size: 13px; cursor: pointer"
                ></i>
              </SyncAllBtn>
            </Tooltip>
          </template>

          <template v-else>
            <SyncAllBtn
              :disabled="disSyncAllBtn"
              @expandClick="openRecovers"
              class="ml8"
              width="90px"
              :is-group="true"
              styleType="primary"
              :allIdsObj="{
                url: apiUrl.getOrderList,
                params: {
                  ...getParams(),
                  only_id: 1,
                },
              }"
              :syncObj="{
                url: '/superAdmin/OrderSuper/BatchBrushPrice',
                reqMethod: order.batchBrushPrice,
                params: {
                  is_brush_price: 1,
                },
              }"
              @done="() => fetchData()"
            >
              <Tooltip slot="tips" placement="top" :maxWidth="300">
                <div slot="content">
                  <div slot="content" v-html="brushPriceTips"></div>
                </div>
                <i
                  class="ml-1 mt-2 mr1 sui-icon icon-tips"
                  style="font-size: 13px; cursor: pointer"
                ></i>
              </Tooltip>
            </SyncAllBtn>
          </template>
        </template>
        <template v-if="isUnDelivery">
          <template v-if="showBatchAddGoods">
            <Button
              :disabled="!DeliveryTimeOne"
              class="ml10"
              @click="openBatchAddGoods"
              >新增商品</Button
            >
            <Tooltip transfer placement="top" :maxWidth="300">
              <div slot="content" class="add-tips">
                <p class="mb12">批量新增商品时，需要注意以下事项：</p>
                <p>1、新增商品仅支持发货日期为一天的订单</p>
                <p>
                  2、未开启重复商品时，新增的商品与原有订单商品累加，下单单价取原有订单商品的下单单价，备注和内部备注用【；】间隔。
                </p>
                <p>
                  3、开启重复商品时，新增商品取新增录入的价格；未开启重复商品时，逻辑与第1点相同。
                </p>
                <p>4、客户不可售卖的商品新增时会自动过滤</p>
              </div>
              <i
                class="ml8 sui-icon icon-tips"
                style="font-size: 13px; cursor: pointer"
              ></i>
            </Tooltip>
          </template>
        </template>
        <Button
          class="ml8"
          @click="handleGetOrders"
          v-if="
            hasAuthority('A002001019') && traceability_platform.includes('2')
          "
          >获取校园食材订单</Button
        >
      </div>
      <div class="flex-con" slot="batch-operation">
        <Row type="flex" justify="space-between">
          <Col>
            <Dropdown
              trigger="click"
              :transfer="false"
              class="orderList__Dropdown--batch operation__item"
              v-if="+filters.modeStatus === 300"
            >
              <Button class="export-more">
                批量操作
                <Icon size="mini" icon="arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem v-show="isUnAudit">
                  <Button
                    class="DropdownItem__button"
                    styleType="btnStyleForAdd"
                    @click="handleBatchAuditOrder"
                    :confirm="true"
                    :title="auditConfirm"
                    >审核订单</Button
                  >
                </DropdownItem>
                <DropdownItem v-show="isUnReceived">
                  <Button
                    class="DropdownItem__button"
                    @click="batchCompleteOrder"
                    :confirm="true"
                    :title="completeConfirm"
                    >完成订单</Button
                  >
                </DropdownItem>
                <DropdownItem>
                  <Button
                    class="DropdownItem__button"
                    @click="_preOnPrintSelected"
                    >打印订单</Button
                  >
                </DropdownItem>
                <DropdownItem>
                  <Button
                    @click="_onExportPrintSelected"
                    class="DropdownItem__button"
                  >
                    <div class="y-center">
                      <span>按打印模板导出</span>
                      <Tooltip
                        max-width="300"
                        placement="top"
                        transfer
                        content="按打印模板导出只应用新版打印模版,配置老版打印模板的客户数据将不会导出!"
                      >
                        <i
                          class="ml8 sui-icon icon-tips"
                          style="font-size: 13px"
                        ></i>
                      </Tooltip>
                    </div>
                  </Button>
                </DropdownItem>
                <template
                  v-if="
                    isEnableOrderSyncContractPrice &&
                    (isAllOrderState ||
                      (!isAllOrderState &&
                        ['100', '300'].includes(filters.modeStatus)))
                  "
                >
                  <DropdownItem>
                    <SyncAllBtn
                      :disabled="disSyncAllBtn"
                      class="DropdownItem__button"
                      :allIdsObj="{
                        ids: selectedOrders.map((item) => item.id),
                      }"
                      :syncObj="{
                        url: '/superAdmin/OrderSuper/BatchBrushPrice',
                        reqMethod: order.batchBrushPrice,
                        params: {
                          is_brush_price: 1,
                        },
                      }"
                      @done="() => fetchData()"
                    />
                  </DropdownItem>
                </template>
                <DropdownItem>
                  <SyncAllBtn
                    class="DropdownItem__button"
                    idsKey="order_id"
                    :allIdsObj="{
                      ids: selectedOrders.map((item) => item.id),
                    }"
                    :syncObj="{
                      url: '/superAdmin/orderSuper/batchEdit',
                      params: {
                        type: 'tax',
                        value: '',
                      },
                    }"
                    :needTips="false"
                    name="更新商品税率"
                    typeText="更新商品税率"
                    afterText="笔订单的商品税率更新为目前该客户所属最新商品税率"
                    @done="() => fetchData()"
                  />
                </DropdownItem>
                <template v-if="isUnDelivery">
                  <DropdownItem>
                    <Button
                      v-if="showBatchAddGoods && DeliveryTimeOne"
                      class="DropdownItem__button"
                      @click="openBatchAddGoods(true)"
                    >
                      新增商品
                    </Button>
                  </DropdownItem>
                </template>
                <template
                  v-if="
                    sysConfig.is_open_order_lock != 0 &&
                    orderMode.undelivery == filters.modeStatus
                  "
                >
                  <DropdownItem v-if="hasAuthority('order_orderList_lock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(1)"
                    >
                      <div class="y-center">
                        <span>订单锁定(半锁)</span>
                        <Tooltip
                          max-width="300"
                          placement="top"
                          transfer
                          :content="`订单半锁定状态下，${lockPlatformText}无法执行订单删除操作`"
                        >
                          <i
                            class="ml8 sui-icon icon-tips"
                            style="font-size: 13px"
                          ></i>
                        </Tooltip>
                      </div>
                    </Button>
                  </DropdownItem>
                  <DropdownItem v-if="hasAuthority('order_orderList_lock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(2)"
                    >
                      <div class="y-center">
                        <span>订单锁定(全锁)</span>
                        <Tooltip
                          max-width="300"
                          placement="top"
                          transfer
                          :content="`订单全锁定状态下，${lockPlatformText}无法执行订单编辑和删除操作`"
                        >
                          <i
                            class="ml8 sui-icon icon-tips"
                            style="font-size: 13px"
                          ></i>
                        </Tooltip>
                      </div>
                    </Button>
                  </DropdownItem>
                  <DropdownItem v-if="hasAuthority('order_orderList_unlock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(0)"
                      >订单解锁</Button
                    >
                  </DropdownItem>
                </template>
                <Dropdown
                  style="width: 100%"
                  :transfer="true"
                  placement="right"
                  class="orderList__Dropdown--batch operation__item"
                >
                  <DropdownItem>
                    <Button class="DropdownItem__button">
                      编辑订单信息
                      <IIcon type="ios-arrow-forward"></IIcon>
                    </Button>
                  </DropdownItem>
                  <DropdownMenu slot="list">
                    <template v-if="isUnDelivery">
                      <template v-if="isOpenOrderTag">
                        <DropdownItem>
                          <Button
                            class="DropdownItem__button_trans"
                            @click="batchSettingOrderTag"
                          >
                            <div class="y-center">
                              <span>设置订单标签</span>
                              <Tooltip
                                placement="top"
                                transfer
                                max-width="300"
                                content="批量设置订单标签会清除当前订单已选标签"
                              >
                                <i
                                  class="ml8 sui-icon icon-tips"
                                  style="font-size: 13px"
                                ></i>
                              </Tooltip>
                            </div>
                          </Button>
                        </DropdownItem>
                      </template>
                    </template>
                    <DropdownItem>
                      <Button
                        class="DropdownItem__button_trans"
                        @click="
                          () => {
                            this.batchSettingReceiptStatusModal.show = true;
                          }
                        "
                      >
                        设置回单状态
                      </Button>
                    </DropdownItem>
                    <DropdownItem
                      v-if="directEditOrderDriver && (isUnDelivery || isAll)"
                    >
                      <Button
                        class="DropdownItem__button_trans"
                        @click="
                          () => {
                            this.batchSettingDriverModal.show = true;
                          }
                        "
                      >
                        指定司机
                      </Button>
                    </DropdownItem>
                    <DropdownItem>
                      <Button
                        class="DropdownItem__button_trans"
                        @click="
                          () => {
                            this.batchSettingDeliveryDateModal.show = true;
                          }
                        "
                      >
                        修改发货日期
                      </Button>
                    </DropdownItem>
                    <DropdownItem>
                      <Button
                        class="DropdownItem__button_trans"
                        @click="
                          () => {
                            this.batchSettingDeliveryTimeModal.show = true;
                          }
                        "
                      >
                        修改发货时间
                      </Button>
                    </DropdownItem>
                  </DropdownMenu>
                </Dropdown>
              </DropdownMenu>
            </Dropdown>
            <Dropdown v-else class="orderList__Dropdown--batch operation__item">
              <Button class="export-more">
                批量操作
                <Icon size="mini" icon="arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem v-show="isUnAudit">
                  <Button
                    class="DropdownItem__button"
                    styleType="btnStyleForAdd"
                    @click="handleBatchAuditOrder"
                    :confirm="true"
                    :title="auditConfirm"
                    >审核订单</Button
                  >
                </DropdownItem>

                <DropdownItem v-show="isUnReceived">
                  <Button
                    class="DropdownItem__button"
                    @click="batchCompleteOrder"
                    :confirm="true"
                    :title="completeConfirm"
                    >完成订单</Button
                  >
                </DropdownItem>

                <DropdownItem>
                  <Button
                    class="DropdownItem__button"
                    @click="_preOnPrintSelected"
                    >打印订单</Button
                  >
                </DropdownItem>

                <DropdownItem>
                  <Button
                    @click="_onExportPrintSelected"
                    class="DropdownItem__button"
                  >
                    <div class="y-center">
                      <span>按打印模板导出</span>
                      <Tooltip
                        max-width="300"
                        placement="top"
                        transfer
                        content="按打印模板导出只应用新版打印模版,配置老版打印模板的客户数据将不会导出!"
                      >
                        <i
                          class="ml8 sui-icon icon-tips"
                          style="font-size: 13px"
                        ></i>
                      </Tooltip>
                    </div>
                  </Button>
                </DropdownItem>

                <template
                  v-if="
                    isEnableOrderSyncContractPrice &&
                    (isAllOrderState ||
                      (!isAllOrderState &&
                        ['100', '300'].includes(filters.modeStatus)))
                  "
                >
                  <DropdownItem>
                    <SyncAllBtn
                      :disabled="disSyncAllBtn"
                      class="DropdownItem__button"
                      :allIdsObj="{
                        ids: selectedOrders.map((item) => item.id),
                      }"
                      :syncObj="{
                        url: '/superAdmin/OrderSuper/BatchBrushPrice',
                        reqMethod: order.batchBrushPrice,
                        params: {
                          is_brush_price: 1,
                        },
                      }"
                      @done="() => fetchData()"
                    />
                  </DropdownItem>
                </template>
                <template v-if="isUnDelivery">
                  <template v-if="isOpenOrderTag">
                    <DropdownItem>
                      <Button
                        class="DropdownItem__button"
                        @click="batchSettingOrderTag"
                      >
                        <div class="y-center">
                          <span>设置订单标签</span>
                          <Tooltip
                            placement="top"
                            transfer
                            max-width="300"
                            content="批量设置订单标签会清除当前订单已选标签"
                          >
                            <i
                              class="ml8 sui-icon icon-tips"
                              style="font-size: 13px"
                            ></i>
                          </Tooltip>
                        </div>
                      </Button>
                    </DropdownItem>
                  </template>
                </template>
                <DropdownItem>
                  <Button
                    class="DropdownItem__button"
                    @click="
                      () => {
                        this.batchSettingReceiptStatusModal.show = true;
                      }
                    "
                  >
                    设置回单状态
                  </Button>
                </DropdownItem>
                <DropdownItem>
                  <SyncAllBtn
                    class="DropdownItem__button"
                    idsKey="order_id"
                    :allIdsObj="{
                      ids: selectedOrders.map((item) => item.id),
                    }"
                    :syncObj="{
                      url: '/superAdmin/orderSuper/batchEdit',
                      params: {
                        type: 'tax',
                        value: '',
                      },
                    }"
                    :needTips="false"
                    name="更新商品税率"
                    typeText="更新商品税率"
                    afterText="笔订单的商品税率更新为目前该客户所属最新商品税率"
                    @done="() => fetchData()"
                  />
                </DropdownItem>
                <DropdownItem
                  v-if="directEditOrderDriver && (isUnDelivery || isAll)"
                >
                  <Button
                    class="DropdownItem__button"
                    @click="
                      () => {
                        this.batchSettingDriverModal.show = true;
                      }
                    "
                  >
                    指定司机
                  </Button>
                </DropdownItem>
                <template v-if="isUnDelivery">
                  <DropdownItem>
                    <Button
                      v-if="showBatchAddGoods && DeliveryTimeOne"
                      class="DropdownItem__button"
                      @click="openBatchAddGoods(true)"
                    >
                      新增商品
                    </Button>
                  </DropdownItem>
                </template>
                <template
                  v-if="
                    sysConfig.is_open_order_lock != 0 &&
                    orderMode.undelivery == filters.modeStatus
                  "
                >
                  <DropdownItem v-if="hasAuthority('order_orderList_lock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(1)"
                    >
                      <div class="y-center">
                        <span>订单锁定(半锁)</span>
                        <Tooltip
                          max-width="300"
                          placement="top"
                          transfer
                          :content="`订单半锁定状态下，${lockPlatformText}无法执行订单删除操作`"
                        >
                          <i
                            class="ml8 sui-icon icon-tips"
                            style="font-size: 13px"
                          ></i>
                        </Tooltip>
                      </div>
                    </Button>
                  </DropdownItem>
                  <DropdownItem v-if="hasAuthority('order_orderList_lock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(2)"
                    >
                      <div class="y-center">
                        <span>订单锁定(全锁)</span>
                        <Tooltip
                          max-width="300"
                          placement="top"
                          transfer
                          :content="`订单全锁定状态下，${lockPlatformText}无法执行订单编辑和删除操作`"
                        >
                          <i
                            class="ml8 sui-icon icon-tips"
                            style="font-size: 13px"
                          ></i>
                        </Tooltip>
                      </div>
                    </Button>
                  </DropdownItem>
                  <DropdownItem v-if="hasAuthority('order_orderList_unlock')">
                    <Button
                      class="DropdownItem__button"
                      @click="handleGoodsLock(0)"
                      >订单解锁</Button
                    >
                  </DropdownItem>
                </template>
              </DropdownMenu>
            </Dropdown>
            <Button
              @click="showNormalExportModal({ type: 'rows' })"
              class="batch__item operation__item"
              text="导出订单"
            />
            <Button
              @click="showExportModal({ type: 'rows' })"
              class="batch__item operation__item"
              text="导出订单明细订单"
            />
            <Button
              class="batch__item operation__item"
              @click="openBatchReturnModal"
              text="整单退货"
              v-show="[400, 600].includes(+filters.modeStatus)"
            >
            </Button>
            <Button
              class="batch__item operation__item"
              text="生成客户单"
              @click="onBatchOrder"
              v-if="is_open_issue_order && isEnableShowOrderListBehalfInfo"
            />
          </Col>
          <Col>
            <Button
              @click="batchCloseOrder"
              :confirm="true"
              :title="closeConfirm"
              v-show="isCanBatchClose"
              >关闭订单</Button
            >
          </Col>
        </Row>
      </div>
    </ListTable>

    <div v-if="isTableDataLoaded">
      <!-- 物流信息 -->
      <logisticsModal ref="logisticsModal"></logisticsModal>

      <!-- 导出分类汇总表 -->
      <Modal
        class="import-modal"
        v-model="exportCategoryModal.show"
        :closable="false"
        width="886"
      >
        <div class="import-modal__hd" slot="header">
          <h5>导出分类汇总表</h5>
          <Icon @click="closeExportCategoryModal" :size="12" icon="close" />
        </div>
        <div class="import-modal-exportCategoryFilter">
          <SFilter
            ref="exportCategoryFilter"
            :showFilterBtn="false"
            :advanceFilter="exportCategoryFilter"
          >
          </SFilter>
        </div>
        <div slot="footer">
          <Button @click="closeExportCategoryModal">取消</Button>
          <Button
            type="primary"
            @click="handleExportCategory"
            :loading="importSubmitLoading"
            >确定</Button
          >
        </div>
      </Modal>

      <!-- 订单导入 商品数量/单价为0  确认窗口-->
      <Modal class="import-zero-confirm" v-model="orderZeroModal.show">
        <div class="import-import-zero-confirm_hd" slot="header">
          <h3>确认</h3>
        </div>
        <div class="content">
          包含商品数量/金额为空或0的数据，请检查excel表格并确认为0时的数据操作
        </div>
        <div slot="footer">
          <Button @click="orderZeroConfirmCLose">取消</Button>
          <!-- 入参 is_filter_zero 表示过滤为0 数据 1 过滤 0不过滤 -->
          <Button type="primary" @click="orderZeroConfirm(0)"
            >创建为0订单</Button
          >
          <Button type="primary" @click="orderZeroConfirm(1)"
            >过滤为0订单</Button
          >
        </div>
      </Modal>

      <Modal
        class="import-modal"
        v-model="showImportModal"
        :closable="false"
        width="886"
      >
        <div class="import-modal__hd" slot="header">
          <h5>批量导入订单</h5>
          <Icon @click="showImportModal = false" :size="12" icon="close" />
        </div>
        <div class="importDes">
          <div class="explain">
            <h4>导入说明：</h4>
            <p>
              1.
              <span style="color: #f13130">*</span>
              标识的字段导入模板中必须包含对应列,非必要字段则可没有对应列.
            </p>
            <p>
              2.
              导入模板后,将引导设置“模板列”与“订单字段”的对应关系来匹配数据,需要导入的模版列都必须设置对应订单字段,否则将无法导入该列数据.
            </p>
            <p>3. 导入文件支持xls、xlsx格式，大小不超过2M，数据不超过2000行.</p>
            <p>4. 导入文件不能包含“合并单元格”,否则无法导入.</p>
            <p>5. 导入时只要有一行数据不正确,则导入失败.</p>
            <p>
              6.
              导入填写订单标签时不能填写错误,请务必将同一个订单的商品订单标签保持一致.
            </p>
          </div>
          <div class="item" :class="{ pb16: templates === 'a222222' }">
            <label class="item-left">选择导入模板：</label>
            <div class="item-right item-right__template">
              <Select
                style="width: 200px"
                v-model="templates"
                @on-change="templateChange"
              >
                <Option
                  v-for="item in showTemplateData"
                  :key="item.value"
                  :value="item.value"
                  :label="item.label"
                  :disabled="item._disabled"
                >
                  <p>
                    {{ item.label }}
                  </p>
                </Option>
              </Select>
              <p v-show="templates === 'a222222'" class="info-tips">
                录入原始订单编号+商品信息，会删除原订单所有商品并新增本次导入的商品信息
              </p>
              <Tooltip
                v-if="isAliasAllowRepeat"
                max-width="300"
                placement="top"
                content="已开启商品别名重复，订单自定义模版选择客户商品别名模式将不支持选择"
              >
                <i
                  style="font-size: 14px; cursor: pointer; margin-top: -1px"
                  class="sui-icon icon-tips"
                ></i>
              </Tooltip>
            </div>
          </div>
          <div class="item" v-if="templates == 'a333333' || isTxlcTemplate">
            <label class="item-left">发货日期：</label>
            <div class="item-right">
              <DatePicker
                style="width: 232px"
                type="date"
                :value="importDeliveryDate"
                @on-change="importDeliveryDateChange"
              />
            </div>
          </div>
          <div v-if="templates != 'a111111' && templates != 'a333333'">
            <template v-if="!isTxlcTemplate">
              <div class="item">
                <label class="item-left" style="width: 70px">固定字段</label>
                <span style="font-weight: 500; margin-left: 16px">商品：</span>
                <div class="item-right">
                  <RadioGroup v-model="importType">
                    <Radio :disabled="disable" :label="0" v-if="template_from != 3">商品编码</Radio>
                    <Radio :disabled="disable && template_from != 3" :label="1">商品名称</Radio>
                    <Radio
                      v-if="!isAliasAllowRepeat && +template_from !== 1"
                      :disabled="disable && template_from != 3"
                      :label="2"
                      >客户商品别名</Radio
                    >
                    <Radio :disabled="disable" v-if="+template_from !== 1 && template_from != 3" :label="3"
                      >客户商品别名编码</Radio
                    >
                  </RadioGroup>
                </div>
              </div>
              <div class="item" v-show="templates !== 'a222222'">
                <label style="margin-left: 13px" class="item-left"
                  >客户：</label
                >
                <div class="item-right">
                  <RadioGroup v-model="userMode">
                    <Radio :disabled="disable" :label="1" v-if="template_from != 3">客户编码</Radio>
                    <Radio :disabled="disable" :label="2">客户名称</Radio>
                  </RadioGroup>
                </div>
              </div>
              <div class="item">
                <label style="margin-left: 13px" class="item-left"
                  >价格：</label
                >
                <div class="item-right">
                  <RadioGroup v-model="price_mode">
                    <Radio :disabled="disable && template_from != 3" :label="0">保持不变</Radio>
                    <Radio :disabled="disable && template_from != 3" :label="1">导入时修改</Radio>
                  </RadioGroup>
                </div>
              </div>
              <div class="item" v-if="(isOpenOrderTag && disable && template_from != 3) || !disable">
                <label class="item-left" style="width: 70px">可选字段</label>
                <div class="item-right" style="margin-left: 16px">
                  <Checkbox
                    v-if="isOpenOrderTag && template_from != 3"
                    :disabled="disable"
                    v-model="order_tag_mode"
                    :true-value="1"
                    :false-value="0"
                    >订单标签</Checkbox
                  >
                  <template v-if="!disable">
                    <Checkbox
                      v-model="order_remark_mode"
                      :true-value="1"
                      :false-value="0"
                      :disabled="disable"
                      >订单备注</Checkbox
                    >
                    <Checkbox
                      v-model="remark_mode"
                      :disabled="remark_mode === 2"
                      :true-value="1"
                      :false-value="0"
                      >商品备注</Checkbox
                    >
                    <Checkbox
                      v-if="isEnableInternalNote"
                      v-model="inner_remark_mode"
                      :true-value="1"
                      :false-value="0"
                      >商品内部备注</Checkbox
                    >
                    <Checkbox
                      v-if="isUserCommodityRemark"
                      v-model="remark_mode"
                      :disabled="remark_mode === 1"
                      :true-value="2"
                      :false-value="0"
                      >客户指定商品备注</Checkbox
                    >
                  </template>
                </div>
              </div>
            </template>
            <div class="item" v-if="template_from != 1 && template_from != 3">
              <label class="item-left">下载模版：</label>
              <div class="item-right">
                <span class="bold">
                  点击下载
                  <em class="download-template" @click="exportOrderTemplate"
                    >订单导入模版</em
                  >
                  <em
                    class="download-template"
                    style="margin-left: 10px"
                    @click="onCustomTpl"
                    >自定义导入模版</em
                  >
                </span>
              </div>
            </div>
          </div>
          <div class="item">
            <label class="item-left">选择上传文件：</label>
            <div class="item-right">
              <div class="upload-item">
                <Upload
                  style="display: inline-block"
                  :show-upload-list="false"
                  :before-upload="importTpl"
                  :format="post.format"
                  :accept="post.accept"
                  :max-size="post.maxSize"
                  :data="uploadData"
                  :on-success="excelImportSuccess"
                  :on-format-error="onFormatError"
                  :on-error="onError"
                  :on-exceeded-size="onExceededSize"
                  :action="actionUrl"
                  ref="upload"
                >
                  <Button type="primary">选择文件</Button>
                </Upload>
                <div class="file-name-item" v-show="fileName">
                  <span class="file-name bold">{{ fileName }}</span>
                  <Icon icon="solid-close" :size="13" @click="cleanFile" />
                </div>
              </div>
            </div>
          </div>
          <div class="item nopd">
            <label class="item-left"></label>
            <div class="item-right">
              <p v-if="importError" class="error text-red">{{ importError }}</p>
            </div>
          </div>
          <Table
            class="mt15"
            :columns="importCol"
            :data="importData"
            height="310"
            v-if="importData.length > 0"
          ></Table>
          <p v-else></p>
        </div>
        <div slot="footer">
          <Button @click="closeImportOrder">取消</Button>
          <Button
            type="primary"
            @click="importSubmit"
            :loading="importSubmitLoading"
            >确定</Button
          >
        </div>
      </Modal>
      <ExportExcel
        @on-cancel="closeExportModal"
        :params="exportModal.params"
        :show="exportModal.show"
      ></ExportExcel>
      <ExportNormalExcel
        @on-cancel="closeExportNormalModal"
        :params="exportNormalModal.params"
        :show="exportNormalModal.show"
      ></ExportNormalExcel>
      <ExportProviderDetailModal
        ref="exportProviderDetailModalRef"
        :params="exportProviderDetailModal.params"
      />
      <PrintTemplateChoose
        ref="printTemplateChoose"
        @printTimesChange="_printTimesChange"
      />
      <!-- 导出选择 -->
      <PrintTemplateExportChoose
        :disabled="exportLoading"
        :btnText="exportLoading ? '导出中...' : '导出'"
        ref="printTemplateExportChoose"
      />
      <!-- 批量打印选择 -->
      <PrintTemplateExportChoose
        showPreview
        :filterOld="false"
        btnText="打印"
        title="选择打印模板"
        ref="printTemplateBatchChoose"
      />
      <SModal
        title="提示"
        text="是否要复制该客户订单"
        ref="smodal"
        type="warning"
        @ok="isCopySyncGoodPriceConfirm"
        okTxt="继续"
      >
        <template #footer-left>
          <div v-if="hasAuthority('A002001012')">
            <Checkbox
              :value="isCopySyncGoodPrice"
              @on-change="isCopySyncGoodPriceChange"
            >
              <span>是否同步订单商品价格？</span>
            </Checkbox>
          </div>
        </template>
      </SModal>
      <Modal
        v-model="batchSettingOrderTagModal.show"
        :title="batchSettingOrderTagModal.title"
        @on-ok="batchSettingOrderTagModal.ok"
        @on-cancel="batchSettingOrderTagModal.cancel"
      >
        <div style="display: flex; align-items: center">
          <span class="mr10">订单标签</span>
          <CheckboxGroup
            v-model="batchSettingOrderTagModal.value"
            style="flex: 1"
          >
            <Checkbox
              v-for="item in orderTagList"
              :key="item.id"
              :label="item.value"
            >
              {{ item.label }}
            </Checkbox>
          </CheckboxGroup>
        </div>
      </Modal>
      <Modal
        v-model="batchSettingReceiptStatusModal.show"
        :title="batchSettingReceiptStatusModal.title"
        @on-ok="batchSettingReceiptStatusModal.ok"
        @on-cancel="batchSettingReceiptStatusModal.cancel"
      >
        <div style="display: flex; align-items: center">
          <span class="mr10">回单状态</span>
          <RadioGroup v-model="batchSettingReceiptStatusModal.value">
            <Radio label="0">
              <span>未回单</span>
            </Radio>
            <Radio label="1">
              <span>已回单</span>
            </Radio>
          </RadioGroup>
        </div>
      </Modal>
      <Modal
        v-model="batchSettingDriverModal.show"
        :title="batchSettingDriverModal.title"
        @on-ok="batchSettingDriverModal.ok"
        @on-cancel="batchSettingDriverModal.cancel"
      >
        <div style="display: flex; align-items: center">
          <span class="mr10">司机</span>
          <driver v-model="batchSettingDriverModal.value" style="width: 230px"></driver>
        </div>
      </Modal>
      <Modal
        v-model="batchSettingDeliveryDateModal.show"
        :title="batchSettingDeliveryDateModal.title"
        @on-cancel="batchSettingDeliveryDateModal.cancel"
      >
        <div style="display: flex; align-items: center">
          <span class="mr10">批量修改勾选订单发货日期为</span>
          <DatePicker
            style="flex: 1"
            type="date"
            placeholder="请选择发货日期"
            v-model="batchSettingDeliveryDateModal.value"
          />
        </div>
        <template #footer>
          <Button @click="batchSettingDeliveryDateModal.cancel()">取消</Button>
          <Button
            :loading="batchSettingDeliveryDateModal.loading"
            type="primary"
            @click="batchSettingDeliveryDateModal.ok"
          >
            {{ batchSettingDeliveryDateModal.loading ? '提交中' : '确定' }}
          </Button>
        </template>
      </Modal>
      <Modal
        v-model="batchSettingDeliveryTimeModal.show"
        :title="batchSettingDeliveryTimeModal.title"
        @on-ok="batchSettingDeliveryTimeModal.ok"
        @on-cancel="batchSettingDeliveryTimeModal.cancel"
      >
        <div style="display: flex; align-items: center">
          <span class="mr10">批量修改勾选订单发货时间为</span>
          <deliveryTime v-model="batchSettingDeliveryTimeModal.value">
          </deliveryTime>
        </div>
      </Modal>

      <BatchReturnGoodsModal
        ref="batch-return-modal"
        v-if="showBatchReturnGoodsModal"
        @on-ok="fetchData"
      ></BatchReturnGoodsModal>

      <SModal ref="payPublicModal" mask :btns="0">
        <div class="payPublic" style="margin-bottom: 24px">
          <p>请输入对公支付付款金额：</p>
          <NumberInput
            :precision="2"
            :min="0"
            :max="999999999.99"
            v-model="payPublicInfo.amount_paid"
            placeholder="请输入"
            style="width: 100%; margin-top: 6px"
          />
          <p
            v-if="payPublicInfo.errText"
            style="
              font-size: 12px;
              height: 18px;
              color: #f13130;
              margin-top: 6px;
            "
          >
            {{ payPublicInfo.errText }}
          </p>
        </div>
        <template #footer>
          <div style="display: flex; justify-content: flex-end">
            <Button class="mr10" @click="hadnlePublickModalClose">取消</Button>
            <Button type="primary" @click="hadnlePublickModalSubmit"
              >确定</Button
            >
          </div>
        </template>
      </SModal>

      <!-- 修改周转筐弹窗 -->
      <BatchUpdateBasket ref="batchUpdateBasket" @refresh="onRefresh" />

      <!-- 订单刷价-一键恢复 右抽屉 -->
      <recover-modal v-if="showRecoverModal" ref="recover-modal">
      </recover-modal>

      <shareModal :config="listItem" v-model="showShare"></shareModal>

      <BatchAddGoodModal
        ref="batchAddGoodModalRef"
        :allIdsObj="{
          url: apiUrl.getOrderList,
          params: {
            ...getParams(),
            only_id: 1,
          },
          ids: BatchAddGoodModalIds,
        }"
        :syncObj="{
          url: '/superAdmin/orderSuper/batchAddOrderCommodity',
          params: {},
        }"
        @done="() => fetchData()"
      />

      <ManualSplitOrderModal
        ref="manualSplitOrder"
        @on-success="handleSplitSuccess"
      ></ManualSplitOrderModal>
      <getOrderModal
        ref="getOrderModal"
        from="orderList"
        @on-close="onRefresh"
      ></getOrderModal>
      <BatchOrderModal ref="batchOrderModal" :selectedRows="selectedOrders"/>
      <sdpPollingTask
        ref="sdpPollingTask"
        :fail-log="2"
        @on-fail="handleShowOrderAuditLog"
        ></sdpPollingTask>
      <sdp-pagination-table-modal ref="sdpPaginationTableModalRef" />
    </div>
  </div>
</template>

<script>
import getOrderModal from '@/pages/appCenter/traceabilityPlatform/shaoxing/components/get-order-modal.vue';
import authority from '@/util/authority.js';

import Tabs from '@components/tabs';
import { Icon } from '@components';
import { Icon as IIcon, Tooltip } from 'view-design';
import ListTable from '@/components/list-table';
import ExportButtonMulti from '@components/common/export-btn-multi';
import Table from '@components/table';
import { orderMode } from '@assets/js/common.js';
import orderService from '@api/order.js';
import OrderFilterDeliveryDate from '@components/order-filter-delivery-date';
import InputAutoComplete from '@/components/common/InputAutoComplete';
import GroupFilter from '@components/common/GroupFilter';
import GoodsAutoComplete from '@components/common/goodsAutoComplete_new';
import SuborganizationCascader from '@/components/common/SuborganizationCascader.vue';
import SyncAllBtn from './components/syncAllBtn.vue';
import Button from '@components/button';
import CheckboxGroup from '@components/CheckboxGroup';
import checkBox from '@components/checkbox';
import orderSerivce from '@api/order.js';
import { MINE_TYPE } from '@/util/const';
import DateUtil from '@/util/date';
import { api } from '@api/api.js';
import Bus from '@api/bus.js';
import moment from 'moment';
// eslint-disable-next-line
import printMixin from '@/mixins/orderPrint';
import ConfigMixin from '@/mixins/config';
import FundAllocationMixin from '@/mixins/fund-allocation';
import RadioGroup from '@components/RadioGroup';
import { formatGoodsThumbnailPath } from '@/util/common';
import TableHeadSortIcon from '@components/common/tableHeadSortIcon';
import Cookies from 'js-cookie';
import { isEqual } from 'lodash-es';
import { orderReturnUnAuditCheck } from './utils';
import StorageUtil from '@util/storage.js';
import cloudPrinting from '@api/cloudPrinting.js';
import Team from '../appCenter/crm/team.vue';
import Template from '../appCenter/certificate/template.vue';
import BatchUpdateBasket from './components/batch-update-basket';
import order from '@/api/order';
import { mapState } from 'vuex';
import { IS_ACCOUNT_ORDER_ENUM } from '@/util/const';
import SFilter from '@components/s-filter';
import SplitRuleCascader from './components/SplitRuleCascader';
import BatchOrderModal from './components/BatchOrderModal.vue';
import { SModal } from '@/components/modal';
import {
  circuit,
  group,
  saller,
  userType,
  userTag,
  area,
  driver,
  operator,
  selfPickup,
  deliveryTime,
  meal,
  orderType,
  orderSource,
  payWay,
  orderTag,
  store
} from '@/components/standard/sdp-filter-items'
import sdpPollingTask from '@/components/standard/sdp-polling-task/index';
import { sdpPaginationTableModal } from '@/components/standard/sdp-pagination-table-modal/index'
import { BUSINESS_TYPE_LIST } from '@/util/const';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const { hasAuthority } = authority;

function getDefaultSplitRule() {
  const splitRuleId = StorageUtil.getLocalStorage('order_split_rule_id');
  return splitRuleId ? splitRuleId.split(',') : [];
}

export default {
  name: 'OrderList',
  mixins: [printMixin, ConfigMixin, FundAllocationMixin],
  components: {
    sdpPollingTask,
    sdpPaginationTableModal,
    getOrderModal,
    deliveryTime,
    driver,
    Tabs,
    Icon,
    IIcon,
    checkBox,
    ListTable,
    ExportButtonMulti,
    Button,
    Table,
    SyncAllBtn,
    Team,
    Template,
    BatchUpdateBasket,
    SFilter,
    SModal,
    BatchOrderModal,
    // 异步组件
    shareModal: () => import('./components/shareModal.vue'),
    logisticsModal: () => import('./components/logistics-modal.vue'),
    BatchAddGoodModal: () => import('./components/batchAddGoodModal/index.vue'),
    BatchReturnGoodsModal: () =>
      import('@/pages/order/components/batchReturnGoodsModal.vue'),
    ExportProviderDetailModal: () =>
      import('./components/export-provider-detail-modal.vue'),
    PrintTemplateExportChoose: () =>
      import('@/components/print-template-export-choose'),
    ExportExcel: () => import('@components/order/ExportExcel'),
    ExportNormalExcel: () => import('@components/order/ExportNormalExcel'),
    PrintTemplateChoose: () => import('@components/print-template-choose'),
    ManualSplitOrderModal: () => import('./components/ManualSplitOrderModal'),
    RecoverModal: () => import('@/pages/order/components/recoverModal.vue'),
  },
  props: {},
  data() {
    return {
      // 除table外的区域渲染条件
      isTableDataLoaded: false,
      // --start 异步组件 ---
      showBatchReturnGoodsModal: false,
      showRecoverModal: false,
      hasExecuted: false, // 添加一个标志变量
      // --end  异步组件 ---
      orderName: null,
      showAdvance: false,
      deliveryDateRange: 0,
      recoverDialog: false,
      recoverList: [],
      payPublicInfo: {},
      listItem: {},
      showShare: false,
      orderZeroModal: {
        show: false,
      },
      orderAuditFailLog: [],
      batchSettingReceiptStatusModal: {
        show: false,
        title: '批量设置回单状态',
        value: '0',
        ok: () => {
          let selectedOrders = this.selectedOrders;
          let ids = selectedOrders
            .map((item) => {
              return item.id;
            })
            .join(',');
          let params = {
            type: 'receipt',
            order_id: ids,
            value: this.batchSettingReceiptStatusModal.value,
          };
          orderSerivce.batchEdit(params).then((res) => {
            const { status, message } = res;
            if (status) {
              this.$smessage({
                type: 'success',
                text: '修改成功',
              });
              this.fetchData();
            } else {
              this.$smessage({
                type: 'error',
                text: message,
              });
            }
          });
        },
        cancel: () => {},
      },
      batchSettingDeliveryDateModal: {
        show: false,
        title: '批量修改发货日期',
        value: '',
        loading: false,
        ok: async () => {
          this.$Modal.confirm({
            title: '提示',
            content: '确认修改当前勾选订单的发货日期？',
            onOk: async () => {
              let selectedOrders = this.selectedOrders;
              let ids = selectedOrders.map((item) => {
                return item.id;
              });

              const pollingEdit = async (ids) => {
                if (!ids.length) return;
                const id = ids.shift();
                const res = await orderSerivce.batchEdit({
                  type: 'delivery_date',
                  order_id: id,
                  value: moment(
                    this.batchSettingDeliveryDateModal.value,
                  ).format('YYYY-MM-DD'),
                });
                if (res.status) {
                  await pollingEdit(ids);
                } else {
                  this.$smessage({
                    type: 'error',
                    text: res.message,
                  });
                }
              };

              this.batchSettingDeliveryDateModal.loading = true;
              await pollingEdit(ids);
              this.batchSettingDeliveryDateModal.loading = false;
              this.$smessage({
                type: 'success',
                text: '操作成功',
              });
              this.batchSettingDeliveryDateModal.show = false;
              this.fetchData();
            },
            onCancel: () => {},
          });
        },
        cancel: () => {
          this.batchSettingDeliveryDateModal.show = false;
        },
      },
      batchSettingDeliveryTimeModal: {
        show: false,
        title: '批量修改发货时间',
        value: '',
        ok: () => {
          this.$smodal({
            type: 'warning',
            title: '提示',
            text: '确认修改当前勾选订单的发货时间？',
            mask: true,
            onOk: () => {
              this.batchSettingDeliveryTimeModal.show = false;
              let selectedOrders = this.selectedOrders;
              let ids = selectedOrders
                .map((item) => {
                  return item.id;
                })
                .join(',');
              let params = {
                type: 'delivery_time',
                order_id: ids,
                value: this.batchSettingDeliveryTimeModal.value,
              };
              orderSerivce.batchEdit(params).then((res) => {
                const { status, message } = res;
                if (status) {
                  this.$smessage({
                    type: 'success',
                    text: '修改成功',
                  });
                  this.fetchData();
                } else {
                  this.$smessage({
                    type: 'error',
                    text: message,
                  });
                }
              });
            },
            onQuit: () => {},
          });
        },
        cancel: () => {},
      },
      batchSettingOrderTagModal: {
        show: false,
        title: '批量设置订单标签',
        value: [],
        ok: () => {
          let selectedOrders = this.selectedOrders;
          let ids = selectedOrders
            .map((item) => {
              return item.id;
            })
            .join(',');
          let params = {
            type: 'tag',
            order_id: ids,
            value: this.batchSettingOrderTagModal.value.join(','),
          };
          orderSerivce.batchEdit(params).then((res) => {
            const { status, message } = res;
            if (status) {
              this.$smessage({
                type: 'success',
                text: '修改成功',
              });
              this.fetchData();
            } else {
              this.$smessage({
                type: 'error',
                text: message,
              });
            }
          });
        },
        cancel: () => {},
      },
      batchSettingDriverModal: {
        show: false,
        title: '批量指定司机',
        value: '',
        ok: () => {
          let selectedOrders = this.selectedOrders;
          let ids = selectedOrders
            .map((item) => {
              return item.id;
            })
            .join(',');
          let params = {
            type: 'driver',
            order_id: ids,
            value: this.batchSettingDriverModal.value,
          };
          orderSerivce.batchEdit(params).then((res) => {
            const { status, message } = res;
            if (status) {
              this.successMessage('指定成功');
              this.fetchData();
              this.batchSettingDriverModal.value = '';
            } else {
              this.errorMessage(message || '指定失败');
            }
          });
        },
        cancel: () => {
          this.batchSettingDriverModal.value = '';
        },
      },
      uploadData: {},
      copyId: '',
      copyNo: '',
      exportBtnMultiData: [
        {
          text: '订单',
          onClick: () => {
            this.showNormalExportModal();
            return false;
          },
          // api: this.apiUrl.exportOrderList,
          // offline: true,
          // paramGetter: () => this.getParams()
        },
        {
          text: '订单明细',
          onClick: () => {
            this.showExportModal();
            return false;
          },
        },
      ],
      disableTemplate: false,
      firstLoad: true,
      uploadResolve: () => {},
      init: false,
      orderMode,
      is_order_audit: false,
      is_open_acceptance_order_export: false,
      showImportModal: false,
      disable: false,
      importType: 0,
      userMode: 1,
      price_mode: 0,
      order_tag_mode: 0,
      order_remark_mode: 1,
      remark_mode: 1,
      inner_remark_mode: 0,
      fileName: '',
      templates: '0',
      templateData: [],
      importError: '',
      importText: '导入订单',
      importData: [],
      disabledImport: false,
      // mineType: MINE_TYPE,
      actionUrl: '/superAdmin/orderSuper/batchImport?',
      sum: {
        presentPrice: 0,
        presentActualPrice: 0,
        allPrice: 0,
        allActualPrice: 0,
        presentBehalfPrice: 0,
        presentNewBehalfPrice: 0,
        allNewBehalfPrice: 0,
        allBehalfPrice: 0,
        allPageActualPrice: 0,
        allActualAmount: 0,
        order_weight: 0
      },
      post: {
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xlsx', 'xls'],
      },
      orderTagList: [],
      importCol: [
        {
          title: '错误提示',
          key: 'content',
          width: 200,
          render: (h, params) => {
            var data = params.row;
            return h(
              'Tooltip',
              {
                props: {
                  maxWidth: 300,
                  transfer: true,
                  content: data.content,
                  placement: 'bottom',
                },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      color: 'red',
                      display: '-webkit-box',
                      '-webkit-box-orient': 'vertical',
                      '-webkit-line-clamp': 2 /* 定义文本的行数 */,
                      overflow: 'hidden',
                      'text-overflow': 'ellipsis',
                      'white-space': 'normal !important',
                    },
                  },
                  data.content,
                ),
              ],
            );
          },
        },
        {
          title: '错误行数',
          key: 'line',
          align: 'center',
        },
        {
          title: '商品名称',
          key: 'commodity_name',
        },
        {
          title: '用户编码',
          key: 'user_code',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.user_name !== '' ? row.user_name : row.user_code,
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
        },
        {
          title: '发货时间',
          key: 'delivery_date',
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      selectedOrders: [],
      columns: [],
      exportCategoryFilter: [],
      filters: {
        modeStatus: '0',
        is_order_page_list: 1,
        commodity_id: '',
        user_id: '',
      },
      filterItems: [
        {
          checked: true,
          required: true,
          label: '发货日期',
          type: 'custom',
          props: {
            type: 'daterange',
            placeholder: '发货日期',
            rangeConfig: this.deliveryDateRange,
            setDefaultEndTime: DateUtil.getTodayDate(),
            setDefaultStartTime: '',
          },
          key: ['startTime', 'endTime'],
          component: OrderFilterDeliveryDate,
          onChange: (value) => {
            this.startTime = value[0];
            this.endTime = value[1];
            return {
              value,
            };
          },
        },
        {
          checked: true,
          required: true,
          type: 'custom',
          hasType: 'GroupFilter',
          style: {
            width: '299px',
          },
          key: ['no_type', 'unc_search'],
          defaultValue: ['1', ''],
          props: {
            placeholder: '输入客户名称/编码 /订单号/收货手机',
            selectData: [
              {
                label: '按客户信息',
                placeholder: '输入客户名称/编码查询',
                value: '1',
                key: 'unc_search',
              },
              {
                label: '按订单号',
                placeholder: '输入按订单号查询',
                value: '2',
                key: 'order_no',
              },
              {
                label: '按手机号',
                placeholder: '输入手机号查询',
                value: '3',
                key: 'user_tel',
              },
            ],
            customType: '1',
            customComp: () => InputAutoComplete,
            customBind: {
              dataProvider: orderService.getUserBySearch,
              valueKey: 'id',
              labelKey: 'email',
            },
            on: {
              'on-enter': (value) => {
                if (value[0] === '1') this.filters.user_id = value[1] || '';
                this.$refs.orderListTable &&
                  this.$refs.orderListTable.fetchData();
              },
              'on-clear': () => {
                this.filters.user_id = '';
              },
            },
          },
          component: GroupFilter,
          onChange: (value = '') => {
            // 输入框删除的是否, 也需要把user_id干掉
            this.filters.user_id = '';

            return { value, stop: true };
          },
        },
      ],
      exportModal: {
        show: false,
        params: {},
      },
      exportNormalModal: {
        show: false,
        params: {},
      },
      exportProviderDetailModal: {
        params: {},
      },
      exportCategoryModal: {
        show: false,
        params: {},
      },
      advanceItems: [],
      sort: StorageUtil.getLocalStorage('order-list-sort-filters') || {
        user_code: 0,
        shop_name: 0,
        delivery_date: 0,
        print_times: 0,
        sale_name: 0,
        line_name: 0,
      },
      isCopySyncGoodPrice: false,
      filterConfig: [
        {
          key: 'delivery_date',
          label: '发货日期配置',
          defaultValue: '00:00',
          component: 'TimePicker',
          style: {
            width: '110px',
          },
          props: {
            value: '',
            transfer: true,
            format: 'HH:mm',
            placement: 'bottom-end',
            placeholder: '请选择时间',
            clearable: false,
          },
          tips: '订单列表以及新增订单发货日期筛选项默认在每天的该时间之前展示为当天',
        },
        {
          key: 'delivery_date_filter_range',
          label: '发货周期配置(天)',
          defaultValue: 30,
          component: 'InputNumber',
          style: {
            width: '110px',
          },
          props: {
            value: '',
            precision: 0,
            min: 1,
            max: this.maxOrderDeliveryDateDays
              ? this.maxOrderDeliveryDateDays
              : 365,
          },
          tips: '订单列表发货日期根据发货日期配置默认筛选周期，数值默认为30天，支持修改录入最大数值为1-365，正整数',
        },
      ],
      defaultOrderState: StorageUtil.getLocalStorage(
        'order-list-checkbox-state',
      )
        ? StorageUtil.getLocalStorage('order-list-checkbox-state')
        : ['0', '1', '2', '4', '5'],
      initParams: {},
      startTime: '',
      endTime: '',
      importDeliveryDate: '',
      importSubmitLoading: false,
      BatchAddGoodModalIds: [],
      template_from: 0,
      create_user: '',
    };
  },
  watch: {
    maxOrderDeliveryDateDays: {
      immediate: true,
      handler(newVal) {
        // 更新发货周期配置(天)的最大值
        const deliveryDateFilterRangeConfigIndex = this.filterConfig.findIndex(
          (config) => config.key === 'delivery_date_filter_range',
        );
        if (deliveryDateFilterRangeConfigIndex > -1) {
          this.$set(
            this.filterConfig[deliveryDateFilterRangeConfigIndex].props,
            'max',
            newVal || 365,
          );
        }
      },
    },
    deliveryDateRange(newVal) {
      const deliveryDataIndex = this.filterItems.findIndex(
        (item) => item.component === OrderFilterDeliveryDate,
      );
      if (deliveryDataIndex > -1) {
        this.$set(
          this.filterItems[deliveryDataIndex].props,
          'rangeConfig',
          newVal || 0,
        );
      }
    },
    columnsRelatedValue: {
      immediate: true,
      deep: true,
      handler(newVal, oldVal) {
        if (!isEqual(newVal, oldVal)) {
          this.initColumns();
        }
      },
    },
    'filters.modeStatus': {
      handler(n, o) {
        this.setAdvanceItems2();
      },
    },
  },
  computed: {
    showTemplateData() {
      // 商品别名支持重复, 禁用掉按商品别名导入的模板
      return this.templateData.map(item => {
        const newItem = { ...item, _disabled: false };
        if (this.isAliasAllowRepeat && +item.type === 2) {
          newItem._disabled = true;
        }
        return newItem;
      });
    },
    isAllOrderState() {
      return +this.sysConfig.batch_brush_order_mode === 0;
    },
    brushPriceTips() {
      return this.isAllOrderState
        ? `<p>一键刷价会把当前筛选条件下所有符合刷价条件的订单进行刷价，以下订单不支持刷价：</p><p>1、订单已关闭</p><p>2、发货订单已支付</p><p>3、订单已对账</p><p>4、订单已关联退货单</p>`
        : `<p>一键把当前筛选条件下所有订单进行刷价操作</p>`;
    },
    order() {
      return order;
    },
    columnsRelatedValue() {
      const {
        isOpenAcceptanceOrderExport,
        isCanChooseDeliveryOrderPrintTemplate,
        isOpenSubsidiary,
        isNewGoodsPackageRecipe,
        isSplitOrderByProvider,
      } = this;
      return {
        isOpenAcceptanceOrderExport,
        isCanChooseDeliveryOrderPrintTemplate,
        isOpenSubsidiary,
        isNewGoodsPackageRecipe,
        isSplitOrderByProvider,
      };
    },
    advanceItemsRelatedValue() {
      const {
        isEnableMultiStore,
        isOpenOrderImportDiffCommodityRemark,
        isOpenSubsidiary,
        isNewGoodsPackageRecipe,
        isOpenSplitOrder,
        isOpenOrderTag,
      } = this;
      return {
        isEnableMultiStore,
        isOpenOrderImportDiffCommodityRemark,
        isOpenSubsidiary,
        isNewGoodsPackageRecipe,
        isOpenSplitOrder,
        isOpenOrderTag,
      };
    },
    // 是否是【天下良仓-单个订单】
    isTxlcTemplate() {
      return this.create_user === 'system' && +this.template_from === 2;
    },
    // 待审核
    isUnAudit() {
      return Number(this.filters.modeStatus) === orderMode.unaudit;
    },
    // 待发货
    isUnDelivery() {
      return Number(this.filters.modeStatus) === orderMode.undelivery;
    },
    // 待收货
    isUnReceived() {
      return Number(this.filters.modeStatus) === orderMode.unreceived;
    },
    // 已关闭
    isClosed() {
      return Number(this.filters.modeStatus) === orderMode.closed;
    },
    isAll() {
      return Number(this.filters.modeStatus) === orderMode.all;
    },
    isCanBatchClose() {
      return this.isUnAudit || this.isUnDelivery;
    },
    closeConfirm() {
      return `当前选中订单共有${this.selectedOrders.length}条，全部关闭?`;
    },
    completeConfirm() {
      return `当前选中订单共有${this.selectedOrders.length}条，全部完成?`;
    },
    auditConfirm() {
      return `当前选中订单共有${this.selectedOrders.length}条，全部审核?`;
    },
    disSyncAllBtn() {
      let flag = true;
      if (
        this.startTime &&
        this.endTime &&
        DateUtil.diffDay(this.startTime, this.endTime) <= 31
      ) {
        flag = false;
      }
      return flag;
    },
    showBatchAddGoods() {
      return hasAuthority('A002001003') && hasAuthority('A002001004');
    },
    DeliveryTimeOne() {
      return (
        this.startTime &&
        this.endTime &&
        DateUtil.diffDay(this.startTime, this.endTime) === 0
      );
    },
    ...mapState({
      sysConfig: 'sysConfig',
    }),
    lockPlatformText() {
      return this.sysConfig.is_open_order_lock == 1
        ? '商城'
        : this.sysConfig.is_open_order_lock == 2
          ? '后台核算'
          : '商城/后台核算';
    },
  },
  methods: {
    handleGetOrders() {
      this.$refs.getOrderModal.open();
    },
    orderZeroConfirm(is_filter_zero) {
      // TODO
      this.uploadData.is_filter_zero = is_filter_zero;
      this.upload();
    },

    orderZeroConfirmCLose() {
      this.cleanFile();
      this.orderZeroModal.show = false;
    },
    orderZeroConfirmOpen() {
      this.orderZeroModal.show = true;
    },
    onBatchOrder() {
      this.$refs.batchOrderModal.open();
    },
    // 打开批量退货弹窗
    openBatchReturnModal() {
      if (this.selectedOrders.length === 0) {
        this.errorMessage('请选择订单');
        return;
      }
      this.showBatchReturnGoodsModal = true;
      this.$nextTick(() => {
        this.$refs['batch-return-modal'].open(this.selectedOrders);
      });
    },
    // 导出汇总表
    async handleExportCategory() {
      let params = this.$refs.exportCategoryFilter.getValue();
      if (params.order_tag && params.order_tag.length) {
        params.order_tag = params.order_tag.join(',');
      }

      if (!params.month) return this.errorMessage('请选择导出月份');
      if (!params.user_id) return this.errorMessage('请选择客户名称');

      const res = await this.$request.post(
        this.apiUrl.exportOrderCategorySummary,
        params,
      );
      if (res.status) {
        window.open(res.data.path, '_self');
        this.closeExportCategoryModal();
      } else {
        this.errorMessage(res.message);
      }
    },
    // 打开可恢复刷价记录列表
    openRecovers() {
      if (this.showRecoverModal) {
        this.$refs['recover-modal'].open();
      } else {
        this.showRecoverModal = true;
      }
    },
    hasAuthority,
    async handleShowLogistics({ express_no }) {
      if (!express_no) return;
      this.$refs.logisticsModal.handleShowLogistics({ express_no });
    },

    filterConfigChange(filterConfig) {
      const deliverDateFilterConfig = filterConfig.find((config) => {
        return config.key === 'delivery_date';
      });
      let endTime = DateUtil.getTodayDate();
      const endDate =
        deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue;
      const isTomorrow = DateUtil.isTimeAfterSixPM(endDate);
      if (isTomorrow) {
        endTime = DateUtil.getTomorrow();
      }
      console.log('endTime', endTime);
      this.importDeliveryDate = endTime;
      const deliveryDateIndex = this.filterItems.findIndex(
        (item) => item.label === '发货日期',
      );
      this.filterItems[deliveryDateIndex].props.setDefaultEndTime = endTime;
      const deliveryDateRange = filterConfig.find((config) => {
        return config.key === 'delivery_date_filter_range';
      });
      if (deliveryDateRange) {
        this.deliveryDateRange = deliveryDateRange.value;
      }
    },
    batchSettingOrderTag() {
      this.getOrderTagList();
      this.batchSettingOrderTagModal.show = true;
    },
    isCopySyncGoodPriceConfirm() {
      this.$router.push({
        path: '/newOrder',
        query: { keep_scroll: 1, copyId: this.copyId, copyNo: this.copyNo },
      });
    },
    isCopySyncGoodPriceChange(val) {
      this.isCopySyncGoodPrice = val;
      console.log(val);
      StorageUtil.setLocalStorage('is_copy_sync_good_price', val);
    },
    _onExportPrintSelected() {
      this.$refs.printTemplateExportChoose.open(
        (template_id, previewChecked, exportSheetMode) => {
          let limitNum = 50;
          // // 单 sheet
          // if (this.$refs.printTemplateExportChoose.exportSheetMode===EXCEL_SHEET_SINGLE) {
          //   limitNum = 50
          // } else { // 多 sheet
          //   limitNum = 50
          // }
          if (this.selectedOrders.length > limitNum) {
            this.$smessage({
              type: 'error',
              text: `一次导出的数据不能超过${limitNum}条`,
            });
            return false;
          }
          let ids = this.selectedOrders.map((item) => item.id).join();
          this._printOrder({
            id: ids,
            is_preview: false,
            is_merge: 0,
            is_export_excel: true,
            template_id,
            exportSheetMode,
            record_print_times: false,
            sheetNameKey: 'user_name',
          });
        },
      );
      // 导出发货单
    },
    /**
     * @description: 获取自定义分页参数
     * @param {*} params 查询参数
     * @return {Promise}
     */
    async _customGetPageParams(params) {
      const sumFields = this.$refs.orderListTable.getSumFields();
      if (sumFields.length && sumFields.find(item => item.key === 'order_weight')) {
        params.order_total_heads = 'order_weight';
      }
      const res = await this.$request.get(
        this.apiUrl.getAjaxListTotalAndPageParams,
        params,
      );
      if (res.data.totalSum && Number(res.data.totalSum.allGoodsNum) === 0) {
        res.data.totalSum.allGoodsNum = '--';
      }
      if (res.data.totalSum && Number(res.data.totalSum.allAmount) === 0) {
        res.data.totalSum.allAmount = '--';
      }
      this.sum = res.data.totalSum;
      console.log('sum', this.sum);


      return res.data;
    },
    _preOnPrintSelected() {
      // 云打印
      if (this.isOpenYunPrint) {
        cloudPrinting
          .printOrder({
            order_id: this.selectedOrders.map((item) => item.id).join(','),
            order_type: 1,
          })
          .then(({ status, message, data }) => {
            if (status) {
              if (data.fail_total > 0) {
                let text = '';
                data.fail_data.forEach((item) => {
                  text += `<p>订单号：${item.no}，失败原因：${item.message}。<p>`;
                });
                this.$snotice({
                  type: 'error',
                  title: `以下${data.fail_total}条订单打印失败`,
                  text: text,
                });
              } else {
                this.successMessage('打印成功');
              }
            } else {
              this.errorMessage(message);
            }
          });
        return;
      }
      this.$refs.printTemplateBatchChoose.open((template_id, preview) => {
        this._onPrintSelected(template_id, preview);
      });
    },
    /**
     * @description: 批量打印
     */
    _onPrintSelected(template_id, preview) {
      // 批量打印时会在遍历时批量调用设置打印模板弹框,会设置不上,所以这里先判断是否设置,再进行打印操作
      let ccode = Cookies.get('ccode') || '';
      if (ccode === '/') {
        ccode = '';
      }
      const printIndex = localStorage.getItem(ccode + 'orderPrinter');
      // 没有设置打印模板,先设置一下
      if (!printIndex) {
        // eslint-disable-next-line no-undef
        __selectPrinterAndContinuePrint(() => {
          // 设置完后再执行回调.
          this._onPrintSelected(template_id, preview);
        }, 'orderPrinter');
        return;
      }
      // 打印发货单
      let ids = this.selectedOrders.map((item) => item.id).join();
      const params = {
        id: ids,
        is_preview: false,
        is_merge: 0,
        template_id,
        requestUrl: '/superAdmin/orderSuper/authorizedGetPrintData'
      };
      console.log(preview);
      if (preview) {
        params.is_preview = true;
      }
      this._printOrder(params);
      let list = this.$refs.orderListTable.getData();
      // this.selectedOrders.forEach(order => {
      //   let find = list.find(item => item.id == order.id);
      //   if (find) {
      //     find.print_times = +find.print_times + 1;
      //     find._checked = true;
      //   }
      // })
      list.forEach((order) => {
        let find = this.selectedOrders.find((item) => item.id == order.id);
        if (find) {
          order.print_times = +find.print_times + 1;
          order._checked = true;
        } else {
          order._checked = false;
        }
      });
    },
    toBehalfOrder() {
      this.$router.push({
        path: '/bills/order/add',
      });
    },
    async configData() {
      let res = await this.$request.get(api.orderImportTemplateGetConfig);
      if (res.status == 1) {
        this.disableTemplate = res.data == 1 ? true : false;
        console.log(this.disableTemplate);
      }
    },
    // 选择模板
    templateChange(val) {
      this.templates = val;
      if (
        val == 0 ||
        val == 'a111111' ||
        val === 'a222222' ||
        val === 'a333333'
      ) {
        this.cleanFile();
        // 系统模板
        this.disable = false;
        this.importType = 0;
        this.userMode = 1;
        this.price_mode = 0;
        this.template_from = 0;
      } else {
        // 自定义模板
        this.disable = true;
        const tempData = this.templateData.find((item) => item.value == val);
        this.create_user = tempData.create_user;
        this.importType = tempData.type ? Number(tempData.type) : 0;
        this.userMode = tempData.user_mode ? Number(tempData.user_mode) : 1;
        this.price_mode = tempData.price_mode ? Number(tempData.price_mode) : 0;
        this.order_tag_mode = tempData.order_tag_mode
          ? Number(tempData.order_tag_mode)
          : 0;
        // YB模版 1
        this.template_from = tempData.template_from || 0;
      }
      let exitIndex = this.importCol.findIndex((item) => item.key === 'line');
      if (val === 'a333333' && ~exitIndex) {
        this.importCol.splice(exitIndex, 1);
      }
      if (val !== 'a333333' && !~exitIndex) {
        this.importCol.splice(1, 0, {
          title: '错误行数',
          key: 'line',
          align: 'center',
        });
      }
    },
    onFormatError() {
      this.importError = '选择文件格式不符合，请修改文件格式';
      this.importSubmitLoading = false;
    },
    // 文件上传失败
    onError() {
      this.importError = `上传文件失败，请重试。`;
      this.importSubmitLoading = false;
    },
    // 文件超过大小限制
    onExceededSize() {
      this.importError = `上传文件超过大小限制，文件不能超过 ${this.post.maxSize}kb。`;
      this.importSubmitLoading = false;
    },
    setAdvanceItems2() {
      let params = this.$refs.orderListTable.getParams();
      if (params.startTime == params.endTime) {
        this.advanceItems[4].items[0].props.disabled = false;
      } else {
        this.advanceItems[4].items[0].props.disabled = true;
      }
      // 全部tab 才显示订单状态筛选项
      if (this.filters.modeStatus === '0') {
        let dataArray = [
          {
            label: '待付款',
            value: '0',
          },
          {
            label: '待发货',
            value: '1',
          },
          {
            label: '待收货',
            value: '2',
          },
          {
            label: '已完成',
            value: '4',
          },
          {
            label: '待审核',
            value: '5',
          },
        ];

        const clearAction = (value) => {
          // 清除之前的本地缓存，防止加权限之后查出了已关闭的数据
          const delIndex = this.defaultOrderState.findIndex(
            (item) => item === value,
          );
          if (~delIndex) {
            this.defaultOrderState.splice(delIndex, 1);
            StorageUtil.setLocalStorage(
              'order-list-checkbox-state',
              this.defaultOrderState,
            );
          }
        }

        if (this.hasAuthority('A002001014')) {
          dataArray.splice(4, 0, {
            label: '已关闭',
            value: '3',
          });
        } else {
          clearAction('3')
        }

        if (this.sysConfig.group_order_audit == 1) {
          dataArray.push({
            label: '待集团审核',
            value: '6',
          })
        } else {
          clearAction('6')
        }

        this.advanceItems.push({
          items: [
            {
              checked: true,
              required: true,
              label: '订单状态',
              type: 'custom',
              noReset: true,
              key: 'mode',
              props: {
                data: [...dataArray],
                key: 'mode',
              },
              style: {
                marginTop: '-8px',
                width: '900px',
              },
              defaultValue: this.defaultOrderState,
              component: CheckboxGroup,
              onChange: (value) => {
                // 勾选状态存在本地
                StorageUtil.setLocalStorage('order-list-checkbox-state', value);
                this.defaultOrderState = StorageUtil.getLocalStorage(
                  'order-list-checkbox-state',
                );
                return {
                  value,
                  stop: true,
                };
              },
            },
          ],
        });
      } else {
        this.advanceItems.forEach((item, index) => {
          item.items.forEach((item2) => {
            if (item2.key === 'mode') {
              this.advanceItems[index].items = [];
            }
          });
        });
      }
    },
    setAdvanceItems() {
      let advanceItems = [
        {
          items: [
            {
              checked: true,
              show: this.isEnableMultiStore,
              label: '仓库',
              type: 'custom',
              key: 'storage_id',
              defaultValue: this.$route.query.store_id || '',
              component: store,
            },
            {
              checked: true,
              required: true,
              label: '下单时间',
              type: 'DatePicker',
              props: {
                options: DateUtil.dateQuickOptions,
                type: 'datetimerange',
                format: 'yyyy-MM-dd HH:mm',
                placeholder: '请选择',
                separator: '~',
              },
              key: ['createStartTime', 'createEndTime'],
              class: 'datepicker-createtime',
              onChange: (val) => {
                return {
                  value: val,
                  stop: false
                }
              }
            },
            {
              checked: true,
              required: true,
              label: '签收日期',
              type: 'DatePicker',
              props: {
                options: DateUtil.dateQuickOptions,
                type: 'daterange',
                placeholder: '请选择',
              },
              key: ['signStartTime', 'signEndTime'],
              onChange: (val) => {
                return {
                  value: val,
                  stop: false
                }
              }
            },
            {
              checked: false,
              label: '送货时间',
              type: 'custom',
              key: 'delivery_time_id',
              component: deliveryTime,
            },
            ...(this.sysConfig.is_raw_package_recipe === 1
              ? [
                  {
                    checked: false,
                    label: '就餐日期',
                    type: 'datePicker',
                    key: 'diner_date',
                    props: {
                      placeholder: '请选择',
                  },
                  onChange: (val) => {
                    return {
                      value: val,
                      stop: false
                    }
                  }
                },
                ]
              : []),
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '付款状态',
              type: 'Select',
              key: 'payStatus',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '未付款',
                },
                {
                  value: '2',
                  label: '已付款',
                },
                {
                  value: '3',
                  label: '部分付款',
                },
              ],
            },
            {
              checked: false,
              label: '下单金额',
              type: 'RangeInput',
              defaultValue: ['', ''],
              key: ['min_price', 'max_price'],
            },
            {
              checked: false,
              label: '运费',
              type: 'Select',
              key: 'freight_filter',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '有运费',
                },
                {
                  value: '2',
                  label: '无运费',
                },
              ],
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '司机',
              type: 'custom',
              key: 'driver',
              component: driver,
            },
            {
              checked: false,
              label: '业务员',
              type: 'custom',
              key: 'salesman',
              component: saller,
              attrs: {
                apiName: this.apiUrl.getSalesList,
                params: {
                  in_list: 1, // 返回list结构
                }
              }
            },
            {
              checked: false,
              label: '客户类型',
              type: 'custom',
              key: 'receivable_style_id',
              defaultValue: this.$route.query.receivable_style_id || '',
              attrs: {
                multiple: true
              },
              component: userType,
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '集团',
              type: 'custom',
              key: 'group_id',
              defaultValue: this.$route.query.group_id || '',
              component: group,
            },
            {
              checked: false,
              label: '区域',
              type: 'custom',
              key: 'area_id',
              defaultValue: this.$route.query.area_id || '',
              component: area,
            },
            {
              checked: false,
              label: '是否打印',
              type: 'Select',
              key: 'is_print',
              data: [
                {
                  label: '全部',
                  value: '0'
                },
                {
                  label: '未打印',
                  value: '1'
                },
                {
                  label: '已打印',
                  value: '2'
                }
              ],
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '商品价格',
              type: 'Input',
              key: 'commodity_unit_price',
              props: {
                disabled: true,
              },
              tooltip: {
                maxWidth: '200',
                content: '需要发货日期筛选为1天才能进行商品价格的筛选',
              },
            },
            {
              required: true,
              checked: true,
              label: '商品',
              type: 'custom',
              key: 'commodity_name',
              defaultValue: '',
              props: {
                on: {
                  'on-enter': (e) => {
                    this.filters.commodity_id =
                      typeof e === 'object' ? e.commodity_id : '';
                    this.$refs.orderListTable &&
                      this.$refs.orderListTable.fetchData();
                  },
                  'on-focus': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.orderListTable.setValue(
                      'commodity_name',
                      '',
                      true,
                    );
                  },
                  'on-clear': (e) => {
                    this.filters.commodity_id = '';
                    this.$refs.orderListTable.setValue(
                      'commodity_name',
                      '',
                      false,
                    );
                  },
                },
              },
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
              component: GoodsAutoComplete,
            },
            {
              checked: false,
              label: '订单来源',
              type: 'custom',
              key: 'source',
              component: orderSource,
            },
            {
              checked: false,
              label: '配送方式',
              type: 'Select',
              key: 'delivery_method',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '配送',
                },
                {
                  value: '2',
                  label: '自提',
                },
              ],
            },
            {
              checked: false,
              label: '自提点',
              type: 'custom',
              key: 'self_pickup_point_id',
              component: selfPickup,
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '备注',
              type: 'Select',
              key: 'orderNotice',
              data: [
                {
                  value: '0',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '有备注',
                },
                {
                  value: '2',
                  label: '无备注',
                },
              ],
            },
            {
              checked: false,
              label: '是否首单',
              type: 'Select',
              key: 'is_first_order',
              data: [
                {
                  value: '',
                  label: '全部订单',
                },
                {
                  value: '1',
                  label: '首单',
                },
                {
                  value: '0',
                  label: '非首单',
                },
              ],
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '配送状态',
              type: 'Select',
              key: 'is_confirm_delivery',
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: 'Y',
                  label: '已送达',
                },
                {
                  value: 'N',
                  label: '未送达',
                },
              ],
            },
            {
              checked: false,
              type: 'custom',
              component: SuborganizationCascader,
              label: '子机构',
              key: 'subsidiary_id',
              show: this.isOpenSubsidiary,
              props: {
                dynamicLoading: true,
              },
            },
            {
              checked: false,
              show: this.isOpenNewRecipe,
              label: '餐次',
              type: 'custom',
              key: 'meal_type',
              component: meal,
              onChange: (value) => {
                // 存储到localStorage
                StorageUtil.setLocalStorage('order_meal_type', value || '');
                return { value };
              },
            },
            {
              checked: false,
              type: 'Input',
              label: '菜谱名称',
              key: 'recipe_name',
              show: this.isOpenNewRecipe,
              props: {
                placeholder: '请输入菜谱名称',
              },
            },
            {
              checked: false,
              label: '商品备注',
              type: 'Input',
              show: this.isOpenOrderImportDiffCommodityRemark,
              key: 'commodity_remark',
              props: {
                placeholder: '请输入',
              },
            },
            {
              checked: false,
              label: '拆单规则',
              type: 'custom',
              component: SplitRuleCascader,
              key: 'split_rule_id',
              show: this.isOpenSplitOrder,
              defaultValue: getDefaultSplitRule(),
              onChange: (value) => {
                // 存储到localStorage
                StorageUtil.setLocalStorage('order_split_rule_id', value || '');
                return { value };
              },
              props: {
                splitProviderFormat: true,
                placeholder: '请选择拆单规则',
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '回单状态',
              type: 'Select',
              key: 'is_receipt',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '未回单',
                  value: '0',
                },
                {
                  label: '已回单',
                  value: '1',
                },
              ],
            },
            {
              checked: false,
              label: '收货地址',
              type: 'Input',
              key: 'address',
              props: {
                placeholder: '请输入收货地址',
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '同步状态',
              type: 'Select',
              key: 'issue_order_sync_status',
              data: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '已同步',
                  value: '1',
                },
                {
                  label: '未同步',
                  value: '2',
                },
              ],
            },
            {
              checked: false,
              label: '少补状态',
              type: 'Select',
              key: 'supply_price_pay_status',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '未付',
                  value: '0',
                },
                {
                  label: '已付',
                  value: '1',
                },
              ],
            },
            {
              checked: false,
              label: '是否刷价',
              type: 'Select',
              key: 'is_brush_price',
              show: this.isEnableOrderSyncContractPrice,
              data: [
                {
                  label: '全部',
                  value: '',
                },
                {
                  label: '未刷价',
                  value: '0',
                },
                {
                  label: '已刷价',
                  value: '1',
                },
              ],
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              label: '制单人',
              type: 'custom',
              key: 'operator_id',
              component: operator,
            },
            // {
            //   checked: false,
            //   label: '订单类型',
            //   type: 'Select',
            //   key: 'order_type',
            //   data: [],
            //   props: {
            //     filterable: true,
            //     clearable: true,
            //     'filter-by-label': true,
            //     placeholder: '请选择制订单类型',
            //   },
            // },
            {
              checked: false,
              label: '订单类型',
              type: 'custom',
              key: 'order_type',
              component: orderType,
            },
            {
              checked: false,
              label: '支付方式',
              type: 'custom',
              key: 'pay_way',
              attrs: {
                multiple: true
              },
              component: payWay,
            },
            {
              show: this.isFundAllocationEnabled,
              type: 'Select',
              label: '分账订单',
              key: 'is_account',
              data: IS_ACCOUNT_ORDER_ENUM,
              placeholder: '分账订单',
            },
            {
              checked: false,
              label: '客户签名图片',
              type: 'Select',
              key: 'filter_signature_pic',
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '有图片',
                },
                {
                  value: '2',
                  label: '无图片',
                },
              ],
            },
            {
              checked: false,
              label: '货物送达图片',
              type: 'Select',
              key: 'filter_delivery_pic',
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '有图片',
                },
                {
                  value: '2',
                  label: '无图片',
                },
              ],
            },
            {
              checked: false,
              label: '实收变更图片',
              type: 'Select',
              key: 'is_modify_pic',
              props: {
                placeholder: '全部',
              },
              data: [
                {
                  value: '',
                  label: '全部',
                },
                {
                  value: '1',
                  label: '已上传',
                },
                {
                  value: '0',
                  label: '未上传',
                },
              ],
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              type: 'Input',
              label: '物流单号',
              key: 'express_no',
              show: this.isOpenExpress100,
              props: {
                placeholder: '请输入物流单号',
              },
            },
            {
              checked: false,
              type: 'Input',
              label: '订单备注',
              key: 'remark',
              props: {
                placeholder: '请输入订单备注',
              },
            },
            {
              checked: false,
              label: '线路',
              type: 'custom',
              key: 'line_id',
              defaultValue: this.$route.query.line_id || '',
              component: circuit,
            },
            {
              checked: false,
              type: 'Input',
              label: '子账号',
              key: 'sub_user_search',
              props: {
                placeholder: '请输入',
              },
            },
            ...(this.isOpenTraceabilityPlatform4 || this.sysConfig.open_openapi == 1 ? [{
              checked: false,
              type: 'Input',
              label: '外部系统编号',
              key: 'third_party_order_no',
              props: {
                placeholder: '请输入',
              },
            }] : []),
            {
              checked: true,
              required: true,
              relation: 'order_tag',
              label: '标签筛选',
              type: 'custom',
              show: this.isOpenOrderTag,
              key: 'order_tag_filter',
              component: RadioGroup,
              props: {
                data: [
                  {
                    label: '同时存在',
                    value: '1',
                  },
                  {
                    label: '存在一个',
                    value: '2',
                  },
                  {
                    label: '无标签',
                    value: '3',
                  },
                ],
                on: {
                  'on-reset': (value) => {
                    // 重置时恢复订单标签可选
                    this._setOrderTagDisabled(+value === 3);
                  },
                },
              },
              style: {
                width: '900px',
              },
              defaultValue: '1',
              onChange: (value) => {
                // 选中标签筛选中的无标签之后，订单标签筛选项不可选择
                this._setOrderTagDisabled(+value === 3);
                return {
                  value,
                  stop: true,
                };
              },
            },
            ...(this.sysConfig.tc_platform == 1 ? [{
              type: 'Select',
              key: 'user_business_type',
              label: '客户业态',
              data: [
                {
                  label: '全部',
                  value: '',
                },
                ...BUSINESS_TYPE_LIST
              ],
              props: {
                placeholder: '选择客户业态',
              },
            }] : []),
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
          ],
        },
        {
          items: [
            {
              checked: true,
              required: true,
              block: true,
              relation: 'order_tag',
              label: '订单标签',
              type: 'custom',
              show: this.isOpenOrderTag,
              key: 'order_tag',
              tagTopStart: true,
              labelWidth: '70',
              attrs: {
                data: [],
                disabled: false,
                key: 'order_tag',
              },
              component: orderTag,
              onChange(value) {
                return {
                  value,
                  stop: true,
                };
              },
            },
          ],
        },
        {
          items: [
            {
              checked: false,
              required: false,
              show: true,
              block: true,
              tagTopStart: true,
              label: '客户标签',
              key: 'user_tag',
              type: 'custom',
              attrs: {
                keyName: 'user_tag',
              },
              component: userTag,
              onChange: (value) => {
                return { value, stop: true };
              },
            },
          ],
        },
      ];
      // 全部tab 才显示订单状态筛选项
      if (this.filters.modeStatus === '0') {
        let dataArray = [
          {
            label: '待付款',
            value: '0',
          },
          {
            label: '待发货',
            value: '1',
          },
          {
            label: '待收货',
            value: '2',
          },
          {
            label: '已完成',
            value: '4',
          },
          {
            label: '待审核',
            value: '5',
          },
        ];
        const clearAction = (value) => {
          // 清除之前的本地缓存，防止加权限之后查出了已关闭的数据
          const delIndex = this.defaultOrderState.findIndex(
            (item) => item === value,
          );
          if (~delIndex) {
            this.defaultOrderState.splice(delIndex, 1);
            StorageUtil.setLocalStorage(
              'order-list-checkbox-state',
              this.defaultOrderState,
            );
          }
        }
        if (this.hasAuthority('A002001014')) {
          dataArray.splice(4, 0, {
            label: '已关闭',
            value: '3',
          });
        } else {
          clearAction('3')
        }

        if (this.sysConfig.group_order_audit == 1) {
          dataArray.push({
            label: '待集团审核',
            value: '6',
          })
        } else {
          clearAction('6')
        }
        advanceItems.push({
          items: [
            {
              checked: true,
              required: true,
              label: '订单状态',
              type: 'custom',
              noReset: true,
              key: 'mode',
              props: {
                data: [...dataArray],
                key: 'mode',
              },
              style: {
                width: '900px',
              },
              defaultValue: this.defaultOrderState,
              component: CheckboxGroup,
              onChange: (value) => {
                // 勾选状态存在本地
                StorageUtil.setLocalStorage('order-list-checkbox-state', value);
                this.defaultOrderState = StorageUtil.getLocalStorage(
                  'order-list-checkbox-state',
                );
                return {
                  value,
                  stop: true,
                };
              },
            },
          ],
        });
      }
      this.advanceItems = advanceItems;
    },
    async initColumns() {
      let columns = [
        {
          type: 'titleCfg',
          sumConfig: {
            fields: [
              {
                title: '下单',
                key: 'allPrice',
              },
              {
                title: '发货',
                key: 'allActualPrice',
              },
              {
                title: '运费',
                key: 'allFreightPrice',
                defaultChecked: false,
              },
              {
                title: '退货',
                key: 'allReturnPrice',
                defaultChecked: false,
              },
              {
                title: '客户单',
                key: 'allBehalfPrice',
                show: () => this.isEnableShowOrderListBehalfInfo,
                defaultChecked: false,
              },
              {
                title: '财务单',
                key: 'allNewBehalfPrice',
                show: () => this.isEnableShowOrderListBehalfInfo,
                defaultChecked: false,
              },
              {
                title: '实际金额',
                key: 'allPageActualPrice',
              },
              {
                title: '下单数量',
                key: 'allAmount',
                defaultChecked: false,
              },
              {
                title: '商品种类数',
                key: 'allGoodsNum',
                defaultChecked: false,
              },
              {
                title: '发货数量',
                key: 'allActualAmount',
                defaultChecked: false,
              },
              {
                title: '订单重量kg',
                key: 'order_weight',
                defaultChecked: false,
              },
            ],
          },
          minWidth: 40,
          // 需添加width，不然fixed: 'left'不起作用
          width: 40,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'order',
          style: {
            paddingRight: 0,
            paddingLeft: 0,
          },
        },
        {
          title: '订单号',
          key: 'order_no',
          fixed: 'left',
          resizable: true,
          minWidth: 180,
          style: {
            paddingRight: 0,
            paddingLeft: 0,
          },
          render: (h, params) => {
            let data = params.row;
            const isModifyOrder = Number(params.row.is_modify_order) === 1;
            const isFirstOrder = Number(params.row.is_first_order) === 1;
            const isReturnOrder = Number(params.row.return_flag) === 1;
            const isAlterOrder = Number(params.row.is_out_reverse_audit) === 1;
            const is_central_purchase =
              Number(params.row.is_central_purchase) === 1;
            const is_brush_price = Number(params.row.is_brush_price) === 1;
            const isMergeOrder = Number(params.row.is_combine_add_order) === 1;
            const isAsynOrder = Number(params.row.order_type) == 4;
            const isFriendPay = Number(params.row.is_friend_pay) == 1;
            const isSplitOrder = Number(params.row.is_split_order) === 1;
            let friendPayTag = '';
            let mergeOrderTag = '';
            let returnTag = '';
            let alterTag = '';
            let firstOrderTag = '';
            let is_central_purchaseTag = '';
            let is_modify_order = '';
            let is_brush_priceTag = '';
            let tagArr = [];
            let tagArrTwo = [];

            let iconLock = null;
            if (
              data.o_lock_status &&
              this.sysConfig.is_open_order_lock != 0 &&
              data.mode == 300
            ) {
              // 订单锁定状态【0未锁，1半锁，2全锁】
              if (data.o_lock_status == 2) {
                iconLock = h(
                  Tooltip,
                  {
                    props: {
                      content: `订单全锁定状态下，${this.lockPlatformText}无法执行订单编辑和删除操作`,
                      maxWidth: 220,
                      transfer: true,
                    },
                  },
                  [
                    h(Icon, {
                      props: {
                        icon: 'quansuo',
                        size: 12,
                      },
                      class: 'ml5',
                      style: {
                        color: '#ff6600',
                        position: 'relative',
                        top: '-1px',
                      },
                    }),
                  ],
                );
              } else if (data.o_lock_status == 1) {
                iconLock = h(
                  Tooltip,
                  {
                    props: {
                      content: `订单半锁定状态下，${this.lockPlatformText}无法执行订单删除操作`,
                      maxWidth: 220,
                      transfer: true,
                    },
                  },
                  [
                    h(Icon, {
                      props: { icon: 'bansuo', size: 12 },
                      class: 'ml5',
                      style: {
                        position: 'relative',
                        top: '-1px',
                      },
                    }),
                  ],
                );
              }
            }

            // #region orderTags
            if (isFirstOrder) {
              // 首
              firstOrderTag = h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'top',
                    content: '该客户第一次下单',
                  },
                  style: {
                    height: '14px',
                    lineHeight: '14px',
                    width: '14px',
                  },
                },
                [
                  h(Icon, {
                    props: {
                      icon: 'first-order',
                      size: 14,
                    },
                    style: {
                      cursor: 'pointer',
                    },
                  }),
                ],
              );
              tagArr.push(firstOrderTag);
            }
            if (isSplitOrder) {
              tagArr.push(
                h(Icon, {
                  props: {
                    icon: 'chai',
                    size: 14,
                  },
                  style: {
                    color: '#03AC54',
                  },
                }),
              );
            }
            if (isMergeOrder) {
              mergeOrderTag = h(Icon, {
                props: {
                  icon: 'he',
                  size: 14,
                },
                style: {
                  color: '#03AC54',
                },
              });
              tagArr.push(mergeOrderTag);
            }
            if (isAsynOrder) {
              // 同步
              let isAsynOrderTag = h(Icon, {
                props: {
                  icon: 'tongbudingdan',
                  size: 14,
                },
              });
              tagArr.push(isAsynOrderTag);
            }
            if (is_central_purchase) {
              // 集
              is_central_purchaseTag = h(Icon, {
                props: {
                  icon: 'central',
                  size: 14,
                },
              });
              tagArr.push(is_central_purchaseTag);
            }
            if (is_brush_price) {
              // 刷
              is_brush_priceTag = h(Icon, {
                props: {
                  icon: 'shua',
                  size: 14,
                },
                style: {
                  color: '#3577f7',
                },
              });
              tagArr.push(is_brush_priceTag);
            }
            if (isAlterOrder) {
              // 改
              alterTag = h(Icon, {
                props: {
                  icon: 'gai',
                  size: 14,
                },
                style: {
                  color: '#f13130',
                },
              });
              tagArr.push(alterTag);
            }
            if (isModifyOrder) {
              // 变
              is_modify_order = h(Icon, {
                props: {
                  icon: 'bian',
                  size: 14,
                },
              });
              tagArr.push(is_modify_order);
            }
            if (isReturnOrder) {
              // 退
              returnTag = h(Icon, {
                props: {
                  icon: 'return',
                  size: 14,
                },
              });
              tagArr.push(returnTag);
            }
            if (isFriendPay) {
              // 代付
              friendPayTag = h(Icon, {
                props: {
                  icon: 'daifu',
                  size: 14,
                },
              });
              tagArr.push(friendPayTag);
            }
            let tagBoxTemp = '';
            let temp = h(
              'a',
              {
                class: {
                  primary: true,
                },
                on: {
                  click: () => {
                    this.toOrderDetail(data);
                  },
                },
              },
              tagArr.length === 1
                ? [
                    tagArr[0],
                    h('span', {}, data.order_no),
                  ]
                : data.order_no,
            );
            tagArrTwo = tagArr;
            if (tagArr.length > 2 && data._show_order_no_tag) {
              temp = h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'top',
                    // maxWidth: 180,
                    theme: 'light',
                  },
                },
                [
                  h(
                    'div',
                    {
                      slot: 'content',
                    },
                    [
                      h(
                        'div',
                        {
                          class: ['order-list-tag-box', 'mr4'],
                        },
                        [...tagArr],
                      ),
                      h(
                        'p',
                        {
                          style: {
                            color: '#303030',
                          },
                        },
                        data.order_no,
                      ),
                    ],
                  ),
                  h(
                    'a',
                    {
                      class: {
                        primary: true,
                      },
                      on: {
                        click: () => {
                          data._show_order_no_tag = false;
                          this.toOrderDetail(data);
                        },
                      },
                    },
                    tagArr.length === 1
                      ? [tagArr[0], data.order_no]
                      : data.order_no,
                  ),
                ],
              );
              // 左侧最多只展示2个tag
              tagArrTwo = tagArr.slice(-2);
            }
            if (tagArr.length) {
              tagBoxTemp = h(
                'div',
                {
                  class: ['order__list__order-no-tag'],
                },
                [...tagArrTwo],
              );
            }
            let tempRender = h('div', {}, [
              h(
                'div',
                {
                  style: { display: 'flex', alignItems: 'center' },
                },
                [temp, iconLock],
              ),
              h(
                'div',
                {
                  style: {
                    color: '#999999',
                  },
                },
                data.create_time,
              ),
            ]);
            // #endregion orderTags

            return h(
              'div',
              {
                class: {
                  'order__list__order-no': tagArr.length > 0,
                },
                style: {
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                },
              },
              tagArr.length > 1 ? [tagBoxTemp, tempRender] : [tempRender],
            );
          },
        },
        {
          resizable: true,
          title: '客户名称',
          poptip: true,
          key: 'shop_name',
          minWidth: 140,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#shopNameSortIcon').click();
                      },
                    },
                  },
                  '客户名称',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.shop_name,
                    id: 'shopNameSortIcon',
                  },
                  ref: 'TableHeadSortIcon',
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('shop_name', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
          render: function (h, params) {
            let data = params.row;
            let name = data.shop_name;
            return h('div', [h('div', {}, name)]);
          },
        },
        {
          title: '客户业态',
          key: 'user_business_type_desc',
          show: () => {
            return this.sysConfig.tc_platform == 1
          },
          align: 'left',
          minWidth: 100
        },
        {
          resizable: true,
          title: '客户编码',
          minWidth: 105,
          key: 'user_code',
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#userCodeSortIcon').click();
                      },
                    },
                  },
                  '客户编码',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.user_code,
                    id: 'userCodeSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('user_code', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '客户标签',
          key: 'user_tag_text',
          minWidth: 120,
        },
        {
          resizable: true,
          title: '子机构',
          key: 'subsidiary',
          show: this.isOpenSubsidiary,
          minWidth: 120,
        },
        {
          resizable: true,
          title: '餐次',
          key: 'meal_type_name',
          show: this.isOpenNewRecipe,
          minWidth: 140,
        },
        {
          resizable: true,
          title: '菜谱名称',
          key: 'recipe_name',
          show: this.isOpenNewRecipe,
          minWidth: 140,
        },
        {
          resizable: true,
          title: '就餐人数',
          key: 'number_of_diners',
          minWidth: 90,
          show:
            this.isOpenNewRecipe &&
            this.sysConfig.is_open_accompanying_of_diners == 1,
        },
        {
          resizable: true,
          title: '餐标',
          key: 'meal_price',
          show: this.isOpenNewRecipe,
          minWidth: 140,
        },
        {
          resizable: true,
          title: '餐标合计',
          key: 'total_meal_price',
          show: this.isOpenNewRecipe,
          minWidth: 90,
        },
        {
          resizable: true,
          title: '临时分拣码',
          key: 'sort_code',
          minWidth: 120,
        },
        {
          resizable: true,
          title: '押金筐金额',
          align: 'right',
          key: 'deposit_basket_price',
          minWidth: 110,
        },
        {
          resizable: true,
          title: '下单金额',
          key: 'price',
          align: 'right',
          minWidth: 90,
          render: (h, params) => {
            let data = params.row;
            return h('span', {}, '' + data.price);
          },
        },
        {
          resizable: true,
          title: '发货金额',
          align: 'right',
          key: 'actual_price',
          minWidth: 90,
          // render: (h, params) => {
          //   let data = params.row;
          //   let value = '';
          //   value = data.actual_price * 1 > 0 ? '' + data.actual_price : '-';
          //   return h('span', {}, value);
          // },
        },
        {
          resizable: true,
          title: '少补金额',
          align: 'right',
          key: 'supply_price',
          minWidth: 90,
        },
        {
          resizable: true,
          title: '订单重量(kg)',
          align: 'right',
          minWidth: 95,
          key: 'order_weight',
        },
        {
          resizable: true,
          title: '下单时运费',
          align: 'right',
          minWidth: 90,
          key: 'before_freight_price',
        },
        {
          resizable: true,
          title: '配送方式',
          align: 'right',
          key: 'delivery_method_text',
          minWidth: 100,
        },
        {
          resizable: true,
          title: '自提点',
          align: 'right',
          key: 'self_pickup_point_name',
          minWidth: 100,
        },
        {
          resizable: true,
          title: '退货金额',
          align: 'right',
          key: 'return_price',
          minWidth: 90,
        },
        {
          resizable: true,
          title: '客户单',
          align: 'right',
          key: 'behalf_price',
          minWidth: 90,
        },
        {
          resizable: true,
          title: '财务单',
          align: 'right',
          key: 'new_behalf_price',
          minWidth: 90,
        },
        {
          resizable: true,
          title: '发货数量',
          align: 'right',
          key: 'total_order_actual_quantity',
          minWidth: 90,
        },
        {
          resizable: true,
          title: '发货日期',
          key: 'delivery_date',
          minWidth: 105,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#deliveryDateSortIcon').click();
                      },
                    },
                  },
                  '发货日期',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.delivery_date,
                    id: 'deliveryDateSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('delivery_date', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          resizable: true,
          poptip: true,
          title: '仓库',
          key: 'storage',
          minWidth: 140,
        },
        {
          resizable: true,
          title: '拆单组合',
          key: 'split_group_name',
          minWidth: 120,
        },
        {
          resizable: true,
          poptip: true,
          title: '子账号',
          key: 'sub_user_name',
          minWidth: 120,
        },
        {
          resizable: true,
          poptip: true,
          title: '备注',
          key: 'remark',
          minWidth: 120,
        },
        {
          resizable: true,
          poptip: true,
          title: '订单标签',
          key: 'order_tag_text',
          minWidth: 120,
        },
        {
          resizable: true,
          title: '回单状态',
          key: 'is_receipt_text',
          minWidth: 85,
          render: (h, params) => {
            let color = '';
            switch (params.row.is_receipt_text) {
              case '未回单':
                color = 'status-error';
                break;
            }
            return h('div', [
              h(
                'span',
                {
                  attrs: {
                    class: color,
                  },
                },
                params.row.is_receipt_text,
              ),
            ]);
          },
        },
        {
          key: 'issue_order_sync_status_text',
          title: '同步状态',
          minWidth: 85,
          render: (h, { row }) => {
            if (+row.issue_order_sync_status === 2) {
              return h('span', row.issue_order_sync_status_text);
            } else
              return h(
                'a',
                {
                  class: {
                    primary: true,
                  },
                  on: {
                    click: () => {
                      this.$router.push({
                        path: '/bills/order/edit',
                        query: {
                          keep_scroll: 1,
                          id: row.issue_id,
                        },
                      });
                    },
                  },
                },
                row.issue_order_sync_status_text,
              );
          },
        },
        {
          resizable: true,
          title: '打印次数',
          key: 'print_times',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#printTimesSortIcon').click();
                      },
                    },
                  },
                  '打印次数',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.print_times,
                    id: 'printTimesSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('print_times', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
          align: 'right',
        },
        {
          title: '发货时间',
          key: 'order_delivery_time',
          minWidth: 110,
        },
        {
          minWidth: 100,
          resizable: true,
          title: '订单状态',
          key: 'mode_txt',
          render: (h, { row }) => {
            const { mode_txt } = row;
            return (
              <span class={{ 'status-warning': ['待审核'].includes(mode_txt) }}>
                {mode_txt}
              </span>
            );
          },
        },
        {
          minWidth: 90,
          resizable: true,
          title: '付款状态',
          key: 'pay_text',
          render: (h, params) => {
            var data = params.row;
            return h(
              'span',
              {
                class: {
                  'c-red-color': data.pay_text === '未付款',
                },
              },
              data.pay_text,
            );
          },
        },
        {
          minWidth: 100,
          resizable: true,
          title: '订单来源',
          key: 'source_txt',
        },
        {
          minWidth: 200,
          resizable: true,
          title: '物流单号',
          key: 'express_no',
          show: this.isOpenExpress100,
          render: (h, params) => {
            let data = params.row;
            if (!data.express_no) {
              return '--';
            }
            return h('div', [
              h(
                'a',
                {
                  style: {
                    color: 'var(--primary-color)',
                  },
                  on: {
                    click: () => {
                      this.handleShowLogistics(data);
                    },
                  },
                },
                data.express_no,
              ),
            ]);
          },
        },
        {
          minWidth: 100,
          resizable: true,
          title: '订单类型',
          key: 'order_type_txt',
        },
        {
          minWidth: 140,
          resizable: true,
          title: '线路',
          key: 'route',
          renderHeader: (h) => {
            const params = this.getParams();
            if (DateUtil.diffDay(params.startTime, params.endTime) >= 31)
              return h('div', '线路');
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                },
              },
              [
                h(
                  'span',
                  {
                    on: {
                      click: () => {
                        $('#routeSortIcon').click();
                      },
                    },
                  },
                  '线路',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.line_name,
                    id: 'routeSortIcon',
                    asc: true,
                  },
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('line_name', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '确认送达时间',
          key: 'arrival_time',
          minWidth: 110,
        },
        {
          minWidth: 100,
          resizable: true,
          title: '支付方式',
          key: 'pay_way_text',
        },
        {
          minWidth: 100,
          title: '司机',
          resizable: true,
          key: 'driver',
        },
        {
          minWidth: 100,
          title: '业务员',
          key: 'salesman',
          renderHeader: (h) => {
            const params = this.getParams();
            if (DateUtil.diffDay(params.startTime, params.endTime) >= 31)
              return h('div', '业务员');
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                },
              },
              [
                h(
                  'span',
                  {
                    on: {
                      click: () => {
                        $('#salesManSortIcon').click();
                      },
                    },
                  },
                  '业务员',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.sale_name,
                    id: 'salesManSortIcon',
                    asc: true,
                  },
                  on: {
                    onChange: (e) => {
                      this.handleSortKeyChange('sale_name', e);
                      this.$refs.orderListTable.fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '送货时间',
          key: 'delivery_time',
          minWidth: 140,
        },
        {
          title: '生产时间',
          key: 'production_date',
          minWidth: 120,
        },
        {
          title: '内部备注',
          key: 'inner_remark',
          minWidth: 120,
        },
        {
          title: '服务费',
          key: 'service_charge',
          minWidth: 100,
        },
        {
          title: '实际服务费',
          key: 'actual_service_charge',
          minWidth: 100,
        },
        {
          title: '制单人',
          key: 'op_user',
          minWidth: 100,
        },
        {
          title: '所属供应商',
          key: 'split_provider_name',
          show: this.isSplitOrderByProvider,
          minWidth: 120,
        },
        {
          title: '分账订单',
          key: 'is_account',
          enum: IS_ACCOUNT_ORDER_ENUM,
          minWidth: 100,
        },
        {
          title: '就餐日期',
          key: 'diner_dates',
          show: +this.sysConfig.is_raw_package_recipe === 1,
          minWidth: 100,
        },
        {
          title: '实际金额',
          key: 'real_order_price',
          minWidth: 80,
        },
        {
          minWidth: 80,
          resizable: true,
          title: '陪餐人数',
          show:
            +this.sysConfig.is_new_recipe === 1 &&
            this.sysConfig.is_open_accompanying_of_diners == 1,
          key: 'accompanying_of_diners',
        },
        {
          title: '审核通过时间',
          key: 'audit_time',
          show: this.isOpenOrderAudit,
          minWidth: 160,
        },
        {
          title: '下单数量',
          key: 'total_order_quantity',
          minWidth: 80,
        },
        {
          title: '客户序号编码',
          key: 'user_serial_code',
          minWidth: 102,
        },
        {
          title: '商品种类数',
          key: 'order_goods_number',
          minWidth: 90,
        },
        {
          title: '对账金额',
          key: 'bill_price',
          minWidth: 80,
        },
        {
          title: '运费',
          key: 'freight_price',
          minWidth: 80,
        },
        {
          title: '对账服务费',
          key: 'bill_service_charge',
          minWidth: 90,
        },
        {
          title: '对账退货金额',
          key: 'bill_return_price',
          minWidth: 102,
        },
        {
          title: '拆单源订单',
          minWidth: 140,
          key: 'split_source_order_no',
          render: (h, { row }) => {
            if (row.split_source_order_no !== '-') {
              return h(
                'a',
                {
                  class: 'primary',
                  on: {
                    click: () => {
                      this.toOrderDetail(
                        { id: row.split_source_order_id },
                        '_blank',
                      );
                    },
                  },
                },
                row.split_source_order_no,
              );
            } else {
              return h('span', row.split_source_order_no);
            }
          },
        },
        {
          width: 110,
          title: '客户签名图片',
          key: 'signature_pic',
          type: 'static',
          render: (h, params) => {
            if (!params.row.signature_pic) return h('span', '--');

            return h('img', {
              style: {
                width: '36px',
                height: '36px',
                cursor: 'pointer',
              },
              on: {
                click: () => {
                  this.viewImage(params.row.signature_pic, 0);
                },
              },
              attrs: {
                src: params.row.signature_pic
                  ? formatGoodsThumbnailPath(params.row.signature_pic)
                  : '',
              },
            });
          },
        },
        {
          width: 180,
          title: '货物送达图片',
          key: 'delivery_pic',
          type: 'static',
          render: (h, params) => {
            if (params.row.delivery_pic && !params.row.delivery_pic.length) return h('span', '--');

            return h('div', {
              class: 'order-list-delivery-pic',
            }, [
              params.row.delivery_pic.map((item) => {
                return h('img', {
                  style: {
                    width: '36px',
                    height: '36px',
                    cursor: 'pointer',
                    'margin-right': '5px',
                  },
                  on: {
                    click: () => {
                      this.viewImage(item, 0);
                    },
                  },
                  attrs: {
                    src: formatGoodsThumbnailPath(item),
                  },
                });
              }),
            ]);
          },
        },
        ...(this.isOpenTraceabilityPlatform4 || this.sysConfig.open_openapi == 1 ? [
        {
          title: '外部系统编号',
          key: 'third_party_order_no',
          minWidth: 102,
        }] : []),
        {
          resizable: false,
          title: '操作',
          key: 'action',
          fixed: 'right',
          type: 'action',
          width: this.is_sys_supervisor ? 80 : 210,
          actionCountLimit: (params) => {
            // 已经关闭状态下按钮有点显示不下，外面就只显示两个按钮
            if (+params.row.mode === 700) {
              return 2;
            }
            return 3;
          },
          actions: (params) => {
            let { row } = params;
            let actions = [];
            if (row.isCanPayment === 'Y') {
              actions.push({
                name: '付款',
                action: (params) => {
                  this.handlePayPublic(params.row);
                },
              });
            }
            if (row.isCanEdit === 'Y') {
              actions.push({
                name: '编辑',
                action: (params) => {
                  this.toEdit(params.row);
                },
              });
            }
            if (row.isCanApproval === 'Y') {
              actions.push({
                name: '核算',
                action: (params) => {
                  this.toApprovalOrder(params.row);
                },
              });
            }
            if (row.isCanAudit === 'Y') {
              actions.push({
                name: '审核',
                confirm: '确定审核通过？',
                action: (params) => {
                  this.handleSingeAuditOrder(params.row);
                },
              });
            }
            if (row.isCanPrint === 'Y') {
              actions.push({
                name: '打印',
                action: (params) => {
                  this._onPrint(params);
                },
              });
            }

            if (row.isCanExport === 'Y') {
              actions.push({
                name: '导出',
                action: (params) => {
                  this.useExportOrder(params.row);
                },
              });
            }
            if (row.isCanReturn === 'Y') {
              actions.push({
                name: '退货',
                action: (params) => {
                  orderReturnUnAuditCheck(
                    { order_id: params.row.id, order_no: params.row.order_no },
                    this,
                  ).then(() => {
                    this.toReturnGoods(params.row);
                  });
                },
              });
            }
            if (row.isCanComplete === 'Y') {
              actions.push({
                name: '完成',
                action: (params) => {
                  this.finishOrder(params.row);
                },
              });
            }
            if (row.isCanCopy == 'Y') {
              actions.push({
                name: '复制',
                action: (params) => {
                  this.copyId = params.row.id;
                  this.copyNo = params.row.order_no;
                  this.$refs.smodal.open();
                },
              });
            }
            if (this.isOpenAcceptanceOrderExport) {
              actions.push({
                name: '验收单导出',
                action: (params) => {
                  this.acceptanceOrderExport(params.row);
                },
              });
            }
            if (row.isEditBasket === 'Y') {
              actions.push({
                name: '修改周转筐',
                action: (params) => {
                  this.showBatchUpdateBasketModal(params.row.id);
                },
              });
            }
            {
              /* 待发货/待审核 */
            } // 是否订单合并，开启隐藏手动拆单按钮
            if (
              (+row.mode === 300 || +row.mode === 100) &&
              !this.isOpenOrderCombineNoAuto
            ) {
              actions.push({
                name: '手动拆单',
                action: (params) => {
                  this.$refs.manualSplitOrder.showModal(params.row);
                },
              });
            }

            if (row.isCanCancel === 'Y') {
              actions.push({
                name: '关闭',
                action: (params) => {
                  this.delOrder(params.row);
                },
                confirm(params) {
                  let confirm = '';
                  if (params.row.pay_text === '已付款') {
                    confirm = '订单已经支付 是否继续取消？';
                  } else {
                    confirm = '确定取消订单信息，取消的订单将不能还原';
                  }
                  return confirm;
                },
              });
            }
            // 开启订单列表显示代开信息
            if (
              this.isEnableShowOrderListBehalfInfo &&
              (row.order_type != 4 || this.consortium_pricing_type == 3)
            ) {
              actions.push({
                name: '客户单',
                confirm: !params.row.isCanBehalf
                  ? !params.row.behalf_order_id
                    ? '还没有客户单，是否复制新增客户单？'
                    : '已有客户单，是否再次新增客户单？'
                  : '',
                action: () => {
                  if (!params.row.isCanBehalf) {
                    const path = this.is_open_issue_order
                      ? '/bills/order/add'
                      : '/behalfNewOrder';
                    this.$router.push({
                      path: path,
                      query: {
                        keep_scroll: 1,
                        copy_id: params.row.id,
                      },
                    });
                  } else {
                    const path = this.is_open_issue_order
                      ? '/bills/order/detail'
                      : '/behalfOrderDetail';
                    this.$router.push({
                      path: path,
                      query: {
                        keep_scroll: 1,
                        id: params.row.behalf_order_id,
                      },
                    });
                  }
                },
              });
              // 旧版单据管理才需要“财务单”
              if (!this.is_open_issue_order) {
                actions.push({
                  name: '财务单',
                  confirm:
                    params.row.isCanBehalf && !params.row.isCanNewBehalf
                      ? '还没有财务单，是否复制新增财务单？'
                      : '',
                  action: () => {
                    if (!params.row.isCanBehalf) {
                      this.errorMessage('请先新增客户单');
                      return false;
                    }
                    if (!params.row.isCanNewBehalf) {
                      this.$router.push({
                        path: '/bills/finance/add',
                        query: {
                          keep_scroll: 1,
                          copy_id: params.row.behalf_order_id,
                        },
                      });
                    } else {
                      this.$router.push({
                        path: '/bills/finance/detail',
                        query: {
                          keep_scroll: 1,
                          id: params.row.new_behalf_order_id,
                        },
                      });
                    }
                  },
                });
              }
            }
            if (this.isEnableOrderListBehalf) {
              actions.push({
                name: '单据新增',
                confirm: !params.row.issue_id
                  ? '该订单还没有单据管理单，是否复制新增单据管理单？'
                  : '',
                action: () => {
                  if (!params.row.issue_id) {
                    this.$router.push({
                      path: '/bills/order/add',
                      query: {
                        keep_scroll: 1,
                        copy_id: params.row.id,
                      },
                    });
                  } else {
                    this.$router.push({
                      path: '/bills/order/edit',
                      query: {
                        keep_scroll: 1,
                        id: params.row.issue_id,
                      },
                    });
                  }
                },
              });
            }
            if (row.isCanPrint === 'Y' && this.hasAuthority('A002001119')) {
              actions.push({
                name: '分享',
                action: (params) => {
                  this.listItem = params.row;
                  this.showShare = true;
                },
              });
            }
            if (row.mode == 300 && this.sysConfig.is_open_order_lock != 0) {
              if (hasAuthority('order_orderList_lock')) {
                actions.push(
                  {
                    name: '订单锁定(半锁)',
                    action: (params) => {
                      this.lockOrder(1, params.row.id);
                    },
                  },
                  {
                    name: '订单锁定(全锁)',
                    action: (params) => {
                      this.lockOrder(2, params.row.id);
                    },
                  },
                );
              }
              if (hasAuthority('order_orderList_unlock')) {
                actions.push({
                  name: '订单解锁',
                  action: (params) => {
                    this.lockOrder(0, params.row.id);
                  },
                });
              }
            }

            // 监管人员登录的话, 除了导出其他都隐藏
            if (this.is_sys_supervisor) {
              return actions.filter((k) => k.name === '导出');
            }

            // 集团待审核订单, 除了复制其他都隐藏
            if (row.mode == 99) {
              return actions.filter((k) => k.name === '复制');
            }

            actions.push({
              name: '导出商品溯源',
              action: (params) => {
                let routeData = this.$router.resolve({
                  path: '/order/export-commodity-trace',
                  query: {
                    order_id: params.row.id,
                  },
                });
                window.open(routeData.href, '_blank', 'noopener,noreferrer');
              },
            });
            return actions;
          },
        },
      ];
      const showCheckBoxStatus = [
        orderMode.all,
        orderMode.unaudit,
        orderMode.undelivery,
        orderMode.unreceived,
        orderMode.completed,
        orderMode.closed,
      ];
      if (
        showCheckBoxStatus.includes(Number(this.filters.modeStatus)) &&
        !this.is_sys_supervisor
      ) {
        columns.splice(1, 0, {
          type: 'selection',
          key: 'selection',
          fixed: 'left',
          width: 40,
          style: {
            paddingLeft: '6px',
          },
        });
      }
      columns = columns.filter((item) => item.show !== false);
      const customFieldKeys = await this.getOrderCustomizeFieldKeys();
      columns.splice(columns.length - 1, 0, ...customFieldKeys);
      this.columns = this.deepClone(columns);
    },
    // 获取订单自定义字段
    getOrderCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '3,14',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                return {
                  title: item.name,
                  key: item.key,
                  poptip: true,
                };
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
    /**
     * 开启 配送单打印自由选择模板 打印次数没刷新处理
     */
    _printTimesChange(val) {
      if (val) {
        let list = this.$refs.orderListTable.getData();
        let find = list.find((item) => item.id == val.id);
        if (find) {
          find.print_times = +find.print_times + 1;
        }
        this.$refs.orderListTable.setData(list);
      }
    },
    /**
     * 打印处理事件
     */
    _onPrint(params) {
      // 云打印
      console.log('isOpenYunPrint', this.isOpenYunPrint);
      if (this.isOpenYunPrint) {
        cloudPrinting
          .printOrder({
            order_id: params.row.id,
            order_type: 1,
          })
          .then(({ status, message, data }) => {
            if (status) {
              if (data.fail_total > 0) {
                let text = '';
                data.fail_data.forEach((item) => {
                  text += `<p>订单号：${item.no}，失败原因：${item.message}。<p>`;
                });
                this.$snotice({
                  type: 'error',
                  title: `以下${data.fail_total}条订单打印失败`,
                  text: text,
                });
              } else {
                this.successMessage('打印成功');
              }
            } else {
              this.errorMessage(message);
            }
          });
        return;
      }
      if (this.isCanChooseDeliveryOrderPrintTemplate) {
        this.$refs.printTemplateChoose.open({
          requestUrl: '/superAdmin/orderSuper/authorizedGetPrintData',
          printClass: 'tpl_print_view_order',
          params: {
            type: 'ORDER',
            user_id: params.row.user_id,
            id: params.row.id,
            is_merge: 0,
          },
          printAttrs: {
            order_id: params.row.id,
          },
          onOk: () => {
            this.$emit('getDataList');
          },
        });
        return false;
      } else {
        params.row.print_times = Number(params.row.print_times) + 1;
        this._printOrder({
          requestUrl: '/superAdmin/orderSuper/authorizedGetPrintData',
          id: params.row.id,
          is_merge: 0,
        });
      }
    },
    handleSelectionChange(selection) {
      this.selectedOrders = selection;
    },
    handleResetChange() {
      this.filters.commodity_id = '';
      StorageUtil.removeLocalStorage('order_split_rule_id');
      this.$refs.orderListTable.setValue('split_rule_id', []);
    },
    batchCloseOrder() {
      let orderIds = this.selectedOrders.map((item) => item.id);
      let params = {
        order_id: orderIds.join(','),
      };
      orderService.batchClose(params).then((res) => {
        let { status, message } = res;
        if (status) {
          this.selectedOrders = [];
          this.successMessage(message || '关闭成功！');
          this.fetchData();
        } else {
          this.errorMessage(message);
        }
      });
    },
    handleSingeAuditOrder({id}) {
      orderService.batchAudit({
        order_id_string: `[${id}]`,
      }).then(res => {
        const { status, message } = res;
        if (status) {
          this.successMessage(message || '审核成功');
          this.fetchData();
        } else {
          this.errorMessage(message);
        }
      })
    },
    handleBatchAuditOrder() {
      const ids = this.selectedOrders.map(val => val.id)
      let tasks = [];
      ids.forEach((id) => {
        tasks.push(() => {
          return orderService.batchAudit({
            order_id_string: `[${id}]`,
          });
        });
      });
      this.orderAuditFailLog = []
      this.$refs.sdpPollingTask.createRequest(tasks, 1, { label: '审核' })
        .then((results) => {
          results.map((res, index) => {
            // 失败
            if (!res.status) {
              this.orderAuditFailLog.push({
                order_no: this.selectedOrders[index].order_no,
                msg: res.message
              })
            }
          })
        })
        .catch(() => {})
        .finally(() => {
          this.fetchData();
        });
    },
    handleShowOrderAuditLog() {
			const columns = [
        {
          title: '订单号',
          key: 'order_no'
        },
				{
					title: '失败原因',
					key: 'msg'
				},
			]
			this.$refs.sdpPaginationTableModalRef.handleOpen({ show: true, columns, data: this.orderAuditFailLog })
		},
    batchCompleteOrder() {
      let orderIds = this.selectedOrders.map((item) => item.id);
      let params = {
        order_id: orderIds.join(','),
      };
      orderService.batchComplete(params).then((res) => {
        let { status, message } = res;
        if (status) {
          this.selectedOrders = [];
          this.successMessage(message || '完成成功！');
          this.fetchData();
        } else {
          this.errorMessage(message);
        }
      });
    },
    changeOrderMode() {
      Bus.$emit('tabsChange');
      this.initColumns();
      this.fetchData();
      this.$nextTick(() => {
        this.filterItems[0].props.open = true;
        this.$refs.orderListTable.setValue(
          'split_rule_id',
          getDefaultSplitRule(),
        );
      });
    },
    showProviderExportModal() {
      this.exportProviderDetailModal.params = this.getParams();
      this.$refs.exportProviderDetailModalRef.setIsShow(true);
    },
    closeProviderExportModal() {
      this.exportModal.show = false;
    },
    showExportModal(options = { type: 'all' }) {
      const params = this.getParams();
      let extraData = {};
      if (options.type === 'rows') {
        extraData.order_ids = this.selectedOrders
          .map((item) => item.id)
          .join(',');
      }
      this.exportModal.params = {
        ...params,
        ...extraData,
        orderName: this.orderName,
      };
      this.exportModal.show = true;
    },
    closeExportModal() {
      this.exportModal.show = false;
    },
    showNormalExportModal(options = { type: 'all' }) {
      const params = this.getParams();
      let extraData = {};
      if (options.type === 'rows') {
        extraData.order_ids = this.selectedOrders
          .map((item) => item.id)
          .join(',');
      }
      this.exportNormalModal.params = {
        ...params,
        ...extraData,
      };
      this.exportNormalModal.show = true;
    },
    closeExportNormalModal() {
      this.exportNormalModal.show = false;
    },
    showCategoryExportModal() {
      this.$refs.exportCategoryFilter &&
        this.$refs.exportCategoryFilter.reset();
      this.exportCategoryModal.show = true;
    },
    closeExportCategoryModal() {
      this.exportCategoryModal.show = false;
    },
    // 修改周转筐
    showBatchUpdateBasketModal(orderId) {
      this.$refs.batchUpdateBasket.showModal(orderId);
    },
    async openImportOrder() {
      this.configData();
      let { status, data, message } = await this.$request.get(
        api.orderImportTemplateList,
        {
          page: 1,
          pageSize: 9999,
        },
      );
      if (status == 1) {
        let list = data.list.map((item) => {
          return {
            value: item.id,
            label: item.name,
            type: item.type,
            create_user: item.create_user,
            price_mode: item.price_mode,
            user_mode: item.user_mode,
            order_tag_mode: item.order_tag_mode,
            order_remark_mode: item.order_remark_mode,
            remark_mode: item.remark_mode,
            inner_remark_mode: item.inner_remark_mode,
            template_from: item.template_from || 0,
          };
        });
        if (!this.disableTemplate) {
          list = [];
        }
        // 为了与自定义模板的id进行区分，暂用a111111/a222222/a333333进行表示
        list.unshift({
          value: 'a111111',
          label: '客户常用商品',
        });
        if (this.isOPenOrderImportEdit) {
          list.unshift(
            {
              value: '0',
              label: '系统模板-新增订单',
            },
            {
              value: 'a222222',
              label: '系统模板-编辑订单',
            },
          );
        } else {
          list.unshift({
            value: '0',
            label: '系统模板',
          });
        }
        list.splice(1, 0, {
          value: 'a333333',
          label: '协议单-下单模板',
        });
        this.templateData = list;
      } else {
        this.$Notice.error({
          title: message,
        });
      }
      this.showImportModal = true;
    },
    cleanFile() {
      this.fileName = '';
      this.importError = '';
      this.orderFile = '';
    },
    closeImportOrder() {
      this.cleanFile();
      this.showImportModal = false;
    },
    importTpl(file) {
      this.orderFile = file;
      this.fileName = file.name;
      this.importText = '正在导入...';
      this.importError = '';
      this.disabledImport = true;
      this.importData = [];
      this.importExceling = true
      return new Promise((resolve) => {
        this.uploadResolve = resolve;
      });
    },
    importSubmit() {
      this.uploadData = {
        template_type: this.templates,
        type: this.importType,
        user_mode: this.userMode,
        price_mode: this.price_mode,
        order_tag_mode: this.order_tag_mode,
        tpl_id: this.templates,
        order_remark_mode: this.order_remark_mode,
        remark_mode: this.remark_mode,
        inner_remark_mode: this.inner_remark_mode,
        template_from: this.template_from,
      };
      switch (this.templates) {
        case 'a111111':
          this.uploadData.template_type = 1;
          break;
        case 'a222222':
          this.uploadData.template_type = 2;
          break;
      }
      if (
        this.templates === '0' ||
        this.templates == 'a111111' ||
        this.templates === 'a222222' ||
        this.templates === 'a333333'
      ) {
        this.uploadData.tpl_id = 0;
      } else {
        this.uploadData.template_type = 0;
      }
      if (!this.orderFile) {
        this.importError = '请选择文件';
        return;
      }
      if (this.templates == 'a333333' || this.isTxlcTemplate) {
        this.uploadData.delivery_date = this.importDeliveryDate;
      }
      if (this.importExceling) {
        this.importSubmitLoading = true;
      }
      this.importExceling = false;
      this.uploadResolve();
    },
    exportOrderTemplate() {
      const templates = this.templates === 'a222222' ? 2 : this.templates;
      let url = `/superAdmin/orderSuper/batchImportTemplate?type=${this.importType}&price_mode=${this.price_mode}&user_mode=${this.userMode}&order_tag_mode=${this.order_tag_mode}&template_type=${templates}&order_remark_mode=${this.order_remark_mode}&remark_mode=${this.remark_mode}&inner_remark_mode=${this.inner_remark_mode}&template_id=${this.templates}`;
      window.location.href = url;
    },
    onCustomTpl() {
      this.closeImportOrder();
      window.open('#/appCenter/orderImportTemplate');
    },
    handleExcelUploadFormatError() {
      this.importText = '导入订单';
      this.importError = '上传的excel文件格式不正确,请选择[csv,xlsx]文件.';
      this.disabledImport = false;
    },
    upload() {
      // 上传文件
      let item = this.orderFile;
      this.actionUrl = '/superAdmin/orderSuper/batchImport?';
      setTimeout(() => {
        this.importSubmitLoading = true;
        this.$refs.upload.post(item);
        this.cleanFile();
      }, 200);
    },
    excelImportSuccess(res) {
      this.importSubmitLoading = false;
      this.importText = '导入订单';
      this.disabledImport = false;
      let { data, message } = res;
      if (res.status) {
        this.successMessage(res.message);
        this.importData = [];
        this.showImportModal = false;
        this.orderZeroConfirmCLose();
        this.cleanFile();
        this.fetchData();
      } else {
        if (res.errCode === 1002) {
          this.orderZeroConfirmOpen();
          return;
        }
        this.orderZeroConfirmCLose();
        if (!data || data.length === 0) {
          this.importError = message;
        } else {
          this.importData = res.data;
        }
      }
    },
    toAdd() {
      try {
        const projectInfo = JSON.parse(this.sysConfig.project_info);
        // 为厦门佼佼者单独做了一个新建订单的页面，优化录单体验是个临时方案
        if (projectInfo.project_code === 'SDP06484') {
          this.$router.push({ path: 'new-v2', query: { keep_scroll: 1 } });
          return;
        }
      } catch (e) {
        console.error(e);
      }
      Bus.$emit(
        'on-renew-countdown',
        this.$store.state.sysConfig.expiration_reminder,
        () => {
          this.router.push({ path: '/newOrder', query: { keep_scroll: 1 } });
        },
      );
    },
    toAddSceneOrder() {
      this.router.push({ path: '/addSceneOrder', query: { keep_scroll: 1 } });
    },
    toOrderDetail(res, target) {
      let id = res.id,
        orderNo = res.order_no,
        isCanApproval = res.isCanApproval,
        isCanEdit = res.isCanEdit;
      const redirectInfo = {
        path: '/orderDetail',
        query: {
          keep_scroll: 1,
          id: id,
          orderNo: orderNo,
          isCanApproval: isCanApproval,
          isCanEdit,
          needCut: 1,
        },
      };
      if (target === '_blank') {
        window.open(this.router.resolve(redirectInfo).href);
      } else {
        this.router.push(redirectInfo);
      }
    },
    toEdit(res) {
      var id = res.id;
      Bus.$emit(
        'on-renew-countdown',
        this.$store.state.sysConfig.expiration_reminder,
        () => {
          this.router.push({
            path: '/orderEdit',
            query: { keep_scroll: 1, id: id },
          });
        },
      );
    },
    toApprovalOrder: function (res) {
      var id = res.id;
      // 需要添加新旧版逻辑
      const path = this.isEnableNewVersionOrderApproval
        ? '/order/approval'
        : '/orderApproval';
      Bus.$emit(
        'on-renew-countdown',
        this.$store.state.sysConfig.expiration_reminder,
        () => {
          this.router.push({ path, query: { keep_scroll: 1, id: id } });
        },
      );
    },
    toReturnGoods(data) {
      let params = {
        order_id: data.id,
      };
      this.$request
        .get(this.apiUrl.getOrderHistoryDetail, params)
        .then((res) => {
          if (res.status == 0) {
            this.errorNotice(res.message);
          } else {
            this.$router.push({
              path: '/newReturnOrder',
              query: { keep_scroll: 1, data: JSON.stringify(data) },
            });
          }
        });
    },
    finishOrder: function (value) {
      var self = this,
        id = value.id;
      self.$Modal.confirm({
        title: '完成订单',
        content: '<p>确认订单已经完成</p>',
        onOk: () => {
          orderService.finishOrder(id).then((res) => {
            if (res.status) {
              this.successMessage('订单已完成');
              this.fetchData();
            } else {
              this.errorMessage(res.message);
            }
          });
        },
        onCancel: function () {},
      });
    },
    delOrder: function (row) {
      orderService.delOrder(row.order_no).then((res) => {
        let { status, message } = res;
        if (status) {
          this.successMessage(message);
          this.fetchData();
        } else {
          this.errorMessage(message);
        }
      });
    },
    // 对公支付付款
    handlePayPublic(row) {
      this.payPublicInfo = { ...row, amount_paid: '', errText: '' };

      this.$refs.payPublicModal.open();
    },
    hadnlePublickModalClose() {
      this.$refs.payPublicModal.close();
    },
    hadnlePublickModalSubmit() {
      const url = '/superAdmin/orderSuper/payment';
      const options = {
        order_id: this.payPublicInfo.id,
        amount_paid: this.payPublicInfo.amount_paid,
      };

      if (!`${options.amount_paid}`) {
        return (this.payPublicInfo.errText = '请输入对公支付付款金额');
      }

      this.$request.post(url, options).then((res) => {
        const { status, message } = res;
        if (status) {
          this.successMessage('付款成功');
          this.hadnlePublickModalClose();

          this.$refs.orderListTable && this.$refs.orderListTable.fetchData();
        } else {
          this.payPublicInfo.errText = message;
        }
      });
    },
    // 导出订单
    useExportOrder(row) {
      location.href = '/superAdmin/orderSuper/AjaxExport?order_id=' + row.id;
    },
    acceptanceOrderExport(row) {
      let url =
        '/superAdmin/orderSuper/acceptanceOrderExport?order_id=' + row.id;
      this.$request.get(url).then((res) => {
        let { status, message } = res;
        if (status) {
          this.successMessage('导出成功');
          location.href = res.data;
        } else {
          this.errorMessage(message);
        }
      });
    },
    // 获取订单标签列表
    getOrderTagList() {
      orderSerivce.qryOrderTagList().then((res) => {
        let { status, data } = res;
        if (status) {
          if (data && Array.isArray(data)) {
            let orderTagList = res.data.map((item) => {
              return {
                label: item.name,
                value: item.id,
              };
            });
            this.orderTagList = orderTagList;
            this.setFileterOrderTagList();
          }
        }
      });
    },
    setFileterOrderTagList() {
      this.setAdvanceItemData('order_tag', this.orderTagList);
    },
    setAdvanceItemData(key, data) {
      // 导出分类汇总表
      if (key === 'order_tag') {
        this.exportCategoryFilter.map((val) => {
          if (val.key === 'order_tag') {
            val.props.data = data;
          }
        });
      }

      // 列表高级筛选
      this.advanceItems.forEach((row) => {
        row.items.forEach((col) => {
          if (col.key === key) {
            if (key === 'order_tag') {
              col.props.data = data;
            } else {
              col.data = data;
            }
          }
        });
      });
    },
    dealParams(params) {
      let dealParams = this.cloneObj(params);
      if (dealParams.commodity_id) dealParams.commodity_name = '';
      if (dealParams.user_id) dealParams.unc_search = '';
      if (Array.isArray(params.mode)) {
        dealParams.mode = params.mode.join(',');
      } else {
        dealParams.mode = '';
      }
      // 不是全部tab
      if (this.filters.modeStatus !== '0') {
        dealParams.mode = '';
      }
      if (!dealParams.order_tag_filter) {
        dealParams.order_tag_filter = '1';
      }
      return dealParams;
    },
    getParams() {
      let params = {};
      if (this.$refs.orderListTable) {
        params = this.$refs.orderListTable.getParams();
        params = this.dealParams(params);
      }
      return params;
    },
    beforeRequest(params) {
      let requestParams = this.dealParams(params);

      // 塞默认值的情况下
      if (
        requestParams.receivable_style_id &&
        Array.isArray(requestParams.receivable_style_id)
      ) {
        requestParams.receivable_style_id =
          requestParams.receivable_style_id.join(',');
      }

      // 这个发货日期太绕了, 业务页面写一套, 组件里再写一套, 骚就骚点, 再在屎上继续堆把
      if (
        this.$route.query.from === 'otherPage' &&
        this.$route.query.startTime &&
        this.$route.query.endTime
      ) {
        // 将查询参数中的 startTime 和 endTime 赋值给 requestParams
        requestParams.startTime = this.$route.query.startTime;
        requestParams.endTime = this.$route.query.endTime;

        this.$nextTick(() => {
          // 清理 URL
          const resolveUrl = this.$router.resolve({ path: '/order/list' });
          const newUrl = `${location.origin}${resolveUrl.href}`;
          window.history.replaceState({}, '', newUrl);

          // 更新路由以反映新的状态
          this.$router.replace({ path: '/order/list' });
        });
      }

      // if (!params.startTime) {
      //   return false;
      // }
      if (Array.isArray(getDefaultSplitRule())) {
        requestParams.split_rule_id = getDefaultSplitRule().join(',');
      }
      // 避免第一次载入时多次刷新
      console.log('paramssss', params);
      if (
        this.firstLoad &&
        (params.startTime === undefined && params.createStartTime === undefined)
      ) {
        return false;
      }
      this.firstLoad = true;
      const maxDay = this.maxOrderDeliveryDateDays;
      if (maxDay && Number(maxDay) > 0) {
        if (
          DateUtil.diffDay(params.startTime, params.endTime) + 1 - maxDay >
          0
        ) {
          this.errorMessage(`发货日期区间不能大于${maxDay}天`);
          return false;
        }
      }
      let sortMap = ['', 'desc', 'asc'];
      let sort = '';
      if (+this.sort.user_code) {
        sort = 'user_code ' + sortMap[this.sort.user_code];
      } else if (+this.sort.shop_name) {
        sort = 'shop_name ' + sortMap[this.sort.shop_name];
      } else if (+this.sort.delivery_date) {
        sort = 'delivery_date ' + sortMap[this.sort.delivery_date];
      } else if (
        +this.sort.sale_name &&
        DateUtil.diffDay(params.startTime, params.endTime) < 31
      ) {
        sort = 'sale_name ' + sortMap[this.sort.sale_name];
      } else if (
        +this.sort.line_name &&
        DateUtil.diffDay(params.startTime, params.endTime) < 31
      ) {
        sort = 'line_name ' + sortMap[this.sort.line_name];
      } else if (+this.sort.print_times) {
        sort = 'print_times ' + sortMap[this.sort.print_times];
      }

      console.log(this.sort.print_times);

      requestParams.modeStatus = this.filters.modeStatus;

      if (sort) {
        requestParams.orderName = sort;
        this.orderName = sort;
      }
      return requestParams;
    },
    afterRequest(list, res) {
      if (res && res.status === 1) {
        // this.is_order_audit = Number(res.data.is_order_audit) === 1;
        // 切换页面时如果发现配置中关闭了订单审核，这里自动切换tab页
        // if (
        //   !this.is_order_audit &&
        //   this.filters.modeStatus === String(this.orderMode.unaudit)
        // ) {
        //   this.filters.modeStatus = '0';
        //   setTimeout(() => {
        //     this.changeOrderMode();
        //   }, 100);
        // }
        StorageUtil.setLocalStorage('order-list-sort-filters', this.sort);
        const filterList = list.map((item) => ({
          id: item.id,
          orderNo: item.order_no,
          isCanApproval: item.isCanApproval,
          isCanEdit: item.isCanEdit,
        }));
        StorageUtil.setLocalStorage('order-list-curFilterList', filterList);
      }
      list.forEach((item) => {
        item._show_order_no_tag = true;
        item._disabled = item.isCanPrint === 'N';
        if (Number(item.order_goods_number) === 0) {
          item.order_goods_number = '--';
        }
        if (Number(item.total_order_quantity) === 0) {
          item.total_order_quantity = '--';
        }
      });
      if (res && res.message) {
        return this.tempList || [];
      } else this.tempList = list;
      return list;
    },
    fetchData(resetPage, keepScroll = true, loadingType = 'spinner') {
      this.$refs.orderListTable.fetchData(resetPage, keepScroll, loadingType);
    },
    onRefresh() {
      this.$refs.orderListTable.fetchData();
    },
    _setOrderTagDisabled(_disabled, filterKey) {
      // 导出汇总表的筛选项
      if (filterKey === 'exportCategoryFilter') {
        this.exportCategoryFilter.map((val) => {
          if (val.key === 'order_tag') {
            val.props.disabled = _disabled;
          }
        });
        if (_disabled) {
          this.$refs.exportCategoryFilter.setValue({ order_tag: [] });
        }
        return;
      }

      this.advanceItems.forEach((item) => {
        item.items.forEach((item2) => {
          if (item2.key === 'order_tag') {
            item2.attrs.disabled = _disabled;
          }
        });
      });
      if (_disabled) {
        // 清空筛选项
        this.$refs.orderListTable.setValue('order_tag', [], true);
      }
    },
    _toAddRecipeOrder() {
      this.$router.push('/recipe/order/add');
    },
    initFilterItems() {
      const filterConfig = StorageUtil.getLocalStorage(
        'order_list_01_filter_config',
      );
      let endDate = '00:00';
      if (filterConfig && filterConfig.length > 0) {
        const deliverDateFilterConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date';
        });
        endDate =
          deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue;
      }
      console.log('localEndDate-----', endDate);
      const isUpdateDeliverDate = DateUtil.isTimeAfterSixPM(endDate);
      if (isUpdateDeliverDate) {
        this.endTime = DateUtil.getTomorrow();
        const deliveryDateIndex = this.filterItems.findIndex(
          (item) => item.label === '发货日期',
        );
        if (deliveryDateIndex > -1) {
          console.log(this.startTime, 'this.startTime');
          this.filterItems[deliveryDateIndex].props.setDefaultStartTime =
            this.startTime;
          this.filterItems[deliveryDateIndex].props.setDefaultEndTime =
            this.endTime;
        }
      }
      this.getHomeSelect()
    },
    initQueryParams() {
      let defaultEndTime = DateUtil.getTodayDate();
      const filterConfig = StorageUtil.getLocalStorage(
        'order_list_01_filter_config',
      );
      let endDate = '00:00';
      if (filterConfig && filterConfig.length > 0) {
        const deliverDateFilterConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date';
        });
        endDate =
          deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue;
      }
      const isTomorrow = DateUtil.isTimeAfterSixPM(endDate);
      if (isTomorrow) {
        defaultEndTime = DateUtil.getTomorrow();
      }
      let defaultStartTime = DateUtil.subTract(28, defaultEndTime);
      if (this.maxOrderDeliveryDateDays !== 0) {
        defaultStartTime = DateUtil.subTract(
          this.maxOrderDeliveryDateDays - 1,
          defaultEndTime,
        );
      }
      if (filterConfig && filterConfig.length > 0) {
        const deliveryDateRangeConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date_filter_range';
        });
        if (deliveryDateRangeConfig) {
          defaultStartTime = DateUtil.subTract(
            (deliveryDateRangeConfig.value ||
              deliveryDateRangeConfig.defaultValue) - 1,
            defaultEndTime,
          );
        }
      }
      this.initParams = {
        startTime: defaultStartTime,
        endTime: defaultEndTime,
        // storage_id: '',
        mode: StorageUtil.getLocalStorage('order-list-checkbox-state')
          ? StorageUtil.getLocalStorage('order-list-checkbox-state')
          : ['0', '1', '2', '4', '5'],
      };
      this.startTime = defaultStartTime;
      this.endTime = defaultEndTime;
    },
    firstTimeDataRendered() {
      if (this.isOpenOrderTag) {
        this.getOrderTagList();
      }
      // 加载xslx相关资源
      if (typeof window.loadXSLX === 'function') {
        window.loadXSLX();
      }
    },
    handleFinishDataLoad() {
      if (!this.isTableDataLoaded) this.isTableDataLoaded = true;
    },
    jumpMergeOrder() {
      this.$router.push({
        path: '/order/merge',
      });
    },
    handleSortKeyChange(sortKey, e) {
      console.log(sortKey, e);
      Object.keys(this.sort).map((key) => {
        this.sort[key] = sortKey === key ? e : 0;
      });
      console.log(this.sort);
    },
    getLocalDeliverDate() {
      let defaultEndTime = DateUtil.getTodayDate();
      const filterConfig = StorageUtil.getLocalStorage(
        'order_list_01_filter_config',
      );
      let endDate = '00:00';
      if (filterConfig && filterConfig.length > 0) {
        const deliverDateFilterConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date';
        });
        endDate =
          deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue;
      }
      console.log('localEndDate-----', endDate);
      const isTomorrow = DateUtil.isTimeAfterSixPM(endDate);
      if (isTomorrow) {
        defaultEndTime = DateUtil.getTomorrow();
      }
      this.importDeliveryDate = defaultEndTime;
    },
    importDeliveryDateChange(value) {
      this.importDeliveryDate = value;
    },
    openBatchAddGoods(isSelect) {
      this.BatchAddGoodModalIds = [];
      if (isSelect) {
        this.BatchAddGoodModalIds = this.selectedOrders.map((item) => item.id);
      }
      this.$nextTick(() => {
        this.$refs.batchAddGoodModalRef.openModal();
      });
    },
    handleSplitSuccess() {
      this.$refs.orderListTable && this.$refs.orderListTable.fetchData();
    },
    initCategoryFilterItems() {
      if (+this.sysConfig.is_open_export_order_commodity === 1) {
        this.exportBtnMultiData.push({
          text: '供应商供货明细',
          onClick: () => {
            this.showProviderExportModal();
            return false;
          },
        });
      }
      if (+this.sysConfig.is_open_order_export_category_summary) {
        this.exportBtnMultiData.unshift({
          text: '导出分类汇总表',
          onClick: () => {
            this.showCategoryExportModal();
            return false;
          },
        });

        this.exportCategoryFilter = [
          {
            label: '导出月份',
            key: 'month',
            type: 'DatePicker',
            props: {
              type: 'month',
              placeholder: '选择月份',
            },
          },
          {
            type: 'custom',
            component: InputAutoComplete,
            key: 'user_search',
            label: '客户名称',
            defaultValue: '',
            stop: true,
            props: {
              placeholder: '输入客户名称/编码搜索',
              dataProvider: orderService.getUserBySearch,
              valueKey: 'id',
              labelKey: 'email',
              on: {
                'on-enter': (value) => {
                  this.$refs.exportCategoryFilter.setValue({ user_id: value });
                },
                'on-focus': () => {
                  this.$refs.exportCategoryFilter.setValue({
                    user_id: '',
                    stop: true,
                  });
                },
              },
            },
          },
          {
            relation: 'order_tag',
            label: '标签筛选',
            type: 'custom',
            show: this.isOpenOrderTag,
            key: 'order_tag_filter',
            component: RadioGroup,
            props: {
              data: [
                {
                  label: '同时存在',
                  value: '1',
                },
                {
                  label: '存在一个',
                  value: '2',
                },
                {
                  label: '无标签',
                  value: '3',
                },
              ],
              on: {
                'on-reset': (value) => {
                  // 重置时恢复订单标签可选
                  this._setOrderTagDisabled(
                    +value === 3,
                    'exportCategoryFilter',
                  );
                },
              },
            },
            defaultValue: '1',
            style: {
              width: '100%',
            },
            onChange: (value) => {
              // 选中标签筛选中的无标签之后，订单标签筛选项不可选择
              this._setOrderTagDisabled(+value === 3, 'exportCategoryFilter');
              return {
                value,
                stop: true,
              };
            },
          },
          {
            relation: 'order_tag',
            label: '订单标签',
            type: 'custom',
            show: this.isOpenOrderTag,
            key: 'order_tag',
            tagTopStart: true,
            props: {
              data: [],
              disabled: false,
              key: 'order_tag',
            },
            style: {
              width: '100%',
            },
            defaultValue: [],
            component: CheckboxGroup,
            onChange(value) {
              return {
                value,
                stop: true,
              };
            },
          },
        ];
      }
    },
    handleGoodsLock(status) {
      // 锁定状态【0解锁，1半锁，2全锁】
      this.$smodal({
        type: 'warning',
        title:
          (status == 0 ? '商品解锁' : '商品锁定') +
          (status == 1 ? '(半锁)' : '(全锁)'),
        text:
          status == 0
            ? '当前操作将解锁所选订单'
            : status == 1
              ? '此操作将对所选订单执行【半锁定】处理'
              : '此操作将对所选订单执行【全锁定】处理',
        okText: '确认',
        onOk: () => {
          this.lockOrder(
            status,
            this.selectedOrders.map((item) => item.id).join(','),
          );
        },
      });
    },
    lockOrder(status, order_ids) {
      this.$request
        .post(this.apiUrl.lockOrder, {
          lock_status: status,
          order_ids,
        })
        .then(({ status, message }) => {
          if (status) {
            this.fetchData();
            this.$smessage({
              type: 'success',
              text: status == 0 ? '订单解锁成功' : '订单锁定成功',
            });
          } else {
            this.$smessage({
              type: 'error',
              text: message || (status == 0 ? '订单解锁失败' : '订单锁定失败'),
            });
          }
        });
    },
    getHomeSelect() {
      // 首页筛选参数
      const dateArr = StorageUtil.getLocalStorage('home_redirect_select_date')
      if (dateArr) {
        if (this.maxOrderDeliveryDateDays == 0) {
          const deliveryDateIndex = this.filterItems.findIndex(
            (item) => item.label === '发货日期',
          );
          if (deliveryDateIndex > -1) {
            this.filterItems[deliveryDateIndex].props.setDefaultStartTime = '';
            this.filterItems[deliveryDateIndex].props.setDefaultEndTime = '';
            this.startTime = '';
            this.endTime = '';
            this.initParams = {
              ...this.initParams,
              startTime: '',
              endTime: ''
            }
          }
          this.$refs.orderListTable && this.$refs.orderListTable.setValue(['startTime', 'endTime'], [], true);
        }
        const createStartTime = moment(dateArr[0]).format('YYYY-MM-DD HH:mm');
        const createEndTime = moment(dateArr[1]).format('YYYY-MM-DD') + ' 23:59';
        this.$refs.orderListTable && this.$refs.orderListTable.setValue(
          ['createStartTime', 'createEndTime'],
          [createStartTime, createEndTime],
          true,
        );
        this.initParams = {
          ...this.initParams,
          createStartTime,
          createEndTime
        }
      }
    }
  },
  created() {
    // 跳转到相应Tab
    if (this.$route.query.navActive) {
      this.filters && (this.filters.modeStatus = this.$route.query.navActive);
    }
    this.MINE_TYPE = MINE_TYPE; // 挂载非响应式常量
    this.initQueryParams();
    this.initFilterItems();
    this.setAdvanceItems();
    this.initCategoryFilterItems();
    this.getLocalDeliverDate();
  },
  activated() {
    this.getHomeSelect();
    // 首页筛选参数
    const otherParams = StorageUtil.getLocalStorage('home_redirect_other_params')
    if (otherParams) {
      this.$refs.orderListTable.setValue(
        'is_first_order',
        otherParams.is_first_order,
        true,
      );
    }
    if (this.init) {
      if (this.$refs.orderListTable) {
        this.$refs.orderListTable.initAction();
        this.$refs.orderListTable.setValue(
          'split_rule_id',
          getDefaultSplitRule(),
          true,
        );
        this.$refs.orderListTable.setValue(
          'meal_type',
          StorageUtil.getLocalStorage('order_meal_type'),
          true,
        );
      }
      console.log(1111111)
      this.fetchData(false, true, 'bar');
    } else {
      this.init = true;
      // StorageUtil.removeLocalStorage('order_split_rule_id');
      StorageUtil.removeLocalStorage('order_meal_type');
    }
  },
};
</script>
<style lang="less" scoped>
/deep/.ivu-select-multiple .ivu-tag {
  max-width: 60%;
}
/deep/.ivu-select-item {
  width: 217px;
  padding-right: 28px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/ .order__list {
  &__tab {
    margin-bottom: 20px;
  }
  &__order-no {
    display: flex;
    &-tag {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      margin-right: 4px;
      /* width: 14px; */
    }
    a {
      display: flex;
      align-items: center;
      i {
        margin-right: 4px;
      }
    }
  }
  .sdp-table__tr:hover .hover--primary {
    color: var(--primary-color);
  }
}

/deep/.sdp-table__tr .primary {
  color: var(--primary-color);
}
.ivu-modal-body {
  padding: 20px 24px 30px 24px;
  .explain {
    padding: 12px 20px 16px 20px;
    background: #f5f6f8;
    color: rgba(0, 0, 0, 0.5);
    border-radius: 2px;
    font-size: 12px;
    h6 {
      color: rgba(0, 0, 0, 0.7);
      font-weight: 400;
      margin-bottom: 2px;
      line-height: 16px;
    }
    p {
      padding-top: 2px;
    }
  }
  .download-template {
    color: #03ac54;
    cursor: pointer;
  }
  .file-name-item {
    display: flex;
    align-items: center;
    .sui-icon {
      color: rgba(0, 0, 0, 0.2);
      margin-left: 10px;
    }
  }
  .file-name {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-left: 12px;
    font-size: 13px;
  }
  .upload-item {
    display: flex;
    align-items: center;
  }

  .item {
    display: flex;
    align-items: center;
    padding-top: 20px;
    label {
      height: 14px;
      line-height: 14px;
    }
    &.nopd {
      padding-top: 6px;
    }
    // line-height: 32px;
    .error {
      font-size: 12px;
    }
    .item-left {
      width: 110px;
      text-align: right;
      font-weight: 500;
    }
    .item-right {
      flex: 1;
      line-height: 14px;
      &__template {
        position: relative;
        .info-tips {
          position: absolute;
          bottom: -20px;
          color: red;
        }
      }
      .ivu-radio-wrapper {
        font-size: 13px;
        margin-right: 22px;
      }
      .bold {
        font-weight: 500;
      }
    }
  }
}
.import-modal {
  &-exportCategoryFilter {
    /deep/ .s-filter__default {
      height: auto;
    }
  }
  /deep/ .ivu-modal-header {
    border-bottom: 1px solid #f0f2f0;
    line-height: 1;
    padding: 14px 16px;
  }
  &__hd {
    width: 100%;
    padding: 3px 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    h5 {
      font-size: 14px;
      color: #303030;
      &::before {
        content: '';
        width: 3px;
        height: 12px;
        background: #505050;
        display: inline-block;
        margin-right: 6px;
        margin-bottom: -1px;
      }
    }
    .sui-icon {
      cursor: pointer;
    }
  }
  .ivu-modal-footer {
    padding: 15px 24px;
    .sdp-button {
      margin-left: 8px;
    }
  }
}
</style>

<style lang="less" scoped>
.order__list {
  /deep/ .base-filter {
    .datepicker-createtime {
      .ivu-input {
        font-size: 12px;
        padding-left: 8px;
        padding-right: 20px;
        &-suffix {
          width: 28px;
        }
        &::-webkit-input-placeholder {
          font-size: 13px;
        }
      }
    }
  }
  /deep/ .groupFilter-custom {
    width: 219px;
  }
  /deep/.ivu-tabs .ivu-tabs-tab {
    line-height: 14px;
    padding: 11px 0;
  }
  /deep/.ivu-tabs .ivu-tabs-tab:hover {
    line-height: 14px;
    padding: 11px 0;
  }
}
.orderList__Dropdown--batch {
  .ivu-dropdown-item {
    padding: 0;
  }
  /deep/ .DropdownItem__button {
    display: block !important;
    button {
      height: unset;
      padding: 0;
      color: inherit;
      border: 0 none;
      background: transparent;
      display: block;
      padding: 7px 16px;
      width: 100%;
      text-align: left;
      &:focus {
        outline: 0 !important;
        box-shadow: unset !important;
      }
    }
  }
}
/deep/.order__list__operation {
  .sdp-button {
    margin-right: 0;
  }
}

.import-explain {
  padding: 12px 20px 16px 20px;
  margin: 20px 24px;
  background: #f5f6f8;
  color: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  font-size: 12px;
  h6 {
    color: rgba(0, 0, 0, 0.7);
    font-weight: 400;
    margin-bottom: 2px;
    line-height: 16px;
  }
  p {
    padding-top: 2px;
    font-weight: 400;
  }
}
.y-center {
  display: flex;
  align-items: center;
}
/deep/ .ivu-poptip {
  .ivu-btn {
    font-size: 13px;
    font-weight: normal;
  }
}
/deep/.ivu-select-placeholder {
  padding-left: 10px !important;
}
.add-tips {
  white-space: normal;
  > p {
    margin-bottom: 3px;
  }
}
/deep/ .DropdownItem__button_trans {
  display: block !important;
  button {
    height: unset;
    padding: 0;
    color: inherit;
    border: 0 none;
    background: transparent;
    display: block;
    padding: 3px 8px;
    width: 100%;
    text-align: left;
    &:focus {
      outline: 0 !important;
      box-shadow: unset !important;
    }
  }
}
/deep/ .sdp-table__th .sdp-table__cell {
  background: #f2f4f5;
  height: 40px;
  min-height: 40px;
}
/deep/ .sdp-table__td .sdp-table__cell,
/deep/ .sdp-table__content .sdp-table__tr {
  height: 50px;
  min-height: 50px;
  line-height: 1.35;
}
</style>
