<template>
  <div
    :style="{
      overflowY: 'auto',
      overflowX: 'hidden'
    }"
    class="new-order"
  >
    <DetailPage ref="detailRef" pageType="add" title="新增现场采购单" :customSaveBtn="true">
      <Form ref="form" inline label-colon :label-width="82" :disabled="false">
        <s-block title="基础信息" class="base-info">
          <Form-item ref="error.user" :error="error.user" label="客户" style="position: relative;">
            <i-input
              ref="userInput"
              placeholder="请输入客户名称/客户编号"
              v-model="user.name"
              @on-change="searchUser"
              @on-enter="enterUser"
              @on-blur="userDropDown = false"
              clearable />
            <transition name="dropdown-fade">
              <div class="newOrder-dropdown" v-show="userDropDown">
                <ul>
                  <li
                    class="dropdown-items"
                    v-for="info in userList"
                    :key="info.id"
                    :class="{ active: user.uid === info.uid }"
                    @click="addUser(info)"
                  >
                    <strong class="dropdown-items-strong">{{
                      info.email
                    }}</strong>
                    <p class="dropdown-items-p">{{ info.user_code }}</p>
                  </li>
                </ul>
              </div>
            </transition>
            <transition name="dropdown-fade">
              <div
                class="newOrder-dropdown-content"
                v-show="!userDropDown && user.name && userList.length === 0"
              >
                <p class="dropdown-empty">暂无数据</p>
              </div>
            </transition>
          </Form-item>
          <Form-item
            ref="error.delivery_date"
            :error="error.delivery_date"
            label="发货日期"
          >
            <DatePicker
              type="date"
              placeholder="选择日期"
              v-model="date"
              @on-change="handleChangeDeliveryDate"
              format="yyyy-MM-dd"
              :editable="false"
            ></DatePicker>
          </Form-item>
          <FormItem
            ref="error.agent_provider"
            :error="error.agent_provider"
            label="采购类型"
          >
            <div style="width: 260px">
              <Select v-model="selectPurchaseType" style="width: 100px;">
                <Option value="1">采购员</Option>
                <Option value="2">供应商</Option>
                <Option value="4">仓库自销</Option>
              </Select>
              <Select
                v-if="+selectPurchaseType !== 4"
                :filterable="true"
                :clearable="true"
                :label="selectPurchaseAgent"
                @on-change="handleChangeAgentAndProvider"
                v-model="selectedId"
                style="width: 150px;"
              >
                <Option
                  v-for="item in purchaseAgent"
                  :value="item.id"
                  :label="item.name"
                  :key="item.id"
                  >{{ item.name }}</Option
                >
              </Select>
            </div>
          </FormItem>
          <FormItem label="制单人">{{ username }}</FormItem>
          <FormItem
            ref="error.agent_provider"
            :error="error.provider_supervisor"
            v-if="+selectPurchaseType !== 4 && isEnablePurchaseTask"
            label="采购负责人"
          >

            <Select
              :filterable="true"
              :clearable="true"
              @on-change="handleChangeAgentAndProvider"
              v-model="selectedSupervisorId">
              <Option
                v-for="item in purchaseAgentAble"
                :value="item.id"
                :label="item.name"
                :key="item.id"
                >{{ item.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem v-if="+selectPurchaseType !== 4">
            <div style="margin-left: -68px; width: 240px;">
              <Checkbox
                :true-value="1"
                :false-value="0"
                v-model="formData.update_in_price"></Checkbox>
              是否更新最近一次采购价
            </div>
          </FormItem>
          <FormItem style="width: 100%" v-if="orderTagList.length > 0" label="订单标签">
            <orderTag
              v-model="selectedOrderTag"
              @on-change="checkTag"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return disabledEverything ||
                  (only_tag === '1' &&
                    selectedOrderTag.length > 0 &&
                    !selectedOrderTag.includes(item.id))
              }
            ">
            </orderTag>
          </FormItem>
        </s-block>
        <s-block title="收货信息" :isToggle="false">
           <FormItem :label-width="56" label="收货人">{{ user.detail.name }}</FormItem>
           <FormItem label="联系电话">{{ user.detail.tel }}</FormItem>
           <FormItem label="收货地址"><SText style="margin-top: 2px" :text="user.detail.address_detail" /></FormItem>
        </s-block>
        <s-block title="商品清单" :isToggle="false" v-if="user.id">
          <Form-item style="width: auto; position: relative;" :error="error.goods" label="商品" :label-width="44">
            <i-input
              class="add-goods"
              ref="goodsInput"
              v-model="commodity.name"
              placeholder="请输入商品名称/编码/别名/关键字"
              @on-change="searchCommodity"
              @on-enter="enterCommodity"
              @on-focus="checkBaseData"
              @on-blur="commodityDropDown = false"
              style="width: 300px;"
              clearable
            >
              <div class="batch-add-goods" slot="append" @click="batchAddGoods">
                <SIcon class="add-goods-icon" icon="add1" />
              </div>
            </i-input>
            <transition name="dropdown-fade">
              <div class="newOrder-dropdown" v-show="commodityDropDown">
                <ul class="commodity-list">
                  <li
                    class="dropdown-items"
                    v-for="info in commodityList"
                    :key="info.cid"
                    :class="{ active: commodity.id === info.id }"
                    @click="addCommodity(info)"
                  >
                    <strong class="dropdown-items-strong">{{
                      info.name
                    }}</strong>
                    <span class="dropdown-items-span">({{ info.unit }})</span>
                    <span
                      v-if="info.is_price_type == 1"
                      class="tag-protocol-price"
                      >协</span
                    >
                    <p class="dropdown-items-p">
                      {{ info.commodity_id }}
                      <Rate
                        style="float: right; font-size: 15px;"
                        :value="info.star"
                        disabled
                      ></Rate>
                    </p>
                  </li>
                </ul>
              </div>
            </transition>
            <transition name="dropdown-fade">
              <div
                class="newOrder-dropdown-content"
                v-show="
                  !commodityDropDown &&
                    commodity.name &&
                    commodityList.length === 0
                "
              >
                <p class="dropdown-empty">暂无数据</p>
              </div>
            </transition>
          </Form-item>
          <Form-item style="width: auto" label="数量">
            <NumberInput
              :precision="2"
              placeholder="输入订购数"
              v-model="commodity.amount"
              @on-focus="checkBaseData"
              @on-enter="enterAmount"
              ref="orderAmountInput"
              id="order-amount-input"
              style="width: 90px;"
            />
          </Form-item>
          <i-button
            @click="addCommodityToList()"
            >添加</i-button>
        <EditableTable
          style="margin-top: 14px"
          highlight-row
          isShowRecordEditor
          isHiddenAdd
          :defaultRow="false"
          :recordEditorConfig="{deleteConfirm: '确定删除商品'}"
          :max-height="tableHeight"
          :columns="columns3"
          :data="newOrderList"
          @on-delete="handleDelete"
          ref="orderTable">
          <template #after-table-left>
            <div class="after-table-left-hotkey">
              <SIcon icon="tips" :size="12" class="mr6" />
              <span>支持键盘操作，</span>
              <Icon type="ios-arrow-round-back" />
              <Icon type="ios-arrow-round-forward" />
              <span>左右切换，</span>
              <Icon type="ios-arrow-round-up" />
              <Icon type="ios-arrow-round-down" />
              <span>上下换行</span>
            </div>
          </template>
          <template #after-table-right>
            <div class="sum-info">
              <span class="label">采购金额：</span>
              <span class="value">¥{{ calPurchaseTotal || 0 }}</span>
              <span class="label" style="margin-left: 20px">合计金额：</span>
              <span class="value">¥{{ calTotal || 0 }}</span>
            </div>
          </template>
        </EditableTable>
        </s-block>
        <s-block title="其他信息" :isToggle="false" v-if="user.id">
          <Form-item class="mb16" label="订单备注">
            <i-input
              style="width: 424px"
              type="textarea"
              maxlength="500"
              show-word-limit
              placeholder="输入订单备注"
              v-model="remarks"
            ></i-input>
          </Form-item>
          <FormItem class="mb16" label="订单附件" style="width: 100%">
            <AttachmentUpload v-model="orderFiles" />
          </FormItem>
          <FormItem label="采购单附件" style="width: 100%">
            <AttachmentUpload v-model="purchaseFiles" />
          </FormItem>
        </s-block>
      <article class="newOrder">
        <!--审核弹框-->
        <div class="goods-promot-mask" v-show="goodsPromot"></div>
        <div class="goods-promot-layout" v-show="goodsPromot">
          <div class="goods-promot-wrap">
            <div class="goods-promot-content">
              <h3 style="text-align: center">现场采购订单</h3>
              <h3 style="color: red; text-align: center">
                确认保存后，现场采购订单不能修改
              </h3>
              <div style="width: 100%;overflow-x: auto">
                <table class="table">
                  <thead>
                  <tr>
                    <th>商品名称</th>
                    <th>订购单位</th>
                    <th>订购数</th>
                    <th>订购单价</th>
                    <th v-if="isEnableUserContractPriceDiscountRatio">折扣率（订）%</th>
                    <th>协议市场价（订）</th>
                    <th>折扣率</th>
                    <th>协议市场价</th>
                    <th>协议价</th>
                    <th v-if="+this.selectPurchaseType !== 4">采购单位</th>
                    <th v-if="+this.selectPurchaseType !== 4">采购数量</th>
                    <th v-if="+this.selectPurchaseType !== 4">采购单价</th>
                    <th v-if="+this.selectPurchaseType !== 4">采购小计</th>
                    <th>订购金额小计</th>
                    <th>备注</th>
                  </tr>
                  </thead>
                  <tbody>
                  <tr v-for="(list, index) in postList" :key="index">
                    <td>{{ list.name }}</td>
                    <td>{{ list.unit }}</td>
                    <td>
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        v-model="list.amount"
                        @on-change="changePurchasePrice(list, index)"
                        style="width: 100px;"
                      />
                    </td>
                    <td>
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        v-model="list.price"
                        style="width: 100px;"
                        @on-change="changePostListItem(list, 'price')"
                      />
                    </td>
                    <td v-if="isEnableUserContractPriceDiscountRatio">
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        v-model="list.order_protocol_discount"
                        style="width: 100px;"
                        @on-change="changePostListItem(list, 'order_protocol_discount')"
                      />
                    </td>
                    <td>
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        v-model="list.order_protocol_org_price"
                        style="width: 100px;"
                        @on-change="changePostListItem(list, 'order_protocol_org_price')"
                      />
                    </td>
                    <td>{{list.user_discount}}</td>
                    <td>{{list.user_org_price}}</td>
                    <td>{{list.user_price}}</td>
                    <td v-if="+selectPurchaseType !== 4">{{ list.unit_sell }}</td>
                    <td v-if="+selectPurchaseType !== 4">{{ list.unit_convert === 'N'?list.amount : (list.amount * list.unit_num).toFixed(2) }}</td>
                    <td v-if="+selectPurchaseType !== 4">
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        @on-change="changePrice(list, index, 'purchase_price')"
                        v-model="list.purchase_price"
                        style="width: 100px;"
                      />
                    </td>
                    <td v-if="+selectPurchaseType !== 4">
                      <NumberInput
                        :min="0"
                        :step="0.01"
                        :precision="2"
                        placeholder=""
                        v-model="list.purchase_sub_price"
                        @on-change="
                          changePrice(list, index, 'purchase_sub_price')
                        "
                        style="width: 100px;"
                      />
                    </td>
                    <td>{{ (list.total).toFixed(2) }}</td>
                    <td>
                      <Input
                        placeholder=""
                        v-model="list.remark"
                        style="width: 100px;"
                      />
                    </td>
                  </tr>
                  </tbody>
                </table>
              </div>
              <div class="newOrder-amount">
                采购金额：¥
                <span class="order-total-amount" style="margin-right: 20px">{{
                  calPurchaseTotal || 0
                }}</span>
                合计金额：¥
                <span class="order-total-amount">{{ calTotal || 0 }}</span>
              </div>

              <div class="newOrder-remarks">
                <i-form :label-width="120">
                  <Form-item label="现场采购单备注">
                    <i-input
                      maxlength="500"
                      placeholder="输入现场采购单备注"
                      v-model="remarks"
                    ></i-input>
                  </Form-item>
                </i-form>
              </div>
              <Button @click="closeConfirmModal">关闭</Button>
              <Button type="primary" @click="save()">{{
                this.returnSaving ? '保存中...' : '保存现场采购订单'
              }}</Button>
            </div>
          </div>
        </div>
        <!-- <div class="newOrder-feature">
          <i-button @click="cancel">取消</i-button>
        </div> -->
        <!-- operation end -->
        <!--<transition name="slide-fade">-->
        <usual v-if="usualOrderActive" @close="activeUsualOrder"></usual>
        <!--</transition>-->
      </article>
      </Form>
      <i-button
        slot="button-after"
        type="success"
        class="c-green-btn"
        @click="createSceneOrder()"
        :disabled="createDisabled"
      >
        保存现场采购订单
      </i-button>
    </DetailPage>
    <goods-list-modal
      :params="goodsFilters"
      v-model="showGoodsListModal"
      :uid="user.id"
      @on-add="handlerAdd"
      :selectedGoods="newOrderList"
    >
    <div></div>
    </goods-list-modal>
  </div>
</template>

<script>
import moment from 'moment'
import { mapState } from 'vuex';
import DetailPage from '@/components/detail-page/index.js'
import usual from '@components/order/NewOrderUsual';
import GoodsListModal from '@components/order/goodsListModal';
import NumberInput from '@components/basic/NumberInput';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue'
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import Goods from '@api/goods.js'
import common from '@api/order.js';
import LayoutMixin from '@/mixins/layout';
import configMixin from "@/mixins/config.js";
import '@assets/scss/mixin.scss';
import CalcPrice from './mixins/calcContractPrice'
import EditableTable from '@/components/editable-table/index.js';
import SIcon from '@/components/icon';
import SBlock from '@/components/s-block';
import SText from '@/components/s-text';
import { get } from "@/api/request";
import { getEditTableHeight } from '@/util/common';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

const purchaseType = {
  agent: {
    label: '采购员',
    value: '1'
  },
  provider: {
    label: '供应商',
    value: '2'
  }
};

export default {
  name: 'add-scene-order',
  mixins: [LayoutMixin, configMixin, CalcPrice],
  watch: {
    selectPurchaseType(newValue, oldValue) {
      const getProviderOrAgent = () => {
        if (newValue === purchaseType.agent.value) {
          this.getBuyer();
        } else {
          let that = this;
          common.getProvider().then(function(res) {
            if (res.status) {
              that.purchaseAgent = [];
              that.purchaseAgent = res.data;
            }
          });
        }
      };
      if (
        this.selectedId &&
        this.newOrderList &&
        this.newOrderList.length > 0
      ) {
        this.$Modal.confirm({
          content: `切换采购类型订购商品清单将被清空，是否继续切换？`,
          onOk: () => {
            getProviderOrAgent();
            this.clearGoods();
          },
          onCancel: () => {
            this.selectPurchaseType = oldValue;
          }
        });
      } else {
        getProviderOrAgent();
      }
    },
    selectedId(newValue, oldValue) {
      if (oldValue && this.newOrderList && this.newOrderList.length > 0) {
        const purchaseTypeLabel =
          this.selectPurchaseType === purchaseType.provider.value
            ? purchaseType.provider.label
            : purchaseType.agent.label;
        this.$Modal.confirm({
          content: `切换${purchaseTypeLabel}订购商品清单将被清空，是否继续切换？`,
          onOk: () => {
            this.clearGoods();
          },
          onCancel: () => {
            this.selectedId = oldValue;
          }
        });
      }
    }
  },
  computed: {
    ...mapState({
      isOrderClose: state => state.isOrderClose,
      isToOrder: state => state.isToOrder
    }),
    calTotal() {
      let totalNum = 0;
      this.postList.map(d => {
        // console.log(d);
        totalNum += +d.total;
        // console.log(d.total);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    // calTotals() {
    //   let totalNum = 0;
    //   this.postList.map(d => {
    //     totalNum = d.total;
    //     // d.price = d.total / d.amount;
    //   });
    //   console.log(totalNum);
    //   return parseFloat(totalNum).toFixed(2);
    // },
    // 采购金额
    calPurchaseTotal() {
      let totalNum = 0;
      this.postList.map(d => {
        // if (d.unit_convert === 'N') {
        //   totalNum += parseFloat(d.amount * d.purchase_price);
        // } else {
        //   totalNum += parseFloat(d.amount * d.unit_num * d.purchase_price);
        // }
        totalNum += parseFloat(d.purchase_sub_price);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    goodsFilters() {
      let filters = {
        is_purchase_agreement_price: 0,
        plan_date: moment(this.date).format('YYYY-MM-DD')
      };
      if (this.selectPurchaseType == purchaseType.provider.value) {
        filters.is_purchase_agreement_price = this.selectedId;
        filters.tax_rate_provider_id = this.selectedId;
      }
      return filters;
    },
    disabledEverything() {
      return this.user.name === '';
    },
  },
  data() {
    return {
      tableHeight: getEditTableHeight(),
      error: {
        user: '',
        delivery_date: '',
        goods: '',
        agent_provider: '',
        tag_id: '',
        provider_supervisor: '',
      },
      tagId: '',
      date: '',
      goodsPromot: false,
      returnSaving: false,
      calTotals: 0,
      showGoodsListModal: false,
      usualOrderActive: false, // 常用清单
      newOrderReviewActive: false,
      commodityDropDown: false, // 商品下拉框
      userDropDown: false,
      totalAmount: '',
      currentAmount: '', // 当前商品的数量(render函数使用)
      tUser: '', // * 追求最好的用户体验，暂时缓存用户信息
      div: '', // 判断是否关闭页面
      commodity: {
        id: '', // 序号
        cid: '', // 商品id
        name: '',
        amount: '',
        detail: '',
        discount: '',
        org_price: '',
      },
      commodityList: '',
      formData: {
        update_in_price: 1
      },
      user: {
        id: '',
        uid: '',
        name: '',
        detail: {}
      },
      userList: [],
      newOrderList: [],
      postList: [],
      remarks: '',
      createDisabled: false,
      isInputNumber: '', // 正在加入商品
      previousValue: '',
      checkOrderList: [],
      inputCheckOrderList: [],
      purchaseAgentAble: [],//非禁用，非回收站中的采购员账号
      purchaseAgent: [], //采购员或者供应商
      selectPurchaseAgent: '', //当前选中采购员或者供应商
      selectedId: '', //当前选中采购员id
      selectedSupervisorId: '',//当前选中采购负责人id
      selectPurchaseType: '1', //默认选中采购员
      username: '', //制单员
      checkColumns: [
        {
          type: 'index',
          width: 60,
          align: 'center'
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'center'
        },
        {
          title: '商品单位',
          key: 'unit',
          align: 'center'
        },
        {
          title: '订购数',
          key: 'amount',
          width: 100,
          align: 'center',
          props: {
            amountArr: ''
          },
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.amount),
                min: 0
                // precision: 2,
              },
              style: {
                width: '100%'
              },
              nativeOn: {
                click: () => {
                  this.isFocus = true;
                }
              },
              on: {
                'on-change': val => {
                  this.inputCheckOrderList[params.index]['check_amount'] = val;
                },
                'on-focus': event => {
                  event.target.select();
                }
              }
            });
          }
        },
        {
          title: '订购单价（元）',
          key: 'price',
          align: 'center'
        }
      ],
      columns3: [
        {
          title: '序号',
          align: 'center',
          width: 60,
          resizable: false,
          fixed: 'left',
          style: {
            paddingLeft: '8px'
          },
          render: (h, params) => {
            var obj = params.row,
              length = this.newOrderList.length,
              index = length - params.index;
            return h(
              'a',
              {
                attrs: {
                  name: obj.id
                }
              },
              [
                h('span', {
                  domProps: {
                    innerHTML: index
                  }
                })
              ]
            );
          }
        },
        {
          title: '商品图片',
          key: 'logo',
          width: 100,
          fixed: 'left',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          fixed: 'left',
          width: 260,
          poptip: true,
          resizable: true,
          className: 'commodity-name-wrap',
          render: (h, params) => {
            var obj = params.row;
            return h('div', [
              h(
                'span',
                {
                  class: {
                    'new-logo': obj.new,
                    dn: !obj.new
                  }
                },
                '新'
              ),
              h(
                'span',
                {
                  class: {
                    'commodity-name': true
                  }
                },
                obj.name
              )
            ]);
          }
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'center',
          width: 120,
        },
        {
          title: '订购单位',
          key: 'unit',
          align: 'center',
          width: 90,
        },
        {
          title: '订购数',
          key: 'amount',
          align: 'center',
          width: 100,
          render: (h, params) => {
            let data = params.row,
              me = this;
            let template = h(NumberInput, {
              props: {
                value: Number(data.amount),
                min: 0,
                precision: 2
              },
              style: {
                width: '100%'
              },
              on: {
                'on-focus': event => {
                  event.target.select();
                  params.row.amount_warning = '';
                },
                'on-change'(val) {
                  params.row.amount = val;
                  let amount =
                    params.row.unit_convert === 'N'
                      ? params.row.amount
                      : (params.row.amount * params.row.unit_num).toFixed(2);
                  me.postList[params.index]['amount'] = val;
                  me.postList[params.index]['amount_warning'] = '';
                  params.row.total = Number(val).mul(Number(params.row.price)).toFixed(2);
                  params.row.purchase_sub_price = +(
                    Number(params.row.purchase_price).mul(Number(amount))
                  ).toFixed(2);

                  me.postList[params.index]['purchase_sub_price'] = +(
                    Number(params.row.purchase_price).mul(Number(amount))
                  ).toFixed(2);
                  me.postList[params.index]['total'] = (
                    Number(val).mul(Number(params.row.price))
                  ).toFixed(2);
                  me.calTotals = '';
                },
                'on-enter'(event) {
                  me.goNextInput(params.index, me.newOrderList.length, event);
                }
              }
            });
            if (params.row.amount_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'top',
                    content: params.row.amount_warning,
                    always: true,
                    transfer: true
                  }
                },
                [template]
              );
            }
            return template;
          }
        },
        {
          title: '订购单价（元）',
          key: 'price',
          align: 'center',
          width: 200,
          render: (h, params) => {
            let obj = params.row,
              me = this;
            let span = '';
            if (obj.is_price_type == 1) {
              span = 'span';
            } else {
              span = '';
            }
            // let price = '';
            // obj.is_price_type == 1
            //   ? (price = obj.type_price)
            //   : (price = obj.price);
            return h('div', [
              h(NumberInput, {
                props: {
                  value: obj.price,
                  min: 0,
                  precision: 2
                },
                style: {
                  width: '120px'
                },
                on: {
                  'on-focus': event => {
                    event.target.select();
                  },
                  'on-change'(val) {
                    params.row.price = val;
                    me.postList[params.index]['price'] = val;
                    me.computeUserContractPrice(params.row, 'price');
                    me.postList[params.index]['order_protocol_discount'] = params.row.order_protocol_discount;
                    me.postList[params.index]['order_protocol_org_price'] = params.row.order_protocol_org_price;
                    me.postList[params.index]['price'] = params.row.price;
                    params.row.total = (Number(val).mul(Number(params.row.amount))).toFixed(2);
                    me.postList[params.index]['total'] = (
                      Number(val).mul(Number(params.row.amount))
                    ).toFixed(2);
                    me.calTotals = '';
                  },
                  'on-enter'(event) {
                    me.goNextInput(params.index, me.newOrderList.length, event);
                  }
                }
              }),
              h(
                span,
                {
                  class: ['tag-protocol-price']
                },
                '协'
              )
            ]);
          }
        },
        {
          width: 120,
          title: '折扣率(订)%',
          key: 'order_protocol_discount',
          render: (h, params) => {
            const me = this;
            let key = 'order_protocol_discount';
            return h(NumberInput, {
              props: {
                value: params.row[key],
                precision: 2
              },
              on: {
                'on-focus': event => {
                  event.target.select();
                },
                'on-change': value => {
                  params.row[key] = value;
                  me.computeUserContractPrice(params.row, key);
                  me.postList[params.index]['order_protocol_discount'] = params.row.order_protocol_discount;
                  me.postList[params.index]['order_protocol_org_price'] = params.row.order_protocol_org_price;
                  me.postList[params.index]['price'] = params.row.price;
                }
              },
            });
          }
        },
        {
          width: 120,
          title: '协议市场价（订)',
          key: 'order_protocol_org_price',
          render: (h, params) => {
            const me = this;
            let key = 'order_protocol_org_price';
            return h(NumberInput, {
              props: {
                value: params.row[key],
                precision: 2
              },
              on: {
                'on-focus': event => {
                  event.target.select();
                },
                'on-change': value => {
                  params.row[key] = value;
                  me.computeUserContractPrice(params.row, key);
                  me.postList[params.index]['order_protocol_discount'] = params.row.order_protocol_discount;
                  me.postList[params.index]['order_protocol_org_price'] = params.row.order_protocol_org_price;
                  me.postList[params.index]['price'] = params.row.price;
                }
              },
            });
          }
        },
        {
          width: 90,
          title: '折扣率',
          key: 'user_discount',
          render: (h, params) => {
            let key = 'user_discount';
            let style = {
              color: params.row[key] - params.row.order_protocol_discount === 0 ? '' : 'red'
            };
            return h(
              'span',
              {
                style
              },
              params.row[key]
            );
          }
        },
        {
          width: 90,
          title: '协议市场价',
          key: 'user_org_price',
          render: (h, params) => {
            let key = 'user_org_price';
            let style = {
              color: params.row[key] - params.row.order_protocol_org_price === 0 ? '' : 'red'
            };
            return h(
              'span',
              {
                style
              },
              params.row[key]
            );
          }
        },
        {
          width: 90,
          title: '协议价',
          key: 'user_price',
          render: (h, params) => {
            let key = 'user_price';
            let style = {
              color: params.row[key] - params.row.price === 0 ? '' : 'red'
            };
            return h(
              'span',
              {
                style
              },
              params.row[key]
            );
          }
        },
        {
          title: '订购金额小计（元）',
          key: 'total',
          align: 'center',
          width: 160,
          render: (h, params) => {
            let me = this;
            return h(NumberInput, {
              props: {
                value: params.row.total,
                min: 0,
                precision: 2
              },
              class: {
                'total-amount': true
              },
              style: {
                width: '100%'
              },
              on: {
                'on-focus': event => {
                  event.target.select();
                },
                'on-change'(val) {
                  params.row.total = val;
                  me.postList[params.index]['total'] = val;
                  me.calTotals = val;
                  params.row.price = (val / params.row.amount).toFixed(2);
                  me.computeUserContractPrice(params.row, 'price');
                  me.postList[params.index]['order_protocol_org_price'] = params.row.order_protocol_org_price;
                  me.postList[params.index]['price'] = (
                    val / params.row.amount
                  ).toFixed(2);
                },
                'on-enter'(event) {
                  me.goNextInput(params.index, me.newOrderList.length, event);
                }
              }
            });
          }
        },
        {
          title: '订单商品标签',
          key: 'order_commodity_tag',
          width: 120,
          render: (h, params) => {
            const {row, index} = params
            const key = 'order_commodity_tag'
            return h(mvSelect, {
              attrs: {
                clearable: true,
                placeholder: '请选择',
                transfer: true,
              },
              class: {
                'required-tip': (row[key] === '' && this.isOrderCommodityTagRequired)
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal: row.order_commodity_tag
              },
              on: {
                'on-change': (tagId, item) => {
                  this.postList[index][key] = this.newOrderList[index][key] = row[key] = tagId || ''
                },
              }
            })
          }
        },
        {
          title: '采购单位',
          key: 'unit_sell',
          align: 'center',
          width: 90,
					// 选择仓库自销时, 置灰表头
					renderHeader: (h, params) => {
						return h('span', {
							style: {
								color: +this.selectPurchaseType === 4 ? '#e6e6e6' : 'black'
							},
							domProps: {
								innerHTML: '采购单位'
							}
						});
					},
					render: (h, params) => {
						return h('span', {
							style: {
								color: +this.selectPurchaseType === 4 ? 'rgba(0,0,0,0)' : 'black'
							},
							domProps: {
								innerHTML: params.row.unit_sell
							}
						});
					}
        },
        {
          title: '采购数量',
          key: 'purchase_num',
          align: 'center',
          width: 90,
					// 选择仓库自销时, 置灰表头
					renderHeader: (h, params) => {
						return h('span', {
							style: {
								color: +this.selectPurchaseType === 4 ? '#e6e6e6' : 'black'
							},
							domProps: {
								innerHTML: '采购数量'
							}
						});
					},
          render: (h, params) => {
            return h('span', {
              class: {
                purchaseNum: true
              },
							style: {
								color: +this.selectPurchaseType === 4 ? 'rgba(0,0,0,0)' : 'black'
							},
              domProps: {
                innerHTML:
                  params.row.unit_convert === 'N'
                    ? params.row.amount
                    : (params.row.amount * params.row.unit_num).toFixed(2)
              }
            });
          }
        },
        {
          title: '采购单价（元）',
          key: 'purchase_price',
          align: 'center',
          width: 120,
					// 选择仓库自销时, 置灰表头
					renderHeader: (h, params) => {
						return h('span', {
							style: {
								color: +this.selectPurchaseType === 4 ? '#e6e6e6' : 'black'
							},
							domProps: {
								innerHTML: '采购单价（元）'
							}
						});
					},
          render: (h, params) => {
            let data = params.row,
              me = this;
            let inputEl = h(NumberInput, {
              props: {
                value: Number(data.purchase_price),
                min: 0,
								disabled: +this.selectPurchaseType === 4,
                precision: 2
              },
              style: {
                width: '100%'
              },
							class: {
								'dn': +this.selectPurchaseType === 4,
							},
              on: {
                'on-focus': event => {
                  params.row.purchase_price_warning = '';
                  event.target.select();
                },
                'on-change'(val) {
                  let amount =
                    params.row.unit_convert === 'N'
                      ? params.row.amount
                      : (params.row.amount * params.row.unit_num).toFixed(2);
                  params.row.purchase_price = val;
                  params.row.purchase_sub_price = +(Number(val).mul(Number(amount))).toFixed(2);
                  me.postList[params.index]['purchase_sub_price'] =
                    params.row.purchase_sub_price;
                  me.postList[params.index]['purchase_price'] = val;
                  me.postList[params.index]['purchase_price_warning'] = '';
                  me.postList[params.index]['purchase_sub_price_warning'] = '';
                  params.row['purchase_price_warning'] = '';
                  params.row['purchase_sub_price_warning'] = '';
                },
                'on-enter'(event) {
                  me.goNextInput(params.index, me.newOrderList.length, event);
                }
              }
            });
            let warningEl = null
            if (params.row.purchase_price_warning) {
              warningEl = h('div', {
                style: {
                  color: 'red',
                  fontSize: '12px'
                }
              }, params.row.purchase_price_warning)
            }
            return h('div', [
              inputEl,
              warningEl
            ]);
          }
        },
        {
          title: '采购小计（元）',
          key: 'purchase_sub_price',
          align: 'center',
          width: 120,
					// 选择仓库自销时, 置灰表头
					renderHeader: (h, params) => {
						return h('span', {
							style: {
								color: +this.selectPurchaseType === 4 ? '#e6e6e6' : 'black'
							},
							domProps: {
								innerHTML: '采购小计（元）'
							}
						});
					},
          render: (h, params) => {
            let data = params.row,
              me = this;
            let inputEl = h(NumberInput, {
              props: {
                value: Number(data.purchase_sub_price),
                min: 0,
								disabled: +this.selectPurchaseType === 4,
                precision: 2
              },
							class: {
								'dn': +this.selectPurchaseType === 4,
							},
              style: {
                width: '100%'
              },
              on: {
                'on-focus': event => {
                  params.row.purchase_sub_price_warning = '';
                  event.target.select();
                },
                'on-change'(val) {
                  let amount = params.row.unit_convert === 'N'
                    ? params.row.amount
                    : (params.row.amount * params.row.unit_num).toFixed(2);
                  params.row.purchase_sub_price = val;
                  params.row.purchase_price = +(
                    Number(val).div(Number(amount))
                  ).toFixed(2);
                  me.postList[params.index]['purchase_price'] = +(
                     Number(val).div(Number(amount))
                  ).toFixed(2);
                  me.postList[params.index]['purchase_sub_price'] = val;
                  me.postList[params.index]['purchase_price_warning'] = '';
                  me.postList[params.index]['purchase_sub_price_warning'] = '';
                  params.row['purchase_price_warning'] = '';
                  params.row['purchase_sub_price_warning'] = '';
                },
                'on-enter'(event) {
                  me.goNextInput(params.index, me.newOrderList.length, event);
                }
              }
            });
            let warningEl = null
            if (params.row.purchase_sub_price_warning) {
              warningEl = h('div', {
                style: {
                  color: 'red',
                  fontSize: '12px'
                }
              }, params.row.purchase_sub_price_warning)
            }
            return h('div', [
              inputEl,
              warningEl
            ]);
          }
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          width: 120,
          render: (h, params) => {
            var data = params.row,
              me = this;
            return h('i-input', {
              class: {
                remarks: true
              },
              attrs: {
                value: data.remark
              },
              nativeOn: {
                change: () => {
                  var index = data._index,
                    remark = document.getElementsByClassName('remarks')[index],
                    input = remark.getElementsByTagName('input')[0],
                    value = input.value;
                  me.postList[index].remark = value;
                }
              }
            });
          }
        }
      ],
      quickClick: true,
      orderTagList: [],
      selectedOrderTag: [],
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      orderGoodsTagList: [],
      orderFiles: [], // 订单附件
      purchaseFiles: [], // 采购单附件
      isChangeTotal: false
    };
  },
  created() {
    this.formData.update_in_price = +this.sysConfig.is_open_update_in_price;
    this.initColumns()
    this.username = sessionStorage['userName'];
    this.$store.state.isToOrder = '';
    this.getDate();
    this.listenerCloseWin();
    this.getBuyer();
    // 得到标签配置，是否可以添加多个标签，是否只能添加一个标签
    this.commonService.getConfig().then(config => {
      const {
        is_open_order_tag_required: must_tag,
        is_open_order_tag_only_one: only_tag
      } = config;
      this.must_tag = must_tag;
      this.only_tag = only_tag;
    });
    this.getOrderGoodsTagList()
  },
  beforeRouteLeave: function(to, from, next) {
    // if (this.newOrderList.length || this.user.id && !this.isClose && !this.isToOrder) {
    if (this.newOrderList.length || (this.user.id)) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function() {
          next();
        },
        onCancel: function() {
          next(false)
        }
      });
      // if (confirm('系统可能不会保存您所做的更改。')) {
      //   next();
      // }
    } else {
      // this.isClose = '';
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  mounted() {
    this.setMode(3);
    this.changeDiscountRatioKey('order_protocol_discount');
    this.changeMarketPriceKey('order_protocol_org_price');
    this.changeAgreementPriceKey('price');
    this.focusUserInput();
    getWidth();
    let date = document.querySelectorAll('.ivu-date-picker')[0],
      input = date.getElementsByClassName('ivu-input')[0];

    input.readOnly = true;
  },
  methods: {
    handleDelete(row) {
      const rowIndex = this.postList.findIndex(item => item.id === row.id)
      if (rowIndex !== -1) {
        this.postList.splice(rowIndex, 1)
      }
      this.syncList(false);
    },
    initColumns() {
      if(!this.isOpenOrderCommodityTag) {
        const existIndex = this.columns3.findIndex(item => item.key === 'order_commodity_tag')
        if(existIndex!==-1) {
          this.columns3.splice(existIndex,1)
        }
      }
      if (!this.isEnableUserContractPriceDiscountRatio) {
        const existIndex = this.columns3.findIndex(item => item.key === 'order_protocol_discount')
        if(existIndex!==-1) {
          this.columns3.splice(existIndex,1)
        }
      }
    },
    // 根据接口数据批量更新计划采购价
    async batchUpdatePurchasePrice() {
      const goodsIds = this.newOrderList.map(item => item.id);
      const params = {
        provider_id: this.selectedId,
        valuation_date: moment(this.date).format('YYYY-MM-DD'),
        commodity_ids: goodsIds.join(',')
      }
      // 返回的商品都是有协议价的商品
      const { data: goodsList } = await get(
        '/superAdmin/purchaseAgreementPrice/getPriceByValuationDate',
        params
      );
      this.newOrderList.forEach((item, index) => {
        const goodsItem = goodsList.find(d => d.commodity_id === item.id)
        if (goodsItem) {
          const { price: purchase_price } = goodsItem
          const purchase_sub_price = +(Number(item.amount).mul(Number(purchase_price))).toFixed(2)
          const postItem = this.postList.find(d => d.id === item.id)
          this.$set(this.newOrderList, index, {
            ...item,
            ...(postItem || {}),
            purchase_price,
            purchase_sub_price,
          })
          this.postList[index].purchase_price = purchase_price
          this.postList[index].purchase_sub_price = purchase_sub_price
        }
      })
    },
    getOrderGoodsTagList () {
      if(!this.isOpenOrderCommodityTag) return
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data
        let defaultTag = this.orderGoodsTagList.find(item => item.is_default === '1')
        this.tagId = defaultTag ? defaultTag.id : ''
      })
    },
    getOrderTagList (user_id) {
      const params = {
        user_id,
        pageSize: 9999
      }
      common.qryOrderTagList(params).then(res => {
        const { status, data } = res
        if (status) {
          this.orderTagList = data || []
          // this.selectedOrderTag中只保留this.orderTagList存在的标签
          this.selectedOrderTag = this.selectedOrderTag.filter(item => {
            return this.orderTagList.some(tag => tag.id === item.id)
          })
        }
      })
    },
    // 订单标签限制
    checkTag() {
      if (this.orderTagList.length === 0) return true;

      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.$Message.warning('一个订单最多存在3个标签');
        return false;
      }

      if (this.only_tag === '1' && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.$Message.warning('一个订单只能选择一个标签');
        return false;
      }

      if (this.must_tag === '1' && this.selectedOrderTag.length === 0) {
        this.$Message.warning('请至少选择一个订单标签');
        return false;
      }
      return true;
    },
    changePrice(list, index, type) {
      const amount = list.unit_convert === 'N' ? list.amount : list.amount * list.unit_num

      if (type === 'purchase_price') {
        list.purchase_sub_price = +(Number(amount).mul(Number(list.purchase_price))).toFixed(
          2
        );
      } else {
        list.purchase_price = +(Number(list.purchase_sub_price).div(Number(amount))).toFixed(
          2
        );
      }
    },
    changePurchasePrice(list) {
      let amount = list.unit_convert === 'N' ? list.amount : list.amount * list.unit_num
      list.purchase_sub_price = (Number(amount).mul(Number(list.purchase_price))).toFixed(2)

      list.total = (list.amount * list.price).toFixed(2)
      this.isChangeTotal = true
    },
    changePostListItem(list, key) {
      // 计算小计
      if (key === 'price') {
        list.total = +(list.amount * list.price).toFixed(2)
        this.isChangeTotal = true
      }
      this.computeUserContractPrice(list, key);
    },
    handleChangeAgentAndProvider() {
      this.checkBaseData();
    },
    handleChangeDeliveryDate() {
      this.checkBaseData();
      // 如果选择的是供应商,检查协议价
      if (this.selectPurchaseType === purchaseType.provider.value) {
        this.batchUpdatePurchasePrice()
      }
    },
    handleOrderCommodityTag() {
      this.checkBaseData();
    },
    clearGoods() {
      this.postList = [];
      this.newOrderList = [];
    },
    closeConfirmModal() {
      this.goodsPromot = false;
      this.syncList(this.isChangeTotal);
    },
    syncList(flag = true) {
      let list = this.deepClone(this.newOrderList);
      list.forEach(item => {
        let postItem = this.postList.find(postItem => postItem.id === item.id);
        if (postItem) {
          item.amount = postItem.amount;
          item.price = postItem.price;
          if (flag) {
            item.total = (postItem.amount * postItem.price).toFixed(2)
          }
          item.purchase_price = postItem.purchase_price;
          item.remark = postItem.remark;
          item.purchase_sub_price = postItem.purchase_sub_price;
          item.order_protocol_discount = postItem.order_protocol_discount;
          item.order_protocol_org_price = postItem.order_protocol_org_price;
        }
      });
      this.newOrderList = list;
      this.postList = this.deepClone(list);
    },
    getDefaultTag() {

      let defaultOption = this.orderGoodsTagList.find(item => item.is_default === '1')
      let tagId = defaultOption ? defaultOption.id : ''
      return tagId
    },
    handlerAdd(orders) {
      if (orders) {
        this.newOrderList = this.cloneObj(this.postList);
        orders.forEach(item => {
          let amount =
            item.unit_convert === 'N'
              ? item.amount
              : (item.amount * item.unit_num).toFixed(2);
          item.purchase_price = this.getPurchaseContractPrice(item) || 0;
          item.purchase_sub_price = +(Number(item.purchase_price).mul(Number(amount))).toFixed(2);
          item.total = (Number(item.amount).mul(Number(item.price))).toFixed(2);
          item.order_commodity_tag = this.getDefaultTag()
          item.order_protocol_discount = item.discount;
          item.user_org_price = item.org_price;
          item.order_protocol_org_price = item.org_price;
          item.user_price = item.price;
          item.user_discount = item.discount;
          this.newOrderList.unshift(item);
        });
        this.postList = this.cloneObj(this.newOrderList);
      }
    },
    focusUserInput() {
      this.$nextTick(() => {
        let input = this.$refs.userInput.$el.querySelector(
          'input[type="text"]'
        );
        if (input) {
          input.focus();
        }
      });
    },
    focusGoodsInput() {
      this.$nextTick(() => {
        let input = this.$refs.goodsInput.$el.querySelector(
          'input[type="text"]'
        );
        if (input) {
          input.focus();
        }
      });
    },
    focusAmountInput() {
      this.$nextTick(() => {
        let input = this.$refs.orderAmountInput.$el.querySelector(
          'input'
        );
        if (input) {
          input.focus();
        }
      });
    },
    batchAddGoods() {
      if (!this.checkBaseData()) {
        return false;
      }
      this.showGoodsListModal = true;
    },
    // 获取商品改变前的数量
    getAmount: function(obj) {
      var index = '';

      index = typeof obj === 'object' ? obj._index : obj;
      var amount = document.getElementsByClassName('amount')[index],
        value = amount.getElementsByTagName('input')[0].value; // 获取选中商品的数量

      this.currentAmount = value;
    },
    // 监听关闭窗口
    listenerCloseWin: function() {
      // var self = this;
      window.onbeforeunload = function() {};
    },
    // 获取现在的日期
    getDate: function() {
      var date = new Date();
      // 获得当前的日期
      // var nowadays = date.getDate();

      var tomorrow = new Date(date.getTime() + 3600 * 1000 * 24);
      var year = tomorrow.getFullYear(),
        month = tomorrow.getMonth() + 1,
        day = tomorrow.getDate();
      // 自动获取明天的日期
      this.date = year + '-' + month + '-' + day;
    },
    getBuyer: function() {
      var that = this;
      common.getPurchaseAgent().then(function(res) {
        if (res.status) {
          that.purchaseAgent = res.data;
        }
      });
      common.getPurchaseAgent({filter_disable_agent:1}).then(function(res) {  //获取非禁用采购员
        if (res.status) {
          that.purchaseAgentAble = res.data;
        }
      });
    },
    changeTypePurchaseType: function() {
      if (this.selectPurchaseType == 1) {
        this.getBuyer();
      } else {
        var that = this;
        common.getProvider().then(function(res) {
          if (res.status) {
            that.purchaseAgent = [];
            that.purchaseAgent = res.data;
          }
        });
      }
    },
    setScrollTop: function(id) {
      var dropdown = document.getElementsByClassName('newOrder-dropdown')[id];
      setTimeout(function() {
        dropdown.scrollTop = 0;
      }, 0);
    },
    userKeyControll: function() {
      var self = this,
        length = self.userList.length,
        first = self.userList[0].uid,
        last = self.userList[length - 1].uid,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0];

      document.onkeydown = function(event) {
        var e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        // self.user.uid = first;
        self.user.uid !== '' ? (checkId = self.user.uid) : (checkId = -1);
        // 如果点击向上按钮
        if (e && e.keyCode === 38 && self.userDropDown) {
          if (checkId !== -1 && checkId > first) {
            self.user.uid = self.user.uid - 1;
            // 控制滚动条的滚动，50为单个选项的高度
            // if (checkId !== 0 && checkId )
            dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 50);
          }
        }

        // 如果点击向下按钮
        if (e && e.keyCode === 40 && self.userDropDown) {
          // 如果是首次点击向下
          if (checkId === -1) {
            self.user.uid = first;
          }
          if (checkId !== -1 && checkId < last) {
            self.user.uid = self.user.uid + 1;
            // 获取下面的内容
            if ((checkId !== 0 && checkId % 3 === 0) || checkId > 3) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 50);
            }
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          if (self.user.uid === 0 || self.user.uid) {
            self.user.id = self.userList[self.user.uid].id;
            self.user.name = self.userList[self.user.uid].email;
            self.user.detail = self.userList[self.user.uid];
            // 处理左侧栏样式问题
            if (self.user.id) {
              self.$store.state.isOrderClose = 1;
            } else {
              self.$store.state.isOrderClose = '';
            }
          }
        }
      };
    },
    commodityKeyControll: function() {
      var self = this,
        length = self.commodityList.length,
        first = self.commodityList[0].id,
        last = self.commodityList[length - 1].id,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[1];

      document.onkeydown = function(event) {
        var e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        self.commodity.id !== ''
          ? (checkId = self.commodity.id)
          : (checkId = -1);

        // 如果点击向上按钮
        if (e && e.keyCode === 38 && self.commodityDropDown) {
          if (checkId !== -1 && checkId > first) {
            self.commodity.id = self.commodity.id - 1;
            // 控制滚动条的滚动，50为单个选项的高度
            dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 50);
          }
        }
        // 如果点击向下按钮
        if (e && e.keyCode === 40 && self.commodityDropDown) {
          // 如果是首次点击向下
          if (checkId === -1) {
            self.commodity.id = first;
          }
          if (checkId !== -1 && checkId < last) {
            self.commodity.id = self.commodity.id + 1;
            if ((checkId !== 0 && checkId % 3 === 0) || checkId > 3) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 50);
            }
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          // 如果已经加入购物车，则无法选择
          if (self.commodity.id === 0 || self.commodity.id) {
            self.commodity.cid =
              self.commodityList[self.commodity.id].commodity_id;
            self.commodity.name = self.commodityList[self.commodity.id].name;
          }
        }
      };
    },

    addUser: function(value) {
      // 注销事件 ，隐藏下拉框
      this.userDropDown = false;
      document.onkeydown = () => {};
      var self = this,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0];

      // 如果已经选择商品，则提示
      if (self.newOrderList.length) {
        self.$Modal.confirm({
          title: '修改客户',
          content: '<p>修改客户之后将清空已加入的商品列表</p>',
          onOk: function() {
            self.user.id = value.id;
            self.user.name = value.email;
            self.user.detail = value;
            self.newOrderList = [];
            self.postList = [];
            // 将搜索出来的商品列表置顶
            dropdown.scrollTop = 0;
            this.getOrderTagList(value.id);
          },
          onCancel: function() {}
        });
      } else {
        self.user.id = value.id;
        self.user.name = value.email;
        self.user.detail = value;
        // 将搜索出来的商品列表置顶
        dropdown.scrollTop = 0;
        // 处理左侧栏样式问题
        if (self.user.id) {
          self.$store.state.isOrderClose = 1;
        } else {
          self.$store.state.isOrderClose = '';
        }
        this.getOrderTagList(value.id);
      }
    },
    addCommodity: function(value) {
      // 如果已经加入订单列表，则无法再次加入
      if (value.is_selected) {
        this.autoFocus(value);
        return;
      }
      this.commodity.cid = value.commodity_id;
      this.commodity.name = value.name;
      this.commodity.discount = value.discount;
      this.commodity.org_price = value.org_price;
      this.commodity.type_price = value.type_price;
      // 将搜索出来的商品列表置顶
      var dropdown = document.getElementsByClassName('newOrder-dropdown')[1];
      dropdown.scrollTop = 0;
      this.focusAmountInput();
    },
    searchUser: function() {
      this.error.user = '';
      var self = this;
      // var dropdown = document.getElementsByClassName('newOrder-dropdown')[0];
      // 如果删除客户搜索信息，则清空客户信息
      // self.user = { id: '', uid: '', name: '', detail: '' };
      common.getUserBySearch(self.user.name,1).then(function(res) {
        if (res.status) {
          var data = res.data;
          self.userList = data;

          // 加上排序
          for (var i = 0, length = self.userList.length; i < length; i++) {
            if (!self.userList[i].uid) {
              self.userList[i].uid = i;
            }
          }
          i = null;

          if (data && data.length) {
            self.userDropDown = true;
            self.userKeyControll();
          } else {
            self.userDropDown = false;
          }

          let firstUser = data[0];
          // 下拉列表默认选中第一个用户
          self.user.default_item = firstUser;
          self.user.uid = firstUser.uid;

          self.setScrollTop(0);
        }
      });
    },
    searchCommodity: function() {
      this.error.goods = '';
      var self = this;
      self.commodity.id = self.commodity.cid = ''; // 每次重新搜索必须清空
      let param = {
        query: self.commodity.name,
        user_id: self.user.id
      };
      // 供应商ID
      if (this.selectPurchaseType == purchaseType.provider.value) {
        param.tax_rate_provider_id = this.selectedId;
      }
      common.getCommodity(param).then(function(res) {
        if (res.status) {
          if (param.query != self.commodity.name) {
            return false;
          }
          let data = res.data.commodities;
          self.commodityList = data;
          // 加上排序
          let i = 0;
          // eslint-disable-next-line no-global-assign
          for (i = 0, length = self.commodityList.length; i < length; i++) {
            if (!self.commodityList[i].id) {
              self.commodityList[i].id = i;
            }
          }
          // eslint-disable-next-line no-global-assign
          i = length = null;
          setTimeout(function() {
            self.setStyle();
          }, 0);
          if (data && data.length) {
            self.commodityDropDown = true;
            self.commodityKeyControll();
          } else {
            self.commodityDropDown = false;
          }
          let firstCom = data[0];
          self.commodity.cid = firstCom.commodity_id; // 默认选中第一个商品
          self.commodity.id = firstCom.id; // 默认选中第一个商品
          self.commodity.default_item = firstCom; // 默认选中第一个商品
          self.setScrollTop(1);
        }
      });
    },
    // 遍历查询商品，如果已经被选中，则设置为不可选择
    setStyle: function() {
      let self = this,
        list = document.getElementsByClassName('commodity-list')[0],
        goods = list.getElementsByClassName('dropdown-items');

      //已经加入订单列表的商品设置属性区分
      if (self.newOrderList.length) {
        for (
          var k = 0, newLength = self.newOrderList.length;
          k < newLength;
          k++
        ) {
          for (var i = 0, length = self.commodityList.length; i < length; i++) {
            if (
              self.commodityList[i].commodity_id === self.newOrderList[k].id
            ) {
              self.commodityList[i].is_selected = true;
              goods[i]
                ? (goods[i].className = 'dropdown-items selected')
                : undefined;
            }
          }
        }
      }
    },
    getPurchaseContractPrice(goods) {
      return goods.purchase_agreement_price &&
        Number(goods.purchase_agreement_price) > 0
        ? goods.purchase_agreement_price
        : '';
    },
    // 添加商品
    addCommodityToList() {
      // 避免连续点击多次
      if (this.quickClick) {
        this.quickClick = false;
      } else {
        return false;
      }
      let amount = this.commodity.amount;
      if (!amount) {
        amount = this.isInputNumber;
      }

      // 必须选择客户和正确的商品
      if (!this.checkBaseData()) {
        this.quickClick = true;
        return;
      }
      if (!this.commodity.cid) {
        this.errorNotice('请输入正确的商品');
        this.focusGoodsInput();
        this.quickClick = true;
        return;
      }
      if (!amount || amount < 0) {
        this.errorNotice('请输入商品订购数量');
        this.quickClick = true;
        this.focusAmountInput();
        return;
      }

      // 这里通过客户id和商品id获取订单商品数据
      let params = {
        plan_date: moment(this.date).format('YYYY-MM-DD'),
        delivery_date: moment(this.date).format('YYYY-MM-DD'),
        user_id: this.user.id,
        commodity_id: this.commodity.cid
      };

      if (this.selectPurchaseType == purchaseType.provider.value) {
        params.provider_id = this.selectedId;
      }
      this.$request.get(this.apiUrl.getOrderCommodity, params).then(res => {
        if (res.status) {
          this.newOrderList = this.cloneObj(this.postList);
          // 商品已经存在
          if (this.newOrderList.find(goods => goods.id == this.commodity.cid)) {
            this.errorNotice('商品已存在');
            this.quickClick = true;
            this.focusGoodsInput();
            return false;
          }
          var data = res.data, arr = {};
          arr = data;
          arr.amount = amount; // 添加商品填写的数量写入商品详情中
          arr.purchase_price = this.getPurchaseContractPrice(data) || 0; // 添加商品填写的数量写入商品详情中
          arr.purchase_sub_price = +(Number(arr.purchase_price).mul(Number(amount))).toFixed(2);
          arr.order_commodity_tag = this.getDefaultTag()
          arr.amount_warning = '';
          arr.purchase_price_warning = '';
          arr.purchase_sub_price_warning = '';
          arr.total = (Number(arr.amount).mul(Number(arr.price))).toFixed(2);
          arr.order_protocol_discount = this.commodity.discount;
          arr.user_org_price = this.commodity.org_price;
          arr.order_protocol_org_price = this.commodity.org_price;
          arr.user_price = this.commodity.type_price;
          arr.user_discount = this.commodity.discount;
          this.newOrderList.unshift(arr);
          this.postList = this.cloneObj(this.newOrderList);
          // 加入商品之后初始化商品
          this.commodity = {
            id: '',
            cid: '',
            name: '',
            amount: '',
            detail: '',
          };
          // 再次跳转商品选择
          this.quickClick = true;
          this.focusGoodsInput();
        }
      });
      this.commodity.amount = '';
      this.isInputNumber = '';
    },

    addUserHandmade(value) {
      this.newOrderList = this.cloneObj(this.postList);
      value.commodity_arr.forEach(e => {
        e.amount_pic = '';
      });

      if (!this.user.id) {
        this.user.id = value.user_info.id;
        this.user.detail = {
          name: value.user_info.name,
          tel: value.user_info.tel,
          address_detail: value.user_info.address_detail
        };

        this.newOrderList = [...this.newOrderList, ...value.commodity_arr];
        this.postList = this.cloneObj(this.newOrderList);
        return;
      }
      if (this.user.id == value.user_info.id) {
        value.commodity_arr.forEach(e => {
          this.newOrderList.forEach(se => {
            if (e.id == se.id) {
              se.amount = parseFloat(se.amount) + parseFloat(e.amount);
            }
          });
        });

        let diffArr = this.diff(value.commodity_arr, this.newOrderList);
        this.newOrderList = [...this.newOrderList, ...diffArr];
        this.postList = this.cloneObj(this.newOrderList);
      }
    },
    //复合数组求差集
    diff(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) {
        return false;
      }
      let result = a;
      for (let i = 0; i < b.length; i++) {
        result = result.filter(e => e.id != b[i].id);
      }

      return result;
    },
    // 按enter之后跳转到订购数的输入框
    enterUser: function() {
      var self = this,
        value = '',
        dropdown = document.getElementsByClassName('newOrder-dropdown')[0],
        userValue = JSON.parse(JSON.stringify(self.user));

      self.tUser ? (value = JSON.parse(JSON.stringify(self.tUser))) : undefined;

      self.userList.forEach(item => {
        if (item.uid == self.user.uid) {
          this.addUser(item);
        }
      });

      if (self.newOrderList.length) {
        self.$Modal.confirm({
          title: '修改客户',
          content: '<p>修改客户之后将清空已加入的商品列表</p>',
          onOk: function() {
            dropdown.scrollTop = 0;
            self.userDropDown = false;
            self.newOrderList = [];
            self.postList = [];
            self.tUser = userValue;
            self.focusGoodsInput();
          },
          onCancel: function() {
            self.user = value;
          }
        });
      } else {
        self.tUser = userValue;
        dropdown.scrollTop = 0;
        self.userDropDown = false;
      }
    },
    // 按enter之后跳转到订购数的输入框
    enterCommodity: function() {
      // 获取订购数的输入框
      var self = this,
        dropdown = document.getElementsByClassName('newOrder-dropdown')[1],
        value = self.commodity;

      if (
        value.default_item &&
        value.default_item.name &&
        value.name != value.default_item.name
      ) {
        self.commodity.name = value.default_item.name;
      }

      self.commodityList.forEach(item => {
        if (item.id == self.commodity.id) {
          self.addCommodity(item);
        }
      });

      var isSelected = self.commodityList.filter(function(item) {
        if (item.commodity_id === value.cid && item.is_selected) {
          return true;
        }
      });
      if (isSelected.length) {
        self.autoFocus(value);
      } else {
        this.focusAmountInput();
      }
      dropdown.scrollTop = 0;
      self.commodityDropDown = false;
    },
    // 订购数量单可以直接加入商品到列表
    enterAmount: function() {
      let self = this;
      // 传1代表是enter加入商品
      window.ez.debounce(self.addCommodityToList(1), 1000);
    },
    createSceneOrder: function() {
      if (!this.checkData()) {
        const warningRow = this.newOrderList.find(item => item.amount_warning || item.purchase_price_warning || item.purchase_sub_price_warning);
        if (warningRow) {
          this.$smessage({
            type: 'error',
            text: `商品${warningRow.name}${warningRow.amount_warning || warningRow.purchase_price_warning || warningRow.purchase_sub_price_warning}`
          })
        }
        return false;
      }
      this.goodsPromot = true;
    },
    resetError() {
      this.error = {
        user: '',
        delivery_date: '',
        goods: '',
        agent_provider: ''
      };
    },
    checkGoods(goods) {
      let valid = true;
      if (!goods.amount || Number(goods.amount) === 0) {
        goods.amount_warning = '请输入订购数量';
        valid = false;
      }
      if (+this.selectPurchaseType !== 4 && (!goods.purchase_price || Number(goods.purchase_price) === 0)) {
        goods.purchase_price_warning = '请输入采购单价';
        valid = false;
      }
      if (+this.selectPurchaseType !== 4 && (!goods.purchase_sub_price || Number(goods.purchase_sub_price) === 0)) {
        goods.purchase_sub_price_warning = '请输入采购小计';
        valid = false;
      }
      return valid;
    },
    checkData() {
      let valid = true;
      let firstErrorGoodsIndex = null;

      if (!this.postList || this.postList.length === 0) {
        valid = false;
        this.error.goods = '请添加商品和数量';
      }

      // 检查订单标签设置
      if (!this.checkTag()) {
        valid = false;
      }

      // 检查订单商品订购数量
      this.postList.map((item, index) => {
        if (!this.checkGoods(item)) {
          valid = false;
          this.newOrderList.splice(index, 1, this.deepClone(item));
          if (firstErrorGoodsIndex === null) {
            firstErrorGoodsIndex = index;
          }
        } else {
          this.newOrderList[index] = item;
        }
      });
      this.syncList(false);
      // 开启订单商品标签必填
      if(this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        for (const [index, good] of this.postList.entries()) {
          if(!good.order_commodity_tag) {
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`)
            // const curElement = this.$refs.orderTable.$el.querySelector(`.ivu-table-body .ivu-table-row:nth-child(${index+1})`)
            // curElement.scrollIntoView()
            return false
          }
        }
      }
      let baseDataValid = this.checkBaseData({
        scrollToError: true
      });

      if (baseDataValid && firstErrorGoodsIndex !== null) {
        this.$nextTick(() => {
          let $firstRow = this.$refs.orderTable.$el.querySelector(
            `.ivu-table-body .ivu-table-row:nth-child(${firstErrorGoodsIndex})`
          );
          if ($firstRow) {
            $firstRow.scrollIntoView();
          }
        });
      }
      return valid && baseDataValid;
    },
    /**
     * @param {scrollToError} options
     * @returns {boolean}
     */
    checkBaseData(options = {}) {
      let { scrollToError } = options;
      let valid = true;
      let firstErrorDom = null;

      if (!this.user.id) {
        valid = false;
        this.error.user = '请选择客户';
        firstErrorDom = this.$refs['error.user'].$el;
      } else {
        this.error.user = '';
      }
      if (!this.date) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.delivery_date = '请选择发货日期';
      } else {
        this.error.delivery_date = '';
      }
      if (!this.selectedId && +this.selectPurchaseType !== 4) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.agent_provider = '请选择采购员/供应商';
      } else {
        this.error.agent_provider = '';
      }
      if(this.isOrderCommodityTagRequired && !this.tagId) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.tag_id'].$el);
        this.error.tag_id = '请选择订单商品标签';
      } else {
        this.error.tag_id = '';
      }
      if (firstErrorDom && scrollToError) {
        firstErrorDom.scrollIntoView();
      }
      return valid;
    },
    save() {
      // 转换中国时区
      let date = new Date(this.date.valueOf() + 28800000);
      date = date.toISOString().slice(0, 10);
      var self = this,
        userId = this.user.id,
        commodityList = this.postList,
        remarks = this.remarks,
        purchase_agent_id = '',
        purchase_provider_id = '';
      if (this.selectPurchaseType == 1) {
        purchase_agent_id = this.selectedId;
      } else {
        purchase_provider_id = this.selectedId;
      }
      //self.createDisabled = true;
      commodityList.forEach(e => {
        e.order_unit_price = e.price;
      });
      commodityList = commodityList.map(item => {
        const configItem = {};
        if (this.isEnableUserContractPriceDiscountRatio) {
          configItem.order_protocol_discount = item.order_protocol_discount;
        }
        return {
					...configItem,
          commodity_id: item.id,
          num: item.amount * 1,
          purchase_unit_price: +this.selectPurchaseType === 4? 0 : item.purchase_price,
          order_unit_price: item.price,
          remark: item.remark || '',
          order_sub_price: item.total,
          purchase_sub_price: +this.selectPurchaseType === 4? 0 : item.purchase_sub_price,
          order_commodity_tag: item.order_commodity_tag,
          order_protocol_org_price: item.order_protocol_org_price || 0,
					order_protocol_discount: item.order_protocol_discount || 0,
        };
      });
      let params = {
        user_id: userId,
        tag_id: this.tagId,
        commodity_string: JSON.stringify(commodityList),
        delivery_date: date,
        remark: remarks,
        purchase_agent_id: +this.selectPurchaseType === 4? 0 : purchase_agent_id,
        purchase_provider_id: +this.selectPurchaseType === 4? 0 : purchase_provider_id,
        provider_supervisor: this.selectedSupervisorId || 0,
        token: window.ez.getCookie('token'),
        update_in_price: this.formData.update_in_price,
        tag_ids: JSON.stringify(this.selectedOrderTag),
        order_files_link: this.orderFiles.map(item=>item.url).join(','),
        purchase_files_link: this.purchaseFiles.map(item=>item.url).join(','),
				purchase_type: this.selectPurchaseType
      };
      if (this.returnSaving) {
        return;
      }
      this.returnSaving = true;
      common
        .saveSceneOrder(params)
        .then(function(res) {
          self.returnSaving = false;
          self.createDisabled = false;
          if (res.status) {
            self.resetError();
            self.successNotice('保存现场采购单成功');
            self.newOrderList = [];
            self.postList = [];
            self.newOrderReviewActive = false;
            self.user = { id: '', uid: '', name: '', detail: '' };
            self.commodity = {
              id: '',
              cid: '',
              name: '',
              amount: '',
              detail: '',
              purchase_price: ''
            };
            self.remarks = '';
            self.$store.state.isOrderClose = '';

            self.router.push({
              path: 'order'
            });
          } else {
            self.errorNotice({
              title: '订单创建失败',
              desc: res.message
            });
          }
        })
        .catch(err => {
          self.createDisabled = false;
          console.log(err);
        });
    },
    activeUsualOrder: function() {
      this.usualOrderActive = !this.usualOrderActive;
    },
    cancel() {
      let self = this;
      // self.isClose = 1;
      self.router.push({
        path: 'order'
      });
      return true;
      // eslint-disable-next-line no-unreachable
      // self.$Modal.confirm({
      //   title: '确定',
      //   content: '<p>系统可能不会保存您所做的更改,确定返回列表页面?</p>',
      //   onOk() {
      //     self.isClose = 1;
      //     self.router.push({
      //       path: 'order'
      //     });
      //   },
      //   onCancel: () => {}
      // });
    },
    activeNewOrderReview(value) {
      if (value === 'close') {
        this.newOrderList = this.newOrderList.reverse();
        this.postList = this.cloneObj(this.newOrderList);
      }

      this.newOrderReviewActive = false;
    },
    delCommodity: function(value) {
      var self = this,
        data = value.row;

      this.$Modal.confirm({
        title: '确定',
        content: '<p>确定删除商品</p>',
        async onOk() {
          self.newOrderList = self.cloneObj(self.postList);
          var arr = JSON.parse(JSON.stringify(self.newOrderList));
          arr.splice(data._index, 1);
          self.newOrderList = arr;
          self.postList = self.cloneObj(self.newOrderList);
          self.successNotice(data.name + '已删除');
        }
      });
    },
    // 导出订单
    useExportOrder: function(value) {
      var id = value.id;

      location.href = '/superAdmin/orderSuper/AjaxExport?order_id=' + id;
    },
    // 已加入列表的商品自动聚焦
    autoFocus: function(value) {
      var self = this,
        amount = document.querySelectorAll('.amount'),
        cid = '';

      cid = value.commodity_id ? value.commodity_id : value.cid;
      for (var i = 0, length = self.newOrderList.length; i < length; i++) {
        if (self.newOrderList[i].id === cid) {
          location.href = '#' + cid; // 通过锚点定位到商品
          amount[i].getElementsByTagName('input')[0].focus();
          amount[i].getElementsByTagName('input')[0].select();
          self.getAmount(i);
        }
      }
      self.commodity.cid = self.commodity.name = '';
    },
  },
  components: {
    orderTag,
    // history,
    // userHandMade,
    // eslint-disable-next-line vue/no-unused-components
    NumberInput,
    usual,
    mvSelect,
    // review,
    GoodsListModal,
    DetailPage,
    AttachmentUpload,
    EditableTable,
    SBlock,
    SText,
    SIcon,
    NumberInput
  }
};

window.onload = function() {};

window.onresize = function() {
  getWidth();
};
function getWidth() {
  var app = document.getElementsByClassName('new-order')[0],
    feature = document.getElementsByClassName('newOrder-feature')[0];

  feature ? (feature.style.width = app.clientWidth + 'px') : undefined;
}
</script>

<style lang="less" scoped>
/deep/.ivu-table-wrapper {
  .required-tip .ivu-select-selection {
    border-color: rgb(243, 51, 51);
  }
}

/deep/ .add-goods input {
  border-bottom-left-radius: 2px !important;
  border-top-left-radius: 2px !important;
}

/deep/ .add-goods .ivu-input-group-append {
  background-color: #fff !important;
  border: 1px solid #d8d8d8;
  line-height: 28px;
  cursor: pointer;
  &:hover {
    border-color: var(--primary-color) !important;
  }
}

/deep/ .attach-upload__file-list--file-name {
  line-height: normal;
}

/deep/ .attach-upload__add__help {
  margin-bottom: 0 !important;
}

/deep/ .attach-upload__file-list {
  margin-top: 0;
  &.empty {
    display: none;
  }
}



.new-order {
  .ivu-input-wrapper, .ivu-date-picker, .ivu-select {
    width: 232px;
  }
  .batch-add-goods:hover {
    .add-goods-icon {
      color: var(--primary-color);
    }
  }
  .add-goods-icon {
    font-size: 16px;
    color: #909090;
    cursor: pointer;
  }
  .mb16 {
    margin-bottom: 16px !important;
  }
  .sum-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    color: #303030;
    text-align: right;
    height: 19px;
    .label {
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
    .value{
      color: #FF6E00;
      font-weight: bold;
      font-family: AvenirNext, AvenirNext;
    }
  }
}

.goods-promot-mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(55, 55, 55, 0.6);
  height: 100%;
  z-index: 1000;
}
.ivu-form {
  text-align: left;
}

.newOrder {
  padding-bottom: 60px;
  background-color: #fff;
  .amount {
    .ivu-input {
      width: 100px;
    }
  }
}

.newOrder-title {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.newOrder-title-strong {
  margin-right: 5px;
  color: #03ac54;
}

.newOrder-customer {
  position: relative;
  border-bottom: 8px solid #f5f5f5;
  width: 100%;
  padding: 15px 10px;
}

.newOrder-customer .ivu-form-item {
  display: inline-block;
  margin-bottom: 0;
}

.newOrder-customer .ivu-input {
  width: 300px;
}

.newOrder-customer .ivu-icon-arrow-down-b {
  right: 50px;
}

.newOrder-customer .ivu-form .ivu-form-item-label {
  text-align: left;
  font-size: 14px;
}

.newOrder-address {
  width: 100%;
  padding: 10px;
  text-align: left;
  border-bottom: 8px solid #f5f5f5;
}

.newOrder-address > span {
  margin-right: 10px;
}

.newOrder-address strong:last-child {
  color: #03ac54;
}

.newOrder-address strong:last-child i {
  margin-right: 5px;
}

.newOrder-address strong:last-child:hover {
  cursor: pointer;
}

.newOrder-operation {
  float: right;
  margin-right: 10px;
  vertical-align: middle;
  font-size: 14px;
  color: #03ac54;
}

.newOrder-operation span:not(:last-child) {
  margin-right: 10px;
}

.newOrder-operation span:hover {
  cursor: pointer;
}

.newOrder-lists {
  width: 100%;
  background-color: #fff;
}

.newOrder-lists input {
  text-align: center;
}

.newOrder-lists img {
  max-width: 50px;
  height: auto;
}

.newOrder-amount {
  padding: 10px;
  text-align: right;
  font-size: 14px;
  color: #ed3f14;
}

.newOrder-remarks {
  margin-top: 10px;
  width: 100%;
  height: 50px;
  padding: 0 10px;
  border-bottom: 8px solid #f5f5f5;
  .ivu-form-item {
    width: 100%;
  }
}

.newOrder-other {
  padding: 10px;
  font-size: 14px;
}

.newOrder-other strong {
  color: #03ac54;
}

.newOrder-other strong:hover {
  cursor: pointer;
}

.newOrder-other i {
  margin-right: 5px;
  vertical-align: middle;
  font-size: 18px;
}

.newOrder-other span {
  font-size: 12px;
  color: #bbbec4;
}

.newOrder-feature {
  position: fixed;
  z-index: 2;
  bottom: 0;
  padding: 15px;
  text-align: left;
  border-top: 1px solid #eee;
  background-color: #fff;
}

.newOrder-feature button {
  margin-right: 5px;
}

.newOrder-feature > span {
  color: #03ac54;
  margin-right: 5px;
}

.newOrder-feature span:hover {
  cursor: pointer;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
  .slide-fade-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */

 {
  transform: translateX(100px);
  opacity: 0;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
  .fade-leave-to
    /* .fade-leave-active below version 2.1.8 */

 {
  opacity: 0;
}

.dropdown-fade-enter-active {
  transition: all 0.3s ease;
}

.dropdown-fade-leave-active {
  transition: all 0.2s ease;
}

.dropdown-fade-enter,
  .dropdown-fade-leave-to
    /* .slide-fade-leave-active for below version 2.1.8 */

 {
  transform: translateY(-20px);
  opacity: 0;
}

.ivu-upload {
  display: inline-block;
}

.newOrder-dropdown,
.newOrder-dropdown-content {
  position: absolute;
  z-index: 199;
  left: 0;
  top: 30px;
  margin-top: 5px;
  width: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  max-height: 200px !important;
  box-shadow: 1px 2px 1px #ccc;
  border-radius: 2px;
}

.newOrder-dropdown .active {
  p {
    color: #03ac54;
  }
  color: #03ac54;
  background-color: #ebf7ff;
}

.newOrder-dropdown .selected {
  color: inherit !important; // color: #c3cbd6;
  /* background-color: #c3cbd6; */
}

.newOrder-dropdown .selected:hover {
  // color: #c3cbd6;
  /* background-color: #c3cbd6; */
}

.dropdown-items {
  width: 100%;
  height: 50px !important;
  padding: 5px 10px !important;
}

.dropdown-items:hover {
  cursor: pointer;
  background-color: #ebf7ff;
  p {
    color: #03ac54;
  }
  color: #03ac54;
}

// .dropdown-items:first-child{
//   cursor: pointer;
//   background-color: #EBF7FF;
//   p { color: #03ac54; }
//   color: #03ac54;
// }
.dropdown-items-strong {
  font-weight: 500;
  font-size: 13px;
  margin-right: 10px;
  display: inline-block;
  max-width: 220px;
  height: 18px;
  line-height: 18px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdown-items-span {
  font-size: 12px;
  vertical-align: top;
  color: #aaa;
}

.dropdown-items-p {
  height: 20px;
  line-height: 20px;
  font-size: 13px;
  color: #aaa;
}

.ivu-icon-trash-a:hover {
  cursor: pointer;
  color: rgba(237, 63, 20, 1) !important;
}

.del-commodity:hover {
  cursor: pointer;
  color: rgba(237, 63, 20, 1) !important;
}

.commodity-name {
  display: inline-block !important;
}



.ivu-date-picker {
  .ivu-input {
    &:hover {
      cursor: pointer;
    }
  }
}

.newOrder {
  .ivu-form-item {
    .ivu-form-item-content {
      padding-right: 50px !important;
    }
  }
}

.dropdown-empty {
  padding: 10px;
  color: #80848f;
  text-align: center;
}
</style>
<style lang="scss">
.ivu-input {
  font-size: 13px;
}
.new-logo {
  display: inline-block !important;
  border-radius: 2px;
      margin-right: 2px;
    padding: 0 5px;
    color: #fff;
    background-color: #39f;
    border-radius: 2px;
}
/* goods promot style */
.goods-promot-mask {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(55, 55, 55, 0.6);
  height: 100%;
  z-index: 1000;
}
.goods-promot-layout {
  position: fixed;
  overflow: auto;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.goods-promot-wrap {
  margin: 0 auto;
  position: relative;
  outline: 0;
  top: 100px;
  display: flex;
  display: -webkit-flex;
  justify-content: center;
  -webkit-justify-content: center;
}
.goods-promot-content {
  position: relative;
  width: 1000px;
  min-height: 200px;
  background-color: #fff;
  border: 0;
  border-radius: 6px;
  background-clip: padding-box;
  h3 {
    padding: 10px;
    text-align: left;
  }
  p {
    padding: 5px 10px;
    text-align: left;
  }
  button {
    float: right;
    margin: 20px 10px;
  }
}
.actual-refund-wrap {
  display: flex;
  // justify-content: center;
  align-items: center;
  .ivu-input-wrapper {
    margin-right: 10px;
    width: 60%;
  }
}
.newOrder-remarks .ivu-form-item {
  width: 85%;
}
.tag-price {
  font-weight: bolder;
  color: red;
  border: 1px solid;
  border-radius: 4px;
  padding: 0 5px;
  display: inline-block;
  font-size: 12px;
  margin-left: 5px;
  text-align: center;
}

.new-order-main {
  position: relative;
  border-bottom: 8px solid #f5f5f5;
  width: 100%;
  padding: 15px 10px;
}
</style>
