<!--
 * @Author: ddcoder
 * @Date: 2022-10-03 18:11:44
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-31 10:21:26
 * @Description: 订单核算 -- 新版版
-->
<template>
  <div
    class="order-detail order-detail-approval"
    :class="{
      disabled:
        consortium_pricing_type == 2 &&
        orderInfo.order_type == 4 &&
        orderInfo.consortium_both_order == 1,
    }"
  >
    <DetailPage
      pageType="view"
      title="订单核算"
      :disabledSave="saveButtonDisabled"
    >
      <s-block title="基础信息" class="base-info__detail">
        <Form inline label-colon :label-width="70" :disabled="false">
          <FormItem label="客户">{{ orderInfo.shop_name }}</FormItem>
          <FormItem label="客户业态" v-if="sysConfig.tc_platform == 1">{{ orderInfo.user_business_type_desc }}</FormItem>
          <FormItem label="发货日期">
            <Date-picker
              @on-change="$_onChangeDate"
              type="date"
              placeholder="选择日期"
              :value="orderInfo.delivery_date"
              format="yyyy-MM-dd"
              :editable="false"
              style="width: 232px"
            ></Date-picker>
          </FormItem>
          <FormItem label="送货时间段">
            <Select style="width: 232px" v-model="orderInfo.delivery_time">
              <Option
                v-for="item in deliveryTimeList"
                :value="item.id"
                :key="item.id"
                >{{ item.timeDu }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="订单号">{{ orderInfo.order_no }}</FormItem>
          <FormItem label="提交时间">{{ orderInfo.create_time }}</FormItem>
          <FormItem label="付款状态"
            >{{ orderInfo.is_pay_text }}({{ orderInfo.pay_way_text }})</FormItem
          >
          <FormItem label="订单状态">{{ orderInfo.mode_text }}</FormItem>
          <FormItem label="配送方式">{{
            orderInfo.delivery_method_text
          }}</FormItem>
          <FormItem label="收货信息">
            <SText
              :text="
                (orderInfo.receive_name || '') +
                (orderInfo.receive_tel || '') +
                (orderInfo.address_detail || '')
              "
            />
          </FormItem>
          <FormItem label="回单状态">
            <Col>
              <RadioGroup v-model="orderInfo.is_receipt">
                <Radio :label="0">未回单</Radio>
                <Radio :label="1">已回单</Radio>
              </RadioGroup>
            </Col>
          </FormItem>
          <FormItem label="子账号">
            <Col>{{orderInfo.sub_user_name || '-'}}</Col>
          </FormItem>
          <FormItem label="物流单号" v-if="isOpenExpress100">
            <Input
              style="width: 232px"
              maxlength="32"
              v-model="orderInfo.express_no"
            />
          </FormItem>
          <FormItem
            class="order-tag"
            v-if="orderTagList && orderTagList.length > 0"
            label="订单标签"
            style="width: 100%"
          >
            <orderTag 
              v-model="orderInfo.tag"
              @on-change="_checkTagLengs"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return (sysConfig.is_open_order_tag_only_one === '1' &&
                  orderInfo.tag.length > 0 &&
                  !orderInfo.tag.includes(item.id))
              }"
            >
            </orderTag>
          </FormItem>
          <FormItem label="外部系统编号" v-if="isOpenTraceabilityPlatform4 || sysConfig.open_openapi == 1">
            {{orderInfo.third_party_order_no || '-'}}
          </FormItem>
          <FormItem
            :label="item.name"
            :key="item.key"
            v-for="item in orderCustomizeField"
          >
            <Input style="width: 256px" v-model="item.value" />
          </FormItem>
          <div v-if="orderInfo.special_remark">
            <FormItem>
              <template #label>
                <span style="color: #f33333">特殊备注:</span>
              </template>
              <div style="white-space: pre">{{ orderInfo.special_remark }}</div>
            </FormItem>
          </div>
        </Form>
      </s-block>
      <base-block
        title="商品清单"
        class="base-info__detail"
        style="margin-top: 28px"
      >
        <SFullscreen @change="updateTableHeight">
          <Row :gutter="10" type="flex" align="middle" slot="action-left">
            <Col>
              <GoodsSort
                @sort="sort"
                :data="sortData"
                :userId="orderInfo.user_id"
              />
            </Col>
            <Col>
              <SearchInputCommodity
                :label="false"
                :originData="commodityList"
                @toScroll="toScroll"
              />
            </Col>
          </Row>
          <SVxeEditableTable
            :drag="true"
            :loading="loading"
            style="margin-top: -16px"
            id="editableTable"
            ref="editableTable"
            :data="commodityList"
            :columns="orderInfo.id ? columns : []"
            :isShowRecordEditor="!orderInfo.consortium_forbid_change_item_num"
            :enterAddRow="!orderInfo.consortium_forbid_change_item_num"
            :scroll-y="{ mode: 'wheel', gt: 100, oSize: 5 }"
            :scroll-x="{ mode: 'wheel', gt: 30, oSize: 5 }"
            :max-height="tableHeight"
            :row-config="{ useKey: true, isCurrent: true }"
            :recordEditorConfig="{
              deleteConfirm: '确认删除此商品？',
            }"
            :defaultRowData="
              () => ({
                editable: 1,
                isAlter: false,
                amount: '',
                order_commodity_tag: undefined,
              })
            "
            :sort-config="{
              trigger: 'cell',
              orders: ['desc', 'asc', null],
            }"
            :isActiveColumn="true"
            @sort-change="handleSortChange"
            @on-delete="onDelete"
            @on-insert="onInsert"
            @on-cols-change="handleColsChange"
            @on-row-drag="onRowDrag"
            rowKey="id"
            :disableAdd="orderInfo.o_lock_status == 2 || orderInfo.consortium_forbid_change_item_num"
            key="order/approval"
          >
            <div
              slot="after-table-right"
              style="display: flex; align-items: center; height: 20px"
            >
              <p class="order-detail-amount">
                <span class="order-detail__price"
                  >下单金额：<span>¥{{ totalOrderPrice }}</span></span
                >
                <span class="order-detail__price"
                  >发货金额：<span>¥{{ deliveryAmount }}</span></span
                >
                <span
                  v-show="totalReferenceProfitShow"
                  class="order-detail__price"
                  >参考毛利总额：<span>¥{{ totalReferenceProfit }}</span></span
                >
                <span
                  class="order-detail__price"
                  v-if="orderInfo.show_freight_price === 'Y'"
                  >运费：¥<NumberInput
                    class="ml3"
                    :min="0"
                    :max="999999999.99"
                    :precision="2"
                    size="small"
                    v-model="orderInfo.freight_price"
                    style="width: 80px; height: 26px; display: inline-block"
                  ></NumberInput
                ></span>
                <span
                  class="order-detail__price"
                  v-show="orderInfo.show_delivery_price === 'Y'"
                  >发货金额：¥{{ orderInfo.delivery_price }}</span
                >
                <span
                  class="order-detail__price"
                  v-show="orderInfo.show_should_price === 'Y'"
                  >应付金额：¥{{ orderInfo.should_price }}</span
                >
                <span
                  class="order-detail__price"
                  v-show="orderInfo.show_pay_price === 'Y'"
                  >实付金额：¥{{ orderInfo.pay_price }}</span
                >
                <span
                  class="order-detail__price"
                  v-show="orderInfo.show_return_price === 'Y'"
                  >退款金额：¥{{ orderInfo.return_price }}</span
                >
                <span
                  class="order-detail__price fl"
                  v-show="orderInfo.show_compensation_price === 'Y'"
                  >退回余额：¥{{ orderInfo.compensation_price }}</span
                >
                <Tooltip
                  placement="right"
                  style="float: left"
                  v-show="orderInfo.show_compensation_price === 'Y'"
                >
                  <Icon style="cursor: pointer" type="ios-help-circle"></Icon>
                  <div slot="content">
                    <p>
                      客户下单时支付金额:￥<strong>{{
                        orderInfo.pay_price
                      }}</strong>
                    </p>
                    <p>
                      分拣发货金额:￥<strong>{{
                        orderInfo.delivery_price
                      }}</strong
                      >,差异金额:￥<strong>{{
                        orderInfo.compensation_price
                      }}</strong>
                    </p>
                    <p>已经退回到客户的余额账户</p>
                  </div>
                </Tooltip>
                <span
                  class="order-detail__price fl"
                  v-show="orderInfo.show_supply_price === 'Y'"
                  >余额欠款：¥{{ orderInfo.supply_price }}</span
                >
                <Tooltip
                  placement="right"
                  style="float: left"
                  v-show="orderInfo.show_supply_price === 'Y'"
                >
                  <Icon style="cursor: pointer" type="ios-help-circle"></Icon>
                  <div slot="content">
                    <p>
                      客户下单时支付金额:￥<strong>{{
                        orderInfo.pay_price
                      }}</strong>
                    </p>
                    <p>
                      分拣发货金额:￥<strong>{{
                        orderInfo.delivery_price
                      }}</strong
                      >,差异金额:￥<strong>{{ orderInfo.supply_price }}</strong>
                    </p>
                    <p>已经从客户的余额账户中扣款</p>
                  </div>
                </Tooltip>
              </p>
              <p
                v-if="
                  consortium_pricing_type == 2 &&
                  orderInfo.order_type == 4 &&
                  orderInfo.consortium_both_order == 1
                "
              >
                仅支持修改下单单价
              </p>
            </div>
          </SVxeEditableTable>
        </SFullscreen>
      </base-block>
      <base-block
        title="其他信息"
        class="base-info__detail"
        style="margin-top: 40px"
      >
        <Form :label-width="100">
          <FormItem label="订单备注:">
            <Input
              type="textarea"
              :maxlength="512"
              show-word-limit
              placeholder="输入订单备注"
              style="width: 418px"
              v-model="orderInfo.remark"
            />
          </FormItem>
          <FormItem
            label="客户签名图片:"
            v-if="orderInfo.signature_pic"
            style="width: 100%"
          >
            <ThumbnailList :images="orderInfo.signature_pic ? [orderInfo.signature_pic] : []" />
          </FormItem>
          <FormItem
            label="货物送达图片:"
            v-if="orderInfo.delivery_pic.length"
            style="width: 100%"
          >
            <ThumbnailList :images="orderInfo.delivery_pic" />
          </FormItem>
        </Form>
      </base-block>
      <base-block
        title="订单流水"
        class="base-info__detail"
        style="margin-top: 26px"
      >
        <Table
          class="table-scroll-x"
          out-border
          :columns="orderInfoColumns"
          :data="orderInfoData"
        ></Table>
      </base-block>
      <template #button-after>
        <div :data-order_id="orderId" style="display: inherit">
          <Button
            v-if="
              !isHasSyncContractPriceRoot || !isEnableOrderSyncContractPrice
            "
            :disabled="saveButtonDisabled"
            type="success"
            @click="approval"
            >一键保存</Button
          >
          <Button @click="useExportOrder">导出</Button>
          <template
            v-if="isEnableOrderSyncContractPrice && isHasSyncContractPriceRoot"
          >
            <Button @click="syncPrice" type="primary" ghost>
              刷价
              <Tooltip placement="top" maxWidth="200" :content="brushTips">
                <SIcon
                  icon="help"
                  :size="12"
                  style="cursor: pointer; margin-top: -2px"
                />
              </Tooltip>
            </Button>
          </template>
          <template
            v-if="isEnableOrderSyncContractPrice && isHasSyncContractPriceRoot"
          >
            <Button
              type="success"
              :disabled="saveButtonDisabled"
              @click="approval"
              >仅保存本单</Button
            >
            <Button
              type="success"
              :disabled="saveButtonDisabled"
              @click="approvalAndSync"
            >
              保存并同步协议单
              <template v-if="is_open_order_add_same_commodity">
                <Tooltip
                  placement="top"
                  maxWidth="200"
                  content="订单中若存在重复商品下单单价、折扣率（订）、协议市场价（订）不一致时，该商品不会同步到协议单中。"
                >
                  <SIcon
                    icon="help"
                    :size="12"
                    style="cursor: pointer; margin-top: -2px"
                  />
                </Tooltip>
              </template>
            </Button>
          </template>
        </div>
      </template>
    </DetailPage>
    <SModal
      title="分拣记录（单位：斤）"
      ref="sModal"
      :width="450"
      @ok="saveModalData"
      okTxt="保存"
      :btns="2"
      @on-cancel="modalClose"
    >
      <Table
        :columns="modal.columns"
        :data="modal.data"
        outer-border
        style="margin: 20px 0"
      ></Table>
    </SModal>
    <AgreementPriceSelectModal
      :show="agreementPriceSelectModal.show"
      :tableData="agreementPriceSelectModal.data"
      @confirm="agreementPriceSelectModal.confirm"
      @cancel="agreementPriceSelectModal.cancel"
    >
    </AgreementPriceSelectModal>
    <ActionRemindModal ref="actionRemind" @on-ok="handleOk" @on-cancel="handleCancel"></ActionRemindModal>
  </div>
</template>
<script>
import StepPricingPoptip from './components/StepPricingPoptip.vue';
import NumberInput from '@/components/basic/NumberInput';
import SIcon from '@components/icon';
import common from '@api/order.js';
import Bus from '@api/bus.js';
import Goods from '@api/goods.js';
import AddGoods from './components/add-goods';
import ConfigMixin from '@/mixins/config';
import Table from '@/components/table';
import { SModal } from '@sdp/ui';
import intro from '@mixins/intro';
import { uniqueId, cloneDeep } from 'lodash-es';
import DetailPage from '@/components/detail-page/index.js';
import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
import CommoditySelect from '@/components/common/CommoditySelectTable';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue';
import SButton from '@components/button';
import { getEditTableHeight } from '@/util/common';
import Icon from 'view-design/src/components/icon';
import AgreementPriceSelectModal from './components/agreementPrice-select-modal/index.vue';
import SearchInputCommodity from './components/SearchInputCommodity/index.vue';
import CalcPrice from './mixins/calcContractPrice';
const CONTRACT_PRICE_KEY = 'price';
import StorageUtil from '@util/storage.js';
const discountRatioKey = 'discount';
const marketPriceKey = 'org_price';
const agreementPriceKey = 'unit_price';
import authority from '@/util/authority.js';
import ThumbnailList from '@components/thumbnail-list';
import SFullscreen from '@/components/s-fullscreen';
import SText from '@/components/s-text';
import SBlock from '@/components/s-block';
import GoodsSort from './components/GoodsSort.vue';
import ActionRemindModal from './components/ActionRemindModal';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'
import SdpTableStaticFormWrap from '@/components/standard/sdp-table-static-form-wrap';

const { hasAuthority } = authority;

export default {
  name: 'order-detail',
  mixins: [ConfigMixin, intro, CalcPrice],
  components: {
    SdpTableStaticFormWrap,
    SBlock,
    SText,
    SFullscreen,
    SVxeEditableTable,
    NumberInput,
    AddGoods,
    DetailPage,
    Table,
    SModal,
    SIcon,
    SButton,
    Icon,
    AgreementPriceSelectModal,
    SearchInputCommodity,
    ThumbnailList,
    GoodsSort,
    ActionRemindModal,
    orderTag,
  },
  data() {
    return {
      totalReferenceProfitShow: false,
      authority,
      dataCopy: {}, // 备份远程数据，用于在最后提交时做数据比对
      is_brush_price: 0,
      agreementPriceSelectModal: {
        show: false,
        data: [],
        confirm: (selectedId) => {
          let extraParams = {
            sync_protocol: 1,
            sync_protocol_id: selectedId,
          };
          this.approval(extraParams);
          this.agreementPriceSelectModal.show = false;
        },
        cancel: () => {
          this.agreementPriceSelectModal.show = false;
        },
      },
      modal: {
        columns: [
          {
            title: '分配量',
            key: 'allot',
          },
          {
            title: '分拣量',
            key: 'sorting',
            render: (h, params) => {
              let row = params.row;
              let index = params.index;
              let key = 'sorting';
              return h(NumberInput, {
                props: {
                  disabled: Goods.isCreditGoods(row),
                  value: Number(row[key]),
                  min: 0,
                  precision: 2,
                },
                on: {
                  'on-focus': (event) => {
                    event.target.select();
                  },
                  'on-change': (val) => {
                    row[key] = val;
                    this.modal.data[index][key] = val;
                  },
                },
              });
            },
          },
          {
            title: '来源',
            key: 'name',
            index: 0,
          },
        ],
        data: [],
      },
      saveButtonDisabled: true,
      loading: false,
      orderCustomizeField: [], // 订单自定义字段
      goodsCustomizeField: [], // 订单明细自定义字段
      showContractPriceTips: false,
      syncContractPrice: false, // 是否同步协议价
      deliveryTimeList: [],
      orderTagList: [],
      orderInfo: {}, // 订单详情
      originalColumns: [],
      commodityList: [],
      serverCommodityList: [],
      originalOrderCommodity: [],
      orderInfoColumns: [
        {
          title: '时间',
          key: 'create_time',
        },
        {
          title: '金额',
          key: 'price',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      current: 0,
      orderInfoData: [],
      orderId: this.$route.query.id,
      splitProviderId: '',
      orderGoodsTagList: [],
      extraParams: {},
      columns: [],
    };
  },
  activated() {
    this.changeAgreementPriceKey(agreementPriceKey);
    this.saveButtonDisabled = false;
    if (this.init) {
      this.getOrderDetail();
      this._getDeliveryTimeList();
      this.getOrderCustomizeFieldKeys();
      this.getOrderGoodsTagList();
    }
  },
  deactivated() {
    console.log('组件失活了');
  },
  created() {
    this.updateTableHeight();
    this.changeAgreementPriceKey(agreementPriceKey);
    this.getOrderDetail();
    this._getDeliveryTimeList();
    this.getOrderCustomizeFieldKeys();
    this.getOrderGoodsTagList();
    this.getColumns();
    this.init = true;
  },
  computed: {
    // 小计修改计算模式
    totalPriceUpdateMode() {
      return +this.sysConfig.total_price_update_mode;
    },
    isGoodsNewestPrice() {
      return +this.sysConfig.batch_brush_order_price_type === 0;
    },
    brushTips() {
      return this.isGoodsNewestPrice
        ? '会刷新订单商品下单单价为最新价格（协议价>客户类型价>市场价）'
        : '会刷新订单商品下单单价为当前客户生效的协议价，没有生效的协议价则不进行刷价';
    },
    deliveryAmount() {
      let prices = 0;
      let commodityList = this.commodityList;
      if (this.$refs.editableTable) {
        const { fullData } = this.$refs.editableTable.getTableData();
        commodityList = fullData;
      }
      commodityList.forEach((item) => {
        prices += +item.delivery_price || 0;
      });
      return prices.toFixed(2);
    },
    totalReferenceProfit() {
      let commodityList = this.commodityList;
      if (this.$refs.editableTable) {
        const { fullData } = this.$refs.editableTable.getTableData();
        commodityList = fullData;
      }
      let allTotal = 0;
      commodityList.forEach((item) => {
        // 参考毛利=下单单价-参考成本价
        const profit = (item.unit_price - item.in_price).toFixed(2);
        // 参考毛利总额=下单数量*参考毛利
        const total = Number(
          +(Number.isNaN(profit) ? '0' : profit) * +item.order_amount,
        ).toFixed(2);
        allTotal = Number(
          allTotal + +(Number.isNaN(total) ? '0' : total),
        ).toFixed(2);
      });
      return allTotal;
    },
    totalOrderPrice() {
      let commodityList = this.commodityList;
      if (this.$refs.editableTable) {
        const { fullData } = this.$refs.editableTable.getTableData();
        commodityList = fullData;
      }
      let price = commodityList.reduce(
        (total, current) => total + (+current.sub_total_price || 0),
        0,
      );
      return price.toFixed(2);
    },
    isShowSyncContractPrice() {
      return this.isEnableOrderSyncContractPrice;
    },
    isHasSyncContractPriceRoot() {
      return this.authority.hasAuthority('A002001010');
    },
  },
  methods: {
    getColumns() {
      const editRenderObj = this.isOpenSubtotalModify ? { editRender: { autofocus: 'input', autoselect: true } } : {};

      this.columns = [
        {
          width: 40,
          minWidth: 40,
          style: {
            paddingLeft: 0,
          },
          type: 'titleCfg',
          titleType: 'order_approval',
        },
        {
          key: 'index',
          fixed: 'left',
          title: '序号',
          // type: 'index',
          resizable: false,
          width: 49,
          align: 'center',
          render: function (h, params) {
            if (params.isHidden) return null;
            const template = [];
            // 预售商品
            if (Number(params.row.date_type) > 0) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 20,
                  },
                  style: {
                    color: 'var(--primary-color)',
                    marginRight: '5px',
                  },
                }),
              );
            }
            template.push(h('span', params.index + 1));
            return template;
          },
        },
        {
          type: 'img',
          title: '商品图片',
          key: 'logo',
          width: 80,
          align: 'left',
          fixed: 'left',
          render: (h, params) => {
            const { row, isHidden } = params;
            if (isHidden) return null;
            return h(
              'div',
              { class: 'demo-upload-list', style: { border: 'none' } },
              [
                <img
                  style="width: 36px;"
                  src={
                    (row.logo ||
                      'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png') +
                    '!40x40'
                  }
                />,
                h(
                  'div',
                  {
                    class: 'demo-upload-list-cover',
                  },
                  [
                    h(
                      'Icon',
                      {
                        props: {
                          type: 'ios-eye-outline',
                        },
                        on: {
                          click: () => this.previewImage(row.logo + '!400x400'),
                        },
                      },
                      [],
                    ),
                  ],
                ),
              ],
            );
          },
          style: {
            padding: '0 10px',
          },
        },
        {
          title: '商品名称',
          minWidth: 266,
          fixed: 'left',
          key: 'name',
          sortable: true,
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            if (params.isHidden) return null;
            let row = params.row;
            let activityIcon = Goods.getActivityIcon(row);
            let newLogoStyle = {
              marginRight: '2px',
              padding: '0 5px',
              color: '#fff',
              backgroundColor: '#3399ff',
              borderRadius: '2px',
            };
            const inputProps = {
              type: 'textarea',
              rows: 1,
              autosize: { minRows: 1, maxRows: 2.2 }
            }
            return (
              <div style="display: flex; align-items: center">
                {(row.new && <span style={newLogoStyle}>新</span>) ||
                  (row.activity_type_desc && (
                    <img
                      style="height: 18px; padding-right: 10px"
                      src={activityIcon}
                    >
                      {row.activity_type_desc}
                    </img>
                  )) ||
                  (row.unit !== row.unit_sell && (
                    <SIcon
                      icon="duoguige"
                      style={{ color: '#f13130', marginRight: '4px' }}
                      size="12"
                    ></SIcon>
                  ))}
                <div class="commodity-select">
                  <SdpTableStaticFormWrap
                    displayValue={{
                      value: row.name,
                      placeholder: '商品名/编码/别名/关键字',
                      disabled: this.orderInfo.consortium_forbid_change_item_num,
                      ...inputProps
                    }}
                    scopedSlots={{
                      default: ({
                        handleUpdateActiveStatus,
                        handleDeactivate,
                      }) => (
                        <CommoditySelect
                          disabled={this.orderInfo.consortium_forbid_change_item_num}
                          commodityName={row.name}
                          params={{
                            user_id: this.orderInfo.user_id,
                            delivery_date: this.orderInfo.delivery_date,
                            is_online: this.createOrderShowOfflineGoods ? '' : 'Y',
                            provider_id: this.splitProviderId,
                            order_id: this.orderInfo.id,
                            pageSize: 30,
                            query_setting: this.excludeName(),
                          }}
                          dataProvider={common.getCommodity}
                          inputProps={inputProps}
                          commodityIdKey="commodity_id"
                          commodityNameKey="commodity_name"
                          onOn-show-dropdown={(show, createGoodsModal) => {
                            if (!createGoodsModal) {
                              const flag = show ? 2 : 1;
                              handleUpdateActiveStatus(flag);
                              if (flag === 1) {
                                handleDeactivate();
                              }
                            }
                          }}
                          onOn-change={(cid, com) => {
                            row.name = com.origin_commodity_name;
                            this._addSingleGoods(com, params)
                          }}
                          onCreateGood={(newGood) => {
                            row.name = newGood.origin_commodity_name;
                            this._addSingleGoods(newGood, params)
                          }}
                          nativeOnKeydown={(e) => {
                            // 单独处理回车交互，解决中文输入法下回车也会跳行的问题
                            if (e.code.includes('Enter')) {
                              e.stopPropagation();
                            }
                          }}
                          onOn-enter={() => {
                            this.$refs.editableTable.insertOneAt(row);
                          }}
                          selectType="table"
                          slot-type="order-is_online"
                          isOpenCreated={true}
                        ></CommoditySelect>
                      ),
                    }}
                  ></SdpTableStaticFormWrap>
                </div>
              </div>
            );
          },
        },
        {
          title: '描述',
          minWidth: 120,
          key: 'summary',
        },
        {
          title: '现有库存',
          key: 'stock',
          width: 100,
        },
        {
          title: '单位',
          key: 'unit',
          width: 80,
        },
        {
          title: '下单数量',
          width: 150,
          key: 'order_amount',
          sortable: true,
          style: {
            paddingRight: 0,
          },
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let { row } = params;
            const dataKey = 'order_amount';
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0;
            this.commodityList.forEach((item) => {
              if (row.commodity_id === item.commodity_id) {
                otherAmount += Number(item.order_amount);
              }
            });
            return h('div', {}, [
              h(
                'div',
                {
                  style: {
                    display: 'flex',
                    margin: '5px 0',
                    alignItems: 'center',
                  },
                },
                [
                  h(NumberInput, {
                    props: {
                      disabled:
                        Goods.isCreditGoods(row) ||
                        this.orderInfo.o_lock_status == 2 ||
                        row.oc_lock_status == 2,
                      value: Number(row[dataKey]),
                      min: 0,
                      max: 999999999.99,
                      precision: 2,
                    },
                    class: [+row[dataKey] === 0 && 'emphasis-tip'],
                    style: {
                      width: '80px',
                      flexShrink: 0,
                      display: 'inline-block',
                    },
                    on: {
                      'on-focus': (event) => {
                        event.target.select();
                        params.row.price_warning = '';
                      },
                      'on-change': (val) => {
                        if (!val) {
                          val = 0;
                        }
                        row[dataKey] = val;
                        this._updateStepPricing(row);
                        this.computeTotalPrice(params);
                        this.handleChangeNum(params.row, params.index);
                      },
                    },
                  }),
                  row._isNewGoods && this.isStepPricingGoods(row) ? (
                    <StepPricingPoptip goods={row}></StepPricingPoptip>
                  ) : null,
                ],
              ),
              h(
                'div',
                {
                  style: {
                    color: '#f13130',
                    fontSize: '12px',
                    marginTop: '-5px',
                  },
                  class: { dn: +row[dataKey] !== 0 },
                },
                '请注意特殊下单数量！',
              ),
              h(
                'div',
                {
                  class: {
                    dn:
                      !row.is_sell_stock_alert ||
                      otherAmount <= Number(row.sell_stock),
                  },
                  style: {
                    color: 'red',
                  },
                },
                '库存不足',
              ),
            ]);
          },
        },
        {
          title: '下单单价',
          width: 160,
          key: 'unit_price',
          className: 'column__unit-price',
          editRender: { autofocus: 'input', autoselect: true },
          sortable: true,
          render: (h, params) => {
            let key = 'unit_price';
            let data = params.row,
              me = this;
            const showWaring =
              +params.row.in_price <= 0 && +params.row[key] === 0;
            const waringTip = h(
              'p',
              {
                style: {
                  color: '#f13130',
                  fontSize: '12px',
                  marginTop: '-5px',
                },
              },
              '请注意特殊下单单价！',
            );
            let template = [
              h(NumberInput, {
                props: {
                  disabled: Goods.isCreditGoods(data),
                  value: Number(data.unit_price),
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                class: {
                  'emphasis-tip': showWaring,
                },
                style: {
                  width: '80px',
                  display: 'inline-block',
                  padding: '5px 0',
                },
                on: {
                  'on-focus': (event) => {
                    event.target.select();
                    params.row.price_warning = '';
                  },
                  'on-change'(val) {
                    // 后端做数据校验计算使用
                    data.input_key = 'unit_price';
                    data.unit_price = val;
                    if (!val) {
                      val = data.old_unit_price;
                    }
                    me.computeUserContractPrice(params.row, agreementPriceKey);
                    me.computeTotalPrice(params);
                    me.handleChangeNum(params.row, params.index);
                  },
                  'on-enter'() {
                    me.approvalSingle(data, params.index);
                  },
                },
              }),
            ];
            if (!data.editable) {
              let protocol = h(SIcon, {
                style: 'margin-left: 9px',
                props: {
                  icon: 'xie',
                  size: 16,
                },
              });
              const { desc } = data.price_type || {};
              if (desc === '~协') {
                protocol = h(SIcon, {
                  props: { icon: 'xieyijiabeigai', size: 16 },
                  style: 'margin-left: 9px; color: #ff6600;',
                });
              }
              template.push(protocol);
            }
            if (Goods.isDiscountGoods(params.row)) {
              const discountIcon = h(SIcon, {
                props: { icon: 'zhe', size: 16 },
                style: 'margin-right: 5px; color: #ff6600;',
              });
              template.unshift(discountIcon);
            }
            if (params.row.in_price - params.row[key] > 0) {
              template.push(
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                      fontSize: '12px',
                      marginTop: '-5px',
                    },
                  },
                  '低于最近一次进货价！',
                ),
              );
            }
            if (showWaring) {
              template.push(waringTip);
            }
            template = h('div', template);
            if (params.row.price_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'top',
                    content: '请输入下单单价',
                    always: true,
                    transfer: true,
                  },
                },
                [template],
              );
            }
            return template;
          },
        },
        {
          title: '下单小计',
          width: 100,
          sortable: true,
          key: 'sub_total_price',
          ...editRenderObj,
          render: (h, params) => {
            const key = 'sub_total_price';
            // 如果开启了小计支持修改，可以输入
            if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
              return h(NumberInput, {
                props: {
                  value: params.row[key],
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                on: {
                  'on-change': (val) => {
                    params.row[key] = val;
                    this.computeOfSubtotalModify(params, false);
                  },
                  'on-blur': (event) => {
                    this.computeOfSubtotalModify(params, false, true);
                  },
                },
              });
            }

            return h('span', '¥' + params.row[key]);
          },
        },
        {
          title: '发货数量',
          sortable: true,
          sortBy: 'amount',
          width: 165,
          key: 'actual_amount',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let me = this;
            let { row, index } = params;
            let key = 'amount';
            return h('div', [
              h(NumberInput, {
                props: {
                  disabled: Goods.isCreditGoods(row),
                  value: Number(row[key]),
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                style: {
                  width: '80px',
                  display: 'inline-block',
                },
                on: {
                  'on-focus': (event) => {
                    event.target.select();
                    if (
                      this.isOpenProviderSortingPurchaseTask &&
                      row.is_show_sort_window > 0
                    ) {
                      this.modal.data = this.deepClone(
                        row.purchase_task_sort_list || [],
                      );
                      this.modal.index = index;
                      this.$refs.sModal.open();
                    }
                  },
                  'on-change'(val) {
                    row[key] = val;
                    me.computeTotalPrice(params);
                    me.computeSortingActualDiffAmount(params);
                  },
                  'on-enter'() {
                    me.approvalSingle(row, params.index);
                  },
                },
              }),
              h(
                'span',
                {
                  class:
                    +row.is_sorting === 1 ? 'status-close' : 'status-error',
                  style: {
                    'margin-left': '5px',
                    'vertical-align': 'middle',
                  },
                },
                +row.is_sorting === 1 ? '已分拣' : '未分拣',
              ),
            ]);
          },
        },
        {
          title: '发货单位',
          key: 'unit_sell',
          width: 120,
        },
        {
          title: '发货数量(原)',
          sortable: true,
          key: 'convert_actual_amount',
          width: 140,
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let me = this;
            let { row } = params;
            let key = 'convert_actual_amount';
            if (row.unit_convert === 'Y' && Number(row.unit_num) - 1 !== 0) {
              row[key] = (row.amount / row.unit_num).toFixed(2);
            } else {
              row[key] = row.amount;
            }
            return h('div', [
              h(NumberInput, {
                props: {
                  value: row[key],
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                style: {
                  marginRight: '5px',
                  width: '100px',
                  display: 'inline-block',
                },
                on: {
                  'on-change'(val) {
                    row[key] = val;
                    me.computeAmount(params);
                  },
                },
              }),
              h('span', row.unit),
            ]);
          },
        },
        {
          title: '发货金额',
          key: 'delivery_price',
          sortable: true,
          width: 120,
          ...editRenderObj,
          render: (h, params) => {
            let row = params.row;
            let key = 'delivery_price';
            let price = 0;
            if (row.unit_convert === 'Y') {
              price += (+row.amount).mul(row.unit_price).div(row.unit_num);
            } else {
              price += (+row.amount).mul(row.unit_price);
            }

            price = price.toFixed(2);

            // 如果开启了小计支持修改，可以输入
            if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
              return h(NumberInput, {
                props: {
                  value: row[key],
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                on: {
                  'on-change': (value) => {
                    params.row[key] = value;
                    this.computeOfSubtotalModify(params, true);
                  },
                  'on-blur': (event) => {
                    this.computeOfSubtotalModify(params, true, true);
                  },
                },
              });
            }
            return h('span', price);
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          minWidth: 150,
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let me = this;
            return h('i-input', {
              props: {
                value: data.remark,
              },
              nativeOn: {
                change: ($event) => {
                  let value = $event.target.value;
                  params.row.remark = value;
                },
              },
            });
          },
        },
        {
          title: '条形码',
          key: 'barcode',
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 140,
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '联营供应商',
          minWidth: 100,
          key: 'provider_pool_name',
        },
        {
          title: '采购单号',
          key: 'pur_no',
          render: (h, params) => {
            const { row } = params;
            if (!row.pur_no) return '--';
            return h('div', [
              h(
                'a',
                {
                  class: 'tableLink',
                  attrs: {
                    // 表明同一个标签同一个域会共享会话
                    rel: 'opener',
                    target: '_blank',
                    href: `#/purchase/detail?keep_scroll=1&id=${row.purchase_id}&pur_no=${row.pur_no}`,
                  },
                },
                row.pur_no,
              ),
            ]);
          },
        },
        {
          title: '实收状态',
          key: 'un_confirm_receive',
          render: (h, params) => {
            let row = params.row;
            return h(
              'span',
              {},
              +row.un_confirm_receive === 1 ? '待实收' : '已实收',
            );
          },
        },
        {
          width: 120,
          title: '折扣率(订)%',
          sortable: true,
          key: 'discount',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let key = 'discount';
            return h(NumberInput, {
              props: {
                value: params.row[key],
              },
              on: {
                'on-change': (value) => {
                  // 后端做数据校验计算使用
                  params.row.input_key = 'discount';
                  params.row[key] = value;
                  this.computeUserContractPrice(params.row, discountRatioKey);
                  this.computeTotalPrice(params);
                },
              },
            });
          },
        },
        {
          width: 120,
          title: '协议市场价（订)',
          key: 'org_price',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let key = 'org_price';
            return h(NumberInput, {
              props: {
                value: params.row[key],
              },
              on: {
                'on-change': (value) => {
                  // 后端做数据校验计算使用
                  params.row.input_key = 'org_price';
                  params.row[key] = value;
                  this.computeUserContractPrice(params.row, marketPriceKey);
                  this.computeTotalPrice(params);
                },
              },
            });
          },
        },
        {
          width: 100,
          title: '折扣率',
          sortable: true,
          key: 'protocol_discount',
          render: (h, params) => {
            let key = 'protocol_discount';
            let style = {
              color: params.row[key] - params.row.discount === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          width: 120,
          title: '协议市场价',
          key: 'protocol_org_price',
          render: (h, params) => {
            let key = 'protocol_org_price';
            let style = {
              color: params.row[key] - params.row.org_price === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          width: 120,
          title: '协议价',
          key: 'protocol_price',
          render: (h, params) => {
            let key = CONTRACT_PRICE_KEY;
            let style = {
              color: params.row[key] - params.row.unit_price === 0 ? '' : 'red',
            };
            return h(
              'span',
              {
                style,
              },
              params.row[key],
            );
          },
        },
        {
          title: '订单商品标签',
          key: 'order_commodity_tag',
          show: () => this.isOpenOrderCommodityTag,
          editRender: { autofocus: '.ivu-select' },
          type: 'select',
          render: (h, { row, index }) => {
            const key = 'order_commodity_tag';
            const getDefaultTag = () => {
              let defaultOption = this.orderGoodsTagList.find(
                (item) => item.is_default === '1',
              );
              let tagId = defaultOption ? defaultOption.id : '';
              row[key] = tagId;
              return tagId;
            };
            return h(mvSelect, {
              attrs: {
                clearable: true,
                placeholder: '请选择',
                transfer: true,
              },
              class: {
                'required-tip':
                  row[key] === '' && this.isOrderCommodityTagRequired,
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal:
                  row.order_commodity_tag !== undefined
                    ? row.order_commodity_tag
                    : getDefaultTag(),
              },
              on: {
                'on-change': (tagId, item) => {
                  row[key] = tagId || '';
                },
                'on-open-change': (isOpen) => {
                  setTimeout(() => {
                    row.isSelectOpen = isOpen;
                  }, 100);
                },
                'on-enter-key-up': () => {
                  if (!row.isSelectOpen)
                    this.$refs.editableTable.insertOneAt(row);
                },
              },
              nativeOn: {
                keydown: (e) => {
                  if ('EnterArrowDownArrowUp'.includes(e.code)) {
                    e.stopPropagation();
                  }
                },
              },
            });
          },
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'center',
          width: 100,
        },
        {
          title: '分拣数量',
          key: 'sorting_amount',
        },
        {
          title: '发货差异',
          key: 'sorting_actual_diff_amount',
          render: (h, { row, index }) => {
            return h(
              'span',
              {
                style: {
                  color:
                    Number(row.sorting_actual_diff_amount) !== 0
                      ? '#FF0000'
                      : '#303030',
                },
              },
              row.sorting_actual_diff_amount,
            );
          },
        },
        {
          title: '商品重量(kg)',
          minWidth: 100,
          align: 'right',
          key: 'commodity_weight',
        },
        {
          title: '下单重量(kg)',
          minWidth: 100,
          align: 'right',
          key: 'commodity_order_weight',
        },
        {
          title: '发货重量(kg)',
          minWidth: 100,
          align: 'right',
          key: 'commodity_delivery_weight',
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          width: 120,
        },
        {
          title: '参考毛利',
          key: 'reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row.in_price
            ).toFixed(2);
            return h('span', Number.isNaN(profit) ? '0' : profit);
          },
        },
        {
          title: '参考毛利总额',
          key: 'total_reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.unit_price - params.row.in_price
            ).toFixed(2);
            // 参考毛利总额=下单数量*参考毛利
            const total = Number(
              +(Number.isNaN(profit) ? '0' : profit) * +params.row.order_amount,
            ).toFixed(2);
            return h('span', Number.isNaN(total) ? '0' : total);
          },
        },
        {
          title: '参考毛利率',
          key: 'reference_profit_rate',
          width: 100,
          render: (h, params) => {
            // 参考毛利率=参考毛利/下单单价*100%
            if (!+params.row.unit_price) return h('span', '0%');
            let profit = params.row.unit_price - params.row.in_price;
            let rate = ((profit / params.row.unit_price) * 100).toFixed(2);
            return h('span', rate + '%');
          },
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          width: 140,
        },
        {
          title: '实时进货价',
          key: 'curr_in_price',
          width: 120,
        },
        {
          title: '税率',
          sortable: true,
          sortBy: 'tax_rate',
          key: 'tax_rate_desc',
          editRender: { autofocus: 'input', autoselect: true },
          width: 130,
          render: (h, params) => {
            const { index, row } = params;
            const key = 'tax_rate';
            const rightTemp = h(SIcon, {
              style: {
                cursor: 'pointer',
              },
              class: {
                ml10: true,
              },
              props: {
                icon: 'edit',
                size: 14,
              },
              on: {
                click: () => {
                  row.isAlter = !row.isAlter;
                },
              },
            });
            let leftTemp = h('span', row.tax_rate || 0);
            if (row.isAlter) {
              leftTemp = h(NumberInput, {
                props: {
                  precision: 0,
                  min: 0,
                  max: 99,
                  placeholder: '',
                  value: row[key],
                },
                on: {
                  'on-change': (value) => {
                    row[key] = value;
                    const taxRatePrice = +row.tax_rate
                      ? (((row.order_amount * row.unit_price) /
                          (1 + row.tax_rate / 100)) *
                          Number(row.tax_rate)) /
                        100
                      : 0;
                    row.tax_rate_price = taxRatePrice.toFixed(2);
                  },
                  'on-enter': () => {
                    row.isAlter = !row.isAlter;
                  },
                },
              });
            }

            const template = h(
              'div',
              {
                style: {
                  display: 'flex',
                  'align-items': 'center',
                },
              },
              [leftTemp, rightTemp],
            );
            return template;
          },
        },
        {
          title: '税额',
          sortable: true,
          key: 'tax_rate_price',
          width: 100,
        },
        {
          title: '内部备注',
          width: 150,
          key: 'inner_remark',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let { row } = params;
            const dataKey = 'inner_remark';
            return h('i-input', {
              props: {
                value: row[dataKey],
              },
              nativeOn: {
                change: ($event) => {
                  let value = $event.target.value;
                  params.row[dataKey] = value;
                },
              },
            });
          },
        },
        {
          title: '服务费',
          key: 'service_charge',
          width: 70,
        },
        {
          title: '客户商品别名',
          width: 150,
          key: 'user_commodity_alias_name',
        },
        {
          title: '客户商品别名编码',
          key: 'user_commodity_alias_code',
          width: 100,
        },
        {
          title: '绑定农批市场',
          minWidth: 140,
          key: 'bind_wholesale_market',
        },
        {
          title: '加单数量',
          key: 'changes_num',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            const { row, index } = params;
            let key = 'changes_num';
            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.handleChangeNum(row, index);
                },
              },
            });
          },
        },
        {
          title: '加单金额',
          key: 'changes_price',
          minWidth: 140,
          align: 'right',
        },
        {
          title: '销售数量',
          key: 'sales_num',
          minWidth: 100,
        },
        {
          title: '销售单价',
          key: 'sales_unit_price',
          minWidth: 100,
          align: 'right',
        },
        {
          title: '销售金额',
          key: 'sales_price',
          align: 'right',
          minWidth: 140,
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 100,
        },
        {
          title: '分账供应商',
          key: 'account_provider_name',
        },
        {
          title: '分账比例',
          key: 'account_ratio',
          render: (h, { row }) => {
            return h(
              'span',
              Number(row.account_ratio) ? row.account_ratio + '%' : '-',
            );
          },
        },
        {
          title: '菜谱套餐',
          key: 'raw_recipe_package_names',
        },
        {
          title: '基准单位',
          key: 'standard_unit_name',
          show: () => this.is_open_standard_unit,
        },
        {
          title: '基准单位数量',
          key: 'standard_unit_sort_amount',
          show: () => this.is_open_standard_unit,
          render: (h, { row }) => {
            if (!row.standard_unit_name || !row.standard_unit_num) {
              return h('span', '--');
            }
            const amount = row.amount || 0;
            return h(
              'span',
              Number(
                (amount * (row.unit_convert === 'Y' ? 1 : row.unit_num)) /
                  row.standard_unit_num,
              ).tofixed(2),
            );
          },
        },
        {
          title: '商品状态',
          key: 'is_online',
          render: (h, { row }) => {
            return h(
              'span',
              row.is_online === 'Y' ? '上架' : '下架',
            );
          },
        },
        {
          title: '实时采购员/供应商',
          key: 'now_agent_provider',
          width: 160,
          tip: '实时读取当前客户对应商品 默认的采购员/供应商信息',
        },
      ]
    },
    handleColsChange(cols) {
      this.totalReferenceProfitShow = !!cols.includes('total_reference_profit');
    },
    hasAuthority,
    updateTableHeight(isFullscreen, screenHeight) {
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 44;
      }
      this.$nextTick(() => {
        const { fullData } = this.$refs.editableTable.getTableData();
        // 通过更新数据重新渲染表格
        this.$refs.editableTable &&
          this.$refs.editableTable.reloadData(fullData);
      });
    },
    excludeName() {
      let arr = ['commodity_code', 'bar_code'];
      let goodNameSearch = StorageUtil.getLocalStorage('goodNameSearch')
        ? StorageUtil.getLocalStorage('goodNameSearch')
        : ['commodity_code', 'bar_code'];
      goodNameSearch.forEach((res) => {
        let index = arr.findIndex((ee) => ee == res);
        if (index > -1) {
          arr.splice(index, 1);
        }
      });
      return {
        query_exclude: arr.join(','),
        only_bar_code: StorageUtil.getLocalStorage('isRightMatch')
          ? StorageUtil.getLocalStorage('isRightMatch')
          : false,
      };
    },
    previewImage(image, _images, _viewIndex = 0) {
      this.viewImage(image, _viewIndex);
    },
    toScroll(curRow) {
      console.log('toScroll-curRow', curRow);
      this.$refs.editableTable.scrollToRow(curRow);
      this.$refs.editableTable.setCurrentRow(curRow);
    },
    onRowDrag({ oldIndex, newIndex }) {
      let { visibleData } = this.$refs.editableTable.getTableData();
      const currRow = visibleData.splice(oldIndex, 1)[0];
      visibleData.splice(newIndex, 0, currRow);
      this.commodityList = visibleData;
    },
    sortData() {
      const { fullData } = this.$refs.editableTable.getTableData();
      return this.deepClone(fullData);
    },
    sort(goodsList) {
      this.commodityList = goodsList;
    },
    onInsert(_, insertIndex) {
      // const { fullData } = this.$refs.editableTable.getTableData();
      // 'https://base-image.sdongpo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg'
      // this.commodityList = cloneDeep(fullData);
      // this._focusNextInput(this.commodityList[insertIndex + 1], 'name')
    },
    onDelete(delRow) {
      this.syncCommodityList();
      const delIndex = this.commodityList.indexOf(delRow);
      if (delIndex !== -1) {
        this.commodityList.splice(delIndex, 1);
      }
      const { fullData } = this.$refs.editableTable.getTableData();
      if (!fullData || fullData.length === 0) {
        this.commodityList = [delRow];
        this.$smessage({
          type: 'error',
          text: '至少保留一条商品',
        });
      }
    },
    getEditTableHeight,
    saveModalData() {
      this.syncCommodityList();
      let sum = 0;
      this.modal.data.forEach((item) => {
        sum += +item.sorting || 0;
      });
      this.commodityList[this.modal.index].purchase_task_sort_list =
        this.modal.data;
      this.commodityList[this.modal.index].amount = sum;
    },
    modalClose() {
      this.$refs.sModal.close();
    },
    // 获取订单自定义字段
    getOrderCustomizeFieldKeys() {
      this.$request
        .get(this.apiUrl.customizeFieldKeys, {
          customize_type: '4',
        })
        .then(({ status, data, message }) => {
          if (status) {
            (data || []).map((item) => {
              // 订单自定义字段，需要可编辑
              if (+item.customize_type === 4 && this.isNewOrderInputVersion) {
                this.goodsCustomizeField.push(item);
                const existIndex = this.columns.findIndex(
                  (col) => col.key === item.key,
                );
                if (existIndex < 0) {
                  this.columns.push({
                    title: item.name,
                    width: 160,
                    key: item.key,
                    editRender: { autofocus: 'input', autoselect: true },
                    render: (h, params) => {
                      let row = params.row;
                      let index = params.index;
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        nativeOn: {
                          change: ($event) => {
                            let value = $event.target.value;
                            row[item.key] = value;
                          },
                        },
                      });
                    },
                  });
                }
              }
            });
          } else {
            this.$Message.error(message || '网络错误');
          }
        });
    },
    handleChangeNum(goods) {
      // 销售单价=销售金额/销售数量
      goods.sales_unit_price = (goods.sales_price / goods.sales_num).toFixed(2);
      // 下单小计 = 下单数量 * 单价
      goods.sub_total_price = (+goods.order_amount)
        .mul(+goods.unit_price)
        .toFixed(2);
      // 固定单价
      if (this.issue_order_data_contact_way) {
        if (!goods.is_sorting || goods.is_sorting === '0') {
          // 销售数量=下单数量+加单数量
          goods.sales_num = (
            Number(goods.order_amount) + Number(goods.changes_num || 0)
          ).toFixed(2);
          // 销售金额=销售单价*销售数量
          goods.sales_price = (+goods.sales_num)
            .mul(+goods.sales_unit_price)
            .toFixed(2);
          let sub_total_price = (+goods.order_amount)
            .mul(+goods.unit_price)
            .toFixed(4);
          // 加单金额=销售金额-下单金额
          goods.changes_price = (+goods.sales_price - sub_total_price).toFixed(
            2,
          );
        } else {
          // 销售数量=发货数量+加单数量
          goods.sales_num = (
            Number(goods.actual_amount) + Number(goods.changes_num || 0)
          ).toFixed(2);
          // 销售金额=销售单价*销售数量
          goods.sales_price = (+goods.sales_num)
            .mul(+goods.sales_unit_price)
            .toFixed(2);
          // 加单金额=销售金额-发货金额
          goods.changes_price = (
            +goods.sales_price - goods.actual_total_price
          ).toFixed(2);
        }
        return;
      }
      // 商品未分拣时
      if (!goods.is_sorting || goods.is_sorting === '0') {
        // 销售数量=下单数量+加单数量
        goods.sales_num = (
          Number(goods.order_amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=下单金额+加单金额
        goods.sales_price = (
          (+goods.order_amount).mul(+goods.unit_price) +
          Number(goods.changes_price)
        ).toFixed(2);
      } else {
        // 销售数量=发货数量+加单数量
        goods.sales_num = (
          Number(goods.actual_amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=发货金额+加单金额
        goods.sales_price = (
          Number(goods.actual_total_price) + Number(goods.changes_price)
        ).toFixed(2);
      }
    },
    /**
     * 检查商品是否已存在
     * @param {object} goods 商品信息
     */
    _isGoodsExists(goods) {
      const { visibleData } = this.$refs.editableTable.getTableData();
      return visibleData.find(
        (item) => Number(item.commodity_id) === Number(goods.commodity_id),
      );
    },
    /**
     * 添加单个商品
     */
    _addSingleGoods(addGoodsInfo, params) {
      const existRow = this._isGoodsExists(addGoodsInfo);
      console.log('existRow', existRow);
      if (!this.is_open_order_add_same_commodity && existRow) {
        this.$smessage({
          type: 'error',
          text: '您已经添加过该商品了',
        });
        this._focusNextInput(existRow);
        return;
      }
      // let extraParams = {
      //   order_id: this.orderInfo.id
      // }
      if (
        !this.userStylePriceIsZeroDefaultMarkPrice &&
        +addGoodsInfo.is_price_type === 2 &&
        +addGoodsInfo.price === 0
      ) {
        this.$Message.warning(`${addGoodsInfo.name}未设置客户类型价`);
      }
      params.row = this.formatRow(addGoodsInfo, params);
      requestAnimationFrame(() => {
        this._focusNextInput(params.row);
        this._updateStepPricingOrderAmountColumnWidth();
      });
      // common
      //   .getOrderCommodity(
      //     this.orderInfo.user_id,
      //     addGoodsInfo.commodity_id,
      //     this.orderInfo.delivery_date,
      //     extraParams
      //   )
      //   .then(res => {
      //     let { data, status, message } = res;
      //     if (status) {
      //       if (
      //         !this.userStylePriceIsZeroDefaultMarkPrice &&
      //         +data.is_price_type === 2 &&
      //         +data.price === 0
      //       ) {
      //         this.$Message.warning(`${data.name}未设置客户类型价`);
      //       }
      //       params.row = this.formatRow(addGoodsInfo, data, params)
      //       requestAnimationFrame(() => {
      //         this._focusNextInput(params.row)
      //         this._updateStepPricingOrderAmountColumnWidth()
      //       })
      //     } else {
      //       this.modalError(message || '商品不存在', 0);
      //     }
      //   })
    },
    formatRow(data, params) {
      data.unit_price = data.type_price;
      data.id = data.commodity_id + uniqueId('$unique-');
      data.commodity_id = data.commodity_id;
      data.order_amount = 1;
      data.sales_num = '';
      data.amount = 0;
      data.convert_actual_amount = 0;
      data.amount_warning = '';
      data.inner_remark = '';
      data.changes_num = 0;
      data.changes_price = 0;
      // 详情接口和新增单个商品查询接口 category_name 返回不一致的问题
      data.category_name1 = data.category_name;
      data.category_name2 = data.category2_name;
      data.category_name3 = data.category3_name;
      data.category_name =
        data.category_name1 +
        (data.category2_name ? '/' + data.category2_name : '') +
        (data.category3_name ? '/' + data.category3_name : '');
      data.sub_total_price = (+data.order_amount)
        .mul(+data.unit_price)
        .toFixed(2);
      data.delivery_price = (+data.amount).mul(+data.unit_price).toFixed(2);
      if (!data.sales_price)
        data.sales_price = (+data.order_amount)
          .mul(+data.unit_price)
          .toFixed(2);
      if (!data.sales_unit_price) data.sales_unit_price = data.unit_price;
      if (this.isShowSyncContractPrice) {
        data.org_price = data.org_price || '';
        data.discount = data.discount || 100;
        if (this.isEnableUserContractPriceDiscountRatio) {
          data.discount = data.discount || 100;
          if (!Number(data.org_price)) {
            this._computeMarketPrice(data);
          }
        }
      }
      data._isNewGoods = true;
      data.isAlter = false;
      if (data.is_price_type === 1) {
        data.editable = 0;
      } else {
        data.editable = 1;
      }
      this._updateStepPricing(data);
      this.handleChangeNum(data, 0);
      // 实际商品信息替换
      Object.assign(params.row, data);
      delete params.row._isNew;
      delete params.row.orderCommodity_id;
      return params.row;
    },
    _focusNextInput(row, columnKey = 'order_amount') {
      // 聚焦下单数量字段
      this.$refs.editableTable.setEditCell(row, columnKey);
    },
    /**
     * 选择发货日期
     */
    $_onChangeDate(date) {
      this.orderInfo.delivery_date = date;
    },
    /**
     * 获取送货时间数据
     */
    _getDeliveryTimeList() {
      common.getDeliveryTimeList({ pageSize: 9999 }).then((res) => {
        if (res.status) {
          this.deliveryTimeList = res.data.list.map((time) => {
            return {
              id: time.id,
              timeDu: time.name + ' ' + time.start_time + '-' + time.end_time,
            };
          });
        }
      });
    },
    /**
     * 获取订单标签列表
     */
    _getOrderTagList(user_id) {
      const params = {
        user_id,
      };
      common.qryOrderTagList(params).then((res) => {
        if (res.status) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = res.data;
          }
        }
      });
    },
    /**
     * 限制一个订单最多使用3个标签
     */
    _checkTagLengs() {
      if (this.orderTagList.length === 0) return true;

      if (this.orderInfo.tag.length > 3) {
        this.orderInfo.tag.pop(this.orderInfo.tag.length - 1);
        this.warningMessage('一个订单最多存在3个标签');
        return false;
      }

      if (this.sysConfig.is_open_order_tag_only_one === '1' && this.orderInfo.tag.length > 1) {
        this.orderInfo.tag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单只能选择一个标签');
        return false;
      }

      if (this.sysConfig.is_open_order_tag_required === '1' && this.orderInfo.tag.length === 0) {
        this.warningMessage('请至少选择一个订单标签');
        return false;
      }

      return true;
    },
    // 修改发货数量（原），同步变动发货数量
    computeAmount(params) {
      if (!params) {
        return;
      }
      params.row.amount = params.row.convert_actual_amount;
      if (
        params.row.unit_convert === 'Y' &&
        Number(params.row.unit_num) - 1 !== 0
      ) {
        params.row.amount =
          params.row.convert_actual_amount * params.row.unit_num;
      }
      this.computeTotalPrice(params);
    },
    /**
     * 刷价
     */
    syncPrice() {
      if (!hasAuthority('DDHSSJ1121')) {
        this.errorMessage('您的权限不足，无法进行当前操作！');
        return;
      }
      const refreshPrice = () => {
        this.syncCommodityList();
        this.is_brush_price = 1;
        this.commodityList.forEach((item) => {
          // 如果是按协议价, 非协议价的商品不刷价
          if (!this.isGoodsNewestPrice && item.new_price_type !== 1) {
            return;
          }
          item.unit_price = item[CONTRACT_PRICE_KEY] || '';
          // 原有商品 new_price_type 对应 新增商品里的 is_price_type 字段
          if (item._isNewGoods) {
            item.org_price = item.org_price || '';
            item.discount = item.discount || 100;
            if (item.is_price_type === 1) {
              item.editable = 0;
            } else {
              item.editable = 1;
            }
          } else {
            item.org_price = item.protocol_org_price || '';
            item.discount = item.protocol_discount || 100;
            if (item.new_price_type === 1) {
              item.editable = 0;
            } else {
              item.editable = 1;
            }
          }
          this.computeTotalPrice({ row: item });
        });
      };
      this.$smodal({
        title: '确认',
        text: '确定执行刷价？',
        type: 'warning',
        btns: 2,
        okTxt: '确定',
        quitTxt: '取消',
        onOk: () => {
          refreshPrice();
        },
      });
    },
    syncCommodityList() {
      const { visibleData } = this.$refs.editableTable.getTableData();
      this.commodityList = visibleData;
    },
    /**
     * @description 开启小计支持修改后的计算逻辑
     * @param {Object} params 计算参数
     * @param {Boolean} isDeliver 是否是发货小计
     * @param {Boolean} onBlur 是否是失焦事件
     */
    computeOfSubtotalModify(params, isDeliver = false, onBlur = false) {
      const totalKey = isDeliver ? 'delivery_price' : 'sub_total_price';
      const anotherTotalKey = isDeliver ? 'sub_total_price' : 'delivery_price';
      const amountKey = isDeliver ? 'amount' : 'order_amount';
      const anotherAmountKey = isDeliver ? 'order_amount' : 'amount';

      // 若单价或数量或小计其中一个数值为0，不论其他两个数值输入多少，均默认为0
      if (onBlur) {
        if (this.totalPriceUpdateMode === 1) {
          if (+params.row.unit_price === 0) {
            params.row[totalKey] = 0;
            params.row[amountKey] = 0;
          }
        } else {
          if (+params.row[amountKey] === 0) {
            this.$nextTick(() => {
              params.row[totalKey] = 0;
              params.row.unit_price = 0;
              params.row[anotherTotalKey] = (
                params.row[anotherAmountKey] * params.row.unit_price
              ).toFixed(2);
            });
          }
        }
        return;
      }
      // 如果是固定单价
      if (this.totalPriceUpdateMode === 1) {
        if (+params.row.unit_price === 0) return;
        params.row[amountKey] = (
          params.row[totalKey] / params.row.unit_price
        ).toFixed(2);
      } else {
        if (+params.row[amountKey] === 0) return;
        if (params.row.unit_convert === 'Y') {
          if (isDeliver) {
            // 修改发货金额时候， 需要考虑是否按基础单位分拣
            params.row.unit_price = (+params.row[totalKey])
              .div(params.row[amountKey])
              .mul(params.row.unit_num)
              .toFixed(2);
            params.row[anotherTotalKey] = (
              params.row[anotherAmountKey] * params.row.unit_price
            ).toFixed(2);
          } else {
            params.row.unit_price = (+params.row[totalKey])
              .div(params.row[amountKey])
              .toFixed(2);
            params.row[anotherTotalKey] = (
              params.row[anotherAmountKey] * params.row.unit_price
            )
              .div(params.row.unit_num)
              .toFixed(2);
          }
        } else {
          params.row.unit_price = (Number(params.row[totalKey])).div(params.row[amountKey]).toFixed(2);
          params.row[anotherTotalKey] = (
            params.row[anotherAmountKey] * params.row.unit_price
          ).toFixed(2);
        }
      }
      this.computeUserContractPrice(params.row, agreementPriceKey);
    },
    computeTotalPrice(params) {
      const { row } = params;
      if (params) {
        params.row.original_total_price = (
          params.row.amount * params.row.unit_price
        ).toFixed(2);
        params.row.sub_total_price = (+row.order_amount)
          .mul(+row.unit_price)
          .toFixed(2);
        // 计算发货小计
        let price = 0;
        if (row.unit_convert === 'Y') {
          price += (+row.amount).mul(row.unit_price).div(row.unit_num);
        } else {
          price += (+row.amount).mul(row.unit_price);
        }
        price = price.toFixed(2);
        params.row.delivery_price = price;
      }
    },
    computeSortingActualDiffAmount(params) {
      if (params) {
        params.row.sorting_actual_diff_amount = (
          params.row.amount - params.row.sorting_amount
        ).toFixed(2);
      }
    },
    handleSortChange(sortObj) {
      // 必须用 visibleData 才能取到排序后的数据
      const { visibleData } = this.$refs.editableTable.getTableData();
      this.commodityList = visibleData;
    },
    /**
     * @description 单个核算
     * <AUTHOR>
     * @param orderCommodityItem 订单商品项
     */
    async approvalSingle(orderCommodityItem, index) {
      if (!orderCommodityItem.orderCommodity_id) {
        return;
      }
      if (this.orderInfo.delivery_date === '') {
        this.errorNotice('请选择发货日期');
        return;
      }
      if (!orderCommodityItem.unit_price) {
        orderCommodityItem.unit_price = orderCommodityItem.old_unit_price;
        return;
      }
      let data = {
        sync_protocol: 0,
        is_receipt: this.orderInfo.is_receipt,
        order_id: this.orderInfo.id,
        delivery_date: this.orderInfo.delivery_date,
        delivery_time: this.orderInfo.delivery_time,
        commodity: [],
      };
      let commodity = this.formatApprovalData(orderCommodityItem, index);
      data.commodity.push(commodity);
      this._approval(data);
    },
    async _approval(params, goBack = false) {
      this.syncCommodityList();
      if (!params.commodity.length) {
        Bus.error('商品信息不能为空！');
        this.saveButtonDisabled = false;
        return;
      }
      if (this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        // 开启订单商品标签必填
        for (const good of this.commodityList.filter(
          (item) => item.commodity_id,
        )) {
          if (!good.order_commodity_tag) {
            console.log('commodity', good);
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`);
            this.$refs.editableTable.scrollToRow(good);
            // this.$refs.editableTable.setCurrentRow(good)
            this.saveButtonDisabled = false;
            return;
          }
        }
      }
      params.remark = this.orderInfo.remark;
      params.is_brush_price = this.is_brush_price;
      let res = await common.editApproval(params);
      if (res.status) {
        Bus.success('保存成功');
        if (goBack === true) {
          setTimeout(() => {
            this.saveButtonDisabled = false;
            this.returnToList();
          }, 300);
        } else {
          this.saveButtonDisabled = false;
        }
      } else {
        Bus.error(res.message);
        this.saveButtonDisabled = false;
      }
    },
    handleActionRemind(commodityList, del_items) {
      const change_ocs = [];
      this.serverCommodityList.forEach((item) => {
        const obj = commodityList.find((c) => item.id === c.id);
        if (obj && obj.order_amount != item.order_amount) {
          change_ocs.push({
            order_commodity_id: obj.orderCommodity_id,
            amount: obj.order_amount,
          });
        }
      });
      if (del_items.length || change_ocs.length) {
        this.submitting = false;
        this.$refs.actionRemind.open({
          order_id: this.orderId,
          del_oc_ids: del_items.join(','),
          change_ocs: JSON.stringify(change_ocs),
        });
        return;
      } else {
        this.approval(
          {
            ...this.extraParams,
            modalType: 'ActionRemind',
          },
          true,
        );
      }
    },
    handleOk() {
      this.approval(
        {
          ...this.extraParams,
          modalType: 'ActionRemind',
        },
        true,
      );
    },
    handleCancel(){
      this.saveButtonDisabled = false
    },
    approvalAndSync() {
      let extraParams = {
        sync_protocol: 1,
      };
      this.syncCommodityList();
      this.saveButtonDisabled = true;
      common
        .checkValidUserContractPriceOrder(
          this.orderInfo.user_id,
          this.orderInfo.delivery_date,
        )
        .then((res) => {
          let { data, status, message } = res;
          if (status) {
            if (!data || (Array.isArray(data) && data.length < 1)) {
              this.$Modal.confirm({
                title: '提示',
                render: (h) => {
                  return h('div', [
                    h(
                      'p',
                      {
                        style: {
                          marginTop: '15px',
                        },
                      },
                      '客户没有已生效的协议单，确定要继续操作吗？',
                    ),
                    h(
                      'p',
                      {
                        style: {
                          marginTop: '15px',
                          color: '#777',
                        },
                      },
                      '注意：因为系统中不存在协议单该客户已生效的协议单，继续操作，订单中的商品不会同步至协议单中！',
                    ),
                  ]);
                },
                onOk: () => {
                  extraParams.sync_protocol = 0;
                  this.approval(extraParams);
                },
              });
            } else {
              const notAgreementPriceGoods = this.commodityList.filter(
                (item) => !Goods.isProtocolGoods(item),
              );
              if (
                Array.isArray(data) &&
                data.length > 1 &&
                notAgreementPriceGoods.length > 0 &&
                this.isOpenSyncProtocolIsAddCommodity
              ) {
                let repeatGoods = []; // 非协议价商品 - 重复商品数组
                let sameRepeatGoods = []; // 下单单价、折扣率（订）和协议市场价（订）一样的商品数组
                for (
                  let index = 0;
                  index < notAgreementPriceGoods.length - 1;
                  index++
                ) {
                  const item = notAgreementPriceGoods[index];
                  for (
                    let index2 = index + 1;
                    index2 < notAgreementPriceGoods.length;
                    index2++
                  ) {
                    const item2 = notAgreementPriceGoods[index2];
                    if (
                      item.commodity_id === item2.commodity_id &&
                      repeatGoods.indexOf(item) === -1
                    ) {
                      repeatGoods.push(item);
                      repeatGoods.push(item2);
                      if (
                        +item.unit_price === +item2.unit_price &&
                        +item.org_price === +item2.org_price &&
                        +item.discount === +item2.discount &&
                        sameRepeatGoods.indexOf(item2) === -1
                      ) {
                        sameRepeatGoods.push(item2);
                      }
                    }
                  }
                }
                console.log(
                  'repeatGoods-notAgreementPriceGoods',
                  notAgreementPriceGoods,
                );
                console.log('repeatGoods', repeatGoods);
                console.log('repeatGoods-sameRepeatGoods', sameRepeatGoods);
                if (
                  (repeatGoods.length > 0 && sameRepeatGoods.length > 0) ||
                  repeatGoods.length < notAgreementPriceGoods.length
                ) {
                  this.agreementPriceSelectModal.show = true;
                  this.agreementPriceSelectModal.data = data;
                } else {
                  this.approval(extraParams);
                }
              } else {
                this.approval(extraParams);
              }
            }
          } else {
            this.$smessage({ type: 'error', text: message });
            this.approval(extraParams);
          }
        })
        .finally(() => {
          this.saveButtonDisabled = false;
        });
    },
    /**
     * @description 批量核算
     * <AUTHOR>
     */
    async approval(extraParams = {}, flag) {
      if (this.orderInfo.delivery_date === '') {
        this.errorNotice('请选择发货日期');
        return;
      }
      
      if (!this._checkTagLengs()) return

      this.saveButtonDisabled = true;
      extraParams = extraParams || {};
      this.syncCommodityList();
      const commodityList = this.commodityList;
      let data = {
        delivery_time: this.orderInfo.delivery_time,
        delivery_date: this.orderInfo.delivery_date,
        is_receipt: this.orderInfo.is_receipt,
        express_no: this.orderInfo.express_no,
        freight_price: this.orderInfo.freight_price
          ? this.orderInfo.freight_price
          : 0,
        orderId: this.orderInfo.id,
        order_id: this.orderInfo.id,
        commodity: [],
        del_items: this.serverCommodityList
          .filter(
            (item) =>
              !commodityList.some(
                (postItem) =>
                  postItem.id === item.id &&
                  postItem.orderCommodity_id === item.orderCommodity_id,
              ),
          )
          .map((item) => item.orderCommodity_id),
        tag_ids: JSON.stringify(this.orderInfo.tag),
        sync_protocol: extraParams.sync_protocol || 0,
        sync_protocol_id: extraParams.sync_protocol_id || '',
      };
      this.extraParams = extraParams;
      if (!extraParams.modalType) {
        this.handleActionRemind(commodityList, data.del_items);
        return;
      }
      await common.saveOrderTags(
        this.orderInfo.id,
        JSON.stringify(this.orderInfo.tag),
      );
      data.del_items = JSON.stringify(data.del_items);
      data.customize_fields = JSON.stringify(this.orderCustomizeField);
      if (!Array.isArray(commodityList)) {
        this.errorNotice('没有商品数据');
        return false;
      }
      commodityList
        .filter((item) => !!item.name)
        .forEach((element, index) => {
          let commodityItem = this.formatApprovalData(element, index);
          data.commodity.push(commodityItem);
        });
      // 把商品根据序号进行排序
      this._checkCommodity(flag)
        .then(() => {
          this._approval(data, true);
        })
        .catch((err) => {
          this.saveButtonDisabled = false;
          console.log(err);
        });
    },
    /**
     * @description: 判断已分拣的商品，数量是否发生了变化
     * @author: lizi
     */
    _checkCommodity(flag) {
      return new Promise((resolve, reject) => {
        let diffCommodity = [];
        this.syncCommodityList();
        this.originalOrderCommodity.forEach((commodity) => {
          // 对比旧商品列表与当前最新的商品列表，数量是否发生变化
          let modifiedCommodity = this.commodityList.find(
            (findCommodity) =>
              findCommodity.orderCommodity_id === commodity.orderCommodity_id,
          );

          // 商品是否已分拣
          if (+commodity.is_sorting === 1) {
            // 如果没有找到旧的商品，代表以及被删除
            if (!modifiedCommodity) {
              diffCommodity.push(commodity.name);
              return false;
            }
            // 商品数量发生变化
            if (commodity.order_amount - modifiedCommodity.order_amount !== 0) {
              diffCommodity.push(commodity.name);
              return false;
            }
          }
        });
        if (diffCommodity.length > 0 && !flag) {
          setTimeout(() => {
            this.$Modal.confirm({
              content: `商品：${diffCommodity.join('，')}已分拣，确定继续保存？`,
              onOk: () => {
                resolve();
              },
              onCancel: () => {
                reject();
              },
            });
          }, 500);
        } else {
          resolve();
        }
      });
    },
    /**
     * @description 格式化传递到后端的订单商品项
     * <AUTHOR>
     * @param orderCommodityItem 订单商品项
     */
    formatApprovalData(orderCommodityItem, index) {
      let formatData = {
        commodity_id: orderCommodityItem.id.replace(/\$unique-.*$/, ''),
        order_commodity_id: orderCommodityItem.orderCommodity_id || '',
        unit_price: orderCommodityItem.unit_price,
        amount: orderCommodityItem.order_amount,
        actual_amount: orderCommodityItem.amount,
        price: orderCommodityItem.unit_price,
        mutate_total_price: orderCommodityItem.sub_total_price,
        mutate_sub_total_price: orderCommodityItem.delivery_price,
        in_price: orderCommodityItem.in_price,
        org_price: orderCommodityItem.org_price,
        discount: orderCommodityItem.discount,
        remark: orderCommodityItem.remark,
        inner_remark: orderCommodityItem.inner_remark,
        sort_num: index,
        changes_num: orderCommodityItem.changes_num,
        changes_price: orderCommodityItem.changes_price,
        sales_unit_price: orderCommodityItem.sales_unit_price,
        order_commodity_tag: orderCommodityItem.order_commodity_tag,
        tax_rate: orderCommodityItem.tax_rate,
        input_key: orderCommodityItem.input_key,
      };
      // 如果开启了供应商分拣采购任务分配商品，收货数量需要判断是否有修改
      // 修改过了的收货数量才会提交给后端
      // dataCopy 是详情接口备份的原始值，用于判断是否有修改
      if (this.isOpenProviderSortingPurchaseTask) {
        let originOrderCommodityItem = this.dataCopy.commodityList.find(
          (item) => item.id === orderCommodityItem.id,
        );
        if (
          originOrderCommodityItem &&
          originOrderCommodityItem.purchase_task_sort_list
        ) {
          if (originOrderCommodityItem.purchase_task_sort_list.length === 1) {
            let item = originOrderCommodityItem.purchase_task_sort_list[0];
            formatData.purchase_taks_sorts = [
              {
                pti_id: item.pti_id,
                sorting: orderCommodityItem.amount,
              },
            ];
          } else {
            let purchase_taks_sorts = [];
            originOrderCommodityItem.purchase_task_sort_list.forEach(
              (item, index) => {
                // if (item.sorting !== orderCommodityItem.purchase_task_sort_list[index].sorting) {
                // 不管是否修改都要传给后端
                purchase_taks_sorts.push({
                  pti_id: item.pti_id,
                  sorting:
                    orderCommodityItem.purchase_task_sort_list[index].sorting,
                });
                // }
              },
            );
            formatData.purchase_taks_sorts = purchase_taks_sorts;
          }
        }
      }

      // 订单自定义字段
      formatData.customize_fields = this.goodsCustomizeField.map((field) => {
        return {
          key: field.key,
          name: field.name,
          value: orderCommodityItem[field.key],
        };
      });

      // 如果更改了发货数量，no_modify_sort_num = 0
      // 先从备源数据里找到本个商品信息
      if (+formatData.actual_amount !== +orderCommodityItem.actual_amount) {
        formatData.no_modify_sort_num = 0;
      } else {
        formatData.no_modify_sort_num = 1;
      }

      // 判断是否新增的商品, 是的话也设置为0
      if (orderCommodityItem._isNewGoods) {
        formatData.no_modify_sort_num = 0;
      }

      return formatData;
    },
    getOrderDetail: function () {
      var self = this,
        id = self.$route.query.id,
        orderNo = self.$route.query.orderNo;
      this.loading = true;
      common
        .getOrderDetail(id, orderNo, '1')
        .then(function (res) {
          if (res.status) {
            var data = res.data;
            data.commodityList.forEach((item, index) => {
              item.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
              // 发货数量（原）在编辑时不显示单位，用这个字段控制单位的显示
              //  接口把值弄反了，需要反过来。
              let discount = item.discount;
              let orgPrice = item.org_price;

              item.discount =
                item.protocol_discount === undefined
                  ? 100
                  : item.protocol_discount;
              item.org_price =
                item.protocol_org_price === undefined
                  ? 0
                  : item.protocol_org_price;

              item.protocol_discount = discount || 100;
              item.protocol_org_price = orgPrice || '';
              item.price_warning = '';
              item.sub_total_price = item.original_total_price;
              item.delivery_price = item.actual_total_price;
              item.isAlter = false;
              if (
                self.sysConfig.is_open_order_lock == 2 ||
                self.sysConfig.is_open_order_lock == 3
              ) {
                if (
                  res.data.order.o_lock_status == 1 ||
                  res.data.order.o_lock_status == 2 ||
                  item.oc_lock_status == 1 ||
                  item.oc_lock_status == 2
                ) {
                  item._disableDelete = true;
                }
              }
            });
            self.commodityList = data.commodityList;
            self.serverCommodityList = self.cloneObj(data.commodityList);
            // 记录原始商品列表数据，用于后续判断商品数量是否发生变更
            self.originalOrderCommodity = self.deepClone(data.commodityList);
            data.order.delivery_time = data.order.delivery_time_id;
            data.order.tag = data.order.tag ? data.order.tag.split(',') : [];
            self.orderInfo = data.order;
            self.orderInfoData = data.amountRecord;
            if (self.isNewOrderInputVersion) {
              self.orderCustomizeField = data.order.customize_fields;
            }
            self.dataCopy = self.deepClone(data);
            self._getOrderTagList(self.orderInfo.user_id);
            // if (self.isSplitOrderLockProvider && data.order.split_provider_name) self.splitProviderId = data.order.split_provider_id

            // @TODO 临时解决vxe-editable-table贯标自动聚焦时快捷键操作会失效问题
            self.$nextTick(() => {
              const editTableEl = self.$refs.editableTable.$el;
              const activeEl = document.activeElement;
              if (
                editTableEl &&
                activeEl &&
                editTableEl.contains(activeEl) &&
                activeEl.blur
              ) {
                document.activeElement.blur();
              }
            });
          } else {
            self.modalError(res.message);
          }
        })
        .finally(() => {
          self.saveButtonDisabled = false;
          this.loading = false;
        });
    },
    // 导出订单
    useExportOrder: function () {
      var self = this,
        id = self.$route.query.id;

      location.href = '/superAdmin/orderSuper/AjaxExport?order_id=' + id;
    },
    returnToList() {
      this.router.go(-1);
    },
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，输入下单数量改变时，需要动态计算阶梯定价的商品价格
     * !! 只对新增的商品生效
     * @param {Object} row 当前商品对象
     */
    _updateStepPricing(row) {
      const { order_amount: amount } = row;
      if (row._isNewGoods && this.isStepPricingGoods(row)) {
        // 仅对新增的商品生效阶梯定价
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true),
        );
        if (stepPriceItem) {
          row.unit_price = stepPriceItem.price;
        }
      }
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      // this.syncCommodityList()
      const { visibleData } = this.$refs.editableTable.getTableData();
      if (
        visibleData.some(
          (goods) => goods._isNewGoods && this.isStepPricingGoods(goods),
        )
      ) {
        const newWidth = 155;
        // 接口请求时间不一定，两个都更新就行
        const order_amount_column = this.columns.find(
          (item) => item.key === 'order_amount',
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
    getOrderGoodsTagList() {
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data;
      });
    },
  },
};
</script>

<style lang="less">
.order-detail-approval {
  /deep/.ivu-checkbox-wrapper {
    margin-right: 24px;
  }
  &.disabled {
    cursor: not-allowed;
    .sdp-detail-page__bd > *,
    .vxe-body--row {
      pointer-events: none;
    }
  }
  .column__unit-price,
  .vxe-table--main-wrapper {
    pointer-events: auto !important;
  }
}
.approval {
  .protocol {
    @pw: 20px;
    font-weight: bolder;
    color: red;
    border: 1px solid;
    border-radius: 50%;
    display: inline-block;
    width: @pw;
    height: @pw;
    line-height: @pw;
    font-size: 12px;
    margin-left: 5px;
    text-align: center;
  }
}
</style>
<style lang="less" scoped>
/deep/.demo-upload-list {
  //display: inline-block;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 2px;
  overflow: hidden;
  height: 36px;
  width: 36px;
  line-height: 36px;
  background: rgba(0, 0, 0, 0);
  position: relative;
  box-shadow: none;
  margin-right: 0px;
}

.demo-upload-list img {
  width: 100%;
  height: 100%;
}
/deep/.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}
/deep/.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  color: #fff;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
/deep/ .goods-form .ivu-form-item {
  margin-bottom: 14px !important;
}
/deep/ .goods-search-row {
  margin-bottom: 18px;
  .ivu-form-item {
    margin-bottom: 0 !important;
  }
}
/deep/ .s-vxe-table {
  .emphasis-tip input {
    color: #f13130;
    border-color: currentColor;
  }
  .required-tip .ivu-select-selection {
    border-color: rgb(243, 51, 51);
  }
}
/deep/ .vxe-body--column .vxe-cell {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: normal;
  overflow-wrap: break-word;
  overflow: hidden;
  text-align: inherit;
  -webkit-line-clamp: 2;
}
.special {
  display: flex;
  margin-bottom: 10px;
  font-size: 13px;
  span {
    display: inline-block;
    box-sizing: border-box;
    padding-right: 12px;
    width: 120px;
    text-align: right;
  }
}
.commodity-table {
  position: relative;
  .custom-column {
    position: absolute;
    top: -44px;
    right: 0;
  }
  /deep/ .column__unit-price .ivu-table-cell {
    padding-right: 8px;
  }
}
.order-info {
  margin-top: 15px;
  /deep/ .ivu-col {
    margin-bottom: 15px;
  }
}

.order-detail {
  background-color: #fff;
}

.order-detail-nav {
  /* display: table; */
  border-bottom: 1px solid rgba(228, 228, 228, 1);
  text-align: left;
  background-color: #fff;
}

.order-detail-nav ul li {
  display: inline-block;
}
.order-detail-nav button {
  float: right;
}

.nav-items {
  display: inline-block;
  margin-right: 25px;
  /* width: 125px; */
  height: 36px;
  line-height: 36px;
  text-align: center;
  color: #999;
}

.nav-items:hover {
  cursor: pointer;
}
.order-detail-nav .active {
  color: #03ac54;
  border-bottom: 2px solid #03ac54;
}

.order-detail-info {
  padding: 20px 20px 10px 20px;
}
.order-detail-info .ivu-steps {
  margin-left: 60px;
  width: 900px;
}
.order-detail-info .ivu-steps .ivu-steps-tail > i {
  height: 2px;
}
.order-detail-info .ivu-steps-status-process .ivu-steps-title {
  color: #03ac54;
}
.order-detail-info .ivu-steps .ivu-steps-title {
  margin-top: 10px;
  margin-left: -5px;
  display: block;
}

.table-button {
  color: #03ac54;
  background-color: #fff !important;
  border: none;
}

.ivu-table-row-hover .table-button {
  background-color: transparent !important;
}

.table-button:hover {
  background-color: transparent !important;
  color: var(--primary-color);
}

.ivu-page {
  margin: 10px;
  text-align: right;
}

.order-detail-title {
  width: 100%;
  height: 50px;
  padding: 10px 0 15px 0;
  font-size: 16px;
  text-align: left;
  border-bottom: none;
}
.order-detail-title span:hover {
  cursor: pointer;
}
.order-detail-title-strong {
  margin-right: 5px;
  color: #03ac54;
}
.order-detail-title button {
  float: right;
  margin-right: 10px;
  color: #fff;
  background-color: #03ac54;
}
.order-detail-title button:hover {
  color: #fff;
  border-color: #03ac54 !important;
  background-color: #03ac54;
}

.order-detail-feature {
  text-align: left;
  position: fixed;
  z-index: 99;
  bottom: 0;
  padding: 15px;
  border-top: 1px solid #eee;
  background-color: #fff;
  width: 100%;
  transform: translateX(-15px);
}

.order-detail-amount {
  padding: 10px;
  text-align: right;
  display: flex;
  gap: 20px;
  .order-detail__price {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
    > span {
      color: #ff6e00;
      font-family: AvenirNext, AvenirNext;
    }
  }
}

.slide-fade-enter-active {
  transition: all 0.2s ease;
}
.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}
.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}
/* 切换订单详情内容的渲染 */
.slide-enter-active {
  transition: all 0.2s ease;
}

.slide-leave-active {
  transition: all 0.2s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-enter,
.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
.commodity-table {
  min-height: 89px;
}

.ivu-table-wrapper img {
  max-width: 50px;
  height: auto;
}
.ivu-col-span-4,
.ivu-col-span-8 {
  text-align: left;
}
.fl {
  float: left;
}

.approval {
  .ivu-input-number {
    width: 100%;
  }

  .sdp-modal {
    .ivu-modal-footer {
      display: none;
    }
  }
  .protocol {
    @pw: 25px;
    font-weight: bolder;
    color: red;
    border: 1px solid;
    border-radius: 50%;
    display: inline-block;
    width: @pw;
    height: @pw;
    line-height: @pw;
    text-align: center;
  }
}
/deep/.drag-btn {
  .icon-sort {
    color: #909090;
  }
}
///deep/.c--ellipsis {
//	width: 40px !important;
//}
/deep/.col--drag {
  width: 40px !important;
  .c--ellipsis {
    width: 100%;
  }
  .icon-sort {
    padding-left: 6px;
  }
}
/deep/.order-tag {
  .ivu-form-item-label {
    margin-top: 2px;
  }
}
/deep/.commodity-select {
  width: 100%;
  .ivu-dropdown {
    width: 100%;
  }
}
/deep/ .vxe-table {
  .vxe-cell--sort {
    .vxe-icon--caret-top {
      top: -1px !important;
      &:hover {
        color: var(--primary-color);
      }
    }
    .vxe-icon--caret-bottom {
      bottom: 3px !important;
      &:hover {
        color: var(--primary-color);
      }
    }
  }
  .vxe-body--row.row--current {
    background: #e6f7ec;
  }
}
</style>
