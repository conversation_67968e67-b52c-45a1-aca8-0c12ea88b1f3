<!--
 * @Description: 订单/退货退款-新增/编辑
 * @url: http://0.0.0.0:8089/#/newReturnOrder
 * @Author: hgj
 * @Date: 2022-09-07 16:01:48
 * @LastEditTime: 2023-09-15 15:23:40
-->
<template>
  <div class="add-return-order">
    <DetailPage
      pageType="add"
      :title="pageTitle"
      :customSaveBtn="true"
    >
      <Form
        ref="formValidate"
        :model="formValidate"
        :rules="ruleValidate"
        :label-width="120"
      >
        <base-block title="基础信息" class="base-info">
          <Row type="flex" justify="start" :gutter="20" class="topRow">
            <Col>
              <FormItem label="客户：" prop="userId" style="width: 378px;">
                <div v-if="shopName">
                  <Tooltip v-if="shopName.length > 16" :content="shopName" max-width="250">
                    <div style="width: 232px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{{ shopName }}</div>
                  </Tooltip>
                  <div v-else style="width: 232px;">{{ shopName }}</div>
                </div>
                <Select
                  style="width: 232px"
                  v-else
                  ref="selectInput"
                  clearable
                  v-model="formValidate.userId"
                  placeholder="请输入客户名称"
                  filterable
                  remote
                  :remote-method="remoteSearch"
                  :loading="searchLoading"
                  @on-change="handleUserChange"
                >
                  <Option
                    :label="`${option.email}`"
                    v-for="(option, index) in userList"
                    :value="option.id"
                    :key="index"
                  >
                    <p>{{ option.email }}</p>
                    <p style="color: #999999">{{ option.user_code }}</p>
                  </Option>
                </Select>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="客户业态：" style="width: 378px;" v-if="sysConfig.tc_platform == 1 && return_id">{{ user_business_type_desc }}</FormItem>
            </Col>
            <Col>
              <FormItem label="售后原因：" prop="reason"  style="width: 378px;">
                <movee-select
                  ref="moveeSelectRef"
                  style="display: inline-block; width: 232px"
                  url="/superAdmin/orderReturn/returnReason"
                  v-model="formValidate.reason"
                  :defaultVal="formValidate.reason"
                  :selectFirst="!isEdit ? 'isTrue' : ''"
                  placeholder="请选择原因"
                  @changeEvent="changeReason"
                  needAll="isNot"
                  :noId="true"
                  _label="remark"
                />
                <Input
                  style="width: 232px"
                  v-show="formValidate.reason === '其他原因'"
                  v-model="formValidate.other_reason"
                  type="text"
                />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="售后类型：" style="width: 378px;">
                <Select
                  :disabled="returnOrderTypeDis || business_type == 2"
                  @on-change="returnTypeOnChange"
                  v-model="formValidate.return_refund_type"
                  style="width: 232px"
                >
                  <Option value="0">退货退款</Option>
                  <Option value="1">仅退款</Option>
                </Select>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="是否关联订单：" style="position: relative;width: 378px;">
                <Select
                  style="width: 232px"
                  v-model="returnOrderMode"
                  :disabled="returnOrderModeDis || business_type == 2"
                  @on-change="returnOrderModeOnChange"
                >
                  <Option value="user" label="不关联"> </Option>
                  <Option value="order" label="关联"> </Option>
                </Select>
                <p
                  v-if="showSelectOrderBtn"
                  style="position: absolute; right: -38px; bottom: -1px"
                  class="primary"
                  @click="getOrderHistoryList"
                >
                  选择订单
                </p>
                <div
                  v-if="this.returnOrderMode === 'user'"
                  class="ml8 price-type-tooltip"
                  style="display: inline-block"
                >
                  <Tooltip max-width="200">
                    <div slot="content">
                      该类型退货单据结算仅支持新版客户结算
                    </div>
                    <SIcon icon="help1" :size="14" />
                  </Tooltip>
                </div>
              </FormItem>
            </Col>
            <Col>
              <FormItem
                label="售后日期："
                v-if="formValidate.return_date || returnDateIsUserAuditDate"
                style="width: 378px;"
              >
                <div>
                  <DatePicker
                    :value="formValidate.return_date"
                    type="date"
                    @on-change="$_onChangeReturnDate"
                    :clearable="false"
                    :disabled="returnDateIsUserAuditDate"
                    style="width: 232px"
                  ></DatePicker>
                  <Tooltip
                    v-if="returnDateIsUserAuditDate"
                    max-width="230"
                    placement="bottom"
                    content="售后日期取审核日期，不可修改！"
                  >
                    <s-icon icon="tips" :size="14" style="cursor: pointer;margin-left: 4px;" />
                  </Tooltip>
                </div>
              </FormItem>
            </Col>
            <Col v-if="formValidate.return_refund_type === '1'">
              <FormItem label="可退金额：" style="width: 378px;">
                <div>
                  <NumberInput
                    :disabled="true"
                    :precision="2"
                    :value="formValidate.actual_pay_price"
                    :min="0"
                    style="width: 232px"
                  ></NumberInput>
                </div>
              </FormItem>
            </Col>
            <Col v-if="formValidate.return_refund_type === '1'">
              <FormItem label="申请退款金额：" style="width: 378px;">
                <div>
                  <NumberInput
                    :precision="2"
                    :value="formValidate.apply_price"
                    :min="0"
                    :max="formValidate.actual_pay_price"
                    @on-change="handleActualPayPriceChange"
                    style="width: 232px"
                  ></NumberInput>
                </div>
              </FormItem>
            </Col>
            <Col
              v-show="
                returnOrderMode === 'order' &&
                isEnablePurchaseTask &&
                !isEdit &&
                !isAntiAudit &&
                +formValidate.return_refund_type !== 1
              "
            >
              <FormItem label="采购退回默认开启" style="width: 378px;">
                <Switch
                  v-model="defaultReturnProvider"
                  @on-change="defaultReturnProviderOnChange"
                  :true-value="1"
                  :false-value="0"
                />
              </FormItem>
            </Col>
          </Row>
        </base-block>
        <base-block>
          <template #title>
            <div>
              <h5>售后商品清单</h5>
              <span
                v-show="relationOrder && returnOrderMode === 'order'"
                class="ml24"
              >
                关联订单号:
                <a
                  :href="
                    '#/orderDetail?id=' +
                    relationOrderId +
                    '&orderNo=' +
                    relationOrder
                  "
                  target="_blank"
                  class="primary"
                >
                  {{ relationOrder }}
                </a>
              </span>
            </div>
            <div></div>
          </template>
        </base-block>
        <SearchInputCommodity
          :label="false"
          :hanlderSingleSearchHandle="hanlderSingleSearchHandle"
          :originData="returnOrderMode === 'order' ? returnList : returnListNew"
          @toScroll="toScroll"
        />
        <template v-if="returnOrderMode === 'order'">
          <SVxeEditableTable
            @on-delete="handleDelete"
            :disableAdd="true"
            :isShowRecordEditor="isAntiAudit"
            v-if="returnOrderMode === 'order' && columnsOrderCur.length"
            :recordEditorConfig="{
              autoInsertDelete: isAntiAudit,
              // autoInsertDelete: false,
            }"
            :loading="loading"
            id="goodsTable"
            ref="goodsTable"
            :data="returnList"
            :columns="columnsOrderCur"
            :isActiveColumn="true"
            :scroll-y="{ mode: 'wheel', gt: 30, oSize: 10 }"
            :defaultRowData="
              () => ({
                editable: 1,
                amount: '',
              })
            "
            :max-height="getEditTableHeight()"
            :row-config="{ useKey: true, isCurrent: true }"
          >
            <template
              #after-table-left
              v-if="formValidate.return_refund_type !== '1'"
            >
              <div class="after-table-left">
                <div class="after-table-left-hotkey">
                  <SIcon icon="tips" :size="12" class="mr6" />
                  <span>支持键盘操作，</span>
                  <Icon type="ios-arrow-round-back" />
                  <Icon type="ios-arrow-round-forward" />
                  <span>左右切换，</span>
                  <Icon type="ios-arrow-round-up" />
                  <Icon type="ios-arrow-round-down" />
                  <span>上下换行。</span>
                </div>
              </div>
            </template>
            <template #after-table-left v-else>
              <div class="after-table-left"><span></span></div>
            </template>
            <template
              #after-table-right
              v-if="formValidate.return_refund_type !== '1'"
            >
              <div>
                <span>退款金额：¥</span>
                <span style="color: #ed3f14">{{ calTotal || 0 }}</span>
                <template v-if="formValidate.return_refund_type !== '1'">
                  <span style="margin-left: 10px">退款服务费：¥</span>
                  <span style="color: #ed3f14">{{
                    returnServiceTotal || 0
                  }}</span>
                </template>
                <span style="margin-left: 10px">合计金额：¥</span>
                <span style="color: #ed3f14">{{ total || 0 }}</span>
              </div>
            </template>
            <template #after-table-right v-else>
              <div class="after-table-right"><span></span></div>
            </template>
          </SVxeEditableTable>
        </template>
        <template v-else>
          <!-- 不关联订单模式 -->
          <SVxeEditableTable
            :isShowRecordEditor="true"
            :loading="loading"
            id="goodsTableNew"
            ref="goodsTableNew"
            :data="returnListNew"
            :columns="columnsNew"
            :scroll-y="{ mode: 'wheel', gt: 30, oSize: 10 }"
            :defaultRowData="
              () => ({
                editable: 1,
                amount: '',
              })
            "
            :max-height="getEditTableHeight()"
            :row-config="{ useKey: true, isCurrent: true }"
            :isActiveColumn="true"
            @on-delete="onDelete"
            @on-insert="onInsert"
          >
            <template #after-table-right>
              <div>
                <span>合计金额：¥</span>
                <span style="color: #ed3f14">{{ calTotal || 0 }}</span>
              </div>
            </template>
          </SVxeEditableTable>
        </template>

        <base-block>
          <template #title>
            <div class="flex">
              <h5>图片信息</h5>
              <span class="ml8" style="color: #999; font-size: 13px"
                >仅支持jpg,jpeg,png格式，文件小于2M,最多添加5张图</span
              >
            </div>
          </template>
        </base-block>
        <div class="description img-upload-list" style="margin-bottom: 100px">
          <div
            class="demo-upload-list"
            v-for="(item, index) in imgList"
            :key="item.id"
          >
            <div class="img"><img :src="item.cdn_url + '!100x100'" /></div>
            <div class="demo-upload-list-cover">
              <i
                class="delete sui-icon icon-solid-close"
                @click="removeDetailImage(item, index)"
              />
              <i
                class="icon-img-view sui-icon view-icon"
                @click="previewImage(item.cdn_url + '!400x400', index)"
              />
            </div>
          </div>
          <UploadForAliyun
            v-show="imgList.length < 5"
            :show-upload-list="false"
            :maxCount="5"
            @on-success="ImageUploadSuccess"
            :format="['jpg', 'jpeg', 'png']"
            :accept="mineType.image.join(',')"
            :max-size="6144"
            :on-format-error="handleImageUploadFormatError"
            :on-exceeded-size="maxSize"
            multiple
            type="drag"
            class="providerImgUpload"
          >
            <div class="change-upload-type">
              <div class="load_box">
                <i
                  class="iconfont icon-add load_img"
                  style="color: #909090; font-size: 20px"
                ></i>
                <span class="load_text"> 上传</span>
              </div>
            </div>
          </UploadForAliyun>
        </div>
      </Form>
      <template #button-after>
        <Button
          v-if="!isAntiAudit"
          type="primary"
          @click="saveOrder('back')"
          :disabled="saveBtnDisabled"
          >保存并返回列表
        </Button>
        <Button
          v-if="isAntiAudit"
          type="primary"
          @click="saveAntiAudit()"
          :disabled="saveBtnDisabled"
          >保存
        </Button>
        <Button
          type="primary"
          v-if="!isEdit && !isAntiAudit"
          @click="saveOrder('continue')"
          :disabled="saveBtnDisabled"
          >保存继续新增
        </Button>
      </template>
    </DetailPage>
    <Modal title="查看图片" width="400px" v-model="modal_preview_image">
      <img :src="preview_image" style="width: 100%" />
    </Modal>
    <!--选择退货订单-->
    <div
      class="modal-mask"
      v-show="showRightModal"
      @click="showRightModal = false"
    ></div>
    <transition name="rightFade">
      <div
        class="rightModal"
        style="width: 620px; padding: 0"
        v-if="showRightModal"
      >
        <Drawer-header
          :title="`选择售后订单`"
          @on-close="showRightModal = false"
        ></Drawer-header>

        <div class="modal-body" style="padding: 16px 24px">
          <div v-show="!isAntiAudit">
            <Row class="selectRow mb10" type="flex" justify="start" :gutter="10">
              <Col style="flex: 1; padding-left: 0">
                <DatePicker
                  type="date"
                  @on-change="selectDate"
                  placeholder="选择发货日期"
                  style="width: 100%"
                ></DatePicker>
              </Col>
              <Col>
                <Select
                  style="width: 70px"
                  @on-change="onSearchTypeChange"
                  v-model="filters.type"
                >
                  <Option :value="searchType.order">单号</Option>
                  <Option :value="searchType.goods">商品</Option>
                </Select>
                <template v-if="filters.type === searchType.order">
                  <Input
                    class="search-value"
                    placeholder="输入单号"
                    v-model="filters.search_value"
                    @on-enter="getOrderHistoryList"
                    clearable
                  />
                </template>
                <GoodsSearch
                  class="search-value"
                  v-if="filters.type === searchType.goods"
                  v-model="filters.search_value"
                ></GoodsSearch>
                <Button
                  class="search-btn"
                  type="primary"
                  @click="getOrderHistoryList"
                >
                  <Icon type="ios-search" size="18"></Icon>
                </Button>
              </Col>
            </Row>
            <Row class="selectRow mb10" type="flex" justify="start" :gutter="10">
              <Col v-if="isOpenOrderImportDiffCommodityRemark" span="12">
                <Input
                  placeholder="输入商品备注"
                  v-model="filters.commodity_remark"
                  @on-enter="getOrderHistoryList"
                  clearable
                >
                  <Button
                    icon="ios-search"
                    slot="append"
                    @click="getOrderHistoryList"
                  ></Button>
                </Input>
              </Col>
            </Row>
          </div>
          <Table
            :height="getTableHeight() + 50"
            :columns="historyCols"
            :data="historyList"
            border
          ></Table>
        </div>
      </div>
    </transition>
    <SModal
      ref="orderLinkModeModalRef"
      :title="orderLinkModeModal[returnOrderMode].title"
      :text="orderLinkModeModal[returnOrderMode].content"
      :type="orderLinkModeModal[returnOrderMode].type"
      @ok="orderLinkModeModal[returnOrderMode].confirm"
      @quit="orderLinkModeModal[returnOrderMode].cancel"
      okTxt="继续"
    >
      <template #footer-left>
        <div>
          <Checkbox
            :value="orderLinkModeModal[returnOrderMode].state"
            @on-change="orderLinkModeModal[returnOrderMode].stateOnChange"
          >
            <span>下次不再提示</span>
          </Checkbox>
        </div>
      </template>
    </SModal>
  </div>
</template>

<script>
import moveeSelect from '@components/basic/moveeSelect';
import GoodsSearch from '@components/common/goodsAutoCompleteSelect';
import { SModal } from '@/components/modal';
import DetailPage from '@components/detail-page/index.js';
import SIcon from '@components/icon';
import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
import NumberInput from '@components/basic/NumberInput';
import CommoditySelect from '@/components/common/CommoditySelect';
import Icon from 'view-design/src/components/icon';
import { uniqueId } from 'lodash-es';
import Goods from '@api/goods.js';
import common from '@api/order.js';
import { getEditTableHeight } from '@/util/common';
import order from '@api/order.js';
import { MINE_TYPE } from '@/util/const';
import ConfigMixin from '@/mixins/config';
import LayoutMixin from '@/mixins/layout';
import FundAllocationMixin from '@/mixins/fund-allocation';
import { orderReturnUnAuditCheck } from './utils';
import DrawerHeader from './components/drawer-header.vue';
import { mapState } from 'vuex';
import date from '@util/date.js';
import storageUtil from '@util/storage';
import SearchInputCommodity from './components/SearchInputCommodity/index.vue';
import { Switch } from 'view-design';
import { post } from '@/api/request';
import SdpTableStaticFormWrap from '@/components/standard/sdp-table-static-form-wrap'

const LIST_KEY = 'order_commodity_id';
const defaultReturnProviderLocalKey = 'newReturnOrder-default-return-provider';

const searchType = {
  order: '0',
  goods: '1',
};
const TYPE_ALL = 'all';
const TYPE_PART = 'part';
const ORDER_LINK_MODE_MODAL_STATE = 'orderLinkModeModalState';
const ORDER_NO_LINK_MODE_MODAL_STATE = 'orderNoLinkModeModalState';
export default {
  name: 'new-return-order',
  mixins: [LayoutMixin, ConfigMixin, FundAllocationMixin],
  data() {
    return {
      orginEditCommodiyList: [],
      orderLinkModeModal: {
        order: {
          type: 'info',
          title: '确认',
          content:
            '从未关联订单状态切换成关联订单状态会清空现有录入数据，请确认',
          state:
            this.storage.getLocalStorage(ORDER_LINK_MODE_MODAL_STATE) || false,
          confirm: () => {
            this.orderModeConfirm();
          },
          cancel: () => {
            this.returnOrderMode = 'user';
            console.log('cancel', this.returnOrderMode);
          },
          stateOnChange: (val) => {
            this.storage.setLocalStorage(ORDER_LINK_MODE_MODAL_STATE, val);
            this.orderLinkModeModal['order'].state = val;
            console.log('val', val);
          },
        },
        user: {
          type: 'warning',
          title: '提示',
          content: '不关联订单产生的退货单据仅支持新版客户结算',
          state:
            this.storage.getLocalStorage(ORDER_NO_LINK_MODE_MODAL_STATE) ||
            false,
          confirm: () => {
            this.userModeConfirm();
          },
          cancel: () => {
            this.returnOrderMode = 'order';
            console.log('cancel', this.returnOrderMode);
          },
          stateOnChange: (val) => {
            console.log('val', val);
            this.storage.setLocalStorage(ORDER_NO_LINK_MODE_MODAL_STATE, val);
            this.orderLinkModeModal['user'].state = val;
          },
        },
      },
      loading: false,
      returnOrderMode: 'order',
      returnType: TYPE_ALL,
      mineType: MINE_TYPE,
      searchType,
      showRightModal: false,
      searchLoading: false,
      shopName: '',
      modal_preview_image: false,
      preview_image: '',
      columnsOrderCur: [],
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          render: (h, params) => {
            const { row } = params;
            return h('div', { class: 'demo-upload-list2' }, [
              <img
                src={
                  (row.logo ||
                    'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png') +
                  '!40x40'
                }
              />,
              h(
                'div',
                {
                  class: 'demo-upload-list2-cover',
                },
                [
                  h(
                    'Icon',
                    {
                      props: {
                        type: 'ios-eye-outline',
                      },
                      on: {
                        click: () => this.previewImage2(row.logo + '!400x400'),
                      },
                    },
                    [],
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: '商品',
          key: 'pur_no',
          width: 200,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('a', {}, data.order_commodity_id),
              h(
                'div',
                {
                  style: {
                    color: '#99999',
                  },
                },
                data.commodity_name,
              ),
            ]);
          },
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
        },
        {
          title: '发货数',
          key: 'actual_amount',
        },
        {
          title: '发货单价',
          key: 'actual_unit_price',
        },
        {
          title: '已退数',
          key: 'return_amount',
        },
        {
          title: '申请退货数',
          key: 'default_return_amount',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            const index = params.index;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_return_amount),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_return_amount = val;
                  params.row.default_total_price = parseFloat(
                    val * data.default_unit_price,
                  ).toFixed(2);
                  this.edittingStore[params.index].default_return_amount = val;
                  this.edittingStore[params.index].default_total_price =
                    params.row.default_total_price;
                  data.sub_bargain_price = (val * data.bargain_price).toFixed(
                    2,
                  );
                  this.edittingStore[params.index].sub_bargain_price =
                    data.sub_bargain_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                  this.edittingStore[params.index].amount_warning = '';
                  params.row.amount_warning = '';
                },
              },
            });
            if (params.row.amount_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'bottom',
                    content: params.row.amount_warning,
                    always: true,
                    transfer: true,
                  },
                },
                [template],
              );
            }
            return template;
          },
        },
        {
          title: '申请退货单价',
          key: 'default_unit_price',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            // .9999
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_unit_price),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_unit_price = val;
                  params.row.default_total_price = parseFloat(
                    val * data.default_return_amount,
                  ).toFixed(2);
                  this.edittingStore[params.index].default_unit_price = val;
                  this.edittingStore[params.index].default_total_price =
                    params.row.default_total_price;
                },
                'on-focus': (event) => {
                  this.edittingStore[params.index].price_warning = '';
                  params.row.price_warning = '';
                  event.target.select();
                },
              },
            });
            if (params.row.price_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'top',
                    content: params.row.price_warning,
                    always: true,
                    transfer: true,
                  },
                },
                [template],
              );
            }
            return template;
          },
        },
        {
          title: '申请退货金额',
          key: 'default_total_price',
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_total_price),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_total_price = val;
                  params.row.default_unit_price = data.default_return_amount
                    ? parseFloat(val / data.default_return_amount).toFixed(4)
                    : params.row.default_unit_price;
                  this.edittingStore[params.index].default_total_price = val;
                  this.edittingStore[params.index].default_unit_price =
                    params.row.default_unit_price;
                },
              },
            });
            return template;
          },
        },
        {
          title: '审批数',
          key: 'audit_amount',
          editRender: { autofocus: 'input', autoselect: true },
          show: () => this.isAntiAudit,
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_amount),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_amount = val;
                  params.row.audit_total_price = parseFloat(
                    val * data.audit_price,
                  ).toFixed(2);
                  this.edittingStore[params.index].audit_amount = val;
                  this.edittingStore[params.index].audit_total_price =
                    params.row.audit_total_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return template;
          },
        },
        {
          title: '审批单价',
          key: 'audit_price',
          show: () => this.isAntiAudit,
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            // .9999
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_price),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_price = val;
                  params.row.audit_total_price = parseFloat(
                    val * data.audit_amount,
                  ).toFixed(2);
                  this.edittingStore[params.index].audit_price = val;
                  this.edittingStore[params.index].audit_total_price =
                    params.row.audit_total_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return template;
          },
        },
        {
          title: '审批金额',
          key: 'audit_total_price',
          show: () => this.isAntiAudit,
          editRender: { autofocus: 'input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_total_price),
                min: 0,
                max: 999999999.99,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_total_price = val;
                  params.row.audit_price = data.audit_amount
                    ? parseFloat(val / data.audit_amount).toFixed(4)
                    : params.row.audit_price;
                  this.edittingStore[params.index].audit_total_price = val;
                  this.edittingStore[params.index].audit_price =
                    params.row.audit_price;
                },
              },
            });
            return template;
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            return h('Input', {
              props: {
                value: data.remark,
                disabled: !data.is_can_edit,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (event) => {
                  const val = event.target.value;
                  params.row.remark = val;
                  this.edittingStore[params.index].remark = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
          },
        },
        {
          title: '分账供应商',
          key: 'account_provider_name',
          show: () => this.isFundAllocationEnabled,
        },
        {
          title: '分账比例',
          key: 'account_ratio',
          show: () => this.isFundAllocationEnabled,
          render: (h, { row }) => {
            return h(
              'span',
              Number(row.account_ratio) ? row.account_ratio + '%' : '-',
            );
          },
        },
        {
          title: '商品默认采购员/供应商',
          key: 'purchase_name',
        },
        {
          title: '分拣员',
          key: 'sort_name',
        },
        {
          title: '司机',
          key: 'driver_name',
        },
        {
          title: '线路',
          key: 'line_name',
        },
      ],
      columnsNew: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
          fixed: 'left',
        },
        {
          type: 'img',
          title: '商品图片',
          key: 'logo',
          align: 'center',
          render: (h, params) => {
            const { row } = params;
            return h('div', { class: 'demo-upload-list2' }, [
              <img
                src={
                  row.logo ||
                  'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40'
                }
              />,
              h(
                'div',
                {
                  class: 'demo-upload-list2-cover',
                },
                [
                  h(
                    'Icon',
                    {
                      props: {
                        type: 'ios-eye-outline',
                      },
                      on: {
                        click: () => this.previewImage2(row.logo + '!400x400'),
                      },
                    },
                    [],
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: '商品名称',
          width: 200,
          key: 'commodity_id',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let row = params.row;
            let iconStyle = {
              color: '#cc4916',
              marginRight: '6px',
              border: '1px solid #cc4916',
              borderRadius: '3px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
            };
            return (
              <div style="display: flex; align-items: center">
                {row.commodity_id &&
                  row.unit_sell &&
                  row.unit !== row.unit_sell && (
                    <div style={iconStyle}>多规格</div>
                  )}
                <SdpTableStaticFormWrap
                  displayValue={{ value: row.commodity_name, placeholder: '商品名/编码/别名/关键字' }}>
                  <CommoditySelect
                    class="commodity-select"
                    commodityName={row.commodity_name}
                    params={{
                      is_online: this.createOrderShowOfflineGoods ? '' : 'Y',
                      choose_parent: '2',
                      user_id: this.formValidate.userId,
                    }}
                    dataProvider={common.getCommodity}
                    commodityIdKey="commodity_id"
                    commodityNameKey="commodity_name"
                    onOn-change={(cid, com) =>
                      this._addSingleGoods(cid, com, params)
                    }
                    nativeOnKeydown={(e) => {
                      // 单独处理回车交互，解决中文输入法下回车也会跳行的问题
                      if (e.code.includes('Enter')) {
                        e.stopPropagation();
                      }
                    }}
                    onOn-enter={() => {
                      this.$refs.goodsTableNew.insertOneAt(row);
                    }}
                    style={{ width: '300px' }}
                    slot-type="order"
                  ></CommoditySelect>
                </SdpTableStaticFormWrap>
              </div>
            );
          },
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
        },
        {
          title: '申请退货数',
          key: 'default_return_amount',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_return_amount),
                min: 0,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_return_amount = val;
                  params.row.default_total_price = parseFloat(
                    val * data.default_unit_price,
                  ).toFixed(2);
                  data.sub_bargain_price = (val * data.bargain_price).toFixed(
                    2,
                  );
                  params.row = this._updateStepPricing(data); // 更新阶梯定价价格

                  this.edittingStore[params.index].sub_bargain_price =
                    data.sub_bargain_price;
                  this.syncStoreListNew();
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.default_return_amount)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '申请退货单价',
          key: 'default_unit_price',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_unit_price),
                min: 0,
                max: 999999999.99,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_unit_price = val;
                  params.row.default_total_price = parseFloat(
                    val * data.default_return_amount,
                  ).toFixed(2);
                  this.syncStoreListNew();
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            if (params.row.price_warning) {
              template = h(
                'Tooltip',
                {
                  props: {
                    theme: 'danger',
                    placement: 'top',
                    content: params.row.price_warning,
                    always: true,
                    transfer: true,
                  },
                },
                [template],
              );
            }
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.default_unit_price)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '申请退货金额',
          key: 'default_total_price',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.default_total_price),
                min: 0,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.default_total_price = val;
                  params.row.default_unit_price = data.default_return_amount
                    ? parseFloat(val / data.default_return_amount).toFixed(2)
                    : params.row.default_unit_price;
                  this.syncStoreListNew();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.default_total_price)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '审批数',
          key: 'audit_amount',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          show: () => this.isAntiAudit,
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_amount),
                min: 0,
                max: 999999999.99,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_amount = val;
                  params.row.audit_total_price = parseFloat(
                    val * data.audit_price,
                  ).toFixed(2);
                  this.edittingStore[params.index].audit_amount = val;
                  this.edittingStore[params.index].audit_total_price =
                    params.row.audit_total_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.audit_amount)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '审批单价',
          key: 'audit_price',
          show: () => this.isAntiAudit,
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            // .9999
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_price),
                min: 0,
                max: 999999999.99,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_price = val;
                  params.row.audit_total_price = parseFloat(
                    val * data.audit_amount,
                  ).toFixed(2);
                  this.edittingStore[params.index].audit_price = val;
                  this.edittingStore[params.index].audit_total_price =
                    params.row.audit_total_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.audit_price)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '审批金额',
          key: 'audit_total_price',
          show: () => this.isAntiAudit,
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.audit_total_price),
                min: 0,
                max: 999999999.99,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.audit_total_price = val;
                  params.row.audit_price = data.audit_amount
                    ? parseFloat(val / data.audit_amount).toFixed(4)
                    : params.row.audit_price;
                  this.edittingStore[params.index].audit_total_price = val;
                  this.edittingStore[params.index].audit_price =
                    params.row.audit_price;
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: Number(data.audit_total_price)
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let index = params.index;
            const template = h('Input', {
              props: {
                value: data.remark,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-blur': (e) => {
                  const val = e.target._value;
                  params.row.remark = val;
                  this.syncStoreListNew();
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: data.remark
                  }
                }
              },
              [
                template
              ]
            );
          },
        },
        {
          title: '商品默认采购员/供应商',
          key: 'purchase_name',
        },
        {
          title: '分拣员',
          key: 'sort_name',
        },
        {
          title: '司机',
          key: 'driver_name',
        },
        {
          title: '线路',
          key: 'line_name',
        },
      ],
      historyCols: [
        {
          title: '订单',
          key: 'order_no',
          render: (h, params) => {
            let data = params.row;
            const isReturnOrder = +data.return_flag === 1;
            let returnTag = '';
            console.log('return_flag', data.return_flag, isReturnOrder);
            if (isReturnOrder) {
              // 退
              returnTag = h(SIcon, {
                props: {
                  icon: 'return',
                  size: 14,
                },
                style: {
                  color: '#FF9F00',
                },
              });
            }
            return h(
              'div',
              {
                style: {
                  display: 'flex',
                },
              },
              [
                h(
                  'div',
                  {
                    style: {
                      display: 'flex',
                      alignItems: 'center',
                      marginRight: '8px',
                    },
                  },
                  [returnTag],
                ),
                h('div', [
                  h('span', {}, data.order_no),
                  h(
                    'div',
                    {
                      style: {
                        color: '#999999',
                      },
                    },
                    data.create_time,
                  ),
                ]),
              ],
            );
          },
        },
        {
          width: 120,
          title: '发货日期',
          key: 'delivery_date',
        },
        {
          title: '金额',
          key: 'delivery_price',
          width: 80,
        },
        {
          title: '操作',
          key: 'action',
          width: 150,
          render: (h, params) => {
            if (this.formValidate.return_refund_type === '1') {
              return h('div', [
                h(
                  'span',
                  {
                    style: {
                      color: '#03ac54',
                      cursor: 'pointer',
                      marginRight: '20px',
                    },
                    on: {
                      click: () => {
                        if (!this.isAntiAudit) {
                          orderReturnUnAuditCheck(
                            {
                              order_id: params.row.id,
                              order_no: params.row.order_no,
                            },
                            this,
                          ).then((res) => {
                            this.getOrderHistoryDetail(params.row, 'all');
                            this.formValidate.actual_pay_price =
                              params.row.delivery_price;
                            this.returnOrderTypeDis = true;
                          });
                        } else {
                          this.getOrderHistoryDetail(params.row, 'all');
                          this.returnOrderTypeDis = true;
                        }
                      },
                    },
                  },
                  '选择',
                ),
              ]);
            }
            return h('div', [
              h(
                'span',
                {
                  style: {
                    color: '#03ac54',
                    cursor: 'pointer',
                    marginRight: '20px',
                  },
                  on: {
                    click: () => {
                      orderReturnUnAuditCheck(
                        {
                          order_id: params.row.id,
                          order_no: params.row.order_no,
                        },
                        this,
                      ).then(() => {
                        this.getOrderHistoryDetail(params.row, 'all');
                        this.returnOrderTypeDis = true;
                      });
                    },
                  },
                },
                '整单退',
              ),
              h(
                'span',
                {
                  style: {
                    color: '#03ac54',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      orderReturnUnAuditCheck(
                        {
                          order_id: params.row.id,
                          order_no: params.row.order_no,
                        },
                        this,
                      ).then(() => {
                        this.getOrderHistoryDetail(params.row, 'part');
                        this.returnOrderTypeDis = true;
                      });
                    },
                  },
                },
                '部分退',
              ),
            ]);
          },
        },
      ],
      historyList: [],
      returnList: [],
      returnListNew: [
        {
          index: 1,
          remark: '',
          inner_remark: '',
          unit: '',
          default_return_amount: 0,
          default_unit_price: 0,
          default_total_price: 0,
          commodity_id: '',
          commodity_name: '',
          bargain_price: 0,
          sub_bargain_price: 0,
          logo: 'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
        },
      ],
      edittingStore: [],
      formValidate: {
        return_refund_type: '0',
        storeId: 1001,
        userId: '',
        reason: '',
        other_reason: '',
        return_date: '',
        apply_price: 0,
        actual_pay_price: 0,
      },
      ruleValidate: {
        reason: [{ required: true, message: '请选择原因', trigger: 'change' }],
        storeId: [{ required: true, message: '请选择仓库', trigger: 'change' }],
        userId: [],
      },
      imgList: [],
      userList: [],
      filters: {
        type: searchType.order,
        search_value: '',
        no: '',
        delivery_date: '',
      },
      orderId: '',
      relationOrder: '',
      relationOrderId: '',
      returnOrderModeDis: false,
      isEdit: false,
      return_id: '',
      defaultReturnProvider:
        storageUtil.getLocalStorage(defaultReturnProviderLocalKey) || 0,
      returnTmpData: {},
      columnsCommdiy: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center',
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          render: (h, params) => {
            const { row } = params;
            return h('div', { class: 'demo-upload-list2' }, [
              <img
                src={
                  (row.logo ||
                    'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png') +
                  '!40x40'
                }
              />,
              h(
                'div',
                {
                  class: 'demo-upload-list2-cover',
                },
                [
                  h(
                    'Icon',
                    {
                      props: {
                        type: 'ios-eye-outline',
                      },
                      on: {
                        click: () => this.previewImage2(row.logo + '!400x400'),
                      },
                    },
                    [],
                  ),
                ],
              ),
            ]);
          },
        },
        {
          title: '商品',
          key: 'pur_no',
          width: 200,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('a', {}, data.order_commodity_id),
              h(
                'div',
                {
                  style: {
                    color: '#99999',
                  },
                },
                data.commodity_name,
              ),
            ]);
          },
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
        },
        {
          title: '发货数',
          key: 'actual_amount',
        },
        {
          title: '发货单价',
          key: 'actual_unit_price',
        },
        {
          title: '商品默认采购员/供应商',
          key: 'purchase_name',
        },
        {
          title: '分拣员',
          key: 'sort_name',
        },
        {
          title: '司机',
          key: 'driver_name',
        },
        {
          title: '线路',
          key: 'line_name',
        },
      ],
      columnsMoney: [
        {
          title: '可退金额',
          key: 'actual_pay_price',
        },
        {
          title: '申请退款金额',
          key: 'apply_price',
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.apply_price),
                min: 0,
                max: +data['actual_pay_price'],
              },
              style: {
                width: '232px',
              },
              on: {
                'on-change': (val) => {
                  params.row.apply_price = val;
                  this.edittingStore[params.index].apply_price =
                    data.apply_price;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return template;
          },
        },
      ],
      returnOrderTypeDis: false,
      initLoad: true,
      business_type: 0
    };
  },
  created() {
    this.initData();
    this.initColumns();
  },
  mounted() {
    this.initLoad = false;
  },
  computed: {
    ...mapState({
      openOrderCommodityInnerRemark: (state) =>
        state.sysConfig.open_order_commodity_inner_remark,
      openOrderReturnServiceChange: (state) =>
        state.sysConfig.is_open_order_return_service_charge,
    }),
    pageTitle() {
      if (this.isAntiAudit) {
        return '反审核退货单';
      }
      return this.isEdit ? '编辑退货单' : '新增退货单';
    },
    // 是否是反审核页面
    isAntiAudit() {
      return this.$route.query.is_anti_audit === 'Y';
    },
    // 退款服务费金额
    returnServiceTotal() {
      if (+this.openOrderReturnServiceChange !== 1) return 0;
      let totalNum = 0;
      let amountKey = 'default_return_amount';
      // 反审核取审批数
      if (this.isAntiAudit) {
        amountKey = 'audit_amount';
      }
      this.edittingStore.map((d) => {
        const price = +d.actual_amount
          ? Number(
              (+(d[amountKey] || 0) / d.actual_amount) *
                (+d.service_charge || 0),
            ).toFixed(2)
          : '0';
        totalNum += parseFloat(price);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    // 退货金额合计
    calTotal() {
      let totalNum = 0;
      let key = 'default_total_price';
      if (this.formValidate.return_refund_type === '1') {
        key = 'apply_price';
      }
      this.edittingStore.map((d) => {
        totalNum += parseFloat(d[key]);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    // 合计
    total() {
      return parseFloat(this.returnServiceTotal + this.calTotal).toFixed(2);
    },
    saveBtnDisabled() {
      if (this.returnOrderMode === 'order') {
        if (this.returnList.length === 0) {
          return true;
        }
      } else {
        let filterEditStore = this.edittingStore.filter(
          (item) => item.commodity_id !== '',
        );
        if (filterEditStore.length === 0) {
          return true;
        }
      }
      return false;
    },
    showSelectOrderBtn() {
      if (this.isEdit) {
        return false;
      } else {
        if (this.returnOrderMode === 'order') {
          return true;
        } else {
          return false;
        }
      }
    },
  },
  watch: {
    returnOrderMode(newVal) {
      if (newVal === 'user') {
        this.returnOrderTypeDis = true;
        this.formValidate.return_refund_type = '0';
      } else {
        this.returnOrderTypeDis = false;
      }
    },
  },
  methods: {
    hanlderSingleSearchHandle(item, value) {
      return (
        item.commodity_name.includes(value) || item.commodity_id.includes(value)
      );
    },
    toScroll(curRow) {
      const tableRef =
        this.$refs[
          this.returnOrderMode === 'order' ? 'goodsTable' : 'goodsTableNew'
        ];
      tableRef.scrollToRow(curRow);
      tableRef.setCurrentRow(curRow);
    },

    handleActualPayPriceChange(val) {
      this.edittingStore[0].apply_price = val;
      this.formValidate.apply_price = val;
    },
    getEditTableHeight,
    initData() {
      this.return_id = this.$route.query.return_id;
      let data = null;
      if (this.$route.query.data) {
        // 订单列表点击退款
        data = JSON.parse(this.$route.query.data);
      }
      const fromOrderList = () => {
        if (data) {
          this.getOrderHistoryDetail(data, 'part');
          this.shopName = data.shop_name;
        }
      };
      const fromReturnOrderList = () => {
        if (!this.isAntiAudit) {
          this.isEdit = true;
        }
        this.getOrderReturnDetail(this.return_id);
      };
      if (data) {
        fromOrderList();
        this.returnOrderTypeDis = true;
        this.returnOrderModeDis = true;
      }
      if (this.return_id) {
        fromReturnOrderList();
      }
    },

    orderModeConfirm() {
      this.returnOrderMode = 'order';
      this.returnList = [];
      this.relationOrder = '';
      this.syncStoreList();
      console.log('confirm', this.returnOrderMode);
    },
    userModeConfirm() {
      this.returnOrderMode = 'user';
      this.asyncReturnListNew();
      this.syncStoreListNew();
      if (!this.returnDateIsUserAuditDate)
        this.formValidate.return_date = date.getTodayDate();
      console.log('confirm', this.returnOrderMode);
      console.log('edittingStore-user', this.edittingStore);
    },
    initColumns() {
      if (this.isOpenUserBargainPrice) {
        const bargainPriceColumn = {
          title: '结算价',
          key: 'bargain_price',
          minWidth: 60,
        };
        const bargainSubPriceColumn = {
          title: '结算金额',
          key: 'sub_bargain_price',
          minWidth: 80,
        };
        let preIndex = this.columns.findIndex(
          (col) => col.key === 'default_total_price',
        );
        let existIndex = this.columns.findIndex(
          (col) => col.key === 'bargain_price',
        );
        let preIndex2 = this.columnsNew.findIndex(
          (col) => col.key === 'default_total_price',
        );
        let existIndex2 = this.columnsNew.findIndex(
          (col) => col.key === 'bargain_price',
        );
        if (preIndex && existIndex < 0) {
          // 申请退货金额后边
          this.columns.splice(
            preIndex + 1,
            0,
            bargainPriceColumn,
            bargainSubPriceColumn,
          );
        }
        if (preIndex2 && existIndex2 < 0) {
          // 申请退货金额后边
          this.columnsNew.splice(
            preIndex2 + 1,
            0,
            bargainPriceColumn,
            bargainSubPriceColumn,
          );
        }
      }
      if (this.isEnablePurchaseTask) {
        const purchaseColumn = {
          title: '关联采购单',
          width: '220',
          key: 'purchase_order_info',
          renderHeader: (h) => {
            return h('div', [
              h('span', '关联采购单'),
              h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    content: '采购任务生成的采购单',
                    placement: 'top',
                  },
                },
                [
                  h(SIcon, {
                    props: {
                      icon: 'tishifu',
                      size: 12,
                    },
                    style: {
                      marginLeft: '3px',
                      verticalAlign: 'baseline',
                    },
                    class: ['pointer'],
                  }),
                ],
              ),
            ]);
          },
          render: (h, params) => {
            const { row, index } = params;
            if (Array.isArray(row.purchase_order_info)) {
              return row.purchase_order_info.map((item) => {
                return (
                  <div>
                    <p>
                      <span>{item.purchase_person}:</span>
                      <span style="margin: 0 5px;">{item.in_num}</span>
                    </p>
                    <p>
                      <span
                        style=" color: #03ac54;cursor: pointer;"
                        onClick={() => {
                          let href = `#/purchase/detail?keep_scroll=1&id=${item.purchase_id}`;
                          window.open(href, '_blank');
                        }}
                      >
                        {item.pur_no}
                      </span>
                      {+item.channel_type === 5 && (
                        <SIcon
                          style="margin-left: 5px; color: #03ac54;"
                          icon="lian"
                          size={16}
                        />
                      )}
                    </p>
                  </div>
                );
              });
            }
            // 展示错误信息
            return h(
              'Tooltip',
              {
                props: {
                  content: row.purchase_order_info,
                  transfer: true,
                  maxWidth: 300,
                  placement: 'top',
                },
              },
              [
                h(SIcon, {
                  props: {
                    icon: 'tips',
                    size: 12,
                  },
                }),
              ],
            );
          },
        };
        const purchaseReturnColumn = {
          title: '采购退回',
          key: 'return_provider',
          render: (h, params) => {
            const { row, index } = params;
            if (isNaN(row.return_provider)) {
              // 展示错误提示
              return h(
                'Tooltip',
                {
                  props: {
                    content: row.return_provider,
                    transfer: true,
                    maxWidth: 300,
                    placement: 'top',
                  },
                },
                [
                  h(SIcon, {
                    props: {
                      icon: 'tips',
                      size: 12,
                    },
                  }),
                ],
              );
            }

            // 普通供应商或采购员
            let trueValue = '3';
            let falseValue = '0';
            if (+row.is_pool_provider_return === 1) {
              // 联营供应商
              trueValue = '1';
              falseValue = '2';
            }

            return h(Switch, {
              props: {
                disabled: this.isAntiAudit,
                value: row.return_provider,
                trueValue: trueValue,
                falseValue: falseValue,
              },
              on: {
                'on-change': (value) => {
                  console.log('on-change-row', value);
                  row.return_provider = value;
                  this.edittingStore[index].return_provider = value;
                },
              },
            });
          },
        };
        this.columns.push(purchaseColumn, purchaseReturnColumn);
      }
      if (Number(this.openOrderCommodityInnerRemark)) {
        this.columns.push({
          title: '退货内部备注',
          key: 'inner_remark',
          minWidth: 120,
          render: (h, params) => {
            let data = params.row;
            let index = params.index;
            return h('Input', {
              props: {
                maxlength: 300,
                value: data.inner_remark,
              },
              on: {
                'on-blur': (e) => {
                  const val = e.target._value;
                  params.row.inner_remark = val;
                  this.edittingStore[index].inner_remark = val;
                },
              },
            });
          },
        });
        this.columnsNew.push({
          title: '退货内部备注',
          key: 'inner_remark',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            let data = params.row;
            let index = params.index;
            const template = h('Input', {
              props: {
                value: data.inner_remark,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-blur': (e) => {
                  const val = e.target._value;
                  params.row.inner_remark = val;
                  this.edittingStore[index].inner_remark = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return h(SdpTableStaticFormWrap,
              {
                props: {
                  displayValue: {
                    value: data.inner_remark
                  }
                }
              },
              [
                template
              ]
            );
          },
        });
      }
      if (+this.openOrderReturnServiceChange === 1) {
        const beforeIndex = this.columns.findIndex(
          (item) => item.key === 'default_total_price',
        );
        const amountKey = this.isAntiAudit ? 'audit_amount' : 'default_return_amount';
        const serviceChangeColumn = {
          title: '退款服务费',
          key: 'return_service_charge',
          render: (h, params) => {
            let data = params.row;
            const price = +data.actual_amount
              ? Number(
                  (+(data[amountKey] || 0) / data.actual_amount) *
                    (+data.service_charge || 0),
                ).toFixed(2)
              : '0';
            return h('div', price);
          },
        };
        this.columns.splice(beforeIndex + 1, 0, serviceChangeColumn);
      }
      if (this.isAntiAudit) {
        const lossColumn = {
          title: '报损数量',
          key: 'loss_num',
          render: (h, params) => {
            let data = params.row;
            let template = h(NumberInput, {
              props: {
                precision: 2,
                value: Number(data.loss_num),
                min: 0,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  params.row.loss_num = val;
                  this.edittingStore[params.index].loss_num = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
            return template;
          },
        }
        this.columns.push(lossColumn);
      }
      this.returnTypeOnChange(this.formValidate.return_refund_type);
      if (this.isEdit || this.isAntiAudit) {
        this.returnOrderModeDis = true;
        this.returnOrderTypeDis = true;
      }
    },
    /**
     * 检查商品是否已存在
     * @param {object} goods 商品信息
     */
    _isGoodsExists(goods) {
      return this.returnListNew.find(
        (item) => Number(item.commodity_id) === Number(goods.commodity_id),
      );
    },
    _focusNextInput(row, columnKey = 'default_return_amount') {
      // 聚焦申请退货数
      this.$nextTick(() => {
        this.$refs.goodsTableNew.setEditCell(row, columnKey);
      });
    },
    onInsert(_, insertIndex) {
      if (!this.formValidate.userId && !this.isEdit) {
        this.$smessage({
          type: 'error',
          text: '请先选择客户',
        });
        this.returnListNew.splice(insertIndex + 1, 1);
        return;
      } else {
        this.returnListNew.splice(insertIndex + 1, 0, ..._);
        //this.returnListNew = [].concat(this.returnListNew, _);
      }
    },
    handleDelete(row) {
      const index = this.edittingStore.findIndex(
        (item) => item.id === row.id,
      );
      if (index !== -1) {
        this.edittingStore.splice(index, 1);
      }
    },
    // 删除行
    onDelete(delRow) {
      const delIndex = this.returnListNew.indexOf(delRow);
      if (delIndex !== -1) {
        this.returnListNew.splice(delIndex, 1);
      }
      this.syncStoreListNew();
      console.log('edittingStore', this.edittingStore);
      const { fullData } = this.$refs.goodsTableNew.getTableData();
      if (!fullData || fullData.length === 0) {
        this.returnListNew = [delRow];
        this.$smessage({
          type: 'error',
          text: '至少保留一条商品',
        });
      }
    },
    /**
     * 添加单个商品
     */
    _addSingleGoods(cid, addGoodsInfo, params) {
      if (!this.formValidate.userId && !this.isEdit) {
        this.$smessage({
          type: 'error',
          text: '请先选择客户',
        });
        this.$nextTick(() => {
          const commodityInputWrap =
            document.getElementsByClassName('commodity-select')[params.index];
          const commodityInput =
            commodityInputWrap && commodityInputWrap.querySelector('input');
          commodityInput.value = '';
        });
        return;
      }
      const existRow = this._isGoodsExists(addGoodsInfo);
      // if (!this.is_open_order_add_same_commodity && existRow) { 重复商品录入暂时不做
      if (existRow) {
        this.$smessage({
          type: 'error',
          text: '您已经添加过该商品了',
        });
        this._focusNextInput(existRow);
        return;
      }
      common
        .getOrderCommodity(this.formValidate.userId, addGoodsInfo.commodity_id)
        .then((res) => {
          this.isAddingGoods = false;
          let { data, status, message } = res;
          if (status) {
            // 如果是详情里已有的商品，填入id
            const existCommody = this.orginEditCommodiyList.find(
              (v) => v.commodity_id === addGoodsInfo.commodity_id,
            );
            if (existCommody && existCommody.id) {
              data.return_commodity_id = existCommody.id;
            }
            if (
              !this.userStylePriceIsZeroDefaultMarkPrice &&
              +data.is_price_type === 2 &&
              +data.price === 0
            ) {
              this.$Message.warning(`${data.name}未设置客户类型价`);
            }
            data.id += uniqueId('$unique-');
            data.commodity_id = addGoodsInfo.commodity_id;
            data.return_commodity_id = '';
            (data.inner_remark = ''),
              (data.default_return_amount = 0),
              (data.default_unit_price = 0),
              (data.default_total_price = 0),
              (data._isNewGoods = true);
            data.commodity_name = data.name;

            if (this.returnOrderMode === 'user') {
              data.default_unit_price = data.unit_price
                ? data.unit_price
                : data.price;
            }
            // 实际商品信息替换
            Object.assign(params.row, data);
            delete params.row._isNew;
            delete params.row.orderCommodity_id;
            const { visibleData } = this.$refs.goodsTableNew.getTableData();
            this.returnListNew = this._updateStepPricing(visibleData);
            this.syncStoreListNew();
            this._focusNextInput(this.returnListNew[params.index]);
          } else {
            this.modalError(message || '商品不存在', 0);
          }
        });
    },
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    returnOrderModeOnChange(val) {
      this.returnOrderMode = val;
      if (val === 'order') {
        if (this.orderLinkModeModal['order'].state === false) {
          this.$refs.orderLinkModeModalRef.open();
        } else {
          this.orderModeConfirm();
        }
      }
      if (val === 'user') {
        this.ruleValidate.userId = [
          { required: true, message: '请选择客户', trigger: 'change' },
        ];
        this.$forceUpdate();
        if (this.orderLinkModeModal['user'].state === false) {
          this.$refs.orderLinkModeModalRef.open();
        } else {
          this.userModeConfirm();
        }
      }
    },
    onSearchTypeChange() {
      this.filters.search_value = '';
    },
    selectDate(val) {
      this.filters.delivery_date = val;
      this.getOrderHistoryList();
    },
    remoteSearch(query) {
      if (query !== '') {
        this.searchLoading = true;
        const consortium_search_type =
          this.consortium_pricing_type == 3 || this.platform_4_b ? '' : 1;
        order.getUserBySearch(query, consortium_search_type).then((res) => {
          if (res.status) {
            if (res.data) {
              this.userList = res.data;
            }
            this.searchLoading = false;
          }
        });
      } else {
        this.userList = [];
      }
    },
    syncStoreList(extraData = {}) {
      let list = this.deepClone(this.returnList);
      list = list.map((item) => {
        let storeItem = this.edittingStore.find(
          (storeItem) => storeItem[LIST_KEY] === item[LIST_KEY],
        );
        // 选择订单, 整单退的话, 需要数据全部替换掉
        if (storeItem && extraData.type !== 'all') {
          return storeItem;
        }
        return item;
      });
      this.edittingStore = list;
    },
    syncStoreListNew() {
      let edittingStore = this.deepClone(this.returnListNew);
      this.edittingStore = edittingStore.filter(
        (item) => item.commodity_id !== '',
      );
    },
    getOrderHistoryDetail(row, type) {
      this.loading = true;
      order.getOrderHistoryDetail({ order_id: row.id }).then((res) => {
        if (res.status) {
          this.returnType = type;
          this.orderId = row.id;
          this.relationOrder = row.order_no;
          this.relationOrderId = row.id;
          let currentReturnList = this.returnList;
          this.returnList = [];
          const notExistList = [];
          if (type === 'part') {
            this.returnList = [];
            const historyList = res.data.map((item) => {
              if (!currentReturnList.find((v) => v.order_commodity_id === item.order_commodity_id)) {
                notExistList.push(item);
              };
              if (!isNaN(item.return_provider)) {
                // 普通供应商或采购员
                let trueValue = '3';
                let falseValue = '0';
                if (+item.is_pool_provider_return === 1) {
                  // 联营供应商
                  trueValue = '1';
                  falseValue = '2';
                }
                if (!this.isEdit && !this.isAntiAudit) {
                  if (this.defaultReturnProvider) {
                    item.return_provider = trueValue;
                  } else {
                    item.return_provider = falseValue;
                  }
                }
              }
              item.default_return_amount = 0;
              // item.default_unit_price = 0;
              item.default_total_price = 0;
              item.audit_amount = item.default_return_amount;
              item.audit_price = item.default_unit_price;
              item.audit_total_price = item.default_total_price;
              item.amount_warning = '';
              item.price_warning = '';
              item.loss_num = 0;
              return item;
            });
            if (this.isAntiAudit) {
              // 反审核只把不存在退货单中的商品加进来
              currentReturnList = currentReturnList.concat(notExistList);
              this.returnList = currentReturnList;
            } else {
              this.returnList = historyList;
            }
          } else {
            this.returnList = [];
            const historyList = res.data.map((item) => {
              if (!currentReturnList.find((v) => v.order_commodity_id === item.order_commodity_id)) {
                notExistList.push(item);
              };
              if (!isNaN(item.return_provider)) {
                // 普通供应商或采购员
                let trueValue = '3';
                let falseValue = '0';
                if (+item.is_pool_provider_return === 1) {
                  // 联营供应商
                  trueValue = '1';
                  falseValue = '2';
                }
                if (!this.isEdit && !this.isAntiAudit) {
                  if (this.defaultReturnProvider) {
                    item.return_provider = trueValue;
                  } else {
                    item.return_provider = falseValue;
                  }
                }
              }
              item.amount_warning = '';
              item.price_warning = '';
              item.loss_num = 0;
              item.audit_amount = item.default_return_amount;
              item.audit_price = item.default_unit_price;
              item.audit_total_price = item.default_total_price;
              return item;
            });
            if (this.isAntiAudit) {
              // 反审核只把不存在退货单中的商品加进来
              currentReturnList = currentReturnList.concat(notExistList);
              this.returnList = currentReturnList;
            } else {
              this.returnList = historyList;
            }
          }
          this.returnList.forEach((item) => {
            item.sub_bargain_price =
              item.sub_bargain_price ||
              ((item.actual_amount || 0) * (item.bargain_price || 0)).toFixed(
                2,
              );
          });
          console.log('returnList', this.returnList);
          this.loading = false;
          this.showRightModal = false;
          // 退货日期默认取发货日期
          if (!this.returnDateIsUserAuditDate) {
            this.formValidate.return_date = row.delivery_date;
          }
          this.edittingStore = this.deepClone(this.returnList);
          this.asyncReturnListNew();
        } else {
          this.errorNotice(res.message);
          this.loading = false;
        }
      });
    },
    asyncReturnListNew() {
      if (this.returnList.length > 0) {
        let returnListNew = this.returnList.map((item) => {
          return {
            commodity_id: item.commodity_id,
            default_return_amount: item.default_return_amount || 0,
            default_unit_price: item.default_unit_price || 0,
            remark: item.remark || '',
            inner_remark: item.inner_remark || '',
            default_total_price: item.default_total_price || '',
            commodity_name: item.commodity_name,
            sub_bargain_price: item.sub_bargain_price || 0,
            bargain_price: item.bargain_price || 0,
            logo: item.logo,
            unit: item.unit,
          };
        });
        this.returnListNew = returnListNew;
      } else {
        this.returnListNew = [
          {
            index: 1,
            inner_remark: '',
            remark: '',
            unit: '',
            default_return_amount: 0,
            default_unit_price: 0,
            default_total_price: 0,
            sub_bargain_price: 0,
            bargain_price: 0,
            commodity_id: '',
            commodity_name: '',
            logo: 'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
          },
        ];
      }
    },
    getOrderHistoryList() {
      if (!this.isAntiAudit) {
        if (this.shopName) {
          this.formValidate.userId = JSON.parse(this.$route.query.data).user_id;
        }
      }
      if (this.formValidate.userId) {
        let params = {
          user_id: this.formValidate.userId,
          delivery_date: this.filters.delivery_date,
          type: this.filters.type,
          search_value: this.filters.search_value,
          commodity_remark: this.filters.commodity_remark,
          return_refund_type:
            this.formValidate.return_refund_type === '1' ? 1 : 0,
        };
        if (this.isAntiAudit) {
          // 反审核只能查看关联订单
          params.search_value = this.relationOrder;
          params.type = searchType.order;
        }
        order.getOrderHistoryList(params).then((res) => {
          if (res.status) {
            this.historyList = res.data.list;
            this.showRightModal = true;
          } else {
            this.errorNotice(res.message);
          }
        });
      } else {
        this.errorNotice('请先选择客户');
      }
    },
    maxSize(file) {
      this.$Notice.warning({
        title: '文件超出限制',
        desc: '文件 ' + file.name + ' 太大, 超过2M限制.',
      });
    },
    handleImageUploadFormatError() {
      this.errorNotice('上传的图片文件格式不正确,仅支持jpg,jpeg,png图片文件');
    },
    ImageUploadSuccess(res) {
      if (!res.status) {
        this.successNotice(res.message);
        return;
      }
      this.successNotice('图片上传成功.');
      let imgObj = {
        url: res.data.server_url,
        cdn_url: res.data.upyun_url,
      };
      this.imgList.push(imgObj);
    },
    removeDetailImage(item, index) {
      this.imgList.splice(index, 1);
    },
    previewImage2(image, _images, _viewIndex = 0) {
      this.viewImage(image, _viewIndex);
    },
    previewImage(image, _viewIndex) {
      // this.preview_image = image;
      // this.modal_preview_image = true;
      const images = this.imgList.map((item) => item.cdn_url);
      this.viewImage(images, _viewIndex);
    },
    goBack() {
      this.router.go(-1);
    },
    asyncSaveData() {
      if (this.returnOrderMode === 'order') {
        this.syncStoreList();
      } else {
        this.syncStoreListNew();
      }
    },
    async saveAntiAudit() {
      let params = {
        return_id: this.return_id,
        remark: this.formValidate.reason,
        return_date: this.formValidate.return_date,
        pic_list: this.imgList.map((item) => ({
          path: item.url,
        })),
      };
      if (this.formValidate.reason === '其他原因') {
        params.remark = this.formValidate.other_reason;
      }
      let lossNumErrorGoods = null;
      const goodsList = this.edittingStore.map(item => {
        if (!lossNumErrorGoods && item.loss_num - item.default_return_amount > 0) {
          lossNumErrorGoods = item;
        }
        return {
          id: isNaN(item.id) ? '' : item.id,
          commodity_id: item.commodity_id,
          audit_amount: item.audit_amount,
          audit_price: item.audit_price,
          total_audit_price: item.audit_total_price,
          apply_amount: item.default_return_amount,
          apply_price: item.default_unit_price,
          apply_total_price: item.default_total_price,
          loss_num: item.loss_num,
          remark: item.remark,
          inner_remark: item.inner_remark,
          order_commodity_id: item.order_commodity_id,
        }
      });
      if (lossNumErrorGoods) {
        this.$smessage({ type: 'error', text: `${lossNumErrorGoods.commodity_name}报损数量不能大于申请退货数量` });
        return;
      }

      params.pic_list = JSON.stringify(params.pic_list);
      params.goods_list = JSON.stringify(goodsList);
      const { status, message } = await post('/superAdmin/orderReturn/antiAudit', params);
      if (status) {
        this.$smessage({ type: 'success', text: message || '保存成功！' });
        this.goBack();
      } else {
        this.$smessage({ type: 'error', text: message || '保存失败！' });
      }
    },
    saveOrder(type) {
      this.asyncSaveData();
      if (this.shopName) {
        this.formValidate.userId = this.shopName;
      }
      if (this.formValidate.userId) {
        let me = this;
        let params = this.paramsFormat();
        if (this.isEdit) {
          // 编辑
          let warning = params.commodity_list.some(
            (item) => Number(item.apply_amount) == 0,
          );
          if (warning) {
            this.errorMessage('申请退货数不能为0');
            return;
          }
          this.editOrder({ commodity_string: JSON.stringify(params) });
          return;
        }
        order
          .addNewOrderReturn({ commodity_string: JSON.stringify(params) })
          .then((res) => {
            let { message, status } = res;
            if (status) {
              this.successNotice(message || '新增成功！');
              if (type === 'back') {
                setTimeout(function () {
                  me.goBack();
                }, 500);
              } else {
                this.imgList = [];
                this.returnList = [];
                this.returnListNew = [
                  {
                    index: 1,
                    inner_remark: '',
                    remark: '',
                    unit: '',
                    default_return_amount: 0,
                    default_unit_price: 0,
                    default_total_price: 0,
                    commodity_id: '',
                    commodity_name: '',
                    logo: 'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
                  },
                ];
                this.edittingStore = [];
                this.formValidate.userId = '';
                this.relationOrder = '';
              }
            } else {
              this.errorNotice({
                tittle: '订单创建失败！',
                desc: message,
              });
            }
          });
      } else {
        this.errorNotice('请选择客户');
      }
    },
    changeStore(val) {
      this.formValidate.storeId = val;
    },
    changeReason(val) {
      this.formValidate.reason = val;
    },
    handleUserChange(val, opt) {
      this.formValidate.userId = val;
      const { business_type } = this.userList.find(item => item.id == val)
      this.business_type = business_type;
      if (business_type == 2) {
        this.formValidate.return_refund_type = '0'
        this.returnOrderMode = 'order';
        this.returnOrderModeDis = false;
        this.returnOrderTypeDis = false;
      }
    },
    $_onChangeReturnDate(val) {
      this.formValidate.return_date = val;
    },
    async getOrderReturnDetail(return_id) {
      this.loading = true;
      const { status, message, data } = await common.getOrderReturnDetail({
        return_id: return_id,
      });
      if (status) {
        this.returnOrderMode = data.return_type;
        this.relationOrder = data.order_no;
        this.relationOrderId = data.order_id;
        if (this.$refs.moveeSelectRef) {
          const returnReason = this.$refs.moveeSelectRef.optionList;
          let isOtherReason = returnReason.every(
            (item) => item.id !== data.remark,
          );
          if (isOtherReason) {
            this.formValidate.reason = '其他原因';
            this.formValidate.other_reason = data.remark;
          } else {
            this.formValidate.reason = data.remark;
          }
        }
        this.orderId = data.order_id;
        this.shopName = data.user_name;
        this.user_business_type_desc = data.user_business_type_desc;
        this.orginEditCommodiyList = data.commodity_list || [];
        this.formValidate.userId = data.user_id;
        this.formValidate.return_refund_type = data.return_refund_type;
        console.log('returnData', data);
        this.imgList = data.pic_list.map((item) => {
          return {
            url: item.path,
            cdn_url: item.path,
          };
        });
        if (!this.returnDateIsUserAuditDate)
          this.formValidate.return_date = data.return_date;
        if (data.return_refund_type === '0') {
          this.returnList = this.dataFormat(data.commodity_list);
          this.returnListNew = this.dataFormat(data.commodity_list);
          this.asyncSaveData();
        } else {
          this.columnsOrderCur = this.columnsCommdiy;
          this.returnList = data.order_detail;
          this.formValidate.apply_price = data.apply_price;
          this.formValidate.actual_pay_price = data.actual_pay_price;
          this.edittingStore = [
            {
              apply_price: data.apply_price,
              actual_pay_price: data.actual_pay_price,
            },
          ];
        }
        this.initialReturnList = this.deepClone(this.returnList);
      } else {
        this.errorMessage(message || '网络异常');
      }
      this.loading = false;
    },
    dataFormat(commodityList) {
      return commodityList.map((item, index) => {
        return {
          id: item.id,
          index: index,
          inner_remark: item.inner_remark,
          remark: item.remark,
          unit: item.unit,
          default_return_amount: item.apply_amount || 0,
          default_unit_price: item.apply_price,
          default_total_price: item.apply_total_price,
          audit_amount: item.audit_amount,
          audit_price: item.audit_price,
          audit_total_price: item.audit_total_price,
          commodity_id: item.commodity_id,
          commodity_name: item.commodity_name,
          logo: item.logo,
          return_commodity_id: item.id,
          order_commodity_id: item.order_commodity_id,
          actual_amount: item.delivery_amount,
          actual_unit_price: item.delivery_unit_price,
          return_amount: item.return_amount,
          is_can_edit: true,
          bargain_price: item.bargain_price || 0,
          sub_bargain_price:
            item.sub_bargain_price ||
            ((item.apply_amount || 0) * (item.bargain_price || 0)).toFixed(2),
          return_provider: item.return_provider,
          purchase_order_info: item.purchase_order_info,
          is_pool_provider_return: item.is_pool_provider_return,
          unit_convert_text: item.unit_convert_text,
          account_provider_name: item.account_provider_name,
          account_ratio: item.account_ratio,
          service_charge: item.service_charge,
          loss_num: item.loss_num || 0,
          purchase_name: item.purchase_name,
          sort_name: item.sort_name,
          driver_name: item.driver_name,
          line_name: item.line_name,
        };
      });
    },
    async editOrder(params) {
      const { status, message } = await order.editOrderReturn(params);
      if (status) {
        this.successMessage('修改成功');
        this.goBack();
      } else {
        this.errorMessage(message);
      }
    },
    paramsFormat() {
      let commodity_list = [],
        pic_list = [],
        saveCommodityList = [],
        apply_price = 0;
      if (this.formValidate.return_refund_type === '0') {
        // 客户退货
        if (this.isEdit) {
          // 编辑
          if (this.returnOrderMode === 'user') {
            // 不关联模式需要过滤发货数量为0的数据
            saveCommodityList = this.edittingStore.filter(
              (item) => Number(item.default_return_amount) > 0,
            );
          } else {
            saveCommodityList = this.edittingStore;
          }
        } else {
          // 新增
          // 新增需要过滤发货数量为0的数据
          saveCommodityList = this.edittingStore.filter(
            (item) => Number(item.default_return_amount) > 0,
          );
        }
        if (this.returnOrderMode === 'order') {
          // 关联订单模式
          saveCommodityList.forEach((item) => {
            console.log('commodity_list-item', item);
            if (item.is_can_edit) {
              let commodity = {
                order_commodity_id: item.order_commodity_id,
                commodity_id: item.order_id,
                remark: item.remark,
                inner_remark: item.inner_remark,
                apply_price: item.default_unit_price,
                apply_amount: item.default_return_amount,
                total_apply_price: item.total_apply_price,
                id: '',
                return_provider: isNaN(item.return_provider)
                  ? '0'
                  : item.return_provider,
              };
              if (this.isEdit) {
                commodity.id = item.return_commodity_id || '';
                commodity.commodity_id = item.commodity_id;
              }
              commodity_list.push(commodity);
            }
          });
        } else {
          saveCommodityList.forEach((item) => {
            // 不关联模式
            console.log('commodity_list-item', item);
            let commodity = {
              apply_price: item.default_unit_price,
              apply_amount: item.default_return_amount,
              total_apply_price: item.total_apply_price,
              commodity_id: item.commodity_id,
              inner_remark: item.inner_remark,
              remark: item.remark,
              id: '',
            };
            if (this.isEdit) {
              commodity.id = item.return_commodity_id || '';
            }
            commodity_list.push(commodity);
          });
        }
      } else {
        // 客户退款
        apply_price = this.formValidate.apply_price || 0;
      }

      this.imgList.forEach((item) => {
        let obj = { path: item.url };
        pic_list.push(obj);
      });
      let params = {
        order_id: this.orderId,
        store_id: this.formValidate.storeId,
        remark: this.formValidate.reason,
        commodity_list: commodity_list,
        pic_list: pic_list,
        return_date: this.returnDateIsUserAuditDate
          ? date.getTodayDate()
          : this.formValidate.return_date,
        return_type: this.returnOrderMode,
        user_id: this.formValidate.userId,
        return_id: '',
        return_refund_type: this.formValidate.return_refund_type,
        apply_price: apply_price,
      };
      if (this.isEdit || this.isAntiAudit) {
        params.return_id = this.return_id;
      }
      if (this.formValidate.reason === '其他原因') {
        params.remark = this.formValidate.other_reason;
      }
      console.log('params', params);
      return params;
    },
    defaultReturnProviderOnChange(value) {
      console.log('onChange---', value);
      storageUtil.setLocalStorage(defaultReturnProviderLocalKey, value);
    },
    returnTypeOnChange(value) {
      this.relationOrder = '';
      this.relationOrderId = '';
      this.returnList = [];
      this.returnListNew = [
        {
          index: 1,
          inner_remark: '',
          remark: '',
          unit: '',
          default_return_amount: 0,
          default_unit_price: 0,
          default_total_price: 0,
          commodity_id: '',
          commodity_name: '',
          logo: 'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png!40x40',
        },
      ];
      this.syncStoreList();
      this.formValidate.return_refund_type = value;
      if (!this.initLoad) {
        this.returnOrderModeDis = value === '1' ? true : false;
      }
      if (value === '1') {
        // this.returnOrderModeOnChange('order')
        this.columnsOrderCur = this.columnsCommdiy;
      } else {
        this.columnsOrderCur = this.columns;
      }
    },
    /**
     * @description: 判断是否为阶梯定价商品
     * @param {*} goods
     */
    _isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，根据商品下单数量，动态计算更新阶梯定价的商品价格
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing(row) {
      if (+row.is_price_type === 3) {
        const { default_return_amount } = row;
        if (this.isStepPricingGoods(row)) {
          const stepPriceItem = row.price_grads_list.find(
            (item) =>
              +default_return_amount >= +item.min_order_num &&
              (item.max_order_num
                ? +default_return_amount < +item.max_order_num
                : true),
          );
          if (stepPriceItem) {
            row.default_unit_price = stepPriceItem.price;
            row.price = stepPriceItem.price;
            row.default_total_price = parseFloat(
              default_return_amount * row.default_unit_price,
            ).toFixed(2);
          }
        }
      }

      return row;
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      if (this.postList.some((goods) => this.isStepPricingGoods(goods))) {
        const newWidth = 155;
        // 接口请求时间不一定，两个都更新就行
        const origin_order_amount_column = this.originCols.find(
          (item) => item.key === 'order_amount',
        );
        if (origin_order_amount_column) {
          origin_order_amount_column.width = newWidth;
        }
        const order_amount_column = this.goodsColumns.find(
          (item) => item.key === 'order_amount',
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
  },
  components: {
    moveeSelect,
    GoodsSearch,
    DrawerHeader,
    DetailPage,
    SIcon,
    SVxeEditableTable,
    NumberInput,
    SModal,
    CommoditySelect,
    SdpTableStaticFormWrap,
    Icon,
    SearchInputCommodity,
  },
};
</script>

<style lang="less">
.return-order-form {
  .ivu-upload-drag {
    width: 100%;
  }
}
</style>
<style scoped lang="less">
.img-upload-list {
  .demo-upload-list {
    margin: 0 20px 0 0;
  }
}
.providerImgUpload {
  display: inline-block;
}

/deep/.demo-upload-list2 {
  display: inline-block;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  height: 40px;
  width: 40px;
  line-height: 40px;
  background: rgba(0, 0, 0, 0);
  position: relative;
  box-shadow: none;
  margin-right: 0px;
}

/deep/.demo-upload-list2 img {
  width: 100%;
  height: 100%;
}
/deep/.demo-upload-list2:hover .demo-upload-list2-cover {
  display: block;
}
/deep/.demo-upload-list2-cover {
  display: none;
  position: absolute;
  color: #fff;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
.add-return-order {
  .primary {
    color: var(--primary-color);
    cursor: pointer;
  }
  .rightModal {
    .search-btn {
      border-bottom-left-radius: 0;
      border-top-left-radius: 0;
      position: relative;
      left: -5px;
    }
    .search-value {
      width: 240px;
      display: inline-block;
    }
  }
  /deep/.ivu-tooltip-inner {
    max-width: inherit;
  }
}
</style>
