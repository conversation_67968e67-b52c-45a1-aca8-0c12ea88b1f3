<!--
 * @Author: <PERSON>
 * @Date: 2021-12-03 14:41:58
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-31 10:21:11
 * @Description: 新增订单 -- 新版
-->
<template>
  <div class="order-new">
    <DetailPage
      pageType="add"
      title="新增订单"
      :disabledSave="createDisabled || disabledEverything"
      @on-save="createOrderBefore(1)"
      @click.native="alwaysShowOnlinePayTip = false"
    >
      <Form ref="form" inline label-colon :label-width="82" :disabled="false">
        <s-block title="基础信息" class="base-info">
          <FormItem label="客户" ref="error.user" :error="error.user">
            <div style="position: relative; width: 232px">
              <Input
                ref="userInput"
                :clearable="true"
                placeholder="请输入客户名称/客户编号"
                v-model="customerInputValue"
                @on-change="getSearchUser"
                @on-enter="enterUser"
                @on-focus="resetUserError"
                @on-blur="userDropDown = false"
              ></Input>
              <transition name="dropdown-fade">
                <div class="dropdown-user" v-show="userDropDown">
                  <ul>
                    <li
                      class="ivu-select-item"
                      v-for="info in userList"
                      :key="info.id"
                      :class="{ active: user.uid === info.uid }"
                      @click.stop="addUser(info)"
                      @mousedown.prevent
                    >
                      <strong
                        class="dropdown-items-strong"
                        v-if="info.email && info.email.length < 32"
                      >
                        {{ info.email }}
                        {{
                          info.receivable_style_name
                            ? `（${info.receivable_style_name}）`
                            : ''
                        }}
                      </strong>
                      <Tooltip
                        v-else
                        :content="
                          info.email +
                          (info.receivable_style_name
                            ? `（${info.receivable_style_name}）`
                            : '')
                        "
                        max-width="240"
                        transfer
                      >
                        <strong class="dropdown-items-strong">
                          {{ info.email }}
                          {{
                            info.receivable_style_name
                              ? `（${info.receivable_style_name}）`
                              : ''
                          }}
                        </strong>
                      </Tooltip>
                      <p class="dropdown-items-p">{{ info.user_code }}</p>
                    </li>
                  </ul>
                </div>
              </transition>
              <transition name="dropdown-fade">
                <div
                  class="dropdown-user-content"
                  v-show="!userDropDown && user.name && userList.length === 0"
                >
                  <p class="dropdown-empty">暂无数据</p>
                </div>
              </transition>
              <div
                v-if="showContractPriceTips || userMessage"
                style="padding-top: 6px; font-size: 12px"
              >
                <p class="text-red" v-if="showContractPriceTips">
                  所选客户未存在生效中的协议单
                </p>
                <p class="text-red" v-if="userMessage">{{ userMessage }}</p>
              </div>
            </div>
          </FormItem>
          <FormItem
            label="发货日期"
            ref="error.delivery_date"
            :error="error.delivery_date"
          >
            <Date-picker
              @on-change="handleChangeDate"
              type="date"
              placeholder="选择日期"
              :disabled="disabledEverything"
              v-model="date"
              format="yyyy-MM-dd"
              :editable="false"
              style="width: 232px"
            ></Date-picker>
          </FormItem>
          <FormItem label="送货时间段">
            <Select
              v-model="selectedDeliveryTime"
              :disabled="disabledEverything"
              style="width: 232px"
            >
              <Option
                v-for="item in deliveryTimeList"
                :value="item.id"
                :key="item.id"
                >{{ item.timeDu }}</Option
              >
            </Select>
          </FormItem>
          <FormItem
            label="支付方式"
            v-if="isOnlinePay && hasAuthority('order_orderList_changePayWay')"
            style="width: 360px"
          >
            <div>
              <Select
                :disabled="disabledEverything"
                v-model="payWay"
                @on-change="handleChangePayWay"
                style="width: 232px"
              >
                <Option
                  :value="payWayItem.value"
                  :key="payWayItem.value"
                  v-for="payWayItem in payWayList"
                  >{{ payWayItem.label }}</Option
                >
              </Select>
              <Tooltip
                v-if="payWay === '1'"
                placement="bottom"
                transfer
                max-width="249"
                content="选择在线支付方式后，请联系客户及时登录商城完成订单费用的支付。若超时未支付，订单将自动关闭"
                :always="alwaysShowOnlinePayTip"
              >
                <i
                  class="s-icon icon-tips"
                  style="margin-left: 0; top: -2px; cursor: pointer"
                ></i>
              </Tooltip>
            </div>
          </FormItem>
          <FormItem label="配送方式">
            <RadioGroup v-model="delivery_method">
              <Radio
                label="1"
                :disabled="disabledEverything"
                v-if="is_can_delivery_home"
              >
                配送
              </Radio>
              <Radio
                label="2"
                :disabled="
                  disabledEverything ||
                  !hasPickUpList.length ||
                  !is_can_self_pickup_point
                "
                v-if="sysConfig.self_pickup_point_apply_scope.indexOf('1') > -1"
              >
                自提
                <Tooltip
                  v-if="!hasPickUpList.length || !is_can_self_pickup_point"
                  :transfer="true"
                  :delay="0"
                  :maxWidth="246"
                  :content="
                    !is_can_self_pickup_point
                      ? '当前客户暂不支持自提配送方式'
                      : '当前未创建自提点，无法选择自提模式，请创建后重新操作'
                  "
                  placement="top"
                >
                  <SIcon class="mt-2 ml-2" icon="help1" :size="12" />
                </Tooltip>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="子机构" v-if="isOpenSubsidiary">
            <SuborganizationCascader
              v-model="formData.subsidiary_id"
              :userId="user.id"
              :disabled="!user.id"
            ></SuborganizationCascader>
          </FormItem>
          <FormItem label="餐次" v-if="isNewGoodsPackageRecipe">
            <MealtimeSelect
              v-model="formData.meal_type"
              style="width: 232px"
            ></MealtimeSelect>
          </FormItem>
          <!-- 开启分账后,拆单规则隐藏 -->
          <FormItem
            label="拆单规则"
            v-if="
              isSplitOrderByProvider &&
              !isFundAllocationEnabled &&
              user.detail.order_audit_inquiry_split == 0
            "
          >
            <Select v-model="formData.split_provider_rule" style="width: 232px">
              <Option
                v-for="item in splitRule"
                :value="item.value"
                :key="item.value"
                >{{ item.name }}
              </Option>
            </Select>
          </FormItem>
          <FormItem
            v-show="isSplitOrderByProvider && formData.split_provider_rule == 2"
            label="所属供应商"
          >
            <Select
              style="width: 232px"
              filterable
              clearable
              :disabled="isFundAllocationEnabled"
              v-model="formData.split_provider_id"
              placeholder="请选择"
            >
              <Option
                :value="info.id"
                v-for="info in initData.providers"
                :key="info.id"
                >{{ info.name }}</Option
              >
            </Select>
          </FormItem>
          <FormItem label="物流单号" v-if="isOpenExpress100">
            <Input
              style="width: 232px"
              maxlength="32"
              placeholder="请输入单号"
              v-model="formData.express_no"
            />
          </FormItem>
          <FormItem
            label="订单标签"
            v-if="orderTagList.length > 0"
            style="width: 100%"
          >
            <orderTag
              v-model="selectedOrderTag"
              @on-change="checkTag"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return disabledEverything ||
                (only_tag === '1' &&
                  selectedOrderTag.length > 0 &&
                  !selectedOrderTag.includes(item.id))
              }
              ">
            </orderTag>
          </FormItem>
          <FormItem
            :label="item.name"
            :key="item.key"
            v-for="item in orderCustomizeField"
          >
            <Input style="width: 232px" maxlength="256" v-model="item.value" />
          </FormItem>
          <div v-if="user.id && user.special_remark">
            <FormItem>
              <template #label>
                <span style="color: #f33333">特殊备注:</span>
              </template>
              <div style="white-space: pre">{{ user.special_remark }}</div>
            </FormItem>
          </div>
        </s-block>
        <base-block class="base-info receive_goods" style="margin-top: 24px">
          <div slot="title">
            <h5>收货信息</h5>
            <Dropdown
              class="check-address"
              style="margin-left: 12px"
              placement="bottom-start"
              trigger="custom"
              :visible="addressDropdownShow"
              @on-clickoutside="addressDropdownShow = false"
              v-if="
                delivery_method == 1 &&
                !disabledEverything &&
                sysConfig.is_open_user_multi_address === '1' &&
                addressList.length > 1
              "
            >
              <a style="color: #03ac54" @click="addressDropdownShow = true">
                切换地址
                <Icon type="ios-arrow-down"></Icon>
              </a>
              <DropdownMenu slot="list">
                <div class="address-header">
                  <div class="name">收货人</div>
                  <div class="tel">联系电话</div>
                  <div class="address">收货地址</div>
                </div>
                <div class="address-content">
                  <div
                    class="address-row"
                    :class="{
                      'address-row--active': item.id === user_address_id,
                    }"
                    v-for="(item, index) in addressList"
                    :key="index"
                    @click="onChangeAddress(item)"
                  >
                    <div
                      class="name"
                      :class="{ 'name--active': item.id === user_address_id }"
                    >
                      <img
                        class="icon"
                        src="../../assets/images/order/ic_default_address.png"
                        v-if="!item.id"
                      />
                      {{ item.name }}
                    </div>
                    <div class="tel">{{ item.tel }}</div>
                    <div class="address">
                      {{ item.address_detail }}{{ item.address_attach }}
                    </div>
                  </div>
                </div>
              </DropdownMenu>
            </Dropdown>
            <span
              class="newOrder-operation"
              style="margin-left: 12px"
              v-if="delivery_method == 2"
              @click="handleShowPick"
              >切换自提点</span
            >
          </div>
          <template v-if="delivery_method == 1">
            <FormItem label="收货人"
              ><SText :text="user.detail.name"
            /></FormItem>
            <FormItem label="联系电话">{{ user.detail.tel }}</FormItem>
            <FormItem label="地址"
              ><SText
                class="mt1"
                :text="
                  user.detail.address_detail + user.detail.address_attach || ''
                "
            /></FormItem>
          </template>
          <!-- 自提点 -->
          <template v-else>
            <FormItem label="自提点"><SText :text="pickInfo.name" /></FormItem>
            <FormItem label="联系电话">{{ pickInfo.mobile }}</FormItem>
            <FormItem label="地址"
              ><SText :text="pickInfo.address || ''"
            /></FormItem>
          </template>
        </base-block>
        <base-block
          title="商品清单"
          v-show="isCanOrder"
          style="margin-top: 27px"
        >
          <SFullscreen @change="updateTableHeight">
            <Row
              style="margin-left: 0"
              :gutter="10"
              type="flex"
              align="middle"
              slot="action-left"
            >
              <goods-list-modal
                :modalProps="{ transfer: true }"
                :params="{
                  is_online: createOrderShowOfflineGoods ? '' : 'Y',
                  delivery_date: delivery_date,
                  is_show_activity_unit_price: 1,
                  query_setting: JSON.stringify(excludeName()),
                  is_sell_independent_from_user_id:
                    superCreateOrderUnsellCommodity,
                }"
                :uid="user.id"
                :selectedGoods="isEnableAddSameGoods ? [] : newOrderList"
                @on-add="handlerAdd"
              >
              </goods-list-modal>
              <Button
                style="margin-left: 10px"
                styleType="btnStyleForAdd"
                v-if="
                  isEnableGoodsPackage &&
                  commodityPackageMode &&
                  !isEnableAddSameGoods
                "
                @click="goodsPackageModalShow"
              >
                添加套餐
              </Button>
              <Button
                style="margin-left: 10px"
                styleType="btnStyleForAdd"
                @click="showImportModal"
                >商品导入</Button
              >
              <div
                class="ai-btn-wrap"
                v-if="aiCreateOrder"
                @click="showSmartCreator"
              >
                <div class="ai-btn"></div>
              </div>
            </Row>
            <Row
              style="color: var(--primary-color); cursor: pointer"
              :gutter="20"
              type="flex"
              align="middle"
              slot="action-right"
            >
              <Col><span @click="activeUserHandmade">智能录单</span></Col>
              <Col
                ><span @click="activeHistoryAddOrder"
                  >从历史订单复制新增</span
                ></Col
              >
              <Col><span @click="searchSet">录入设置</span></Col>
            </Row>
            <EditableTable
              :max-height="tableHeight"
              ref="orderGoodsTable"
              :row-class-name="rowClassName"
              outer-border
              stickyTop="102"
              :columns="goodsColumns"
              :data="newOrderList"
              @on-draggable-data="_onDraggableData"
              @on-row-click="handleRowClick"
              @on-cols-change="handleColsChange"
              @on-sort-change="handleSortChange"
            >
              <template #after-table-left>
                <div class="after-table-left-hotkey">
                  <SIcon icon="tips" :size="12" class="mr6" />
                  <span>支持键盘操作，</span>
                  <Icon type="ios-arrow-round-back" />
                  <Icon type="ios-arrow-round-forward" />
                  <span>左右切换，</span>
                  <Icon type="ios-arrow-round-up" />
                  <Icon type="ios-arrow-round-down" />
                  <span>上下换行，</span>
                  <span>Enter 键新增一行， </span>

                  <span
                    >Shift+C 生成商品清单长图至粘贴板
                    <Tooltip
                      :transfer="true"
                      :delay="0"
                      :maxWidth="400"
                      :content="`固定打印【序号】、【商品名称】、【单位】、【下单数量】、【备注】五列`"
                      placement="top"
                    >
                      <SIcon class="mt-2 ml-2" icon="help1" :size="12" />
                    </Tooltip>
                  </span>
                </div>
              </template>
              <template #after-table-right>
                <div class="newOrder-amount" style="height: 19px">
                  <span class="label">下单数量：</span>
                  <span
                    style="
                      color: #03ac54;
                      font-weight: 500;
                      font-family: Avenir, Avenir;
                    "
                    class="mr20"
                    >{{ orderTotalNum }}</span
                  >
                  <span class="c6" v-show="totalReferenceProfitShow"
                    >参考毛利总额：</span
                  >
                  <span
                    v-show="totalReferenceProfitShow"
                    class="c6 mr10 order-total-num"
                    >{{ totalReferenceProfit }}</span
                  >
                  <span class="label">合计金额：</span>
                  <span
                    style="font-weight: 600; color: #ff6e00"
                    class="newOrder-amount-total"
                    >¥{{ calTotal || 0 }}</span
                  >
                </div>
              </template>
            </EditableTable>

            <ImportButton
              ref="importBtnRef"
              modalWidth="800"
              @beforeUpload="beforeUpload"
              @on-completed="importCompletedHandel"
              title="商品导入"
              :showTrigger="false"
              :post="importPost"
              :modalProps="{ transfer: true }"
              :data="{
                type:
                  importDownParams.template_type === '1'
                    ? '0'
                    : importDownParams.type,
                price_mode: importDownParams.price_mode,
                template_type: importDownParams.template_type,
                user_id: user.id,
                is_show_activity_unit_price: '1',
                delivery_date: delivery_date,
              }"
            >
              <template #custom-area>
                <div class="import-explain">
                  <h6>文本导入说明fff：</h6>
                  <p>
                    1. *
                    标识的字段导入模板中必须包含对应列,非必要字段则可没有对应列.
                  </p>
                  <p>
                    2.
                    导入模板后,将引导设置“模板列”与“订单字段”的对应关系来匹配数据,需要导入的模版列都必须设置对应订单字段,否则将无法导入该列数据.
                  </p>
                  <p>
                    3. 导入文件支持xls、xlsx格式，大小不超过2M，数据不超过200行.
                  </p>
                  <p>4. 导入文件不能包含“合并单元格”,否则无法导入.</p>
                  <p>5. 导入时只要有一行数据不正确,则导入失败.</p>
                  <p>
                    6.
                    导入填写订单标签时不能填写错误,请务必将同一个订单的商品订单标签保持一致.
                  </p>
                </div>
                <div class="import-mode-box">
                  <div class="label">选择导入模板:</div>
                  <div class="ml12">
                    <Select
                      v-model="importDownParams.template_type"
                      style="width: 230px"
                    >
                      <Option value="0"> 系统模板 </Option>
                      <Option value="1"> 客户常用商品 </Option>
                    </Select>
                  </div>
                </div>
                <template v-if="importDownParams.template_type == '0'">
                  <div class="import-mode-box">
                    <div class="label">导入模式(商品):</div>
                    <div class="ml12">
                      <RadioGroup v-model="importDownParams.type">
                        <Radio label="0">商品编码</Radio>
                        <Radio label="1">商品名称</Radio>
                        <Radio v-if="!isAliasAllowRepeat" label="2"
                          >商品别名</Radio
                        >
                      </RadioGroup>
                    </div>
                  </div>
                  <div class="import-mode-box">
                    <div class="label">价格:</div>
                    <div class="ml12">
                      <RadioGroup v-model="importDownParams.price_mode">
                        <Radio label="0">保持不变</Radio>
                        <Radio v-if="hasAuthority('A002001012')" label="1"
                          >导入时修改</Radio
                        >
                      </RadioGroup>
                    </div>
                  </div>
                  <div class="import-mode-box">
                    <div class="label">下载模版:</div>
                    <div class="ml12">
                      <span class="bold"
                        >点击下载
                        <span
                          class="ml16"
                          style="color: #03ac54; cursor: pointer"
                          @click="exportOrderTemplate"
                        >
                          订单商品导入模版
                        </span>
                      </span>
                    </div>
                  </div>
                </template>
              </template>
              <template slot="custom-error-tip" slot-scope="{ errorTable }">
                <p
                  v-show="importError"
                  style="margin: -20px 0 0 130px; color: #f33333"
                >
                  {{ importError }}
                </p>
                <Table
                  v-show="errorTable.length > 0"
                  :border="false"
                  outerBorder
                  class="mt15"
                  :columns="importCol"
                  :data="errorTable"
                  height="300"
                  max-height="300"
                >
                </Table>
              </template>
              <span>导入</span>
            </ImportButton>

            <userHandMade
              v-if="userHandmadeActive"
              :userId="user.id"
              :isFullscreen="isFullscreen"
              :deliveryDate="delivery_date"
              @addRecgData="addUserHandmade"
              @addOrder="handlerAdd"
              @close="activeUserHandmade"
            ></userHandMade>

            <history
              :user="user"
              :goods="newOrderList"
              :isFullscreen="isFullscreen"
              v-if="historyAddOrderActive"
              @close="activeHistoryAddOrder"
              @history="addHistoryGoods"
            ></history>

            <Modal
              v-model="isShowSet"
              title="录入设置"
              @on-ok="confirmSet"
              @on-cancel="isShowSet = false"
            >
              <Form ref="form" :label-width="140">
                <FormItem label="商品条码精准匹配：" style="margin-bottom: 0px">
                  <Switch v-model="isRightMatch" />
                </FormItem>
                <div class="tips_line" style="padding-bottom: 10px">
                  开启后，仅支持条码精准匹配，需要输入完整条码才能录入。配置将影响订单新增、编辑、核算、追加修改页面
                </div>
                <FormItem
                  v-if="!isRightMatch"
                  label="商品模糊匹配："
                  style="margin-bottom: 0px"
                >
                  <CheckboxGroup class="checkbox" v-model="goodNameSearch">
                    <Checkbox label="commodity_code">商品编码</Checkbox>
                    <Checkbox label="bar_code">商品条码</Checkbox>
                  </CheckboxGroup>
                </FormItem>
                <span v-if="!isRightMatch" class="tips_line"
                  >配置将影响订单新增、编辑、核算、追加修改页面</span
                >
              </Form>
            </Modal>

            <GoodsPackageModal
              ref="goodsPackageModal"
              :modalProps="{ transfer: true }"
              :show-mode-filter="true"
              :show="goodsPackageModal.show"
              :columns="goodsPackageModal.columns"
              :defaultValue="true"
              @on-cancel="$_closeGoodsPackageModal"
              @on-ok="$_onSelectGoodsPackage"
            />
          </SFullscreen>
          <div
            v-show="isOpenOrderCombine && hasStepPricingGoods"
            class="text--error"
            style="padding: 10px 0 0 22px"
          >
            当前订单包含阶梯定价商品，不支持合并订单。
          </div>
        </base-block>

        <base-block
          title="其他信息"
          v-show="isCanOrder"
          style="margin-top: 0px"
        >
          <div class="newOrder-remarks">
            <FormItem label="订单备注" style="width: 100%">
              <Input
                style="width: 418px"
                v-model="remarks"
                type="textarea"
                :maxlength="512"
                show-word-limit
                placeholder="输入订单备注"
              ></Input>
            </FormItem>

            <FormItem label="附件" style="margin-top: 10px; width: 100%">
              <AttachmentUpload v-model="attachmentFiles" />
            </FormItem>
          </div>
        </base-block>
      </Form>
      <template #button-between>
        <Button
          type="primary"
          ghost
          :disabled="disabledEverything"
          @click="showNewOrderReview"
          >核对订单</Button
        >
      </template>
      <template #button-after>
        <Button
          type="primary"
          ghost
          :disabled="createDisabled || disabledEverything"
          @click="createOrderBefore(2)"
          >保存并新增</Button
        >
        <Button
          v-if="
            isOpenSyncProtocolIsAddCommodity &&
            isEnableOrderSyncContractPrice &&
            hasAuthority('A002001003001')
          "
          type="primary"
          ghost
          :disabled="createDisabled || disabledEverything"
          @click="createOrderBefore(1, { sync_protocol: 1 })"
          >保存并同步协议单</Button
        >
      </template>
    </DetailPage>

    <Modal
      v-model="newOrderReviewActive"
      title="核对订单"
      width="1000"
      :mask-closable="false"
      :closable="false"
    >
      <i-table
        highlight-row
        :columns="checkColumns"
        :data="checkOrderList"
      ></i-table>
      <div slot="footer">
        <Button @click="newOrderReviewActive = false">取 消</Button>
        <Button type="primary" @click="confirmReview">确 认</Button>
      </div>
    </Modal>
    <AgreementPriceSelectModal
      :show="agreementPriceSelectModal.show"
      :tableData="agreementPriceSelectModal.data"
      @confirm="agreementPriceSelectModal.confirm"
      @cancel="agreementPriceSelectModal.cancel"
    >
    </AgreementPriceSelectModal>

    <pickUpModal
      :selectId="pickInfo.id"
      ref="pickUpModal"
      @change="handleChangePick"
    ></pickUpModal>

    <template v-if="aiCreateOrder">
      <!-- AI录单 -->
      <NewOrderSmartModal
        ref="orderSmartModal"
        :delivery_date="delivery_date"
        :userId="user.id"
        @smart-goods-list="handleSmartGoodsList"
        @smart-order-start="handleSmartStart"
        @smart-order-end="handleSmartEnd"
        @orginal-data="handleOrginalData"
        @tag-label-show="handleTagLabelShow"
      ></NewOrderSmartModal>
      <TagLabel v-model="showTagLabel" :label="smartOrginalData" />
    </template>

    <div class="process-wrapper">
      <div class="process-content">
        <Progress
          v-if="percent <= 100 && percent > 0"
          :percent="percent"
          :stroke-width="3"
          :hide-info="true"
          :stroke-color="['#48FFA0', '#C04DFF']"
        />
        <Progress
          :stroke-color="['#48FFA0', '#C04DFF']"
          :stroke-width="3"
          :percent="110"
          v-if="percent > 100"
        >
          <Icon type="checkmark-circled"></Icon>
          <span style="color: var(--primary-color)">全部识别完成</span>
        </Progress>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import DetailPage from '@/components/detail-page/index.js';
import SuborganizationCascader from '@/components/common/SuborganizationCascader.vue';
import MealtimeSelect from '@/components/common/mealTimeSelect.vue';
import EditableTable from '@/components/editable-table/index.js';
import Table from '@/components/table';
import CommoditySelect from '@/components/common/CommoditySelectTable';
import mvSelect from '@components/basic/mvSelect/mvSelect.vue';
import { mapState } from 'vuex';
import GoodsPackageModal from '@/components/packageGoods/PackageGoodsModal.vue';
import NumberInput from '@/components/basic/NumberInput.vue';
import history from '@/components/order/NewOrderHistory.vue';
import userHandMade from '@/components/order/NewOrderUserHandMade.vue';
import GoodsListModal from '@/components/order/goodsListModal.vue';
import StepPricingPoptip from './components/StepPricingPoptip.vue';
import { debounce } from 'lodash-es';
import common from '@/api/order.js';
import Goods from '@/api/goods.js';
import '@/assets/scss/mixin.scss';
import SIcon from '@/components/icon';
import DateUtil from '@/util/date.js';
import HeaderFilter from './components/header-filter.vue';
import { uniqueId } from 'lodash-es';
import LayoutMixin from '@/mixins/layout';
import ConfigMixin from '@/mixins/config.js';
import CalcPrice from './mixins/calcContractPrice';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import authority from '@/util/authority.js';
import { getEditTableHeight } from '@/util/common';
import StorageUtil from '@util/storage.js';
import AgreementPriceSelectModal from './components/agreementPrice-select-modal/index.vue';
import pickUpModal from './components/pickUpModal.vue';
import { MINE_TYPE } from '@/util/const';
import Tooltip from '@components/base-tooltip';
import { Table as ITable } from 'view-design';
import SFullscreen from '@/components/s-fullscreen';
import Button from '@components/button';
import SText from '@/components/s-text';
import SBlock from '@/components/s-block';
import SLoading from '@components/s-global-loading';
import { Sentry } from '../../init/init-sentry';
import FundAllocationMixin from '@/mixins/fund-allocation';
import NewOrderSmartModal from '@/components/order/NewOrderSmart';
import TagLabel from '@/components/tag-label/index';
import SdpTableStaticFormWrap from '@/components/standard/sdp-table-static-form-wrap';
import SdpTableStaticPoptip from '@/components/standard/sdp-table-static-poptip';
import { initPage, addTools, removeTool } from '@sdp/xiaodong-sdk';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

const aiIcon = require('../../assets/images/aiOrderCreate/ai-icon.png');

import coreEfficiencyIndexLogger, {
  INDEX_TYPE,
} from '@/util/coreEfficiencyIndexLogger';
import user from '@/api/user';
import Template from '../appCenter/certificate/template.vue';
import { type } from 'os';
const { hasAuthority } = authority;
const filterList = [
  {
    name: '最近一次进价',
    value: '1',
    tip: '每次入库单审核过后都会更新，但是可以进行手动更改',
  },
  {
    name: '最近一次入库价',
    value: '2',
    tip: '每次入库单审核之后才可以更新',
  },
  {
    name: '库存均价',
    value: '3',
    tip: '现有库存中的库存均价',
  },
  {
    name: '最近一次采购价',
    value: '4',
    tip: '采购单收货之后更新的采购价',
  },
];
const priceTypeMap = {
  1: 'in_price',
  2: 'store_in_price',
  3: 'average_price',
  4: 'last_receipt_price',
};
const discountRatioKey = 'discount';
const marketPriceKey = 'org_price';
const agreementPriceKey = 'price';

export default {
  name: 'OrderNew',
  components: {
    SdpTableStaticFormWrap,
    SdpTableStaticPoptip,
    SBlock,
    Button,
    SFullscreen,
    SText,
    DetailPage,
    SuborganizationCascader,
    MealtimeSelect,
    EditableTable,
    history,
    userHandMade,
    GoodsListModal,
    GoodsPackageModal,
    AttachmentUpload,
    AgreementPriceSelectModal,
    pickUpModal,
    Table,
    Tooltip,
    SIcon,
    ITable,
    NewOrderSmartModal,
    TagLabel,
    orderTag
  },
  mixins: [LayoutMixin, ConfigMixin, CalcPrice, FundAllocationMixin],
  data() {
    return {
      alwaysShowOnlinePayTip: false,
      payWay: '0',
      //TODO
      isAddingGoods: false, // 标志位，防止在商品添加过程中重复添加
      goodsQueue: [], // 待添加商品队列
      progressInterval: null,
      totalReferenceProfitShow: false,
      processText: '正在识别中......',
      percent: 0,
      smartOrginalData: '',
      showTagLabel: false,
      isFullscreen: false,
      tableHeight: getEditTableHeight(),
      isRightMatch: StorageUtil.getLocalStorage('isRightMatch')
        ? StorageUtil.getLocalStorage('isRightMatch')
        : false,
      goodNameSearch: StorageUtil.getLocalStorage('goodNameSearch')
        ? StorageUtil.getLocalStorage('goodNameSearch')
        : ['commodity_code', 'bar_code'],
      isShowSet: false,
      hasPickUpList: [], // 是否有自提仓库
      pickInfo: {},
      addressDropdownShow: false,
      user_address_id: '', // 多地址的id，如果是默认地址，就传空
      addressList: [],
      addressColumns: [
        {
          title: '收货人',
          key: 'name',
        },
        {
          title: '联系电话',
          key: 'tel',
        },
        {
          title: '收货地址',
          key: 'address_detail',
        },
      ],
      is_can_self_pickup_point: true, // 选择的客户是否支持自提配送
      is_can_delivery_home: true, // 选择的客户是否支持配送上门
      initData: {
        providers: [],
      },
      splitRule: [
        {
          name: '按供应商拆单',
          value: '1',
        },
        {
          name: '指定订单所属供应商',
          value: '2',
        },
      ],
      orderCustomizeField: [], // 订单自定义字段
      goodsCustomizeField: [], // 订单明细自定义字段
      originUser: {},
      user: {
        id: '',
        uid: '',
        name: '',
        detail: '',
        address_attach: '',
        receivable_style_name: '',
        special_remark: '',
      },
      formData: {
        subsidiary_id: '',
        meal_type: '',
        split_provider_rule: '1',
        split_provider_id: '',
      },
      goodsPackageModal: {
        show: false,
        columns: [
          {
            width: 60,
            type: 'selection',
          },
          {
            title: '图片',
            render: (h, params) => {
              let { row } = params;
              let key = 'pic_url';
              return h('img', {
                style: {
                  width: '40px',
                },
                attrs: {
                  src: row[key],
                },
              });
            },
          },
          {
            title: '套餐名称',
            key: 'name',
          },
          {
            title: '单位',
            key: 'unit',
          },
          {
            title: '描述',
            key: 'summary',
          },
          {
            title: '下单数量',
            render: (h, params) => {
              const { row, index } = params;
              const key = 'amount';
              return h('NumberInput', {
                props: {
                  value: row.amount,
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                on: {
                  'on-change': (value) => {
                    row[key] = value;
                  },
                  'on-blur': () => {
                    const list = this.$refs.goodsPackageModal.getList();
                    list.forEach((item) => {
                      if (item.id === row.id) {
                        item.amount = row.amount;
                      }
                    });
                    this.$refs.goodsPackageModal.setList(list);
                  },
                  'on-click': (e) => {
                    window.event
                      ? (window.event.cancelBubble = true)
                      : e.stopPropagation();
                  },
                },
              });
            },
          },
        ],
      },
      must_tag: '0', // 是否必须选择一个标签
      only_tag: '0', // 是否只能选择一个标签
      idGeneratorIndex: 1,
      userMessage: '',
      selectedOrderTag: [],
      orderTagList: [],
      error: {
        user: '',
        delivery_date: '',
        goods: '',
      },
      date: '',
      delivery_date: '',
      delivery_method: '1',
      userHasContractPriceOrder: true,
      showContractPriceTips: false,
      userHandmadeActive: false, // 客户手工单
      historyAddOrderActive: false, //  从历史订单复制新增
      newOrderReviewActive: false,
      userDropDown: false,
      tUser: '', // * 追求最好的用户体验，暂时缓存用户信息
      deliveryTimeList: [],
      selectedDeliveryTime: '',
      userList: [],
      newOrderList: [],
      postList: [],
      remarks: '',
      createDisabled: false,
      previousValue: '',
      checkOrderList: [],
      inputCheckOrderList: [],
      checkColumns: [
        {
          type: 'index',
          width: 60,
          align: 'center',
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'center',
        },
        {
          title: '商品单位',
          key: 'unit',
          align: 'center',
        },
        {
          title: '描述',
          key: 'summary',
          align: 'center',
        },
        {
          title: '订购数',
          key: 'amount',
          width: 100,
          align: 'center',
          props: {
            amountArr: '',
          },
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.amount),
                min: 0,
                // precision: 2,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change': (val) => {
                  this.inputCheckOrderList[params.index]['check_amount'] = val;
                },
                'on-focus': (event) => {
                  event.target.select();
                },
              },
            });
          },
        },
        {
          title: '订购单价（元）',
          key: 'price',
          align: 'center',
        },
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('i-input', {
              props: {
                value: data.remark,
              },
              class: {
                remarks: true,
              },
              nativeOn: {
                click: ($event) => {
                  $event.stopPropagation();
                },
                change: ($event) => {
                  let value = $event.target.value;
                  params.row.remark = value;
                  this.inputCheckOrderList[params.index].remark = value;
                },
              },
            });
          },
        },
      ],
      activeRowIndex: 0,
      goodsColumns: [],
      ifFromCopy: false,
      originCols: [
        {
          type: 'drag',
          width: 28,
          minWidth: 28,
          fixed: 'left',
          align: 'center',
          style: {
            paddingRight: 0,
            paddingLeft: '6px',
          },
          render: (h, parmas) => {
            return h('Icon', {
              class: {
                'text sui-icon icon-sort': true,
              },
              style: {
                cursor: 'pointer',
              },
              props: {
                size: 13,
              },
            });
          },
        },
        {
          type: 'titleCfg',
          titleType: 'order_edit',
          minWidth: 32,
          width: 32,
          style: {
            paddingRight: 0,
            paddingLeft: '12px',
          },
          align: 'center',
          fixed: 'left',
          key: 'title',
          render: (h, params) => {
            const { row, index } = params;
            const operation = h('div', [
              h(
                SdpTableStaticPoptip,
                {
                  props: {
                    poptipProps: {
                      title: '确认删除此商品？',
                      confirm: true,
                      transfer: true,
                      placement: 'right',
                      disabled: this.newOrderList.length === 1,
                    },
                  },
                  on: {
                    'on-ok': () => {
                      this._deleteGoods(row, index);
                    },
                  },
                },
                [
                  h(SIcon, {
                    class:
                      'icon-record-editor icon-record-editor--delete custom-teleport',
                    props: {
                      icon: 'jian',
                      size: 16,
                    },
                  }),
                ],
              ),
              h(SIcon, {
                props: {
                  icon: 'jia1',
                  size: 16,
                },
                class: 'icon-record-editor icon-record-editor--insert mt7',
                on: {
                  click: (event) => {
                    event.stopPropagation();
                    this._addGoods(index);
                  },
                },
              }),
            ]);
            return operation;
          },
        },
        {
          title: '序号',
          align: 'center',
          minWidth: 44,
          width: 44,
          key: 'index',
          style: {
            paddingRight: 0,
            paddingLeft: '10px',
          },
          fixed: 'left',
          render: (h, params) => {
            const template = [];
            // 预售商品
            if (params.row.expected_arrival_date) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'yu',
                    size: 14,
                  },
                  style: {
                    marginRight: '2px',
                    verticalAlign: 'middle',
                  },
                }),
              );
            }
            template.push(
              h(
                'span',
                {
                  style: {
                    verticalAlign: 'middle',
                  },
                },
                params.index + 1,
              ),
            );
            return template;
          },
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'left',
          minWidth: 70,
          width: 70,
          fixed: 'left',
          style: {
            paddingRight: 0,
            paddingLeft: '18px',
          },
          render: (h, params) => {
            const { row } = params;
            return h(
              'div',
              { class: 'demo-upload-list', style: { border: 'none' } },
              [
                <img
                  style="width: 36px;"
                  src={
                    (row.logo ||
                      'https://base-image.sdongpo.com/base_1550/upload_pic/default_2x_com_thumb_2024101816421846e865ff67121f6a2d06e.png') +
                    '!40x40'
                  }
                />,
                h(
                  'div',
                  {
                    class: 'demo-upload-list-cover',
                  },
                  [
                    h(
                      'Icon',
                      {
                        props: {
                          type: 'ios-eye-outline',
                        },
                        on: {
                          click: () => this.previewImage(row.logo + '!400x400'),
                        },
                      },
                      [],
                    ),
                  ],
                ),
              ],
            );
          },
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left',
          width: 290,
          fixed: 'left',
          resizable: true,
          style: {
            paddingLeft: '15px',
          },
          cellContentStyle: {
            width: '100%'
          },
          sortable: true,
          render: (h, params) => {
            const { row, index } = params;
            const activityIcon = Goods.getActivityIcon(row);
            let iconStyle = {
              color: '#cc4916',
              marginRight: '6px',
              border: '1px solid #cc4916',
              borderRadius: '3px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
            };

            const smartWarning = row._isSmart && !row.commodity_id;
            const inputBorder = smartWarning ? '1px solid #F13130' : 'none'; // 默认边框
            const inputProps = {
              type: 'textarea',
              rows: 1,
              autosize: { minRows: 1, maxRows: 2.2 }
            }

            return (
              <div style="display: flex; align-items: center">
                {row._isSmart && (
                  <img
                    width="14px"
                    height="14px"
                    src={aiIcon}
                    class="smart-logo"
                  />
                )}
                {(row.new && <span class="new-logo">新</span>) ||
                  (row.activity_type_desc && (
                    <img
                      style="height: 18px; padding-right: 10px"
                      src={activityIcon}
                    >
                      {row.activity_type_desc}
                    </img>
                  )) ||
                  (row.unit_sell && row.unit && row.unit !== row.unit_sell && (
                    <div style={iconStyle}>多规格</div>
                  ))}
                <SdpTableStaticFormWrap
                  displayValue={{
                    value: row.name,
                    placeholder: '商品名/编码/别名/关键字',
                    style: { width: '100%', border: inputBorder },
                    class: 'commodity-select',
                    ...inputProps
                  }}
                  scopedSlots={{
                    default: ({
                      handleUpdateActiveStatus,
                      handleDeactivate,
                    }) => (
                      <CommoditySelect
                        class="commodity-select"
                        commodityName={row.name}
                        params={{
                          is_show_activity_unit_price: 1,
                          user_id: this.user.id,
                          delivery_date: this.delivery_date,
                          is_online: this.createOrderShowOfflineGoods
                            ? ''
                            : 'Y',
                          query_setting: this.excludeName(),
                          pageSize: 30,
                        }}
                        inputStyle={{
                          border: inputBorder,
                        }}
                        inputProps={inputProps}
                        dataProvider={common.getCommodity}
                        selectedData={this.newOrderList}
                        commodityIdKey="commodity_id"
                        commodityNameKey="commodity_name"
                        onOn-focus={() => this.isUserInput()}
                        onOn-show-dropdown={(show, createGoodsModal) => {
                          if (!createGoodsModal) {
                            const flag = show ? 2 : 1;
                            handleUpdateActiveStatus(flag);
                            if (flag === 1) {
                              handleDeactivate();
                            }
                          }
                        }}
                        onOn-change={(cid, com) => {
                          row.name = com.origin_commodity_name;
                          this._setCommodity(cid, com, row, index);
                        }}
                        onCreateGood={(newGood) => {
                          row.name = newGood.origin_commodity_name;
                          this._setCommodity(
                            newGood.commodity_id,
                            newGood,
                            row,
                            index,
                          );
                        }}
                        onOn-enter={() => this._addGoods(index)}
                        style={{ width: '100%' }}
                        slot-type="order-is_online"
                        selectType="table"
                        isOpenCreated={true}
                      ></CommoditySelect>
                    ),
                  }}
                ></SdpTableStaticFormWrap>
              </div>
            );
          },
        },
        {
          title: '描述',
          key: 'summary',
          ellipsis: true,
          align: 'left',
          minWidth: 120,
        },
        {
          title: '现有库存',
          key: 'stock',
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              row.stock ? row.stock + ' ' + row.unit_sell : '--',
            );
          },
        },
        {
          title: '单位',
          key: 'unit',
          align: 'center',
          render: (h, params) => {
            const { row } = params;
            return h('span', row.unit === '未知' ? '--' : row.unit);
          },
        },
        {
          title: '下单数量',
          key: 'order_amount',
          align: 'left',
          width: 200,
          sortable: true,
          style: {
            paddingRight: 0,
          },
          render: (h, params) => {
            const { row, index } = params;
            // 因为有相同商品下单的配置，这里计算库存的时候，要把所有的相同商品数量加起来
            let otherAmount = 0;
            row.amount = Number.isNaN(Number(row.amount)) ? 0 : row.amount;
            this.newOrderList.forEach((item) => {
              if (row.commodity_id === item.commodity_id) {
                otherAmount += Number(item.amount);
              }
            });
            let template = [];
            const inputClassName = [
              'order-amount-input',
              row.amount_warning && 'amount_warning',
              +row.amount === 0 && 'emphasis-tip',
            ];
            const inputStyle = {
              maxWidth: '80px',
              minWidth: '50px',
            };
            const numberInput = h(NumberInput, {
              class: inputClassName,
              props: {
                value: Number(row.amount),
                min: 0,
                max: 999999999.99,
                precision: 2,
              },
              style: inputStyle,
              on: {
                'on-focus': () => {
                  this.postList[index]['amount_warning'] = row[
                    'amount_warning'
                  ] = false;
                  this.newOrderList[index]['amount_warning'] = false;
                },
                'on-change': (val) => {
                  this.postList[index]['amount'] = row.amount = val;
                  if (row._smartSuccess) {
                    this.newOrderList[index]['_smartAmountCount'] = undefined;
                  }
                  this.newOrderList[index]['amount'] = row.amount = val;
                  this._updateStepPricing(row, index); // 更新阶梯定价价格
                  this.handleChangeNum(row, index);
                },
                'on-blur': () => {
                  this.newOrderList[index]['amount'] = row.amount;
                  this.newOrderList[index]['price'] = row.price;
                  this.newOrderList[index]['sales_num'] =
                    this.postList[index]['sales_num'];
                },
                'on-enter': () => {
                  this.newOrderList[index]['amount'] = row.amount;
                  this._addGoods(index);
                },
              },
            });

            let orginAmountInput = h('div', {}, [
              h(
                'div',
                {
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                  },
                },
                [
                  h(
                    SdpTableStaticFormWrap,
                    {
                      props: {
                        displayValue: {
                          value: row.amount,
                          style: inputStyle,
                          class: inputClassName,
                        },
                      },
                    },
                    [numberInput],
                  ),
                  row._smartSuccess && row._smartAmountCount
                    ? h(
                        Tooltip,
                        {
                          props: {
                            transfer: true,
                            content: row._smartAmountCount,
                            placement: 'top',
                            maxWidth: 200,
                          },
                        },
                        [
                          h(SIcon, {
                            class: {
                              'ml-2': true,
                              'ml-2': true,
                            },
                            style: {
                              marginLeft: '2px',
                            },
                            props: {
                              icon: 'help1',
                              size: 12,
                            },
                          }),
                        ],
                      )
                    : null,
                  this.isStepPricingGoods(row) ? (
                    <StepPricingPoptip goods={row}></StepPricingPoptip>
                  ) : null,
                ],
              ),
              h(
                'div',
                {
                  style: { color: '#ff6e00', fontSize: '12px' },
                  class: { dn: +row.amount !== 0 },
                },
                '请注意特殊下单数量！',
              ),
              h(
                'span',
                {
                  class: {
                    dn:
                      !row.is_sell_stock_alert ||
                      otherAmount <= Number(row.sell_stock),
                  },
                  style: {
                    color: 'red',
                  },
                },
                '库存不足',
              ),
            ]);
            template.push(orginAmountInput);
            return template;
          },
        },
        {
          title: '下单单价',
          key: 'unit_price',
          sortable: true,
          align: 'left',
          width: 160,
          render: (h, params) => {
            const { row, index } = params;
            const key = 'price';
            const protocol = h(SIcon, {
              class: '',
              props: {
                icon: 'xie',
                size: 16,
              },
            });
            const tip = h(
              'p',
              {
                style: {
                  color: 'red',
                  fontSize: '12px',
                },
              },
              '低于最近一次进货价！',
            );
            const showWaring = +row.in_price <= 0 && +row[key] === 0;
            const warningTip = h(
              'div',
              {
                style: { color: '#ff6e00', fontSize: '12px' },
              },
              '请注意特殊下单单价！',
            );
            let template = [];
            if (Goods.isDiscountGoods(row)) {
              const discountIcon = h(SIcon, {
                class: 'mr5',
                props: {
                  icon: 'zhe',
                  size: 16,
                },
                style: 'color: #ff6600;',
              });
              template.push(discountIcon);
            }
            // if (this.isShowSyncContractPrice) {
            // 下单时改价的权限
            if (this.hasAuthority('A002001012')) {
              if (Goods.isProtocolGoods(row) && !this.showContractPriceTips) {
                protocol.data.class = 'mr5';
                template.push(protocol);
              }
              const inputValue = row[key];
              const inputStyle = { width: '95px' };

              const numberInput = h(NumberInput, {
                style: inputStyle,
                class: {
                  'emphasis-tip': showWaring,
                },
                props: {
                  value: inputValue,
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                on: {
                  'on-change': (value) => {
                    this.newOrderList[index][key] = row[key] = value;
                    this.postList[index][key] = row[key] = value;
                    this.computeUserContractPrice(row, agreementPriceKey, 4);
                    this.updateStoreListPrice(params);
                    this.handleChangeNum(row, index);
                    this.newOrderList[index].org_price = row.org_price;
                  },
                  'on-enter': () => {
                    this.newOrderList[index][key] = row[key];
                    this._addGoods(index);
                  },
                },
              });

              template.push(
                h(
                  SdpTableStaticFormWrap,
                  {
                    props: {
                      displayValue: {
                        value: inputValue,
                        style: inputStyle,
                        class: {
                          'emphasis-tip': showWaring,
                        },
                      },
                    },
                    style: {
                      display: 'inline-block',
                      width: '95px',
                    },
                  },
                  [numberInput],
                ),
              );
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              if (showWaring) {
                template.push(warningTip);
              }
              return template;
            } else {
              template = [h('span', row[key] || '--')];
              if (Goods.isProtocolGoods(row)) {
                protocol.data.class = 'ml5';
                template.push(protocol);
              }
              if (row.in_price - row[key] > 0) {
                template.push(tip);
              }
              return h(
                'div',
                {
                  style: {
                    padding: '5px 0',
                  },
                },
                template,
              );
            }
          },
        },
        {
          title: '下单小计',
          key: 'sub_total_price',
          align: 'left',
          sortable: true,
          width: 120,
          render: (h, params) => {
            const { row, index } = params;
            const key = 'sub_total_price';
            // 如果开启了小计支持修改且有改价权限，可以输入
            if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
              let inputValue = params.row[key];
              // NumberInput里面默认值是1,  下面给SdpTableStaticFormWrap用的
              if (inputValue === undefined) inputValue = 1;

              const numberInput = h(NumberInput, {
                props: {
                  value: inputValue,
                  min: 0,
                  max: 999999999.99,
                  precision: 2,
                },
                on: {
                  'on-change': (val) => {
                    params.row[key] = val;
                    this.newOrderList[params.index][key] = val;
                    this.postList[params.index][key] = val;
                    this.computeOfSubtotalModify(params, false);
                  },
                  'on-blur': (event) => {
                    this.computeOfSubtotalModify(params, true);
                  },
                  'on-enter': () => {
                    this.newOrderList[index][key] = row[key];
                    this._addGoods(index);
                  },
                },
              });
              return h(
                SdpTableStaticFormWrap,
                {
                  props: {
                    displayValue: {
                      value: inputValue,
                    },
                  },
                },
                [numberInput],
              );
            }
            let subTotalPrice = (+row.amount).mul(+row.price);

            return h(
              'span',
              {
                class: {
                  'total-amount': true,
                },
              },
              Number.isNaN(subTotalPrice) ? '-' : subTotalPrice.toFixed(2),
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          minWidth: 100,
        },
        {
          title: '条形码',
          key: 'barcode',
        },
        {
          title: '分类',
          key: 'category_name',
          minWidth: 120,
          render: (h, params) => {
            const {
              row: { category_name, category_name2, category_name3 },
            } = params;
            return (
              <span sdp-no-replace>
                {category_name
                  ? `
                    ${category_name}
                    ${category_name2 ? `/${category_name2}` : ''}
                    ${category_name3 ? `/${category_name3}` : ''}
                    `
                  : '--'}
              </span>
            );
          },
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          minWidth: 120,
        },
        {
          title: '售卖库存',
          key: 'sell_stock_text',
          align: 'right',
        },
        {
          title: '实收状态',
          width: 200,
          key: 'un_confirm_receive',
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            let selectItem = [
              {
                label: '待实收',
                value: 1,
              },
              {
                label: '已实收',
                value: 0,
              },
            ];
            return h(
              'Select',
              {
                props: {
                  value: row.un_confirm_receive,
                  transfer: true,
                },
                on: {
                  'on-change': (val) => {
                    this.postList[index]['un_confirm_receive'] =
                      row.un_confirm_receive = val;
                  },
                },
              },
              selectItem.map((item) => {
                return h('Option', {
                  props: {
                    value: item.value,
                    label: item.label,
                  },
                });
              }),
            );
          },
        },
        {
          title: '订单商品标签',
          align: 'left',
          width: 150,
          key: 'order_commodity_tag',
          render: (h, { row, index }) => {
            const key = 'order_commodity_tag';
            const getDefaultTag = () => {
              let defaultOption = this.orderGoodsTagList.find(
                (item) => item.is_default === '1',
              );
              let tagId = defaultOption ? defaultOption.id : '';
              this.postList[index][key] =
                this.newOrderList[index][key] =
                row[key] =
                  tagId;
              return tagId;
            };
            const select = h(mvSelect, {
              attrs: {
                clearable: true,
                'sdp-no-replace': true,
                placeholder: '请选择',
                transfer: true,
              },
              style: {
                width: '100px !important',
              },
              class: {
                'required-tip':
                  row[key] === '' && this.isOrderCommodityTagRequired,
              },
              props: {
                JsonData: this.orderGoodsTagList,
                defaultVal:
                  row[key] !== undefined
                    ? row[key]
                    : this.showCreateOrderLastOcGag
                      ? ''
                      : getDefaultTag(),
              },
              on: {
                'on-change': (tagId, item) => {
                  this.postList[index][key] =
                    this.newOrderList[index][key] =
                    row[key] =
                      tagId || '';
                },
                'on-open-change': (isOpen) => {
                  setTimeout(() => {
                    this.newOrderList[index].isSelectOpen = isOpen;
                  }, 100);
                },
                'on-enter-key-up': () => {
                  if (!this.newOrderList[index].isSelectOpen)
                    this._addGoods(index);
                },
              },
            });
            return select;
          },
        },
        {
          width: 100,
          title: '折扣率(%)',
          key: 'discount',
          align: 'left',
          render: (h, params) => {
            const { row, index } = params;
            const key = 'discount';
            return h(NumberInput, {
              props: {
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.postList[params.index][key] = row[key] = value;
                  this.computeUserContractPrice(row, discountRatioKey, 4);
                  this.updateStoreListPrice(params);
                  this.handleChangeNum(row, index);
                },
                'on-enter': () => {
                  this.newOrderList[index][key] = row[key];
                  this._addGoods(index);
                },
              },
            });
          },
        },
        {
          width: 110,
          title: '协议市场价',
          key: 'org_price',
          align: 'left',
          render: (h, params) => {
            const { row, index } = params;
            const key = 'org_price';
            return h(NumberInput, {
              props: {
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.postList[params.index][key] = row[key] = value;
                  this.computeUserContractPrice(params.row, marketPriceKey);
                  this.updateStoreListPrice(params);
                  this.handleChangeNum(row, index);
                },
                'on-enter': () => {
                  this.newOrderList[index][key] = row[key];
                  this._addGoods(index);
                },
              },
            });
          },
        },
        {
          title: '最近一次进价',
          key: 'in_price',
          filterMultiple: false,
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            const defaultFilter = this.priceType;
            return h(HeaderFilter, {
              props: {
                filterList,
                defaultFilter,
              },
              on: {
                'on-change': (_value) => {
                  this.handleChangePriceType(_value);
                },
              },
            });
          },
          render: (h, params) => {
            const { row } = params;
            let key = '';
            switch (this.priceType) {
              case '1':
                key = 'in_price';
                break;
              case '2':
                key = 'store_in_price';
                break;
              case '3':
                key = 'average_price';
                break;
              case '4':
                key = 'last_receipt_price';
                break;
              default:
                break;
            }
            // 根据选择的价格模式显示不同的数据
            return h('span', row[key] || '--');
          },
        },
        {
          title: '参考毛利',
          key: 'reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.price - params.row[priceTypeMap[this.priceType]]
            ).toFixed(2);
            return h('span', Number.isNaN(profit) ? '0' : profit);
          },
        },
        {
          title: '参考毛利总额',
          key: 'total_reference_profit',
          align: 'right',
          render: (h, params) => {
            // 参考毛利=下单单价-参考成本价
            const profit = (
              params.row.price - params.row[priceTypeMap[this.priceType]]
            ).toFixed(2);
            // 参考毛利总额=下单数量*参考毛利
            const total = Number(
              +(Number.isNaN(profit) ? '0' : profit) * +params.row.amount,
            ).toFixed(2);
            return h('span', Number.isNaN(total) ? '0' : total);
          },
        },
        {
          title: '参考毛利率',
          key: 'reference_profit_rate',
          width: 100,
          render: (h, params) => {
            // 参考毛利率=参考毛利/下单单价*100%
            if (!+params.row.price) return h('span', '0%');
            let profit =
              params.row.price - params.row[priceTypeMap[this.priceType]];
            let rate = ((profit / params.row.price) * 100).toFixed(2);
            return h('span', Number.isNaN(rate) ? '-' : rate + '%');
          },
        },
        {
          title: '最近一次下单单价',
          key: 'last_price',
          align: 'right',
        },
        {
          width: 80,
          title: '税率',
          key: 'tax_rate_desc',
          sortable: true,
          align: 'right',
          render: (h, params) => h('span', params.row.tax_rate || 0),
        },
        {
          width: 80,
          title: '税额',
          key: 'tax_rate_price',
          sortable: true,
          align: 'right',
          render: (h, params) => {
            const { row } = params;
            let taxRatePrice = +row.tax_rate
              ? (
                  (((row.amount * row.price) / (1 + row.tax_rate / 100)) *
                    Number(row.tax_rate)) /
                  100
                ).toFixed(2)
              : 0;
            return h('span', {}, taxRatePrice.toFixed(2));
          },
        },
        {
          title: '商品备注',
          key: 'remark',
          align: 'left',
          minWidth: 90,
          render: (h, params) => {
            const { row, index } = params;
            return h('i-input', {
              props: {
                value: row.remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': () => {
                  this.newOrderList[index]['remark'] = row['remark'];
                  this._addGoods(index);
                },
              },
              nativeOn: {
                change: ($event) => {
                  const value = $event.target.value;
                  this.postList[index].remark = row.remark = value;
                  this.newOrderList[index]['remark'] = value;
                },
              },
            });
          },
        },
        {
          title: '内部备注',
          key: 'inner_remark',
          align: 'left',
          minWidth: 90,
          renderHeader: (h) => {
            return h('span', '内部备注');
          },
          render: (h, params) => {
            const { row, index } = params;
            return h('i-input', {
              props: {
                value: row.inner_remark,
              },
              class: {
                remarks: true,
              },
              on: {
                'on-enter': () => {
                  this.newOrderList[index]['inner_remark'] =
                    row['inner_remark'];
                  this._addGoods(index);
                },
              },
              nativeOn: {
                change: ($event) => {
                  const value = $event.target.value;
                  this.postList[index].inner_remark = row.inner_remark = value;
                  this.newOrderList[index]['inner_remark'] = value;
                },
              },
            });
          },
        },
        {
          title: '客户商品别名',
          key: 'user_commodity_alias_name',
        },
        {
          title: '绑定农批市场',
          minWidth: 140,
          key: 'bind_wholesale_market',
        },
        {
          title: '加单数量',
          key: 'changes_num',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单数量',
            );
          },
          render: (h, params) => {
            const { row, index } = params;
            let key = 'changes_num';
            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                value: row[key],
              },
              on: {
                'on-change': (value) => {
                  this.postList[index][key] = row[key] = value;
                  this.handleChangeNum(row, index);
                },
              },
            });
          },
        },
        {
          title: '加单金额',
          key: 'changes_price',
          minWidth: 140,
          align: 'right',
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '加单金额',
            );
          },
        },
        {
          title: '销售数量',
          key: 'sales_num',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售数量',
            );
          },
        },
        {
          title: '销售单价',
          key: 'sales_unit_price',
          align: 'right',
          minWidth: 100,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售单价',
            );
          },
        },
        {
          title: '销售金额',
          key: 'sales_price',
          align: 'right',
          minWidth: 140,
          renderHeader: (h) => {
            return h(
              'span',
              {
                class: 'InternalNote',
              },
              '销售金额',
            );
          },
        },
        {
          title: '品牌',
          key: 'brand',
          minWidth: 100,
        },
        {
          title: '分账供应商',
          key: 'account_provider_name',
        },
        {
          title: '分账比例',
          key: 'account_ratio',
          render: (h, { row }) => {
            return h(
              'span',
              Number(row.account_ratio) ? row.account_ratio + '%' : '-',
            );
          },
        },
        {
          title: '菜谱套餐',
          key: 'raw_recipe_package_names',
        },
        {
          title: '客户商品别名编码',
          key: 'user_commodity_alias_code',
        },
        {
          title: '商品状态',
          key: 'is_online',
          render: (h, { row }) => {
            return h(
              'span',
              row.commodity_id ? (row.is_online === 'Y' ? '上架' : '下架') : '--',
            );
          },
        },
        {
          title: '实时采购员/供应商',
          key: 'now_agent_provider',
          width: 160,
          tip: '实时读取当前客户对应商品 默认的采购员/供应商信息',
        },
      ],
      priceType:
        window.localStorage.getItem('order_base_price_mode') ||
        filterList[0].value,
      attachmentFiles: [],
      agreementPriceSelectModal: {
        show: false,
        data: [],
        confirm: (selectedId) => {
          this.createOrder(this.addMode, {
            sync_protocol_id: selectedId,
            sync_protocol: 1,
          });
          this.agreementPriceSelectModal.show = false;
        },
        cancel: (selectedId) => {
          this.agreementPriceSelectModal.show = false;
        },
      },
      addMode: 1,
      orderGoodsTagList: [],
      importPost: {
        url: '/superAdmin/orderSuper/ImportOrderCommodity',
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xls', 'xlsx'],
      },
      importDownParams: {
        template_type: '0',
        type: '0',
        price_mode: '0',
      },
      importCol: [
        {
          title: '错误提示',
          key: 'content',
          width: 200,
          render: (h, params) => {
            var data = params.row;
            return h(
              'span',
              {
                style: {
                  color: 'red',
                  'white-space': 'normal !important',
                },
              },
              data.content,
            );
          },
        },
        {
          title: '错误行数',
          key: 'line',
          align: 'center',
        },
        {
          title: '商品名称',
          key: 'commodity_name',
        },
        {
          title: '用户编码',
          key: 'user_code',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              row.user_name !== '' ? row.user_name : row.user_code,
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '备注',
          key: 'remark',
        },
      ],
      importError: '',
    };
  },
  watch: {
    userHasContractPriceOrder() {
      this.handleSetCols();
    },
  },
  created() {
    // 1220暂时不上 客户推荐
    // if(!this.$route.query.copyId){
    //   this.searchUser()
    // }
    this.changeAgreementPriceKey(agreementPriceKey);
    this.getSearchUser = debounce(this.searchUser, 300);
    this.handleKeyDown = debounce((event) => this.handlePrint(event), 500);
    this.getDate();
    this.getDeliveryTimeList();
    this.getOrderGoodsTagList();
    window.addEventListener('keydown', this.handleKeyDown);

    this._initColumns();
    //获取列表页复制来的id
    let id = this.$route.query.copyId;
    const orderNewData = StorageUtil.getLocalStorage('ORDER_NEW_DATA');
    if (id != undefined) {
      this.getOrderDetail(id);
      this.ifFromCopy = true;
    } else if (orderNewData) {
      this.$Modal.confirm({
        title: '提示',
        content: '已保存本地离线数据，是否恢复？',
        onOk: () => {
          StorageUtil.removeLocalStorage('ORDER_NEW_DATA');
          this.getOrderNewData(orderNewData);
        },
        onCancel: () => {
          StorageUtil.removeLocalStorage('ORDER_NEW_DATA');
          this._initTableData();
          this.getOrderCustomizeFieldKeys();
        },
      });
    } else {
      this._initTableData();
      this.getOrderCustomizeFieldKeys();
    }
    // 得到标签配置，是否可以添加多个标签，是否只能添加一个标签
    this.commonService.getConfig().then((config) => {
      const {
        is_open_order_tag_required: must_tag,
        is_open_order_tag_only_one: only_tag,
      } = config;
      this.must_tag = must_tag;
      this.only_tag = only_tag;
    });
  },
  beforeDestroy() {
    window.removeEventListener('keydown', this.handleKeyDown);
  },

  mounted() {
if (Number(this.sysConfig.is_open_ai_chat_dialog)){
      initPage({
            // pageContext: `当前页面路由为"/order/list", 页面名称为"订单列表", 支持查询 search 操作，你需要先调用this.$ref.orderListTable.setValue(['createStartTime', 'createEndTime'],时间范围,true)，然后再调用this.fetchData()方法，描述为："根据下单时间查询订单信息，请把时间转成时间范围：例如：2021-03-01 13:06至 2021-03-07 11:15，"`,
            context: {
              pageContext: `当前页面路由为"/order/new"
              页面名称为"新增订单"，支持新增订单信息 "addOrder" 工具操作，
              当需要进行新增订单操作时 先和用户确认客户名称和商品信息，
              确认完成之后再进行工具调用操作
              返回的格式为：{customerName: "用户输入的客户名称", goodsInfo: "用户输入的商品信息（例如：茄子10斤）"}。注意：请严格按照此格式返回！
              如果有用户信息你就调用addOrder，没有就提示"请先输入客户"，
              pageData表示当前页面的所有数据`,
              pageData: this.$data,
            },
            actions: {
              addOrder: (params, cb) => {
                console.log('params', params);
                // this.customerName = params.customerName;
                this.handleSetname(params.customerName, params.goodsInfo);
                cb();
              },
            },
            tools: [
              {
                name: 'addOrder',
                description:
                  '当前工具必须要在新增订单页面 页面路由为/order/new 才能使用，如果不是此路由则先使用工具跳转到此页面才可以进行使用 当得到了客户信息之后可以使用新增订单的工具，接收订单信息比如茄子50斤,2斤苹果之类的 如果没有客户信息就提示"请先输入客户 如果没有商品信息就提示"请先输入商品信息"',
                parameters: {
                  type: 'object',
                  properties: {
                    customerName: {
                      type: 'string',
                      description: '客户名称',
                    },
                    goodsInfo:{
                      type:'string',
                      description: '商品信息'
                    }
                  },
                  required: ['customerName','goodsInfo'],
                  returnType: {
                    type: 'object',
                    description: '新增订单结果',
                  },
                },
              }
            ],
            toolCallback: async(toolName, params) => {
              console.log('toolName', toolName, 'params', params);

              switch (toolName) {
                case 'getCustomerInputValue':
                  return params;
                case 'addOrder':
                console.log('addOrder123',params)
                const  result = await this.handleSetname(params.customerName, params.goodsInfo);
                console.log('result',result)
                return result;
                default:
                  return '没有对应工具方法';
              }
            },
      })
    }

    this.$refs.userInput.focus();
    this.getInitData();
    // 获取自提列表, 是否有数据
    this.getPickUpList();

    window.addEventListener('unload', this.setStoreData);
  },
  beforeRouteLeave(to, from, next) {
    if (this.$refs.orderSmartModal && this.$refs.orderSmartModal.show) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>系统正在进行AI识别，您确定放弃等待吗？</p>',
        onOk: () => {
          this.$refs.orderSmartModal.close();
          return next();
        },
        onCancel: () => {
          next(false);
        },
      });
    } else if (
      this.user.id ||
      this.newOrderList.filter((item) => item.commodity_id).length
    ) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: () => next(),
        onCancel: () => {
          next(false);
        },
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  computed: {
    ...mapState({
      isOrderClose: (state) => state.isOrderClose,
    }),
    isOnlinePay() {
      return +this.sysConfig.is_online_pay === 1;
    },
    isUserSupportOnlinePay() {
      return this.user.detail && +this.user.detail.is_support_online_pay === 1;
    },
    payWayList() {
      const payWayList = [
        {
          label: '货到付款',
          value: '0',
        },
      ];
      if (this.isUserSupportOnlinePay) {
        payWayList.push({
          label: '在线支付',
          value: '1',
        });
      }
      return payWayList;
    },
    // 小计修改计算模式
    totalPriceUpdateMode() {
      return +this.sysConfig.total_price_update_mode;
    },
    // 是否开启 AI录单
    aiCreateOrder() {
      return +this.sysConfig.ai_order_create;
    },
    disabledEverything() {
      return this.user.id === '';
    },
    calTotal() {
      const postList = this.postList.filter((goods) => goods.commodity_id);
      if (this.isOpenSubtotalModify && hasAuthority('A002001012')) {
        return postList
          .reduce((prev, next) => prev.add(+next.sub_total_price), 0)
          .toFixed(2);
      }
      return postList
        .reduce((prev, next) => prev.add((+next.amount).mul(+next.price)), 0)
        .toFixed(2);
    },
    orderTotalNum() {
      let num = this.postList
        .filter((goods) => goods.commodity_id)
        .reduce((prev, next) => prev.add(next.amount), 0)
        .toFixed(2);
      return Number.isNaN(Number(num)) ? 0 : num;
    },
    totalReferenceProfit() {
      let allTotal = 0;
      this.postList
        .filter((goods) => goods.commodity_id)
        .forEach((item) => {
          // 参考毛利=下单单价-参考成本价
          const profit = (
            item.price - item[priceTypeMap[this.priceType]]
          ).toFixed(2);
          // 参考毛利总额=下单数量*参考毛利
          const total = Number(
            +(Number.isNaN(profit) ? '0' : profit) * +item.amount,
          ).toFixed(2);
          allTotal = Number(
            allTotal + +(Number.isNaN(total) ? '0' : total),
          ).toFixed(2);
        });
      return allTotal;
    },
    isShowSyncContractPrice() {
      return (
        this.isEnableOrderSyncContractPrice && this.userHasContractPriceOrder
      );
    },
    isCanOrder() {
      if (!this.user.id) {
        return false;
      }
      // 超出账期允许后台下单
      if (this.OrdersBeyondPaymentDaysAreAllowed) {
        return true;
      }
      // 超出账期不允许后台下单，且当前客户超出了账期
      if (!this.OrdersBeyondPaymentDaysAreAllowed && this.userMessage) {
        return false;
      }
      return true;
    },
    // 是否包含阶梯定价商品
    hasStepPricingGoods() {
      return this.newOrderList.find((goods) => this.isStepPricingGoods(goods));
    },
    // 是否允许添加相同商品
    isEnableAddSameGoods() {
      return (
        this.isAddSameCommoditySplitOrder ||
        this.is_open_order_add_same_commodity
      );
    },
    showCreateOrderLastOcGag() {
      return Number(this.sysConfig.show_create_order_last_oc_tag) === 1;
    },
    customerInputValue: {
      get() {
        return (
          this.user.name &&
          `${this.user.name}${this.user.receivable_style_name ? `（${this.user.receivable_style_name}）` : ''}`
        );
      },
      set(val) {
        // 手动改变则置空
        this.user.receivable_style_name = '';
        this.user.name = val;
      },
    },
  },
  methods: {
    handleSetname(customerName = '', orderInfo = '') {
      if (!customerName.trim() || !orderInfo.trim()) {
        this.errorNotice('请先输入客户名称或订单信息！');
        return '请先输入客户名称或订单信息！';
      }
      this.user.id = this.user.uid = ''; // 每次重新搜索必须清空
      this.user.name = customerName;
      const consortium_search_type = this.consortium_pricing_type == 3 ? '' : 1;
      common.getUserFull(this.user.name, consortium_search_type).then((res) => {
        console.log('res', res);

        if (res.status) {
          var data = res.data;
          this.userList = data;
          let isFindUser = false;

          // 加上排序
          for (let i = 0; i < this.userList.length; i++) {
            if (!this.userList[i].uid) {
              this.userList[i].uid = i;
            }
            if (this.userList[i].email.indexOf(customerName) !== -1) {
              this.addUser(this.userList[i]);
              isFindUser = true;
            }
          }
          if (!isFindUser) {
            this.errorNotice('没有找到该客户，请重新输入！');
            return '没有找到该客户，请重新输入！';
          }

          if (data && data.length) {
            this.userDropDown = true;
            this.$refs.userInput.focus();
            this.userKeyControll();
          } else {
            this.userDropDown = false;
          }
          let firstUser = data[0] || {};
          // 下拉列表默认选中第一个用户
          this.user.default_item = firstUser;
          this.user.uid = firstUser.uid;
          this.setUserScrollTop();
          this.$refs.orderSmartModal.open();
          this.$refs.orderSmartModal.userId = this.user.id;
          this.$refs.orderSmartModal.originalData = orderInfo;
          this.$refs.orderSmartModal.save();
          console.log(
            'this.$refs.orderSmartModal',
            this.$refs.orderSmartModal.test_user_id,
            this.user.id,
          );
          return `成功调用此方法 现在需要用户手动操作提交`;
        }
      });
      this.customerInputValue = customerName;
      // this.searchUser()
      // this.getSearchUser()
      // this.enterUser()
      // console.log('userList', this.userList);
    },
    handleSmartStart() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval); // 清除已有的定时器
      }
      this.percent = 5; // 设置起始进度
    },

    // 结束时归零并重新开始
    handleSmartEnd() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval); // 清除已有的定时器
      }
      this.percent = 110; // 设置结束进度，触发过渡
      setTimeout(() => {
        this.percent = 0; // 归零
      }, 1000); // 延迟500ms，给人一种平滑的结束效果
    },
    handleColsChange(cols) {
      this.totalReferenceProfitShow = cols.includes('total_reference_profit');
    },
    handleOrginalData(orginalData) {
      this.smartOrginalData = orginalData;
    },
    handleTagLabelShow(tagLabelShow) {
      console.log('handleTagLabelShow', tagLabelShow);
      setTimeout(() => {
        this.showTagLabel = tagLabelShow;
      }, 500);
    },
    // 处理商品列表，模拟进度条递增
    handleSmartGoodsList(list) {
      console.log('handleSmartGoodsList', list);
      // 每次有新数据进来时，直接添加到待添加队列中
      if (!this.isAddingGoods) {
        this._addSmartGoods(list);
      } else {
        // 如果当前正在添加商品，将新数据加入队列
        this.goodsQueue.push(...list);
      }

      if (this.percent >= 100) {
        return; // 防止进度超出 100
      }

      // 平滑递增
      this.incrementProgress();
    },

    // 递增进度条的方法
    incrementProgress() {
      // 每次递增的数值
      const increment = 5;
      const targetPercent = 90;

      // 如果进度小于 100，启动递增
      if (this.percent < targetPercent) {
        if (this.progressInterval) {
          clearInterval(this.progressInterval); // 清除已有定时器
        }

        // 启动定时器，逐步增加进度
        this.progressInterval = setInterval(() => {
          if (this.percent < targetPercent) {
            this.percent += increment; // 每次递增 10
          } else {
            clearInterval(this.progressInterval); // 到达目标后清除定时器
          }
        }, 200); // 每 200 毫秒递增一次，调节这个间隔来控制动画流畅度
      }
    },
    updateTableHeight(isFullscreen, screenHeight) {
      this.isFullscreen = isFullscreen;
      if (!isFullscreen) {
        this.tableHeight = getEditTableHeight();
      } else {
        this.tableHeight = screenHeight - 94;
      }
    },
    handleShowPick() {
      this.$refs.pickUpModal.open();
    },
    // 获取自提点列表
    async getPickUpList() {
      const { status, message, data } = await this.$request.get(
        this.apiUrl.SelfPickupPointList,
      );
      if (status && data && data.length) {
        this.hasPickUpList = data;
      }
    },
    handleChangePick(item) {
      this.pickInfo = item;
    },
    excludeName() {
      let arr = ['commodity_code', 'bar_code'];
      this.goodNameSearch.forEach((res) => {
        let index = arr.findIndex((ee) => ee == res);
        if (index > -1) {
          arr.splice(index, 1);
        }
      });
      return {
        query_exclude: arr.join(','),
        only_bar_code: this.isRightMatch,
      };
    },
    confirmSet(value) {
      StorageUtil.setLocalStorage('isRightMatch', this.isRightMatch);
      StorageUtil.setLocalStorage('goodNameSearch', this.goodNameSearch);
    },
    searchSet() {
      this.isShowSet = true;
    },
    hasAuthority,
    getInitData() {
      common.getPurchaseType({}).then((res) => {
        if (res.status) {
          this.initData = res.data || {};
        }
      });
    },
    previewImage(image, _images, _viewIndex = 0) {
      this.viewImage(image, _viewIndex);
    },
    getEditTableHeight,
    _onDraggableData(data) {
      this.newOrderList = data;
      this.setSortNum();
      this.postList = this.deepClone(this.newOrderList);
    },
    async _initColumns() {
      const customFieldKeys = await this.setCustomizeFieldKeys();
      this.originCols = this.originCols.concat(customFieldKeys);
      this.handleSetCols();
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '0,4',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                // 商品自定义字段，只需要展示
                if (+item.customize_type === 0) {
                  return {
                    title: item.name,
                    key: item.key,
                  };
                }
                // 订单自定义字段，需要可编辑
                if (+item.customize_type === 4) {
                  this.goodsCustomizeField.push(item);
                  return {
                    title: item.name,
                    key: item.key,
                    render: (h, params) => {
                      let row = params.row;
                      let index = params.index;
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        on: {
                          'on-enter': () => {
                            this.newOrderList[index][item.key] = row[item.key];
                          },
                        },
                        nativeOn: {
                          change: ($event) => {
                            console.log('item.keyitem.key', item.key);
                            const value = $event.target.value;
                            this.postList[index][item.key] = row[item.key] =
                              value;
                            console.log();
                            this.newOrderList[index][item.key] = value;
                          },
                        },
                      });
                    },
                  };
                }
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
    // 获取订单自定义字段
    getOrderCustomizeFieldKeys() {
      this.$request
        .get(this.apiUrl.customizeFieldKeys, {
          customize_type: '3',
        })
        .then(({ status, data }) => {
          if (status) {
            data = data || [];
            data.forEach((item) => {
              '';
            });
            this.orderCustomizeField = data;
          }
        });
    },
    _initTableData(list = []) {
      if (list.length === 0) {
        this.newOrderList = [];
        this._addGoods(0);
      } else {
        this.newOrderList = list;
      }
      this.postList = this.deepClone(this.newOrderList);
    },
    _deleteGoods(row, index) {
      if (this.newOrderList.length > 1) {
        this.newOrderList.splice(index, 1);
        this.syncPostList();
        this.infoMessage(row.name + '已删除');
      }
    },

    _handleSmartGoodsNums(goods) {
      // 数据联动模式(false固定销售金额、true固定销售单价)
      if (this.issue_order_data_contact_way) {
        goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=销售单价*销售数量
        goods.sales_price = (+goods.sales_num)
          .mul(+goods.sales_unit_price)
          .toFixed(4);
        let sub_total_price = (+goods.amount).mul(+goods.price);
        // 加单金额=销售金额-下单金额
        goods.changes_price = (+goods.sales_price - sub_total_price).toFixed(2);
      } else {
        // 销售数量=下单数量+加单数量
        goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=下单金额+加单金额
        goods.sales_price =
          (+goods.amount).mul(+goods.price).toFixed(4) +
          Number(goods.changes_price);
        // 销售单价=销售金额/销售数量
        goods.sales_unit_price = (goods.sales_price / goods.sales_num).toFixed(
          2,
        );
      }
      // 更新下单小计
      goods.sub_total_price = (+goods.amount).mul(+goods.price || 1).toFixed(2);
      return goods;
    },
    // 添加商品到列表
    _addSmartGoods(goods) {
      if (this.isAddingGoods) {
        // 如果正在添加商品，将新数据放入队列中等待
        this.goodsQueue.push(...goods);
        return;
      }

      this.isAddingGoods = true; // 设置标志位，表示开始添加商品
      let _index = this.newOrderList.length - 1;
      let newOrderList = this.deepClone(this.newOrderList);

      // 判断新订单列表是否为空或无效
      if (
        newOrderList.length === 1 &&
        !newOrderList[0].commodity_id &&
        !newOrderList[0].name
      ) {
        newOrderList = [];
        _index = _index;
      } else {
        _index = _index + 1;
      }

      let itemIndex = 0; // 当前添加的商品索引
      const totalItems = goods.length; // 总商品数量

      const addOrUpdateItem = (v) => {
        const commodity_id = v.commodity_id ? v.commodity_id : v.id || '';

        // 检查商品是否已存在
        const existingItemIndex = newOrderList.findIndex(
          (existingItem) =>
            existingItem.commodity_id === v.commodity_id &&
            existingItem.commodity_name === v.commodity_name,
        );

        if (existingItemIndex !== -1) {
          // 如果商品已存在，根据配置处理
          if (!this.isEnableAddSameGoods) {
            let _existingItem = newOrderList[existingItemIndex];

            // 更新 _smartAmountCount
            if (_existingItem._smartAmountCount) {
              // 如果 _smartAmountCount 已经存在，拼接新的数量
              _existingItem._smartAmountCount += ` + ${v.num}`;
            } else {
              // 如果没有 _smartAmountCount，初始化
              _existingItem._smartAmountCount = `重复商品合计数量：${_existingItem.amount} + ${v.num}`;
            }
            _existingItem.amount += v.num;

            newOrderList[existingItemIndex] = this._handleSmartGoodsNums(
              this.deepClone(_existingItem),
            );
            // 合并数量
            console.log('_existingItem', _existingItem);
          } else {
            // 添加重复商品（如果允许重复添加）
            createAndAddItem(v, commodity_id);
          }
        } else {
          // 如果商品不存在，直接添加
          createAndAddItem(v, commodity_id);
        }
      };

      const createAndAddItem = (v, commodity_id) => {
        let item = {
          id: v.id,
          commodity_name: v.commodity_name,
          commodity_id: commodity_id,
          cid: commodity_id,
          name: v.commodity_name || v.raw_name,
          amount: v.num,
          _sortNum: _index + 1,
          unit: v.unit,
          ...v,
        };
        item = this._handleSmartGoodsNums(this.deepClone(item));
        item.id += uniqueId('$unique-');
        newOrderList.push(item); // 添加商品到列表
        _index += 1; // 更新索引
      };

      // 用于逐步添加商品
      const addItem = () => {
        if (itemIndex < totalItems) {
          const v = goods[itemIndex];
          addOrUpdateItem(v);
          itemIndex++; // 增加商品索引
          // 使用 requestAnimationFrame 动画效果，平滑添加商品
          requestAnimationFrame(addItem);
        } else {
          // 添加完成，更新数据和同步
          this.showTagLabel = true;
          this.newOrderList = newOrderList;
          this.postList = this.deepClone(this.newOrderList);
          this.isAddingGoods = false; // 设置标志位，表示商品添加完成

          // 如果队列中有待添加的商品，继续添加
          if (this.goodsQueue.length > 0) {
            const nextGoods = this.goodsQueue.splice(0, this.goodsQueue.length);
            this._addSmartGoods(nextGoods); // 递归调用，继续添加队列中的商品
          }
        }
      };

      // 启动添加商品动画
      addItem();
    },
    _addGoods(index) {
      this.newOrderList.splice(index + 1, 0, {
        id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
        commodity_id: '',
        name: '',
        amount: 1,
        price: '',
        un_confirm_receive: 0,
        changes_num: 0,
        changes_price: 0,
        sales_num: 1,
        sales_price: 0,
        _sortNum: index + 1,
        is_online: 'Y',
      });
      this.syncPostList();
      this._focusGoodsInput(index + 1);
    },
    setSortNum() {
      this.newOrderList.forEach((item, index) => {
        item._sortNum = index;
      });
      console.log('_sortNum', this.newOrderList);
    },
    handleSortChange(columns, key, order) {
      console.log('newOrderList-sort-before', this.newOrderList);
      if (key === 'order_amount') {
        // 特殊字段替换
        key = 'amount';
      }
      if (key === 'tax_rate_desc') {
        // 特殊字段替换
        key = 'tax_rate';
      }
      if (key === 'unit_price') {
        key = 'price';
      }
      this.newOrderList = this.newOrderList.sort((a, b) => {
        console.log('sort-item', a[key]);
        if (key === 'sub_total_price') {
          // 特殊字段替换 表格本身没有这个字段，前端运算展示
          a[key] = (+a.amount).mul(+a.price).toFixed(4);
          b[key] = (+b.amount).mul(+b.price).toFixed(4);
        }
        if (key === 'tax_rate_price') {
          a[key] = +a.tax_rate
            ? (
                (((a.amount * a.price) / (1 + a.tax_rate / 100)) *
                  Number(a.tax_rate)) /
                100
              ).toFixed(2)
            : 0;
          b[key] = +b.tax_rate
            ? (
                (((b.amount * b.price) / (1 + b.tax_rate / 100)) *
                  Number(b.tax_rate)) /
                100
              ).toFixed(2)
            : 0;
        }
        if (order === 'asc') {
          if (isNaN(a[key])) {
            return a[key].localeCompare(b[key]);
          } else {
            return a[key] - b[key];
          }
        } else if (order === 'desc') {
          if (isNaN(a[key])) {
            return b[key].localeCompare(a[key]);
          } else {
            return b[key] - a[key];
          }
        } else {
          return a._sortNum - b._sortNum;
        }
      });
      console.log('newOrderList-sort-after', this.newOrderList);
      this.syncPostList();
    },
    goodsPackageModalShow() {
      this.syncPostList();
      this.goodsPackageModal.show = true;
    },
    $_closeGoodsPackageModal() {
      this.goodsPackageModal.show = false;
    },
    dataProcessing(val) {
      const goodsList = [];
      const addGoodsToGoodsList = ({ goods, amount }) => {
        const existGoods = goodsList.find(
          (existGoods) => +existGoods.commodity_id === +goods.commodity_id,
        );
        if (!existGoods) {
          goods = this.deepClone(goods);
          goods.amount = Number(amount);
          goodsList.push(goods);
        } else {
          existGoods.amount += Number(amount).toFixed(4);
        }
      };
      val.forEach((ite) => {
        const packageNum = ite.amount || 0;
        ite.item.forEach((vals) => {
          vals.id = vals.commodity_id;
          if (vals.item) {
            vals.item.forEach((ites) => {
              ites.id = ites.commodity_id;
              addGoodsToGoodsList({
                goods: ites,
                amount: (packageNum * vals.num * ites.num).toFixed(4),
              });
            });
          } else {
            addGoodsToGoodsList({
              goods: vals,
              amount: (packageNum * vals.num).toFixed(4),
            });
          }
        });
      });
      let newOrderList = this.deepClone(this.newOrderList);
      if (newOrderList.length === 1 && !newOrderList[0].commodity_id)
        newOrderList = [];
      if (newOrderList.length > 0) {
        newOrderList.forEach((items) => {
          let find = goodsList.find(
            (val) => +items.commodity_id === +val.commodity_id,
          );
          if (find) {
            items.amount = (Number(find.amount) + Number(items.amount)).toFixed(
              4,
            );
          }
        });
        let arr = [];
        goodsList.forEach((value) => {
          let find = newOrderList.find(
            (ites) => Number(ites.commodity_id) === Number(value.commodity_id),
          );
          if (!find) {
            arr.push(value);
          }
        });
        newOrderList.splice(this.activeRowIndex + 1, 0, ...arr);
        this.activeRowIndex += arr.length;
      } else {
        newOrderList.splice(0, 0, ...goodsList);
        this.activeRowIndex += goodsList.length;
      }
      this.newOrderList = newOrderList;
      this.postList = this.deepClone(newOrderList);
    },
    async $_onSelectGoodsPackage(value) {
      let commodity_id = [];
      let goodsPackage = this.deepClone(value);
      goodsPackage.forEach((ite) => {
        ite.item.forEach((val) => {
          if (val.commodity_id !== undefined) {
            commodity_id.push(val.commodity_id);
          }
          if (val.item) {
            val.item.forEach((v) => {
              if (v.commodity_id !== undefined) {
                commodity_id.push(v.commodity_id);
              }
            });
          }
        });
      });
      let id = Array.from(new Set(commodity_id));
      let params = {
        user_id: this.user.id,
        commodity_id: id.toString(),
        delivery_date: this.delivery_date,
      };
      let { data, status, message } = await this.$request.get(
        this.apiUrl.orderSuperAjaxGetMultiOrderCommodity,
        params,
      );
      if (status) {
        goodsPackage.forEach((ite) => {
          ite.item.forEach((val) => {
            if (val.commodity_id !== undefined) {
              this._newPackageDataProcessing(data, val);
            }
            if (val.item) {
              val.item.forEach((i) => {
                this._newPackageDataProcessing(data, i);
              });
            }
          });
        });
        this.dataProcessing(goodsPackage);
        this.$_closeGoodsPackageModal();
      } else {
        this.modalError(message);
      }
    },
    _newPackageDataProcessing(data, val) {
      let find = data.find((v) => +v.id === +val.commodity_id);
      if (find) {
        val.price = find.price;
        val.logo = find.logo;
        val.stock = find.stock;
        val.discount = find.discount;
        val.org_price = find.org_price;
        val.in_price = find.in_price;
        val.tax_rate = find.tax_rate;
        val.tax_rate_price = find.tax_rate_price;
        val.remark = find.remark;
        val.inner_remark = find.inner_remark;
        val.unit_sell = find.unit_sell;
        val.un_confirm_receive = 0;
      }
      return val;
    },
    handleSetCols() {
      const columns = this.deepClone(this.originCols);

      if (!this.userHasContractPriceOrder) {
        if (columns.some((col) => col.key === 'org_price')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'org_price'),
            1,
          );
        }
        if (columns.some((col) => col.key === 'discount')) {
          columns.splice(
            columns.findIndex((col) => col.key === 'discount'),
            1,
          );
        }
      }
      console.log('handleSetCols');
      this.goodsColumns = columns;
    },
    // 订单标签限制
    checkTag() {
      if (this.orderTagList.length === 0) return true;

      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单最多存在3个标签');
        return false;
      }

      if (this.only_tag === '1' && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop(this.selectedOrderTag.length - 1);
        this.warningMessage('一个订单只能选择一个标签');
        return false;
      }

      if (this.must_tag === '1' && this.selectedOrderTag.length === 0) {
        this.warningMessage('请至少选择一个订单标签');
        return false;
      }
      return true;
    },
    // 获取订单标签列表
    getOrderTagList(user_id) {
      const params = {
        user_id,
      };
      return common.qryOrderTagList(params).then((res) => {
        if (+res.status === 1) {
          if (res.data && Array.isArray(res.data)) {
            this.orderTagList = res.data;
            // this.selectedOrderTag中只保留this.orderTagList存在的标签
            this.selectedOrderTag = this.selectedOrderTag.filter((item) => {
              return this.orderTagList.some((tag) => tag.id === item.id);
            });
          }
        }
      });
    },
    handleRowClick(row, index) {
      this.activeRowIndex = index;
    },
    rowClassName(row, index) {
      let rowClassName = [];
      if (index === this.activeRowIndex) {
        rowClassName.push('sdp-table__tr-highlight');
      }
      if (row._isSmart && row._smartSuccess) {
        rowClassName.push('sdp-table_tr-order-smart-success');
      }

      if (row._isSmart && !row._smartSuccess) {
        rowClassName.push('sdp-table_tr-order-smart-process');
      }
      return rowClassName.join(' ');
    },
    resetUser() {
      this.user = {
        id: '',
        uid: '',
        name: '',
        detail: '',
      };
    },
    /**
     * @description 开启小计支持修改后的计算逻辑
     * @param {Object} params 计算参数
     * @param {Boolean} onBlur 是否是失焦事件，失焦事件主要是处理0值逻辑
     */
    computeOfSubtotalModify(params, onBlur = false) {
      const totalKey = 'sub_total_price';
      const amountKey = 'amount';

      const index = params.index;

      const asyncSetData = (keys, values) => {
        keys.forEach((key, i) => {
          this.newOrderList[index][key] = values[i];
          this.postList[index][key] = values[i];
        });
      };

      // 若单价或数量或小计其中一个数值为0，不论其他两个数值输入多少，均默认为0
      if (onBlur) {
        if (this.totalPriceUpdateMode === 1) {
          if (+params.row.price === 0) {
            params.row[totalKey] = 0;
            params.row[amountKey] = 0;
            asyncSetData([totalKey, amountKey], [0, 0]);
          }
        } else {
          if (+params.row[amountKey] === 0) {
            this.$nextTick(() => {
              params.row[totalKey] = 0;
              params.row.price = 0;
              asyncSetData([totalKey, 'price'], [0, 0]);
            });
          }
        }
        return;
      }
      // 如果是固定单价
      if (this.totalPriceUpdateMode === 1) {
        if (+params.row.price === 0) return;
        params.row[amountKey] = (
          params.row[totalKey] / this.postList[index].price
        ).toFixed(2);
        asyncSetData([amountKey], [params.row[amountKey]]);
      } else {
        if (+params.row[amountKey] === 0) return;
        params.row.price = (Number(params.row[totalKey]).div(params.row[amountKey])).toFixed(2);
        asyncSetData(['price'], [params.row.price]);
      }
      this.computeUserContractPrice(params.row, agreementPriceKey, 4);
      asyncSetData(
        ['org_price', 'discount'],
        [params.row.org_price, params.row.discount],
      );
    },
    handleChangeNum(goods, index) {
      // 数据联动模式(false固定销售金额、true固定销售单价)
      if (this.issue_order_data_contact_way) {
        // 销售数量=下单数量+加单数量
        this.postList[index].sales_num = goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=销售单价*销售数量
        this.postList[index].sales_price = goods.sales_price =
          (+goods.sales_num).mul(+goods.sales_unit_price).toFixed(4);
        let sub_total_price = (+goods.amount).mul(+goods.price);
        // 加单金额=销售金额-下单金额
        this.postList[index].changes_price = goods.changes_price = (
          +goods.sales_price - sub_total_price
        ).toFixed(2);
      } else {
        // 销售数量=下单数量+加单数量
        this.postList[index].sales_num = goods.sales_num = (
          Number(goods.amount) + Number(goods.changes_num)
        ).toFixed(2);
        // 销售金额=下单金额+加单金额
        this.postList[index].sales_price = goods.sales_price =
          (+goods.amount).mul(+goods.price).toFixed(4) +
          Number(goods.changes_price);
        // 销售单价=销售金额/销售数量
        this.postList[index].sales_unit_price = goods.sales_unit_price = (
          goods.sales_price / goods.sales_num
        ).toFixed(2);
      }
      // 更新下单小计
      this.newOrderList[index].sub_total_price =
        this.postList[index].sub_total_price =
        goods.sub_total_price =
          (+this.postList[index].amount).mul(+goods.price).toFixed(2);
    },
    updateStoreListPrice(params) {
      this.newOrderList[params.index].price = this.postList[
        params.index
      ].price = params.row.price;
      this.newOrderList[params.index].discount = this.postList[
        params.index
      ].discount = params.row.discount;
      this.newOrderList[params.index].org_price = this.postList[
        params.index
      ].org_price = params.row.org_price;
    },
    isProtocolGoods(goodsInfo) {
      return Goods.isProtocolGoods(goodsInfo);
    },
    setAddressList() {
      const promiseAll = Promise.all([
        user.getUserDetail(this.user.id),
        user.UserAddressList({ user_id: this.user.id }),
      ]);
      promiseAll.then(([res1, res2]) => {
        let defaultAddress = null;
        let otherAddressList = [];
        if (res1.status) {
          defaultAddress = {
            id: '',
            name: res1.data.name,
            tel: res1.data.tel,
            address_detail: res1.data.address_detail,
            address_attach: res1.data.address_attach,
          };
          this.user.detail = {
            ...this.user.detail, // 保留其他属性
            name: defaultAddress.name,
            tel: defaultAddress.tel,
            address_detail: defaultAddress.address_detail,
            address_attach: defaultAddress.address_attach,
          };
        }
        if (res2.status) {
          otherAddressList = res2.data.map((item) => ({
            id: item.id,
            name: item.receive_name,
            tel: item.receive_tel,
            address_detail: item.address_detail,
            address_attach: item.address_attach,
          }));
        }
        this.addressList = defaultAddress
          ? [defaultAddress].concat(otherAddressList)
          : otherAddressList;
      });
    },
    // 获取复制的商品详情
    getOrderDetail(id) {
      common
        .getModifyOrderDetail(id, true, {
          delivery_date: this.delivery_date,
        })
        .then((res) => {
          if (res.status) {
            var data = res.data.data,
              list = data.commodityList,
              detail = data.order || {};
            this.formData.subsidiary_id = detail.subsidiary_id;
            this.formData.meal_type = detail.meal_type;
            this.formData.split_provider_rule =
              detail.split_provider_rule || '1';
            this.orderCustomizeField = detail.customize_fields || [];
            list.forEach((e, index) => {
              e.amount = e.order_amount;
              e.changes_price = 0;
              e.changes_num = 0;
              e.sales_num = e.amount;
              e.sales_unit_price = e.price;
              e._sortNum = index;
              if (
                this.isShowSyncContractPrice &&
                this.isEnableUserContractPriceDiscountRatio
              ) {
                e.discount = e.discount || 100;
                if (e.org_price === '') {
                  this._computeMarketPrice(e, 4);
                }
              }
              // 复制订单勾选是否同步订单商品价格？ 不需要根据下单数量更新阶梯定价
              if (StorageUtil.getLocalStorage('is_copy_sync_good_price')) {
                e.price = e.unit_price;
              } else {
                this._updateStepPricing(e);
              }
              e.sub_total_price = (+e.amount).mul(+e.price).toFixed(4);
              e.sales_price = (+e.amount).mul(+e.price).toFixed(4);
              e.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
            });

            // 复制订单时过滤积分商品
            if (this.ifFromCopy) {
              list = list.filter((goods) => !Goods.isCreditGoods(goods));
            }

            this.user.id = detail.user_id;
            this.user.name = detail.user_name;
            this.user.detail = detail;
            this.user.detail.name = detail.receive_name;
            this.user.detail.tel = detail.receive_tel;
            this.user.detail.order_audit_inquiry_split =
              detail.order_audit_inquiry_split;
            this.user.special_remark = detail.special_remark;
            this.originUser = this.deepClone(this.user);
            this.remarks = detail.remark;
            this.newOrderList = list;
            this.postList = this.deepClone(this.newOrderList);
            this._updateStepPricingOrderAmountColumnWidth();
            this.activeRowIndex = list.length - 1;
            this.userList = [1];
            this.selectedDeliveryTime = detail.delivery_time_id;
            this.checkContractPrice();
            this.getOrderTagList(this.user.id);
            this.setAddressList();
          }
        });
    },
    getOrderNewData(newData) {
      this.formData.subsidiary_id = newData.subsidiary_id;
      this.formData.meal_type = newData.meal_type;
      this.formData.split_provider_rule = newData.split_provider_rule;
      this.formData.express_no = newData.express_no;
      this.orderCustomizeField = newData.customize_fields;

      this.user.id = newData.user_id;
      this.user.name = newData.user_name;
      this.user.detail = newData;
      this.originUser = this.deepClone(this.user);
      this.remarks = newData.remark;
      this.newOrderList = newData.commodity_list.map((e, index) => {
        e.sales_num = e.amount;
        e._sortNum = index;
        e.sales_price = (+e.amount).mul(+e.price).toFixed(4);
        if (
          this.isShowSyncContractPrice &&
          this.isEnableUserContractPriceDiscountRatio
        ) {
          e.discount = e.discount || 100;
          if (e.org_price === '') {
            this._computeMarketPrice(e, 4);
          }
        }
        // 复制订单勾选是否同步订单商品价格？ 不需要根据下单数量更新阶梯定价
        if (StorageUtil.getLocalStorage('is_copy_sync_good_price')) {
          e.price = e.unit_price;
        } else {
          this._updateStepPricing(e);
        }
        e.id += uniqueId('$unique-'); // 添加唯一键，保证rowKey唯一, 提交时去除
        return e;
      });
      this.postList = this.deepClone(this.newOrderList);
      this._updateStepPricingOrderAmountColumnWidth();
      this.activeRowIndex = this.newOrderList.length - 1;
      this.userList = [1];
      this.selectedDeliveryTime = newData.delivery_time + '';
      this.checkContractPrice();
      this.getOrderTagList(this.user.id).then(() => {
        this.selectedOrderTag = newData.tag_ids;
      });
      this.setAddressList();
    },
    handlerAdd(orders) {
      if (!orders || orders.length <= 0) {
        this.warningMessage('没有可下单的商品');
        return;
      }
      const removeDuplicatesByCommodityId = (arr1, arr2) => {
        let result = [];
        const uniqueIds = (arr) => {
          const uniqueIds = {}; // 辅助对象，用于记录已经出现过的 commodity_id
          return arr.filter((item) => {
            if (!uniqueIds[item.commodity_id]) {
              uniqueIds[item.commodity_id] = true; // 将当前的 commodity_id 记录为已出现
              return true; // 保留该元素
            }
            return false; // 删除重复的元素
          });
        };
        arr1 = uniqueIds(arr1);
        result = arr1.filter((item1) => {
          const hasDuplicate = arr2.some(
            (item2) => item2.commodity_id === item1.commodity_id,
          );
          return !hasDuplicate;
        });
        return result;
      };
      if (orders) {
        if (!this.is_open_order_add_same_commodity) {
          orders = removeDuplicatesByCommodityId(orders, this.newOrderList);
        }
        console.log('orders---', orders);
        orders.forEach((item) => {
          // 如果是禁止售卖商品, 且开启了允许禁止售卖商品录单, 则将商品id赋值给commodity_id
          if (
            !item.commodity_id &&
            +this.sysConfig.super_create_order_unsell_commodity === 1
          ) {
            item.commodity_id = item.id;
          }
          item.id += uniqueId('$unique-');
          item.un_confirm_receive = 0;
          if (
            !this.userStylePriceIsZeroDefaultMarkPrice &&
            +item.is_price_type === 2 &&
            +item.price === 0
          ) {
            this.$Message.warning(`${item.name}未设置客户类型价`);
          }
          item.discount = item.discount || 100;
          if (!item.inner_remark) {
            item.inner_remark = '';
          }
          if (item.org_price === '') {
            this._computeMarketPrice(item, 4);
          }
          item.amount_warning = false;
          item.changes_price = 0;
          item.changes_num = 0;
          item.sales_num = item.amount;
          item.sales_unit_price = item.price;
          item.sales_price = (+item.amount).mul(+item.price).toFixed(4);
          item.sub_total_price = (+item.amount).mul(+item.price).toFixed(4);
        });
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(this.activeRowIndex, 1, ...orders);
          this.activeRowIndex += orders.length - 1;
        } else {
          this.newOrderList.splice(this.activeRowIndex + 1, 0, ...orders);
          this.activeRowIndex += orders.length;
        }
        this.setSortNum();
        this.syncPostList();
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    // 批量新增订单
    _batchAddGoods() {
      if (this.user.id) {
      } else {
        this.modalError('请选择正确的客户');
      }
    },
    // 检测是否输入用户
    isUserInput() {
      if (!this.user.id) {
        this.modalError('请选择正确的客户');
        this.$refs.userInput.focus();
      }
    },
    handleChangeDate(date) {
      this.delivery_date = date;
      // 协议单按发货日期模式生效
      if (
        this.newOrderList &&
        this.newOrderList.length > 0 &&
        this.isContractPriceModeDelivery
      ) {
        this.$Modal.warning({
          title: '提示',
          content:
            '协议价按发货日期生效时,更改订单发货期日,商品价格以下单时价格为准!',
        });
      }
      this.syncPostList();
      this.checkContractPrice();
    },
    // 获取现在的日期
    getDate() {
      let defaultEndTime = DateUtil.getTodayDate();
      const filterConfig = StorageUtil.getLocalStorage(
        'order_list_01_filter_config',
      );
      let endDate = '00:00';
      if (filterConfig && filterConfig.length > 0) {
        const deliverDateFilterConfig = filterConfig.find((config) => {
          return config.key === 'delivery_date';
        });
        endDate =
          deliverDateFilterConfig.value || deliverDateFilterConfig.defaultValue;
      }
      console.log('localEndDate-----', endDate);
      const isTomorrow = DateUtil.isTimeAfterSixPM(endDate);
      if (isTomorrow) {
        defaultEndTime = DateUtil.getTomorrow();
      }
      this.date = defaultEndTime;
      this.delivery_date = this.date;
    },
    getDeliveryTimeList() {
      common
        .getDeliveryTimeList({ pageSize: 9999 })
        .then(({ status, data }) => {
          if (status && data && Array.isArray(data.list)) {
            data.list.forEach((time) => {
              const timeJson = {
                id: time.id,
                timeDu: time.name + ' ' + time.start_time + '-' + time.end_time,
              };
              this.deliveryTimeList.push(timeJson);
            });
          }
        });
    },
    setUserScrollTop() {
      const dropdown = document.getElementsByClassName('dropdown-user')[0];
      setTimeout(() => {
        dropdown.scrollTop = 0;
      }, 0);
    },
    userKeyControll() {
      const length = this.userList.length,
        first = this.userList[0].uid,
        last = this.userList[length - 1].uid,
        dropdown = document.getElementsByClassName('dropdown-user')[0];

      document.onkeydown = (event) => {
        if (!this.userDropDown) return;
        let e = event || window.event || arguments.callee.caller.arguments[0],
          checkId = -1; // 选中的序号

        this.user.uid !== '' ? (checkId = this.user.uid) : (checkId = -1);

        // 如果点击向上按钮
        if (e && e.keyCode === 38) {
          if (checkId !== -1 && checkId > first) {
            this.user.uid = this.user.uid - 1;
            // 控制滚动条的滚动，60为单个选项的高度
            if (checkId < last - 1) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 60);
            }
          }
        }

        // 如果点击向下按钮
        if (e && e.keyCode === 40) {
          // 如果是首次点击向下
          if (checkId === -1) {
            this.user.uid = first;
          }
          if (checkId !== -1 && checkId < last) {
            this.user.uid = this.user.uid + 1;
            // 获取下面的内容
            if (checkId > 1) {
              dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 60);
            }
          }
        }

        if ((e && e.keyCode === 38) || (e && e.keyCode === 40)) {
          if (this.user.uid === 0 || this.user.uid) {
            this.user.name = this.userList[this.user.uid].email;
            this.user.detail = this.userList[this.user.uid];
            // 处理左侧栏样式问题
            if (this.user.id) {
              this.$store.state.isOrderClose = 1;
            } else {
              this.$store.state.isOrderClose = '';
            }
          }
        }
      };
    },
    addUserSave(value) {
      let dropdown = document.getElementsByClassName('dropdown-user')[0];
      this.historyAddOrderActive = false;
      // 如果已经选择商品，则提示
      if (this.newOrderList.length && this.newOrderList[0].commodity_id) {
        setTimeout(
          () => {
            this.$Modal.confirm({
              title: '修改客户',
              content: '<p>确定修改客户？</p>',
              onOk: () => {
                this._setUser(value);
                this.formData.subsidiary_id = '';
                this.checkContractPrice();

                // 刷新商品价格
                this.refreshGoodsList();

                // 将搜索出来的商品列表置顶
                // dropdown.scrollTop = 0
                this._focusGoodsInput(0);
                this.getOrderTagList(value.id);
              },
              onCancel: () => {
                this.user = this.deepClone(this.originUser);
              },
            });
          },
          !value.is_cod ? 301 : 0,
        );
      } else {
        this._setUser(value);
        this.selectedDeliveryTime = value.delivery_time;

        this.formData.subsidiary_id = '';
        this.formData.meal_type = '';

        // 将搜索出来的商品列表置顶
        dropdown.scrollTop = 0;
        // 处理左侧栏样式问题
        if (this.user.id) {
          this.$store.state.isOrderClose = 1;
        } else {
          this.$store.state.isOrderClose = '';
        }
        this.checkContractPrice();
        this._focusGoodsInput(0);
        this.getOrderTagList(value.id);
      }
    },
    handleChangePayWay() {
      if (this.payWay === '0') {
        this.$Modal.confirm({
          title: '提示',
          content: '<p>该客户不支持货到付款是否继续下单？</p>',
          onCancel: () => {
            this.payWay = '1';
          },
        });
      } else {
        this.alwaysShowOnlinePayTip = true;
      }
    },
    confirmIsCod(userDetail) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>该客户不支持货到付款是否继续下单？</p>',
        onOk: () => {
          if (userDetail) {
            setTimeout(() => {
              this.addUserSave(userDetail);
            }, 500);
          }
        },
        onCancel: () => {
          this.user.id = '';
          this.user.uid = '';
          this.user.name = '';
          this.user.detail = '';
          this.user.special_remark = '';
          this.originUser = this.deepClone(this.user);
          this.showContractPriceTips = false;
        },
      });
    },
    addUser(value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeStart(INDEX_TYPE.createOrder);
      } catch (error) {
        console.log(error);
      }
      // 客户异常提示文案展示
      this.userMessage = '';
      common.accountRemind({ user_id: value.id }).then((res) => {
        if (res.status) {
          this.userMessage = res.message;
        }
      });
      // 如果当前支付方式是在线支付，当前客户不支持在线支付，则设置为货到付款
      if (this.payWay === '1' && +value.is_support_online_pay === 0) {
        this.payWay = '0';
      }
      //判断是否货到付款
      if (!value.is_cod) {
        this.confirmIsCod(value);
      } else {
        this.addUserSave(value);
      }
    },
    _setUser(value) {
      this.formData.split_provider_rule = '1';

      this.user.uid = value.uid;
      this.user.id = value.id;
      this.user.name = value.email;
      this.$set(
        this.user,
        'receivable_style_name',
        value.receivable_style_name,
      );
      // this.$set(this.user, 'show_name', `${value.email}${value.receivable_style_name ? `（${value.receivable_style_name}）` : ''}`);
      this.user.detail = value;
      this.originUser = this.deepClone(this.user);
      this.user.special_remark = value.special_remark;
      this.user_address_id = '';

      // 当前客户是否支持自提
      this.is_can_self_pickup_point = value.is_can_self_pickup_point;
      // 当前客户是否支持送货上门
      this.is_can_delivery_home = value.is_can_delivery_home;

      if (this.is_can_delivery_home) {
        this.delivery_method = '1';
      } else {
        this.delivery_method = '2';
      }

      this.createDisabled = false;
      // 未开启送货上门, 同时自提的后台没开启的话
      if (
        !this.is_can_delivery_home &&
        this.sysConfig.self_pickup_point_apply_scope.indexOf('1') === -1
      ) {
        this.$Modal.confirm({
          title: '提示',
          content:
            '当前客户暂无支持的送货方式，请修改客户档案送货方式或自提点生效范围',
        });
        this.createDisabled = true;
      }

      if (this.hasPickUpList.length) {
        // 当前客户绑定的默认自提点
        const defalutIndex = this.hasPickUpList.findIndex(
          (k) => k.id === value.self_pickup_point_id,
        );
        this.pickInfo = this.hasPickUpList[~defalutIndex ? defalutIndex : 0];
      }
      const defaultAddress = {
        id: '',
        name: value.name,
        tel: value.tel,
        address_detail: value.address_detail,
        address_attach: value.address_attach,
      };
      user.UserAddressList({ user_id: value.id }).then((res) => {
        if (res.status) {
          this.addressList = [defaultAddress].concat(
            res.data.map((item) => ({
              id: item.id,
              name: item.receive_name,
              tel: item.receive_tel,
              address_detail: item.address_detail,
              address_attach: item.address_attach,
            })),
          );
        } else {
          this.errorNotice(res.message);
        }
      });
    },
    onChangeAddress(item) {
      this.user_address_id = item.id;
      this.user.detail.name = item.name;
      this.user.detail.tel = item.tel;
      this.user.detail.address_detail = item.address_detail;
      this.user.detail.address_attach = item.address_attach;
      this.addressDropdownShow = false;
    },
    // 获取并设置商品相关数据
    _setCommodity(cid, com, row, index) {
      com.cid = com.commodity_id;
      com.price = com.type_price || '';
      com.discount = com.discount || 100;
      com.org_price = com.org_price || '';

      // 如果已经加入订单列表，则无法再次加入
      const findIndex = this.newOrderList.findIndex(
        (goods) => goods.commodity_id === cid,
      );
      if (!this.isEnableAddSameGoods && ~findIndex && findIndex !== index) {
        this.newOrderList[index] = {
          // 清空当前行商品
          id: uniqueId('$unique-'), // 生成唯一key
          commodity_id: '',
          name: '',
          amount: row.amount,
          price: '',
          un_confirm_receive: 0,
        };
        this.syncPostList();
        this._focusAmountInput(findIndex);
        this.$Message.destroy();
        return this.warningMessage('商品已存在');
      }
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        return this.errorMessage('请输入客户');
      }
      if (!com.cid) {
        this._focusGoodsInput(index);
        return this.errorMessage('请输入正确的商品');
      }

      common
        .getOrderCommodity(this.user.id, com.cid, this.delivery_date, {
          is_show_activity_unit_price: 1,
        })
        .then((res) => {
          const { status, data, message } = res;
          if (status) {
            if (
              !this.userStylePriceIsZeroDefaultMarkPrice &&
              +data.is_price_type === 2 &&
              +data.price === 0
            ) {
              this.warningMessage(`${data.name}未设置客户类型价`);
            }

            if (this.isShowSyncContractPrice) {
              data.org_price = com.org_price;
              data.discount = com.discount;
              data.un_confirm_receive = 0;
              if (this.isEnableUserContractPriceDiscountRatio) {
                data.discount = data.discount || 100;
                if (!Number(data.org_price)) {
                  this._computeMarketPrice(data, 4);
                }
              }
            }
            data.commodity_id = data.id;
            data.amount = row.amount;
            data.amount_warning = false;
            data.changes_num = 0;
            data.changes_price = 0;
            data.sales_unit_price = data.price; // 初始化销售单价为下单单价
            data.is_online = data.is_online || 'Y';

            // 可能是重复添加的商品，ID先标记为不一样，提交的时候，再去掉
            // 直接标记，不用判断开关，因为可能有批量添加进来的和单个添加的，都直接标记为唯一值，提交的时候去掉标记即可
            data.id += uniqueId('$unique-');
            data.inner_remark = '';
            data.un_confirm_receive = 0;
            data._sortNum = index;
            this.handleChangeNum(data, index);
            this._updateStepPricing(data);
            this.newOrderList[index] = this.deepClone(data);
            console.log('this.newOrderList[index]', this.newOrderList[index]);

            // 重新更新列表数据，防止重新渲染填写的数量和备注被覆盖
            this.syncPostList();
            this._updateStepPricingOrderAmountColumnWidth();
            this._focusAmountInput(index);
          } else {
            this.modalError(message || '商品不存在', 0);
          }
        });
    },
    searchUser(e) {
      this.user.id = this.user.uid = ''; // 每次重新搜索必须清空
      this.user.name = e.target.value;
      const consortium_search_type =
        this.consortium_pricing_type == 3 || this.platform_4_b ? '' : 1;
      common.getUserFull(this.user.name, consortium_search_type).then((res) => {
        if (res.status) {
          var data = res.data;
          this.userList = data;
          // 加上排序
          for (let i = 0; i < this.userList.length; i++) {
            if (!this.userList[i].uid) {
              this.userList[i].uid = i;
            }
          }

          if (data && data.length) {
            this.userDropDown = true;
            this.$refs.userInput.focus();
            this.userKeyControll();
          } else {
            this.userDropDown = false;
          }
          let firstUser = data[0] || {};
          // 下拉列表默认选中第一个用户
          this.user.default_item = firstUser;
          this.user.uid = firstUser.uid;
          this.setUserScrollTop();
        }
      });
    },
    // 将历史订单中的商品加入到订单商品列表中
    addHistoryGoods(value) {
      // 必须选择客户和正确的商品
      if (!this.user.id) {
        this.modalError('请输入客户');
        return;
      }
      if (!value || !value.length) {
        this.modalError('请选择正确的历史订单商品');
        return;
      }

      if (
        this.isShowSyncContractPrice &&
        this.isEnableUserContractPriceDiscountRatio
      ) {
        value.forEach((item) => {
          item.price = item.curr_price;
          item.discount = item.discount || 100;
          if (item.org_price === '') {
            this._computeMarketPrice(item, 4);
          }
        });
      }
      value.forEach((item) => {
        item.id += uniqueId('$unique-');
        item.un_confirm_receive = 0;
        item.changes_price = 0;
        item.changes_num = 0;
        item.sales_num = item.amount;
        item.sales_unit_price = item.price;
        item.sales_price = (+item.amount).mul(+item.price).toFixed(4);
        item.sub_total_price = (+item.amount).mul(+item.price).toFixed(4);
      });
      // 清空已经存在的订单商品列表
      this.newOrderList = [];
      this.newOrderList = value.concat(this.newOrderList);
      this.postList = this.deepClone(this.newOrderList);
    },
    addUserHandmade(value) {
      value.commodity_arr.forEach((e) => {
        (e.amount_pic = ''), (e.commodity_id = e.id);
      });

      if (!this.user.id) {
        this.user.id = value.user_info.id;
        this.user.detail = {
          name: value.user_info.name,
          tel: value.user_info.tel,
          address_detail: value.user_info.address_detail,
        };
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(
            this.activeRowIndex,
            1,
            ...value.commodity_arr,
          );
          this.activeRowIndex += value.commodity_arr.length - 1;
        } else {
          this.newOrderList.splice(
            this.activeRowIndex + 1,
            0,
            ...value.commodity_arr,
          );
          this.activeRowIndex += value.commodity_arr.length;
        }
        this.syncPostList();
        this._updateStepPricingOrderAmountColumnWidth();
        return;
      }

      if (this.user.id == value.user_info.id) {
        value.commodity_arr.forEach((e) => {
          this.newOrderList.forEach((se) => {
            if (e.id == se.id) {
              se.amount = parseFloat(se.amount) + parseFloat(e.amount);
            }
          });
        });

        let diffArr = this.diff(value.commodity_arr, this.newOrderList);
        if (
          this.newOrderList[this.activeRowIndex] &&
          !this.newOrderList[this.activeRowIndex].commodity_id
        ) {
          this.newOrderList.splice(this.activeRowIndex, 1, ...diffArr);
          this.activeRowIndex += diffArr.length - 1;
        } else {
          this.newOrderList.splice(this.activeRowIndex + 1, 0, ...diffArr);
          this.activeRowIndex += diffArr.length;
        }
        this.syncPostList();
        this._updateStepPricingOrderAmountColumnWidth();
      }
    },
    //复合数组求差集
    diff(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) {
        return false;
      }
      let result = a;
      for (let i = 0; i < b.length; i++) {
        result = result.filter((e) => e.id != b[i].id);
      }

      return result;
    },
    // 按enter之后跳转到列表第一个商品输入框
    enterUser() {
      this.historyAddOrderActive = false;
      this.user.uid !== '' && this.addUser(this.userList[this.user.uid]);
    },
    checkContractPrice() {
      // 没有启用订单同步客户协议价
      this.showContractPriceTips = false;
      if (!this.isEnableOrderSyncContractPrice) {
        return false;
      }
      common
        .checkValidUserContractPriceOrder(this.user.id, this.delivery_date)
        .then((res) => {
          let { data } = res;
          if (!data) {
            this.userHasContractPriceOrder = false;
            this.showContractPriceTips = true;
          } else {
            this.userHasContractPriceOrder = true;
          }
        });
    },
    selectAgreementPriceOrder(value, extraParams) {
      common
        .checkValidUserContractPriceOrder(this.user.id, this.delivery_date)
        .then((res) => {
          let { status, message, data } = res;
          if (status) {
            const notAgreementPriceGoods = this.newOrderList.filter(
              (item) => !Goods.isProtocolGoods(item),
            );
            if (
              Array.isArray(data) &&
              data.length > 1 &&
              notAgreementPriceGoods.length > 0 &&
              this.isOpenSyncProtocolIsAddCommodity
            ) {
              let repeatGoods = []; // 非协议价商品 - 重复商品数组
              let sameRepeatGoods = []; // 下单单价、折扣率（订）和协议市场价（订）一样的商品数组
              for (
                let index = 0;
                index < notAgreementPriceGoods.length - 1;
                index++
              ) {
                const item = notAgreementPriceGoods[index];
                for (
                  let index2 = index + 1;
                  index2 < notAgreementPriceGoods.length;
                  index2++
                ) {
                  const item2 = notAgreementPriceGoods[index2];
                  if (
                    item.commodity_id === item2.commodity_id &&
                    repeatGoods.indexOf(item) === -1
                  ) {
                    repeatGoods.push(item);
                    repeatGoods.push(item2);
                    if (
                      +item.price === +item2.price &&
                      +item.org_price === +item2.org_price &&
                      +item.discount === +item2.discount &&
                      sameRepeatGoods.indexOf(item2) === -1
                    ) {
                      sameRepeatGoods.push(item2);
                    }
                  }
                }
              }

              if (
                extraParams &&
                extraParams.sync_protocol &&
                ((repeatGoods.length > 0 && sameRepeatGoods.length > 0) ||
                  repeatGoods.length < notAgreementPriceGoods.length)
              ) {
                this.agreementPriceSelectModal.show = true;
                this.agreementPriceSelectModal.data = data;
              } else {
                this.createOrder(value, extraParams);
              }
            } else {
              this.createOrder(value, extraParams);
            }
          } else {
            this.$smessage({ type: 'error', text: message });
            this.createOrder(value, extraParams);
          }
        });
    },
    _focusGoodsInput(index) {
      this.activeRowIndex = index;
      this.$nextTick(() => {
        const commodityInput =
          document.getElementsByClassName('commodity-select')[index];
        commodityInput &&
          commodityInput.querySelector('textarea') &&
          commodityInput.querySelector('textarea').focus();
      });
    },
    _focusAmountInput(index) {
      setTimeout(() => {
        const $currentRow =
          this.$refs.orderGoodsTable.$el.querySelectorAll('tbody tr')[index];
        $currentRow
          .querySelector('.order-amount-input')
          .querySelector('input')
          .focus();
      });
    },
    showNewOrderReview() {
      // 有商品才能够打开订单核价
      if (!this.newOrderList.length) {
        this.modalError('您还未选择任何商品');
        return;
      }
      let newList = this.deepClone(
        this.postList.filter((goods) => goods.commodity_id),
      );
      newList.forEach((item) => {
        item.check_amount = parseFloat(item.amount);
      });
      this.checkOrderList = newList;
      this.inputCheckOrderList = this.deepClone(newList);
      this.newOrderReviewActive = true;
    },
    /**
     * 刷新商品列表
     */
    refreshGoodsList() {
      let params = {
        user_id: this.user.id,
        commodity_type: 1,
        commodity_string: '',
        showAll: true,
        page: 1,
        pageSize: 99999,
        is_sell_independent_from_user_id: this.superCreateOrderUnsellCommodity,
      };
      let goodsIdArr = this.postList.map((goods) => goods.id);
      const originalGoodsArr = this.postList;
      params.commodity_string = JSON.stringify(goodsIdArr);
      this.$request.post(this.apiUrl.getGoodsList, params).then((res) => {
        let { status, data } = res;
        if (status) {
          let newOrderList = this.newOrderList.filter((goods) =>
            data.list.find(
              (retGoods) => goods.commodity_id === retGoods.commodity_id,
            ),
          );
          newOrderList.forEach((goods) => {
            let retGoods = data.list.find(
              (retGoods) => goods.commodity_id === retGoods.commodity_id,
            );
            if (goods) {
              goods.price = retGoods.price;
              // 刷新相关价格
              goods.in_price = retGoods.in_price;
              goods.store_in_price = retGoods.store_in_price;
              goods.average_price = retGoods.average_price;
              goods.last_receipt_price = retGoods.last_receipt_price;
              goods.sub_total_price = (+goods.amount)
                .mul(+goods.price)
                .toFixed(4);
              goods.is_price_type = retGoods.is_price_type; // 更新价格类型
            }
          });
          // this.newOrderList = newOrderList
          this._initTableData(newOrderList);
          this.$nextTick(() => {
            setTimeout(() => {
              this.warningDelGoods(originalGoodsArr, newOrderList);
            }, 500);
          });
        }
      });
    },
    // 提醒客户因为屏蔽商品而被删除的商品有哪些, 增加确认弹窗：所选客户以下商品【xx】不支持售卖
    warningDelGoods(oldList, newOrderList) {
      let delGoods = [];
      oldList.forEach((goods) => {
        const newDataItem = newOrderList.find((item) => item.id === goods.id);
        if (!newDataItem && newDataItem.is_sell != 1) {
          delGoods.push(`${goods.name}(${goods.unit})`);
        }
      });
      if (delGoods.length > 0) {
        this.$Modal.confirm({
          title: '提示',
          content: `所选客户以下商品【${delGoods.join('、')}】不支持售卖`,
        });
      }
    },
    /**
     * 同步postList 和 newOrderList数据
     */
    syncPostList() {
      let newOrderList = this.deepClone(this.newOrderList);
      newOrderList.forEach((goods) => {
        const storeGoods = this.postList.find(
          (storeGoods) => storeGoods.id === goods.id,
        );
        if (storeGoods) {
          // Object.keys(goods).forEach(key => goods[key] = storeGoods[key])
          Object.assign(goods, storeGoods);
        }
      });
      this.newOrderList = newOrderList;
      this.postList = this.deepClone(newOrderList);
      // console.log(this.newOrderList, this.postList);
    },
    confirmReview() {
      this.inputCheckOrderList.map((checkGoods) => {
        this.postList.forEach((goods) => {
          if (checkGoods.id == goods.id) {
            goods.amount = checkGoods.check_amount;
            goods.remark = checkGoods.remark;
            this._updateStepPricing(goods);
          }
        });
      });
      this.syncPostList();
      this.newOrderReviewActive = false;
    },
    createOrderBefore(value, extraParams) {
      this.addMode = value;
      this.selectAgreementPriceOrder(value, extraParams);
    },
    getSubmitParams({ extraParams, commodityList }) {
      let goodsList =
        commodityList ||
        this.deepClone(this.postList.filter((goods) => goods.commodity_id));
      // 转换中国时区
      let date = new Date(this.date.valueOf() + 28800000);
      date = date.toISOString().slice(0, 10);

      let userId = this.user.id,
        userName = this.user.name,
        remarks = this.remarks,
        selectedDeliveryTime = this.selectedDeliveryTime;

      !selectedDeliveryTime && (selectedDeliveryTime = 0);

      goodsList = goodsList.map((item, index) => {
        let result = {
          sort_num: index,
          commodity_id: item.id
            ? item.id.replace(/\$unique-.*$/, '')
            : item.commodity_id,
          amount: item.amount,
          remark: item.remark,
          inner_remark: item.inner_remark,
          price: item.price,
          mutate_price: item.price,
          in_price: item.in_price,
          org_price: item.org_price,
          discount: item.discount,
          un_confirm_receive: item.un_confirm_receive,
          changes_num: item.changes_num,
          changes_price: item.changes_price,
          sales_unit_price: item.sales_unit_price,
          order_commodity_tag: item.order_commodity_tag,
          mutate_total_price: item.sub_total_price,
        };
        // 订单自定义字段
        result.customize_fields = this.goodsCustomizeField.map((field) => {
          return {
            key: field.key,
            name: field.name,
            value: item[field.key],
          };
        });
        return result;
      });

      let orderParams = {
        user_id: userId,
        user_name: userName,
        user_address_id: this.user_address_id,
        delivery_date: date,
        delivery_method: this.delivery_method,
        self_pickup_point_id: this.pickInfo.id,
        remark: remarks,
        delivery_time: selectedDeliveryTime,
        ...(extraParams || {}),
        ...this.formData,
      };
      orderParams.tag_ids = this.selectedOrderTag;
      orderParams.commodity_list = goodsList;
      // 订单自定义字段
      orderParams.customize_fields = this.orderCustomizeField;
      orderParams.attachment_link = this.attachmentFiles
        .map((item) => item.url)
        .join(',');

      return orderParams;
    },
    createOrder(value, extraParams) {
      if (!this.checkData()) {
        return false;
      }

      if (!this.date) {
        this.modalError('请选择日期');
        return;
      }

      let commodityList = this.deepClone(
        this.postList.filter((goods) => goods.commodity_id),
      );

      if (!commodityList.length) {
        this.errorNotice('请先选择商品再创建订单');
        return;
      }

      const orderParams = this.getSubmitParams({ extraParams, commodityList });
      orderParams.tag_ids = JSON.stringify(orderParams.tag_ids);
      orderParams.commodity_list = JSON.stringify(orderParams.commodity_list);
      orderParams.customize_fields = JSON.stringify(
        orderParams.customize_fields,
      );
      orderParams.pay_way = this.payWay === '1' ? 'is_online_pay' : '';

      // 复制新增, 追加参数让操作日志区分创建来源
      if (this.$route.query.copyId) {
        orderParams.is_copy = 1;
        orderParams.original_order_no = this.$route.query.copyNo;
      }

      //判断是否开启订单合并
      if (this.isOpenOrderCombine && !this.hasStepPricingGoods) {
        if (this.createDisabled) {
          return false;
        }
        this.createDisabled = true;
        //检查订单是否满足合并条件
        common
          .orderCombineCheck(orderParams)
          .then((res) => {
            const { status, message, data } = res;
            const { can_order_combine, combine } = data;
            if (status) {
              //1.满足合并条件，提示用户是否进行合并
              if (can_order_combine) {
                this.$Modal.confirm({
                  title: '确定要合并到之前的订单吗？',
                  content: `<div>
                  <p>订单号:${combine.order_no}</p>
                  <p>下单时间:${combine.create_time}</p>
                  <p>发货日期:${combine.delivery_date}</p>
                  <p>注意:如果已经生成采购单或者已经分拣,请谨慎操作</p>
                </div>`,
                  onOk: () => {
                    orderParams.is_order_combine = 1;
                    orderParams.order_no = combine.order_no;
                    this.orderCombine(orderParams, value, true);
                  },
                  onCancel: () => {
                    orderParams.is_order_combine = 0;
                    orderParams.order_no = combine.order_no;
                    this.orderCombine(orderParams, value, false);
                  },
                });
              } else {
                // 2.不满足合并条件，后台已默认创建订单，不用手动创建，这里需要清除前端信息
                this.clearInfo(value);
              }
            } else {
              this.errorNotice(message);
            }
          })
          .finally(() => {
            this.createDisabled = false;
          });
      } else {
        //未开启订单合并，正常提交
        if (this.createDisabled) {
          return false;
        }
        this.confirmAddOrder(orderParams, value);
      }
    },
    /**
     * 确认提交订单
     * @param(params) 提交参数
     */
    confirmAddOrder(params, value) {
      // 录单效率指标统计
      try {
        coreEfficiencyIndexLogger.timeEnd(INDEX_TYPE.createOrder, {
          goods_count: JSON.parse(params.commodity_list).length,
          page: location.href,
        });
      } catch (error) {
        console.log(error);
      }
      this.createDisabled = true;
      common
        .createOrderV2(params)
        .then((res) => {
          const { status, message } = res;
          if (status) {
            this.successMessage(message || '订单创建成功');
            this.clearInfo(value);
          } else {
            this.errorNotice({
              title: '订单创建失败!',
              desc: message || '订单创建失败！',
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    /**
     * 订单提交之后，清除信息
     */
    clearInfo(value) {
      this._initTableData();
      this.newOrderReviewActive = false;
      this.user = { id: '', uid: '', name: '', detail: '' };
      this.$refs.userInput && this.$refs.userInput.focus();

      this.remarks = '';
      this.$store.state.isOrderClose = '';
      this.selectedOrderTag = [];
      // 自定义字段置空
      this.orderCustomizeField.map(val => val.value = '')

      if (value === 1) {
        this.back();
      } else if (value === 2) {
        console.log(value);
      } else {
        this.back();
      }
    },
    /**
     * 进行订单合并
     * @param (params) 请求参数
     * @param (value) 区分是哪个点击
     * @param (ifCombine) 是否进行合并
     */
    orderCombine(params, value, ifCombine) {
      if (this.createDisabled) {
        return false;
      }
      this.createDisabled = true;
      common
        .orderCombine(params)
        .then((res) => {
          let { status, message } = res;
          this.createDisabled = false;
          message = message
            ? message
            : ifCombine
              ? '订单合并成功'
              : '订单创建成功';
          if (status) {
            this.successMessage(message);
            this.clearInfo(value);
          } else {
            this.errorNotice({
              title: '订单合并失败!',
              desc: message || '订单合并失败！',
            });
          }
        })
        .finally(() => {
          this.createDisabled = false;
        });
    },
    checkGoodsAmount(goods) {
      if (!goods.amount && Number(goods.amount) !== 0) {
        return false;
      }
      return true;
    },
    resetError() {
      this.error = {
        user: '',
        delivery_date: '',
        goods: '',
      };
    },
    resetUserError() {
      this.error.user = '';
    },
    /**
     * @param {scrollToError} options
     * @returns {boolean}
     */
    checkBaseData(options = {}) {
      let { scrollToError } = options;
      let valid = true;
      let firstErrorDom = null;

      if (!this.user.id) {
        valid = false;
        this.error.user = '请选择客户';
        firstErrorDom = this.$refs['error.user'].$el;
      } else {
        this.error.user = '';
      }
      if (!this.date) {
        valid = false;
        !firstErrorDom &&
          (firstErrorDom = this.$refs['error.delivery_date'].$el);
        this.error.delivery_date = '请选择发货日期';
      } else {
        this.error.delivery_date = '';
      }
      if (firstErrorDom && scrollToError) {
        firstErrorDom.scrollIntoView({ block: 'center' });
      }
      return valid;
    },
    checkData() {
      let valid = true;
      let firstErrorGoodsIndex = null;

      // 如果开启了允许不可售卖商品录单, 不过滤
      const filterPostList = this.postList.filter((goods) => {
        return goods.commodity_id;
      });

      if (filterPostList.length === 0) {
        // valid = false
        this.errorMessage('请添加商品和数量');
        this._focusGoodsInput(0);
        return false;
      }

      // 检查订单标签设置
      if (!this.checkTag()) {
        valid = false;
      }

      // 检查订单商品订购数量
      filterPostList.forEach((item, index) => {
        if (!this.checkGoodsAmount(item)) {
          valid = false;
          item.amount_warning = true;
          if (firstErrorGoodsIndex === null) {
            this.errorMessage('请输入订购数量');
            firstErrorGoodsIndex = index;
          }
        } else {
          item.amount_warning = false;
        }
      });

      this.syncPostList();
      // 开启订单商品标签必填
      if (this.isOpenOrderCommodityTag && this.isOrderCommodityTagRequired) {
        for (const [index, good] of filterPostList.entries()) {
          if (!good.order_commodity_tag) {
            this.errorMessage(`${good.name}订单商品标签未选择，请重试！`);
            const scrollBox = this.$refs.orderGoodsTable.$el.querySelector(
              '.sdp-table__content',
            );
            const curElement = this.$refs.orderGoodsTable.$el.querySelector(
              `[data-key="body_tr_${index}"]`,
            );
            scrollBox.scrollTo(0, curElement.offsetTop);
            return false;
          }
        }
      }
      let baseDataValid = this.checkBaseData({
        scrollToError: true,
      });

      if (baseDataValid && firstErrorGoodsIndex !== null) {
        this.$nextTick(() => {
          const $firstRow = this.$refs.orderGoodsTable.$el.querySelector(
            `.editable-table tbody tr:nth-child(${firstErrorGoodsIndex + 1})`,
          );
          if ($firstRow) {
            $firstRow.scrollIntoView({ block: 'center' });
          }
        });
      }
      return valid && baseDataValid;
    },
    back() {
      this.router.push({
        path: '/order',
      });
    },
    activeUserHandmade() {
      this.userHandmadeActive = !this.userHandmadeActive;
    },

    handlePrint(event) {
      // 检查是否按下了 Command (Mac) 或 Ctrl (Windows) 键
      if (event.shiftKey && event.key === 'C') {
        // 调用 printSkuImg 方法，并传递图片名称
        this.printSkuImg();
      }
    },
    printSkuImg() {
      SLoading.showLoading('生成商品清单长图……');

      // 要打印的数据
      const toPrint = [
        {
          th: '序号',
          key: 'index',
          width: '70px',
        },
        {
          th: '商品名称',
          key: 'name',
          width: '266px',
        },
        {
          th: '单位',
          key: 'unit',
          width: '100px',
        },
        {
          th: '下单数量',
          key: 'amount',
          width: '120px',
        },
        {
          th: '备注',
          key: 'remark',
          width: '200px',
        },
      ];

      import('@assets/js/html2canvas.min.js')
        .then((html2canvas) => {
          // 创建新的table元素
          const newTable = document.createElement('table');
          newTable.classList.add('print-table'); // 添加表格的类名，确保样式正确

          // 创建表头
          const tableHead = document.createElement('thead');
          const headerRow = document.createElement('tr');
          toPrint.forEach((item) => {
            const th = document.createElement('th');
            th.textContent = item.th;
            th.style.border = '1px solid #ccc'; // 设置表头边框
            th.style.padding = '8px'; // 设置表头内边距
            th.style.width = item.width; // 设置表头宽度
            th.style.textAlign = 'center'; // 居中对齐
            headerRow.appendChild(th);
          });
          tableHead.appendChild(headerRow);

          // 创建表格内容
          const tableBody = document.createElement('tbody');
          this.newOrderList.forEach((dataItem, index) => {
            const row = document.createElement('tr');
            row.style.border = '1px solid #ccc'; // 设置表格行边框
            row.style.textAlign = 'center'; // 居中对齐
            toPrint.forEach((item) => {
              const td = document.createElement('td');
              td.textContent =
                item.key === 'index' ? index + 1 : dataItem[item.key];
              td.style.border = '1px solid #ccc'; // 设置单元格边框
              td.style.padding = '8px'; // 设置单元格内边距
              td.style.width = item.width; // 设置单元格宽度
              row.appendChild(td);
            });
            tableBody.appendChild(row);
          });

          newTable.appendChild(tableHead);
          newTable.appendChild(tableBody);

          // 将新的表格元素添加到文档中，以便截图
          document.body.appendChild(newTable);

          // 使用html2canvas截图新的表格元素
          html2canvas
            .default(newTable, {
              x: -20,
              y: -30,
              width: newTable.offsetWidth + 30,
              height: newTable.scrollHeight + 60,
            })
            .then((canvas) => {
              // 将生成的图像转换为Blob对象
              canvas.toBlob((blob) => {
                // 将图片添加至剪贴板
                navigator.clipboard
                  .write([
                    new ClipboardItem({
                      'image/png': blob,
                    }),
                  ])
                  .then(() => {
                    this.successMessage('商品清单图片已复制至粘贴板！');
                    SLoading.hideLoading();
                  })
                  .catch((error) => {
                    console.error('复制图片到剪贴板失败:', error);
                    this.errorMessage('复制失败！');
                    SLoading.hideLoading();
                  });

                // 移除新创建的表格元素
                document.body.removeChild(newTable);
              }, 'image/png');
            })
            .catch((error) => {
              console.error('生成图像失败:', error);
              this.errorMessage('生成图像失败！');
              SLoading.hideLoading();

              // 移除新创建的表格元素
              document.body.removeChild(newTable);
            });
        })
        .catch((error) => {
          console.error('加载html2canvas库失败:', error);
          this.errorMessage('加载html2canvas库失败！');
          SLoading.hideLoading();
        });

      try {
        this.sentryMsg();
      } catch (err) {
        console.error('Sentry错误:', err);
      }
    },

    sentryMsg() {
      // 自定义数据
      const customData = {
        action: 'print_sku_long_img',
        timestamp: new Date().toISOString(),
        user: this.user,
        additionalInfo: {
          context: '用户截商品长图',
        },
      };

      // 上报自定义数据到 Sentry
      Sentry.captureMessage('print_sku_long_img', {
        level: 'info',
        tags: {
          action: 'print_sku_long_img', // 添加自定义标签
        },
        extra: customData,
      });
    },
    activeHistoryAddOrder() {
      if (!this.user.id) {
        this.modalError('请先选择客户');
        return;
      }
      this.historyAddOrderActive = !this.historyAddOrderActive;
    },
    handleChangePriceType(_value) {
      window.localStorage.setItem('order_base_price_mode', _value);
      this.priceType = _value;
      // 更新价格模式配置
      this.commonService.editSingleConfig('order_base_price_mode', _value);
    },
    /**
     * @description: 判断是否为阶梯定价商品
     * @param {*} goods
     */
    isStepPricingGoods(goods) {
      return Goods.isStepPricingGoods(goods);
    },
    /**
     * @description: 若商品配置了阶梯定价，根据商品下单数量，动态计算更新阶梯定价的商品价格
     * @param {Object} row 当前商品对象
     * @param {Number} index 当前行下标
     */
    _updateStepPricing(row, index = -1) {
      const { amount } = row;
      if (this.isStepPricingGoods(row)) {
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true),
        );
        if (stepPriceItem) {
          row.price = stepPriceItem.price;
          if (index >= 0 && this.postList[index]) {
            // 作修改时, 需要更新至postList
            this.postList[index].price = stepPriceItem.price;
            this.newOrderList[index].price = stepPriceItem.price;
          }
        }
      }
    },
    /**
     * @description: 根据列表数据判断，存在阶梯定价商品时，需要加宽下单数量列，以显示阶梯定价标签
     */
    _updateStepPricingOrderAmountColumnWidth() {
      if (this.postList.some((goods) => this.isStepPricingGoods(goods))) {
        const newWidth = 155;
        // 接口请求时间不一定，两个都更新就行
        const origin_order_amount_column = this.originCols.find(
          (item) => item.key === 'order_amount',
        );
        if (origin_order_amount_column) {
          origin_order_amount_column.width = newWidth;
        }
        const order_amount_column = this.goodsColumns.find(
          (item) => item.key === 'order_amount',
        );
        if (order_amount_column) {
          this.$set(order_amount_column, 'width', newWidth);
        }
      }
    },
    getOrderGoodsTagList() {
      Goods.getOrderGoodsTagList().then((data) => {
        this.orderGoodsTagList = data;
      });
    },
    showSmartCreator() {
      // TODO 智能录单入口
      if (this.percent !== 0) {
        this.$Message.warning(`AI识别正在进行中，请稍后……`);
        return;
      }
      this.$refs.orderSmartModal.open();
    },
    showImportModal() {
      this.$refs.importBtnRef.showModal();
    },
    beforeUpload() {
      this.importError = '';
    },
    importCompletedHandel(status, data) {
      if (status) {
        this.handlerAdd(data);
      } else {
        this.importError = data;
      }
    },
    exportOrderTemplate() {
      let url = `/superAdmin/orderSuper/BatchImportOrderCommodityTemplate?type=${this.importDownParams.type}&price_mode=${this.importDownParams.price_mode}`;
      window.location.href = url;
    },
    setStoreData() {
      if (
        this.user.id ||
        this.newOrderList.filter((item) => item.commodity_id).length
      ) {
        const newData = this.getSubmitParams({});
        const list = this.postList.filter((goods) => goods.commodity_id);
        newData.commodity_list = this.deepClone(
          list.length ? list : [{ amount: 1, price: 1 }],
        );
        StorageUtil.setLocalStorage('ORDER_NEW_DATA', newData);
      }
    },
  },
};
</script>

<style lang="less" scoped>
.process-wrapper {
  position: relative;
  margin: 0 auto;
  .process-content {
    position: absolute;
    z-index: 600;
    left: 0;
    right: 0;
    top: -61px;
  }
}
/deep/.demo-upload-list {
  display: inline-block;
  text-align: center;
  border: 1px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  height: 40px;
  width: 40px;
  line-height: 40px;
  background: rgba(0, 0, 0, 0);
  position: relative;
  box-shadow: none;
  margin-right: 0px;
}

.order-new {
  .ai-btn-wrap {
    margin-left: 10px;
    cursor: pointer;
    width: 82px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .ai-btn {
      position: relative;
      text-align: center;
      vertical-align: middle;
      width: 82px;
      height: 30px;
      background-image: url('../../assets/images/aiOrderCreate/ai-btn.png');
      background-size: cover;
      background-position: center center;
      border: 3px solid transparent; /* 初始边框透明 */
      border-image: linear-gradient(
          102deg,
          rgba(253, 97, 255, 1),
          rgba(50, 161, 255, 1),
          rgba(0, 238, 255, 1),
          rgba(72, 255, 160, 1)
        )
        1; /* 只设置边框的渐变 */

      &:hover {
        filter: contrast(1.1);
      }

      &:active {
        filter: contrast(0.9);
      }

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid transparent; /* 初始边框透明 */
        animation: borderScroll 4s infinite linear; /* 应用滚动动画 */
      }

      &::after {
        animation-delay: -2s; /* 延迟 */
      }
    }
  }

  @keyframes borderScroll {
    0% {
      clip-path: inset(0 0 98% 0);
      border-color: #d761ff;
    }
    25% {
      clip-path: inset(0 98% 0 0);
      border-color: #32a1ff;
    }
    50% {
      clip-path: inset(98% 0 0 0);
      border-color: #00eeff;
    }
    75% {
      clip-path: inset(0 0 0 98%);
      border-color: #48ffa0;
    }
    100% {
      clip-path: inset(0 0 98% 0);
      border-color: #d761ff;
    }
  }

  // UI重构调整
  /deep/.ivu-input-disabled {
    background-color: #f7f7f7;
    color: rgba(0, 0, 0, 0.4);
  }
  /deep/.ivu-select-disabled .ivu-select-selection {
    background-color: #f7f7f7;
    color: rgba(0, 0, 0, 0.4);
  }
  /deep/.ivu-checkbox-wrapper {
    margin-right: 24px;
  }
  /deep/.ivu-form-item {
    margin: 0 36px 12px -2px !important;
  }
  /deep/.receive_goods {
    .ivu-form-item {
      margin: -2px 36px 0px -2px !important;
    }
    .base-block__hd {
      padding-bottom: 6px;
    }
  }
  /deep/.ivu-dropdown {
    width: 100%;
  }
}

/deep/.demo-upload-list img {
  width: auto;
  height: auto;
}
.demo-upload-list:hover .demo-upload-list-cover {
  display: block;
}
.demo-upload-list-cover {
  display: none;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
}
.dropdown-user,
.dropdown-user-content {
  position: absolute;
  z-index: 199;
  left: 0;
  margin-top: 5px;
  width: 300px;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  max-height: 200px !important;
  box-shadow: 1px 2px 1px #ccc;
  border-radius: 2px;

  .dropdown-items {
    width: 100%;
    padding: 5px 10px !important;

    &-strong {
      font-weight: 500;
      font-size: 13px;
      margin-right: 10px;
      line-height: 19.2px;
    }
    &-span {
      font-size: 12px;
      color: #aaa;
    }
    &-p {
      line-height: 20px;
      font-size: 13px;
      color: #aaa;
    }
    &.active {
      p {
        color: #03ac54;
      }
      color: #03ac54;
      background-color: #ebf7ff;
    }
    &:hover {
      cursor: pointer;
      background-color: #ebf7ff;
      p {
        color: #03ac54;
      }
      color: #03ac54;
    }
  }
}

/deep/ .editable-table {
  .sdp-table__cell > div {
    display: block !important;
  }
  .sdp-table_tr-order-smart-success .sdp-table__td {
  }
  .sdp-table_tr-order-smart-process .sdp-table__td {
    background-color: #fff5f4;
    &:hover {
    }
  }
  .new-logo {
    margin-right: 2px;
    padding: 0 3px;
    font-size: 12px;
    color: #fff;
    background-color: #3399ff;
    border-radius: 2px;
  }

  .smart-logo {
    margin-right: 2px;
    border-radius: 2px;
  }

  .InternalNote {
    color: #3399ff;
  }

  .order-amount-input.amount_warning input {
    border-color: red;
  }
  .emphasis-tip input {
    color: #ff6e00;
    border-color: currentColor;
  }
  .required-tip .ivu-select-selection {
    border-color: rgb(243, 51, 51);
  }
}

.newOrder-operation {
  align-self: flex-end;
  line-height: 15px;
  color: #03ac54;

  span {
    cursor: pointer;
  }

  span:not(:last-child) {
    margin-right: 10px;
  }
}

.newOrder-amount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #303030;
  text-align: right;
  .label {
    font-weight: 500;
    color: rgba(0, 0, 0, 0.85);
  }
  &-total {
    color: #ed3f14;
    font-family: AvenirNext, AvenirNext;
  }
}

.newOrder-remarks {
  width: 100%;
  border: none;
  .ivu-form-item {
    width: 100%;
  }
}
.tips_line {
  font-size: 12px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.7);
  padding-left: 140px;
}
/deep/ .dropdown-empty {
  padding: 10px;
  color: #80848f;
  text-align: center;
}
/deep/ .ivu-poptip-rel {
  line-height: 1;
}
/deep/ .dropdown-user {
  width: 302px;
  height: 237px;
  max-height: 237px;
  box-shadow: 0px 2px 14px 1px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  .ivu-select-item {
    white-space: wrap;
    .dropdown-items-strong {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    &.active {
      background: #f2f4f5;
      .dropdown-items-strong {
        font-weight: bold;
      }
    }
    &:hover {
      background: #e6f7ec;
      color: var(--primary-color);
    }
  }
}
.check-address {
  /deep/ .ivu-select-dropdown {
    margin: 10px 0 0;
    padding: 0;
    box-shadow: 0px 2px 14px 1px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
  }
  .address-content {
    max-height: 300px;
    overflow-y: auto;
  }
  .address-header,
  .address-row {
    display: flex;
    align-items: center;
    .name {
      width: 132px;
      padding: 0 24px;
      .icon {
        width: 26px;
        height: 15px;
        vertical-align: top;
      }
    }
    .tel {
      width: 132px;
      padding: 0 24px;
    }
    .address {
      width: 260px;
      padding: 0 24px;
    }
  }
  .address-header {
    height: 40px;
    background: #f4f5f6;
    font-size: 13px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 500;
    color: #2b2b2b;
  }

  .address-row {
    min-height: 50px;
    font-size: 13px;
    font-family:
      PingFangSC,
      PingFang SC;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    background: white;
    &:hover {
      background: rgba(3, 172, 84, 0.08);
    }
    .name--active {
      font-weight: 500;
      color: #03ac54;
    }
  }
  .address-row--active {
    background: rgba(3, 172, 84, 0.08);
  }
}
</style>
