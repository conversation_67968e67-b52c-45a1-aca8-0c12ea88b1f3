<style lang="less">
.openingHoursConfigEdit {
  .primaryColor {
    color: var(--primary-color);
    padding: 0 5px;
    cursor: pointer;
  }
  &.disabled {
    cursor: not-allowed;
    .ivu-form {
      pointer-events: none;
    }
  }
  .hoursItem {
    background: #FFFFFF;
    border-radius: 2px;
    border: 1px solid #E8E8E8;
    margin-bottom: 10px;
    .CategorySelectForMultiple-label {
      width: 89px;
    }
    &__header {
      height: 48px;
      background: #F6F8F9;
      display: flex;
      align-items: center;
      padding: 9px 25px;
      .labelText {
        font-size: 13px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: rgba(0,0,0,0.85);
        line-height: 15px;
        flex-shrink: 0;
        margin-right: 5px;
        & ~ * {
          margin-right: 10px;
        }
      }
    }
    &__rules {
      padding: 5px 26px;
      .ruleLabel {
        padding-bottom: 10px;
        .ruleNumber {
          color: #000;
          display: inline-block;
          transform: rotate(90deg);
          margin-bottom: 3px;
        }
        .ruleDelete {
          display: inline-block;
          cursor: pointer;
          background: #FFFFFF;
          border: 1px solid #D8D8D8;
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #F13130;
          padding: 3px 7px;
        }
      }
      &--add {
        margin-left: 114px;
        margin-bottom: 16px;
        margin-top: -5px;
      }
    }
  }
}
</style>
<style scoped>
.form-item-label {
  width: 89px;
  text-align: right;
}
.desc {
  color: rgba(0,0,0,0.65);
}
.margin-bottom {
  margin-bottom: 12px;
}
/deep/ {
  .ivu-form-item {
    margin-bottom: 16px;
  }
  .ivu-checkbox-wrapper {
    margin-right: 16px;
  }
  .ivu-radio-wrapper {
    margin-right: 12px;
    font-size: 13px;
  }
}
</style>

<template>
  <div class="openingHoursConfigEdit" :class="{disabled: !isOperatingTimeBindDeliveryDate && (currentRow.link_type && currentRow.link_type != 1)}">
    <DetailPage
      class="good_page"
      :disabledSave="saving"
      @on-save="handleSubmit"
      :showFooter="true"
      :title="$route.query.type === 'edit' ? '编辑运营时间段' : '新增运营时间段'"
      :pageType="!isOperatingTimeBindDeliveryDate && (currentRow.link_type && currentRow.link_type != 1) ? 'view' : 'add, edit'"
    >
    <Form
      ref="formValidate"
      :model="formValidate"
      :rule="ruleValidate"
      :label-width="120"
    >
      <base-block title="基础信息">
        <FormItem label="运营时间名称" prop="title">
          <Input v-model="formValidate.title" placeholder="请输入名称" style="width: 280px;"></Input>
        </FormItem>
        <FormItem label="说明">
          <Input v-model="formValidate.description" placeholder="请输入..." style="width: 280px;"></Input>
        </FormItem>
      </base-block>
      <base-block title="配送规则">
        <FormItem style="margin-bottom: 10px;" label="可下单周期">
          <CheckboxGroup v-model="formValidate.week">
            <Checkbox
              :key="weekIndex"
              v-for="(weekItem, weekIndex) in operatingWeekDayList"
              :label="weekItem.value"
            >
              {{ weekItem.label }}
            </Checkbox>
          </CheckboxGroup>
        </FormItem>
        <FormItem label="关联类型">
          <RadioGroup v-model="formValidate.link_type" @on-change="handleChangeType">
            <Radio label="1" :disabled="$route.query.type === 'edit'">按运营时间关联送货时间</Radio>
            <Radio label="2" :disabled="!isOperatingTimeBindDeliveryDate || $route.query.type === 'edit'">按分类关联送货时间</Radio>
            <Radio label="3" :disabled="!isOperatingTimeBindDeliveryDate || $route.query.type === 'edit'" v-if="is_open_operating_time_commodity">按商品关联送货时间</Radio>
          </RadioGroup>
        </FormItem>
      </base-block>
      <base-block title="配送时间段">
        <div v-for="(item, index) in hoursItem" class="hoursItem" :key="item.key">
          <div class="hoursItem__header margin-bottom">
            <span class="labelText">时间段: </span>
            <TimePicker
              format="HH:mm"
              placeholder="开始时间"
              v-model="item.start"
              style="width: 136px"
            ></TimePicker>
            <Select v-model="item.method" style="width: 84px">
              <Option value="today">当日</Option>
              <Option value="tomorrow">次日</Option>
            </Select>
            <TimePicker
              format="HH:mm"
              placeholder="结束时间"
              v-model="item.end"
              style="width: 136px"
            ></TimePicker>
            <SIcon
              class="icon-delete"
              icon="solid-close"
              :size="14"
              @click="removeItem(index)"
            />
          </div>
          <div v-for="(time, childIndex) in item.rule" class="hoursItem__rules" :key="time.key">
            <div class="ruleLabel" v-if="formValidate.link_type != 1">
              <SIcon
                class="ruleNumber"
                icon="arrow-solid-up"
                :size="14"
              />
              {{ '规则' + (childIndex + 1) }}：<span class="ruleDelete" @click="deleteRule(index, childIndex)" v-if="item.rule.length > 1">删除</span>
            </div>
            <template v-if="isOperatingTimeBindDeliveryDate || (currentRow.link_type && currentRow.link_type != 1)">
              <Row
                class="margin-bottom"
                :gutter="10"
                align="middle"
                type="flex"
              >
                <Col>可选发货日期:</Col>
                <Col
                  ><DeliveryDay v-model="time.delivery_start"></DeliveryDay
                ></Col>
                <Col>-</Col>
                <Col
                  ><DeliveryDay v-model="time.delivery_end"></DeliveryDay
                ></Col>
              </Row>
              <Row
                class="margin-bottom"
                :gutter="10"
                align="middle"
                type="flex"
              >
                <Col>{{ formValidate.link_type == 2 ? '当天' : '可选' }}送货时间:</Col>
                <Col>
                  <DeliveryTime
                    :multiple="true"
                    :clearable="true"
                    :showAll="false"
                    v-model="time.delivery_time_id"
                  ></DeliveryTime>
                </Col>
                <Col v-if="formValidate.link_type == 2">
                  <Tooltip
                    :transfer="true"
                    :delay="0"
                    :maxWidth="246"
                    content="该配置仅影响当天下单时可选择的送货时间，次日及之后可以选择【配置 - 商城运营 - 送货时间】中所有设置的送货时间"
                    placement="top"
                  >
                    <SIcon icon="help1" :size="12"/>
                  </Tooltip>
                </Col>
              </Row>
            </template>

            <Row
              :gutter="10"
              class="margin-bottom"
              align="middle"
              type="flex"
              v-if="formValidate.link_type != 3"
            >
              <Col><CategorySelectForMultiple v-if="showAddModal" :twoLine="true" v-model="time.can_buy_commodity_cate" /></Col>
            </Row>
            <template v-else>
              <Row
                :gutter="10"
                class="margin-bottom"
                align="middle"
                type="flex"
              >
                <Col class="form-item-label">运费规则:</Col>
                <Col>
                  <Select placeholder="请选择运费规则" v-model="time.template_freight_code" filterable clearable>
                    <Option :value="item.code" v-for="(item, index) in freightList" :key="index">{{ item.name }}</Option>
                  </Select>
                </Col>
                <Tooltip
                  :transfer="true"
                  :delay="0"
                  :maxWidth="246"
                  placement="top"
                >
                  <div slot="content">
                    <p>配送时段与客户类型同时绑定运费规则时，优先取配送时段绑定的运费规则；</p>
                    <p>运费规则仅可选择下单时生效模式下非重量计算方式</p>
                  </div>
                  <SIcon icon="help1" :size="12"/>
                </Tooltip>
              </Row>
              <Row
                :gutter="10"
                class="margin-bottom"
                align="middle"
                type="flex"
              >
                <Col class="form-item-label">关联商品:</Col>
                <Col>
                  已关联商品<span class="primaryColor" @click="handleAddGoods(childIndex, 1)">{{ time.can_buy_commodity_id.length }}</span>个
                  <span class="primaryColor" @click="handleAddGoods(childIndex, 2)">添加新商品</span>
                </Col>
              </Row>
            </template>
            <Row
              :gutter="10"
              class="margin-bottom"
              align="middle"
              v-show="showPackageGoods"
              type="flex">
              <Col align="right" style="width: 89px;">套餐商品:</Col>
              <Col>
                <RadioGroup v-model="time.is_all_package">
                  <Radio :disabled="isCategory" label="1">全部套餐</Radio>
                  <Radio :disabled="isCategory" label="0">部分套餐</Radio>
                </RadioGroup>
              </Col>
            </Row>
            <Row
              :gutter="10"
              class="margin-bottom"
              align="middle"
              v-show="showPackageGoods"
              type="flex">
              <Col align="right" style="padding-left: 94px; text-align: left;">
                <Button
                  type="primary"
                  :disabled="Number(time.is_all_package) === 1"
                  @click="handleSelectPackageGoods(time)">添加套餐</Button>
                  <span style="color: rgba(0,0,0,0.5);margin-left: 12px;">此为非必添加项，整个运营时间段均未添加套餐，则所有套餐均不允许售卖。</span>
              </Col>
            </Row>
          </div>
          <Button @click="handleAddRulesItem(index)" class="hoursItem__rules--add" styleType="btnStyleForAdd" v-if="formValidate.link_type != 1">新增规则</Button>
        </div>
        <Row v-if="!hoursItem.length || (hoursItem.length < 5 && formValidate.link_type != 3)" style="margin-top: 10px">
          <Col span="24">
            <Button type="dashed" style="width: 100%;" long @click="handleAddHoursItem"><Icon type="md-add" style="margin-right: 2px;"></Icon>新增时间段</Button>
          </Col>
        </Row>
        <div class="tips" style="margin-top: 10px;">
          <cross-day-delivery-date-tips></cross-day-delivery-date-tips>
        </div>
      </base-block>
      <base-block title="说明">
        <div class="desc">
          <p>1.设置下单起止时间后，客户可以在下单起止时间内去微信商城下单，下单起止时间段外不允许微信商城下单。</p>
          <p>2.多时间段运营，运营时间段不允许重叠</p>
          <p>3.如果打开了发货日期可选，那么客户在商城端选择的发货日期只能是设置好的发货日期和以后发货日期</p>
          <p>4.如果关闭了发货日期可选，那么下单只能选择这个发货日进行下单。</p>
          <p>5.如果打开了送货时间可选那么送货时间设置的只是一个默认值，如果关闭了送货时间那么送货时间只能是设置好的送货时间，除非在客户档案中设置好了送货时间。</p>
        </div>
      </base-block>
    </Form>
    </DetailPage>

    <goods-modal
      v-if="goodsModal.show"
      :show="goodsModal.show"
      :type="goodsModal.type"
      :added-goods="addedGoods"
      @on-cancel="handleSwitchGoodsShow"
      @on-add="handleSubmitGoods"
      @on-delete="handleDeleteGoods">
    </goods-modal>

    <PackageGoodsModal ref="packageGoodsModal" />
  </div>
</template>

<script>
import DeliveryDay from '../../components/settings/delviery-day/index.vue';
import DeliveryTime from '../../components/delivery/deliveryTimeSelect.vue';
import CategorySelectForMultiple  from '@components/common/CategorySelectForMultiple';
import DetailPage from '@/components/detail-page/index.js';
import crossDayDeliveryDateTips from '@components/common/crossDayDeliveryDateTips';
import settings from '@api/settings.js';
import SIcon from '@components/icon';
import ConfigMixin from '@/mixins/config';
import GoodsModal from './components/goods-modal.vue';
import Button from '@components/button';
import PackageGoodsModal from './components/PackageGoodsModal.vue';
import { mapState } from 'vuex';

const operatingWeekDayList = [
  {
    label: '周一',
    value: '1'
  },
  {
    label: '周二',
    value: '2'
  },
  {
    label: '周三',
    value: '3'
  },
  {
    label: '周四',
    value: '4'
  },
  {
    label: '周五',
    value: '5'
  },
  {
    label: '周六',
    value: '6'
  },
  {
    label: '周日',
    value: '0'
  }
]

const GOODS_KEY = 'id'

export default {
  name: 'openingHoursConfigEdit',
  mixins: [ConfigMixin],
  data() {
    return {
      tempFormValidate: {},
      tempHoursItem: {},
      currentRow: {},
      showAddModal: true,
      showPackageModal: true,
      saving: false,
      isSave: false,
      isOperatingTimeBindDeliveryDate: false,
      freight_cal_range_mode: 0,
      freightList: [],
      hoursItem: [],
      formValidate: {
        link_type: '',
        title: '',
        description: '',
        week: operatingWeekDayList.map(item => item.value)
      },
      goodsModal: {
        show: false,
        type: 2,
        index: 0
      },
      operatingWeekDayList,
      ruleValidate: {
        title: [{ required: true, message: '名称为必填', trigger: 'blur' }]
      }
    }
  },
  components: {
    PackageGoodsModal,
    Button,
    SIcon,
    DetailPage,
    DeliveryDay,
    DeliveryTime,
    crossDayDeliveryDateTips,
    CategorySelectForMultiple,
    GoodsModal
  },
  computed: {
    ...mapState({
			sysConfig: 'sysConfig'
		}),
    isCategory  () {
      return +this.formValidate.link_type === 2
    },
    showPackageGoods() {
      const linkType = [
        '1', // 按运营时间关联送货时间
        '2'  // 按分类关联送货时间
      ];
      return Number(this.sysConfig.is_commodity_package) === 1 && // 开启套餐
        Number(this.sysConfig.commodity_package_mode) === 2 && // 按套餐定价
        linkType.includes(this.formValidate.link_type)
    },
    addedGoods() {
      const rules = this.hoursItem[0] && this.hoursItem[0].rule
      let can_buy_commodity_id = []

      if (this.goodsModal.type === 2) {
        // 添加的话, 去掉所有规则下的已添加的商品
        rules.map(val => {
          can_buy_commodity_id = [...can_buy_commodity_id, ...val.can_buy_commodity_id]
        })
      } else {
        can_buy_commodity_id = rules[this.goodsModal.index].can_buy_commodity_id
      }

      return can_buy_commodity_id
    }
  },
  methods: {
    handleSelectPackageGoods(rule) {
      if (this.$refs.packageGoodsModal) {
        const can_buy_package_id = rule.can_buy_package_id || [];
        this.$refs.packageGoodsModal.open(can_buy_package_id || []).then(data => {
          rule.can_buy_package_id = data
        })
      }
    },
    handleSubmitGoods(goodsList) {
      let can_buy_commodity_id = this.hoursItem[0].rule[this.goodsModal.index].can_buy_commodity_id

      goodsList.map((goods) => {
        if (!can_buy_commodity_id.find((fGoods) => fGoods[GOODS_KEY] === goods[GOODS_KEY])) {
          can_buy_commodity_id.push(goods)
        }
      })
      this.handleSwitchGoodsShow()
    },
    handleDeleteGoods(goodsList) {
      let can_buy_commodity_id = this.hoursItem[0].rule[this.goodsModal.index].can_buy_commodity_id

      goodsList.map((goods) => {
        if (can_buy_commodity_id.find((fGoods) => fGoods[GOODS_KEY] === goods[GOODS_KEY])) {
          can_buy_commodity_id.splice(can_buy_commodity_id.findIndex((fGoods) => fGoods[GOODS_KEY] === goods[GOODS_KEY]), 1)
        }
      })
      this.handleSwitchGoodsShow()
    },
    handleSwitchGoodsShow() {
      this.goodsModal.show = !this.goodsModal.show
    },
    handleAddGoods(index, type) {
      this.goodsModal.index = index
      this.goodsModal.type = type
      this.handleSwitchGoodsShow()
    },
    handleCheckData() {
      try {
        this.hoursItem.forEach(item => {
          if (item.delivery_start - item.delivery_end > 0) {
            throw '可选发货日期开始日期不能大于结束日期';
          }
        });
        return true;
      } catch (err) {
        this.modalError(err, 0);
        return false;
      }
    },
    handleSubmit() {
      let me = this;
      if (!this.handleCheckData()) {
        return false;
      }
      let hoursItem = this.deepClone(this.hoursItem);
      // 如果选择了一级分类，但是没有选择二级分类，这里需要阻断提交
      let needBreak = false;
      // 按商品的话, 是否有选择商品
      let needGoods = false;
      // 保存在一个字段的分类信息，接口需要一二级分类分开传，这里做下拆分
      hoursItem.forEach(items => {
        items.rule.map(item => {
          const can_buy_commodity_cate = item.can_buy_commodity_cate || []
          // 按运营时间关联送货时间
          // 按分类关联送货时间
          if (+this.formValidate.link_type !== 3) {
            if (can_buy_commodity_cate[0] && !can_buy_commodity_cate[1]) {
              needBreak = true
            }
            if (can_buy_commodity_cate[1])  {
              item.can_buy_commodity_cate = can_buy_commodity_cate[1].split(',')
              item.can_buy_first_commodity_cate = can_buy_commodity_cate[0].split(',')
            } else {
              delete item.can_buy_commodity_cate
            }
            // 套餐商品选了全部商品，删除选择的套餐商品
            if (Number(item.is_all_package) === 1) {
              delete item.can_buy_package_id
            }
          } else {
            item.can_buy_commodity_id = item.can_buy_commodity_id.map(val => val[GOODS_KEY])
            // 按商品关联送货时间
            if (!item.can_buy_commodity_id.length) {
              needGoods = true
            }
          }
        })
      })

      if (needBreak) {
        return this.$Message.error('已选择一级分类未选择二级分类无法提交，请重新选择');
      }

      if (needGoods) {
        return this.$Message.error('请先关联商品');
      }

      this.saving = true

      this.$refs.formValidate.validate(valid => {
        if (valid) {
          let params = {
            ...this.formValidate,
            time_bucket: hoursItem
          };

          // 按分类的话是放在rules, 按运营时段放外面
          if (+params.link_type === 1) {
            params.time_bucket = params.time_bucket.map(val => {
              val = { ...val, ...val.rule[0] }
              delete val.rule
              return val
            })
          }

          let postUrl = 'addOperatingTime';
          if (this.$route.query.type === 'edit') {
            postUrl = 'updateOperatingTime';
            params.id = this.currentRow.id;
          }
          settings[postUrl]({ data: JSON.stringify(params) }).then(res => {
            this.saving = false

            if (res.status) {
              me.showAddModal = false;
              me.$Modal.success({
                title: '提示',
                content: this.$route.query.type === 'edit' ? '更新成功' : '添加成功'
              });
              this.isSave = true
              this.$router.go(-1)
            } else {
              me.$Modal.error({
                title: '提示',
                content: res.message
              });
            }
          });
        }
      });
    },
    handleChangeType() {
      // 更换类型直接置空
      this.hoursItem = []
    },
    handleAddRulesItem(index) {
      this.hoursItem[index].rule.push(
        {
          key: Math.random(),
          delivery_start: '',
          delivery_end: '',
          can_buy_commodity_cate: ['', ''],
          can_buy_commodity_id: [],
          template_freight_code: ''
        }
      )
    },
    handleAddHoursItem() {
      if (!this.formValidate.link_type) {
        this.$Modal.error({
          title: '提示',
          content: '请先选择运营时段关联类型'
        });
        return
      }

      if (+this.formValidate.link_type === 3 && this.hoursItem.length > 0) {
        this.$Modal.error({
          title: '提示',
          content: '按商品关联送货时间暂不支持多个时间段'
        });
        return
      }

      const Obj = {
        key: Math.random(),
        start: '',
        method: '',
        end: '',
        rule: [
          {
            delivery_start: '',
            delivery_end: '',
            can_buy_commodity_cate: ['', ''],
            can_buy_commodity_id: [],
            template_freight_code: '',
            can_buy_package_id: [],
            is_all_package: '1'
          }
        ]
      }
      // 如果是按分类关联送货时间， 不可选择全部套餐
      if (+this.formValidate.link_type === 2) {
        Obj.rule[0].is_all_package = '0'
      }
      this.hoursItem.push(Obj)
    },
    handleGetFreightList() {
      const options = {
        freight_cal_range_mode: this.freight_cal_range_mode,
        is_operation_use: 1,
        page: 1,
        pageSize: 1000
      }

      this.$request.get(this.apiUrl.freight.templateList, options).then(res => {
        if (res.status) {
          this.freightList = res.data.list
        }
      })
    },
    getConfig() {
      this.commonService.getConfig().then(config => {
        this.freight_cal_range_mode = config.freight_cal_range_mode
        this.isOperatingTimeBindDeliveryDate =
          Number(config.is_operating_time_bind_delivery_date) === 1
      });
    },
    deleteRule(parentIndex, childIndex) {
      this.hoursItem[parentIndex].rule.splice(childIndex, 1)
    },
    removeItem(index) {
      this.hoursItem.splice(index, 1)
    },
    render() {
      if (this.$route.query.type === 'edit') {
        const rowData = JSON.parse(sessionStorage.getItem('operatingTime'))
        this.formValidate.title = rowData.title
        this.formValidate.link_type = rowData.link_type
        this.formValidate.week = Array.isArray(rowData.week) ? rowData.week : []
        this.formValidate.description = rowData.description

        this.hoursItem = this.cloneObj(rowData.time_bucket).map(item => {
          item.method = item.method === '今日' ? 'today' : 'tomorrow'
          // 组装成统一个数据格式
          if (+rowData.link_type === 1) {
            const { can_buy_commodity_cate, can_buy_first_commodity_cate, delivery_end, delivery_start, delivery_time_id, can_buy_package_id, is_all_package = '1' } = item
            item.rule = [{
              can_buy_package_id,
              can_buy_commodity_cate,
              can_buy_first_commodity_cate,
              delivery_end,
              delivery_start,
              delivery_time_id,
              is_all_package
            }]
            // 清空, 避免后台解析混乱
            delete item.can_buy_package_id
            delete item.can_buy_commodity_cate
            delete item.can_buy_first_commodity_cate
            delete item.delivery_end
            delete item.delivery_start
            delete item.delivery_time_id
          }

          if (+rowData.link_type === 3) {
            item.rule.map(val => {
              val.can_buy_commodity_id = val.can_buy_commodity_id.map(val => {
                return {
                  [GOODS_KEY]: val
                }
              })
            })
          }
          if (item.rule) {
            item.rule.forEach(val => {
              // 如果是按分类关联送货时间， 不可选择全部套餐
              if (+rowData.link_type === 2) {
                val.is_all_package = '0'
              }
            })
          }
          return item
        })

        this.currentRow = rowData

        this.tempHoursItem = this.deepClone(this.hoursItem)
        this.tempFormValidate = this.deepClone(this.formValidate)

      } else {
        this.tempHoursItem = this.$options.data.call(this).hoursItem
        this.tempFormValidate = this.$options.data.call(this).formValidate
      }
    }
  },
  beforeRouteLeave(to, from, next) {
    const modified = JSON.stringify(this.tempFormValidate) !== JSON.stringify(this.formValidate) || JSON.stringify(this.tempHoursItem) !== JSON.stringify(this.hoursItem)

    if (modified && !this.isSave) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function() {
          next();
        },
        onCancel: function() {
          next(false);
        }
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  created() {
    this.render()
    this.getConfig()
    // 获取按商品运营所需的运费模板信息
    this.handleGetFreightList()
  }
}
</script>
