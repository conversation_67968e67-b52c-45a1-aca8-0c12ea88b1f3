<!--
 * @Descripttion: 首页 / 新版打印
 * @url: http://sdpdev.sdongpo.com/superAdmin/view#/settings/print-template
 * @Author: lizhiwei 🙂
-->
<template>
  <div>
    <div id="tfooter"></div>
    <div
      :style="{ height: getTableHeight() + 110 + 'px' }"
      class="editor"
      v-show="showEditor"
    >
       <iframe
        class="editor__frame"
        ref="editor"
        :src="webPrinterUrl"
        frameborder="0"
      ></iframe>

    </div>
    <div v-show="!showEditor" class="list-padding-left list-padding-right">
      <Tabs @on-click="changeTemplate" v-model="filters.type">
        <TabPane
          :label="tab.TYPE_NAME"
          :name="tab.TYPE"
          v-for="(tab, index) in typeList"
          :key="index"
        >
        </TabPane>
      </Tabs>
      <ListTable
        ref="list"
        :filters="filters"
        :show-pagination="false"
        :border="false"
        :outer-border="true"
        :max-line="2"
        row-key="id"
        :advance="false"
        :columns="columns"
        :data-provider="apiUrl.getPrintTemplate"
        :before-request="beforeRequest"
        :autoLoadData="true"
      >
        <div slot="before-table">
          <Button styleType="btnStyleForAdd" @click="handleAdd">新增</Button>
        </div>
      </ListTable>
    </div>
  </div>
</template>

<script>
import { subscribe } from '@sdptest/webprinter/dist/webprinter.common';
import webPrint from '@/util/print';
import Tabs from '@components/tabs';
import ListTable from '@components/list-table';
import settings from '@api/settings.js';
import Goods from '@api/goods.js';
import ConfigMixin from '@/mixins/config';
import { _handleSummaryPickConfigLinkage } from '@util/print/summaryPick.js';
import { _handleOrderConfigLinkage } from '@util/print/order.js';
import { handlePaginationData } from '@/util/print/order-pagination';
import { handleDataSourceByTemplate } from '@/util/print/@extraConfig';
import { get } from '@/api/request';
import { addCompatiblePoint } from '@/util/print';
import {
  PRINT_CONFIG,
  setDefaultHooks,
  setProcessOrderPrintConfig,
  setOrderPrintConfig,
  setSummaryPickPrintConfig,
  formatSummaryPickData,
  formatOrderData,
  formatMutiData,
  formatPickOrderData,
} from '@/util/print';
import Vue from 'vue';
import { debounce } from 'lodash-es';

// 模版自定义字段配置
const customFieldsConfig = {
  // 客户对账单
  SOA: {
    // 主体信息自定义字段
    '表头/表尾区字段': [
      3, // 订单主体信息自定义字段
      6,  // 客户对账单自定义字段
      14, // 客户自定义字段
    ]
  },
	PUR: {
		'表头/表尾区字段': [
			7
		]
	},
	PUR_TAKE: {
		'表头/表尾区字段': [
			7
		]
	},
	PUR_RETURN: {
		'表头/表尾区字段': [
			7
		]
	},
	IN_STORAGE: {
		'表头/表尾区字段': [
			7
		]
	},
	PURCHASE_BILL: {
		'表头/表尾区字段': [
			7
		]
	}
};

export default {
  name: 'PrintTemplate',
  mixins: [ConfigMixin],
  components: {
    Tabs,
    ListTable
  },
  props: {},
  data() {
    return {
      isOpenNewPage: true, // 通过新增,编辑,复制打开时为新开page设置为true
      COPY_PRINT_CONFIG: null, // 副本
      template: null,
      templateEdit: null,
      showEditor: false,
      currentType: null,
      typeList: [],
      customizeFieldKeys: [], // 商品自定义字段
      orderMainCustomizeFieldKeys: [], // 订单主体信息自定义字段
      orderGoodsCustomizeFieldKeys: [], // 订单商品信息自定义字段
      traceCustomizeFieldKeys: [], // 商品朔源自定义字段
      customerMainCustomizeFieldKeys: [], // 客户自定义字段
      filters: {
        type: '',
        version: 2
      },
      columns: [
        {
          width: 80,
          title: '模板ID',
          key: 'id'
        },
        {
          title: '模板名称',
          key: 'name'
        },
        {
          width: 80,
          title: '宽',
          key: 'page_width'
        },
        {
          width: 80,
          title: '高',
          key: 'page_height'
        },
        {
          width: 80,
          title: '上边距',
          key: 'page_top'
        },
        {
          width: 80,
          title: '左边距',
          key: 'page_left'
        },
        {
          width: 140,
          title: '是否每页显示表头',
          key: 'is_show_header',
          render: (h, params) => {
            let data = params.row;
            return h('span', data.is_show_header == '1' ? '是' : '否');
          }
        },
        {
          width: 160,
          title: '底部显示收货人签名',
          key: 'is_show_sign',
          render: (h, params) => {
            let data = params.row;
            return h('span', data.is_show_sign == '1' ? '是' : '否');
          }
        },
        {
          title: '修改时间',
          key: 'update_time'
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          type: 'action',
          width: 210,
          actionCountLimit: 3,
          actions: params => {
            const actions = [
              {
                name: '导出',
                action: ({ row }) => {
                  let url = this.apiUrl.exportPrintTemplate + '?id=' + row.id;
                  window.open(url, '_blank');
                }
              },
              {
                name: '复制',
                action: params => {
                  this.$Modal.confirm({
                    content: '是否要复制此打印模板？',
                    onOk: () => {
                      this._handleCopyTemplate(params);
                    }
                  });
                }
              },
              {
                name: '编辑',
                action: params => {
                  this.handleEditTemplate(params);
                }
              }
            ];
            if (parseInt(params.row.it_can_delete)) {
              actions.push({
                name: '删除',
                confirm: '确认删除此打印模版？',
                action: this.apiUrl.deletePrintTemplate,
                param: 'id',
                class: params => {
                  return parseInt(params.row.it_can_delete) ? {} : { dn: true };
                },
                success: () => {
                  this.refreshList();
                }
              });
            }
            return actions;
          }
        }
      ]
    };
  },
  computed: {
    webPrinterUrl() {
      // return 'https://192.168.30.173:8888/webprint/'
      if (window.printEditorUrl) {
        return window.printEditorUrl;
      }
      if (window._ccode && window._ccode.length > 1) {
        return (
          window._ccode + 'webprint/index.html' + `?v=${new Date().getTime()}`
        );
      }
      return `/webprint/index.html?v=${new Date().getTime()}`;
    }
  },
  async created() {
    this.getTemplateType();
    this.unsubscribe = subscribe({
      onPreview: ({ template }) => {
        if (!this.checkTemplate(template)) {
          return;
        }
        this.template = template;
        // 打印预览
        this.handlePreview();
      },
      onClose: () => {
        this.template = null;
        this.closeEditor();
      },
      onSave: ({ template }) => {
        if (!this.checkTemplate(template)) {
          this.openEditor(template);
          return;
        }
        this.template = template;
        this.saveTemplate();
      },
      // 加防抖避免有些情况下频繁触发导致死循环
      onUpdateConfig: debounce(({ template }) => {
        this._handleConfigChange(template);
        this.setEditorTemplate(template);
      }, 300),
      // 打开新模板时执行一次函数
      onOpenNewPage: ({ template }) => {
        this._handleConfigChange(template);
      }
    });
    await this._getCustomizeFieldKeys();
    await this._getOrderMainCustomizeFieldKeys();
    await this._getOrderGoodsCustomizeFieldKeys();
		// await this._getProviderMainCustomizeFieldKeys();
    await this._getTraceCustomizeFieldKeys();
    await this._getCustomerMainCustomizeFieldKeys();
    await this._getCustomFields();
    this.COPY_PRINT_CONFIG = this._filterOverallTemplateDataSource();
  },
  beforeDestroy() {
    this.$root.$emit('menuModeChange', false);
    // 组件销毁时取消打印事件订阅，不然会产生重复的订阅
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  },
  beforeRouteLeave(to, from, next) {
    if (this.showEditor) {
      this.closeEditor()
    }
    else {
      next();
    }
  },
  methods: {
    /**
     * 从接口获取自定义字段
     * @param {*} type
     */
    _getCustomFields(type) {
      const customFieldsData = {}
      if (!type) {
        let typeArr = [];
        for(const templateType in customFieldsConfig) {
          const templateFiledConfig = customFieldsConfig[templateType];
          if (templateFiledConfig) {
            Object.values(templateFiledConfig).forEach(item => {
              if (Array.isArray(item)) {
                typeArr = typeArr.concat(item);
              } else {
                typeArr.push(item);
              }
            });
          }
        }
        const showInfo = {
          14: this.sysConfig.is_open_customer_customize_field == 1,
        }
        typeArr = typeArr.filter(type => {
          // 未开启客户自定义字段，则打印模版不显示客户自定义字段
          if (type == 14 && showInfo[type] === false) {
            return false;
          }
          return true;
        })
        type = type || typeArr.join();
      }
      return get('/superAdmin/commodityCustomizeField/customizeFieldKeys', {
        customize_type: type
      }).then(res => {
        if (res.data) {
          res.data.forEach((item) => {
            const { customize_type } = item;
            if (!customFieldsData[customize_type]) {
              customFieldsData[customize_type] = [];
            }
            customFieldsData[customize_type].push({
              key: item.key,
              label: item.name,
              labelShow: item.name
            });
          });
          this.customFieldsData = customFieldsData;
        }
      })
    },
    /**
     * 根据模版类型获取自定义字段
     * @param {*} templateKey 模版key
     * @param {*} section 添加自定义字段的区域，如：表头/表尾区字段
     */
    _getCustomFieldsData(templateKey, section) {
      if (!section) {
        return [];
      }
      let customFieldsData = [];
      const config = customFieldsConfig[templateKey];
      if (config && config[section]) {
        const fieldType = config[section];
        console.log({config, fieldType, customFieldsData: this.customFieldsData});
        if (typeof fieldType === 'number') {
          customFieldsData = this.customFieldsData[fieldType] || [];
        }
        if (Array.isArray(fieldType)) {
          fieldType.forEach(item => {
            customFieldsData = customFieldsData.concat(this.customFieldsData[item] || []);
          });
        }
      }
      console.log({customFieldsData});
      return customFieldsData;
    },
    /**
     * @description: 提前获取订单主体信息自定义字段
     */

    async _getOrderMainCustomizeFieldKeys() {
      this.orderMainCustomizeFieldKeys = [];
      const { data } = await Goods.getCustomizeFieldKeys({
        customize_type: '3'
      });
      this.orderMainCustomizeFieldKeys =
        Array.isArray(data) &&
        data.map(item => {
          return {
            key: item.key,
            label: item.name,
            labelShow: item.name
          };
        });
    },
    /**
     * @description: 提前获取订单商品信息自定义字段
     */

    async _getOrderGoodsCustomizeFieldKeys() {
      this.orderGoodsCustomizeFieldKeys = [];
      const { data } = await Goods.getCustomizeFieldKeys({
        customize_type: '4'
      });
      this.orderGoodsCustomizeFieldKeys =
        Array.isArray(data) &&
        data.map(item => {
          return {
            key: item.key,
            label: item.name,
            labelShow: item.name
          };
        });
    },
		/**
		 * @description: 提前获取订单主体信息自定义字段
		 */

		async _getProviderMainCustomizeFieldKeys() {
			this.orderMainCustomizeFieldKeys = [];
			const { data } = await Goods.getCustomizeFieldKeys({
				customize_type: '7'
			});
			this.orderMainCustomizeFieldKeys =
				Array.isArray(data) &&
				data.map(item => {
					return {
						key: item.key,
						label: item.name,
						labelShow: item.name
					};
				});
		},
    async _getCustomerMainCustomizeFieldKeys() {
      if (+this.sysConfig.is_open_customer_customize_field !== 1) return;
      this.customerMainCustomizeFieldKeys = [];
      const { data } = await Goods.getCustomizeFieldKeys({
        customize_type: '14'
      });
      this.customerMainCustomizeFieldKeys =
        Array.isArray(data) &&
        data.map(item => {
          return {
            key: item.key,
            label: item.name,
            labelShow: item.name
          };
        });
    },
    /**
     * @description: 提前获取自定义字段
     */
    async _getCustomizeFieldKeys() {
      this.customizeFieldKeys = [];
      const { data } = await Goods.getCustomizeFieldKeys({
        customize_type: '0,4'
      });
      this.customizeFieldKeys =
        Array.isArray(data) &&
        data.map(item => {
          return {
            key: item.key,
            label: item.name,
            labelShow: item.name
          };
        });
    },
    /**
     * @description: 提前获取商品朔源自定义字段
     */
    async _getTraceCustomizeFieldKeys() {
      this.traceCustomizeFieldKeys = [];
      const { data } = await Goods.getCustomizeFieldKeys({
        customize_type: 1
      });
      this.traceCustomizeFieldKeys =
        Array.isArray(data) &&
        data.map(item => {
          return {
            key: item.key,
            label: item.name,
            labelShow: item.name
          };
        });
    },
    $_setTraceCustomizeFieldKeys(items) {
      return [...items, ...this.traceCustomizeFieldKeys];
    },
    $_setCustomizeFieldKeys(items) {
      return [...items, ...this.customizeFieldKeys];
    },
    $_setOrderMainCustomizeFieldKeys(items) {
      return [...items, ...this.orderMainCustomizeFieldKeys];
    },
    $_setOrderGoodsCustomizeFieldKeys(items) {
      return [...items, ...this.orderGoodsCustomizeFieldKeys];
    },
    $_setCustomerCustomizeFieldKeys(items) {
      return [...items, ...this.customerMainCustomizeFieldKeys];
    },
    /**
     * @description: 根据系统配置,以及 show 参数配置.  全局过滤不显示某些字段.
     * {
          "label": "到期日期",
          "key": "expire_date",
          "show": 'is_batch === 0'
        }
     */
    _filterOverallTemplateDataSource() {
      const COPY_PRINT_CONFIG = this.deepClone(PRINT_CONFIG);
      // 数据源:COPY_PRINT_CONFIG
      // 系统配置,this.sysConfig . 通过mixin引入
      for (let key in COPY_PRINT_CONFIG) {
        // 拿到数据源
        const template = COPY_PRINT_CONFIG[key];
        const dataSourceList = template.config.dataSourceList;
        // 循环数据源
        dataSourceList.forEach(source => {
          const HEADER_FOOTER_TITLE = '表头/表尾区字段';
          // 自定义字段title: '商品信息'
          if (key === 'ORDER' && source.type === 'Table') {
            const customFields = this.$_setCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
          }
          if (
            (key === 'PROTOCOL_PRICE' && source.type === 'Table') ||
            (key === 'GROUP_ORDER' && source.type === 'Table') ||
            (key === 'PICK' && source.title === '商品信息')
          ) {
            const customFields = this.$_setCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
            if (key === 'PICK' && source.title === '商品信息') {
              const orderCustomFields = this.$_setOrderMainCustomizeFieldKeys([]);
              if (orderCustomFields) {
                const orderInfo = source.items.find(item => item.title === '订单信息');
                orderInfo.items.push({
                  title: '订单自定义字段',
                  items: orderCustomFields
                });
              }
            }
          }
          // 发货单主体信息自定义字段
          if (key === 'GROUP_ORDER' && source.title === HEADER_FOOTER_TITLE) {
            const customFields = this.$_setOrderMainCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
          }
          // 发货单主体信息自定义字段 表头/表尾
          if (key === 'ORDER' && source.title === HEADER_FOOTER_TITLE) {
            const mainInfoIndex = source.items.findIndex(item => item.title === '主体信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
            const orderCustomFields = this.$_setOrderMainCustomizeFieldKeys([]);
            const customerCustomFields = this.$_setCustomerCustomizeFieldKeys([]);
            if (orderCustomFields.length > 0 || customerCustomFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: [...orderCustomFields, ...customerCustomFields]
              });
            }
          }
          // 发货单 表格区字段
          if (key === 'ORDER' && source.type === 'Table') {
            const mainInfoIndex = source.items.findIndex(item => item.title === '商品信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
          }
          if (key === 'ORDER_RETURN' && source.title === HEADER_FOOTER_TITLE) {
            const customerCustomFields = this.$_setCustomerCustomizeFieldKeys([]);
            if (customerCustomFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: [...customerCustomFields]
              });
            }
          }
          // 客户对账单主体信息自定义字段
          if (key === 'SOA' && source.title === HEADER_FOOTER_TITLE) {
            const mainInfoIndex = source.items.findIndex(item => item.title === '主体信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
          }
          // 客户对账单主体信息自定义字段
          if (key === 'SOA' && source.type === 'Table') {
            const mainInfoIndex = source.items.findIndex(item => item.title === '商品信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
            const customFields = this.$_setOrderGoodsCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
          }
          // 分拣
          if (key === 'PICK' && source.type === 'Normal') {
            if (source.title === '商品信息') {
              const mainInfoIndex = source.items.findIndex(item => item.title === '订单信息')
              const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
              if(!this.isOpenNewRecipe) {
                source.items[mainInfoIndex].items.splice(existIndex, 1)
              }
            } else if (source.title === '收货人信息') {
              const customFields = this.$_setCustomerCustomizeFieldKeys([]);
              if (customFields.length > 0) {
                source.items.push(...customFields);
              }
            }
          }

          // 分拣 摘果
          // SUMMARY_PICK
          if (key === 'SUMMARY_PICK' && source.type === 'Table') {
            const mainInfoIndex = source.items.findIndex(item => item.title === '商品信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
          }
          if (key === 'SUMMARY_PICK' && source.type === 'Normal') {
            const mainInfoIndex = source.items.findIndex(item => item.title === '主体信息')
            const existIndex = source.items[mainInfoIndex].items.findIndex(item => item.title === '菜谱信息')
            if(!this.isOpenNewRecipe) {
              source.items[mainInfoIndex].items.splice(existIndex, 1)
            }
          }
          // --------------------------------商品朔源,检测报告单--------------------------------
          if (key === 'SOURCE_TRACE' && source.type === 'Table') {
            const customFields = this.$_setTraceCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
          }
          // 发货检测单，检测属性自定义字段
          if (key === 'ORDER_CHECK' && source.type === 'Table') {
            const customFields = this.$_setTraceCustomizeFieldKeys([]);
            if (customFields.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFields
              });
            }
          }

          // 定义字段
          if (customFieldsConfig[key] && customFieldsConfig[key][source.title]) {
            const customFieldsData = this._getCustomFieldsData(key, source.title);
            if (customFieldsData && customFieldsData.length > 0) {
              source.items.push({
                title: '自定义字段',
                items: customFieldsData
              });
            }
          }

          source.items = source.items || [];
        });

        // 根据配置过滤字段
        const filterDatasourceByConfig = dataSourceList => {
          // 循环配置
          return dataSourceList.filter(item => {
            if (item.items) {
              item.items = filterDatasourceByConfig(item.items);
            }
            // 如果配置了show属性, show必须为 is_open_order_tag === 1 这种格式.
            // 多个配置项共同影响时，格式为 config_a === 1 && config_b === 2
            //  配置项 === 值
            if (item.show !== undefined) {
              const configArr = item.show.split(/(?:&&)/); // 将&&作为分隔符转为数组
              return configArr.reduce((prev, next) => {
                let operator = next.includes('!==') ? '!==' : '==='
                const configKey = next.split(operator)[0].trim(); // 拿到系统配置key值
                const configValue = next.split(operator)[1].trim();
                if (next.includes('!==')) {
                  return prev && +this.sysConfig[configKey] !== +configValue
                }
                return prev && +this.sysConfig[configKey] === +configValue;
              }, true);
            } else {
              return true;
            }
          });
        };
        filterDatasourceByConfig(dataSourceList);
      }
      return COPY_PRINT_CONFIG;
    },
    checkTemplate(template) {
      if (!template.config.name) {
        this.errorMessage('模版名称不能为空！');
        return false;
      }
      if (!template.children || template.children.length === 0) {
        this.errorMessage('模版内容不能为空！');
        return false;
      }
      if (template.config.paperWidth - template.config.offsetLeft < 0) {
        this.errorMessage('纸张左边距不能大于纸张宽度！');
        return false;
      }
      if (template.config.paperHeight - template.config.offsetTop < 0) {
        this.errorMessage('纸张上边距不能大于纸张高度！');
        return false;
      }
      return true;
    },
    /**
     * @description: 切换tab
     * @param {*} type 切换对应类型
     */
    changeTemplate(type) {
      this.typeList.forEach(item => {
        if (item.TYPE === type) {
          this.currentType = item;
        }
      });
      this.filters.type = type;
      this.refreshList();
    },
    /**
     * @description: 请求对应类型模板数据
     */
    refreshList() {
      this.$refs.list.fetchData();
    },
    /**
     * @description: 获取所有的模板.tab切换
     */
    getTemplateType() {
      settings.getPrintTemplateType().then(res => {
        if (res.status && res.data.list && res.data.list.length > 0) {
          // 过滤出已完成重构的模块
          const refactorModules = [
            'ORDER',
            'PRE_PACKAGE',
            'GROUP_ORDER',
            'PUR',
            'PUR_TAKE',
            'PUR_RETURN',
            'SOURCE_TRACE', // 检测报告单
            'ACCT_BILL_OD', // 订单记录
            'PICK', // 分拣单
            'SOA', // 客户对账单
            'IN_STORAGE', // 入库单
            'OUT_STORAGE',
            'SUMMARY', // 拣货单
            'CHECK', // 盘点单
            'ORDER_RETURN', // 订单退货单
            'PROCESS', // 加工单
            'SUMMARY_PICK', // 拣货单摘果
            'ACCT_BILL_CS', // 客户对账单商品分类汇总
            'PRE_BILL_CS', // 采购对账单商品分类汇总
            'PUR_SETTLE', //采购结算单
            'PACKAGE', // 套餐订单
            'GET_METERIA', // 领料单
            'RETURN_METERIA', //退料单
            'COMPLETE', // 完工单
            'TAG', // 贴签打印单
            'TRANSFER_ORDER', // 调拨单
            'ORDER_MODIFY', // 实收变更单
            'ORDER_CHECK', // 发货检测单
            'PROTOCOL_PRICE', //协议价
            'PURCHASE_BILL', //采购对账单
            'ACCOUNT_SETTLE', //客户结算单
            'LOAD_BARCODE', //  装框条码打印单
            'PURCHASE_PAY', // 采购付款单
            'CUS_IN_STOCK', // 客户入库单
            'CUS_OUT_STOCK', // 客户出库单
            'CUS_PURCHASE', // 客户采购单
            'CUS_INVENTORY', // 客户盘点
            'OUT_TRANSFER',  // 门店调拨出库单
            'IN_TRANSFER',  // 门店调拨入库单
            'NORMAL_BARCODE', // 条码
            'GOODS_SUM_PICK', //商品汇总拣货单
            'LOSS_OVER', //报损报溢单。
          ];
          this.typeList = res.data.list.filter(item => {
            if (!this.$hasModule(item.TYPE)) {
              return false;
            }
            return refactorModules.includes(item.TYPE);
          });
          this.filters.type = this.typeList[0]['TYPE'];
          this.currentType = this.typeList[0];
          if (this.$refs.list) {
            this.refreshList();
          }
        }
      });
    },
    beforeRequest(params) {
      if (!params.type) {
        return false;
      }
      return params;
    },
    /**
     * @description: 打开设计器
     * @param {*} template 模板数据
     * @return {*}
     */
    openEditor(template) {
      this.showEditor = true;
      if (template) {
        this.$root.$emit('menuModeChange', true);
        template = this._filterTemplateDataSource(template);
        console.log('openEditor', template);
        this.setEditorTemplate(template);
      }
    },
    /**
     * 根据系统配置过滤掉某些数据源
     * @param {Object} template 打印模版数据
     */
    _filterTemplateDataSource(template) {
      // 采购进项税相关字段
      const purchaseTaxColumns = [
        'input_tax_rate',
        'input_tax',
        'tax_exclusive',
        'total_tax_exclusive'
      ];
      // 订单服务费相关字段
      const orderServiceChargeColumns = [
        'service_charge',
        'order_service_charge',
        'should_pay_price_contain_service_charge'
      ];
      (template.config.dataSourceList || []).forEach(item => {
        item.items = item.items.filter(dataSourceItem => {
          // 没有开启进项税、过滤掉进项税相关字段
          if (
            ['PUR', 'PUR_TAKE', 'PUR_RETURN'].includes(this.filters.type) && // 采购单、采购收货、采购退货模版
            !this.isEnableInput_tax_rate && // 没有开启进项税
            purchaseTaxColumns.includes(dataSourceItem.key)
          ) {
            return false;
          }
          // 没有开启服务费过滤掉服务费相关字段
          if (
            ['ORDER'].includes(this.filters.type) && // 订单模版
            !this.isOpenServiceCharge && // 没有开启服务费
            orderServiceChargeColumns.includes(dataSourceItem.key)
          ) {
            return false;
          }
          return true;
        });
      });
      return template;
    },
    setEditorTemplate(template) {
      this.initDefaultTemplateData(template);
      const iframeWin = this.$refs.editor.contentWindow;
      const printConfig = this.deepClone(
        this.COPY_PRINT_CONFIG[this.filters.type]
      );
      // 分类汇总字段设置默认值设置
      if (template.config.category_group_fields === undefined) {
        template.config.category_group_fields = ['count', 'total_price'];
      }
      let previewConfig = {
        template: this.deepClone(template),
        data: printConfig.data,
        hook: {}
      };
      // 通用自定义底部信息
      if (
        [
          'PUR',
          'PUR_TAKE',
          'PUR_RETURN',
          'SOA',
          'CHECK',
          'SUMMARY',
          'PROCESS',
          'IN_STORAGE',
          'OUT_STORAGE',
          'SOURCE_TRACE',
          'ACCT_BILL_OD',
          'ORDER_RETURN',
          'SUMMARY_PICK',
          'ORDER_CHECK'
        ].includes(this.filters.type)
      ) {
        previewConfig = setDefaultHooks(previewConfig);
      }
      // 加工单特殊样式
      if (['PROCESS'].includes(this.filters.type)) {
        previewConfig = setProcessOrderPrintConfig(previewConfig);
      }
      // 设置订单打印配置
      if (
        ['ORDER', 'GROUP_ORDER', 'PROTOCOL_PRICE'].includes(this.filters.type)
      ) {
        previewConfig = setOrderPrintConfig(previewConfig);
        previewConfig.data = handleDataSourceByTemplate(
          previewConfig.data,
          previewConfig.template
        );
        // previewConfig.data = printConfig.data.map(order => {
        //   return formatOrderData(order, this.template.config)
        // });
      }
      // 设置分拣单打印配置
      if (['PICK'].includes(this.filters.type)) {
        previewConfig.data = formatPickOrderData(
          printConfig.data,
          template.config
        );
      }
      // 设置拣货单摘果
      if (['SUMMARY_PICK'].includes(this.filters.type)) {
        previewConfig = setSummaryPickPrintConfig(previewConfig);
        previewConfig.data = formatSummaryPickData(
          previewConfig.data,
          template.config
        );
      }

      let footerHtml = '';
      const tableItem = template.children.find(item => item.type === 'Table');
      if (
        tableItem &&
        previewConfig.hook &&
        previewConfig.hook.items &&
        previewConfig.hook.items.footer
      ) {
        const footerRender = previewConfig.hook.items.footer;
        const Footer = Vue.extend({
          render(h) {
            const overflowStyle = {
              width: '100%',
              wordBreak: 'keep-all',
              whiteSpace: 'nowrap',
              overflow: 'visible',
              textOverflow: 'ellipsis'
            };
            const tableBorder = '1px solid #000';
            const bordered = template.config.tableBordered !== 'N';
            const cellCssStyle = { ...tableItem.style, ...tableItem.bodyStyle };
            let cellStyle = {
              minHeight:
                cellCssStyle.fontSize * cellCssStyle.lineHeight + 6 + 'px',
              borderBottom: bordered ? tableBorder : 'none',
              borderRight: bordered ? tableBorder : 'none',
              align: 'middle',
              textAlign: cellCssStyle.textAlign,
              padding: '2px 4px',
              fontSize: cellCssStyle.fontSize + 'px',
              fontFamily: cellCssStyle.fontFamily,
              letterSpacing: cellCssStyle.letterSpacing + 'px',
              lineHeight: cellCssStyle.lineHeight,
              fontWeight: cellCssStyle.fontWeight || '700',
              fontStyle: cellCssStyle.fontStyle || 'normal',
              textDecoration: cellCssStyle.textDecoration || 'initial'
            };
            // 固定模式
            if (template.config.is_auto_adaptive_table_size === 'N') {
              cellStyle = {
                ...cellStyle,
                ...overflowStyle
              };
            }
            return h(
              'div',
              footerRender({
                h,
                columns: tableItem.props.columns,
                borderStyle: tableBorder,
                cellStyle,
                mainData: previewConfig.data[0],
                rows: previewConfig.data[0].items
              })
            );
          }
        });
        const footerEl = new Footer().$mount().$el;
        // 处理footerEl中tr下td的colspan，如果总和大于列数，从后往前减
        const handleTdColspan = (footerEl) => {
          const trs = footerEl.querySelectorAll('tr');
          trs.forEach(tr => {
            const tds = tr.querySelectorAll('td');
            let colspan = 0;
            tds.forEach(td => {
              colspan += td.getAttribute('colspan') ? +td.getAttribute('colspan') : 1;
            });
            if (colspan > tableItem.props.columns.length) {
              let diff = colspan - tableItem.props.columns.length;
              for (let i = tds.length - 1; i >= 0; i--) {
                if (diff <= 0) {
                  break;
                }
                const td = tds[i];
                const tdColspan = td.getAttribute('colspan') ? +td.getAttribute('colspan') : 1;
                if (tdColspan > diff) {
                  td.setAttribute('colspan', tdColspan - diff);
                  break;
                } else {
                  td.setAttribute('colspan', 1);
                  diff -= tdColspan;
                }
              }
            }
          });
        };
        handleTdColspan(footerEl);
        footerHtml = footerEl.innerHTML;
        if (footerHtml) {
          footerHtml = footerHtml.replace('第#页', '第1页');
          footerHtml = footerHtml.replace('共#页', '共1页');
          footerHtml = footerHtml.replace(
            'format="#,##0.00" tindex="5">#<',
            'format="#,##0.00" tindex="5">84<'
          );
          footerHtml = footerHtml.replace(
            'format="UpperMoney" tindex="5">#<',
            'format="UpperMoney" tindex="5">捌拾肆圆整<'
          );
        }
      }
      console.log({
        template
      })
      iframeWin.postMessage(
        {
          // previewConfig,
          tableHookRender: { footer: footerHtml },
          demoData: this.deepClone(printConfig.data[0]),
          printTemplate: template || previewConfig.template,
          isOpenNewPage: this.isOpenNewPage
        },
        '*'
      );
    },
    closeEditor() {
      // 展开二级菜单
      this.$root.$emit('menuModeChange', false);
      this.showEditor = false;
    },
    /**
     * @description: 预览模板
     */
    handlePreview() {
      // this._saveTemplate()
      //   .then(() => {
      const printConfig = this.deepClone(
        this.COPY_PRINT_CONFIG[this.filters.type]
      );
      if (!printConfig || !printConfig.data) {
        this.errorMessage('没有预览数据');
        return;
      }
      try {
        let previewConfig = {
          template: this.deepClone(this.template),
          data: printConfig.data,
          hook: {}
        };
        const inAllPageItems = previewConfig.template.config.inAllPageItems;
        // 通用自定义底部信息
        if (
          [
            'PUR',
            'PUR_TAKE',
            'PUR_RETURN',
            'SOA',
            'CHECK',
            'SUMMARY',
            'PROCESS',
            'IN_STORAGE',
            'OUT_STORAGE',
            'SOURCE_TRACE',
            'ACCT_BILL_OD',
            'ORDER_RETURN',
            'SUMMARY_PICK',
            'ORDER_CHECK'
          ].includes(this.filters.type)
        ) {
          previewConfig = setDefaultHooks(previewConfig);
        }
        // 加工单特殊样式
        if (['PROCESS'].includes(this.filters.type)) {
          previewConfig = setProcessOrderPrintConfig(previewConfig);
        }
        // 设置订单打印配置
        if (
          ['ORDER', 'GROUP_ORDER', 'PROTOCOL_PRICE'].includes(this.filters.type)
        ) {
       let nodeIndex = previewConfig.template.children.findIndex(e=>e.type === 'Table')
       const isNeedActualSubPriceEl = (config) => { // 自定义设置是否需要实价空元素 计算本页小计
        if(!config.customField || config.customField.length<=0) return false
        const lineTypes = config.customField.flatMap(item => item.lineType || []);
        return lineTypes.includes('is_show_actual_sub_price_total') || lineTypes.includes('is_show_actual_sub_price_total_capital');
      }
      //  打印不勾选实际价格
       if(
          nodeIndex>=0  &&
        (previewConfig.template.config.is_show_actual_sub_price_total  ||
        previewConfig.template.config.is_show_actual_sub_price_total_capital) ||
        isNeedActualSubPriceEl(previewConfig.template.config)
       ){
       let index =  previewConfig.template.children[nodeIndex].props.columns.findIndex(res=>res.key=='actual_sub_price')
       if(index==-1){
        previewConfig.template.children[nodeIndex].props.columns.push({
          key:'actual_sub_price',
          title:'',
          width:0,
          hidden:true
        })
       }
       }
          previewConfig = setOrderPrintConfig(previewConfig, this.filters.type);
          previewConfig.data = printConfig.data.map(order => {
            return formatOrderData(order, this.template.config);
          });
        }
        if(['PUR','PUR_TAKE'].includes(this.filters.type)){
            previewConfig.data = printConfig.data.map(order => {
            return formatMutiData(order, this.template.config);
           });
        }
        // 设置分拣单打印配置
        if (['PICK'].includes(this.filters.type)) {
          previewConfig.data = formatPickOrderData(
            printConfig.data,
            this.template.config
          );
        }
        // 设置拣货单摘果
        if (['SUMMARY_PICK'].includes(this.filters.type)) {
          previewConfig = setSummaryPickPrintConfig(previewConfig);
          previewConfig.data = formatSummaryPickData(
            previewConfig.data,
            this.template.config
          );
        }
        previewConfig.data = handleDataSourceByTemplate(
          previewConfig.data,
          previewConfig.template
        );
        // 订单类型
        if (
          this.filters.type === 'ORDER' &&
          inAllPageItems &&
          inAllPageItems.includes('pageInfoInAllPage')
        ) {
          const _template = this.deepClone(previewConfig.template);
          _template.config.inAllPageItems = inAllPageItems;
          handlePaginationData(previewConfig.data, _template);
        }
        webPrint(previewConfig);
      } catch (err) {
        console.error(err);
        throw '预览失败';
      }
      // })
      // .catch(this.errorMessage);
    },
    /**
     * @description: 点击新增模板
     */
    handleAdd() {
      this.isOpenNewPage = true;
      this.templateEdit = null;
      // 需要在 PRINT_CONFIG 里面配置模板
      if (!this.filters.type || !this.COPY_PRINT_CONFIG[this.filters.type]) {
        this.errorMessage('不存在模版配置');
        return false;
      }
      let template;
      // if (this.filters.type === 'SOURCE_TRACE') {
      //   template = DEAULT_PRINT_CONFIG[this.filters.type];
      // } else {
        template = this.COPY_PRINT_CONFIG[this.filters.type];
        template.children = [];
        template.config.is_kg_print = 'N';
        template.config.tableBordered = 'Y';
      // }
      // 只有这次调整后新增的模版，行高自适应支持拖动高度才生效
      addCompatiblePoint('table_row_height_mode_auto', template.config);
      this.openEditor(template);
    },
    /**
     * @description: 从源模板复制打印模板
     * @param {object} params 当前操作的行，模板相关数据
     */
    _handleCopyTemplate(params) {
      this.isOpenNewPage = true;
      const { row } = params;
      let template = null;
      try {
        template = JSON.parse(row.tpl_data);
      } catch (err) {
        this.errorMessage(err);
        return false;
      }
      if (!template) {
        this.errorMessage('模版数据异常');
        return false;
      }

      const defaultTemplate = this.COPY_PRINT_CONFIG[this.filters.type];
      // 纸张大小实时取
      template.config.pageSizeOptions = defaultTemplate.config.pageSizeOptions;
      // 数据源实时取
      template.config.dataSourceList = defaultTemplate.config.dataSourceList;

      this.templateEdit = null;
      template.config.name = template.config.name + '复制1';
      if (template.config.is_auto_adaptive_table_size) {
        delete template.config.is_auto_adaptive_table_size;
      }
      this.template = template;
      this.templateEdit = null;
      // 只有这次调整后新增的模版，行高自适应支持拖动高度才生效
      addCompatiblePoint('table_row_height_mode_auto', template.config);
      this.openEditor(template);
    },
    initDefaultTemplateData(template) {
      const defaultTemplate = PRINT_CONFIG[this.filters.type];
      // 纸张大小实时取
      template.config.pageSizeOptions = defaultTemplate.config.pageSizeOptions;
      // 数据源实时取
      // template.config.dataSourceList = defaultTemplate.config.dataSourceList;

      // 每页显示信息配置实时取
      template.inAllPageItems = defaultTemplate.inAllPageItems;

      // 表尾设置只有订单发货单、集团订单发货单会显示
      if (defaultTemplate.inAllPageItems && ['ORDER', 'GROUP_ORDER'].includes(this.filters.type)) {
        template.inAllPageItems.forEach(item => {
          // 每页显示表尾统一添加：跟随表格|固定底部 的配置
          if (item.value === 'footerInAllPage') {
            item.config = {
              key: 'fixedFooter',
              type: 'RadioGroup',
              defaultValue: 'N',
              items: [
                {
                  label: '跟随表格',
                  value: 'N'
                },
                {
                  label: '固定底部',
                  value: 'Y'
                },
              ]
            }
          }
        });
        // 主体信息表尾，默认跟随表格
        if (!template.config.fixedFooter) {
          template.config.fixedFooter = 'N';
        }
      }

      // 额外配置实时取
      template.extraConfig =
        template.extraConfig || defaultTemplate.extraConfig || [];

      // 不手动设置 children 就会丢失 很奇怪 可能是函数形参的问题
      template.children = template.children;
    },
    /*
     * @description: 点击编辑模板
     * @param {*} params 表格行内容
     */
    handleEditTemplate(params) {
      this.isOpenNewPage = true;
      let { row } = params;
      let template = null;
      try {
        template = JSON.parse(row.tpl_data);
      } catch (err) {
        this.errorMessage(err);
        return false;
      }
      if (!template) {
        this.errorMessage('模版数据异常');
        return false;
      }
      const defaultTemplate = this.COPY_PRINT_CONFIG[this.filters.type];
      // 纸张大小实时取
      template.config.pageSizeOptions = defaultTemplate.config.pageSizeOptions;
      // 数据源实时取
      template.config.dataSourceList = defaultTemplate.config.dataSourceList;
      // 每页显示信息配置实时取
      template.inAllPageItems = defaultTemplate.inAllPageItems;
      // 额外配置实时取
      template.extraConfig = defaultTemplate.extraConfig || [];
      template.config.hideSave = row.is_consortium_sync == 1
      this.template = template;
      this.templateEdit = params.row;
      this.adoptCode(template);
      // 公斤打印配置兼容
      if (!template.config.kg_print_mode && template.config.is_kg_print == 'Y') {
        if (template.config.unit_show_kg === 'Y') {
          template.config.kg_print_mode = '千克';
        } else {
          template.config.kg_print_mode = '公斤';
        }
      }
      this.openEditor(template);
    },
    adoptCode(template) {
      template.config.paperDirection = template.config.paperDirection || '1';
      if (template.config.sort_type != 'sort_vertical') {
        template.config.sort_type = 'sort_horizontal';
      }
      if (!template.config.print_format&&!template.config.print_line) {
        // 兼容数据处理
        if (template.config.print_style == 'category_group') {
          template.config.print_line = 'column_1';
          template.config.print_format = 'category_group';
        } else if (template.config.print_style == 'default') {
          template.config.print_line = 'column_1';
          template.config.print_format = 'default';
        } else if (template.config.print_style == 'multi_column_2') {
          template.config.print_line = 'column_2';
          template.config.print_format = 'default';
        } else if (template.config.print_style == 'multi_column_3') {
          template.config.print_line = 'column_3';
          template.config.print_format = 'default';
        } else if (
          template.config.print_style
        ) {
          template.config.print_line = 'column_1';
          template.config.print_format = 'default';
        }else if(!template.config.print_style){
          template.config.print_line = 'column_1';
          template.config.print_format = 'default';
        }
      }
      if (!template.config.user_order_detail_split) {
        template.config.user_order_detail_split = 'N';
      }
    },
    /**
     * @description: 保存模板
     */
    saveTemplate() {
      this._saveTemplate()
        .then(message => {
          this.successMessage(message);
          this.closeEditor();
          this.refreshList();
        })
        .catch(error => {
          this.errorMessage(error);
        });
    },
    /**
     * @description: 调用保存模板接口
     * @return {*} Promise
     */
    _saveTemplate() {
      const template = this.template;
      const config = template.config;
      const templateEdit = this.templateEdit;
      template.version = '1.0.0';
      template.config.printerKey = this.currentType.PRINTER_KEY;
      (template.children || []).map(item => {
        let containerProps = item.containerProps;
        containerProps.y = Math.ceil(containerProps.y);
        containerProps.x = Math.ceil(containerProps.x);
        containerProps.w = Math.ceil(containerProps.w);
        containerProps.h = Math.ceil(containerProps.h);
      });
      // 如果是新增模版
      if (!this.templateEdit) {
        if (template.config.is_auto_adaptive_table_size) {
          delete template.config.is_auto_adaptive_table_size;
        }
        // 供应商小程序tsc指令集二维码宽度兼容
        addCompatiblePoint('tsc_qrcode_width_compatible', template.config);
        addCompatiblePoint('tsc_qrcode_width_compatible_v2', template.config);
      }
      if (template.config.is_show_page_no === undefined) {
        template.config.is_show_page_no = false;
      }
      // 如果开启按商品分类汇总，将每页显示行数设置为0
      if (template.config.print_format === 'category_group') {
        template.config.row_num = 0;
      }
      // 公斤打印配置兼容
      if (template.config.kg_print_mode) {
        if (template.config.kg_print_mode !== '无') {
          template.config.kg_print_unit = template.config.kg_print_mode;
          template.config.is_kg_print = 'Y';
        } else {
          template.config.is_kg_print = 'N';
          template.config.kg_print_unit = '';
        }
        if (template.config.kg_print_mode === '千克') {
          template.config.unit_show_kg = 'Y';
        } else {
          template.config.unit_show_kg = 'N';
        }
      }
      const params = {
        type: this.filters.type,
        tpl_data: JSON.stringify(template),
        base_field: '-',
        goods_field: '-',
        is_default: '0',
        extral: 'cross'
      };
      if (this.templateEdit) {
        params.id = templateEdit.id;
        params.is_default = templateEdit.is_default;
      }
      params.other_data = {
        name: config.name,
        print_direct: config.printDirection,
        page_width: config.paperWidth,
        page_height: config.paperHeight,
        page_top: config.offsetTop,
        page_left: config.offsetLeft
      };
      params.other_data = JSON.stringify(params.other_data);
      config.inAllPageItems = config.inAllPageItems || [];
      (params.is_show_header =
        config.inAllPageItems.indexOf('theadInAllPage') > -1 ? 1 : 0),
        (params.is_show_sign =
          config.inAllPageItems.indexOf('_receiver_sign') > -1 ? 1 : 0),
        (params.version = 2);
      return new Promise((resolove, reject) => {
        this.$request
          .post('/superAdmin/printerConfig/saveData', params)
          .then(res => {
            let { status, message } = res;
            if (status) {
              resolove('保存成功！');
            } else {
              this.openEditor(this.template);
              reject(message || '保存失败！');
            }
          })
          .catch(res => {
            this.openEditor(this.template)
            reject(res.data.message || '保存失败！')
          });
      });
    },
    /**
     * @description: 判断是否能需要更新
     * @param: 模板数据
     * @return {Boolean} true能, false 不能
     */
    _canRefresh(e) {
      const type = this.filters.type;
      // 首次打开默认更新一次
      if (this.isOpenNewPage) {
        this.isOpenNewPage = false;
        return true;
      }
      // 订单发货单
      if (
        (type === 'ORDER' ||
          type === 'GROUP_ORDER' ||
          type === 'PROTOCOL_PRICE') &&
        this.template &&
        (this.template.config.print_style !== e.config.print_style ||
          this.template.config.print_format !== e.config.print_format ||
          this.template.config.is_commodity_class_level !==
            e.config.is_commodity_class_level)
      ) {
        // 需要联动
        return true;
      }

      // 摘果单
      if (
        type === 'SUMMARY_PICK' &&
        this.template &&
        this.template.config.is_merge_same_commodity !==
          e.config.is_merge_same_commodity
      ) {
        return true;
      }
      return false;
    },
    /**
     * @description: 处理模板配置发生改变时的情况,用作配置联动
     * @param {*} 模板
     */
    _handleConfigChange(e) {
      if (!this._canRefresh(e)) return;
      // 需要处理配置联动的模板
      const needHandleTemplate = {
        SUMMARY_PICK: _handleSummaryPickConfigLinkage,
        ORDER: _handleOrderConfigLinkage,
        GROUP_ORDER: _handleOrderConfigLinkage,
        PROTOCOL_PRICE: _handleOrderConfigLinkage
      };
      if (!e.config || !this.template || !needHandleTemplate[this.filters.type])
        return;
      const handleFunc = needHandleTemplate[this.filters.type];

      const templateIncludeDatasource = this.deepClone(this.template);
      if (!templateIncludeDatasource.config.dataSourceList) {
        const defaultTemplate = this.COPY_PRINT_CONFIG[this.filters.type];
        templateIncludeDatasource.config.dataSourceList =
          defaultTemplate.config.dataSourceList;
      }

      this.template = handleFunc(this.deepClone(e), templateIncludeDatasource);
      this.openEditor(this.template);
    }
  }
};
</script>
<style lang="less" scoped>
.editor {
  &__frame {
    width: 100%;
    height: 100%;
  }
}
/deep/.ivu-tabs-nav-wrap {
  display: flex;
  align-items: center;
}
</style>
