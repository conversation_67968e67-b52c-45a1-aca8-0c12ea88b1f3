<template>
  <div class="base-wrapper2">
    <ListTable
      ref="listTable"
      :height="getTableHeight() - 78"
      outer-border
      :columns="columns"
      :filter-items="filterItems"
      :data-provider="apiUrl.getOperatorList"
    >
      <template #before-table>
        <div style="display: flex;justify-content: space-between;align-items: center;">
          <template v-if="sysConfig.is_add_operator || currentEdition < 101">
            <SButton styleType="btnStyleForAdd" @click="_add">新增</SButton>
          </template>
          <template v-else>
            <Tooltip content="已超出可新增账号数量，请联系客户经理" max-width="220">
              <Button styleType="btnStyleForAdd" @click="_add" disabled>新增</Button>
            </Tooltip>
          </template>
          <span v-if="currentEdition >= 101">【{{ operator_number }} / {{ operator_max_number }}】</span>
        </div>
      </template>
    </ListTable>

    <Modal
      v-model="editModal.show"
      :title="editModal.title"
      :mask-closable="false"
      class-name="vertical-center-modal"
    >
      <Form :label-width="85" ref="operatorForm" :model="formData" :rules="ruleValidate">
        <FormItem label="用户名" prop="user_name">
          <Input :disabled="editModal.editing" v-model="formData.user_name" placeholder="用户名为登录账号，新增后不可修改" />
        </FormItem>
        <FormItem label="中文名" prop="cn_name">
          <Input v-model="formData.cn_name" />
        </FormItem>
        <FormItem label="手机号" prop="mobile">
          <Input maxlength="11" v-model="formData.mobile"/>
        </FormItem>
        <FormItem label="密码" prop="password">
          <Input v-model="formData.password" type="password" />
        </FormItem>
        <FormItem label="角色" prop="group_id">
          <Select v-model="formData.group_id" placeholder="请选择角色" style="text-align: left" @on-change="handleChangeGroup" label-in-value>
            <Option v-for="item in roleList" :value="item.id" :key="item.id">{{ item.group_name }}</Option>
          </Select>
        </FormItem>
        <FormItem
          label="仓库权限"
          prop="storage_ids"
          v-if="isMultiStorage && +formData.adminId !== +formData.group_id"
        >
          <Select v-model="formData.storage_ids" multiple :capture="false" style="text-align: left" :max-tag-count="30">
            <Option v-for="item in storeList" :key="item.id" :value="item.id">{{item.name}}</Option>
          </Select>
        </FormItem>
        <FormItem label="团餐权限" prop="organizeIds" v-if="sysConfig.tc_platform == 1">
          <span slot="label">团餐权限<Tooltip transfer
              placement="top"
              max-width="200" content="该权限是为团餐用户应用，仅供应链用户无需配置。">
            <SIcon
              icon="tips"
              style="cursor: pointer;margin-left: 2px;"
              :size="12"
            />
          </Tooltip></span>
          <Cascader :treeData="treeData" show-checkbox v-model="formData.organizeIds" style="width: 100%" placeholder="请选择团餐权限"></Cascader>
        </FormItem>
        <div v-show="formData.group_id == roleSorter && roleSorter">
          <FormItem label="设备地址" prop="device_addr">
            <Input v-model="formData.device_addr" />
          </FormItem>
          <FormItem label="设备密码" prop="device_addr">
            <Input v-model="formData.device_pwd" />
          </FormItem>
          <FormItem label="打印编号" prop="print_num">
            <Input v-model="formData.print_num" />
          </FormItem>
          <FormItem label="打印密码" prop="print_pwd">
            <Input v-model="formData.print_pwd" />
          </FormItem>
        </div>
      </Form>
      <div slot="footer">
        <i-button type="text" @click="editModal.show = false">取消</i-button>
        <i-button type="primary" :loading="loadingSave" @click="save('operatorForm')">保存</i-button>
      </div>
    </Modal>
  </div>
</template>

<script>
import ListTable from '@/components/list-table'
import SButton from '@/components/button'
import power from '@api/power.js'
import store from '@api/storeRoom.js'
import ConfigMixin from '@/mixins/config';
import SIcon from '@components/icon';
import tuancan from '@api/tuancan.js'
import Cascader from '@/components/cascader/index.vue';
import { uniq } from "lodash-es"

export default {
  name: "operator",
  mixins: [ConfigMixin],
  components: {
    ListTable,
    SButton,
    SIcon,
    Cascader
  },
  data () {
    // 校验密码规则
    const validatePass = (rule, value, callback) => {
      // 如果没传就不校验了
      if (!value) {
        callback()
        return
      }
      let flag = true
      // 校验数字是否出现
      let regNumber = /\d+/
      // 校验字母是否出现
      let regString = /[a-zA-Z]+/
      if (value.length < 8 || !regNumber.test(value) || !regString.test(value)) {
        flag = false
      }
      if (flag) {
        callback()
      } else {
        callback('请输入8位及以上字母+数字组合字符')
      }
    }
    return {
      loadingSave: false,
      isMultiStorage: false,
      editModal: {
        title: "操作员",
        show: false,
        editing: false,
        canEditMobile:true,
      },
      storeList: [],
      roleList: [],
      roleSorter: '',
      formData: {
        id: '',
        user_name: '',
        cn_name: '',
        password: '',
        group_id: '',
        device_addr: '',
        device_pwd: '',
        print_num: '',
        print_pwd: '',
        storage_ids: [],
        mobile:''
      },
      ruleValidate: {
        user_name: [
          { required: true, message: '用户名不能为空', trigger: 'blur' }
        ],
        cn_name: [
          { required: true, message: '姓名不能为空', trigger: 'blur' }
        ],
        password: [
          { required: false, message: '密码不能为空', trigger: 'blur' },
          { validator: validatePass }
        ],
        group_id: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ],
        storage_ids: [
          {
            required: true, message: '请设置仓库权限', validator: (rule, value, callback) => {
              if (!value || value.length == 0) {
                callback(new Error('请设置仓库权限'))
              } else {
                callback()
              }
            }, trigger: 'change'
          }
        ],
        mobile:[
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\d{8}$/,
            message: "手机号错误，请检查",
          },
        ]
      },
      filterItems: [
        {
          type: 'Select',
          label: '角色筛选',
          key: 'group_id',
          data: [],
          props: {
            placeholder: '全部'
          }
        },
        {
          type: 'Input',
          label: '搜索',
          key: 'cn_name',
          props: {
            placeholder: '请输入中文名'
          }
        }
      ],
      columns: [
        {
          title: 'ID',
          key: 'id',
          width: 80,
          fixed: 'left',
          resizable: false
        },
        {
          title: '用户名',
          key: 'user_name',
          width: 200,
          fixed: 'left',
          resizable: false
        },
        {
          title: '中文名',
          key: 'cn_name'
        },
        {
          title: '角色',
          key: 'group_name'
        },
        {
          title: '创建时间',
          key: 'create_time'
        },
        {
          type: 'action',
          title: '操作',
          key: 'action',
          align: 'left',
          width: 110,
          actions: [
            {
              name: "编辑",
              action: params => {
                this._edit(params.row)
              }
            },
            {
              name: "删除",
              confirm: "确认删除此操作员？",
              action: async (param) => {
                // 开启团餐模式
                if (this.sysConfig.tc_platform == 1) {
                  try {
                    await tuancan.delTenantUser({ id: param.row.id }).then(res => {
                      if (res.resultCode !== 200) {
                        console.error(res.resultMsg);
                      }
                    })
                  } catch(e) {
                    console.error(e)
                  }
                }
                this.$request.post(this.apiUrl.deleteOperator, { id: param.row.id }).then(() => {
                  this.$Message.success('删除成功')
                  this.getOperatorNumber()
                  this._fetchData(false)
                })
              },
            }
          ]
        }
      ],
      operator_max_number: 0,
      operator_number: 0,
      treeData: [],
      roleName: ''
    }
  },
  created () {
    store.checkIsMultiStorage().then((res) => {
      if (res.status) {
        this.isMultiStorage = res.data
      }
    })
    this.getRoleList()
    this.getStoreList()
    this.getOperatorNumber()
  },
  methods: {
    _edit (row) {
      this.$refs.operatorForm.resetFields()
      this.editModal.show = true
      this.editModal.editing = true
      // 判断手机号是否可编辑
      if(row.mobile === ''){
        this.editModal.canEditMobile = true;
      }else{
        this.editModal.canEditMobile = false;
      }
      // 查找系统管理员的id
      this.roleList.forEach(item => {
        if (item.group_name === '系统管理员') {
          row.adminId = item.id
        }
      })

      function getAllPaths(nodes, currentPath = [], organizeSelectedIds) {
        let paths = [];

        for (const node of nodes) {
          // 创建当前节点的新路径
          const newPath = [...currentPath, node.enterpriseOrganizeIdStr];

          if (node.children && node.children.length > 0) {
            // 如果有子节点，递归处理子节点
            paths = paths.concat(getAllPaths(node.children, newPath, organizeSelectedIds));
          } else {
            // 如果没有子节点，将当前路径添加到结果中
            if (organizeSelectedIds.includes(node.enterpriseOrganizeIdStr)){
              paths.push(newPath);
            }
          }
        }

        return paths;
      }

      this.formData = this.cloneObj(row)
      // 开启团餐模式
      if (this.sysConfig.tc_platform == 1) {
        tuancan.getEnterpriseOrganizeTree().then(async res => {
          const resp = await tuancan.getTenantUser({
            id: row.id
          })
          this.treeData = res.data ? this.loop(res.data) : res.data
          this.formData.organizeIds = getAllPaths(res.data, [], resp.data && resp.data.organizeInfos ? resp.data.organizeInfos.map(item => item.organizeIdStr) : [])
        })
      }
    },
    resetFormData () {
      this.formData = {
        id: '',
        user_name: '',
        cn_name: '',
        password: '',
        group_id: '',
        device_addr: '',
        device_pwd: '',
        print_num: '',
        print_pwd: '',
        storage_ids: [],
      }
    },
    _add () {
      this.editModal.editing = false
      this.editModal.show = true
      this.$refs.operatorForm.resetFields()
      this.resetFormData()
      tuancan.getEnterpriseOrganizeTree().then(async res => {
        this.treeData = res.data ? this.loop(res.data) : res.data
      })
      this.roleList.forEach(item => {
        if (item.group_name === '系统管理员') {
          this.formData.adminId = item.id
        }
      })
    },
    async getRoleList () {
      let res = await power.getSelectRoleList()
      this.roleList = []
      if (res.status) {
        this.roleList = res.data
        this.roleList.find(item => {
          // 分拣员
          if (item.group_name === power.role.sorter.name) {
            this.roleSorter = item.id
            return true
          }
        })
        this.filterItems.find(item => {
          if (item.key === 'group_id') {
            const roleListForSelect = this.roleList.map(item => ({
              value: item.id,
              label: item.group_name
            }))
            roleListForSelect.unshift({
              value: '',
              label: '全部'
            })
            item.data = roleListForSelect
            return true
          }
        })
      }
    },
    save (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          let params = this.cloneObj(this.formData)
          params['storage_list'] = params['storage_ids']
          // 新增下，如果是系统管理员，默认勾选所有仓库权限
          if (!params.id && +this.formData.adminId === +this.formData.group_id) {
            params['storage_list'] = this.storeList.map(item => {
              return item.id
            })
          }
          if (this.loadingSave) return
          this.loadingSave = true
          power.saveOperator(params).then(async res => {
            const { status, message } = res
            if (status) {
              // 开启团餐模式
              if (this.sysConfig.tc_platform == 1) {
                if (this.formData.organizeIds && this.formData.organizeIds.length || this.formData.id) {
                  try {
                    await tuancan.saveTenantUser({
                      userName: this.formData.cn_name,
                      loginName: this.formData.user_name,
                      password: 'as123456', // this.formData.password
                      phoneNumber: this.formData.mobile,
                      baseRoleId: this.formData.group_id,
                      roleName: this.roleName || this.formData.group_name,
                      baseSuperUserId: res.data,
                      organizeIds: this.formData.organizeIds ? uniq(this.formData.organizeIds.flat(Infinity)) : this.formData.organizeIds
                    })
                  } catch(e) {
                    console.error(e)
                  }
                }
              }
              const editing = this.editModal.editing
              this.resetFormData()
              this.editModal.show = false
              this._fetchData(!editing, editing)
              this.successMessage('保存成功')
              this.getOperatorNumber()
            } else {
              this.errorNotice({ desc: message })
            }
          }).finally(() => {
            this.loadingSave = false
          })
        } else {
          this.errorMessage("请正确填写信息")
        }
      })
    },
    getStoreList () {
      power.getStoreList({
        page_size: 2000
      }).then((res) => {
        if (res.status) {
          this.storeList = res.data.list
        } else {
          this.storeList = []
        }
      })
    },
    _fetchData (resetPage, keepScroll) {
      this.$refs.listTable.fetchData(resetPage, keepScroll)
    },
    getOperatorNumber() {
      this.$request.get(this.apiUrl.operatorNumber).then(res => {
        this.operator_number = res.data.operator_number
        this.operator_max_number = res.data.operator_max_number
      })
    },
    loop(data) {
      return data.map(item => {
        if (item.children && item.children.length) {
          return {
            value: item.enterpriseOrganizeIdStr,
            label: item.enterpriseOrganizeName,
            children: this.loop(item.children)
          }
        } else {
          return {
            value: item.enterpriseOrganizeIdStr,
            label: item.enterpriseOrganizeName,
          }
        }
      })
    },
    handleChangeGroup(labelInValue) {
      this.roleName = labelInValue.label
    }
  }
}
</script>
<style lang="less" scoped>
/deep/ .s-cascader {
  .el-cascader {
    width: 100%!important;
  }
  .el-input {
    width: 100%!important;
  }
  .el-cascader.max-input .el-input {
    .el-input__inner {
      height: auto !important;
    }
  }
}
</style>
