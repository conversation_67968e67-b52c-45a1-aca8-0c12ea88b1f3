<template>
  <div class="business-config bgf" @scroll="handleScroll">
    <div
      class="group"
      v-for="(group, index) in groupList"
      :key="group.uid"
      :class="group.class"
    >
      <div
        v-if="group.title"
        class="group-title-box scroll-top"
        :id="group.title + 1"
      >
        <span class="group-title">{{ group.title }}</span>
      </div>
      <div
        class="group-content-box"
        v-for="(item, idx) in group.classify"
        :key="item.uid"
      >
        <div
          v-if="item.title"
          class="classify-title-box scroll-top"
          :id="item.title + 0"
        >
          <span class="classify-title">{{ item.title }}</span>
        </div>
        <div
          class="group-content"
          v-for="(config, inde) in item.configItems"
          :key="config.uid"
          :data-key="getEnv() !== 'prod' ? config.key : ''"
        >
          <div class="config-item" v-if="config.type === configType.custom">
            <!-- 确认送达可点击范围-->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'confirm_delivery_allow_coverage' && Number(configData.delivery_of_goods_must_be_positioned) === 1"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
              >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block;width: 98px;"
                  :precision="2"
                  :min="0"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="ml7">km</span>
                <span class="tip"
                >提示信息：必须开启"货物送达必须定位"，司机实时位置与客户收货定位直线距离在该距离内才能点击确认送达，设置为 0 时不限制。</span
                >
              </Col>
            </Row>
            <!--#region自定义的配置项-->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'sort_num_threshold'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block;width: 98px;"
                  :precision="2"
                  :min="0"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="ml7">％</span>
                <span class="tip"
                  >填写阈值，例如20%，分拣的时候数量如果超过或者低于客户下单量20%，系统会自动预警，如果不需要则留空；在【应用中心-分拣阈值管理】中可以针对商品分类进行阈值设置。</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'max_stock_limit_flag'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <div>
                  <RadioGroup
                    v-model="pre_purchase_config.max_stock_limit_flag"
                  >
                    <Radio label="1">
                      <span>库存上限</span>
                    </Radio>
                    <Radio label="0">
                      <span>上限备货天数</span>
                    </Radio>
                  </RadioGroup>
                  <InputNumber
                    :precision="0"
                    v-model="pre_purchase_config.max_pur_limit_days"
                  ></InputNumber
                  >*日均销量
                </div>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'min_stock_limit_flag'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <div>
                  <RadioGroup
                    v-model="pre_purchase_config.min_stock_limit_flag"
                  >
                    <Radio label="1">
                      <span>库存下限</span>
                    </Radio>
                    <Radio label="0">
                      <span>下限备货天数</span>
                    </Radio>
                  </RadioGroup>
                  <InputNumber
                    :precision="0"
                    v-model="pre_purchase_config.min_pur_limit_days"
                  ></InputNumber
                  >*日均销量
                </div>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'last_few_days'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <div>
                  前
                  <InputNumber
                    :precision="0"
                    v-model="pre_purchase_config.last_few_days"
                  ></InputNumber
                  >天日均销量
                </div>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'remember_pre_choice'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <RadioGroup v-model="pre_purchase_config.remember_pre_choice">
                  <Radio label="0">
                    <span>默认采购员/供应商</span>
                  </Radio>
                  <Radio label="1">
                    <span>记住上一次分配</span>
                  </Radio>
                </RadioGroup>
              </Col>
            </Row>

            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'is_open_purchase_task'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <div @click="showCantEditTips">
                  <RadioGroup
                    @on-change="changePurchaseTask"
                    v-model="configData.is_open_purchase_task"
                  >
                    <Radio :disabled="isOpenProviderDeliver" label="0">
                      <span>订单汇总</span>
                    </Radio>
                    <Tooltip
                      :transfer="true"
                      :delay="0"
                      :maxWidth="246"
                      content="汇总订单商品生成采购单，是一种供应商固定的以销定采模式"
                      placement="top"
                    >
                      <SIcon icon="help1" :size="12" />
                    </Tooltip>
                    <Radio :disabled="isOpenProviderDeliver" label="1">
                      <span>采购任务</span>
                    </Radio>
                    <Tooltip
                      :transfer="true"
                      :delay="0"
                      :maxWidth="246"
                      content="汇总订单商品后，可以将采购任务，按需分配给多个采购员或供应商，适用灵活变动供应商的采购模式"
                      placement="top"
                    >
                      <SIcon icon="help1" :size="12" />
                    </Tooltip>
                  </RadioGroup>
                </div>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="
                (config.key == 'purchase_task_receipt_default_amount' ||
                  config.key == 'order_summary_export_type') &&
                  config.show
              "
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col>
                <RadioGroup
                  @on-change="
                    onRadioGroupChange(config, item, configData[config.key])
                  "
                  v-model="configData[config.key]"
                >
                  <template v-for="(item, index) in config.items">
                    <Radio :label="item.value" :key="item.uid">
                      {{ item.label }}
                    </Radio>
                    <Tooltip
                      :transfer="true"
                      :delay="0"
                      :maxWidth="246"
                      :content="item.tips"
                      placement="top"
                      :key="item.uid + index"
                    >
                      <SIcon icon="help1" :size="12" />
                    </Tooltip>
                  </template>
                </RadioGroup>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'receipts_num_threshold'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block; width: 98px; height: 30px;"
                  :precision="2"
                  :min="0"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="ml7">％</span>
                <span class="tip"
                  >填写阈值，例如20%，采购收货的时候数量如果超过或者低于计划采购数20%，系统会自动预警，如果不需要则留空</span
                >
                <span class="preview-btn" @click="previewImg(config.tipsImgUrl)"
                  >查看示例</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'process_return_max_hour'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block; width: 142px;"
                  :precision="2"
                  :min="1"
                  :max="360"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="ml7">小时</span>
                <span class="tip"
                  >从审核加工单开始计算，最大支持15天(360小时)，超出时间后不可退料</span
                >
              </Col>
            </Row>
            <Row
              v-show="configData.point_enable"
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'point_lower_order_price'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block;width: 142px;"
                  :precision="2"
                  :min="0"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="tip"
                  >单个订单满{{
                    configData[config.key]
                      ? configData[config.key] + '元'
                      : 'x'
                  }}，可兑换积分商品
                </span>
                <span class="preview-btn" @click="previewImg(config.tipsImgUrl)"
                  >查看示例</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'point_ratio'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block;width: 142px;"
                  :disabled="
                    configDataMap[config.key] &&
                      Number(configDataMap[config.key].is_set) === 1
                  "
                  :min="0"
                  v-model="configData[config.key]"
                >
                </NumberInput>
                <span class="ml7">元 = 1积分</span>
                <span class="tip"
                  >客户在商城消费{{
                    configData[config.key] ? configData[config.key] : 'x'
                  }}元=1积分</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="
                config.key === 'order_summary_format' ||
                  config.key === 'is_show_purchase_no_depart'
              "
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col>
                <RadioGroup v-model="configData[config.key]">
                  <template v-for="(item, index) in config.items">
                    <Radio
                      style="margin-left: 5px"
                      :label="item.value"
                      :key="item.uid"
                      >{{ item.label }}</Radio
                    >
                    <Tooltip
                      :placement="
                        index === config.items.length - 1 ? 'left' : 'top'
                      "
                      class="white"
                      theme="light"
                      :key="item.uid + index"
                    >
                      <SIcon
                        v-if="item.tipsImg || item.tips"
                        icon="help1"
                        :size="12"
                      />
                      <div
                        slot="content"
                        :style="item.innerStyles ? item.innerStyles : {}"
                      >
                        <img
                          v-if="item.tipsImg"
                          :style="'width:' + item.imgWidth + 'px'"
                          :src="item.tipsImg"
                          alt=""
                        />
                        <p v-if="item.tips" style="white-space: break-spaces;">
                          {{ item.tips }}
                        </p>
                      </div>
                    </Tooltip>
                  </template>
                </RadioGroup>
              </Col>
            </Row>

            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key === 'use_new_sort_commodity'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col class="config-item-value">
                <i-switch
                  :before-change="setSortCommoditytype"
                  v-model="configData[config.key]"
                />
              </Col>
            </Row>

            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key === 'order_input_version'"
            >
              <Col
                v-if="config.label"
                class="config-item-label"
                :style="{
                  width: group.labelWidth + 'px'
                }"
                >{{ config.label }}
              </Col>
              <Col class="config-item-value">
                <i-switch
                  v-model="configData[config.key]"
                  :before-change="setOrderInputVersion"
                  true-value="2"
                  false-value="1"
                />
                <span v-if="config.tips" class="tip">{{ config.tips }}</span>
              </Col>
            </Row>

            <!-- 过期预警(开启批次管理才能配置) -->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'batch_stock_early_warn_day'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <NumberInput
                  style="display: inline-block;width:142px"
                  :disabled="
                    configDataMap[config.key] &&
                      Number(configDataMap[config.key].is_set) === 1
                  "
                  :min="0"
                  :max="365"
                  :precision="0"
                  v-model="configData[config.key]"
                  placeholder="请输入>0的数值"
                />
                <span class="tip">{{ config.tips }}</span>
                <span class="preview-btn" @click="previewImg(config.tipsImgUrl)"
                  >查看示例</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key === 'in_time_default_type' && configData.modify_store_time"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
              ></Col>
              <Col>
                <Checkbox
                  v-model="configData[config.key]"
                  true-value="1"
                  false-value="0"
                >采购入库单入库时间默认取采购单的计划交货日期+00:00:00</Checkbox>
                <Tooltip
                  :transfer="true"
                  :delay="0"
                  :maxWidth="246"
                  content="现场采购单、订单直采及联营采购单不受配置影响"
                  placement="top"
                >
                  <SIcon
                    icon="help1"
                    :size="12"
                    style="margin-left: 5px"
                  />
                </Tooltip>
              </Col>
            </Row>
            <!-- #region 朔源自定义配置开始 -->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="
                config.key == 'show_sys_commodity_trace_field' &&
                  configData['is_stsource_inspect']
              "
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Checkbox
                  v-model="show_sys_commodity_trace_field.is_show_control"
                  :true-value="1"
                  :false-value="0"
                  >对照值</Checkbox
                >
                <Checkbox
                  v-model="show_sys_commodity_trace_field.is_show_measured"
                  :true-value="1"
                  :false-value="0"
                  >测定值</Checkbox
                >
                <Checkbox
                  v-model="show_sys_commodity_trace_field.is_show_restrain"
                  :true-value="1"
                  :false-value="0"
                  >抑制率</Checkbox
                >
              </Col>
            </Row>
            <!-- #region 菜谱显示字段配置 -->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="
                config.key == 'raw_recipe_show_fields' &&
                  isShow(config)
              "
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Checkbox
                  v-model="raw_recipe_show_fields.summary"
                  :true-value="1"
                  :false-value="0"
                  >描述</Checkbox
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="
                config.key == 'meal_plan_audit' &&
                  isShow(config)
              "
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Checkbox
                  v-model="meal_plan_audit.is_group_audit"
                  :true-value="1"
                  :false-value="0"
                  >集团审核</Checkbox
                >
              </Col>
              <Col style="margin-left: 20px">
                <Checkbox
                  v-model="meal_plan_audit.is_super_audit"
                  :true-value="1"
                  :false-value="0"
                  >后台审核</Checkbox
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'trace_report_expiry_time'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Input
                  style="display: inline-block;width: 142px;"
                  v-model="configData[config.key]"
                  :maxlength="3"
                  type="text"
                  @on-change="inputLimit"
                >
                </Input>
                <span class="tip"
                  >商品溯源信息中显示的检测报告从检测单中读取信息,超过有效期后，溯源信息不显示检测单中的检测结果。</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'trace_device_url'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <span>
                  {{ configData[config.key] }}
                </span>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'trace_device_name'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Input
                  style="display: inline-block;width: 142px"
                  :maxlength="30"
                  show-word-limit
                  v-model="configData[config.key]"
                >
                </Input>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key == 'trace_device_password'"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col>
                <Input
                  :maxlength="30"
                  show-word-limit
                  style="display: inline-block;width: 142px"
                  v-model="configData[config.key]"
                >
                </Input>
              </Col>
            </Row>
            <!-- #endregion 朔源自定义配置结束 -->
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.key === 'in_price_audit' && !inPriceAuditDisabled"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col>
                <RadioGroup
                  @on-change="
                    onRadioGroupChange(config, item, configData[config.key])
                  "
                  v-model="configData[config.key]"
                  v-for="(item, index) in config.items"
                  :key="item.uid"
                >
                  <Radio
                    :class="index !== 0 ? 'ml13' : ''"
                    :disabled="inPriceAuditDisabled"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </Radio>
                  <Tooltip
                    :transfer="true"
                    :delay="0"
                    :maxWidth="246"
                    :content="item.tips ? item.tips : ''"
                    placement="top"
                  >
                    <SIcon
                      v-if="item.tips || item.tipsImgUrl"
                      icon="help1"
                      :size="12"
                    />
                  </Tooltip>
                  <span
                    v-if="item.tipsImgUrl"
                    class="preview-btn"
                    @click="previewImg(item.tipsImgUrl)"
                  >
                    查看示例
                  </span>
                </RadioGroup>
                <span class="tip" v-if="config.tips">{{ config.tips }}</span>
              </Col>
            </Row>
          </div>
          <!--#endregion自定义的配置项-->
          <div
            class="config-item"
            :class="config.key"
            v-if="config.type !== configType.custom"
          >
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.checkboxGroup && isShow(config)"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
              >
                {{ config.label }}
              </Col>
              <Col>
                <CheckboxGroup v-model="configData[config.key]" @on-change="handleCheckboxChange(config.key, configData[config.key])">
                  <Checkbox
                    v-for="(item, index) in config.items"
                    :label="item.value"
                    :key="item.value"
                    :disabled="item.disabled"
                    >{{ item.label }}</Checkbox
                  >
                </CheckboxGroup>
              </Col>
            </Row>
            <div
              v-if="config.type === configType.checkboxGroup && config.tips"
              style="margin-left: 230px;color: #888;"
              v-html="config.tips"
            ></div>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.checkboxList && isShow(config)"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
              >
                {{ config.label }}
              </Col>
              <Col class="config-checkbox-list">
                <template v-for="item in config.items">
                  <Checkbox
                    :key="item.uid"
                    v-show="isShow(item)"
                    v-model="configData[item.value]">
                    {{ item.label }}
                    <Tooltip
                      :key="item.uid + index"
                      v-if="item.tips"
                      :transfer="true"
                      :delay="0"
                      :maxWidth="246"
                      :content="item.tips"
                      placement="top"
                    >
                      <SIcon class="ml3" icon="help1" :size="12" />
                    </Tooltip>
                  </Checkbox>
                  <span
                    :key="item.uid + item.tipsImgUrl"
                    v-if="item.tipsImgUrl"
                    class="preview-btn"
                    style="margin-left: 0;"
                    @click="previewImg(item.tipsImgUrl)"
                    >查看示例</span
                  >
                </template>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.radioGroup && isShow(config)"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
              >
                {{ config.label }}
              </Col>
              <Col>
                <RadioGroup
                  v-for="(item, index) in config.items"
                  :key="item.uid"
                  @on-change="
                    onRadioGroupChange(config, item, configData[config.key])
                  "
                  v-model="configData[config.key]"
                >
                  <Radio
                    :label="item.value"
                    :disabled="item.disabled ? item.disabled : false"
                    :class="index !== 0 ? 'ml20' : ''"
                  >
                    {{ item.label }}
                  </Radio>
                  <Tooltip
                    :transfer="true"
                    :delay="0"
                    :maxWidth="246"
                    :content="item.tips ? item.tips : ''"
                    placement="top"
                  >
                    <SIcon v-if="item.tips" icon="help1" :size="12" />
                  </Tooltip>
                  <span
                    v-if="item.tipsImgUrl"
                    class="preview-btn"
                    @click="previewImg(item.tipsImgUrl)"
                  >
                    查看示例
                  </span>
                </RadioGroup>
                <span class="tip" v-if="config.tips && !config.tipsBlock">{{
                  config.tips
                }}</span>
                <span
                  v-if="config.tipsImgUrl"
                  class="preview-btn"
                  @click="previewImg(config.tipsImgUrl)"
                >
                  查看示例
                </span>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.radioGroup && config.tipsBlock"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
              >
              </Col>
              <Col class="tip" v-if="config.tips">{{ config.tips }}</Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.select"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col>
                <Select v-model="configData[config.key]">
                  <Option
                    v-for="item in config.items"
                    :key="item.uid"
                    :value="item.uid"
                    >{{ item.label }}</Option
                  >
                </Select>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.switch && isShow(config)"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                >{{ config.label }}</Col
              >
              <Col class="config-item-value">
                <i-switch
                  @on-change="onSwitchChange(config, item, configData[config.key])"
                  :disabled="typeof config.disabled === 'function' ? config.disabled(configData) : config.disabled"
                  v-model="configData[config.key]"
                />
                <!-- 集团订单定时自动审核 -->
                <TimePicker
                  v-if="config.key === 'is_open_group_order_cron_audit'"
                  format="HH:mm"
                  style="margin-left: 8px;width: 80px;"
                  :disabled="!configData.is_open_group_order_cron_audit"
                  v-model="configData.group_order_cron_audit_time"></TimePicker>
                <span v-if="config.tips" class="tip">{{ config.tips }}</span>
                <span
                  v-if="config.tipsImgUrl"
                  class="preview-btn"
                  @click="previewImg(config.tipsImgUrl)"
                  >查看示例</span
                >
                <span
                  v-if="config.tipsImgUrl2"
                  class="preview-btn"
                  @click="previewImg(config.tipsImgUrl2)"
                  >查看示例</span
                >
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.input"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col>
                <Input
                  v-model="configData[config.key]"
                  style="display: inline-block; width: 142px"
                />
                <span class="tip" style="">{{ config.tips }}</span>
              </Col>
            </Row>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.numberInput"
              v-show="config.key !== 'fresh_wholesale_market_threshold' || (config.key == 'fresh_wholesale_market_threshold' && sysConfig.is_open_fresh_wholesale_market > 0)"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col v-if="config.key !== 'provider_offer_price_threshold' && config.key !== 'fresh_wholesale_market_threshold'">
                <NumberInput
                  v-model="configData[config.key]"
                  :precision="config.props.percision"
                  :min="config.props.min"
                  :max="config.props.max"
                  style="display: inline-block; width: auto"
                />
                <span class="tip" v-if="config.tips">{{ config.tips }}</span>
              </Col>
              <Col v-else>
                <div style="display:flex;align-items: center;">
                  {{config.key == 'provider_offer_price_threshold' ? '供应商商品售价调整不能高于上一次的' : '供应商报价价格不能高于农批价格'}}
                  <NumberInput
                    v-model="configData[config.key].up"
                    :precision="config.props.percision"
                    :min="config.props.min"
                    :max="config.props.max"
                    style="display: inline-block; width: 60px;margin:0 3px;"
                  /> %,
                  {{config.key == 'provider_offer_price_threshold' ? '降价下浮率不能低于' : '不能低于农批价格'}}
                  <NumberInput
                    v-model="configData[config.key].down"
                    :precision="config.props.percision"
                    :min="config.props.min"
                    :max="config.props.max"
                    style="display: inline-block; width: 60px;margin:0 3px;"
                  /> %
                </div>
              </Col>
            </Row>
            <!-- 供应商调价阈值设置 -->
            <div class="tip" style="width: 100%;margin-left: 230px;margin-top: 5px" v-if="config.tips && (config.key === 'provider_offer_price_threshold' || (config.key === 'fresh_wholesale_market_threshold' && sysConfig.is_open_fresh_wholesale_market > 0))">{{ config.tips }}</div>
            <Row
              type="flex"
              align="middle"
              class="config-item-content"
              v-if="config.type === configType.text"
            >
              <Col
                class="config-item-label"
                :style="{
                  width: group.labelWidth ? group.labelWidth + 'px' : ''
                }"
                v-if="config.label"
                >{{ config.label }}</Col
              >
              <Col class="config-item-value">{{ config.data }}</Col>
            </Row>
          </div>
        </div>
      </div>
    </div>
    <div class="box"></div>
    <div class="aside-box">
      <div
        style="transition:all .5s "
        v-for="(group, index) in sections"
        :class="[
          group.isMain ? 'aside-1-box' : 'aside-2-box',
          activeStep == index ? 'active' : ''
        ]"
        :key="group.uid"
      >
        <span
          class="txt"
          @click="jumpTo(group.title + Number(group.isMain))"
          v-if="group.title"
        >
          {{ group.title }}
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import apiUtil from '@/api/util';
import settings from '@api/settings.js';
// import Bus from '@api/bus.js';
import Tooltip from '@components/base-tooltip';
import SIcon from '@components/icon';
import NumberInput from '@components/basic/NumberInput';
import { uniqueId } from 'lodash-es';
import { getEnv } from '@/util/common';

const configType = {
  checkboxGroup: 'checkboxGroup',
  radioGroup: 'radioGroup',
  checkboxList: 'checkboxList',
  select: 'select',
  input: 'input',
  numberInput: 'numberInput',
  switch: 'switch',
  custom: 'custom'
};
import orderSummaryModeLine from '../../assets/images/settings/orderSummary/mode-line.png';
import orderSummaryModeComma from '../../assets/images/settings/orderSummary/mode-comma.png';
import orderSummaryModeCell from '../../assets/images/settings/orderSummary/mode-cell.png';
import orderSummaryModeAdd from '../../assets/images/settings/orderSummary/mode-add.png';
import orderSummaryModeLineDetail from '../../assets/images/settings/orderSummary/mode-line-detail.png';
import ConfigMixin from '@/mixins/config';
const baseImgUrl =
  'https://base-oss.shudongpoo.com/static/superadmin-local/images/settings/';
const configGroup = [
  {
    title: '商品配置',
    index: 0,
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            label: '商品资料改价增加审核流程',
            key: 'open_commodity_price_offer_channel',
            type: configType.checkboxGroup,
            defaultValue: [],
            items: [
              {
                label: '配送商改价',
                value: '1'
              },
              {
                label: '供应商改价',
                value: '8'
              }
            ],
            tips: `勾选后对应平台修改商品资料中的市场价与客户类型价格时，需要审核才会生效<br/>
                  配送商改价来源有配送后台与订单助手，供应商改价有供应商小程序与供应商后台<br/>
                  仅在商品档案编辑及导入修改时需要审核，批量刷价不会触发审核`
          },
          {
            key: 'only_threshold_offer_audit',
            label: '仅审核改价超过调价阈值的商品',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true,
            tips: '供应商修改销售价格时调整的价格超过了调价阈值则需要进行审核'
          },
        ]
      }
    ]
  },
  {
    title: '套餐配置',
    index: 0,
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            label: '下单后保留小数位数',
            key: 'mall_commodity_amount_decimal',
            type: configType.radioGroup,
            defaultValue: true,
            items: [
              {
                label: '2位',
                value: '2'
              },
              {
                label: '1位',
                value: '1'
              },
              {
                label: '整数',
                value: '0'
              }
            ],
            tips: '在套餐下单完成之后可优化小数点位数'
          },
          {
            key: 'open_mall_rough_commodity_amount_int',
            label: '下单标品是否保留整数',
            type: configType.switch,
            tips:
              '开启后如果套餐转化为原料中有标品则自动转化为整数，取值方式为向上取整',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'package_ingredient_show_commodity_price',
            label: '套餐原料展示商品价格',
            type: configType.switch,
            tips:
              '开启后在商城中套餐原料支持展示原料销售价格，展示优先级为客户协议价>客户类型价>市场价',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'is_new_recipe',
            label: '新版菜谱',
            type: configType.switch,
            tips:
              '一键生成所有菜谱订单，可按餐次设置餐标、订单标签，灵活调整组成数量',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true
          },
          {
            key: 'raw_recipe_gen_order_type',
            label: '按餐次拆',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true
          },
          {
            key: 'is_open_accompanying_of_diners',
            label: '陪餐人员',
            type: configType.switch,
            tips:
              '开启后，客户资料中可维护陪餐人数，生成订单时，同时计算陪餐人员人数',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true
          },
          {
            label: '就餐计划审核',
            key: 'meal_plan_audit',
            type: configType.custom,
            show: true
          },
          {
            key: 'raw_recipe_show_fields',
            label: '菜谱清单显示字段',
            type: configType.custom,
            show: true
          },
          {
            key: 'raw_recipe_by_meal_type',
            label: '手机下单按餐次选择菜谱',
            type: configType.switch,
            tips:
              '开启后，在商城手机端下单时，可按餐次选择菜谱',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true
          },
          {
            key: 'raw_recipe_commodity_convert_mode',
            label: '标品优先使用大规格单位',
            type: configType.switch,
            tips:
              '开启后，生成订单时，标品讲优先使用大规格的单位',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: true
          },
        ]
      }
    ]
  },
  {
    title: '订单配置',
    labelWidth: 230,
    classify: [
      {
        title: '订单录单',
        configItems: [
          {
            key: 'order_input_version',
            label: '新版录单页面',
            tips: '全新录单体验，支持全键盘操作，更多新增特性',
            type: configType.custom
          },
          {
            key: 'is_order_audit',
            label: '订单审核',
            type: configType.switch,
            tips: '开启后，新增订单都需要审核通过才能进行后续处理',
            tipsImgUrl: baseImgUrl + 'is_order_audit.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          // {
          //   key: 'is_open_order_combine',
          //   label: '订单合并',
          //   type: configType.radioGroup,
          //   defaultValue: '0',
          //   items: [
          //     {
          //       label: '不生效',
          //       value: '0',
          //       disabled: false
          //     },
          //     {
          //       label: '自动合并仅后台生效',
          //       value: '2',
          //       disabled: false
          //     },
          //     {
          //       label: '自动合并仅商城生效',
          //       value: '3',
          //       disabled: false
          //     },
          //     {
          //       label: '全部生效',
          //       value: '1',
          //       disabled: false
          //     }
          //   ],
          //   tipsImgUrl: baseImgUrl + 'is_open_order_combine.png'
          // },
          {
            key: 'show_create_order_last_remark',
            label: '录单时显示上次备注',
            type: configType.switch,
            tips:
              '开启后，录单时自动带出最近90天内订单中上一次填写的该客户商品备注',
            tipsImgUrl: baseImgUrl + 'show_create_order_last_remark.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'is_sell_stock_alert',
            label: '录单时显示库存预警',
            type: configType.switch,
            tips: '开启后，下单数量超出商品库存时，将提示库存不足',
            tipsImgUrl: baseImgUrl + 'is_sell_stock_alert.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          // {
          //   key: 'is_order_quick_open',
          //   label: '录单支持键盘快捷键',
          //   type: configType.switch,
          //   trueValue: 1,
          //   falseValue: 0,
          //   defaultValue: true
          // },
          {
            key: 'super_create_order_offline_commodity',
            label: '录单可选商品',
            type: configType.checkboxList,
            defaultValue: [],
            items: [
              {
                label: '已下架商品',
                value: 'super_create_order_offline_commodity',
                trueValue: 1,
                falseValue: 0,
                tips: '勾选后，商城已下架的商品可以通过后台下单'
              },
              {
                label: '不可售卖商品',
                value: 'super_create_order_unsell_commodity',
                trueValue: 1,
                falseValue: 0,
                tips:
                  '勾选后，商品档案中已设置不可售卖的商品规格可以通过后台下单'
              }
            ]
          },
          {
            key: 'open_order_commodity_inner_remark',
            label: '内部备注',
            type: configType.switch,
            tips:
              '开启后，订单商品、订单退货单商品显示【内部备注】字段，该字段商城和打印不展示',
            tipsImgUrl: baseImgUrl + 'open_order_commodity_inner_remark.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'approval_delivery_num_zero_enabled',
            label: '核算支持发货数量为0',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            tips: '开启后,订单核算时允许将发货数量修改为0',
            defaultValue: false
          },
          {
            key: 'new_order_notice',
            label: '订单语音提醒',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            tips: '开启后，商城下单成功，后台会有语音提醒',
            defaultValue: true
          },
          {
            key: 'sync_protocol_is_add_commodity',
            label: '下单时新商品加入协议单',
            type: configType.switch,
            tips:
              '开启后，不在客户协议单中的商品，在保存订单后会将商品加入到对应的生效客户协议单中',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'use_last_order_price',
            label: '下单单价默认取客户订单商品上一笔下单单价',
            type: configType.switch,
            tips:
              '开启后，非协议价商品下单时默认取该商品最近30天上一笔订单中该商品的下单单价；商城订单允许编辑配置项会默认关闭。',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'actual_unit_price_no_tax_decimal',
            label: '发货单价(不含税)小数位数',
            type: configType.radioGroup,
            defaultValue: '8',
            items: [
              {
                label: '8位',
                value: '8'
              },
              {
                label: '4位',
                value: '4'
              },
              {
                label: '2位',
                value: '2'
              }
            ]
          },
          {
            key: 'is_open_order_lock',
            label: '订单锁定',
            type: configType.radioGroup,
            tips:
              '选择对应生效范围后，后台可对订单进行全/半锁定，锁定后将限制生效范围内操作用户对订单商品的删除或修改权限。',
            defaultValue: '0',
            items: [
              {
                label: '不开启',
                value: '0'
              },
              {
                label: '仅商城开启',
                value: '1',
                tips: '含手机商城、集团端、openapi'
              },
              {
                label: '仅后台订单核算开启',
                value: '2'
              },
              {
                label: '全部开启',
                value: '3'
              }
            ]
          },
          {
            key: 'is_open_super_sell_stock_limit',
            label: '后台录单不允许超过实时库存量',
            type: configType.switch,
            tips: '仅有在商品为实时售卖库存时生效，开启后商品为实时售卖库存时下单数量超过实时库存商品后台不允许下单',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
					{
						key: 'is_can_total_price_update',
						label: '小计支持修改',
						type: configType.switch,
						tips: '开启后，支持在订单新增/核算/追加修改页面对下单小计和发货小计进行修改',
						trueValue: 1,
						falseValue: 0,
						defaultValue: false,
					},
					{
						key: 'total_price_update_mode',
						label: '小计修改计算逻辑',
						type: configType.radioGroup,
						defaultValue: '1',
						items: [
							{
								label: '固定单价',
								value: '1',
								tips: '修改小计后，会联动计算数量字段，单价字段不做变更'
							},
							{
								label: '固定数量',
								value: '2',
								tips: '修改小计后，会联动计算单价字段，数量字段不做变更'
							}
						],
						show: (configData, _this) => {
							const { is_can_total_price_update } = _this.configData;
							return +is_can_total_price_update === 1;
						}
					}
        ]
      },
      {
        title: '订单导出',
        configItems: [
          {
            key: 'export_order_no_in_price',
            label: '单个订单导出进货价',
            type: configType.switch,
            tips: '开启后，订单单个导出时显示进货价字段',
            tipsImgUrl: baseImgUrl + 'export_order_no_in_price.png',
            trueValue: 0,
            falseValue: 1,
            defaultValue: true
          },
          {
            key: 'order_export_blank_line',
            label: '订单导出汇总金额',
            type: configType.radioGroup,
            tips: '开启后，订单批量导出时，按订单维度汇总发货小计、实际金额',
            tipsImgUrl: baseImgUrl + 'export_order_no_in_price_total.png',
            defaultValue: 1,
            items: [
              {
                label: '是',
                value: '1'
              },
              {
                label: '否',
                value: '0'
              }
            ]
          }
        ]
      },
      {
        title: '定价',
        configItems: [
          {
            key: 'is_open_new_smart_pricing',
            label: '新版智能定价',
            trueValue: 1,
            falseValue: 0,
            type: configType.switch,
            tips: '智能定价新旧版本切换，新版支持全键盘操作'
          },
          {
            key: 'intelligent_price_dimension',
            show: (configData) => {
              return +configData.is_open_new_smart_pricing === 1;
            },
            label: '智能定价维度',
            type: configType.radioGroup,
            defaultValue: 1,
            items: [
              {
                label: '按商品',
                value: '1'
              },
              {
                label: '按商品分类',
                value: '2'
              }
            ]
          },
          {
            key: 'user_style_price_is_zero_default_mark_price',
            label: '客户类型价未填写时取市场价格',
            type: configType.switch,
            tips: '开启后，商品档案中客户类型价若不填写将自动取市场价的值',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'intelligent_price_decimal',
            label: '智能定价保留小数',
            type: configType.radioGroup,
            tips:
              '通过定价公式计算出来的的销售价，保留指定的小数位数（四舍五入）',
            defaultValue: 1,
            items: [
              {
                label: '1位',
                value: '1'
              },
              {
                label: '2位',
                value: '2'
              }
            ]
          },
          {
            key: 'is_protocol_price_by_base_unit',
            label: '客户协议价按基础单位定价',
            type: configType.switch,
            disabled: (configData) => Number(configData.commodity_edit_protocol) === 1,
            tips:
              '开启后，客户协议单，辅助单位价格=基础单位价格*转换系数，不能与协议价折扣及协议价公式同时启用',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'batch_brush_order_mode',
            label: '订单批量刷价范围',
            type: configType.radioGroup,
            show: (configData, _this) => {
              return +_this.sysConfig.is_open_order_sync_protocol === 1;
            },
            defaultValue: 1,
            items: [
              {
                label: '全部状态订单',
                value: '0'
              },
              {
                label: '待发货订单',
                value: '1'
              }
            ]
          },
          {
            key: 'batch_brush_order_price_type',
            label: '订单批量刷价内容',
            show: (configData, _this) => {
              return +_this.sysConfig.is_open_order_sync_protocol === 1;
            },
            type: configType.radioGroup,
            defaultValue: 1,
            items: [
              {
                label: '商品最新价',
                value: '0',
                tips:
                  '会刷新订单商品下单单价为最新价格（协议价>客户类型价>市场价）'
              },
              {
                label: '商品协议价',
                value: '1',
                tips:
                  '会刷新订单商品下单单价为当前客户生效的协议价，没有生效的协议价则不进行刷价'
              }
            ]
          }
        ]
      },
      {
        title: '集团订单',
        configItems: [
          {
            key: 'group_order_audit',
            label: '集团审核门店订单',
            type: configType.switch,
            tips: '开启后，门店下单需要集团审核通过才能进行后续处理',
            tipsImgUrl: baseImgUrl + 'order_must_check.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'forbid_audit_on_group_order_time',
            label: '审核订单规则',
            // type: configType.switch,
            // trueValue: 1,
            // falseValue: 0,
            // defaultValue: true
            type: configType.radioGroup,
            items: [
              {
                label: '无限制',
                value: '0',
                tips:
                  '可任意时间审核订单且无其他校验'
              },
              {
                label: '下单截止时间前',
                value: '1',
                tips:
                  '仅允许在订单的下单截止时间前审核，超时禁止操作，审核时会校验发货日期是否满足运营时段要求'
              },
              {
                label: '按发货日期校验',
                value: '2',
                tips: '可随时审核，但审核时会校验发货日期是否满足运营时段要求'
              },
            ],
            defaultValue: '0'
          },
          {
            key: 'group_admin_user_valet_order',
            label: '集团管理账号代客付款',
            type: configType.switch,
            tips:
              '开启：集团管理员账号支持对集团管理下客户订单进行付款，商城端进行在线支付，pc端不支持',
            tipsImgUrl: baseImgUrl + 'group_admin_user_valet_order.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'group_show_user_tag',
            label: '集团后台展示客户标签',
            type: configType.switch,
            tips: '开启后，集团后台筛选条件中将展示客户标签筛选项',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'is_open_group_order_cron_audit',
            label: '集团订单定时自动审核',
            type:  configType.switch,
            tips: '开启了集团订单定时自动审核，则每日会根据设置的时间将未审核的集团订单进行审核。',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '退货退款',
        configItems: [
          {
            key: 'return_date_user_audit_date',
            label: '客户退货单退货日期取审核日期',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            tips: '开启后，客户退货退款时自动取退货单的审核日期作为退货日期'
          },
          {
            key: 'is_open_order_return_service_charge',
            label: '退货退款关联退款服务费',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            tips:
              '开启后，客户申请退货退款会根据退货数量自动退款该商品产生的服务费'
          }
        ]
      },
      {
        title: '实收变更',
        configItems: [
          {
            label: '实收变更审核',
            key: 'order_modify_audit',
            type: configType.checkboxGroup,
            defaultValue: [],
            items: [
              {
                label: '后台',
                value: 'super'
              },
              {
                label: '商城',
                value: 'mall'
              },
              {
                label: '配送助手',
                value: 'delivery'
              },
              {
                label: '智能收货秤',
                value: 'scale'
              },
              {
                label: 'Openapi',
                value: 'openApi'
              },
              {
                label: '供应商PC端',
                value: 'provider'
              }
            ],
            tips: `勾选后，勾选端新增的实收变更单都需要审核通过才能进行后续的处理`
          },
          {
            key: 'order_modify_only_show_diff',
            label: '实收变更单仅展示差异商品',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            tips: '启用时，已审核的实收变更单中将仅展示存在差异的商品，无差异的商品不再展示'
          },
        ]
      }
    ]
  },
  {
    title: '采购配置',
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            label: '采购模式',
            key: 'is_open_purchase_task',
            type: configType.custom
          }
        ]
      },
      {
        title: '采购任务',
        configItems: [
          {
            label: '生成采购单拆单方式',
            key: 'is_purchase_task_gen_order_use_bind_provider',
            type: configType.radioGroup,
            items: [
              {
                label: '按客户指定供应商',
                value: '0',
                tips:
                  '客户指定供应商的商品，生成采购单时将拆单生成采购单，采购类型为指定供应商'
              },
              {
                label: '按供应商/采购员',
                value: '1'
              },
              {
                label: '按客户',
                value: '3',
                tips: '订单按客户拆分后汇总生成采购单，采购单会增加备注记录客户信息'
              },
              {
                label: '按订单号',
                value: '2',
                tips: '按订单号生成采购单，采购单备注会显示客户名称'
              },
            ],
            defaultValue: '0'
          },
          {
            label: '供应商分拣采购任务分配商品',
            key: 'is_open_provider_sorting_purchase_task',
            type: configType.switch,
            tips:
              '开启后，供应商可在生成采购单后，按采购任务中分配的商品，对客户订单进行分拣',
            tipsImgUrl: baseImgUrl + 'purchase_sorting_goods.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '采购任务收货数量默认值',
            key: 'purchase_task_receipt_default_amount',
            type: configType.custom,
            show: true,
            defaultValue: '1',
            items: [
              {
                label: '待采购量',
                value: '1',
                tips: '与采购单的采购量一致'
              },
              {
                label: '供应商分拣量',
                value: '2',
                tips:
                  '由采购任务生成的采购单，收货数为采购单关联的订单的供应商分拣量汇总'
              }
            ]
          },
          {
            label: '联营供应商参与计算库存',
            tips:
              '开启后，联营供应商负责配送库存不足商品。以订单单品整体数量参与库存抵扣，库存数量仅能抵扣部分商品数量时，不参与抵扣联营供应商商品；关闭后，联营供应商供货不参与计算仓库库存，仅非联营供应商、采购员计算仓库库存',
            tipsImgUrl: baseImgUrl + 'provider_join_calc_ inventory.png',
            key: 'purchase_task_provider_supplier_cal_stock',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '采购任务修改后更新客户指定供应商',
            tips:
              '开启后，在采购任务修改供应商后，供应商信息将更新至客户指定供应商（批量设置除外；若客户指定供应商选择商品模式为分类模式，则不生效该配置）',
            key: 'is_open_purchase_task_link_user_bind_provider',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '供应商分拣量自动重置',
            type: configType.radioGroup,
            key: 'is_open_provider_sort_amount_auto_reset',
            trueValue: 1,
            falseValue: 0,
            defaultValue: '0',
            items: [
              {
                label: '不生效',
                value: '0',
              },
              {
                label: '仅订单未回单时生效',
                value: '2',
                tips: '选中后，订单未回单：采购单变更供应商/采购员、关闭、删除商品（勾选了重置采购任务状态）会自动重置分拣量；已回单时：不重置分拣量但会更新供应商'
              },
              {
                label: '全部生效',
                value: '1',
                tips: '选中后，采购单变更供应商/采购员、关闭、删除商品（勾选了重置采购任务状态）会自动重置分拣量'
              },
            ]
          },
					{
						label: '指定供应商参与库存抵扣',
						key: 'is_bind_provider_can_contain_stock',
						tips: '开启后，采购任务计算库存时，客户指定供应商、客户类型指定供应商的商品会参与库存抵扣',
						type: configType.switch,
						trueValue: 1,
						falseValue: 0,
						defaultValue: false,
						show: configData => {
							return +configData.is_open_purchase_task === 1;
						},
					},
					{
						label: '采购任务指定供应商',
						key: 'is_open_purchase_task_bind_provider',
						tips: '开启后，客户/客户类型指定供应商可设置需生效的发货日期',
						type: configType.switch,
						trueValue: 1,
						falseValue: 0,
						defaultValue: 0,
					}
        ]
      },
      {
        title: '订单汇总',
        configItems: [
          {
            label: '订单汇总导出字段',
            key: 'show_order_pool_user_name',
            type: configType.checkboxList,
            defaultValue: [],
            items: [
              {
                label: '显示客户名称',
                value: 'show_order_pool_user_name',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '显示客户编码',
                value: 'show_order_pool_user_code',
                trueValue: 1,
                falseValue: 0
              },
							{
								label: '显示客户标签',
								value: 'show_order_pool_user_tag',
								trueValue: 1,
								falseValue: 0
							},
              {
                label: '显示订单商品备注',
                value: 'show_order_pool_remark',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '显示订单商品内部备注',
                value: 'open_order_summary_inner_remark',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '显示订单标签',
                value: 'show_order_pool_order_tag',
                show: false,
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '简洁模式',
                value: 'open_concise_order_pool',
                tips:
                  '默认只展示转换后的订购数量，有备注的商品，同时显示备注和客户编码',
                trueValue: 1,
                falseValue: 0
              }
            ]
          },
          {
            label: '订单汇总导出明细分隔形式',
            key: 'order_summary_format',
            type: 'custom',
            defaultValue: 0,
            items: [
              {
                label: '每个客户一行',
                value: '0',
                imgWidth: '200',
                tipsImg: orderSummaryModeLine
              },
              {
                label: '客户之间“，”隔开',
                value: '1',
                imgWidth: '200',
                tipsImg: orderSummaryModeComma
              },
              {
                label: '客户之间单元格分开',
                value: '2',
                imgWidth: '500',
                tipsImg: orderSummaryModeCell
              },
              {
                label: '客户明细单独一行展示',
                value: '3',
                imgWidth: '500',
                tipsImg: orderSummaryModeLineDetail
              },
              {
                label: '客户之间"+"隔开',
                value: '4',
                imgWidth: '200',
                tipsImg: orderSummaryModeAdd
              },
              {
                label: '按商品下单数量',
                value: '5',
                imgWidth: '246',
                tips:
                  '注：选择【按商品下单数量导出】后，订单汇总导出字段配置字段均不生效',
                tipsImg: baseImgUrl + 'order_summary_format1.png',
                innerStyles: {
                  width: '300px'
                }
              }
            ]
          },
          {
            label: '订单汇总导出明细单位',
            key: 'order_summary_export_type',
            show: true,
            type: configType.custom,
            defaultValue: 0,
            items: [
              {
                label: '按采购单位',
                value: '0',
                tips: '订单汇总导出表格中，下单明细将按采购单位换算展示'
              },
              {
                label: '按下单单位',
                value: '1',
                tips: '订单汇总导出表格中，下单明细将按下单单位展示'
              }
            ]
          }
        ]
      },
      {
        title: '采购录单',
        configItems: [
          {
            label: '采购单审核',
            key: 'purchase_order_audit',
            type: configType.switch,
            tips: '开启后，新增采购单都需要审核通过才能进行后续处理',
            tipsImgUrl: baseImgUrl + 'purchase_note_check.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '采购单模板填充采购数量&采购价格',
            key: 'purchase_template_price_num',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            tips:
              '开启后，下载导入采购单模板中，采购数量默认为库房分拣量，采购价格默认为最近一次进货价',
            tipsImgUrl: baseImgUrl + 'purchase_template_price_num.png'
          },
          {
            label: '采购单收货数量阈值',
            key: 'receipts_num_threshold',
            type: configType.custom,
            tipsImgUrl: baseImgUrl + 'receipts_num_threshold.png'
          },
          {
            label: '下架商品可采购',
            key: 'display_down_shelf_commodity',
            type: configType.switch,
            tips: '开启后，下架及回收站商品在采购及入库选择商品时可见',
            tipsImgUrl: baseImgUrl + 'sold_out_can_purchase.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          // {
          //   label: '多单位进价同步更新',
          //   key: 'open_purchase_in_update_another_unit_in_price',
          //   type: configType.switch,
          //   tips: '多单位商品，更新其中一个单位进价时，同时更新其他单位的进价',
          //   tipsImgUrl: baseImgUrl + 'more_unit_async_update_price.png',
          //   trueValue: 1,
          //   falseValue: 0,
          //   defaultValue: true
          // },
          {
            label: '采购单自动合并',
            key: 'is_purchase_order_combine_list_auto_popup',
            type: configType.switch,
            tips:
              '开启后，生成采购单后，如采购单可合并，将提示是否需要合并。关闭后不再提示。',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '生成采购单自动更新最近一次采购价',
            key: 'purchase_update_last_receipt_price',
            type: configType.switch,
            tips: '后台新增编辑采购单将会同步最近一次采购价。',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '更新最近一次进货价',
            key: 'is_open_update_in_price',
            type: configType.switch,
            tips: '开启后，生成采购单后，最近一次进货价开关将默认开启',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          }
        ]
      },
      {
        title: '采购单导出&分享',
        configItems: [
          {
            label: '采购单单个导出（主体信息）',
            key: 'purchase_single_export_main',
            type: configType.checkboxList,
            defaultValue: [],
            items: [
              {
                label: '一二级分类',
                value: 'is_purchase_export_category',
                trueValue: 1,
                falseValue: 0
              }
            ]
          },
          {
            label: '采购单导出、打印（客户下单明细）',
            key: 'purchase_share_config',
            type: configType.checkboxList,
            defaultValue: [],
            items: [
              {
                label: '客户名称',
                value: 'is_show_purchase_user_name',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '客户编码',
                value: 'is_show_purchase_user_code',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '线路',
                value: 'is_purchase_show_order_line',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '客户地址',
                value: 'is_purchase_show_order_address',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '订单商品备注',
                value: 'is_show_purchase_order_remark',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '订单商品内部备注',
                value: 'is_show_purchase_order_inner_remark',
                show: configData => {
                  return configData.open_order_commodity_inner_remark;
                },
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '商品描述',
                value: 'is_show_purchase_com_summary',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '分拣明细',
                value: 'is_show_purchase_order_sort_detail',
                trueValue: 1,
                falseValue: 0
              },
            ]
          },
          {
            label: '采购单导出明细分隔形式',
            key: 'is_show_purchase_no_depart',
            type: 'custom',
            defaultValue: '1',
            items: [
              {
                label: '客户之间“，”隔开',
                value: '1',
                imgWidth: '200',
                tipsImg: orderSummaryModeComma
              },
              {
                label: '客户之间单元格分开',
                value: '0',
                imgWidth: '500',
                tipsImg: orderSummaryModeCell
              },
              {
                label: '客户明细单独一行展示',
                value: '2',
                imgWidth: '500',
                tipsImg: orderSummaryModeLineDetail
              }
            ]
          },
          {
            label: '采购单分享',
            key: 'purchase_share_config',
            type: configType.checkboxList,
            defaultValue: [],
            items: [
              {
                label: '客户名称',
                value: 'is_show_share_purchase_user_name',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '客户编码',
                value: 'is_show_share_purchase_user_code',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '客户地址',
                value: 'is_share_purchase_show_order_address',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '订单商品备注',
                value: 'is_show_share_purchase_order_remark',
                trueValue: 1,
                falseValue: 0
              },
              {
                label: '订单商品内部备注',
                value: 'open_purchase_share_inner_remark',
                trueValue: 1,
                falseValue: 0,
                show: configData => {
                  return configData.open_order_commodity_inner_remark;
                },
                tipsImgUrl: baseImgUrl + 'is_show_share_purchase_user_name.png'
              }
            ]
          },
          {
            label: '客户下单明细取值',
            key: 'purchase_user_order_item',
            type: configType.radioGroup,
            defaultValue: '0',
            items: [
              {
                label: '实时下单数量',
                value: '0',
                tips:
                  '生成采购单后，客户订单数量变更、商品删除将实时同步至客户下单明细'
              },
              {
                label: '生成采购单时的固定数量',
                value: '1',
                tips:
                  '订单汇总：取生成采购单时的客户下单数量，重复生成时，取首次的;采购任务：取采购任务分配的数量'
              }
            ]
          }
        ]
      },
      // #region 隐藏智能预采
      // {
      //   title: '智能预采',
      //   configItems: [
      //     {
      //       label: '智能预采购',
      //       key: 'pre_purchase_config',
      //       type: configType.custom
      //     },
      //     {
      //       label: '采购下限取值',
      //       type: configType.custom,
      //       key: 'min_stock_limit_flag'
      //     },
      //     {
      //       label: '采购上限取值',
      //       type: configType.custom,
      //       key: 'max_stock_limit_flag'
      //     },
      //     {
      //       type: configType.custom,
      //       key: 'max_pur_limit_days'
      //     },
      //     {
      //       label: '日均销量取值',
      //       key: 'last_few_days',
      //       type: configType.custom
      //     },
      //     {
      //       label: '预采分配取值',
      //       key: 'remember_pre_choice',
      //       type: configType.custom
      //     },
      //   ]
      // }
      // #endregion 隐藏智能预采
      {
        title: '供应商端',
        configItems: [
          {
            label: '供应商确认供货',
            // tipsImgUrl: baseImgUrl + 'provider_purchase_node_can_offer.png',
            key: 'supplier_edit_purchase_order',
            type: configType.radioGroup,
            items: [
              {
                label: '关闭',
                value: '0',
                tips: '供应商端不可修改数量和价格'
              },
              {
                label: '可修改所有商品价格',
                value: '1',
                tips: '供应商端可修改数量和价格'
              },
              {
                label: '可修改非协议价商品的价格',
                value: '2',
                tips: '供应商端可修改所有商品数量、非协议价商品的价格'
              },
              {
                label: '仅可点击确认供货',
                value: '3',
              },
              {
                label: '仅可修改数量',
                value: '4',
              }
            ],
            defaultValue: '0'
          },
          {
            label: '供应商端显示客户名称',
            type: configType.switch,
            tips: '关闭后，供应商端仅显示客户编码，保护客户信息',
            key: 'provider_show_userinfo',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '联营供应商发货出库',
            type: configType.switch,
            tips:
              '开启后，订单所属供应商为联营供应商时，该联营供应商可以对订单进行发货出库操作',
            key: 'is_open_provider_pool_delivery',
            trueValue: 1,
            falseValue: 0,
            defaultValue: 0
          },
          {
            label: '供应商分拣',
            tips: '开启后，供应商可通过网页端、小程序进行商品分拣',
            type: configType.switch,
            key: 'is_supplier_sort',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '备货参考',
            type: configType.switch,
            key: 'prepare_reference',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '供应商调价阈值设置',
            key: 'provider_offer_price_threshold',
            type: configType.numberInput,
            defaultValue: { up: 0, down: 0 },
            tips: `设置之后供应商端修改商品价格时会根据阈值判断调价范围，设置为0时则不设置阈值范围`,
            props: {
              min: 0,
              max: 100,
              percision: 2
            }
          },
          {
            label: '询价报价报价阈值设置',
            key: 'fresh_wholesale_market_threshold',
            type: configType.numberInput,
            defaultValue: { up: 0, down: 0 },
            tips: `仅在商品绑定了【生鲜行情- 农批价格】的商品会应用该限制，未绑定的将不会影响`,
            show: true,
            props: {
              min: 0,
              max: 100,
              percision: 2
            }
          },
          {
            label: '查询条件显示字段',
            key: 'is_show_query_fields_in_provider',
            type: configType.checkboxGroup,
            defaultValue: ['1', '2', '3', '4'],
            tips: '不勾选时，供应商后台&供应商小程序&供应商分拣端在分拣时，查询条件不展示这个字段',
            items: [
              {
                label: '客户类型',
                value: '1'
              },
              {
                label: '集团',
                value: '2'
              },
              {
                label: '客户标签',
                value: '3'
              },
              {
                label: '线路',
                value: '4'
              }
            ]
          },
          {
            label: '选择采购单打印模板',
            type: configType.switch,
            key: 'provider_select_purchase_order_tpl',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
        ]
      },
      {
        title: '其他配置',
        configItems: [
          {
            label: '按辅助单位下单显示方式',
            key: 'order_amount_format_type',
            tips: '商品按基础单位分拣时，客户下单明细中，辅助单位的展示方式',
            tipsBlock: true,
            type: configType.radioGroup,
            items: [
              {
                label: '默认',
                value: '0',
                tips: '版本升级前默认展示方式'
              },
              {
                label: '16斤（2包x8斤）',
                value: '1'
              },
              {
                label: '16斤（2包，每包8斤）',
                value: '2'
              },
              {
                label: '2包（每包8斤，共16斤）',
                value: '3'
              },
              {
                label: '2包',
                value: '4'
              }
            ],
            defaultValue: '0'
          },
          {
            label: '报价方案默认选中',
            type: configType.switch,
            key: 'is_open_rfq_offer_default',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true,
            show: (configData, vm) => {
              return Number(vm.sysConfig.is_open_rfq) === 1;
            },
            tips: '开启后，将按照设定的优先级默认填充报价方案：手动选择 > 最低报价供应商 > 默认供应商。（报价供应商价格一致时取早报价的、默认供应商不属于询价对象则为空）'
          },
          {
            label: '询价报价不影响按客户/客户类型指定的供应商',
            type: configType.switch,
            key: 'is_open_offer_bind_provider',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            tips: '在供应商报价或询价报价选择供应商后，采购任务页面不会更新按客户/客户类型指定的供应商',
          },
					{
						label: '多单位价格同步更新',
						type: configType.checkboxGroup,
						key: 'multiUnitPrice',
						defaultValue: [],
						items: [
							{
								label: '最近一次进价',
								value: 'open_purchase_in_update_another_unit_in_price'
							},
							{
								label: '采购询价',
								value: 'one_unit_inquiry_multi_unit_sync',
							}
						]
					},
          {
            label: '客户下单明细排序',
            key: 'purchase_task_user_order_detail_sort',
            tipsBlock: true,
            type: configType.radioGroup,
            items: [
              {
                label: '默认',
                value: '0',
                tips: '采购任务：先按“待采购”、“采购中”、“已完成”的顺序排序，然后再每个状态下按数字、字母、文字顺序排序'
              },
              {
                label: '客户名称',
                value: '2',
                tips: '按客户名称的字母顺序排序，会先排数字，再排英文字母，最后排汉字'
              },
              {
                label: '客户编码',
                value: '1',
                tips: '按客户编码的字母顺序排序，会先排数字，再排英文字母，最后排汉字。无客户编码的客户最后按客户创建时间正序排序'
              },
            ],
            defaultValue: '0'
          },
          {
            label: '供应商发起询价',
            type: configType.switch,
            key: 'is_open_provider_rfq',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: (configData, vm) => {
              return Number(vm.sysConfig.is_open_rfq) !== 0;
            },
            tips: '开启后，供应商小程序可主动发起询价单',
          },
          {
            label: '生成采购单后自动锁单',
            type: configType.switch,
            key: 'is_open_gen_purchase_auto_order_lock',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            show: (configData, vm) => {
              return Number(vm.sysConfig.is_open_order_lock) !== 0;
            },
            tips: '开启后，生成采购单将自动全锁订单',
          },
        ]
      }
    ]
  },
  {
    title: '加工配置',
    key: 'config_process',
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            label: '加工端审核',
            type: configType.checkboxList,
            key: '_process_audit',
            items: [
              {
                label: '领料',
                value: 'is_get_material_audit',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true
              },
              {
                label: '完工入库',
                value: 'is_process_completion_audit',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true
              },
              {
                label: '退料',
                value: 'is_return_material_audit',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true
              },
            ]
          },
          {
            label: '领料出库模式',
            type: configType.radioGroup,
            defaultValue: 0,
            key: 'open_picking_audit',
            items: [
              {
                label: '直接出库',
                value: '0',
                tips: '领料单审核对应生成已出库的领料出库单，不可编辑'
              },
              {
                label: '审核出库',
                value: '1',
                tips:
                  '领料单审核对应生成待出库的领料出库单，可以进行编辑审核操作'
              }
            ]
          },
          {
            label: '完工入库模式',
            key: 'process_completion_in_mode',
            type: configType.radioGroup,
            defaultValue: true,
            items: [
              {
                label: '按商品完工入库',
                value: '1',
                tips: '完工入库时录入成品的数量',
                tipsImgUrl: baseImgUrl + 'process_completion_in_mode.png'
              },
              {
                label: '按工人完工入库',
                value: '2',
                tips: '完工入库时录入各工人分别完成的成品数量',
                tipsImgUrl: baseImgUrl + 'process_completion_in_mode2.png'
              }
            ]
          },
          {
            label: '最大退料时间',
            key: 'process_return_max_hour',
            type: configType.custom
          }
        ]
      }
    ]
  },
  {
    title: '分拣配置',
    labelWidth: 230,
    classify: [
      {
        title: '分拣配置',
        configItems: [
          {
            label: '分拣任务',
            type: configType.radioGroup,
            defaultValue: 1,
            key: 'sort_binding_power_mode',
            items: [
              {
                label: '按商品分类',
                value: '1'
              },
              {
                label: '按单品',
                value: '2'
              },
              {
                label: '按客户',
                value: '3'
              },
              {
                label: '按分类以及客户',
                value: '4'
              },
              {
                label: '按单品以及客户',
                value: '5'
              },
              {
                label: '按分类及客户类型',
                value: '7'
              }
            ]
          },
          {
            label: '分拣列表商品排序',
            type: configType.radioGroup,
            defaultValue: 1,
            key: 'sorting_list_sort',
            items: [
              {
                label: '按商品名称',
                value: 'commodity',
                tips:
                  '依次按商品名称、商品单位、数量由小到大、客户创建时间正序排序'
              },
              {
                label: '按线路',
                value: 'line',
                tips:
                  '依次按线路ID、物流排序号、商品名称、商品单位、数量由小到大、客户创建时间正序排序'
              },
              {
                label: '按照临时编码',
                value: 'sort_code',
                tips:
                  '开启临时编码：依次按照商品名称、商品单位、临时编码；关闭临时编码：依次按商品名称、商品单位、数量由小到大、客户创建时间正序排序'
              },
              {
                label: '先商品后线路',
                value: 'commodity_line',
                tips:
                  '依次按商品名称、商品单位、线路ID、物流排序号、下单顺序排序'
              },
              {
                label: '先商品后客户',
                value: 'commodity_user',
                tips:
                  '开启临时编码：依次按照商品名称、商品单位、临时编码、下单顺序排序；关闭临时编码：依次按照商品名称、商品单位、客户编码、下单顺序'
              },
              {
                label: '按分类名称',
                value: 'category',
                tips: '先后按一级、二级分类名称GBK编码正序、相同时按ID正序'
              }
            ]
          },
          {
            label: '导出汇总表显示客户名称',
            key: 'open_sort_summary_column_user_name',
            type: configType.switch,
            tips:
              '开启后，商品分拣导出表格中会显示【客户名称-客户编码】；关闭后只展示【客户编码】',
            tipsImgUrl: baseImgUrl + 'export_total_show_client_name.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '分拣阈值',
            key: 'sort_num_threshold',
            type: 'custom'
          },
          {
            label: '超过阈值不能分拣',
            type: configType.switch,
            tips: '开启后，超过阈值不能分拣',
            tipsImgUrl: baseImgUrl + 'beyond_max_can_not_sorting.png',
            key: 'open_exceeded_threshold_cannot_sort',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '按下单单位分拣',
            type: configType.switch,
            key: 'open_order_unit_sorting',
            tips:
              '客户下单的时候是按照辅助单位(比如包)下单，在采购的时候都是按照斤采购，按照固定的分量进行预打包分拣给客户的时候就需要按照标品的方式来分拣，即按照下单单位分拣，出库仍按照斤进行出库',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '使用新版商品分拣页面',
            type: configType.custom,
            key: 'use_new_sort_commodity'
          },
          {
            label: '安卓、windows分拣端隐藏供应商联营商品',
            type: configType.switch,
            key: 'android_windows_hidden_provider_pool_goods',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '客户临时编码',
        configItems: [
          {
            label: '客户编码模式',
            type: configType.radioGroup,
            defaultValue: '0',
            key: 'user_code_mode',
            items: [
              {
                label: '客户临时编码',
                value: '0'
              },
              {
                label: '客户序号编码',
                value: '1'
              }
            ]
          },
          {
            key: 'sort_code',
            label: '分拣客户临时编码',
            tips: '根据每天下单客户和线路分配生成客户临时编码',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'auto_sort_code',
            label: '自动生成临时编码',
            tips: '根据客户下单自动生成客户的临时编码',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          }
        ]
      },
      {
        title: '标签打印',
        configItems: [
          {
            key: 'sort_print',
            type: configType.switch,
            label: '商品分拣自动打印小票',
            tips:
              '开启后，使用网页填写分拣数量后可同时打印小票，优先级低于分拣页面的相同配置',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            key: 'sorting_end_prints_labels_according_order_num',
            label: '小票多份打印',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '关',
                value: '0'
              },
              {
                label: '按下单数量',
                value: '1',
                tips:
                  '商品档案中设置为标品的商品会按照分拣的数量打印分拣标签数，适用于一键分拣、一键打印；当标品的商品下单数量为小数点时， 与非标品一样仅打印一份标签'
              },
              {
                label: '按包装规格',
                value: '2',
                tips:
                  '开启后，商品资料可维护【包装规格数量】，分拣时，只有分拣数量达到包装规格数量时，才会打印出一份标签，如白菜的包装规格数量是20斤，60斤白菜分拣时，将打印出3个标签，每个20斤，超出部分小于包装规格数量时，另外打印1个标签。'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    title: '库房配置',
    labelWidth: 230,
    classify: [
      {
        title: '库房配置',
        configItems: [
          {
            key: 'modify_store_time',
            label: '自定义出入库时间',
            tips: '开启后，出入库单据与盘点单据可以手动设置出入库时间',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: ' ',
            key: 'in_time_default_type',
            type: configType.custom
          },
          {
            key: 'is_multi_mfg_date',
            label: '生产日期可多选',
            tips: '开启后，订单商品的生成日期可配置多个',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'is_batch_can_select_mfg_date',
            label: '批次商品可选择生产日期',
            tips: '开启后，批次商品可选择生产日期。发货单中的生产日期仍取出库批次的生产日期',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            key: 'is_in_record_use_pool_provider_mfg_date',
            label: '联营入库单商品备注显示关联订单生产日期',
            tips: '开启后，订单发货出库后，联营采购单自动入库，入库单商品备注追加显示分拣页面显示的生产日期',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
        ]
      },
      {
        title: '入库配置',
        configItems: [
          {
            label: '入库模式',
            key: 'in_mode',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '直接入库',
                value: '0',
                tips: '业务产生入库单时，状态直接是“已入库”，不需要审核'
              },
              {
                label: '审核入库',
                value: '1',
                tips:
                  '业务产生入库单时，状态为“待入库”，可进行编辑及审核；影响范围包括采购入库、调货入库、订单退货入库',
                tipsImgUrl: baseImgUrl + 'in_mode.png'
              }
            ]
          },
          {
            label: '审核入库',
            key: 'in_price_audit',
            defaultValue: 1,
            type: configType.custom,
            items: [
              {
                label: '改数量,同时变更单价',
                value: '0',
                tips: '开启后：可修改入库数量，同步变更单价，金额不变',
                tipsImgUrl: baseImgUrl + 'check_in_mode_can_alter_num1.png'
              },
              {
                label: '改数量,同时变更金额',
                value: '2',
                tips: '开启后：可修改入库数量，单价不变，同步变更金额',
                tipsImgUrl: baseImgUrl + 'check_in_mode_can_alter_num2.png'
              },
              {
                label: '改数量、单价、金额',
                value: '1',
                tips: '可同时修改数量、单价、金额',
                tipsImgUrl: baseImgUrl + 'check_in_mode_can_alter_num3.png'
              }
            ]
          },
          {
            label: '批次号默认规则',
            key: 'batch_no_gen_rule',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '入库日期+4位流水号',
                value: '0',
                tips:
                  '自动入库商品的批次号规则（如单位转换、完工入库）为入库单审核日期+4位流水号，如202110100001'
              },
              {
                label: '生产日期+4位流水号',
                value: '1',
                tips:
                  '自动入库商品的批次号规则（如单位转换、完工入库）为商品生产日期+4位流水号，如202110100001'
              },
              {
                label: '随机12位数字',
                value: '2',
                tips:
                  '自动入库商品的批次号规则（如单位转换、完工入库）与审核入库单时商品默认展示的批次号规则（如其他入库、采购入库）为随机12位数字，如346029337889'
              }
            ]
          },
          {
            label: '审核/反审核更新采购收货数据',
            key: 'is_in_order_sync_receipt',
            type: configType.switch,
            show: true,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false,
            tips:
              '开启后，一次性采购收货的采购单在审核及反审核对应的入库单时调整的数据将同步更新采购单的收货数据，若采购单未全部收货/对应多张入库单/关联有采购退货单，则数据不会同步！'
          }
        ]
      },
      {
        title: '出库配置',
        configItems: [
          {
            label: '标品单位转换规则',
            key: 'unit_convert_rule_standard',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '被转换单位按整数转',
                value: '1',
                tips:
                  '发货出库单位转换时，转换后的数值为小数时，将自动进位，如A商品1箱=5斤，当斤缺货16斤时，系统将转换4箱（20斤）进行出库'
              },
              {
                label: '按缺货数量精准转换',
                value: '2',
                tips:
                  '发货出库单位转换时，转换后的数值为小数时，按照实际数值出库，如A商品1箱=5斤，当斤缺货16斤时，系统将3.2箱（16斤）进行出库'
              }
            ]
          },
          {
            label: '非标品单位转换规则',
            key: 'unit_convert_rule_non_standard',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '被转换单位按整数转',
                value: '1',
                tips:
                  '发货出库单位转换时，转换后的数值为小数时，将自动进位，如A商品1箱=5斤，当斤缺货16斤时，系统将转换4箱（20斤）进行出库'
              },
              {
                label: '按缺货数量精准转换',
                value: '2',
                tips:
                  '发货出库单位转换时，转换后的数值为小数时，按照实际数值出库，如A商品1箱=5斤，当斤缺货16斤时，系统将3.2箱（16斤）进行出库'
              }
            ]
          },
          {
            label: '发货出库单位自动转换',
            tips:
              '发货出库时，当商品出库单位库存不足时，将自动把该商品其他单位的库存转为为当前单位库存进行出库，转换后，会生成单位转换类型的出入库单',
            key: 'auto_unit_convert',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '采购退货单位自动转换',
            tips: '采购退货时，当商品退货单位库存不足时，自动将该商品其他单位的库存转为当前单位库存进行出库，转换后，会生成单位转换类型的出入库单',
            key: 'purchase_return_auto_unit_convert',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '领料出库单位自动转换',
            tips: '领料出库时，当商品领料单位库存不足时，自动将该商品其他单位的库存转为当前单位库存进行出库，转换后，会生成单位转换类型的出入库单',
            key: 'process_picking_auto_unit_convert',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '过期批次销售出库',
            key: 'overdue_batch_allow_sale_out',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '禁止销售出库',
                value: '0',
                tips: '过期批次商品禁止销售出库'
              },
              {
                label: '支持手动销售出库',
                value: '1',
                tips: '在发货出库时可以手动选择过期批次商品进行销售'
              },
              {
                label: '先进先出销售出库',
                value: '2',
                tips: '在发货出库时过期批次也将按照先进先出的逻辑进行出库操作'
              }
            ]
          },
          {
            label: '负毛利禁止出库',
            tips:
              '未开启批次，订单商品存在发货单价低于出库成本价时禁止发货出库；若开启批次，则为订单商品存在发货单价低于批次均价时禁止发货出库',
            key: 'minus_margin_out_stock',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          }
        ]
      },
      {
        title: '仓库管理',
        configItems: [
          {
            label: '库位管理',
            key: 'is_open_store_area_location',
            type: configType.switch,
            tips: '开启后，可以对仓库进行库区库位管理',
            tipsImgUrl: baseImgUrl + 'warehouse_management.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '批次商品过期预警',
            key: 'batch_stock_early_warn_day',
            tips:
              '未设置时，不进行过期提醒；设置对应的天数时，当商品过期日期-当前日期<预警天数时，系统将在首页预警模块及通知中进行预警提醒',
            tipsImgUrl: baseImgUrl + 'batch_stock_early_warn_day.png',
            type: 'custom'
          },
          {
            label: '盘点单未审核禁止出入库',
            key: 'inventory_un_reviewed_forbid_in_out_stock',
            type: configType.switch,
            tips:
              '开启后，有未审核的盘点单时，系统将进行锁库操作，操作出入库时均会阻断提醒，确保盘点库存准确',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '新增商品后库区立即可设置',
            key: 'new_commodity_add_store_record',
            type: configType.switch,
            tips: '开启后，新增商品将增加一条库存记录，增加后，库区可配置及筛选',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '小数点配置',
        configItems: [
          {
            label: '单价小数位配置',
            tips:
              '小数位仅涉及展示及打印，因单价计算时仍采用4位小数位，故可能存在精度问题，导致界面上单价*数量≠金额',
            key: 'warehouse_unit_price_decimal',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '整数',
                value: '0'
              },
              {
                label: '1位',
                value: '1'
              },
              {
                label: '2位',
                value: '2'
              },
              {
                label: '4位',
                value: '4'
              }
            ]
          },
          {
            label: '金额小数位配置',
            tips:
              '小数位仅涉及展示及打印，因单价计算时仍采用4位小数位，故可能存在精度问题，导致界面上单价*数量≠金额',
            key: 'warehouse_total_price_decimal',
            type: configType.radioGroup,
            defaultValue: 0,
            items: [
              {
                label: '整数',
                value: '0'
              },
              {
                label: '1位',
                value: '1'
              },
              {
                label: '2位',
                value: '2'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    title: '配送配置',
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            // 司机APP
            label: '配送排序',
            key: 'delivery_user_list_ordering_rule',
            type: configType.radioGroup,
            defaultValue: true,
            items: [
              {
                label: '物流排线顺序',
                value: '1',
                tips:
                  '影响范围：配送助手-配送单列表，线路订单列表，线路客户列表，线路列表-按订单打印，按客户打印'
              },
              {
                label: '临时编码顺序',
                value: '2',
                tips:
                  '影响范围：配送助手-配送单列表，线路订单列表，线路客户列表，线路列表-按订单打印，按客户打印（未开启临时编码时，按照客户名称的字母顺序升序排列）'
              }
            ]
          },
          {
            label: '默认上一次排线顺序',
            key: 'is_open_last_line_sort',
            type: configType.switch,
            defaultValue: 0,
            trueValue: 1,
            falseValue: 0,
            show: configData => {
              return configData.delivery_user_list_ordering_rule == 1;
            }
          },
          {
            label: '扫码装车模式',
            type: configType.radioGroup,
            defaultValue: 1,
            key: 'scan_loading_mode',
            items: [
              {
                label: '按商品装车',
                value: '1',
                tips: '多次分拣时，打印相同的分拣码'
              },
              {
                label: '按分拣次数装车',
                value: '2',
                tips:
                  '多次分拣时，打印不同的分拣码，司机需要扫完全部分拣码才能完成装车',
                tipsImgUrl: baseImgUrl + 'scan_loading_mode.png'
              }
            ]
          },
          {
            label: '配送APP显示设置',
            type: configType.checkboxList,
            key: 'is_app_show_price',
            defaultValue: [],
            items: [
              {
                label: '金额',
                value: 'is_app_show_price',
                trueValue: 1,
                falseValue: 0,
                tips: '取消勾选后，配送APP将隐藏金额',
                tipsImgUrl: baseImgUrl + 'delivery_app_show_setting_moeny.jpg'
              },
              {
                label: '数量',
                value: 'is_app_show_num',
                trueValue: 1,
                falseValue: 0,
                tips: '取消勾选后，配送APP将隐藏数量',
                tipsImgUrl: baseImgUrl + 'delivery_app_show_setting_num.jpg'
              }
            ]
          },
          {
            label: '配送APP送达配置',
            type: configType.radioGroup,
            defaultValue: 1,
            key: 'app_order_delivery_mode',
            items: [
              {
                label: '按客户确认送达',
                value: '1',
                tips: '同一发货日期，同一客户的订单全部发货后才能确认送达'
              },
              {
                label: '按订单确认送达',
                value: '2',
                tips: '订单发货出库后，司机可以针对单个订单确认送达'
              }
            ]
          },
          {
            label: '货物送达必传图片',
            key: 'delivery_pictures_must_be_delivered',
            type: configType.switch,
            tips: '订单发货后，司机端点击确认送达时必须上传图片',
            trueValue: 1,
            falseValue: 0
          },
          {
            label: '货物送达必须定位',
            key: 'delivery_of_goods_must_be_positioned',
            type: configType.switch,
            tips: '订单发货后，司机端点击确认送达时必须获取当前定位',
            trueValue: 1,
            falseValue: 0
          },
          {
            label: '确认送达可点击范围',
            key: 'confirm_delivery_allow_coverage',
            type: configType.custom,
          },
          {
            label: '未发货订单支持上传图片',
            key: 'undelivery_allow_upload_pictures',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: 0,
          },
          {
            label: '送达图片可查看范围',
            key: 'delivered_pictures_show',
            type: configType.checkboxGroup,
            defaultValue: ['0', '1'],
            items: [
              {
                label: '配送助手',
                value: '0',
                disabled: true
              },
              {
                label: '商城订单详情',
                value: '1'
              }
            ]
          }
        ]
      }
    ]
  },
  {
    title: '打印配置',
    labelWidth: 230,
    classify: [
      {
        title: '发货单',
        configItems: [
          {
            label: '发货单总金额小数点位数',
            key: 'delivery_total_decimal',
            type: configType.radioGroup,
            defaultValue: 2,
            items: [
              {
                label: '0（整数）',
                value: '0'
              },
              {
                label: '0.0（一位）',
                value: '1'
              },
              {
                label: '0.00（二位）',
                value: '2',
                tips: '配置主体信息中实际总价字段的小数点',
                tipsImgUrl: baseImgUrl + 'deliver_goods_moeny_ point_num.png'
              }
            ]
          },
          {
            label: '发货数量为“0”时显示发货单价',
            key: 'delivery_amount_zero_show_delivery_unit_price',
            type: configType.switch,
            tips:
              '关闭时，当商品发货数量为“0”时，发货单价发货金额字段将显示为空',
            tipsImgUrl:
              baseImgUrl + 'delivery_amount_zero_show_delivery_unit_price.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '发货单价为“0”时显示为空',
            key: 'delivery_price_zero_show_delivery_unit_price',
            type: configType.switch,
            tips:
              '开启后，当商品发货单价为“0”时，发货单价发货金额字段将显示为空',
            trueValue: 0,
            falseValue: 1,
            defaultValue: false
          },
          {
            label: '发货单位与下单单位显示一致',
            key: 'open_print_order_unit',
            type: configType.switch,
            tips:
              '商品按基础单位分拣，当辅助单位下单时，发货单位打印时与下单单位保持一致，不取发货单位，发货数量根据分拣数量及转换系数重新计算',
            tipsImgUrl: baseImgUrl + 'async_unit_unit_sell.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          {
            label: '发货单打印商品排序方式',
            key: 'is_invoice_print_sort',
            type: configType.radioGroup,
            defaultValue: 1,
            items: [
              {
                label: '按下单顺序排序',
                value: '0'
              },
              {
                label: '按商品分类创建时间排序',
                value: '1',
                tips: '按商品分类创建时间正序排序'
              },
              {
                label: '按商品分类名称排序',
                value: '3',
                tips:
                  '商品分类先排：先按一级分类名称拼音字母正序、再按二级分类；商品名称后排：按商品名称拼音首字母正序、相同时按ID正序'
              },
              {
                label: '按商品编码排序',
                value: '2'
              }
            ]
          },
          {
            label: '隐藏分拣数量为“0”的商品',
            key: 'sort_num_null_is_show_delivery',
            type: configType.switch,
            tips: '开启后，当商品分拣数量为“0”时，打印模板上隐藏该商品的信息',
            tipsImgUrl: baseImgUrl + 'hide_sorting_num_0_goods.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '时价商品发货出库前，价格显示为"时价"',
            key: 'print_time_price_com_view',
            type: configType.switch,
            tips:
              '开启后，时价商品在发货出库前打印时价格显示为"时价"字样，金额显示为空，合计金额不统计时价商品金额（合并打印汇总打印不受此配置项影响）',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '拣货单',
        configItems: [
          {
            label: '打印拣货单顺序',
            type: configType.radioGroup,
            defaultValue: 1,
            key: 'picking_summary_sort',
            items: [
              {
                label: '默认排序',
                value: 'commodity'
              },
              {
                label: '按下单商品排序打印',
                value: 'order_commodity'
              },
              {
                label: '按库区库位排序打印',
                value: 'shelf_code'
              },
              {
                label: '按商品分类创建时间排序打印',
                value: 'commodity_category'
              },
              {
                label: '按商品分类排序号打印',
                value: 'commodity_category_sequence'
              },
            ]
          }
        ]
      },
      {
        title: '配送单',
        configItems: [
          {
            label: '展示配送批量打印侧边栏设置',
            key: 'open_show_delivery_batch_print_sidebar',
            type: configType.switch,
            tipsImgUrl:
              baseImgUrl + 'open_show_delivery_batch_print_sidebar.png',
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '对账单',
        configItems: [
          {
            label: '对账单打印使用发货单模板',
            key: 'open_bill_delivery_print',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          }
        ]
      },
      {
        title: '其他',
        configItems: [
          {
            label: '开启新版打印',
            key: 'is_open_new_version_print',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: false
          },
          // {
          //   label: '打印优先显示别名',
          //   key: 'commodity_alias_none_show_commodity_name',
          //   type: configType.switch,
          //   trueValue: 1,
          //   falseValue: 0,
          //   defaultValue: false,
          //   tips: '开启配置后，打印时优先用取值客户商品别名，其次使用商品别名，都没有则使用商品名称',
          //   show: configData => {
          //     return Number(configData.is_open_new_version_print) === 1;
          //   }
          // },
          {
            label: '打印支持模板选择',
            type: configType.checkboxList,
            key: '_template_label',
            items: [
              {
                label: '配送单',
                value: 'open_choose_delivery_order_print_template',
                trueValue: 1,
                falseValue: 0,
                defaultValue: false,
                tips:
                  '开启后，在配送线路订单列表及线路客户列表中批量及单个打印时显示打印模板弹窗，可以指定模板打印'
                // tipsImgUrl: baseImgUrl + 'open_choose_delivery_order_print_template.png'
              },
              {
                label: '采购单',
                value: 'open_choose_purchase_order_print_template',
                trueValue: 1,
                falseValue: 0,
                defaultValue: false,
                tips: '开启后，打印采购单时支持选择'
              },
							{
								label: '分拣单',
								value: 'open_choose_sort_order_print_template',
								trueValue: 1,
								falseValue: 0,
								defaultValue: false,
								tips: '开启后，后台分拣单打印时增加打印模板弹窗选择'
							},
							{
								label: '单据订单',
								value: 'open_choose_issue_order_print_template',
								trueValue: 1,
								falseValue: 0,
								defaultValue: false,
								show: false,
								tips: '开启后，在单据订单和单据打印时，勾选打印后弹窗可选打印模版',
							}
            ]
          }
        ]
      }
    ]
  },
  {
    title: '营销配置',
    labelWidth: 230,
    classify: [
      {
        title: '',
        configItems: [
          {
            label: '支持多次满赠',
            key: 'can_many_gift',
            type: configType.switch,
            tips:
              '开启后，一个客户一天内多次下单，只要符合满赠条件，可多次满赠',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '支持多次满减',
            key: 'can_many_discount',
            type: configType.switch,
            tips:
              '开启后，一个客户一天内多次下单，只要符合满减条件，可多次满减',
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '启用积分',
            tips:
              '开启后，客户在商城消费会产生积分，可用于兑换“积分活动”中的商品',
            tipsImgUrl: baseImgUrl + 'point_enable.png',
            tipsImgUrl2: baseImgUrl + 'point_enable2.png',
            key: 'point_enable',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '优惠券与营销活动支持同时提交订单',
            tips: '当优惠券与营销活动设置不能叠加使用时，允许同一个订单部分使用优惠券部分使用营销活动。商品同时在两个活动中时，默认优先参与营销活动。',
            key: 'open_coupon_overlap',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '每日仅支持使用一张优惠券',
            tips: '开启后，所有客户在商城提交订单后每日仅允许使用一张优惠券。',
            key: 'user_use_coupon_one_per_day',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
            defaultValue: true
          },
          {
            label: '积分获取条件',
            type: configType.radioGroup,
            defaultValue: '1',
            key: 'point_back_mall_order',
            items: [
              {
                label: '后台订单以及商场订单',
                value: '1',
              },
              {
                label: '仅商城订单',
                value: '2',
              }
            ]
          },
          {
            label: '客户消费___元=1积分',
            key: 'point_ratio',
            type: 'custom'
          },
          {
            label: '订单满___元，可兑换积分商品',
            key: 'point_lower_order_price',
            tipsImgUrl: baseImgUrl + 'point_lower_order_price.png',
            type: 'custom'
          }
        ]
      }
    ]
  },
  {
    title: '溯源配置',
    key: 'config_source_tracing',
    class: 'message-config',
    labelWidth: 230,
    classify: [
      {
        title: '溯源查询',
        configItems: [
          {
            label: '溯源显示信息',
            type: configType.checkboxList,
            key: '_light_label',
            items: [
              {
                label: '供应商资质',
                value: 'is_stsource_provider',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品所属供应商的资质信息'
              },
              {
                label: '检测信息',
                value: 'is_stsource_inspect',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品的溯源检测信息'
              },
              {
                label: '溯源信息',
                value: 'is_stsource_info',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品的采购、分拣、配送信息'
              },
              {
                label: '订单信息',
                value: 'is_stsource_order',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品的下单和签收信息'
              },
              {
                label: '单据商品检测报告',
                value: 'is_show_trace_commodity_report',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品所属溯源报告单中商品检测报告'
              },
              {
                label: '单据检测报告',
                value: 'is_show_trace_order_report',
                trueValue: 1,
                falseValue: 0,
                defaultValue: true,
                tips: '开启后将展示订单商品所属溯源报告单的检测报告'
              },
            ]
          },
          {
            label: '检测结果信息',
            key: 'show_sys_commodity_trace_field',
            type: configType.custom,
          },
          {
            label: '采购日期匹配差值',
            key: 'is_stsource_purchase_day',
            tips:
              '采购日期默认取值为溯源商品分拣日期前的最近一次采购日期，如果在输入的天数内溯源商品未有采购记录，则不显示采购日期。举例：输入配置差值为两天，则默认采购日期取值为商品分拣日期2天内的最新一次采购记录日期，没有则不显示采购日期',
            type: configType.input
          },
          {
            label: '检测报告有效期配置(天)',
            key: 'trace_report_expiry_time',
            type: configType.custom
          },
          {
            label: '检测有效期模式',
            key: 'trace_report_validity_duration_mode',
            type: configType.radioGroup,
            defaultValue: '0',
            items: [
              {
                label: '系统总配置',
                value: '0'
              },
              {
                label: '检测商品有效期',
                value: '1'
              },
              {
                label: '检测单有效期',
                value: '2'
              },
            ],
          },
          {
            label: '订单溯源仅展示检测商品',
            key: 'is_show_detection_source',
            type: configType.switch,
            trueValue: 1,
            falseValue: 0,
          }
        ]
      },
      {
        title: '溯源设备配置',
        configItems: [
          {
            label: '溯源设备上传地址',
            key: 'trace_device_url',
            type: configType.custom
          },
          {
            label: '溯源设备账号名称',
            key: 'trace_device_name',
            type: configType.custom
          },
          {
            label: '溯源设备账号密码',
            key: 'trace_device_password',
            type: configType.custom
          }
          // 可能不需要
          // {
          //   label: '开启溯源',
          //   key: '_text',
          //   type: configType.text,
          //   data:
          //     '提示：请到打印模板--分拣单中选中其中一个模板打开编辑页面，在编辑中勾选是否显示溯源码'
          // },
        ]
      }
    ]
  }
];
let configData = {};
configGroup.map(item => {
  item.classify.forEach((item2, idx) => {
    item2.configItems.forEach((config, index) => {
      if (config.key) {
        if (config.key === 'delivered_pictures_show') {
          console.log('--config.defaultValue--', config, config.defaultValue);
        }
        configData[config.key] =
          config.defaultValue !== undefined ? config.defaultValue : false;
        // 供应商调价阈值设置
        if (config.key === 'provider_offer_price_threshold' || config.key === 'fresh_wholesale_market_threshold') {
          if (!configData[config.key]) {
            configData[config.key]  = { up: 0, down: 0 }
          }
          configData[config.key].up =
            config.defaultValue !== undefined ? config.defaultValue.up : false;
          configData[config.key].down =
            config.defaultValue !== undefined ? config.defaultValue.down : false;
        }
      }
      if (config.type === configType.checkboxList) {
        config.items.forEach(checkBoxItem => {
          configData[checkBoxItem.value] = checkBoxItem.defaultValue
            ? checkBoxItem.defaultValue
            : false;
        });
      }
    });
  });
});

const multiUnitPriceArr = ['one_unit_inquiry_multi_unit_sync', 'open_purchase_in_update_another_unit_in_price']
const newRecipeFields = ['raw_recipe_gen_order_type', 'is_open_accompanying_of_diners', 'raw_recipe_show_fields', 'meal_plan_audit', 'raw_recipe_by_meal_type', 'raw_recipe_commodity_convert_mode']

// 设置唯一key
configGroup.forEach(item => {
  item.uid = uniqueId('$uniqueId-');
  item.classify.forEach(item => {
    item.uid = uniqueId('$uniqueId-classify');
    item.configItems.forEach(item => {
      item.uid = uniqueId('$uniqueId-classify-configItems');
      if (item.items) {
        item.items.forEach(item => {
          item.uid = uniqueId('$uniqueId-classify-configItems-items');
        });
      }
    });
  });
});
export default {
  mixins: [ConfigMixin],
  components: {
    NumberInput,
    Tooltip,
    SIcon
  },
  data() {
    return {
      activeStep: 0,
      sections: [],
      configDataMap: {},
      inPriceAuditDisabled: false,
      configType: configType,
      configData: configData,
      originConfigData: {},
      groupList: [],
      pre_purchase_config: {
        max_stock_limit_flag: 0,
        min_stock_limit_flag: 0,
        max_pur_limit_days: 3,
        min_pur_limit_days: 1,
        last_few_days: 1,
        remember_pre_choice: 0
      },
      show_sys_commodity_trace_field: {
        // 溯源配置 - 检测结果信息
        is_show_control: 1, // 对照值
        is_show_restrain: 1, // 抑制率
        is_show_measured: 1 // 测定值
      },
      raw_recipe_show_fields: {
        summary: 0
      },
      meal_plan_audit: {
        is_group_audit: 0,
        is_super_audit: 0
      }
    };
  },
  beforeCreate() {},
  created() {
    this.init();
    this.getConfig();
  },
  mounted() {},
  methods: {
    getEnv,
    isShow(field) {
      if (field.show === undefined) {
        return true;
      }
      if (typeof field.show === 'function') {
        return field.show(this.configData, this);
      }
      return field.show;
    },
    inputLimit(e) {
      this.$nextTick(() => {
        let value = e.target.value;
        value = value.replace(/\D/g, '');
        if (value < 1 && value) {
          value = 1;
        }
        if (value > 365) {
          value = 365;
        }
        this.configData.trace_report_expiry_time = value;
      });
    },
    initMenuData() {
      let sections = [];
      this.groupList.forEach((item, index) => {
        if (item.title) {
          sections.push({ title: item.title, isMain: true });
        }
        item.classify.forEach((item, index) => {
          if (item.title) {
            sections.push({ title: item.title, isMain: false });
          }
        });
      });
      this.sections = sections;
      console.log('groupList2', sections);
    },
    handleScroll(e) {
      this.rafThrottle(this.handleHighLight(e));
    },
    rafThrottle(func) {
      let lock = false;
      return function(...args) {
        if (lock) return;
        lock = true;
        requestAnimationFrame(() => {
          func.apply(this, args);
          lock = false;
        });
      };
    },
    handleHighLight(e) {
      let scrollItems = document.querySelectorAll('.scroll-top');
      // 判断滚动条是否滚动到底部
      for (let i = scrollItems.length - 1; i >= 0; i--) {
        // 判断滚动条滚动距离是否大于当前滚动项可滚动距离
        let judge =
          e.target.scrollTop >=
          scrollItems[i].offsetTop - scrollItems[0].offsetTop;
        if (judge) {
          this.activeStep = i;
          break;
        }
      }
    },
    jumpTo(id) {
      console.log(id);
      let scrolls = document.getElementById(id);
      scrolls &&
        scrolls.scrollIntoView({ block: 'start', behavior: 'instant' });
    },
    previewImg(url) {
      this.viewImage(url, 0);
    },
    /**
     * @description: 根据版本配置初始化处理配置项
     */
    _initConfigGroup() {
      this.groupList = configGroup.filter(item => {
        item.classify.forEach(item2 => {
          item2.configItems = item2.configItems.filter(config => {
            console.log(
              'this.$hasModule(config.key)',
              this.$hasModule(config.key)
            );
            return this.$hasModule(config.key);
          });
        });
        return this.$hasModule(item.key) && item.classify.length !== 0;
      });
      console.log('configGroup', this.groupList);
      this.initMenuData();
    },
    showCantEditTips() {
      if (this.isOpenProviderDeliver) {
        this.errorNotice('供应商联营模式不可使用订单汇总生成采购单');
      }
    },
    init() {
      this.commonService
        .getConfig()
        .then(config => {
          // 是否关闭套餐
          const isClosePackage = config.is_commodity_package !== '1';
          //是否隐藏新版食谱配置项
          const isHideNewRecipe =
            config.is_new_recipe === '2' ||
            config.commodity_package_mode !== '1';
          if (isHideNewRecipe) {
            configGroup.forEach((items, idx) => {
              if (items.title == '套餐配置') {
                items.classify.forEach(items2 => {
                  items2.configItems.forEach((config, index) => {
                    if (config.key == 'is_new_recipe' || newRecipeFields.includes(config.key)) {
                      config.show = false;
                    }
                  });
                });
              }
            });
          } else {
            const isNewRecipe = +config.is_new_recipe !== 0
            if (!isNewRecipe) {
              configGroup.forEach((items, idx) => {
                if (items.title == '套餐配置') {
                  items.classify.forEach(items2 => {
                    items2.configItems.forEach((config, index) => {
                      if (newRecipeFields.includes(config.key)) {
                        config.show = false;
                      }
                    });
                  });
                }
              });
            }
          }

          // 是否开启供应商分拣采购任务分配商品
          const isOpenProviderSortingPurchaseTask =
            config.is_open_provider_sorting_purchase_task === '1';
          // 是否开启采购任务
          const isEnablePurchaseTask = this.isEnablePurchaseTask;
          // 是否开启供应商联营
          const isProviderSupplier =
            config.provider_supplier_pool_switch == '1';
          // 未开启采购任务 不显示采购任务配置
          configGroup.forEach(items => {
            if (items.title == '采购配置') {
              items.classify.forEach((items2, idx) => {
                if (items2.title == '采购任务') {
                  //
                  if (!isEnablePurchaseTask) {
                    items.classify.splice(idx, 1);
                  }
                  items2 &&
                    items2.configItems.forEach((config, index) => {
                      if (!isOpenProviderSortingPurchaseTask) {
                        if (
                          config.key ==
                          'is_open_provider_sort_amount_auto_reset'
                        ) {
                          items2.configItems.splice(index, 1);
                        }
                        if (
                          config.key == 'purchase_task_receipt_default_amount'
                        ) {
                          items2.configItems.splice(index, 1);
                        }
                        if (!isProviderSupplier) {
                          if (
                            config.key ==
                            'purchase_task_provider_supplier_cal_stock'
                          ) {
                            items2.configItems.splice(index, 1);
                          }
                        }
                      }
                    });
                }
                if (items2.title == '采购单导出&分享') {
                  let dataItems = [
                    {
                      label: '实时下单数量',
                      value: '0',
                      uid: uniqueId('$uniqueId-classify-configItems-items'),
                      tips:
                        '生成采购单后，客户订单数量变更、商品删除将实时同步至客户下单明细'
                    },
                    {
                      label: '生成采购单时的固定数量',
                      value: '1',
                      uid: uniqueId('$uniqueId-classify-configItems-items'),
                      tips:
                        '订单汇总：取生成采购单时的客户下单数量，重复生成时，取首次的;采购任务：取采购任务分配的数量'
                    }
                  ];
                  let dataItems2 = [
                    ...dataItems,
                    {
                      label: '生成采购单时的固定信息（含商品、备注）',
                      value: '2',
                      uid: uniqueId('$uniqueId-classify-configItems-items')
                      // tips: '订单汇总：取生成采购单时的客户下单数量，重复生成时，取首次的;采购任务：取采购任务分配的数量'
                    }
                  ];
                  items2 &&
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'purchase_user_order_item') {
                        if (isEnablePurchaseTask) {
                          // 开启了采购任务
                          config.items = dataItems2;
                          //
                        } else {
                          config.items = dataItems;
                        }
                      }
                    });
                }
              });
            } else if (items.title === '打印配置') {
              items.classify.forEach(items2 => {
                items2.configItems.forEach((config, index) => {
                  if (config.key === '_template_label') {
                    config.items.forEach(item => {
                      if (item.value == 'open_choose_issue_order_print_template') {
                        item.show = this.sysConfig.is_open_issue_order == 1;
                      }
                    })
                  }
                });
              });
            }
          });

          if (+config.custom_batch_no !== 1) {
            configGroup.forEach((items, idx) => {
              if (items.title == '库房配置') {
                items.classify.forEach(items2 => {
                  items2.configItems.forEach((config, index) => {
                    if (config.key == 'batch_no_gen_rule') {
                      items2.configItems && items2.configItems.splice(index, 1);
                    }
                  });
                });
              }
            });
          }
          if (isClosePackage) {
            configGroup.forEach((items, idx) => {
              if (items.title == '套餐配置') {
                configGroup.splice(idx, 1);
              }
            });
          }

          if (+config.commodity_package_mode !== 2) {
            configGroup.forEach((items, idx) => {
              if (items.title == '套餐配置') {
                items.classify.forEach(items2 => {
                  items2.configItems.forEach((config, index) => {
                    if (
                      config.key == 'package_ingredient_show_commodity_price'
                    ) {
                      items2.configItems && items2.configItems.splice(index, 1);
                    }
                  });
                });
              }
            });
          }
          // 是否启用净菜加工
          if (!this.util.isEnableProcess(config)) {
            configGroup.forEach((items, idx) => {
              if (items.title == '加工配置') {
                items.classify.forEach(items2 => {
                  items2.configItems.forEach((config, index) => {
                    if (config.key == 'open_picking_audit') {
                      items2.configItems.splice(index, 1);
                    }
                  });
                });
              }
              if (items.title == '加工配置') {
                configGroup.splice(idx, 1);
              }
            });
          }
          // 是否显示 下单时新商品是否加入协议单
          if (+config.is_open_order_sync_protocol === 0) {
            configGroup.forEach(items => {
              if (items.title == '订单配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '订单录单') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'sync_protocol_is_add_commodity') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 是否显示 安卓、windows分拣端隐藏供应商联营商品
          if (+config.provider_supplier_pool_switch === 0) {
            configGroup.forEach(items => {
              if (items.title == '分拣配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '分拣配置') {
                    items2.configItems.forEach((config, index) => {
                      if (
                        config.key ==
                        'android_windows_hidden_provider_pool_goods'
                      ) {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 联营入库单商品备注显示关联订单生产日期”，仅开启供应商联营时展示
          if (+config.provider_supplier_pool_switch == 0) {
            configGroup.forEach(items => {
              if (items.title == '库房配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '库房配置') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'is_in_record_use_pool_provider_mfg_date') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // pms开启隐藏自动投框
          if (+config.sort_binding_power_mode === 6) {
            configGroup.forEach(items => {
              if (items.title == '分拣配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '分拣配置') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'sort_binding_power_mode') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 是否隐藏 供应商分拣
          if (+config.provider_supplier_pool_switch === 0) {
            configGroup.forEach(items => {
              if (items.title == '采购配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '供应商端') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'is_open_provider_pool_delivery') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 是否隐藏 供应商分拣
          if (+config.provider_supplier_pool_switch === 1) {
            configGroup.forEach(items => {
              if (items.title == '采购配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '供应商端') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'is_supplier_sort') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 是否隐藏 过期批次销售出库
          if (+config.is_batch === 0) {
            configGroup.forEach(items => {
              if (items.title == '库房配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '库房配置') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'is_batch_can_select_mfg_date') {
                        items2.configItems.splice(index, 1);
                      }
                    })
                  }
                  if (items2.title == '出库配置') {
                    items2.configItems.forEach((config, index) => {
                      if (config.key == 'overdue_batch_allow_sale_out') {
                        items2.configItems.splice(index, 1);
                      }
                      // 未开启批次隐藏 批次商品过期预警
                      if (config.key == 'batch_stock_early_warn_day') {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          // 如果开启了订单拆单，则不能再开启订单合并
          if (+config.is_open_split_order === 1) {
            configGroup.forEach(items => {
              if (items.title == '订单配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '订单录单') {
                    items2.configItems.forEach(config => {
                      if (config.key == 'is_open_order_combine') {
                        console.log('is_open_order_combine', config);
                        config.items.forEach(item => (item.disabled = true));
                        config.tips = '已经开启了订单拆单，不能使用订单合并';
                      }
                    });
                  }
                });
              }
            });
          }
          if (
            +config.is_supplier_sort === 0 ||
            +config.is_open_purchase_task === 0
          ) {
            configGroup.forEach(items => {
              if (items.title == '采购配置') {
                items.classify.forEach(items2 => {
                  if (items2.title == '采购任务') {
                    items2.configItems.forEach((config, index) => {
                      if (
                        config.key == 'is_open_provider_sorting_purchase_task'
                      ) {
                        items2.configItems.splice(index, 1);
                      }
                    });
                  }
                });
              }
            });
          }
          configGroup.forEach(items => {
            if (items.title == '采购配置') {
              items.classify.forEach(items2 => {
                if (items2.title == '订单汇总') {
                  items2.configItems.forEach(configItem => {
                    configItem.items.forEach((item, index) => {
                      // 如果没开启了订单标签，则显示订单标签勾选项隐藏
                      if (
                        item.value == 'show_order_pool_order_tag' &&
                        +config.is_open_order_tag === 0
                      ) {
                        item.show = false;
                        setTimeout(() => {
                          this.configData.show_order_pool_order_tag = false;
                        }, 500);
                      } else if (
                        +config.is_open_order_tag &&
                        item.value == 'show_order_pool_order_tag'
                      ) {
                        item.show = true;
                      }
                    });
                  });
                }
              });
            }
          });
          if (+config.is_open_order_tag === 0) {
          }
        })
        .then(() => {
          this._initConfigGroup();
        });
    },
    getConfig() {
      settings.getSystemConfig().then(res => {
        let { status, data } = res;
        if (status) {
          this.initConfigData(data);
        }
      });
    },
    initConfigData(configList) {
      let configData = {};
      let configDataMap = {};
      let configInfoMap = this.getConfigInfoMap();
      configData['use_new_sort_commodity'] = JSON.parse(
        localStorage.getItem('use_new_sort_commodity') || 'true'
      );
      configList.forEach(config => {
        let configKey = config.key;
        let configValue = config.value;
        let configInfo = configInfoMap[configKey];
        if (config.key == 'pre_purchase_config') {
          let value = JSON.parse(config.value);
          this.pre_purchase_config.max_stock_limit_flag = value.max_stock_limit_flag.toString();
          this.pre_purchase_config.min_stock_limit_flag = value.min_stock_limit_flag.toString();
          this.pre_purchase_config.max_pur_limit_days =
            value.max_pur_limit_days;
          this.pre_purchase_config.min_pur_limit_days =
            value.min_pur_limit_days;
          this.pre_purchase_config.last_few_days = value.last_few_days;
          this.pre_purchase_config.remember_pre_choice = value.remember_pre_choice.toString();
          // config.value = JSON.stringify(value)
        } else if (config.key === 'show_sys_commodity_trace_field') {
          const value = JSON.parse(config.value);
          this.show_sys_commodity_trace_field = value;
        } else if (config.key === 'raw_recipe_show_fields') {
          const value = JSON.parse(config.value);
          this.raw_recipe_show_fields = value;
        } else if (config.key === 'meal_plan_audit') {
          const value = JSON.parse(config.value);
          this.meal_plan_audit = value;
        }
        configDataMap[configKey] = config;
        if (configKey in this.configData) {
          if (configInfo) {
            // 转为布尔值
            if (
              configInfo.type === configType.checkboxList ||
              configInfo.type === configType.switch
            ) {
              configData[configKey] =
                configValue == configInfo.trueValue ? true : false;
            } else {
              // 供应商调价阈值设置
              if (configKey === 'provider_offer_price_threshold' || configKey === 'fresh_wholesale_market_threshold') {
                if (!configData[configKey]) {
                  configData[configKey] = {up: 0, down: 0}
                }
                if (configValue === 'Array') {
                  configValue = ``
                }
                const dataValue = configValue ? JSON.parse(configValue) : {up: 0, down: 0}
                configData[configKey].up = +dataValue.up;
                configData[configKey].down = +dataValue.down;
              } else {
                configData[configKey] = configValue;
              }
            }
          } else {
            configData[configKey] = configValue;
          }
        }

        if (configKey === 'open_commodity_price_offer_channel') {
          // 商品资料改价增加审核流程
          configData[configKey] = (
            configData.open_commodity_price_offer_channel || ''
          ).split(',');
        }
        if (configKey === 'is_show_query_fields_in_provider') {
          configData[configKey] = (
            configData.is_show_query_fields_in_provider || ''
          ).split(',');
        }

        if (configKey === 'delivered_pictures_show') {
          configData[configKey] = (
            configData.delivered_pictures_show
          ).split(',');
        }

        if (configKey === 'order_modify_audit') {
          const obj = JSON.parse(configData[configKey]);
          if (!obj.hasOwnProperty('openApi')) {
            obj.openApi = '1';
            configData[configKey] = JSON.stringify(obj);
          }
          if (!obj.hasOwnProperty('provider')) {
            obj.provider = '1';
            configData[configKey] = JSON.stringify(obj);
          }
        }
      });

			configData['multiUnitPrice'] = []
			multiUnitPriceArr.forEach(item => {
				const itemVal = configList.find(config=> item === config.key);
				if (itemVal && +itemVal.value === 1) {
					configData['multiUnitPrice'].push(item)
				}
			})

      if (
        configData.purchase_user_order_item == 2 &&
        !this.isEnablePurchaseTask
      ) {
        configData.purchase_user_order_item = '1';
      }
      const use_new_order_placement = localStorage.getItem(
        'use_new_order_placement'
      );
      const isLocalNewVersion =
        use_new_order_placement !== null && !!+use_new_order_placement; // 本地有配置且是新版
      if (isLocalNewVersion) {
        configData['order_input_version'] = '2';
      }
      const order_modify_audit = JSON.parse(configData.order_modify_audit)
      configData.order_modify_audit = Object.keys(order_modify_audit).filter(key => +order_modify_audit[key] === 1)

      this.configDataMap = configDataMap;
      this.configData = configData;
      this.originConfigData = this.deepClone(configData);
      // console.log(configData.display_down_shelf_commodity);
      // console.log(configData.sort_print);
      //   if (configData.sort_print == true ) {
      //     Bus.$emit('sortsd',{sort_print: configData.sort_print})
      //   }
      this.groupList.forEach(items => {
        items.classify.forEach(items2 => {
          items2.configItems.forEach(config => {
            // 触发客户临时编码change
            if (config.key === 'sort_code') {
              this.onSwitchChange(config, items2);
            }
            // 触发入库模式change
            if (config.key === 'in_mode') {
              this.onRadioGroupChange(config, items2, configData.in_mode);
            }
            //  触发客户编码模式change
            if (config.key === 'user_code_mode') {
              this.onRadioGroupChange(
                config,
                items2,
                configData.user_code_mode
              );
            }
            //  触发客户编码模式change
            if (config.key === 'open_commodity_price_offer_channel') {
              this.handleCheckboxChange(
                config.key,
                configData.open_commodity_price_offer_channel
              );
            }
            if (config.key === 'use_last_order_price') {
              config.tips = `开启后，非协议价商品下单时默认取该商品最近${configDataMap.use_last_order_price_duration.value}天上一笔订单中该商品的下单单价；商城订单允许编辑配置项会默认关闭。`
            }
          });
        });
      });
    },
    /**
     * @param configItem 配置项
     * @param group 配置分组
     * @param value 当前值
     */
    // eslint-disable-next-line no-unused-vars
    onRadioGroupChange(configItem, items2, value) {
      // 更改入库模式
      if (configItem.key === 'in_mode') {
        // 不是审核入库
        if (this.configData['in_mode'] !== '1') {
          this.inPriceAuditDisabled = true;
          // this.configData.in_price_audit = '0';
        } else {
          this.inPriceAuditDisabled = false;
        }
      }
      // 更改客户编码模式
      if (configItem.key === 'user_code_mode') {
        this.groupList.forEach(items => {
          if (items.title == '分拣配置') {
            items.classify.forEach(items2 => {
              if (items2.title == '客户临时编码') {
                items2.configItems.forEach((config, index) => {
                  if (
                    config.key == 'sort_code' ||
                    config.key == 'auto_sort_code'
                  ) {
                    config.show = value == '0';
                    console.log('show----', config.show, value);
                    if (!config.show) {
                      this.configData.sort_code = 0;
                      this.configData.auto_sort_code = 0;
                    } else {
                      this.configData.sort_code = this.originConfigData.sort_code;
                      this.configData.auto_sort_code = this.originConfigData.auto_sort_code;
                    }
                  }
                });
              }
            });
          }
        });
      }
    },
    /**
     * @param configItem 配置项
     * @param items2 配置分组
     */
    onSwitchChange(configItem, items2, value) {
      // 分拣客户临时编码
      if (configItem.key === 'sort_code') {
        let autoSorCodeConfig = items2.configItems.find(
          item => item.key === 'auto_sort_code'
        );
        if (autoSorCodeConfig) {
          // 没有开始客户临时编码,禁用自动生成客户临时编码
          if (!this.configData['sort_code']) {
            autoSorCodeConfig.disabled = true;
            this.configData['auto_sort_code'] = false;
          } else {
            autoSorCodeConfig.disabled = false;
          }
        }
      }
      if (configItem.key === 'is_open_provider_sorting_purchase_task') {
        this.groupList.forEach(items => {
          if (items.title == '采购配置') {
            items.classify.forEach(items2 => {
              if (items2.title == '采购任务') {
                items2.configItems.forEach((config, index) => {
                  if (
                    config.key == 'purchase_task_receipt_default_amount' ||
                    config.key == 'is_open_provider_sort_amount_auto_reset'
                  ) {
                    if (
                      !this.configData.is_open_provider_sorting_purchase_task
                    ) {
                      config.show = false;
                    } else {
                      config.show = true;
                    }
                  }
                });
              }
            });
          }
        });
      }
      if (configItem.key === 'is_new_recipe') {
        const isNewRecipe = value
        this.groupList.forEach(items => {
          if (items.title == '套餐配置') {
            items.classify.forEach(items2 => {
              items2.configItems.forEach((config, index) => {
                if (!isNewRecipe) {
                  if (newRecipeFields.includes(config.key)) {
                    config.show = false;
                  }
                } else {
                  if (newRecipeFields.includes(config.key)) {
                    config.show = true;
                  }
                }
              });
            });
          }
        });
      }
      if(configItem.key === 'modify_store_time' && !value) {
        this.configData.in_time_default_type = '0';
      }
      if (configItem.key === 'is_open_group_order_cron_audit' && !value) {
        this.configData.group_order_cron_audit_time = ''
      }
    },
    getConfigInfoMap() {
      let configMap = {};
      configGroup.forEach(items => {
        items.classify.forEach(items2 => {
          items2.configItems.forEach(config => {
            if (config.type === configType.text) {
              return false;
            }
            if (config.type === configType.checkboxList) {
              config.items.forEach(checkBoxItem => {
                checkBoxItem.type = configType.checkboxList;
                configMap[checkBoxItem.value] = checkBoxItem;
              });
            } else {
              configMap[config.key] = config;
            }
          });
        });
      });

      return configMap;
    },
    getConfigData() {
      let data = [];
      let configInfoMap = this.getConfigInfoMap();

      const configData = this.deepClone(this.configData);

      // 删除本地存储的新老分拣页面切换配置
      delete configData.use_new_sort_commodity;
      // 新老订单下单切换配置在本地存储，不发送至后端
      delete configData.use_new_order_placement;
      // 不发送至后端
      delete configData.trace_device_url;

      // 订单锁定关闭后,生成采购单后自动锁单配置也需要跟着关掉
      if (configData.is_open_order_lock === '0') {
        configData.is_open_gen_purchase_auto_order_lock = false
      }

      if (configData.open_commodity_price_offer_channel) {
        configData.open_commodity_price_offer_channel = configData.open_commodity_price_offer_channel.join(
          ','
        );
      }

      if (configData.is_show_query_fields_in_provider) {
        configData.is_show_query_fields_in_provider = configData.is_show_query_fields_in_provider.join(
          ','
        );
      }

      if (configData.delivered_pictures_show) {
        configData.delivered_pictures_show = configData.delivered_pictures_show.join(
          ','
        );
      }

      // 数据验证
      if (
        Number(this.configData.is_stsource_purchase_day) < 0 ||
        !Number.isInteger(+this.configData.is_stsource_purchase_day)
      ) {
        this.modalError('采购日期匹配差值不能为负数或者小数', 0);
        return false;
      }

			const selectMultiUnitPrice = configData.multiUnitPrice
			multiUnitPriceArr.forEach(item => {
				configData[item] = selectMultiUnitPrice.includes(item)? '1': '0'
			})
			delete configData.multiUnitPrice

      for (let configKey in configData) {
        let configItem = {
          key: configKey,
          value: configData[configKey]
        };

        let configInfo = configInfoMap[configKey];
        if (configInfo) {
          // 布尔值转换为对应的原始值
          if (
            configInfo.type === configType.checkboxList ||
            configInfo.type === configType.switch
          ) {
            configItem.value = configData[configKey]
              ? configInfo.trueValue
              : configInfo.falseValue;
          }
        }
        // 库位开关保存到sessionStorage中
        if (configItem.key === 'is_open_store_area_location') {
          apiUtil.setIsOpenStoreMGT(configItem.value);
        }
        // 配置同步
        // open_print_order_unit 如果开了 is_open_hkd_user_show_order_unit切换到1
        if (
          configItem.key === 'open_print_order_unit' &&
          +configItem.value === 1
        ) {
          data.push({
            key: 'is_open_hkd_user_show_order_unit',
            value: 1
          });
        }
        if (configItem.key === 'show_sys_commodity_trace_field') {
          configItem.value = JSON.stringify(
            this.show_sys_commodity_trace_field
          );
        }
        if (configItem.key === 'raw_recipe_show_fields') {
          configItem.value = JSON.stringify(
            this.raw_recipe_show_fields
          );
        }
        if (configItem.key === 'meal_plan_audit') {
          configItem.value = JSON.stringify(
            this.meal_plan_audit
          );
        }
        if (configItem.key === 'provider_offer_price_threshold' || configKey === 'fresh_wholesale_market_threshold') {
          configItem.value = JSON.stringify(
            configItem.value
          );
        }
        data.push(configItem);
      }

      data.forEach(items => {
				if (items.key === 'is_open_provider_sorting_purchase_task') {
					if (!this.isEnablePurchaseTask) {
						items.value = '0'
					}
				}
        if (items.key == 'pre_purchase_config') {
          let pre_purchase_config = JSON.parse(items.value);
          pre_purchase_config.max_stock_limit_flag = this.pre_purchase_config.max_stock_limit_flag;
          pre_purchase_config.min_stock_limit_flag = this.pre_purchase_config.min_stock_limit_flag;
          pre_purchase_config.max_pur_limit_days = this.pre_purchase_config.max_pur_limit_days;
          pre_purchase_config.min_pur_limit_days = this.pre_purchase_config.min_pur_limit_days;
          pre_purchase_config.last_few_days = this.pre_purchase_config.last_few_days;
          pre_purchase_config.remember_pre_choice = this.pre_purchase_config.remember_pre_choice;
          items.value = JSON.stringify(pre_purchase_config);
        }

        // 实收变更审核
        if (items.key === 'order_modify_audit') {
          const key = ['super', 'mall', 'delivery', 'scale', 'openApi', 'provider']
          let objectValue = {}
          key.map(val => {
            objectValue[val] = items.value.indexOf(val) > -1 ? 1 : 0
          })
          items.value = JSON.stringify(objectValue)
        }
      });
      //将 '是否开启库存批次' 也传入，否则保存之后commit systemConfig 没有is_batch字段，过期预警配置会隐藏掉
      data.push({ key: 'is_batch', value: this.sysConfig.is_batch });
      return data;
    },
    setSortCommoditytype() {
      return new Promise(resolve => {
        const val = this.configData['use_new_sort_commodity'];
        if (val) {
          this.$smodal({
            type: 'warning',
            title: '确定切换到旧版商品分拣页面？',
            text:
              '如非紧急状态，请勿切换到旧版商品分拣页面。建议您在使用新版页面过程中，遇到问题时及时向工程师反馈，我们将根据您的反馈就行优化。为带给你更好的体验，旧版分拣页面将在近期下线，下线后，您仅能使用新版页面',
            onOk: () => {
              localStorage.setItem('use_new_sort_commodity', !val);
              resolve();
            }
          });
        } else {
          localStorage.setItem('use_new_sort_commodity', !val);
          resolve();
        }
      });
    },
    setOrderInputVersion() {
      return new Promise((resolve, reject) => {
        const val = this.configData['order_input_version'];
        if (val === '2') {
          this.$Modal.confirm({
            title: '确定切换到旧版商品下单页面？',
            content:
              '在使用旧版或者新版下单页面过程中，请勿切换开关，将会丢失正处于编辑中的订单信息',
            onOk: () => {
              resolve();
            },
            onCancel: () => reject()
          });
        } else {
          resolve();
        }
      });
    },
    saveConfiguration() {
      console.log('业务配置保存调用');
      //过期预警判断(开启了批次库存，判断过期预警天数)
      let earlyWarnFlag = this.isEnableBatch
        ? this.configData.batch_stock_early_warn_day &&
        this.configData.batch_stock_early_warn_day > 0 &&
        this.configData.batch_stock_early_warn_day <= 365
        : true;
      if (earlyWarnFlag) {
        this.save();
      } else {
        this.$Notice.error({
          title: '过期预警(提前天数)应该大于0,小于365'
        });
      }
    },
    save() {
      let configList = this.getConfigData();
      settings.saveSystemConfig(configList).then(res => {
        let { message, status } = res;
        if (status) {
          this.$Message.success('保存成功');
          this.getConfig();

          if (this.configData['order_input_version'] === '2') {
            localStorage.removeItem('use_new_order_placement');
          }
        } else {
          this.modalError(message, 0);
          // 有些互斥的开关, 抛错后, 重置下数据
          this.getConfig();
        }
      });
    },
    changePurchaseTask() {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>切换生成采购单模式的时候需要注意，不要重复生成采购单。</p>',
        // okText: '我知道了',
        // cancelText: '',
        onOk: () => {},
        onCancel: () => {}
      });
    },
    handleCheckboxChange(key, value) {
       // 商品资料改价增加审核流程 选中供应商改价时，仅审核改价超过调价阈值的商品才显示
      if (key === 'open_commodity_price_offer_channel') {
        this.groupList.forEach(items => {
          if (items.title == '商品配置') {
            items.classify.forEach(items2 => {
              items2.configItems.forEach((config) => {
                if (
                  config.key == 'only_threshold_offer_audit'
                ) {
                  config.show = value.includes('8');
                  // if (!config.show) {
                  //   this.configData.only_threshold_offer_audit = 0;
                  // } else {
                  //   this.configData.only_threshold_offer_audit = this.originConfigData.only_threshold_offer_audit;
                  // }
                }
              });
            });
          }
        });
      }
    }
  }
};
</script>
<style lang="less" scoped>
@colorGray: #999;
.business-config {
  height: calc(~'100vh - 158px');
  box-sizing: border-box;
  overflow-y: scroll;
  font-size: 13px;
  text-align: left;
  padding: 0 170px 0 0;
  .group {
    margin-bottom: 15px;
    border-radius: 2px;
    border: 1px solid #e8e8e8;
    .group-title-box {
      height: 36px;
      padding: 10px 24px 10px;
      border-bottom: 1px solid #e8e8e8;
      background-color: #f6f8f9;
      .group-title {
        font-size: 13px;
        height: 16px;
        line-height: 16px;
        font-family: PingFangHK-Medium, PingFangHK;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
    }
    .group-content-box {
      padding: 0 20px 24px 24px;
      .classify-title-box {
        margin-bottom: 10px;
      }
      .classify-title {
        height: 16px;
        font-size: 13px;
        font-family: PingFangHK-Medium, PingFangHK;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.75);
        line-height: 16px;
      }
      .classify-title:not(:first-child) {
        margin-top: 32px;
      }
      .classify-title::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 11px;
        background: rgba(0, 0, 0, 0.6);
        margin-right: 6px;
        margin-bottom: -1px;
      }
      .group-content {
        margin-bottom: 16px;
        // height: 25px;
        .config-item {
          height: 100%;
          .config-item-content {
            .config-item-label {
              padding-right: 10px;
              text-align: right;
            }
            .ivu-col:last-child {
              display: flex;
              flex: 1;
              align-items: center;
            }
            .ivu-radio-wrapper {
              margin-right: 0px;
            }
            .ivu-radio-wrapper:not(:first-child) {
              margin-left: 13px;
            }
          }
        }
      }
    }
    .group-content-box:nth-child(2) {
      padding-top: 20px;
    }
    .group-content:last-of-type {
      margin-bottom: 0;
    }
  }
  .box {
    height: calc(~'100vh - 300px');
  }
}
.group.message-config {
}
.business-config::-webkit-scrollbar {
  width: 0;
}
.confirm-btn {
  margin-top: 30px;
}
.tip {
  font-size: 12px;
  max-width: 65%;
  line-height: 16px;
  font-family: PingFangSC-Regular, PingFang SC;
  margin-left: 10px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.5);
}
.preview-btn {
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #03ac54;
  cursor: pointer;
  margin-left: 20px;
}
.config-checkbox-list {
  flex-wrap: wrap;
}
.ivu-checkbox-wrapper {
  margin-left: 0px;
  margin-right: 28px;
}
.ivu-checkbox-wrapper:last-child {
  margin-right: 0px;
}
.ivu-checkbox + span,
.ivu-checkbox-wrapper + span {
  margin-right: 0;
}
</style>
<style lang="less">
.aside-box {
  position: fixed;
  overflow-y: auto;
  top: 126px;
  right: 15px;
  width: 145px;
  z-index: 100;
  height: calc(~'100vh - 188px');
  border-left: 1px solid #e8e8e8;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.75);
  .aside-1-box {
    margin-bottom: 20px;
    font-size: 12px;
    line-height: 12px;
    margin-left: 10px;
    position: relative;
    .txt {
      cursor: pointer;
    }
    .txt::before {
      position: absolute;
      top: 0px;
      left: -11px;
      content: '';
      width: 3px;
      height: 12px;
      background: #d8d8d8;
    }
  }
  .aside-2-box {
    margin-top: 12px;
    margin-left: 18px;
    font-size: 12px;
    line-height: 12px;
    .txt {
      cursor: pointer;
      color: rgba(0, 0, 0, 0.5);
      position: relative;
    }
    .txt::before {
      position: absolute;
      top: 0px;
      left: -19px;
      content: '';
      width: 3px;
      height: 12px;
      opacity: 0;
    }
  }
  .aside-1-box:not(:first-child) {
    margin-top: 20px;
  }
  .active {
    > .txt {
      color: #03ac54;
    }
    > .txt::before {
      background: #03ac54;
      opacity: 1;
    }
  }
}
.aside-box&::-webkit-scrollbar {
  width: 1;
}
</style>
