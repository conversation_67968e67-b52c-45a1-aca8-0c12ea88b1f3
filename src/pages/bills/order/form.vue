<template>
  <div class="content-wrap">
    <DetailPage pageType="edit" :title="isAddPage || $route.query.is_copy ? '单据新增' : '单据编辑'" customSaveBtn>
      <Form ref="form" inline label-colon :label-width="98">
        <s-block title="基础信息" class="base-info">
          <div>
            <FormItem label="客户名称">
              <UserSearch
                ref="userInput"
                api="/superAdmin/userApi/getUser"
                :filterable="true"
                @on-change="onChangeUser"
                v-show="isAddPage && !isCopy"
                v-model="formData.user_id"
                :customLabel="handleCustomLabel"
                :params="$route.query.is_copy ? {initQuery: formData.email} : {}"
              />
              <span v-if="!isAddPage || isCopy">{{ formData.email }}</span>
            </FormItem>
            <FormItem label="发货日期">
              <DatePicker
                ref="date"
                :value="formData.delivery_date"
                @on-change="onChangeDate"
                type="date"
                placeholder="发货日期"
              ></DatePicker>
            </FormItem>
            <FormItem label="单号">
              <Input
                :disabled="formData.disabled"
                placeholder="输入单号(不填时自动生成)"
                v-model="formData.order_no"
              />
            </FormItem>
            <FormItem label="送货时间">
              <DeliveryTime
                v-model="formData.delivery_time_id"
              ></DeliveryTime>
            </FormItem>
            <FormItem label="订单状态">
              <CommonSelect
                placeholder="选择订单状态"
                v-model="formData.mode"
                label-key="value"
                value-key="key"
                :data="orderStatus"
              ></CommonSelect>
            </FormItem>
            <FormItem label="支付状态">
              <CommonSelect
                placeholder="选择支付状态"
                v-model="formData.is_pay"
                label-key="value"
                value-key="key"
                :data="payStatus"
              ></CommonSelect>
            </FormItem>
            <FormItem label="订单来源">
              <CommonSelect
                placeholder="选择订单来源"
                v-model="formData.source"
                label-key="value"
                value-key="key"
                :data="orderSource"
              ></CommonSelect>
            </FormItem>
            <FormItem label="对账状态">
              <CommonSelect
                placeholder="选择对账状态"
                v-model="CheckStateData"
                label-key="value"
                value-key="key"
                :data="CheckState"
              ></CommonSelect>
            </FormItem>
            <FormItem label="结算状态">
              <CommonSelect
                placeholder="选择结算状态"
                v-model="SettlementStatusData"
                label-key="value"
                value-key="key"
                :data="SettlementStatus"
              ></CommonSelect>
            </FormItem>
            <FormItem label="创建时间" v-show="showCreateTime">
              <DatePicker
                placeholder="选择创建时间"
                v-model="formData.create_time"
                type="datetime"
              ></DatePicker>
            </FormItem>
            <FormItem label="回单状态">
              <RadioGroup v-model="formData.is_receipt">
                <Radio :label="0">未回单</Radio>
                <Radio :label="1">已回单</Radio>
              </RadioGroup>
            </FormItem>
						<FormItem label="销售金额" v-if="!isAddPage">
							<NumberInput
								v-model="inputTotalPrice"
								:precision="2"
								@on-blur="handleChangeTotalPrice"
								:controls="false"
							></NumberInput>
						</FormItem>
          </div>
          <FormItem label="订单标签" style="width: 100%" v-if="isOpenOrderTag && orderTagList.length > 0">
            <orderTag 
              v-model="selectedOrderTag"
              @on-change="_checkOrderTag"
              :isVisibleArea="false"
              :emitArray="true"
              :checkboxItems="orderTagList"
              :disabled="(item) => {
                return tag_only_one &&
                  selectedOrderTag.length > 0 &&
                  !selectedOrderTag.includes(item.id)
              }
              ">
            </orderTag>
          </FormItem>
          <FormItem v-show="showMainInfo" :label="item.name" :key="item.key" v-for="item in orderCustomizeField">
            <Input style="width: 256px" v-model="item.value" />
          </FormItem>
        </s-block>
        <base-block title="收货信息" class="base-info">
          <FormItem label="收货人">{{ user.receive_name }}</FormItem>
          <FormItem label="联系电话">{{ user.receive_tel }}</FormItem>
          <FormItem label="地址">{{ user.address_detail }}</FormItem>
        </base-block>
        <base-block title="商品清单" v-show="showMainInfo">
          <SVxeEditableTable
            :drag="true"
            show-overflow="ellipsis"
            :max-height="getEditTableHeight()"
            ref="editableTable"
            :columns="columns"
            :data="goodsList"
            :loading="loading"
            :row-class-name="rowClassName"
            :row-config="{useKey: true}"
            :isActiveColumn="true"
            @cell-click="handleRowClick"
            @on-row-drag="onRowDrag"
            storePath="title-/bills/order/edit"
          >
            <template #before-table v-if="isEditPage ? formData.can_add_commodity : true" >
              <SButton styleType="btnStyleForAdd" @click="_batchAddGoods">批量添加</SButton>

              <div v-if="!isCopy && isAddPage" class="add-operation">
                <span v-if="!c_exist_from_is_open_issue_order" @click="showCopyOrder">从历史订单复制新增</span>
                <Tooltip placement="top" maxWidth="120" v-if="is_open_issue_order_un_need_delivery">
                  <template #content>
                    <div style="white-space: normal">订单导入之后修改原订单单据中数据不会发生改变</div>
                  </template>
                  <span @click="copyOrderType = 'import';showCopyOrder()">从订单导入</span>
                </Tooltip>
              </div>
            </template>
            <template #after-table-left>
              <div class="notice-info">
                <s-icon icon="solid-notice" class="tip_icon" />
                <span class="renew_title">取值说明：</span>
                <p>1、销售单价=发货单价；若下单单位与发货单位不一致时，发货单价取下单单价根据转换系统换算数值。配置订单发货单模板时，销售单价默认为发货单中的【发货单价】字段</p>
                <p>2、销售数量=发货数量+加单数量；若原订单未分拣时复制生成客户单据，默认下单数量=发货数量（若下单单位与发货单位不一致时，发货数量取下单单位根据转换系统换算数值）。配置订单发货单模板时，销售单价默认为发货单中的【发货单价】字段</p>
                <p>3、销售金额=发货金额+加单金额，配置订单发货单模板时，销售金额默认为发货单中的【发货金额】字段</p>
              </div>
            </template>
            <template #after-table-right>
              <div>
                <span>销售金额合计：¥{{ totalPrice }}</span>
                <span class="extra-price-info">加单金额合计：¥{{ c_surcharge_price }}</span>
              </div>
            </template>
          </SVxeEditableTable>
        </base-block>
        <base-block title="其他信息"  v-show="showMainInfo">
          <div class="newOrder-remarks">
            <FormItem label="订单备注" style="width: 100%">
              <Input
                v-model="formData.remark"
                type="textarea"
                :maxlength="512"
                show-word-limit
                placeholder="输入订单备注"
              ></Input>
            </FormItem>
          </div>
        </base-block>
      </Form>
      <template #button-after>
        <Button
          type="success"
          @click="save(true)"
        >
          {{saving ? '保存中...' : isAddPage ? '保存' : '保存修改'}}
        </Button>
        <Button
          v-if="isAddPage"
          type="primary"
          ghost
          @click="save(false)"
        >保存并继续新增</Button>
      </template>
    </DetailPage>
    <CopyOrder
      :user="user"
      :goods="goodsList"
      v-if="copyOrder.show"
      :copyOrderType="copyOrderType"
      @on-close="closeCopyOrder"
      @on-copy="copyOrderGoods"
    ></CopyOrder>
    <goods-list-modal
       v-model="showGoodsListModal"
      :params="{
        is_online: createOrderShowOfflineGoods ? '' : 'Y',
        delivery_date: formData.delivery_date,
        is_temp_c: 2
      }"
      modalType="issueOrder"
      :uid="formData.user_id"
      :selectedGoods="is_open_order_add_same_commodity ? [] : goodsList"
      @on-add="selectGoods"
    ><div></div></goods-list-modal>
  </div>
</template>

<script>
import moment from 'moment'
import DateUtil from '../../../util/date';
import DetailPage from '@/components/detail-page/index.js'
import EditableTable from '@/components/editable-table/index.js'
import SIcon from '@components/icon'
import SButton from '@components/button';
import NumberInput from '@/components/basic/NumberInput';
import DeliveryTime from '@/components/delivery/deliveryTimeSelect';
import InfoCard from '@/components/common/info-card/index.vue';
import UserSearch from '@/components/user/userSearch';
import CommoditySelect from '@/components/common/CommoditySelectTable'
import GoodsModal from '@/components/common/goods-modal/modal';
import GoodsListModal from '@components/order/goodsListModal'
import CopyOrder from './copy.vue';
import Goods from '@api/goods.js';
import order from '@api/order.js'
import ConfigMixin from '@/mixins/config';
import IssueShowMixin from './mixins/IssueShowMixin';
import { uniqueId, cloneDeep} from 'lodash-es';
import { get } from '@api/request.js';
import { getEditTableHeight } from '@/util/common'
import SVxeEditableTable from '@/components/s-vxe-editable-table/index.js';
// 修复表头设置脏数据
import { fixTitleCfgData, getTitleList } from './util';
import { DatePicker } from 'view-design';
import iconLin from '@assets/images/appCenter/lin.png'
import {debounce} from 'lodash-es'
import SBlock from '@/components/s-block';
import {
  orderTag,
} from '@/components/standard/sdp-filter-items'

fixTitleCfgData();

const ITEM_KEY = 'id';
const STATUS_NORMAL = 1;
const STATUS_DELETE = 0;
const createOrderGoodsId = () => {
  return uniqueId('goods_')
};
let timeout = null;

// const NONE_LABEL = '--';
export default {
  mixins: [ConfigMixin, IssueShowMixin],
  components: {
    orderTag,
    DetailPage,
    SVxeEditableTable,
    EditableTable,
    NumberInput,
    InfoCard,
    UserSearch,
    SButton,
    // eslint-disable-next-line vue/no-unused-components
    GoodsModal,
    GoodsListModal,
    CopyOrder,
    DeliveryTime,
    SBlock,
  },
  computed: {
		protocolPriceDiscountFixMode() {
			return this.sysConfig.protocol_price_discount_fix_mode
		},
    isAddPage() {
      return !this.formData.id;
    },
    isEditPage () {
      return this.$route.path === '/bills/order/edit'
    },
    showMainInfo () {
      if (!this.isAddPage) return true
      if (!this.formData.user_id) {
        return false
      } else {
        return true
      }
    },
    isCopy() {
      return !!this.$route.query.copy_id;
    },
    isNewPage(){
      const {path, query} = this.$route;
      if(path ==='/bills/order/add' && Object.keys(query).length ===0){
        return true;
      }
      return false;
    },
    c_exist_from_is_open_issue_order(){
      const index = this.goodsList.findIndex(item=>item.from_is_open_issue_order === true);
      if(index > -1){
        return true
      }
      return false;
    },
    // 是否存在从历史单据复制的数据
    c_from_copy(){
      const index = this.goodsList.findIndex(item=>item.from_copy === true);
      if(index > -1){
        return true
      }
      return false;
    },
    // 是否能够编辑商品
    canUpdateCommodity () {
      return !this.isEditPage || this.formData.can_update_commodity
    },
    canDelete() {
      return this.isEditPage ? this.formData.can_deleted_commodity : true
    },
    isVersion() {
      let version = this.$store.state.sysConfig.construct_version
      const nowVersion = `${version}`.split('.').length < 3 ? `${version}`.concat('.0') : `${version}`;
      const cVersion = '001200030000';
      //计算版本号大小，转化大小
      let c = nowVersion.toString().split('.');
      const num_place = ["0000", "000", "00", "0", ""]
      for (let i = 0; i < c.length; i++) {
          const len= c[i].length;
          c[i] = num_place[len] + c[i];
      }
      c = c.join('');
      return c >= cVersion
    }
  },
  watch: {
    storeGoodsList: {
      deep: true,
      handler() {
        setTimeout(() => {
          this.totalPrice = this.storeGoodsList
            .filter(item => !this.isGoodsDelete(item))
            .reduce((total, item) => {
              total += this.computeSellTotalPrice(item);
              total = total.toFixed(2);
              return total;
            }, 0);
          setTimeout(() => {
            this.inputTotalPrice = this.totalPrice
          }, 100)
            this.c_surcharge_price = this.storeGoodsList
              .filter(item => !this.isGoodsDelete(item))
              .reduce((total, item) => {
                total += this.computeExtraPrice(item);
                total = Number(total).toFixed(2);
                return total;
              }, 0);
        }, 0);
      }
    },
    sysConfig () {
      this.tag_only_one = +this.sysConfig.is_open_order_tag_only_one === 1
      this.tag_required = +this.sysConfig.is_open_order_tag_required === 1
    },
    deleteCount(val) {
      let type = this.columns[this.columns.length - 1].type
      if (val === 0 && type === 'action') {
        this.columns.pop()
      }
      else if (val != 0 && type !== 'action' && this.showDeleteCommodity) {
        this.columns.push({
          title: '操作',
          key: 'action',
          type: 'action',
          fixed: 'right',
          width: 150,
          resizable: false,
          actions: ({row}) => {
            let actions = []
            let isDelete = this.isGoodsDelete(row)
            actions.push({
              name: '恢复',
              action: params => {
                if (!isDelete) return
                this.recoverGoods(row);
              },
              style: !isDelete ? {
                color:  '#999',
                cursor: 'not-allowed'
              } : {}
            })
            if (isDelete) actions[0].confirm = '确认恢复商品?'
            return actions
          }
        })
        let titleList = getTitleList() || []
        if (titleList.length > 0 && !titleList.includes('action')) {
          titleList.push('action')
          this.$refs.editableTable && this.$refs.editableTable.$refs.sTable && this.$refs.editableTable.$refs.sTable._colsChange(this.columns.filter(item => titleList.includes(item.key)))
        }
      }
    }
  },
  data() {
    let hasBillLockExtraPrice = localStorage.getItem('isBillLockExtraPrice') !== null
    return {
      configList:{},
			inputTotalPrice: 0,
      totalPrice: 0,
      c_surcharge_price: 0,
      copyOrderType: 'copy', // 标记复制单据弹框类型 copy:复制 / import: 导入
      isRelatedOrderImport: false, // 是否从订单导入
      tag_only_one: false,
      tag_required: false,
      selectedOrderTag: [],
      orderTagList: [],
      CheckState: [
        {
          key: 1,
          value: '未对账'
        },
        {
          key: 2,
          value: '已对账'
        }
      ],
      SettlementStatus: [
        {
          key: 1,
          value: '未结算'
        },
        {
          key: 2,
          value: '部分结算'
        },
        {
          key: 3,
          value: '已结算'
        }
      ],
      showSelectPanel: false,
      copyOrder: {
        show: false
      },
      saving: false,
      continueSaving: false,
      currentIndex: 0,
      auditSaving: false,
      isCanAudit: false,
      isOnlyAdd: false,
      user: {
        id: '',
        email: '',
        address_detail: '',
        receivable_style_name: '',
        tel: ''
      },
      formData: {
        // relate_order_no: null
        id: '',
        order_no: '',
        remarks: '',
        create_time: '',
        is_receipt: 0,
      },
      CheckStateData: 1,
      SettlementStatusData: 1,
      orderStatus: [
        {
          key: 300,
          value: '待发货'
        },
        {
          key: 400,
          value: '待收货'
        },
        {
          key: 500,
          value: '货物送达'
        },
        {
          key: 600,
          value: '已完成'
        }
      ],
      orderMode: [],
      payStatus: [],
      orderSource: [],
      addGoodsInfo: {},
      storeGoodsList: [],
      goodsList: [],
      remoteGoodsList: [],
      deleteCount: 0,
      columns: [],
      isLockExtraPrice: hasBillLockExtraPrice ? +localStorage.getItem('isBillLockExtraPrice') : 1, // 默认锁定加单金额字段
      showGoodsListModal: false,
      loading: false,
      goodsCustomizeField: [], // 订单明细自定义字段
      orderCustomizeField: [], // 订单自定义字段
    };
  },
  created() {
    this.getConfig()
    if(this.isAddPage && !this.$route.query.is_copy) {
      // 只有纯新增的单据（不带订单数据）才需要或许空值的订单自定义字段
      // 先获取公共的订单自定义字段，后续要被单据详情的覆盖
      this.getOrderCustomizeField()
    }
    this.initFormData();
    this.getInitData();
    this.initColumns();
    this.initAddGoodsInfo();
    this.initSysConfig()
    if (this.$route.query.id) {
      this.formData.id = this.$route.query.id;
      this.getDetail(this.formData.id);
    }
    if (this.$route.query.copy_id) {
      this.handleCopyOrder();
    }
  },
  mounted() {
    if (this.isAddPage && !this.isCopy) {
      this.focusUserInput()
      this.initList()
    }
  },
  methods: {
    handleCustomLabel (_, opt) {
      return opt.email && `${opt.email}${opt.receivable_style_name ? `（${opt.receivable_style_name}）` : ''}`;
    },
		// 更改总共销售金额时更改商品明细的销售金额
		// 商品明细存在N条时（N>1),前N-1条修改后的商品明细销售金额 = （修改后的销售金额 / 销售金额）*发货金额
		// 第N条修改后的商品明细销售金额 = 修改后的销售金额 - ∑前N-1条修改后的商品明细销售金额
		// 商品明细存在1条时 修改后的商品明细销售金额 = 修改后的销售金额
		handleChangeTotalPrice(e) {
			const inputValue = e.target.value
			// 判断protocolPriceDiscountFixMode的值,如果为1或3则计算协议市场价,否则计算折扣率,拿到对应的方法赋予changeSellPriceOp
			const changeSellPriceOp =
				+this.protocolPriceDiscountFixMode === 1 || +this.protocolPriceDiscountFixMode === 3 ?
				this.computeAgreedPrice : this.computeDiscount
			if (this.goodsList.length === 0) {
				this.inputTotalPrice = 0;
				return
			}
			if (this.goodsList.length === 1) {
				// 如果列表只有一条已删除的商品,则return
				if (this.isGoodsDelete(this.goodsList[0])) {
					this.inputTotalPrice = 0;
					return
				}
				this.$set(this.goodsList[0], 'sell_total_price', inputValue)
				// 同步到storeGoodsList
				this.storeGoodsList[0] = this.goodsList[0]
        // 更新协议市场价
        // this.$set(this.goodsList[0], 'show_actual_total_price', (inputValue / this.totalPrice * this.goodsList[0].show_actual_total_price).toFixed(2))
				this.handleChangeSellTotalPrice({ row: this.goodsList[0] })
        changeSellPriceOp(this.goodsList[0])
			}

			if (this.goodsList.length > 1) {
        let addUpSellTotalPrice = 0
				// 找到最后一个不是已删除的商品的下标
				let lastUnDeleteIndex = this.goodsList.findLastIndex(item => !this.isGoodsDelete(item));

				// 循环商品进行价格分配,如果遇到删除的商品则跳过
        this.goodsList.forEach((item, index) => {
					if (this.isGoodsDelete(item)) {
						console.log('已删除物品,什么也不做')
						return
					}
          if (index < lastUnDeleteIndex && !this.isGoodsDelete(item)) {
            this.$set(this.goodsList[index], 'sell_total_price', (inputValue / this.totalPrice * item.sell_total_price).toFixed(2))
            this.storeGoodsList[index] = item
            this.handleChangeSellTotalPrice({ row: item })
            // 更新协议市场价
            changeSellPriceOp(item)
            addUpSellTotalPrice = addUpSellTotalPrice + Number(this.goodsList[index].sell_total_price)
          } else {
            this.$set(this.goodsList[index], 'sell_total_price', (inputValue - addUpSellTotalPrice).toFixed(2))
            this.storeGoodsList[index] = item

            // this.$set(this.goodsList[index], 'show_actual_total_price', (inputValue / this.totalPrice * item.show_actual_total_price).toFixed(2))
            this.handleChangeSellTotalPrice({ row: item })
						// 更新协议市场价
						changeSellPriceOp(item)
          }
        })
			}

			this.$forceUpdate()
		},
    getConfig() {
      get(this.apiUrl.getIssueConfig).then((res) => {
        this.configList = res.data || this.configList;
      });
    },
    _batchAddGoods () {
      if (this.formData.user_id) {
        this.showGoodsListModal = true
      } else {
        this.modalError('请选择正确的客户')
      }
    },
    getEditTableHeight,
    isOriginalOrderCommodity(row) {
      if (
        (row.order_commodity_id === '0' || row.order_commodity_id === '' || !row.order_commodity_id) &&
        !this.isRelatedOrderImport
      ) {
        return false
      }
      return true;
    },
    isRowEditable(row, columnKey) {
      if (!row) {
        return false
      }
      // 开启后不能编辑，只能查看
      if(!this.canUpdateCommodity){
        return false
      }
      // 删除的商品不能编辑
      if (this.isGoodsDelete(row)) {
        return false
      }
      /**
       * 开启单据无需发货
       * 新增页
       * 不是从历史订单复制
       */
      if (
        this.is_open_issue_order_un_need_delivery &&
        this.isNewPage &&
        !this.c_from_copy &&
        [
          'extra_amount', // 加单数量
          'sell_amount', // 销售数量
          'sales_unit_num', // 销售数量原
          'sell_price', // 销售单价
          'sell_total_price', // 销售金额
        ].includes(columnKey)
      ) {
        return false
      }
      // 没开启单据无需发货才能编辑的字段
      if (
        [
          'show_actual_amount' // 发货数量
        ].includes(columnKey) &&
        !this.is_open_issue_order_un_need_delivery
      ) {
        return false
      }
      // 从历史订单复制不能编辑的字段
      if (
        [
          'show_actual_amount'
        ].includes(columnKey) &&
        this.c_from_copy
      ) {
        return false
      }
      // 只有新增页面才能编辑的字段
      if (
        [
          'show_actual_amount'
        ].includes(columnKey) &&
        !this.isAddPage
      ) {
        return false
      }
      // 锁定加单金额
      if (this.isVersion && !!this.isLockExtraPrice && columnKey === 'extra_price') {
        return false
      }
      // 没有锁定加单金额，加单数量不能修改
      if (this.isVersion && !this.isLockExtraPrice && columnKey === 'extra_amount') {
        return false
      }
      return true
    },
    /**
     * @description: 获取订单标签列表
     */
    _getTagList (user_id) {
      const params = {
        user_id
      }
      order.qryOrderTagList(params).then(res => {
        const { status, data } = res
        if (status) {
          this.orderTagList = data
          // 新增的时候
          if (this.isAddPage) {
            // this.selectedOrderTag中只保留this.orderTagList存在的标签
            this.selectedOrderTag = this.selectedOrderTag.filter(item => {
              return this.orderTagList.some(tag => tag.id === item.id)
            })
          }
        }
      })
    },
    /**
     * @description: 选择订单标签限制
     */
    _checkOrderTag () {
      if (this.tag_only_one && this.selectedOrderTag.length > 1) {
        this.selectedOrderTag.pop();
        this.$Message.warning('只能选择1个订单标签');
        return
      }
      if (this.selectedOrderTag.length > 3) {
        this.selectedOrderTag.pop();
        this.$Message.warning('最多选择3个订单标签');
        return
      }

      if (this.tag_required && this.selectedOrderTag.length === 0) {
        this.$Message.warning('请至少选择一个订单标签');
        return
      }
    },
    initSysConfig () {
      this.tag_only_one = +this.sysConfig.is_open_order_tag_only_one === 1
      this.tag_required = +this.sysConfig.is_open_order_tag_required === 1
    },
    handleRowClick(event) {
      this.currentIndex = event.rowIndex;
    },
    rowClassName ({row, rowIndex}) {
      const classList = []
      if (this.isGoodsDelete(row) && !this.showDeleteCommodity) {
        classList.push('row-delete')
      }
      if (rowIndex === this.currentIndex) {
        classList.push('vxe-body--row--highlight')
      }
      return classList.join(' ')
    },
    // 更改销售数量（原）同时变动销售数量
    computeAmount(params) {
      params.row.sell_amount = params.row.sales_unit_num;
      if (
        params.row.unit_convert === 'Y' &&
        Number(params.row.unit_num) - 1 !== 0
      ) {
        params.row.sell_amount =
          params.row.sales_unit_num * params.row.unit_num;
      }
      this.handleChangeSellAmount(params);
    },
    reset() {
      this.resetUser();
      this.initList();
      if (this.isCopy) {
        this.$router.replace({ query: '' })
        this.initFormData()
        this.getInitData()
      }
    },
    initFormData() {
      this.formData = {
        email: '',
        user_id: '',
        delivery_date: DateUtil.getTomorrow(),
        remark: '',
        delivery_time_id: '',
        receive_tel: '',
        receive_name: '',
        address_details: '',
        mode: 300,
        is_pay: '',
        source: '',
        order_no: '',
        is_receipt: 0,
      };
    },
    getInitData() {
      this.$request.get(this.apiUrl.getBehalfOrderStatus).then(res => {
        let { status, data } = res;
        if (status) {
          this.payStatus = data.is_pay;
          this.orderStatus = data.mode;
          this.orderSource = data.source;
          this.formData.mode = data.mode[0].key;
          this.formData.is_pay = data.is_pay[0].key;
          this.formData.source = data.source[0].key;
        }
      });
    },
    // 用户自定义字段的key不固定，需要通过接口获取
    setCustomizeFieldKeys() {
      return new Promise((resolve) => {
        this.$request
          .get(this.apiUrl.customizeFieldKeys, {
            customize_type: '0,4',
          })
          .then(({ status, data }) => {
            if (status && data && data.length) {
              const keys = data.map((item) => {
                // 商品自定义字段，只需要展示
                if (+item.customize_type === 0) {
                  return {
                    title: item.name,
                    key: item.key,
                  };
                }
                // 订单自定义字段，需要可编辑
                if (+item.customize_type === 4) {
                  this.goodsCustomizeField.push(item);
                  return {
                    title: item.name,
                    key: item.key,
                    render: (h, params) => {
                      let row = params.row;
                      let index = params.index;
                      return h('i-input', {
                        props: {
                          value: row[item.key],
                          maxlength: '256',
                        },
                        on: {
                          'on-enter': () => {
                            this.goodsList[index][item.key] = row[item.key];
                          },
                        },
                        nativeOn: {
                          change: ($event) => {
                            const value = $event.target.value;
                            this.storeGoodsList[index][item.key] = row[item.key] =
                              value;
                            this.goodsList[index][item.key] = value;
                          },
                        },
                      });
                    },
                  };
                }
              });
              resolve(keys);
            } else {
              resolve([]);
            }
          });
      });
    },
  	async initColumns() {
      let columns = [
        {
          type: 'titleCfg',
          titleType: 'issue_order',
          key: 'title',
          width: 52,
          align: 'center',
          fixed: 'left',
          render: (h, params) => {
            const { row, index } = params
            let forbidDelete = (!this.canDelete || this.isGoodsDelete(params.row))
            let deleteOp
            if (forbidDelete) {
              deleteOp = h(SIcon, {
                props: {
                  icon: 'jian',
                  size: 16
                },
                class: ['not-allowed'],
              })
            } else {
              deleteOp = h('Poptip', {
                class: 'pointer icon-record-editor icon-record-editor--insert',
                props: {
                  title: '确认删除此商品？',
                  confirm: true,
                  transfer: true,
                  placement: 'right',
                  // disabled: this.storeGoodsList.length === 1
                },
                on: {
                  'on-ok': () => { this.deleteGoods(row, index) }
                },
                nativeOn: {
                  click: $event => {
                    $event.stopPropagation()
                  }
                }
              }, [
                h(SIcon, {
                  props: {
                    icon: 'jian',
                    size: 16
                  },
                  class: ['pointer'],
                })
              ])
            }
            const operation = h('div', [
              deleteOp,
              h(SIcon, {
                props: {
                  icon: 'jia1',
                  size: 16
                },
                class: [ this.isEditPage && this.formData.can_add_commodity === 0 ? 'not-allowed' : 'pointer', 'icon-record-editor', 'icon-record-editor--delete'],
                style: {
                  marginTop: '5px'
                },
                on: {
                  click: event => {
                    // this.addBlankGoods(index)
                    this.$refs.editableTable.insertOneAt(row)
                    event.stopPropagation()
                  }
                }
              })
            ])
            return operation
          }
        },
        {
          title: '序号',
          align: 'center',
          key: 'index',
          fixed: 'left',
          width: 50,
          render: (h, params) => {
            return h('span', params.index + 1);
          }
        },
        {
          title: '商品图片',
          key: 'logo',
          fixed: 'left',
          // width: 80,
          align: 'center',
          render: (h, params) => {
            return h('img', {
              attrs: {
                src: (params.row.logo || 'https://base-image.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg') + '!40x40'
              }
            });
          }
        },
        {
          title: '商品名称',
          width: 275,
          key: 'name',
          fixed: 'left',
          editRender: { autofocus: '.ivu-input', autoselect: true },
          render: (h, params) => {
            const { row, index } = params
            const inputProps = {
              type: 'textarea',
              rows: 1,
              autosize: { minRows: 1, maxRows: 2.2 }
            }

            if (row.commodity_id) {
              let isDelete = this.isGoodsDelete(row)
              const isOriginalOrderCommodity = this.isOriginalOrderCommodity(row);
              return (
                <span class="commodity-select" style={{ display: 'flex', alignItems: 'center' }}>
                  {+row.is_temp_c === 1 ? <img
                    src={iconLin}
                    class="icon-lin mr5"
                    style={{ width: '16px' }}
                  /> : null}
                  <span class="line-clamp-2">{row.name}</span>
                  {isDelete && <span class="common-tag tag-red">已删除</span>}
                  {!isOriginalOrderCommodity && <SIcon style="font-size: 14px; margin-left: 4px; color: rgb(53, 119, 247)" icon="dan"/>}
                </span>
              )
            }
            return (
              <CommoditySelect
                class="commodity-select"
                commodityName={row.name}
                params={{
                  user_id: this.formData.user_id,
                  delivery_date: this.formData.delivery_date,
                  is_show_activity_unit_price: 1,
									pageSize: 30,
                  is_online: this.createOrderShowOfflineGoods ? '' : 'Y'
                }}
                dataProvider={this.apiUrl.getCommodity}
                selectedData={this.goodsList}
                commodityNameKey="name"
                selectType="table"
                onOn-change={debounce((cid) => this.$_onSelectGoods(cid, index), 300)}
                onCreateGood={(newGood) => {
                  this.$_onSelectGoods(
                    newGood.commodity_id,
                    index,
                  )
                }}
                nativeOnKeydown={e => {
                  // 单独处理回车交互，解决中文输入法下回车也会触发回车事件问题
                  if (e.code.includes('Enter')) {
                    e.stopPropagation();
                  }
                }}
                onOn-enter={() => {
                  this.$refs.editableTable.insertOneAt(row)
                  // this.addBlankGoods(index)
                }}
                inputProps={inputProps}
                slot-type="bill"
                isOpenCreated
                isShowTemporary
              />
            )
          }
        },
        {
          title: '客户商品别名',
          width: 100,
          key: 'user_commodity_alias_name',
        },
        {
          title: '描述',
          key: 'summary',
          minWidth: 100,
          poptip: true,
        },
        {
          title: '下单单位',
          key: 'unit'
          // width: 80,
        },
        {
          title: '下单数量',
          key: 'amount',
          // width: 90,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'amount')
          },
          render: (h, params) => {
            let key = 'amount';
            if (!this.isRowEditable(params.row, 'amount')) {
              return h('span', {}, params.row[key]);
            }
            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                min: 0,
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  this.goodsList[params.index].is_amount_change = true;
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
                    this.handleChangeAmount(params);
                    this.handleChangeExtraAmount(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              }
            });
          }
        },
        {
          title: '下单单价',
          key: 'unit_price',
          align: 'right',
          width: 140,
          render: (h, params) => {
            const { row } = params;
            let key = 'unit_price';
            let text = row[key]
            if (text && text !== '--') {
              text = '¥' + row[key]
            }
            let template = [h('span', text)];
            if (row[key] && Goods.isProtocolGoods(row)) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'xie',
                    size: 16
                  },
                  class: 'mr5',
                  style: {
                    marginLeft: '5px',
                    position: 'relative',
                    top: '-2px'
                  }
                })
              );
            }
            return h('div', template);
          }
        },
				{
					title: '折扣率（订）%',
					key: 'protocol_discount'
				},
				{
					title: '协议市场价（订）',
					key: 'protocol_org_price'
				},
        {
          title: '下单金额',
          key: 'place_price',
          align: 'right',
          minWidth: 140,
          render: (h, params) => {
            let text = params.row.place_price
            if (text && text !== '--') {
              text = '¥' + params.row.place_price
            }
            return h('span', {}, text);
          }
        },
        {
          title: '发货单位',
          key: 'sort_unit'
          // width: 90,
        },
        {
          title: '发货数量',
          key: 'show_actual_amount',
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'show_actual_amount')
          },
          render:(h, params)=>{
              // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
              const {is_open_issue_order_un_need_delivery} = this;
              let key = 'show_actual_amount';
              // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
              if(this.isRowEditable(params.row, key)){
                // 开启后可以输入内容
                return h(NumberInput, {
                    props: {
                      precision: 2,
                      placeholder: '',
                      value: params.row[key]
                    },
                    on: {
                      'on-change': value => {
                        params.row[key] = value;
                        this.$_onChange(key, params);
                      },
                      'on-enter': () => {
                        this.goodsList[params.index][key] = params.row[key]
                        // this.addBlankGoods(params.index)
                      }
                    }
                  });
              }
              return h('span', {}, params.row[key]);
          },
        },
        {
          title: '发货单价',
          key: 'show_actual_unit_price',
          align: 'right',
          minWidth: 100,
          render: (h, params) => {
            let text = params.row.show_actual_unit_price
            if (text && text !== '--') {
              text = '¥' + params.row.show_actual_unit_price
            }
            return h('span', {}, text);
          }
        },
        {
          title: '发货金额',
          key: 'show_actual_total_price',
          align: 'right',
          minWidth: 100,
          render: (h, params) => {
            let text = params.row.show_actual_total_price
            if (text && text !== '--') {
              text = '¥' + params.row.show_actual_total_price
            }
            return h('span', {}, isNaN(text)? '--' : text);
          }
        },
        {
          title: '加单数量',
          key: 'extra_amount',
          tip: '单据订单新增的商品若修改加单数量则会联动修改下单数量，但修改下单数量不联动修改加单数量',
          // width: 100,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'extra_amount')
          },
          render: (h, params) => {
            let { extra_amount, unit_sell } = params.row;
            let amount = extra_amount;
            if (params.row._salesUnitNumshowUnit) {
              amount = unit_sell ? extra_amount + unit_sell : extra_amount;
            }
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            const {is_open_issue_order_un_need_delivery} = this;
            let key = 'extra_amount';

            // 开启后不能编辑，只能查看
            if(!this.isRowEditable(params.row, key)){
              return h('span', {}, amount);
            }

            return h(NumberInput, {
              props: {
                precision: 2,
                placeholder: '',
                value: amount
              },
              on: {
                'on-focus': event => {
                  params.row._salesUnitNumshowUnit = false;
                  if (timeout) clearTimeout(timeout);
                  timeout = setTimeout(() => {
                    event.target.select();
                  });
                },
                'on-blur': value => {
                  params.row._salesUnitNumshowUnit = true;
                },
                'on-change': value => {
                  params.row[key] = value;
                  if (!this.isOriginalOrderCommodity(params.row)) {
                    const unitNum = params.row.unit_convert === 'Y' ? params.row.unit_num : 1;
                    params.row.amount = (value / unitNum).toFixed(2);
                    this.handleChangeAmount(params, false);
                  }
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
                    this.handleChangeExtraAmount(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              }
            });
          }
        },
        {
          title: '加单金额',
          key: 'extra_price',
          width: 100,
          align: 'center',
          renderHeader: h => {
            if (!this.isVersion) return h('div', '加单金额')
            return h('div', {}, [
              h('span', '加单金额'),
              h('Icon', {
                class: 'tip-icon',
                props: {
                  type: this.isLockExtraPrice ? 'ios-lock' : 'ios-unlock',
                },
                on: {
                  'click': () => {
                    this.isLockExtraPrice = !this.isLockExtraPrice ? 1 : 0
                    localStorage.setItem('isBillLockExtraPrice', this.isLockExtraPrice)
                  }
                }
              })
            ])
          },
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'extra_price')
          },
          render: (h, params) => {
            let key = 'extra_price';
            const {is_open_issue_order_un_need_delivery} = this;

            if (!this.isRowEditable(params.row, 'extra_price')) {
              return h('span', {}, params.row[key]);
            }
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            if(is_open_issue_order_un_need_delivery && this.isNewPage && !this.c_from_copy) {
              // 开启后可以输入内容
              return h(NumberInput, {
                  props: {
                    disabled: this.isGoodsDelete(params.row) || (this.isVersion && !!this.isLockExtraPrice),
                    precision: 2,
                    placeholder: '请输入',
                    value: params.row[key]
                  },
                  on: {
                    'on-change': value => {
                      params.row[key] = value;
                      this.$_onChange(key, params);
                    },
                    'on-enter': () => {
                      this.goodsList[params.index][key] = params.row[key]
                    }
                  }
                });
            }
            return h(NumberInput, {
              props: {
                disabled: this.isGoodsDelete(params.row) || (this.isVersion && !!this.isLockExtraPrice),
                precision: 2,
                placeholder: '请输入',
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;

                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
                    this.handleChangeExtraPrice(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              }
            });
          }
        },
        {
          title: '销售数量',
          key: 'sell_amount',
          width: 100,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'sales_unit_num')
          },
          render: (h, params) => {
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            const {is_open_issue_order_un_need_delivery} = this;
            let key = 'sell_amount';
            // 开启后不能编辑，只能查看
            if(!this.isRowEditable(params.row, key)) {
              return h('span', {}, params.row[key]);
            }
            return h(NumberInput, {
              props: {
                disabled: this.isGoodsDelete(params.row),
                min: 0,
                precision: 2,
                placeholder: '',
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
                    this.handleChangeSellAmount(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                }
              }
            });
          }
        },
        {
          title: '销售数量（原）',
          width: 120,
          key: 'sales_unit_num',
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'sales_unit_num')
          },
          render: (h, params) => {
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            const {is_open_issue_order_un_need_delivery} = this;
            let { row, index } = params;
            let key = 'sales_unit_num';
            let me = this;
            let { sell_amount, unit_convert, unit_num, unit } = params.row;
            function getAmount () {
              // 销售数量(原) 编辑时不显示单位
              if (row._salesUnitNumshowUnit) {
                var amount = unit ? sell_amount + unit : sell_amount;
                if (unit_convert === 'Y' && Number(unit_num) - 1 !== 0) {
                  amount = (sell_amount / unit_num).toFixed(2) + unit;
                }
                return amount
              } else {
                // eslint-disable-next-line no-redeclare
                var amount = sell_amount;
                if (unit_convert === 'Y' && Number(unit_num) - 1 !== 0) {
                  amount = (sell_amount / unit_num).toFixed(2);
                }
                return amount
              }
            }
            if(!this.isRowEditable(params.row, key)){
              let amount = getAmount()
              return h('span', {}, amount);
            }
            let amount = getAmount()
            return h(NumberInput, {
              props: {
                disabled: this.isGoodsDelete(row),
                value: amount,
                min: 0,
                precision: 2
              },
              style: {
                width: '100%',
                display: 'inline-block'
              },
              on: {
                'on-focus': event => {
                  row._salesUnitNumshowUnit = false;
                  if (timeout) clearTimeout(timeout);
                  timeout = setTimeout(() => {
                    event.target.select();
                  });
                },
                'on-blur': () => {
                  row._salesUnitNumshowUnit = true;
                  // eslint-disable-next-line no-self-assign
                  row[key] = row[key];
                },
                'on-change'(val) {
                  row[key] = val;
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(me.issue_order_data_contact_way){
                    me.$_onModeChange(key, params);
                  }else{
                    me.computeAmount(params);
                  }
                },
                'on-enter': () => {
                  me.goodsList[index][key] = row[key]
                  // me.addBlankGoods(index)
                }
              }
            });
          }
        },
        {
          title: '销售单价',
          key: 'sell_price',
          width: 100,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'sell_price')
          },
          render: (h, params) => {
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            const {is_open_issue_order_un_need_delivery} = this;
            let key = 'sell_price';
            // 开启后不能编辑，只能查看
            if(!this.isRowEditable(params.row, key)){
              return h('span', {}, params.row[key]);
            }
            return h(NumberInput, {
              props: {
                disabled: this.isGoodsDelete(params.row),
                precision: 2,
                placeholder: '请输入',
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
										this.fixationValueChange(key, params)
                    this.handleChangeSellPrice(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              }
            });
          }
        },
				{
					title: '折扣率（销）%',
					key: 'discount',
					width: 140,
					editRender: {
						autofocus: 'input',
						autoselect: true,
						editAble: (row) => this.isRowEditable(row, 'discount')
					},
					render: (h, params) => {
						let key = 'discount';
						// 开启后不能编辑，只能查看
						if(!this.isRowEditable(params.row, key)){
							return h('span', {}, params.row[key]);
						}
						return h(NumberInput, {
							props: {
								precision: 2,
								placeholder: '请输入',
								value: params.row[key]
							},
							on: {
								'on-change': value => {
									params.row[key] = value;
                  this.handleChangesDiscount(params)
								},
								'on-enter': () => {
									this.goodsList[params.index][key] = params.row[key]
								}
							}
						});
					}
				},
				{
					title: '协议市场价（销）',
					key: 'org_price',
					width: 140,
					editRender: {
						autofocus: 'input',
						autoselect: true,
						editAble: (row) => this.isRowEditable(row, 'org_price')
					},
					render: (h, params) => {
						let key = 'org_price';
						// 开启后不能编辑，只能查看
						if(!this.isRowEditable(params.row, key)){
							return h('span', {}, params.row[key]);
						}
						return h(NumberInput, {
							props: {
								precision: 2,
								placeholder: '请输入',
								value: params.row[key]
							},
							on: {
								'on-change': value => {
									params.row[key] = value;
                  this.handleChangesAgreementPrice(params);
								},
								'on-enter': () => {
									this.goodsList[params.index][key] = params.row[key]
								}
							}
						});
					}
				},
        {
          title: '销售金额',
          key: 'sell_total_price',
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row, 'sell_total_price')
          },
          // width: 100,
          render: (h, params) => {
            // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
            const {is_open_issue_order_un_need_delivery} = this;
            let key = 'sell_total_price';
            // 开启后不能编辑，只能查看
            if(!this.isRowEditable(params.row, 'sell_total_price')){
              return h('span', {}, params.row[key]);
            }
            return h(NumberInput, {
              props: {
                disabled: this.isGoodsDelete(params.row),
                precision: 2,
                placeholder: '',
                value: params.row[key]
              },
              on: {
                'on-change': value => {
                  params.row[key] = value;
                  // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
                  if(this.issue_order_data_contact_way){
                    this.$_onModeChange(key, params);
                  }else{
                    this.handleChangeSellTotalPrice(params);
                  }
                },
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              }
            });
          }
        },
        {
          title: '税率',
          sortable: true,
          sortBy: 'tax_rate',
          key: 'tax_rate_desc',
          editRender: { autofocus: 'input', autoselect: true },
          width: 130,
          render: (h, params) => {
            const { index, row } = params;
            const key = 'tax_rate';
            const rightTemp = h(SIcon, {
              style: {
                cursor: 'pointer',
              },
              class: {
                ml10: true,
              },
              props: {
                icon: 'edit',
                size: 14,
              },
              on: {
                click: () => {
                  row.isAlter = !row.isAlter;
                },
              },
            });
            let leftTemp = h('span', row.tax_rate || 0);
            if (row.isAlter) {
              leftTemp = h(NumberInput, {
                props: {
                  precision: 0,
                  min: 0,
                  max: 99,
                  placeholder: '',
                  value: row[key],
                },
                on: {
                  'on-change': (value) => {
                    row[key] = value;
                    this.storeGoodsList[index][key] = value;
                  },
                  'on-enter': () => {
                    row.isAlter = !row.isAlter;
                  },
                },
              });
            }

            const template = h(
              'div',
              {
                style: {
                  display: 'flex',
                  'align-items': 'center',
                },
              },
              [leftTemp, rightTemp],
            );
            return template;
          },
        },
        {
          title: '税额',
          sortable: true,
          key: 'tax_rate_price',
          width: 100,
          render: (h, { row }) => {
            const sell_total_price = isNaN(row.sell_total_price) ? 0 : row.sell_total_price;
            const taxRatePrice = +row.tax_rate
              ? sell_total_price / (1 + row.tax_rate / 100) * row.tax_rate / 100
              : 0;
            return h('span', taxRatePrice.toFixed(2))
          }
        },
				{
					title: '生产日期',
					key: 'produce_date',
          width: 140,
          // 日期选择器
          render: (h, params) => {
            const { row, index } = params
            let key = 'produce_date';
            if (!this.isRowEditable(params.row, key)) {
              return h('span', {}, params.row[key]);
            }
            return h(DatePicker, {
              props: {
                type: 'date',
                placeholder: '请选择',
                transfer: true,
                value: row[key]
              },
              on: {
                'on-change': value => {
                  row[key] = value;
                  this.goodsList[index][key] = value;
                  this.$set(this.storeGoodsList, index, this.deepClone(row));
                },
                'on-enter': () => {
                  this.goodsList[index][key] = row[key]
                }
              }
            });
          }
				},
        {
          title: '备注',
          key: 'remark',
          align: 'center',
          width: 140,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row)
          },
          render: (h, params) => {
            let key = 'remark';
            if (!this.isRowEditable(params.row)) {
              return h('span', params.row[key])
            }
            return h('i-input', {
              props: {
                placeholder: '',
                value: params.row[key],
                disabled: !this.canUpdateCommodity
              },
              on: {
                'on-enter': () => {
                  this.goodsList[params.index][key] = params.row[key]
                  // this.addBlankGoods(params.index)
                }
              },
              nativeOn: {
                change: $event => {
                  let value = $event.target.value
                  params.row[key] = value;
                  this.storeGoodsList[params.index][key] = value;
                }
              }
            });
          }
        },
        {
          title: '内部备注',
          key: 'inner_remark',
          align: 'center',
          minWidth: 90,
          editRender: {
            autofocus: 'input',
            autoselect: true,
            editAble: (row) => this.isRowEditable(row)
          },
          renderHeader: h => {
            return h('span', {
              class: 'InternalNote'
            }, '内部备注')
          },
          render: (h, params) => {
            const { row, index } = params
            let key = 'inner_remark';
            if (!this.isRowEditable(params.row)) {
              return h('span', params.row[key])
            }
            return h('i-input', {
              props: {
                value: row.inner_remark
              },
              class: {
                remarks: true
              },
              on: {
                'on-enter': () => {
                  this.goodsList[index][key] = row[key]
                }
              },
              nativeOn: {
                change: $event => {
                  let value = $event.target.value
                  row.inner_remark = value
                  this.storeGoodsList[index].inner_remark = value
                }
              }
            })
          }
        },
        {
          title: '分拣状态',
          key: 'is_sorting',
          render: (h, { row }) => {
            const status = Number(row.is_sorting)
            const statusMap = {
              0: '未分拣',
              1: '已分拣',
            };
            return h('span', statusMap[status]);
          }
        }
      ];
      const customFieldKeys = await this.setCustomizeFieldKeys();
      columns = columns.concat(customFieldKeys);
      this.columns = this.deepClone(columns);
    },
    // 修改订购数量的时候同时修改订购金额
    // 公式为 订购金额=订购数量*订购单价
    handleChangeAmount(params, sync = true) {
      if (!Number.isNaN(params.row.amount * params.row.unit_price)) {
        params.row.place_price = (
          params.row.amount * params.row.unit_price
        ).toFixed(2);
      } else {
        params.row.place_price = this.remoteGoodsList[params.index].amount;
      }
      if (sync) {
        this.syncChangeToStoreGoodsList(params);
      }
    },
    syncChangeToStoreGoodsList(params) {
      this.$set(this.storeGoodsList, params.index, this.deepClone(params.row));
    },
    handleChangeExtraAmount(params) {
      this.computeSellAmount(params.row);
      // this.computeSellTotalPrice(params.row);
      this.computeSellPrice(params.row);
      this.syncChangeToStoreGoodsList(params);
    },
    handleChangeExtraPrice(params) {
      this.computeSellTotalPriceViaExtraPrice(params.row);
      this.computeSellPrice(params.row);
      this.syncChangeToStoreGoodsList(params);
    },
    handleChangeSellAmount(params) {
      const { row } = params
      row.sell_total_price = Number(row.sell_amount).mul(+row.sell_price);
      this.computeExtraAmount(params.row);
      // this.computeSellTotalPrice(params.row);
      row.extra_price = this.computeExtraPrice(params.row);
      this.syncChangeToStoreGoodsList(params);
    },
		// 设置notFixCompute参数防止循环调用
    handleChangesAgreementPrice(params, notFixCompute) {
      const { row } = params

			!notFixCompute && this.fixationValueChange('org_price', params)
      // row.sell_price = Number(row.org_price).mul(+row.discount).div(100).toFixed(2);
      this.handleChangeSellPrice(params)
    },
    handleChangesDiscount(params) {
      const { row } = params
			this.fixationValueChange('discount', params)
      // row.org_price = Number(row.sell_price).div(+row.discount).mul(100).toFixed(2);
      this.handleChangesAgreementPrice(params, true)
    },
    handleChangeSellPrice(params) {
      const { row } = params
      row.sell_total_price = Number(row.sell_amount).mul(+row.sell_price);
      // this.computeSellTotalPrice(params.row);
      row.extra_price = this.computeExtraPrice(params.row);
      // 计算协议市场价(销)
      // row.org_price = this.computeAgreedPrice(params.row);
      console.log('row.org_price', row.org_price);
      // this.syncChangeToStoreGoodsList(params);
    },
    handleChangeSellTotalPrice(params) {
      this.computeSellPrice(params.row);
      params.row.extra_price = this.computeExtraPrice(params.row);
      this.syncChangeToStoreGoodsList(params);
    },
    /**
     * 附加数量 = 销售数量 - 实际数量（如果是不同单位且未分拣，这个实际数量要乘以转换系数）
     * 根据销售数量计算附加数量
     */
    computeExtraAmount(row) {
      let actualAmount;
      if (row.unit !== row.unit_sell && row.is_sorting == 0) {
        actualAmount = (row.actual_amount * row.unit_num) || 0;
      } else {
        actualAmount = row.actual_amount || 0;
      }
      let sellAmount = row.sell_amount || 0;
      row.extra_amount = (sellAmount - actualAmount).toFixed(2);
      // if (row.extra_amount < 0) {
      //   row.extra_amount = 0;
      // }
      return row.extra_amount.toFixed(2);
    },
    /**
     * 当商品有发货数量时：销售数量=发货数量 + 附加数量
     * 当商品没有发货数量时：销售数量=订购数量 + 附加数量
     */
    computeSellAmount(row) {
      // 发货数量
      let show_actual_amount = isNaN(row.show_actual_amount) ? 0 : +row.show_actual_amount;
      // 附加数量
      let extra_amount = isNaN(row.extra_amount) ? 0 : +row.extra_amount;
      // 订购数量
      let amount = isNaN(row.amount) ? 0 : +row.amount;
      if (this.isGoodsDelivered(row)) {
        row.sell_amount = (show_actual_amount + extra_amount).toFixed(2)
      } else if (amount > 0) {
        if (row.unit !== row.unit_sell) {
          row.sell_amount = (amount * row.unit_num + extra_amount).toFixed(2)
        } else {
          row.sell_amount = (amount + extra_amount).toFixed(2)
        }
      } else {
        row.sell_amount = extra_amount;
      }
      return row.sell_amount;
    },
    /**
     * 销售单价 = 销售金额 / 销售数量
     * 计算销售单价
     */
    computeSellPrice(row, notCompute) {
      row.sell_price =
        row.sell_total_price && row.sell_amount
          ? (row.sell_total_price / row.sell_amount).toFixed(2)
          : 0;

			!notCompute &&  this.fixationValueChange('sell_price', { row });
      return row.sell_price;
    },
    // 协议市场价（销）=销售单价/折扣率（销）
    computeAgreedPrice(row) {
      console.log('computeAgreedPrice');
			if (row.discount == null) return;
      row.org_price = row.sell_price.div(row.discount).mul(100).toFixed(2)
      // 判断是否为NAN或者Infinity
      if (isNaN(row.org_price) || !isFinite(row.org_price)) {
        row.org_price = 0
      }
      return row.org_price.toFixed(2)
    },
    /**
     * 当商品有发货数量时：销售金额 = 发货金额 + 附加金额
     * 当商品没有发货数量时：销售金额 = 订购金额 + 附加金额
     */
    computeSellTotalPrice(row) {
      // 发货金额
      let show_actual_total_price = isNaN(row.show_actual_total_price) ? 0: +row.show_actual_total_price;
      // 附加金额
      let extra_price = +row.extra_price;
      // 订购金额
      let place_price = +row.place_price
      let sell_total_price = 0;
      if (this.isGoodsDelivered(row)) {
        sell_total_price = (show_actual_total_price + extra_price).toFixed(2);
      } else if (place_price > 0) {
        sell_total_price = (place_price + extra_price).toFixed(2);
      } else {
        sell_total_price = extra_price.toFixed(2);
      }
      return sell_total_price;
    },
    /**
     * 计算销售
     * 销售金额 = 实际金额 + 附加金额
     */
    computeSellTotalPriceViaExtraPrice(row) {
      let actualPrice = row.actual_total_price || 0;
      let extraPrice = row.extra_price || 0;
      // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
      const {is_open_issue_order_un_need_delivery} = this;
      let show_actual_total_price = isNaN(row.show_actual_total_price) ? 0: row.show_actual_total_price;
      if(is_open_issue_order_un_need_delivery && (!this.c_from_copy && !row.from_copy || this.isRelatedOrderImport)){
        // 开启后 销售金额 = 发货金额 + 附加金额
        actualPrice = show_actual_total_price || 0;
        if(row.is_sorting === '0'){
          // 未分拣场景，销售金额 = 订购金额 + 附加金额
          actualPrice = row.actual_total_price || 0;
        }
      }
      row.sell_total_price = (actualPrice * 1 + extraPrice * 1).toFixed(2);
      return row.sell_total_price;
    },
    /**
     * 附加金额 = 销售金额 - 实际金额
     * 计算销售金额
     */
    computeExtraPrice(row) {
      let actualPrice = row.actual_total_price || 0;
      let sellTotalPrice = row.sell_total_price || 0;
      let extra_price = (sellTotalPrice - actualPrice).toFixed(2);
      // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
      const {is_open_issue_order_un_need_delivery} = this;
      // if(is_open_issue_order_un_need_delivery && !this.c_from_copy && !row.from_copy){
      //   // 开启后 附加金额 = 附加金额
      //   extra_price = row.extra_price;
      // }
      // if (row.extra_price < 0) {
      //   row.extra_price = 0;
      // }
      return Number(extra_price);
    },
    showCopyOrder() {
      if (!this.user.id) {
        this.modalError('请先选择客户', 0);
        return false;
      }
      this.copyOrder.show = true;
    },
    closeCopyOrder() {
      this.copyOrder.show = false;
      this.copyOrderType = 'copy';
    },
    handleCopyOrder() {
      let params = {
        order_id: this.$route.query.copy_id
      };
      this.$request.get(this.apiUrl.getOrderDetail, params).then(res => {
        let { status, data } = res;
        if (status) {
          this.copyOrderGoods(data);
        }
      });
    },
    copyOrderGoods(data) {
      if (data) {
        data.commodityList.forEach(item => {
          item.commodity_id = item.id;
          item.actual_amount = item.amount;
          item.amount = item.order_amount;
          item.place_price = item.original_total_price;
          item.actual_total_price = item.sub_total_price;
          item.order_commodity_id = item.orderCommodity_id;
          item.status = STATUS_NORMAL;
          item.from_copy = true;
          item.produce_date = item.production_date || '';
          if (item.actual_amount == 0 && item.unit !== item.unit_sell && item.is_sorting == 0) {
            item.org_price = Number((Number(item.org_price) || Number(item.protocol_org_price) || item.sales_unit_price || 0)/item.unit_num).toFixed(2);
          } else {
            item.org_price = Number(item.org_price) || Number(item.protocol_org_price) || item.sales_unit_price || 0;
          }
					item.discount = Number(item.discount) || Number(item.protocol_discount) || 100;
          item.id = createOrderGoodsId();
        });
        this.currentIndex = 0;

        this.initOrderGoods(data.commodityList);
        this.initOrderInfo(data.order, 1);
        if(this.configList.issue_create_time==2){
          this.formData.create_time = ''
        }
        this.formData.id = '';
        this.formData.order_id = '';
        this.isRelatedOrderImport = this.copyOrderType === 'import'
        this.closeCopyOrder();
      }
    },
    resetUser() {
      this.$refs.userInput.setQuery('');
      this.formData.user_id = '';
      this.user = {
        id: '',
        receive_name: '',
        address_detail: '',
        receive_tel: ''
      };
    },
    onChangeUser(user) {
      if (user) {
        this.user = {
          id: user.id,
          receive_name: user.name,
          address_detail: user.address_detail,
          receive_tel: user.tel
        };
        // this.initFormData();
        this.formData.relate_order_no = '';
        this.formData.order_no = '';
        this.formData.disabled = false;
        this.isOpenOrderTag && this._getTagList(user.id);
        const { fullData } = this.$refs.editableTable.getTableData();
        if (fullData.length === 0) {
          // 延迟是为了避免回车选择的时候触发商品名称的回车事件
          setTimeout(() => {
            this.$refs.editableTable.insertOneAt()
            // this.focusInput(this.goodsList[0], 'name')
          }, 100)
        }
        this.copyOrder.show = false
      } else {
        this.resetUser();
      }
    },
    onChangeDate(date) {
      this.formData.delivery_date = date;
    },
    onRowDrag({ oldIndex, newIndex }) {
      let { fullData } = this.$refs.editableTable.getTableData();
      fullData = cloneDeep(fullData);
      const currRow = fullData.splice(oldIndex, 1)[0];
      fullData.splice(newIndex, 0, currRow);
      this.goodsList = fullData;
      this.syncGoodsList()
    },
    syncGoodsList() {
      let goodsList = this.deepClone(this.goodsList);
      goodsList = goodsList.map(item => {
        let newItem = item;
        let storeItem = this.storeGoodsList.find(
          storeItem => storeItem[ITEM_KEY] === item[ITEM_KEY]
        );
        if (storeItem) {
          newItem = storeItem;
        }
        return newItem;
      });
      this.goodsList = goodsList;
      this.storeGoodsList = this.cloneObj(goodsList);
    },
    focusUserInput() {
      this.$nextTick(() => {
        const input = this.$refs.userInput.$el.querySelector(
          'input[type="text"]'
        );
        input && input.focus();
      });
    },
    focusInput(row, columnKey) {
      const tableHead = document.querySelector('.s-vxe-editable-table .vxe-table--header .vxe-header--row')
      if (!tableHead) return
      let currentCols = []
      tableHead.querySelectorAll('th .vxe-cell--title').forEach(th => {
        th.innerText && currentCols.push(th.innerText)
      })
      if (!columnKey) {
        const isFirstEditableCol = (col) => {
          return col.editRender && col.key !== 'name' && this.isRowEditable(row, col.key)
        }
        let firstEditableCol
        if (currentCols.length) {
          currentCols.find(title => {
            let col = this.columns.find(item => item.title === title)
            if (col && isFirstEditableCol(col)) firstEditableCol =  col
            return col && isFirstEditableCol(col)
          })
        } else {
          firstEditableCol = this.columns.find(col => {
            return isFirstEditableCol(col)
          })
        }
        console.log('firstEditableCol', firstEditableCol)
        if (firstEditableCol) {
          columnKey = firstEditableCol.key
        }
      }
      this.$nextTick(() => {
        this.$refs.editableTable.setEditCell(row, columnKey);
      })
    },
    addGoods(goods, spliceNum = 0, needFocus = false) {
      // 开启单据无需发货模式下，商品数量必须输入
      // const {is_open_issue_order_un_need_delivery, issue_order_data_contact_way} = this;
      // if((is_open_issue_order_un_need_delivery || issue_order_data_contact_way) && (this.addGoodsInfo.extra_amount === '' || this.addGoodsInfo.extra_amount === 0)){
      //   this.errorMessage('添加商品必须输入数量');
      //   return false;
      // }
      goods = this.adapterGoods(goods);

      let extraPrice = '';
      let unitPrice = Number(goods.price);
      if (goods.unit_convert === 'Y') {
        let base_price = unitPrice / goods.unit_num
        goods.extra_amount = goods.extra_amount * goods.unit_num;
        goods.amount = goods.extra_amount / goods.unit_num;
        goods.unit_price = 
        unitPrice = Number(base_price);
      }
      extraPrice = Number(goods.extra_amount) * unitPrice;
      if (!this.isGoodsExist(goods) || this.is_open_order_add_same_commodity) {
        let formatGoods = {
          ...goods
        };
        formatGoods.extra_price =
          formatGoods.extra_price || extraPrice.toFixed(2) || 0;
        formatGoods.extra_amount = formatGoods.extra_amount || 0;

        formatGoods.actual_amount = 0;
        formatGoods.actual_total_price = 0;

        this.computeSellTotalPriceViaExtraPrice(formatGoods);
        this.computeSellAmount(formatGoods);
        this.computeSellPrice(formatGoods);
        this.computeAgreedPrice(formatGoods);
        // 不知道是否有问题，应该没问题，改动原因：之前开发会调用上面方法联动计算了协议市场价（销），但是新加的商品不需要计算的，直接从接口拿就行，我不理解！
        formatGoods.org_price = goods.org_price;

        console.log(formatGoods);
        if (this.isRelatedOrderImport) {
          // 从订单导入的情况下，新增商品时:
          formatGoods.actual_amount = +formatGoods.show_actual_amount // 实际数量
          formatGoods.actual_total_price = +formatGoods.show_actual_total_price // 实际金额
          // 初始化销售单价为发货单价
          // 初始化销售金额为发货金额
          formatGoods.sell_price = formatGoods.show_actual_unit_price
          formatGoods.sell_total_price = formatGoods.show_actual_total_price
        }
        // 新增商品订购数量不允许编辑
        formatGoods.amountEditDisable = true;
        formatGoods.id = createOrderGoodsId();

        // 初始化下单单价: 销售单价 * 转换系数
        const unitNum = formatGoods.unit_convert === 'Y' ? goods.unit_num : 1;
        formatGoods.unit_price = (formatGoods.sell_price * unitNum).toFixed(2);
        formatGoods.amount = formatGoods.extra_amount / unitNum;
        formatGoods.place_price = (formatGoods.unit_price * formatGoods.amount).toFixed(2);

        const index = this.currentIndex + 1 - spliceNum
        this.goodsList.splice(index, 0, formatGoods);
        this.syncGoodsList();
        if (needFocus) this.focusInput(this.goodsList[index])
      } else {
        // 不显示删除商品时，若商品已经存在只是状态是已删除状态，这时候需要恢复商品
        const existGoods = this.goodsList.find(item => item.commodity_id === goods.commodity_id);
        if (
          !this.showDeleteCommodity &&
          this.isGoodsDelete(existGoods)
        ) {
          this.recoverGoods(existGoods);
          const { fullData } = this.$refs.editableTable.getTableData();
          this.$refs.editableTable.remove(fullData[this.currentIndex]);
          this.focusInput(existGoods)
          return
        }
        this.modalError(
          {
            content: '商品已经存在',
            onOk: () => {
              this.focusInput(this.goodsList[this.currentIndex])
            }
          },
          0
        );
      }
    },
    // 增加空的商品
    addBlankGoods(index = -1) {
      if (this.isEditPage && this.formData.can_add_commodity === 0) return
      this.goodsList.splice(index + 1, 0, {
        id: createOrderGoodsId(),
        commodity_id: '',
        name: '',
        extra_price: '',
        extra_amount: 1,
        sell_total_price: 0,
        sell_price: '',
      })
      this.syncGoodsList();
    },
    adapterGoods(goods){
      // 是否开启单据无需发货(开启后只能编辑发货数量、附加金额)
      const {is_open_issue_order_un_need_delivery} = this;
      if((!is_open_issue_order_un_need_delivery ||  this.c_from_copy || !this.isNewPage) && !this.isRelatedOrderImport){
        return goods;
      }else{
        // 开启后，添加的商品要增加一些字段的值
        let {unit_convert, extra_amount ,unit_num, price,unit, unit_sell} = goods;

        // 订购单位
        goods.unit = unit ;
        // 订购数量
        goods.amount = extra_amount;
        // // 订购单价
        goods.unit_price = price;
        // // 订购金额=订购数量*订购单价
        let place_price =  (extra_amount * price).toFixed(2);
        goods.place_price = place_price
        // 发货单位
        goods.sort_unit = unit_sell;
        // 发货数量
        // (转换单位开启时订购数量， 发货数量 =订购数量 * 转换系数)
        let show_actual_amount = unit_convert === 'Y' ? Number(goods.amount * unit_num) : goods.amount;
        goods.show_actual_amount = show_actual_amount;
        // 发货金额 = 订购金额
        goods.show_actual_total_price = place_price
         // 发货单价(发货单价 = 发货金额/发货数量)

         // 发货单价
         let show_actual_unit_price = 0;
         if(unit_convert === 'Y'){
           // 开启单位转换时，发货单价= 订购单价 / 转换系数
           show_actual_unit_price = price / unit_num;
         }else{
           // 没有开启时，发货单价 = 订购单价
           show_actual_unit_price = Number(price);
         }
        goods.show_actual_unit_price = show_actual_unit_price.toFixed(2);

        // 置空附加数量
        goods.extra_amount = 0

        // 记录是开启单据无需发货后的新增记录(存在新增记录是不能从历史单据复制)
        goods.from_is_open_issue_order = true;

        return goods;
      }
    },
    getItemIndex(item) {
      return this.goodsList.findIndex(
        findItem => findItem[ITEM_KEY] === item[ITEM_KEY]
      );
    },
    isOriginalOrderGoods(goods) {
      return goods.order_commodity_id && Number(goods.order_commodity_id);
    },
    isGoodsDelivered(goods) {
      return Number(goods.is_sorting) > 0
    },
    isGoodsDelete(goods) {
      return Number(goods.status) === STATUS_DELETE;
    },
    deleteGoods(goods, index) {
      if (!goods.commodity_id) {
        this.$refs.editableTable.remove(goods)
        // this.goodsList.splice(index, 1)
        // this.syncGoodsList();
        return
      }
      goods.status = STATUS_DELETE;
      this.storeGoodsList[this.getItemIndex(goods)].status = STATUS_DELETE;
      this.deleteCount += 1
    },
    recoverGoods(goods) {
        console.log(this.getItemIndex(goods), this.storeGoodsList[this.getItemIndex(goods)], '88')
        goods.status = STATUS_NORMAL;
        this.storeGoodsList[this.getItemIndex(goods)].status = STATUS_NORMAL;
        this.deleteCount -= 1
    },
    $_onSelectGoods(commodity_id, index) {
      if (!commodity_id) {
        this.errorMessage('请输入正确的商品')
        return
      }
      let params = {
        commodity_id,
        user_id: this.formData.user_id,
        delivery_date: this.formData.delivery_date
      };
      this.$request
        .get(this.apiUrl.getBehalfOrderCommodity, params)
        .then(res => {
          let { status, data } = res;
          if (status) {
            data.commodity_id = data.id;
            data.extra_price = this.addGoodsInfo.extra_price;
            data.extra_amount = this.addGoodsInfo.extra_amount;
            data.remark = '';
            data.inner_remark = '';
            data.id = undefined;
            data.status = STATUS_NORMAL;
            data._salesUnitNumshowUnit = true
            data.isAlter = false;
            data.is_sorting = 1;
            data.show_actual_amount = 0;
            this.addGoodsInfo = data;
            this.currentIndex = index
            // 替换商品信息
            this.addGoods(this.addGoodsInfo, 1, true);
            this.initAddGoodsInfo()
          }
        });
    },
    initAddGoodsInfo() {
      this.addGoodsInfo = {
        commodity_id: '',
        extra_price: '',
        extra_amount: 1,
        is_sorting: 1,
        isAlter: false
      };
    },
    // 批量添加商品
    selectGoods(goodsList) {
      goodsList && goodsList.forEach((item, index, arr) => {
        item.commodity_id = item.id;
        item.id = undefined;
        item.amount = '';
        item.extra_amount = item.extra_amount || 1;
				item.discount = item.discount || 100;
        // item.extra_price = item.price || 1;
        item._salesUnitNumshowUnit = true;
        item.status = STATUS_NORMAL
        item.is_sorting = 1;
        item.isAlter = false;
        if (index === 0 && this.goodsList[this.currentIndex] && !this.goodsList[this.currentIndex].commodity_id) {
          this.addGoods(item, 1);
        } else {
          this.addGoods(item);
          this.currentIndex++
        }
      });
      this.showGoodsListModal = false;
    },
    isGoodsExist(goods) {
      return this.goodsList.some(item => item.commodity_id === goods.commodity_id);
    },
    initOrderGoods(goodsList) {
      goodsList.forEach(item => {
        const org = item.org_price;
        // 销售数量(原) 编辑时不显示单位，用这个字段控制
        item._salesUnitNumshowUnit = true;
        item.extra_price = item.changes_price || 0;
        item.extra_amount = item.changes_num || 0;
        // 未发货取下单数据
        item.show_actual_amount = item.actual_amount;
        item.show_actual_unit_price = item.actual_unit_price;
        item.show_actual_total_price = item.actual_total_price;
        if (!this.isGoodsDelivered(item)) {
          item.actual_amount = item.amount || 0;
          item.actual_total_price = item.place_price || 0;
        }
        if (this.isGoodsDelete(item)) this.deleteCount++
        if (isNaN(item.actual_amount)) {
          item.actual_amount = 0;
        }
        if (isNaN(item.actual_total_price)) {
          item.actual_total_price = 0;
        }
        this.computeSellTotalPriceViaExtraPrice(item);
        this.computeSellAmount(item);
        // if (item.show_actual_amount == 0 && (this.isAddPage || this.$route.query.is_copy) && item.unit !== item.unit_sell && item.is_sorting == 0) {
        //   item.sell_price = item.sales_unit_price / item.unit_num;
        // } else {
        //   item.sell_price = item.sales_unit_price;
        // }
        item.sell_price = item.sales_unit_price;
        // 坑逼啊，不想再改了，静待有缘人改单据管理的联动计算。这里是上面调的computeSellPrice里面会计算协议市场价（销），导致精度不对，所以在开始用变量org存了接口返回的，它算任它算，我反正最后再赋值回去
        item.org_price = org;
        item.isAlter = false;
      });
      this.storeGoodsList = []
      this.goodsList = goodsList
      this.remoteGoodsList = this.deepClone(goodsList);
      this.syncGoodsList();
    },
    // 获取订单自定义字段
    async getOrderCustomizeField () {
      const { status, message, data } = await this.$request.get(this.apiUrl.customizeFieldKeys, {
        customize_type: '3'
      })
      if (status) {
        this.orderCustomizeField = data
        console.log('getOrderCustomizeField-empty', this.orderCustomizeField)
      } else {
        this.errorMessage(message)
      }
    },
    initOrderInfo(data, res) {
      console.log('AAA');
      return new Promise(resolve => {
        // 关闭时不能更改状态
        if (this.formData.mode == 700) {
          data.mode = 700;
        } else {
          data.mode = Number(data.mode);
        }
        if (res == 1) {
          if (data.mode_text == '待发货' || data.mode_text == '待审核') {
            this.CheckStateData = 1;
            this.SettlementStatusData = 1;
          } else {
            this.CheckStateData = Number(data.bill_status);
            this.SettlementStatusData = Number(data.settle_status);
          }
          data.disabled = true;
          data.relate_order_no = data.order_no;
          data.copy = 1;
        } else {
          // console.log('data',data);
          if (data.relate_order_no !== '' && data.relate_order_no !== undefined) {
            data.disabled = true;
          }
          this.CheckStateData = Number(data.bill_status);
          this.SettlementStatusData = Number(data.settle_status);
        }
        data.source = Number(data.source);
        // 订单来源是授权同步订单, 重置为后台
        if (data.source === 20) {
          data.source = Number(this.orderSource[0].key)
        }
        data.copy_order_no = data.order_no;
        // 从订单列表新增单据
        if (this.isCopy) {
          data.email = data.user_name;
          this.user.receive_tel = data.receive_tel;
          this.user.receive_name = data.receive_name;
          this.user.address_detail = data.address_detail;
          this.user.id = data.user_id;
        }
        if (!this.$route.query.is_copy || this.isOpenOrderTag) this.selectedOrderTag = data.tag ? data.tag.split(',') : []
        // 复制单据
        if (this.$route.query.is_copy) {
          data.id = ''
          data.order_no = ''
          data.disabled = false
          this.formData = data;
          // this.$nextTick(() => {
          //   this.$refs.userInput.setQuery(data.email).then(() => {
          //     document.body.click();
          //   });
          // })
        } else {
          this.formData = data;
        }
        this.orderCustomizeField = data.customize_fields
        console.log('getOrderCustomizeField--value', this.orderCustomizeField)
        resolve()
      })
    },
    initList() {
      this.goodsList = []
      // this.addBlankGoods()
    },
    async getDetail(id) {
      const is_copy = this.$route.query.is_copy // 是否是复制的单据
      let params = {
        id,
        is_copy: is_copy ? 1 : 0
      };
      this.loading = true
      let res = await this.$request.get(this.apiUrl.billsOrder.detail, params)
      let { status, data } = res;
      if (status) {
        this.user = {
          id: data.issue_order.user_id,
          receive_name: data.issue_order.receive_name,
          receive_tel: data.issue_order.receive_tel,
          address_detail: data.issue_order.address_detail
        };
        this.isOpenOrderTag && this._getTagList(this.user.id)
        await this.initOrderInfo(data.issue_order);
        const goodsList = data.issue_order_commodity;
        if (is_copy) {
          goodsList.forEach(item => {
            item.id = createOrderGoodsId();
          });
        }
        this.initOrderGoods(goodsList);
        if (goodsList.length) this.currentIndex = goodsList.length - 1
      }
      this.loading = false
    },
    getSaveData() {
      let data = {
        id: this.formData.id,
        user_id: this.user.id,
        delivery_date: this.formData.delivery_date,
        remark: this.formData.remark,
        delivery_time_id: this.formData.delivery_time_id,
        mode: this.formData.mode,
        is_pay: this.formData.is_pay,
        source: this.formData.source,
        receive_name: this.user.receive_name,
        receive_tel: this.user.receive_tel,
        address_detail: this.user.address_detail,
        bill_status: this.CheckStateData,
        settle_status: this.SettlementStatusData,
        tag: this.selectedOrderTag.join(','),
        relate_order_import: this.isRelatedOrderImport ? 1 : 0,
        create_time: this.formData.create_time ? moment(this.formData.create_time).format('YYYY-MM-DD HH:mm:ss') : '',
        is_receipt: this.formData.is_receipt,
        customize_fields: JSON.stringify(this.orderCustomizeField),
      };
      if (this.formData.copy == 1) {
        console.log(this.formData.relate_order_no);
        data.relate_order_no = this.formData.relate_order_no;
        data.order_no = this.formData.order_no;
      } else {
        data.order_no = this.formData.order_no;
        data.relate_order_no = '';
      }
      // if (!data.id) {
      //   data.order_no = this.formData.copy_order_no;
      // }
      let goodsList = this.storeGoodsList.filter(goods => goods.commodity_id)
      console.log('goodsList', goodsList)
      goodsList = goodsList.map((item, index) => {
        let data = {
          id: typeof item.id === 'string' && item.id.startsWith('goods_') ? '' : item.id,
          sort_num: index,
          commodity_id: item.commodity_id,
          origin_order_commodity_id: item.orderCommodity_id || '',
          changes_price: item.extra_price,
          changes_num: item.extra_amount,
          remark: item.remark,
          inner_remark: item.inner_remark,
          status: item.status,
          discount: item.discount,
          org_price: item.org_price,
          sales_unit_price: item.sell_price,
          // 暂且打个补丁, 等后台bug转需求去调整
          produce_date: this.isEnableBatch ? '' : item.produce_date,
          tax_rate: item.tax_rate,
          amount: item.amount,
          unit_price: item.unit_price,
        };
        data.customize_fields = this.goodsCustomizeField.map((field) => {
          return {
            key: field.key,
            name: field.name,
            value: item[field.key],
          };
        });
        const {is_open_issue_order_un_need_delivery} = this;
        // 只有在编辑模式才判断是否添加 amount
        if (
          this.isRelatedOrderImport || // 从订单导入的模式下必须传amount
          (this.remoteGoodsList[index] &&
          item.amount !== this.remoteGoodsList[index].amount)
        ) {
          data.amount = item.amount;
        }else if(is_open_issue_order_un_need_delivery){
          // 开启单据无需发货时也需要添加amount
          data.amount = item.amount;
        }

        // 如果是原单商品数量没改过则不传数量和单价过去
        if (this.isOriginalOrderCommodity(item)) {
          if (!item.is_amount_change) {
            delete data.amount;
          }
          delete data.unit_price;
        }
        return data;
      });
      data.commodity = JSON.stringify(goodsList);
      return data;
    },
    checkData() {
      const filterStoreGoodsList = this.storeGoodsList.filter(goods => goods.commodity_id)
      if (filterStoreGoodsList.length === 0) {
        this.errorMessage('请添加商品和数量')
        return false
      }
      return true
    },
    save(goBack = true) {
      if (!this.storeGoodsList || !this.checkData()) {
        return false;
      }
      let saveData = this.getSaveData();
      if (this.saving || this.continueSaving) {
        return false;
      }
      this.continueSaving = !goBack;
      this.saving = goBack;
      this.$request.post(this.apiUrl.billsOrder.save, saveData).then(
        res => {
          this.saving = false;
          this.continueSaving = false;
          let { status, message } = res;
          if (status) {
            this.successMessage(message || '保存成功')
            if (goBack) {
              this.back();
            } else {
              this.reset();
            }
          } else {
            this.modalError(message || '保存失败！', 0);
          }
        },
        () => {
          this.saving = false;
          this.continueSaving = false;
        }
      );
    },
    back() {
      this.$router.go(-1)
    },
    $_onChange(key, params){
      // 判断数据联动模式(false固定销售金额(默认)、true固定销售单价)
      const {row, index} = params;
      const {unit_convert} = row;
      if(this.issue_order_data_contact_way){
        // 固定销售单价
        if(key === 'show_actual_amount'){
          // 发货数量变动

           // 1.订购数量
          let amount = 0;
          if(unit_convert === 'Y'){
            amount = row.show_actual_amount / row.unit_num;
          }else{
            amount = row.show_actual_amount
          }
          row.amount = amount.toFixed(2);
          // 2.订购金额
          row.place_price  = (row.unit_price * row.amount).toFixed(2);
          // 3.发货金额(发货金额 = 发货单价 * 发货数量)
          row.show_actual_total_price = row.show_actual_unit_price * row.show_actual_amount;
          // 4.销售数量(销售数量=附加数量+发货数量)
          row.sell_amount = Number(row.extra_amount) + Number(row.show_actual_amount)
          // 5.销售金额(销售金额=销售数量*销售单价)
          row.sell_total_price = row.show_actual_total_price + row.extra_price;
        }else if(key === 'extra_price'){
          // 附加金额变动

          // 1.销售金额(销售金额=发货金额+附加金额)
          row.sell_total_price = (row.show_actual_total_price + row.extra_price).toFixed(2);
          // 2.销售数量(销售单价 = 销售金额/销售数量)
          row.sell_amount = row.sell_price === 0 ? 0: Number(row.sell_total_price / row.sell_price).toFixed(2)
          // 3.附加数量(销售数量 = 附加数量 + 发货数量)
          if (row.unit !== row.unit_sell && row.is_sorting == 0) {
            row.extra_amount = (row.sell_amount - row.show_actual_amount * row.unit_num).toFixed(2)
          } else {
            row.extra_amount = (row.sell_amount - row.show_actual_amount).toFixed(2)
          }
        }
      }else{
        // 固定销售金额
        if(key === 'show_actual_amount'){
        // 发货数量变动

          // 1.订购数量
          let amount = 0;
          if(unit_convert === 'Y'){
            amount = row.show_actual_amount / row.unit_num;
          }else{
            amount = row.show_actual_amount
          }
          row.amount = amount.toFixed(2);
          // 2.订购单价(订购金额= 订购数量 * 订购单价)
          row.unit_price = (row.place_price / row.amount).toFixed(2);
          // 3.发货单价(发货金额 = 发货单价 * 发货数量)
          row.show_actual_unit_price = (row.show_actual_total_price / row.show_actual_amount).toFixed(2);
          // 4.销售数量(销售数量=附加数量+发货数量)
          row.sell_amount = row.extra_amount + row.show_actual_amount;
          // 5.销售单价(销售单价 = 销售金额/销售数量)
          row.sell_price = Number(row.sell_total_price / row.sell_amount).toFixed(2);

        }else if(key === 'extra_price'){
          // 附加金额变动、销售金额固定；反算发货金额、发货数量、订购数量、订购金额

          // 1.销售金额(销售金额=发货金额+附加金额)
          row.sell_total_price = (row.show_actual_total_price + row.extra_price).toFixed(2);
          // 2.销售单价(销售金额=销售数量*销售单价)
          row.sell_price = (row.sell_total_price / row.sell_amount).toFixed(2);

        }
      }
      this.storeGoodsList.splice(index,1,row);
      // this.syncGoodsList();
    },
		computeDiscount(row) {
			let discount = 0;
			if (row.sell_price > 0) {
				row.discount = (row.sell_price / row.org_price * 100).toFixed(2);
			}
			// 判断是不是NaN或者Infinity, 是的话返回0
			if (isNaN(row.discount) || !isFinite(row.discount)) {
				row.discount = 0;
			}
			return row.discount;
		},
		// 固定三种值情况下的联动计算
		fixationValueChange(key, params) {
			const { row } = params || {}
			console.log(key, this.protocolPriceDiscountFixMode, '模式', row);
			switch (+this.protocolPriceDiscountFixMode) {
				case 1:
					if (key === 'sell_price') {
						row.org_price = this.computeAgreedPrice(row)
					} else if (key === 'discount') {
						// 根据折扣率和市场价计算销售单价
						row.sell_price = Number(row.org_price).mul(+row.discount).div(100).toFixed(2);
					} else if (key === 'org_price') {
						row.sell_price = Number(row.org_price).mul(+row.discount).div(100).toFixed(2);
					}
					break;
				case 2:
					if (key === 'sell_price') {
						// 根据单价和市场价计算折扣率
						row.discount = this.computeDiscount(row)
					} else if (key === 'discount') {
						row.sell_price = Number(row.org_price).mul(+row.discount).div(100).toFixed(2);
					} else if (key === 'org_price') {
						row.sell_price = Number(row.org_price).mul(+row.discount).div(100).toFixed(2);
					}
					break;
				case 3:
					if (key === 'sell_price') {
						row.org_price = this.computeAgreedPrice(row)
					} else if (key === 'discount') {
						row.org_price = this.computeAgreedPrice(row)
					} else if (key === 'org_price') {
						row.discount = this.computeDiscount(row)
					}
					break;
			}
      const index = this.goodsList.findIndex(item => item[ITEM_KEY] === row[ITEM_KEY]);
			this.storeGoodsList.splice(index,1,row);
			// this.syncGoodsList();
		},
    $_onModeChange(key,params){
      // 这里都是处理销售单价固定的场景，与原有处理固定销售金额的场景区分开
      const {row, index} = params;
      let show_actual_total_price = isNaN(row.show_actual_total_price) ? 0: row.show_actual_total_price;
      let show_actual_amount = isNaN(row.show_actual_amount) ? 0: row.show_actual_amount;
      switch(key){
        // 订购数量
        case 'amount':
          // 1.订购金额改变(订货金额=订购数量*订购单价)
          row.place_price = (row.amount * row.unit_price).toFixed(2);

          // 2.销售数量改变(销售数量=附加数量+发货数量(订购数量*转换系数))
          if(
            +row.show_actual_amount > 0 ||
            !this.isOriginalOrderCommodity(row) // 纯代开商品
          ){
            // 当商品有发货数量时：销售数量=发货数量 + 附加数量
            row.sell_amount = (Number(row.show_actual_amount) + Number(row.extra_amount)).toFixed(2);
          }else{
            // 当商品没有发货数量时：销售数量=订购数量 + 附加数量
            row.sell_amount = (Number(row.amount) + Number(row.extra_amount)).toFixed(2);
            // 销售金额变动(销售金额=销售数量*销售单价)
            row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
          }

          break;
        // 附加数量变动
        case 'extra_amount':
          // 1.销售数量(销售数量=附加数量+发货数量)
         if(
          +row.show_actual_amount > 0 ||
          !this.isOriginalOrderCommodity(row) // 纯代开商品
        ){
           row.sell_amount = Number(row.extra_amount + Number(show_actual_amount || 0)).toFixed(2);
         }else{
            // 当商品没有发货数量时：销售数量=订购数量 + 附加数量
            const amount = isNaN(row.amount) ? 0 : row.amount;
           if (row.unit !== row.unit_sell && row.is_sorting == 0) {
             row.sell_amount = (Number((amount * row.unit_num) || 0) + Number(row.extra_amount)).toFixed(2);
           } else {
             row.sell_amount = (Number(amount || 0) + Number(row.extra_amount)).toFixed(2);
           }
         }
         // 2.销售金额(销售金额=销售数量*销售单价)
         row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);

         // 3.附加金额(销售金额=发货金额+附加金额)
         if(
          +row.show_actual_amount > 0 ||
          !this.isOriginalOrderCommodity(row) // 纯代开商品
        ){
           // 当商品有发货数量时:附加金额 = 销售金额 - 发货金额
           row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)
         }else{
           // 当商品没有发货数量时: 附加金额 = 销售金额 - 订购金额
           const place_price = isNaN(row.place_price) ? 0: row.place_price;
           row.extra_price = (row.sell_total_price - Number(place_price || 0)).toFixed(2);
         }
          break;
        // 附加金额变动
        case 'extra_price':
          // 1.销售金额变动(销售金额=发货金额+附加金额)
          if(
            +row.show_actual_amount > 0 ||
           !this.isOriginalOrderCommodity(row) // 纯代开商品
          ){
            // 当商品有发货数量时:销售金额=发货金额+附加金额
            row.sell_total_price =  (Number(show_actual_total_price || 0) + row.extra_price).toFixed(2);
            // 2.销售数量变动(销售金额=销售数量*销售单价)
            row.sell_amount = Number(row.sell_price) === 0 ? 0 : (row.sell_total_price/row.sell_price).toFixed(2);
            // 3.附加数量变动(销售数量=附加数量+发货数量)
            row.extra_amount = (row.sell_amount - (show_actual_amount || 0)).toFixed(2)
          }else{
            // 当商品没有发货数量时(销售金额= 订购金额  + 附加金额)
            const place_price = isNaN(row.place_price) ? 0: row.place_price;
            row.sell_total_price = (Number(place_price || 0) + Number(row.extra_price)).toFixed(2);
            // 2.销售数量变动(销售金额=销售数量*销售单价)
            row.sell_amount = Number(row.sell_price) === 0 ? 0 : (row.sell_total_price/row.sell_price).toFixed(2);
            // 3.附加数量变动(销售数量=订购数量 + 附加数量)
            const amount = isNaN(row.amount) ? 0 : row.amount;
            if (row.unit !== row.unit_sell && row.is_sorting == 0) {
              row.extra_amount = (row.sell_amount - Number(amount * row.unit_num || 0)).toFixed(2)
            } else {
              row.extra_amount = (row.sell_amount - Number(amount || 0)).toFixed(2)
            }
          }

          break;
        // 销售数量变动
        case 'sell_amount':
          if(
            +row.show_actual_amount > 0 ||
            !this.isOriginalOrderCommodity(row) // 纯代开商品
          ){
            // 1.附加数量变动(销售数量=附加数量+发货数量)
            row.extra_amount = (row.sell_amount - (show_actual_amount || 0)).toFixed(2)
            // 2.销售金额变动(销售金额=销售数量*销售单价)
            row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
            // 3.附加金额变动(销售金额=发货金额+附加金额)
            row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)
          }else{
            // 1.附加数量变动(销售数量 = 订购数量 + 附加数量)
            const amount = isNaN(row.amount) ? 0 : row.amount;
            if (row.unit !== row.unit_sell && row.is_sorting == 0) {
              row.extra_amount = (row.sell_amount - Number((amount * row.unit_num) || 0)).toFixed(2)
            } else {
              row.extra_amount = (row.sell_amount - Number(amount || 0)).toFixed(2)
            }
            // 2.销售金额变动(销售金额=销售数量*销售单价)
            row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
            // 3.附加金额变动 附加金额 = 销售金额 - 订购金额
            const place_price = isNaN(row.place_price) ? 0: row.place_price;
            row.extra_price = (row.sell_total_price - Number(place_price || 0)).toFixed(2);
          }
          break;
        // 销售数量(原)变动
        case 'sales_unit_num':
          // 1.销售数量变动(销售数量(原) * 转换系数)
          row.sell_amount = row.sales_unit_num;

          if (row.unit_convert === 'Y' && Number(row.unit_num) - 1 !== 0) {
            row.sell_amount = row.sales_unit_num * row.unit_num;
          }

          if(
            +row.show_actual_amount > 0 ||
            !this.isOriginalOrderCommodity(row) // 纯代开商品
          ){
            row.extra_amount = (row.sell_amount - (show_actual_amount || 0)).toFixed(2)
            // 3.销售金额变动(销售金额=销售数量*销售单价)
            row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
            // 4.附加金额(销售金额=发货金额+附加金额)
            row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)
          }else{
            // 2.附加数量变动(销售数量 = 订购数量 + 附加数量)
            const amount = isNaN(row.amount) ? 0 : row.amount;
            if (row.unit !== row.unit_sell && row.is_sorting == 0) {
              row.extra_amount = (row.sell_amount - Number((amount * row.unit_num) || 0)).toFixed(2)
            } else {
              row.extra_amount = (row.sell_amount - Number(amount || 0)).toFixed(2)
            }
             // 3.销售金额变动(销售金额=销售数量*销售单价)
            row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
            // 4.附加金额 = 销售金额 - 订购金额
            const place_price = isNaN(row.place_price) ? 0: row.place_price;
            row.extra_price = (row.sell_total_price - Number(place_price || 0)).toFixed(2);
          }

          break;
        // 销售单价变动
        case 'sell_price':
          // 数量固定算金额
          // 1.销售金额变动(销售金额=销售数量*销售单价)
          row.sell_total_price = (row.sell_amount * row.sell_price).toFixed(2);
          // 2.附加金额变动(销售金额=发货金额+附加金额)
          if (+row.is_sorting === 1) {
            row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)
          } else {
            row.extra_price = (row.sell_total_price - (+row.place_price || 0)).toFixed(2)
          }
					this.fixationValueChange(key, params)
          break;
        // 销售金额变动
        case 'sell_total_price':
          // 数量固定算金额
          // // 1.销售单价变动(销售金额=销售数量*销售单价)
          // row.sell_price = (row.sell_total_price / row.sell_amount).toFixed(2)
          // // 2.附加金额变动(销售金额=发货金额+附加金额)
          // row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)

          // 销售单价不变
          // 1.销售数量变动(销售金额=销售数量*销售单价)
          row.sell_amount = Number(row.sell_price) === 0 ? 0 : (row.sell_total_price/row.sell_price).toFixed(2);
          if(
            +row.show_actual_amount > 0 ||
            !this.isOriginalOrderCommodity(row) // 纯代开商品
          ){
            // 2.附加数量变动(销售数量=附加数量+发货数量)
            row.extra_amount = (row.sell_amount - (show_actual_amount || 0)).toFixed(2)
            // 3.附加金额变动(销售金额=发货金额+附加金额)
            row.extra_price = (row.sell_total_price - (show_actual_total_price || 0)).toFixed(2)
          }else{
            // 2.附加数量(销售数量 = 订购数量 + 附加数量)
            const amount = isNaN(row.amount) ? 0 : row.amount;
            if (row.unit !== row.unit_sell && row.is_sorting == 0) {
              row.extra_amount = (row.sell_amount - Number(amount * row.unit_num || 0)).toFixed(2)
            } else {
              row.extra_amount = (row.sell_amount - Number(amount || 0)).toFixed(2)
            }
            // 4.附加金额 = 销售金额 - 订购金额
            const place_price = isNaN(row.place_price) ? 0: row.place_price;
            row.extra_price = (row.sell_total_price - Number(place_price || 0)).toFixed(2);
          }
          break;
        default:
          break;
      }
      this.storeGoodsList.splice(index,1,row);
      // this.syncGoodsList();
    },
  }
};
</script>

<style lang="less">
.bills-finance-form {
  .ivu-form-item {
    margin-bottom: 10px;
  }
}
</style>
<style lang="less" scoped>
/deep/ .row-delete {
  display: none;
}
.newOrder-remarks {
  border: none;
}
.content-wrap /deep/ .ivu-select-item {
  width: 315px;
  white-space: normal;
}
.bills-finance-form {
  text-align: left;
  padding-bottom: 50px;
  .add-goods {
    position: relative;
    width: 300px;
    display: inline-block;
    .add-icon {
      position: absolute;
      top: 0;
      right: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .card-content {
    padding: 15px 10px 0;
  }
}
.mt15 {
  margin-top: 15px;
}
.fixedBtns {
  text-align: left;
  z-index: 3;
}
/deep/ .not-allowed {
  color: #b2b2b2;
  cursor: not-allowed;
}
/deep/ .add-operation {
  line-height: 32px;
  color: var(--primary-color);
  span {
    cursor: pointer;
  }
  span:not(:last-child) {
    margin-right: 10px;
  }
  .ivu-tooltip {
    position: relative;
  }
}
/deep/ .notice-info {
  min-width: 820px;
  max-width: 1080px;
  padding-right: 10px;
  color: #b2b2b2;
  text-align: left;
  .tip_icon::before {
    position: relative;
    top: -1px;
    font-size: 16px;
    vertical-align: middle;
  }
}
/deep/ .extra-price-info {
  margin-left: 5px;
  white-space: nowrap;
}
/deep/ .tip-icon {
  cursor: pointer;
  margin-left: 4px;
  font-size: 14px;
  vertical-align: text-top;
}
/deep/ .InternalNote {
  color: #3399ff;
}
/deep/.deliveryTimeSelect {
	width: 210px !important;
	/deep/ .ivu-select-placeholder {
		padding-left: 8px;
	}
}
/deep/.commodity-select {
  width: 100%;
  .ivu-dropdown {
    width: 100%;
  }
}
.icon-lin {
  display: inline-block;
  width: 16px;
  height: 16px;
}
</style>
