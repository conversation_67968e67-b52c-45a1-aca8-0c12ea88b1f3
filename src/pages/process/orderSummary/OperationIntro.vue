<template>
  <StepIntro :stepInfo="stepInfo" ref="introRef">
    <slot></slot>
  </StepIntro>
</template>
<script>
import StepIntro from '@/components/step-intro'
export default {
  name: 'ProcessIntro',
  components: {
    StepIntro
  },
  data() {
    return {
      stepInfo: [
        {
          title: '准备工作',
          steps: [
            {
              title: '新增加工品',
              desc: '为加工品设置名称分类售价等信息',
              redirect: '/goods/list?is_process=1'
            },
            {
              title: '设置加工品BOM',
              desc: '加工品由普通商品或其他加工品组成',
              redirect: '/process/goodsBom/list'
            },
            {
              title: '新增生产线',
              desc: '加工流程可按不同生产线独立进行',
              redirect: '/process/productLine/list'
            },
            {
              title: '新增工人',
              desc: '记录加工过程关联的工人信息',
              redirect: '/process/worker/list'
            },
          ]
        },
        {
          title: '加工操作',
          steps: [
            {
              title: '销售汇总',
              desc: '按发货日期汇总加工品，可生成加工单、反算原料到订单汇总/采购任务模块',
              redirect: '/process/orderSummary'
            },
            {
              title: '审核加工单',
              desc: '审核后可开始加工流程',
              redirect: '/process/processOrder/list'
            },
            {
              title: '领料/入库/退料/标记完工',
              desc: '可在生产加工页面完成所有相关操作',
              redirect: '/process/processOrder/list'
            },
            {
              title: '查看加工报表',
              desc: '完工后可看报表',
              redirect: '/process/reports/yield'
            },
          ]
        }
      ]
    };
  },
}
</script>
