<!--
 * @Description: 客户-协议价
 * @Autor: lizi
 * @Date: 2021-11-10 10:12:03
 * @LastEditors: hgj
 * @LastEditTime: 2023-09-04 17:37:16
 * @FilePath: /sdpbase-pro/src/pages/agreementPrice/components/tabs/Price.vue
-->
<template>
  <div class="common agreementPriceList">
    <Alert
      type="warning"
      show-icon
      class="outOfDate"
      closable
      v-show="expireList && expireList.length > 0"
    >
      <div slot="desc">
        有客户协议单
        <template v-for="(expire, index) in expireList">
          <span
            class="orderNo"
            :key="index"
            @click="toOrderDetail(expire.id)"
            v-if="index == 0"
            >{{ expire.no }}</span
          >
        </template>
        <template v-for="(expire, index) in expireList">
          <span
            class="orderNo"
            :key="index"
            @click="toOrderDetail(expire.id)"
            v-if="index > 0"
            >，{{ expire.no }}</span
          >
        </template>

        即将过期，请尽快处理！
      </div>
    </Alert>

    <ListTable
      ref="listTable"
      tableId="agreementPriceList"
      :initParams="initParams"
      :auto-load-data="true"
      :debounceOptions="{ leading: true, trailing: false }"
      :border="false"
      :outer-border="true"
      :data-provider="apiUrl.getAgreementPriceList"
      :columns="columns"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :before-request="_beforeRequest"
      :after-request="_afterRequest"
      :height="getTableHeight() - 90"
      @on-sort-change="sortChange"
      :row-class-name="getRowClass"
      @on-selection-change="handleSelectionChange"
    >
      <template #button>
        <ExportButtonMulti :data="exportBtnMultiData" />
      </template>
      <div
        slot="before-table"
        v-show="selectedRows.length === 0"
        class="goods__operation"
        :style="{ 'margin-bottom': this.selectedRows.length > 0 ? '10px' : '' }"
      >
        <Button @click="add" styleType="btnStyleForAdd">新增</Button>

        <ImportButton
          class="ml10"
          title="导入协议单"
          offline
          :modalWidth="560"
          :download="{
            url: `${apiUrl.exportAgreementPriceTpl}?import_type=${importAgreementPrice.import_type}`,
            text: `协议单导入模板${importAgreementPrice.import_type === '1' ? ' - 折扣率' : isEnableUserContractPriceDiscountRatio ? ' - 协议价' : ''}`,
          }"
          :post="importPost"
          :data="{
            import_type: importAgreementPrice.import_type,
            import_mode: importAgreementPrice.import_mode,
          }"
          @on-completed="_onImportCompleted"
        >
          <template #custom-area>
            <div class="import-explain">
              <h6>使用说明：</h6>
              <p>
                1.选择导入模式（商品）为商品名称时，导入文件会校验商品名称字段为必填字段；导入模式为商品编码时，导入文件会校验商品编码字段为必填字段。
              </p>
              <p>2.商品别名模式仅支持一个客户导入新增，且仅支持导入新增。若导入的商品别名存在匹配多个系统商品，则默认把匹配到的商品均导入</p>
            </div>
            <div class="import-mode-box">
              <div class="label">导入模式(商品):</div>
              <div class="ml12">
                <RadioGroup v-model="importAgreementPrice.import_mode">
                  <Radio label="commodity_name">商品名称</Radio>
                  <Radio label="commodity_code">商品编码</Radio>
                  <Radio label="user_commodity_name">商品别名</Radio>
                  <Radio
                    v-if="!isAliasAllowRepeat"
                    label="user_alias_or_commodity_name"
                    >商品别名+商品名称</Radio
                  >
                  <Tooltip
                    v-if="!isAliasAllowRepeat"
                    :transfer="true"
                    :delay="0"
                    :maxWidth="246"
                    content="优先匹配别名商品，如果别名匹配不存在则匹配商品名称"
                    placement="top"
                  >
                    <SIcon
                      icon="help1"
                      style="margin-left: -8px; cursor: pointer"
                      :size="12"
                    />
                  </Tooltip>
                </RadioGroup>
              </div>
            </div>
            <div class="mt20" v-if="isEnableUserContractPriceDiscountRatio">
              <label class="w120 tr">录入方式：</label>
              <RadioGroup
                class="ml12"
                v-model="importAgreementPrice.import_type"
              >
                <Radio label="0">协议价</Radio>
                <Radio label="1">折扣率</Radio>
              </RadioGroup>
            </div>
          </template>
          <span>导入</span>
        </ImportButton>

        <Button
          class="ml10"
          @click="update"
          v-if="
            this.isEnableUserContractPriceFormula == true ||
            isOpenCommodityWholesaleMarketPrice
          "
          >{{ updates }}</Button
        >
      </div>
      <div class="flex-con" slot="batch-operation">
        <Button
          class="mr10"
          @click="update"
          v-if="
            this.isEnableUserContractPriceFormula == true ||
            isOpenCommodityWholesaleMarketPrice
          "
          >{{ updates }}</Button
        >
        <SyncAllBtn
          key="batch-audit"
          :allIdsObj="{
            ids: batchAuditIds,
          }"
          :syncObj="{
            url: apiUrl.checkAgreementPrice,
          }"
          idsKey="pp_id"
          :needTips="false"
          :groupSize="1"
          name="批量审核"
          typeText="批量审核"
          afterText="笔协议单进行批量审核"
          @done="() => _fetchData()"
        />
        <SyncAllBtn
          v-if="isEnableBatchDelete"
          :allIdsObj="{
            ids: batchCloseIds,
          }"
          :syncObj="{
            url: apiUrl.protocolBatchClose,
          }"
          key="batch-close"
          idsKey="pp_id"
          :needTips="false"
          :groupSize="1"
          name="批量关闭"
          typeText="批量关闭"
          afterText="笔协议单进行批量关闭"
          @done="() => _fetchData()"
        />
      </div>
    </ListTable>
    <ClientModal ref="clientModalRef" :curUserIds="curUserIds" />
  </div>
</template>

<script>
import ClientModal from '../clientModal.vue';
import Button from '@components/button';
import ListTable from '@components/list-table';
import user from '@api/user.js';
import agreementPrice from '@api/agreementPrice';
import goodsAutoCompleteSelect from '@components/common/goodsAutoCompleteSelect_new';
import { api } from '@api/api.js';
import { MINE_TYPE } from '@/util/const';
import ConfigMixin from '@/mixins/config';
import TableHeadSortIcon from '@components/common/tableHeadSortIcon';
import SelectAndInput from '@components/common/SelectAndInput';
import CheckboxGroup from '@components/CheckboxGroup';
import SyncAllBtn from '@/pages/order/components/syncAllBtn.vue';
import StorageUtil from '@util/storage.js';
import printMixin from '@/mixins/orderPrint';
import { getViewportHeight } from '@/util/common';
import SIcon from '@components/icon';
import Tooltip from '@components/base-tooltip';
import ExportButtonMulti from '@components/common/export-btn-multi';
import {
  userType,
  operator,
} from '@/components/standard/sdp-filter-items'
const DUFAULTSTATUS = [0, 1, 2, 3, 4];
const LOCALSTATUSKEY = 'agreement-price-list-checkbox-state';

export default {
  name: 'agreementPriceList-price',
  components: {
    SIcon,
    Tooltip,
    Button,
    ListTable,
    CheckboxGroup,
    ClientModal,
    SyncAllBtn,
    ExportButtonMulti,
  },
  mixins: [ConfigMixin, printMixin],
  data() {
    return {
      isEnableBatchDelete: false,
      curUserIds: '',
      selectedRows: [],
      exportBtnMultiData: Object.freeze([
        {
          text: '按商品导出',
          api: '/superAdmin/protocolPrice/exportProtocolCommodity',
          offline: true,
          paramGetter: () =>
            this._getExportParams({ export_type: 'commodity' }),
        },
        {
          text: '按客户导出',
          api: '/superAdmin/protocolPrice/exportProtocolCommodity',
          offline: true,
          paramGetter: () => this._getExportParams({ export_type: 'user' }),
        },
      ]),
      importAgreementPrice: {
        import_type: '0',
        import_mode: 'commodity_name',
      },
      filterItems: [
        {
          label: '选择日期',
          key: ['start_time', 'end_time'],
          type: 'DatePicker',
          props: {
            type: 'daterange',
            placeholder: '请选择日期',
          },
        },
        {
          label: '客户类型',
          key: 'rece_style',
          type: 'custom',
          component: userType,
        },
      ],
      advanceItems: [],
      columns: [
        {
          align: 'left',
          type: 'selection',
          width: 40,
          fixed: 'left',
        },
        {
          title: '单号',
          key: 'no',
          width: 200,
          fixed: 'left',
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h(
                'a',
                {
                  class: {
                    tableLink: true,
                    'tag-green': true,
                  },
                  on: {
                    click: () => {
                      this.toOrderDetail(data.id);
                    },
                  },
                },
                data.no,
              ),
              h(
                'div',
                {
                  style: {
                    color: '#999999',
                  },
                },
                data.create_time,
              ),
            ]);
          },
        },
        {
          title: '协议单ID',
          key: 'id',
        },
        {
          title: '商品种类数',
          key: 'commodity_species_num',
        },
        {
          title: '客户名称',
          key: 'user_relation_desc',
          render: (h, params) => {
            return h(
              'div',
              {
                class: ['ellipsis-2'],
                on: {
                  click: () => {
                    this.curUserIds = params.row.user_ids;
                    this.$refs.clientModalRef.show();
                  },
                },
              },
              params.row.user_relation_desc,
            );
          },
        },
        {
          title: '客户编码',
          key: 'user_code',
        },
        {
          title: '开始时间',
          key: 'start_time',
          width: 120,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#startTimeSortIcon').click();
                      },
                    },
                  },
                  '开始时间',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.start_time,
                    id: 'startTimeSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      this.sort.start_time = e;
                      this.sort.end_time = 0;
                      this._fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '终止时间',
          key: 'end_time',
          width: 120,
          renderHeader: (h) => {
            return h(
              'div',
              {
                style: { display: 'flex', alignItems: 'center' },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      cursor: 'pointer',
                    },
                    on: {
                      click: () => {
                        $('#endTimeSortIcon').click();
                      },
                    },
                  },
                  '终止时间',
                ),
                h(TableHeadSortIcon, {
                  props: {
                    sortRule: this.sort.end_time,
                    id: 'endTimeSortIcon',
                  },
                  on: {
                    onChange: (e) => {
                      this.sort.end_time = e;
                      this.sort.start_time = 0;
                      this._fetchData();
                    },
                  },
                }),
              ],
            );
          },
        },
        {
          title: '客户类型',
          key: 'rece_name',
        },
        {
          title: '制单人',
          key: 'creater',
        },
        {
          title: '状态',
          key: 'status',
          render: (h, params) => {
            let data = params.row;
            let status = parseInt(data.status);
            let text = this.statusMap[status] ? this.statusMap[status] : '未知';
            let color = '';
            switch (text) {
              case '已关闭': {
                color = '#999';
                break;
              }
              default:
                break;
            }
            return h(
              'span',
              {
                style: {
                  width: '100%',
                  color: color,
                },
              },
              text,
            );
          },
        },
        {
          title: '备注',
          key: 'remark',
        },
        {
          title: '操作',
          key: 'action',
          type: 'action',
          width: 230,
          actions: (params) => {
            const { index, row } = params;
            const { status, id } = row;
            let actions = [];
            // 状态显示
            let text = this.statusMap[status] ? this.statusMap[status] : '未知';
            text = text.trim();

            if (+status !== 0) {
              actions.push({
                name: '复制',
                action: () => {
                  this.router.push({
                    path: 'form',
                    query: { copeOrderId: id },
                  });
                },
              });
            }
            if (['未审核'].indexOf(text) !== -1) {
              actions.push({
                name: '审核',
                action: () => {
                  this.checkOrder(params);
                },
              });
            }

            if (['已失效', '已关闭'].indexOf(text) === -1) {
              actions.push({
                name: '编辑',
                action: () => {
                  this.router.push({
                    path: 'form',
                    query: { id },
                  });
                },
              });
            }

            actions.push({
              name: '导出',
              action: () => {
                this.exportOrder(id, index);
              },
            });
            actions.push({
              name: '打印',
              action: () => {
                this.printOrder(id, index);
              },
            });

            actions.push({
              name: '报价单导出',
              action: () => {
                this.exportOrderForPrice(id, index);
              },
            });

            actions.push({
              name: '协议单-下单模板导出',
              action: () => {
                this.exportOrderForTemplate(id, index);
              },
            });

            if (['已审核', '已生效'].indexOf(text) !== -1) {
              actions.push({
                name: '关闭',
                action: () => {
                  this.closeOrder(id, index);
                },
              });
            }

            if (+status === 0) {
              actions.push({
                name: '删除',
                action: () => {
                  this.deleteOrder(id, index);
                },
              });
            }

            return actions;
          },
        },
      ],
      statusMap: {},
      importPost: {
        url: '/superAdmin/ProtocolPrice/offlineImport',
        accept: MINE_TYPE.excel.join(','),
        format: ['csv', 'xls', 'xlsx'],
      },
      updates: '更新协议价',
      sort: {
        start_time: 0,
        end_time: 0,
      },
      expireList: null,
      expireNoArr: [],
      initParams: {
        status: StorageUtil.getLocalStorage(LOCALSTATUSKEY)
          ? StorageUtil.getLocalStorage(LOCALSTATUSKEY)
          : DUFAULTSTATUS,
      },
    };
  },
  computed: {
    batchAuditIds() {
      return this.selectedRows
        .filter(
          (item) => ['未审核'].indexOf(this.statusMap[item.status]) !== -1,
        )
        .map((item) => item.id);
    },
    batchCloseIds() {
      return this.selectedRows.map((item) => item.id);
    },
  },
  created() {
    this.initColumns();
    this.initAdvanceItems();
  },
  mounted() {
    this.getStatus();
  },
  activated() {
    this._fetchData();
  },
  methods: {
    getViewportHeight,
    initColumns() {
      if (this.isOpenNewProtocolPrice) {
        this.columns.forEach((item, idx) => {
          if (item.key === 'no') {
            this.columns.splice(idx + 1, 0, {
              title: '协议单名称',
              key: 'no_name',
              width: 100,
              render: (h, params) => {
                const { no_name } = params.row;
                let spanTem = h('span', no_name);
                if (!no_name) {
                  spanTem = h('span', '--');
                }
                let divTem = h('div', {}, [spanTem]);
                return divTem;
              },
            });
          }
        });
      }
    },
    initAdvanceItems() {
      this.advanceItems = [
        {
          items: [
            {
              type: 'custom',
              component: SelectAndInput,
              style: {
                width: '299px',
              },
              key: ['no_type', 'no_value'],
              defaultValue: ['1', ''],
              props: {
                inputPlaceholder: '输入客户名称/编码 /订单查询',
                selectDefaultValue: '1',
                selectData: [
                  {
                    label: '按客户信息',
                    placeholder: '输入客户名称/编码查询',
                    value: '1',
                  },
                  {
                    label: '按单号',
                    placeholder: '输入单号查询',
                    value: '2',
                  },
                  {
                    label: '按协议单ID',
                    placeholder: '输入协议单ID查询',
                    value: '3',
                  },
                ],
              },
            },
            {
              label: '商品',
              type: 'custom',
              key: 'commodity_id',
              component: goodsAutoCompleteSelect,
            },
            {
              label: '制单人',
              key: 'creater',
              type: 'custom',
              component: operator,
              attrs: {
                valueKey: 'user_name'
              }
            },
            {
              label: '协议单名称',
              type: 'Input',
              key: 'no_name',
              show: this.isOpenNewProtocolPrice,
              props: {
                placeholder: '输入协议单名称',
              },
            },
            {
              checked: true,
              required: true,
              label: '单据状态',
              type: 'custom',
              noReset: true,
              key: 'status',
              props: {
                data: [],
              },
              style: {
                width: '1500px',
              },
              defaultValue: StorageUtil.getLocalStorage(LOCALSTATUSKEY)
                ? StorageUtil.getLocalStorage(LOCALSTATUSKEY)
                : DUFAULTSTATUS,
              component: CheckboxGroup,
              onChange: (value) => {
                // 勾选状态存在本地
                StorageUtil.setLocalStorage(LOCALSTATUSKEY, value);
                // 当协议单状态筛选为【已审核、已生效】时，批量操作新增【批量关闭】
                this.isEnableBatchDelete =
                  value.length === 2 &&
                  [1, 2].every((val) => value.includes(val));
                return {
                  value,
                  stop: true,
                };
              },
            },
          ],
        },
      ];
    },
    async getStatus() {
      let res = await agreementPrice.getStatus();
      let statusList = [];
      if (res.status) {
        statusList = res.data;
      }
      statusList.map((status) => {
        this.statusMap[status.value] = status.desc;
      });
      this.advanceItems[0].items[4].props.data = statusList.map(
        ({ desc, value }) => ({ label: desc, value }),
      );

      if (StorageUtil.getLocalStorage(LOCALSTATUSKEY)) {
        let defaultStatus = StorageUtil.getLocalStorage(LOCALSTATUSKEY);
        if (
          defaultStatus.length === 2 &&
          defaultStatus.includes(1) &&
          defaultStatus.includes(2)
        ) {
          this.isEnableBatchDelete = true;
        } else {
          this.isEnableBatchDelete = false;
        }
      }
    },
    _beforeRequest(params) {
      if (params.status === '') {
        delete params.status;
      }
      if (Array.isArray(params.status)) {
        params.status = params.status.join(',');
      }
      let sort = '';
      let sortMap = ['', 'desc', 'asc'];
      if (+this.sort.start_time) {
        sort = 'start_time ' + sortMap[this.sort.start_time];
      } else if (+this.sort.end_time) {
        sort = 'end_time ' + sortMap[this.sort.end_time];
      }
      params.sort = sort;

      // 搜索框类型
      params.no_type === '1' && (params.unc_search = params.no_value);
      params.no_type === '2' && (params.order_no = params.no_value);
      if (params.no_type === '3') {
        params.id = params.no_value;
      }
      delete params.no_type;
      delete params.no_value;

      return params;
    },
    _afterRequest(res) {
      // 每查询一次都重新获取客户协议单
      this.getExpireList();
      if (!res.status) {
        this.errorNotice(res.message);
        return false;
      }
      // 状态为已关闭、已失效的数据不能勾选
      const { data } = res;
      data.list.map((item) => {
        item._disabled =
          +item.status === 4 || +item.status === 3 ? true : false;
      });
      return res;
    },
    _onImportCompleted(isSuccess) {
      isSuccess && this._fetchData(true);
    },
    _fetchData(resetPage) {
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage);
    },
    add() {
      this.router.push({ path: 'form' });
    },
    update() {
      this.updates = '更新中...';
      let params = this._getParams();
      this.$request.get(api.batchRefresh, params).then((res) => {
        if (res.status == 1) {
          this.$Notice.success({
            title: '操作成功！',
          });
        } else {
          this.$Notice.error({
            title: res.message,
          });
        }
        this.updates = '更新协议价';
        this._fetchData();
      });
    },
    _getExportParams(extraParams = {}) {
      const params = this._getParams();
      return {
        ...params,
        ...extraParams,
        status: Array.isArray(params.status)
          ? params.status.join(',')
          : params.status,
      };
    },
    _getParams() {
      const params = this.$refs.listTable && this.$refs.listTable.getParams();
      params.no_type === '1' && (params.unc_search = params.no_value);
      params.no_type === '2' && (params.order_no = params.no_value);
      delete params.no_type;
      delete params.no_value;
      // 判断是否勾选操作，如果勾选了只传勾选的id值
      if (this.selectedRows.length) {
        let ids = this.selectedRows.map((row) => row.id).join(',');
        params.id = ids;
      }

      return params;
    },
    sortChange(column, key, order) {
      // 排序格式 key + 空格 + 降序/升序
      this.sort = key + ' ' + order;
      this._fetchData(false);
    },
    deleteOrder(pp_id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>您是否确定删除该协议单？</p>',
        onOk: () => {
          agreementPrice.deleteAgreementPrice({ pp_id }).then((res) => {
            let { message, status } = res;
            if (status) {
              this.successNotice(message || '删除成功');
              this._fetchData(false);
            } else {
              this.errorNotice({
                title: '删除失败',
                desc: message,
              });
            }
          });
        },
      });
    },
    closeOrder(pp_id) {
      this.$Modal.confirm({
        title: '提示',
        content: '<p>您是否确定关闭该协议单？</p>',
        onOk: () => {
          agreementPrice.closeAgreementPrice({ pp_id }).then((res) => {
            let { message, status } = res;
            if (status) {
              this.successNotice(message || '关闭成功');
              this._fetchData(false);
            } else {
              this.errorNotice({
                title: '关闭失败',
                desc: message,
              });
            }
          });
        },
      });
    },
    exportOrderForPrice(pp_id) {
      window.location.href = `${api.exportMulti}?pp_id=${pp_id}`;
    },
    async exportOrderForTemplate(pp_id) {
      const { status, message, data } = await this.$request.get(
        this.apiUrl.exportAgreementPrice,
        {
          pp_id: pp_id,
          export_type: 1,
        },
      );
      if (status) {
        window.location.href = data;
      } else {
        this.errorMessage(message);
      }
    },
    printOrder(id) {
      // 打印
      this._printProtocolPrice({ id: id });
    },
    exportOrder(pp_id) {
      window.location.href = `${api.exportAgreementPrice}?pp_id=${pp_id}`;
    },
    checkOrder(params) {
      let { row } = params;
      let pp_id = row.id;
      this.$Modal.confirm({
        title: '提示',
        content: '<p>您是否确定审核该协议单？</p>',
        onOk: () => {
          agreementPrice.checkAgreementPrice({ pp_id }).then((res) => {
            if (res.status) {
              this.$Message.success('审核成功！');
              this._fetchData(false);
            } else {
              this.modalError(
                {
                  content: res.message,
                },
                500,
              );
            }
          });
        },
      });
    },
    toOrderDetail(id) {
      this.router.push({
        path: 'detail',
        query: { id: id },
      });
    },
    async getExpireList() {
      let res = await agreementPrice.getExpireList();
      this.expireList = [];
      this.expireNoArr = [];
      if (res.status) {
        let data = res.data;
        let list = [];
        for (let no in data) {
          list.push({
            no: no,
            id: data[no],
          });
          this.expireNoArr.push(no);
        }
        this.expireList = list;
      } else {
        this.expireList = [];
      }
    },
    getRowClass(row) {
      if (this.expireNoArr.includes(row.no)) {
        return 'warning';
      }
      return '';
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
  },
};
</script>

<style lang="less" scoped>
.outOfDate {
  position: fixed;
  z-index: 999;
  top: 0;
  max-width: 50%;
  left: 50%;
  transform: translateX(-50%);
}
.orderNo {
  cursor: pointer;
  color: red;
}
</style>
<style lang="less">
.agreementPriceList {
  .warning {
    color: red !important;
  }
}
.user-filter-modal {
  .ivu-modal-body {
    .list-table {
      .filter__col {
        display: flex;
        align-items: center;
        margin-right: 36px;
      }
      .filter__col:last-of-type {
        margin-right: 0;
      }
      .filter__operation {
        margin-left: 12px;
      }
    }
  }
}
</style>
