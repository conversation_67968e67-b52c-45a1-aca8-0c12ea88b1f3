<template>
  <div class="sort-outofstock">
    <ListTable
      row-key="order_commodity_id"
      :border="false"
      :outer-border="true"
      :max-line="2"
      :auto-load-data="true"
      :before-request="_beforeRequest"
      :after-request="_afterRequest"
      data-provider="/superAdmin/sortSuper/OrderCommodityList"
      :columns="originCols"
      :filter-items="filterItems"
      :advance="true"
      :showAdvance="showAdvance"
      :advance-items="advanceItems"
      @advanceChange="advanceChange"
      @on-selection-change="_handleSelectionChange"
      @pageChange="pageChange"
      @pageSizeChange="pageSizeChange"
      @paginationReset="paginationReset"
      :defaultPageSize="page.pageSize"
      :pageSizeOpts="page.pageSizeOpts"
      :customPagination="page"
      ref="table"
      :pageSizeCache="{ suffix: 'list' }"
    >
      <template #before-table>
        <div v-show="selectedRows.length === 0" class="common-operation">
          <div class="slot-left">
            <Dropdown
              @on-click="$_onExportStockOutTable"
              placement="bottom-end"
              slot="button"
            >
              <Button class="more-btn w85" type="default">
                导 出
                <s-icon :size="12" class="ml3" icon="arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem name="summary">商品汇总</DropdownItem>
                <DropdownItem name="detail">商品明细</DropdownItem>
              </DropdownMenu>
            </Dropdown>
            <Button class="ml15" type="primary" @click="_openCreateModal"
              >生成采购单</Button
            >
            <div class="slot-left__tip ml10 f14" v-if="isEnableProcess">
              （加工品不会在缺货商品列表中显示）
            </div>
          </div>
        </div>
      </template>
      <template #batch-operation>
        <Button class="ml10" type="primary" @click="_openCreateModal"
          >生成采购单</Button
        >
        <Button class="ml10" type="primary" ghost @click="handleBatchSplitOrder"
          >一键拆单
          <Tooltip
            placement="top"
            content="一键拆单将把已选择的商品在原客户的订单里删除，然后新增一个相同发货日期和下单数量、单价的订单，并默认订单备注为缺货订单"
            max-width="300"
          >
            <Icon type="ios-alert-outline" />
          </Tooltip>
        </Button>
        <SButton
          class="ml10"
          type="primary"
          ghost
          confirm
          title="取消缺货之后该包裹将不会在缺货商品页面中显示"
          placement="top"
          @click="handleBatchStockOut"
          >取消缺货</SButton
        >
        <SButton
          class="ml10"
          type="primary"
          ghost
          confirm
          title="确定重置已勾选商品的分拣状态？"
          placement="top"
          @click="handleBatchSet"
          >重置</SButton
        >
      </template>
    </ListTable>
    <s-modal
      ref="s-modal"
      type="default"
      :width="400"
      title="生成采购单"
      text="选择供应商/采购员生成采购单，统一生成一张采购单"
      :before-ok="_beforeOkCreate"
      @ok="$_onCreatePurchaseOrder"
    >
      <div style="padding-bottom: 40px">
        <div style="display: flex" class="mb10">
          <label style="width: 100px">采购方式</label>
          <radio-group v-model="procurementMethod">
            <radio label="1">临时指定</radio>
            <radio label="2">商品默认/客户、客户类型指定</radio>
          </radio-group>
        </div>
        <div style="display: flex" v-if="+procurementMethod === 1">
          <label style="width: 100px">供应商/采购员</label>
          <purchase-type
            v-model="purchaseType"
            :mode="['agent', 'direct_provider']"
            :change-on-select="false"
            @on-change="_changePurchaseType"
            placeholder="选择供应商/采购员"
            style="width: 220px"
          ></purchase-type>
        </div>
      </div>
    </s-modal>
    <batch-action ref="batch"></batch-action>
  </div>
</template>

<script>
import sort from '@api/sort.js';
import SortMixin from '@components/sort/mixins';
import ListTable from '@components/list-table';
import goodsAutoComplete from '@components/common/goodsAutoComplete_new';
import ConfigMixin from '@/mixins/config';
import LineSelect from '@components/delivery/lineSelect_new';
import PurchaseType from '@components/purchase-type';
import SButton from '@/components/button';
import BatchAction from './components/BatchAction.vue';
import _ from 'lodash-es';
import CommodityCascader from '@/components/base-filter-components/commodityCascader/index.vue';
import CustomizeCascader from "@/components/customize-cascader/index.vue";

export default {
  name: 'SortOutOfStock',
  mixins: [SortMixin, ConfigMixin],
  components: {
    ListTable,
    PurchaseType,
    LineSelect,
    SButton,
    BatchAction,
  },
  data() {
    return {
      procurementMethod: '1',
      filters: '', // 当前筛选条件
      curGoods: '',
      deliveryDate: '',
      currentStore: {},
      selectedRows: [],
      commodityList: [],
      showAdvance: true,
      purchaseType: [],
      pageSizeCacheKey: `pageSize_${this.$route.path}_list`,
      page: {
        pageSizeOpts: [100, 200, 300],
        pageSize: 200,
        totalPage: 0,
        total: 0,
        currentPage: 1,
      },
      originCols: [
        {
          type: 'selection',
          width: 60,
          align: 'left',
        },
        {
          width: 240,
          minWidth: 140,
          fixed: 'left',
          title: '商品名称',
          key: 'commodity_name',
          poptip: true,
          render: (h, params) => {
            let { row } = params;
            let tag = [];
            const isFirstOrder = Number(params.row.is_first_order) === 1;
            let firstOrderTag = '';
            if (isFirstOrder) {
              firstOrderTag = h(
                'Tooltip',
                {
                  props: {
                    transfer: true,
                    placement: 'top',
                    content: '该客户第一次下单',
                  },
                },
                [
                  h('i', {
                    style: {
                      color: '#03AC53',
                      fontSize: '14px',
                      marginTop: '-2px',
                      marginRight: '4px',
                    },
                    class: ['sui-icon', 'icon-first-order'],
                  }),
                ],
              );
            }
            // 赠品
            if (row.isGift) {
              tag = h(
                'span',
                {
                  style: {
                    color: 'red',
                  },
                },
                '[赠品]',
              );
            }
            let stockOutTag = this.getStockOutTag(h, row);
            return h(
              'div',
              {
                style: {
                  position: 'relative',
                },
              },
              [firstOrderTag, stockOutTag, h('span', row.commodity_name), tag],
            );
          },
        },
        {
          title: '商品分类',
          minWidth: 100,
          key: 'category_name',
        },
        {
          width: 180,
          minWidth: 140,
          title: '默认采购员/供应商',
          key: 'agent_name',
        },
        {
          minWidth: 120,
          title: '描述',
          key: 'summary',
        },
        {
          minWidth: 140,
          title: '客户名称',
          key: 'user_name',
        },
        {
          width: 140,
          minWidth: 140,
          title: '子账号',
          key: 'sub_user_name',
        },
        {
          width: 100,
          title: '订购数量',
          key: 'order_amount_desc',
        },
        {
          width: 120,
          title: '已分拣数量',
          key: 'actual_amount',
        },
        {
          width: 120,
          title: '缺货数量',
          key: 'actual_amount',
          render(h, params) {
            return h(
              'span',
              _.round(
                params.row.convert_order_amount - params.row.actual_amount,
                2,
              ),
            );
          },
        },
        {
          minWidth: 120,
          title: '备注',
          poptip: true,
          key: 'remark',
        },
        {
          width: 140,
          title: '采购单',
          poptip: true,
          key: 'purchase_order_generated_status_text',
        },
        {
          title: '操作',
          type: 'action',
          key: 'action',
          actionCountLimit: 3,
          width: 150,
          actions: (params) => {
            let data = params.row;
            const actions = [
              {
                name: '取消缺货',
                ctrl: true,
                confirm: () => '取消缺货之后该包裹将不会在缺货商品页面中显示',
                action: () => this.$_onCancelStockOut(params.index, data),
              },
              {
                name: '重置',
                ctrl: true,
                confirm: () => `确定重置商品${data.commodity_name}分拣状态？`,
                action: () => this.$_onReset(data),
              },
            ];
            return actions.filter(
              (item) => item.ctrl === true || data[item.ctrl],
            );
          },
        },
      ],
      filterItems: [
        {
          label: '发货日期',
          type: 'DatePicker',
          key: 'delivery_date',
          props: {
            editable: false,
            clearable: false,
          },
          defaultValue: '',
          noreset: true,
          onChange: (value) => {
            this.deliveryDate = value;
            return { value };
          },
        },
        {
          type: 'custom',
          component: goodsAutoComplete,
          key: 'commodity_name',
          label: '商品搜索',
          onChange(value) {
            return {
              value,
              stop: true,
            };
          },
          props: {
            placeholder: '名称/编码/助记码/别名/关键字',
            on: {
              'on-enter': (e) => {
                this.selectGoods(e);
              },
              'on-focus': (e) => {
                this.resetCommodityName(e);
              },
            },
          },
        },
      ],
      advanceItems: [
        {
          items: [
            {
              type: 'Select',
              data: [],
              key: 'store_id',
              defaultValue: '',
              label: '仓库',
              props: {
                placeholder: '全部',
                filterable: true,
                'filter-by-label': true,
              },
              noreset: true,
            },
            {
              type: 'custom',
              key: ['category_id', 'category_id2'],
              component: CommodityCascader,
              checked: true,
              require: true,
              width: 'auto',
              name: '商品分类',
              defaultValue: [],
              props: {
                noMaxHeight: true,
                needThreeLevel: false,
              },
            },
            {
              type: 'Input',
              key: 'user_name',
              label: '客户',
              props: {
                placeholder: '请输入客户名称/编码',
              },
            },
            {
              type: 'custom',
              component: LineSelect,
              key: 'line_id',
              label: '线路',
              props: {
                data: [],
                showAll: false,
                remote: true,
                multiple: true,
                placeholder: '全部',
              },
            },
            {
              key: ['channel_type', 'channel_type_value'],
              label: '采购员/供应商',
              type: 'custom',
              component: PurchaseType,
              props: {
                placeholder: '选择采购类型',
                mode: ['agent', 'direct_provider'],
                'change-on-select': false,
              },
            },
            {
              checked: false,
              label: '采购单',
              type: 'Select',
              key: 'generated_status',
              data: [
                {
                  label: '全部',
                  value: '0',
                },
                {
                  label: '已生成',
                  value: '2',
                },
                {
                  label: '未生成',
                  value: '1',
                },
              ],
            },
            {
              type: 'Input',
              label: '子账号',
              key: 'sub_user_search',
              props: {
                placeholder: '输入子账号',
              },
            },
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
          ],
        },
      ],
    };
  },
  created() {
    const cachedPageSize = this.pageSizeCacheKey
      ? localStorage.getItem(this.pageSizeCacheKey)
      : null;
    this.page.pageSize = cachedPageSize ? parseInt(cachedPageSize, 10) : 200;
    this.getSearchConfig();
    this.showAdvance =
      localStorage.getItem(this.$route.name + '-showAdvance') !== 'false';
  },
  methods: {
    $_onReset(row) {
      let order_commodity_id = row.order_commodity_id;
      sort.resetSort({ order_commodity_id }).then((res) => {
        if (res.status) {
          this.$refs.table.fetchData();
          this.$smessage({ type: 'success', text: '重置成功！' });
        } else {
          this.$smessage({ type: 'warning', text: res.message });
        }
      });
    },
    async getSearchConfig() {
      try {
        let res = await sort.getSortSearchConfig();
        if (res.status) {
          let data = res.data;
          // this.searchConfig = res.data;
          this.deliveryDate = data.default_delivery_date;
          this.currentStore = data.store_list[0];
          this.filterItems[0].defaultValue = data.default_delivery_date;
          this.advanceItems[0].items[0].data = data.store_list.map(
            ({ id, name }) => ({
              label: name,
              value: id,
            }),
          );
          this.advanceItems[0].items[0].props.storeId = data.store_list[0].id;

          this.$refs.table.setValue(
            'delivery_date',
            data.default_delivery_date,
          );
          this.$refs.table.setValue('store_id', data.store_list[0].id);
        }
      } catch (e) {
        console.log(e);
      }
    },
    advanceChange(e) {
      localStorage.setItem(this.$route.name + '-showAdvance', e);
    },
    _handleSelectionChange(selection) {
      this.selectedRows = selection;
    },
    selectGoods(e) {
      this.curGoods = e;
      // this.$refs.table.setValue('commodity_name', '', true);
      this.$_onGetList();
    },
    resetCommodityName() {
      this.$refs.table.setValue('commodity_name', '', true);
    },
    $_onGetList() {
      if (this.$refs.table) {
        this.$refs.table.fetchData();
      }
    },
    delParams(params) {
      if (params.channel_type) {
        if (params.channel_type === '1')
          params.agent_id = params.channel_type_value;
        else params.provider_id = params.channel_type_value;
      }
      delete params.channel_type;
      delete params.channel_type_value;
    },
    _beforeRequest(params) {
      if (!params.delivery_date || !params.store_id) {
        return false;
      }
      if (Array.isArray(params.line_id)) {
        params.line_id = params.line_id.join(',');
      }
      this.delParams(params);
      params.order_delivery_date = params.delivery_date;
      params.is_stockout = 2;
      return params;
    },
    _afterRequest(res) {
      this.filters = this.deepClone(this.getFilters()); // 保存当前筛选条件

      if (res.status === 1) {
        const { data } = res;
        const { pageParams } = data;

        if (data.sort_alert) {
          this.$smodal({
            type: 'info',
            title: '提醒',
            text: data.sort_alert,
            mask: false,
            btns: 1,
          });
        }
        // 总条数
        this.page.total = parseInt(pageParams.count);
      }
      return res;
    },
    /**
     * 取消缺货
     * @param {index} 操作的行的下标
     * @param {goods} 操作的订单商品对象
     */
    $_onCancelStockOut(index, goods) {
      let params = {
        order_commodity_id: goods.order_commodity_id,
      };
      sort.cancelMarkStockOut(params).then((res) => {
        let { status, message } = res;
        if (status) {
          let tableList = this.$refs.table.getData();
          tableList.splice(index, 1);

          this.$smessage({
            type: 'success',
            text: '取消缺货成功',
          });
        } else {
          this.$smessage({
            type: 'error',
            text: message || '取消缺货失败',
          });
        }
      });
    },
    getStockOutTag(h, goods) {
      let stockOutTag = '';
      if (this.isStockOut(goods)) {
        stockOutTag = h('i', {
          style: {
            color: '#F13031',
            fontSize: '14px',
            marginTop: '-2px',
            marginRight: '4px',
          },
          class: ['sui-icon', 'icon-out-stock'],
        });
      }
      return stockOutTag;
    },
    /**
     * 导出
     * @param {exportType} 导出类型 summary：汇总 / detail：明细
     */
    $_onExportStockOutTable(exportType) {
      let {
        delivery_date,
        store_id,
        user_name,
        commodity_id,
        commodity_name,
        line_id,
        category_id,
        category_id2,
        provider_id,
        agent_id,
        generated_status,
        sub_user_search
      } = this.filters;

      let params = {
        type: exportType,
        delivery_date,
        store_id,
        user_name,
        commodity_id,
        commodity_name,
        category_id,
        category_id2,
        provider_id,
        agent_id,
        generated_status,
        sub_user_search
      };

      if (Array.isArray(line_id)) {
        params.line_id = line_id.join(',');
      } else {
        params.line_id = line_id;
      }
      sort.stockOutExport(params).then((res) => {
        let { status, message, data } = res;
        if (status) {
          window.open(window.location.origin + data.url, '_self');

          this.$smessage({
            type: 'success',
            text: '导出成功',
          });
        } else {
          this.$smessage({
            type: 'error',
            text: message || '导出失败',
          });
        }
      });
    },
    /**
     * @description: 生成采购单之前，选择采购员/供应商，将选择的商品置为同一个采购员/供应商
     */
    _changePurchaseType() {
      const type = this.purchaseType[0];
      const value = this.purchaseType[1];
      this.commodityList.map((item) => {
        if (+type === 1) {
          item.purchase_type = '1';
          item.agent_id = value;
          item.provider_id = '0';
        } else if (+type === 2) {
          item.purchase_type = '2';
          item.agent_id = '0';
          item.provider_id = value;
        }
      });
    },
    /**
     * @description: 点击‘生成采购单’，弹出选择供应商/采购员弹框
     */
    _openCreateModal() {
      if (this.$refs.table) {
        if (this.selectedRows.length === 0) {
          let tableData = this.$refs.table.getData();
          if (tableData.length === 0) {
            this.$smessage({
              type: 'error',
              text: '商品列表为空',
            });
            return;
          }
          this.commodityList = this._uniqueArray(tableData);
        } else {
          this.commodityList = this._uniqueArray(this.selectedRows);
        }
        let uniqueType = this.commodityList[0].purchase_type;
        let uniqueAgent = this.commodityList[0].agent_id;
        let uniqueProvider = this.commodityList[0].provider_id;
        if (
          this.commodityList.every(
            (item) =>
              +item.agent_id === +uniqueAgent &&
              +item.provider_id === +uniqueProvider,
          )
        ) {
          if (uniqueType === 1) {
            this.purchaseType = [uniqueType.toString(), uniqueAgent.toString()];
          } else if (uniqueType === 2) {
            this.purchaseType = [
              uniqueType.toString(),
              uniqueProvider.toString(),
            ];
          }
        } else {
          this.purchaseType = [];
        }
        this.$refs['s-modal'] && this.$refs['s-modal'].open();
      }
    },
    /**
     * @description: 数组去重，并累加相同商品的订购数量 与 已分拣数量 (后端接口要求如此)。这样有问题，现在又不去重了！！！！！！
     */
    _uniqueArray(arr) {
      return arr.map((cur) => ({
        commodity_id: cur.commodity_id,
        order_commodity_id: cur.order_commodity_id,
        purchase_type: +cur.agent_id ? 1 : +cur.provider_id ? 2 : 0,
        agent_id: cur.agent_id,
        provider_id: cur.provider_id,
        pur_amount:
          Number(cur.convert_order_amount) - Number(cur.actual_amount),
      }));
    },
    /**
     * @description: 生成采购单之前的数据合法性校验
     * @return {object} Promise状态，resolve()/reject()
     */
    _beforeOkCreate() {
      if (
        this.purchaseType &&
        this.purchaseType.length === 0 &&
        +this.procurementMethod === 1
      ) {
        this.errorMessage('请选择供应商/采购员');
        return Promise.reject();
      }
    },
    /**
     * 生成采购单
     */
    $_onCreatePurchaseOrder() {
      let { store_id, delivery_date } = this.filters;
      let params = {
        commodity_list: JSON.stringify(this.commodityList), // 商品列表json
        order_commodity_ids: this.commodityList
          .map((item) => item.order_commodity_id)
          .join(','),
        store_id: store_id, // 仓库id
        delivery_date: delivery_date, // 发货日期
      };
      if (+this.procurementMethod === 2) {
        params.is_default_purchase_person = 1;
      }
      sort.createPurchaseOrder(params).then((res) => {
        let { status, message } = res;
        if (status) {
          this.$smessage({
            type: 'success',
            text: message || '生成采购单成功',
          });
          this.$router.push('/purchase/order-list');
        } else {
          this.$smessage({
            type: 'error',
            text: message || '生成采购单失败',
          });
        }
      });
    },
    pageChange(page) {
      this.page.currentPage = page;
    },
    pageSizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    paginationReset() {
      this.page.totalPage = 0;
      this.page.total = 0;
      this.page.currentPage = 1;
    },
    getFilters() {
      let params = {};
      if (this.$refs.table) {
        params = this.$refs.table.getParams();
        params.page = this.page.currentPage;
        params.pageSize = this.page.pageSize;
        params.order_delivery_date = params.delivery_date;
        params.commodity_id =
          this.curGoods && this.curGoods.commodity_id
            ? this.curGoods.commodity_id
            : '';
        this.delParams(params);
      }
      return params;
    },
    handleBatchStockOut() {
      this.$refs.batch
        .open(this.apiUrl.cancelMarkStockOut, {
          selectedRows: this.selectedRows,
          taskSize: 1,
        })
        .then(() => {
          this.$refs.table.fetchData();
        });
    },
    handleBatchSet() {
      this.$refs.batch
        .open(this.apiUrl.resetSort, {
          selectedRows: this.selectedRows,
          taskSize: 20,
        })
        .then(() => {
          this.$refs.table.fetchData();
        });
    },
    loopSplitOrder(splitOrderList, number) {
      this.$request
        .post(this.apiUrl.userOrderDesignateSplit, {
          user_id: splitOrderList[number].user_id,
          order_commodity_ids:
            splitOrderList[number].order_commodity_ids.join(','),
        })
        .finally(() => {
          console.log(splitOrderList.length - 1, number);
          if (splitOrderList.length - 1 > number) {
            ++number;
            this.loopSplitOrder(splitOrderList, number);
            this.$refs.batch.openPercent({
              totalCount: splitOrderList.length,
              doneCount: number,
            });
          } else {
            this.$Message.success('拆单成功');
            this.$refs.batch.closeProgressModal();
            this.$refs.table.fetchData();
          }
        });
    },
    handleBatchSplitOrder() {
      this.$smodal({
        title: '一键拆单确认',
        type: 'warning',
        text: '【一键拆单】将对符合要求的订单商品进行拆单（在原始订单中将其删除，同时创建一个新的订单其发货日期与原始订单相同），成功拆单的商品分拣状态将从"缺货”变更为"未分拣"',
        onOk: () => {
          const splitOrderList = [];
          this.selectedRows.forEach((item) => {
            const index = splitOrderList.findIndex(
              (ite) => ite.user_id === item.user_id,
            );
            if (index === -1) {
              splitOrderList.push({
                user_id: item.user_id,
                order_commodity_ids: [item.order_commodity_id],
              });
            } else {
              splitOrderList[index].order_commodity_ids.push(
                item.order_commodity_id,
              );
            }
          });
          console.log(splitOrderList);
          this.$refs.batch.openPercent({
            totalCount: splitOrderList.length,
            doneCount: 0,
          });
          this.loopSplitOrder(splitOrderList, 0);
        },
      });
    },
  },
};
</script>

<style scoped lang="less">
.common {
  background-color: #fff;
}
</style>

<style lang="less">
.sort-outofstock {
  .common-operation {
    padding-top: 8px;
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    .slot-left {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      &__tip {
        display: inline-block;
        color: #ff9f00;
      }
    }
    .slot-right {
      color: rgba(0, 0, 0, 0.85);
      line-height: 14px;
      .ivu-checkbox-wrapper {
        height: auto;
        line-height: normal;
      }
    }
  }
  .more-btn-group {
    .main-btn {
      .ivu-btn {
        border-radius: 2px 0 0 2px;
        border-right-width: 0;
      }
    }
    .more-btn {
      .ivu-btn {
        border-radius: 0 2px 2px 0;
      }
    }
  }
  .sdp-table__tr.checked {
    .sdp-table__td,
    .warning,
    .light {
      color: #f13130;
      background: #fef6f5;
      font-size: 16px;
      font-weight: 500;
    }
    input {
      border-color: #f13130;
    }
  }
  .warning {
    color: #ff9f00;
  }
  .light {
    color: rgba(0, 0, 0, 0.5);
  }
  .process-list {
    height: 52px;
    line-height: 52px;
    background: rgba(250, 251, 252, 0.5);
    border-radius: 1px 1px 0px 0px;
    border: solid #e8e8e8;
    border-width: 1px 1px 0 1px;
    padding: 0 18px;
    .process-item {
      display: flex;
      align-items: center;
    }
    h6 {
      font-size: 13px;
      margin-left: 12px;
    }
    .sorted {
      color: #03ac54;
      margin: 0 4px;
    }
    .sort {
      color: #ff9f00;
      margin: 0 4px;
    }
  }
}
</style>
