<template>
  <div class="diff-sort">
    <ListTable
      :filters="extraFilters"
      ref="listTable"
      outer-border
      :autoLoadData="false"
      :columns="columns"
      :filter-items="filterItems"
      :advance="true"
      :advance-items="advanceItems"
      :height="getTableHeight() - 80"
      :data-provider="apiUrl.getSortOrderCommodity"
      :beforeRequest="_beforeRequest"
      :afterRequest="_afterRequest"
      :pageSizeOpts="page.pageSizeOpts"
      :customPagination="page"
      :pageSizeCache="{ suffix: 'list' }"
      @reset-change="handleResetChange"
    >
      <div
        slot="before-table-head"
        class="process-list"
        v-show="sortProcessList.length"
      >
        <div
          class="process-item"
          :key="index"
          v-for="(item, index) in sortProcessList"
        >
          <s-circle-rate
            :rate="
              (item.sortedAmount / (item.sortedAmount + item.sortAmount)) * 100
            "
          />
          <h6>
            <span>{{ item.commodityName }}：已分拣</span>
            <span class="sorted">{{ item.sortedAmount }}</span>
            <span>{{ item.commodityUnit }} 未分拣</span>
            <span class="sort">{{ item.sortAmount }}</span>
            <span>{{ item.commodityUnit }}</span>
          </h6>
        </div>
      </div>
      <ExportButton
        type="default"
        slot="button"
        text="导出"
        :offline="true"
        :param-getter="getExportParams"
        api="/superAdmin/sortSuper/exportDiffSortList"
      ></ExportButton>
    </ListTable>

    <Modal
      :title="sortManyModal.title"
      :mask-closable="false"
      v-model="sortManyModal.show"
      :closable="false"
      class-name="vertical-center-modal sort-many-modal"
    >
      <Form
        label-position="left"
        onsubmit="return false"
        :label-width="80"
        style="text-align: left"
      >
        <FormItem label="分拣数：">
          <Tooltip
            :disabled="!sortManyModal.amount_warning"
            :always="true"
            theme="danger"
            placement="right"
            :content="sortManyModal.amountWarningContent"
          >
            <input
              type="text"
              @keyup.enter="saveSortMany"
              class="sort-many-input"
              v-model="sortManyModal.amount"
              style="width: 120px; display: inline-block"
            />
          </Tooltip>
          <span class="many-sort-tips">输入数量，按回车</span>
        </FormItem>
        <FormItem label="分拣记录：">
          <div
            ref="record"
            :style="{
              maxHeight: getTableHeight() - 50 + 'px',
              overflow: 'auto',
            }"
          >
            <p v-for="(item, index) in sortManyModal.list" :key="index">
              {{ item }}{{ sortManyModal.rowParams.row.unit }}
            </p>
          </div>
        </FormItem>
      </Form>
      <div slot="footer">
        <Row type="flex" align="middle">
          <Col style="flex: 1" align="left">
            <span v-if="sortManyModal.show"
              >分拣累计：{{ sortManyTotal
              }}{{ sortManyModal.rowParams.row.unit }}</span
            >
          </Col>
          <Col align="right">
            <Button type="primary" @click="closeSortManyModal">完成</Button>
          </Col>
        </Row>
      </div>
      <order-exception
        :deliveryDate="curFilters.delivery_date"
      ></order-exception>
    </Modal>
    <MultiPrint
      postUrl="/superAdmin/sortSuper/AuthorizedPrintMultipleLabels"
      @on-ok="itemSortComplete"
      :goods="multiPrintModal.goods"
      v-model="multiPrintModal.show"
    />
    <CheckSorterTask />
  </div>
</template>

<script>
import CustomizeCascader from "@/components/customize-cascader/index.vue";

const MIN_PAGE_SIZE = 100;
const MAX_PAGE_SIZE = 300;
import ListTable from '@/components/list-table/index.js';
import GoodsAutoComplete from '@/components/common/goodsAutoComplete_new.vue';
import StoreSelect from '@/components/common/storeSelect_new.vue';
import StoreAreaSelect from '@/components/common/storeAreaSelect_new.vue';
import orderException from '@/components/common/orderException.vue';
import CheckSorterTask from '@/components/store/CheckSorterTask/index.vue';
import SCircleRate from '@/components/circle-rate/index.js';

import sort from '@/api/sort.js';
import apiUtil from '@/api/util.js';
import SortMixin from '@/components/sort/mixins/index.js';
import ConfigMixin from '@/mixins/config.js';
import PickPrint from '@/mixins/print/pickPrint.js';
import CommodityCascader from '@/components/base-filter-components/commodityCascader/index.vue';
import CheckboxGroup from '@/components/CheckboxGroup';
import MultiPrint from '@components/sort/MultiPrint';

export default {
  name: 'DiffSort',
  mixins: [SortMixin, ConfigMixin, PickPrint],
  components: {
    ListTable,
    orderException,
    CheckSorterTask,
    SCircleRate,
    MultiPrint
  },
  computed: {
    sortManyTotal() {
      let sortManyList = this.sortManyModal.list;
      return sortManyList.length > 0
        ? sortManyList.reduce(
            (total, currentValue) => total * 1 + currentValue * 1,
          )
        : 0;
    },
  },
  data() {
    return {
      extraFilters: {
        category_id: '', // 一级分类id
        category_id2: '', // 二级分类id
      },
      curFilters: {},
      // postList: [],
      pageSizeCacheKey: `pageSize_${this.$route.path}_list`,
      page: {
        pageSizeOpts: [MIN_PAGE_SIZE, 200, MAX_PAGE_SIZE],
        total: 0,
        currentPage: 1,
        pageSize: 100,
      },
      filterItems: [
        {
          type: 'DatePicker',
          label: '发货日期',
          key: 'delivery_date',
          props: {
            clearable: false,
            editable: false,
          },
        },
        {
          type: 'custom',
          component: GoodsAutoComplete,
          label: '商品',
          key: 'commodity_name',
          onChange: (value) => {
            this.$refs.listTable.setValue('commodity_id', '', true);
            return { value, stop: true };
          },
          props: {
            on: {
              'on-enter': (goods) => {
                this.$refs.listTable.setValue(
                  'commodity_id',
                  goods.commodity_id,
                ); // 将自动触发fetchData
              },
              'on-clear': () => {
                this.$refs.listTable.setValue('commodity_id', '', true);
                this.$refs.listTable.setValue('commodity_name', '');
              },
            },
          },
        },
      ],
      advanceItems: [
        {
          items: [
            {
              type: 'custom',
              component: StoreSelect,
              label: '仓库',
              key: 'store_id',
              asyncComponent: true,
              onChange: (value) => {
                const index = this.advanceItems[0].items.findIndex(
                  (item) => item.key === 'store_area_id',
                );
                if (~index) {
                  this.$refs.listTable &&
                    this.$refs.listTable.setValue('store_area_id', '', true);
                  this.advanceItems[0].items[index].props.storeId = value;
                }
                return { value };
              },
            },
            {
              type: 'custom',
              component: StoreAreaSelect,
              label: '库区',
              key: 'store_area_id',
              show: apiUtil.getIsOpenStoreMGT(),
              props: {
                storeId: '',
              },
            },
            {
              type: 'Select',
              label: '异常情况',
              key: 'diff_type',
              defaultValue: 0,
              data: Object.freeze([
                {
                  label: '全部',
                  value: 0,
                },
                {
                  label: '下单数量变化',
                  value: 1,
                },
                {
                  label: '下单商品删除',
                  value: 2,
                },
                {
                  label: '核算数量变化',
                  value: 3,
                },
                {
                  label: '核算商品删除',
                  value: 4,
                },
                {
                  label: '订单关闭',
                  value: 5,
                },
                {
                  label: '分拣超过阈值',
                  value: 6,
                },
                {
                  label: '分拣低于阈值',
                  value: 7,
                },
              ]),
            },
            {
              type: 'Input',
              label: '客户',
              key: 'user_name',
              props: {
                placeholder: '输入客户名称/编码',
              },
            },
            {
              type: 'Input',
              label: '客户临时编码',
              key: 'sort_code',
              show: false,
              props: {
                placeholder: '分拣客户临时编码',
              },
            },
            {
              checked: true,
              require: true,
              width: 'auto',
              type: 'custom',
              name: '商品分类',
              key: ['category1', 'category2', 'category_id3'],
              defaultValue: [],
              component: CommodityCascader,
              props: {
                noMaxHeight: true,
              },
              onChange: (value) => {
                // 如果每个级别都为空字符串,则清空筛选条件
                if (value.every((item) => !item)) {
                  this.extraFilters.category_id = '';
                  this.extraFilters.category_id2 = '';
                  this.extraFilters.category_id3 = '';
                  this._fetchData();
                  return { stop: false };
                }
                this.extraFilters.category_id = value[0];
                // 数组剩下的所有值,数量不确定
                this.extraFilters.category_id2 = value.slice(1).join(',');
                this._fetchData();
                return { stop: false };
              },
            },
            {
              type: 'Input',
              label: '子账号',
              key: 'sub_user_search',
              props: {
                placeholder: '输入子账号',
              },
            },
            {
              show: () => this.isOpenCustomerFieldCustomize,
              checked: false,
              width: 'auto',
              type: 'custom',
              name: '客户自定义字段',
              key: ['user_customize_id', 'user_customize_field_select_config_ids'],
              defaultValue: [],
              props: {
                customizeType: '14',
                label: '客户自定义字段',
              },
              component: CustomizeCascader,
            },
            {
              checked: false,
              required: false,
              show: false,
              label: '客户标签',
              block: true,
              key: 'user_tag',
              type: 'custom',
              tagTopStart: true,
              defaultValue: [],
              props: {
                key: 'user_tag',
              },
              component: CheckboxGroup,
              onChange: (value) => {
                return { value, stop: true };
              },
            },
            // {
            // 	required: false,
            // 	checked:true,
            // 	type: 'custom',
            // 	component: MutiCascader,
            // 	key: ['category_id', 'category_id2'],
            // 	label: '商品分类',
            // 	props: {
            // 		data: [],
            // 		clearable: true,
            // 		'change-on-select': true,
            // 		placeholder: '全部',
            // 		filterable: true
            // 	},
            // 	onChange: (value) => {
            // 		this.extraFilters.category_id = value[0];
            // 		// 数组剩下的所有值,数量不确定
            // 		this.extraFilters.category_id2 = value.slice(1).join(',');
            // 		this._fetchData();
            // 		return { stop: false }
            // 	}
            // },
          ],
        },
      ],
      columns: [
        {
          type: 'titleCfg',
          titleType: 'sort_diff',
          width: 40,
          align: 'center',
          fixed: 'left',
          key: 'title',
        },
        {
          title: '商品名称',
          key: 'commodity_name',
          width: 240,
          minWidth: 140,
          fixed: 'left',
          resizable: true,
          poptip: true,
          render: (h, params) => {
            const { row } = params;
            let tag = [];
            // 赠品
            if (row.isGift) {
              tag = h(
                'span',
                {
                  style: {
                    color: 'red',
                  },
                },
                '[赠品]',
              );
            }
            const stockOutTag = this.getStockOutTag(h, row);
            return h(
              'div',
              {
                style: {
                  position: 'relative',
                },
              },
              [stockOutTag, h('span', row.commodity_name), tag],
            );
          },
        },
        {
          width: 120,
          minWidth: 120,
          title: '临时分拣码',
          key: 'sort_code',
        },
        {
          width: 120,
          minWidth: 160,
          title: '库区/库位',
          key: 'shelf_code',
        },
        {
          width: 140,
          minWidth: 140,
          title: '客户名称',
          key: 'shop_name',
        },
        {
          width: 140,
          minWidth: 140,
          title: '子账号',
          key: 'sub_user_name',
        },
        {
          width: 100,
          title: '订购数量',
          key: 'order_amount_desc',
          align: 'right',
        },
        {
          width: 120,
          title: '实际数量',
          key: 'actual_amount',
          render: (h, params) => {
            const { row, index } = params;
            const input = h('input', {
              attrs: {
                min: 0,
                class: 'table-number-input',
                disabled: +row.sort_status === 1 || +row.diff_type === 5,
              },
              domProps: {
                value:
                  +row.sort_status === 1 || +row.diff_type === 5
                    ? row.actual_amount
                    : row.actual_amount || '',
              },
              on: {
                change: (event) => {
                  row.actual_amount = event.target.value;
                  row.input_amount = event.target.value;
                },
                focus: (event) => {
                  event.target.select();
                  // this.hightlightRow(index, 'red', 'bolder', '16px', 'listTable')
                  row._checked = true;
                  this.activeRow.index = index;
                  this.activeRow.event = event;
                },
                blur: () => {
                  row._checked = false;
                },
                keyup: (event) => {
                  if (event.keyCode === 13) {
                    const valid = this.saveSortInfo(
                      event.target.value,
                      params,
                      event,
                    );
                    if (valid) {
                      this.setFocus();
                    }
                  }
                },
              },
            });
            return input;
          },
        },
        {
          width: 130,
          title: '生产日期',
          key: 'mfg_date',
          render: (h, params) => {
            const { row } = params;
            return h('DatePicker', {
              props: {
                value: row.mfg_date,
                options: {
                  disabledDate: (date) => {
                    const timestamp = Date.parse(this.curFilters.delivery_date);
                    return date && date.valueOf() > timestamp;
                  },
                },
                transfer: true,
                disabled: +row.diff_type === 5,
              },
              on: {
                'on-change': (date) => {
                  this.saveMfgDate(row.order_commodity_id, date);
                },
              },
            });
          },
        },
        {
          width: 70,
          title: '单位',
          key: 'unit',
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          width: 120,
        },
        {
          width: 120,
          minWidth: 120,
          title: '描述',
          key: 'summary',
        },
        {
          title: '备注',
          key: 'remark',
          minWidth: 120,
        },
        {
          title: '异常情况',
          key: 'diff_type_desc',
          width: 120,
        },
        {
          title: '库存',
          key: 'record',
          width: 90,
          align: 'right',
        },
        {
          minWidth: 90,
          title: '分拣状态',
          key: 'sort_status_desc',
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              {
                class: row.sort_status_desc === '未分拣' ? 'warning' : 'light',
              },
              row.sort_status_desc,
            );
          },
        },
        {
          width: 80,
          title: '分拣员',
          key: 'sort_name',
        },
        {
          type: 'action',
          title: '操作',
          key: 'action',
          width: 248,
          actions: (params) => {
            const { row } = params;
            if (
              +row.diff_type === 5 ||
              +row.diff_type === 2 ||
              +row.diff_type === 4
            ) {
              // 订单关闭，禁止任何操作
              return [];
            }
            const actions = [
              {
                name: '打印多份标签',
                ctrl: this.isEnableMultiPrint,
                action: () => {
                  this.showMultiPrint(params.row);
                },
              },
              {
                name: '打印',
                ctrl: true,
                action: () => {
                  this.print(params);
                },
              },
              {
                name: '多次分拣',
                ctrl: 'isCanManySort',
                action: () => {
                  this.openSortManyModal(params);
                },
              },
              {
                name: '标记缺货',
                ctrl: 'isCanMarkStockOut',
                action: () => {
                  this._markStockOut(row, () => this._fetchData());
                },
              },
              {
                name: '重置',
                ctrl: 'isCanReset',
                action: () => {
                  this.$Modal.confirm({
                    content: `确定重置商品${row.commodity_name}分拣状态？`,
                    onOk: () => {
                      this.resetSort(params);
                    },
                  });
                },
              },
            ];
            return actions.filter(
              (item) => item.ctrl === true || row[item.ctrl],
            );
          },
        },
      ],
      // isMultiStorage: false,
      countTask: {
        task_no: '',
        timer: 0,
        loop: true,
        time: 0,
        frequency: 1000,
        maxLoopTime: 2 * 60 * 1000,
      },
      searchConfig: {
        sort_status: [],
        standard_status: [],
      },
      sortManyModal: {
        // 多次分拣模态框
        title: '多次分拣',
        amount_warning: false,
        show: false,
        amount: '',
        list: [],
        rowParams: {},
        amountWarningContent: '',
      },
      sortProcessList: [],
      activeRow: {
        index: 0,
        event: null,
      },
    };
  },
  watch: {
    sysConfig() {
      const index = this.advanceItems[0].items.findIndex(
        (item) => item.key === 'sort_code',
      );
      if (~index) {
        this.advanceItems[0].items[index].show = this.isEnableSortCode;
      }
    },
  },
  created() {
    const cachedPageSize = this.pageSizeCacheKey
      ? localStorage.getItem(this.pageSizeCacheKey)
      : null;
    this.page.pageSize = cachedPageSize
      ? parseInt(cachedPageSize, 10)
      : MAX_PAGE_SIZE;
  },
  mounted() {
    this.init();
    this._getSearchConfig();
    this.getUserTagList();
    // storeRoom.checkIsMultiStorage().then((res) => {
    //   if (res.status) {
    //     this.isMultiStorage = res.data
    //   }
    // })
    // 没有开启库区
    if (!apiUtil.getIsOpenStoreMGT()) {
      this.columns.splice(
        this.columns.findIndex((col) => col.key === 'shelf_code'),
        1,
      );
    }
  },
  methods: {
    getUserTagList() {
      this.$request.get(this.apiUrl.getUserTagList).then((res) => {
        if (res.status && res.data && res.data.length) {
          const tagFilter = this.advanceItems.find((arr) =>
            arr.items.find((item) => item.key === 'user_tag'),
          );
          const userTagItem = tagFilter.items.find(
            (item) => item.key === 'user_tag',
          );
          if (userTagItem) {
            userTagItem.show = true;
            userTagItem.props.data = res.data.map((item) => ({
              value: item.id,
              label: item.name,
            }));
          }
        }
      });
    },
    init() {
      const index = this.advanceItems[0].items.findIndex(
        (item) => item.key === 'sort_code',
      );
      if (~index) {
        this.advanceItems[0].items[index].show = this.isEnableSortCode; // 根据配置的计算属性初始化
      }
    },
    /**
     * 获取分拣搜索配置
     */
    async _getSearchConfig() {
      const { status, data } = await sort.getSortSearchConfig();
      // 商品分类联级数据, 给最后一级的数据添加multiple属性以实现多选
      this.advanceItems.forEach((item) => {
        item.items.forEach((child) => {
          if (child.label === '商品分类') {
            child.props.data = data.commodity_category.map((item) => {
              if (item.children && item.children.length) {
                item.children.forEach((child) => {
                  child.multiple = true;
                });
              }
              return item;
            });
          }
        });
      });
      if (status) {
        this.searchConfig = data;
        this.$nextTick(() => {
          // 异常订单检查
          const index = this.filterItems.findIndex(
            (item) => item.key === 'delivery_date',
          );
          ~index &&
            this.$set(
              this.filterItems[index],
              'defaultValue',
              data.default_delivery_date,
            );
          this.$refs.listTable.setValue(
            'delivery_date',
            data.default_delivery_date,
          );
        });
      }
    },
    /**
     * 获取分拣商品
     * @param resetPage
     */
    _fetchData(resetPage) {
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage);
    },
    _beforeRequest(params) {
      this.resetActiveRow();
      // 库房id / 发货日期 必传
      if (!params.store_id || !params.delivery_date) {
        console.log('请选择库房和发货日期！');
        return false;
      }
      if (params.commodity_id) {
        params.commodity_name = '';
      } else {
        this.resetSortProgress();
      }
      return params;
    },
    _afterRequest(res) {
      this.curFilters = this.deepClone(this._getFilters());
      const { status, data, message } = res;
      // 刷新分拣进度
      if (this.curFilters.commodity_id) {
        this.getSortProgress();
      }
      if (status) {
        const { list, pageParams } = data;
        list.forEach((item) => {
          item.input_amount = +item.actual_amount
            ? item.actual_amount.toString()
            : '';
          // item.amount_warning = false
          // item.warning_msg = '您设置的数量已超过阈值'
        });
        // this.postList = this.deepClone(list)
        if (data.sort_alert) {
          this.$Modal.remove();
          this.$Modal.warning({
            title: '提醒',
            content: data.sort_alert,
          });
        }
        // 获取分拣总条数
        if (data.refresh_count) {
          this.genCountTask();
        } else {
          this.page.total = parseInt(pageParams.count);
          this.endCountLoop();
        }
      } else {
        this.$refs.listTable.resetPagination();
        switch (res.errCode) {
          // 客户未设置区域
          case sort.errorCode.user_no_area:
            this.modalError({
              content: message,
              onOk: () => {
                this.router.push({
                  path: '/userList',
                  query: {
                    area_id: '0',
                  },
                });
              },
            });
            break;
          // 客户区域被删除
          case sort.errorCode.user_area_deleted:
            this.modalError(message);
            break;
          // 客户对应的区域未分配到线路
          case sort.errorCode.area_no_match_line:
            this.modalError(message);
            break;
          default:
            break;
        }
      }
      return res;
    },
    _getFilters() {
      const filters = this.$refs.listTable.getParams();
      // console.log('filters', filters);

      filters.order_delivery_date = filters.delivery_date;
      return filters;
    },
    genCountTask() {
      sort.genSortCount(this.curFilters).then((res) => {
        const { status, data } = res;
        if (status) {
          this.countTask.task_no = data.task_no;
          this.getCount();
        } else {
          this.countTask.task_no = '';
        }
      });
    },
    getCount() {
      const taskNo = this.countTask.task_no;
      sort.getSortCount(taskNo).then((res) => {
        const { status, data } = res;
        if (status) {
          this.page.total = parseInt(data.sort_count);
          this.endCountLoop();
        } else {
          this.startCountLoop();
        }
      });
    },
    startCountLoop() {
      this.countTask.timer = setTimeout(() => {
        this.countTask.time += this.countTask.frequency;
        this.getCount();
      }, this.countTask.frequency);
    },
    endCountLoop() {
      if (this.countTask.timer) {
        clearTimeout(this.countTask.timer);
      }
      this.countTask.time = 0;
      this.countTask.timer = 0;
    },
    openSortManyModal(rowParams) {
      this.sortManyModal.rowParams = rowParams;
      this.sortManyModal.show = true;
      this.sortManyModal.amount_warning = false;
      this.sortManyModal.title = `${rowParams.row.commodity_name}多次分拣`;
      this.sortManyFocus();
    },
    sortManyFocus() {
      this.$nextTick(function () {
        document.querySelector('.sort-many-input').focus();
      });
    },
    closeSortManyModal() {
      this.sortManyModal.list = [];
      this.sortManyModal.rowParams = {};
      this.sortManyModal.show = false;
      this.sortManyModal.amount = '';
      this._fetchData(false, this.curFilters.commodity_id);
    },
    changeDeliveryDate(date) {
      this.curFilters.delivery_date = date;
      this._fetchData();
    },
    selectGoods(goods) {
      if (goods) {
        this.curFilters.commodity_name = '';
        this._fetchData(true, goods.commodity_id);
      }
    },
    /**
     * 保存多次分拣
     * @param amount 分拣数量
     * @param order_commodity_id 订单商品id
     */
    saveSortMany(event) {
      let rowParams = this.sortManyModal.rowParams;
      let row = rowParams.row;
      sort
        .saveSortMany({
          amount: this.sortManyModal.amount,
          order_commodity_id: row.order_commodity_id,
        })
        .then((res) => {
          const { status, data } = res;
          if (status) {
            // 允许打印
            if (rowParams.row.isCanPrint) {
              this.printTicket(row.order_commodity_id, true);
            }
            this.sortManyModal.list.push(this.sortManyModal.amount);
            this.sortManyModal.amount = '';
            event.target.focus();
            this.$nextTick(() => {
              let sortRecordDom = this.$refs.record;
              sortRecordDom.scrollTop = sortRecordDom.scrollHeight;
            });
            this.sortManyFocus();
            let amountWarningMessage = '';
            if (+data.sort_threshold_status === 2) {
              amountWarningMessage = '您设置的数量已超过阈值';
            } else if (+data.sort_threshold_status === 3) {
              amountWarningMessage = '您设置的数量已低于阈值';
            }
            this.sortManyModal.amountWarningContent = amountWarningMessage;
            this.sortManyModal.amount_warning = !!amountWarningMessage;
          } else {
            this.modalError({
              content: res.message,
              onOk: () => {
                event.target.focus();
                this.sortManyModal.amount = '';
              },
            });
          }
        });
    },
    /**
     * 保存分拣信息
     * @param amount 分拣数量
     * @param rowParams table中的每行数据
     * @param event 输入框事件对象
     */
    saveSortInfo(amount, rowParams, event) {
      amount = amount.trim();
      if (isNaN(+amount)) {
        this.warningMessage('分拣数量必须是数字');
        return false;
      }

      if (+amount < 0) {
        this.warningMessage('分拣数量必须大于0');
        return false;
      }

      const { row } = rowParams;
      const params = {
        order_commodity_id: row.order_commodity_id,
        amount,
      };
      sort.saveSortInfo(params).then((res) => {
        const { status, data } = res;
        if (status) {
          // 允许打印
          if (row.isCanPrint) {
            this.printTicket(row.order_commodity_id);
          }

          row.sort_status = '1';
          row.sort_status_desc = '已分拣';
          row.actual_amount = amount;
          row.isCanReset = true;
          row.isCanMarkStockOut = false;
          this.cancelStockOut(row);

          // this.postList.splice(rowParams.index, 1, this.cloneObj(rowParams.row))

          this.getSortProgress();

          let amountWarningMessage = '';
          if (+data.sort_threshold_status === 2) {
            amountWarningMessage = '您设置的数量已超过阈值';
          } else if (+data.sort_threshold_status === 3) {
            amountWarningMessage = '您设置的数量已低于阈值';
          }
          amountWarningMessage && this.warningMessage(amountWarningMessage);
        } else {
          row.actual_amount = '';
          this.warningMessage(res.message || res.errMsg || '未知错误');
        }
      });
      return true;
    },
    print(params) {
      const { row, index } = params;
      const data = this.$refs.listTable.getData();
      const amount = data[index]['input_amount'];
      if (row.sort_status == sort.sortStatus.sorted) {
        this.printTicket(row.order_commodity_id);
      } else {
        this.saveSortInfo(amount, params);
      }
    },
    /**
     * 打印标签
     * @param orderCommodityId 订单商品id
     */
    printTicket(orderCommodityId, isSortMany) {
      // prckPrint  mixin混入函数
      this._mxPrintPickOrder({
        className: 'tpl_print_pick',
        mulitPick: isSortMany ? '1' : '',
        dataId: orderCommodityId,
      });
    },
    setFocus(index, refresh) {
      const nextRowIndex =
        index === undefined ? +this.activeRow.index + 1 : +index + 1;
      if (
        // 不走这个逻辑
        false &&
        refresh !== false &&
        ((this.activeRow.index == this.list.length - 1 &&
          this.list.length > 1) || // 最后一条纪录
          (this.list.length > 0 &&
            nextRowIndex != 1 &&
            this.list[nextRowIndex] &&
            this.list[nextRowIndex]['sort_status'] == '1')) // 下一个商品已分拣
      ) {
        this._fetchData();
      } else {
        const input = document.querySelectorAll('.table-number-input');
        const idx = index ? index : this.activeRow.index + 1;
        for (let i = idx; i < input.length; i++) {
          if (!input[i].disabled) {
            return input[i].focus();
          }
        }
      }
    },

    /**
     * 重置分拣
     */
    resetSort(params) {
      const { row, index } = params;
      let order_commodity_id = row.order_commodity_id;
      sort.resetSort({ order_commodity_id }).then(({ status, message }) => {
        if (status) {
          this.getSortProgress();
          this.successMessage('重置成功！');
          row.sort_status = '0';
          row.sort_status_desc = '未分拣';
          row.actual_amount = '';
          row.isCanReset = false;
          row.isCanMarkStockOut = true;
          row.amount_warning = false;
          this.cancelStockOut(row);
          // this.postList.splice(params.index, 1, this.cloneObj(params.row))
          this.$nextTick(() => {
            this.setFocus(index, false);
          });
          // this._fetchData();
        } else {
          this.errorMessage(message);
        }
      });
    },
    /**
     * 获取分拣进度
     */
    getSortProgress() {
      // this.resetSortProgress()
      const filters = this.curFilters;
      const params = {
        commodity_id: filters.commodity_id,
        delivery_date: filters.delivery_date,
        commodity_name: filters.commodity_name,
        store_id: filters.store_id,
      };
      sort.getSortProgress(params).then((res) => {
        if (res.status) {
          this.sortProcessList = res.data;
        } else {
          this.resetSortProgress();
        }
      });
    },
    /**
     * 重置分拣进度数据
     */
    resetSortProgress() {
      this.sortProcessList = [];
    },
    resetActiveRow() {
      this.activeRow = {
        index: 0,
        event: null,
      };
    },
    saveMfgDate(id, date) {
      let par = {
        order_commodity_id: id,
        produce_date: date,
      };
      sort.submitDate(par).then(({ status, message }) => {
        if (status) {
          this.successMessage('保存生产日期成功！');
        } else {
          this.errorMessage(message);
        }
      });
    },
    /**
     * 获取导出参数
     */
    getExportParams() {
      const params = this._getFilters();
      const temp = this.$refs.listTable.getFilterColumns();
      const headersArray = temp.slice(1).map((item) => item.key);
      const headerStr = headersArray.join(',');
      params.headers = headerStr;
      return params;
    },
    handleResetChange() {
      this.extraFilters.category_id = '';
      this.extraFilters.category_id2 = '';
      this.extraFilters.category_id3 = '';
      this.$refs.listTable.setValue(['category1', 'category2', 'category_id3'], []);
    },
  },
};
</script>

<style scoped lang="less">
.diff-sort {
  /deep/ .warning {
    color: #ff9f00;
  }
  /deep/ .light {
    color: rgba(0, 0, 0, 0.5);
  }
  /deep/ .table-number-input {
    padding: 0 7px;
    &[disabled] {
      opacity: 0.72;
      cursor: not-allowed;
      background-color: #f3f3f3;
    }
  }
  /deep/ .sdp-table__tr.checked {
    .sdp-table__td,
    .warning,
    .light {
      color: #f13130;
      background: #fef6f5;
      font-size: 16px;
      font-weight: 500;
    }
    input {
      border-color: #f13130;
    }
  }

  .process-list {
    height: 52px;
    line-height: 52px;
    background: rgba(250, 251, 252, 0.5);
    border-radius: 1px 1px 0px 0px;
    border: solid #e8e8e8;
    border-width: 1px 1px 0 1px;
    padding: 0 18px;
    .process-item {
      display: flex;
      align-items: center;
    }
    h6 {
      font-size: 13px;
      margin-left: 12px;
    }
    .sorted {
      color: #03ac54;
      margin: 0 4px;
    }
    .sort {
      color: #ff9f00;
      margin: 0 4px;
    }
  }
}
</style>
