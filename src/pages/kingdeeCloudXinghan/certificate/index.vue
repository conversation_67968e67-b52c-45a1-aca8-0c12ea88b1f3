<template>
  <div class="common">
    <ListTable
      ref="listTable"
      :border="false"
      tableId="kingdeeCloudXinghanCertificateListTable"
      :rowClassName="rowClassName"
      :outer-border="true"
      data-provider="/superAdmin/accountingCertificate/certList"
      :filters="{is_external_finance_sync_list: 1,}"
      :columns="columns"
      :before-request="beforeRequest"
      :after-request="afterRequest"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      @on-selection-change="handleSelectionChange"
    >
      <template #before-table>
        <div v-show="selectedRows.length === 0">
          <SyncAllButton
            styleType="btnStyleForAdd"
            class="mr10 d-ilb"
            name="同步筛选条目"
            :syncParams="syncParams"
            totalKey="order_count"
            @done="_handleSyncCompleted"
            @click="_handleSyncAllButtonClick"
          ></SyncAllButton>
          <Button @click="openCertificateCreate">生成凭证</Button>
        </div>
      </template>

      <template #batch-operation>
        <Button @click="$_onSyncSelected">同步选中条目</Button>
      </template>

    </ListTable>
  </div>
</template>
<script>
import ListTable from '@components/list-table';
import Icon from '@components/icon';
import CheckboxGroup from '@components/CheckboxGroup'
import RelationNo from '@/components/relation-no';
import SyncAllButton from "../components/SyncAllButton/index"
import SelectAndInput from '@components/common/SelectAndInput';
import TimeTypeSelect from '@/components/common/TimeTypeSelect.vue';
import StoreSelect from '@components/common/storeSelect_new.vue';
import cloudStarMixin from '../mixins/cloud-star-mixin.js';

import StorageUtil from '@util/storage.js'
import DateUtil from '@/util/date';
import { mapState } from 'vuex';

import { commonVariable, default as tableMixins } from '../mixins/table-mixins.js';

const LOCAL_KEY = 'kingdeeCloudXinghan-certificate-checkbox-state'

export default {
  name: 'kingdeeCloudXinghan-certificate-create',
  mixins: [tableMixins, cloudStarMixin],
  components: {
    ListTable,
    Icon,
    RelationNo,
    CheckboxGroup,
    SyncAllButton,
    SelectAndInput,
    TimeTypeSelect
  },
  data () {
    return {
      creating: false,
      selectedRows: [],
      columns: [
        {
          type: 'titleCfg',
          width: 40,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'accounting_certificate_cert_list',
          style: {
            paddingRight: 0
          }
        },
        {
          type: 'selection',
          fixed: 'left',
          width: 40
        },
        {
          title: '凭证单号',
          key: 'cert_order_no',
          fixed: 'left',
          width: 180,
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.cert_order_no);
          }
        },
        {
          title: '记账日期',
          key: 'sign_date',
          width: 110,
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.sign_date);
          }
        },
        {
          title: '凭证号',
          key: 'cert_no',
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.cert_no);
          }
        },
        {
          title: '摘要',
          key: 'summary',
          minWidth: 160,
        },
        {
          title: '科目编码',
          key: 'subject_no',
          minWidth: 100,
        },
        {
          title: '科目名称',
          key: 'subject_name',
          width: 200,
        },
        {
          title: '辅助核算',
          key: 'auxiliary_approval',
          width: 200,
          render: (h, params) => {
            const { row } = params;
            return h(
              'span',
              { style: 'white-space: pre-line;' },
              (row.auxiliary_approval_list || []).join('\n')
            );
          }
        },
        {
          title: '借方金额',
          key: 'debit_money',
        },
        {
          title: '贷方金额',
          key: 'lender_money',
        },
        {
          title: '总金额',
          key: 'money',
          minWidth: 120,
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.money);
          }
        },
        {
          title: '制单人',
          key: 'create_user',
          minWidth: 80,
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.create_user);
          }
        },
        {
          title: '凭证状态',
          key: 'cert_status',
          minWidth: 90,
          render: (h, params) => {
            const { row } = params
            if(row._isDetail) return ''
            if (row.cert_status == '0') {
              return <span>已生成</span>
            } else {
              return <span class="c-red-color">已删除</span>
            }
          }

        },
        {
          minWidth: 90,
          ...commonVariable.syncColumn,
          render: (h, params) => {
            const { row } = params;
            return row._isDetail ? '' : h('span',row.external_finance_sync_status_name);
          }
        }
      ],
      filterItems: [
        {...commonVariable.syncStatus},
        {
          type: 'custom',
          component: SelectAndInput,
          style: {
            width: '299px'
          },
          // stop: true,
          key: ['searchType', 'searchValue'],
          defaultValue: ['1', ''],
          props: {
            selectDefaultValue: '1',
            selectData: [
              {
                label: '搜索',
                placeholder: '摘要/辅助核算/科目',
                value: '1'
              },
              {
                label: '凭证单号',
                placeholder: '凭证单号',
                value: '2'
              },
            ]
          },
          onChange:value=>{
            return {
              value,
              stop:true
            }
          }
        }
      ],
      advanceItems: [],
      // 同步接口地址
      syncUrl: '/superAdmin/externalFinance/addCertOrder',
      syncParams: {
        listUrl: '/superAdmin/accountingCertificate/certList',
        syncUrl: '/superAdmin/externalFinance/addCertOrder',
        listQuery: {
        }
      },
    }
  },
  computed: {
    ...mapState({
      sysConfig: 'sysConfig',
    }),
  },
  watch: {
    externalFinanceTypeStore() {
      this.initAdvanceItems()
    }
  },
  created () {
    this.initAdvanceItems()
  },
  mounted () {
    this.setOperatorUserList()
  },
  methods: {
    initAdvanceItems() {
      const advanceItems = [
        {
          type: 'custom',
          component: TimeTypeSelect,
          width: 'auto',
          key: ['start_date', 'end_date', 'date_type'],
          defaultValue: [DateUtil.getTodayDate(), DateUtil.getTodayDate(), 1],
          props: {
            data: [
              {
                label: '生成时间',
                value: 1
              }, {
                label: '记账日期',
                value: 2
              }
            ]
          },
        },
        {
          checked: false,
          label: '制单人',
          type: 'Select',
          key: 'create_user',
          data: [],
          props: {
            filterable: true,
            clearable: true,
            'filter-by-label': true,
            placeholder: "请选择制单人"
          }
        },
        {
          checked: true,
          required: true,
          label: '凭证状态',
          type: 'custom',
          component: CheckboxGroup,
          noReset: true,
          key: 'cert_status',
          props: {
            data: [
              {
                label: '已生成',
                value: 0,
              },
              {
                label: '已删除',
                value: 1,
              }
            ]
          },
          defaultValue: StorageUtil.getLocalStorage(LOCAL_KEY) || [0],
          onChange: (value) => {
            // 勾选状态存在本地
            StorageUtil.setLocalStorage(LOCAL_KEY, value)
            return {
              value,
              stop: true
            }
          }
        },
      ];
      if (this.externalFinanceTypeStore) {
        advanceItems.push({
          checked:true,
          type: 'custom',
          component: StoreSelect,
          key: 'store_id',
          label: '仓库',
          props: {
            storageKey: 'kingdee_cloud_xinghan_store_id',
            showAll: false,
            filterable: true,
            clearable: false,
            defaultFirst: true,
            'filter-by-label':true
          }
        })
      }
      this.advanceItems = advanceItems;
    },
    _getParams () {
      let params = this.$refs.listTable && this.$refs.listTable.getParams()
      params = this.paramsFormat(params)
      return params;
    },
    afterRequest(res) {
      const newList = [];
      if (res.data.list) {
        res.data.list.forEach(item => {
          if (item.items) {
            item.items.forEach((detailItem, index) => {
              const _isDetail = index !== 0
              newList.push({
                pid: item.id,
                _isDetail,
                ...item,
                ...detailItem,
                id: index === 0 ? item.id : item.id + detailItem.id,
                _disabled: _isDetail
              });
            })
          }
        });
      }
      res.data.list = newList;
      return res;
    },
    handleSelectionChange (selection) {
      this.selectedRows = selection;
    },
    /**
     * 获取制单人列表
     */
    async setOperatorUserList() {
      let { data, status } = await this.$request.get(this.apiUrl.getOperatorList, {
        page: 1,
        pageSize: 999999
      });
      if (status == 1) {
        let operatorData = data.list.map(item => {
          return {
            label: `${item.user_name}（${item.cn_name}）`,
            value: item.cn_name
          };
        });
        operatorData.unshift({
          value: '',
          label: '全部'
        })
        console.log('operatorData', operatorData)
        this.setAdvanceItemData('create_user', operatorData)
      }
    },
    setAdvanceItemData(key, data) {
      this.advanceItems.forEach(row => {
        if (row.key === key) {
          row.data = data;
        }
      });
    },
    openCertificateCreate() {
      if(Number(this.sysConfig.is_open_accounting_certificate) !== 1) {
        this.$smessage({
          type: 'warning',
          text: '未开通会计凭证'
        })
        return false
      }
      this.jump()

    },
    jump () {
      let routeUrl = this.$router.resolve({
        path: "/appCenter/certificate/create",
      });
      window.open(routeUrl.href, '_blank');
    },
    _handleSyncCompleted () {
      this.$refs.listTable.fetchData();
    },
    _handleSyncAllButtonClick () {
      let unSyncValue = '';
      let find = commonVariable.syncStatus.data.find(item => item.label === '未同步');
      if (find) {
        unSyncValue = find.value;
      }
      this.$refs.listTable.setValue(commonVariable.syncStatus.key, unSyncValue)
    },
    beforeRequest (params) {
      params = this.paramsFormat(params);
      params.storage_id = params.store_id;
      this.syncParams.listQuery = params;
      this.queryParams = this.deepClone(params);
      return params
    },
    paramsFormat (params) {
      if(Array.isArray(params.cert_status)) {
        params.cert_status = params.cert_status.join(',')
      }
      (params.searchType === '1') && (params.search_value = params.searchValue);
      (params.searchType === '2') && (params.cert_order_no = params.searchValue);
      delete params.searchType;
      delete params.searchValue;
      return params
    },
    rowClassName(row) {
      return row._isDetail ? 'row--detail' : ''
    },
  }
}
</script>
<style lang="less" scoped>
.d-ilb {
  display: inline-block;
}
/deep/ .row--detail {
  .sdp-table__td {
    background: #fafbfc;
  }
  .ivu-checkbox-wrapper {
    display: none;
  }
}
</style>
