<!--
 * @Description: 金蝶云星空同步日志
 * @Autor: lizi
 * @Date: 2021-12-06 10:03:36
 * @LastEditors: lizi
 * @LastEditTime: 2022-01-10 09:55:29
 * @FilePath: \sdpbase-pro\src\pages\kingdee\logs.vue
-->
<template>
  <div class="common">
    <ListTable
      ref="listTable"
      :border="false"
      :outer-border="true"
      :before-request="_beforeRequest"
      data-provider="/superAdmin/externalFinance/logList"
      :filter-items="filterItems"
      :advance-items="advanceItems"
      :columns="columns"
      :height="getTableHeight() - 80"
      :pageSizeOpts="pageSizeOpts"
    >

    </ListTable>
  </div>
</template>

<script>
import ListTable from '@components/list-table';
import appRes from '@/api/appCenter.js';
import SLoading from '@components/s-global-loading';
import { get } from '@api/request.js';
import { api } from '@api/api.js';
import StoreSelect from '@components/common/storeSelect_new.vue';
import cloudStarMixin from '../mixins/cloud-star-mixin.js';

export default {
  mixins: [cloudStarMixin],
  components: {
    ListTable
  },
  data () {
    return {
      queryParams: {},
      pageSizeOpts: [20],
      filterItems: [{
        label: '同步结果',
        type: 'Select',
        key: 'sync_status',
        data: [
          {
            label: '全部',
            value: ''
          },
          {
            label: '同步中',
            value: '1'
          },
          {
            label: '同步成功',
            value: '2'
          },
          {
            label: '同步失败',
            value: '3'
          }
        ]
      }, {
        label: '搜索',
        key: 'search_value',
        type: 'Input',
        props: {
          placeholder: '同步内容/失败原因'
        }
      }],
      advanceItems: [{
        items: [{
          label: '同步类型',
          key: 'entity_type',
          type: 'Select',
          data: [{
            label: '全部',
            value: ''
          },{
            label: '客户',
            value: '5'
          }, {
            label: '供应商',
            value: '6'
          },{
            label: '采购员',
            value: '7'
          }, {
            label: '凭证',
            value: '40'
          },]
        }, {
          label: '操作员',
          type: 'Select',
          key: 'operator',
          data: []
        },
        {
          label: '同步方式',
          key: 'sync_type',
          type: 'Select',
          data: [{
            label: '全部',
            value: ''
          }, {
            label: '手动同步',
            value: '1'
          }, {
            label: '自动同步',
            value: '2'
          }]
        },
        {
          label: '同步时间',
          type: 'DatePicker',
          props: {
            type: 'daterange',
            placeholder: '请选择同步时间'
          },
          key: ['start_date', 'end_date']
        },
        {
          checked:true,
          type: 'custom',
          component: StoreSelect,
          key: 'store_id',
          label: '仓库',
          show: true,
          props: {
            storageKey: 'kingdee_cloud_xinghan_store_id',
            showAll: false,
            filterable: true,
            clearable: false,
            defaultFirst: true,
            'filter-by-label':true
          }
        },
        ]
      }],
      columns: Object.freeze([{
        title: '同步ID',
        key: 'id',
        fixed: 'left'
      }, {
        title: '同步内容',
        key: 'content',
        width: '300',
        render: (h, params) => {
          const { content } = params.row;
          const [phone, name] = content.split('===');
          return h('div', [
            h('p', phone),
            h('p', name)
          ])
        }
      }, {
        title: '同步方式',
        key: 'sync_type_name'
      }, {
        title: '同步类型',
        key: 'entity_type_name'
      }, {
        title: '操作人',
        key: 'operator'
      }, {
        title: '同步时间',
        key: 'update_time'

      }, {
        title: '同步结果',
        key: 'sync_status_name'
      }, {
        title: '失败原因',
        key: 'response',
        width: 300,
        render: (h, params) => {
          let { fail_response, sync_status } = params.row;
          let message = '';
          if (+sync_status === 3) {
            let failObj = JSON.parse(fail_response || "{message:''}")
            message = failObj.message;
          }
          return h('span', {
            class: 'hover-span',
          }, message)
        }
      }, {
        title: '操作',
        key: 'action',
        type: 'action',
        actions: (params) => {
          // 3 失败 2成功 1同步中
          const { sync_status, id } = params.row;
          let actions = []
          if (+sync_status == 3) {
            actions.push({
              name: '重试',
              action: () => {
                this.$_onReloadSync(id)
              }
            })
          }
          return actions;
        }

      }])
    }
  },
  watch: {
    externalFinanceTypeStore () {
      this.updateAdvanceItems()
    }
  },
  created () {
    this.getOperatorList();
    this.updateAdvanceItems()
  },

  methods: {
    updateAdvanceItems () {
      if (this.externalFinanceTypeStore) {
        this.advanceItems[0].items[3].show = true;
      } else {
        this.advanceItems[0].items[3].show = false;
      }
    },
    _beforeRequest(params) {
      params.is_external_finance_sync_list = 1;
      this.queryParams = {
        ...params
      };
      return params
    },
    _fetchData (resetPage) {
      this.$refs.listTable && this.$refs.listTable.fetchData(resetPage);
    },
    $_onReloadSync (_id) {
      SLoading.showLoading('同步中...');
      appRes.resaveEntity({ id: _id, store_id: this.queryParams.store_id }).then(res => {
        SLoading.hideLoading();
        if (res.status) {
          if (res.data && res.data.fail_msg) {
            this.errorMessage('同步失败');
          } else {
            this.successMessage('同步成功');
          }
          this._fetchData();
        } else {
          this.errorMessage('同步失败');
        }
      })
    },
    async getOperatorList () {
      let { data, status } = await get(api.getOperatorList, {
        page: 1,
        pageSize: 999999
      });
      if (status == 1) {
        let datas = data.list.map(items => {
          return {
            value: items.id,
            label: items.user_name
          };
        });
        let operatorData = datas;
        operatorData.unshift({
          value: '',
          label: '全部'
        });
        this.advanceItems[0].items[1].data = Object.freeze(operatorData)
      }
    },

  }
}
</script>
<style lang="less">
.hover-span {
  -webkit-line-clamp: 3;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  &:hover{
    -webkit-line-clamp: initial;
  }
}
</style>
