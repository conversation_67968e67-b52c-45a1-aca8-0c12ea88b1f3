<template>
  <div>
    <router-view></router-view>
  </div>
</template>
<script>
import Bus from '@api/bus.js';
import ConfigMixin from '@/mixins/config';

export default {
  name: 'kingdeeCloudXinghan',
  mixins: [ConfigMixin],
  data() {
    return {
			localSubMenu: [
				{
					name: '同步管理',
					children: [
						{
							name: '同步日志',
							path: '/kingdeeCloudXinghan/logs',
							is_active: true,
						},
					]
				},
				{
					name: '基础资料',
					children: [
						{
							name: '往来单位',
							path: '/kingdeeCloudXinghan/sync-unit/user',
							is_active: false,
							path_list: [
								'/kingdeeCloudXinghan/sync-unit/user',
								'/kingdeeCloudXinghan/sync-unit/provider',
								'/kingdeeCloudXinghan/sync-unit/buyer'
							]
						},
					]
				},
				{
					name: '总账',
					children: [{
						name: '凭证',
						path: '/kingdeeCloudXinghan/certificate',
						is_active: false,
						path_list: [
							'/kingdeeCloudXinghan/certificate',
						]
					},]
				}
			],
    };
  },
  watch: {
    'sysConfig.external_finance_system_type'(val) {
      if (val != 5) {
        this.$router.replace('/index')
        return;
      }
    }
  },
  created() {
    if (this.sysConfig.external_finance_system_type != 5) {
      this.$router.replace('/index')
      return;
    }
    this.getPower()
  },
  destroyed () {
    Bus.$emit('update-local-submenu', []);
  },
  methods: {
    getPower() {
      this.$request.get(this.apiUrl.getAdminMenu, {}).then(res => {
        if (res.status) {
          let data = res.data || [];
          let appCenterList = (data.find(item => item.code === 'A012') || {}).son || [];

          appCenterList.forEach(item => {
            if (item.code === 'cloud_xinghan') {
              (item.son || []).forEach(i => {
                if (i.code === 'cloud_xinghan_init') {
                  this.localSubMenu[0].children.push({
                    name: '初始化',
                    path: '/kingdeeCloudXinghan/auth',
                    is_active: false,
                  })
                }
                if (i.code === 'cloud_xinghan_sync_setting') {
                  this.localSubMenu[0].children.push({
                    name: '同步配置',
                    path: '/kingdeeCloudXinghan/sync',
                    is_active: false
                  })
                }
              })
            }
          })
          Bus.$emit('update-local-submenu', this.localSubMenu);
        }
      })
    }
  }
};
</script>
