
<template>
  <div class="kingdeeCloudXinghan-auth">
    <Form
      :label-width="300"
      :model="params"
      :rules="rules"
      ref="formRef">
      <div class="kingdeeCloudXinghan-auth__panel">
        <h3>授权绑定</h3>
        <div class="kingdeeCloudXinghan-auth__panel__form">
            <FormItem
              label="第三方应用系统编码(appId)"
              prop="app_id"
            >
              <Input :disabled="has_accountId" v-model="params.app_id" />
            </FormItem>

            <FormItem
              label="第三方应用AccessToken认证密钥(appSecret)"
              prop="app_secret"
            >
              <Input :disabled="has_accountId" v-model="params.app_secret" />
            </FormItem>
            <FormItem
              label="数据中心id"
              prop="external_system_relevance"
            >
              <Input :disabled="has_accountId" v-model="params.external_system_relevance" />
            </FormItem>
            <FormItem
              label="第三方应用代理用户的用户名"
              prop="username"
            >
              <Input :disabled="has_accountId" v-model="params.username" />
            </FormItem>
            <FormItem
              label="账套地址"
              prop="server_url"
            >
              <Input :disabled="has_accountId" v-model="params.server_url" />
            </FormItem>
            <FormItem>
              <Button styleType="btnStyleForAdd" @click="bindAccount" :loading="authLoading" :disabled="has_accountId">
                确认绑定
              </Button>
            </FormItem>
        </div>
      </div>
    </Form>
  </div>

</template>

<script>
import Button from '@/components/button/index.js';
import StoreSelect from '@components/common/storeSelect_new.vue';
import cloudStarMixin from './mixins/cloud-star-mixin.js';

export default {
  name: 'kingdeeCloudStart-auth',
  mixins: [cloudStarMixin],
  components: {
    Button,
    StoreSelect
  },
  data () {
    return {
      authLoading: false,
      has_accountId: false,
      params: {
        external_system_relevance: '',
        app_id: '',
        app_secret: '',
        username: '',
        server_url: '',
      },
      rules: Object.freeze({
        app_id: [{
          required: true,
          message: '请输入第三方应用系统编码(appId)',
          trigger: 'blur'
        }],
        app_secret: [{
          required: true,
          message: '请输入第三方应用AccessToken认证密钥(appSecret)',
          trigger: 'blur'
        }],
        external_system_relevance: [{
          required: true,
          message: '请输入数据中心id',
          trigger: 'blur'
        }],
        username: [{
          required: true,
          message: '请输入第三方应用代理用户的用户名',
          trigger: 'blur'
        }],
        server_url: [{
          required: true,
          message: '请输入账套地址',
          trigger: 'blur'
        }],
      }),
      accountList: []
    }
  },
  created () {
    this.getAuthStatus();
  },
  methods: {
    validate() {
      let isValid = false
      this.$refs.formRef.validate(valid => {
        isValid = valid
      })
      return isValid
    },
    async bindAccount() {
      if(!this.validate()) return
      this.authLoading = true
      const { status, message } = await this.$request.post(this.apiUrl.kdxhAuth, this.params)
      if(status) {
        this.successMessage('授权成功')
        this.has_accountId = true
      } else {
        this.errorMessage(message || '授权失败')
      }
      this.authLoading = false
    },
    async getAuthStatus() {
      const { status, message, data } = await this.$request.post(this.apiUrl.kdxhAuthStatus)
      if (status) {
        if(data.app_id) {
          this.has_accountId = true
        } else {
          this.has_accountId = false
        }
        this.params = data
      } else {
        this.has_accountId = false
        this.errorMessage(message || '接口获取失败')
      }
    },
  }

}
</script>
<style lang="less" scoped>
.kingdeeCloudXinghan-auth {
  padding: 24px 30px;
  font-family: PingFangHK-Medium, PingFangHK;
  &__panel {
    h3 {
      font-size: 13px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.7);
      line-height: 16px;
      position: relative;
      padding-left: 9.3px;
      &::before {
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        content: '';
        width: 3px;
        height: 11px;
        background: rgba(0, 0, 0, 0.6);
      }
    }
    &__form {
      padding: 18px 9.3px 24px 9.3px;
      width: 800px;
      label {
        float: left;
        padding-right: 16px;
      }
      &__content {
        display: inline-block;
        .ivu-input-wrapper {
          width: 162px;
          margin-right: 12px;
        }
      }
      &--tip {
        margin-top: 12px;
        width: 498px;
        height: 16px;
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5);
        line-height: 16px;
      }
    }
  }
  .mt10 {
    margin-top: 10px;
  }
}
</style>
