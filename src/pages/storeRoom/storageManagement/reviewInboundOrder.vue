<template>
  <div class="flex-box review-in-store">
		<DetailPage
			pageType="edit"
			title="编辑入库单"
			:custom-save-btn="true"
		>
			<Form inline label-colon :label-width="82" :disabled="false">
				<base-block title="基本信息" class="base-info">
					<FormItem label="入库仓库" prop="store_id">
						<movee-select
							url="/superAdmin/StockInManagement/getUserWarehouse"
							:defaultVal="storeId"
							selectFirst="isTrue"
							placeholder="默认仓库"
							style="width: 232px"
							@changeEvent="changeStore"
							needAll="isNot"
							:disabled="true"
						/>
					</FormItem>
					<FormItem label="入库类型">
						<Input
							disabled
							placeholder="其他入库"
							style="width: 232px"
							:value="auditDetail.type_name"
						/>
					</FormItem>
					<FormItem label="单号" style="text-align: left">
						<a :href="jumpUrl" target="_blank"> {{ auditDetail.in_no }}</a>
					</FormItem>
					<FormItem label="单据日期" style="text-align: left">
						<span>{{ auditDetail.create_time }}</span>
					</FormItem>
					<FormItem label="制单人" style="text-align: left">
						<span>{{ auditDetail.operator }}</span>
					</FormItem>
					<FormItem
						v-if="
              sysConfig.modify_store_time == 1 &&
              ['1', '2', '4', '15'].includes(auditDetail.in_type)
            "
						label="入库时间">
						<div style="display: flex;align-items: center">
							<DatePicker
								:value="auditDetail.modify_in_time"
								placeholder="入库时间"
								style="width: 232px"
								format="yyyy-MM-dd HH:mm:ss"
								type="datetime"
								@on-change="handleStoreDateChange"
							></DatePicker>
							<Tooltip
								style="font-weight: 500;position: absolute;right: -6px"
								max-width="170"
								transfer
								placement="top"
								content="不填写时，默认获取单据的审核时间作为入库单的入库时间，手动填写时按照填写的时间作为入库单的入库时间"
							>
								<i
									class="iconfont icon-tishifu icontip"
									style=";position: relative;font-size: 12px;"
								></i>
							</Tooltip>
						</div>
					</FormItem>
					<FormItem
						label="入库原因"
						v-if="
              auditDetail.type_name.includes('其他入库') ||
              auditDetail.type_name.includes('退仓入库')
            ">
						<movee-select
							style="width: 232px"
							:url="apiUrl.getCustomizeFieldList"
							:defaultVal="auditDetail.in_reason"
							:request-data="{
                  customize_type: 10,
                }"
							placeholder="选择入库原因"
							@changeEvent="changeReason"
							needAll="isNot"
						/>
					</FormItem>
					<FormItem
						label="计划交货日期"
						v-if="openUpdatePurchase && +auditDetail.in_type === 1"
					>
						<div style="display: flex;align-items: center">
							<DatePicker
								:value="auditDetail.plan_date"
								placeholder="计划交货日期"
								:disabled="(!auditDetail.is_can_edit_purchase) || (!auditDetail.is_can_sync_receipt)"
								style="width: 232px"
								format="yyyy-MM-dd"
								type="date"
								@on-change="handlePlanDateChange"
							></DatePicker>
							<Tooltip
								v-if="receiptErrInfo"
								style="font-weight: 500;position: absolute;right: -6px"
								max-width="170"
								transfer
								placement="top"
								:content="receiptErrInfo"
							>
								<i
									class="iconfont icon-tishifu icontip"
									style="margin-left: 6px; position: relative;"
								></i>
							</Tooltip>
						</div>
					</FormItem>
					<FormItem label="采购" prop="purchase_type" v-if="openUpdatePurchase && +auditDetail.in_type === 1">
						<PurchaseSelect
							:value="purchaseSelectValue"
							:level="[0, 1, 2, 3, 4, 5]"
							style="width: 232px"
							:change-on-select="false"
							:disabled="(!auditDetail.is_can_edit_purchase) || (!auditDetail.is_can_sync_receipt)"
							use-hotkey
							placeholder="请选择采购类型"
							@on-change="_changePurchase"
						></PurchaseSelect>
						<Tooltip
							v-if="receiptErrInfo"
							style="font-weight: 500;position: absolute;right: -6px"
							max-width="170"
							transfer
							placement="top"
							:content="receiptErrInfo"
						>
							<i
								class="iconfont icon-tishifu icontip"
								style="margin-left: 6px; position: relative;"
							></i>
						</Tooltip>
					</FormItem>
          <FormItem label="抹零金额">{{ auditDetail.reduction_price || '' }}</FormItem>
				</base-block>
				<base-block title="入库单商品清单" class="mt25">
					<Row :gutter="10" type="flex" align="middle">
						<Col v-if="isCanUpdate">
							<SButton  style-type="btnStyleForAdd" @click="addGoods">批量添加</SButton>
						</Col>
						<Col align="right">
							<InputSearch
								v-model="searchName"
								placeholder="请输入商品名称"
								@on-change="handleSearch"
								style="margin-bottom: 1px;"
							></InputSearch>
						</Col>
					</Row>
					<EditableTable
						class="mt-2"
						:columns="columns"
						:data="newOrderList"
						ref="listTable"
						:row-key="rowKey"
						:is-hidden-del="true"
						:is-hidden-add="!isCanUpdate"
						:isShowRecordEditor="true"
						:max-height="getEditTableHeight()"
						:loading="tableLoading"
						:virtualScroll="true"
						:virtualScrollBuff="{ top: 2100, bottom: 2100 }"
						enterAsDown
						@on-insert="onInsert"
						@on-delete="onDelete"
					>
						<template #after-table-right>
							<div class="total-info">
								<p>
									合计数量:
									<span class="table-total-num mr20">{{ totalInStore || 0 }}</span>
                  <template v-if="authority.hasAmountInfoAuth()">
                    合计(含税): &nbsp;&nbsp;<span class="table-total-amount mr20">{{
                      calTotal || 0
                    }}</span>
                  </template>
									<template v-if="input_tax_rate == 1 && authority.hasAmountInfoAuth()">
										合计(不含税):
										<span class="table-total-amount">{{ tax_exclusive || 0 }}</span>
									</template>
								</p>
							</div>
            </template>
            <template #after-table-left>
              <div class="after-table-left-hotkey">
                <SelfIcon icon="tips" :size="12" class="mr6" />
                <span>支持键盘操作，</span>
                <Icon type="ios-arrow-round-back" />
                <Icon type="ios-arrow-round-forward" />
                <span>左右切换，</span>
                <Icon type="ios-arrow-round-up" />
                <Icon type="ios-arrow-round-down" />
                <span>上下换行</span>
                <span v-show="!isUserReturn">，Enter 键新增一行</span>
              </div>
						</template>
					</EditableTable>
				</base-block>
				<base-block title="费用分摊" class="mt25" v-if="isEnableStoreInCost && isInTypeCanAllot">
					<FeeAllot
						class="mt-16"
						type="2"
						ref="allot"
						:maxItems="10"
						:allotType="allotType"
						:beforeAllot="beforeAllot"
						@on-delete="onAllotDelete"
						@on-allot="onAllot"
					/>
				</base-block>
				<base-block title="其他信息" class="mt25 base-info">
					<FormItem label="备注" :label-width="46" style="width: 80%;">
						<Input
              style="width: 100%;"
							v-model="auditDetail.memo"
							type="textarea"
							:autosize="{ minRows: 3, maxRows: 8 }"
              :maxlength="512"
							placeholder="请输入备注信息，长度 < 512"
						/>
					</FormItem>
					<br>
					<FormItem label="附件" :label-width="46">
						<AttachmentUpload v-model="orderFiles" />
					</FormItem>
				</base-block>
			</Form>
			<template #button-between>
				<Button
					class="ml15"
					@click="resetNum"
					v-if="!isUserReturn && !typeAllotIn"
				>重 置</Button
				>
				<Button
					type="success"
					class="ml15"
					@click="checkAuditModal = true"
					:disabled="postList.length === 0"
				>审 核</Button
				>
				<Tooltip content="已有分摊记录，不能保存！">

					<Button
						v-if="hasAllot"
						class="ml15"
						type="primary"
						:loading="saving"
						@click="updateAudit"
						:disabled="postList.length === 0 || hasAllot || saving"
					>保 存</Button
					>
				</Tooltip>
				<Button
					v-if="!hasAllot"
					class="ml15"
					type="primary"
					:loading="saving"
					@click="updateAudit"
					:disabled="postList.length === 0 || saving"
				>保 存</Button
				>
			</template>
		</DetailPage>
    <goods-list-modal
      v-model="showGoodsListModal"
      modalType="storeGoods"
      @on-add="handlerAdd"
      :selectedGoods="newOrderList"
      :isShowStockStoreId="storeId"
      :params="{ store_in_id: storeId }"
      :isShowPrice="authority.hasAmountInfoAuth()"
      ><div></div
    ></goods-list-modal>
    <Modal v-model="checkAuditModal" title="审核提示">
      <p style="line-height: 36px">
        <span
          v-if="
            +sysConfig.is_in_order_sync_receipt === 1 &&
            (+auditDetail.in_type === 1 || +auditDetail.in_type === 12)
          "
        >
          {{
            auditDetail.receipt_err_info
              ? auditDetail.receipt_err_info
              : '对应采购单的收货信息将更新'
          }}，
        </span>
        {{
          allGoodsNumEmpty ? '入库商品的全部数量为0，' : ''
        }}请确认是否审核入库单？
      </p>
      <p>
        共 <b class="greenColor">{{ goodNumber }}</b> 种商品， 入库金额
        <b class="greenColor">{{ calTotal }}</b> 元
      </p>
      <div slot="footer">
        <Button :disabled="auditConfirmDoing" @click="checkAuditModal = false"
          >取 消</Button
        >
        <Button
          type="success"
          :disabled="auditConfirmDoing"
          @click="auditConfirmInBefore"
          >确 定</Button
        >
      </div>
    </Modal>
    <Modal v-model="areaLocationModal.show" title="修改库区库位">
      <ReservoirAreaLocation
        @on-change="handleSelectAreaLocation"
        :data="reservoirData"
        :value="[
          areaLocationModal.item.area_id,
          areaLocationModal.item.location_id,
        ]"
      />
      <div slot="footer">
        <Button @click="areaLocationModal.show = false">取 消</Button>
        <Button type="success" @click="areaLocationModalConfirmIn"
          >确 定</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
import ReservoirAreaLocation from '@components/common/reservoir-area-location';
import NumberInput from '@components/basic/NumberInput';
import moveeSelect from '@components/basic/moveeSelect';
import GoodsListModal from '@components/order/goodsListModal';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import EditableTable from '@/components/editable-table';
import FeeAllot from '@/components/fee-allot';
import Icon from '@/components/icon';
import CommoditySelect from '@/components/common/CommoditySelect';
import LayoutMixin from '@/mixins/layout';
import ConfigMixin from '@/mixins/config';
import GoodsBatchMixin from '@/mixins/GoodsBatch';
import STMixin from './mixins';

import apiUtil from '@/api/util';
import authority from '@/util/authority';
import { getEditTableHeight } from '@/util/common';
import img from '@assets/images/icon/status_icon.png';
import ware from '@api/storeRoom.js';
import DetailPage from '@/components/detail-page';
import SButton from '@/components/button';
import InputSearch from '@/pages/purchase/purchaseOrder_New/components/input-search.vue';
import PurchaseSelect from '@/components/common/purchaseSelect.vue';
import { uniqueId } from 'lodash-es';
import SelfIcon from '@/components/icon';
import {floor} from 'lodash-es'

const isCanUpdateTypes = [
  '1', // 采购入库
  '2', // 其他入库
  '15', // 退仓入库
];

export default {
  mixins: [LayoutMixin, ConfigMixin, GoodsBatchMixin, STMixin],
  components: {
		SelfIcon,
		PurchaseSelect,
		InputSearch,
		DetailPage,
    FeeAllot,
    moveeSelect,
    NumberInput,
    GoodsListModal,
    ReservoirAreaLocation,
    AttachmentUpload,
    EditableTable,
		CommoditySelect,
		SButton
  },
  watch: {
    sysConfig() {
      this.initTableCols();
    },
    $route() {
      // 仅调试使用
      const delIndex = this.$route.query.del_index;
      if (delIndex) {
        this.newOrderList.splice(delIndex, 1);
        this.postList.splice(delIndex, 1);
      }
    }
  },
  data() {
    let that = this;
    const amountKey = 'actual_num';
    const priceKey = 'price';
    const totalPriceKey = 'total_price';
    const inPrice = 'in_price';
    const onPriceChange = (row, value) => {
      row[priceKey] = value;
      const postIndex = this.postList.findIndex((k) => k.id === row.id);
      row[totalPriceKey] = parseFloat(Number(value).mul(row[amountKey])).toFixed(
        this.warehouse_total_price_decimal,
      );
      if (~postIndex) {
        this.postList[postIndex][inPrice] = row[inPrice];
        this.postList[postIndex][priceKey] = row[priceKey];
        this.postList[postIndex][totalPriceKey] = row[totalPriceKey];
        this.postList[postIndex].in_record_no_change = false;
      }
      this.updateBeforeAllotData('cost_before_in_price', row[priceKey], row);
      this.updateBeforeAllotData(
        'cost_before_total_price',
        row[totalPriceKey],
        row,
      );
    };
    return {
			activeRowIndex: 0,
      tableLoading: true,
			rowKey: 'id',
      hasAllot: false,
      allotType: [
        {
          label: '入库金额分配',
          value: '1',
        },
        {
          label: '入库数量分配',
          value: '2',
        },
      ],
      isFixedPrice: localStorage.getItem('isFixedPrice') == 1 ? 1 : 0, // 默认锁定加单金额字段,
      areaLocationModal: {
        item: {},
        show: false,
        selected: [],
        index: -1,
      },
      authority,
      isSaved: false,
      modifiedItemId: new Map(),
      selectLoading: false,
      auditConfirmDoing: false,
      checkAuditModal: false,
      showGoodsListModal: false,
      input_tax_rate: 0,
      goodsNum: 1,
      reservoirData: [],
      columns: [],
			originalColumns: [
				{
					type: 'titleCfg',
					titleType: 'in_order_detail',
					width: 52,
					align: 'center',
					fixed: 'left'
				},
				{
					key: 'index',
					type: 'index',
					width: 50,
					align: 'center',
					fixed: 'left',
					style: {
						padding: '0 10px'
					}
				},
				{
					title: '商品',
					width: 220,
					fixed: 'left',
					key: 'commodity_name',
					render: (h, params) => {
						const { row, index } = params;
						let data = params.row;
						if (!data.commodity_code) {
							return (
								<CommoditySelect
									class="commodity_name"
									commodityName={row.name}
									dataProvider={this.apiUrl.getBaseCommoditys}
									params={{
										unfilter_online: 1,
										un_limit_status: 0
									}}
									trimInput={true}
									commodityIdKey="commodity_id"
									commodityNameKey="name"
									selectedData={this.dataList}
									onOn-change={async (cid) => { await this.goInputNum(cid); this.addOrder(cid, row, index) }}
									onOn-enter={() => this._insertAt(index)}
									style={{ width: '174px' }}
									inputProps={{
										type: 'textarea',
										rows: 1,
										autosize: { minRows: 1, maxRows: 2.2 }
									}}
									slot-type="purchase"
								></CommoditySelect>
							)
						}
						return h('div', [
							h('span', { class: Number(data.num) !== Number(data.actual_num) && 'warning-tip'}, data.commodity_name),
							h(
								'div',
								{
									style: {
										color: '#999999'
									}
								},
								data.commodity_code
							)
						]);
					}
				},
				{
					title: '描述',
					minWidth: 160,
					key: 'commodity_summary'
				},
				{
					width: 80,
					title: '单位',
					key: 'unit'
				},
				{
					title: '转换系数',
					key: 'unit_convert_text',
					width: 120,
				},
				{
					title: '应入库数量',
					key: 'num',
					width: 120,
					render: (h, params) => {
						let data = params.row;
						return h('span', { class: Number(data.num) !== Number(data.actual_num) && 'warning-tip'}, data.num)
					}
				},
				{
					width: 140,
					title: '实际入库数量',
					key: 'actual_num',
					render: (h, params) => {
						let data = params.row,
							me = this,
							actualNum = data.actual_num;

						const postIndex = me.postList.findIndex(k => k.id === params.row.id)

						let dealData = function() {
							if (~postIndex && Number(actualNum) === Number(me.postList[postIndex].actual_num)) {
								let cid = params.row.id
								if (me.modifiedItemId.has(cid)) {
									let item = me.modifiedItemId.get(cid)
									me.changeCommodityStatus(cid, item.index, item.num, params)
								}
								return
							}
							params.row.actual_num = actualNum;
							if (~postIndex) {
								me.postList[postIndex].actual_num = actualNum;
								me.postList[postIndex].price = params.row.price;
								me.postList[postIndex].inPrice = params.row.price;
								me.postList[postIndex].total_price = params.row.total_price;
							}
							me.newOrderList[params.index].actual_num = actualNum;
							me.newOrderList[params.index].price = params.row.price;
							me.newOrderList[params.index].total_price = params.row.total_price;
							me.changeCommodityStatus(
								data.commodity_id,
								params.index,
								actualNum,
								params
							);
						};
						return h(NumberInput, {
							props: {
								value: Number(data.actual_num) || 0,
                min: 0,
                max: 999999999.99,
                precision: 2,
								disabled: (!data.actual_num_input && (+this.auditDetail.in_type === 1 && +me.newOrderList[params.index]._total_price !== 0 ? false : Number(actualNum) > 0)) ||
									this.hasAllot // 有分摊不能编辑
							},
							style: {
								width: '100%'
							},
							class: Number(data.num) !== Number(data.actual_num) && 'warning-tip',
							on: {
								'on-change'(val) {
                  if (~postIndex) {
                    me.postList[params.index].in_record_no_change = false;
                  }
									data.actual_num = val;
									if (+val === 0 && +data.total_price === 0) return;
									else if (+val === 0) {
										// 存着
										console.log('触发存折')
										me.newOrderList[params.index]._total_price = data.total_price;
										me.newOrderList[params.index]._price = data.price;
										me.newOrderList[params.index].total_price = 0;
										me.newOrderList[params.index].actual_num = 0;
										params.row.total_price  = 0;
									} else {
										console.log('触发继续算')
										// 还回去再继续算
										console.log({params})
										// params.row.total_price = me.newOrderList[params.index]._total_price;
										params.row.total_price = me.newOrderList[params.index]._total_price;
										params.row.price = me.newOrderList[params.index]._price || params.row.price;
										if (me.isEnableCheckStockPriceZero) {
											console.log('走1')
											params.row.price = parseFloat(
												Number(params.row.total_price).div(val)
											).toFixed(me.warehouse_unit_price_decimal) || 0;
										}
										if (me.isEnableCheckStockMoneyTwo) {
											console.log('走2')
											params.row.total_price = parseFloat(
												Number(params.row.price).mul(val)
											).toFixed(me.warehouse_total_price_decimal) || 0
										}
										if (me.isEnableCheckStockPrice) {
											if(me.isFixedPrice==1){
												params.row.total_price = parseFloat(
													Number(params.row.price).mul(val)
												).toFixed(me.warehouse_total_price_decimal) || 0
											}else{
												params.row.price = parseFloat(
													Number(params.row.total_price).div(val)
												).toFixed(me.warehouse_unit_price_decimal) || 0;
											}
										}
										params.row.inPrice = parseFloat(
											Number(params.row.total_price).div(val)
										).toFixed(2) || 0;
									}
                  me.newOrderList[params.index].actual_num = params.row.actual_num;
                  me.newOrderList[params.index].price = params.row.price;
                  me.newOrderList[params.index].inPrice = params.row.inPrice;
                  me.newOrderList[params.index].total_price = params.row.total_price;
									me.updateBeforeAllotData('cost_before_total_price', params.row.total_price, params.row);
									me.updateBeforeAllotData('cost_before_in_price', params.row.price, params.row);
									// me.changes(params.row.total_price, params.index);
								},
								'on-focus'(event) {
									event.target.select();
									// me.hightlightRow(params.index, '#03ac54');
								},
								'on-blur': (event) => {
									me.newOrderList[params.index].actual_num_input = false;
                  this.checkPriceDiff(params.row);
									if(~postIndex) {
                    me.postList[postIndex].price = params.row.price;
                    me.postList[postIndex].total_price = params.row.total_price;
                    me.postList[postIndex].actual_num_input = false;
                    me.postList[postIndex].price_diff_warning = params.row.price_diff_warning;
                  }
									dealData();
								},
								'on-enter'(event) {
									dealData();
									me._insertAt(params.index);
								}
							}
						});
					}
				},
				{
					title: '单价',
					key: 'show_in_price',
					minWidth: 160,
					isShow: authority.hasAmountInfoAuth(),
					renderHeader: h => {
						if (!that.isEnableCheckStockPrice || (!([1, 2, 15].indexOf(+this.auditDetail.in_type) > -1)))  return h('div', '单价')
            return <div class="show_price_title" onClick={() => {
              that.isFixedPrice = !that.isFixedPrice ? 1 : 0;
              localStorage.setItem('isFixedPrice', that.isFixedPrice);
            }}>
              单价<i disabled={this.hasAllot} class={that.isFixedPrice ? 'icon-lock' : 'icon-unlock'}></i>
            </div>
					},
					render: (h, { row, index }) => {
						let priceTag = ''
						const { desc, type } = row.price_type  || {}
						if (this.auditDetail.in_type === '1' && +type === 1) {
							if (desc === '~协') {
								priceTag = h('s-icon', {
									props: {icon: 'xieyijiabeigai', size: 16},
									class: 'mr5',
									style: {
										lineHeight: 'inherit',
										color:'#ff6600',
										verticalAlign: '-1px'
									}
								})
							} else if (desc === '协') {
								priceTag = h('s-icon', {
									props: {icon: 'xie', size: 16},
									class: 'mr5',
									style: {
										lineHeight: 'inherit',
										color:'#5367ff',
										verticalAlign: '-1px'
									}
								})
							}
						}
						// 开启审核入库金额
						if (this.isEnableCheckStockPrice) {
							const data = row
							const inputValue = row[priceKey]
							const input =  h(NumberInput, {
								props: {
									value: Number(row[priceKey]).toFixed(this.warehouse_unit_price_decimal) || 0,
									min: 0,
                  max: floor(999999999.9999, this.warehouse_unit_price_decimal),
									disabled: this.hasAllot,
									precision: this.warehouse_unit_price_decimal
								},
								style: {
									width: '80%'
								},
								on: {
									'on-change': (val) => {
										onPriceChange(row, val);
									},
									'on-focus'(event) {
										event.target.select();
									},
									'on-blur': () => {
                    this.checkPriceDiff(row);
										const postIndex = this.postList.findIndex(k => k.id === row.id)
										if (~postIndex) {
											this.postList[postIndex][priceKey] = row[priceKey];
											this.postList[postIndex][inPrice] = row[priceKey];
											this.postList[postIndex][totalPriceKey] = row[totalPriceKey];
											this.postList[postIndex]['_total_price'] = row[totalPriceKey]
										}
										this.newOrderList[index][priceKey] = row[priceKey];
										this.newOrderList[index][totalPriceKey] = row[totalPriceKey];
										this.newOrderList[index]['_total_price'] = row[totalPriceKey]
                    this.newOrderList[index].price_diff_warning = row.price_diff_warning;
									},
									'on-enter'(event) {
										that._insertAt(index);
									}
								}
							})
							const priceTypeConst = this.priceTypeConst;
							const key = Object.keys(priceTypeConst).find(listKey => priceTypeConst[listKey].value === this.priceType);
							const comparePrice = data[key];
							const template = [priceTag, input];
              if (row.price_diff_warning) {
                const priceWarningIcon =	h('Tooltip', {
                  props: {
                    transfer: true,
                    maxWidth: 300,
                    content: row.price_diff_warning
                  }
                }, [
                  h(Icon, {
                    props: {
                      icon: 'tips',
                      size: '13'
                    },
                    style: {
                      marginLeft: '5px',
                      fontSize: '13px',
                      color: 'red',
                      cursor: 'pointer'
                    }
                  })
                ]);
                template.push(priceWarningIcon);
              }
							if (Number(inputValue) > Number(comparePrice)) {
								template.push(
									h(
										'p',
										{
											style: {
												color: 'red',
												fontSize: '12px'
											}
										},
										`大于${priceTypeConst[key].label}！`
									)
								);
							}
							return h('div', template)
						}
						return h('div', [priceTag, h('span', row.price || 0)])
					}
				},
				{
					width: 100,
					title: '金额(元)',
					key: 'total_price',
					isShow: authority.hasAmountInfoAuth(),
          render(h, { row }) {
            return parseFloat(row.total_price || 0).toFixed(2)
          }
				},
				{
					title: '参考价格',
					key: 'refer_price',
					filterMultiple: false,
					width: 150,
					align: 'right',
					renderHeader: h => {
						const priceTypeList = Object.values(this.priceTypeConst);
						let currentPriceType = priceTypeList.find(
							item => (item.value === this.priceType)
						);
						if (!currentPriceType) {
							currentPriceType = priceTypeList[0];
						}
						this.baseValue = currentPriceType.value;
						const dropDownList = priceTypeList.map(item => {
							return h(
								'DropdownItem',
								{
									props: {
										name: item.value
									},
									style: {
										display: 'flex',
										alignItems: 'center'
									}
								},
								[
									h('span', item.label),
									!!item.tip && h(
										'Tooltip',
										{
											props: {
												content: item.tip,
												placement: 'right',
												transfer: true
											},
											on: {
												'on-popper-show': () => {
													$('.ivu-tooltip-popper').css('z-index', '99999');
													$('.ivu-tooltip-inner').css('white-space', 'normal');
												},
												'on-popper-hide': () => {
													$('.ivu-tooltip-popper').css('z-index', '99999');
													$('.ivu-tooltip-inner').css('white-space', 'normal');
												}
											}
										},
										[
											h(Icon, {
												props: {
													icon: 'tips',
													size: '13'
												},
												style: {
													marginLeft: '5px',
													fontSize: '13px',
												}
											}),
										]
									)
								]
							);
						});
						const dropDown = h(
							'Dropdown',
							{
								props: {
									trigger: 'click',
									transfer: true
								},
								on: {
									'on-click': name => {
										this.priceType = name;
										this.handleChangePriceType();
									}
								}
							},
							[
								h('img', {
									style: {
										width: '12px',
										height: '12px',
										marginLeft: '4px',
										marginTop: '2px'
									},
									attrs: {
										src: img
									}
								}),
								h(
									'DropdownMenu',
									{
										slot: 'list'
									},
									dropDownList
								)
							]
						);
						return h('div', [h('span', currentPriceType.label), dropDown]);
					},
					render: (h, params) => {
						const data = params.row;
						// 取选中当前项的key, priceTypeConst的键
						const priceTypeConst = this.priceTypeConst;
						const key = Object.keys(priceTypeConst).find(listKey => priceTypeConst[listKey].value === this.priceType);
						return h('span', {}, data[key]);
					}
				},
				{
					title: '税率',
					width: 80,
					key: 'input_tax_rate',
					render: (h, params) => {
						let { row } = params;
						return h(
							'span',
							(!row.input_tax_rate || row.input_tax_rate === '-') ? '--' : row.input_tax_rate + '%'
						);
					}
				},
				{
					title: '税额',
					width: 80,
          key: 'input_tax',
          isShow: authority.hasAmountInfoAuth(),
					render: (h, params) => {
						let { row } = params;
						return h('span', (!row.input_tax || row.input_tax === '-') ? '--' : row.input_tax);
					}
				},
				{
					title: '金额（不含税）',
					width: 120,
					key: 'total_price_no_tax',
					isShow: authority.hasAmountInfoAuth(),
					render: (h, params) => {
						let { row } = params;
						let tax_exclusive = Number(row.tax_exclusive).toFixed(4);
						return h('span', tax_exclusive ? tax_exclusive : '--');
					}
				},
				{
					title: '库区库位',
					key: 'area_location',
					isShow: apiUtil.getIsOpenStoreMGT(),
					width: 200,
					render: (h, params) => {
						const data = params.row
						if (+data.is_batch) {
							return h('span', {}, [
								h('span', data.area_and_location_name || '-'),
								h('Icon', {
									style: {
										marginLeft: '5px',
										cursor: 'pointer'
									},
									attrs: {
										type: 'ios-create-outline'
									},
									on: {
										'click': () => {
											this.areaLocationModal.show = true;
											this.areaLocationModal.item = data;
											this.areaLocationModal.index = params.index;
										}
									}
								})
							])
						} else {
							return h('span', data.area_and_location_name || '-')
						}
					}
				},
				{
					width: 80,
					title: '状态',
					key: 'in_status_name',
					render: (h, params) => {
						let data = params.row;
						let text = Number(data.actual_num) > 0 ? '已录入' : '未录入';
						return h(
							'span',
							{
								class: {
									greenColor: Number(data.actual_num) > 0
								}
							},
							text
						);
					}
				},
				{
					title: '批次号',
					key: 'batch_no',
					width:150,
					isShow: that.isEnableBatch && that.customBatchNo,
					render: (h, params) => {
						let data = params.row,
							me = this;
						if(data.is_batch==1){
							return h('i-input', {
								props: {
									value: data.batch_no,
									maxlength:12
								},
								style: {
									width: '100%'
								},
								on: {
									'on-change'(event) {
										params.row.batch_no = event.target.value;
									},
									'on-focus'(event) {
										event.target.select();
									},
									'on-blur'() {
										me.postList[params.index].batch_no = params.row.batch_no;
									}
								}
							});
						}else{
							return (<span>-</span>)
						}

					}
				},
				{
					title: '备注',
					width: 164,
					key: 'remarks',
					render: (h, params) => {
						let data = params.row,
							me = this;
						return h('i-input', {
							props: {
								value: data.remarks
							},
							on: {
								'on-change'(event) {
									params.row.remarks = event.target.value;
								},
								'on-blur'() {
                  me.newOrderList[params.index].remarks = params.row.remarks
									const postIndex = me.postList.findIndex(k => k.id === params.row.id)
									if (postIndex !== -1) {
										me.postList[postIndex].remarks = params.row.remarks
									}
								},
								'on-enter': event => {
									this._insertAt(params.index);
								},
							}
						});
					}
				},
				{
					title: '操作',
					key: 'action',
					type: 'action',
					width: 120,
					align: 'center',
					fixed: 'right',
					render: (h, params) => {
						const { row } = params;
						let me = this;
						if (this.isUserReturn || this.typeAllotIn) {
							return h('span', '-');
						}
						return h('div', [
							h(
								'span',
								{
									style: {
										cursor: 'pointer',
										color: '#03AC54'
									},
									on: {
										click: () => {
											if (!this.checkModify('重置')) {
												return;
											}
											const postIndex = me.postList.findIndex(k => k.id === row.id)
											if (~postIndex && me.postList[postIndex].actual_num_input) {
												return;
											}
											let canSet = false
											if ((Number(row.actual_num) === 0 && Number(row.total_price) === 0)) {
												canSet = true
											}
											row.actual_num = row.num?Number(row.num):0;
											row.total_price = row.org_total_price?Number(row.org_total_price):0;
											if (row.actual_num) {
												row.price = (row.total_price / row.actual_num).toFixed(me.warehouse_unit_price_decimal);
											}
											if (~postIndex) {
												const postItem = me.postList.find(k => k.id === row.id)
												postItem.price = row.price;
												postItem.actual_num = row.actual_num;
												postItem.total_price = row.org_total_price;
												postItem._total_price = row.org_total_price;
												me.setModifiedItem(me.postList[postIndex], postIndex, canSet)
												postItem.actual_num_input = true;
												this._syncStoreList()
											}
										}
									}
								},
								'重置'
							),
						]);
					}
				}
			],
      storeId: '',
      goodsId: '',
      searchName: '',
      delivery_type: 1,
      newOrderList: [],
      postList: [],
      remoteList: [],
      newOrderInfo: '',
      auditDetail: {
        type_name: ''
      },
      orderIds: [],
      orderFiles: [],
      saving: false,
			isChangePurchaseData: false,
			purchaseForm: {
				channel_type: '',
				agent_id: '',
				provider_id: ''
			},
		};
  },
  created() {
    this.getConfig();
    this._getReservoirData();
    // this.initTableCols();
    this.getInBoundAudit();
    this.isFixedPrice = localStorage.getItem('isFixedPrice') == 1 ? 1 : 0;
  },
  computed: {
    warehouse_unit_price_decimal () {
      return 4; // 单价固定按4位小数计算（精度需求产品确认的）
    },
    warehouse_total_price_decimal () {
      return 2; // 金额固定按2位小数计算（精度需求产品确认的）
    },
		receiptErrInfo() {
			const tip = '入库单不能修改计划交货日期/供应商/采购员';
			const { receipt_err_info, is_can_sync_receipt }  = this.auditDetail || {}
			if (is_can_sync_receipt) return '';
			// 将第一个逗号之后的字符替换成tip
			return (receipt_err_info || '').replace(/，.*$/, `, ${tip}`);
		},
		// 是否开启审核/反审核更新采购数据
		openUpdatePurchase () {
			return +this.sysConfig.is_in_order_sync_receipt === 1;
		},
		purchaseSelectValue () {
			const { channel_type, agent_id, provider_id } = this.purchaseForm
			const purchaseValue = +channel_type === 1? agent_id : provider_id
			return channel_type ? [String(channel_type), String(purchaseValue)] : []
		},
    totalInStore() {
      const total = this.newOrderList.reduce((total, item) => {
        return total + Number(item.actual_num);
      }, 0);
      return total.toFixed(2);
    },
    isInTypeCanAllot() {
      return [
        1, // 采购入库
        2, // 其他入库
        15, // 退仓入库
      ].includes(+this.auditDetail.in_type);
    },
    goodNumber() {
      let comSet = new Set();
      this.newOrderList.forEach((item) => comSet.add(item.commodity_id));
      return comSet.size;
    },
    /**
     * 所有商品实际入库数量为0
     * @returns {boolean}
     */
    allGoodsNumEmpty() {
      return this.postList.every(
        (item) => !item.actual_num || Number(item.actual_num) === 0,
      );
    },
    /**
     * 是否是客户退货入库单据
     * @returns {boolean}
     */
    isUserReturn() {
      return Number(this.auditDetail.in_type) === 4;
    },
    /**
     * 是否是调拨入库
     * @returns {boolean}
     */
    typeAllotIn() {
      return Number(this.auditDetail.in_type) === 3;
    },
    /**
     * 不含税合计
     * @returns {Number}
     */
    tax_exclusive() {
      let totalNum = 0;
      this.postList.map((d) => {
        if (d.tax_exclusive !== undefined && d.tax_exclusive !== null) {
          totalNum += parseFloat(d.tax_exclusive);
        }
      });
      return parseFloat(totalNum).toFixed(this.warehouse_total_price_decimal);
    },
    calTotal() {
      let totalNum = 0;
      this.postList.map((d) => {
        totalNum += parseFloat(d.total_price || 0);
      });
      return parseFloat(totalNum).toFixed(this.warehouse_total_price_decimal);
    },
    // 跳转连接
    jumpUrl() {
      const types = ['1', '3', '4', '9', '11'];
      const urls = [
        '#/purchase/purchaseOrderDetail',
        '#/storeRoom/commodityACDetail',
        '#/returnDetail',
        '#/process/completion/edit',
        '#/storeRoom/partition/list',
      ];
      console.log(this.auditDetail);
      const { in_type, about_id, about_no } = this.auditDetail;
      let url = urls[types.indexOf(in_type)];
      /**
      <Option :value="0">全部类型</Option>
        <Option :value="1">采购入库</Option>
        about_id
        http://localhost:8089/view#/purchase/purchaseOrderDetail?keep_scroll=1&id=8285

        <Option :value="2">其他入库</Option>
        <Option :value="3">调货入库</Option>
        id
        http://localhost:8089/view#/storeRoom/commodityACDetail?id=1

        <Option :value="4">订单退货</Option>
        id
        http://localhost:8089/view#/returnDetail?id=351

        <Option :value="5">单位转换</Option>
        <Option :value="7">期初入库</Option>
        <Option :value="8">报溢入库</Option>
        <Option :value="9" v-if="isEnableProcess">完工入库</Option>
        about_no
        http://localhost:8089/#/process/completion/edit?is_can_print=false&completion_no=WG20120200001&page=detail

        <Option :value="10">退料入库</Option>
        <Option :value="11">分割入库</Option>
        http://cpzs.sdongpo.com/superAdmin/view#/storeRoom/partition/list
       */
      const index = types.indexOf(in_type);
      if (index === -1) return;
      if (index === 3) {
        url = `${url}?is_can_print=false&completion_no=${about_no}&page=detail`;
      } else if (index === 4) {
        url = `#/storeRoom/partition/list`;
      } else {
        url = `${url}?id=${about_id}`;
      }
      return url;
    },
    isCanUpdate() {
      return isCanUpdateTypes.includes(this.auditDetail.in_type);
    },
  },
  methods: {
    checkPriceDiff(row) {
      const amountKey = 'actual_num';
      const priceKey = 'price';
      const totalPriceKey = 'total_price';

      row.price_diff_warning = '';

      // 数量或金额为0时不检查
      if (+row[amountKey] === 0 || +row[totalPriceKey] === 0) {
        return;
      }

      const priceFixed2 = Number(row[priceKey]).toFixed(2);
      const computedPrice = Number(row[totalPriceKey]).div(row[amountKey]).toFixed(2);

      let unitPriceDecimal = 0;
      const unitPriceDecimalArr = Number(row[priceKey]).toString().split('.');
      if (unitPriceDecimalArr.length > 1) {
        unitPriceDecimal = unitPriceDecimalArr[1].length;
      }

      // row[priceKey]小数位数不大于2时不检查
      if (unitPriceDecimal <= 2) {
        return;
      }

      // 只有 1: 采购入库, 12: 采购入库【联营】,需要做提示
      if (![1, 12].includes(+this.auditDetail.in_type)) {
        return;
      }
      if (priceFixed2 - computedPrice !== 0) {
        row.price_diff_warning = `当前入库单价大于2位小数（${row[priceKey]}），系统将通过「入库金额 ÷ 入库数量」计算对账单价（结果：${computedPrice}），与直接四舍五入（${row[priceKey]}→${priceFixed2}）会存在差异。`
      }
    },
		_insertAt (index) {
      if (!this.isCanUpdate) return;
			if (index === this.newOrderList.length - 1) { // 只在最后一行添加
				this.newOrderList.splice(index + 1, 0, {
					id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
					commodity_id: '',
					commodity_name: '',
					commodity_code: '',
					commodity_summary: '',
					unit: '',
					unit_convert_text: '',
					num: 0,
					actual_num: 0,
					price: 0,
					total_price: 0,
					area_id: '',
					location_id: '',
					area_and_location_name: '',
					in_status_name: '',
					remarks: '',
					batch_no: '',
					is_batch: 0,
					_sortNum: index + 1,
				});
				this._focusGoodsInput(index);
				this._syncStoreList()
			}
		},
		_focusGoodsInput(index) {
			this.activeRowIndex = index;
      if (!this.$refs.listTable)  {
        return;
      }
			const curTr = this.$refs.listTable.$el.querySelector(
				`[data-key="body_tr_${index}"]`,
			);

      if (!curTr) {
        return;
      }

			this.$nextTick(() => {
				let nextTr = curTr.nextElementSibling;

				if (nextTr) {
					nextTr
						.querySelector('.commodity_name')
						.querySelector('textarea')
						.focus();
					this.activeRowIndex = index + 1;
				}
			});
		},
		onInsert (insertedRow, index) {
			this._focusGoodsInput(index);
		},
		onDelete () {
			if (!this.checkModify('删除商品')) {
				// 回滚删除操作
				this.newOrderList = this.deepClone(this.postList);
				return;
			}
			this._syncStoreList()
		},
    changeReason(val) {
      this.auditDetail.in_reason = val;
    },
    checkModify(op = '添加商品') {
      if (this.hasAllot) {
        this.$smessage({
          type: 'error',
          text: `单据已有分摊记录，不允许${op}！`,
        });
        return false;
      }
      return true;
    },
    // 更新分摊前的数据
    updateBeforeAllotData(field, value, row) {
      if (!this.hasAllot) {
        this.postList.forEach((r) => {
          if (r.id === row.id) {
            r[field] = value;
          }
        });
      }
    },
    // 删除分摊重置到初始数据
    onAllotDelete() {
      this.hasAllot = false;
      this.postList = this.postList.map((item) => ({
        ...item,
        price: item.cost_before_in_price,
        total_price: item.cost_before_total_price,
      }));
      this.newOrderList = this.deepClone(this.postList);
    },
    beforeAllot(allotItemInfo) {
      // 增加费用不做限制
      if (allotItemInfo.type === '1') {
        return true;
      }
      const totalPrice = this.newOrderList.reduce((total, item) => {
        return total + Number(item.total_price);
      }, 0);
      const valid = totalPrice - allotItemInfo.price >= 0;
      if (!valid) {
        this.$smessage({
          type: 'error',
          text: '减少费用时分摊金额不能大于合计金额!',
        });
      }
      return valid;
    },
    onAllot(allotItemInfo, allotFn) {
      this.hasAllot = true;
      let allotList = [];
      let allotBaseList = [];
      if (Number(allotItemInfo.costsType) === 1) {
        // 按入库金额分配
        allotBaseList = this.newOrderList.map((item) => item.total_price);
      } else {
        // 按入库数量分配
        allotBaseList = this.newOrderList.map((item) => item.actual_num);
      }
      allotList = allotFn(allotBaseList, allotItemInfo);
      this.newOrderList = this.newOrderList.map((item, index) => {
        item.total_price = Number(allotList[index]).add(Number(item.total_price));
        item.price = (item.total_price.div(item.actual_num)).toFixed(
          this.warehouse_unit_price_decimal,
        );
        item.in_record_no_change = false;
        this.postList[index].in_record_no_change = false;
        this.postList[index].total_price = item.total_price;
        this.postList[index].price = item.price;
        return item;
      });
    },
    getEditTableHeight,
    areaLocationModalConfirmIn() {
      let selectedData = this.areaLocationModal.selected;
      let index = this.areaLocationModal.index;
      const postIndex = this.postList.findIndex(
        (k) => k.id === this.newOrderList[index].id,
      );
      if (~postIndex) {
        this.postList[postIndex].area_id = this.newOrderList[index].area_id =
          selectedData[0].value;
        this.postList[postIndex].location_id = this.newOrderList[
          index
        ].location_id = selectedData[1].value;
        this.postList[postIndex].area_and_location_name = this.newOrderList[
          index
        ].area_and_location_name =
          `${selectedData[0].label}/${selectedData[1].label}`;
      }
      this.areaLocationModal.show = false;
    },
    handleSelectAreaLocation(value, selectedData) {
      this.areaLocationModal.selected = selectedData;
    },
    _getReservoirData() {
      const params = {
        store_id: this.storeId,
      };
      ware.getReservoirList(params).then((res) => {
        const { status, data } = res;
        if (status) {
          this.reservoirData = data;
        }
      });
    },
    // 获取进项税配置 是否展示 税率 税额 小计（不含税）合计（不含税）
    getConfig() {
      this.commonService.getConfig().then((config) => {
        let { input_tax_rate } = config;
        this.input_tax_rate = input_tax_rate;
        if (+input_tax_rate === 0) {
          let originalColumns = this.originalColumns.filter((item) => {
            return (
              item.key !== 'input_tax_rate' &&
              item.key !== 'input_tax' &&
              item.key !== 'tax_exclusive'
            );
          });
          this.originalColumns = originalColumns;
          this.initTableCols();
        } else {
          this.initTableCols();
        }
      });
    },
    initTableCols() {
      let showColumns = this.deepClone(this.originalColumns);
      this.columns = showColumns;
      this.initPriceColumns();
      this.initLossColumn();
      this.initBatchEditColumns({
        columns: showColumns,
        showBatchNo: false,
        showProductionDate: true,
        showExpireDate: true,
        autoSetDate: true,
        nextColumn: 'remarks',
        listDataKey: 'postList',
        forceAutoSetDate: true,
      });
      showColumns.forEach((res) => {
        if (res.key == 'batch_no') {
          res.isShow = this.isEnableBatch && this.customBatchNo;
        }
      });
      this.columns = showColumns.filter((col) => col.isShow !== false);
      console.log(this.columns);
    },
    initLossColumn() {
      let position = this.columns.findIndex(
        (item) => item.key === 'actual_num',
      );
      let column = {
        title: '报损数量',
        key: 'loss_num',
        width: 140,
        render: (h, params) => {
          let data = params.row;
          let key = 'loss_num';
          data[key] = data[key] ? Number(data[key]) : 0;
					const _this = this;
          return h(NumberInput, {
            props: {
              value: data[key],
              min: 0,
              max: 999999999.99,
              precision: 2,
            },
            style: {
              width: '100%',
            },
            on: {
              'on-change': (val) => {
                const postIndex = this.postList.findIndex(
                  (k) => k.id === params.row.id,
                );
                params.row[key] = val;

								if (+postIndex !== -1) {
									this.postList[postIndex][key] = val
								}
              },
              'on-focus': (event) => {
                event.target.select();
              },
							'on-enter'(event) {
								_this._insertAt(params.index);
							}
            },
          });
        },
      };

      if (!this.isUserReturn && !this.typeAllotIn) {
        return false;
      }
      this.columns.splice(position - 1, 0, column);
    },
    initPriceColumns() {
      const amountKey = 'actual_num';
      const priceKey = 'price';
      const totalPriceKey = 'total_price';
      const inPrice = 'in_price';
      const onTotalPriceChange = (params, value) => {
        const postIndex = this.postList.findIndex(
          (k) => k.id === params.row.id,
        );
        params.row[totalPriceKey] = value;
        params.row[priceKey] =
          Number(params.row[amountKey]) === 0
            ? 0
            : (Number(params.row[totalPriceKey]).div(params.row[amountKey])).toFixed(
                this.warehouse_unit_price_decimal,
              );
        if (~postIndex) {
          this.postList[postIndex][priceKey] = params.row[priceKey];
          this.postList[postIndex][inPrice] = params.row[inPrice];
          this.postList[postIndex][totalPriceKey] = params.row[totalPriceKey];
        }
        this.updateBeforeAllotData(
          'cost_before_total_price',
          params.row[totalPriceKey],
          params.row,
        );
        this.updateBeforeAllotData(
          'cost_before_in_price',
          params.row[priceKey],
          params.row,
        );
      };
			const _this = this;
      this.columns.forEach((column) => {
        // 金额
        if (column.key === 'total_price') {
          // 开启审核入库金额
          if (this.isEnableCheckStockPrice) {
            column.render = (h, params) => {
              let row = params.row;
              const postIndex = this.postList.findIndex(
                (k) => k.id === params.row.id,
              );
              return h(NumberInput, {
                props: {
                  value: Number(row[totalPriceKey]).toFixed(
                    this.warehouse_total_price_decimal,
                  ) || 0,
                  min: 0,
                  max: floor(999999999.9999, this.warehouse_total_price_decimal),
                  disabled: this.hasAllot,
                  precision: this.warehouse_total_price_decimal,
                },
                style: {
                  width: '100%',
                },
                on: {
                  'on-change': (val) => {
                     if (~postIndex) {
                      this.postList[postIndex].in_record_no_change = false;
                    }
                    onTotalPriceChange(params, val);
                  },
                  'on-focus'(event) {
                    event.target.select();
                  },
                  'on-blur': () => {
                    this.checkPriceDiff(params.row);
                    if (~postIndex) {
                      this.postList[postIndex][priceKey] = params.row[priceKey];
                      this.postList[postIndex][inPrice] = params.row[priceKey];
                      this.postList[postIndex][totalPriceKey] =
                        params.row[totalPriceKey];
                      this.postList[postIndex]['_total_price'] =
                        params.row[totalPriceKey];
                      this.postList[postIndex].price_diff_warning = params.row.price_diff_warning;
                    }
                    this.newOrderList[params.index][priceKey] =
                      params.row[priceKey];
                    this.newOrderList[params.index][totalPriceKey] =
                      params.row[totalPriceKey];
                    this.newOrderList[params.index]['_total_price'] =
                      params.row[totalPriceKey];
                    this.newOrderList[params.index].price_diff_warning = params.row.price_diff_warning;
                  },
									'on-enter'(event) {
										_this._insertAt(params.index);
									}
                },
              });
            };
          }
        }
      });
    },
    columnHook(h, params, column) {
      if (
        column === 'shelf_life' &&
        (this.auditDetail.type_name === '其他入库' ||
          this.auditDetail.type_name === '退仓入库')
      ) {
        return h('span', params.row.shelf_life);
      }
    },
    resetNum() {
      if (!this.checkModify('重置')) {
        return;
      }
      this.postList.forEach((item, index) => {
        item.actual_num = item.num ? Number(item.num) : 0;
        item.total_price = item.org_total_price
          ? Number(item.org_total_price)
          : 0;
        item._total_price = item.org_total_price || 0;
        if (item.actual_num) {
          item.price = Number(item.total_price)
            .div(item.actual_num)
            .toFixed(this.warehouse_unit_price_decimal);
        }
        this.setModifiedItem(item, index);
        item.actual_num_input = true;
      });
      this.syncList();
    },
    setModifiedItem(item, index, isSetMap = false) {
      // 进入页面后未编辑过实际入库数量
      if (item.actual_num_input === undefined) {
        if (Number(item.in_price) !== item.price || isSetMap) {
          this.modifiedItemId.set(item.id, { index, num: item.actual_num });
        }
      }
      // 编辑实际入库数量
      if (item.actual_num_input === false)
        this.modifiedItemId.set(item.id, { index, num: item.actual_num });
    },
    changeCommodityStatus(
      commodity_id,
      index,
      num,
      rowParams,
      showInfo = true,
    ) {
      // let params = {
      //   in_record_id:rowParams.row.id,
      //   in_id: this.$route.query.id,
      //   commodity_id: commodity_id,
      //   num,
      //   is_lock_in_price:this.isFixedPrice ? 1 : 0
      // };
      // if (this.modifiedItemId.has(rowParams.row.id)) this.modifiedItemId.delete(rowParams.row.id)
      // ware.editStockInNum(params).then(res => {
      //   if (res.status) {
      //     // this.newOrderList[index].isNew = false;
      //     rowParams.row.isNew = false;
      //     if (showInfo) {
      //       this.$Message.success('修改成功');
      //     }
      //   } else {
      //     const postIndex = this.postList.findIndex(k => k.id === rowParams.row.id)
      //     this.newOrderList[index].total_price = 0;
      //     this.newOrderList[index].actual_num = 0;
      //     rowParams.row.actual_num = 0;
      //     rowParams.row.total_price = 0;
      //     if (~postIndex) {
      //       this.postList[postIndex].actual_num = 0;
      //       this.postList[postIndex].total_price = 0;
      //     }
      //     this.$Modal.error({
      //       title: '错误',
      //       content: res.message
      //     });
      //   }
      //   this.originData = this.cloneObj(this.newOrderList);
      // });
    },
    handleSearch() {
      // this.newOrderList = this.postList;
      this.newOrderList = this.search(this.postList, {
        commodity_name: this.searchName,
      });
      // this.syncList();
    },
    search(data, argumentObj) {
      let res = data;
      let dataClone = data;
      for (let argu in argumentObj) {
        if (argumentObj[argu].length > 0) {
          res = dataClone.filter((d) => {
            return d[argu].indexOf(argumentObj[argu]) > -1;
          });
          dataClone = res;
        }
      }
      return res;
    },
    getCommoditys(isUpdate) {
      let commoditys = [];
      this.postList.filter(item => item.commodity_id).forEach((item) => {
        let obj = {
          in_record_id: item.id || '0',
          purchase_order_com_id: item.purchase_order_com_id,
          id: item.commodity_id,
          num: item.num || 0,
          actual_num: item.actual_num || 0,
          in_price: item.price || 0,
          total_price: item.total_price,
          batch_no: item.batch_no,
          remarks: item.remarks,
          cost_before_in_price: item.cost_before_in_price,
          cost_before_total_price: item.cost_before_total_price,
          loss_num: item.loss_num,
          in_record_no_change: item.in_record_no_change
        };
        if (isUpdate === true) {
          // obj.num = obj.actual_num;
        }
        if (this.isEnableBatch) {
          obj = {
            ...obj,
            ...this.getBatchInfo(item),
            area_id: item.area_id || 0,
            location_id: item.location_id || 0,
          };
        }
        obj.batch_id = '';
        commoditys.push(obj);
      });
      return JSON.stringify(commoditys);
    },
    getTotal() {
      let totalNum = 0;
      this.postList.map((d) => {
        totalNum += parseFloat(d.total_price);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    removeGoods(goods) {
      let itemKey = 'id';
      if (this.modifiedItemId.has(goods[itemKey]))
        this.modifiedItemId.delete(goods[itemKey]);
      this.newOrderList.splice(
        this.newOrderList.findIndex(
          (findItem) => findItem[itemKey] === goods[itemKey],
        ),
        1,
      );
      this.postList.splice(
        this.postList.findIndex(
          (findItem) => findItem[itemKey] === goods[itemKey],
        ),
        1,
      );
      // this.originData.splice(
      //   this.originData.findIndex(
      //     findItem => findItem[itemKey] === goods[itemKey]
      //   ),
      //   1
      // );
    },
    isInvalidDelete() {
      if (!this.disableDeleteItems || this.disableDeleteItems.length === 0) {
        return false;
      }
      const invalidItems = [];
      this.disableDeleteItems.forEach(item => {
        const postItem = this.postList.find(postItem => postItem.commodity_id === item.commodity_id);
        if (!postItem || postItem.isNew) {
          invalidItems.push(`${item.commodity_name}(${item.unit})`);
        }
      });
      if (invalidItems.length > 0) {
        this.$smodal({
          title: '确认',
          text: `商品：${invalidItems.join('；')} 数据异常，请刷新后重试！`,
          type: 'warning',
          btns: 1,
        })
        return true;
      }
      return false;
    },
    updateAudit(type, needAudit = false) {
      if (this.isInvalidDelete()) {
        return;
      }
      let costs;
      if (this.$refs.allot) {
        costs = JSON.stringify(this.$refs.allot.getAllotItems());
      }
      let params = {
        costs,
        in_id: this.$route.query.id,
        in_no: this.auditDetail.in_no,
        in_reason: this.auditDetail.in_reason,
        in_type: +this.auditDetail.in_type,
        //  === 1 ? 1 : 2,
        memo: this.auditDetail.memo,
        total_price: this.getTotal(),
        commodity_list: this.getCommoditys(true),
        is_lock_in_price: this.isFixedPrice ? 1 : 0,
        order_files_link: this.orderFiles.map((item) => item.url).join(','),
        modify_in_time: this.auditDetail.modify_in_time,
				plan_date: this.auditDetail.plan_date,
				...this.purchaseForm
      };
      if (!type) {
        this.modifiedItemId.forEach((item, id) => {
          this.changeCommodityStatus(
            id,
            item.index,
            item.num,
            { row: this.newOrderList[item.index] },
            false,
          );
        });
      }
      this.saving = true;

			const updateHandle = async () => {
				const res = await ware.updateAudit(params);
				if (res.status) {
					this.modifiedItemId.set('save', true);
					if (needAudit) {
						if (this.isHasRepeatGood()) {
							// 存在重复商品需要去拉详情数据, 拿到最新的 id 作为 in_record_id 传给后端
							await this.getInBoundAudit();
						}
						this.auditConfirmIn();
					} else {
						this.successMessage('保存成功');
						this.goBack();
					}
				} else {
					this.$Modal.error({
						title: '错误',
						content: res.message,
					});
				}
				this.saving = false;
			}

			// 如果修改了采购类型或者计划交货日期, 弹窗确认提示
			if (this.isChangePurchaseData) {
				this.$Modal.confirm({
					title: '提示',
					content: '采购类型及计划交货日期不会保存，是否确认？',
					onOk: () => {
						setTimeout(async () => {
							await updateHandle()
						}, 600);
					},
					onCancel: () => {
						this.saving = false;
					}
				})
			} else {
				setTimeout(async () => {
					await updateHandle()
				}, 600);
			}
    },
    /**
     * @description: 判断是否有重复商品
     * @return {Boolean}
     */
    isHasRepeatGood() {
      const set = new Set();
      for (const item of this.postList) {
        if (set.has(item.commodity_id)) {
          return true;
        }
        set.add(item.commodity_id);
      }
      return false;
    },
    /**
     * @description: 审核前置操作
     * @return {*}
     */
    async auditConfirmInBefore() {
      this.auditConfirmDoing = true;
      if (this.originData.length !== this.postList.length) {
        // 做了新增操作 需要先保存，再查详情，再审核
        await this.updateAudit('update', true);
        this.auditConfirmDoing = false;
      } else {
        this.auditConfirmIn();
      }
    },
    auditConfirmIn() {
      let me = this;
      if (
        me.postList.filter(
          (item) => +item.total_price !== 0 && +item.actual_num === 0,
        ).length
      ) {
        this.errorNotice('请输入实际入库数量');
        this.checkAuditModal = false;
        return;
      }
      this.$Modal.confirm({
        title: '提示',
        content: '是否确定审核？',
        onCancel: () => {
          this.auditConfirmDoing = false;
        },
        onOk: () => {
          let commoditys = [];
          me.postList.forEach((item) => {
            let obj = {
              in_record_id: item.id,
              id: item.commodity_id,
              actual_num: item.actual_num || 0,
              remarks: item.remarks,
              loss_num: item.loss_num,
              batch_no: item.batch_no,
              total_price: item.total_price,
              in_price: item.price || item.in_price || 0,
              cost_before_in_price: item.cost_before_in_price,
              cost_before_total_price: item.cost_before_total_price,
              in_record_no_change: item.in_record_no_change,
            };
            if (this.isEnableBatch) {
              obj = {
                ...obj,
                ...this.getBatchInfo(item),
                area_id: item.area_id || 0,
                location_id: item.location_id || 0,
              };
            }
            obj.batch_id = '';
            commoditys.push(obj);
          });
          let costs;
          if (this.$refs.allot) {
            costs = JSON.stringify(this.$refs.allot.getAllotItems());
          }
          let params = {
            costs,
            memo: this.auditDetail.memo,
            in_id: me.$route.query.id,
            in_reason: this.auditDetail.in_reason,
            commodity_list: JSON.stringify(commoditys),
            is_lock_in_price: this.isFixedPrice ? 1 : 0,
            modify_in_time: this.auditDetail.modify_in_time,
            order_files_link: this.orderFiles.map((item) => item.url).join(','),
						plan_date: me.auditDetail.plan_date,
						...me.purchaseForm
          };
          ware.auditConfirmIn(params).then((res) => {
            if (res.status) {
              me.successMessage('审核成功');
              me.modifiedItemId.clear();
              this.modifiedItemId.set('save', true);
              me.checkAuditModal = false;
              me.auditConfirmDoing = false;
              me.goBack();
            } else {
              me.$Modal.error({
                title: '错误',
                content: res.message,
              });
              me.auditConfirmDoing = false;
            }
          });
        },
      });
    },
    addGoods() {
      if (!this.checkModify('添加商品')) {
        return;
      }
      this.showGoodsListModal = true;
    },
    goBack() {
      this.router.go(-1);
    },
    isGoodsExist(goodsId) {
      return this.postList.some((item) => item.commodity_id === goodsId);
    },
		addOrder(cid, row, index) {
			// 商品没有变更，return
			if (cid === row.commodity_id) return
			if (!this.checkModify()) {
				return;
			}
			let me = this;
			if (this.goodsId) {
				if (!this.isGoodsExist(this.goodsId)) {
					this.addInfo(cid, row, index);
				} else {
					this.$Modal.error({
						title: '提示',
						content: '您已经添加过该商品了',
						onOk: () => {
						}
					});
				}
			} else {
				this.$Modal.error({
					title: '提示',
					content: '请选择商品',
					onOk: () => {

					}
				});
			}
		},
    recordGoods(type, ids) {
      if (type === 'single') {
        this.newOrderList.unshift(this.cloneObj(this.newOrderInfo));
      } else {
        this.newOrderList = this.cloneObj(this.postList);
        ids.forEach((item) => {
          this.orderIds.unshift(item);
        });
      }
      // this.originData = this.cloneObj(this.newOrderList);
      // let me = this;
      // let params = {
      //   in_id: this.$route.query.id,
      //   in_no: this.auditDetail.in_no,
      //   in_type: 2,
      //   memo: this.auditDetail.memo,
      //   total_price: this.getTotal(),
      //   commodity_list: this.getCommoditys()
      // };
      // ware.updateAudit(params).then(res => {
      //   if (res.status) {
      //     if (type === 'single') {
      //       this.newOrderList.unshift(this.cloneObj(this.newOrderInfo));
      //     } else {
      //       this.newOrderList = this.cloneObj(this.postList);
      //       ids.forEach(item => {
      //         me.orderIds.unshift(item);
      //       });
      //     }
      //     this.originData = this.cloneObj(this.newOrderList);
      //   } else {
      //     if (type === 'single') {
      //       this.postList.splice(0, 1);
      //       this.orderIds.splice(0, 1);
      //     } else {
      //       for (let i = 0; i < type; i++) {
      //         this.postList.splice(i, 1);
      //       }
      //     }
      //     this.$Modal.error({
      //       title: '错误',
      //       content: res.message
      //     });
      //   }
      // });
    },
    addInfo(cid, row, index) {
      this.orderIds[index] = cid;
      this.newOrderInfo['actual_num'] = this.goodsNum;
			this.newOrderInfo['commodity_summary'] = this.newOrderInfo['summary'];
      this.newOrderInfo['total_price'] = parseFloat(
        this.newOrderInfo['price'] * this.goodsNum,
      ).toFixed(this.warehouse_total_price_decimal);

      this.newOrderInfo['_total_price'] = this.newOrderInfo['total_price'];
      const inputTaxRate = +this.newOrderInfo['input_tax_rate'];
      this.newOrderInfo['tax_exclusive'] = inputTaxRate
        ? this.newOrderInfo['total_price'] / (1 + inputTaxRate / 100)
        : this.newOrderInfo['total_price'];
      this.newOrderInfo['input_tax'] = inputTaxRate
        ? parseFloat((this.newOrderInfo['tax_exclusive'] * inputTaxRate) / 100)
        : '';
      this.newOrderInfo['batch_no'] = this.newOrderInfo['budget_batch_no'];

			console.log(JSON.parse(JSON.stringify(this.newOrderInfo)), 'this.newOrderInfo');
			this.newOrderList[index] = this.postList[index] = this.cloneObj({
				...this.newOrderInfo,
				[this.rowKey]: this.newOrderList[index][this.rowKey],
			});
			console.log(JSON.parse(JSON.stringify(this.newOrderList)), 'this.newOrderList', this.postList);
			this._syncStoreList()
      this.goodsId = '';
      this.goodsNum = 1;
    },
		/**
		 * @description: 同步 newOrderList 和 postList 数据
		 */
		_syncStoreList () {
			const postListCopy = this.cloneObj(this.newOrderList)
			postListCopy.forEach(goods => {
				const storeGoods = this.postList.find(storeGoods => {
					return (storeGoods && storeGoods[this.rowKey]) === goods[this.rowKey]
				})
				if (storeGoods) Object.assign(goods, storeGoods)
			})
			this.$set(this, 'newOrderList', postListCopy)
			// this.newOrderList = postListCopy
			// 如果搜索框有值，不同步到postList防止搜索后数据不一致
			if (!this.searchName) {
				this.postList = this.cloneObj(postListCopy)
			}
		},
    syncList() {
      this.newOrderList = this.newOrderList.map((item) => {
        const postItem = this.postList.find(
          (postItem) => postItem.id === item.id,
        );
        if (postItem) {
          return {
            ...postItem,
          };
        }
        return item;
      });
    },
		handlerAdd(orders) {
			let tempIds = [];
			if (orders) {
				orders = orders.filter(good => (
					!this.isGoodsExist(good.id)
				))
				orders.forEach(item => {
					item.batch_no = item.budget_batch_no
					item.price = item.average_price;
					item.inPrice = item.in_price;
					item.commodity_name = item.name;
					item.in_no = item.commodity_code;
					item.actual_num = Number(item.amount);
					item.num = 0;
					item.isNew = true;
					item.commodity_id = item.id;
					item.total_price = Number(item.average_price) * Number(item.amount);
					item.area_id = item.default_area_id
					item.location_id = item.default_location_id
					item._total_price = item.total_price
					this.newOrderList.unshift(item);
					tempIds.unshift(item.commodity_id);
				});
			}
			this.initBatchData(this.postList);
			this._syncStoreList();
		},
		async goInputNum(goodsId) {
			this.goodsId = goodsId;
			if (this.goodsId) {
				const res = await ware.getCommodityInfo({
					commodity_id: this.goodsId,
					store_id: this.storeId
				})

				if (res.status && res.data) {
					const commodity = res.data.commodity
					console.log('xxxxxxxccccccc', commodity);
					this.newOrderInfo = this.cloneObj(commodity);
					this.newOrderInfo['price'] = Number(
						commodity.average_price
					);
					this.newOrderInfo['inPrice'] = Number(
						commodity.average_price
					);
					this.newOrderInfo['commodity_name'] = commodity.name;
					this.newOrderInfo['out_no'] = commodity.commodity_code;
					this.newOrderInfo['num'] = 0;
					this.newOrderInfo['isNew'] = true;
					this.newOrderInfo.area_id = commodity.default_area_id
					this.newOrderInfo.location_id = commodity.default_location_id
				}
			}
		},
    remoteSearch(query) {
      if (query !== '') {
        this.selectLoading = true;
        ware.getBaseCommoditys({ query, unfilter_online: 1 }).then((res) => {
          if (res.status) {
            if (res.data) {
              this.remoteList = res.data.commodities;
            }
            this.selectLoading = false;
          }
        });
        let dropDownDom = this.$refs.selectInput.$el.querySelector(
          '.ivu-select-dropdown',
        );
        dropDownDom.style.display = '';
        dropDownDom.querySelector('li').className += 'ivu-select-item-selected';
      } else {
        this.remoteList = [];
      }
    },
    changeStore(val) {
      this.storeId = val;
      this._getReservoirData();
    },
    async getInBoundAudit() {
      this.disableDeleteItems = [];
      let me = this;
      const res = await ware.getInBoundAudit({ id: this.$route.query.id });
      if (res.status) {
        this.orderFiles = res.data.store_in.order_files_link || [];
        const store_in_record = res.data.store_in_record;
        store_in_record.forEach(item => {
          item.price_diff_warning = '';
        })
        this.initBatchData(store_in_record);
        this.newOrderList = store_in_record;
				const auditDetail = res.data.store_in;
				const { channel_type, agent_id, provider_id} = auditDetail
				this.purchaseForm = {
					channel_type,
					agent_id,
					provider_id
				};

				this.newOrderList.forEach((item) => {
          item.in_record_no_change = true;
					if (+auditDetail.in_type !== 2 && +auditDetail.in_type !== 15) {
						item.hideDel = true;
            this.disableDeleteItems.push({
              ...item
            });
					}
          item.total_price = item.model_total_price;
          if (Number(item.is_updated) === 0) {
            item.actual_num = item.num;
          }
          if (Number(item.actual_num) === 0) {
            item.total_price = 0;
          }
          item.isNew = false;
          item.price  = item.model_in_price;
          item.inPrice = item.in_price;
          item.amount = Number(item.actual_num);
          item._total_price = item.total_price;
          item._org_total_price = item.total_price;
          me.orderIds.push(item.commodity_id);
        });
        const { in_status, in_time, modify_in_time } = res.data.store_in || {};
        // 入库状态 in_status 【未入库: 1, 已入库: 2】
        this.auditDetail = {
          ...res.data.store_in,
          modify_in_time: +in_status === 2 ? in_time : modify_in_time,
        };

        this.storeId = this.auditDetail.store_id;
        this.postList = this.cloneObj(this.newOrderList);
        this.originData = this.cloneObj(this.newOrderList);
        // this.$watch('orderRemark', function(){this.modified = true}, {deep: true});
        // this.$watch('postList', function(){this.modified = true}, {deep: true});
        this.initTableCols();
        this.tableLoading = false;
      } else {
        this.tableLoading = false;
        this.$Modal.error({
          title: '错误',
          content: res.message,
        });
      }
    },
    /**
     * @description: 删除页面新增的一个商品
     * @param {*} index
     * @return {*}
     */
    deleteOne(index) {
      this.orderIds.splice(index, 1);
      this.newOrderList.splice(index, 1);
      this.postList.splice(index, 1);
    },
    handleStoreDateChange(val) {
      this.auditDetail.modify_in_time = val;
    },
		checkCanSyncReceipt() {
			this.isChangePurchaseData = true;
			const { is_can_sync_receipt, receipt_err_info } = this.auditDetail
			if (!is_can_sync_receipt && receipt_err_info) {
				this.$Message.warning(receipt_err_info)
			}
		},
		handlePlanDateChange (val) {
			this.checkCanSyncReceipt();
			this.auditDetail.plan_date = val
		},
		_changePurchase (value) {
			this.checkCanSyncReceipt();
			if (value && value.length) {
				const [purchaseType, purchaseValue] = value
				this.purchaseForm.channel_type = purchaseType
				if (+purchaseType === 1) {
					this.purchaseForm.agent_id = purchaseValue
					this.purchaseForm.provider_id = ''
				} else {
					this.purchaseForm.provider_id = purchaseValue
					this.purchaseForm.agent_id = ''
				}
			} else {
				this.purchaseForm.channel_type = ''
				this.purchaseForm.agent_id = ''
				this.purchaseForm.provider_id = ''
			}
		},
  },
  beforeRouteLeave(to, from, next) {
    let isChange = this.newOrderList.find((item) => {
      return (
        Number(item.price) !== Number(item.show_in_price) ||
        Number(item.total_price) !== Number(item._org_total_price)
      );
    });
    if (
      !this.modifiedItemId.get('save') &&
      (this.modifiedItemId.size || isChange)
    ) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function () {
          next();
        },
        onCancel: function () {
          next(false);
        },
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
};
</script>

<style lang="less" scoped>
.flex-box {
  display: flex;
  flex-direction: column;
  .content-box {
    flex: 1;
    overflow-y: auto;
  }
  .btn-box {
    flex: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #eee;
    background-color: #fff;
  }
}
.batchAddBtn {
  position: absolute;
  top: 2px;
  right: 10px;
}
/deep/ .warning-tip {
  color: red;
  .ivu-input-number-input {
    color: red;
  }
  input {
    color: red!important;;
  }
}
.total-info {
	font-weight: 500;
	font-size: 13px;
	color: rgba(0,0,0,0.85);
	p {
		>span {
			color: #FF6E00;
		}
	}
}
/deep/.after-table {
	padding: 13px 24px;
}
/deep/.ivu-input-disabled {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
/deep/.ivu-select-disabled .ivu-select-selection {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
/deep/ .sdp-table__cell {
  .show_price_title {
    display: flex;
    align-items: center;
    cursor: pointer;
    .icon-lock, .icon-unlock {
      display: inline-block;
      width: 13px;
      height: 13px;
      margin-left: 4px;
    }
  }
  .icon-lock {
    background: url(./images/icon-lock.png);
    background-size: 100% 100%;
  }
  .icon-unlock {
    background: url(./images/icon-unlock.png);
    background-size: 100% 100%;
  }
}
</style>
