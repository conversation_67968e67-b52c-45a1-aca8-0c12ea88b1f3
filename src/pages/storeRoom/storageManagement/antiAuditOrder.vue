/* * @Description:
反审核页面(内容与编辑页面基本一致，为考虑后续加功能，暂独立文件) * @Autor: lizi
* @Date: 2021-12-07 10:33:31 * @LastEditors: lizi * @LastEditTime: 2021-12-07
10:38:35 */
<template>
  <div class="basePadding anti-audit-in-store" >
		<DetailPage
			v-if="auditDetail"
			pageType="edit"
			title="反审核入库单"
			:custom-save-btn="true"
		>
			<Form inline label-colon :label-width="82" :disabled="false">
				<base-block title="基本信息" class="base-info">
					<FormItem label="入库仓库" prop="store_id">
						<movee-select
							url="/superAdmin/StockInManagement/getUserWarehouse"
							:defaultVal="storeId"
							selectFirst="isTrue"
							placeholder="默认仓库"
							style="width: 232px"
							@changeEvent="changeStore"
							needAll="isNot"
							:disabled="true"
						/>
					</FormItem>
					<FormItem label="入库类型">
						<Input
							disabled
							placeholder="其他入库"
							style="width: 232px"
							:value="auditDetail.type_name"
						/>
					</FormItem>
					<FormItem label="单号" style="text-align: left">
						<a :href="jumpUrl" target="_blank"> {{ auditDetail.in_no }}</a>
					</FormItem>
					<FormItem label="单据日期" style="text-align: left">
						<span>{{ auditDetail.create_time }}</span>
					</FormItem>
					<FormItem label="制单人" style="text-align: left">
						<span>{{ auditDetail.operator }}</span>
					</FormItem>
					<FormItem
						v-if="
              sysConfig.modify_store_time == 1 &&
              ['1', '2', '4', '15'].includes(auditDetail.in_type)
            "
						label="入库时间">
						<div style="display: flex;align-items: center">
							<DatePicker
								:value="auditDetail.modify_in_time"
								placeholder="入库时间"
								style="width: 232px"
								format="yyyy-MM-dd HH:mm:ss"
								type="datetime"
								@on-change="handleStoreDateChange"
							></DatePicker>
							<Tooltip
								style="font-weight: 500;position: absolute;right: -6px"
								max-width="170"
								transfer
								placement="top"
								content="不填写时，默认获取单据的审核时间作为入库单的入库时间，手动填写时按照填写的时间作为入库单的入库时间"
							>
								<i
									class="iconfont icon-tishifu icontip"
									style="left: -4px;position: relative;font-size: 12px;"
								></i>
							</Tooltip>
						</div>
					</FormItem>
					<FormItem
						label="入库原因"
						v-if="
              auditDetail.type_name.includes('其他入库') ||
              auditDetail.type_name.includes('退仓入库')
            ">
						<movee-select
							style="width: 232px"
							:url="apiUrl.getCustomizeFieldList"
							:defaultVal="auditDetail.in_reason"
							:request-data="{
                  customize_type: 10,
                }"
							placeholder="选择入库原因"
							@changeEvent="changeReason"
							needAll="isNot"
						/>
					</FormItem>
					<FormItem
						label="计划交货日期"
						v-if="openUpdatePurchase && +auditDetail.in_type === 1"
					>
						<div style="display: flex;align-items: center">
							<DatePicker
								:value="auditDetail.plan_date"
								placeholder="计划交货日期"
								:disabled="(!auditDetail.is_can_edit_purchase) || (!auditDetail.is_can_sync_receipt)"
								style="width: 232px"
								format="yyyy-MM-dd"
								type="date"
								@on-change="handlePlanDateChange"
							></DatePicker>
							<Tooltip
								v-if="receiptErrInfo"
								style="font-weight: 500;position: absolute;right: -6px"
								max-width="170"
								transfer
								placement="top"
								:content="receiptErrInfo"
							>
								<i
									class="iconfont icon-tishifu icontip"
									style="margin-left: 6px; position: relative;"
								></i>
							</Tooltip>
						</div>
					</FormItem>
					<FormItem label="采购" prop="purchase_type" v-if="openUpdatePurchase && +auditDetail.in_type === 1">
						<PurchaseSelect
							style="width: 232px"
							:level="[0, 1, 2, 3, 4, 5]"
							:value="purchaseSelectValue"
							:change-on-select="false"
							:disabled="(!auditDetail.is_can_edit_purchase) || (!auditDetail.is_can_sync_receipt)"
							use-hotkey
							placeholder="请选择采购类型"
							@on-change="_changePurchase"
						></PurchaseSelect>
						<Tooltip
							v-if="receiptErrInfo"
							style="font-weight: 500;position: absolute;right: -6px"
							max-width="170"
							transfer
							placement="top"
							:content="receiptErrInfo"
						>
							<i
								class="iconfont icon-tishifu icontip"
								style="margin-left: 6px; position: relative;"
							></i>
						</Tooltip>
					</FormItem>
          <FormItem label="抹零金额">{{ auditDetail.reduction_price || '' }}</FormItem>
				</base-block>
				<base-block title="入库单商品清单" class="mt25">
					<Row :gutter="10" type="flex" align="middle">
						<Col v-if="isCanUpdate && authority.hasAuthority('A00400600801')" >
							<SButton style-type="btnStyleForAdd" @click="addGoods">批量添加</SButton>
						</Col>
						<Col align="right">
							<InputSearch
								v-model="searchName"
								placeholder="请输入商品名称"
								@on-change="handleSearch"
                style="margin-bottom: 1px;"
							></InputSearch>
						</Col>
					</Row>
					<EditableTable
						class="mt-2"
						:columns="columns"
						:data="newOrderList"
						:is-hidden-del="!isCanUpdate"
						:is-hidden-add="!(isCanUpdate && authority.hasAuthority('A00400600801'))"
						ref="listTable"
						:row-key="rowKey"
						:isShowRecordEditor="true"
						:max-height="getEditTableHeight()"
						:loading="tableLoading"
						:virtualScroll="true"
						:virtualScrollBuff="{ top: 2100, bottom: 2100 }"
						enterAsDown
						@on-insert="onInsert"
						@on-delete="onDelete"
					>
						<template #after-table-right>
							<div class="total-info">
								<p>
									合计数量 :
									<span class="table-total-num mr20">{{ totalInStore || 0 }}</span>
                  <template v-if="authority.hasAmountInfoAuth()">
                    合计(含税) : &nbsp;&nbsp;<span class="table-total-amount mr20">{{
                      calTotal || 0
                    }}</span>
                  </template>
									<template v-if="input_tax_rate == 1 && authority.hasAmountInfoAuth()">
										合计(不含税) :
										<span class="table-total-amount">{{ tax_exclusive || 0 }}</span>
									</template>
								</p>
							</div>
						</template>
						<template #after-table-left>
							<div class="after-table-left-hotkey">
								<SelfIcon icon="tips" :size="12" class="mr6" />
								<span>支持键盘操作，</span>
								<Icon type="ios-arrow-round-back" />
								<Icon type="ios-arrow-round-forward" />
								<span>左右切换，</span>
								<Icon type="ios-arrow-round-up" />
								<Icon type="ios-arrow-round-down" />
								<span>上下换行</span>
								<span v-show="isCanUpdate && authority.hasAuthority('A00400600801')">，Enter 键新增一行</span>
							</div>
						</template>
					</EditableTable>
				</base-block>
				<base-block title="费用分摊" class="mt25" v-if="isEnableStoreInCost && isInTypeCanAllot">
					<FeeAllot
						:value="allotItems"
						class="mt-16"
						type="2"
						ref="allot"
						:maxItems="10"
						:allotType="allotType"
						:beforeAllot="beforeAllot"
						@on-delete="onAllotDelete"
						@on-allot="onAllot" />
				</base-block>
				<base-block title="其他信息" class="mt25 base-info">
					<FormItem label="备注" :label-width="46" style="width: 80%;">
						<Input
              style="width: 100%;"
							v-model="auditDetail.memo"
							type="textarea"
							:autosize="{ minRows: 3, maxRows: 8 }"
              :maxlength="512"
							placeholder="请输入备注信息，长度 < 512"
						/>
					</FormItem>
					<br>
          <FormItem :label-width="46" label="附件">
            <AttachmentUpload v-model="orderFiles" />
          </FormItem>
				</base-block>
			</Form>
			<template #button-between>
				<Button
					class="ml15"
					@click="resetNum"
					v-if="!isUserReturn && !typeAllotIn"
				>重 置</Button
				>
				<Button
					class="ml15"
					type="primary"
					@click="updateAudit"
					:loading="saving"
					:disabled="postList.length === 0 || saving"
				>{{ saving ? '保存中' : '保 存' }}</Button
				>
			</template>
		</DetailPage>
    <div
      :style="{ height: auto, position: 'relative', marginTop: '100px' }"
      class="s-loading s-loading--default"
      v-if="!auditDetail"
    >
      <span class="s-loading-content" style="margin-top: -60px"></span>
      <p class="s-loading-text">数据加载中，请耐心等待...</p>
    </div>
		<goods-list-modal
			v-model="showGoodsListModal"
			modalType="storeGoods"
			@on-add="handlerAdd"
			:selectedGoods="newOrderList"
			:isShowStockStoreId="storeId"
			:params="{ store_in_id: storeId }"
      :isShowPrice="authority.hasAmountInfoAuth()"
		><div></div
		></goods-list-modal>
		<Modal v-model="checkAuditModal" title="审核提示">
			<p style="line-height: 36px">请确认是否审核入库单？</p>
			<p>
				共 <b class="greenColor">{{ newOrderList.length }}</b> 种商品，
				入库金额 <b class="greenColor">{{ calTotal }}</b> 元
			</p>
			<div slot="footer">
				<Button @click="checkAuditModal = false">取 消</Button>
				<Button type="success" @click="auditConfirmIn">确 定</Button>
			</div>
		</Modal>
  </div>
</template>

<script>
import NumberInput from '@components/basic/NumberInput';
import moveeSelect from '@components/basic/moveeSelect';
import GoodsListModal from '@components/order/goodsListModal';
import ware from '@api/storeRoom.js';
import GoodsBatchMixin from '@/mixins/GoodsBatch';
import apiUtil from '@/api/util';
import authority from '@/util/authority';
import ConfigMixin from '@/mixins/config';
import FeeAllot from '@/components/fee-allot';
import Icon from '@/components/icon';
import STMixin from './mixins';
import modifiedTips from '@/mixins/modified-tips';
import img from '@assets/images/icon/status_icon.png';
import EditableTable from '@/components/editable-table/index.js';
import { getEditTableHeight } from '@/util/common';
import CommoditySelect from '@/components/common/CommoditySelect';
import SButton from '@/components/button';
import AttachmentUpload from '@/components/attachment-upload/attachment-upload.vue';
import DetailPage from '@/components/detail-page';
import InputSearch from '@/pages/purchase/purchaseOrder_New/components/input-search.vue';
import PurchaseSelect from '@/components/common/purchaseSelect.vue';
import SelfIcon from '@/components/icon';
import { uniqueId, floor } from 'lodash-es';

const isCanUpdateTypes = [
  '1', // 采购入库
  '2', // 其他入库
  '15', // 退仓入库
];

export default {
  mixins: [GoodsBatchMixin, ConfigMixin, STMixin, modifiedTips],
  components: {
		SelfIcon,
		PurchaseSelect,
		InputSearch,
		DetailPage, AttachmentUpload, SButton,
    FeeAllot,
    moveeSelect,
    NumberInput,
    GoodsListModal,
    EditableTable,
		CommoditySelect
  },
  watch: {
    sysConfig() {
      this.initTableCols();
    },
  },
  data() {
    let that = this;
    return {
      tableLoading: true,
			rowKey: 'id',
      hasAllot: false,
      orderFiles: [],
      allotItems: [],
      allotType: [
        {
          label: '入库金额分配',
          value: '1',
        },
        {
          label: '入库数量分配',
          value: '2',
        },
      ],
      isFixedPrice: localStorage.getItem('isFixedPrice') == 1 ? 1 : 0, // 默认锁定加单金额字段,
      authority,
      saving: false,
      modified: false,
      selectLoading: false,
      checkAuditModal: false,
      showGoodsListModal: false,
      input_tax_rate: 0,
      goodsNum: 1,
      reservoirData: [],
      columns: [],
      originalColumns: [
				{
					type: 'titleCfg',
					titleType: 'in_order_detail',
					width: 52,
					align: 'center',
					fixed: 'left'
				},
				{
					key: 'index',
					type: 'index',
					width: 50,
					align: 'center',
					fixed: 'left',
					style: {
						padding: '0 10px'
					}
				},
				{
					title: '商品',
					width: 220,
					fixed: 'left',
					key: 'commodity_name',
					render: (h, params) => {
						const { row, index } = params;
						let data = params.row;
						if (!data.commodity_code) {
							return (
								<CommoditySelect
									class="commodity_name"
									commodityName={row.name}
									dataProvider={this.apiUrl.getBaseCommoditys}
									params={{
										unfilter_online: 1,
										un_limit_status: 0
									}}
									trimInput={true}
									commodityIdKey="commodity_id"
									commodityNameKey="name"
									selectedData={this.dataList}
									onOn-change={async (cid) => { await this.goInputNum(cid); this.addOrder(cid, row, index) }}
									onOn-enter={() => this._insertAt(index)}
									style={{ width: '174px' }}
									inputProps={{
										type: 'textarea',
										rows: 1,
										autosize: { minRows: 1, maxRows: 2.2 }
									}}
									slot-type="purchase"
								></CommoditySelect>
							)
						}
						return h('div', [
							h('span', { class: Number(data.num) !== Number(data.actual_num) && 'warning-tip'}, data.commodity_name),
							h(
								'div',
								{
									style: {
										color: '#999999'
									}
								},
								data.commodity_code
							)
						]);
					}
				},
        {
          title: '描述',
          width: 160,
          key: 'commodity_summary',
        },
        {
          width: 80,
          title: '单位',
          key: 'unit',
        },
        {
          title: '应入库数量',
          key: 'num',
          width: 120,
        },
        {
          width: 140,
          title: '实际入库数量',
          key: 'actual_num',
          render: (h, params) => {
            let data = params.row,
              me = this,
              actualNum = data.actual_num;
            let dealData = function () {
              if (data.actual_num !== (me.postList[params.index] && me.postList[params.index].actual_num)) {
                params.row.actual_num = data.actual_num;
								me.newOrderList[params.index].actual_num = params.row.actual_num;
								me.newOrderList[params.index].price = params.row.price;
								me.newOrderList[params.index].total_price = params.row.total_price;

                const originIndex = me.postList.findIndex(item => item[me.rowKey] === data[me.rowKey]);
                if (originIndex > -1) {
                  me.postList[originIndex] = me.postList[originIndex] || {}
                  me.postList[originIndex].actual_num = params.row.actual_num;
                  me.postList[originIndex].price = params.row.price;
                  me.postList[originIndex].inPrice = params.row.price;
                  me.postList[originIndex].total_price = params.row.total_price;
                }
              }
            };
            return h(NumberInput, {
              props: {
                value: Number(data.actual_num),
                min: 0,
                max: 999999999.99,
                precision: 2,
                // 只允许采购入库, 其他入库允许允许修改
                disabled:
                  !([1, 2, 15].indexOf(+this.auditDetail.in_type) > -1) ||
                  !this.auditDetail.is_can_edit_actual_num ||
                  this.hasAllot, // 有分摊不允许修改
                // 去除禁用逻辑
                // disabled: !data.actual_num_input && (+this.auditDetail.in_type === 1 && +me.newOrderList[params.index]._total_price !== 0 ? false : Number(actualNum) > 0)
              },
              style: {
                width: '100%',
              },
							class: 'num',
              on: {
                'on-change'(val) {
                  params.row.actual_num = val;
                  me.newOrderList[params.index].actual_num = val;
                  if (+val === 0 && +data.total_price === 0) return;
                  else if (+val === 0) {
                    // 存着
                    me.newOrderList[params.index]._total_price =
                      data.total_price;
                    me.newOrderList[params.index]._price = data.price;
                    // console.log(data.total_price)
                    params.row.total_price = 0;
                    params.row.price =
                      me.isFixedPrice == 1 ? params.row.price : 0;
                    params.row.inPrice = 0;
                  } else {
                    // 还回去再继续算
                    params.row.total_price = me.newOrderList[params.index]._total_price;
                    params.row.price = me.newOrderList[params.index]._price || params.row.price;
                    if (me.isFixedPrice == 1) {
                      params.row.total_price =
                        parseFloat(Number(params.row.price).mul(val)).toFixed(
                          me.warehouse_total_price_decimal,
                        ) || 0;
                    } else {
                      params.row.price =
                        parseFloat(Number(params.row.total_price).div(val)).toFixed(
                          me.warehouse_unit_price_decimal,
                        ) || 0;
                    }
                    params.row.inPrice =
                      parseFloat(Number(params.row.total_price).div(val)).toFixed(
                        me.warehouse_total_price_decimal,
                      ) || 0;
                  }

                  dealData();

                  // me.changes(params.row.total_price, params.index);
                  me.updateBeforeAllotData(
                    'cost_before_total_price',
                    params.row.total_price,
                    params.row,
                  );
                  me.updateBeforeAllotData(
                    'cost_before_in_price',
                    params.row.price,
                    params.row,
                  );
                },
                'on-focus'(event) {
                  event.target.select();
                },
                'on-blur': (event) => {
                  me.checkPriceDiff(params.row);
                  me.newOrderList[params.index].actual_num_input = false;
                  me.postList[params.index].actual_num_input = false;
                  me.newOrderList[params.index].price_diff_warning = params.row.price_diff_warning;
                  me.postList[params.index].price_diff_warning = params.row.price_diff_warning;
                  me.newOrderList[params.index].price = params.row.price;
                  me.postList[params.index].price = params.row.price;
                  me.newOrderList[params.index].total_price = params.row.total_price;
                  me.newOrderList[params.index].total_price = params.row.total_price;
                  dealData();
                },
                'on-enter'(event) {
                  dealData();

                 	me._insertAt(params.index);
                },
              },
            });
          },
        },
        {
					minWidth: 160,
          title: '单价',
          key: 'show_in_price',
          isShow: authority.hasAmountInfoAuth(),
          renderHeader: (h) => {
            if (!([1, 2, 15].indexOf(+this.auditDetail.in_type) > -1)) {
              return h('div', '单价');
            }
            return <div class="show_price_title" onClick={() => {
              that.isFixedPrice = !that.isFixedPrice ? 1 : 0;
              localStorage.setItem('isFixedPrice', that.isFixedPrice);
            }}>
              单价<i disabled={this.hasAllot} class={that.isFixedPrice ? 'icon-lock' : 'icon-unlock'}></i>
            </div>
          },
					render: (h, params) => {
						return h('span', params.row.price);
					}
        },
        {
          width: 100,
          title: '金额(元)',
          key: 'total_price',
          isShow: authority.hasAmountInfoAuth(),
        },
        {
          title: '参考价格',
          key: 'refer_price',
          filterMultiple: false,
          width: 150,
          align: 'right',
          renderHeader: (h) => {
            const priceTypeList = Object.values(this.priceTypeConst);
            let currentPriceType = priceTypeList.find(
              (item) => item.value === this.priceType,
            );
            if (!currentPriceType) {
              currentPriceType = priceTypeList[0];
            }
            this.baseValue = currentPriceType.value;
            const dropDownList = priceTypeList.map((item) => {
              return h(
                'DropdownItem',
                {
                  props: {
                    name: item.value,
                  },
                  style: {
                    display: 'flex',
                    alignItems: 'center',
                  },
                },
                [
                  h('span', item.label),
                  !!item.tip &&
                    h(
                      'Tooltip',
                      {
                        props: {
                          content: item.tip,
                          placement: 'right',
                          transfer: true,
                        },
                        on: {
                          'on-popper-show': () => {
                            $('.ivu-tooltip-popper').css('z-index', '99999');
                            $('.ivu-tooltip-inner').css(
                              'white-space',
                              'normal',
                            );
                          },
                          'on-popper-hide': () => {
                            $('.ivu-tooltip-popper').css('z-index', '99999');
                            $('.ivu-tooltip-inner').css(
                              'white-space',
                              'normal',
                            );
                          },
                        },
                      },
                      [
                        h(Icon, {
                          props: {
                            icon: 'tips',
                            size: '13',
                          },
                          style: {
                            marginLeft: '5px',
                            fontSize: '13px',
                          },
                        }),
                      ],
                    ),
                ],
              );
            });
            const dropDown = h(
              'Dropdown',
              {
                props: {
                  trigger: 'click',
                  transfer: true,
                },
                on: {
                  'on-click': (name) => {
                    this.priceType = name;
                    this.handleChangePriceType();
                  },
                },
              },
              [
                h('img', {
                  style: {
                    width: '12px',
                    height: '12px',
                    marginLeft: '4px',
                    marginTop: '2px',
                  },
                  attrs: {
                    src: img,
                  },
                }),
                h(
                  'DropdownMenu',
                  {
                    slot: 'list',
                  },
                  dropDownList,
                ),
              ],
            );
            return h('div', [h('span', currentPriceType.label), dropDown]);
          },
          render: (h, params) => {
            const data = params.row;
            // 取选中当前项的key, priceTypeConst的键
            const priceTypeConst = this.priceTypeConst;
            const key = Object.keys(priceTypeConst).find(
              (listKey) => priceTypeConst[listKey].value === this.priceType,
            );
            return h('span', {}, data[key]);
          },
        },
        {
          title: '税率',
          width: 80,
          key: 'input_tax_rate',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              !row.input_tax_rate || row.input_tax_rate === '-'
                ? '--'
                : row.input_tax_rate + '%',
            );
          },
        },
        {
          title: '税额',
          width: 80,
          key: 'input_tax',
          isShow: authority.hasAmountInfoAuth(),
          render: (h, params) => {
            let { row } = params;
            // return h('span', row.input_tax == '-' ? '--' : row.input_tax);
            return h(
              'span',
              !row.input_tax || row.input_tax === '-'
                ? '--'
                : Number(row.input_tax).toFixed(this.warehouse_total_price_decimal),
            );
          },
        },
        {
          title: '金额（不含税）',
          width: 80,
          key: 'total_price_no_tax',
          isShow: authority.hasAmountInfoAuth(),
          render: (h, params) => {
            let { row } = params;
            let tax_exclusive = Number(row.tax_exclusive).toFixed(
              this.warehouse_total_price_decimal,
            );
            return h('span', tax_exclusive ? tax_exclusive : '--');
          },
        },
        {
          title: '库区库位',
          isShow: apiUtil.getIsOpenStoreMGT(),
          width: 170,
          key: 'area_location',
        },
        {
          width: 80,
          title: '状态',
          key: 'in_status_name',
          render: (h, params) => {
            let data = params.row;
            let text = Number(data.actual_num) > 0 ? '已录入' : '未录入';
            return h(
              'span',
              {
                class: {
                  greenColor: Number(data.actual_num) > 0,
                },
              },
              text,
            );
          },
        },
        {
          minWidth: 164,
          title: '备注',
          key: 'remarks',
          render: (h, params) => {
            let data = params.row,
              me = this;
            return h('i-input', {
              props: {
                value: data.remarks,
              },
              on: {
                'on-change'(event) {
                  params.row.remarks = event.target.value;
                },
                'on-blur'() {
                  me.newOrderList[params.index].remarks = params.row.remarks;

                  const originIndex = me.postList.findIndex(item => item[me.rowKey] === data[me.rowKey]);
                  if (originIndex > -1) {
                    me.postList[originIndex].remarks = params.row.remarks;
                  }
                },
								'on-enter': event => {
									this._insertAt(params.index);
								},
              },
            });
          },
        },
        {
          title: '操作',
          key: 'action',
					type: 'action',
          width: 120,
          align: 'center',
          fixed: 'right',
          render: (h, params) => {
            let me = this;
            if (this.isUserReturn || this.typeAllotIn) {
              return h('span', '-');
            }
            return h('div', [
              h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
										color: '#FF6E00',
                  },
                  on: {
                    click: () => {
                      if (!this.checkModify('重置')) {
                        return;
                      }
                      if (me.postList[params.index].actual_num_input) {
                        return;
                      }
                      params.row.actual_num = 0;
                      me.postList[params.index].actual_num = 0;
                      me.postList[params.index].actual_num_input = true;
                      this._syncStoreList()
                    },
                  },
                },
                '重置',
              )
            ]);
          },
        },
      ],
      storeId: '',
      goodsId: '',
      searchName: '',
      delivery_type: 1,
      newOrderList: [],
      postList: [],
      remoteList: [],
      newOrderInfo: '',
      auditDetail: '',
      orderIds: [],
      deleteList: [],
			purchaseForm: {
				channel_type: '',
				agent_id: '',
				provider_id: ''
			},
			// 是否改动过采购相关数据
			isChangePurchaseData: 0,
    };
  },
  created() {
    this.getConfig();
    this._getReservoirData();
    // this.initTableCols();
    this.getInBoundAudit();
  },
  computed: {
    warehouse_unit_price_decimal () {
      return 4; // 单价固定按4位小数计算（精度需求产品确认的）
    },
    warehouse_total_price_decimal () {
      return 2; // 金额固定按2位小数计算（精度需求产品确认的）
    },
		// 是否开启审核/反审核更新采购数据
		openUpdatePurchase () {
			return +this.sysConfig.is_in_order_sync_receipt === 1;
		},
		purchaseSelectValue () {
			const { channel_type, agent_id, provider_id } = this.purchaseForm
			const purchaseValue = +channel_type === 1? agent_id : provider_id
			return channel_type ? [String(channel_type), String(purchaseValue)] : []
		},
    totalInStore() {
      const num = this.newOrderList.reduce((total, item) => {
        return total + Number(item.actual_num).toFixed(2);
      }, 0);
      return num.toFixed(this.warehouse_total_price_decimal);
    },
    isInTypeCanAllot() {
      return [
        1, // 采购入库
        2, // 其他入库
        15, // 退仓入库
      ].includes(+this.auditDetail.in_type);
    },
    /**
     * 是否是客户退货入库单据
     * @returns {boolean}
     */
    isUserReturn() {
      return Number(this.auditDetail.in_type) === 4;
    },
    /**
     * 是否是调拨入库
     * @returns {boolean}
     */
    typeAllotIn() {
      return Number(this.auditDetail.in_type) === 3;
    },
    /**
     * 不含税合计
     * @returns {Number}
     */
    tax_exclusive() {
      let totalNum = 0;
      this.postList.map((d) => {
        if (d.tax_exclusive !== undefined && d.tax_exclusive !== null) {
          totalNum += parseFloat(d.tax_exclusive);
        }
      });
      return parseFloat(totalNum).toFixed(this.warehouse_total_price_decimal);
    },
    calTotal() {
      let totalNum = 0;
      this.postList.map((d) => {
        totalNum += parseFloat(d.total_price);
      });
      return parseFloat(totalNum).toFixed(this.warehouse_total_price_decimal);
    },
    // 跳转连接
    jumpUrl() {
      const types = ['1', '3', '4', '9', '11'];
      const urls = [
        +this.$store.state.sysConfig.purchase_order_input_version === 2
          ? '#/purchase/detail'
          : '#/purchase/purchaseOrderDetail',
        '#/storeRoom/commodityACDetail',
        '#/returnDetail',
        '#/process/completion/edit',
        '#/storeRoom/partition/list',
      ];
      console.log(this.auditDetail);
      const { in_type, about_id, about_no } = this.auditDetail;
      let url = urls[types.indexOf(in_type)];
      /**
      <Option :value="0">全部类型</Option>
        <Option :value="1">采购入库</Option>
        about_id
        http://localhost:8089/view#/purchase/purchaseOrderDetail?keep_scroll=1&id=8285

        <Option :value="2">其他入库</Option>
        <Option :value="3">调货入库</Option>
        id
        http://localhost:8089/view#/storeRoom/commodityACDetail?id=1

        <Option :value="4">订单退货</Option>
        id
        http://localhost:8089/view#/returnDetail?id=351

        <Option :value="5">单位转换</Option>
        <Option :value="7">期初入库</Option>
        <Option :value="8">报溢入库</Option>
        <Option :value="9" v-if="isEnableProcess">完工入库</Option>
        about_no
        http://localhost:8089/#/process/completion/edit?is_can_print=false&completion_no=WG20120200001&page=detail

        <Option :value="10">退料入库</Option>
        <Option :value="11">分割入库</Option>
        http://cpzs.sdongpo.com/superAdmin/view#/storeRoom/partition/list
       */
      const index = types.indexOf(in_type);
      if (index === -1) return;
      if (index === 3) {
        url = `${url}?is_can_print=false&completion_no=${about_no}&page=detail`;
      } else if (index === 4) {
        url = `#/storeRoom/partition/list`;
      } else {
        url = `${url}?id=${about_id}`;
      }
      return url;
    },
    isCanUpdate() {
      return (
        isCanUpdateTypes.includes(this.auditDetail.in_type) &&
        this.auditDetail.is_can_add_commodity
      );
    },
		receiptErrInfo() {
			const tip = '入库单不能修改计划交货日期/供应商/采购员';
			const { receipt_err_info, is_can_sync_receipt }  = this.auditDetail || {}
			if (is_can_sync_receipt) return '';
			// 将第一个逗号之后的字符替换成tip
			return (receipt_err_info || '').replace(/，.*$/, `, ${tip}`);
		},
  },
  methods: {
    getEditTableHeight,
    checkPriceDiff(row) {
      const amountKey = 'actual_num';
      const priceKey = 'price';
      const totalPriceKey = 'total_price';
      
      row.price_diff_warning = '';

      // 数量或金额为0时不检查
      if (+row[amountKey] === 0 || +row[totalPriceKey] === 0) {
        return;
      }

      const priceFixed2 = Number(row[priceKey]).toFixed(2);
      const computedPrice = Number(row[totalPriceKey]).div(row[amountKey]).toFixed(2);

      let unitPriceDecimal = 0;
      const unitPriceDecimalArr = Number(row[priceKey]).toString().split('.');
      if (unitPriceDecimalArr.length > 1) {
        unitPriceDecimal = unitPriceDecimalArr[1].length;
      }
   
      // row[priceKey]小数位数不大于2时不检查
      if (unitPriceDecimal <= 2) {
        return;
      }

      // 只有 1: 采购入库, 12: 采购入库【联营】,需要做提示
      if (![1, 12].includes(+this.auditDetail.in_type)) {
        return;
      }
      if (priceFixed2 - computedPrice !== 0) {
        row.price_diff_warning = `当前入库单价大于2位小数（${row[priceKey]}），系统将通过「入库金额 ÷ 入库数量」计算对账单价（结果：${computedPrice}），与直接四舍五入（${row[priceKey]}→${priceFixed2}）会存在差异。`
      } 
    },
		_insertAt (index) {
      if (!(authority.hasAuthority('A00400600801') && this.isCanUpdate)) return
			if (index === this.newOrderList.length - 1) { // 只在最后一行添加
				this.newOrderList.splice(index + 1, 0, {
					id: uniqueId('$unique-'), // 生成唯一key, 用来防止commodity_id与index重合时报错
					commodity_id: '',
					commodity_name: '',
					commodity_code: '',
					commodity_summary: '',
					unit: '',
					unit_convert_text: '',
					num: 0,
					actual_num: 0,
					price: 0,
					total_price: 0,
					area_id: '',
					location_id: '',
					area_and_location_name: '',
					in_status_name: '',
					remarks: '',
					batch_no: '',
					is_batch: 0,
					_sortNum: index + 1,
				});
				this._focusGoodsInput(index);
				this._syncStoreList();
			}
		},
		focusNum(index) {
			this.$nextTick(() => {
				const $currentRow = this.$refs.listTable.$el.querySelectorAll('tbody tr')[index];
				if ($currentRow) {
					$currentRow
						.querySelector('.num')
						.querySelector('input')
						.focus();
				}
			});
		},
		_focusGoodsInput(index) {
			this.activeRowIndex = index;
			const curTr = this.$refs.listTable.$el.querySelector(
				`[data-key="body_tr_${index}"]`,
			);

			this.$nextTick(() => {
				let nextTr = curTr.nextElementSibling;

				if (nextTr) {
					setTimeout(() => {
						nextTr
							.querySelector('.commodity_name')
							.querySelector('textarea')
							.focus();
					}, 1)

					this.activeRowIndex = index + 1;
				}
			});
		},
		onInsert (insertedRow, index) {
			this._focusGoodsInput(index);
		},
		onDelete (deleteRow) {
			if (!this.checkModify('删除商品')) {
				// 回滚删除操作
				this.newOrderList = this.deepClone(this.postList);
				return;
			}
			if (deleteRow.id && deleteRow.commodity_id) {
				this.deleteList.push(deleteRow)
			}
			this._syncStoreList()
		},
		/**
		 * @description: 同步 newOrderList 和 postList 数据
		 */
		_syncStoreList () {
			const postListCopy = this.cloneObj(this.newOrderList)
			postListCopy.forEach(goods => {
				const storeGoods = this.postList.find(storeGoods => {
					return (storeGoods && storeGoods[this.rowKey]) === goods[this.rowKey]
				})
				if (storeGoods) Object.assign(goods, storeGoods)
			})
			this.$set(this, 'newOrderList', postListCopy)
			// this.newOrderList = postListCopy
			this.postList = this.cloneObj(postListCopy)
		},
    changeReason(val) {
      this.auditDetail.in_reason = val;
    },
    checkModify(op = '添加商品') {
      if (this.hasAllot) {
        this.$smessage({
          type: 'error',
          text: `单据已有分摊记录，不允许${op}！`,
        });
        return false;
      }
      return true;
    },
    // 更新分摊前的数据
    updateBeforeAllotData(field, value, row) {
      if (!this.hasAllot) {
        this.postList.forEach((r) => {
          if (r.id === row.id) {
            r[field] = value;
          }
        });
      }
    },
    // 删除分摊重置到初始数据
    onAllotDelete() {
      this.hasAllot = false;
      this.postList = this.postList.map((item) => ({
        ...item,
        price: item.cost_before_in_price,
        total_price: item.cost_before_total_price,
      }));
      this.newOrderList = this.deepClone(this.postList);
    },
    beforeAllot(allotItemInfo) {
      // 增加费用不做限制
      if (allotItemInfo.type === '1') {
        return true;
      }
      const totalPrice = this.newOrderList.reduce((total, item) => {
        return total + Number(item.total_price);
      }, 0);
      const valid = totalPrice - allotItemInfo.price >= 0;
      if (!valid) {
        this.$smessage({
          type: 'error',
          text: '减少费用时分摊金额不能大于合计金额!',
        });
      }
      return valid;
    },
    onAllot(allotItemInfo, allotFn) {
      this.hasAllot = true;
      let allotList = [];
      let allotBaseList = [];
      if (Number(allotItemInfo.costsType) === 1) {
        // 按入库金额分配
        allotBaseList = this.newOrderList.map((item) => item.total_price);
      } else {
        // 按入库数量分配
        allotBaseList = this.newOrderList.map((item) => item.actual_num);
      }
      allotList = allotFn(allotBaseList, allotItemInfo);
      this.newOrderList = this.newOrderList.map((item, index) => {
        item.total_price = Number(allotList[index]).add(Number(item.total_price));
        item.price = (item.total_price.div(item.actual_num)).toFixed(
          this.warehouse_unit_price_decimal,
        );
        this.postList[index].total_price = item.total_price;
        this.postList[index].price = item.price;
        return item;
      });
      console.log('allotList', allotList);
    },
    _getReservoirData() {
      const params = {
        store_id: this.storeId,
      };
      ware.getReservoirList(params).then((res) => {
        const { status, data } = res;
        if (status) {
          this.reservoirData = data;
        }
      });
    },
    // 获取进项税配置 是否展示 税率 税额 小计（不含税）合计（不含税）
    getConfig() {
      this.commonService.getConfig().then((config) => {
        let { input_tax_rate } = config;
        this.input_tax_rate = input_tax_rate;
        if (+input_tax_rate === 0) {
          let originalColumns = this.originalColumns.filter((item) => {
            return (
              item.key !== 'input_tax_rate' &&
              item.key !== 'input_tax' &&
              item.key !== 'tax_exclusive'
            );
          });
          this.originalColumns = originalColumns;
          this.initTableCols();
        } else {
          this.initTableCols();
        }
      });
    },
    initTableCols() {
      let showColumns = this.deepClone(this.originalColumns);
      this.columns = showColumns;
      this.initPriceColumns();
      this.initLossColumn();
      this.initBatchEditColumns({
        columns: showColumns,
        showBatchNo: false,
        showProductionDate: true,
        showExpireDate: true,
        autoSetDate: true,
        nextColumn: 'remarks',
        listDataKey: 'postList',
      });
      this.columns = showColumns.filter((col) => col.isShow !== false);
    },
    initLossColumn() {
      let position = this.columns.findIndex(
        (item) => item.key === 'actual_num',
      );
      let column = {
        title: '报损数量',
        key: 'loss_num',
        width: 140,
        render: (h, params) => {
          let data = params.row;
          let key = 'loss_num';
          data[key] = data[key] ? Number(data[key]) : 0;
          return h(NumberInput, {
            props: {
              value: data[key],
              min: 0,
              max: 999999999.99,
              precision: 2,
            },
            style: {
              width: '100%',
            },
            on: {
              'on-change': (val) => {
                params.row[key] = val;
                this.postList[params.index][key] = val;
              },
              'on-focus': (event) => {
                event.target.select();
              },
							'on-enter': (event) => {
								this._insertAt(params.index + 1);
							},
            },
          });
        },
      };
      if (!this.isUserReturn && !this.typeAllotIn) {
        return false;
      }
      this.columns.splice(position - 1, 0, column);
    },
    initPriceColumns() {
      const amountKey = 'actual_num';
      const priceKey = 'price';
      const totalPriceKey = 'total_price';
      const inPrice = 'in_price';
      const onPriceChange = (params, value) => {
        params.row[priceKey] = value;
        params.row[totalPriceKey] = parseFloat(Number(value).mul(params.row[amountKey])).toFixed(this.warehouse_total_price_decimal);
        const originIndex = this.postList.findIndex(item => item[this.rowKey] === params.row[this.rowKey]);
        if (originIndex > -1) {
          this.postList[originIndex][inPrice] = params.row[inPrice];
          this.postList[originIndex][priceKey] = params.row[priceKey];
          this.postList[originIndex][totalPriceKey] = params.row[totalPriceKey];
        }
        this.updateBeforeAllotData(
          'cost_before_in_price',
          params.row[priceKey],
          params.row,
        );
        this.updateBeforeAllotData(
          'cost_before_total_price',
          params.row[totalPriceKey],
          params.row,
        );
      };
      const onTotalPriceChange = (params, value) => {
        params.row[totalPriceKey] = value;
        params.row[priceKey] =
          Number(params.row[amountKey]) === 0
            ? 0
            : (Number(params.row[totalPriceKey]).div(params.row[amountKey])).toFixed(
                this.warehouse_unit_price_decimal,
              );
        const originIndex = this.postList.findIndex(item => item[this.rowKey] === params.row[this.rowKey]);
        if (originIndex > -1) {
          this.postList[originIndex][priceKey] = params.row[priceKey];
          this.postList[originIndex][inPrice] = params.row[inPrice];
          this.postList[originIndex][totalPriceKey] = params.row[totalPriceKey];
        }
        this.updateBeforeAllotData(
          'cost_before_total_price',
          params.row[totalPriceKey],
          params.row,
        );
        this.updateBeforeAllotData(
          'cost_before_in_price',
          params.row[priceKey],
          params.row,
        );
      };
      this.columns.forEach((column) => {
        // 单价
        if (column.key === 'show_in_price') {
          // column.render = undefined;
          // 开启审核入库金额
          // if (this.isEnableCheckStockPrice) {
          column.render = (h, params) => {
            let row = params.row;
            const inputValue = row[priceKey];
            const priceTypeConst = this.priceTypeConst;
            const key = Object.keys(priceTypeConst).find(
              (listKey) => priceTypeConst[listKey].value === this.priceType,
            );
            const comparePrice = row[key];
            const input = h(NumberInput, {
              props: {
                value: Number(row[priceKey]).toFixed(
                  this.warehouse_unit_price_decimal,
                ) || 0,
                min: 0,
                max: 999999999.99,
                precision: 2,
                disabled: this.hasAllot,
                precision: this.warehouse_unit_price_decimal,
              },
              style: {
                width: '80%',
              },
              on: {
                'on-change'(val) {
                  onPriceChange(params, val);
                },
                'on-focus'(event) {
                  event.target.select();
                },
                'on-blur': () => {
                  this.checkPriceDiff(params.row);
                  this.newOrderList[params.index][priceKey] = params.row[priceKey];
                  this.newOrderList[params.index][totalPriceKey] = params.row[totalPriceKey];
                  this.newOrderList[params.index]['loss_num'] = params.row['loss_num'];
                  this.newOrderList[params.index].price_diff_warning = params.row.price_diff_warning;

                  const originIndex = this.postList.findIndex(item => item[this.rowKey] === params.row[this.rowKey]);
                  if (originIndex > -1) {
                    this.postList[originIndex][priceKey] = params.row[priceKey];
                    this.postList[originIndex][inPrice] = params.row[priceKey];
                    this.postList[originIndex][totalPriceKey] = params.row[totalPriceKey];
                    this.postList[params.index].price_diff_warning = params.row.price_diff_warning;
                  }
                },
								'on-enter': event => {
									this._insertAt(params.index);
								},
              },
            });
            const template = [input];
            if (row.price_diff_warning) {
              const priceWarningIcon =	h('Tooltip', {
                props: {
                  transfer: true,
                  maxWidth: 300,
                  content: row.price_diff_warning 
                }
              }, [
                h(Icon, {
                  props: {
                    icon: 'tips',
                    size: '13'
                  },
                  style: {
                    marginLeft: '5px',
                    fontSize: '13px',
                    color: 'red',
                    cursor: 'pointer'
                  }
                })
              ]);
              template.push(priceWarningIcon);
            }
            if (Number(inputValue) > Number(comparePrice)) {
              template.push(
                h(
                  'p',
                  {
                    style: {
                      color: 'red',
                      fontSize: '12px',
                    },
                  },
                  `大于${priceTypeConst[key].label}！`,
                ),
              );
            }
            return h('div', template);
          };
          // }
        }
        // 金额
        if (column.key === 'total_price') {
          // column.render = undefined;
          // 开启审核入库金额
          // if (this.isEnableCheckStockPrice) {
          column.render = (h, params) => {
            let row = params.row;
            return h(NumberInput, {
              props: {
                disabled: this.hasAllot,
                value: Number(row[totalPriceKey]).toFixed(
                  this.warehouse_total_price_decimal,
                ) || 0,
                min: 0,
                max: floor(999999999.9999, this.warehouse_total_price_decimal),
                precision: this.warehouse_total_price_decimal,
              },
              style: {
                width: '100%',
              },
              on: {
                'on-change'(val) {
                  onTotalPriceChange(params, val);
                },
                'on-focus'(event) {
                  event.target.select();
                },
                'on-blur': () => {

                  this.checkPriceDiff(params.row);
                  this.newOrderList[params.index][priceKey] = params.row[priceKey];
                  this.newOrderList[params.index][totalPriceKey] = params.row[totalPriceKey];
                  this.newOrderList[params.index]['loss_num'] = params.row['loss_num'];
                  this.newOrderList[params.index].price_diff_warning = params.row.price_diff_warning;

                  const originIndex = this.postList.findIndex(item => item[this.rowKey] === params.row[this.rowKey]);
                  if (originIndex > -1) {
                    this.postList[originIndex][priceKey] = params.row[priceKey];
                    this.postList[originIndex][inPrice] = params.row[priceKey];
                    this.postList[originIndex][totalPriceKey] = params.row[totalPriceKey];
                    this.postList[params.index].price_diff_warning = params.row.price_diff_warning;
                  }
                },
								'on-enter': event => {
									this._insertAt(params.index);
								},
              },
            });
          };
          // }
        }
      });
    },
    resetNum() {
      if (!this.checkModify('重置')) {
        return;
      }
      let list = this.cloneObj(this.newOrderList);
      list.forEach((item) => {
        item.actual_num = 0;
        item.actual_num_input = true;
        this.postList.forEach((postItem) => {
          if (postItem.id === item.id) {
            postItem.actual_num = 0;
          }
        });
      });
      this.newOrderList = list;
    },
    changeCommodityStatus(commodity_id, index, num, rowParams) {
      let params = {
        in_record_id: rowParams.row.in_record_id,
        in_id: this.$route.query.id,
        commodity_id: commodity_id,
        num,
      };
      ware.editStockInNum(params).then((res) => {
        if (res.status) {
          // this.newOrderList[index].isNew = false;
          rowParams.row.isNew = false;
          this.$Message.success('修改成功');
        } else {
          this.newOrderList[index].total_price = 0;
          this.newOrderList[index].actual_num = 0;
          rowParams.row.actual_num = 0;
          rowParams.row.total_price = 0;
          this.postList[index].actual_num = 0;
          this.postList[index].total_price = 0;
          this.$Modal.error({
            title: '错误',
            content: res.message,
          });
        }
        this.originData = this.cloneObj(this.newOrderList);
      });
    },
    rowClassName(row, index) {
      if (!row.isSearchResult && this.searchName) {
        return 'hide-row';
      } else {
        return '';
      }
    },
    handleSearch() {
      // this.newOrderList = this.originData;
      this.newOrderList = this.search(this.postList, {
        commodity_name: this.searchName,
      });
			// this._syncStoreList()
    },
		search(data, argumentObj) {
			let res = data;
			let dataClone = data;
			for (let argu in argumentObj) {
				if (argumentObj[argu].length > 0) {
					res = dataClone.filter((d) => {
						return d[argu].indexOf(argumentObj[argu]) > -1;
					});
					dataClone = res;
				}
			}
			return res;
		},
    getData(item) {
      let obj = {
        in_record_id: item.id,
        purchase_order_com_id: item.purchase_order_com_id,
        id: item.commodity_id,
        num: item.num || 0,
        actual_num: item.actual_num || 0,
        in_price: item.price || item.in_price || 0,
        loss_num: item.loss_num,
        total_price: item.total_price,
        remarks: item.remarks,
        cost_before_in_price: item.cost_before_in_price,
        cost_before_total_price: item.cost_before_total_price,
      };
      if (item.isNew === true) {
        obj.in_record_id = 0;
      }
      if (this.isEnableBatch) {
        obj = {
          ...obj,
          ...this.getBatchInfo(item),
          area_id: item.area_id || 0,
          location_id: item.location_id || 0,
        };
      }
      obj.batch_id = '';
      return obj;
    },
    getCommoditys() {
      let commoditys = [];
      this.postList.filter(item => item.commodity_id).forEach((item) => {
        commoditys.push(this.getData(item));
      });
      console.log(commoditys);
      // deleteList删除的数据列表
      return JSON.stringify(
        commoditys.concat(
          // 会把新增的商品页加入到deleteList, 提交的时候需要去掉
          this.deleteList.filter(k => !k.id.startsWith('row')).map((item) => {
            return {
              ...this.getData(item),
              is_delete: 1,
            };
          }),
        ),
      );
    },
    getTotal() {
      let totalNum = 0;
      this.postList.map((d) => {
        totalNum += parseFloat(d.total_price);
      });
      return parseFloat(totalNum).toFixed(2);
    },
    removeGoods(goods) {
      let itemKey = 'commodity_id';
      this.newOrderList.splice(
        this.newOrderList.findIndex(
          (findItem) => findItem[itemKey] === goods[itemKey],
        ),
        1,
      );
      this.postList.splice(
        this.postList.findIndex(
          (findItem) => findItem[itemKey] === goods[itemKey],
        ),
        1,
      );
      this.originData.splice(
        this.originData.findIndex(
          (findItem) => findItem[itemKey] === goods[itemKey],
        ),
        1,
      );
    },
    updateAudit(type, goods) {
      let costs;
      if (this.$refs.allot) {
        costs = JSON.stringify(this.$refs.allot.getAllotItems());
      }
      const params = {
        costs,
        in_id: this.$route.query.id,
        memo: this.auditDetail.memo,
        in_reason: this.auditDetail.in_reason,
        commodity_list: this.getCommoditys(true),
        is_lock_in_price: this.isFixedPrice ? 1 : 0,
        order_files_link: this.orderFiles.map((item) => item.url).join(','),
        modify_in_time: this.auditDetail.modify_in_time,
				plan_date: this.auditDetail.plan_date,
				is_change_purchase_data: this.isChangePurchaseData,
				...this.purchaseForm
      };
      const action = () => {
        ware.antiAudit(params).then((res) => {
          this.saving = false;
          if (res.status) {
            if (type === 'delete') {
              this.modalSuccess('删除成功');
              this.removeGoods(goods);
            } else {
              // this.$Modal.success({
              //   title: '提示',
              //   content: '保存成功'
              // });
              this.modified = false;
              this.goBack();
            }
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message,
            });
          }
        });
      };

      if (type !== 'delete') {
        let otherMsg = '';
        // 开启同步，且为采购入库或采购入库(联营)
        if (
          +this.sysConfig.is_in_order_sync_receipt === 1 &&
          (+this.auditDetail.in_type === 1 || +this.auditDetail.in_type === 12)
        ) {
          otherMsg = this.auditDetail.receipt_err_info
            ? this.auditDetail.receipt_err_info + '，'
            : '对应采购单的收货信息将更新，';
        }
				// 检查是不是有未填写生产日期且开启了批次的商品, 将商品名统计出来并提示
				const noProductionDateGoods = this.postList.filter(item => !item.production_date && +item.is_batch === 1)
				if (noProductionDateGoods.length) {
					const goodsNames = noProductionDateGoods.map(item => item.commodity_name).join('、')
					this.$Modal.confirm({
						title: '提示',
						content: `<p>以下商品未填写生产日期：${goodsNames}，请填写后再反审核。</p>`,
						onOk: () => {
							this.checkAuditModal = false
						}
					})
					return
				}

        this.$Modal.confirm({
          title: '提示',
          content: `<p>反审核后，后续的出入库单据成本价与成本金额将重新计算，${otherMsg}是否确认操作？</p>`,
          onOk: () => {
            this.saving = true;
            action();
          },
        });
      } else {
        action();
      }
    },
    auditConfirmIn() {
      let me = this;
      if (
        me.postList.filter(
          (item) => +item.total_price !== 0 && +item.actual_num === 0,
        ).length
      ) {
        this.errorNotice('请输入实际入库数量');
        this.checkAuditModal = false;
        return;
      }
      this.$Modal.confirm({
        title: '提示',
        content: '是否确定审核？',
        onOk: () => {
          let commoditys = [];
          me.postList.forEach((item) => {
            let obj = {
              in_record_id: item.id,
              id: item.commodity_id,
              actual_num: item.actual_num || 0,
              remarks: '',
              loss_num: item.loss_num,
              total_price: item.total_price,
            };
            if (this.isEnableBatch) {
              obj = {
                ...obj,
                ...this.getBatchInfo(item),
                area_id: item.area_id || 0,
                location_id: item.location_id || 0,
              };
            }
            obj.batch_id = '';
            commoditys.push(obj);
          });
          let params = {
            in_id: me.$route.query.id,
            commodity_list: JSON.stringify(commoditys),
          };
          ware.auditConfirmIn(params).then((res) => {
            if (res.status) {
              me.$Modal.success({
                title: '提示',
                content: '审核成功',
              });
              me.modified = false;
              me.checkAuditModal = false;
              me.goBack();
            } else {
              me.$Modal.error({
                title: '错误',
                content: res.message,
              });
            }
          });
        },
      });
    },
    addGoods() {
      if (!this.checkModify('添加商品')) {
        return;
      }
      this.showGoodsListModal = true;
    },
    goBack() {
      this.router.go(-1);
    },
    // getTotal() {
    //   let totalNum = 0;
    //   this.postList.map(d => {
    //     totalNum += parseFloat(d.total_price);
    //   });
    //   return parseFloat(totalNum).toFixed(2);
    // },
    isGoodsExist(goodsId) {
      return this.postList.some((item) => item.commodity_id === goodsId);
    },
    addOrder(cid, row, index) {
      if (!this.checkModify()) {
        return;
      }
      let me = this;
      if (this.goodsId) {
        if (!this.isGoodsExist(this.goodsId)) {
          this.addInfo(cid, row, index);
        } else {
          this.$Modal.error({
            title: '提示',
            content: '您已经添加过该商品了',
            onOk: () => {
              me.$refs.selectInput.$el
                .querySelector('input[type="text"]')
                .focus();
            },
          });
        }
      } else {
        this.$Modal.error({
          title: '提示',
          content: '请选择商品',
          onOk: () => {
            me.$refs.selectInput.$el
              .querySelector('input[type="text"]')
              .focus();
          },
        });
      }
    },
    addInfo(cid, row, index) {
      this.orderIds[index] = this.goodsId;
      this.newOrderInfo['actual_num'] = this.goodsNum;
			this.newOrderInfo['commodity_summary'] = this.newOrderInfo['summary'];
      this.newOrderInfo['total_price'] = parseFloat(
        this.newOrderInfo['price'] * this.goodsNum,
      ).toFixed(this.warehouse_total_price_decimal);
      this.newOrderInfo['_total_price'] = this.newOrderInfo['total_price'];
      const inputTaxRate = +this.newOrderInfo['input_tax_rate'];
      this.newOrderInfo['tax_exclusive'] = inputTaxRate
        ? this.newOrderInfo['total_price'] / (1 + inputTaxRate / 100)
        : this.newOrderInfo['total_price'];
      this.newOrderInfo['input_tax'] = inputTaxRate
        ? parseFloat((this.newOrderInfo['tax_exclusive'] * inputTaxRate) / 100)
        : '';
			this.newOrderList[index] = this.postList[index] = this.cloneObj({
				...this.newOrderInfo,
				[this.rowKey]: this.newOrderList[index][this.rowKey],
			});
			this._syncStoreList()
			this.focusNum(index);
      this.goodsId = '';
      this.goodsNum = 1;
    },
    syncList() {
      this.newOrderList = this.newOrderList.map((item) => {
        const postItem = this.postList.find(
          (postItem) => postItem.commodity_id === item.commodity_id,
        );
        if (postItem) {
          return {
            ...postItem,
          };
        }
        return item;
      });
    },
    handlerAdd(orders) {
      let tempIds = [];
      if (orders) {
        orders = orders.filter((good) => !this.isGoodsExist(good.id));
        orders.forEach((item) => {
          item.price = item.average_price;
          item.inPrice = item.in_price;

          item.commodity_name = item.name;
          item.in_no = item.commodity_code;
          item.actual_num = Number(item.amount);
          item.num = 0;
          item.isNew = true;
          item.commodity_id = item.id;
          item.total_price = Number(item.average_price) * Number(item.amount);
          item.area_id = item.default_area_id;
          item.location_id = item.default_location_id;
          item._total_price = item.total_price;
          this.newOrderList.unshift(item);
          tempIds.unshift(item.commodity_id);
        });
      }
      this.initBatchData(this.postList);
      this._syncStoreList();
			console.log(this.postList, 'postList')
    },
		async goInputNum(goodsId) {
			this.goodsId = goodsId;
			if (this.goodsId) {
				const res = await ware.getCommodityInfo({
					commodity_id: this.goodsId,
					store_id: this.storeId
				})

				if (res.status && res.data) {
					const commodity = res.data.commodity;
					this.newOrderInfo = this.cloneObj(commodity);
					this.newOrderInfo['price'] = Number(commodity.average_price);
					this.newOrderInfo['inPrice'] = Number(commodity.average_price);
					this.newOrderInfo['commodity_name'] = commodity.name;
					this.newOrderInfo['out_no'] = commodity.commodity_code;
					this.newOrderInfo['num'] = 0;
					this.newOrderInfo['isNew'] = true;
					this.newOrderInfo.area_id = commodity.default_area_id;
					this.newOrderInfo.location_id = commodity.default_location_id;
				}
			}
		},
    remoteSearch(query) {
      if (query !== '') {
        this.selectLoading = true;
        ware.getBaseCommoditys({ query, unfilter_online: 1 }).then((res) => {
          if (res.status) {
            if (res.data) {
              this.remoteList = res.data.commodities;
            }
            this.selectLoading = false;
          }
        });
        let dropDownDom = this.$refs.selectInput.$el.querySelector(
          '.ivu-select-dropdown',
        );
        dropDownDom.style.display = '';
        dropDownDom.querySelector('li').className += 'ivu-select-item-selected';
      } else {
        this.remoteList = [];
      }
    },
    changeStore(val) {
      this.storeId = val;
      this._getReservoirData();
    },
    getInBoundAudit() {
      let me = this;
      this.tableLoading = true;
      ware.getInBoundAudit({ id: this.$route.query.id }).then((res) => {
        const { data } = res;
        if (res.status) {
          if (data.costs && data.costs.length > 0) {
            this.hasAllot = true;
            this.allotItems = data.costs;
          }
          res.data.store_in_record.forEach(item => {
            item.price_diff_warning = '';
          });
          this.initBatchData(res.data.store_in_record);
          this.orderFiles = res.data.store_in.order_files_link || [];
          this.newOrderList = res.data.store_in_record;
          let { in_type } = res.data.store_in;
					const auditDetail = res.data.store_in;
					const { channel_type, agent_id, provider_id} = auditDetail
					this.purchaseForm = {
						channel_type,
						agent_id,
						provider_id
					};
          this.newOrderList.forEach((item, index) => {
						if (!(this.auditDetail.type_name === '其他入库' || this.auditDetail.type_name === '采购入库' || this.auditDetail.type_name === '退仓入库')) {
							item.hideDel = true;
						}
            item.isNew = false;
            item.total_price = item.model_total_price;
            item.price  = item.model_in_price;
            item.inPrice = item.in_price;
            item.index = index;
            // item.re_location_id_name = item.location_id_name;
            // item.re_area_id_name = item.area_id_name;
            // if([1,3,4].indexOf(+in_type)>-1){
            //   item.actual_num =
            //     Number(item.actual_num) > 0 ? item.actual_num : item.num;
            //   item.amount = Number(item.actual_num);
            // }

            // item.actual_num = Number(item.actual_num) > 0 ? item.actual_num : item.num;

            item.amount = Number(item.actual_num);
            item._total_price = item.total_price;
            me.orderIds.push(item.commodity_id);

            // 生产日期、过期日期固定不能编辑
            // 反审核支持修改批次商品的生产日期, 过期日期
            // item.production_date_disabled = true;
            // item.expired_date_disabled = true;
          });
          this.auditDetail = {
            ...res.data.store_in,
            modify_in_time: res.data.store_in.in_time,
          };
          this.storeId = this.auditDetail.store_id;
          this.postList = this.cloneObj(this.newOrderList);
          this.originData = this.cloneObj(this.newOrderList);
          // this.$watch('orderRemark', function(){this.modified = true}, {deep: true});
          // this.$watch('postList', function(){this.modified = true}, {deep: true});
          this.initTableCols();
          this.tableLoading = false;
        } else {
          this.$Modal.error({
            title: '错误',
            content: res.message,
          });
          this.tableLoading = false;
        }
      });
    },
    /**
     * @description: 删除页面新增的一个商品
     * @param {*} index
     * @return {*}1
     */
    deleteOne(index) {
      this.orderIds.splice(index, 1);
      this.newOrderList.splice(index, 1);
      this.postList.splice(index, 1);
    },
    handleStoreDateChange(val) {
      this.auditDetail.modify_in_time = val;
    },
		handlePlanDateChange (val) {
			this.checkCanSyncReceipt()
			this.auditDetail.plan_date = val
		},
		checkCanSyncReceipt() {
			this.isChangePurchaseData = 1;
			const { is_can_sync_receipt, receipt_err_info } = this.auditDetail
			if (!is_can_sync_receipt && receipt_err_info) {
				this.$Message.warning(receipt_err_info)
			}
		},
		_changePurchase (value) {
			this.checkCanSyncReceipt()
			if (value && value.length) {
				const [purchaseType, purchaseValue] = value
				this.purchaseForm.channel_type = purchaseType
				if (+purchaseType === 1) {
					this.purchaseForm.agent_id = purchaseValue
					this.purchaseForm.provider_id = ''
				} else {
					this.purchaseForm.provider_id = purchaseValue
					this.purchaseForm.agent_id = ''
				}
			} else {
				this.purchaseForm.channel_type = ''
				this.purchaseForm.agent_id = ''
				this.purchaseForm.provider_id = ''
			}
		},
  },
};
</script>

<style lang="less" scoped>
.batchAddBtn {
  position: absolute;
  top: 2px;
  right: 10px;
}
/deep/ .hide-row {
  display: none;
}
.anti-audit-in-store {
	padding: 1px 0;
}
.total-info {
	font-weight: 500;
	font-size: 13px;
	color: rgba(0,0,0,0.85);
	p {
		>span {
			color: #FF6E00;
		}
	}
}
/deep/.after-table {
	padding: 13px 24px;
}
/deep/.ivu-input-disabled {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
/deep/.ivu-select-disabled .ivu-select-selection {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
/deep/ .sdp-table__cell {
  .show_price_title {
    display: flex;
    align-items: center;
    cursor: pointer;
    .icon-lock, .icon-unlock {
      display: inline-block;
      width: 13px;
      height: 13px;
      margin-left: 4px;
    }
  }
  .icon-lock {
    background: url(./images/icon-lock.png);
    background-size: 100% 100%;
  }
  .icon-unlock {
    background: url(./images/icon-unlock.png);
    background-size: 100% 100%;
  }
}
</style>
