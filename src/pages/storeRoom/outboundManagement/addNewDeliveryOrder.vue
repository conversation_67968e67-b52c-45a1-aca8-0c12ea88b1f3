<template>
  <div class="add-new-out-store">
		<DetailPage
			pageType="add"
			title="新增出库"
			:disabledSave="postList.length === 0"
			@on-save="createStock"
		>
			<Form inline label-colon :label-width="82" :disabled="false">
				<base-block title="基础信息" class="base-info">
					<FormItem label="出库仓库" prop="storeId">
						<movee-select
							style="width: 232px"
							url="/superAdmin/StockInManagement/getUserWarehouse"
							:defaultVal="storeId"
							selectFirst="isTrue"
							placeholder="请选择仓库"
							@changeEvent="changeStore"
							needAll="isNot"
						/>
					</FormItem>
					<FormItem label="出库类型">
						<Input  disabled placeholder="其他出库" style="width: 232px" />
					</FormItem>
					<FormItem label="出库原因">
						<movee-select
							style="width: 232px"
							:url="apiUrl.getCustomizeFieldList"
							:request-data="{
                  customize_type: 11
                }"
							placeholder="选择出库原因"
							@changeEvent="changeReason"
							needAll="isNot"
							selectFirst="isTrue"
						/>
					</FormItem>
					<FormItem label="出库时间" v-if="canCustomTime">
						<div style="display: flex;align-items: center">
							<DatePicker
								:value="out_time"
								placeholder="出库时间"
								style="width: 232px"
								format="yyyy-MM-dd HH:mm:ss"
								type="datetime"
								@on-change="handleStoreDateChange"
								@on-ok="handleStoreDateConfirm"
							></DatePicker>
							<Tooltip
								style="font-weight: 500;position: absolute;right: -10px"
								max-width="170"
								transfer
								placement="top"
								content="不填写时，默认获取单据的审核时间作为出库单的出库时间，手动填写时按照填写的时间作为出库单的出库时间"
							>
								<i
									class="iconfont icon-tishifu icontip"
									style="left: -6px;position: relative;font-size: 12px;"
								></i>
							</Tooltip>
						</div>

					</FormItem>
					<FormItem label="制单人">
						{{ operator }}
					</FormItem>
				</base-block>
				<base-block
					title="出库单商品清单"
					style="margin-top: 25px"
				>
					<Row type="flex" justify="start" :gutter="20" class="tmt12">
						<Col span="6">
							<SButton styleType="btnStyleForAdd"  @click="addGoods" >
								批量添加
							</SButton>
						</Col>
					</Row>
					<EditableTable
						class="mt14"
						rowKey="id"
						:max-height="getEditTableHeight()"
						ref="orderTable"
						:columns="columns"
						:isShowRecordEditor="true"
						enterAsDown
						@on-insert="onInsert"
						@on-delete="onDelete"
						:data="newOrderList"
						:virtualScroll="true"
						:virtualScrollBuff="{ top: 2100, bottom: 2100 }"
					>
						<template #after-table-right>
							<p>
								合计数量 : &nbsp;&nbsp;<span class="table-total-num mr20">{{ totalOutStoreNum || 0 }}</span>
								<template v-if="authority.hasOutAmountAuth()">合计金额 : &nbsp;&nbsp;<span class="table-total-amount">{{ calTotal || 0 }}</span></template>
							</p>
						</template>
					</EditableTable>
				</base-block>
				<base-block
					title="其他信息"
					style="margin-top: 25px"
				>
					<FormItem :label-width="46" label="备注" style="width: 80%;">
						<Input
              style="width: 100%;"
							v-model="orderRemark"
							type="textarea"
              :maxlength="512"
							:autosize="{ minRows: 3, maxRows: 8 }"
							placeholder="请输入备注信息，长度 < 512"
						/>
					</FormItem>
				</base-block>
			</Form>

			<goods-list-modal
				v-model="showGoodsListModal"
				modalType="storeGoods"
				:noInput="true"
				@on-add="handlerAdd"
				:selectedGoods="newOrderList"
				:storeId="storeId"
        :isShowPrice="authority.hasOutAmountAuth()"
			><div></div
			></goods-list-modal>
			<ChooseBatch ref="batch" :isShowPrice="authority.hasOutAmountAuth()"></ChooseBatch>
		</DetailPage>
  </div>
</template>

<script>
import NumberInput from '@components/basic/NumberInput';
import moveeSelect from '@components/basic/moveeSelect';
import GoodsListModal from '@components/order/goodsListModal';
import ware from '@api/storeRoom.js';
import GoodsBatchMixin from '@/mixins/GoodsBatch';
import apiUtil from '@/api/util';
import ConfigMixin from '@/mixins/config';
import { getEditTableHeight } from '@/util/common';
import pos from '../../appCenter/pos/index.vue'
import EditableTable from '@/components/editable-table';
import CommoditySelect from '@/components/common/CommoditySelect'
import DetailPage from '@/components/detail-page';
import SBlock from '@/components/s-block';
import SButton from '@/components/button';
import { cloneDeep } from 'lodash-es'
import authority from '@/util/authority.js';

export default {
  mixins: [GoodsBatchMixin, ConfigMixin],
  watch: {
    sysConfig: {
      immediate: true,
      handler() {
        // 逻辑：开启了实时售卖库存，显示【现有库存】【】
        const addColumns = [];
        const stockColumn = {
          title: '现有库存',
          key: 'stock',
          width: 120,
          render: (h, params) => {
            let data = params.row;
            return h('span', null, data.stock);
          }
        };
        const sellStockTextColumn = {
          title: '售卖库存',
          key: 'sell_stock_text',
          width: 80,
          align: 'center'
        };
        const stockColumnIndex = this.columns.findIndex(item => item.key === 'stock');
        const sellStockColumnIndex = this.columns.findIndex(item => item.key === 'sell_stock_text');
        const toIndex = this.columns.findIndex(item => item.key === 'unit_convert_text');
        if (this.isOpenRealTimeSellStock) {
          if (stockColumnIndex === -1 && sellStockColumnIndex === -1) {
            addColumns.push(stockColumn);
            addColumns.push(sellStockTextColumn);
            this.columns.splice(toIndex, 0, ...addColumns);
          }
        } else {
          if (stockColumnIndex === -1) {
            addColumns.push(stockColumn);
            this.columns.splice(toIndex, 0, ...addColumns);
          }
        }
        this.initTableCols();
      }
    }
  },
  data() {
    return {
      saveLoading: false,
      modified: false,
      selectLoading: false,
      showGoodsListModal: false,
      goodsNum: 1,
      columns: [
				{
					type: 'titleCfg',
					titleType: 'out_order_detail',
					width: 62,
					style: {
						width: '62px !important',
					},
					align: 'center',
					fixed: 'left'
				},
        {
          title: '序号',
          type: 'index',
          width: 70,
					key: 'index',
          align: 'center',
          fixed: 'left'
        },
        {
          title: '商品',
          width: 250,
          key: 'commodity_name',
          fixed: 'left',
					render: (h, params) => {
						const { row, index } = params
						return (
							<CommoditySelect
                class="name"
								commodityName={row.name}
								dataProvider={this.apiUrl.getBaseCommoditys}
								params={{
									unfilter_online: 1,
									un_limit_status: 0
								}}
								trimInput={true}
								commodityIdKey="commodity_id"
								commodityNameKey="name"
								selectedData={this.dataList}
								onOn-change={async (cid) => { await this.goInputNum(cid); this.addOrder(cid, row, index) }}
								onOn-enter={() => this._insertAt(index)}
								style={{ width: '174px' }}
								inputProps={{
									type: 'textarea',
									rows: 1,
									autosize: { minRows: 1, maxRows: 2.2 }
								}}
								slot-type="purchase"
							></CommoditySelect>
						)
					}
        },
				{
					title: '商品编码',
					key: 'commodity_code',
				},
        {
          title: '描述',
          key: 'commodity_summary',
          width: 80
        },
        {
          title: '单位',
          key: 'unit',
          width: 80
        },
        {
          title: '转换系数',
          key: 'unit_convert_text',
          width: 120
        },
        {
          title: '出库数量',
          key: 'num',
          width: 150,
          render: (h, params) => {
            let data = params.row,
              me = this;
            if (!authority.hasOutAmountAuth()) {
              let {
                num,
                averagePrice
              } = params.row;
              averagePrice = isNaN(averagePrice) ? 0 : Number(averagePrice);
              num = isNaN(num) ? 0 : Number(num);
              const totalPrice = averagePrice
                .mul(num)
                .toFixed(this.warehouse_total_price_decimal);
              this.postList[params.index] && this.$set(this.postList[params.index], 'total_price', totalPrice || 0);
            }
            return h(NumberInput, {
              class: 'num',
              props: {
                value: Number(data.num),
                min: 0,
                max: 999999999.99,
                precision: 2,
              },
              style: {
                width: '100%'
              },
              on: {
                'on-change'(val) {
                  params.row.num = val;
                  me.postList[params.index].num = params.row.num;
                },
                'on-focus'(event) {
                  event.target.select();
                },
                'on-enter': () => {
									this.newOrderList[params.index].num = params.row.num;
									this.postList[params.index].num = params.row.num;
									this._syncStoreList();
                  this._insertAt(params.index)
                }
              }
            });
          }
        },
        {
          title: '单价',
          key: 'show_price',
          width: 150,
          isShow: authority.hasOutAmountAuth(),
          renderHeader: h => {
            return h('div', [
              h('span', '单价'),
              h(
                'Tooltip',
                {
                  props: {
                    content:
                      '参考成本，由审核时的库存总金额/库存数量得出库存的均价(保留4位小数),参与金额计算',
                    placement: 'top',
                    transfer: true,
                    maxWidth: '200'
                  }
                },
                [
                  h('i', {
                    class: 'iconfont icon-tishifu icontip ml5',
										style: 'font-size: 12px;'
                  })
                ]
              )
            ]);
          },
          render: (h, params) => {
            return h('span', params.row.price);
          }
        },
        {
          title: '金额(元)',
          key: 'total_price',
          width: 150,
          isShow: authority.hasOutAmountAuth(),
          render: (h, params) => {
            let {
              // price,
              num,
              averagePrice
            } = params.row;
            averagePrice = isNaN(averagePrice) ? 0 : Number(averagePrice);
            num = isNaN(num) ? 0 : Number(num);
            const totalPrice = averagePrice
              .mul(num)
              .toFixed(this.warehouse_total_price_decimal);
						this.postList[params.index] && this.$set(this.postList[params.index], 'total_price', totalPrice || 0);
            return h('span', totalPrice);
          }
        },
        // {
        //   title: '库区',
        //   width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
        //   key: apiUtil.getIsOpenStoreMGT() ? 're_area_id_name' : ''
        // },
        // {
        //   title: '库位',
        //   width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
        //   key: apiUtil.getIsOpenStoreMGT() ? 're_location_id_name' : ''
        // },
        {
          title: '库区库位',
          isShow: apiUtil.getIsOpenStoreMGT(),
          key: 'area_location',
          width: 170
        },
        {
          title: '备注',
          key: 'remarks',
          minWidth: 164,
          render: (h, params) => {
            let data = params.row,
              me = this;
            return h('i-input', {
              props: {
                value: data.remarks
              },
              style: {
                width: '100%'
              },
              on: {
                'on-change'(event) {
                  params.row.remarks = event.target.value;
                },
                'on-focus'(event) {
                  event.target.select();
                },
                'on-blur'() {
                  me.postList[params.index].remarks = params.row.remarks;
                },
								'on-enter': () => {
									this.newOrderList[params.index].remarks = params.row.remarks;
									this.postList[params.index].remarks = params.row.remarks;
									this._syncStoreList();
									this._insertAt(params.index)
								}
              }
            });
          }
        },
        {
          title: '占用库存',
          key: 'occupy_inventory'
        },
        {
          title: '操作',
					type: 'action',
          key: 'action',
          width: 120,
          align: 'center',
          render: (h, params) => {
            let opList = [];
            if (this.isEnableBatch && this.isGoodsEnableBatch(params.row)) {
              opList.push(
                h(
                  'span',
                  {
                    style: {
                      color: '#3cc347',
                      cursor: 'pointer',
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        let config = {
													isBatchOutStock: params.row.fixed_batch_num,
													batch_data: params.row.batch_remark || [],
                          name: params.row.name,
                          unit: params.row.unit,
                          amount: params.row.num,
                          filters: {
                            commodity_id: params.row.commodity_id
                          },
                          defaultSelected: params.row.batch_id
                            .split(',')
                            .map(no => {
                              return {
                                id: no
                              };
                            })
                        };
                        this.$refs.batch.chooseBatch(config, (batchItems, isBatchOutStock) => {
													// 内部返回的状态更新父级的状态
													params.row.fixed_batch_num = isBatchOutStock
													this.postList[params.index].fixed_batch_num = isBatchOutStock
													this.newOrderList[params.index].fixed_batch_num = isBatchOutStock
													// 更内多批次信息
													params.row.batch_remark = batchItems.map(item => {
														return {
															id: item.batch_id,
															num: item.outStockNum
														}
													}) || [];
													this.postList[params.index].batch_remark = params.row.batch_remark
													this.newOrderList[params.index].batch_remark = params.row.batch_remark

                          this.setListDataBatchInfo(
                            params,
                            batchItems,
                            'postList'
                          );
                          this._syncStoreList();
                          // 单价取批次价
                          if (
                            batchItems &&
                            Array.isArray(batchItems) &&
                            batchItems.length > 0
                          ) {
                            let batch = batchItems[0];
                            this.newOrderList[params.index].price = batch.price;
                            this.newOrderList[params.index].averagePrice =
                              batch.average_price;
                            this.newOrderList[params.index].area_location =
                              batch.area_name + batch.location_name;
                            this.postList[params.index].price = batch.price;
                            this.postList[params.index].averagePrice =
                              batch.average_price;

                            this.postList[params.index].area_id = batch.area_id;
                            this.postList[params.index].location_id =
                              batch.location_id;
                          }
                        });
                      }
                    }
                  },
                  '设置批次'
                )
              );
            }
            return h(
							'div',
							opList);
          }
        }
      ],
      storeId: '',
      out_time: '',
      orderRemark: '',
      goodsId: '',
      delivery_type: 1,
      newOrderList: [],
      postList: [],
      remoteList: [],
      newOrderInfo: '',
      orderIds: [],
      operator: sessionStorage['userName'],
      out_reason: '',
      rowKey: 'id',
      authority
    };
  },
  created() {
    this.initTableCols();
    this.$watch(
      'orderRemark',
      function() {
        this.modified = true;
      },
      { deep: true }
    );
    this.$watch(
      'postList',
      function() {
        this.modified = true;
      },
      { deep: true }
    );
  },
  mounted() {
    this.storeId = sessionStorage['storeId'];
  },
  computed: {
    totalOutStoreNum() {
      const num = this.postList.reduce((total, item) => {
        return total + Number(item.num);
      }, 0);
      return parseFloat(num).toFixed(this.warehouse_total_price_decimal);
    },
    pos () {
      return pos
    },
		canCustomTime() {
			return this.sysConfig && +this.sysConfig.modify_store_time === 1
		},
    calTotal() {
      let totalNum = 0;
      this.postList.map(d => {
        totalNum += parseFloat(d.total_price);
      });
      return parseFloat(totalNum).toFixed(this.warehouse_total_price_decimal);
    }
  },
  methods: {
    getEditTableHeight,
    initTableCols() {
      let cols = this.deepClone(this.columns);
      cols = this.initChooseBatchColumns({
        columns: this.columns,
        prevColumn: 'total_price'
      });
      this.columns = cols.filter(col => col.isShow !== false);
    },
		onInsert (insertedRow, index) {
      // this._syncStoreList();
		},
		onDelete (row) {
			// 同时删除orderIds中的id
			const index = this.orderIds.indexOf(row.commodity_id)
			if (index > -1) {
				this.orderIds.splice(index, 1)
			}
			this._syncStoreList();
		},
    addGoods() {
      this.showGoodsListModal = true;
    },
    goBack() {
      this.router.go(-1);
    },
    getTotal() {
      return this.calTotal;
    },
		/**
		 * @description: 同步 newOrderList 和 postList 数据
		 */
		_syncStoreList () {
			const postListCopy = cloneDeep(this.newOrderList)
			postListCopy.forEach(goods => {
				const storeGoods = this.postList.find(storeGoods => storeGoods && storeGoods[this.rowKey] === goods[this.rowKey])
				if (storeGoods) Object.assign(goods, storeGoods)
			})
			this.newOrderList = postListCopy
			this.postList = cloneDeep(postListCopy)
		},
    addOrder(cid, row, index) {
      let me = this;
      if (this.goodsId) {
        if (!this.orderIds.includes(this.goodsId)) {
          this.addInfo(cid, row, index);
        } else {
          this.$Modal.error({
            title: '提示',
            content: '您已经添加过该商品了',
            onOk: () => {
              me.$refs.selectInput.$el
                .querySelector('input[type="text"]')
                .focus();
            }
          });
        }
      } else {
        this.$Modal.error({
          title: '提示',
          content: '请选择商品',
          onOk: () => {
            me.$refs.selectInput.$el
              .querySelector('input[type="text"]')
              .focus();
          }
        });
      }
    },
    changeReason(val) {
      this.out_reason = val;
    },
    addInfo(cid, row, index) {
      this.orderIds[index] = this.goodsId;
      this.newOrderInfo['num'] = this.goodsNum;
			this.newOrderInfo['commodity_summary'] = this.newOrderInfo.summary;
      this.newOrderInfo.commodity_id = this.goodsId;
      this.newOrderInfo.remarks = '';
      this.newOrderInfo.id = row.id;
			this.newOrderList[index] = this.cloneObj(this.newOrderInfo);
      // 不理解下面的_syncStoreList方法为啥会用postList去覆盖newOrderList，这不就导致数据又重置了
      this.postList[index] = this.cloneObj(this.newOrderInfo);
      this.initBatchData(this.newOrderList);
      this._syncStoreList();
      this.goodsId = '';
      this.goodsNum = 1;
      this.focusNum(index);
    },
    focusName(index) {
      this.$nextTick(() => {
        const $currentRow = this.$refs.orderTable.$el.querySelectorAll('tbody tr')[index];
        if ($currentRow) {
          $currentRow
            .querySelector('.name')
            .querySelector('textarea')
            .focus();
        }
      });
    },
    focusNum(index) {
      this.$nextTick(() => {
        const $currentRow = this.$refs.orderTable.$el.querySelectorAll('tbody tr')[index];
        if ($currentRow) {
          $currentRow
            .querySelector('.num')
            .querySelector('input')
            .focus();
        }
      });
    },
    handlerAdd(orders) {
      if (orders) {
        orders.forEach(item => {
					console.log(item)
          item.commodity_id = item.id;
          item.price = isNaN(item.show_average_price)
            ? 0
            : item.show_average_price;
          item.averagePrice = isNaN(item.average_price)
            ? 0
            : item.average_price;
          item.area_location = '';
          item.remarks = '';
					item.fixed_batch_num = false
					item.num = item.amount
          this.newOrderList.unshift(item);
          this.orderIds.unshift(item.commodity_id);
        });
      }
      this.initBatchData(this.newOrderList);
      this._syncStoreList();
    },
		_insertAt (index) {
			if (index === this.newOrderList.length - 1) { // 只在最后一行添加
				setTimeout(() => {
					this.$refs.orderTable.insertAt(index)
          this.focusName(index + 1)
				})
			}
		},
    async goInputNum(goodsId) {
      this.goodsId = goodsId;
			const res = await ware.getCommodityInfo({
				commodity_id: goodsId,
				store_id: this.storeId
			})

			if (res.status && res.data) {
				const commodity = res.data.commodity;
				this.newOrderInfo = this.cloneObj(commodity);
				this.newOrderInfo['price'] = isNaN(commodity.show_average_price)
					? 0
					: Number(commodity.show_average_price);
				this.newOrderInfo['averagePrice'] = isNaN(commodity.average_price)
					? 0
					: Number(commodity.average_price);
				this.newOrderInfo.area_location = '';
			}
    },
		handleStoreDateChange(value) {
			this.out_time = value;
		},
		handleStoreDateConfirm() {
			const dateTime = new Date(this.out_time).getTime();
			// 如果设置的时间早于当前时间的31天,就提示：自定义出库时间不能早于当前时间往前推31天
			if (dateTime < new Date().getTime() - 31 * 24 * 60 * 60 * 1000) {
				console.log(999)
				this.$Modal.error({
					title: '提示',
					content: '自定义出库时间不能早于当前时间往前推31天'
				});
				this.out_time = '';
			}
		},
    remoteSearch(query) {
      if (query !== '') {
        this.selectLoading = true;
        ware
          .getBaseCommoditys({
            query,
            unfilter_online: 1,
            store_id: this.storeId,
            un_limit_status: 0
          })
          .then(res => {
            if (res.status) {
              if (res.data) {
                this.remoteList = res.data.commodities;
              }
              this.selectLoading = false;
            }
          });
        let dropDownDom = this.$refs.selectInput.$el.querySelector(
          '.ivu-select-dropdown'
        );
        dropDownDom.style.display = '';
        dropDownDom.querySelector('li').className += 'ivu-select-item-selected';
      } else {
        this.remoteList = [];
      }
    },
    changeStore(val) {
      this.storeId = val;
      sessionStorage['storeId'] = val;
			const newOrderList = this.newOrderList.filter(item => item.commodity_id)
      if (newOrderList.length) {
        this.$Modal.confirm({
          title: '修改仓库',
          content: '<p>修改仓库之后将清空已加入的商品列表</p>',
          onOk: () => {
            this.newOrderList = [];
            this.postList = [];
            this.orderIds = [];
            this.orderRemark = '';
            if (this.$refs.orderTable) {
              this.$nextTick(() => {
                this.$refs.orderTable._initTableData();
              });
            }
          }
        });
      }
    },
		// 检车出库数量和指定批次数量是否一致
		checkBatchInfo(item, batchInfo) {
			let batchNum = batchInfo.reduce((total, item) => {
				return total + Number(item.num)
			}, 0)
			return batchNum === Number(item.num)
		},
    createStock() {
      this.saveLoading = true;
      let commoditys = [];
			let batchCheckInfo = [];
			const postList = this.postList.filter(item => item.commodity_id)
      postList.forEach(item => {
        let obj = {
          id: item.commodity_id,
          batch_no: item.batch_no,
          batch_id: item.batch_id,
          num: item.num,
          remarks: item.remarks,
          price: item.averagePrice,
          total_price: item.total_price,
          area_id: item.area_id || 0,
          location_id: item.location_id || 0,
					fixed_batch_num: item.fixed_batch_num || false,
					batch_remark: item.batch_remark || []
        };
        commoditys.push(obj);
				if (item.fixed_batch_num && !this.checkBatchInfo(obj, obj.batch_remark)) {
					batchCheckInfo.push({
						name: item.name,
						batch_remark: obj.batch_remark
					})
				}
      });

			if (batchCheckInfo.length) {
				this.$Message.error({
					content: `商品${batchCheckInfo.map(item => item.name).join(',')}指定批次出库数量与实际出库数量不相等，请重新调整！`,
					duration: 5
				})
				this.saveLoading = false;
				return;
			}

      let params = {
        total_price: this.getTotal(),
        memo: this.orderRemark,
        store_id: this.storeId,
        out_reason: this.out_reason,
				out_time: this.out_time,
        commoditys: JSON.stringify(commoditys)
      };
      ware
        .createStockOut(params)
        .then(res => {
          if (res.status) {
            this.$Modal.success({
              title: '提示',
              content: '创建成功'
            });
            this.modified = false;
            this.goBack();
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
        .finally(() => {
          this.saveLoading = false;
        });
    },

  },
  beforeRouteLeave(to, from, next) {
    this.newOrderList = [];
    this.postList = [];
    this.orderIds = [];
    this.orderRemark = '';
    if (this.modified) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function() {
          next();
        },
        onCancel: function() {
          next(false);
        }
      });
    } else {
      next();
    }
  },
  components: {
		SBlock,
		DetailPage,
		EditableTable,
    moveeSelect,
    NumberInput,
    GoodsListModal,
		CommoditySelect,
		SButton
  },
	beforeDestroy() {
		window.removeEventListener('keydown', () => {})
    this.$Modal.remove();
	}
};
</script>

<style scoped>
.batchAddBtn {
  position: absolute;
  top: 2px;
  right: 10px;
}
</style>
<style lang="less" scoped>
.scroll-table {
  /deep/table {
    min-width: 100% !important;
  }
  /deep/.ivu-table-body {
    overflow: auto !important;
  }
}
/deep/.after-table {
	padding: 13px 24px;
}
/deep/.ivu-input-disabled {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
/deep/.ivu-select-disabled .ivu-select-selection {
	background-color: #f7f7f7;
	color: rgba(0, 0, 0, .4);
}
</style>
