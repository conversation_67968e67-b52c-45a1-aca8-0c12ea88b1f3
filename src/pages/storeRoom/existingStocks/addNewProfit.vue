<template>
  <div>
    <div class="content-wrap basePadding">
      <Tabs><TabPane label="基本信息"></TabPane></Tabs>
      <Form :label-width="80">
        <Row type="flex" justify="start" :gutter="20" class="topRow">
          <Col span="6">
            <FormItem label="仓库" prop="storeId">
              <movee-select
                url="/superAdmin/StockInManagement/getUserWarehouse"

                :defaultVal="storeId"
                selectFirst="isTrue"
                placeholder="请选择仓库"
                @changeEvent="changeStore"
                needAll="isNot"
              >

              </movee-select>
            </FormItem>
          </Col>
        </Row>

        <Tabs><TabPane label="成本调整商品清单"></TabPane></Tabs>
        <Row type="flex" justify="start" :gutter="20" class="topRow">
          <Col span="6">
            <FormItem label="商品">
              <Select
                ref="selectInput"
                clearable
                v-model="goodsId"
                placeholder="请选商品名/助记码/编码/别名/关键字"
                filterable
                remote
                @on-change="goInputNum"
                :remote-method="remoteSearch"
                :loading="selectLoading"
              >
                <Option
                  :label="
                    `${option.name} ${option.unit_sell} ${option.summary}`
                  "
                  v-for="(option, index) in remoteList"
                  :value="option.commodity_id"
                  :key="index"
                >
                  <p>
                    {{ option.name }} {{ option.unit_sell }}
                    <span v-if="option.summary">({{ option.summary }})</span>
                  </p>
                  <p style="color: #999999">{{ option.commodity_code }}</p>
                </Option>
              </Select>
            </FormItem>
            <Button type="success" @click="addGoods" class="batchAddBtn"
              ><Icon type="md-add" size="20"></Icon
            ></Button>
          </Col>
          <Col span="6">
            <FormItem label="新成本价">
              <NumberInput
                v-model="goodsPrice"
                placeholder="请输入价格"
                style="width: 100%"
                :min="0"
                ref="orderInput"
                @on-enter="addOrder"
              />
            </FormItem>
          </Col>
          <Col span="1">
            <Button type="primary" @click="addOrder" icon="md-add"
              >&nbsp;&nbsp;添 加</Button
            >
          </Col>
        </Row>
        <Table
          :row-class-name="rowClassName"
          :columns="columns"
          :data="newOrderList"
          border
          ref="orderTable"
        ></Table>
      </Form>
      <Row class="orderRemark">
        <Col span="1">备注: </Col>
        <Col span="23">
          <Input
            v-model="orderRemark"
            type="textarea"
            :autosize="{ minRows: 1, maxRows: 8 }"
            placeholder="请输入备注信息，长度 < 100"
          />
        </Col>
      </Row>
    </div>
    <Row class="fixedBtns">
      <Col span="6" align="left">
        <Button
          type="primary"
          @click="createADJ"
          :disabled="postList.length === 0"
          >保 存</Button
        >&nbsp;&nbsp; <Button @click="goBack">取 消</Button>&nbsp;&nbsp;
      </Col>
    </Row>
    <goods-list-modal
      v-model="showGoodsListModal"
      modalType="storeGoods"
      :storeId="storeId"
      :noInput="true"
      url="/superAdmin/Warehouse/ajaxStoreList"
      @on-add="handlerAdd"
      :selectedGoods="newOrderList"
    ><div></div></goods-list-modal>
    <ChooseBatch :max-select-count="1" ref="batch" :isShowPrice="authority.hasAvailableStockAmountAuth()"></ChooseBatch>
  </div>
</template>

<script>
import NumberInput from '@components/basic/NumberInput';
import moveeSelect from '@components/basic/moveeSelect';
import GoodsListModal from '@components/order/goodsListModal';
import ware from '@api/storeRoom.js';
import GoodsBatchMixin from '@/mixins/GoodsBatch';
import ConfigMixin from '@/mixins/config';
import authority from '@/util/authority';
import { debounce } from 'lodash-es';

export default {
  mixins: [GoodsBatchMixin, ConfigMixin],
  watch: {
    sysConfig() {
      this.initTableCols();
    }
  },
  data() {
    return {
      isSaved: false,
      modified: false,
      selectLoading: false,
      showGoodsListModal: false,
      goodsPrice: 0,
      columns: [
        {
          title: '序号',
          type: 'index',
          width: 70,
          align: 'center'
        },
        {
          title: '商品',
          key: 'name',
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('span', {}, data.name),
              h(
                'div',
                {
                  style: {
                    color: '#999999'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '描述',
          key: 'summary'
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '现有库存',
          key: 'existing'
        },
        {
          title: '原成本价',
          key: 'average_price',
          show: authority.hasAvailableStockAmountAuth(),
          render: (h, params) => {
            return h(
              'span',
              Number(params.row.average_price).toFixed(this.warehouse_unit_price_decimal)
            );
          }
        },
        {
          title: '原成本金额',
          key: 'old_total',
          show: authority.hasAvailableStockAmountAuth(),
          render: (h, params) => {
            return h(
              'span',
              (params.row.existing * params.row.average_price).toFixed(this.warehouse_total_price_decimal)
            );
          }
        },
        {
          title: '新成本价',
          key: 'new_average_price',
          show: authority.hasAvailableStockAmountAuth(),
          render: (h, params) => {
            let data = params.row,
              me = this;
            return h(NumberInput, {
              props: {
                value: Number(data.new_average_price).toFixed(me.warehouse_unit_price_decimal),
                min: 0,
                precision: me.warehouse_unit_price_decimal
              },
              style: {
                width: '100%'
              },
              on: {
                'on-change'(val) {
                  params.row.new_average_price = val;
                  params.row.balance = parseFloat(
                    params.row.new_total - params.row.old_total
                  ).toFixed(me.warehouse_total_price_decimal);
                },
                'on-focus'(event) {
                  event.target.select();
                  // me.hightlightRow(params.index, '#03ac54');
                },
                'on-blur'() {
                  me.postList[params.index].new_average_price =
                    params.row.new_average_price;
                },
                'on-enter'(event) {
                  me.goNextInput(params.index, me.newOrderList.length, event);
                }
              }
            });
          }
        },
        {
          title: '新成本金额',
          key: 'new_total',
          show: authority.hasAvailableStockAmountAuth(),
          render: (h, params) => {
            return h(
              'span',
              (params.row.existing * params.row.new_average_price).toFixed(this.warehouse_total_price_decimal)
            );
          }
        },
        {
          title: '差额',
          key: 'balance',
          show: authority.hasAvailableStockAmountAuth(),
          render: (h, params) => {
            const newTotal = (
              params.row.existing * params.row.new_average_price
            ).toFixed(this.warehouse_total_price_decimal);
            const oldTotal = (
              params.row.existing * params.row.average_price
            ).toFixed(this.warehouse_total_price_decimal);
            const balance = (newTotal - oldTotal).toFixed(this.warehouse_total_price_decimal);
            return h('span', balance);
          }
        },
        {
          title: '备注',
          key: 'remark',
          render: (h, params) => {
            let data = params.row,
              me = this;
            return h('Input', {
              props: {
                value: data.remark,
                placeholder: '输入备注'
              },
              style: {
                width: '100%'
              },
              on: {
                'on-change'(event) {
                  params.row.remark = event.target.value;
                },
                'on-focus'(event) {
                  event.target.select();
                  // me.hightlightRow(params.index, '#03ac54');
                },
                'on-blur'() {
                  me.postList[params.index].remark = params.row.remark;
                }
              }
            });
          }
        },
        {
          title: '操作',
          key: 'action',
          width: 120,
          align: 'center',
          render: (h, params) => {
            let opList = [];
            if (this.isEnableBatch && this.isGoodsEnableBatch(params.row)) {
              opList.push(
                h(
                  'span',
                  {
                    style: {
                      color: '#03ac54',
                      cursor: 'pointer',
                      marginRight: '5px'
                    },
                    on: {
                      click: () => {
                        let config = {
                          isOut: false,
                          amount: params.row.amount,
                          name: params.row.name,
                          unit: params.row.unit,
                          filters: {
                            commodity_id: params.row.commodity_id
                          },
                          defaultSelected: params.row.batch_id
                            .split(',')
                            .map(id => {
                              return { id };
                            })
                        };
                        this.$refs.batch.chooseBatch(config, batchItems => {
                          this.setListDataBatchInfo(
                            params,
                            batchItems,
                            'postList'
                          );
                          // 单价取批次价
                          if (
                            batchItems &&
                            Array.isArray(batchItems) &&
                            batchItems.length > 0
                          ) {
                            let batch = batchItems[0];
                            params.row.existing = batch.curr_stock;
                            params.row.average_price = batch.price;
                            this.postList[params.index].existing =
                              batch.curr_stock;
                            this.postList[params.index].average_price =
                              batch.price;
                            this.cancelBatchWarning(params.row.commodity_id);
                          }
                          this.syncList();
                        });
                      }
                    }
                  },
                  '设置批次'
                )
              );
            }
            opList.push(
              h(
                'span',
                {
                  style: {
                    cursor: 'pointer',
                    color: 'red'
                  },
                  on: {
                    click: () => {
                      this.newOrderList.splice(params.index, 1);
                      this.orderIds.splice(params.index, 1);
                      this.syncList();
                    }
                  }
                },
                '删除'
              )
            );
            return h('div', opList);
          }
        }
      ],
      storeId: '',
      type: 1,
      orderRemark: '',
      goodsId: '',
      delivery_type: 1,
      newOrderList: [],
      postList: [],
      remoteList: [],
      newOrderInfo: '',
      orderIds: [],
      authority,
    };
  },
  created() {
    this.initTableCols();
  },
  mounted() {
    this.storeId = sessionStorage['storeId'];
  },
  methods: {
    initTableCols() {
      this.columns = this.initChooseBatchColumns({
        columns: this.columns,
        prevColumn: 'unit'
      });
    },
    addGoods() {
      this.showGoodsListModal = true;
    },
    goBack() {
      this.router.push({
        path: '/storeRoom/existingStockList',
        query: { tabName: 'profit' }
      });
    },
    addOrder() {
      let me = this;
      if (this.goodsId) {
        if (!this.orderIds.includes(this.goodsId)) {
          this.addInfo();
        } else {
          this.$Modal.error({
            title: '提示',
            content: '您已经添加过该商品了',
            onOk: () => {
              me.$refs.selectInput.$el
                .querySelector('input[type="text"]')
                .focus();
            }
          });
        }
      } else {
        this.$Modal.error({
          title: '提示',
          content: '请选择商品',
          onOk: () => {
            me.$refs.selectInput.$el
              .querySelector('input[type="text"]')
              .focus();
          }
        });
      }
    },
    syncList() {
      let list = this.deepClone(this.newOrderList);
      list.forEach(item => {
        item.batch_no = '';
        item.batch_id = '';
        let postItem = this.postList.find(
          findItem => findItem.commodity_id === item.commodity_id
        );
        if (postItem) {
          item.batch_no = postItem.batch_no;
          item.batch_id = postItem.batch_id;
          item.new_average_price = postItem.new_average_price;
          item.remark = postItem.remark;
          item.average_price = postItem.average_price;
          item.existing = postItem.existing;
        }
      });
      this.newOrderList = list;
      this.postList = this.deepClone(list);
    },
    addInfo() {
      this.orderIds.unshift(this.goodsId);
      this.newOrderInfo['new_average_price'] = this.goodsPrice;
      this.newOrderInfo['new_total'] = parseFloat(
        this.goodsPrice * this.newOrderInfo.existing
      ).toFixed(this.warehouse_total_price_decimal);
      this.newOrderInfo['balance'] = parseFloat(
        this.newOrderInfo.new_total - this.newOrderInfo.old_total
      ).toFixed(this.warehouse_total_price_decimal);
      this.newOrderList.unshift(this.cloneObj(this.newOrderInfo));
      this.syncList();
      this.goodsId = '';
      this.goodsPrice = 0;
      this.$refs.selectInput.$el.querySelector('input[type="text"]').select();
    },
    handlerAdd(orders) {
      if (orders) {
        orders.forEach(item => {
          item.commodity_id = item.id;
          item.remark = '';
          item.old_total = parseFloat(
            Number(item.average_price) * Number(item.existing)
          ).toFixed(this.warehouse_total_price_decimal);
          item.new_total = item.old_total;
          item.balance = 0;
          item.new_average_price = Number(item.average_price);
          this.newOrderList.unshift(item);
          this.orderIds.push(item.commodity_id);
        });
      }
      this.syncList();
    },
    goInputNum(goodsId) {
      this.goodsId = goodsId;
      if (this.goodsId) {
        ware
          .getCommodityInfo({
            commodity_id: this.goodsId,
            store_id: this.storeId
          })
          .then(res => {
            if (res.status && res.data) {
              let commodity = res.data.commodity;
              this.newOrderInfo = this.cloneObj(commodity);
              this.newOrderInfo['old_total'] = parseFloat(
                commodity.average_price * commodity.existing
              ).toFixed(this.warehouse_total_price_decimal);
              this.newOrderInfo['remark'] = '';
              this.goodsPrice = Number(commodity.average_price).toFixed(this.warehouse_unit_price_decimal);
              this.$refs.orderInput.$el.querySelector('input').value = Number(
                commodity.average_price
              );
              this.$refs.orderInput.$el.querySelector('input').select();
            }
          });
      }
    },
    remoteSearch: debounce(function (query) {
      if (query !== '') {
        this.selectLoading = true;
        ware
          .getBaseCommoditys({
            query,
            unfilter_online: 1,
            store_id: this.storeId
          })
          .then(res => {
            if (res.status) {
              if (res.data) {
                this.remoteList = res.data.commodities;
              }
              this.selectLoading = false;
            }
          });
        let dropDownDom = this.$refs.selectInput.$el.querySelector(
          '.ivu-select-dropdown'
        );
        dropDownDom.style.display = '';
        dropDownDom.querySelector('li').className += 'ivu-select-item-selected';
      } else {
        this.remoteList = [];
      }
    }, 200),
    changeStore(val) {
      this.storeId = val;
      sessionStorage['storeId'] = val;
      if (this.newOrderList.length) {
        let me = this;
        this.$Modal.confirm({
          title: '修改仓库',
          content: '<p>修改仓库之后将清空已加入的商品列表</p>',
          onOk: function() {
            me.newOrderList = [];
            me.postList = [];
            me.orderRemark = '';
          }
        });
      }
    },
    createADJ() {
      this.isSaved = true;
      let stocks = [];
      if (!this.checkBatch(this.postList)) {
        return false;
      }
      this.postList.forEach(item => {
        let obj = {
          id: item.commodity_id,
          batch_id: item.batch_id,
          new_average_price: item.new_average_price,
          remark: item.remark || ' '
        };
        stocks.push(obj);
      });
      let params = {
        remark: this.orderRemark,
        store_id: this.storeId,
        commoditys: JSON.stringify(stocks)
      };
      ware.createCostChangeOrder(params).then(res => {
        if (res.status) {
          this.$Modal.success({
            title: '提示',
            content: '创建成功'
          });
          this.goBack();
        } else {
          this.$Modal.error({
            title: '错误',
            content: res.message
          });
        }
        this.isSaved = false;
      });
    }
  },
  beforeRouteLeave(to, from, next) {
    this.postList = [];
    this.newOrderList = [];
    this.orderIds = [];
    this.orderRemark = '';
    if (this.modified) {
      this.$Modal.confirm({
        title: '提示',
        content:
          '<p>系统检测到您发生了更改，退出页面将不会保存您所做的更改，您确定放弃修改吗？</p>',
        onOk: function() {
          next();
        },
        onCancel: function() {
          next(false);
        }
      });
    } else {
      next();
    }
  },
  beforeDestroy() {
    this.$Modal.remove();
  },
  components: {
    moveeSelect,
    NumberInput,
    GoodsListModal
  }
};
</script>

<style scoped>
.batchAddBtn {
  position: absolute;
  top: 2px;
  right: 10px;
}
</style>
