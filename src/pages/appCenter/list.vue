<template>
  <div>
    <div class="app-center" v-if="isReady">
      <div class="app-category-wrap">
        <div class="app-category-menu">
          <div
            class="app-category-menu-item"
            :class="activeStep == index ? 'active' : ''"
            :key="index"
            v-for="(appItem, index) in appListFilter"
            v-show="appItem.apps.length"
            @click="jump(index)"
          >
            <Icon icon="xuanzhong_xuanzhong" :size="16" class="active-icon" />
            <span class="app-category-menu-item-txt">{{
              appItem.category_name
            }}</span>
            <img
              v-if="appItem.category_name == '移动办公'"
              class="hot-icon"
              src="@/assets/images/appCenter/hot.png"
              alt="hot"
            />
            <span
              class="more-icon"
              v-if="appItem.category_name == '更多'"
            ></span>
          </div>
        </div>
        <div class="app-category-main" @scroll="handleScroll">
          <app-category
            :title="appItem.category_name"
            :key="index"
            v-for="(appItem, index) in appListFilter"
            v-show="appItem.apps.length"
          >
            <app-item
              v-for="(app) in appItem.apps"
              v-show="app.show !== false"
              :key="app.key || app.config_key"
              :name="app.name"
              :link="app.link"
              :desc="app.desc"
              :service-name="app.serviceName"
              :is-free="app.is_free"
              :is-temp-free="app.is_temp_free"
              :is-special="app.is_special"
              :is-get="app.is_get"
              :detail-link="app.detail_link"
              :generalFreshAdmins="generalFreshAdmins"
              :pay-tips="app.pay_tips"
              :logo="app.logo"
              :not-get-but-accessible="app.notGetButAccessible"
              :is-free-system="app.is_free_system"
            >
              <div
                class="qrcode-container"
                slot="button-extra"
                v-show="app.name === '生鲜管家V1.0' && configuration == 1"
                style="display: inline-block"
              >
                <Button type="success">查看二维码</Button>
                <img
                  class="qrcode"
                  :src="shengxianGuanjiMiniAppQrcode"
                  alt=""
                />
              </div>
            </app-item>
          </app-category>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import ShengxianGuanjia from '@api/ShengxianGuanjia.js';
import common from '@api/main.js';
import AppCategory from './components/app-category_new';
import AppItem from './components/app-item_new';
import appCenterMixin from '@/mixins/appCenter.js';
import Icon from '@/components/icon';
export default {
  mixins: [appCenterMixin],
  components: {
    AppCategory,
    AppItem,
    Icon,
  },
  data() {
    return {
      generalFreshAdmins: '',
      // 配置项
      enable_kingdee: 0,
      configuration: '',
      showAppCenterBanner: true,
      payModal: {
        show: false,
        title: '提示',
        payText: '',
      },
      isReady: false,
      config: {},
      thirdPartyApps: [],
      activeStep: 0,
      shengxianGuanjiMiniAppQrcode: ShengxianGuanjia.miniAppQrcode,
    };
  },
  computed: {
    appListFilter() {
      return this.appList.map((item) => {
        const { category_name, apps } = item;
        return {
          category_name,
          apps: apps.filter((app) => {
            if (app.config_key === 'is_open_issue_order') {
              console.log(
                this.$hasModule(app.config_key),
                'is_open_issue_order',
              );
            }
            return (
              this.$hasModule(app.config_key) &&
              (app.always !== false || (!app.always && app.is_get))
            );
          }),
        };
      }).filter((item) => item.apps.length);
    },
  },
  beforeCreate() {
    common.getConfig().then((config) => {
      this.configuration = config.enable_fresh_manage;
    });
  },
  created() {
    this.init();
    this.setAppList();
    this.getThirdPartyApp();
    this.addAppListFirst();
  },
  methods: {
    getThirdPartyApp() {
      this.$request.get(this.apiUrl.getThirdApp).then((res) => {
        let { status, data } = res;
        if (status && data.length > 0) {
          let token = localStorage['admin_token'];
          this.thirdPartyApps = data.map((item) => {
            item.url += item.url.includes('?')
              ? `&token=${token}`
              : `?token=${token}`;
            let link = item.url;
            item.link = () => {
              window.open(link);
            };
            item.logo = require('../../assets/images/appCenter/common.png');
            item.is_free = true;
            item.is_get = true;
            return item;
          });
          for (let item of this.appList) {
            if (item.category_name == '更多') {
              item.apps.push(...this.thirdPartyApps);
            }
          }
        }
      });
    },
    init() {
      // 先从本地拿如果没有则走接口
      let bussinessConfig = localStorage.getItem('business_config')
        ? JSON.parse(localStorage.getItem('business_config'))
        : '';
      if (bussinessConfig) {
        if (bussinessConfig.enable_kingdee == 1) {
          this.enable_kingdee = bussinessConfig.enable_kingdee;
        }
        this.isReady = true;
        return;
      }
      this.commonService.getConfig().then((config) => {
        if (config.enable_kingdee == 1) {
          this.enable_kingdee = config.enable_kingdee;
        }
        this.isReady = true;
      });
    },
    handleScroll(e) {
      let scrollItems = document.querySelectorAll('.app-category');
      // 判断滚动条是否滚动到底部
      for (let i = scrollItems.length - 1; i >= 0; i--) {
        // 判断滚动条滚动距离是否大于当前滚动项可滚动距离
        let judge =
          e.target.scrollTop >=
          scrollItems[i].offsetTop - scrollItems[0].offsetTop;
        if (judge) {
          this.activeStep = i;
          break;
        }
      }
    },
    jump(index) {
      let scrollItems = document.querySelectorAll('.app-category');
      let total = scrollItems[index].offsetTop - scrollItems[0].offsetTop; // (待滚动的距离)
      let distance = document.querySelector('.app-category').scrollTop; // 滚动条距离滚动区域顶部的距离
      if (total > distance) {
        smoothDown(document.querySelector('.app-category'));
      } else {
        smoothUp(document.querySelector('.app-category'));
      }
      // 参数element为滚动区域
      function smoothDown(element) {
        element.scrollTop = total;
      }
      // 参数element为滚动区域
      function smoothUp(element) {
        element.scrollTop = total;
      }
      document.querySelectorAll('.app-category').forEach((item, index1) => {
        if (index === index1) {
          item.scrollIntoView({
            block: 'start',
            behavior: 'smooth',
          });
        }
      });
    },
    addAppListFirst() {
      let _usedStoreForAppCenterList = window.localStorage.getItem(
        '_usedStoreForAppCenterList',
      );
      if (_usedStoreForAppCenterList) {
        _usedStoreForAppCenterList = JSON.parse(_usedStoreForAppCenterList);
      } else {
        _usedStoreForAppCenterList = [];
        return;
      }
      let newApps = [];
      let appList = this.appList;
      for (let name of _usedStoreForAppCenterList) {
        for (let item of appList) {
          for (let item2 of item.apps) {
            if (name == item2.name) {
              let idx = newApps.indexOf(item2);
              if (idx !== -1) {
                newApps.splice(idx, 1);
              }
              newApps.push(item2);
            }
          }
        }
      }
      // 最近使用只展示8个
      let newAppsS = newApps.slice(0, 8);
      if (newAppsS.length > 0) {
        appList.unshift({
          apps: [...newAppsS],
          category_name: '最近使用',
        });
      }
    },
  },
};
</script>
<style lang="less">
.app-center {
  height: 100%;
  background: #f2f3f5;
}
.no-footer {
  .ivu-modal-footer {
    display: none;
  }
  .ivu-modal-body {
    padding: 30px 16px;
  }
}
.app-category:last-of-type {
  height: calc(~'100vh - 130px');
}
</style>
<style scoped lang="less">
.qrcode-container {
  display: inline-block;
  position: relative;
  .qrcode {
    position: absolute;
    display: none;
    z-index: 255;
  }
  &:hover {
    .qrcode {
      display: block;
    }
  }
  img {
    width: 200px;
  }
}
.ivu-btn {
  padding: 0 10px;
  height: auto;
  line-height: 2;
  &.ghost {
    color: inherit;
    background: transparent;
    &:hover {
      background: transparent;
      color: inherit;
    }
    &.ivu-btn-warning {
      color: #fac13c;
    }
  }
}

@app: app-category;
.@{app}-wrap {
  display: flex;
  padding-top: 24px;
  padding-left: 24px;
  background-color: #ffffff;
  .@{app}-menu {
    position: relative;
    max-height: 80vh;
    overflow-y: auto;
    min-width: 84px;
    overflow-x: hidden;
    z-index: 9;
    &-item {
      width: 64px;
      display: block;
      background: #f5f6f8;
      border-radius: 4px;
      padding: 13px 20px 13px 20px;
      margin-bottom: 4px;
      position: relative;
      cursor: pointer;
      &:hover {
        color: #000000d9;
        background-color: rgba(3, 172, 84, 0.2);
        .active-icon {
          color: #ffffff;
        }
      }
      &-txt {
        display: block;
        width: 26px;
        word-break: break-all;
      }
      .hot-icon {
        width: 22px;
        height: 12px;
        position: absolute;
        top: 6px;
        right: 6px;
      }
      .more-icon {
        width: 6px;
        height: 6px;
        background: #f13130;
        border-radius: 3px;
        position: absolute;
        top: 16px;
        right: 10px;
      }
      .active-icon {
        color: #f5f6f8;
        position: absolute;
        top: 50%;
        right: -13px;
        transform: translate(-50%, -50%);
      }
    }
    .active {
      color: #ffffff;
      background-color: #03ac54;
      font-weight: 600;
      .active-icon {
        color: #ffffff;
      }
    }
  }
  .@{app}-main {
    padding-top: 2px;
    padding-left: 10px;
    background-color: #ffffff;
    overflow-y: scroll;
    height: calc(~'100vh - 130px');
    &::-webkit-scrollbar {
      width: 0;
    }
  }
}
</style>
