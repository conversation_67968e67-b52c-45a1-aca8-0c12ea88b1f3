<template>
  <div class="common setting-container">
    <ListTable
      :advance="true"
      :outer-border="true"
      :advance-items="advanceItems"
      :data-provider="apiUrl.getBusinessData"
      :max-line="2"
      :columns="columns"
      :height="getTableHeightNew() + 30"
      ref="table">
      <ExportButton
        type="default"
        slot="button"
        text="导出"
        :offline="true"
        :param-getter="getExportParams"
        :api="apiUrl.getBusinessDataExport"
      ></ExportButton>
    </ListTable>
  </div>
</template>

<script>
import ListTable from "@components/list-table";
import DateUtil from '@util/date.js';

export default {
  name: 'self-collection-cabinet-data',
  components: {
    ListTable,
  },
  data() {
    return {
      advanceItems: [
        {
          label: '支付时间',
          key: ['begin_pay_time', 'end_pay_time'],
          type: 'DatePicker',
          style: { width: '300px' },
          defaultValue: [DateUtil.getBeforeDate(30, 'YYYY-MM-DD') + ' 00:00:00', DateUtil.getTodayDate('YYYY-MM-DD') + ' 00:00:00'],
          props: {
            type: 'datetimerange',
            format: "yyyy-MM-dd HH:mm:ss",
            placeholder: '请选择日期',
            separator: '~',
          },
        },
      ],
      list: [],
      columns: [
        {
          title: '支付日期',
          key: 'pay_data',
        },
        {
          title: '商品总价',
          key: 'sum_goods_total_money',
        },
        {
          title: '商品总成本价',
          key: 'sum_goods_price_cost',
        },
        {
          title: "商品总优惠金额",
          key: "sum_goods_discount_money"
        },
        {
          title: "商品总利润",
          key: "sum_goods_profit",
        },
        // {
        //   title: "退款金额",
        //   key: "sum_goods_refund_money",
        // },
      ],
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    getExportParams() {
      const params = this.$refs.table.getParams();
      return params
    },
  }
}
</script>

<style scoped lang="less">
</style>
