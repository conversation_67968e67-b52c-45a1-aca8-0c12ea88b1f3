<template>
  <div class="common setting-container">
    <ListTable
      :advance="true"
      :outer-border="true"
      :advance-items="advanceItems"
      :data-provider="apiUrl.getOrderGoodsList"
      :max-line="2"
      :columns="columns"
      :height="getTableHeightNew() + 30"
      ref="table">
      <ExportButton
        type="default"
        slot="button"
        text="导出"
        :offline="true"
        :param-getter="getExportParams"
        :api="apiUrl.getOrderGoodsListExport"
      ></ExportButton>
    </ListTable>
  </div>
</template>

<script>
import ListTable from "@components/list-table";
import DateUtil from '@util/date.js';

export default {
  name: 'self-collection-cabinet-data',
  components: {
    ListTable,
  },
  data() {
    return {
      advanceItems: [
        {
          label: '支付时间',
          key: ['begin_pay_time', 'end_pay_time'],
          type: 'DatePicker',
          style: { width: '300px' },
          defaultValue: [DateUtil.getBeforeDate(30, 'YYYY-MM-DD') + ' 00:00:00', DateUtil.getTodayDate('YYYY-MM-DD')+ ' 00:00:00'],
          props: {
            type: 'datetimerange',
            format: "yyyy-MM-dd HH:mm:ss",
            placeholder: '请选择日期',
            separator: '~',
          },
        },
        {
          label: '会员手机号',
          key: 'member_tel',
          type: 'Input',
          props: {
            placeholder: '请输入会员手机号'
          },
        },
      ],
      list: [],
      columns: [
        {
          title: '订单号',
          key: 'order_no',
        },
        {
          title: '支付订单号',
          key: 'pay_order_no',
        },
        {
          title: '会员id',
          key: 'member_id',
        },
        {
          title: "设备id",
          key: "device_id"
        },
        {
          title: "会员手机号",
          key: "member_tel",
        },
        {
          title: "支付方式",
          key: "pay_type_desc",
        },
        {
          title: "支付时间",
          key: "pay_time",
        },
        {
          title: "订单创建时间",
          key: "create_time",
        },
        {
          title: "商品名称",
          key: "goods_name",
        },
        {
          title: "商品数量",
          key: "goods_quantity",
        },
        {
          title: "商品总价",
          key: "goods_total_money",
        },
        {
          title: "商品总成本价",
          key: "goods_price_cost",
        },
        {
          title: "商品优惠金额",
          key: "goods_discount_money",
        },
        {
          title: "商品总利润",
          key: "goods_profit",
        },
        // {
        //   title: "退款金额",
        //   key: "goods_refund_money",
        // },
      ],
    }
  },
  created() {
  },
  activated() {
  },
  methods: {
    getExportParams() {
      const params = this.$refs.table.getParams();
      return params
    },
  }
}
</script>

<style scoped lang="less">
</style>
