<!--
 * @Author: lizhiwei😊
 * @Date: 2021-10-15 10:14:56
 * @url:
 * @Description: 汇率管理
-->

<template>
  <div class="setting">
    <div class="set_line">
      <p class="title">应用状态：</p>
      <Switch
        :value="provider_comment != 1"
        @on-change="onStatusChange"
      />
    </div>
    <template v-if="provider_comment != 1">
      <div class="set_line">
        <p class="title">评价模式：</p>
        <RadioGroup v-model="provider_comment" @on-change="handleChange">
          <Radio label="2">
            按订单-商城评价
            <Tooltip
              :maxWidth="200"
              content="选择【按订单-商城评价】模式时需设置拆单规则为按供应商且未开启商品评价"
              placement="bottom"
            >
              <i class="sui-icon icon-tips"></i> </Tooltip
            ></Radio>
          <Radio label="3"
          >按供应商-后台评价
            <Tooltip :maxWidth="200" content="选择后台评价模式后，评价来源仅支持后台人员录入" placement="bottom">
              <i class="sui-icon icon-tips"></i>
            </Tooltip>
          </Radio>
        </RadioGroup>
      </div>
      <div class="set_line" v-if="provider_comment == 3">
        <p class="title">计分方式：</p>
        <RadioGroup v-model="provider_comment_grade_mode">
          <Radio label="1">
            总分5分
          </Radio>
          <Radio label="2">
            总分100分 + 权重
          </Radio>
        </RadioGroup>
      </div>
    </template>
    <Button :loading="saveLoading" class="save" type="success" @click="_save">
      保存
    </Button>
  </div>
</template>

<script>
export default {
  name: 'ExchangeRate',
  data() {
    return {
      provider_comment: '1',
      provider_comment_grade_mode: '1',
      saveLoading: false,
    };
  },
  created() {
    this.getConfig();
  },
  watch: {},
  methods: {
    getConfig() {
      this.commonService.getConfig().then(config => {
        this.provider_comment = config.provider_comment
        this.provider_comment_grade_mode = config.provider_comment_grade_mode
      })
    },
    handleChange(e) {
      if (e == 2) {
        this.provider_comment_grade_mode = '1'
      }
    },
    _save() {
      this.saveLoading = true;
      let params = {
        config_list: [
          {
            // provider_comment_grade_mode 得在前面让后台去验证
            key: 'provider_comment_grade_mode',
            value: this.provider_comment_grade_mode
          },
          {
            key: 'provider_comment',
            value: this.provider_comment
          },
        ]
      };
      params.config_list = JSON.stringify(params.config_list);
      this.commonService.saveConfig(params).then(res => {
        let { status, message } = res
        if (status) {
          this.successMessage('保存成功');
          this.$store.commit('updateSysConfig', {
            provider_comment: this.provider_comment,
            provider_comment_grade_mode: this.provider_comment_grade_mode,
          });
        } else {
          this.errorMessage(message)
          this.getConfig()
        }
      }).finally(() => {
        this.saveLoading = false;
      })
    },
    onStatusChange(val) {
      if (val) {
        this.provider_comment = '2';
      } else {
        this.provider_comment = '1';
      }
    },
  },
};
</script>

<style lang="less" scoped>
.setting {
  padding: 20px;
  .set_line {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .title {
      height: 32px;
      width: 80px;
      text-align: right;
      line-height: 32px;
      margin-right: 10px;
    }
    .tips {
      margin-left: 10px;
      color: #999999;
    }
    .sui-icon {
      font-size: 14px;
    }
  }
  .save {
    margin-top: 30px;
    margin-left: 20px;
  }
}
</style>
