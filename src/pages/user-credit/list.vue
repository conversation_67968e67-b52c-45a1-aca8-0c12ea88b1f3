<template>
  <div class="s-common base-wrapper2 user-credit">
		<list-table
			ref="list"
			:border="false"
			:outer-border="true"
			:height="getTableHeightNew() + 80"
			:keepScroll="true"
			:auto-load-data="true"
			:data-provider="apiUrl.userCredit.list"
			:advance-items="advanceItems"
			:columns="columns"
			@on-finish-data-load="handleFinishDataLoad"
			:before-request="beforeRequest"
			@on-sort-change="onSortChange"
		>
			<ExportButton
				slot="button"
				:offline="true"
				:param-getter="getExportParams"
				:api="apiUrl.exportPoint"
			></ExportButton>
		</list-table>
		<CreditRecord
				v-if="isTableDataLoaded"
				:user="recordModal.user"
				:show="recordModal.show"
				@on-cancel="closeRecordModal"></CreditRecord>
		<form-modal
				ref="formModal"
				:validate="true"
				:save-api="formModal.saveApi"
				:title="formModal.title"
				:form-style="formModal.style"
				:width="formModal.width"
				:label-width="90"
				:show-modal="formModal.show"
				:show-cancel-btn="true"
				:form-data="formModal.formData"
				:form-columns="formModal.formColumns"
				:validate-rules="formModal.validateRules"
				@on-visible-change="handleFormModalVisibleChange"
				@on-success="saveSuccess"
				@on-cancel="closeFormModal">
				<NumberInput slot="point" v-model="formModal.formData.point"></NumberInput>
			</form-modal>
  </div>
</template>

<script>
import ListTable from '@components/list-table'
import CreditRecord from './record.vue';
import FormModal from '@components/basic/formModal';
import NumberInput from '@components/basic/NumberInput';
import LayoutMixin from '@/mixins/layout'
import { getClientData, useClientData } from 'prefetch-preload/esm/vue2';
import {
  area,
  userType
} from '@/components/standard/sdp-filter-items'

export const getCreditList = getClientData((ctx) => {
	return ctx.get('/superAdmin/userSuper/pointList', {
		pageSize: 20,
		currentPage: 1,
		page: 1,
		receStyle: '',
		area_id: '',
		searchKey: ''
	});
});

export default {
	name: 'user-credit-list',
	components: {
		ListTable,
		CreditRecord,
		FormModal,
		NumberInput
	},
	mixins: [ LayoutMixin ],
	// beforeCreate() {
	// 	useClientData('getCreditList').then((v) => {
	// 		if (v && v.data) {
	// 			this.preData = v.data.list;
	// 			this.isTableDataLoaded = true;
	// 			this.$nextTick(() => {
	// 				this.$refs.list.setData(this.preData, v.data, false, true);
	// 			})
	// 		} else {
	// 			this.preData = [];
	// 		}
	// 	});
	// },
	data() {
		return {
			preData: [],
			isTableDataLoaded: false,
			recordModal: {
				show: false,
				user: {}
			},
			formModal: {
				show: false,
				saveApi: this.apiUrl.basket.record.add,
				width: 600,
				title: '',
				style: {
					paddingRight: '35px'
				},
				formData: {
					user_id: '',
					point: '',
					account_tel: '',
					email: '',
					admin_account: '',
				},
				validateRules: {
				},
				formColumns: []
			},
			filterItems: [],
			advanceItems: [{
				items: [
					{
						label: '客户类型',
						key: 'receStyle',
						type: 'custom',
						component: userType,
					},
					{
						label: '区域',
						key: 'area_id',
						type: 'custom',
						component: area,
					},
					{
						label: '搜索',
						key: 'searchKey',
						type: 'Input',
						props: {
							placeholder: '请输入客户名称／账号／编码',
						},
					},
				],
			},
			],
			columns: [
				{
					title: '客户账号',
					key: 'account_tel'
				},
				{
					title: '客户编码',
					key: 'user_code'
				},
				{
					title: '客户名称',
					key: 'email'
				},
				{
					title: '联系人',
					key: 'contact_name'
				},
				{
					title: '客户类型',
					key: 'receivable_style'
				},
				{
					title: '累计积分',
					key: 'point',
//            sortable: 'custom'
          },
          {
            title: '可用积分',
            key: 'available_point',
//            sortable: 'custom'
          },
					{
						title: '操作',
						key: 'action',
						type: 'action',
						width: 270,
						actions: [
							{
								name: '积分变动记录',
								action: (params) => {
									this.showRecordModal(params);
								},
								class: (params) => {
									return {
										// dn: !isStatusCanAudit(params.row)
									}
								},
							},
							{
								name: '增加积分',
								action: (params) => {
									this.showInCreaseModal(params);
								},
								class: (params) => {
									return {
										// dn: !isStatusCanDelete(params.row)
									}
								},
							},
							{
								name: '扣减积分',
								action: (params) => {
									this.showDecreaseModal(params);
								},
								class: (params) => {
									return {
										// dn: !isStatusCanDelete(params.row)
									}
								},
							}
						]
					},
        ],
        searchParams: {}
      }
    },
    activated () {
      this.loadList();
    },
    methods: {
			handleFinishDataLoad() {
				this.getCommonData();
				if (!this.isTableDataLoaded) this.isTableDataLoaded = true;
			},
      getCommonData() {
        this.commonService.getTopMenu().then((res) => {
          let { status, data } = res;
          if (status) {
            this.formModal.formData.admin_account = data.username;
          }
        });
      },
      handleFormModalVisibleChange() {
        this.$nextTick(() => {
          this.$refs.formModal.resetFields();
        })
      },
      saveSuccess() {
        this.loadList(false);
        this.closeFormModal();
      },
      showRecordModal(params) {
        let { row } = params;
        this.recordModal.show = true;
        this.recordModal.user = row;
      },
      closeRecordModal() {
        this.recordModal.show = false;
      },
      showInCreaseModal(params) {
        let { row } = params;
        this.formModal.formData.user_id = row.id;
        this.formModal.formData.email = row.email;
        this.formModal.formData.account_tel = row.account_tel;
        this.formModal.title = '增加积分';
        this.formModal.saveApi = this.apiUrl.userCredit.addIncreaseRecord;
        this.showFormModal();
      },
      showDecreaseModal(params) {
        let { row } = params;
        this.formModal.formData.email = row.email;
        this.formModal.formData.user_id = row.id;
        this.formModal.formData.account_tel = row.account_tel;
        this.formModal.title = '扣减积分';
        this.formModal.saveApi = this.apiUrl.userCredit.addDecreaseRecord;
        this.showFormModal();
      },
      closeFormModal(params) {
        this.formModal.show = false;
      },
      loadList(resetPage) {
        this.$refs.list._fetchData(resetPage);
      },
      onSortChange(column, key, order) {
        console.log(column)
        console.log(key)
        console.log(order)
      },
      showFormModal() {
        let formColumns = [
          {
            disabled: true,
            label: '客户账号',
            name: 'account_tel'
          },
          {
            disabled: true,
            label: '客户名称',
            name: 'email'
          },
          {
            disabled: true,
            label: '操作员',
            name: 'admin_account',
          },
        ];
        let amountColumn = {
          label: '',
          name: 'point',
          type: 'slot',
        };
        if (this.formModal.title === '扣减积分') {
          this.formModal.validateRules = {
            point: [
              {
              	trigger: 'blur',
                required: true,
                validator: (rule, value, callback) => {
                	if (!value) {
                    callback(new Error('请输入积分'));
                  } else {
                    callback();
                  }
                }
              }
            ]
          };
        } else {
          this.formModal.validateRules = {
            point: [
              {
                required: true,
                trigger: 'blur',
                validator: (rule, value, callback) => {
                  if (!value) {
                    callback(new Error('请输入积分'));
                  } else {
                    callback();
                  }
                }
              }
            ]
          };
        }
        amountColumn.label = this.formModal.title;
        formColumns.push(amountColumn);
        formColumns.push({
          label: '备注',
          name: 'remark',
          type: 'textarea',
          rows: 3
        });
        this.formModal.formColumns = formColumns;
        this.formModal.show = true;
      },
      beforeRequest(params) {
        this.searchParams = params
        return params
      },
      getExportParams() {
				this.searchParams.page = this.searchParams.currentPage;
				delete this.searchParams.currentPage;
				delete this.searchParams.pageSize;
				delete this.searchParams.page;
        return this.searchParams
      }
    },
  }
</script>

<style lang="less" scoped>
  .user-credit-list {
    padding-top: 15px;
  }
</style>
