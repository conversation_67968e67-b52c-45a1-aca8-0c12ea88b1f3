export default {
  data: function() {
    return {
      errorIndex: '',
      errorTip: ''
    };
  },
  computed: {
    showError() {
      var _self = this;
      return function(_index) {
        return _self.errorIndex === _index && _self.errorTip;
      };
    }
  },
  methods: {
    setErrorInfo(_index, _error) {
      this.errorIndex = _index;
      this.errorTip = _error;
    },
    //校验banner列表数据
    validateBanner(_list) {
      if (_list.length == 0) return true;
      for (let index = 0, length = _list.length; index < length; index++) {
        let _item = _list[index];
        let errorTip = '';
        if (_item.link_type === '') {
          errorTip = '请选择跳转内容页面';
        } else if (_item.link_type === '3' || _item.link_type === '2' || _item.link_type === '29') {
          if (!_item.category_id || !_item.category_id.length) errorTip = '请选择分类'
        }
        if (errorTip !== '') {
          this.setErrorInfo(index, errorTip);
          return false;
        }
      }
      this.setErrorInfo('', '');
      return true;
    },
    //校验图标列表数据
    validateIcon(_list) {
      if (_list.length == 0) return true;
      for (let index = 0, length = _list.length; index < length; index++) {
        let _item = _list[index];
        let errorTip = '';
        if (_item.link_type === '') {
          errorTip = '请选择跳转内容页面';
        } else if (_item.link_type === '2' || _item.link_type === '3' || _item.link_type === '29') {
          if (!_item.category_id || !_item.category_id.length) errorTip = '请选择商品分类';
        }

        if (errorTip !== '') {
          this.setErrorInfo(index, errorTip);
          return false;
        }
      }
      this.setErrorInfo('', '');
      return true;
    },
    // 建议广告带商品数据
    validateGroup(_list) {
      if (!_list.length) return true;
      for (let index = 0; index < _list.length; index++) {
        let _item = _list[index];
        let errorTip = '';
        if (!_item.ad.name) {
          errorTip = '请输入专题名称';
        } else if (!_item.ad.pic) {
          errorTip = '请选择专题图片';
        } else if (!_item.ad.link_type) {
          errorTip = '请选择跳转链接';
        } else if ((+_item.ad.link_type === 2 || +_item.ad.link_type === 3) && (!_item.ad.category1 || _item.ad.category1 === '0')) {
          errorTip = '请选择跳转链接分类';
        } else if (!_item.items.link_type) {
          errorTip = '请选择商品来源';
        } else if (+_item.items.link_type === 1 && !_item.items.commodity_list.length) {
          errorTip = '请添加商品';
        } else if (+_item.items.link_type === 2 || +_item.items.link_type === 3) {
          if (!_item.items.limit || +_item.items.limit <= 0) {
            errorTip = '请设置显示数量，1-50之间的双数';
          } else if (!_item.items.category1 || _item.items.category1 === '0') {
            errorTip = '请选择商品分类';
          }
        }
        console.log(errorTip)
        if (errorTip !== '') {
          this.errorMessage(errorTip);
          this.setErrorInfo(index, errorTip);
          return false;
        }
      }
      this.setErrorInfo('', '');
      return true;
    },
    //校验主题列表数据
    validateSubject(_list, _templateType) {
      if (_list.length == 0) return true;
      if (_templateType === '1') {
        _list = _list.slice(0, 1);
      } else if (_templateType === '2') {
        _list = _list.slice(1, 4);
      } else if (_templateType === '3') {
        _list = _list.slice(4, 6);
      } else if (_templateType === '0') {
        _list = [];
      }
      for (let index = 0, length = _list.length; index < length; index++) {
        let _item = _list[index];
        let errorTip = '';
        if (!_item.name) {
          errorTip = '请输入专题名称';
        } else if (_item.link_type === this.goodsType.category1.value || _item.link_type === this.goodsType.category2.value || _item.link_type === this.goodsType.category3.value) {
          if (!_item.category1 || !_item.category1.length) errorTip = '请选择分类';
        } else if (_item.link_type === this.goodsType.link.value) {
          if (!_item.linkInfo.value) errorTip = '请输入链接'
        } else if (_item.commodity_list.length === 0 && _item.link_type !== this.goodsType.supplier.value && _item.link_type !== this.goodsType.newGoods.value && _item.link_type !== this.goodsType.customer.value && _item.link_type !== this.goodsType.collection.value && _item.link_type !== this.goodsType.voucher.value && _item.link_type !== this.goodsType.recipeOrder.value) {
          errorTip = '请设置商品来源';
        }

        if (errorTip !== '') {
          index = _templateType === '2' ? index + 1 : index;
          this.setErrorInfo(index, errorTip);
          return false;
        }
      }
      this.setErrorInfo('', '');
      return true;
    },
    //校验推荐商品列表数据
    validateRecommend(_list) {
      if (_list.length == 0) return true;
      for (let index = 0, length = _list.length; index < length; index++) {
        let _item = _list[index];
        let errorTip = '';
        if (!_item.name) {
          errorTip = '请输入专题名称';
        } else if (
          _item.link_type === '1' &&
          _item.commodity_list.length === 0
        ) {
          errorTip = '请添加商品';
        } else if (_item.link_type === '9' && _item.package_list.length === 0) {
          errorTip = '请添加套餐';
        } else if (_item.link_type === '3' && _item.category.length === 0) {
          errorTip = '请选择分类';
        } else if (_item.link_type === '2' && !_item.category1) {
          errorTip = '请选择分类';
        } else if (_item.link_type === '29' && _item.category3.length === 0) {
          errorTip = '请选择分类';
        }

        if (errorTip !== '') {
          this.setErrorInfo(index, errorTip);
          return false;
        }
      }
      this.setErrorInfo('', '');
      return true;
    }
  }
};
