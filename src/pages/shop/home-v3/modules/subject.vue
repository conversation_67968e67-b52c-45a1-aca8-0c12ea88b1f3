<template>
  <div class="module module-subject" :class="isDefaultTheme ? 'default' : 'theme'">
    <div class="title">
      <p>推荐专题</p>
      <div class="config" v-if="sysConfig.open_mall_custom_decorate == 0">
        商城展示 :
        <!-- <Switch v-model="enableFreight" @on-change="saveConfig"></Switch> -->
        <Switch v-model="ifShowTemplate" @on-change="closeTemplate"></Switch>
      </div>
    </div>
    <template>
      <div class="item">
        <p>菜单数量 :</p>
        <div class="subject-menu">
          <div
            :class="{ active: this.template === '1' }"
            @click="setTemplate('1')"
          >
            <div class="menu-type-1"></div>
            <p>单个居中</p>
          </div>
					<div
						:class="{ active: this.template === '3' }"
						@click="setTemplate('3')"
					>
						<div class="menu-type-3">
							<span></span>
							<span></span>
						</div>
						<p>两列2个</p>
					</div>
          <div
            :class="{ active: this.template === '2' }"
            @click="setTemplate('2')"
          >
            <div class="menu-type-2">
              <span></span>
              <div>
                <span></span>
                <span></span>
              </div>
            </div>
            <p>两列3个</p>
          </div>
        </div>
      </div>
      <div class="item">
        <p>展示图片 :</p>
        <div class="module-content">
          <!-- 循环显示 -->
          <!-- 一行显示时只取localtion为1，其他行显示方式就取2、3、4所有 -->
          <div
            v-for="(item, subjectIndex) in subjectList"
            :key="subjectIndex"
						v-if="showSubjectList(item)"
            :class="showError(subjectIndex) ? 'border-error ' : ''"
          >
            <div class="content">
              <div class="subject-img">
                <div
                  class="img-wrap"
                  :style="{
                    height:
                      item.location !== '1' && item.location !== '2'
                        ? '68px'
                        : '102px'
                  }"
                >
                  <img v-if="item.pic" :src="item.pic" />
                  <div v-if="!item.pi" class="img-holder"></div>
                  <p>
                    <span @click="showImportCloudImg(item, subjectIndex)"
                      >更换图片</span
                    >
                  </p>
                </div>

                <div class="help-px">
                  <template v-if="item.location === '1'">
                    960*270
                  </template>

                  <template v-else-if="item.location === '2'">
                    440×440
                  </template>
                  <template v-else>
                    440×200
                  </template>
                </div>
              </div>

              <div class="select-wrap">
                <Form :label-width="73" class="subject-form">
                  <FormItem ref="name" label="专题名称 :">
                    <Input
                      :maxlength="maxNameLen"
                      :placeholder="`专题名称，最多${maxNameLen}个字符`"
                      style="width:232px;"
                      v-model="item.name"
                      :show-word-limit="true"
                      @on-change="doModified(true)"
                    />
                  </FormItem>
                  <FormItem ref="content" label="商品来源 :">
                    <div>
                      <RewriteSelect
                        v-model="item.link_type"
                        @on-change="onChangeGoodsType(subjectIndex)"
                        :options="showGoodsTypeList"
                      />
                    </div>

                    <template v-if="item.link_type === goodsType.normal.value">
                      <div
                        :class="dragging ? 'dragging' : ''"
                        class="goods-list clearfix"
                      >
                        <Draggable
                          v-model="item.commodity_list"
                          @start="onDragStart"
                          @end="onDragEnd"
                          :options="dragOptions"
                        >
                          <goods-item
                            class="goods-item"
                            v-for="(goods, goodIndex) in item.commodity_list"
                            :key="goodIndex"
                            :goods="goods"
                            @on-delete="deleteGoods($event, subjectIndex)"
                            showOrder
                            @on-order-up="
                              itemOrderUp($event, subjectIndex, goodIndex)
                            "
                            @on-order-down="
                              itemOrderDown($event, subjectIndex, goodIndex)
                            "
                            @on-order-top="
                              itemOrderTop($event, subjectIndex, goodIndex)
                            "
                            @on-selectReplace="
                              selectReplace(
                                item.commodity_list,
                                subjectIndex,
                                goodIndex,
                                $event
                              )
                            "
                            :hiddenUp="goodIndex === 0"
                            :hiddenDown="
                              goodIndex === item.commodity_list.length - 1
                            "
                          >
                          </goods-item>
                        </Draggable>
                        <add-goods
                          class="goods-item"
                          v-show="item.link_type === goodsType.normal.value"
                          :default-selected-goods="item.commodity_list"
                          :disabledGoods="item.commodity_list"
                          @on-ok="addGoods($event, subjectIndex)"
                        ></add-goods>
                      </div>
                    </template>

                    <template v-if="item.link_type === goodsType.package.value">
                      <div
                        :class="dragging ? 'dragging' : ''"
                        class="goods-list clearfix"
                      >
                        <Draggable
                          v-model="item.commodity_list"
                          @start="onDragStart"
                          @end="onDragEnd"
                          :options="dragOptions"
                        >
                          <goods-item
                            class="goods-item"
                            v-for="(goods, goodIndex) in item.commodity_list"
                            :key="goodIndex"
                            :goods="goods"
                            @on-delete="deleteGoods($event, subjectIndex)"
                            showOrder
                            @on-order-up="
                              itemOrderUp($event, subjectIndex, goodIndex)
                            "
                            @on-order-down="
                              itemOrderDown($event, subjectIndex, goodIndex)
                            "
                            @on-order-top="
                              itemOrderTop($event, subjectIndex, goodIndex)
                            "
                            replaceModule="package"
                            @on-selectReplace="
                              selectReplace(
                                item.commodity_list,
                                subjectIndex,
                                goodIndex,
                                $event
                              )
                            "
                            :hiddenUp="goodIndex === 0"
                            :hiddenDown="
                              goodIndex === item.commodity_list.length - 1
                            "
                          >
                          </goods-item>
                        </Draggable>

                        <AddPanel
                          v-show="item.link_type === goodsType.package.value"
                          @on-click="
                            showPackageGoodsModal(
                              item.commodity_list,
                              subjectIndex
                            )
                          "
                        />
                      </div>
                    </template>
                    <template v-if="item.link_type === goodsType.category1.value || item.link_type === goodsType.category2.value || item.link_type === goodsType.category3.value">
                      <category-select
                        v-model="item.category1"
                        :level="item.link_type == 2 ? 1 : item.link_type == 3 ? 2 : 3"
                        :placeholder="'请选择商品' + (item.link_type == 2 ? '一' : item.link_type == 3 ? '二' : '三')  +'级分类'"
                        :use-special-style="false"
                        :meta="item"
                      ></category-select>
                    </template>
                    <template v-if="item.link_type === goodsType.link.value">
                      <div class="select-link">
                        <span class="select-link--content">{{ item.linkInfo.value ? `${item.linkInfo.name} | ${item.linkInfo.value}` : ''}}</span>
                      </div>
                      <span class="select-link--edit" @click="handleLink(item.linkInfo, subjectIndex)">修改</span>
                    </template>
                    <template v-if="item.link_type === goodsType.supplier.value">
                      <RadioGroup v-model="item.linkInfo.value">
                        <Radio label="1"><span>一行一个</span></Radio>
                        </Radio>
                        <Radio label="2"><span>一行两个</span></Radio>
                      </RadioGroup>
                    </template>
                  </FormItem>
                </Form>
              </div>
            </div>

            <div class="operation-btns">
              <span />
              <div>
                <!-- <span class="sui-icon  icon-arrow-solid-up" />
              <span class="sui-icon  icon-arrow-down" /> -->
              </div>
            </div>

            <div class="error-tip-info" v-if="showError(subjectIndex)">
              {{ errorTip }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <div class="confirm-btns">
      <Button type="primary" @click="save">{{
        saving ? '保存中...' : '保 存'
      }}</Button>
    </div>

    <!-- <cloud-img
      :pageSizeOpts="[32, 64]"
      :rowCount="cloudImgModal.rowCount"
      :defaultCategory="cloudImgModal.defaultCategory"
      :type="imgType.normal.value"
      :show="cloudImgModal.show"
      @on-close="cloudImgModal.show = false"
      @on-import="handleImportCloudImg"
    ></cloud-img> -->

    <ImgUpload
      v-model="showUploadImg"
      :defaultCategory="cloudImgModal.defaultCategory"
      :type="imgType.normal.value"
      @local-up-success="handleUploadSuccess"
      @cloud-up-success="handleImportCloudImg"
    />

    <PackageGoodsModal
      :show-mode-filter="true"
      :default-selected-goods="current_commodity_list"
      :disabled-goods="current_commodity_list"
      :show="packageGoodsModal.show"
      @on-cancel="closePackageGoodsModal"
      @on-ok="onSelectPackageGoods"
    ></PackageGoodsModal>

    <!-- 更换商品弹出框 -->
    <ModalGoods
      v-model="replaceGoods"
      :max-select-count="1"
      @on-ok="replceGoods"
    ></ModalGoods>

    <!-- 更换套餐弹出框 -->
    <PackageGoodsModal
      :show-mode-filter="true"
      :max-select-count="1"
      :show="replacePackage"
      @on-cancel="closeReplcePackage"
      @on-ok="replcePackage"
    ></PackageGoodsModal>
    <!-- 自定义链接 -->
    <ModalLink
      ref="linkModal"
      v-model="showLinkModal"
      indexs='0'
      @confirm="handleLinkChange"
    />
  </div>
</template>

<script>
import Draggable from 'vuedraggable';
import GoodsItem from '../components/goods-item.vue';
import AddGoods from '../components/add-goods.vue';
import { imgType, subject as cloudSubject } from '@components/cloudImage/util';
// import CloudImg from "@components/cloudImage/index";
import PackageGoodsModal from '@components/packageGoods/PackageGoodsModal';
import { MINE_TYPE } from '@/util/const';
import ImgUpload from '../components/img-upload';
import AddPanel from '../components/add-panel';
import ErrorTip from '../mixins/error-tip';
import ModalGoods from '../components/modal-goods.vue';
import Modified from '../mixins/modified';
import RewriteSelect from '../components/rewrite-select';
import CategorySelect from '../components/category-select'
import ModalLink from '../components/modal-link';
import ImportButton from '@/components/common/import-btn/index.vue';
import ConfigMixin from "@/mixins/config";

const templateType = {
  one: {
    value: '1'
  },
  thee: {
    value: '2'
  },
  style: {
    value: '0'
  }
};
const goodsType = {
  normal: {
    label: '推荐商品',
    value: '1'
  },
  category1: {
    label: '一级分类',
    value: '2'
  },
  category2: {
    label: '二级分类',
    value: '3'
  },
  category3: {
    label: '三级分类',
    value: '29'
  },
  package: {
    label: '推荐套餐',
    value: '9'
  },
  supplier: {
    label: '供应商推荐',
    value: '20'
  },
  link: {
    label: '自定义链接',
    value: '12'
  },
  newGoods: {
    label: '新品需求',
    value: '21'
  },
  customer: {
    label: '客户协议单',
    value: '23'
  },
  collection: {
    label: '常用清单',
    value: '7'
  },
  voucher: {
    label: '领券中心',
    value: '5'
  },
  recipeOrder: {
    label: '菜谱下单',
    value: '25'
  }
};
const location = {
  one: '1',
  two: '2',
  three: '3',
  four: '4',
	five: '5',
	six: '6',
};
const GOODS_KEY = 'id';
export default {
  mixins: [ErrorTip, Modified, ConfigMixin],
  components: {
		ImportButton,
    GoodsItem,
    AddGoods,
    Draggable,
    PackageGoodsModal,
    ImgUpload,
    AddPanel,
    ModalGoods,
    RewriteSelect,
    CategorySelect,
    ModalLink
  },
  props: {
    isDefaultTheme: {
      type: String,
      default: ''
    },
    extraParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
			importPost: {
				url: '/superAdmin/mall/ImportCommodity',
				accept: MINE_TYPE.excel.join(','),
				format: ['csv', 'xlsx']
			},
			download: Object.freeze({
				url: '/superAdmin/mall/Template',
				text: '推荐商品批量导入模版'
			}),
      maxImgSize: 1024 * 3, // 图片大小限制3m
      maxNameLen: 6,
      goodsType,
      goodsTypeList: [goodsType.normal, goodsType.category1, goodsType.category2, goodsType.newGoods, goodsType.customer, goodsType.collection],
      currentSubject: {
        id: '',
        location: '',
        pic: '',
        link_type: goodsType.normal.value,
        commodity_list: []
      },
      subjectList: [],
      saving: false,
      dragging: false,
      dragOptions: {
        dragClass: 'dragging',
        ghostClass: 'dragging-ghost',
        chosenClass: 'dragging-chosen',
        handle: '.drag'
      },
      packageGoodsModal: {
        show: false
      },
      imgType,
      cloudImgModal: {
        show: false,
        rowCount: 8,
        defaultCategory: ''
      },
      currentIndex: '',
      mineType: MINE_TYPE,
      error: {
        img: '',
        name: '',
        content: ''
      },
      main: {
        name: ''
      },
      template: '',
      ifShowTemplate: false,
      showUploadImg: false,
      replaceGoods: false,
      replacePackage: false,
      subjectIndex: '',
      goodIndex: '',
      current_commodity_list: [],
      showLinkModal: false,
      linkIndex: ''
    };
  },
  computed: {
    showGoodsTypeList () {
      return [...this.goodsTypeList, goodsType.supplier, goodsType.link,  goodsType.voucher, goodsType.recipeOrder];
    },
  },
  created() {
    if (this.isOpenCommodityCategoryThree) {
      this.goodsTypeList.splice(3, 0, goodsType.category3);
    }
    this.getConfig();
    this.getSubject();
  },
  inject: ['handleUpdateModule', 'handleSaveModule'],
  methods: {
		showSubjectList(item) {
			if (+this.template === 1 && +item.location === 1) {
				return true
			}
			if (+this.template === 2 && [2, 3 ,4].includes(Number(item.location))) {
				return true
			}
			if (+this.template === 3 && [5, 6].includes(Number(item.location))) {
				return true
			}
			return false;
		},
		afterImport(res, subjectIndex) {
			this.addGoods(res, subjectIndex);
		},
    onDragStart() {
      this.dragging = true;
    },
    onDragEnd() {
      this.dragging = false;
    },
    onChangeGoodsType(_index) {
      this.subjectList[_index].commodity_list = [];
      this.subjectList[_index].category1 = ''
      this.subjectList[_index].linkInfo = {}
      this.doModified(true);
      this.$nextTick(() => {
        if (this.subjectList[_index].link_type === '12') {
          this.handleLink(this.subjectList[_index].linkInfo, _index)
        }
        if (this.subjectList[_index].link_type === '20') {
          this.subjectList[_index].linkInfo.value = '2'
        }
        this.$forceUpdate()
      })
    },
    addGoods(goodsList, _index) {
      goodsList.forEach(goods => {
        if (
          !this.subjectList[_index].commodity_list.find(
            fGoods => fGoods[GOODS_KEY] === goods[GOODS_KEY]
          )
        ) {
          this.subjectList[_index].commodity_list.push(goods);
        }
      });
    },
    deleteGoods(goods, _index) {
      if (
        this.subjectList[_index].commodity_list.find(
          findGoods => findGoods[GOODS_KEY] === goods[GOODS_KEY]
        )
      ) {
        this.subjectList[_index].commodity_list.splice(
          this.subjectList[_index].commodity_list.findIndex(
            findGoods => findGoods[GOODS_KEY] === goods[GOODS_KEY]
          ),
          1
        );
        this.doModified(true);
      }
    },
    showPackageGoodsModal(commodity_list, _index) {
      this.currentIndex = _index;
      this.packageGoodsModal.show = true;
      this.current_commodity_list = commodity_list;
    },
    closePackageGoodsModal() {
      this.packageGoodsModal.show = false;
    },
    onSelectPackageGoods(goodsList) {
      this.resetError();
      this.closePackageGoodsModal();
      console.log('...');
      goodsList.forEach(goods => {
        if (
          !this.subjectList[this.currentIndex].commodity_list.find(
            fGoods => fGoods.id === goods[GOODS_KEY]
          )
        ) {
          goods.logo = goods.pic_url;
          this.subjectList[this.currentIndex].commodity_list.push(goods);
        }
      });
    },
    getConfig() {
      this.commonService.getConfig().then(config => {
        let { is_commodity_package } = config;
        if (Number(is_commodity_package) === 1) {
          this.goodsTypeList.push(goodsType.package);
        }
      });
    },
    getSubject() {
      const options = { ...this.extraParams }

      this.$request.get(this.apiUrl.shop.subject.getSubject, options).then(res => {
        let { data, status } = res;
        if (status) {
          if (data.subject_list.length > 0) {
            data.subject_list.forEach((subject, index) => {
              subject.index = index;
              const categoryVal = {
                2: subject.category_id1,
                3: [subject.category_id1, subject.category_id2],
                29: [subject.category_id1, subject.category_id2, subject.category_id3]
              };
              subject.category1 =  categoryVal[subject.link_type] || [];
              subject.linkInfo = {
                name: subject.link_name || '',
                // 给个默认'一行两个'兼容之前老数据
                value: subject.link_type === '20' && (!subject.link_value || ['1', '2'].indexOf(subject.link_value) === -1) ? '2' : (subject.link_value || '')
              };
            });
          }
          this.template = data.style;
          this.ifShowTemplate = data.style === '0' ? false : true;
					console.log(data.subject_list,' kkkk')
          this.initSubject(data.subject_list);
        } else {
          this.initSubject();
        }
        this.saving = false;
      });
    },
    initSubject(subjectList = []) {
      let list = [];
      const defaultItem = {
        id: '',
        name: '',
        location: '',
        pic: '',
        linkInfo: {},
        link_type: goodsType.normal.value,
        commodity_list: []
      };
      Object.values(location).forEach((loc, index) => {
        let subjectItem = this.cloneObj(defaultItem);
        let subject = subjectList.find(subject => subject.location === loc);
        subjectItem.location = loc;
        if (subject) {
          subjectItem = Object.assign(subjectItem, subject);
        }
        if (subjectItem.link_type === goodsType.package.value) {
          subjectItem.package_list.forEach(item => {
            item.logo = item.pic_url;
          });
          subjectItem.commodity_list = subjectItem.package_list;
        }
        if (subjectItem) list[index] = subjectItem;
      });
      // if (this.template === templateType.one.value) {
      //   this.currentSubject = list[location.one];
      // } else {
      //   if (this.currentSubject && list[this.currentSubject.location]) {
      //     this.currentSubject = list[this.currentSubject.location];
      //   } else {
      //     this.currentSubject = list[location.two];
      //   }
      // }
      // if (this.template === "0") {
      //   //不显示
      //   this.subjectList = [];
      // } else if (this.template === "1") {
      //   //一居中
      //   this.subjectList = list.length > 1 ? list.slice(0, 1) : list;
      // } else {
      //   //一左二右
      //   this.subjectList = list.length > 3 ? list.slice(0, 3) : list;
      // }
      this.subjectList = list;
      console.log(this.subjectList)
      this.saving = false;
    },
    showImportCloudImg(subject, _index) {
      this.cloudImgModal.rowCount = 3;
      console.log(subject, 'subject');
      switch (subject.location) {
        case location.one:
          this.cloudImgModal.defaultCategory = cloudSubject.one.label;
          break;
        case location.two:
          this.cloudImgModal.rowCount = 4;
          this.cloudImgModal.defaultCategory = cloudSubject.two.label;
          break;
        case location.three:
        case location.four:
          this.cloudImgModal.defaultCategory = cloudSubject.three.label;
          break;
      }
      this.cloudImgModal.show = true;
      // this.currentSubject = subject;
      //this.currentIndex = _index;
      this.showImgUpload(_index);
    },
    handleImportCloudImg(upyunUrl) {
      this.subjectList[this.currentIndex].pic = upyunUrl;
      //this.currentSubject.pic = upyunUrl;
      this.cloudImgModal.show = false;
    },
    handleUploadSuccess(imgData) {
      this.subjectList[this.currentIndex].pic = imgData.upyun_url;
      //  meta.subject.pic = imgData.upyun_url;
      //   this.currentSubject = meta.subject;
    },
    async save() {
      let params = this.getSaveData();
      // if (!this.checkData(JSON.parse(params.data))) {
      //   return false;
      // }
      this.resetError();

      var flag = this.validateSubject(this.subjectList, this.template);

      if (this.saving || !flag) {
        return false;
      }

      if (this.handleSaveModule) {
        const handleSaveModule = this.handleSaveModule();
        // handleSaveModule返回promise的场景
        if (typeof handleSaveModule.then === 'function') {
          await handleSaveModule;
        }
      }

      this.saving = true;
      this.$request.post(this.apiUrl.shop.subject.saveSubject, params).then(
        res => {
          let { message, status } = res;
          if (status) {
            this.getSubject();
            this.successMessage(message ? message : '保存成功');
            this.doModified(false);
            // 更新
            if (this.handleUpdateModule) this.handleUpdateModule()
          } else {
            this.saving = false;
            this.errorMessage(message);
          }
        },
        () => {
          this.saving = false;
        }
      );
    },
    getSaveData() {
      let params = {
        ...this.extraParams,
        name: this.main.name,
        style: this.template,
        data: []
      };
      params.data = Object.values(this.subjectList)
        .filter(subject => {
          if (this.template === templateType.one.value) {
            return subject.location === location.one;
          } else if (+this.template === 2) {
						return [2, 3, 4].includes(Number(subject.location))
					} else {
            return [5, 6].includes(+subject.location)
          }
        })
        .map(subject => {
          const categoryId = {
            2: subject.category1,
            3: subject.category1 && subject.category1[1],
            29: subject.category1 && subject.category1[2]
          };
          let subjectItem = {
            name: subject.name,
            id: subject.id,
            location: subject.location,
            pic: subject.pic,
            link_type: subject.link_type,
            category_id: categoryId[subject.link_type] || '',
            link_value:(subject.linkInfo && subject.linkInfo.value) || '',
            link_name: subject.link_type === '12' ? subject.linkInfo.name : ''
          };
          let commodityIdArr = subject.commodity_list.map(
            goods => goods[GOODS_KEY]
          );
          if (subject.link_type === goodsType.normal.value ) {
            subjectItem.commodity_id = commodityIdArr.join(',');
          } else {
            subjectItem.package_id = commodityIdArr.join(',');
          }
          return subjectItem;
        });
      params.data = JSON.stringify(params.data);
      return params;
    },
    // checkData(data) {
    //   if (this.template === templateType.style.value) {
    //     return true;
    //   }
    //   try {
    //     data.forEach(item => {
    //       if (!item.pic) {
    //         //this.$refs.img.scrollIntoView();
    //         //this.error.img = "请上传图片";
    //         throw `请给所有专题上传图片!`;
    //       }
    //       if (!item.name) {
    //         throw `请给所有专题输入名称!`;
    //       }
    //       if (!item.commodity_id && !item.package_id) {
    //         throw `请给所有专题设置商品来源!`;
    //       }
    //     });
    //   } catch (err) {
    //     console.log(err);
    //     this.errorMessage(err);
    //     return false;
    //   }
    //   return true;
    // },
    resetError() {
      this.error.img = '';
      this.error.name = '';
      this.error.content = '';
    },
    setTemplate(_template) {
      this.template = _template;
      this.doModified(true);
    },
    setViewImg(_index) {
      this.$emit('on-imgChange', _index);
    },
    closeTemplate() {
      if (!this.ifShowTemplate) {
        this.template = '0';
      } else {
        this.template = '1';
      }
      this.doModified(true);
    },
    showImgUpload(_currentIndex) {
      this.currentIndex = _currentIndex;
      this.showUploadImg = true;
    },
    itemOrderUp(_item, _subjectIndex, _itemIndex) {
      let newIndex = _itemIndex - 1;
      // 第一一专题点上移动
      if (newIndex < 0) {
        newIndex = 0;
      }
      //移除
      this.subjectList[_subjectIndex].commodity_list.splice(_itemIndex, 1);
      this.subjectList[_subjectIndex].commodity_list.splice(newIndex, 0, _item);
      this.updateGoodsSequence();
      this.doModified(true);
    },
    itemOrderDown(_item, _subjectIndex, _itemIndex) {
      let newIndex = _itemIndex + 1;
      // 最后一个banner点击下移
      if (newIndex >= this.subjectList[_subjectIndex].commodity_list.length) {
        newIndex = 0;
      }
      this.subjectList[_subjectIndex].commodity_list.splice(_itemIndex, 1);
      this.subjectList[_subjectIndex].commodity_list.splice(newIndex, 0, _item);
      this.updateGoodsSequence();
      this.doModified(true);
    },
    // 置顶
    itemOrderTop(_item, _subjectIndex, _itemIndex) {
      // Remove the item from its current position
      const item = this.subjectList[_subjectIndex].commodity_list.splice(_itemIndex, 1)[0]
      // Add the item to the beginning of the array
      this.subjectList[_subjectIndex].commodity_list.unshift(item)

      this.updateGoodsSequence();
      this.doModified(true);
    },
    updateGoodsSequence() {
      this.subjectList.forEach(item => {
        item.commodity_list.forEach((goodItem, goodIndex) => {
          goodItem.sequence = goodIndex;
        });
      });
    },
    replceGoods(goodsList) {
      // let exist = false;
      // this.subjectList[_subjectIndex].commodity_list.forEach(goods => {
      //   if (goods.id === _newGoods.id) {
      //     exist = true;
      //   }
      // });
      // if (exist) {
      //   this.errorMessage('不能重复选择商品');
      //   return;
      // }
      if (goodsList) {
        let goods = goodsList[0];
        this.subjectList[this.subjectIndex].commodity_list.splice(
          this.goodIndex,
          1,
          goods
        );
      }
    },
    replcePackage(goodsList) {
      //   let exist = false;
      // this.subjectList[_subjectIndex].package_list.forEach(goods => {
      //   if (goods.id === _newGoods.id) {
      //     exist = true;
      //   }
      // });

      // if (exist) {
      //   this.errorMessage('不能重复选择套餐');
      //   return;
      // }

      if (goodsList) {
        if (goodsList.length > 1) {
          this.errorMessage('最多只能选择一个套餐');
          return false;
        }
        let goods = goodsList[0];
        goods.logo = goods.pic_url;
        this.subjectList[this.subjectIndex].commodity_list.splice(
          this.goodIndex,
          1,
          goods
        );
        this.replacePackage = false;
      }
    },
    selectReplace(commodity_list, _subjectIndex, _goodIndex, _module) {
      this.subjectIndex = _subjectIndex;
      this.goodIndex = _goodIndex;
      if (_module === 'goods') {
        this.replaceGoods = true;
        this.current_commodity_list = commodity_list;
      } else {
        this.replacePackage = true;
        this.current_commodity_list = commodity_list;
      }
    },
    closeReplcePackage() {
      this.replacePackage = false;
    },
    handleLink (linkInfo, index) {
      this.linkIndex = index
      this.$refs.linkModal.open(linkInfo)
      this.showLinkModal = true
    },
    handleLinkChange (value, name = '') {
      this.subjectList[this.linkIndex].linkInfo = {
        name,
        value
      }
    },
  },
  watch: {
    template(newVal) {
      if (newVal === '0') {
        this.setViewImg(3);
      } else {
        this.setViewImg(newVal);
      }
    }
  }
};
</script>
>

<style lang="less">
@menu-bg-color: var(--primary-color-5);
.module-subject {
  min-width: 350px;
  padding: 24px !important;
  width: 563px;
  .subject-menu {
    display: flex;
    justify-content: space-between;
		gap: 16px;
    > div {
      background: rgba(255, 255, 255, 0.96);
      border: 0.5px solid rgba(216, 216, 216, 0.65);
      width: 248px;
      height: 142px;
      text-align: center;
      margin-bottom: 10px;
      // margin-right: 18px;
      &.active {
        border: 0.5px solid rgba(3, 172, 84, 1);
      }
      .menu-type-1 {
        width: 121px;
        height: 72px;
        background: @menu-bg-color;
        display: block;
        margin: 20px auto 0 auto;
        ~ p {
          height: 14px;
          margin: 18px 0 16px 0;
        }
      }
      .menu-type-2 {
        margin: 20px auto 0 auto;
        font-size: 0;
        ~ p {
          height: 14px;
          margin: 18px 0 16px 0;
        }
        > span {
          width: 59px;
          height: 72px;
          background: @menu-bg-color;
          display: inline-block;
        }
        > div {
          display: inline-block;
          margin-left: 12px;
          span {
            width: 54px;
            height: 30px;
            background: @menu-bg-color;
            display: block;
            &:first-of-type {
              margin-bottom: 10px;
            }
          }
        }
      }
			.menu-type-3 {
				margin: 20px 18px ;
				font-size: 0;
				display: flex;
				justify-content: space-between;
				> span {
					width: 55px;
					height: 72px;
					background: @menu-bg-color;
					display: inline-block;
				}
			}
    }
  }
  .module-content > div {
    width: 515px;
    height: auto;
    align-items: start;
    margin-right: 0;
    padding: 16px 0 20px 16px;
  }

  .goods-list {
    .goods-item {
      display: block;
      float: left;
      margin-bottom: 16px;
      font-size: 0;
      .drag {
        position: absolute;
        z-index: 255;
        top: -3px;
        left: 3px;
        font-size: 24px;
        color: #fff;
        display: none;
      }
      &:hover {
        .drag {
          display: block;
        }
      }
      &:first-of-type {
        margin-top: 8px;
      }
    }
  }
  .config {
    float: right;
    height: 14px;
    line-height: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.7);
    /deep/.ivu-switch {
      width: 30px;
      height: 18px;
      top: -1px;
      margin-left: 4px;
      border: none;
      &:focus {
        box-shadow: 0 0 0 0 transparent;
      }
    }
    /deep/.ivu-switch:after {
      width: 15px;
      height: 15px;
      top: 1.2px;
    }
    /deep/.ivu-switch-checked:after {
      left: 14px;
    }
  }
  .help-px {
    display: block;
    text-align: center;
    font-weight: 400;
    color: rgba(144, 144, 144, 1);
    line-height: 14px;
    font-size: 12px;
    margin-top: 8px;
  }
  .subject-img {
    .img-wrap {
      width: 102px;
      height: 102px;
    }
  }
  /deep/.ivu-form-item-content {
    padding-right: 0 !important;
  }
  /deep/.ivu-form-item-label {
    // padding-top: 12px !important;
  }
  .img-holder {
    width: 100%;
    height: 100%;
    background: rgba(246, 248, 249, 0.6);
  }
}
</style>
