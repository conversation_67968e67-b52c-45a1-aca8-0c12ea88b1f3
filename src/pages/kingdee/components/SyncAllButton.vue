<template>
  <div>
    <Button :styleType="styleType" @click="handleClick">{{name}}</Button>
    <s-modal
        :mask-closable="false"
        :closable="false"
        :btns="0"
        type="info"
        ref="sModal"
        :title="name">
        <div style="text-align: center; font-size: 18px">{{now}}/{{count}}</div>
        <div v-if="status === ''" style="text-align: center; font-size: 18px">同步中，请稍后...</div>
        <div v-if="status === 'done'" style="text-align: center; font-size: 18px">同步完成。</div>
        <div v-if="status === 'cancel'" style="text-align: center; font-size: 18px">正在等待最近一次任务完成，请稍后。</div>
        <div style="text-align: right">
          <Button v-if="status === ''" @click="cancelSync">取消</Button>
          <Button v-if="status === 'done'" style="margin-left: 10px" type="primary" @click="toLogsPage">前往查看同步日志页面查看结果</Button>
          <Button v-if="status === 'done'" style="margin-left: 10px" @click="closeModal">确定</Button>
        </div>
    </s-modal>
  </div>
</template>
<script>
import Button from "@components/button";
import { SModal } from '@sdp/ui';
export default {
  name: 'SyncAllButton',
  components: {
    Button,
    SModal
  },
  props: {
    name: {
      require: false,
      default: '同步筛选结果内所有条目'
    },
    syncParams: {
      require: true,
      default: () => {
        return {
          listUrl: '',
          listQuery: {}
        }
      }
    },
    styleType: {
      type: String,
      default: 'default',
    },
    totalKey: {
      type: String,
      default: 'count'
    }
  },
  data () {
    return {
      count: 0,
      now: 0,
      modal: false,
      page: 0,
      status: ''
    }
  },
  created () {
    this.init();
  },
  methods: {
    init () {
      this.page = 0;
      this.done = false;
      this.count = 0;
      this.status = '';
      this.now = 0;
      this.syncTimes = {}
    },
    toLogsPage () {
      this.$router.push('/kingdee/logs');
      this.init();
    },
    handleClick () {
      // 同步次数
      this.syncTimes = {}
      this.$emit('click');
      this.checkIfCanSync();
    },
    stopSync() {
      this.status = '';
      this.$emit('done');
      this.init();
      if (this.$refs.sModal) {
        this.$refs.sModal.close();
      }
    },
    checkIfCanSync () {
      if (this.status === 'cancel') {
        this.$refs.sModal.close();
        this.$emit('done');
        this.init();
        return false;
      }
      let listQuery = Object.assign({}, this.syncParams.listQuery, {
        external_finance_sync_status: 33, // 固定同步未同步的
        page: 1, // 此处 page 固定为1
        pageSize: 1
      });
      this.page += 1;
      this.$request.get(this.syncParams.listUrl, listQuery).then(res => {
        if (res.status && res.data) {
          let count = +res.data.pageParams[this.totalKey];
          this.now += res.data.list.length;
          if (this.page === 1) {
            this.count = count;
          } else {
            if (this.now > this.count) {
              this.now = this.count;
            }
          }
          if (res.data.list.length) {
            this.$refs.sModal.open();
            let ids = res.data.list.map(item => item.id).join(',');
            this.doSync(ids)
          } else {
            if (this.page === 1) {
              this.$Notice.warning({
                title: '警告',
                desc: '没有可以同步的数据'
              });
            } else {
              this.status = 'done';
              this.$emit('done');
            }
          }
        } else {
          this.errorNotice(res.message || '操作失败');
        }
      })
    },
    doSync (ids) {
      if (!this.syncTimes[ids]) {
        this.syncTimes[ids] = 1
      } else {
        if(this.syncTimes[ids] > 3) {
          this.stopSync();
          this.$Notice.warning({
            title: '警告',
            desc: '同步异常，请检查配置'
          });
          return;
        }
        this.syncTimes[ids]++
      }
      this.$request.post(this.syncParams.syncUrl, {id: ids}).then(res => {
        // // 有fail_msg信息就显示fail_msg信息，没有就提示同步成功
        // if (res.data && res.data.fail_msg) {
        //   this.errorNotice({
        //     title: '同步失败',
        //     desc: res.data.fail_msg,
        //     btnTxt:'查看同步日志',
        //     goto: '/kingdee/logs'
        //   });
        // } else {
        this.checkIfCanSync()
        // }
      })
    },
    closeModal () {
      this.$refs.sModal.close();
      this.init();
    },
    cancelSync () {
      this.status = 'cancel';
    }
  }
}
</script>
<style lang="less" scoped>

</style>
