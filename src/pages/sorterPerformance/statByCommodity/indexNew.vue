<template>
  <div id="sort-performance-stat-by-commodity">
    <list-table
      ref="list"
      style="margin-top: 24px"
      :height="getTableHeight() - 140"
      :border="false"
      :outer-border="true"
      row-key="index"
      :table-height="layoutTableHeight"
      :data-provider="apiUrl.sorterPerformance.statByCommodity.list"
      :filter-items="filterItems"
      :before-request="beforeRequest"
      :advance-items="advanceItems"
      :columns="columns"
      keepScroll
    >
      <Button slot="button" @click="exportExcel">导出</Button>
      <div slot="before-table" class="control-box">
        <Checkbox @on-change="getlist" style="margin-right: 40px"  false-value="0" true-value="1" v-model="is_hide_provider"><span style="margin-left: 6px">隐藏供应商分拣信息</span></Checkbox>
				
        <Checkbox @on-change="getlist"  false-value="1" true-value="2" v-model="salary_calc_type">
					<span style="margin-left: 6px">分拣单位基数计算工资</span>
					<Tooltip
						style="margin-left: 6px"
						:transfer="true"
						:max-width="200"
						content="勾选后绩效工资将按照分拣单位的绩效基数来计算分拣工作，未勾选时会按照下单单位的绩效基数计算分拣工资"
					>
						<Icon class="filter__tips" type="ios-alert-outline" /> </Tooltip
					>
				</Checkbox>
      </div>
    </list-table>
  </div>
</template>

<script>
  import freeze from '../freeze'
  import TimeTypeSelect from '@/components/common/TimeTypeSelect'
  import SorterSelect from '@components/common/sorterSelect.vue'
  import GoodsSearch from '@components/common/goodsAutoComplete';
  import LayoutMixin from '@/mixins/layout';
  import ListTable from '@components/list-table';
  import Button from '@components/button';
  import LineSelect from '@components/delivery/lineSelect_new';
	import StoreSelect from '@/components/common/storeSelect.vue';


  export default {
    name: "statByCommodity",
    components: {
      ListTable,
      SorterSelect,
      GoodsSearch,
      Button
    },
    mixins: [ LayoutMixin ],
    data() {
      return {
        selectedGoods: {},
        timeType: 'sorting_time',
        filterItems: [
          {
            type: 'custom',
            component: TimeTypeSelect,
            key: ['start_time', 'end_time', 'date_type'],
            defaultValue: [this.getToday(), this.getToday(), 1],
            props: {
              data: freeze.TIME_TYPE_LIST
            },
            width: 299
          },
          {
            label: '商品',
            type: 'custom',
            component: GoodsSearch,
            props: {
              adaptNewVersion: true
            },
            onChange: (value, goods) => {
              this.selectedGoods = goods;
              return {
                value
              }
            },
            key: 'commodity_name',
          }
        ],
        advanceItems: [
          {
            items: [
              {
                label: '分拣员',
                type: 'custom',
                component: SorterSelect,
                key: 'sorter',
              },
              {
                type: 'custom',
                label: '线路',
                component: LineSelect,
                key: 'line_id',
                props: {
                  multiple: true,
                  maxTagCount: 1,
                  placeholder: '全部',
                }
              },
							{
								type: 'custom',
								component: StoreSelect,
								key: 'store_id',
								label: '仓库',
								props: {
									defaultFirst: false,
									showAll: true,
									filterable: true,
								},
								defaultValue: '',
								placeholder: '选择仓库',
								onChange: (value) => {
									return {
										value: value? value.id : '',
										stop: false
									}
								}
							}
            ]
          }
        ],
        columns: [
          {
            title: '仓库名称',
            key: 'storage_id',
          },
          {
            title: '分拣员',
            key: 'sorter',
          },
          {
            title: '商品名称',
            key: 'name',
          },
          {
            title: '一级分类',
            key: 'category_id',
          },
					{
						title: '二级分类',
						key: 'category_two_name'
					},
          {
            title: '分拣订单数',
            align: 'right',
            key: 'order_count',
          },
          {
            title: '分拣包裹数',
            align: 'right',
            key: 'sort_count_sum',
          },
          {
            title: '分拣数量',
            align: 'right',
            key: 'sort_sum',
          },
          {
            title: '分拣金额',
            align: 'right',
            key: 'total_price',
          },
          {
            title: '分拣重量',
            align: 'right',
            key: 'weight_num',
          },
          {
            title: '分拣工资',
            align: 'right',
            key: 'salary_value',
            render: (h, params) => {
              return h('span', params.row.salary_value || '--')
            }
          },
          {
            type: 'action',
            align: 'right',
            actions: (h, params) => {
              let actions = [];
              actions.push({
                name: '详情',
                action: (params) => {
                  let { row } = params;
                  this.$router.push({
                    path: '/sorter-performance/stat-by-commodity-detail',
                    query: {
                      ...this.requestParams,
                      commodity_id: row.id,
                      sorter: row.sorter_id,
                      sorter_type: row.sorter_type
                    }
                  });
                }
              });
              return actions;
            }
          }
        ],
        requestParams: {},
        is_hide_provider: '0',
				salary_calc_type: '1'
      }
    },
    mounted() {
    },
    activated () {
      this.getlist();
    },
    methods: {
      getlist () {
        this.$refs.list.fetchData(false);
      },
      beforeRequest (params) {
        params.is_hide_provider = this.is_hide_provider;
				params.salary_calc_type = this.salary_calc_type;
        if (this.selectedGoods) {
          params.commodity_id = this.selectedGoods.commodity_id;
        }
        this.requestParams = params;
        return params;
      },
      selectGoods(goods) {
        this.filters.commodity_id = goods.commodity_id;
        this.loadList();
      },
      search() {
        if (!this.filters.commodity_name) {
          this.filters.commodity_id = '';
        }
        this.loadList();
      },
      loadList() {
        let filters = this.getFilters();
        if (!filters.start_time) {
          return false;
        }
        this.$refs.list.loadListData();
      },
      exportExcel() {
        let filters = this.requestParams;
        this.$request.get(this.apiUrl.sorterPerformance.statByCommodity.listExport, filters).then((res) => {
          let { status, message, data } = res;
          if (status) {
            location.href = data;
          } else {
            this.modalError(message ? message : '导出失败', 0);
          }
        });
      }
    }
  }
</script>

<style lang="less" scoped>
  #sort-performance-stat-by-commodity {
    .filter-right {
      text-align: right;
    }
    .search-btn {
      position: absolute;
      right: -24px;
      top: 0;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .control-box{
    height: 30px;
    position: relative;
    display: flex;
    justify-content: flex-end;
  }
  .ivu-checkbox-wrapper {
    margin-right: 0px;
  }
  .ivu-checkbox + span {
    margin-right: 0px;
  }
</style>
