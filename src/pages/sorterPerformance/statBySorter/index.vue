<template>
  <div id="coupon-list">
    <div class="rankingModule">
      <div class="_screening">
        <div class="left-fil">
          <div class="_titles">
            <span class="_icons"></span>
            <span class="_titleContent">分拣绩效排名</span>
          </div>
          <div class="_timeSwitch">
            <Select v-model="filters.date_type" @on-change="fetchData(true)">
              <Option
                v-for="item in timeTypeList"
                :value="item.value"
                :key="item.value"
                >{{ item.label }}</Option
              >
            </Select>
            <RadioGroup
              size="default"
              style="margin-bottom: 3px"
              v-model="dateSelection"
              @on-change="daterangeChange"
              type="button"
            >
              <Radio :label="1">昨日</Radio>
              <Radio :label="2">近7日</Radio>
              <Radio :label="3">近30天</Radio>
            </RadioGroup>
            <DatePicker
              v-model="datePickerDate"
              @on-change="datePickerChange"
              type="daterange"
              :clearable="false"
              placeholder="请选择日期"
              class="_datePicker"
            ></DatePicker>
          </div>
          <div class="store-main">
            <span>仓库:</span>
            <StoreSelect
              placeholder="全部仓库"
              @on-change="storeChange"
              :show-all="true"
              v-model="filters.store_id"
              filterable
              clearable
              :defaultFirst="false"
            ></StoreSelect>
          </div>
        </div>
        <div class="_sorting">
          <RadioGroup
            v-model="sort_value"
            @on-change="sortValueChange"
            type="button"
          >
            <Radio :label="1">分拣订单数</Radio>
            <Radio :label="2">分拣包裹数</Radio>
            <Radio :label="3">分拣数量</Radio>
            <Radio :label="4">分拣金额</Radio>
            <Radio :label="5">分拣重量</Radio>
          </RadioGroup>
        </div>
      </div>
      <div class="_ranking">
        <div id="main"></div>
      </div>
    </div>
    <div class="divider"></div>
    <div style="margin-top: 20px; padding: 0 20px 10px">
      <ListTable
        ref="list"
        :border="false"
        :filters="filters"
        :outer-border="true"
        :max-line="2"
        :before-request="beforeRequest"
        :after-request="afterRequest"
        row-key="order_no"
        :filter-items="filterItems"
        :columns="columns"
        :height="getTableHeight()"
        data-provider="/superAdmin/sorterHistory/accordingSorter"
      >
        <Button type="default" slot="button" @click="exportExcel">导出</Button>
      </ListTable>
    </div>
  </div>
</template>

<script>
// eslint-disable-next-line no-unused-vars
import freeze from '../freeze';
import { get, post } from '@api/request';
import { api } from '@api/api';
import echarts from '@/common/init-echarts.js';
import imgs1 from '@/assets/images/ranking/NO1.svg';
import imgs2 from '@/assets/images/ranking/NO2.svg';
import imgs3 from '@/assets/images/ranking/NO3.svg';
import ListTable from '@components/list-table';
import {
  getYesterDay,
  getWeekStartDate,
  getWeekEndDate,
  getRecentDay,
  // eslint-disable-next-line no-unused-vars
  getLastWeekStartDate,
  // eslint-disable-next-line no-unused-vars
  getLastWeekEndDate,
} from '@assets/js/getDate.js';
import StoreSelect from '@/components/common/storeSelect.vue';

const imgList = [imgs1, imgs2, imgs3];
function renderItem(params, api) {
  const topCenter = api.coord([api.value(0), api.value(1)]); // 顶点中心
  let groups = {
    // 图片装饰
    type: 'image',
    style: {
      image: '',
      x: topCenter[0] - 17,
      y: topCenter[1] - 25,
      width: 35,
      height: 26,
    },
  };
  groups.style.image = imgList[params.dataIndex];
  return groups;
}
let myChart = null;
export default {
  name: 'statBySorter',
  components: {
    StoreSelect,
    ListTable,
  },
  data() {
    return {
      sortType: 1,
      timeTypeList: freeze.TIME_TYPE_LIST,
      listData: [],
      canRequest: true,
      start_date: '',
      end_date: '',
      baseData: [],
      sort_value: 1,
      baseIconData: [],
      sorterList: [],
      dateSelection: 2,
      filterItems: [
        {
          label: '搜索',
          type: 'Select',
          key: 'sorter',
          props: {
            placeholder: '输入分拣员',
            filterable: true,
            clearable: true,
          },
          data: [],
        },
        {
          type: 'CheckboxGroup',
          key: 'salary_calc_type',
          tooltip: {
            afterItem: true,
            maxWidth: '200',
            style: {
              marginLeft: '-20px',
            },
            content:
              '勾选后绩效工资将按照分拣单位的绩效基数来计算分拣工作，未勾选时会按照下单单位的绩效基数计算分拣工资',
          },
          style: {
            width: '188px',
          },
          data: [
            {
              label: '按分拣单位基数计算工资',
              value: 1,
            },
          ],
        },
        {
          type: 'CheckboxGroup',
          key: 'is_hide_provider',
          data: [
            {
              label: '隐藏供应商分拣信息',
              value: 1,
            },
          ],
        },
      ],
      datePickerDate: [getRecentDay(7), this.getToday()],
      filters: {
        store_id: '',
        // sorter: '',
        date_type: 1,
        start_time: getRecentDay(7),
        end_time: this.getToday(),
      },
      columns: [
        {
          title: '仓库名称',
          key: 'storage_id',
        },
        {
          title: '分拣员',
          key: 'sorter',
        },
        {
          title: '分拣订单数',
          key: 'order_count',
        },
        {
          title: '分拣包裹数',
          key: 'sort_count_sum',
        },
        {
          title: '分拣数量',
          key: 'sort_sum',
        },
        {
          title: '分拣金额',
          key: 'total_price',
        },
        {
          title: '分拣重量',
          key: 'weight_num',
        },
        {
          title: '分拣工资',
          key: 'salary_value',
        },
      ],
    };
  },
  created() {
    this.getSorterList();
  },
  activated() {
    this.fetchData();
  },
  deactivated() {
    this.deInit();
  },
  methods: {
    afterRequest(val) {
      if (!val.status) {
        this.errorMessage(val.message);
      }
      console.log(val, '--/');
      this.listData = val.data.list;
      this.getRankingData(this.sortType);
      return val;
    },
    beforeRequest(val) {
      if (!this.canRequest) {
        return false;
      }

      val.is_hide_provider && val.is_hide_provider.length
        ? (val.is_hide_provider = 1)
        : (val.is_hide_provider = 0);
      val.salary_calc_type && val.salary_calc_type.length
        ? (val.salary_calc_type = 2)
        : (val.salary_calc_type = 1);

      this.canRequest = false;
      setTimeout(() => {
        this.canRequest = true;
      }, 100);
      return val;
    },
    getRankingData(val) {
      this.sortType = val;
      this.listData.sort((a, b) => {
        if (val === 1) {
          return b.order_count - a.order_count;
        } else if (val === 2) {
          return b.sort_count_sum - a.sort_count_sum;
        } else if (val === 3) {
          return b.sort_sum - a.sort_sum;
        } else if (val === 4) {
          return b.total_price - a.total_price;
        } else {
          return b.weight_num - a.weight_num;
        }
      });
      let list = this.deepClone(this.listData);
      this.listData = list.slice(0, 10);
      this.init();
    },
    sortValueChange(val) {
      this.getRankingData(val);
    },
    daterangeChange(val) {
      switch (val) {
        case 1:
          this.start_date = getYesterDay();
          this.end_date = getYesterDay();
          this.filters.start_time = getYesterDay();
          this.filters.end_time = getYesterDay();
          this.datePickerDate = [getYesterDay(), getYesterDay()];
          this.fetchData(true);
          break;
        case 2:
          this.start_date = getRecentDay(7);
          this.end_date = this.getToday();
          this.filters.start_time = this.start_date;
          this.filters.end_time = this.end_date;
          this.datePickerDate = [getRecentDay(7), this.getToday()];
          this.fetchData(true);
          break;
        case 3:
          this.start_date = this.getCurrentLastMonth();
          this.end_date = this.getToday();
          this.filters.start_time = this.getCurrentLastMonth();
          this.filters.end_time = this.getToday();
          this.datePickerDate = [this.getCurrentLastMonth(), this.getToday()];
          this.fetchData(true);
          break;
      }
    },
    storeChange(val) {
      this.fetchData(true);
    },
    async getSorterList() {
      let { data, status, message } = await get(api.getSorter, {
        page: 1,
        pageSize: 99999,
      });
      if (status) {
        this.filterItems.forEach((item, index) => {
          if (item.key === 'sorter') {
            this.filterItems[index].data = data.sorter_list.map((i) => {
              return {
                value: i.id,
                label: i.username,
              };
            });
            this.filterItems[index].data.unshift({
              value: '',
              label: '全部',
            });
          }
        });
      } else {
        this.$Notice.error({
          title: message,
        });
      }
    },
    init() {
      const option = {
        grid: {
          height: '80%',
          y: '12%',
          left: '60',
          right: '5',
        },
        tooltip: {
          trigger: 'axis',
          formatter: '',
        },
        xAxis: {
          type: 'category',
          data: [],
          axisLine: {
            lineStyle: {
              color: 'rgba(178, 178, 178, 0.85)',
            },
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value}',
            color: 'rgba(0, 0, 0, 0.65)',
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'rgba(178, 178, 178, 0.85)',
            },
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            color: 'rgba(0, 0, 0, 0.65)',
          },
          splitLine: {
            lineStyle: {
              color: '#D8D8D8',
              width: 0.5,
              type: 'dashed',
            },
          },
        },
        series: [
          {
            type: 'bar',
            barWidth: 30,
            data: [],
            z: 10,
            itemStyle: {
              normal: {
                color: function (params) {
                  let colorList = [
                    '#FF6E58',
                    '#FF943C',
                    '#FFC137',
                    '#11B05D',
                    '#11B05D',
                    '#11B05D',
                    '#11B05D',
                    '#11B05D',
                    '#11B05D',
                    '#11B05D',
                  ];
                  return colorList[params.dataIndex];
                },
              },
            },
          },
          {
            type: 'custom',
            data: [],
            renderItem: '',
          },
        ],
      };
      let list = [];
      if (this.listData.length > 0) {
        this.baseData = [];
        this.listData.forEach((item) => {
          list.push(item.sorter);
        });
        let baseIconData = [];
        this.listData.forEach((item) => {
          switch (this.sort_value) {
            case 1:
              option.tooltip.formatter = function (params) {
                return `NO.${params[0].dataIndex + 1} ${
                  params[0].name
                }<br />分拣订单数：${params[0].value}`;
              };
              baseIconData.push(item.order_count);
              break;
            case 2:
              option.tooltip.formatter = function (params) {
                return `NO.${params[0].dataIndex + 1} ${
                  params[0].name
                }<br />分拣包裹数：${params[0].value}`;
              };
              baseIconData.push(item.sort_count_sum);
              break;
            case 3:
              option.tooltip.formatter = function (params) {
                return `NO.${params[0].dataIndex + 1} ${
                  params[0].name
                }<br />分拣数量：${params[0].value}`;
              };
              baseIconData.push(item.sort_sum);
              break;
            case 4:
              option.tooltip.formatter = function (params) {
                return `NO.${params[0].dataIndex + 1} ${
                  params[0].name
                }<br />分拣金额：￥${params[0].value}`;
              };
              baseIconData.push(item.total_price);
              break;
            case 5:
              option.tooltip.formatter = function (params) {
                return `NO.${params[0].dataIndex + 1} ${
                  params[0].name
                }<br />分拣重量：${params[0].value}`;
              };
              baseIconData.push(item.weight_num);
              break;
          }
        });

        option.series[0].data = baseIconData;
        option.xAxis.data = list;
        option.series[1].data = baseIconData;
        option.series[1].renderItem = renderItem;
      } else {
        myChart.clear();
        return;
      }
      this.$nextTick(() => {
        console.log(myChart, document.getElementById('main'));
        if (!myChart) {
          myChart = echarts.init(document.getElementById('main'));
        }
        // 一个页面只有一个echarts图形时自适应
        myChart.setOption(option, true);
        window.onresize = myChart.resize;
      });
    },
    deInit() {
      window.onresize = null;
      myChart.clear();
      myChart.dispose();
      myChart = null;
    },
    datePickerChange(val) {
      this.dateSelection = '0';
      this.filters.start_time = val[0];
      this.filters.end_time = val[1];
      this.fetchData(true);
    },
    getFilters() {
      let params = this.$refs.list.getParams();
      if (params.is_hide_provider && params.is_hide_provider.length) {
        params.is_hide_provider = 1;
      } else {
        params.is_hide_provider = 0;
      }
      return params;
    },
    exportExcel() {
      let filters = this.getFilters();
      filters.salary_calc_type && filters.salary_calc_type.length
        ? (filters.salary_calc_type = 2)
        : (filters.salary_calc_type = 1);
      this.$request
        .get(this.apiUrl.sorterPerformance.statBySorter.export, filters)
        .then((res) => {
          let { status, message, data } = res;
          if (status) {
            location.href = data;
          } else {
            this.modalError(message ? message : '导出失败', 0);
          }
        });
    },
    /**
     * @description: 取表格数据
     * @param {Boolean} resetPage 是否重置分页
     */
    fetchData(resetPage) {
      this.$refs.list.fetchData(resetPage);
    },
  },
  beforeDestroy() {
    this.deInit();
  },
};
</script>

<style lang="less" scoped>
#coupon-list {
  .filter-right {
    text-align: right;
  }
}

.rankingModule {
  margin: 10px 0 40px;
}
._ranking {
  height: 300px;
  margin: 10px;
  div {
    width: 100%;
    height: 300px;
  }
}
.divider {
  height: 16px;
  background: #f2f3f5;
  width: 100%;
}
._icons {
  display: inline-block;
  width: 3px;
  height: 12px;
  background: #333333;
}
._screening {
  //height: 30px;
  position: relative;
  display: flex;
  justify-content: space-between;
}
.left-fil {
  width: 65%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}
._titles {
  width: 110px;
  height: 30px;
  line-height: 30px;
  //position: absolute;
  top: 0;
  left: 0;
}
._sorting {
  // width: 360px;
  height: 30px;
  line-height: 30px;
  //position: absolute;
  right: 20px;
  top: 0;
}
._datePicker {
  width: 228px;
}
._timeSwitch {
  // width: 588px;
  width: auto;
  height: 30px;
  line-height: 30px;
  > .ivu-select {
    width: 120px;
  }
  > div ~ div {
    margin-left: 20px;
  }
}
.store-main {
  margin-left: 20px;
  display: flex;
  align-items: center;
  > span {
    // 文字禁止换行
    white-space: nowrap;
    margin-right: 10px;
  }
}
@media (max-width: 1365px) {
  ._screening {
    ._sorting {
      right: 0;
    }
    ._timeSwitch {
      left: 115px;
      > div ~ div {
        margin-left: 10px;
      }
      ._datePicker {
        width: 190px;
      }
      > .ivu-select {
        width: 90px;
      }
    }
  }
}
._titleContent {
  width: 96px;
  height: 16px;
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  line-height: 16px;
  display: inline-block;
  margin-left: 2px;
}
</style>
