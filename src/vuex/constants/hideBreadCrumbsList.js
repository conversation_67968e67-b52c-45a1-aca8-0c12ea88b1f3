/*
 * @Author: Eco
 * @Date: 2022-03-01 14:20:32
 * @LastEditTime: 2023-08-25 11:23:20
 * @Description: 需要隐藏面包屑高度的页面路由
 */
export default [
  '/purchase/report-price/report-price-detail',
  '/purchase/returnOrderDetail',
  '/caixiao-dingjia/list',
  '/caixiao-dingjia/add',
  '/sort-tpl/rules/list',
  '/sort-tpl/rules/add',
  '/sort-tpl/rules/edit',
  '/sort-tpl/rules/detail',
  '/sort-tpl/settings',
  '/monthly-bills/reports',
  '/monthly-bills/setting',
  '/monthly-bills/init',
  '/finance/newBills',
  '/finance/edit',
  '/finance/purchaseReturnAuditDetail',
  '/shop/notice/list',
  '/user-credit',
  '/deliveryTime',
  '/role',
  '/openingHoursConfig',
  '/appCenter/beginningBalance',
  '/storeRoom/importInventory',
  '/appCenter/provider-splitting-bill/audit/list',
  '/appCenter/provider-splitting-bill/flowing/list',
  '/appCenter/provider-splitting-bill/refund-fail/list',
  '/orderHistory',
  '/commodityList/newCommodity',
  '/orderChangePrice/new',
  '/orderChangePrice/record',
  '/indexMini',
  '/indexNew',
  '/index',
  '/goods/list',
  '/commodityList/commodityDetail',
  '/goodsImg',
  '/appCenter/index',
  '/appCenter/list',
  '/behalfNewOrder',
  '/behalfOrderEdit',
  '/behalfOrderDetail',
  '/reports/sales/trade',
  '/order/list',
  '/orderDetail',
  '/order-manage/list',
  '/order-manage/detail',
  '/purchase/order-list',
  '/user/list',
  '/finance/user/audit-list',
  '/coupon/list',
  '/delivery/line/list',
  '/delivery/line/template',
  '/delivery/line/template/form',
  '/delivery/line/range',
  '/shop/home-v3',
  '/shop/home-v3/color',
  '/shop/home-v3/base-info',
  '/sort-code-list',
  '/appCenter/sort-verify',
  '/sorting/commodity',
  '/sort/diff-sort',
  '/sort/statByCommodity',
  '/sort/statByShop',
  '/sort/outOfStockByCommodity',
  '/sorting/standardProduct',
  '/loss-profit-report',
  '/loss-profit-report/detail',
  '/finance/funds-list',
  '/reports/profit/finance',
  '/finance/fund-account/account-manage',
  '/finance/fund-account/account-entries',
  '/finance/purchaseAuditList/byInOrder',
  '/finance/purchaseAuditList/byPurchaseOrder',
  '/reports/profit/sale',
  '/reports/profit/user',
  '/reports/profit/depository',
  '/reports/profit/income-statement',
  '/reports/sales/user-statistics',
  '/reports/sales/seller-statistics',
  '/reports/sales/goods',
  '/reports/loss/profile',
  '/reports/purchase/price-fluctuation',
  '/reports/purchase/plan',
  '/reports/receive-statistics/byGoods',
  '/reports/receive-statistics/byDiff',
  '/purchase/collectList',
  '/reports/sales/order-statistics',
  '/goods-package-recipe/add', // 周菜谱
  '/goods-package-recipe/list', // 周菜谱
  '/package-nutrition', // 营养分析报表
  // '/delivery/set-line/list', // 物流排线
  '/guide', // 新手引导
  '/delivery/statistics/list',
  '/data-screen',
  '/trace-screen',
  '/deliver-screen',
  '/distribution-screen',
  '/documentsPrint', // 单据打印
  '/settings/print-template',
  '/video-presentation',
  '/purchase/provider', // 供应商
  '/purchase/purchaseOrderList', // 采购-供应商
  '/abnormalOrdersList',
  '/purchase/report-price/list',
  '/purchase/offerList',
  '/purchase/purchaseSituation', // 采购-采购情况
  '/process/workersPerformance/list',
  '/process/picking/list',
  '/process/pickingReturn/list',
  '/process/completion/list',
  '/process/goodsBom/list', // BOM表
  '/process/productLine/list', //生产线
  '/delivery/car/list',
  '/order/orderDirectPurchaseList',
  '/storeRoom/deliveryMGTList',
  '/storeRoom/storeDetail',
  '/storeRoom/partition-record/list',
  '/finance/purchaseSum/settleFlowing',
  '/finance/purchaseSum/paySum',
  '/storeRoom/inRoomQuery/collect',
  '/storeRoom/inRoomQuery/flow',
  '/storeRoom/inRoomQuery/container',
  '/storeRoom/earlyRepertory',
  '/storeRoom/addNewStorage',
  '/storeRoom/antiAuditOrder',
  '/storeRoom/reviewInboundOrder',
  '/storeRoom/addNewDeliveryOrder',
  '/storeRoom/auditOutStoreOrder',
  '/storeRoom/outStoreDetail',
  '/sorter-performance/stat-by-sorter',
  '/sorter-performance/stat-by-commodity',
  '/sorter-performance/sort-history',
  '/appCenter/WarehouseManagement',
  '/basket/circulation-statistics',
  '/reports/onlinePayList',
  '/reports/onlinePayDetail',
  '/reports/onlinePayFlow',
  '/reports/onlinePaySummary',
  '/appCenter/costCalculation/new',
  '/purchase/advancePurchase',
  '/sorter/list',
  '/delivery/line/order',
  '/delivery/line/user',
  '/returnOrder',
  '/orderState',
  '/delivery/driver/list',
  '/delivery/area/list',
  '/delivery/area/unavailableGoods',
  '/userType',
  '/agreementPrice/list',
  '/purchase/buyer',
  '/groupManage',
  '/group-manage/profile',
  '/group-manage/administrator',
  '/storeRoom/storageMGTList',
  '/purchase/plan',
  '/log',
  '/fail-log',
  '/finance/userBalanceFlowing',
  '/storeRoom/existingStockList',
  '/storeRoom/storeSetting',
  '/purchase/purchaseReturnList',
  '/taxRate/supplierTaxRate',
  '/taxRate/rules/list',
  '/basket/list',
  '/empty',
  '/blank',
  '/appCenter/customerCommodityAlias',
  '/taxRate/inputTax/list',
  '/basket/circulation-manage',
  '/finance/purchaseAuditList',
  '/commoditySource/search',
  '/commoditySource/InspectionRecords',
  '/commoditySource/AccountSetting',
  '/commoditySource/CustomizeField',
  '/commoditySource/expireStatistics',
  '/commoditySource/expireStatisticsByGoods',
  '/commoditySource/list/add',
  '/commoditySource/list/edit',
  '/commoditySource/list/copy',
  '/order-modify/list',
  '/recipe/file',
  '/recipe/file/add',
  '/recipe/file/edit',
  '/recipe/file/audit',
  '/recipe/file/detail',
  '/recipe/order',
  '/recipe/order/add',
  '/recipe/order/edit',
  '/recipe/order/audit',
  '/recipe/order/detail',
  '/recipe/mealTimeSet',
  '/recipe/process',
  '/marketing/send-group-message',
  '/marketing/specialActivity/form',
  '/appCenter/auto-purchase-order/config',
  '/appCenter/auto-purchase-order/list',
  '/appCenter/crm/team-add',
  '/appCenter/crm/team-edit',
  '/appCenter/form',
  '/appCenter/crm/sales-add',
  '/appCenter/crm/sales-edit',
  '/appCenter/crm/task-add',
  '/appCenter/crm/task-edit',
  '/appCenter/crm/task-detail',
  '/auth-login',
  '/changePassword',
  '/user/exclusiveOrderLabel',
  '/smartPricingNew',
  '/delivery/set-line/list',
  '/orderApproval',
  '/orderTag',
  '/batchSummary',
  '/delivery/setLine/map',
  '/profile',
  '/finance/batchChecking',
  '/finance/customer-cost',
  '/appCenter/certificate/setting',
  '/reports/inventory/invoicing',
  '/reports/inventory/customerInventory',
  '/bills/order/list',
  '/bills/order/detail',
  '/bills/order/edit',
  '/bills/order/add',
  '/finance/userBalanceList/userBalance',
  '/finance/userBalanceList/userBalanceFlowing',
  '/order/alter',
  '/reports/purchaseWaste',
  '/reports/depositoryWaste',
  '/reports/returnWaste',
  '/addSceneOrder',
  '/returnDetail',
  '/goods-package/list',
  '/deliveryMap',
  '/behalfOrder',
  '/sysConfig',
  '/process/processImg',
  '/finance/user/audit-list-new',
  '/finance/userAuditDetail',
  '/purchase/provider/add',
  '/purchase/provider/baseInfoView',
  '/purchase/provider/baseInfo',
  '/sortBoxConfig',
  '/newReturnOrder',
  '/NewOrderReport',
  '/billsOrderSet',
  '/matchImg',
  '/order/merge',
  '/appCenter/printReceipt',
  '/freight/setting',
  '/freight/template',
  '/appCenter/mall-inventory/typeList',
  '/appCenter/mall-inventory/dataList',
  '/appCenter/mall-inventory/videoList',
  '/appCenter/system/logs',
  '/appCenter/system/goods',
  '/appCenter/system/order',
  '/appCenter/system/print',
  '/appCenter/system/customer',
  '/appCenter/system/return',
  '/appCenter/system/settlement',
  '/appCenter/affordableGoodsManagement',
  '/appCenter/selfPickUp',
  '/recycle/userRecycle',
  '/recycle/shopRecycle',
  '/recycle/commodityRecycle',
  '/recycle/areaRecycle',
  '/goods/salesSituation',
  '/delivery/video/list',
  '/delivery/video/history',
  '/marketing/goodsComment',
  '/orderChangeRecord',
  '/causeManagement',
  '/traceabilityPlatform/apollo-v2/init',
  '/traceabilityPlatform/apollo-v2/goods',
  '/traceabilityPlatform/apollo-v2/customer',
  '/traceabilityPlatform/apollo/init',
  '/traceabilityPlatform/apollo/goods',
  '/traceabilityPlatform/apollo/customer',
  '/traceabilityPlatform/apollo/order',
  '/traceabilityPlatform/apollo/log',
  '/traceabilityPlatform/shaoxing/init',
  '/traceabilityPlatform/shaoxing/goods',
  '/traceabilityPlatform/shaoxing/customer',
  '/traceabilityPlatform/shaoxing/log',
  '/traceabilityPlatform/suyuan/init',
  '/traceabilityPlatform/suyuan/goods',
  '/traceabilityPlatform/suyuan/order',
  '/traceabilityPlatform/suyuan/customer',
  '/traceabilityPlatform/suyuan/log',
  // 安鲜行
  '/traceabilityPlatform/anxianxing/init',
  '/traceabilityPlatform/anxianxing/asyncConfig',
  '/traceabilityPlatform/anxianxing/goods',
  '/traceabilityPlatform/anxianxing/customer',
  '/traceabilityPlatform/anxianxing/log',
  '/traceabilityPlatform/anxianxing/order',
  '/traceabilityPlatform/anxianxing/supplier',
  '/traceabilityPlatform/anxianxing/standing-book',
  '/traceabilityPlatform/hainan/init',
  '/traceabilityPlatform/hainan/asyncConfig',
  '/traceabilityPlatform/hainan/goods',
  '/traceabilityPlatform/hainan/customer',
  '/traceabilityPlatform/hainan/provider',
  '/traceabilityPlatform/hainan/log',
  '/traceabilityPlatform/hainan/in',
  '/traceabilityPlatform/hainan/out',
  '/traceabilityPlatform/hainan/stock',
  '/traceabilityPlatform/zhejiang/init',
  '/traceabilityPlatform/zhejiang/asyncConfig',
  '/traceabilityPlatform/zhejiang/goods',
  '/traceabilityPlatform/zhejiang/customer',
  '/traceabilityPlatform/zhejiang/provider',
  '/traceabilityPlatform/zhejiang/log',
  '/traceabilityPlatform/zhejiang/in',
  '/traceabilityPlatform/zhejiang/out',
  '/traceabilityPlatform/zhejiang/stock',
  '/traceabilityPlatform/guizhou/init',
  '/traceabilityPlatform/guizhou/goods',
  '/traceabilityPlatform/guizhou/provider',
  '/traceabilityPlatform/guizhou/log',
  '/traceabilityPlatform/guizhou/in',
  '/traceabilityPlatform/chongqing/init',
  '/traceabilityPlatform/chongqing/goods',
  '/traceabilityPlatform/chongqing/customer',
  '/traceabilityPlatform/chongqing/provider',
  '/traceabilityPlatform/chongqing/log',
  '/traceabilityPlatform/chongqing/in',
  '/traceabilityPlatform/chongqing/out',
  '/traceabilityPlatform/chongqing/stock',
  // oa 审批
  '/oa-audit/helper',
  '/oa-audit/user/list',
  '/oa-audit/audit/list',
  '/oa-audit/audit/detail',
  '/oa-audit/audit/edit',
  '/oa-audit/template/list',
  '/oa-audit/template/detail',
  '/oa-audit/template/edit',
  '/oa-audit/statistics/edit',
  '/appCenter/separateOrder/settings/new',
  '/appCenter/separateOrder/settings/edit',
  '/appCenter/customerCommodityAlias/goodsAlias',
  '/appCenter/customerCommodityAlias/goodsAlias/new',
  '/appCenter/customerCommodityAlias/goodsAlias/edit',
  '/appCenter/customerCommodityAlias/goodsAlias/detail',
  '/appCenter/customerCommodityAlias/categoryAlias',
  '/appCenter/customerCommodityAlias/categoryAlias/new',
  '/appCenter/customerCommodityAlias/categoryAlias/edit',
  '/appCenter/providerComment/list',
  '/appCenter/providerComment/stat',
  '/appCenter/providerComment/setStar',
  '/appCenter/providerComment/statManage',
  '/appCenter/providerComment/statStatistics',
  '/appCenter/providerComment/setting',
  '/appCenter/topSellPrice',
  '/group-manage/unavailableGoods',
  '/order-modify/add',
  '/order-modify/edit',
  '/order-modify/detail',
  '/addDirectPurchaseOrder',
  '/editDirectPurchaseOrder',
  '/viewDirectPurchaseOrder',
  '/goods-package-reports',
  '/package-recipe/list',
  '/settings/mall-app',
  '/process/reports/yield',
  '/process/orderSummary',
  '/process/worker/list',
  '/process/workersManagement/list',
  '/tuancan/operate-area',
  '/tuancan/point/enterprises',
  '/tuancan/report/operation',
  '/tuancan/mall/service-terms',
  '/tuancan/setting/process',
  '/tuancan/standing-list',
  '/delivery/driving-report/list',
  '/appCenter/self-collection-cabinet/order',
  '/appCenter/self-collection-cabinet/data',
];
