/**全局默认设置**/
html,body{color:#555;background:#F5F5F5; font-size:13px;}
a{color:#555;}

/**默认设置**/
.cdefault{color:#99d9f8}
.bgdefault{background:#99d9f8}
.bdefault{border:1px solid #99d9f8;}
.bgf7{background:#f7f7f7;}
.pl160{padding:160px;}

/**文字颜色定义**/
.cpurple{color:#91aae9;} /*紫色*/
.cgreen{color:var(--primary-color);}
/*.cblue{color:#29abed;}*/
.cblue{color:#3fb884;} /*蓝色统一为绿色*/
.cred{color:#ff2630;}
.corange{color:#f58971;}
.cwhite{color: white}

/**背景颜色定义**/
.bgred{background:#ff2630;}
/*.bgblue{background:#29abed;}*/
.bgblue{background:#3fb884;} /*蓝色统一为绿色*/
.bgorange{background:#f58971;}
.bgy<PERSON>w{background:#3fb884;}
.bgf5{background:#f5f5f5;}

/*border 颜色*/
.bd_gred{border-color: #ff2630}
/**按钮样式**/
.butdefault,.butyellow,.butblue{height:32px; display: inline-block; padding: 3px 20px; margin-bottom: 0; font-size: 14px; font-weight: 400; line-height: 1.42857143; text-align: center; white-space: nowrap; vertical-align: middle; -ms-touch-action: manipulation; touch-action: manipulation; cursor: pointer; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-image: none; color: #555; background-color: #fff; border: 1px solid transparent; border-radius: 2px; }

.butdefault,div.butdefault,a.butdefault,i.butdefault,span.butdefault,p.butdefault,
.butgreen,div.butgreen,a.butgreen,i.butgreen,span.butgreen,p.butgreen,
.butyellow,div.butyellow,a.butyellow,i.butyellow,span.butyellow,p.butyellow,
.butblue,div.butblue,a.butblue,i.butblue,span.butblue,p.butblue{
    padding: 0px 25px;
    line-height: 30px;
    border:none;
}
.butdefault,div.butdefault,a.butdefault,i.butdefault,span.butdefault,p.butdefault{/*默认按钮*/
    border:1px solid #ccc;
    color:#bbb;
}
.butgreen,div.butgreen,a.butgreen,i.butgreen,span.butgreen,p.butgreen{/*黄色按钮*/
    border:1px solid #03ac54;
    background:var(--primary-color);
    color:#FFF;
}
.butyellow,div.butyellow,a.butyellow,i.butyellow,span.butyellow,p.butyellow{/*黄色按钮*/
    border:1px solid #3fb884;
    background:#3fb884;
    color:#FFF;
}
.butblue,div.butblue,a.butblue,i.butblue,span.butblue,p.butblue{/*蓝色按钮*/
/*    border:1px solid #29abed;
    background:#29abed;*/
    border:1px solid #3fb884; /*蓝色统一为绿色*/
    background:#3fb884;
    color:#FFF;
}

/**整站左导航图标**/
.SSicon{
    font-size:22px !important;
    color:#FFF;
    float:left;
    padding-left: 20px;
    padding-right: 10px;
}
.SSiconSpan{
    line-height:48px;
    display:inline-block;
}

/**首页样式**/
/* .SShomebg1{background:#f58971 url("../image/homeimg1.png") no-repeat center center;} */
/* .SShomebg2{background:#f5b451 url("../image/homeimg2.png") no-repeat center center;} */
/* .SShomebg3{background:#42bbce url("../image/homeimg3.png") no-repeat center center;} */
.SShomecolor1{color:#f58971;}
.SShomecolor2{color:#f5b451;}
.SShomecolor3{color:#42bbce;}

/**
*其他样式
*/

/**定义右侧Tab标题**/
.SStabTitle{height:44px; line-height:44px; font-size:14px; background:#FFF; position:relative; border-bottom: 1px solid #e6e6e6;}
.SStabTitle .manage.bgf{background:transparent; position: relative; color:var(--primary-color);}
.SStabTitle .manage.bgf:after{position:absolute; left:0px; bottom:0px; content: ""; display: block; height: 2px; width: 100%; background:var(--primary-color);}
.SStabTitle a.bgf{background:transparent; position: relative; color:var(--primary-color);}
.SStabTitle a.bgf:after{position:absolute; left:0px; bottom:0px; content: ""; display: block; height: 2px; width: 100%; background:var(--primary-color);}
.SStabTitle>a{margin-left:15px; margin-right:30px; display:inline-block;cursor:pointer;}
.SStabTitle>span{margin-left:15px; margin-right:30px; display:inline-block;cursor:pointer;}
/**定义左侧带红色块非Tab标题**/
.SSblockTitle:after{display:table; content:''; clear:both}
.SSblockTitle{position:relative; padding-top:5px; padding-bottom:20px; display:none;}
.SSblockTitle>span{font-size:16px; float: left; height:16px; line-height:16px;}
.SSblockTitle i{width:13px; height:13px; background:#ff2630; float: left; display: inline-block; margin-top:2px; margin-right:15px;}
/**定义右侧按钮，打印、新增、导出等**/
.SSrightBtn{}
/**定义右侧最小宽度**/
.SSrightmin{min-width: 1040px;}

/*线条*/
.SSline{position:relative; width:30px;}
.SSline:after{
    content:"";
    position: absolute; left: 0px; top:50%;
    display: block;
    margin-left:20%;
    height:1px;
    width: 60%;
    background-color: #a6a6a6;
}
/*红点*/
.SSdot{position:relative;}
.SSdot:after{
    content:"";
    position: absolute; left: 2px; top:50%;
    display: block;
    height:6px;
    width: 6px;
    margin-top:-3px;
    border-radius: 10px;
    background-color: #fa552c;
}

/*标题前绿点*/
.SStitleDot {
    position: relative;
    font-weight:normal;
    padding-left:18px;
}
.SStitleDot:after{
    content: ""; display: block;
    position: absolute;
    top:50%; left: 2px;
    width:6px; height: 6px;
    margin-top:-3px;
    border-radius: 4px;
    background: var(--primary-color);
}

/**
*头部导航
*/
.SSheader li{float:left; margin-left:30px;}
.SSheader li i{float:left; margin-right:8px; font-size:18px;}
/**头部右侧功能图标**/
.SStopIcon1,.SStopIcon2,.SStopIcon3,.SStopIcon4{padding-left:25px; padding-right:15px;}
.SStopIcon1{background:url(/adminv3/image/top1.png) no-repeat left center;}
.SStopIcon2{background:url(/adminv3/image/top2.png) no-repeat left center;}
.SStopIcon3{background:url(/adminv3/image/top3.png) no-repeat left center;}
.SStopIcon4{background:url(/adminv3/image/top4.png) no-repeat left center;}


/**
*翻页样式
*/
/*.page{padding:22px 30px 65px 0px;}*/
.page ul{float: right;}
.page:after{display:table; content:''; clear:both}
.page li{float:left; width:34px; text-align:center; height:34px; line-height:34px; display: block; border:1px solid #d6dee3; border-radius: 4px;
    margin-left:8px; overflow:hidden;cursor: pointer;
}
.page li.none{
    border:none;
    width:auto;
    min-width: 34px;
    overflow:auto;
}       
.page li input{
    float: left;
    width:32px;
    height:32px;
    line-height: 32px;
    text-align: center;
    border:none;
}       
.page li.on{border:1px solid #99d9f8; background:#99d9f8;}
.page li.on a{color:#FFF;}

/*
 * sellStatic 销售统计页面page
 */
.page_sellStatic{
    padding-right:15px;
}
.page_sellStatic span, .page_sellStatic input, .page_sellStatic a{
    float:left; min-width:34px; text-align:center; height:34px; line-height:34px; display: block; border:1px solid #d6dee3; border-radius: 4px;
    margin-left:8px; overflow:hidden; padding:0px 10px; color:#555;
}
.page_sellStatic .bn{
    border:none;
}
.page_sellStatic button{
    height:32px; line-height:32px; padding:0px; color:#555;
    font-family:"Microsoft Yahei",Verdana,Arial,Helvetica,sans-serif;
}


/**
 * 大部分使用于报表页面
 */
/**类似客户看板顶部信息鼠标经过绿框绿字样式**/
.SShover{background:#FFF; border:1px solid #FFF; }
.SShover:hover{color:var(--primary-color); border:1px solid #03ac54; }
.SShover:hover span,.SShover:hover p,.SShover:hover i,.SShover:hover em{color:var(--primary-color);}


/**
*动画样式
*/

/**底部购物车抖动动画**/
.animated {
    -webkit-animation-duration: 300ms;
    animation-duration: 300ms;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both
}
@-webkit-keyframes shake {
    0%,100% {
        -webkit-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0)
    }
    10%,30%,50%,70%,90% {
        -webkit-transform: translate3d(-5px,0,0);
        transform: translate3d(-5px,0,0)
    }
    20%,40%,60%,80% {
        -webkit-transform: translate3d(5px,0,0);
        transform: translate3d(5px,0,0)
    }
}
@keyframes shake {
    0%,100% {
        -webkit-transform: translate3d(0,0,0);
        -ms-transform: translate3d(0,0,0);
        transform: translate3d(0,0,0)
    }
    10%,30%,50%,70%,90% {
        -webkit-transform: translate3d(-5px,0,0);
        -ms-transform: translate3d(-5px,0,0);
        transform: translate3d(-5px,0,0)
    }
    20%,40%,60%,80% {
        -webkit-transform: translate3d(5px,0,0);
        -ms-transform: translate3d(5px,0,0);
        transform: translate3d(5px,0,0)
    }
}
.shake {
    -webkit-animation-name: shake;
    animation-name: shake
}

/**首页添加物品放大动画**/
@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
    50% {
        -webkit-transform: scale3d(1.4,1.4,1.4);
        transform: scale3d(1.4,1.4,1.4)
    }
    100% {
        -webkit-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
}
@keyframes pulse {
    0% {
        -webkit-transform: scale3d(1,1,1);
        -ms-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
    50% {
        -webkit-transform: scale3d(1.4,1.4,1.4);
        -ms-transform: scale3d(1.4,1.4,1.4);
        transform: scale3d(1.4,1.4,1.4)
    }
    100% {
        -webkit-transform: scale3d(1,1,1);
        -ms-transform: scale3d(1,1,1);
        transform: scale3d(1,1,1)
    }
}
.pulse {
    -webkit-animation-name: pulse;
    animation-name: pulse
}


/**首页左侧导航动画**/
/*.leftNav1,.leftNav2,.rightMain1,.rightMain2,.mainTop1,.mainTop2 {
    transition: all 0.5s;
    -moz-transition: all 0.5s;
    -webkit-transition: all 0.5s;
    -o-transition: all 0.5s;
}*/

/**公共左侧二级导航动画**/
/*.leftNav1 {width:80px !important;}
.leftNav2 {width:193px !important;}*/

/**公共右侧内容动画**/
/*.rightMain1{margin-left: 80px !important;}
.rightMain2{margin-left: 193px !important;}*/

/**公共右侧顶部菜单动画**/
/*.mainTop1{padding-left: 80px !important;}
.mainTop2{padding-left: 193px !important;}*/


/**加载旋转动画**/


@keyframes rotate {
    from {
        transform:rotate(360deg);
        -ms-transform:rotate(360deg);
        -moz-transform:rotate(360deg);
        -webkit-transform:rotate(360deg);
        -o-transform:rotate(360deg);
    }
    to {
        transform:rotate(0deg);
        -ms-transform:rotate(0deg);
        -moz-transform:rotate(0deg);
        -webkit-transform:rotate(0deg);
        -o-transform:rotate(0deg);
    }
}

@-moz-keyframes rotate {
    from {
        transform:rotate(360deg);
        -ms-transform:rotate(360deg);
        -moz-transform:rotate(360deg);
        -webkit-transform:rotate(360deg);
        -o-transform:rotate(360deg);
    }
    to {
        transform:rotate(0deg);
        -ms-transform:rotate(0deg);
        -moz-transform:rotate(0deg);
        -webkit-transform:rotate(0deg);
        -o-transform:rotate(0deg);
    }
}

@-webkit-keyframes rotate {
    from {
        transform:rotate(360deg);
        -ms-transform:rotate(360deg);
        -moz-transform:rotate(360deg);
        -webkit-transform:rotate(360deg);
        -o-transform:rotate(360deg);
    }
    to {
        transform:rotate(0deg);
        -ms-transform:rotate(0deg);
        -moz-transform:rotate(0deg);
        -webkit-transform:rotate(0deg);
        -o-transform:rotate(0deg);
    }
}

@-o-keyframes rotate {
    from {
        transform:rotate(360deg);
        -ms-transform:rotate(360deg);
        -moz-transform:rotate(360deg);
        -webkit-transform:rotate(360deg);
        -o-transform:rotate(360deg);
    }
    to {
        transform:rotate(0deg);
        -ms-transform:rotate(0deg);
        -moz-transform:rotate(0deg);
        -webkit-transform:rotate(0deg);
        -o-transform:rotate(0deg);
    }
}

.SSrotate {
    animation: rotate linear 1s infinite;
    -moz-animation: rotate linear 1s infinite; /* Firefox */
    -webkit-animation: rotate linear 1s infinite;  /* Safari 和 Chrome */
    -o-animation: rotate linear 1s infinite;   /* Opera */
}


/*vue使用*/
[v-cloak] {
	display: none;
}


/*add by ddcoder*/
/*垂直剧中*/
.a-center-middle {
	position: absolute;
	top: 50%;
	left: 50%;
	-ms-transform: translate(-50%,-50%); 	/* IE 9 */
	-moz-transform: translate(-50%,-50%); 	/* Firefox */
	-webkit-transform: translate(-50%,-50%); /* Safari 和 Chrome */
	-o-transform: translate(-50%,-50%);
	transform: translate(-50%,-50%);
}

/*flex*/
.F_C_between {
	align-content: space-between;
	-webkit-align-content: space-between;
}
.F_C_middle {
	align-content: center;
}
.F_align_end {
	align-items: flex-end;
}
.F_start {
	justify-content: flex-start;
}
.F_align_stretch {
	align-items: stretch;
}


.line-clamp-1 {
	display: -webkit-box;
	-webkit-line-clamp: 1;
	text-overflow: ellipsis;
	overflow: hidden;
	word-wrap: break-word;
	-webkit-box-orient: vertical;
}

.line-clamp-2 {
	display: -webkit-box;
	-webkit-line-clamp: 2;
	text-overflow: ellipsis;
	overflow: hidden;
	word-wrap: break-word;
	-webkit-box-orient: vertical;
}