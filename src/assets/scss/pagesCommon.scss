body {
  overflow-y: auto;
}
[v-cloak] {
  display: none;
}
$baseColor: var(--primary-color);
.clearfix:after {
  content: '';
  display: table;
  clear: both;
}
.absolute-middle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}
.pointer {
  cursor: pointer;
}

// body {
//   overflow-y: hidden;
// }

/* 基础的色块 */
$tableRowHeight: 50px;
$primary: #2d8cf0;
$lightPrimary: #5cadff;
$darkPrimary: #2b85e4;

$info: #2db7f5;
$success: #19be6b;
$warning: #ff9900;
$error: #ed4014;

.c-green {
  background-color: var(--primary-color);
}
.c-red-color {
  color: #f33333;
}
.c-gray-color {
  color: #aaa;
}
.c-red {
  background-color: #f33333;
}

.text-gray {
  color: #aaa;
}

.text-green {
  color: $baseColor;
}
.text-red {
  color: #f33333;
}
.text-gray {
  color: #aaa;
}

$basicBorderColor: 1px solid #d7dde4;

@mixin basicBorder() {
  border: 1px solid #0e1722;
}

/* 字体 */
$fontSize: 14px;

#app {
  width: 100%;
  margin: auto;
  background-color: #f5f5f5;
}

.basePadding {
  padding: 5px 15px 0;
}

/* 绿色的按钮 */

.c-green-btn {
  background-color: var(--primary-color) !important;
  color: #fff !important;
}

.c-green-btn:hover {
  color: #fff;
  background-color: var(--primary-color) !important;
  border-color: #03ac54 !important;
}

/* basic的按钮 */

.c-basic-btn {
  color: #495060 !important;
  background-color: transparent !important;
  border-color: #dddee1 !important;
}

.c-basic-btn:hover {
  border: 1px solid #03ac54 !important;
  color: var(--primary-color) !important;
}

/* 蓝色的按钮 */
.c-blue-btn {
  color: #fff;
  background-color: #2db7f5 !important;
  border-color: #2db7f5 !important;
}

.c-blue-btn:hover {
  color: #fff;
  background-color: #2db7f5 !important;
  border-color: #2db7f5 !important;
}

/* 红色的按钮 */

.c-red-btn {
  color: #fff;
  background-color: #ff6600 !important;
  border-color: #ff6600 !important;
}
.ivu-icon-plus,
.ivu-icon-minus {
  &:hover {
    cursor: pointer;
  }
}
/**************** iview按钮样式修改 start ****************/
#app {
  // .ivu-btn-primary {
  //   min-width: 64px;
  //   color: #657180;
  //   background: #fff;
  //   border: 1px solid #9EA7B4;
  // }
  // .ivu-btn-primary:hover {
  //   min-width: 64px;
  //   color: $success;
  //   border: 1px solid $success;
  // }
  // .ivu-btn-ghost {
  //   min-width: 64px;
  // }
  .ml15 {
    margin-left: 15px;
  }
}

/**************** iview按钮样式修改 end ****************/

/* iview样式修改 */
.ivu-table-row-hover {
  .table-button {
    background-color: transparent !important;
  }
}
.ivu-table-row-danger {
  color: red;
}

.ivu-cascader {
  cursor: pointer;
  &-label {
    border: 1px solid #d8d8d8;
    transition: border 0.2s ease-in-out;
    &:hover {
      border: solid 1px var(--primary-color-second);
    }
  }
  // 【【采购-采购单】新增采购单，搜索供应商进行选择时，双击操作会选错供应商】https://www.tapd.cn/21392371/bugtrace/bugs/view/1121392371001040337
  // 搜索后选择一项，会显示选项列表，此时还能继续点击选择，因此点选后将动画禁用，直接隐藏下拉选择框
  .transition-drop-leave-active {
    animation: none;
    opacity: 0;
  }
}
.demo-upload-list {
  line-height: 80px;
}

.table-button {
  color: var(--primary-color) !important;
  background-color: #fff !important;
  border: none !important;
  &:hover {
    background-color: transparent !important;
    color: var(--primary-color);
  }
}

.ivu-breadcrumb-item-separator {
  color: #8a8c8a !important;
}

// /* 表格列表样式 */
// .ivu-page {
//   padding: 5px 10px !important;
//   text-align: right !important;
// }
// /* 表格列表样式 */
// .ivu-page {
//   padding: 5px 0 !important;
//   text-align: right !important;
// }
// ivew search input

#app .ivu-input,
.ivu-modal .ivu-input {
  text-align: left;
}
.ivu-input-wrapper.ivu-input-group  {
  display: inline-block;
}
.ivu-input-wrapper .ivu-input {
  display: block;
}
.ivu-input-wrapper .ivu-input-group-append {
  position: absolute;
  top: 0;
  background-color: $baseColor;
  padding: 0;
  right: -1px;
  width: 40px;
  height: 30px;
  border: none;
  z-index: 9;
}
.ivu-input-wrapper .ivu-input-group-append .ivu-btn {
  font-size: 20px;
  color: #ffffff;
  //font-weight: bold;
  //margin: 0;
  //padding-top: 0;
  //background-color: transparent !important;
  //padding-right: 12px;
}
.ivu-input-wrapper .ivu-input-group-append .ivu-btn .ivu-icon {
  vertical-align: top;
}
.ivu-input-wrapper .ivu-input-group-append .ivu-btn:focus {
  -webkit-box-shadow: none;
}
/* ivue modal 样式重新定义 */
.sdp-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }

  .ivu-modal {
    position: relative;
    height: 80%;
    overflow: hidden;
  }

  .ivu-modal-close {
    z-index: 256;
  }

  .ivu-modal-content {
    position: relative;
    height: 100%;
  }

  .ivu-modal-footer,
  .ivu-modal-header {
    position: absolute;
    z-index: 255;
    width: 100%;
    background: #fff;
    height: 50px;
    padding: 0 16px;
  }

  .ivu-modal-header {
    top: 0;
    left: 0;
    line-height: 55px;
  }

  .ivu-modal-footer {
    bottom: 0;
    line-height: 50px;
    left: 0;
  }

  .ivu-modal-body {
    height: 100%;
    overflow: auto;
    padding: 50px 0;
  }
}

.table-total-num {
  font-size: 14px;
  font-weight: 500;
  font-family: Avenir, Avenir;
  color: #03AC54;
}
.table-total-amount {
  color: #FF6E00;
  font-size: 14px;
  font-weight: 500;
  font-family: Avenir, Avenir;
}

// iview modal组件样式统一调整
.ivu-modal {
  & &-header {
    &-inner {
      &:before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 11px;
        background: rgba(0, 0, 0, 0.7);
        margin-left: 10px;
        margin-right: 6px;
        margin-bottom: -1px;
      }
    }
  }
  & &-body {
    padding: 20px 24px 30px;
  }
  & &-footer {
    .ivu-btn-text {
      border: 1px solid #d8d8d8;
      &:hover {
        border-color: var(--primary-color);
      }
    }
  }
}

/* .ivu-page-item-jump-prev,
.ivu-page-item-jump-next */

.clear {
  clear: both;
}
.dn {
  display: none !important;
}
.height100 {
  height: 100%;
  background: #ffffff;
}
/* 标题栏 */
.admin-title {
  width: 100%;
  padding: 10px;
  font-size: 16px;
  text-align: left;
  border-bottom: 1px solid #eee;
  .admin-title__strong {
    margin-right: 5px;
    color: var(--primary-color);
  }
}

.floatRightBtns {
  right: 5px;
  //margin-top: -53px;
  position: absolute;
  text-align: right;
}
.sumRow {
  border: 1px solid #f0f2f0;
  border-top: none;
  padding: 15px;
}
.floatRightBtns button {
  margin-left: 10px;
}
.topRow {
  padding: 5px 0;
}
.show-edit-btn {
  display: none;
  margin-left: -10px;
}
.btnsPanel {
  color: #ffffff;
  //width: 100px;
  height: $tableRowHeight;
  position: relative;
  z-index: 99;
  //right: 0px;
  //margin-top: -31px;
  line-height: $tableRowHeight;
  span {
    display: inline-block;
    padding: 1px 20px;
    cursor: pointer;
    background: $baseColor;
  }
}
.btnsPanel_long {
  color: #ffffff;
  //width: 100px;
  height: $tableRowHeight;
  position: relative;
  z-index: 99;
  //right: 0px;
  //margin-top: -31px;
  line-height: $tableRowHeight;
  span {
    display: inline-block;
    padding: 1px 26px;
    cursor: pointer;
    background: $baseColor;
  }
}
.btnsPanel_muti {
  width: 0;
  color: #ffffff;
  height: $tableRowHeight;
  position: relative;
  z-index: 99;
  line-height: $tableRowHeight;
  span {
    display: inline-block;
    padding: 1px 32px;
    cursor: pointer;
    background: $baseColor;
  }
}
.table-row-btn-wrap {
  display: none !important;
}
.table-close-row {
  color: #999999;
}
.table-close-row a {
  color: #999999 !important;
}
//去除inputNumber 组件的上下箭头
.ivu-input-number-handler-wrap {
  display: none;
}
.ivu-table-body {
  overflow: auto !important;
  overflow-x: hidden !important;
}
// 隐藏下拉框的横向滚动条
.ivu-select-dropdown {
  overflow-x: hidden !important;
}
// 滚动条样式
// ::-webkit-scrollbar {
//   width: 10px;
//   height: 10px;
//   display: block;
//   position: absolute;
// }
// //滚动条里面小方块
// ::-webkit-scrollbar-thumb {
//   box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.3);
//   border: 2px solid transparent;
//   border-radius: 10px;
//   position: absolute
// }
// //滚动条里面轨道
// ::-webkit-scrollbar-track {
//   // -webkit-box-shadow: inset 0 0 5px rgba(78, 78, 78, 0.2);
//   // border-radius: 10px;
//   // background: #dddddd;
// }

// checkbox border
// .ivu-checkbox-inner {
//   border: 1px solid #d0d0d0 !important;
// }
// 下拉框颜border色调整
// .ivu-select-selection {
//   border: 1px solid #d4dcd4 !important;
// }
.ivu-input[disabled] {
  color: #333333;
}
.ivu-select-disabled .ivu-select-selection {
  color: #333333;
}
.ivu-radio-group-button .ivu-radio-wrapper-disabled:first-child,
.ivu-radio-group-button .ivu-radio-wrapper-disabled:hover {
  color: #333333;
}
.ivu-radio-group-button .ivu-radio-wrapper-disabled.ivu-radio-wrapper-checked {
  color: $baseColor;
}
.ivu-select-disabled .ivu-select-selection {
  color: #333333;
}
.ivu-radio-disabled {
  .ivu-radio-inner {
    &:after {
      background-color: #989898 !important;
    }
  }
}
.totalNum {
  font-size: 16px;
  color: #e20000;
}
.orderRemark {
  padding: 20px 0 80px 0;
  line-height: 70px;
}
._actionBtns {
  text-align: right;
  position: absolute;
  right: -6px;
  top: -52px;
}
._total {
  font-size: 15px;
  color: $baseColor;
  font-weight: bold;
}
.remark {
  text-align: left;
  margin: 30px 0;
}
.showMapMarks {
  color: $baseColor;
}
.paddingB-30 {
  padding-bottom: 30px;
}
.status-info {
  color: #FF9F00;
}
.status-success {
  color: #333333;
}
.status-success2 {
  color:$success
}
.status-warning {
  color: #ff9900;
}
.status-error {
  color: #ed3f14;
}
.status-close {
  color: #999999;
}
.dropdown-fade-enter-active {
  transition: all 0.3s ease;
}

.dropdown-fade-leave-active {
  transition: all 0.2s ease;
}

.dropdown-fade-enter,
.dropdown-fade-leave-to
  /* .slide-fade-leave-active for below version 2.1.8 */

 {
  transform: translateY(-20px);
  opacity: 0;
}
.tableLink {
  color: $baseColor;
  cursor: pointer;
}
.ivu-cascader-menu-item {
  text-align: left;
}
.ivu-select-item {
  text-align: left;
}
// 修改鼠标选中的颜色
::selection {
  background: $baseColor;
  color: #fff;
}

::-moz-selection {
  background: $baseColor;
  color: #fff;
}

::-webkit-selection {
  background: $baseColor;
  color: #fff;
}
.ivu-collapse-header {
  padding-left: 15px !important;
}
// 修改input 的默认样式
.ivu-input-group .ivu-input {
  border-radius: 4px;
}
.ivu-input-group-with-append .ivu-input {
  border-bottom-right-radius: 7px !important;
  border-top-right-radius: 7px !important;
}
// iview select padding 覆盖

// .ivu-select .ivu-select-selection .ivu-icon {
//     right: 12px;
// }

.ivu-select .ivu-select-placeholder,
.ivu-select .ivu-select-selected-value {
  padding-left: 12px;
}

// iview时间选择器

//.ivu-date-picker .ivu-input {
//    width: 204px;
//    padding-right: 47px !important;
//}
input {
  border-radius: 4px;
  width: 100%;
  padding-left: 10px;
  &:focus {
    border-color: $baseColor;
  }
  &:hover {
    border-color: $baseColor;
  }
}
.ivu-table-danger {
  .ivu-table-cell {
    color: red;
  }
}
.ivu-table-cell {
  white-space: nowrap !important;
  word-break: unset !important;
}
// .buttomPage {
//   padding-bottom: 10px;
// }
// iview 表格字体设置
.ivu-table {
  font-size: 13px !important;
}
.selectRow {
  margin: 8px 0 !important;
}
.modal-mask {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  position: fixed;
  z-index: 99;
}
.progressMask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  margin: auto;
  background: #ffffff;
  opacity: 0.7;
  z-index: 100;
}
.waitContent {
  position: absolute;
  text-align: center;
  z-index: 101;
  top: 45%;
  left: 0;
  right: 0;
  color: $baseColor;
  font-size: 15px;
}
.rightModal {
  background: #fff;
  right: 0;
  position: fixed;
  z-index: 999;
  top: 0;
  box-shadow: 9px 0 37px 20px #f2f0fa;
  padding: 30px;
  width: 450px;
  height: 100%;
  overflow: auto;
}
.selectColsPanel {
  background: #fff;
  right: 0;
  position: fixed;
  z-index: 1030 !important;
  top: 0;
  box-shadow: 9px 0 37px 20px #f2f0fa;
  width: 230px;
  height: 100%;
  overflow: auto;
  .panelHeader {
    font-size: 15px;
    line-height: 35px;
    padding: 5px 10px;
    border-bottom: 1px solid #f3f3f3;
    height: 44px;
    .title {
      float: left;
    }
    .close {
      float: right;
      font-size: 23px;
      cursor: pointer;
    }
  }
  .panelBody {
    padding: 15px 0 15px 20px;
    label {
      display: block !important;
      text-align: left !important;
      font-size: 14px !important;
      padding: 20px 0px;
      .ivu-checkbox {
        margin-right: 10px;
        .ivu-checkbox-inner {
          //width: 16px !important;
          //height: 16px !important;
        }
      }
    }
  }
  .panelFooter {
    position: fixed;
    bottom: 10px;
    padding: 5px;
    button {
      margin: 5px;
    }
  }
}
.noBg {
  background: none;
}
.rightFade-enter-active,
.rightFade-leave-active {
  transition: all 0.3s linear;
}
.rightFade-enter,
.rightFade-leave-active {
  transform: translate3d(100%, 0, 0);
}
.notice-badge {
  background: $baseColor !important;
  margin-left: 10px;
}
.rightModalBtns {
  text-align: right;
  margin-top: -15px;
  margin-bottom: 20px;
}
.messages {
  font-size: 13px;
  border-bottom: 1px solid #f3f3f3;
  &:hover {
    color: $baseColor;
    //font-weight: bold;
  }
  .messagesContent {
    cursor: pointer;
    text-align: left;
    line-height: 23px;
    padding-top: 10px;
  }
  a {
    color: $baseColor;
  }
  .messageTime {
    text-align: right;
    color: #999999;
    line-height: 23px;
    margin-top: 0;
    padding-bottom: 10px;
  }
}
.viewMore {
  color: #999999;
  text-align: center;
  padding: 15px 0;
  cursor: pointer;
  font-size: 13px;
  &:hover {
    color: $baseColor;
  }
}
.noMore {
  color: #999999;
  text-align: center;
  padding: 15px 0;
}
.advancedSearch {
  background: #f3f5f2;
  cursor: pointer;
  margin-left: 15px;
  float: left;
  font-size: 13px;
  padding: 6px 20px;
  color: #2c3e50;
}
.advancedPanel {
  background: #f3f5f2;
  width: 100%;
  margin-top: -3px;
  padding: 25px 10px 15px 15px;
  margin-bottom: 5px;
  .advanceRow {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  p {
    cursor: pointer;
  }
}
@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
@keyframes fadeOut {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}
.fade-enter-active {
  animation-name: fadeIn;
}
.fade-live-active {
  animation-name: fadeIn;
}
.fade-enter-active,
.fade-live-active {
  animation-duration: 1s;
  animation-fill-mode: both;
}
.panelFade-enter-active,
.panelFade-leave-active {
  transition: all 0.3s linear;
}
.panelFade-enter,
.panelFade-leave-active {
  transform: translate3d(100%, 0, 0);
}
.upload:not(.img-upload-list),
.description:not(.img-upload-list) {
  width: 100%;
  padding: 10px;
  text-align: left;
  .description__textarea {
    display: inline-block;
    margin-left: 10px;
    vertical-align: top;
    width: 200px;
    height: 80px;
  }
  .demo-upload-list {
    display: inline-block;
    width: 80px;
    height: 80px;
    text-align: center;
    line-height: 80px;
    border: 1px solid transparent;
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    position: relative;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
    margin-right: 4px;
  }

  .demo-upload-list img {
    width: 100%;
    height: 100%;
  }

  .demo-upload-list-cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
  }

  .demo-upload-list:hover .demo-upload-list-cover {
    display: block;
  }

  .demo-upload-list-cover i {
    color: #fff;
    font-size: 20px;
    cursor: pointer;
    margin: 0 2px;
    font-weight: bold;
  }
}
.normalRow {
  cursor: pointer;
}
.customRow {
  position: relative;
  margin-left: 0;
  margin-right: 0;
  height: 111px;
  display: block;
  .col_5 {
    position: relative;
    float: left;
    flex: 0 0 auto;
    width: 19.5%;
    display: block;
    margin-right: 0.5%;
    &:last-child {
      margin-right: 0;
      width: 20%;
    }
  }
  .col_7 {
    position: relative;
    float: left;
    flex: 0 0 auto;
    width: 13.8%;
    display: block;
    margin-right: 0.48%;
    &:last-child {
      margin-right: 0;
      width: 14.28%;
    }
  }
}
.flexRow {
  display: flex;
  background: #ffffff;
  margin-bottom: 8px !important;
  padding: 10px;
}
.flexRow .row_item {
  padding-right: 7px;
  width: 250px;
  text-align: left;
}
.tradeData {
  background: #ffffff;
  padding: 20px 0;
  b {
    font-size: 30px;
    display: block;
  }
  span {
    color: #999999;
  }
  &:hover {
    span {
      color: $baseColor;
    }
    b {
      color: $baseColor;
    }
    box-shadow: 0 2px 7px rgba(0, 0, 0, 0.15);
    border-color: transparent;
  }
}
.formTitle {
  font-size: 15px;
  text-align: left;
  margin-bottom: 15px;
  .subTitle {
    color: #999999;
    font-size: 13px;
  }
  ._info {
    margin-left: 100px;
    color: $baseColor;
    font-size: 13px;
  }
}
._modalBody {
  padding: 20px;
  overflow-y: scroll;
  max-height: 92vh;
}
.noScroll {
  padding: 0;
  overflow: hidden;
}
.modal_header {
  font-size: 15px;
  background: rgb(242, 242, 242);
  padding: 18px;
  margin-bottom: 15px;
}
.redColor {
  color: red;
}
.grayColor {
  color: #999999;
}
.greenColor {
  color: $baseColor;
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    position: relative;
    top: 0;
  }
}
/**表格展开图标大小调整**/
.log-table {
  .iconA {
    .ivu-icon {
      font-weight: 500;
      color: #000;
      font-size: 24px !important;
    }
    .ivu-icon-ios-arrow-down {
      font-weight: bolder;
      font-size: 22px !important;
    }
  }
}

/**
 * 协议价tag
 */
.tag-protocol-price {
  font-weight: bolder;
  color: #47a7db;
  border: 1px solid;
  border-radius: 4px;
  padding: 0 5px;
  display: inline-block;
  font-size: 12px;
  margin-left: 5px;
  text-align: center;
}

/**
 * 动画
 */
.animated {
  -webkit-animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-duration: 1s;
  animation-fill-mode: both;
}
@keyframes shake {
  0%,
  to {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  10%,
  30%,
  50%,
  70%,
  90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }

  20%,
  40%,
  60%,
  80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}

.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

body {
  .ivu-select-dropdown {
    border-radius: 4px;
  }
  .ivu-tooltip-content {
    .ivu-tooltip-inner {
      background: rgba(0, 0, 0, 0.7);
    }
  }
  .ivu-tooltip-light {
    .ivu-tooltip-content {
      .ivu-tooltip-inner {
        background-color: #fff;
      }
    }

  }
  .ivu-badge-count {
    height: 14px;
    line-height: 12px;
    padding: 0 2px;
    top: -4px;
  }
}

.list-table,
.layout-nav,
.s-filter,
.s-table,
.s-common {
  .ivu-select-single .ivu-select-selection .ivu-select-placeholder,
  .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    padding-left: 10px;
  }
  .ivu-select-input[disabled] {
    -webkit-text-fill-color: initial;
  }
  .ivu-input[disabled],
  .ivu-select-input[disabled],
  .ivu-select-disabled .ivu-select-selection {
    color: rgba(0, 0, 0, 0.4);
    background: #f8f9fa;
  }
  .ivu-radio-wrapper-disabled {
    color: rgba(0, 0, 0, 0.4);
  }
  .ivu-input,
  .ivu-select-input {
    padding-left: 10px;
  }
  .ivu-select .ivu-select-input {
    height: 28px;
    line-height: 1.5;
  }
  .ivu {
    &-input-word-count {
      right: 12px;
      color: rgba(0, 0, 0, 0.3);
    }
    &-select-dropdown {
      border-radius: 4px;
    }
    &-input,
    &-select,
    &-select-input {
      color: rgba(0, 0, 0, 0.7);
    }
    &-checkbox-wrapper + span,
    &-checkbox + span {
      margin-right: 16px;
      margin-left: 1px;
      color: rgba(0, 0, 0, 0.85);
    }
    &-select-arrow {
      color: #b2b2b2;
    }
    &-select-dropdown {
      .ivu-cascader-menu {
        .ivu-cascader-menu-item {
          transition: 0.3s all;
        }
        .ivu-cascader-menu-item:hover {
          color: var(--primary-color);
          // background: rgba(3, 172, 84, 0.05);
          background: var(--primary-tr-hover);

        }
        .ivu-cascader-menu-item-active {
          font-weight: 500;
          color: #303030;
          background: #f5f6f8;
        }
      }
    }
    &-dropdown {
      cursor: pointer;
    }
    &-dropdown-menu {
      .ivu-dropdown-item {
        transition: 0.3s all;
      }
      .ivu-dropdown-item:hover {
        color: var(--primary-color);
        // background: rgba(3, 172, 84, 0.05);
        background: var(--primary-tr-hover);
      }
    }
    &-select-dropdown-list {
      .ivu-select-item {
        transition: 0.3s all;
        color: rgba(0, 0, 0, 0.85);
      }
      .ivu-select-item:hover:not(.ivu-select-item-disabled) {
        color: var(--primary-color);
        // background: rgba(3, 172, 84, 0.05);
        background: var(--primary-tr-hover);
      }
      .ivu-select-item-selected {
        font-weight: 500;
        color: var(--primary-color);
        background: var(--primary-tr-hover);
      }
    }
    &-select-multiple {
      .ivu-select-item-focus {
        background: #fff;
      }
    }
    &-checkbox {
      .ivu-checkbox-inner {
        width: 14px;
        height: 14px;
        &::after {
          top: 1px;
          left: 4px;
        }
      }
    }

    &-radio-wrapper {
      margin-right: 24px;
      font-size: 13px;
    }
  }

  .ivu-table {
    .ivu-table-row.ivu-table-row-hover {
      td {
        background: var(--primary-tr-hover);
      }
    }
    tr th {
      background: #f6f8f9;
    }
  }
}

.s-filter {
  .ivu-date-picker {
    display: block;
    .ivu-input-suffix {
      cursor: pointer;
    }
  }
  .ivu-radio-group-button .ivu-radio-wrapper {
    height: 30px;
    line-height: 28px;
  }
}

.s-common {
  .primary-color {
    color: var(--primary-color);
  }
  .warning-color {
    color: #ff9f00;
  }
  .error-color {
    color: #f13130;
  }
  .light-color {
    color: rgba(0, 0, 0, 0.5);
  }
  .text-color {
    color: rgba(0, 0, 0, 0.7);
  }
  .error .ivu-input {
    border-color: #f67f7f;
  }
  .remote-more {
    position: absolute;
    top: 0;
    right: 0;
  }
  .statistics {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-top: 20px;
    &__item {
      text-align: center;
      height: 114px;
      padding: 32px 20px 0 20px;
      font-size: 12px;
      line-height: 16px;
      &.selected {
        em {
          color: var(--primary-color);
        }
      }
      em {
        font-size: 32px;
        font-family: AvenirNextCondensed-DemiBold, AvenirNextCondensed,SimHei,Microsoft YaHei;
        font-weight: 600;
        color: rgba(0, 0, 0, 0.85);
        line-height: 34px;
      }
    }
    &__value {
      margin-bottom: 8px;
      height: 34px;
    }
    &__label-text {
      color: rgba(0, 0, 0, 0.7);
      height: 16px;
      img {
        height: 16px;
        width: 16px;
        vertical-align: bottom;
      }
    }
    &__label-explain {
      margin-top: 2px;
      color: rgba(0, 0, 0, 0.5);
    }
  }
  .common-wrap {
    padding: 24px 20px 20px 20px;
  }
  .common-operation {
    &.flex-between {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .s-button {
      margin-right: 10px;
    }
  }
  .common-summary-list {
    .item {
      display: inline-block;
      margin-right: 20px;
      span {
        font-weight: 500;
      }
    }
  }
  .detail__wrap {
    display: flex;
    flex-direction: column;
    .blk {
      margin-top: 24px;
      padding-top: 0;
    }
    .detail__hd {
      height: 40px;
      flex: 0 0 40px;
      padding: 0 20px;
    }
    .detail__bd {
      flex: 1;
      overflow-y: auto;
      padding: 0 20px 20px 20px;

      .detail__form__inline {
        .ivu-form-item {
          width: 314px;
          margin: 10px 10px 10px -2px;
          &.span-2 {
            width: 640px;
          }
          .ivu-form-item-label {
            padding-top: 8px;
            padding-bottom: 9px;
          }
        }
        .text-items {
          .ivu-form-item {
            margin: 2px 0 2px -2px;
          }
        }
      }
      .detail__form {
        width: 654px;
        padding-right: 162px;
        margin: 0 auto;
        .ivu-form-item {
          margin-bottom: 16px;
        }
        .ivu-form-item-label,
        .ivu-radio-wrapper {
          height: 30px;
          line-height: 30px;
          color: rgba(0, 0, 0, 0.85);
        }
        .ivu-form-item-label {
          padding: 0 12px 0 0;
        }
        .ivu-form-item-content {
          line-height: normal;
          padding-right: 0;
        }
        .ivu-form-item-error-tip {
          font-size: 12px;
        }
      }
    }
    .detail__ft {
      background: #fff;
      text-align: center;
      height: 46px;
      line-height: 46px;
      flex: 0 0 46px;
      box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
      .s-button {
        margin: 0 6px;
        vertical-align: middle;
      }
    }
  }
  .ivu-tabs {
    overflow: hidden;
  }
  .ivu-tabs-tabpane {
    padding: 0;
  }
  .ivu-tabs-bar {
    padding: 0 20px;
  }
  .empty {
    height: 100px;
    line-height: 100px;
    text-align: center;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.7);
  }
  .charts {
    &__item,
    .nodata {
      position: relative;
      height: 396px;
      background: #fff;
    }
    .chart {
      height: 394px;
    }
  }
  .blk {
    &.divider {
      background: #f2f3f5;
      padding-top: 14px;
      .blk-hd,
      .blk-bd {
        background: #fff;
        padding-left: 20px;
        padding-right: 20px;
      }
      .blk-hd {
        padding-top: 16px;
      }
      .blk-bd {
        padding-bottom: 20px;
      }
    }
    padding-top: 20px;
    &-hd {
      padding-bottom: 12px;
      line-height: 15px;
      &.flex-between {
        display: flex;
        justify-content: space-between;
      }
      h5 {
        height: 15px;
        font-size: 13px;
        color: rgba(0, 0, 0, 0.85);
        display: inline-block;
        &::before {
          content: '';
          display: inline-block;
          width: 3px;
          height: 11px;
          background: rgba(0, 0, 0, 0.7);
          margin-right: 6px;
          margin-bottom: -1px;
        }
        span {
          color: rgba(0, 0, 0, 0.7);
        }
      }
    }
  }
  .circle-wrap {
    border: 1px solid #e8e8e8;
    padding: 50px 60px 40px 60px;
  }
  .ivu-page-options-sizer {
    margin-right: 0;
  }
}
.common {
  padding: 24px 20px 0 20px;
  background-color: #fff;
  .detail-name {
    color: var(--primary-color);
    cursor: pointer;
  }
  .sdp-table__tr:hover {
    .detail-name {
      color: var(--primary-color);
    }
  }
}

// 页面顶部有tab时
.base-wrapper {
  padding: 0 20px;

  .ivu-tabs {
    margin-bottom: 24px;
  }
}
// 页面顶部无tab时
.base-wrapper2 {
  padding: 24px 20px 0;
}

.sdp-table {
  .ivu-checkbox {
    &:hover .ivu-checkbox-inner {
      border-color: $baseColor;
    }
    &-disabled {
      &:hover .ivu-checkbox-inner {
        border-color: #d8d8d8;
      }
    }
  }
}

// 必填时的*号标识统一样式
.label-required::before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  line-height: 1;
  font-family: SimSun;
  font-size: 13px;
  color: #f55654;
  vertical-align: middle;
}

// 未覆盖 iview 样式 暂时用 !important 提升权值
// 原因：main.js 中导入顺序先于@assets/styles/all.less，被覆盖了
.ivu-switch.ivu-switch-default {
  background-color: rgba(209, 209, 209, 1) !important;
}
.ivu-switch.ivu-switch-checked.ivu-switch-default {
  background-color: #03AC54 !important;
}
.ivu-switch.ivu-switch-default.ivu-switch-disabled {
  background-color: rgba(209, 209, 209, 0.6) !important;
}
.ivu-switch.ivu-switch-disabled.ivu-switch-checked {
  background-color: var(--primary-color) !important;
}

/* 覆盖 vxe-table 中复选框的颜色*/
.vxe-table--render-default .vxe-cell--checkbox:not(.is--disabled):hover .vxe-checkbox--icon:before {
  border-color: #03ac54;
}

.vxe-table--render-default .is--checked.vxe-cell--checkbox .vxe-checkbox--icon:before, .vxe-table--render-default .is--indeterminate.vxe-cell--checkbox .vxe-checkbox--icon:before {
  background-color: #03ac54;
  border-color: #03ac54;
}

.sdp-table__cell {
  .icon-sort {
    color: #909090;
  }
}

.max-text-2 {
  // 超出两行显示省略号
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
