@charset "utf-8";

/*
  Sass @mixin style
  Released under the MIT license

  author: LXJ
  date: 2017.5.27
*/

//快速的开发
//--------------------------------
@mixin wh($width, $height) {
  width: $width;
  height: $height;
}

//flex box
//--------------------------------
@mixin flex($direction: row, $wrap: wrap, $justify: center, $align: center) {
  display: flex;
  display: -webkit-flex;
  flex-direction: $direction;
  flex-wrap: $wrap;
  justify-content: $justify;
  align-items: $align;
}

//文本控制
//--------------------------------
//文本绝对居中
@mixin textAbsoluteCenter($height) {
  line-height: $height;
  text-align: center;
}

//使用省略号
@mixin ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

//表单字体对齐
@mixin formAlignment {
  text-align:justify;
  text-justify:distribute-all-lines;  /*ie6-8*/
  text-align-last:justify;  /* ie9*/
  -moz-text-align-last:justify;  /*ff*/
  -webkit-text-align-last:justify;  /*chrome 20+*/
}

//浏览器默认样式的修改
//--------------------------------
//清除默认的input/textarea/button样式
@mixin clearStyle {
  -moz-appearance: none;
  -webkit-appearance: none;
  appearance: none;
}

//css3 transform 布局的用法
//--------------------------------
//绝对居中
@mixin absoluteCenter {
  position: absolute;
  top: 50%;
  left: 50%;
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

//垂直居中
@mixin vetically {
  position: absolute;
  top: 50%;
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}

//水平居中
@mixin horizontally {
  position: absolute;
  left: 50%;
  -moz-transform: translateX(-50%);
  -ms-transform: translateX(-50%);
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
}

//响应式布局，其中默认大小参考bootstrap栅格布局
//--------------------------------
//小屏幕手机
@mixin xs-screen($res: 768px) {
  @media only screen and (max-width: $res) {
    @content;
  }
}

//小屏幕平板
@mixin sm-screen($res: 768px) {
  @media only screen and (min-width: $res) {
    @content;
  }
}

//中等屏幕显示器
@mixin md-screen($res: 970px) {
  @media only screen and (min-width: $res) {
    @content;
  }
}

//大屏幕显示器
@mixin lg-screen($res: 1170px) {
  @media only screen and (min-width: $res) {
    @content;
  }
}

