/*
* 2016-05-25 v1.0
* 2016-12-05 v1.1
* 2017-01-03 v1.2  新增letter-spacing(1~20)
* 2017-02-08 v1.3  m0与mt1等位置互换后mt1能覆盖m0属性
*/
@charset "utf-8";

/* ---------------------single CSS----------------------- */

html,
body {
  height: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); /*position:relative;*/
} /*App中需要设置*/
/* body{font:14px/1.0 "Microsoft Yahei",Verdana,Arial,Helvetica,sans-serif;} */
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  word-wrap: break-word;
}
body,
dl,
dt,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
p,
form {
  margin: 0;
}
button {
  border: none;
  background: transparent;
  padding: 6px 15px;
}
ul,
ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
i,
em {
  font-style: normal;
}
a {
  text-decoration: none;
}
a:hover {
  text-decoration: none;
  cursor: pointer;
}

input,
select,
textarea {
  vertical-align: middle;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0px 1000px white inset;
} /*input聚焦后黄色背景清除*/
input[type='button'],
input[type='submit'],
input[type='reset'],
textarea {
  -webkit-appearance: none;
  outline: none;
}
textarea {
  resize: none;
} /*去掉苹果默认样式*/
button,
input,
optgroup,
select,
textarea {
  margin: 0px;
  outline: none;
}

/*清除默认样式*/
/*select {
  border: solid 1px #000;
  appearance:none; -moz-appearance:none; -webkit-appearance:none;*/ /*很关键：将默认的select选择框样式清除*/
/*  background:url("http://ourjs.github.io/static/2015/arrow.png") no-repeat scroll right center transparent;padding-right: 23px !important;
}*/
/*清除ie的默认选择框样式清除，隐藏下拉箭头*/
select::-ms-expand {
  display: none;
}

/*表单样式定义*/
input[readonly] {
  background: #fff;
}
/* input[disabled]{background:rgba(245,246,248,0.8);color:rgba(0,0,0,0.3);} */
/* select[disabled]{background:rgba(245,246,248,0.8);color:rgba(0,0,0,0.3);} */
button,
.button {
  height: 32px;
  display: inline-block;
  padding: 3px 20px;
  margin-bottom: 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  color: #555;
  background-color: #fff;
  border: 1px solid transparent;
  border-radius: 2px;
}
input,
.input {
  display: inline-block;
  height: 32px;
  padding: 3px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d8d8d8;
  border-radius: 2px;
}
div.button,
a.button,
i.button,
span.button,
p.button {
  padding: 0px 20px;
  line-height: 30px;
}
select,
.select {
  display: inline-block;
  height: 32px;
  padding: 3px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d6ded9;
  border-radius: 2px;
}
textarea,
.textarea {
  display: inline-block;
  height: auto;
  padding: 3px 12px;
  font-size: 14px;
  line-height: 1.42857143;
  color: #555;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d6ded9;
  border-radius: 2px;
}
label,
.label {
  display: inline-block;
  height: 32px;
  line-height: 32px;
}
/*表格样式*/
.table {
  border-collapse: collapse;
  text-align: center;
  width: 100%;
}
.table thead {
  background: #f7f7f7;
  font-size: 14px;
  font-weight: bold;
  height: 50px;
  line-height: 50px;
}
.table thead th,
.table thead td {
  padding: 0px 10px;
  font-size: 13px;
  white-space: nowrap;
}
.table tbody tr {
  height: 50px;
  vertical-align: middle;
  border-bottom: 1px solid #f2f2f2;
  line-height: 1.7;
}
.table tbody tr:hover {
  background: #ecf5f1;
}
.table tbody tr td {
  font-size: 13px;
  padding: 10px 10px;
}
.table tbody tr td:last-child a {
  cursor: pointer;
}

/* table */
.T {
  display: table;
  width: 100%;
  float: none;
}
.t {
  display: table-cell;
  vertical-align: middle;
}
.tt {
  display: table-cell;
  vertical-align: top;
}

/* box--flexbox--flex */
.flex,
.F,
.F_ {
  display: -webkit-box;
  display: -moz-box;
  display: -webkit-flex;
  display: -moz-flex;
  display: -ms-flexbox;
  display: flex;
}
.F {
  height: 100%;
  -moz-box-orient: vertical;
  -webkit-box-orient: vertical;
  box-orient: vertical;
  flex-direction: column;
}
.F_ {
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}
.f {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
}

/* display */
.db {
  display: block;
}
.dib {
  display: inline-block;
}
.dn {
  display: none;
}

/* position */
.pa,
.T0,
.T1,
.T2,
.T3,
.T4,
.T5,
.T6,
.T7,
.T8,
.T9,
.T10,
.T11,
.T12,
.T13,
.T14,
.T15,
.T16,
.T17,
.T18,
.T19,
.T20,
.R0,
.R1,
.R2,
.R3,
.R4,
.R5,
.R6,
.R7,
.R8,
.R9,
.R10,
.R11,
.R12,
.R13,
.R14,
.R15,
.R16,
.R17,
.R18,
.R19,
.R20,
.B0,
.B1,
.B2,
.B3,
.B4,
.B5,
.B6,
.B7,
.B8,
.B9,
.B10,
.B11,
.B12,
.B13,
.B14,
.B15,
.B16,
.B17,
.B18,
.B19,
.B20,
.L0,
.L1,
.L2,
.L3,
.L4,
.L5,
.L6,
.L7,
.L8,
.L9,
.L10,
.L11,
.L12,
.L13,
.L14,
.L15,
.L16,
.L17,
.L18,
.L19,
.L20 {
  position: absolute;
}
.pr,
.Z1,
.Z2,
.Z3,
.Z4,
.Z5 {
  position: relative;
}
.pf {
  position: fixed;
}
.oh {
  overflow: hidden;
}

/* width height */
.W {
  width: 100%;
}
.H {
  height: 100%;
}
.max {
  max-width: 100%;
}
.max img {
  max-width: 100%;
}

/* width */
.W1,
.W2,
.W3,
.W4,
.W5,
.W6,
.W7,
.W8,
.W9,
.C1,
.C2,
.C3,
.C4,
.C5,
.C6,
.C7,
.C8,
.C9,
.C10,
.C11 {
  float: left;
}
.W1 {
  width: 10%;
}
.W2 {
  width: 20%;
}
.W3 {
  width: 30%;
}
.W4 {
  width: 40%;
}
.W5 {
  width: 50%;
}
.W6 {
  width: 60%;
}
.W7 {
  width: 70%;
}
.W8 {
  width: 80%;
}
.W9 {
  width: 90%;
}
.C1 {
  width: 8.33333333%;
}
.C2 {
  width: 16.66666667%;
}
.C3 {
  width: 25%;
}
.C4 {
  width: 33.33333333%;
}
.C5 {
  width: 41.66666667%;
}
.C6 {
  width: 50%;
}
.C7 {
  width: 58.33333333%;
}
.C8 {
  width: 66.66666667%;
}
.C9 {
  width: 75%;
}
.C10 {
  width: 83.33333333%;
}
.C11 {
  width: 91.66666667%;
}

/* float */
.fl {
  float: left;
}
.fr {
  float: right;
}
.fn {
  float: none;
}

/* text-align */
.tl {
  text-align: left;
}
.tr {
  text-align: right;
}
.tc {
  text-align: center;
}

/* cursor */
.S {
  cursor: pointer;
}
.s {
  cursor: default;
}

/* font */
.f0 {
  font-size: 0;
}

/* border-radius */
.r {
  border-radius: 1000px;
}

/* line-height */
.lh {
  line-height: 1;
}

/* border */
.bn {
  border: none;
}

/* background */
.bgn {
  background: transparent;
}

/* white-space */
.n {
  font-weight: normal;
  font-style: normal;
}
.b {
  font-weight: bold;
}

/* 综合属性 */
.clear:after {
  display: table;
  content: '';
  clear: both;
} /* 清除浮动 */
.auto {
  margin-left: auto;
  margin-right: auto;
  float: none;
} /* 块居中 */
.scroll {
  overflow-y: auto;
  -webkit-text-size-adjust: none;
  -webkit-overflow-scrolling: touch;
} /* 滚动条 */
/*.scrollX{overflow-x:auto;} /* 横向滚动条 */
.line {
  text-decoration: line-through;
} /* 中划线 */
.ell {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
} /* 单行文字溢出虚点显示*/
.nowrap {
  white-space: nowrap;
} /* 禁止换行 */

/* 基本样式 */
.p0-5 {
  padding: 0px 5px;
}
.p0-10 {
  padding: 0px 10px;
}
.p0-15 {
  padding: 0px 15px;
}
.p0-20 {
  padding: 0px 20px;
}
.p5-0 {
  padding: 5px 0px;
}
.p5-10 {
  padding: 5px 10px;
}
.p5-15 {
  padding: 5px 15px;
}
.p5-20 {
  padding: 5px 20px;
}
.p10-0 {
  padding: 10px 0px;
}
.p10-5 {
  padding: 10px 5px;
}
.p10-15 {
  padding: 10px 15px;
}
.p10-20 {
  padding: 10px 20px;
}
.p15-0 {
  padding: 15px 0px;
}
.p15-5 {
  padding: 15px 5px;
}
.p15-10 {
  padding: 15px 10px;
}
.p15-20 {
  padding: 15px 20px;
}
.p20-0 {
  padding: 20px 0px;
}
.p20-5 {
  padding: 20px 5px;
}
.p20-10 {
  padding: 20px 10px;
}
.p20-15 {
  padding: 20px 15px;
}

.m0-5 {
  margin: 0px 5px;
}
.m0-10 {
  margin: 0px 10px;
}
.m0-15 {
  margin: 0px 15px;
}
.m0-20 {
  margin: 0px 20px;
}
.m5-0 {
  margin: 5px 0px;
}
.m5-10 {
  margin: 5px 10px;
}
.m5-15 {
  margin: 5px 15px;
}
.m5-20 {
  margin: 5px 20px;
}
.m10-0 {
  margin: 10px 0px;
}
.m10-5 {
  margin: 10px 5px;
}
.m10-15 {
  margin: 10px 15px;
}
.m10-20 {
  margin: 10px 20px;
}
.m15-0 {
  margin: 15px 0px;
}
.m15-5 {
  margin: 15px 5px;
}
.m15-10 {
  margin: 15px 10px;
}
.m15-20 {
  margin: 15px 20px;
}
.m20-0 {
  margin: 20px 0px;
}
.m20-5 {
  margin: 20px 5px;
}
.m20-10 {
  margin: 20px 10px;
}
.m20-15 {
  margin: 20px 15px;
}

/* position    0~20    */
.T0 {
  top: 0px;
}
.R0 {
  right: 0px;
}
.B0 {
  bottom: 0px;
}
.L0 {
  left: 0px;
}
.T1 {
  top: 1px;
}
.R1 {
  right: 1px;
}
.B1 {
  bottom: 1px;
}
.L1 {
  left: 1px;
}
.T2 {
  top: 2px;
}
.R2 {
  right: 2px;
}
.B2 {
  bottom: 2px;
}
.L2 {
  left: 2px;
}
.T3 {
  top: 3px;
}
.R3 {
  right: 3px;
}
.B3 {
  bottom: 3px;
}
.L3 {
  left: 3px;
}
.T4 {
  top: 4px;
}
.R4 {
  right: 4px;
}
.B4 {
  bottom: 4px;
}
.L4 {
  left: 4px;
}
.T5 {
  top: 5px;
}
.R5 {
  right: 5px;
}
.B5 {
  bottom: 5px;
}
.L5 {
  left: 5px;
}
.T6 {
  top: 6px;
}
.R6 {
  right: 6px;
}
.B6 {
  bottom: 6px;
}
.L6 {
  left: 6px;
}
.T7 {
  top: 7px;
}
.R7 {
  right: 7px;
}
.B7 {
  bottom: 7px;
}
.L7 {
  left: 7px;
}
.T8 {
  top: 8px;
}
.R8 {
  right: 8px;
}
.B8 {
  bottom: 8px;
}
.L8 {
  left: 8px;
}
.T9 {
  top: 9px;
}
.R9 {
  right: 9px;
}
.B9 {
  bottom: 9px;
}
.L9 {
  left: 9px;
}
.T10 {
  top: 10px;
}
.R10 {
  right: 10px;
}
.B10 {
  bottom: 10px;
}
.L10 {
  left: 10px;
}
.T11 {
  top: 11px;
}
.R11 {
  right: 11px;
}
.B11 {
  bottom: 11px;
}
.L11 {
  left: 11px;
}
.T12 {
  top: 12px;
}
.R12 {
  right: 12px;
}
.B12 {
  bottom: 12px;
}
.L12 {
  left: 12px;
}
.T13 {
  top: 13px;
}
.R13 {
  right: 13px;
}
.B13 {
  bottom: 13px;
}
.L13 {
  left: 13px;
}
.T14 {
  top: 14px;
}
.R14 {
  right: 14px;
}
.B14 {
  bottom: 14px;
}
.L14 {
  left: 14px;
}
.T15 {
  top: 15px;
}
.R15 {
  right: 15px;
}
.B15 {
  bottom: 15px;
}
.L15 {
  left: 15px;
}
.T16 {
  top: 16px;
}
.R16 {
  right: 16px;
}
.B16 {
  bottom: 16px;
}
.L16 {
  left: 16px;
}
.T17 {
  top: 17px;
}
.R17 {
  right: 17px;
}
.B17 {
  bottom: 17px;
}
.L17 {
  left: 17px;
}
.T18 {
  top: 18px;
}
.R18 {
  right: 18px;
}
.B18 {
  bottom: 18px;
}
.L18 {
  left: 18px;
}
.T19 {
  top: 19px;
}
.R19 {
  right: 19px;
}
.B19 {
  bottom: 19px;
}
.L19 {
  left: 19px;
}
.T20 {
  top: 20px;
}
.R20 {
  right: 20px;
}
.B20 {
  bottom: 20px;
}
.L20 {
  left: 20px;
}
/* color       0~9 a~f */
.cr {
  color: #ff2630;
}
.c0 {
  color: #000;
}
.c1 {
  color: #111;
}
.c2 {
  color: #222;
}
.c3 {
  color: #333;
}
.c4 {
  color: #444;
}
.c5 {
  color: #555;
}
.c6 {
  color: #666;
}
.c7 {
  color: #777;
}
.c8 {
  color: #888;
}
.c9 {
  color: #999;
}
.ca {
  color: #aaa;
}
.cb {
  color: #bbb;
}
.cc {
  color: #ccc;
}
.cd {
  color: #ddd;
}
.ce {
  color: #eee;
}
.cf {
  color: #fff;
}
/* background  0~9 a~f */
.bgr {
  background-color: #c80000;
}
.bg0 {
  background-color: #000;
}
.bg1 {
  background-color: #111;
}
.bg2 {
  background-color: #222;
}
.bg3 {
  background-color: #333;
}
.bg4 {
  background-color: #444;
}
.bg5 {
  background-color: #555;
}
.bg6 {
  background-color: #666;
}
.bg7 {
  background-color: #777;
}
.bg8 {
  background-color: #888;
}
.bg9 {
  background-color: #999;
}
.bga {
  background-color: #aaa;
}
.bgb {
  background-color: #bbb;
}
.bgc {
  background-color: #ccc;
}
.bgd {
  background-color: #ddd;
}
.bge {
  background-color: #eee;
}
.bgf {
  background-color: #fff;
}
.bln {
  border-left: none;
}
.brn {
  border-right: none;
}
.btn {
  border-top: none;
}
.bbn {
  border-bottom: none;
}
/* border      0~9 a~f */
.b0 {
  border: 1px solid #000;
}
.bl0 {
  border-left: 1px solid #000;
}
.br0 {
  border-right: 1px solid #000;
}
.bt0 {
  border-top: 1px solid #000;
}
.bb0 {
  border-bottom: 1px solid #000;
}
.b1 {
  border: 1px solid #111;
}
.bl1 {
  border-left: 1px solid #111;
}
.br1 {
  border-right: 1px solid #111;
}
.bt1 {
  border-top: 1px solid #111;
}
.bb1 {
  border-bottom: 1px solid #111;
}
.b2 {
  border: 1px solid #222;
}
.bl2 {
  border-left: 1px solid #222;
}
.br2 {
  border-right: 1px solid #222;
}
.bt2 {
  border-top: 1px solid #222;
}
.bb2 {
  border-bottom: 1px solid #222;
}
.b3 {
  border: 1px solid #333;
}
.bl3 {
  border-left: 1px solid #333;
}
.br3 {
  border-right: 1px solid #333;
}
.bt3 {
  border-top: 1px solid #333;
}
.bb3 {
  border-bottom: 1px solid #333;
}
.b4 {
  border: 1px solid #444;
}
.bl4 {
  border-left: 1px solid #444;
}
.br4 {
  border-right: 1px solid #444;
}
.bt4 {
  border-top: 1px solid #444;
}
.bb4 {
  border-bottom: 1px solid #444;
}
.b5 {
  border: 1px solid #555;
}
.bl5 {
  border-left: 1px solid #555;
}
.br5 {
  border-right: 1px solid #555;
}
.bt5 {
  border-top: 1px solid #555;
}
.bb5 {
  border-bottom: 1px solid #555;
}
.b6 {
  border: 1px solid #666;
}
.bl6 {
  border-left: 1px solid #666;
}
.br6 {
  border-right: 1px solid #666;
}
.bt6 {
  border-top: 1px solid #666;
}
.bb6 {
  border-bottom: 1px solid #666;
}
.b7 {
  border: 1px solid #777;
}
.bl7 {
  border-left: 1px solid #777;
}
.br7 {
  border-right: 1px solid #777;
}
.bt7 {
  border-top: 1px solid #777;
}
.bb7 {
  border-bottom: 1px solid #777;
}
.b8 {
  border: 1px solid #888;
}
.bl8 {
  border-left: 1px solid #888;
}
.br8 {
  border-right: 1px solid #888;
}
.bt8 {
  border-top: 1px solid #888;
}
.bb8 {
  border-bottom: 1px solid #888;
}
.b9 {
  border: 1px solid #999;
}
.bl9 {
  border-left: 1px solid #999;
}
.br9 {
  border-right: 1px solid #999;
}
.bt9 {
  border-top: 1px solid #999;
}
.bb9 {
  border-bottom: 1px solid #999;
}
.ba {
  border: 1px solid #aaa;
}
.bla {
  border-left: 1px solid #aaa;
}
.bra {
  border-right: 1px solid #aaa;
}
.bta {
  border-top: 1px solid #aaa;
}
.bba {
  border-bottom: 1px solid #aaa;
}
.bb {
  border: 1px solid #bbb;
}
.blb {
  border-left: 1px solid #bbb;
}
.brb {
  border-right: 1px solid #bbb;
}
.btb {
  border-top: 1px solid #bbb;
}
.bbb {
  border-bottom: 1px solid #bbb;
}
.bc {
  border: 1px solid #ccc;
}
.blc {
  border-left: 1px solid #ccc;
}
.brc {
  border-right: 1px solid #ccc;
}
.btc {
  border-top: 1px solid #ccc;
}
.bbc {
  border-bottom: 1px solid #ccc;
}
.bd {
  border: 1px solid #ddd;
}
.bld {
  border-left: 1px solid #ddd;
}
.brd {
  border-right: 1px solid #ddd;
}
.btd {
  border-top: 1px solid #ddd;
}
.bbd {
  border-bottom: 1px solid #ddd;
}
.be {
  border: 1px solid #eee;
}
.ble {
  border-left: 1px solid #eee;
}
.bre {
  border-right: 1px solid #eee;
}
.bte {
  border-top: 1px solid #eee;
}
.bbe {
  border-bottom: 1px solid #eee;
}
.bf {
  border: 1px solid #fff;
}
.blf {
  border-left: 1px solid #fff;
}
.brf {
  border-right: 1px solid #fff;
}
.btf {
  border-top: 1px solid #fff;
}
.bbf {
  border-bottom: 1px solid #fff;
}
/* - margin    -1~-20  */
.mt-1 {
  margin-top: -1px;
}
.mr-1 {
  margin-right: -1px;
}
.mb-1 {
  margin-bottom: -1px;
}
.ml-1 {
  margin-left: -1px;
}
.mt-2 {
  margin-top: -2px;
}
.mr-2 {
  margin-right: -2px;
}
.mb-2 {
  margin-bottom: -2px;
}
.ml-2 {
  margin-left: -2px;
}
.mt-3 {
  margin-top: -3px;
}
.mr-3 {
  margin-right: -3px;
}
.mb-3 {
  margin-bottom: -3px;
}
.ml-3 {
  margin-left: -3px;
}
.mt-4 {
  margin-top: -4px;
}
.mr-4 {
  margin-right: -4px;
}
.mb-4 {
  margin-bottom: -4px;
}
.ml-4 {
  margin-left: -4px;
}
.mt-5 {
  margin-top: -5px;
}
.mr-5 {
  margin-right: -5px;
}
.mb-5 {
  margin-bottom: -5px;
}
.ml-5 {
  margin-left: -5px;
}
.mt-6 {
  margin-top: -6px;
}
.mr-6 {
  margin-right: -6px;
}
.mb-6 {
  margin-bottom: -6px;
}
.ml-6 {
  margin-left: -6px;
}
.mt-7 {
  margin-top: -7px;
}
.mr-7 {
  margin-right: -7px;
}
.mb-7 {
  margin-bottom: -7px;
}
.ml-7 {
  margin-left: -7px;
}
.mt-8 {
  margin-top: -8px;
}
.mr-8 {
  margin-right: -8px;
}
.mb-8 {
  margin-bottom: -8px;
}
.ml-8 {
  margin-left: -8px;
}
.mt-9 {
  margin-top: -9px;
}
.mr-9 {
  margin-right: -9px;
}
.mb-9 {
  margin-bottom: -9px;
}
.ml-9 {
  margin-left: -9px;
}
.mt-10 {
  margin-top: -10px;
}
.mr-10 {
  margin-right: -10px;
}
.mb-10 {
  margin-bottom: -10px;
}
.ml-10 {
  margin-left: -10px;
}
.mt-11 {
  margin-top: -11px;
}
.mr-11 {
  margin-right: -11px;
}
.mb-11 {
  margin-bottom: -11px;
}
.ml-11 {
  margin-left: -11px;
}
.mt-12 {
  margin-top: -12px;
}
.mr-12 {
  margin-right: -12px;
}
.mb-12 {
  margin-bottom: -12px;
}
.ml-12 {
  margin-left: -12px;
}
.mt-13 {
  margin-top: -13px;
}
.mr-13 {
  margin-right: -13px;
}
.mb-13 {
  margin-bottom: -13px;
}
.ml-13 {
  margin-left: -13px;
}
.mt-14 {
  margin-top: -14px;
}
.mr-14 {
  margin-right: -14px;
}
.mb-14 {
  margin-bottom: -14px;
}
.ml-14 {
  margin-left: -14px;
}
.mt-15 {
  margin-top: -15px;
}
.mr-15 {
  margin-right: -15px;
}
.mb-15 {
  margin-bottom: -15px;
}
.ml-15 {
  margin-left: -15px;
}
.mt-16 {
  margin-top: -16px;
}
.mr-16 {
  margin-right: -16px;
}
.mb-16 {
  margin-bottom: -16px;
}
.ml-16 {
  margin-left: -16px;
}
.mt-17 {
  margin-top: -17px;
}
.mr-17 {
  margin-right: -17px;
}
.mb-17 {
  margin-bottom: -17px;
}
.ml-17 {
  margin-left: -17px;
}
.mt-18 {
  margin-top: -18px;
}
.mr-18 {
  margin-right: -18px;
}
.mb-18 {
  margin-bottom: -18px;
}
.ml-18 {
  margin-left: -18px;
}
.mt-19 {
  margin-top: -19px;
}
.mr-19 {
  margin-right: -19px;
}
.mb-19 {
  margin-bottom: -19px;
}
.ml-19 {
  margin-left: -19px;
}
.mt-20 {
  margin-top: -20px;
}
.mr-20 {
  margin-right: -20px;
}
.mb-20 {
  margin-bottom: -20px;
}
.ml-20 {
  margin-left: -20px;
}
/* margin      0~50    */
.m0 {
  margin: 0px;
}
.m1 {
  margin: 1px;
}
.m2 {
  margin: 2px;
}
.m3 {
  margin: 3px;
}
.m4 {
  margin: 4px;
}
.m5 {
  margin: 5px;
}
.m6 {
  margin: 6px;
}
.m7 {
  margin: 7px;
}
.m8 {
  margin: 8px;
}
.m9 {
  margin: 9px;
}
.m10 {
  margin: 10px;
}
.m11 {
  margin: 11px;
}
.m12 {
  margin: 12px;
}
.m13 {
  margin: 13px;
}
.m14 {
  margin: 14px;
}
.m15 {
  margin: 15px;
}
.m16 {
  margin: 16px;
}
.m17 {
  margin: 17px;
}
.m18 {
  margin: 18px;
}
.m19 {
  margin: 19px;
}
.m20 {
  margin: 20px;
}
.m21 {
  margin: 21px;
}
.m22 {
  margin: 22px;
}
.m23 {
  margin: 23px;
}
.m24 {
  margin: 24px;
}
.m25 {
  margin: 25px;
}
.m26 {
  margin: 26px;
}
.m27 {
  margin: 27px;
}
.m28 {
  margin: 28px;
}
.m29 {
  margin: 29px;
}
.m30 {
  margin: 30px;
}
.m31 {
  margin: 31px;
}
.m32 {
  margin: 32px;
}
.m33 {
  margin: 33px;
}
.m34 {
  margin: 34px;
}
.m35 {
  margin: 35px;
}
.m36 {
  margin: 36px;
}
.m37 {
  margin: 37px;
}
.m38 {
  margin: 38px;
}
.m39 {
  margin: 39px;
}
.m40 {
  margin: 40px;
}
.m41 {
  margin: 41px;
}
.m42 {
  margin: 42px;
}
.m43 {
  margin: 43px;
}
.m44 {
  margin: 44px;
}
.m45 {
  margin: 45px;
}
.m46 {
  margin: 46px;
}
.m47 {
  margin: 47px;
}
.m48 {
  margin: 48px;
}
.m49 {
  margin: 49px;
}
.m50 {
  margin: 50px;
}
.mt0 {
  margin-top: 0px;
}
.mr0 {
  margin-right: 0px;
}
.mb0 {
  margin-bottom: 0px;
}
.ml0 {
  margin-left: 0px;
}
.mt1 {
  margin-top: 1px;
}
.mr1 {
  margin-right: 1px;
}
.mb1 {
  margin-bottom: 1px;
}
.ml1 {
  margin-left: 1px;
}
.mt2 {
  margin-top: 2px;
}
.mr2 {
  margin-right: 2px;
}
.mb2 {
  margin-bottom: 2px;
}
.ml2 {
  margin-left: 2px;
}
.mt3 {
  margin-top: 3px;
}
.mr3 {
  margin-right: 3px;
}
.mb3 {
  margin-bottom: 3px;
}
.ml3 {
  margin-left: 3px;
}
.mt4 {
  margin-top: 4px;
}
.mr4 {
  margin-right: 4px;
}
.mb4 {
  margin-bottom: 4px;
}
.ml4 {
  margin-left: 4px;
}
.mt5 {
  margin-top: 5px;
}
.mr5 {
  margin-right: 5px;
}
.mb5 {
  margin-bottom: 5px;
}
.ml5 {
  margin-left: 5px;
}
.mt6 {
  margin-top: 6px;
}
.mr6 {
  margin-right: 6px;
}
.mb6 {
  margin-bottom: 6px;
}
.ml6 {
  margin-left: 6px;
}
.mt7 {
  margin-top: 7px;
}
.mr7 {
  margin-right: 7px;
}
.mb7 {
  margin-bottom: 7px;
}
.ml7 {
  margin-left: 7px;
}
.mt8 {
  margin-top: 8px;
}
.mr8 {
  margin-right: 8px;
}
.mb8 {
  margin-bottom: 8px;
}
.ml8 {
  margin-left: 8px;
}
.mt9 {
  margin-top: 9px;
}
.mr9 {
  margin-right: 9px;
}
.mb9 {
  margin-bottom: 9px;
}
.ml9 {
  margin-left: 9px;
}
.mt10 {
  margin-top: 10px;
}
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.ml10 {
  margin-left: 10px;
}
.mt11 {
  margin-top: 11px;
}
.mr11 {
  margin-right: 11px;
}
.mb11 {
  margin-bottom: 11px;
}
.ml11 {
  margin-left: 11px;
}
.mt12 {
  margin-top: 12px;
}
.mr12 {
  margin-right: 12px;
}
.mb12 {
  margin-bottom: 12px;
}
.ml12 {
  margin-left: 12px;
}
.mt13 {
  margin-top: 13px;
}
.mr13 {
  margin-right: 13px;
}
.mb13 {
  margin-bottom: 13px;
}
.ml13 {
  margin-left: 13px;
}
.mt14 {
  margin-top: 14px;
}
.mr14 {
  margin-right: 14px;
}
.mb14 {
  margin-bottom: 14px;
}
.ml14 {
  margin-left: 14px;
}
.mt15 {
  margin-top: 15px;
}
.mr15 {
  margin-right: 15px;
}
.mb15 {
  margin-bottom: 15px;
}
.ml15 {
  margin-left: 15px;
}
.mt16 {
  margin-top: 16px;
}
.mr16 {
  margin-right: 16px;
}
.mb16 {
  margin-bottom: 16px;
}
.ml16 {
  margin-left: 16px;
}
.mt17 {
  margin-top: 17px;
}
.mr17 {
  margin-right: 17px;
}
.mb17 {
  margin-bottom: 17px;
}
.ml17 {
  margin-left: 17px;
}
.mt18 {
  margin-top: 18px;
}
.mr18 {
  margin-right: 18px;
}
.mb18 {
  margin-bottom: 18px;
}
.ml18 {
  margin-left: 18px;
}
.mt19 {
  margin-top: 19px;
}
.mr19 {
  margin-right: 19px;
}
.mb19 {
  margin-bottom: 19px;
}
.ml19 {
  margin-left: 19px;
}
.mt20 {
  margin-top: 20px;
}
.mr20 {
  margin-right: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.ml20 {
  margin-left: 20px;
}
.mt21 {
  margin-top: 21px;
}
.mr21 {
  margin-right: 21px;
}
.mb21 {
  margin-bottom: 21px;
}
.ml21 {
  margin-left: 21px;
}
.mt22 {
  margin-top: 22px;
}
.mr22 {
  margin-right: 22px;
}
.mb22 {
  margin-bottom: 22px;
}
.ml22 {
  margin-left: 22px;
}
.mt23 {
  margin-top: 23px;
}
.mr23 {
  margin-right: 23px;
}
.mb23 {
  margin-bottom: 23px;
}
.ml23 {
  margin-left: 23px;
}
.mt24 {
  margin-top: 24px;
}
.mr24 {
  margin-right: 24px;
}
.mb24 {
  margin-bottom: 24px;
}
.ml24 {
  margin-left: 24px;
}
.mt25 {
  margin-top: 25px;
}
.mr25 {
  margin-right: 25px;
}
.mb25 {
  margin-bottom: 25px;
}
.ml25 {
  margin-left: 25px;
}
.mt26 {
  margin-top: 26px;
}
.mr26 {
  margin-right: 26px;
}
.mb26 {
  margin-bottom: 26px;
}
.ml26 {
  margin-left: 26px;
}
.mt27 {
  margin-top: 27px;
}
.mr27 {
  margin-right: 27px;
}
.mb27 {
  margin-bottom: 27px;
}
.ml27 {
  margin-left: 27px;
}
.mt28 {
  margin-top: 28px;
}
.mr28 {
  margin-right: 28px;
}
.mb28 {
  margin-bottom: 28px;
}
.ml28 {
  margin-left: 28px;
}
.mt29 {
  margin-top: 29px;
}
.mr29 {
  margin-right: 29px;
}
.mb29 {
  margin-bottom: 29px;
}
.ml29 {
  margin-left: 29px;
}
.mt30 {
  margin-top: 30px;
}
.mr30 {
  margin-right: 30px;
}
.mb30 {
  margin-bottom: 30px;
}
.ml30 {
  margin-left: 30px;
}
.mt31 {
  margin-top: 31px;
}
.mr31 {
  margin-right: 31px;
}
.mb31 {
  margin-bottom: 31px;
}
.ml31 {
  margin-left: 31px;
}
.mt32 {
  margin-top: 32px;
}
.mr32 {
  margin-right: 32px;
}
.mb32 {
  margin-bottom: 32px;
}
.ml32 {
  margin-left: 32px;
}
.mt33 {
  margin-top: 33px;
}
.mr33 {
  margin-right: 33px;
}
.mb33 {
  margin-bottom: 33px;
}
.ml33 {
  margin-left: 33px;
}
.mt34 {
  margin-top: 34px;
}
.mr34 {
  margin-right: 34px;
}
.mb34 {
  margin-bottom: 34px;
}
.ml34 {
  margin-left: 34px;
}
.mt35 {
  margin-top: 35px;
}
.mr35 {
  margin-right: 35px;
}
.mb35 {
  margin-bottom: 35px;
}
.ml35 {
  margin-left: 35px;
}
.mt36 {
  margin-top: 36px;
}
.mr36 {
  margin-right: 36px;
}
.mb36 {
  margin-bottom: 36px;
}
.ml36 {
  margin-left: 36px;
}
.mt37 {
  margin-top: 37px;
}
.mr37 {
  margin-right: 37px;
}
.mb37 {
  margin-bottom: 37px;
}
.ml37 {
  margin-left: 37px;
}
.mt38 {
  margin-top: 38px;
}
.mr38 {
  margin-right: 38px;
}
.mb38 {
  margin-bottom: 38px;
}
.ml38 {
  margin-left: 38px;
}
.mt39 {
  margin-top: 39px;
}
.mr39 {
  margin-right: 39px;
}
.mb39 {
  margin-bottom: 39px;
}
.ml39 {
  margin-left: 39px;
}
.mt40 {
  margin-top: 40px;
}
.mr40 {
  margin-right: 40px;
}
.mb40 {
  margin-bottom: 40px;
}
.ml40 {
  margin-left: 40px;
}
.mt41 {
  margin-top: 41px;
}
.mr41 {
  margin-right: 41px;
}
.mb41 {
  margin-bottom: 41px;
}
.ml41 {
  margin-left: 41px;
}
.mt42 {
  margin-top: 42px;
}
.mr42 {
  margin-right: 42px;
}
.mb42 {
  margin-bottom: 42px;
}
.ml42 {
  margin-left: 42px;
}
.mt43 {
  margin-top: 43px;
}
.mr43 {
  margin-right: 43px;
}
.mb43 {
  margin-bottom: 43px;
}
.ml43 {
  margin-left: 43px;
}
.mt44 {
  margin-top: 44px;
}
.mr44 {
  margin-right: 44px;
}
.mb44 {
  margin-bottom: 44px;
}
.ml44 {
  margin-left: 44px;
}
.mt45 {
  margin-top: 45px;
}
.mr45 {
  margin-right: 45px;
}
.mb45 {
  margin-bottom: 45px;
}
.ml45 {
  margin-left: 45px;
}
.mt46 {
  margin-top: 46px;
}
.mr46 {
  margin-right: 46px;
}
.mb46 {
  margin-bottom: 46px;
}
.ml46 {
  margin-left: 46px;
}
.mt47 {
  margin-top: 47px;
}
.mr47 {
  margin-right: 47px;
}
.mb47 {
  margin-bottom: 47px;
}
.ml47 {
  margin-left: 47px;
}
.mt48 {
  margin-top: 48px;
}
.mr48 {
  margin-right: 48px;
}
.mb48 {
  margin-bottom: 48px;
}
.ml48 {
  margin-left: 48px;
}
.mt49 {
  margin-top: 49px;
}
.mr49 {
  margin-right: 49px;
}
.mb49 {
  margin-bottom: 49px;
}
.ml49 {
  margin-left: 49px;
}
.mt50 {
  margin-top: 50px;
}
.mr50 {
  margin-right: 50px;
}
.mb50 {
  margin-bottom: 50px;
}
.ml50 {
  margin-left: 50px;
}
/* padding     0~50    */
.p0 {
  padding: 0px;
}
.p1 {
  padding: 1px;
}
.p2 {
  padding: 2px;
}
.p3 {
  padding: 3px;
}
.p4 {
  padding: 4px;
}
.p5 {
  padding: 5px;
}
.p6 {
  padding: 6px;
}
.p7 {
  padding: 7px;
}
.p8 {
  padding: 8px;
}
.p9 {
  padding: 9px;
}
.p10 {
  padding: 10px;
}
.p11 {
  padding: 11px;
}
.p12 {
  padding: 12px;
}
.p13 {
  padding: 13px;
}
.p14 {
  padding: 14px;
}
.p15 {
  padding: 15px;
}
.p16 {
  padding: 16px;
}
.p17 {
  padding: 17px;
}
.p18 {
  padding: 18px;
}
.p19 {
  padding: 19px;
}
.p20 {
  padding: 20px;
}
.p21 {
  padding: 21px;
}
.p22 {
  padding: 22px;
}
.p23 {
  padding: 23px;
}
.p24 {
  padding: 24px;
}
.p25 {
  padding: 25px;
}
.p26 {
  padding: 26px;
}
.p27 {
  padding: 27px;
}
.p28 {
  padding: 28px;
}
.p29 {
  padding: 29px;
}
.p30 {
  padding: 30px;
}
.p31 {
  padding: 31px;
}
.p32 {
  padding: 32px;
}
.p33 {
  padding: 33px;
}
.p34 {
  padding: 34px;
}
.p35 {
  padding: 35px;
}
.p36 {
  padding: 36px;
}
.p37 {
  padding: 37px;
}
.p38 {
  padding: 38px;
}
.p39 {
  padding: 39px;
}
.p40 {
  padding: 40px;
}
.p41 {
  padding: 41px;
}
.p42 {
  padding: 42px;
}
.p43 {
  padding: 43px;
}
.p44 {
  padding: 44px;
}
.p45 {
  padding: 45px;
}
.p46 {
  padding: 46px;
}
.p47 {
  padding: 47px;
}
.p48 {
  padding: 48px;
}
.p49 {
  padding: 49px;
}
.p50 {
  padding: 50px;
}
.pt0 {
  padding-top: 0px;
}
.pr0 {
  padding-right: 0px;
}
.pb0 {
  padding-bottom: 0px;
}
.pl0 {
  padding-left: 0px;
}
.pt1 {
  padding-top: 1px;
}
.pr1 {
  padding-right: 1px;
}
.pb1 {
  padding-bottom: 1px;
}
.pl1 {
  padding-left: 1px;
}
.pt2 {
  padding-top: 2px;
}
.pr2 {
  padding-right: 2px;
}
.pb2 {
  padding-bottom: 2px;
}
.pl2 {
  padding-left: 2px;
}
.pt3 {
  padding-top: 3px;
}
.pr3 {
  padding-right: 3px;
}
.pb3 {
  padding-bottom: 3px;
}
.pl3 {
  padding-left: 3px;
}
.pt4 {
  padding-top: 4px;
}
.pr4 {
  padding-right: 4px;
}
.pb4 {
  padding-bottom: 4px;
}
.pl4 {
  padding-left: 4px;
}
.pt5 {
  padding-top: 5px;
}
.pr5 {
  padding-right: 5px;
}
.pb5 {
  padding-bottom: 5px;
}
.pl5 {
  padding-left: 5px;
}
.pt6 {
  padding-top: 6px;
}
.pr6 {
  padding-right: 6px;
}
.pb6 {
  padding-bottom: 6px;
}
.pl6 {
  padding-left: 6px;
}
.pt7 {
  padding-top: 7px;
}
.pr7 {
  padding-right: 7px;
}
.pb7 {
  padding-bottom: 7px;
}
.pl7 {
  padding-left: 7px;
}
.pt8 {
  padding-top: 8px;
}
.pr8 {
  padding-right: 8px;
}
.pb8 {
  padding-bottom: 8px;
}
.pl8 {
  padding-left: 8px;
}
.pt9 {
  padding-top: 9px;
}
.pr9 {
  padding-right: 9px;
}
.pb9 {
  padding-bottom: 9px;
}
.pl9 {
  padding-left: 9px;
}
.pt10 {
  padding-top: 10px;
}
.pr10 {
  padding-right: 10px;
}
.pb10 {
  padding-bottom: 10px;
}
.pl10 {
  padding-left: 10px;
}
.pt11 {
  padding-top: 11px;
}
.pr11 {
  padding-right: 11px;
}
.pb11 {
  padding-bottom: 11px;
}
.pl11 {
  padding-left: 11px;
}
.pt12 {
  padding-top: 12px;
}
.pr12 {
  padding-right: 12px;
}
.pb12 {
  padding-bottom: 12px;
}
.pl12 {
  padding-left: 12px;
}
.pt13 {
  padding-top: 13px;
}
.pr13 {
  padding-right: 13px;
}
.pb13 {
  padding-bottom: 13px;
}
.pl13 {
  padding-left: 13px;
}
.pt14 {
  padding-top: 14px;
}
.pr14 {
  padding-right: 14px;
}
.pb14 {
  padding-bottom: 14px;
}
.pl14 {
  padding-left: 14px;
}
.pt15 {
  padding-top: 15px;
}
.pr15 {
  padding-right: 15px;
}
.pb15 {
  padding-bottom: 15px;
}
.pl15 {
  padding-left: 15px;
}
.pt16 {
  padding-top: 16px;
}
.pr16 {
  padding-right: 16px;
}
.pb16 {
  padding-bottom: 16px;
}
.pl16 {
  padding-left: 16px;
}
.pt17 {
  padding-top: 17px;
}
.pr17 {
  padding-right: 17px;
}
.pb17 {
  padding-bottom: 17px;
}
.pl17 {
  padding-left: 17px;
}
.pt18 {
  padding-top: 18px;
}
.pr18 {
  padding-right: 18px;
}
.pb18 {
  padding-bottom: 18px;
}
.pl18 {
  padding-left: 18px;
}
.pt19 {
  padding-top: 19px;
}
.pr19 {
  padding-right: 19px;
}
.pb19 {
  padding-bottom: 19px;
}
.pl19 {
  padding-left: 19px;
}
.pt20 {
  padding-top: 20px;
}
.pr20 {
  padding-right: 20px;
}
.pb20 {
  padding-bottom: 20px;
}
.pl20 {
  padding-left: 20px;
}
.pt21 {
  padding-top: 21px;
}
.pr21 {
  padding-right: 21px;
}
.pb21 {
  padding-bottom: 21px;
}
.pl21 {
  padding-left: 21px;
}
.pt22 {
  padding-top: 22px;
}
.pr22 {
  padding-right: 22px;
}
.pb22 {
  padding-bottom: 22px;
}
.pl22 {
  padding-left: 22px;
}
.pt23 {
  padding-top: 23px;
}
.pr23 {
  padding-right: 23px;
}
.pb23 {
  padding-bottom: 23px;
}
.pl23 {
  padding-left: 23px;
}
.pt24 {
  padding-top: 24px;
}
.pr24 {
  padding-right: 24px;
}
.pb24 {
  padding-bottom: 24px;
}
.pl24 {
  padding-left: 24px;
}
.pt25 {
  padding-top: 25px;
}
.pr25 {
  padding-right: 25px;
}
.pb25 {
  padding-bottom: 25px;
}
.pl25 {
  padding-left: 25px;
}
.pt26 {
  padding-top: 26px;
}
.pr26 {
  padding-right: 26px;
}
.pb26 {
  padding-bottom: 26px;
}
.pl26 {
  padding-left: 26px;
}
.pt27 {
  padding-top: 27px;
}
.pr27 {
  padding-right: 27px;
}
.pb27 {
  padding-bottom: 27px;
}
.pl27 {
  padding-left: 27px;
}
.pt28 {
  padding-top: 28px;
}
.pr28 {
  padding-right: 28px;
}
.pb28 {
  padding-bottom: 28px;
}
.pl28 {
  padding-left: 28px;
}
.pt29 {
  padding-top: 29px;
}
.pr29 {
  padding-right: 29px;
}
.pb29 {
  padding-bottom: 29px;
}
.pl29 {
  padding-left: 29px;
}
.pt30 {
  padding-top: 30px;
}
.pr30 {
  padding-right: 30px;
}
.pb30 {
  padding-bottom: 30px;
}
.pl30 {
  padding-left: 30px;
}
.pt31 {
  padding-top: 31px;
}
.pr31 {
  padding-right: 31px;
}
.pb31 {
  padding-bottom: 31px;
}
.pl31 {
  padding-left: 31px;
}
.pt32 {
  padding-top: 32px;
}
.pr32 {
  padding-right: 32px;
}
.pb32 {
  padding-bottom: 32px;
}
.pl32 {
  padding-left: 32px;
}
.pt33 {
  padding-top: 33px;
}
.pr33 {
  padding-right: 33px;
}
.pb33 {
  padding-bottom: 33px;
}
.pl33 {
  padding-left: 33px;
}
.pt34 {
  padding-top: 34px;
}
.pr34 {
  padding-right: 34px;
}
.pb34 {
  padding-bottom: 34px;
}
.pl34 {
  padding-left: 34px;
}
.pt35 {
  padding-top: 35px;
}
.pr35 {
  padding-right: 35px;
}
.pb35 {
  padding-bottom: 35px;
}
.pl35 {
  padding-left: 35px;
}
.pt36 {
  padding-top: 36px;
}
.pr36 {
  padding-right: 36px;
}
.pb36 {
  padding-bottom: 36px;
}
.pl36 {
  padding-left: 36px;
}
.pt37 {
  padding-top: 37px;
}
.pr37 {
  padding-right: 37px;
}
.pb37 {
  padding-bottom: 37px;
}
.pl37 {
  padding-left: 37px;
}
.pt38 {
  padding-top: 38px;
}
.pr38 {
  padding-right: 38px;
}
.pb38 {
  padding-bottom: 38px;
}
.pl38 {
  padding-left: 38px;
}
.pt39 {
  padding-top: 39px;
}
.pr39 {
  padding-right: 39px;
}
.pb39 {
  padding-bottom: 39px;
}
.pl39 {
  padding-left: 39px;
}
.pt40 {
  padding-top: 40px;
}
.pr40 {
  padding-right: 40px;
}
.pb40 {
  padding-bottom: 40px;
}
.pl40 {
  padding-left: 40px;
}
.pt41 {
  padding-top: 41px;
}
.pr41 {
  padding-right: 41px;
}
.pb41 {
  padding-bottom: 41px;
}
.pl41 {
  padding-left: 41px;
}
.pt42 {
  padding-top: 42px;
}
.pr42 {
  padding-right: 42px;
}
.pb42 {
  padding-bottom: 42px;
}
.pl42 {
  padding-left: 42px;
}
.pt43 {
  padding-top: 43px;
}
.pr43 {
  padding-right: 43px;
}
.pb43 {
  padding-bottom: 43px;
}
.pl43 {
  padding-left: 43px;
}
.pt44 {
  padding-top: 44px;
}
.pr44 {
  padding-right: 44px;
}
.pb44 {
  padding-bottom: 44px;
}
.pl44 {
  padding-left: 44px;
}
.pt45 {
  padding-top: 45px;
}
.pr45 {
  padding-right: 45px;
}
.pb45 {
  padding-bottom: 45px;
}
.pl45 {
  padding-left: 45px;
}
.pt46 {
  padding-top: 46px;
}
.pr46 {
  padding-right: 46px;
}
.pb46 {
  padding-bottom: 46px;
}
.pl46 {
  padding-left: 46px;
}
.pt47 {
  padding-top: 47px;
}
.pr47 {
  padding-right: 47px;
}
.pb47 {
  padding-bottom: 47px;
}
.pl47 {
  padding-left: 47px;
}
.pt48 {
  padding-top: 48px;
}
.pr48 {
  padding-right: 48px;
}
.pb48 {
  padding-bottom: 48px;
}
.pl48 {
  padding-left: 48px;
}
.pt49 {
  padding-top: 49px;
}
.pr49 {
  padding-right: 49px;
}
.pb49 {
  padding-bottom: 49px;
}
.pl49 {
  padding-left: 49px;
}
.pt50 {
  padding-top: 50px;
}
.pr50 {
  padding-right: 50px;
}
.pb50 {
  padding-bottom: 50px;
}
.pl50 {
  padding-left: 50px;
}
/* radius      2~10    */
.r2 {
  border-radius: 2px;
}
.r3 {
  border-radius: 3px;
}
.r4 {
  border-radius: 4px;
}
.r5 {
  border-radius: 5px;
}
.r6 {
  border-radius: 6px;
}
.r7 {
  border-radius: 7px;
}
.r8 {
  border-radius: 8px;
}
.r9 {
  border-radius: 9px;
}
.r10 {
  border-radius: 10px;
}
/* z-index     1~5     */
.z1,
.Z1 {
  z-index: 1;
}
.z2,
.Z2 {
  z-index: 2;
}
.z3,
.Z3 {
  z-index: 3;
}
.z4,
.Z4 {
  z-index: 4;
}
.z5,
.Z5 {
  z-index: 5;
}
/* width       0~500   */
.w1 {
  width: 1px;
}
.w2 {
  width: 2px;
}
.w3 {
  width: 3px;
}
.w4 {
  width: 4px;
}
.w5 {
  width: 5px;
}
.w6 {
  width: 6px;
}
.w7 {
  width: 7px;
}
.w8 {
  width: 8px;
}
.w9 {
  width: 9px;
}
.w10 {
  width: 10px;
}
.w11 {
  width: 11px;
}
.w12 {
  width: 12px;
}
.w13 {
  width: 13px;
}
.w14 {
  width: 14px;
}
.w15 {
  width: 15px;
}
.w16 {
  width: 16px;
}
.w17 {
  width: 17px;
}
.w18 {
  width: 18px;
}
.w19 {
  width: 19px;
}
.w20 {
  width: 20px;
}
.w21 {
  width: 21px;
}
.w22 {
  width: 22px;
}
.w23 {
  width: 23px;
}
.w24 {
  width: 24px;
}
.w25 {
  width: 25px;
}
.w26 {
  width: 26px;
}
.w27 {
  width: 27px;
}
.w28 {
  width: 28px;
}
.w29 {
  width: 29px;
}
.w30 {
  width: 30px;
}
.w31 {
  width: 31px;
}
.w32 {
  width: 32px;
}
.w33 {
  width: 33px;
}
.w34 {
  width: 34px;
}
.w35 {
  width: 35px;
}
.w36 {
  width: 36px;
}
.w37 {
  width: 37px;
}
.w38 {
  width: 38px;
}
.w39 {
  width: 39px;
}
.w40 {
  width: 40px;
}
.w41 {
  width: 41px;
}
.w42 {
  width: 42px;
}
.w43 {
  width: 43px;
}
.w44 {
  width: 44px;
}
.w45 {
  width: 45px;
}
.w46 {
  width: 46px;
}
.w47 {
  width: 47px;
}
.w48 {
  width: 48px;
}
.w49 {
  width: 49px;
}
.w50 {
  width: 50px;
}
.w51 {
  width: 51px;
}
.w52 {
  width: 52px;
}
.w53 {
  width: 53px;
}
.w54 {
  width: 54px;
}
.w55 {
  width: 55px;
}
.w56 {
  width: 56px;
}
.w57 {
  width: 57px;
}
.w58 {
  width: 58px;
}
.w59 {
  width: 59px;
}
.w60 {
  width: 60px;
}
.w61 {
  width: 61px;
}
.w62 {
  width: 62px;
}
.w63 {
  width: 63px;
}
.w64 {
  width: 64px;
}
.w65 {
  width: 65px;
}
.w66 {
  width: 66px;
}
.w67 {
  width: 67px;
}
.w68 {
  width: 68px;
}
.w69 {
  width: 69px;
}
.w70 {
  width: 70px;
}
.w71 {
  width: 71px;
}
.w72 {
  width: 72px;
}
.w73 {
  width: 73px;
}
.w74 {
  width: 74px;
}
.w75 {
  width: 75px;
}
.w76 {
  width: 76px;
}
.w77 {
  width: 77px;
}
.w78 {
  width: 78px;
}
.w79 {
  width: 79px;
}
.w80 {
  width: 80px;
}
.w81 {
  width: 81px;
}
.w82 {
  width: 82px;
}
.w83 {
  width: 83px;
}
.w84 {
  width: 84px;
}
.w85 {
  width: 85px;
}
.w86 {
  width: 86px;
}
.w87 {
  width: 87px;
}
.w88 {
  width: 88px;
}
.w89 {
  width: 89px;
}
.w90 {
  width: 90px;
}
.w91 {
  width: 91px;
}
.w92 {
  width: 92px;
}
.w93 {
  width: 93px;
}
.w94 {
  width: 94px;
}
.w95 {
  width: 95px;
}
.w96 {
  width: 96px;
}
.w97 {
  width: 97px;
}
.w98 {
  width: 98px;
}
.w99 {
  width: 99px;
}
.w100 {
  width: 100px;
}
.w101 {
  width: 101px;
}
.w102 {
  width: 102px;
}
.w103 {
  width: 103px;
}
.w104 {
  width: 104px;
}
.w105 {
  width: 105px;
}
.w106 {
  width: 106px;
}
.w107 {
  width: 107px;
}
.w108 {
  width: 108px;
}
.w109 {
  width: 109px;
}
.w110 {
  width: 110px;
}
.w111 {
  width: 111px;
}
.w112 {
  width: 112px;
}
.w113 {
  width: 113px;
}
.w114 {
  width: 114px;
}
.w115 {
  width: 115px;
}
.w116 {
  width: 116px;
}
.w117 {
  width: 117px;
}
.w118 {
  width: 118px;
}
.w119 {
  width: 119px;
}
.w120 {
  width: 120px;
}
.w121 {
  width: 121px;
}
.w122 {
  width: 122px;
}
.w123 {
  width: 123px;
}
.w124 {
  width: 124px;
}
.w125 {
  width: 125px;
}
.w126 {
  width: 126px;
}
.w127 {
  width: 127px;
}
.w128 {
  width: 128px;
}
.w129 {
  width: 129px;
}
.w130 {
  width: 130px;
}
.w131 {
  width: 131px;
}
.w132 {
  width: 132px;
}
.w133 {
  width: 133px;
}
.w134 {
  width: 134px;
}
.w135 {
  width: 135px;
}
.w136 {
  width: 136px;
}
.w137 {
  width: 137px;
}
.w138 {
  width: 138px;
}
.w139 {
  width: 139px;
}
.w140 {
  width: 140px;
}
.w141 {
  width: 141px;
}
.w142 {
  width: 142px;
}
.w143 {
  width: 143px;
}
.w144 {
  width: 144px;
}
.w145 {
  width: 145px;
}
.w146 {
  width: 146px;
}
.w147 {
  width: 147px;
}
.w148 {
  width: 148px;
}
.w149 {
  width: 149px;
}
.w150 {
  width: 150px;
}
.w151 {
  width: 151px;
}
.w152 {
  width: 152px;
}
.w153 {
  width: 153px;
}
.w154 {
  width: 154px;
}
.w155 {
  width: 155px;
}
.w156 {
  width: 156px;
}
.w157 {
  width: 157px;
}
.w158 {
  width: 158px;
}
.w159 {
  width: 159px;
}
.w160 {
  width: 160px;
}
.w161 {
  width: 161px;
}
.w162 {
  width: 162px;
}
.w163 {
  width: 163px;
}
.w164 {
  width: 164px;
}
.w165 {
  width: 165px;
}
.w166 {
  width: 166px;
}
.w167 {
  width: 167px;
}
.w168 {
  width: 168px;
}
.w169 {
  width: 169px;
}
.w170 {
  width: 170px;
}
.w171 {
  width: 171px;
}
.w172 {
  width: 172px;
}
.w173 {
  width: 173px;
}
.w174 {
  width: 174px;
}
.w175 {
  width: 175px;
}
.w176 {
  width: 176px;
}
.w177 {
  width: 177px;
}
.w178 {
  width: 178px;
}
.w179 {
  width: 179px;
}
.w180 {
  width: 180px;
}
.w181 {
  width: 181px;
}
.w182 {
  width: 182px;
}
.w183 {
  width: 183px;
}
.w184 {
  width: 184px;
}
.w185 {
  width: 185px;
}
.w186 {
  width: 186px;
}
.w187 {
  width: 187px;
}
.w188 {
  width: 188px;
}
.w189 {
  width: 189px;
}
.w190 {
  width: 190px;
}
.w191 {
  width: 191px;
}
.w192 {
  width: 192px;
}
.w193 {
  width: 193px;
}
.w194 {
  width: 194px;
}
.w195 {
  width: 195px;
}
.w196 {
  width: 196px;
}
.w197 {
  width: 197px;
}
.w198 {
  width: 198px;
}
.w199 {
  width: 199px;
}
.w200 {
  width: 200px;
}
.w201 {
  width: 201px;
}
.w202 {
  width: 202px;
}
.w203 {
  width: 203px;
}
.w204 {
  width: 204px;
}
.w205 {
  width: 205px;
}
.w206 {
  width: 206px;
}
.w207 {
  width: 207px;
}
.w208 {
  width: 208px;
}
.w209 {
  width: 209px;
}
.w210 {
  width: 210px;
}
.w211 {
  width: 211px;
}
.w212 {
  width: 212px;
}
.w213 {
  width: 213px;
}
.w214 {
  width: 214px;
}
.w215 {
  width: 215px;
}
.w216 {
  width: 216px;
}
.w217 {
  width: 217px;
}
.w218 {
  width: 218px;
}
.w219 {
  width: 219px;
}
.w220 {
  width: 220px;
}
.w221 {
  width: 221px;
}
.w222 {
  width: 222px;
}
.w223 {
  width: 223px;
}
.w224 {
  width: 224px;
}
.w225 {
  width: 225px;
}
.w226 {
  width: 226px;
}
.w227 {
  width: 227px;
}
.w228 {
  width: 228px;
}
.w229 {
  width: 229px;
}
.w230 {
  width: 230px;
}
.w231 {
  width: 231px;
}
.w232 {
  width: 232px;
}
.w233 {
  width: 233px;
}
.w234 {
  width: 234px;
}
.w235 {
  width: 235px;
}
.w236 {
  width: 236px;
}
.w237 {
  width: 237px;
}
.w238 {
  width: 238px;
}
.w239 {
  width: 239px;
}
.w240 {
  width: 240px;
}
.w241 {
  width: 241px;
}
.w242 {
  width: 242px;
}
.w243 {
  width: 243px;
}
.w244 {
  width: 244px;
}
.w245 {
  width: 245px;
}
.w246 {
  width: 246px;
}
.w247 {
  width: 247px;
}
.w248 {
  width: 248px;
}
.w249 {
  width: 249px;
}
.w250 {
  width: 250px;
}
.w251 {
  width: 251px;
}
.w252 {
  width: 252px;
}
.w253 {
  width: 253px;
}
.w254 {
  width: 254px;
}
.w255 {
  width: 255px;
}
.w256 {
  width: 256px;
}
.w257 {
  width: 257px;
}
.w258 {
  width: 258px;
}
.w259 {
  width: 259px;
}
.w260 {
  width: 260px;
}
.w261 {
  width: 261px;
}
.w262 {
  width: 262px;
}
.w263 {
  width: 263px;
}
.w264 {
  width: 264px;
}
.w265 {
  width: 265px;
}
.w266 {
  width: 266px;
}
.w267 {
  width: 267px;
}
.w268 {
  width: 268px;
}
.w269 {
  width: 269px;
}
.w270 {
  width: 270px;
}
.w271 {
  width: 271px;
}
.w272 {
  width: 272px;
}
.w273 {
  width: 273px;
}
.w274 {
  width: 274px;
}
.w275 {
  width: 275px;
}
.w276 {
  width: 276px;
}
.w277 {
  width: 277px;
}
.w278 {
  width: 278px;
}
.w279 {
  width: 279px;
}
.w280 {
  width: 280px;
}
.w281 {
  width: 281px;
}
.w282 {
  width: 282px;
}
.w283 {
  width: 283px;
}
.w284 {
  width: 284px;
}
.w285 {
  width: 285px;
}
.w286 {
  width: 286px;
}
.w287 {
  width: 287px;
}
.w288 {
  width: 288px;
}
.w289 {
  width: 289px;
}
.w290 {
  width: 290px;
}
.w291 {
  width: 291px;
}
.w292 {
  width: 292px;
}
.w293 {
  width: 293px;
}
.w294 {
  width: 294px;
}
.w295 {
  width: 295px;
}
.w296 {
  width: 296px;
}
.w297 {
  width: 297px;
}
.w298 {
  width: 298px;
}
.w299 {
  width: 299px;
}
.w300 {
  width: 300px;
}
.w301 {
  width: 301px;
}
.w302 {
  width: 302px;
}
.w303 {
  width: 303px;
}
.w304 {
  width: 304px;
}
.w305 {
  width: 305px;
}
.w306 {
  width: 306px;
}
.w307 {
  width: 307px;
}
.w308 {
  width: 308px;
}
.w309 {
  width: 309px;
}
.w310 {
  width: 310px;
}
.w311 {
  width: 311px;
}
.w312 {
  width: 312px;
}
.w313 {
  width: 313px;
}
.w314 {
  width: 314px;
}
.w315 {
  width: 315px;
}
.w316 {
  width: 316px;
}
.w317 {
  width: 317px;
}
.w318 {
  width: 318px;
}
.w319 {
  width: 319px;
}
.w320 {
  width: 320px;
}
.w321 {
  width: 321px;
}
.w322 {
  width: 322px;
}
.w323 {
  width: 323px;
}
.w324 {
  width: 324px;
}
.w325 {
  width: 325px;
}
.w326 {
  width: 326px;
}
.w327 {
  width: 327px;
}
.w328 {
  width: 328px;
}
.w329 {
  width: 329px;
}
.w330 {
  width: 330px;
}
.w331 {
  width: 331px;
}
.w332 {
  width: 332px;
}
.w333 {
  width: 333px;
}
.w334 {
  width: 334px;
}
.w335 {
  width: 335px;
}
.w336 {
  width: 336px;
}
.w337 {
  width: 337px;
}
.w338 {
  width: 338px;
}
.w339 {
  width: 339px;
}
.w340 {
  width: 340px;
}
.w341 {
  width: 341px;
}
.w342 {
  width: 342px;
}
.w343 {
  width: 343px;
}
.w344 {
  width: 344px;
}
.w345 {
  width: 345px;
}
.w346 {
  width: 346px;
}
.w347 {
  width: 347px;
}
.w348 {
  width: 348px;
}
.w349 {
  width: 349px;
}
.w350 {
  width: 350px;
}
.w351 {
  width: 351px;
}
.w352 {
  width: 352px;
}
.w353 {
  width: 353px;
}
.w354 {
  width: 354px;
}
.w355 {
  width: 355px;
}
.w356 {
  width: 356px;
}
.w357 {
  width: 357px;
}
.w358 {
  width: 358px;
}
.w359 {
  width: 359px;
}
.w360 {
  width: 360px;
}
.w361 {
  width: 361px;
}
.w362 {
  width: 362px;
}
.w363 {
  width: 363px;
}
.w364 {
  width: 364px;
}
.w365 {
  width: 365px;
}
.w366 {
  width: 366px;
}
.w367 {
  width: 367px;
}
.w368 {
  width: 368px;
}
.w369 {
  width: 369px;
}
.w370 {
  width: 370px;
}
.w371 {
  width: 371px;
}
.w372 {
  width: 372px;
}
.w373 {
  width: 373px;
}
.w374 {
  width: 374px;
}
.w375 {
  width: 375px;
}
.w376 {
  width: 376px;
}
.w377 {
  width: 377px;
}
.w378 {
  width: 378px;
}
.w379 {
  width: 379px;
}
.w380 {
  width: 380px;
}
.w381 {
  width: 381px;
}
.w382 {
  width: 382px;
}
.w383 {
  width: 383px;
}
.w384 {
  width: 384px;
}
.w385 {
  width: 385px;
}
.w386 {
  width: 386px;
}
.w387 {
  width: 387px;
}
.w388 {
  width: 388px;
}
.w389 {
  width: 389px;
}
.w390 {
  width: 390px;
}
.w391 {
  width: 391px;
}
.w392 {
  width: 392px;
}
.w393 {
  width: 393px;
}
.w394 {
  width: 394px;
}
.w395 {
  width: 395px;
}
.w396 {
  width: 396px;
}
.w397 {
  width: 397px;
}
.w398 {
  width: 398px;
}
.w399 {
  width: 399px;
}
.w400 {
  width: 400px;
}
.w401 {
  width: 401px;
}
.w402 {
  width: 402px;
}
.w403 {
  width: 403px;
}
.w404 {
  width: 404px;
}
.w405 {
  width: 405px;
}
.w406 {
  width: 406px;
}
.w407 {
  width: 407px;
}
.w408 {
  width: 408px;
}
.w409 {
  width: 409px;
}
.w410 {
  width: 410px;
}
.w411 {
  width: 411px;
}
.w412 {
  width: 412px;
}
.w413 {
  width: 413px;
}
.w414 {
  width: 414px;
}
.w415 {
  width: 415px;
}
.w416 {
  width: 416px;
}
.w417 {
  width: 417px;
}
.w418 {
  width: 418px;
}
.w419 {
  width: 419px;
}
.w420 {
  width: 420px;
}
.w421 {
  width: 421px;
}
.w422 {
  width: 422px;
}
.w423 {
  width: 423px;
}
.w424 {
  width: 424px;
}
.w425 {
  width: 425px;
}
.w426 {
  width: 426px;
}
.w427 {
  width: 427px;
}
.w428 {
  width: 428px;
}
.w429 {
  width: 429px;
}
.w430 {
  width: 430px;
}
.w431 {
  width: 431px;
}
.w432 {
  width: 432px;
}
.w433 {
  width: 433px;
}
.w434 {
  width: 434px;
}
.w435 {
  width: 435px;
}
.w436 {
  width: 436px;
}
.w437 {
  width: 437px;
}
.w438 {
  width: 438px;
}
.w439 {
  width: 439px;
}
.w440 {
  width: 440px;
}
.w441 {
  width: 441px;
}
.w442 {
  width: 442px;
}
.w443 {
  width: 443px;
}
.w444 {
  width: 444px;
}
.w445 {
  width: 445px;
}
.w446 {
  width: 446px;
}
.w447 {
  width: 447px;
}
.w448 {
  width: 448px;
}
.w449 {
  width: 449px;
}
.w450 {
  width: 450px;
}
.w451 {
  width: 451px;
}
.w452 {
  width: 452px;
}
.w453 {
  width: 453px;
}
.w454 {
  width: 454px;
}
.w455 {
  width: 455px;
}
.w456 {
  width: 456px;
}
.w457 {
  width: 457px;
}
.w458 {
  width: 458px;
}
.w459 {
  width: 459px;
}
.w460 {
  width: 460px;
}
.w461 {
  width: 461px;
}
.w462 {
  width: 462px;
}
.w463 {
  width: 463px;
}
.w464 {
  width: 464px;
}
.w465 {
  width: 465px;
}
.w466 {
  width: 466px;
}
.w467 {
  width: 467px;
}
.w468 {
  width: 468px;
}
.w469 {
  width: 469px;
}
.w470 {
  width: 470px;
}
.w471 {
  width: 471px;
}
.w472 {
  width: 472px;
}
.w473 {
  width: 473px;
}
.w474 {
  width: 474px;
}
.w475 {
  width: 475px;
}
.w476 {
  width: 476px;
}
.w477 {
  width: 477px;
}
.w478 {
  width: 478px;
}
.w479 {
  width: 479px;
}
.w480 {
  width: 480px;
}
.w481 {
  width: 481px;
}
.w482 {
  width: 482px;
}
.w483 {
  width: 483px;
}
.w484 {
  width: 484px;
}
.w485 {
  width: 485px;
}
.w486 {
  width: 486px;
}
.w487 {
  width: 487px;
}
.w488 {
  width: 488px;
}
.w489 {
  width: 489px;
}
.w490 {
  width: 490px;
}
.w491 {
  width: 491px;
}
.w492 {
  width: 492px;
}
.w493 {
  width: 493px;
}
.w494 {
  width: 494px;
}
.w495 {
  width: 495px;
}
.w496 {
  width: 496px;
}
.w497 {
  width: 497px;
}
.w498 {
  width: 498px;
}
.w499 {
  width: 499px;
}
.w500 {
  width: 500px;
}
/* height      0~200   */
.h1 {
  height: 1px;
}
.h2 {
  height: 2px;
}
.h3 {
  height: 3px;
}
.h4 {
  height: 4px;
}
.h5 {
  height: 5px;
}
.h6 {
  height: 6px;
}
.h7 {
  height: 7px;
}
.h8 {
  height: 8px;
}
.h9 {
  height: 9px;
}
.h10,
.hl10 {
  height: 10px;
}
.h11,
.hl11 {
  height: 11px;
}
.h12,
.hl12 {
  height: 12px;
}
.h13,
.hl13 {
  height: 13px;
}
.h14,
.hl14 {
  height: 14px;
}
.h15,
.hl15 {
  height: 15px;
}
.h16,
.hl16 {
  height: 16px;
}
.h17,
.hl17 {
  height: 17px;
}
.h18,
.hl18 {
  height: 18px;
}
.h19,
.hl19 {
  height: 19px;
}
.h20,
.hl20 {
  height: 20px;
}
.h21,
.hl21 {
  height: 21px;
}
.h22,
.hl22 {
  height: 22px;
}
.h23,
.hl23 {
  height: 23px;
}
.h24,
.hl24 {
  height: 24px;
}
.h25,
.hl25 {
  height: 25px;
}
.h26,
.hl26 {
  height: 26px;
}
.h27,
.hl27 {
  height: 27px;
}
.h28,
.hl28 {
  height: 28px;
}
.h29,
.hl29 {
  height: 29px;
}
.h30,
.hl30 {
  height: 30px;
}
.h31,
.hl31 {
  height: 31px;
}
.h32,
.hl32 {
  height: 32px;
}
.h33,
.hl33 {
  height: 33px;
}
.h34,
.hl34 {
  height: 34px;
}
.h35,
.hl35 {
  height: 35px;
}
.h36,
.hl36 {
  height: 36px;
}
.h37,
.hl37 {
  height: 37px;
}
.h38,
.hl38 {
  height: 38px;
}
.h39,
.hl39 {
  height: 39px;
}
.h40,
.hl40 {
  height: 40px;
}
.h41,
.hl41 {
  height: 41px;
}
.h42,
.hl42 {
  height: 42px;
}
.h43,
.hl43 {
  height: 43px;
}
.h44,
.hl44 {
  height: 44px;
}
.h45,
.hl45 {
  height: 45px;
}
.h46,
.hl46 {
  height: 46px;
}
.h47,
.hl47 {
  height: 47px;
}
.h48,
.hl48 {
  height: 48px;
}
.h49,
.hl49 {
  height: 49px;
}
.h50,
.hl50 {
  height: 50px;
}
.h51,
.hl51 {
  height: 51px;
}
.h52,
.hl52 {
  height: 52px;
}
.h53,
.hl53 {
  height: 53px;
}
.h54,
.hl54 {
  height: 54px;
}
.h55,
.hl55 {
  height: 55px;
}
.h56,
.hl56 {
  height: 56px;
}
.h57,
.hl57 {
  height: 57px;
}
.h58,
.hl58 {
  height: 58px;
}
.h59,
.hl59 {
  height: 59px;
}
.h60,
.hl60 {
  height: 60px;
}
.h61 {
  height: 61px;
}
.h62 {
  height: 62px;
}
.h63 {
  height: 63px;
}
.h64 {
  height: 64px;
}
.h65 {
  height: 65px;
}
.h66 {
  height: 66px;
}
.h67 {
  height: 67px;
}
.h68 {
  height: 68px;
}
.h69 {
  height: 69px;
}
.h70 {
  height: 70px;
}
.h71 {
  height: 71px;
}
.h72 {
  height: 72px;
}
.h73 {
  height: 73px;
}
.h74 {
  height: 74px;
}
.h75 {
  height: 75px;
}
.h76 {
  height: 76px;
}
.h77 {
  height: 77px;
}
.h78 {
  height: 78px;
}
.h79 {
  height: 79px;
}
.h80 {
  height: 80px;
}
.h81 {
  height: 81px;
}
.h82 {
  height: 82px;
}
.h83 {
  height: 83px;
}
.h84 {
  height: 84px;
}
.h85 {
  height: 85px;
}
.h86 {
  height: 86px;
}
.h87 {
  height: 87px;
}
.h88 {
  height: 88px;
}
.h89 {
  height: 89px;
}
.h90 {
  height: 90px;
}
.h91 {
  height: 91px;
}
.h92 {
  height: 92px;
}
.h93 {
  height: 93px;
}
.h94 {
  height: 94px;
}
.h95 {
  height: 95px;
}
.h96 {
  height: 96px;
}
.h97 {
  height: 97px;
}
.h98 {
  height: 98px;
}
.h99 {
  height: 99px;
}
.h100 {
  height: 100px;
}
.h101 {
  height: 101px;
}
.h102 {
  height: 102px;
}
.h103 {
  height: 103px;
}
.h104 {
  height: 104px;
}
.h105 {
  height: 105px;
}
.h106 {
  height: 106px;
}
.h107 {
  height: 107px;
}
.h108 {
  height: 108px;
}
.h109 {
  height: 109px;
}
.h110 {
  height: 110px;
}
.h111 {
  height: 111px;
}
.h112 {
  height: 112px;
}
.h113 {
  height: 113px;
}
.h114 {
  height: 114px;
}
.h115 {
  height: 115px;
}
.h116 {
  height: 116px;
}
.h117 {
  height: 117px;
}
.h118 {
  height: 118px;
}
.h119 {
  height: 119px;
}
.h120 {
  height: 120px;
}
.h121 {
  height: 121px;
}
.h122 {
  height: 122px;
}
.h123 {
  height: 123px;
}
.h124 {
  height: 124px;
}
.h125 {
  height: 125px;
}
.h126 {
  height: 126px;
}
.h127 {
  height: 127px;
}
.h128 {
  height: 128px;
}
.h129 {
  height: 129px;
}
.h130 {
  height: 130px;
}
.h131 {
  height: 131px;
}
.h132 {
  height: 132px;
}
.h133 {
  height: 133px;
}
.h134 {
  height: 134px;
}
.h135 {
  height: 135px;
}
.h136 {
  height: 136px;
}
.h137 {
  height: 137px;
}
.h138 {
  height: 138px;
}
.h139 {
  height: 139px;
}
.h140 {
  height: 140px;
}
.h141 {
  height: 141px;
}
.h142 {
  height: 142px;
}
.h143 {
  height: 143px;
}
.h144 {
  height: 144px;
}
.h145 {
  height: 145px;
}
.h146 {
  height: 146px;
}
.h147 {
  height: 147px;
}
.h148 {
  height: 148px;
}
.h149 {
  height: 149px;
}
.h150 {
  height: 150px;
}
.h151 {
  height: 151px;
}
.h152 {
  height: 152px;
}
.h153 {
  height: 153px;
}
.h154 {
  height: 154px;
}
.h155 {
  height: 155px;
}
.h156 {
  height: 156px;
}
.h157 {
  height: 157px;
}
.h158 {
  height: 158px;
}
.h159 {
  height: 159px;
}
.h160 {
  height: 160px;
}
.h161 {
  height: 161px;
}
.h162 {
  height: 162px;
}
.h163 {
  height: 163px;
}
.h164 {
  height: 164px;
}
.h165 {
  height: 165px;
}
.h166 {
  height: 166px;
}
.h167 {
  height: 167px;
}
.h168 {
  height: 168px;
}
.h169 {
  height: 169px;
}
.h170 {
  height: 170px;
}
.h171 {
  height: 171px;
}
.h172 {
  height: 172px;
}
.h173 {
  height: 173px;
}
.h174 {
  height: 174px;
}
.h175 {
  height: 175px;
}
.h176 {
  height: 176px;
}
.h177 {
  height: 177px;
}
.h178 {
  height: 178px;
}
.h179 {
  height: 179px;
}
.h180 {
  height: 180px;
}
.h181 {
  height: 181px;
}
.h182 {
  height: 182px;
}
.h183 {
  height: 183px;
}
.h184 {
  height: 184px;
}
.h185 {
  height: 185px;
}
.h186 {
  height: 186px;
}
.h187 {
  height: 187px;
}
.h188 {
  height: 188px;
}
.h189 {
  height: 189px;
}
.h190 {
  height: 190px;
}
.h191 {
  height: 191px;
}
.h192 {
  height: 192px;
}
.h193 {
  height: 193px;
}
.h194 {
  height: 194px;
}
.h195 {
  height: 195px;
}
.h196 {
  height: 196px;
}
.h197 {
  height: 197px;
}
.h198 {
  height: 198px;
}
.h199 {
  height: 199px;
}
.h200 {
  height: 200px;
}
/* font        10~40   */
.f10 {
  font-size: 10px;
}
.f11 {
  font-size: 11px;
}
.f12 {
  font-size: 12px;
}
.f13 {
  font-size: 13px;
}
.f14 {
  font-size: 14px;
}
.f15 {
  font-size: 15px;
}
.f16 {
  font-size: 16px;
}
.f17 {
  font-size: 17px;
}
.f18 {
  font-size: 18px;
}
.f19 {
  font-size: 19px;
}
.f20 {
  font-size: 20px;
}
.f21 {
  font-size: 21px;
}
.f22 {
  font-size: 22px;
}
.f23 {
  font-size: 23px;
}
.f24 {
  font-size: 24px;
}
.f25 {
  font-size: 25px;
}
.f26 {
  font-size: 26px;
}
.f27 {
  font-size: 27px;
}
.f28 {
  font-size: 28px;
}
.f29 {
  font-size: 29px;
}
.f30 {
  font-size: 30px;
}
.f31 {
  font-size: 31px;
}
.f32 {
  font-size: 32px;
}
.f33 {
  font-size: 33px;
}
.f34 {
  font-size: 34px;
}
.f35 {
  font-size: 35px;
}
.f36 {
  font-size: 36px;
}
.f37 {
  font-size: 37px;
}
.f38 {
  font-size: 38px;
}
.f39 {
  font-size: 39px;
}
.f40 {
  font-size: 40px;
}
/* line-height 10~60   */
.lh10,
.hl10 {
  line-height: 10px;
}
.lh11,
.hl11 {
  line-height: 11px;
}
.lh12,
.hl12 {
  line-height: 12px;
}
.lh13,
.hl13 {
  line-height: 13px;
}
.lh14,
.hl14 {
  line-height: 14px;
}
.lh15,
.hl15 {
  line-height: 15px;
}
.lh16,
.hl16 {
  line-height: 16px;
}
.lh17,
.hl17 {
  line-height: 17px;
}
.lh18,
.hl18 {
  line-height: 18px;
}
.lh19,
.hl19 {
  line-height: 19px;
}
.lh20,
.hl20 {
  line-height: 20px;
}
.lh21,
.hl21 {
  line-height: 21px;
}
.lh22,
.hl22 {
  line-height: 22px;
}
.lh23,
.hl23 {
  line-height: 23px;
}
.lh24,
.hl24 {
  line-height: 24px;
}
.lh25,
.hl25 {
  line-height: 25px;
}
.lh26,
.hl26 {
  line-height: 26px;
}
.lh27,
.hl27 {
  line-height: 27px;
}
.lh28,
.hl28 {
  line-height: 28px;
}
.lh29,
.hl29 {
  line-height: 29px;
}
.lh30,
.hl30 {
  line-height: 30px;
}
.lh31,
.hl31 {
  line-height: 31px;
}
.lh32,
.hl32 {
  line-height: 32px;
}
.lh33,
.hl33 {
  line-height: 33px;
}
.lh34,
.hl34 {
  line-height: 34px;
}
.lh35,
.hl35 {
  line-height: 35px;
}
.lh36,
.hl36 {
  line-height: 36px;
}
.lh37,
.hl37 {
  line-height: 37px;
}
.lh38,
.hl38 {
  line-height: 38px;
}
.lh39,
.hl39 {
  line-height: 39px;
}
.lh40,
.hl40 {
  line-height: 40px;
}
.lh41,
.hl41 {
  line-height: 41px;
}
.lh42,
.hl42 {
  line-height: 42px;
}
.lh43,
.hl43 {
  line-height: 43px;
}
.lh44,
.hl44 {
  line-height: 44px;
}
.lh45,
.hl45 {
  line-height: 45px;
}
.lh46,
.hl46 {
  line-height: 46px;
}
.lh47,
.hl47 {
  line-height: 47px;
}
.lh48,
.hl48 {
  line-height: 48px;
}
.lh49,
.hl49 {
  line-height: 49px;
}
.lh50,
.hl50 {
  line-height: 50px;
}
.lh51,
.hl51 {
  line-height: 51px;
}
.lh52,
.hl52 {
  line-height: 52px;
}
.lh53,
.hl53 {
  line-height: 53px;
}
.lh54,
.hl54 {
  line-height: 54px;
}
.lh55,
.hl55 {
  line-height: 55px;
}
.lh56,
.hl56 {
  line-height: 56px;
}
.lh57,
.hl57 {
  line-height: 57px;
}
.lh58,
.hl58 {
  line-height: 58px;
}
.lh59,
.hl59 {
  line-height: 59px;
}
.lh60,
.hl60 {
  line-height: 60px;
}
/* letter-spacing 1~20 */
.ls1 {
  letter-spacing: 1px;
}
.ls2 {
  letter-spacing: 2px;
}
.ls3 {
  letter-spacing: 3px;
}
.ls4 {
  letter-spacing: 4px;
}
.ls5 {
  letter-spacing: 5px;
}
.ls6 {
  letter-spacing: 6px;
}
.ls7 {
  letter-spacing: 7px;
}
.ls8 {
  letter-spacing: 8px;
}
.ls9 {
  letter-spacing: 9px;
}
.ls10 {
  letter-spacing: 10px;
}
.ls11 {
  letter-spacing: 11px;
}
.ls12 {
  letter-spacing: 12px;
}
.ls13 {
  letter-spacing: 13px;
}
.ls14 {
  letter-spacing: 14px;
}
.ls15 {
  letter-spacing: 15px;
}
.ls16 {
  letter-spacing: 16px;
}
.ls17 {
  letter-spacing: 17px;
}
.ls18 {
  letter-spacing: 18px;
}
.ls19 {
  letter-spacing: 19px;
}
.ls20 {
  letter-spacing: 20px;
}
