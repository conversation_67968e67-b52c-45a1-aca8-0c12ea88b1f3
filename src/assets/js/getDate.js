import moment from 'moment'
var now = new Date(); //当前日期
var nowDayOfWeek = now.getDay(); //今天本周的第几天
var nowDay = now.getDate(); //当前日
var nowMonth = now.getMonth(); //当前月
/*var nowYear = now.getYear(); //当前年
nowYear += (nowYear < 2000) ? 1900 : 0;  //保证获取的年份正确显示*/
var nowYear = now.getFullYear(); //当前年
var lastMonthDate = new Date(); //上月日期
lastMonthDate.setDate(1);
lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
var lastYear = lastMonthDate.getYear();
var lastMonth = lastMonthDate.getMonth();

//格式化日期：yyyy-MM-dd
function formatDate(date) {
  var myyear = date.getFullYear();
  var mymonth = date.getMonth() + 1;
  var myweekday = date.getDate();
  if (mymonth < 10) {
    mymonth = "0" + mymonth;
  }
  if (myweekday < 10) {
    myweekday = "0" + myweekday;
  }
  return (myyear + "-" + mymonth + "-" + myweekday);
}
export function format(date) {
  return formatDate(date);
}
//获得某月的天数
export function getMonthDays(M) {
  M = M + 1;
  let year = moment().year()
  let days = moment(`${year}-${M}`, "YYYY-MM").daysInMonth();
  return days;
}
//获得本季度的开始月份
export function getQuarterStartMonth() {
  var quarterStartMonth = 0;
  if (nowMonth < 3) {
    quarterStartMonth = 0;
  }
  if (2 < nowMonth && nowMonth < 6) {
    quarterStartMonth = 3;
  }
  if (5 < nowMonth && nowMonth < 9) {
    quarterStartMonth = 6;
  }
  if (nowMonth > 8) {
    quarterStartMonth = 9;
  }
  return quarterStartMonth;
}
//获取上一季度开始月份
export function getPriorSeasonFirstDay(year,month){
    var quarterMonthStart=0;
    var spring=0; //春
    var summer=3; //夏
    var fall=6;   //秋
    var winter=9;//冬
    //月份从0-11
    switch(month){//季度的其实月份
        case spring:
            //如果是第一季度则应该到去年的冬季
            year--;
            month=winter;
            break;
        case summer:
            month=spring;
            break;
        case fall:
            month=summer;
            break;
        case winter:
            month=fall;
            break;

    }
    return new Date(year,month,1);
}
// 获取今天数据{}
export  function getToday() {
  return moment().subtract(0, 'days').format('YYYY-MM-DD')
}
//获取昨天的时间
export  function getYesterDay() {
  return moment().subtract(1, 'days').format('YYYY-MM-DD')
}
//获得本周的开始日期
export function getWeekStartDate() {
  var weekStartDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek);
  return formatDate(weekStartDate);
}
//获得本周的结束日期
export function getWeekEndDate() {
  var weekEndDate = new Date(nowYear, nowMonth, nowDay + (6 - nowDayOfWeek));
  return formatDate(weekEndDate);
}
//获得上周的开始日期
export function getLastWeekStartDate() {
  var weekStartDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 7);
  return formatDate(weekStartDate);
}
//获得上周的结束日期
export function getLastWeekEndDate() {
  var weekEndDate = new Date(nowYear, nowMonth, nowDay - nowDayOfWeek - 1);
  return formatDate(weekEndDate);
}
//获得本月的开始日期
export function getMonthStartDate() {
  var monthStartDate = new Date(nowYear, nowMonth, 1);
  return formatDate(monthStartDate);
}
//获得本月的结束日期
export function getMonthEndDate() {
  var monthEndDate = new Date(nowYear, nowMonth, getMonthDays(nowMonth));
  return formatDate(monthEndDate);
}
//获得上月开始时间
export function getLastMonthStartDate() {
  var lastMonthStartDate = new Date(nowYear, lastMonth, 1);
  if (nowMonth === 0) {
    lastMonthStartDate = new Date(nowYear - 1, lastMonth, 1);
  }
  return formatDate(lastMonthStartDate);
}
//获得上月结束时间
export function getLastMonthEndDate() {
  var lastMonthEndDate = new Date(nowYear, lastMonth, getMonthDays(lastMonth));
  if (nowMonth === 0) {
    lastMonthEndDate = new Date(nowYear - 1, lastMonth, getMonthDays(lastMonth));
  }
  return formatDate(lastMonthEndDate);
}
//获得本季度的开始日期
export function getQuarterStartDate() {
  var quarterStartDate = new Date(nowYear, getQuarterStartMonth(), 1);
  return formatDate(quarterStartDate);
}
//或的本季度的结束日期
export function getQuarterEndDate() {
  var quarterEndMonth = getQuarterStartMonth() + 2;
  var quarterStartDate = new Date(nowYear, quarterEndMonth,
    getMonthDays(quarterEndMonth));
  return formatDate(quarterStartDate);
}
//获取上一季度开始日期
export function getLastQuarterStartDate(){
    //获取当前时间
    var currentDate = new Date();
    //获得当前月份0-11
    var currentMonth=currentDate.getMonth();
    //获得当前年份4位年
    var currentYear=currentDate.getFullYear();
    //上季度的第一天
    var priorSeasonFirstDay=getPriorSeasonFirstDay(currentYear, currentMonth);
    //添加至数组
    return formatDate(priorSeasonFirstDay);
}
//获取上一季度结束日期
export function getLastQuarterEndDate(){
    //获取当前时间
    var currentDate = new Date();
    //获得当前月份0-11
    var currentMonth=currentDate.getMonth();
    //获得当前年份4位年
    var currentYear=currentDate.getFullYear();
    //上季度的最后一天
    var priorSeasonFirstDay=getPriorSeasonFirstDay(currentYear, currentMonth);
    var priorSeasonLastDay=new Date(priorSeasonFirstDay.getFullYear(),priorSeasonFirstDay.getMonth()+2,getMonthDays(priorSeasonFirstDay.getFullYear(), priorSeasonFirstDay.getMonth()+2));
    return formatDate(priorSeasonLastDay);
}
/**
 * 近N天
 */
export function getRecentDay(n) {
  let currentDate = new Date()
  let day = moment(currentDate).subtract(n, "days").format("YYYY-MM-DD");
  return day;

}


