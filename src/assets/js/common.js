
/*
 * 订单收货状态
 */

let getPageName = () => {
  let reg = new RegExp('\\?.*', 'gi');
  return location.href.replace(reg, '').split('/').find((value, index, arr) => {
    return index === arr.length -1 ? value : null;
  });
}

let isJSON = (str) => {
  if (typeof str == 'string') {
    try {
      var obj = JSON.parse(str);
      if (typeof obj == 'object' && obj) {
        return true;
      } else {
        return false;
      }

    } catch (e) {
      // console.log('error：' + str + '!!!' + e);
      return false;
    }
  }
  // console.log('It is not a string!')
}

let bottomTip = (ele, type, msg, callback,exwidth) => {

    let e = document.createElement("div"),
        icon = document.createElement("i"),
        desc = document.createElement("span"),
        height = ele.offsetHeight,
        width = ele.offsetWidth,
        left='50%',
        // left: ${left};
        // transform:translate(-50%);
        parEle = ele.parentNode;
        if(exwidth && exwidth.price_warning){
          width = ele.offsetWidth + 14
          left = 'calc(50% - 7px)'
        }
    desc.innerHTML = msg;
    icon.className =
        type == "success"
            ? "ivu-icon ivu-icon-checkmark-circled"
            : "ivu-icon ivu-icon-information-circled";
    icon.style.marginRight = "5px";
    e.style.cssText = ` position: absolute;
                            top: ${height}px;
                            white-space: nowrap;
                            height: 30px;
                            z-index: 999;
                            line-height: 30px;
                            border: 1px solid #03ac54;
                            color: var(--primary-color);
                            padding: 0 10px;
                            background: white;
                            border-radius: 3px;`;

    e.appendChild(icon);
    e.appendChild(desc);
    parEle.appendChild(e);
    
    setTimeout(() => {
        if(typeof callback == 'function') {
            callback();
        }
        e.remove();
    }, 1000);
}
let getTop = e => {
    var offset = e.offsetTop;
    if (e.offsetParent != null) offset += getTop(e.offsetParent);
    return offset;
}
let getLeft = e => {
    var offset = e.offsetLeft;
    if (e.offsetParent != null) offset += getLeft(e.offsetParent);
    return offset;
}
//format时间

let format = (date, fmt) => {
    var o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length == 1
                    ? o[k]
                    : ("00" + o[k]).substr(("" + o[k]).length)
            );
        }
    }
    return fmt;
}

const orderMode = {
  'all': 0,
  'unaudit': 100,
  'unpay': 200,
  'undelivery': 300,
  'unreceived': 400,
  'received': 500,
  'completed': 600,
  'closed': 700,
  'groupUnaudit': 99,
}

export { 
  getPageName,
  orderMode,
  isJSON,
  format,
  bottomTip
};