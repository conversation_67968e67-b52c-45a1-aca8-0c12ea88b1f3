@message-prefix-cls: ~"@{css-prefix}message";
@icon-prefix-cls: ~"@{css-prefix}icon";

.@{message-prefix-cls} {
  &-notice-content {
    border-radius: 2px;
    font-size: 15px;
    min-width: 220px;
    text-align: left;
  }
  &-notice-content-success {
    color: var(--primary-message-text);
    box-shadow: 0px 4px 12px 0px rgba(15, 33, 27, 0.1);
    background: rgba(234, 251, 242, 1);
    border: 1px solid rgba(122, 201, 144, 1);
  }
  &-notice-content-error {
    color: #f13130;
    box-shadow: 0px 4px 12px 0px rgba(33, 15, 15, 0.1);
    background: rgba(254, 246, 245, 1);
    border: 1px solid rgba(246, 127, 127, 1);
  }
  &-notice-content-warning {
    color: #ff8a00;
    background: rgba(255, 245, 229, 1);
    box-shadow: 0px 4px 12px 0px rgba(33, 26, 15, 0.1);
    border: 1px solid rgba(255, 198, 104, 1);
  }
  &-notice-content-info {
    color: #0879FF;
    background: rgba(229, 241, 255, 1);
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(96, 169, 255, 1);
  }
  &-success .@{icon-prefix-cls} {
    color: var(--primary-message-text);
  }

  &-error .@{icon-prefix-cls} {
    color: #f13130;
  }

  &-warning .@{icon-prefix-cls} {
    color: #ff8a00;
  }

  &-info .@{icon-prefix-cls},
  &-loading .@{icon-prefix-cls} {
    color: #0879FF;
  }

  .@{icon-prefix-cls} {
    font-size: 20px;
  }
}
