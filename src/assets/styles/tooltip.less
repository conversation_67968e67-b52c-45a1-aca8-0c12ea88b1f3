@import '~view-design/src/styles/components/tooltip.less';
@danger-bg: #f6e2e1;
@danger-text: #ff0000;
@primary-bg: #03ac54;
@primary-text: #fff;
.@{tooltip-prefix-cls} {

  display: inline-block;

  &-rel{
    display: inline-block;
    position: relative;
    width: inherit;
  }

  &-popper{
    .popper(@tooltip-arrow, @tooltip-arrow-width, @tooltip-distance, @tooltip-bg);
  }
  &-light&-popper{
    .popper(@tooltip-arrow, @tooltip-arrow-width-light, @tooltip-distance-light, @tooltip-arrow-color);

    &[x-placement^="top"] .@{tooltip-arrow}:after {
      content: " ";
      bottom: 1px;
      margin-left: -@tooltip-arrow-width-light;
      border-bottom-width: 0;
      border-top-width: @tooltip-arrow-width-light;
      border-top-color: #fff;
    }

    &[x-placement^="right"] .@{tooltip-arrow}:after {
      content: " ";
      left: 1px;
      bottom: -@tooltip-arrow-width-light;
      border-left-width: 0;
      border-right-width: @tooltip-arrow-width-light;
      border-right-color: #fff;
    }

    &[x-placement^="bottom"] .@{tooltip-arrow}:after {
      content: " ";
      top: 1px;
      margin-left: -@tooltip-arrow-width-light;
      border-top-width: 0;
      border-bottom-width: @tooltip-arrow-width-light;
      border-bottom-color: #fff;
    }

    &[x-placement^="left"] .@{tooltip-arrow}:after {
      content: " ";
      right: 1px;
      border-right-width: 0;
      border-left-width: @tooltip-arrow-width-light;
      border-left-color: #fff;
      bottom: -@tooltip-arrow-width-light;
    }
  }

  &-inner{
    max-width: @tooltip-max-width;
    min-height: 34px;
    padding: 8px 12px;
    color: @tooltip-color;
    text-align: left;
    text-decoration: none;
    background-color: @tooltip-bg;
    border-radius: @border-radius-small;
    box-shadow: @shadow-base;
    white-space: nowrap;

    &-with-width{
      white-space: pre-wrap;
      text-align: justify;
    }
  }

  &-light &-inner{
    background-color: #fff;
    color: @text-color;
  }

  &-arrow{
    position: absolute;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
  }

  &-light {
    .@{tooltip-arrow}{
      &:after{
        display: block;
        width: 0;
        height: 0;
        position: absolute;
        border-color: transparent;
        border-style: solid;
        content: "";
        border-width: @tooltip-arrow-width-light;
      }
      border-width: @tooltip-arrow-outer-width-light;
    }
  }

  &-danger {
    .popper(@tooltip-arrow, @tooltip-arrow-width-light, @tooltip-distance-light, @danger-bg);
    .@{tooltip-prefix-cls}-inner {
      background-color: @danger-bg;
      color: @danger-text;
    }
  }

  &-primary {
    .popper(@tooltip-arrow, @tooltip-arrow-width-light, @tooltip-distance-light, @primary-bg);
    .@{tooltip-prefix-cls}-inner {
      background-color: @primary-bg;
      color: @primary-text;
    }
  }
}
