@import '~view-design/src/styles/index.less';
// Prefix
@css-prefix             : ivu-;
@css-prefix-iconfont    : ivu-icon;

// Color
@primary-color          : #03ac54;
@info-color             : #44D8F5;
@success-color          : #03ac54;
@warning-color          : #fac13c;
@error-color            : #f55654;
@link-color             : #03ac54;
// @link-hover-color       : tint(@link-color, 20%);
@link-hover-color       : tint(@link-color, 20%);
@link-active-color      : shade(@link-color, 65%);
@selected-color         : fade(@primary-color , 90%);
//@tooltip-color          : #6d7f6c;
@subsidiary-color       : #9ea7b4;
@rate-star-color        : #f5a623;
@sub-info-color         : #F59DD8;
@disabled-color         : #999;

// Base
@body-background        : #fff;
@font-family            : "Source Han Sans CN","Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
@code-family            : <PERSON><PERSON><PERSON>,<PERSON><PERSON>,Courier,monospace;
@title-color            : #222127;
@text-color             : #333333;
// @font-size-base         : 14px;
@font-size-base         : 12px;
@font-size-small        : 12px;
@font-size-large        : 14px;
@line-height-base       : 1.5;
@line-height-computed   : floor((@font-size-base * @line-height-base));
@border-radius-base     : 6px;
@border-radius-small    : 4px;
@cursor-disabled        : not-allowed;
@padding-base           : 6px;
// Add Base
@text-sub-color         : #515a6e;
@text-light-color       : #D3CFE6;
// Border color
@border-color-base      : #dcdee2;  // outside
@border-color-split     : #f0f2f0;  // inside

// Background color
@background-color-base        : #fff;  // base
@background-color-select-hover: @input-disabled-bg;
//@tooltip-bg                   : #ebf6ea;
@head-bg                      : #f9fafc;
@table-thead-bg               : #f3f5f3;
@table-td-stripe-bg           : #f9f9f9;
@table-td-hover-bg            : #e6f7ec;
@table-td-highlight-bg        : #f9f9f9;
@menu-dark-active-bg          : #313540;
@date-picker-cell-hover-bg    : #e1f0fe;

// Shadow
@shadow-color           : rgba(242, 240, 250, 1);
@shadow-base            : @shadow-down;
@shadow-card            : 0 1px 1px 0 rgba(242, 240, 250, 1);
@shadow-up              : 0 -1px 6px @shadow-color;
@shadow-down            : 0 2px 6px 0 @shadow-color;
@shadow-left            : -1px 0 6px @shadow-color;
@shadow-right           : 1px 0 6px @shadow-color;

// Button
@btn-font-weight        : 500;
@btn-padding-base       : 6px 15px;
@btn-padding-large      : 7px 15px 8px 15px;
@btn-padding-small      : 2px 15px;
@btn-font-size          : 12px;
@btn-font-size-large    : 13px;
@btn-border-radius      : 4px;
@btn-border-radius-small: 3px;
@btn-group-border       : shade(@primary-color, 5%);

@btn-disable-color      : #888888;
@btn-disable-bg         : @background-color-base;
@btn-disable-border     : @border-color-base;

@btn-default-color      : @text-sub-color;
@btn-default-bg         : @background-color-base;
@btn-default-border     : @border-color-base;

@btn-primary-color      : #fff;
@btn-primary-bg         : @primary-color;

@btn-ghost-color        : @text-color;
@btn-ghost-bg           : transparent;
@btn-ghost-border       : @border-color-base;

@btn-circle-size        : 32px;
@btn-circle-size-large  : 36px;
@btn-circle-size-small  : 24px;

// Layout and Grid
@grid-columns           : 24;
@grid-gutter-width      : 0;

// Legend
@legend-color           : #999;

// Input
@input-height-base           : 32px;
@input-height-large          : 36px;
@input-height-small          : 24px;

@input-padding-horizontal    : 7px;
@input-padding-vertical-base : 4px;
@input-padding-vertical-small: 1px;
@input-padding-vertical-large: 6px;

@input-placeholder-color     : @btn-disable-color;
@input-color                 : @text-color;
//@input-border-color          : @border-color-base;
@input-border-color          : #d8d8d8;
@input-bg                    : #fff;

@input-hover-border-color    : @primary-color;
@input-focus-border-color    : @primary-color;
@input-disabled-bg           : #f3f3f3;

// Tag
@tag-font-size          : 12px;

// Media queries breakpoints
// Extra small screen / phone
@screen-xs              : 480px;
@screen-xs-min          : @screen-xs;
@screen-xs-max          : (@screen-xs-min - 1);

// Small screen / tablet
@screen-sm              : 768px;
@screen-sm-min          : @screen-sm;
@screen-sm-max          : (@screen-sm-min - 1);

// Medium screen / desktop
@screen-md              : 992px;
@screen-md-min          : @screen-md;
@screen-md-max          : (@screen-md-min - 1);

// Large screen / wide desktop
@screen-lg              : 1200px;
@screen-lg-min          : @screen-lg;
@screen-lg-max          : (@screen-lg-min - 1);

// Z-index
@zindex-spin            : 8;
@zindex-affix           : 10;
@zindex-back-top        : 10;
@zindex-select          : 900;
@zindex-modal           : 1000;
@zindex-message         : 1010;
@zindex-notification    : 1010;
@zindex-tooltip         : 1060;
@zindex-loading-bar     : 2000;

// Animation
@animation-time         : .3s;
@transition-time        : .2s;
@ease-in-out            : ease-in-out;

// Slider
@slider-color              : tint(@primary-color, 20%);
@slider-height             : 4px;
@slider-margin             : 16px 0;
@slider-button-wrap-size   : 18px;
@slider-button-wrap-offset : -4px;
@slider-disabled-color     : #ccc;



/* iview 变量替换 */
@font-size-base: 13px;
@primary-color: #03ac54;
@primary-color: #03ac54;
@btn-border-radius: 2px;

@btn-circle-size: 26px;
@input-height-base: 30px;
@btn-height-base: 30px;

@border-color-base: #d8d8d8;
@btn-default-color: #303030;
@input-placeholder-color: #909090;
