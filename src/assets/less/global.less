@import 'variable';
table {
  line-height: 1.35;
}
a {
  color: #303030;
}
:root {
  --primary-color: @theme-color;
  --warning-color: #ff6e00;
  --error-color: #f13130;
  --secondary-text-color: #999;
}
.ivu {
  &-select-dropdown {
    .ivu-cascader-menu {
      .ivu-cascader-menu-item {
        transition: 0.3s all;
      }
      .ivu-cascader-menu-item:hover {
        color: var(--primary-color);
        background: var(--primary-tr-hover);
      }
      .ivu-cascader-menu-item-active {
        color: var(--primary-color);
        font-weight: 500;
        background: var(--primary-tr-hover);
      }
    }
  }
  &-dropdown {
    cursor: pointer;
  }
  &-dropdown-menu {
    .ivu-dropdown-item {
      transition: 0.3s all;
    }
    .ivu-dropdown-item:hover {
      color: var(--primary-color);
      background: var(--primary-tr-hover);
    }
  }
  &-select-dropdown-list {
    .ivu-select-item {
      transition: 0.3s all;
      color: rgba(0, 0, 0, 0.85);
    }
    .ivu-select-item:hover:not(.ivu-select-item-disabled) {
      color: var(--primary-color);
      background: var(--primary-tr-hover);
    }
    .ivu-select-item-selected {
      font-weight: 500;
      color: var(--primary-color);
      background: var(--primary-tr-hover);
    }
  }
}
/* 解决s-notice层级低 */
.s-notice__container {
  z-index: 999999 !important;
}
.flex {
  display: flex;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.flex-con {
  flex: 1;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.clearfix:after {
  content: '';
  clear: both;
}
.float--right {
  float: right;
}
.height--full--hidden {
  height: 100%;
  overflow: hidden;
}
.height--full--scroll {
  height: 100%;
  overflow: auto;
}
.padding {
  padding: @global-gutter;
}
.padding-top {
  padding-top: @global-gutter;
}
.padding-bottom {
  padding-bottom: @global-gutter;
}
.margin-bottom {
  margin-bottom: @global-gutter;
}
.margin-top {
  margin-top: @global-gutter;
}
.margin-left {
  margin-left: @global-gutter;
}
.list-padding-left {
  padding-left: @list-horizontal-gutter;
}
.list-padding-right {
  padding-right: @list-horizontal-gutter;
}
.button-margin-left {
  margin-left: 15px;
}
.spacing {
  margin: @spacing 0;
}
.border-bottom {
  border-bottom: 1px solid #dedede;
}
.border-top {
  border-top: 1px solid #dedede;
}
.border {
  border: 1px solid #dedede;
}
.text-color-secondary {
  color: @text-color-secondary;
}
.text--warning {
  color: #ff9f00;
}
.text--primary {
  color: var(--primary-color);
}
.text--error {
  color: #f13130;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.color-initial {
  color: initial;
}
.hover--primary {
  &:hover {
    color: var(--primary-color);
  }
}
.ml12 {
  margin-left: 12px;
}

.tag14 {
  width: 18px;
  height: 18px;
  display: block;
  padding-left: 1.5px;
  text-align: center;
  line-height: 18px;
  font-size: 13px;
  border-radius: 2px;
  transform: scale(0.78);
  &--warning {
    color: #ff9f00;
    border: 1px solid #ff9f00;
  }
  &--primary {
    color: var(--primary-color);
    border: 1px solid #03ac54;
  }
  &--error {
    color: #f13130;
    border: 1px solid #f13130;
  }
}

// 操作列左侧阴影
.table-fix-op {
  position: relative;
  &:after {
    content: '';
    position: absolute;
    right: 55px;
    top: 0;
    height: 100%;
    display: block;
    width: 10px;
    background: transparent;
    box-shadow: -2px 0 6px -2px rgba(0, 0, 0, 0.2);
  }
}
// 列表表格操作列样式
.op-list {
  span {
    cursor: pointer;
    display: block;
    padding: 5px 15px;
    &:hover {
      color: @theme-color;
    }
  }
}

// 横向滚动表格
.table-scroll-x {
  .ivu-table {
    overflow-x: auto;
    .ivu-table-body {
      overflow: auto !important;
    }
  }
  .body-wrap {
    overflow: visible !important;
  }
}

//.ivu-table-header-resizable {
//  &:after {
//    content: "";
//    position: absolute;
//    height: 50%;
//    width: 1px;
//    background: #ddd;
//    top: 50%;
//    transform: translateY(-50%);
//  }
//}
.common-link {
  color: var(--primary-color);
  cursor: pointer;
}
.common-tag {
  font-weight: bolder;
  border: 1px solid;
  border-radius: 4px;
  padding: 0 5px;
  display: inline-block;
  font-size: 12px;
  margin-left: 5px;
  text-align: center;
  line-height: normal;
  &.tag-red {
    color: @tag-color-red;
  }
  &.tag-green {
    color: @theme-color;
  }
  &.tag-blue {
    color: @tag-color-blue;
  }
}

.table-normal {
  .ivu-table-row {
    .ivu-table-cell {
      overflow: visible !important;
      white-space: normal !important;
    }
  }
}

//带搜索按钮的输入框样式调整
.ivu-input-group-append {
  .ivu-btn {
    margin: 0;
  }
}

// 表格中的复选框大小调整
.ivu-table {
  .ivu-checkbox-inner {
    width: 14px;
    height: 14px;
  }
}

// form-item 错误信息定位调整
.ivu-form-item-error-tip {
  position: relative;
}

// notice 标题支持换行
.ivu-notice-title {
  white-space: normal;
}

.table-row-active {
  td {
    background: #f3f3f3 !important;
  }
}

/*** 面包屑右侧操作按钮 start ***/
.breadcrumb-button-wrap {
  position: fixed;
  top: -(@tab-header-height + @global-gutter + @breadcrumb-height);
  right: 0;
  z-index: 15;
  height: @breadcrumb-height;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  .ivu-btn {
    margin-left: 15px;
  }
}
.fixed-60 {
  .breadcrumb-button-wrap;
  right: 25px;
  top: @nav-height;
}
.tabs-top {
  .breadcrumb-button-wrap;
  position: absolute;
  right: (-@global-gutter + 5);
  width: auto;
}
/*** fixed by transform ***/
.fixed-tabs1 {
  .breadcrumb-button-wrap;
  top: -97px;
  right: 15px;
}
.fixed-tabs2 {
  .breadcrumb-button-wrap;
  top: -96px;
  right: -98%;
}
.fixed-tabs3 {
  .breadcrumb-button-wrap;
  top: -96px;
  right: -198%;
}
.fixed-tabs4 {
  .breadcrumb-button-wrap;
  top: -96px;
  right: -298%;
}
.fixed-pluraltabs {
  .breadcrumb-button-wrap;
  top: -(@tab-header-height * 2 + @global-gutter * 3 + @breadcrumb-height + 31px);
  right: 0;
}
.floatRightBtns {
  .breadcrumb-button-wrap;
  top: @nav-height;
}
/*** fixed by transform ***/
/*** 面包屑右侧操作按钮 end ***/

/*** 修改tabs overflow导致按钮无法fixed start ***/
.ivu-tabs {
  overflow: visible !important;
}
.ivu-tabs-tabpane {
  padding: 0 15px;
}
.ivu-tabs-bar {
  margin-bottom: 0px !important;
}
/*** 修改tabs overflow导致按钮无法fixed end ***/

/*** 页底按钮 end ***/
.fixedBtns {
  position: fixed !important;
  border-top: 1px solid #f5f5f5;
  z-index: 9;
  width: 100%;
  bottom: 0;
  background: #ffffff;
  padding: 10px 20px;
  line-height: 33px;
}
/*** 页底按钮 end ***/

.modal-no-footer {
  .ivu-modal-footer {
    display: none;
  }
}

//客户打点输入提示
.amap-sug-result {
  z-index: 9999;
}

// 公用列表组件分页样式调整
.list-table {
  color: @default-text-color;
  .ivu-page-item {
    min-width: 32px;
    height: 26px;
    line-height: 26px;
    padding: 0;
    font-size: 13px;
    a {
      color: @pagination-text-color;
    }
    &.ivu-page-item-active a {
      color: var(--primary-color);
    }
  }
  .ivu-page {
    padding: 5px 10px;
    .ivu-page-prev,
    .ivu-page-next {
      line-height: 24px !important;
      width: 32px;
    }
  }
  &&--new {
    .ivu-page {
      padding: 0;
    }
  }
  .ivu-page-options .ivu-select-selection {
    height: 26px;
  }
  .ivu-page-options .ivu-select-selection .ivu-select-selected-value {
    font-size: 13px;
    height: 26px;
    line-height: 26px;
  }
  .ivu-page .ivu-select-item {
    font-size: 13px;
  }
}

/* 分包init-sdp时遗漏的ivu-page样式 */
.ivu-page {
  font-size: 13px;
  .ivu-page-item,
  .ivu-page-prev,
  .ivu-page-next,
  .ivu-page-item-jump-prev,
  .ivu-page-item-jump-next,
  .ivu-select-single .ivu-select-selection .ivu-select-selected-value {
    height: 26px;
    line-height: 24px;
    vertical-align: top;
  }
  .ivu-select-single .ivu-select-selection {
    height: 26px;
  }
  .ivu-page-options {
    margin-left: 4px;
  }
  .ivu-page-total {
    height: 26px;
    line-height: 26px;
  }
  .ivu-page-options {
    vertical-align: top;
  }
}

.ivu-page {
  &-options-elevator {
    input {
      position: relative;
      top: -1px;
      height: 26px;
    }
  }
}

/* 动画 */
@ease-out-circ: cubic-bezier(0.08, 0.82, 0.17, 1);
@wave-animation-width: 6px;
@keyframes loadingCircle {
  100% {
    transform: rotate(360deg);
  }
}
.click-pulse {
  position: relative;

  &::after {
    position: absolute;
    top: -1px;
    right: -1px;
    bottom: -1px;
    left: -1px;
    display: block;
    border: 0 solid var(--primary-color);
    border-radius: inherit;
    opacity: 0.2;
    animation:
      fadeEffect 2s @ease-out-circ,
      waveEffect 0.4s @ease-out-circ;
    animation-fill-mode: forwards;
    pointer-events: none;
    content: '';
  }
}
@keyframes waveEffect {
  100% {
    top: -@wave-animation-width;
    right: -@wave-animation-width;
    bottom: -@wave-animation-width;
    left: -@wave-animation-width;
    border-width: @wave-animation-width;
  }
}
@keyframes fadeEffect {
  100% {
    opacity: 0;
  }
}

// 商品列表样式
.goods__list {
  .sdp-table__tr .goods-name {
    color: var(--primary-color);
    cursor: pointer;
  }
  .ivu-tabs-bar {
    margin-bottom: 24px !important;
  }
}

// 登录页样式调整
.loginWrap {
  .ivu-input {
    &:hover {
      border-color: var(--common-primary-color);
    }
    &:focus {
      border-color: var(--common-primary-color);
      box-shadow: 0 0 0 2px rgba(3, 172, 84, 0.2);
    }
  }
}
.highlight {
  color: var(--primary-color);
  cursor: pointer;
}
.hover-highlight {
  &:hover {
    color: var(--primary-color);
  }
  &__click {
    cursor: pointer;
    &:hover {
      color: var(--primary-color);
    }
  }
}
// table列的超出显示poptip 样式修改
.table-column-poptip {
  min-width: 0;
  &:not(.white-popper) .ivu-poptip-body-content,
  &:not(.white-popper) .ivu-poptip-body-content-inner {
    color: #ffffff;
  }
  &.white-popper .ivu-poptip-body-content {
    color: rgba(0, 0, 0, 0.85);
  }
  &:not(.white-popper) .ivu-poptip-inner {
    background-color: rgba(0, 0, 0, 0.7);
  }
  &:not(.white-popper) .ivu-poptip-arrow:after {
    border-bottom-color: rgba(0, 0, 0, 0.7) !important;
  }
}
.alert-text-container {
  position: relative;
  margin-top: 44px;
}
// 解决$smessage 组件被modal覆盖的问题
.s-message__container {
  z-index: 9999 !important;
}

// 弹窗统一修改padding间距
.ivu-modal-header {
  padding: 0 24px;
  height: 42px;
  line-height: 42px;
  display: flex;
  align-items: center;
}
.ivu-modal-close {
  top: 5.5px;
  right: 14px;
}

.ivu-modal .ivu-modal-header-inner {
  display: flex;
  align-items: center;
  &:before {
    margin-bottom: 0;
    height: 13px;
    margin-left: 0;
    vertical-align: middle;
  }
}

.ivu-modal-footer {
  padding: 14.5px 24px;
}
.tooltip_custom {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}
.ivu-radio-inner {
  border: 1px solid #b2b2b2;
}
.ivu-checkbox-inner {
  border: 1px solid #b2b2b2;
}
.ivu-auto-complete.ivu-select-dropdown {
  max-height: 200px;
}
.ivu-select-dropdown-list {
  max-width: 500px;
}
.ivu-select-item {
  padding-right: 28px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.ivu-select-multiple {
  .ivu-select-placeholder {
    padding-left: 10px !important;
  }
  .ivu-select-input {
    padding-left: 10px;
    padding-right: 24px;
    top: 0px;
  }
  .ivu-select-selection {
    padding: 0;
  }
}
.s-modal__title {
  align-items: center;
}
.s-filter-table__table-head {
  height: 48px !important;
  line-height: 48px;
}
.ivu-btn-icon-only {
  padding: 0 !important;
}
.ivu-btn {
  padding: 0 15px;
}
.ivu-input-group-append .ivu-btn {
  margin: 0;
  padding: 0;
}
.order-list-tag-box {
  div,
  i {
    margin-right: 4px;
  }
}

.img-upload-list {
  display: flex;
  flex-wrap: wrap;
  .demo-upload-list {
    position: relative;
    display: inline-block;
    width: 90px;
    height: 90px;
    line-height: 90px;
    margin: 10px 16px 0 0;
    padding: 2px;
    overflow: visible;
    border: 1px solid rgba(216, 216, 216, 0.8);
    border-radius: 2px;
    box-shadow: none;
    &-cover {
      position: absolute;
      left: -1px;
      right: -1px;
      top: -1px;
      bottom: -1px;
      visibility: hidden;
      opacity: 0;
      background: rgba(0, 0, 0, 0.3);
    }
    .delete {
      position: absolute;
      top: -8px;
      right: -8px;
      color: #b2b2b2;
      background: #fff;
      border-radius: 50%;
      cursor: pointer;
      z-index: 255;
    }
    .img {
      width: 100%;
      height: 100%;
      overflow: hidden;
      & img {
        position: relative;
        top: 50%;
        width: 100%;
        height: auto;
        vertical-align: top;
        transform: translateY(-50%);
      }
    }
    &.show-cover .demo-upload-list-cover,
    &:hover .demo-upload-list-cover {
      visibility: visible;
      opacity: 1;
    }
    .view-icon {
      position: absolute;
      right: 12px;
      bottom: 20px;
      color: #fff;
      font-size: 22px;
      cursor: pointer;
    }
    .drag {
      position: absolute;
      left: 12px;
      bottom: 18px;
      width: 24px;
      height: 25px;
      cursor: move;
    }
  }
  .change-upload-type {
    position: relative;
    display: inline-block;
    width: 90px;
    height: 90px;
    padding: 2px;
    text-align: center;
    border: 1px solid rgba(216, 216, 216, 0.8);
    border-radius: 1px;
    cursor: pointer;
    &:hover {
      border: 1px dashed #03ac54;
    }
  }
  .load_box {
    position: relative;
    height: 100%;
    width: 100%;
    background: #f6f8f9;
  }
  .load_img {
    position: absolute;
    top: calc(50% - 10px);
    left: 50%;
    transform: translate(-50%, -50%);
  }
  .load_text {
    position: absolute;
    top: calc(50% + 20px);
    left: 50%;
    transform: translate(-50%, -50%);
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
  }
  .ivu-upload-drag {
    height: 90px;
    border-radius: 2px;
    border: none;
  }
}
.static-modal {
  & .ivu-modal-mask,
  & .ivu-modal-wrap {
    z-index: 1000 !important;
  }
}
.static-modal-200 {
  & .ivu-modal-mask,
  & .ivu-modal-wrap {
    z-index: 200 !important;
  }
}
.s-drawer__wrapper:not(.show) {
  display: none;
}
.icon--new {
  font-size: 16px;
  margin-top: -2px;
  margin-right: 5px;
  color: #03ac54;
}
.ivu-select-multiple {
  .ivu-tag {
    font-size: 12px;
    line-height: 1;
    margin-left: 6px;
    background: rgba(243, 243, 243, 0.7);
    color: rgba(0, 0, 0, 0.65);
    border-radius: 2px;
    border: 1px solid #d8d8d8;
    width: 60px;
    height: 20px;
    padding: 3px 4px 4px;
    margin-top: 4px;
    .ivu-tag-text {
    }
    .ivu-icon {
      font-size: 12px;
    }
  }
}
.dot-title {
  padding-left: 6px;
  position: relative;
}
.dot-title::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: black;
}
.import-explain {
  padding: 12px 20px 16px 20px;
  margin: 20px 24px;
  background: #f5f6f8;
  color: rgba(0, 0, 0, 0.5);
  border-radius: 2px;
  font-size: 12px;
  h6 {
    color: rgba(0, 0, 0, 0.7);
    font-weight: 400;
    margin-bottom: 2px;
    line-height: 16px;
  }
  p {
    padding-top: 2px;
  }
}
.import-mode-box {
  display: flex;
  align-items: center;
  .label {
    width: 112px;
    text-align: right;
  }
}

.commodity-select.ivu-select-dropdown {
  max-height: 260px !important;
  box-shadow: 0px 2px 14px 1px rgba(0, 0, 0, 0.15);
}
.commodity-select-table {
  &.ivu-select-dropdown {
    max-height: 280px !important;
    box-shadow: 0px 2px 14px 1px rgba(0, 0, 0, 0.15);
    padding: 0;
  }
  &.sdp-table__th {
    background-color: #f2f4f6 ;
  }
}
.el-cascader__dropdown {
  .in-active-path {
    color: #03ac54 !important;
  }
}
.el-checkbox__input.is-indeterminate {
  .el-checkbox__inner {
    border-color: #03ac54 !important;
    background-color: #03ac54 !important;
  }
}
.el-checkbox__inner:hover {
  border-color: #03ac54 !important;
}
.el-checkbox__input.is-checked {
  .el-checkbox__inner {
    border-color: #03ac54 !important;
    background-color: #03ac54 !important;
  }
}
.el-cascader-node.is-active {
  color: #03ac54 !important;
}
.el-cascader__suggestion-item.is-checked {
  color: #03ac54 !important;
}
// 解决 iView 组件库 DatePicker 组件库 clearable: false 还是显示了清空按钮 的bug
.no-clearable {
  .ivu-picker-confirm {
    .ivu-btn:nth-of-type(2) {
      display: none;
    }
  }
}

.switch-new {
  width: 42px;
  height: 24px;
  &:after {
    width: 20px;
    height: 20px;
  }
  &.ivu-switch-checked:after {
    left: 19px;
  }
}

.fullscreen-container {
  .editable-table {
    .sdp-table__container {
      margin-top: 12px !important;
    }
  }
  .s-vxe-editable-table {
    margin-top: 12px !important;
    .s-vxe-table {
      margin-top: 12px !important;
    }
  }
}

.sui-title-config__dialog {
  z-index: 100000 !important;
}

.ivu-select-dropdown-list .ivu-select-item-disabled {
  color: #888;
}

.price-type-tooltip {
  .ivu-tooltip-inner {
    white-space: normal !important;
  }
}

// modal设置高度, 滚动条美化且贴近右边
.height-modal-scroll {
  .ivu-modal-body {
    padding: 0;
    > * {
      padding: 20px 24px 30px;
    }
    // padding-top: 0;
    // padding-right: 0;
  }
  .scroll-wrap {
    // padding-top: 20px;
    // padding-right: 24px;
    &::-webkit-scrollbar {
      width: 10px;
      height: 10px;
      display: block;
      position: absolute;
    }
    &::-webkit-scrollbar-thumb {
      box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.3);
      border: 2px solid transparent;
      border-radius: 10px;
    }
  }
}

.print-drawer {
  .ivu-drawer-body {
    height: calc(~"100% - 150px");
    overflow-y: auto;
    overflow-x: hidden;
  }
}

.line-clamp-2 {
  display: -webkit-box;      /* 设置弹性盒子模型 */
  -webkit-line-clamp: 2;     /* 限制显示2行 */
  -webkit-box-orient: vertical; /* 垂直方向排列 */
  overflow: hidden;          /* 隐藏溢出内容 */
  text-overflow: ellipsis;   /* 超出部分显示省略号 */
}