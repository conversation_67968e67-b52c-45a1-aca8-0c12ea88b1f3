/**
 * 基础版主题
 */
@shadow-color: rgba(20, 104, 255, 0.1);
.basic-container {
  // 输入框focus阴影
  .ivu-input:focus {
    box-shadow: 0 0 0 2px @shadow-color;
  }
  // 下拉框focus阴影
  .ivu-select-visible {
    .ivu-select-selection {
      box-shadow: 0 0 0 2px @shadow-color;
    }
  }
  .ivu-radio-group-button {
    .ivu-radio-wrapper-checked.ivu-radio-focus {
      box-shadow: 0 0 0 2px @shadow-color;
    }
  }
  .ivu-radio-wrapper-checked.ivu-radio-focus {
    box-shadow: 0 0 0 2px @shadow-color;
  }
  // 下拉选项hover
  .ivu-select {
    .ivu-select-item:hover {
      background: var(--primary-tr-hover) !important; // 其他地方选择器层级太深
    }
  }
  // 级联选项hover
  .ivu-cascader {
    .ivu-cascader-menu-item:hover {
      background: var(--primary-tr-hover) !important;
    }
  }
}
