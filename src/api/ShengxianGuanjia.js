import {get,post} from './request';
import {api} from './api';

class ShengxianGuanjia {

  constructor () {
  }

  /**
   * 获取公众号二维码
   * @param params
   */
  getQrCode (params) {
    return get(api.shengxianGuanjia.qrcode, params);
  }

  /**
   * 获得 union_id
   * @param params
   */
  getUnionId (params) {
    return get(api.shengxianGuanjia.wechatUser.getUnionId, params);
  }

  /**
   * Ajax 轮询获得用户关注信息
   * @param params
   */
  loopUserSatus (params) {
    return get(api.shengxianGuanjia.wechatUser.loopUserStatus, params);
  }

  /**
   * 保存小程序账号注册信息
   * @param params
   */
  setUser (params) {
    return post(api.shengxianGuanjia.wechatUser.setUser, params);
  }

  /**
   * 获取短信验证码
   * @param params
   */
  sendSmsCode (params) {
    return post(api.shengxianGuanjia.sms.send, params);
  }

  /**
   * 检查短信验证码
   * @param params
   */
  checkSmsCode (params) {
    return post(api.shengxianGuanjia.sms.check, params);
  }

}

// 小程序二维码
ShengxianGuanjia.miniAppQrcode = 'https://img.sdongpo.com/base/shengxianguanjia-mini-app-qrcode.jpg';
// 已有账号
ShengxianGuanjia.hasAccount = 200;
// 未关注，提示关注
ShengxianGuanjia.unSubscribe = 501;
// 可以弹出完善信息窗口
ShengxianGuanjia.showMessage = 502;

export default ShengxianGuanjia;
