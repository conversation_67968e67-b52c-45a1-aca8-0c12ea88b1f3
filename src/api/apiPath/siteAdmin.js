/**
 * Created by ddcoder on 2019/6/3.
 */
let api = {}

api.goods = {
  search: 'commodity/ajaxGetCommodity',
  list: 'commodity/ajaxList',
  skuList: 'commodity/GetCommoditySkuList',
  save: 'commodity/saveCommodity',
  detail: 'commodity/getCommodityDetail',
  commonData: 'commodity/getInitData',
  delete: 'commodity/deleteCommoditys',
  editOnline: 'commodity/editOnline',
  modifyStock: 'commodity/ModifyStock',
  exportExcel: 'commodity/exportExcel',
  genCode: 'commodity/getCommodityCode',
  imgData: 'commodity/imgData',
  getImg: 'commodity/getCommodityImg',
  deleteImg: 'commodity/delImg'
}

api.goodsCategory = {
  list: 'commodity/getGoodsCategory',
  add: 'category/add',
  edit: 'category/edit',
  delete: 'category/delete',
  tree: 'category/tree'
}

api.site = {
  list: 'site/list'
}

api.common = {
  switchSite: 'frame/switchSite'
}

const formatModuleApi = (module, api) => {
  let result = {}
  module = '/' + module
  for (let subModule in api) {
    if (!result[subModule]) {
      result[subModule] = {}
    }
    for (let apiKey in api[subModule]) {
      let apiPath = api[subModule][apiKey]
      result[subModule][apiKey] = apiPath.startsWith('/') ? module + apiPath : `${module}/${apiPath}`
    }
  }
  return result
}

api = formatModuleApi('siteAdmin', api)

export default api
