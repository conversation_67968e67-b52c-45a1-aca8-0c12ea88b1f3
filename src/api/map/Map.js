/**
 * Created by ddcoder on 19/4/28.
 */

import util from '../util';

const mapType = util.mapType;

class Map {

  /**
   * @param {type} options 地图类型amap: 高德地图，google: 谷歌地图
   */
  constructor(options) {
    this.options = options;
    this.version = '';
    this.key = '';
    this.type = '';
    this.callback = 'initMap';
    this.map = null;
    this.isGoogleMap = false;
    this.isAmap = false;
    this.markers = [];
  }

  /**
   * @param type 地图类型amap: 高德地图，google: 谷歌地图
   * @param callback 回调函数名称，需要在全局注册该回调函数
   */
  use(type, callback) {
    this.type = type;
    this.callback = callback;
    let mapConfig = this.options[type];
    if (!type) {
      this.error('地图配置错误！');
      return this;
    }
    this.isGoogleMap = type === mapType.google;
    this.isAmap = type === mapType.amap;
    this.key = mapConfig.key;
    this.version = mapConfig.version;
    this.load();
    return this;
  }

  /**
   * 加载地图js
   */
  load() {
    // 已加载谷歌地图
    if (this.isGoogleMap && window.google && window.google.maps) {
      // 触发地图加载完的回调
      window[this.callback] && window[this.callback]();
      return this;
    }
    if (this.isAmap && window.AMap) {
      // 触发地图加载完的回调
      window[this.callback] && window[this.callback]();
      return false;
    }
    const url = this._getMapUrl();
    console.log(url, 888 + '')
    const jsApi = document.createElement('script');
    jsApi.charset = 'utf-8';
    jsApi.src = url;
    document.head.appendChild(jsApi);
    return this;
  }

  /**
   * 初始化地图
   * @param {selector} options dom选择器目前只支持id
   * @param {zoom} options 缩放级别
   * @param {center} options 地图中心
   */
  initMap(options) {
    let { center } = options;
    switch (this.type) {
      case mapType.amap:
        let amapOpts = {
          zoom: options.zoom ? options.zoom : 14
        };
        if (center) {
          amapOpts.center = [center.lng, center.lat];
        }
        this.map = new AMap.Map(options.selector, amapOpts);
        break;
      case mapType.google:
        let googleMapOpts = {
          panControl:true,
          zoomControl:true,
          mapTypeControl:true,
          scaleControl:true,
          streetViewControl:true,
          overviewMapControl:true,
          rotateControl:true,
          mapTypeId: google.maps.MapTypeId.ROADMAP,
          zoomControlOptions: {
            style:google.maps.ZoomControlStyle.SMALL
          },
          zoom: options.zoom ? options.zoom : 14,
        };
        if (options.center) {
          googleMapOpts.center = options.center;
        }
        this.map = new google.maps.Map(document.getElementById(options.selector), googleMapOpts);
        break;
      default:
        break;
    }
    return this;
  }

  clearMap() {
    if (!this.map) {
      return false;
    }
    switch (this.type) {
      case mapType.amap:
        this.map.clearMap();
        break;
      case mapType.google:
        // this.map.clearOverlays();
        this.markers.forEach((marker) => {
          if (!marker) {
            return false;
          }
          marker.setMap(null);
        });
        break;
      default:
        break;
    }
    this.markers = [];
  }

  /**
   * 地图事件代理
   * @param eventName 事件名称
   * @param callback 回调函数
   */
  on(eventName, callback) {
    switch (this.type) {
      case mapType.amap:
        this.map.on(eventName, (...args) => {
          callback && callback.apply(this, args);
        });
        break;
      case mapType.google:
        this.map.addListener(eventName, (...args) => {
          callback && callback.apply(this, args);
        });
        break;
      default:
        break;
    }
  }

  /**
   * 缩放地图事件
   * @param callback 回调函数
   */
  onZoomChange(callback) {
    switch (this.type) {
      case mapType.amap:
        this.on('zoomchange', callback);
        break;
      case mapType.google:
        break;
      default:
        break;
    }
  }

  onMoveEnd(callback) {
    switch (this.type) {
      case mapType.amap:
        this.on('moveend', callback);
        break;
      case mapType.google:
        break;
      default:
        break;
    }
  }

  /**
   * 地图点击事件
   * @param callback 回调函数
   */
  onClick(callback) {
    switch (this.type) {
      case mapType.amap:
        this.map.on('click', (event) => {
          let position = {
            lat: event.lnglat.getLat(),
            lng: event.lnglat.getLng()
          };
          callback && callback(position, event);
        });
        break;
      case mapType.google:
        this.map.addListener('click', (event) => {
          let position = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
          };
          callback && callback(position, event);
        });
        break;
      default:
        break;
    }
  }

  /**
   * 定位
   */
  location(callback) {
    switch (this.type) {
      case mapType.amap:
        this.amapLocation(callback);
        break;
      case mapType.google:
        this.googleGeolocation(callback);
        break;
      default:
        break;
    }
  }

  /**
   * 高德地图定位
   * @param callback
   */
  amapLocation(callback) {
    /***************************************
     由于Chrome、IOS10等已不再支持非安全域的浏览器定位请求，为保证定位成功率和精度，请尽快升级您的站点到HTTPS。
     ***************************************/
    let map, geolocation;
    //加载地图，调用浏览器定位服务
    map = this.map;
    let self = this;
    //解析定位结果
    const onComplete = (data) => {
      let position = {
        lat: data.position.getLat(),
        lng: data.position.getLng()
      };
      callback && callback(position);
    };
    //解析定位错误信息
    const onError = (data) => {
      this.amapGetLocalCity()
    };
    map.plugin('AMap.Geolocation', function() {
      geolocation = new AMap.Geolocation({
        enableHighAccuracy: true,//是否使用高精度定位，默认:true
        timeout: 20 * 1000,          //超过10秒后停止定位，默认：无穷大
        buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
        zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
        buttonPosition:'RB'
      });
      map.addControl(geolocation);
      geolocation.getCurrentPosition();
      AMap.event.addListener(geolocation, 'complete', onComplete);//返回定位信息
      AMap.event.addListener(geolocation, 'error', onError);      //返回定位出错信息
    });
  }

  /**
   * @description: 高德地图IP定位获取当前城市信息
   * 如果不需要获取精确的位置，只需要城市级别的定位信息，推荐使用AMap.CitySearch插件
   */
  amapGetLocalCity (callback) {
    let map = this.map
    //实例化城市查询类
    let citysearch = new AMap.CitySearch();
    //自动获取用户IP，返回当前城市
    citysearch.getLocalCity(function(status, result) {
      if (status === 'complete' && result.info === 'OK') {
        if (result && result.city && result.bounds) {
          let citybounds = result.bounds;
          //地图显示当前城市
          map && map.setBounds(citybounds);
          callback && callback(result)
        } else {
          callback && callback()
        }
      } else {
        callback && callback()
        console.error("定位失败,status:" + status + ", error:" + JSON.stringify(result))
      }
    });
  }
  /**
   * 谷歌地图定位
   */
  googleGeolocation(callback) {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(position => { // 获取当前坐标
        let currentLoc = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        callback && callback(currentLoc);
      }, (err) => {
        this.error('获取位置失败');
      });
    } else {
      this.error('浏览器不支持定位');
    }
  }

  /**
   * @param {lat} position 经度
   * @param {lng} position 纬度
   */
  setCenter(position) {
    switch (this.type) {
      case mapType.amap:
        this.map.setCenter([position.lng, position.lat]);
        break;
      case mapType.google:
        this.map.setCenter(position);
        break;
      default:
        break;
    }
  }

  /**
   * @param {url} options marker图标
   * @param {title} options marker标题
   * @param {postion} options marker位置
   * @param {icon} options 地图icon
   * @param {offset} options 地图偏移
   */
  setMarker(options) {
    let marker = null;
    switch (this.type) {
      case mapType.amap:
        marker = this.setAmapMarker(options);
        break;
      case mapType.google:
        marker = this.setGoogleMarker(options);
        break;
      default:
        break;
    }
    this.markers.push(marker);
    return marker;
  }

  clearMarkers() {
    if (!this.markers) {
      return false;
    }
    switch (this.type) {
      case mapType.amap:
        if (this.map.getAllOverlays('marker')) {
          this.map.remove(this.map.getAllOverlays('marker'));
        }
        break;
      case mapType.google:
        // this.map.clearOverlays();
        this.markers.forEach((marker) => {
          if (!marker) {
            return false;
          }
          marker.setMap(null);
        });
        break;
      default:
        break;
    }
  }

  /**
   * 设置高德地图marker
   */
  setAmapMarker(options) {
    const defaultIconSize = [25, 25];
    const defaultImageSize = [25, 25];
    const defaultOffset = [-18, -33];
    let { url, title, position, icon, offset } = options;
    let markerOpts = {
      map: this.map,
      position: [position.lng, position.lat],
      offset: new AMap.Pixel(-18, -33)
    };
    if (url) {
      markerOpts.icon = new AMap.Icon({
        image: url,
        size: new AMap.Size(25, 25),
        imageSize: new AMap.Size(25, 25)
      });
    }
    if (icon) {
      if (!icon.size || !Array.isArray(icon.size)) {
        icon.size = defaultIconSize;
      }
      if (!icon.imageSize || !Array.isArray(icon.imageSize)) {
        icon.imageSize = defaultImageSize;
      }
      markerOpts.icon = new AMap.Icon({
        image: icon.image,
        size: new AMap.Size(icon.size[0], icon.size[1]),
        imageSize: new AMap.Size(icon.imageSize[0], icon.imageSize[1])
      });
    }
    if (offset) {
      if (!Array.isArray(offset)) {
        offset = defaultOffset;
      }
      markerOpts.offset = new AMap.Pixel(offset[0], offset[1]);
    }
    return new AMap.Marker(markerOpts);
  }

  /**
   * 设置谷歌地图marker
   * @param {url} options marker图标
   * @param {title} options marker标题
   * @param {postion} options marker位置
   */
  setGoogleMarker(options) {
    let { url, title, position, icon, offset } = options;
    let markerOpts = {
      map: this.map,
      title: '',
      position: position,
    };
    if (options.title) {
      markerOpts.title = options.title;
    }
    if (icon) {
      let iconOpts = {
        url: icon.image,
        size: new google.maps.Size(71, 71),
        origin: new google.maps.Point(-17, -32),
        // anchor: new google.maps.Point(17, 34),
        scaledSize: new google.maps.Size(40, 40)
      };
      markerOpts.icon = iconOpts;
    }
    return new google.maps.Marker(markerOpts);
  }

  /**
   * 设置maker位置
   * @param marker
   * @param position
   */
  setMarkerPosition(marker, position) {
    let newPosition = null;
    switch (this.type) {
      case mapType.amap:
        newPosition = [position.lng, position.lat];
        break;
      case mapType.google:
        newPosition = position;
        break;
      default:
        break;
    }
    marker.setPosition(newPosition);
  }

  /**
   * 设置marker title
   * @param marker
   * @param title
   */
  setMarkerTitle(marker, title) {
    switch (this.type) {
      case mapType.amap:
        marker.setTitle(title);
        break;
      case mapType.google:
        marker.setTitle(title);
        break;
      default:
        break;
    }
  }

  /**
   * marker点击事件
   * @param marker
   * @param callback
   */
  onClickMarker(marker, callback) {
    switch (this.type) {
      case mapType.amap:
        marker.on('click', (...args) => {
          callback && callback.apply(args);
        });
        break;
      case mapType.google:
        break;
      default:
        break;
    }
  }

  bounceMarker(marker, duration = 1000 * 3) {
    let animationName = '';
    let stopAnimation = null;
    switch (this.type) {
      case mapType.amap:
        animationName = 'AMAP_ANIMATION_BOUNCE';
        stopAnimation = 'AMAP_ANIMATION_NONE';
        break;
      case mapType.google:
        animationName = google.maps.Animation.BOUNCE;
        break;
      default:
        break;
    }
    marker.setAnimation(animationName);
    setTimeout(() => {
      marker.setAnimation(stopAnimation);
    }, duration);
  }

  /**
   * @param {selector} options 输入框id
   * @param {onClickMarker} options 点击marker回调
   */
  autoComplete(options) {
    switch (this.type) {
      case mapType.amap:
        this.amapAutoComplete(options);
        break;
      case mapType.google:
        this.googleAutoComplete(options);
        break;
      default:
        break;
    }
  }

  amapAutoComplete(options) {
    let { selector, onClickMarker } = options;
    let autoOptions = {
      input: selector
    };
    let auto = new AMap.Autocomplete(autoOptions);
    let placeSearch = new AMap.PlaceSearch({
      map: this.map
    });  //构造地点查询类
    const select = (e) => {
      placeSearch.setCity(e.poi.adcode);
      placeSearch.search(e.poi.name);  //关键字查询查询
      setTimeout(() => {
        // let markers = this.map.getAllOverlays('marker');
        let markers = this.map.getAllOverlays();
        if (!markers) {
          return false;
        }
        markers.forEach((marker) => {
          marker.on('click', (e) => {
            let lnglat = e.lnglat;
            let position = {
              lat: lnglat.lat,
              lng:lnglat.lng
            };
            onClickMarker && onClickMarker(position, marker, e);
          });
        });
      }, 500);
    };
    AMap.event.addListener(auto, "select", select); //注册监听，当选中某条记录时会触发
  }

  /**
   * 谷歌地图地区搜索
   * @param selector
   */
  googleAutoComplete(options) {
    let map = this.map;
    let { selector, onClickMarker } = options;
    // Create the search box and link it to the UI element.
    let input = document.getElementById(selector);
    let searchBox = new google.maps.places.SearchBox(input);
    map.controls[google.maps.ControlPosition.TOP_LEFT].push(input);

    // Bias the SearchBox results towards current map's viewport.
    map.addListener('bounds_changed', function() {
      searchBox.setBounds(map.getBounds());
    });

    let markers = [];
    // Listen for the event fired when the user selects a prediction and retrieve
    // more details for that place.
    searchBox.addListener('places_changed', function() {
      let places = searchBox.getPlaces();

      if (places.length === 0) {
        return;
      }

      // Clear out the old markers.
      markers.forEach(function(marker) {
        marker.setMap(null);
      });
      markers = [];

      // For each place, get the icon, name and location.
      let bounds = new google.maps.LatLngBounds();
      places.forEach(function(place) {
        if (!place.geometry) {
          console.log("Returned place contains no geometry");
          return;
        }
        let icon = {
          url: place.icon,
          size: new google.maps.Size(71, 71),
          origin: new google.maps.Point(0, 0),
          anchor: new google.maps.Point(17, 34),
          scaledSize: new google.maps.Size(25, 25)
        };

        // Create a marker for each place.
        let marker = new google.maps.Marker({
          map: map,
          icon: icon,
          title: place.name,
          position: place.geometry.location
        });
        marker.addListener('click', (event) => {
          let position = {
            lat: event.latLng.lat(),
            lng: event.latLng.lng()
          };
          onClickMarker && onClickMarker(position, marker, event);
        });
        markers.push(marker);

        if (place.geometry.viewport) {
          // Only geocodes have viewport.
          bounds.union(place.geometry.viewport);
        } else {
          bounds.extend(place.geometry.location);
        }
      });
      map.fitBounds(bounds);
    });
  }

  /**
   * @param {path} options 路径点数组[{lat: '', lng}]
   */
  drawPath(options) {
    switch (this.type) {
      case mapType.amap:
        this.drawAmapPath(options);
        break;
      case mapType.google:
        this.drawGooglePath(options);
        break;
      default:
        break;
    }
  }

  drawAmapPath(options) {
    let { path } = options;
    let lineArr = path.map((item) => {
      return [item.lng, item.lat]
    });
    if (!this.map) {
      return false;
    }
    new AMap.Polyline({
      map: this.map,
      path: lineArr,
      showDir:true,
      strokeColor: "#f5af08",  //线颜色
      // strokeOpacity: 1,     //线透明度
      strokeWeight: 6,      //线宽
      // strokeStyle: "solid"  //线样式
    });
    this.map.setFitView();
  }

  drawGooglePath(options) {
    let { path } = options;
    if (!path || !Array.isArray(path) || path.length === 0) {
      return false;
    }
    let flightPath = new google.maps.Polyline({
      path: path,
      geodesic: true,
      strokeColor: '#f5af08',
      // strokeOpacity: 1.0,
      strokeWeight: 6
    });
    flightPath.setMap(this.map);
    /*
    let bounds = new google.maps.LatLngBounds();
    path.forEach((position) => {
      bounds.extend(position);
    });
    this.map.fitBounds(bounds);
    */
  }

  _getMapUrl() {
    let url = '';
    switch (this.type) {
      case mapType.amap:
        url = this._getAmapUrl();
        break;
      case mapType.google:
        url = this._getGoogleMapUrl();
        break;
      default:
        break;
    }
    if (!url) {
      this.error('地图url错误！');
    }
    return url;
  }

  _getAmapUrl() {
    const url = `https://webapi.amap.com/maps?v=${this.version}&key=${this.key}&callback=${this.callback}&plugin=AMap.Autocomplete,AMap.PlaceSearch,AMap.CitySearch,AMap.MouseTool,AMap.PolyEditor`;
    return url;
  }

  _getGoogleMapUrl() {
    const url = `https://maps.googleapis.com/maps/api/js?key=${this.key}&libraries=places&callback=${this.callback}`;
    return url;
  }

  error() {
    console.error(message);
  }

}

export default Map;
