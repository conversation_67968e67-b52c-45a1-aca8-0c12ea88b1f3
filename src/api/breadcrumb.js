/**
 * Created by ddcoder on 19/2/13.
 */
const breadcrumbTree = [
  /*
  {
    name: '订单',
    path: '/order?module=order',
    icon: '',
    children: [
      {
        name: '订单列表',
        path: '/order',
        icon: '',
        children: [
          {
            name: '订单新增',
            path: '/newOrder',
            icon: '',
          },
          {
            name: '订单编辑',
            path: '/orderEdit',
            icon: '',
          }
        ]
      },
      {
        name: '订单汇总',
        path: '/batchSummary',
        icon: '',
      },
      {
        name: '批量核价',
        path: '/orderChangePrice/new?module=checkPrice',
        icon: '',
        children: [
          {
            name: '批量核价',
            path: '/orderChangePrice/new',
            icon: '',
          },
          {
            name: '改价纪录',
            path: '/orderChangePrice/record',
            icon: '',
          }
        ]
      },
    ]
  },
  {
    name: '商品',
    path: '/commodityList/baseCommodity?module=commodity',
    icon: '',
    children: [
      {
        name: '商品档案',
        path: '/commodityList/baseCommodity',
        icon: '',
        children: [
          {
            name: '商品新增',
            path: '/commodityList/newCommodity',
            icon: '',
          },
        ]
      },
    ]
  },
  {
    name: '客户',
    path: '/userList',
    icon: '',
    children: [
      {
        name: '客户档案',
        path: '/order',
        icon: '',
        children: [
          {
            name: '客户新增/编辑',
            path: '/baseInfo',
            icon: '',
          }
        ]
      },
      {
        name: '客户类型',
        path: '/userType',
        icon: '',
      }
    ]
  }
  */
];

/**
 * 根据路由地址获取获取面包屑路径
 * @param tree 树结构数据 todo 目前要求是数组结构
 * @param nodeValue 要查找的节点值
 * @param nodeKey 要查找的节点key
 * @param childrenKey 树形结构子节点的key
 */
let getTreePath = (tree, nodeValue, nodeKey = 'path', childrenKey = 'children') => {
  // 定义变量保存路径
  let path = [];
  try {
    // 获取节点路径
    let getNodePath = (node) => {
      let pathNode = {...node};
      delete pathNode[childrenKey];
      path.push(pathNode);
      // 找到节点, 通过throw终止递归
      if (node[nodeKey] === nodeValue) {
        throw (`get node ${nodeValue}`);
      }
      if (node[childrenKey] && node[childrenKey].length > 0) {
        node[childrenKey].forEach((childNode) => {
          getNodePath(childNode);
        });
        // 遍历完当前节点的所有子节点依然没有找到, 择删除路径中的该节点
        path.pop();
      } else {
        // 找到叶子节点仍然不是要找的节点时删除路径中的该节点
        path.pop();
      }
    };
    tree.forEach((item) => {
      getNodePath(item)
    });
  } catch (e) {
    return path;
  }
  return path;
};

/**
 * 根据路由路径获取当前的面包屑
 * @param currentRoutePath
 */
export const getCurrentBreadcrumb = (currentRoutePath) => {
  let breadcrumb = getTreePath(breadcrumbTree, currentRoutePath);
  // 最后一个面包屑不需要跳转地址
  if (breadcrumb && breadcrumb.length > 0) {
    breadcrumb[breadcrumb.length - 1].path = '';
  }
  return breadcrumb;
};
