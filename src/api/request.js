/*
 * 1. 使用了 axios 的拦截器
 * 2. 封装了 axios 中的 GET 与 POST 请求
 * 3. axios 使用 XMLHTTPRequest
 */

import axios from 'axios';
// import iView from 'view-design';
import qs from 'qs';
import { Message } from 'view-design';
import { Modal } from 'view-design';
import { isJSON } from '../assets/js/common.js';
import { api } from './api';
import SdpProgress from '../plugins/iview/progress';
import storage from '@/util/storage';
import jsCookie from 'js-cookie';
// import E2E from '@sdp/end-to-end-log';
import Fingerprint from '../util/fingerPrint';
import md5 from 'js-md5';
import DateUtil from '@util/date.js';

const signCode = String.fromCharCode(
  120,
  45,
  115,
  105,
  103,
  110,
  45,
  99,
  111,
  100,
  101,
);

const xParts = [
  'lRd',
  'loE',
  'YEC',
  'mAW',
  'YGL',
  '1ir',
  'bqn',
  '1TJ',
  'jA4',
  'Cq4',
  'LK',
];
const secretKey = xParts.join('');

const generateSign = (params, timestamp, bodyData) => {
  const allParams = { ...params, ...bodyData };

  // 参数排序
  const sortedParams = Object.keys(allParams)
    .sort()
    .map((key) => {
      const value = allParams[key];
      if (
        (typeof value === 'object' && String(value) === '') ||
        value === undefined
      ) {
        return 'notyouxxx';
      }
      if (value === null) {
        return `${key}=`;
      }
      if (Array.isArray(value)) {
        return `${key}=${JSON.stringify(value)}`;
      }
      return `${key}=${value}`;
    })
    .filter((v) => v !== 'notyouxxx')
    .join('&');

  // 拼接字符串生成签名
  const signStr = `${sortedParams}${timestamp}${secretKey}`;
  const rst = md5(signStr);
  return rst;
};

// let logger = undefined;
// const scopedLogger = new E2E({
//   host: 'cn-beijing.log.aliyuncs.com',
//   project: 'end-to-end-log',
//   logstore: 'base',
//   logstore: 'base',
// });
// logger = scopedLogger;
// const isApiEnableE2eLog = (api) => {
//   const excludeDomains = ['restapi.amap.com'];
//   if (!api) {
//     return true;
//   }
//   if (excludeDomains.some((excludeDomain) => api.includes(excludeDomain))) {
//     return false;
//   }
//   return true;
// };

/**
 * 导出进度统一处理
 */
const SHOW_PROGRESS_URL = [
  api.userAuditListExport,
  api.userAuditExport,
  api.userSettlementExport,
  api.userPaySumExport,

  api.purchaseAuditListExport,
  api.purchaseAuditDetailExport,
  api.purchaseSettlementExport,
  api.purchaseSumFlowingExport,
  api.purchaseSumPaySumExport,

  api.sales.commoditySellStatisticsExport,
  api.exportUsualGoods,
  api.exportDdeliveryData,
  api.exportUserTypeReportPrice,

  api.delivery.line.exportHandover,
  api.exportBillList,
  api.exportOrders,
  api.sorterPerformance.statBySorter.export,
  api.sorterPerformance.statByCommodity.listExport,
  api.sorterPerformance.statByCommodity.detailExport,
  api.exportInventoryReport,

  api.reports.purchase.salesExport,
  api.reports.provider.salesExport,

  api.exportPurchaseOrder,
  api.exportSortHistory,
  api.exportOrderDeliveryData,

  api.exportProfitDetail,

  api.process.processOrder.export,
  api.process.picking.export,
  api.process.pickingReturn.export,
  api.process.completion.export,
  api.orderReturnSingleExport,
  api.orderReturnSingleExport,
];
// const PROGRESS_SUCCESS = 'success';
// const PROGRESS_ERROR = 'wrong';
const PROGRESS_STEP = 5;
let progressTimer = 0;
let progressPercent = 0;

// Vue.use(iView);
// new Vue();
axios.defaults.headers.post['Content-Type'] =
  'application/x-www-form-urlencoded';

// 多站点需求，添加set_id字段参数
const needSetId = [
  '/api/wechat-user/set-user',
  '/api/wechat-user/get-unionid',
  '/api/wechat-user/user-manage',
  '/api/wechat-user/user-manage',
];
/**
 * @description: 判断是否需要添加set_id参数
 * @param {*} url
 * @return {*} Boolean
 */
function isNeedSetId(url) {
  for (let i = 0; i < needSetId.length; i++) {
    const ele = needSetId[i];
    if (url.includes(ele)) {
      return true;
    }
  }
  return false;
}
function addToken(config) {
  // 如果没有token,则带上
  config.params = config.params ? config.params : {};
  if (typeof config.params.token === 'undefined') {
    const token = sessionStorage['token'] || localStorage['admin_token'] || '';
    config.params.token = token;
  }
}
/**
 * 添加指纹
 */
function addClientUuid(config) {
  // 加上指纹信息
  const clientUuid = Fingerprint();
  if (config.method === 'get') {
    config.params.client_uuid = clientUuid;
  } else {
    config.data = config.data ? config.data : {};
    config.data.client_uuid = clientUuid;
  }
}
/**
 * 添加set_id参数
 */
function addSetId(config) {
  // 添加set_id字段参数
  const url = config.url;
  if (isNeedSetId(url) && localStorage['site_id']) {
    config.params = config.params || {};
    let site_id = localStorage['site_id'];
    site_id = JSON.parse(site_id);
    config.params.site_id = site_id;
  }
}
/**
 * 处理data请求参数
 */
function handleData(config) {
  let originalConfigData = {};
  // 处理post中的data数据
  if (config.method === 'get' || isJSON(config.data)) {
    return;
  }
  // 如果没有额外设置请求头的时候,直接返回一个序列化结果
  if (!config.headers['Content-Type']) {
    originalConfigData = config.data;
    config.data = qs.stringify(config.data);
    return originalConfigData;
  }
  if (config.headers['Content-Type'].includes('multipart/form-data')) {
    return;
  }
  if (config.headers['Content-Type'].includes('application/json')) {
    config.data = JSON.stringify(config.data);
    originalConfigData = config.data;
  }
  return originalConfigData;
}
// 全局拦截器，用来控制加载效果和报错的处理方式
axios.interceptors.request.use(
  function (config) {
    addToken(config);
    addClientUuid(config);
    addSetId(config);
    const configData = handleData(config);

    /**
     * 导出进度统一处理
     */
    if (SHOW_PROGRESS_URL.includes(config.url)) {
      SdpProgress.config({
        hideInfo: true,
      });
      SdpProgress.start();
      if (progressTimer) {
        clearInterval(progressTimer);
      }
      progressTimer = setInterval(() => {
        if (progressPercent < 100) {
          progressPercent += PROGRESS_STEP;
          SdpProgress.update(progressPercent);
        } else {
          progressPercent = PROGRESS_STEP;
        }
      }, 1000);
    }

    // if (isApiEnableE2eLog(config.url)) {
    //   // 端到端监控
    //   try {
    //     const { traceId } = logger.apiStart(config);
    //     config.headers.traceid = traceId;
    //     config.params.traceId = traceId;
    //   } catch (err) {
    //     console.log(err);
    //   }
    // }
    // 端标识
    config.params.terminal_trace_id = '1';

    config.params.saas_client_version = process.env.__version__;
    // 高德地图添加请求头会导致跨越
    if (config.url.indexOf('https://restapi.amap.com') === -1) {
      // 添加sign
      const timestamp = DateUtil.format(Date.now(), 'YYYY-MM-DD HH:mm:ss');

      const sign = generateSign(config.params || {}, timestamp, configData || {});
      // 设置到请求头中
      config.headers['timestamp'] = timestamp;
      config.headers[signCode] = sign;
    }

    return config;
  },
  function (error) {
    // Do something with request error
    return Promise.reject(error);
  },
  { synchronous: true },
);

axios.interceptors.response.use(
  (response) => {
    let data = response.data;

    // 端到端监控
    // try {
    //   const userData = storage.getLocalStorage('user_info');
    //   const api = response.request.responseURL;
    //   let environment = 'pro';
    //   if (!api.includes('sdongpo.com') || api.includes('sdpdev.sdongpo.com')) {
    //     environment = 'dev';
    //   } else if (
    //     api.startsWith('http://base') ||
    //     api.startsWith('https://base')
    //   ) {
    //     environment = 'fat';
    //   }
    //   if (isApiEnableE2eLog(api) && userData) {
    //     let entry = performance && performance.getEntriesByName(api);
    //     let time = entry && entry.length > 0 && entry[0].duration;
    //     logger.apiEnd({
    //       time,
    //       api,
    //       page: window.location.href,
    //       code: response.status,
    //       msg: response.status === 200 ? '' : data,
    //       device: 'sdpbase-pro',
    //       tenantcode: jsCookie.get('ccode') || '',
    //       ua: navigator.userAgent,
    //       uid: userData.userid || '',
    //       uuid: response.config.params.client_uuid || '',
    //       username: userData.username || '',
    //       success: response.status === 200 ? 1 : 0,
    //       environment,
    //       traceid: response.config.params.traceId || '',
    //     });
    //   }
    // } catch (err) {
    //   console.log(err);
    // }

    /**
     * 导出进度统一处理
     */
    if (SHOW_PROGRESS_URL.includes(response.config.url)) {
      clearInterval(progressTimer);
      if (data.status) {
        SdpProgress.finish();
      } else {
        SdpProgress.error();
      }
    }

    if (response.config.url === '/superAdmin/frame/menu') {
      if (response.data.data) {
        response.data.data.forEach((menu) => {
          if (
            menu.name === '商城' &&
            sessionStorage['mall_subject_version'] * 1 === 2
          ) {
            menu.index_key = '/superAdmin/view#/shop/home-v2/banner';
            menu.original_index_key = '/superAdmin/view#/shop/home-v2/banner';
          }
          // 开启新版分拣时替换菜单新版分拣地址
          if (
            menu.name === '库房' &&
            menu.son.length > 0 &&
            menu.son[0].name === '商品分拣'
          ) {
            const cfg = localStorage.getItem('use_new_sort_commodity');
            const isNewVersion = cfg === 'true' || !cfg;
            // console.log(isNewVersion, menu);
            if (isNewVersion) {
              menu.son[0].index_key = '/sorting/commodity';
              menu.index_key = '/sorting/commodity';
            }
          }
        });
      }
    }

    /*
     * 如果没登录则跳转登录页面
     */
    if (
      data.errCode === 1 ||
      data.errorCode === 401
    ) {
      localStorage.removeItem('admin_token');
      localStorage.removeItem('login');
      // if (window._ccode !== '/') {
      //   window.location.href = '/superAdmin/view#/unique-login';
      // } else {
      //   window.location.href = '/superAdmin/view#/login';
      // }
      window.location.href = '/';
    }
    return response;
  },
  (error) => {
    // Do something with response error
    if (error.response) {
      let configUrl =
        error.response.config.url ===
          '/superAdmin/commoditySuper/saveCommodity' ||
        error.response.config.url === '/superAdmin/printerConfig/saveData';

      if (configUrl && error.response.status === 500) {
        return error.response;
      }
      if (error.response.status === 500 && !configUrl) {
        Modal.error({
          title: '错误',
          content: '网络异常',
        });
        /*
      if (error.response.data && error.response.data.message) {
        Modal.error({content: error.response.data.message});
      } else {
        Modal.error({content: error.response.statusText});
      }
      */
      }
    }
    return Promise.reject(error);
  },
);

// 处理带有文件的post请求辅助方法
function postFormData(url, params, headerParams) {
  let formdata = new FormData();
  for (let key in params) {
    formdata.append(key, params[key]);
  }
  return new Promise((resolve, reject) => {
    axios
      .post(url, formdata, {
        headers: Object.assign(
          {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type':
              'multipart/form-data;charset=UTF-8 --boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW',
          },
          headerParams,
        ),
      })
      .then(
        (response) => {
          resolve(response.data);
        },
        (err) => {
          reject(err);
        },
      )
      .catch((error) => {
        reject(error);
      });
  });
}

var post = function (url, params, headerParams = {}) {
  if (params && params.file) {
    return postFormData(url, params, headerParams);
  }
  return new Promise(function (resolve, reject) {
    axios
      .post(url, params, {
        headers: Object.assign(
          {'X-Requested-With': 'XMLHttpRequest',},
          headerParams,
        ),
      })
      .then(
        function (response) {
          resolve(response.data);
        },
        function (err) {
          reject(err);
        },
      )
      .catch(function (error) {
        reject(error);
      });
  });
};

var postForOcrLlm = function (url, params, headerParams = {}) {
  let _params = {};
  let _headerParams = {};
  if (params && params.file) {
    let formdata = new FormData();
    for (let key in params) {
      formdata.append(key, params[key]);
    }
    _params = formdata;
    _headerParams = {
      ...headerParams,
      ...{ 'Content-Type': 'mutlipart/form-data' },
    };
  } else {
    _params = params;
    _headerParams = headerParams;
  }
  return new Promise(function (resolve, reject) {
    axios
      .post(
        // `https://qianfan.baidubce.com/v2/app/conversation${url || ''}`,
        `/superAdmin/orderSuper${url}`,
        _params,
        {
          headers: _headerParams,
        },
      )
      .then(
        function (response) {
          resolve(response.data);
        },
        function (err) {
          reject(err);
        },
      )
      .catch(function (error) {
        reject(error);
      });
  });
};

const cache = {};

var get = function (
  url,
  params,
  options = { cache: false, cacheDuration: Infinity },
) {
  // 移除 traceId 参数
  const filteredParams = { ...params };
  delete filteredParams.traceId;

  const cacheKey = `${url}_${JSON.stringify(filteredParams)}`;

  options = { cache: false, cacheDuration: Infinity, ...options };

  if (options.cache) {
    // 检查缓存
    if (cache[cacheKey]) {
      const { expires, data, promise, subscribers } = cache[cacheKey];
      const now = Date.now();

      if (expires === Infinity || now < expires) {
        // 如果缓存未过期且已有数据，直接返回缓存数据
        if (data) {
          return Promise.resolve(data);
        } else {
          // 如果缓存未过期但没有数据（请求正在进行），订阅结果返回
          return (
            promise ||
            new Promise((resolve, reject) => {
              subscribers.push({ resolve, reject });
            })
          );
        }
      } else {
        // 缓存过期，删除缓存
        delete cache[cacheKey];
      }
    }

    // 如果缓存已过期或没有缓存，执行请求并缓存结果
    const newPromise = new Promise((resolve, reject) => {
      axios
        .get(url, {
          params: params,
          headers: { 'X-Requested-With': 'XMLHttpRequest' },
        })
        .then(
          function (response) {
            // 存储缓存数据
            cache[cacheKey] = {
              data: response.data,
              expires:
                options.cacheDuration === Infinity
                  ? Infinity
                  : Date.now() + options.cacheDuration,
              promise: null,
              subscribers: [],
            };
            // 触发所有订阅者的 resolve
            cache[cacheKey].subscribers.forEach((sub) =>
              sub.resolve(response.data),
            );
            resolve(response.data);
          },
          function (err) {
            // 触发所有订阅者的 reject
            if (cache[cacheKey]) {
              cache[cacheKey].subscribers.forEach((sub) => sub.reject(err));
              delete cache[cacheKey];
            }
            reject(err);
          },
        )
        .catch(function (error) {
          if (cache[cacheKey]) {
            cache[cacheKey].subscribers.forEach((sub) => sub.reject(error));
            delete cache[cacheKey];
          }
          reject(error);
        });
    });

    // 缓存请求的 Promise 和初始化订阅者
    cache[cacheKey] = {
      data: null,
      expires:
        options.cacheDuration === Infinity
          ? Infinity
          : Date.now() + options.cacheDuration,
      promise: newPromise,
      subscribers: [],
    };

    return newPromise;
  } else {
    // 如果未启用缓存，直接执行请求
    return new Promise(function (resolve, reject) {
      axios
        .get(url, {
          params: params,
          headers: { 'X-Requested-With': 'XMLHttpRequest' },
        })
        .then(
          function (response) {
            resolve(response.data);
          },
          function (err) {
            reject(err);
          },
        )
        .catch(function (error) {
          reject(error);
        });
    });
  }
};

export { post, get, postForOcrLlm };
