// 拓客crm相关的api
import { get, post } from './request.js'

const admin = '/superAdmin/';
export let api = {
  getTeam: admin + 'salesSuper/GetTeamList',
  getTaskList:  admin + 'crmTask/list', // 客户CRM - 任务管理 获取任务列表
  getTaskDetail: admin + 'crmTask/detail', // 客户CRM - 任务管理 - 详情 获取某一任务详情
  getTaskListDetail: admin + 'crmTask/itemList', // 客户CRM - 任务管理 - 详情 获取某一任务列表详情
  getTaskListOfUser: admin + 'crmTask/parseUserInfo', // 客户CRM - 任务管理 - 新增、编辑 选择客户得到任务列表
  createTask: admin + 'crmTask/add', // 客户CRM - 任务管理 - 新增任务
  editTask: admin + 'crmTask/edit', // 客户CRM - 任务管理 - 编辑任务
  importUser: admin + 'crmTask/parseImportUserInfo', // 客户CRM - 任务管理 - 新增任务 导入客户
  closeTask: admin + 'crmTask/close', // 客户CRM - 任务管理 - 关闭任务
  userList: admin + 'crmTask/userList', // 客户CRM - 任务管理 - 选择客户 获取客户列表
  visitList: admin + 'crmVisitRecord/visitListGroupByUser', // 客户CRM - 拜访信息列表
  visitDetailList: admin + 'crmVisitRecord/VisitRecordList', // 客户CRM - 拜访信息详情列表
  exportVisitDetail: admin + 'crmVisitRecord/exportVisitListGroupByUser', // 客户CRM - 拜访信息导出
  getVisitRecordList: admin + 'crmVisitRecord/VisitRecordList',
  exportVisitRecordList: admin + 'crmVisitRecord/ExportVisitRecordList'
}
export default {
  getTaskList(params) {
    return get(api.getTaskList, params)
  },
  getTaskDetail(params) {
    return get(api.getTaskDetail, params)
  },
  getTaskListDetail(params) {
    return get(api.getTaskListDetail, params)
  },
  createTask (params) {
    return post(api.createTask, params);
  },
  editTask (params) {
    return post(api.editTask, params);
  },
  importUser (params) {
    return post(api.importUser, params);
  },
  closeTask (params) {
    return post(api.closeTask, params);
  },
  exportVisitDetail(params) {
    return get(api.exportVisitDetail, params)
  },
  exportVisitRecordList(params) {
    return get(api.exportVisitRecordList, params)
  },
}
