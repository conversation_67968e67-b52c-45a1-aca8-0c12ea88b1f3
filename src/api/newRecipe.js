/*
 * @Date: 2022-12-28 14:47:23
 * @LastEditors: hgj
 * @LastEditTime: 2023-04-18 10:50:03
 * @FilePath: /sdpbase-pro/src/api/newRecipe.js
 */
import { get, post } from './request.js'

const admin = '/superAdmin/';
export const api = {
  getTableDetail: admin + 'recipe/getDetail', // 获取菜谱表格信息
  getMainInfo: admin + 'recipe/getMainInfo', // 获取菜谱主体信息
  save: admin + 'recipe/save', // 保存菜谱
  getRecipeUserList: admin + 'recipe/recipeUserList', // 获取选择客户列表
  getUserRelationsList: admin + 'recipe/userRelationsList', // 获取菜谱关联客户列表
  genOrder: admin + 'recipe/genOrder', // 生成订单
  getRecipeList: admin + 'recipe/list', // 获取菜谱列表
  replace: admin + 'recipe/replace', // 菜谱替换
  checkCopy: admin + 'recipe/checkCopy', // 菜谱复制检查
  mealPlanAudit: admin + 'mealPlan/audit', // 审批
}
export default {
  getTableDetail(params) {
    return get(api.getTableDetail, params)
  },
  getMainInfo(params) {
    return get(api.getMainInfo, params)
  },
  save(params) {
    return post(api.save, params)
  },
  getRecipeUserList(params) {
    return get(api.getRecipeUserList, params)
  },
  getUserRelationsList(params) {
    return get(api.getUserRelationsList, params)
  },
  genOrder(params) {
    return post(api.genOrder, params)
  },
  getRecipeList(params) {
    return get(api.getRecipeList, params)
  },
  replace(params) {
    return post(api.replace, params)
  },
  checkCopy(params) {
    return get(api.checkCopy, params)
  },
  mealPlanAudit(params) {
    return post(api.mealPlanAudit, params)
  }
}
