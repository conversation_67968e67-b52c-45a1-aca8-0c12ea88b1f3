/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2021-02-28 18:46:57
 * @FilePath: \sdpbase-pro\src\api\power.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let power = {};

  power.role = {
    sorter: {
      name: "分拣员"
    }
  };

  /**
   * 获取所有权限
   * @param params
   */
  power.getAllPower = (params) => {
    return post(api.getAllPower, params);
  };

  /**
   * 获取某个角色权限
   * @param params
   */
  power.getRolePower = (params) => {
    return post(api.getRolePower, params);
  };

  /**
   * 保存角色
   * @param params
   */
  power.saveRole = (params) => {
    return post(api.saveRole, params);
  };

  /**
   * 获取角色列表
   * @param params
   */
  power.getSelectRoleList = (params) => {
    return post(api.getSelectRoleList, params);
  };

  /**
   * 保存操作员
   * @param params
   */
  power.saveOperator = (params) => {
    return post(api.saveOperator, params);
  };

  /**
   * 保存操作员
   * @param params
   */
  power.getStoreList = (params) => {
    return post(api.getStoreList, params);
  };

  /**
   * 保存操作员
   * @param params
   */
  power.saveOperatorStore = (params) => {
    return post(api.saveOperatorStore, params);
  };


export default power;
