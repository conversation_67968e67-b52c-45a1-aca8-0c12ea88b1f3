import {get,post} from './request';
import {api} from './api';

class UserBalance {

  constructor () {
  }

  /**
   * 获取搜索配置
   */
  getBalanceListSearchConfig () {
    return get(api.userBalanceListSearchConfig);
  }

  /**
   * 获取收支明细搜索配置
   */
  getBillRecordSearchConfig () {
    return get(api.userBillRecordSearchConfig);
  }

  /**
   * 获取账款搜索配置
   */
  getCreditSearchConfig () {
    return get(api.userCreditSearchConfig);
  }

  /**
   * 获取充值客户信息
   */
  getRechargeUserInfo(params) {
    return get(api.rechargeUserInfo, params);
  }

  sendDunningSms(params){
    return post(api.sendDunningSms, params)
  }

}

export default UserBalance;

