import axios from 'axios'
import { api } from './api.js'
import { get, post } from './request.js'

let userGoodsProvider = {};

/**
 * @description 获取用户商品供应商
 * <AUTHOR>
 */
userGoodsProvider.getGoods = (value) => {
  let obj = {};
  value ? obj = value : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  return get(api.getUserGoodsProvider, obj);
}

/**
 * @description 删除用户商品供应商
 * <AUTHOR>
 */
userGoodsProvider.delGoods = (param) => {
  return post(api.deleteUserGoodsProvider, param);
}

// 保存商品信息
userGoodsProvider.saveGoods = (data) => {
  return post(api.saveUserGoodsProvider, data);
}
// 根据筛选条件批量保存
userGoodsProvider.batchAddCommodity = (data) => {
  return post(api.batchAddCommodity, data);
}

export default userGoodsProvider;
