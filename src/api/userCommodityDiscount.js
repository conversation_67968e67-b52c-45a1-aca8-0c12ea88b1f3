// 客户商品折扣率 Http
import { get, post } from './request.js'

const admin = '/superAdmin/';
export const api = {
  add: admin + 'userCommodityDiscount/add', // 新增客户商品折扣率
  update: admin + 'userCommodityDiscount/batchUpdate', // 编辑客户商品折扣率
  delete: admin + 'userCommodityDiscount/del', // 删除客户商品折扣率
}
export default {
  add(params) {
    return post(api.add, params)
  },
  update(params) {
    return post(api.update, params)
  },
  delete(params) {
    return post(api.delete, params)
  }
}