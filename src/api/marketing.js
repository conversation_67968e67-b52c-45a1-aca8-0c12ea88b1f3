import { api } from './api.js'
import { get, post } from './request.js'

let market = {};
market.fullReductionList = params => {
  return get(api.fullReductionList, params);
}

market.fullReductionConfig = _ => {
  return get(api.fullReductionConfig, _);
}

market.closeFullReductionGift = params => {
  return post(api.closeFullReductionGift, params);
}

market.addFullReductionGift = params => {
  return post(api.addFullReductionGift, params);
}

market.fullReductionDetail = params => {
  return get(api.fullReductionDetail, params);
}

market.timerPurchaseList = params => {
  return get(api.timerPurchaseList, params);
}

market.closePromotion = params => {
  return post(api.closePromotion, params);
}

market.getTimerDetail = params => {
  return get(api.getTimerDetail, params);
}

market.addPromotion = params => {
  return post(api.addPromotion, params);
}

market.updatePromotion = params => {
  return post(api.updatePromotion, params);
}

market.updateFullReductionGift = params => {
  return post(api.updateFullReductionGift, params);
}

market.timerGetConfig = () => {
  return get(api.timerGetConfig);
}

market.getCommodityMinPrice = params => {
  return get(api.getCommodityMinPrice, params);
}

market.GetUserPromotionCommodityList = params => {
  return get(api.GetUserPromotionCommodityList, params);
}

market.ExportUserPromotionCommodityList = params => {
  return get(api.ExportUserPromotionCommodityList, params);
}

market.saveSpecial = params => {
  return post(api.saveSpecial, params);
}

market.editOnline = params => {
  return post(api.editOnline, params);
}

export default market;
