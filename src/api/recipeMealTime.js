import api from './api.js';
import RECIPE_MEAL_TIME from './urls/recipe/mealTime';
import { get, post } from './request.js';
const url = api[RECIPE_MEAL_TIME.key];
export const add = async (name) => {
  return await post(url.add, { name });
};
export const edit = async (id, name) => {
  return await post(url.edit, { id, name });
};
export const getList = async (params, config) => {
  return await get(url.list, params, config);
};
export const del = async (id) => {
  return await post(url.del, { id });
};
