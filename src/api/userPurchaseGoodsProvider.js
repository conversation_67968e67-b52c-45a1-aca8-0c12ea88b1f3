import axios from 'axios'
import { api } from './api.js'
import { get, post } from './request.js'

let userPurchaseGoodsProvider = {};

/**
 * @description 获取用户商品供应商
 * <AUTHOR>
 */
userPurchaseGoodsProvider.getGoods = (value) => {
  let obj = {};
  value ? obj = value : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  return get(api.getUserPurchaseGoodsProvider, obj);
}

/**
 * @description 删除用户商品供应商
 * <AUTHOR>
 */
userPurchaseGoodsProvider.delGoods = (param) => {
  return post(api.deleteUserPurchaseGoodsProvider, param);
}

// 保存商品信息
userPurchaseGoodsProvider.saveGoods = (data) => {
  return post(api.saveUserPurchaseGoodsProvider, data);
}
// 根据筛选条件批量保存
userPurchaseGoodsProvider.batchAddCommodity = (data) => {
  return post(api.batchAddPurchaseCommodity, data);
}

export default userPurchaseGoodsProvider;
