import {get,post} from './request';
import {api} from './api';

class BusinessException {

  /**
   * @param vm vue实例
   */
  constructor () {
  }

  /**
   * 异常订单处理
   * @param deliveryDate
   */
  static orderException (delivery_date, date_type) {
    let params = {
      date_type,
      delivery_date
    };
    if (Array.isArray(delivery_date)) {
      params = {
        date_type,
        delivery_start_date: delivery_date[0],
        delivery_end_date: delivery_date[1]
      }
    }
    return get(api.checkDeliveryUser, params);
  }

}

export default BusinessException;
