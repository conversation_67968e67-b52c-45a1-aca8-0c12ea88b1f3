const key = 'wholesaleMarket'

export default {
  key,
  create: prefix => {
    const api = {}
    api.getCommodityBindList = `${prefix}${key}/getCommodityBindList`
    api.exportCommodityBind = `${prefix}${key}/exportCommodityBind`
    api.exportCommodityRecordList = `${prefix}${key}/exportCommodityRecordList`
    api.importCommodityBind = `${prefix}${key}/importCommodityBind`
    api.getCategoryTree = `${prefix}${key}/getCategoryTree`
    api.getCommodityRecordList = `${prefix}${key}/getCommodityRecordList`
    api.exportCommodityList = `${prefix}${key}/exportCommodityList`
    api.bindWholesaleMarket = `${prefix}${key}/bindWholesaleMarket`
    api.autoBindWholesaleMarket = `${prefix}${key}/autoBindWholesaleMarket`
    api.getWholesaleMarketPriceList = `${prefix}${key}/getWholesaleMarketPriceList`
    return api
  }
}
