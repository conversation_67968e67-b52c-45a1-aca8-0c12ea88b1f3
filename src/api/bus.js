/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2022-03-02 17:43:59
 * @FilePath: \sdpbase-pro\src\api\bus.js
 */
import Vue from 'vue';
import { Message } from 'view-design';

export default new Vue({
  created() {
    Message.config({
      duration: 5
    });
  },
  methods: {
        /**
         * @description 公共错误提示方法
         * <AUTHOR>
         * @param msg 错误信息
         */
        error(msg) {
            Message.error(msg);
        },
        /**
         * @description 公共警告提示方法
         * <AUTHOR>
         * @param msg 错误信息
         */
        warning(msg) {
            Message.warning(msg);
        },
        /**
         * @description 公共成功提示方法
         * <AUTHOR>
         * @param msg 错误信息
         */
        success(msg) {
            Message.success(msg);
        },
        /**
         * @description 公共确认方法
         * <AUTHOR>
         */
        confirm({title = "确定", content = "确定之行此操作？", okCallback = () => {}, cancelCallback = () => {}} = {}) {

            this.$Modal.confirm({
                title: title,
                content: content,
                async onOk() {
                    okCallback &&
                    okCallback instanceof Function &&
                    okCallback();
                },
                onCancel: () => {
                    cancelCallback &&
                    cancelCallback instanceof Function &&
                    cancelCallback();
                }
              });
        },
    }
});
