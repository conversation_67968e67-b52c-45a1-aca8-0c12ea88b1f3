/*
 * @Date: 2022-12-28 14:47:23
 * @LastEditors: hgj
 * @LastEditTime: 2023-03-15 10:58:05
 * @FilePath: /sdpbase-pro/src/api/cloudPrinting.js
 */
import { get, post } from './request.js'

const admin = '/superAdmin/';
export const api = {
  getUserPrinter: admin + 'printerConfig/getUserPrinter', // 获取用户配置打印机
  getYunConfig: admin + 'printerConfig/GetYunConfig', // 获取云打印机配置
  editYunPrinter: admin + 'printerConfig/editYunPrinter', // 编辑云打印机 暂时用不上
  disableYunPrinter: admin + 'printerConfig/disableYunPrinter', // 禁用云打印机配置
  addYunPrinter: admin + 'printerConfig/addYunPrinter', // 新增云打印机
  printOrder: admin + 'printerConfig/printOrder', // 打印订单（暂时只支持发货单）
  delYunPrinter: admin + 'printerConfig/delYunPrinter', // 删除云打印机
  saveYunConfig: admin + 'printerConfig/saveYunConfig', // 保存云打印配置
}
export default {
  getUserPrinter(params) {
    return get(api.getUserPrinter, params)
  },
  getYunConfig(params) {
    return get(api.getYunConfig, params)
  },
  editYunPrinter(params) {
    return post(api.editYunPrinter, params)
  },
  disableYunPrinter(params) {
    return post(api.disableYunPrinter, params)
  },
  addYunPrinter(params) {
    return post(api.addYunPrinter, params)
  },
  printOrder(params) {
    return get(api.printOrder, params)
  },
  delYunPrinter(params) {
    return post(api.delYunPrinter, params)
  },
  saveYunConfig(params) {
    return post(api.saveYunConfig, params)
  }
}