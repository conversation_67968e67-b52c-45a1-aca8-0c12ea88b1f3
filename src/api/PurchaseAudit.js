import {get,post} from './request';
import {api} from './api';

class PurchaseAudit {

  constructor () {
  }

  /**
   * 获取采购结算列表搜索配置
   */
  getAuditListSearchConfig () {
    return get(api.purchaseAuditListSearchConfig);
  }

  /**
   * 获取采购结算单列表搜索配置
   */
  getPurchaseSettlementSearchConfig () {
    return get(api.getPurchaseSettlementSearchConfig);
  }

  /**
   * 获取对账详情数据
   * @param params
   */
  getAuditDetail (params) {
    return get(api.purchaseAuditDetail, params);
  }
  /**
   * 导出结算单
   * @param params
   */
  purchaseSettlementExport (params) {
    return get(api.purchaseSettlementExport, params);
  }

  /**
   * 冲销
   * @param params
   */
  hedged (params) {
    return post(api.hedged, params);
  }

  /**
   * 登记开票
   * @param params
   */
  recordInvoice (params) {
    return post(api.recordInvoice, params);
  }

  /**
   * 保存对账数据
   * @param params
   */
  auditSave (params) {
    return post(api.purchaseAuditSave, params);
  }

  /**
   * 获取结算数据
   * @param params
   */
  getSettlementData (params) {
    return get(api.getPurchaseSettlementData, params);
  }

  /**
   * 获取结算数据
   * @param params
   */
  createSettlement (params) {
    return post(api.createPurchaseSettlement, params);
  }

  /**
   * 获取结算单详情
   * @param params
   */
  getSettlementDetail (params) {
    return get(api.getPurchaseSettlementDetail, params);
  }

  /**
   * @description: 电子钱包付款时，发送验证码
   * @param {*} params
   */
  sendSmsCode (params) {
    return post(api.sendBTwoSms, params);
  }

}

// 计算状态
PurchaseAudit.settleStatus = {
  do: 1, // 未结算
  doing: 2, // 部分结算
  done: 3, // 结算完成
};

PurchaseAudit.auditType = {
  purchase: 1, // 采购收货
  purchaseReturn: 2, // 采购退货
};

PurchaseAudit.settlementStatusList = [
  {
    value: 1,
    label: '结算中'
  },
  {
    value: 2,
    label: '结算失败'
  },
  {
    value: 3,
    label: '已结算'
  },
  {
    value: 4,
    label: '已冲销'
  }
];

export default PurchaseAudit;
