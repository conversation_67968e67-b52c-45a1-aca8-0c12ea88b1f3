import { api } from './api.js'

import { get, post } from './request.js'

let sales = {};

  // 下拉框 业务员选团队
  sales.getAjaxTeamList = (params) => {
    return get(api.sales.getAjaxTeamList, params);
  };
  // 下拉框 获取团队列表
  sales.getTeamSelectAdmin = (params) => {
    return get(api.sales.getTeamSelectAdmin, params);
  }
  // 删除
  sales.delTeam = (params) => {
    return post(api.sales.delTeam, params);
  }
  // 编辑
  sales.saveTeam = (params) => {
    return post(api.sales.saveTeam, params);
  }
  // 详情
  sales.getTeam = (params) => {
    return get(api.sales.getTeam, params);
  }
  // 获取可选区域
  sales.getUnselectAreas = (params) => {
    return get(api.sales.getUnselectAreas, params);
  }
export default sales;
