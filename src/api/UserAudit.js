import { get, post } from './request';
import { api } from './api';

class UserAudit {
  constructor() {}

  /**
   * 获取搜索配置
   */
  getAuditListSearchConfig() {
    return get(api.userAuditListSearchConfig);
  }
  getAccountSettleData(params) {
    return post(api.getAccountSettleData, params);
  }
  /**
   * 收支明细（余额账款）搜索配置
   */
  getBillRecordSearchConfig() {
    return get(api.userBillRecordSearchConfig);
  }

  /**
   * 保存对账数据
   * @param params
   */
  auditSave(params) {
    return post(api.userAuditSave, params);
  }

  /**
   * 获取结算数据
   * @param params
   */
  getSettlementData(params) {
    return post(api.userSettlementData, params);
  }

  /**
   * 获取对账单数据
   * @param params
   */
  getAuditDetail(params) {
    return get(api.userAuditDetail, params);
  }
  /**
   * 获取对账单打印列表
   * @param params
   */
  getAuditPrintList(params) {
    return get(api.userAuditPrintList, params);
  }

  /**
   * 获取结算单详情数据
   * @param params
   */
  getSettlementDetail(params) {
    return get(api.userSettlementDetail, params);
  }

  /**
   * 保存结算数据
   * @param params
   */
  settlementSave(params) {
    return post(api.userSettlementSave, params);
  }

  /**
   * 自动完成订单
   * @param params
   */
  completeOrder(params) {
    return post(api.userAuditCompleteOrder, params);
  }
  getPurchaseBillPrintData(params) {
    return post(api.getPurchaseBillPrintData, params);
  }
  /**
   * @description: 批量标记为对账状态
   */
  batchMarkAsReconciled(params) {
    return post(api.batchMarkAsReconciled, params);
  }
}

export default UserAudit;
