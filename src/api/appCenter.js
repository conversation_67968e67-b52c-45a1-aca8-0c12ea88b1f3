import { api } from './api.js'
import { get, post } from './request.js'

const admin = '/superAdmin/'

let appApi = {
    saveOrderCommodityTag: admin + 'orderTag/saveOrderCommodityTag',
    delOrderCommodityTag: admin + 'orderTag/delOrderCommodityTag',
}

let app = {};

const url = {
    cloudStarts: {
        auth: admin + 'externalFinance/jdyAuth',
        init: admin + 'externalFinance/jdyInit',
        getAuthStatus: admin + 'externalFinance/jdyAuthStatus',
        getAccountList: admin + 'externalFinance/getJdyAccountList',
        bindAccount: admin + 'externalFinance/saveJdyAccount',
    }
};

app.newSyncDeliveryOrder = params => {
    return post(api.newSyncDeliveryOrder, params);
}
app.syncOrderReturnList = params => {
    return post(api.syncOrderReturnList, params);
}
app.syncReturnDeliveryOrder = params => {
    return post(api.syncReturnDeliveryOrder, params);
}
app.syncAccountAdd = params => {
    return post(api.syncAccountAdd, params);
};

app.syncYYCommodity = params => {
    return post(api.syncYYCommodity, params);
};

app.syncLogList = params => {
    return get(api.syncLogList, params);
};

app.versionList = () => {
    return get(api.versionList);
};

app.syncDeliveryOrder = params => {
    return post(api.syncDeliveryOrder, params);
};

app.syncSaleOrder = params => {
    return post(api.syncSaleOrder, params);
};

app.syncUser = params => {
    return post(api.syncUser, params);
};

app.getUnSyncId = params => {
    return get(api.getUnSyncId, params);
};

app.defaultAccount = () => {
    return get(api.defaultAccount);
};

app.aliasAdd = params => {
    return post(api.aliasAdd, params);
};

app.getAliasInfo = params => {
    return get(api.getAliasInfo, params);
};

app.aliasEdit = params => {
    return post(api.aliasEdit, params);
};

/**
 * 新版用友同步接口
 */
app.syncYYCommodityNew = params => {
  return post(api.YYLinkNew.syncYYCommodity, params)
}

app.syncPurchase = params => {
  return post(api.YYLinkNew.syncPurchase, params)
}

app.syncPurchaseReturn = params => {
  return post(api.YYLinkNew.syncPurchaseReturn, params)
}

/**
 * 周转筐
 *  api.depositBasket = {
    searchConfig: '/superAdmin/depositBasket/searchConfig',
    list: '/superAdmin/depositBasket/list',
    detail: '/superAdmin/depositBasket/detail',
    audit: '/superAdmin/depositBasket/audit',
    batchAudit: '/superAdmin/depositBasket/batchAudit',
    statisticsList: '/superAdmin/depositBasket/statisticsList',
    statisticsDetail: '/superAdmin/depositBasket/statisticsDetail',
    export: '/superAdmin/depositBasket/exportDepositTurnoverBasket'
  };
 */

app.depositBasketBatchAudit = params => {
    return post(api.depositBasket.batchAudit, params)
}

/**
 * @description: // 用友T + Cloud授权接口
 * @author: lizi
 */
app.yytpAuth = params =>{
    return get(api.YYCloud.auth, params)
}

/**
 * @description: 日志列表重新同步
 * @author: lizi
 */
app.resaveEntity = params =>{
    return post(api.YYCloud.resaveEntity, params)
}

/**
 * @description: 查询授权状态
 * @author: lizi
 */
app.yytpAuthStatus = params => {
    return get(api.YYCloud.yytpAuthStatus, params);
}
/**
 * @description: 客户商品别名导出
 */
app.aliasExportData = params => {
    return get(api.aliasExportData, params);
}
/**
 * @description: 客户商品别名删除
 */
app.aliasDelete = params => {
    return post(api.aliasDelete, params);
}
/**
 * @description: 招标信息
 */
app.getGeneralBiddingAuthorizeInfo = params => {
    return get(api.getGeneralBiddingAuthorizeInfo, params);
}

/**
 * @description: // 用友T + Cloud授权接口
 * @author: lizi
 */
 app.kdcsAuth = params =>{
    return get(api.kdcs.kdcsAuth, params)
}

/**
 * @description: 查询金蝶授权状态
 * @author: lizi
 */
 app.kdcsAuthStatus = params => {
    return get(api.kdcs.kdcsAuthStatus, params);
}

app.getGenPurchaseOrderConfig = params =>{
    return get(api.getGenPurchaseOrderConfig, params)
}

app.addGenPurchaseOrderConfig = params =>{
    return post(api.addGenPurchaseOrderConfig, params)
}

app.updateGenPurchaseOrderConfig = params =>{
    return post(api.updateGenPurchaseOrderConfig, params)
}

app.getCronHandleRecordDetail = params =>{
    return get(api.getCronHandleRecordDetail, params)
}
/**
 * 订单商品标签相关
 */
app.saveOrderCommodityTag = params =>{
    return get(appApi.saveOrderCommodityTag, params)
}
app.delOrderCommodityTag = params =>{
    return post(appApi.delOrderCommodityTag, params)
}


// 云星辰获取授权状态
app.cloudStartsGetAuthStatus = params => {
    return get(url.cloudStarts.getAuthStatus, params)
}

// 云星辰 授权
app.cloudStartsAuth = params => {
    return get(url.cloudStarts.auth, params)
}
// 云星辰 初始化授权
app.cloudStartsInit = params => {
    return get(url.cloudStarts.init, params)
}
// 云星辰 获取账套列表
app.cloudStartsGetAccountList = params => {
    return get(url.cloudStarts.getAccountList, params)
}

// 云星辰 绑定账套
app.cloudStartsBindAccount = params => {
    return get(url.cloudStarts.bindAccount, params)
}
export default app;
