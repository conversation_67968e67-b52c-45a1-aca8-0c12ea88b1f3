import { api } from './api.js'
import { get, post } from './request.js'

let finance = {};

/**
 * 批量对账保存
 * @param params
 */
finance.batchCheckingSave = params => {
  return post(api.batchBill.updatePrice, params);
};

finance.getPurchaseBatchBillList = params => {
  return post(api.purchaseBatchBill.list, params)
}

finance.updateUnitPrice = params => {
  return post(api.purchaseBatchBill.updateUnitPrice, params)
}

// 在线支付流水下拉框配置
finance.onlinePaymentConfig = params => {
  return get(api.finance.onlinePaymentConfig, params)
}

// 审核资金流水
finance.auditJournalAccount = params => {
  return post(api.finance.auditJournalAccount, params);
}

// 编辑资金流水
finance.editJournalAccount = params => {
  return post(api.finance.editJournalAccount, params);
}

// 关闭资金流水
finance.closeJournalAccount = params => {
  return post(api.finance.closeJournalAccount, params);
}

// 异常支付订单
finance.AbnormalPayOrderList = params => {
  return post(api.finance.AbnormalPayOrderList, params);
}

export default finance;
