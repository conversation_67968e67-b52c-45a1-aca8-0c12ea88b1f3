/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2021-02-28 18:46:13
 * @FilePath: \sdpbase-pro\src\api\notice.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let notice = {};



  notice.type = {
    order: 3,
    normal: 1,
    upgrade: 2,
  };

  /**
   * @description 获取通知详情
   * <AUTHOR>
   */
  notice.getNotice = (params) => {
    return get(api.getNotice, params);
  };

  /**
   * @description 获取通知详情
   * <AUTHOR>
   */
  /**
   * @description 获取通知详情
   * <AUTHOR>
   */
  notice.getNoticeItem = (params) => {
    return get(api.getNoticeItem, params);
  };
  notice.getNoticeCounter = (params) => {
    return get(api.getNoticeCounter, params);
  };

  notice.getNoticeBox = (params) => {
    return get(api.getNoticeBox, params);
  };

  notice.updateNotice = (params) => {
    return post(api.updateNotice, params);
  };

  notice.cleanNotice = (params) => {
    return post(api.cleanNotice, params);
  };


export default notice;
