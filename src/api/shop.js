import { api } from './api.js'
import { get, post } from './request.js'

let shop = {};

  shop.subjectType = {
    normal: 0,
    activity: 1,
    recommend: 2,
  };


  /**
   * 专题列表样式
   */
  shop.subjectListStyle = {
    grid: 1,
    grid3: 3,
    list: 2,
  };

  /**
   * @description 获取banner数据
   * <AUTHOR>
   */
  shop.getBanner = (params) => {
    return get(api.getBanner, params);
  };

  /**
   * @description 保存banner数据
   * <AUTHOR>
   */
  shop.saveBanner = (params) => {
    return post(api.saveBanner, params);
  };

  /**
   * @description 保存banner数据
   * <AUTHOR>
   */
  shop.batchUpdateBanner = (params) => {
    return post(api.batchUpdateBanner, params);
  };

  /**
   * @description 删除banner数据
   * <AUTHOR>
   */
  shop.delBanner = (params) => {
    return post(api.delBanner, params);
  };


  /*********************
   *  专题模块
   ********************/
  shop.getActivity = (params) => {
    return post(api.getActivity, params);
  };

  shop.getSubject = (params) => {
    return post(api.getSubject, params);
  };

  shop.saveSubject = (params) => {
    return post(api.saveSubject, params);
  };

  shop.batchSaveSubject = (params) => {
    return post(api.batchSaveSubject, params);
  };

  shop.delSubject = (params) => {
    return post(api.delSubject, params);
  };

  shop.delSubjectGoods = (params) => {
    return post(api.delSubjectGoods, params);
  };

  shop.getShopColor = (params) => {
    return post(api.getShopColor, params);
  };

  shop.saveShopColor = (params) => {
    return post(api.saveShopColor, params);
  };

  shop.imageGalleryList = (params) => {
    return get(api.imageGalleryList, params);
  };

  shop.categoryList = params => {
    return get(api.categoryList, params);
  };

  shop.afterSale = params => {
    return get(api.afterSale, params);
  };

  shop.shopAbout = params => {
    return get(api.shopAbout, params);
  };

  shop.shopAgreement = params => {
    return get(api.shopAgreement, params);
  };

  shop.SaveConfig = params => {
    return post(api.SaveConfig, params);
  };

export default shop;
