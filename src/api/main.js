import store from '../vuex';
import { api } from './api.js';
import { get, post } from './request.js';
import storage from '../util/storage';

let main = {};

main.getMenu = () => {
  return get(api.getAdminMenu, {});
};

main.getTopMenu = () => {
  return get(api.getTopMenu, {});
};

main.getProjectInfo = () => {
  return get(api.getProjectInfo, {});
};

main.logOut = () => {
  return get(api.logOut, {});
};

main.commonData = () => {
  return get(api.getTopMenu, {});
};

main.getCommonConfig = () => {
  //判断是否登录，只有登录才获取配置信息
  let isLogin = localStorage['admin_token'];
  if (!isLogin)
    return new Promise(resolve => {
      resolve({});
    });
  return get(api.getTopMenu).then(res => {
    if (res.status) {
      return res.data;
    } else {
      return {};
    }
  });
};

main._configPromise = null;
main.getConfig = ({ fromCache = false, queue = true } = {}) => {
  //判断是否登录，只有登录才获取配置信息
  let isLogin = localStorage['admin_token'];
  if (!isLogin)
    return new Promise(resolve => {
      resolve({});
    });
  const config = store.state.sysConfig;
  const isConfigEmpty = config => !config || JSON.stringify(config) === '{}';
  if (fromCache === true && !isConfigEmpty(config)) {
    return new Promise(resolve => {
      resolve(config);
    });
  }
  // 存在未返回的请求,复用之前的请求
  if (queue === true && main._configPromise) {
    return main._configPromise;
  }
  const configPromise = get(api.getSystemConfig)
    .then(res => {
      if (res.status) {
        let data = res.data;
        let configMap = {};
        data.map(config => {
          configMap[config.key] = config.value;
        });
        store.commit('setSysConfig', configMap);
        storage.setLocalStorage('business_config', configMap);
        return configMap;
      } else {
        return {};
      }
    })
    .finally(() => (main._configPromise = null));
  main._configPromise = configPromise;
  return configPromise;
};

main.saveConfig = params => {
  return post(api.saveSystemConfig, params);
};

main.editSingleConfig = (key, value) => {
  let params = {
    key: key,
    value: value
  };
  return post(api.saveSingleConfig, params);
};
main.saveSearchConfig = params =>{
  return post(api.saveListSearchFieldsConfig, params)
};
main.getSearchConfig = params =>{
  return get(api.getListSearchFieldsConfig, params)
};

export default main;
