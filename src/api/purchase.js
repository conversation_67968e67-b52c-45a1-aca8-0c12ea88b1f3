import { api } from './api.js';
import { get, post } from './request.js';
import { Modal } from 'view-design';

let purchase = {};


  /**
   * 两个采购单是否可以合并
   * @TODO 合并规则： 1.相同的采购员/供应商; 2.相同的交货日期; 3.待采购状态
   * @param order1
   * @param order2
   */
  // eslint-disable-next-line no-undef
  purchase.isOrderCanMerge = (order1, order2) => {
    const planPurchase = '待采购';
    return (
      order1.purchase_status === planPurchase &&
      order2.purchase_status === planPurchase &&
      order1.plan_date === order2.plan_date &&
      // 相同的采购员或者供应商
      ((order1.agent_id * order2.agent_id > 0 &&
        order1.agent_id === order2.agent_id) ||
        (order1.provider_id * order2.provider_id > 0 &&
          order1.provider_id === order2.provider_id))
    );
  };

  // eslint-disable-next-line no-undef
  purchase.mergeOrder = orderNoArr => {
    let params = {
      data: JSON.stringify(orderNoArr)
    };
    return post(api.purchaseOrder.mergeOrder, params);
  };

  // eslint-disable-next-line no-undef
  purchase.batchCloseOrder = idList => {
    let params = {
      id: idList
    };
    return post(api.purchaseOrder.batchCloseOrder, params);
  };

  // eslint-disable-next-line no-undef
  purchase.checkMergeOrder = (params, callback) => {
    post(api.purchaseOrder.checkMergeOrder, params).then(res => {
      let { status, message } = res;
      if (status) {
        Modal.confirm({
          render: h => {
            return h('div', [
              h(
                'p',
                message ||
                  '该交货日期下已经存在采购单，新生成的采购单是否合并到早期采购单中？'
              ),
              h(
                'p',
                {
                  style: {
                    marginTop: '15px',
                    color: '#777'
                  }
                },
                '注意：只有同一个采购员或供应商相同的交货日期的采购单才能合并，并且只有待采购的单据才能合并，已收货的采购单无法合并！'
              )
            ]);
          },
          onOk: () => {
            callback(true);
          },
          onCancel: () => {
            callback(false);
          }
        });
      } else {
        callback(false);
      }
    });
  };

  /**
   * @description 获取采购单列表
   * <AUTHOR>
   * @param filters 筛选条件
   */
  // eslint-disable-next-line no-undef
  purchase.getPurchaseOrderList = filters => {
    return get(api.getPurchaseOrderList, filters);
  };

  /**
   * @description: 获取采购单商品列表
   */
  purchase.getPurchaseOrderGoodsDetail = filters => {
    return get(api.getPurchaseOrderGoodsDetail, filters)
  }

  //获取采购单详情
  // eslint-disable-next-line no-undef
  purchase.getPurchaseOrderDetail = function(param) {
    return get(api.getPurchaseOrderDetail, param);
  };

  //采购单收货
  // eslint-disable-next-line no-undef
  purchase.purchaseReceipt = function(param) {
    return post(api.purchaseReceipt, param);
  };

  //保存采购单
  // eslint-disable-next-line no-undef
  purchase.savePurchaseOrder = function(param) {
    return post(api.savePurchaseOrder, param);
  };

  //编辑采购单
  // eslint-disable-next-line no-undef
  purchase.editPurchaseOrder = function(param) {
    return post(api.editPurchaseOrder, param);
  };

  //审核采购单
  // eslint-disable-next-line no-undef
  purchase.auditPurchaseOrder = function(param) {
    return post(api.auditPurchaseOrder, param);
  };

  //新增采购单
  // eslint-disable-next-line no-undef
  purchase.addPurchaseOrder = function(param) {
    return post(api.addPurchaseOrder, param);
  };
  //新增采购单
  // eslint-disable-next-line no-undef
  purchase.printPurchaseOrder = function(param) {
    return post(api.printPurchaseOrder, param);
  };
  /**
   * 获取打印数据
   * @param {number} id 采购单id
   */
  // eslint-disable-next-line no-undef
  purchase.getPrintData = params => {
    return get(api.purchaseOrder.getPrintData, params);
  };
  /**
   * 获取采购单批量打印数据
   * @param {number} id 采购单id
   */
  // eslint-disable-next-line no-undef
  purchase.getBatchPrintData = params => {
    return get(api.purchaseOrder.batchPrintData, params);
  };
  /**
   * 获取打印数据
   * @param {number} id 采购单id
   */
  // eslint-disable-next-line no-undef
  purchase.getPurchaseReturnPrintData = id => {
    return get(api.purchaseReturn.getPrintData, { id });
  };
  // 获取过滤数据
  // eslint-disable-next-line no-undef
  purchase.getBaseCommoditysAndPrice = function(param) {
    return get(api.getBaseCommoditysAndPrice, param);
  };
  // eslint-disable-next-line no-undef
  purchase.getBaseCommoditys = function(param) {
    return get(api.getBaseCommoditys, param);
  };
  // eslint-disable-next-line no-undef
  purchase.getPurchaseCommoditys = function(param) {
    return get(api.getPurchaseCommoditys, param);
  };
  // 获取过滤数据详情
  // eslint-disable-next-line no-undef
  purchase.getMoreOrderInfo = function(param) {
    return post(api.getMoreOrderInfo, param);
  };
  // 获得采购商品补充信息
  // eslint-disable-next-line no-undef
  purchase.getCommodityInfo = function(param) {
    return get(api.getCommodityInfo, param);
  };

  // 新增采购退货
  // eslint-disable-next-line no-undef
  purchase.addPurchaseReturn = function(param) {
    return post(api.addPurchaseReturn, param);
  };
  // eslint-disable-next-line no-undef
  purchase.addPurchaseNewAudit = function(param) {
    return post(api.addPurchaseNewAudit, param);
  };

  // 新增采购退货
  // eslint-disable-next-line no-undef
  purchase.savePurchaseReturn = function(param) {
    return post(api.savePurchaseReturn, param);
  };
  purchase.savePurchaseAudit = function(param) {
    return post(api.savePurchaseAudit, param);
  }
  // eslint-disable-next-line no-undef
  purchase.purchaseAntiAudit = function(param) {
    return post(api.purchaseAntiAudit, param);
  };
  // eslint-disable-next-line no-undef
  purchase.savePurchaseAntiAudit = function(param) {
    return post(api.savePurchaseAntiAudit, param);
  };

  // 获取采购退货详情
  // eslint-disable-next-line no-undef
  purchase.purchaseReturnDetail = function(param) {
    return post(api.purchaseReturnDetail, param);
  };

  // 关闭采购单
  // eslint-disable-next-line no-undef
  purchase.closeOrder = function(param) {
    return post(api.closeOrder, param);
  };

  // 关闭退回采购单
  // eslint-disable-next-line no-undef
  purchase.closeReturnOrder = function(param) {
    return post(api.closeReturnOrder, param);
  };
  // 删除采购单商品
  // eslint-disable-next-line no-undef
  purchase.cancelCommodity = function(param) {
    return post(api.cancelCommodity, param);
  };
  // 导出采购模板
  // eslint-disable-next-line no-undef
  purchase.exportOrderTemplate = function(param) {
    return get(api.exportOrderTemplate, param);
  };
  // eslint-disable-next-line no-undef
  purchase.purchaseStatDetail = function(param) {
    return get(api.purchaseStatDetail, param);
  };
  // eslint-disable-next-line no-undef
  purchase.purchaseStatAgent = function(param) {
    return get(api.purchaseStatAgent, param);
  };
  // eslint-disable-next-line no-undef
  purchase.purchaseStatProvider = function(param) {
    return get(api.purchaseStatProvider, param);
  };
  // eslint-disable-next-line no-undef
  purchase.purchaseStatCommodity = function(param) {
    return get(api.purchaseStatCommodity, param);
  };
  // eslint-disable-next-line no-undef
  purchase.purchaseStatData = function(param) {
    return get(api.purchaseStatData, param);
  };
  // eslint-disable-next-line no-undef
  purchase.exportPurchaseStatDetail = function(param) {
    return get(api.exportPurchaseStatDetail, param);
  };
  // eslint-disable-next-line no-undef
  purchase.exportPurchaseStatCommodity = function(param) {
    return get(api.exportPurchaseStatCommodity, param);
  };
  // eslint-disable-next-line no-undef
  purchase.exportPurchaseStatAgent = function(param) {
    return get(api.exportPurchaseStatAgent, param);
  };
  // eslint-disable-next-line no-undef
  purchase.exportPurchaseStatProvider = function(param) {
    return get(api.exportPurchaseStatProvider, param);
  };
  // eslint-disable-next-line no-undef
  purchase.getPurchaseCommoditys = function(param) {
    return get(api.getPurchaseCommoditys, param);
  };
  purchase.purchaseAgentDisable = function(param) {
    return post(api.purchaseAgentDisable, param);
  };
  purchase.purchaseAgentDelete = function(param) {
    return post(api.purchaseAgentDelete, param);
  };
  purchase.supplyCostList = function(param) {
    return get(api.supplyCostList, param);
  };
  purchase.supplyCostSum = function(param) {
    return get(api.supplyCostSum, param);
  };
  purchase.supplyCostSaleSum = function(param) {
    return get(api.supplyCostSaleSum, param);
  };
  purchase.supplyCostExport = function(param) {
    return get(api.supplyCostExport, param);
  };
  purchase.ExportCostDetail = function(param) {
    return get(api.ExportCostDetail, param);
  };
  purchase.supplySaleExport = function(param) {
    return get(api.supplySaleExport, param);
  };
  purchase.getReferInfo = function(param) {
    return get(api.getReferInfo, param);
  };

// eslint-disable-next-line no-undef
export default purchase;
