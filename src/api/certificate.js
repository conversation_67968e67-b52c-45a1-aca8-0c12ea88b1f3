/*
 * @Description:  凭证相关的api操作
 * @Autor: lizi
 * @Date: 2022-06-13 17:25:36
 * @LastEditors: lizi
 * @LastEditTime: 2022-06-15 17:44:27
 * @FilePath: \sdpbase-pro\src\api\certificate.js
 */
import { get, post } from './request.js'

// api地址分模块分开存放，方便后续拆包
let api = {
    editSubject: '/accountingCertificate/editSubject',
    templateDetail: '/accountingCertificate/templateDetail',
    editTemplate: '/accountingCertificate/editTemplate',
    genCert: '/accountingCertificate/genCert',
    searchConfig: '/accountingCertificate/searchConfig',
    deleteCert: '/accountingCertificate/deleteCert',
    exportCert: '/accountingCertificate/exportExternalCert',
    getCertType: '/accountingCertificate/getExternalType',
    saveCertType: '/accountingCertificate/saveExternalType',
    deleteReferCert:'/accountingCertificate/deleteReferCert',
    updateCustomExportColumn: '/general/updateCustomExportColumn'
}

for (let key in api) {
    apiProxy(api, key);
}

function apiProxy (obj, key) {
    let val = obj[key];
    Object.defineProperty(obj, key, {
        get () {
            return '/superAdmin' + val;
        },
        set (_val) {
            if (val === _val) return;
            val = _val
        }
    })
}



export default {
    /**
     * @description: 编辑科目
     * @author: lizi
     */
    editSubject (params) {
        return post(api.editSubject, params)
    },
    /**
     * @description: 凭证模板详情
     * @author: lizi
     */
    templateDetail (params) {
        return get(api.templateDetail, params)
    },
    /**
     * @description: 凭证模板编辑
     * @author: lizi
     */
    editTemplate (params) {
        return post(api.editTemplate, params)

    },
    /**
     * @description: 生成凭证
     * @author: lizi
     */
    genCert (params) {
        return post(api.genCert, params)
    },
    /**
     * @description: 查询条件中的配置项
     * @author: lizi
     */
    searchConfig (params) {
        return get(api.searchConfig, params)
    },
    /**
     * @description: 批量删除凭证
     * @author: lizi
     */
    deleteCert (params) {
        return post(api.deleteCert, params)
    },
    // 获取凭证类型
    getCertType (params) {
        return get(api.getCertType, params)
    },
    // 保存凭证类型
    saveCertType (params) {
        return post(api.saveCertType, params)
    },
    // 导出凭证
    exportCert(params){
        return post(api.exportCert, params)
    },
    /**
     * @description: 批量删除凭据生成
     * @author: lizi
     */    
    deleteReferCert(params){
        return post(api.deleteReferCert, params)
    },
    /**
     * 
     * @description: 保存单据类型配置
     * @author: hgj
     */
    updateCustomExportColumn(params) {
        return get(api.updateCustomExportColumn, params)
    }

}