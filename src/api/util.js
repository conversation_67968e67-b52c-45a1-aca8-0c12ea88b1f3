let util = {};

util.mapType = {
  amap: 'amap', // 高德地图,
  google: 'google' // 谷歌地图,
};

/**
 * 是否启用谷歌地图
 * @param config 配置信息
 * @returns {boolean}
 */
util.getMapType = (config) => {
  return config['project_nationality'] === 'zh' ? util.mapType.amap : util.mapType.google;
};

/**
 * 是否启用谷歌地图
 * @param config 配置信息
 * @returns {boolean}
 */
util.isEnableGoogleMap = (config) => {
  return config['project_nationality'] !== 'zh';
};

/**
 * 是否是多仓库
 * @returns {*}
 */
util.isMultiStore = () => {
  return !!parseInt(sessionStorage.getItem('_muti_store'));
};

/**
 * @description 设置是否是多仓库
 */
util.setIsMultiStore = (value) => {
  sessionStorage.setItem('_muti_store', value);
};

/**
 * @description 获取是否开启库区库位
 */
util.getIsOpenStoreMGT = () => {
  return !!parseInt(sessionStorage.getItem('_isOpenStoreMGT'));
};

util.setIsOpenStoreMGT = (value) => {
  sessionStorage.setItem('_isOpenStoreMGT', value);
};

/**
 * 是否启用净菜加工
 * @param config 配置信息
 * @returns {boolean}
 */
util.isEnableProcess = function (config) {
  return config.is_enabled_processed_vegetables * 1 == 1;
};

/**
 * 生产加工模式是否是客户模式
 * @returns {boolean}
 */
util.isUserModeProcess = function (config) {
  return config.process_mode * 1 === util.processMode.user.value * 1;
};

util.getTextColor = function (text) {
  let color = '';
  let redColorText = ['未审核', '未完成', '已关闭'];
  if (redColorText.indexOf(text.trim()) !== -1) {
    color = 'red';
  }
  return color;
};

util.processPurchaseType = {
  raw: {
    label: '按原料采购',
    value: 1
  },
  manufactured: {
    label: '按成品采购',
    value: 2,
  }
};

util.getProcessPurchaseType = function(value) {
  value = value * 1;
  let typeText = '';
  Object.values(util.processPurchaseType).forEach((type) => {
    if (type.value === value) {
      typeText = type.label;
    }
  });
  return typeText;
};

// 采购类型
util.purchaseType = {
  typeAgent: {
    value: 1,
    label: "市场自采",
  },
  typeProviderDirect: {
    label: "供应商直供",
    value: 2
  },
  typeProvider: {
    label: "指定供应商",
    value: 3
  },
};

// 支付类型
util.payType = {
  wxpay: {
    value: "1",
    label: "微信支付",
  },
  balancePay: {
    value: "2",
    label: "余额支付",
  },
  alipay: {
    value: "3",
    label: "支付宝支付",
  }
};

// 加工模式
util.processMode = {
  user: {
    value: 1,
    label: "按客户",
  },
  balancePay: {
    value: 2,
    label: "按商品",
  },
};

// 采购收货分摊项目
util.costsItem = [
  {
    value: '1',
    label: "运费分摊",
  },
  {
    value: '2',
    label: "其他费用分摊",
  },
];

// 采购收货分摊类型
util.costsType = [
  {
    value: '1',
    label: "增加费用",
  },
  {
    value: '2',
    label: "减少费用",
  },
];

util.getCostsItemDesc = function (value) {
  let item = util.costsItem.find((item) => item.value === value);
  return item ? item.label : '';
};

util.getCostsTypeDesc = function (value) {
  let item = util.costsType.find((item) => item.value === value);
  return item ? item.label : '';
};

// 增加费用
util.isCostsTypeAdd = function (value) {
  return value === '1';
};

// 切换分摊类型
util.toggleCostsType = function (value) {
  return value === '1' ? '2' : '1';
};

util.tree2Array = function (tree, childrenKey) {
  let ret = [];
  for (let i in tree) {
    ret.push(tree[i]);
    if (tree[i][childrenKey]) {
      ret = ret.concat(util.tree2Array(tree[i][childrenKey], childrenKey));
    }
  }
  return ret;
};

//格式化date
util.formatDate = (date, fmt) => {
  date = new Date(date);
  var o = {
      "M+": date.getMonth() + 1, //月份
      "d+": date.getDate(), //日
      "h+": date.getHours(), //小时
      "m+": date.getMinutes(), //分
      "s+": date.getSeconds(), //秒
      "q+": Math.floor((date.getMonth() + 3) / 3), //季度
      S: date.getMilliseconds() //毫秒
  };
  if (/(y+)/.test(fmt)) {
      fmt = fmt.replace(
          RegExp.$1,
          (date.getFullYear() + "").substr(4 - RegExp.$1.length)
      );
  }
  for (var k in o) {
      if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
              RegExp.$1,
              RegExp.$1.length == 1
                  ? o[k]
                  : ("00" + o[k]).substr(("" + o[k]).length)
          );
      }
  }
  return fmt;
}

util.getDay = (day) => {
  var today = new Date();

  var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;

  today.setTime(targetday_milliseconds); //注意，这行是关键代码

  var tYear = today.getFullYear();
  var tMonth = today.getMonth();
  var tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + "-" + tMonth + "-" + tDate;

  function doHandleMonth(month) {
    var m = month;
    if (month.toString().length == 1) {
      m = "0" + month;
    }
    return m;
  }
}

util.Math = {
  /**
   * 保留两位小数不四舍五入，直接舍去后面的。
   * @param number
   * @param decimalNum
   * @returns {*}
   */
  floorFormat(number, decimalNum = 2) {
    if (isNaN(number) || isNaN(decimalNum)) {
      return number;
    }
    decimalNum = parseInt(decimalNum);
    let base = Math.pow(10, decimalNum);
    return Math.floor(number * base) / base;
  }
};

util.getMenuPath = (url) => {
  let pathArr = url.split('#');
  let path = pathArr.length > 1 ? pathArr[1] : url;
  // 没做前后端分离
  if (!url.includes('#')) {
    return url;
  }
  return path;
};

export default util;
