import axios from 'axios';
import router from '../router';
import { api } from './api.js';
import { get, post } from './request.js';

let user = {};
/**
 * 获取账期类型
 */
user.remindType = {
  all: {
    label: '全部',
    value: '',
  },
  no: {
    label: '无账期',
    value: '0',
  },
  time: {
    label: '时间账期',
    value: '2',
  },
  price: {
    label: '金额账期',
    value: '1',
  },
};

user.getAddressByLocation = (param) => {
  let token = '1'; // 此接口是高德地图提供的 api，不应把我们自己的 token 传给高德，这里用一个假的代替
  let key = '4726a819551f7a7c242282f12eb1b87d'; // 高德地图
  return get(api.amap.getAddressByLocation, {
    key,
    token,
    ...param,
  });
};
/**
 * 获取账期描述
 * @param value
 * @returns {string}
 */
user.getRemindTypeLabel = (value) => {
  let remindType = Object.values(user.remindType).find(
    (type) => parseInt(type.value, 10) === parseInt(value, 10),
  );
  return remindType ? remindType.label : '未知';
};

user.getUserTypeList = function (value) {
  let data = value ? value : undefined;
  return get(api.getUserTypeList, data);
};

user.getUserAjaxList = function (value) {
  let data = value ? value : undefined;
  return get(api.getUserAjaxList, data);
};

user.delUserType = function (value) {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    rece_style: obj.receStyle,
  };

  return get(api.delUserType, data);
};

user.editUserType = function (value) {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    style_info: JSON.stringify(obj.styleInfo),
    rece_style: obj.receStyle,
  };

  return post(api.editUserType, data);
};

user.newUserType = function (value) {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    style_info: JSON.stringify(obj.styleInfo),
  };

  return post(api.newUserType, data);
};

user.getUserList = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;
  return get(api.getUserList, obj);
};

user.getOrderUser = () => {
  return get(api.getOrderUser);
};

user.getAreaList = () => {
  return get(api.getAreaList);
};

user.getGroupList = () => {
  return get(api.getGroupList);
};

user.getSalesList = (params) => {
  return get(api.getSalesList, params);
};

user.getDeliveryDateList = () => {
  return get(api.getDeliveryDateList);
};

user.getPrintTemplateList = () => {
  return get(api.getPrintTemplateList);
};

user.setNewUser = (
  user_info,
  number_of_diners,
  accompanying_of_diners,
  recipe_id,
) => {
  let data = {
    user_info: JSON.stringify(user_info),
    number_of_diners: JSON.stringify(number_of_diners),
    accompanying_of_diners: JSON.stringify(accompanying_of_diners),
    recipe_id,
  };
  return post(api.setNewUser, data);
};

user.modifyUser = (
  user_id,
  user_info,
  number_of_diners,
  accompanying_of_diners,
  recipe_id,
) => {
  let data = {
    user_id,
    user_info: JSON.stringify(user_info),
    number_of_diners: JSON.stringify(number_of_diners),
    accompanying_of_diners: JSON.stringify(accompanying_of_diners),
    recipe_id,
  };
  return post(api.modifyUser, data);
};

user.setLocation = (data) => {
  return post(api.setLocation, data);
};

user.getUserDetail = (id) => {
  let data = {
    user_id: id,
  };
  return get(api.getUserDetail, data);
};

user.getUsualGoods = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  let data = {
    is_print: obj.isPrint,
    user_id: obj.uid,
    searchKey: obj.searchValue,
    category_id: obj.firstCategoryId,
    category_id2: obj.secondCategoryId,
    page: obj.page,
    pageSize: obj.pageSize,
  };

  return post(api.getUsualGoods, data);
};

user.setUsualGoods = (id, value) => {
  let data = {
    user_id: id,
    commodity_ids: value,
  };
  return post(api.setUsualGoods, data);
};

user.delUsualGoods = (value) => {
  let data = { ids: value.cid };
  return post(api.delUsualGoods, data);
};

user.collectionAddPrint = (ids, user_id, is_print) => {
  let data = { ids: ids, user_id: user_id, is_print: is_print };
  return post(api.collectionAddPrint, data);
};

user.getUnavailableGoods = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  let data = {
    user_id: obj.uid,
    searchKey: obj.searchValue,
    category_id: obj.firstCategoryId,
    category_id2: obj.secondCategoryId,
    page: obj.page,
    pageSize: obj.pageSize,
  };

  return post(api.getUnavailableGoods, data);
};

user.setUnavailableGoods = (id, value, type = undefined, style = undefined) => {
  let data = {
    user_id: id,
    commodity_ids: value,
    type,
    style,
  };
  return post(api.setUnavailableGoods, data);
};

user.addDisabledGoods = (params) => {
  return post(api.addDisabledGoods, params);
};

user.addUserTypeDisabledGoods = (params) => {
  return post(api.addUserTypeDisabledGoods, params);
};

user.getUserGroup = (value) => {
  let data = value ? value : undefined;
  return get(api.getUserGroup, data);
};

user.NewUserGroup = (value) => {
  let obj = value ? value : undefined;
  let data = {
    group_info: obj,
  };
  return post(api.NewUserGroup, data);
};

user.delUserGroup = (id) => {
  let groupId = id ? id : undefined;
  let data = {
    group_id: groupId,
  };
  return post(api.delUserGroup, data);
};

user.editUserGroup = (value) => {
  let obj = value ? value : undefined;
  return post(api.editUserGroup, obj);
};

user.groupInvoiceUpdate = (params) => {
  return post(api.invoice.groupInvoiceUpdate, params);
};

user.groupGetInvoice = (params) => {
  return get(api.invoice.groupGetInvoice, params);
};

user.delUser = (id) => {
  let data = { user_id: id };
  return post(api.delUser, data);
};

user.getUserPanel = () => {
  return get(api.getUserPanel);
};

user.getIncrementTrend = (type) => {
  let data = { type: type };
  return post(api.getIncrementTrend, data);
};

user.delUnavailableGoods = (value) => {
  let data = { ids: value.cid };
  return post(api.delUnavailableGoods, data);
};

user.delUnavailableCategories = (value) => {
  let data = { ids: value.map((item) => item.id).join(',') };
  return post(api.delUnavailableGoods, data);
};

user.getSpecialPriceList = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  let data = {
    user_id: obj.uid,
    searchKey: obj.searchValue,
    category_id: obj.firstCategoryId,
    category_id2: obj.secondCategoryId,
    page: obj.page,
    pageSize: obj.pageSize,
  };

  return post(api.getSpecialPriceList, data);
};

user.setSpecialPrice = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    user_id: value.uid,
    commodity_ids: value.cid,
    prices: JSON.stringify(value.price),
  };

  return post(api.setSpecialPrice, data);
};

user.modifySpecialPrice = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    sp_id: value.id,
    prices: value.price,
  };

  return post(api.modifySpecialPrice, data);
};

user.delSpecialPrice = (value) => {
  let data = { sp_id: value.cid };
  return post(api.delSpecialPrice, data);
};

/**
 * @description 修改账期
 * <AUTHOR>
 * @param data 账期数据
 */
user.modifyPayment = (data) => {
  return post(api.modifyPayment, data);
};

user.getSingleCase = (value) => {
  return post(api.getSingleCase, value);
};

user.singleHistory = (value) => {
  return post(api.singleHistory, value);
};

user.getOrderHistory = (value) => {
  // let data = {
  //   start_time: value.start_time,
  //   end_time: value.end_time,
  //   user_id: value.user_id,
  //   type: value.type,
  //   seller_id: value.seller_id,
  //   commodity_id: value.commodity_id
  // }

  return post(api.getOrderHistory, value);
};

user.getUserList = (params) => {
  return get(api.getUserList, params);
};
/**
 * @description 获取账期
 * <AUTHOR>
 * @param data 账期数据
 */
user.getPayment = (data) => {
  return get(api.getPayment, data);
};

user.exportExcel = (filters) => {
  return get(api.userExport, filters);
};
user.disabledCommodityExport = (filters) => {
  return get(api.userDisabledCommodityExport, filters);
};

/**
 * @description 下载模版
 * <AUTHOR>
 */
user.exportTemplate = () => {
  return get(api.exportTemplate);
};

user.doExport = (param) => {
  return api.doExport;
};

user.orderExport = (param) => {
  return get(api.orderExport, param);
};

/**
 * @description 特殊价格导出
 * <AUTHOR>
 * @param filters 筛选条件
 */
user.specialPriceExport = (filters) => {
  return get(api.specialPriceExport, filters);
};

user.getUserProfitList = (param) => {
  return get(api.getUserProfitList, param);
};

user.getUserProfitDetail = (param) => {
  return get(api.getUserProfitDetail, param);
};
user.showDisableCommodity = (param) => {
  return get(api.showDisableCommodity, param);
};
user.setDisableCommodity = (param) => {
  return get(api.setDisableCommodity, param);
};
user.cancelDisableCommodity = (params) => {
  return get(api.cancelDisableCommodity, params);
};
// 导出用户毛利详情
user.exportUserProfit = (param) => {
  return get(api.exportUserProfit, param);
};

user.exportProfitStatList = (params) => {
  return get(api.exportProfitStatList, params);
};

user.exportUserOrderPdfTemplate = (params) => {
  return post(api.exportUserOrderPdfTemplate, params);
};

user.editPassword = (params) => {
  return post(api.editPassword, params);
};

user.statCancelEditPassword = (params) => {
  return post(api.statCancelEditPassword, params);
};

user.exportCommodityProviders = (params) => {
  return get(api.userProvider.exportCommodityProviders, params);
};
user.exportPurchaseCommodityProviders = (params) => {
  return get(api.exportPurchaseCommodityProviders, params);
};
// getUserTags
user.getUserTags = (params) => {
  return get(api.getUserTags, params);
};
// saveUserTags
user.saveUserTags = (params) => {
  return post(api.saveUserTags, params);
};
// delOrderTag
user.delOrderTag = function (params) {
  return post(api.delOrderTag, params);
};
// saveTagUsers
user.saveTagUsers = function (params) {
  return post(api.saveTagUsers, params);
};
user.addUserTagRecommendCommodity = function (params) {
  return post(api.addUserTagRecommendCommodity, params);
};
user.delUserTagRecommendCommodity = function (params) {
  return post(api.delUserTagRecommendCommodity, params);
};
// getUserProfileStat
user.getUserProfileStat = function (params) {
  return get(api.getUserProfileStat, params);
};
// exportUserProfileList
user.exportUserProfileList = function (params) {
  return get(api.exportUserProfileList, params);
};
user.exportTypeDisableCommodity = function (params) {
  return get(api.exportDisableCommodity, params);
};
user.UserAddressList = function (params) {
  return get(api.UserAddressList, params);
};
user.SaveUserAddress = function (params) {
  return get(api.SaveUserAddress, params);
};
user.DelUserAddress = function (params) {
  return get(api.DelUserAddress, params);
};
user.getBuyerType = function (params) {
  return get(api.getBuyerType, params);
};
user.getUserTagList = function (params) {
  return get(api.getUserTagList, params);
};
user.saveUserTag = function (params) {
  return post(api.saveUserTag, params);
};
user.delUserTag = function (params) {
  return post(api.delUserTag, params);
};
export default user;
