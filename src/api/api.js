// ====== 订单相关 ======

import siteAdminApi from '././apiPath/siteAdmin';
import RECIPE_MEAL_TIME_API from './urls/recipe/mealTime';
import wholesaleMarket_API from './urls/wholesaleMarket.js';
var api = Object.create(null);
var admin = '/superAdmin/';

/**
 * 其他API
 */
api.baseApi = {
  getCityData: '/area.json', // 获取城市数据
};

/*
 * 订单相关的 API
 */
api.QueryDeliverInfoByExpress100 =
  admin + 'orderSuper/QueryDeliverInfoByExpress100'; // 查询快递物流信息-快递100
api.saveCommodityOther = admin + 'commoditySuper/saveCommodityOther'; // 保存商品其他
api.getCommodityBySearch = admin + 'commoditySuper/ajaxGetCommodity'; // 获取商品列表（通过商品名、商品编码、商品拼音）
api.getCommodity = admin + 'commoditySuper/GetRateCommodity'; // 获取商品列表（通过商品名、商品编码、商品拼音）
api.getUserInfo = admin + 'userApi/query'; // 获取用户数据
api.getOrderCommodity = admin + 'orderSuper/ajaxGetOrderCommodity'; // 取订单商品信息
api.createOrder = admin + 'orderSuper/AjaxAdd'; // 新增后台订单
api.addSceneOrder = admin + 'orderSuper/spotPurchaseOrder'; // 新增后台现场采购订单
api.getOrderData = admin + 'orderSuper/AjaxModeAsTotal'; // 获取订单数量统计数据
api.getOrderList = admin + 'orderSuper/AjaxList'; // 获取订单列表
api.getAjaxListTotalAndPageParams =
  admin + 'orderSuper/getAjaxListTotalAndPageParams'; // 获取订单列表分页栏参数
api.orderListSearchConfig = admin + 'orderSuper/searchConfig'; // 获取订单列表搜索配置
api.getOrderDetail = admin + 'orderSuper/AjaxDetail'; // 获取订单详情
api.getOrderDetail = admin + 'orderSuper/AjaxDetail'; // 获取订单详情
api.exportOrder = admin + 'orderSuper/AjaxExport'; // 导出订单
api.getMoreInfo = admin + 'orderSuper/getJsonData'; // 获取用户和商品信息
api.getReturnInfo = admin + 'orderSuper/AjaxReturnCommodity'; // 获取退货信息
api.batchEdit = admin + 'orderSuper/batchEdit'; // 批量发货、批量完成
api.finish = admin + '/orderSuper/complete/'; // 订单完成
api.delOrder = admin + 'orderSuper/AjaxDelete'; // 删除订单
api.getModityOrder = admin + 'orderSuper/getModifyOrderCommodity'; // 获取修改订单
api.saveModityOrder = admin + 'orderSuper/AjaxSaveModifiedOrder'; // 保存修改后的订单
api.saveOrder = admin + 'orderSuper/saveOrderCommodity'; // 保存订单
api.getHistoryOrder = admin + 'orderSuper/ajaxGetHistoryOrder'; // 获取用户历史订单列表
api.getHistoryGoodsList = admin + 'orderSuper/ajaxCopyOrder'; // 从历史订单中获取商品列表
api.orderReturn = admin + 'orderReturn/add'; // 创建退货单
api.exportConditionData = admin + 'orderSuper/ExportConditionData'; // 订单按照条件导出
api.ajaxExportOrderCommodity = admin + 'orderSuper/AjaxExportOrderCommodity'; // 订单供货商供货明细导出接口
api.exportOrderList = admin + 'orderSuper/ExportOrderList'; // 订单清单导出
api.approval = admin + 'orderSuper/approval'; // 订单核算
api.editApproval = admin + 'orderSuper/editApproval'; // 订单核算
api.getOrderSummaryList = admin + 'purchase/getCommodityPool'; // 获得需要采购的商品池
api.getCommodityOrderList = admin + 'purchase/getCommodityOrderList'; // 获得需要采购商商品的订单列表
api.checkPurchaseOrder = admin + 'purchase/checkPurchaseOrder'; // 生成采购单检查
api.genPurchaseOrder = admin + 'purchase/genPurchaseOrder'; // 生成采购单
api.cancelCommodity = admin + 'purchaseOrder/cancelCommodity'; // 删除采购单商品
api.exportPurchaseOrder = admin + 'purchaseOrder/exportDetail'; // 删除采购单商品
api.updatePurchaseTax = admin + 'purchaseOrder/updateTax'; // 更新采购单税率
api.ExportOrderBatch = admin + 'purchaseOrder/ExportOrderBatch'; // 导出采购单
api.ExportModify = admin + 'orderModify/conditionExport'; // 导出实收变更列表
api.initCustomExportColumn = admin + 'general/initCustomExportColumn'; // 回复打印模板选项
api.getAbnormalList = admin + 'orderSuper/abnormalList'; // 获取异常订单列表
api.getAbnormalOrderDetail = admin + 'orderSuper/getAbnormalOrderDetail'; // 获取异常订单列表
api.batchAbnormal = admin + 'orderSuper/batchAbnormal'; // 批量处理异常（导入订单）
api.batchAudit = admin + 'orderSuper/audit'; // 批量订单审核
api.batchClose = admin + 'orderSuper/batchEdit'; // 批量订单关闭
api.batchComplete = admin + 'orderSuper/batchEdit';
api.batchImportTemplate = admin + 'orderSuper/batchImportTemplate'; // 批量订单审核
api.ocrRecognise = admin + 'orderSuper/ocrRecognise'; //拍照上传
api.getImgCommonditData = admin + 'orderSuper/getImgCommonditData'; //ocr获取图片商品详情
api.checkDeliveryUser = admin + 'orderSuper/CheckDeliveryUser'; // 异常订单检查
api.orderLog = admin + 'log/getYunList'; //日志列表
api.orderLogDetail = admin + 'log/getDetail'; //日志详情
api.getPurchaseAgent = admin + 'orderSuper/getPurchaseAgent'; //获取采购员列表
api.getPurchaseAgentList = admin + 'purchaseAgent/list'; //获取采购员列表
api.purchaseAgentDisable = admin + 'purchaseAgent/Disable';
api.purchaseAgentDelete = admin + 'purchaseAgent/delete';
api.getDriverList = admin + 'driver/list'; //获取司机列表
api.exportOrders = admin + '/orderSuper/ExportConditionData'; // 批量导出订单
api.addOrderTag = admin + 'orderTag/add'; // 订单添加标签
api.qryOrderTagList = admin + 'orderTag/getList'; // 订单标签列表
api.AddExpressNo = admin + 'orderSuper/AddExpressNo'; // 订单关联快递100
api.providerBindSplitOrderTag = admin + 'orderSuper/providerBindSplitOrderTag'; // 供应商绑定拆单标签

api.editOrderTag = admin + 'orderTag/edit'; // 订单修改标签
api.delOrderTagSingle = admin + 'orderTag/del'; // 订单删除标签
api.getOrderTag = admin + 'orderTag/getOrderTag'; // 查询标签状态
api.openOrderTag = admin + 'orderTag/openOrderTag'; // 切换订单状态
// SaveTagUsers
api.saveTagUsers = admin + 'orderTag/SaveTagUsers'; // 保存标签用户
api.delOrderTag = admin + 'orderTag/DelTagUsers'; // 订单删除标签
api.saveOrderTags = admin + 'orderSuper/tagging'; // 保存订单标签
api.getOrderSuperAaiSearch = admin + 'orderSuper/aiRecording'; // 订单文字识别
api.getOrderSuperAaiSearchOlder = admin + 'orderSuper/aiSearch'; // 订单文字识别(旧版)
api.orderImageOcr = admin + 'orderSuper/orderImageOcr'; // 订单图像识别

api.accountRemind = admin + 'userApi/accountRemind'; // 订单获取客户状态
api.orderCombineCheck = admin + 'orderSuper/orderCombineCheck'; //订单合并检查
api.orderCombine = admin + 'orderSuper/orderCombine'; //订单合并
api.orderSuperAjaxGetMultiOrderCommodity =
  admin + 'orderSuper/ajaxGetMultiOrderCommodity';
api.getOrderReturnPrintData = admin + '/orderReturn/printData'; // 退货单打印数据
api.logGetYunList = admin + 'log/getYunList'; // new日志列表
api.logGetModuleMap = admin + 'log/getModuleMap'; // 操作模块枚举列表
api.logGetTypeMap = admin + 'log/getTypeMap'; // 操作类型枚举列表
api.getOrderDirectPurchaseList =
  admin + 'orderSuper/getOrderDirectPurchaseList'; // 直采单列表
api.closeOrderDirectPurchase = admin + 'orderSuper/closeOrderDirectPurchase'; // 关闭直采单
api.getOrderDirectPurchaseInfo =
  admin + 'orderSuper/getOrderDirectPurchaseInfo'; // 直采单详情
api.addOrderDirectPurchase = admin + 'orderSuper/addOrderDirectPurchase'; // 新增直采单
api.updateOrderDirectPurchase = admin + 'orderSuper/updateOrderDirectPurchase'; // 编辑直采单
api.reverseAudit = admin + 'orderSuper/reverseAudit'; // 追加修改
api.orderReturnSingleExport = admin + 'orderReturn/singleExport'; // 退货单单个导出
//智能定价相关
api.CommodityCategory = admin + 'commodityIntelligentPrice/CommodityCategory'; // 获取智能定价商品分类
api.OfflineExportCommodityPriceHistory =
  admin + 'commodityIntelligentPrice/OfflineExportCommodityPriceHistory'; // 导出历史定价
api.batchSetConfig = admin + 'commodityIntelligentPrice/batchSetConfig'; // 获取批量设置配置
api.getIntelligentPriceList = admin + 'commodityIntelligentPrice/getList'; // 获取智能定价列表数据
api.getExecList = admin + 'commodityIntelligentPrice/getExecList'; // 获取智能定价列表数据，跟上个接口返回一样的数据，这里只是为了做权限区分才分成两个接口
api.FormulaDetail = admin + 'commodityIntelligentPrice/FormulaDetail'; // 根据商品id获取公式详情
api.batchSetFormula = admin + 'commodityIntelligentPrice/batchSetFormula'; // 批量设置公式
api.getPriceHistory = admin + 'commodityIntelligentPrice/priceHistory'; // 批量设置公式
api.syncCommodity = admin + 'commodityIntelligentPrice/syncCommodity'; // 同步商品价格
api.syncOrderPrice = admin + 'commodityIntelligentPrice/syncOrderPrice'; // 同步订单价格
api.editCommodityInPrice =
  admin + 'commodityIntelligentPrice/editCommodityInPrice'; // 更新商品最近一次进价
api.priceHistoryDetail = admin + 'commodityIntelligentPrice/priceHistoryDetail'; // 获取定价历史详情
api.IntelligentPriceExport = admin + 'commodityIntelligentPrice/export'; // 导出
api.syncDefaultFormula = admin + 'commodityIntelligentPrice/syncDefaultFormula'; // 同步预设公式
// api.getFormulaDetail = admin + 'commodityIntelligentPrice/FormulaDetail'; // 获取预设公式详情
// api.setBatchSetFormula = admin + 'commodityIntelligentPrice/batchSetFormula'; // 保存预设公式
api.saveCustomizeFormula =
  admin + 'commodityIntelligentPrice/saveCustomizeFormula'; // 保存自定义公式
api.customizeFormulaInfo =
  admin + 'commodityIntelligentPrice/customizeFormulaInfo'; // 获取自定义公式信息
api.deleteCustomizeFormula =
  admin + 'commodityIntelligentPrice/deleteCustomizeFormula'; // 删除自定义公式
api.commodityCustomizeFormulaOfflineImport =
  admin + 'commodityIntelligentPrice/commodityCustomizeFormulaOfflineImport'; // 导入商品自定义公式（离线）
api.downloadCommodityCustomizeFormulaTemplate =
  admin + 'commodityIntelligentPrice/downloadCommodityCustomizeFormulaTemplate'; // 下载商品自定义公式模板
api.retrySync = admin + 'commodityIntelligentPrice/RunSyncRetry'; // 智能定价失败重试
api.exportOrderCategorySummary =
  admin + 'orderSuper/exportOrderCategorySummary'; // 导出订单分类汇总表（誉美定制）
// 预设公式功能
api.presetFormulaPaginate =
  admin + 'commodityIntelligentPrice/presetFormulaPaginate'; // 预设公式列表
api.savePresetFormula = admin + 'commodityIntelligentPrice/savePresetFormula'; // 保存预设公式
api.deletePresetFormula =
  admin + 'commodityIntelligentPrice/deletePresetFormula'; // 删除预设公式

//售后、退货相关
api.getOrderReturnList = admin + 'orderReturn/list'; // 获取退货单列表
api.getOrderHistoryList = admin + 'orderReturn/historyOrder'; // 获取历史退货单列表
api.getOrderHistoryDetail = admin + 'orderReturn/orderDetail'; // 获取历史退货单详情
api.addNewOrderReturn = admin + 'orderReturn/insert'; // 获取历史退货单详情
api.editOrderReturn = admin + 'orderReturn/edit'; // 修改退货单
api.getOrderReturnDetail = admin + 'orderReturn/detail'; // 获取退货单详情
api.orderReturnAudit = admin + 'orderReturn/Audit'; // 获取退货单详情
api.orderRefuseAudit = admin + 'orderReturn/refuse'; // 审核拒绝
api.orderPassAudit = admin + 'orderReturn/batchEdit'; // 审核通过
api.orderConfirmRefund = admin + 'orderReturn/confirmRefund'; // 退货单退款
api.orderReturnExport = admin + 'orderReturn/singleExport'; // 退货单导出

// 订单刷价相关api
api.batchBrushPriceUrl = admin + 'OrderSuper/BatchBrushPrice'; //批量刷价
api.getBrushHistory = admin + 'orderSuper/getBrushOrderPriceLog'; //获取刷价历史
api.getBrushNo = admin + 'orderSuper/getBrushNo'; //获取批量刷价批次号
api.getCanRecoverObplId = admin + 'orderSuper/getCanRecoverObplId'; // 获取批量刷价单批次可恢复的记录id
api.recoverBrushPrice = admin + 'OrderSuper/recoverOrderBrushPrice'; //恢复刷价
api.getRecoverFailList = admin + 'orderSuper/getRecoverFailOrderLog'; //获取恢复失败列表

api.reportCSM = admin + 'general/Visit'; //csm数据上报

//首页
api.getIndexData = admin + 'home/indexData'; // 获取首页详情
api.getChartData = admin + 'home/chartData'; // 获取首页图标数据
api.getPreWarnList = admin + 'home/preWarnList'; // 获取首页预警信息
api.getPreviewTodayList = admin + 'home/previewTodayList'; // 获取首页今日数据
api.getTodoList = admin + 'home/todoList'; // 获取首页待办事项
api.getYesterdaySaleCateMap = admin + 'home/YesterdaySaleCateMap'; // 昨日配送品类占比
api.getProductVersionList = admin + 'home/ProductVersionList'; // 产品列表
api.getOpChartData = admin + 'home/opChartData'; // 首页-经营数据图
api.getCateSaleMap = admin + 'home/cateSaleMap'; // 首页-分类销售占比
api.getUserSalesTop = admin + 'home/userSalesTop'; // 首页-客户销售排行
api.getUserOrderData = admin + 'home/UserOrderData'; // 首页-客户数据

//登录
api.login = admin + 'loginSuper/login'; // 登录获取token
api.getCode = admin + 'loginSuper/verifyCode'; // 获取验证码
api.logOut = admin + 'loginSuper/logout'; // 登出
api.getLoginCompanyInfo = admin + 'loginSuper/company'; // 获取公司信息
api.authorize = admin + 'loginSuper/authorize'; // 自动登录
api.SendVerifyCode = admin + 'adminSuper/SendVerifyCode'; // 登录绑定手机号，发送验证码
api.BindMobile = admin + 'adminSuper/BindMobile'; // 登录绑定手机号
api.ResetPassword = admin + 'adminSuper/ResetPassword'; // 忘记密码
api.getUserByName = admin + 'adminSuper/getUserByName'; // 忘记密码根据用户名查手机号
/*
 * 代开订单模块
 */
api.createBehalfOrder = admin + 'behalfSuper/AjaxAdd'; // 新增后台订单
api.getBehalfOrderList = admin + 'behalfSuper/AjaxList'; // 获取代开订单列表
api.getModityBehalfOrder = admin + 'behalfSuper/getModifyOrderCommodity'; // 获取修改代开订单
api.getBehalfOrderCommodity = admin + 'behalfSuper/ajaxGetOrderCommodity'; // 获取代开订单商品信息
api.getBehalfOrderDetail = admin + 'behalfSuper/AjaxDetail'; // 获取代开订单详情
api.exportConditionBehalfData = admin + 'behalfSuper/ExportConditionData'; // 订单按照条件导出
api.delBehalfOrder = admin + 'behalfSuper/AjaxDelete'; // 删除订单
api.saveModityBehalfOrder = admin + 'behalfSuper/AjaxSaveModifiedOrder'; // 保存修改后的订单
api.getBehalfOrderStatus = admin + 'behalfSuper/getBehalfOrderStatus'; // 获取订单所有状态
api.finishBehalf = admin + 'behalfSuper/complete/'; // 订单完成
api.getBehalfHistoryGoodsList = admin + 'behalfSuper/ajaxCopyOrder'; // 从历史订单中获取商品列表

/*
 * 通用配置
 */
api.getAdminMenu = admin + 'frame/menu'; // 获取后台菜单配置
api.batchShelve = admin + 'commodityBase/batchUp'; // 批量上架
api.batchOffShelve = admin + 'commodityBase/batchDown'; // 批量下架
api.batchDelete = admin + 'commodityBase/batchDelete'; // 批量删除商品
api.getTopMenu = admin + 'frame/sysMenu'; // 获取顶部菜单栏
api.getNoticeCounter = admin + 'general/getNoticeCounter'; // 获取消息计数器
api.getNoticeBox = admin + 'general/getNoticeBox'; // 获取消息队列
api.updateNotice = admin + 'general/updateNotice'; // 消息全部已读
api.cleanNotice = admin + 'general/cleanNotice'; // 清空消息
api.getSearchConfig = admin + 'orderSuper/searchConfig'; // 获取订单列表搜索配置
api.getVersionList = admin + 'general/editionList'; //获取版本列表
api.saveVersion = admin + 'general/switchEdition'; //保存版本

api.getNoticeQr = admin + 'notice/qr'; //获取公众号二维码
api.getNoticeNodeList = admin + 'notice/nodeList'; //获取消息节点列表
api.saveNoticeSaveNodeEnable = admin + 'notice/saveNodeEnable'; //保存消息节点启用开关
api.getNoticeNodeDesc = admin + 'notice/nodeDesc'; //获取消息节点详情
api.saveNoticeNodeDesc = admin + 'notice/saveNodeDesc'; //保存消息节点详情
api.getProviderVsBuyerNodeList = admin + 'notice/pANodeList'; // 获取供应商/采购员短信配置
api.saveProviderVsBuyerNodeEnable = admin + 'notice/savePANodeEnable'; // 保存供应商/采购员短信配置
api.saveProviderAccountInfo = admin + 'ProviderAccount/SaveProviderAccountInfo'; // 供应商进件
api.searchBankList = admin + 'ProviderAccount/SearchBankList'; // 供应商进件的银行列表

/*
 * 商品
 */
api.editPurchase = admin + 'commoditySuper/editPurchase'; // 批量修改商品信息
api.syncGoods = admin + 'commoditySuper/syncSellStockCommodity'; // 同步商品售卖库存
api.getSyncGoodsAmount = admin + 'commoditySuper/getSellStockCommodityCount'; // 获得待同步售卖库存的商品数量
api.getGoodsList = admin + 'commoditySuper/ajaxList'; // 获取商品列表
api.getGoodsCategory = admin + 'commoditySuper/ajaxGetCategory'; // 获取商品分类
api.getGoodsCategoryTree = admin + 'categorySuper/tree'; // 获取一级和二级分类
api.addCategory = admin + 'categorySuper/add'; // 新增商品分类
api.editCategory = admin + 'categorySuper/edit'; // 新增商品分类
api.genCommodityCodeIncreaseMode =
  admin + 'commoditySuper/genCommodityCodeIncreaseMode'; // 获取按（分类编码+递增数）模式下的商品编码
api.searchCommodity = admin + 'customProvider/searchCommodity'; // 指定供应商 获取商品列表
api.bindCommodityImg = admin + 'commoditySuper/bindCommodityImg'; // 商品绑定图片
api.getWatermark = admin + 'commoditySuper/getWatermark'; // 获取水印位置
api.updateWatermark = admin + 'commoditySuper/updateWatermark'; // 保存水印位置

api.delCategory = admin + 'categorySuper/delete'; // 删除商品分类
api.delGoods = admin + 'commoditySuper/deleteCommoditys'; // 删除商品
api.checkIsExistStock = admin + 'commoditySuper/checkIsExistStock'; // 检查基础单位是否有库存
api.checkSkuIsExistStock = admin + 'commoditySuper/checkSkuIsExistStock'; // 检查商品规格是否含有库存
api.changeShelvesStatus = admin + 'commoditySuper/editOnline'; // 修改上下架状态
api.batchUpdateCommodity = admin + 'commoditySuper/batchUpdateCommodity'; // 批量编辑商品基础信息
api.getPurchaseType = admin + 'commoditySuper/ajaxGetPurchaseType'; // 获取采购方式
api.searchGoods = admin + 'commoditySuper/ajaxGetCommodity'; // 搜索商品
api.getPurchaseInfo = admin + 'commoditySuper/ajaxGetPurchaseType'; // 获取采购员和供应商信息
api.saveGoods = admin + 'commoditySuper/saveCommodity'; // 保存商品
api.getGoodsData = admin + 'commoditySuper/getInitData'; // 获取商品相关信息
api.brushPrice = admin + 'commoditySuper/brushPrice'; // 商品批量刷价
api.getGoodsDetail = admin + 'commoditySuper/getCommodityDetail'; // 获取商品详情
api.getGoodsImg = admin + 'commoditySuper/getCommodityImg'; // 获取商品图片
api.getFormatDetail = admin + 'commoditySuper/GetCommoditySkuList'; // 获取规格详情
api.getGoodsUnit = admin + 'commoditySuper/getUnit'; // 获取商品单位
api.getGoodsTag = admin + 'commoditySuper/getTag'; // 获取商品标签
api.getCommodityStorePurchase =
  admin + 'commoditySuper/getCommodityStorePurchase'; // 获取商品多仓采购配置
api.saveCommodityStorePurchase =
  admin + 'commoditySuper/saveCommodityStorePurchase'; // 保存商品多仓采购配置
api.saveGoodsUnit = admin + 'commoditySuper/saveUnit';
api.saveGoodsTag = admin + 'commoditySuper/saveTag';
api.delGoodsTag = admin + 'commoditySuper/delTag';
api.delGoodsUnit = admin + 'commoditySuper/delUnit';
api.getGoodsImgList = admin + 'commoditySuper/imgData'; // 获取商品图片列表
api.batchDelImg = admin + 'commoditySuper/batchDelImg'; // 获取商品图片列表
api.preOneClickMatchCloudImg =
  admin + 'commoditySuper/preOneClickMatchCloudImg'; // 获取没有匹配的商品图片
api.oneClickMatchCloudImg = admin + 'commoditySuper/oneClickMatchCloudImg'; // 获取没有匹配的商品图片
api.oneClickApplyCloudImg = admin + 'commoditySuper/oneClickApplyCloudImg'; // 一键应用
api.saveCommodityImg = admin + 'commoditySuper/saveCommodityImg'; // 保存图片
api.saveStageCommodityCloudImg =
  admin + 'commoditySuper/saveStageCommodityCloudImg'; // 保存图片
api.delGoodsImg = admin + 'commoditySuper/delImg';
api.uploadGoodsImg = admin + 'upload/commodityImgUpload';
api.uploadImg = admin + 'upload/uploadImg';
api.exportImgNameData = admin + 'commoditySuper/exportImgNameData'; // 导出商品图片
api.autoCode = admin + 'commoditySuper/getCommodityCode';
api.getAllCommodity = admin + 'commoditySuper/GetAllCommodity';
api.ModifyStock = admin + 'commoditySuper/ModifyStock';
api.providerTemplate = admin + 'provider/template'; // 下载 供应商基础资料模版
api.saveSequence = admin + 'commoditySuper/saveSequence'; // 保存商品商城排序
api.saveUnitSort = admin + 'commoditySuper/saveUnitSort'; // 保存商品单位排序
api.checkHasExistBussinessData = admin + 'commoditySuper/IsExistBussinessData'; // 修改的商品是否含有业务数据
api.initLastOrderTime = admin + 'commoditySuper/initLastOrderTime'; // 初始化商品最后一次下单时间

api.counterAudit = admin + 'commodityTrace/AntiAudit'; // 溯源反审核
// https://api.movee.cn/project/162/interface/api/42059
api.getCustomizeFieldKeys =
  admin + 'commodityCustomizeField/customizeFieldKeys'; // 获取自定义字段键值列表（受开关影响）

/*
 * 配置中心
 */
api.getSystemConfig = admin + 'sysconfig/list'; // 获取系统配置
api.saveSystemConfig = admin + 'sysconfig/edit'; // 保存系统配置
api.getIsMallApp = admin + 'sysconfig/isMallApp'; // 获取是否启用了 APP 商城
api.getOperatingTimeList = admin + 'OperatingTime/getOperatingTimeList'; // 获得运营时段列表
api.addOperatingTime = admin + 'OperatingTime/addOperatingTime'; // 增加 运营时段
api.updateOperatingTime = admin + 'OperatingTime/updateOperatingTime'; // 增加 运营时段
api.deleteOperatingTime = admin + 'OperatingTime/deleteOperatingTime'; // 删除 运营时段
api.saveSingleConfig = admin + 'general/editSysConfig'; // 保存单个系统配置
api.getsysConfigRegisterConfig = admin + 'sysconfig/registerConfig'; // 获取注册配置
api.commodityAttrConfig = admin + 'sysconfig/commodityAttrConfig'; // 获取商品属性配置
api.attrConfigSave = admin + 'sysconfig/attrConfigSave'; // 商品属性保存
api.postregisterSave = admin + 'sysconfig/registerSave'; // 保存注册配置
api.centralPurchaseSite = admin + 'sysconfig/centralPurchaseSite'; // 集采平台配置信息获取
api.centralPurchaseSiteSave = admin + 'sysconfig/centralPurchaseSiteSave'; // 保存集采平台配置信息
api.EditSysConfig = admin + 'General/EditSysConfig'; // 对公账户添加、编辑接口
api.getAccountCard = admin + 'sysconfig/getAccountCard'; // 新增获取对公账户信息接口
api.getConfigLastEditInfo = admin + 'sysconfig/getConfigLastEditInfo'; // 获取最一次开启时间
/*
 * 客户
 */
api.getUserAjaxList = admin + 'receStyle/ajaxList'; // 获取客户类型列表
api.getUserTypeList = admin + 'receStyle/list'; // 获取客户类型列表
api.delUserType = admin + 'receStyle/delete'; // 删除客户类型
api.editUserType = admin + 'receStyle/edit'; // 编辑客户类型
api.newUserType = admin + 'receStyle/new'; // 新增客户
api.exportUserTypeReportPrice = admin + 'receStyle/exportQuotation/new'; //导出客户类型报价单
api.exportDisableCommodity = admin + 'receStyle/ExportDisableCommodity'; // 导出客户类型屏蔽商品
api.getUserGroup = admin + 'userGroup/list'; // 集团用户列表
api.NewUserGroup = admin + 'userGroup/new'; // 新增集团用户
api.delUserGroup = admin + 'userGroup/delete'; // 删除集团用户
api.editUserGroup = admin + 'userGroup/edit'; // 编辑集团用户

api.getUserPanel = admin + 'userPanel/getData'; // 客户看板
api.getIncrementTrend = admin + 'userPanel/getIncrementTrend'; // 客户看板趋势

api.getSingleCase = admin + 'statUser/orderList'; // 下单情况
api.orderExport = admin + 'statUser/orderExport'; // 下单情况导出excel

api.statUserOffLineOrderExport = admin + 'statUser/OffLineOrderExport'; // 客户下单情况离线导出
api.getOrderHistory = admin + 'orderStatistics/getData'; // 获取订货历史数据
api.getUserList = admin + 'orderStatistics/getUserList'; // 获取订货历史用户列表
api.getOrderUser = admin + 'orderStatistics/getUserList'; // 获取订货历史用户列表
api.singleHistory = admin + 'statUser/orderHistory'; // 下单历史
api.doExport = admin + 'orderStatistics/doExport'; // 导出订货历史数据

api.modifyPayment = admin + 'userSuper/remindUpdate'; // 修改账期
api.getPayment = admin + 'userSuper/remindShow'; // 获取账期数据
api.userImport = admin + 'userSuper/userImport'; // 导出用户excel
api.userExport = admin + 'userSuper/userExport'; // 导出用户excel
api.userDisabledCommodityExport = admin + 'userSuper/ExportDisabledCommodity'; // 导出客户屏蔽商品
api.areaAndGroupDisabledCommodityExport =
  admin + 'commodityDisabled/exportDisableCommodity'; // 导出集团、区域屏蔽商品
api.exportTemplate = admin + 'userSuper/userTemplate'; // 下载模版
api.specialPriceExport = admin + 'userSuper/specialPriceExport'; // 导出用户excel

// 客户行为分析统计 userProfileStat
api.getUserProfileStat = admin + 'userSuper/userProfileStat'; // 获取客户行为分析统计
api.exportUserProfileList = admin + 'userSuper/exportUserProfileList'; // 导出客户行为分析统计
api.getUserList = admin + 'userSuper/list'; // 获取客户列表
api.batchDeleteUser = admin + 'userSuper/batchDelete'; // 批量删除客户
api.batchEditUser = admin + 'userSuper/batchEdit'; // 批量编辑客户
api.getUserTags = admin + 'userSuper/GetUserTags'; // 获取专属订单标签
api.saveUserTags = admin + 'userSuper/SaveUserTags'; // 保存专属订单标签
api.getAreaList = admin + 'userSuper/ajaxAreaList'; // 获取区域
api.getGroupList = admin + 'userSuper/ajaxGroupList'; // 获取集团列表
api.getSalesList = admin + 'userSuper/ajaxSalesList'; // 获取业务员
api.getDeliveryDateList = admin + 'userSuper/ajaxDeliveryTimeList'; // 获取送货时间
api.getPrintTemplateList = admin + 'userSuper/ajaxPrtTplList'; // 获取打印模板
api.setNewUser = admin + 'userSuper/new'; // 新增客户信息
api.modifyUser = admin + 'userSuper/edit'; // 修改客户信息
api.setLocation = admin + 'userSuper/setLocation'; // 修改客户经纬度
api.delUser = admin + 'userSuper/delete'; // 删除客户信息
api.getUserDetail = admin + 'userSuper/detail'; // 获取客户详情
api.getUnavailableGoods = admin + 'userSuper/disabledList'; // 获取不可用商品
api.getUnavailableGoodsByArea = admin + 'commodityDisabled/disabledList'; // 获取不可用商品-区域
api.getUnavailableGoodsByGroup =
  admin + 'commodityDisabled/categoryDisabledList'; // 获取不可用商品-集团
api.getUnavailableGoodsByGroupArea =
  admin + 'commodityDisabled/categoryDisabledList'; // 获取可屏蔽和已屏蔽的分类（集团或者区域屏蔽/售卖商品用）
api.setUnavailableGoods = admin + 'userSuper/disabledNew'; // 新增不可用商品
api.setUnavailableType = admin + 'userSuper/switchDisableType'; // 设置屏蔽商品类型
api.setNominatedSupplier = admin + 'customProvider/switchBindType'; // 设置指定供应商类型
api.addDisabledGoods = admin + 'userSuper/addDisabledGoods'; // 根据筛选条件添加不可用商品
api.addUserTypeDisabledGoods = admin + 'receStyle/addDisabledGoods'; // 根据筛选条件添加不可用商品
api.delUnavailableGoods = admin + 'userSuper/disabledDelete'; // 删除不可用商品
api.getSpecialPriceList = admin + 'userSuper/specialPriceList'; // 获取客户特殊价格
api.setSpecialPrice = admin + 'userSuper/specialPriceNew'; // 新增客户特殊价
api.modifySpecialPrice = admin + 'userSuper/specialPriceEdit'; // 修改客户特殊价格
api.delSpecialPrice = admin + 'userSuper/specialPriceDelete'; // 删除客户特殊价格
api.exportUsualGoods = admin + 'userSuper/exportCollectionList'; // 常用商品导出
api.getUsualGoods = admin + 'userSuper/collectionList'; // 获取常用商品
api.setUsualGoods = admin + 'userSuper/collectionNew'; // 新增常用商品
api.delUsualGoods = admin + 'userSuper/collectionDelete'; // 删除常用商品
api.addUserTagRecommendCommodity =
  admin + 'userSuper/addUserTagRecommendCommodity'; // 新增客户标签推荐商品
api.delUserTagRecommendCommodity =
  admin + 'userSuper/delUserTagRecommendCommodity'; // 删除客户标签推荐商品
api.collectionAddPrint = admin + 'userSuper/collectionPrint'; // 批量操作是否加入手工单打印
api.exportUserOrderPdfTemplate = admin + 'userSuper/exportUserOrderPdfTemplate'; // 导出用户手工单模板

// 指定供应商
api.saveUserGoodsProvider = admin + 'customProvider/bindCommodityProvider'; // 保存用户供应商商品
api.batchAddCommodity = admin + 'customProvider/batchAddCommodity'; // 保存用户筛选商品
api.getUserGoodsProvider = admin + 'customProvider/commodityProviders'; // 保存用户供应商商品
api.deleteUserGoodsProvider = admin + 'customProvider/deleteBindRelation'; // 删除用户供应商商品
// 采购任务指定供应商
api.setPurchaseNominatedSupplier =
  admin + 'PurchaseTaskCustomProvider/switchBindType'; // 设置指定供应商类型
api.saveUserPurchaseGoodsProvider =
  admin + 'PurchaseTaskCustomProvider/bindCommodityProvider'; // 保存用户供应商商品
api.batchAddPurchaseCommodity =
  admin + 'PurchaseTaskCustomProvider/batchAddCommodity'; // 保存用户筛选商品
api.getUserPurchaseGoodsProvider =
  admin + 'PurchaseTaskCustomProvider/commodityProviders'; // 保存用户供应商商品
api.deleteUserPurchaseGoodsProvider =
  admin + 'PurchaseTaskCustomProvider/deleteBindRelation'; // 删除用户供应商商品
api.exportPurchaseCommodityProviders =
  admin + 'PurchaseTaskCustomProvider/exportCommodityProviders';

api.getUserProfitList = admin + 'profitStatics/userStatic'; // 获取客户毛利列表
api.getUserProfitDetail = admin + 'statSuper/ProfitStatList'; // 获取客户毛利详情
api.exportProfitDetail = admin + 'report/exportCommodityProfitList'; // 获取客户毛利详情
api.exportUserProfit = admin + 'profitStatics/exportUserStatic'; // 导出客户毛利
api.exportProfitStatList = admin + 'statSuper/exportProfitStatList'; //导出毛利详情
api.showDisableCommodity = admin + 'receStyle/showDisableCommodity'; // 显示类型不可用商品
api.setDisableCommodity = admin + 'receStyle/setDisableCommodity'; // 新增类型不可用商品
api.cancelDisableCommodity = admin + 'receStyle/cancelDisableCommodity'; // 删除类型不可用商品
api.getsubaccountList = admin + 'userSuper/subaccountList'; // 子账号列表
api.addSubaccount = admin + 'userSuper/addSubaccount'; // 添加子账号
api.updateSubaccount = admin + 'userSuper/updateSubaccount'; // 编辑子账号
api.deleteSubaccount = admin + 'userSuper/deleteSubaccount'; // 删除客户子账号
api.deleteSubaccountRecycle = admin + 'recycle/DelSubUser'; // 删除回收站子账号
api.recoverSubaccount = admin + 'recycle/recoverSubUser'; // 恢复子账号
api.exportProfitStatList = admin + 'statSuper/exportProfitStatList'; // 导出毛利详情
api.editPassword = admin + 'loginSuper/editPassword'; // 重置密码
api.statCancelEditPassword = admin + 'loginSuper/statCancelEditPassword'; //统计点击'下次重置'的数量
api.SaveUserAddress = admin + 'userSuper/SaveUserAddress'; // 保存地址
api.DelUserAddress = admin + 'userSuper/DelUserAddress'; // 删除地址
api.UserAddressList = admin + 'userSuper/UserAddressList'; // 地址列表
api.getBuyerType = admin + 'userSuper/getBuyerType'; // 获取客户性质

api.performanceSalerankList = admin + 'performance/salerankList'; // 业务员排名
api.performanceSaleList = admin + 'performance/saleList'; // 业务员排名
api.performanceSaleDetailList = admin + 'performance/SaleDetailList'; // 按客户详情列表

api.performanceSaleDetailCategoryList =
  admin + 'performance/saleDetailCategoryList'; // 按分类详情列表
api.SalesDetailVisitRecord = admin + 'performance/SalesDetailVisitRecord'; // 按拜访记录

api.performanceSaleExport = admin + 'performance/exportSale'; // 业务员绩效导出

api.generalCustomExportColumn = admin + 'general/customExportColumn'; // 导出

// 屏蔽商品
api.ImportDisabledCommodityTemplate =
  admin + 'userSuper/ImportDisabledCommodityTemplate'; // 屏蔽商品 下载模板接口
api.ImportDisabledCommodity = admin + 'userSuper/ImportDisabledCommodity'; // 屏蔽商品部分  导入接口

// 企业微信
api.getCorpTagList = '/qyweixinApi/Qwadmin/GetCorpTagList';
api.GetQywxConfig = '/qyweixinApi/Qwadmin/GetQywxConfig';
api.updateQywxConfig = '/qyweixinApi/Qwadmin/updateQywxConfig';
api.editCorpTag = '/qyweixinApi/Qwadmin/editCorpTag';
api.delCorpTag = '/qyweixinApi/Qwadmin/delCorpTag';
api.AddCorpTag = '/qyweixinApi/Qwadmin/AddCorpTag';
api.userInfo = '/qyweixinApi/Qwadmin/userInfo'; // 客户详情-企业微信信息
api.trackRecord = '/qyweixinApi/Qwadmin/trackRecord'; // 客户跟进
api.departmentList = '/qyweixinApi/Qwadmin/departmentList'; // 获取部门列表
api.departmentUser = '/qyweixinApi/Qwadmin/departmentUser'; // 获取部门成员详情
api.boundUser = '/qyweixinApi/Qwadmin/boundUser'; // 绑定企业微信
api.unbundUser = '/qyweixinApi/Qwadmin/unbundUser'; // 解绑
api.MarkTag = '/qyweixinApi/Qwadmin/MarkTag'; // 设置标签
// 企业微信新版
api.getAuthorizeLink = '/qyweixinApi/Qwadmin/getAuthorizeLink'; // 授权链接
api.getAuthorizeInfo = '/qyweixinApi/Qwadmin/getAuthorize'; // 授权状态

//新品需求
api.getNeedList = admin + 'commodityDemandSuper/ajaxList';
api.removeNeedItem = admin + 'commodityDemandSuper/delteCommodityDemand';
api.editNeedItem = admin + 'commodityDemandSuper/saveReply';
api.getGoodsTree = admin + 'categorySuper/tree'; // 获取商品分类树
api.getGoodsCategoryList = admin + 'categorySuper/list'; // 获取商品分类平铺列表
//订单批量核价
api.pageEditPriceNeedData = admin + 'orderChangePrice/pageEditPriceNeedData';

/**
 * 基础数据
 */
// api.getStores = admin + 'store/getStores'; // 获取所有库房 已与后端确认 没有此接口
api.getFields = admin + 'general/customExportColumn'; // 获取所有导出字段
api.updateFields = admin + 'general/updateCustomExportColumn'; // 更新导出字段
/**
 * 采购相关api
 */
api.getBaseCommoditysAndPrice =
  admin + 'commoditySuper/getBaseCommoditysAndPrice'; //
api.getPurchaseCommoditys = admin + 'commoditySuper/getPurchaseCommoditys';
api.getMoreOrderInfo = admin + 'purPlan/getNewMoreInfo'; //
api.getPurchaseOrderStatusMap =
  admin + 'purchaseOrder/getPurchaseOrderStatusMap'; // 获得采购单列表页面
api.getPurchaseOrderList = admin + 'purchaseOrder/orderPage'; // 获得采购单列表页面
api.getPurchaseOrderDetail = admin + 'purchaseOrder/detail'; // 获得采购单详情
api.getPurchaseOrderGoodsDetail = admin + 'purchaseOrder/orderDetail'; // 获得采购单商品列表
api.purchaseReceipt = admin + 'purchaseOrder/receipt'; // 采购单收货
api.addPurchaseOrder = admin + 'purchaseOrder/new'; // 新增采购单
api.editPurchaseOrder = admin + 'purchaseOrder/save'; // 保存采购单
api.auditPurchaseOrder = admin + 'purchaseOrder/audit'; // 审核采购单
api.savePurchaseOrder = admin + 'purchaseOrder/saveStageData'; // 保存采购单
api.printPurchaseOrder = admin + 'purchaseOrder/print'; // 打印采购单
api.getPurchaseReturnList = admin + 'purchaseReturn/orderPage'; // 获得采购退货单列表页面
api.purchaseReturnDetail = admin + 'purchaseReturn/detail'; // 采购单退货详情
api.addPurchaseReturn = admin + 'purchaseReturn/new'; // 新增采购退货单
api.addPurchaseNewAudit = admin + 'purchaseReturn/newAudit';
api.savePurchaseAudit = admin + 'purchaseReturn/saveAudit';
api.purchaseAntiAudit = admin + 'purchaseReturn/antiAudit';
api.savePurchaseReturn = admin + 'purchaseReturn/save'; // 保存采购退货单
api.getProvider = admin + 'providerHistory/getProviders'; // 获取供应商
api.orderPoolExport = admin + 'purchase/orderPoolExport'; // 导出订单汇总
api.getCommodityInfo = admin + 'purchaseOrder/getCommodityInfo'; // 获取采购商品补充信息
api.getCommoditySpareProviderList =
  admin + 'commoditySuper/getCommoditySpareProviderList'; // 获取指定商品备用供应商
api.closeOrder = admin + 'purchaseOrder/cancel'; // 关闭采购单
api.closeReturnOrder = admin + 'purchaseReturn/cancel'; // 关闭采购退货单
api.orderPageExport = admin + 'purchaseReturn/OrderPageExport'; // 采购退回导出
api.exportOrderTemplate = admin + 'purchaseOrder/exportTemplate'; // 导出采购模板
// 这个好像已经被覆盖了
api.getProviderList = admin + 'provider/list'; //获取供应商列表
api.getProviderOldList = admin + 'provider/list'; //获取供应商列表
api.provierDisable = admin + 'provider/Disable';
api.batchUpdateProvider = admin + 'provider/batchUpdate'; //批量更新供应商
api.getProviderAllIds = admin + 'provider/ids'; // 获取所有供应商id
api.purchaseProfitDetail = admin + 'purchaseProfit/detail';
api.purchaseProfitList = admin + 'purchaseProfit/list';
api.purchaseProfitExport = admin + 'purchaseProfit/exportList';
api.prePurchaseList = admin + 'prePurchase/list';
api.prePurchaseDetail = admin + 'prePurchase/detail';
api.prePurchaseSavePurDetail = admin + 'prePurchase/saveTodayPurAmount';
api.prePurchaseCheckPurchaseOrder = admin + 'prePurchase/checkPurchaseOrder';
api.prePurchaseGenPurchaseOrder = admin + 'prePurchase/genPurchaseOrder';
api.savePreProcessAmount = admin + 'prePurchase/savePreProcessAmount'; // 保存实际数量
api.createRawPurchaseOrder = admin + 'prePurchase/createRawPurchaseOrder'; // 生成原料采购单
api.createProcessOrder = admin + 'prePurchase/createProcessOrder'; // 生成加工单
api.exportPlanPurchase = admin + 'prePurchase/ExportPlanPurchase'; // 周配货商品导出
api.getProcessPrintData = admin + 'process/printData'; // 获取加工单打印数据
api.syncCentralPurchaseOrder = admin + 'purchaseOrder/syncCentralPurchaseOrder';
api.AuditCentralPurchaseOrder =
  admin + 'purchaseOrder/AuditCentralPurchaseOrder';
api.commodityOrderList = admin + 'purchaseOrder/commodityOrderList'; // 采购商品关联的订单信息
api.providerImport = admin + 'provider/export';
api.getAdminList = admin + 'purchaseOrder/adminList'; // 获取操作员
api.getPriceGroupByProvider =
  admin + 'purchaseAgreementPrice/getPriceGroupByProvider'; // 获取供应商价格
api.purchaseTaskExportExcel = admin + 'purchaseTask/exportExcel'; // 采购任务导出
api.purchaseTaskExportHistoryExcel = admin + 'purchaseTask/exportHistory'; // 采购任务历史数据导出
api.supplyCostList = admin + 'providerGoodsCost/list'; // 供货成本列表
api.supplyCostSum = admin + 'providerGoodsCost/sum'; // 供货成本列表底部合计
api.supplyCostExport = admin + 'providerGoodsCost/exportList'; // 供货成本列表底部合计
api.supplyCostSaleList = admin + 'providerGoodsCost/saleList'; // 供货成本列表
api.supplyCostSaleSum = admin + 'providerGoodsCost/saleSum'; // 供货成本列表底部合计
api.supplySaleExport = admin + 'providerGoodsCost/exportSaleList'; // 供货成本列表底部合计
api.ExportCostDetail = admin + 'providerGoodsCost/ExportCostDetail'; // 供应商供货明细报表
api.PurGoodsSituationList = admin + 'StockInManagement/PurGoodsSituationList'; // 商品列表
api.PurRecordsSituationList =
  admin + 'StockInManagement/PurRecordsSituationList'; // 商品记录列表
api.getReferInfo = admin + 'purchaseOrder/referInfo'; // 采购商品参考信息

/**
 * 配送模块相关api
 */
api.getDeliveryMapList = admin + 'deliveryMap/list'; // 获取配送地图信息
api.getTrace = admin + 'deliveryMap/trace'; // 获取司机轨迹

api.getPerformanceData = admin + 'performance/driverList';
api.driverrankList = admin + 'performance/driverrankList'; // 绩效排序
api.driverExport = admin + 'performance/DriverExport'; // 导出

api.getCarList = admin + 'car/list'; // 车辆列表
api.getCarDetail = admin + 'car/detail'; // 车辆详情
api.saveCar = admin + 'car/save'; // 保存车辆信息
api.delCar = admin + 'car/del'; //删除车辆信息

/**
 * 公告相关
 */
api.getNotice = admin + 'general/GetNotice'; // 获取通知详情
api.getNoticeItem = admin + 'general/GetNoticeItem'; // 根据编号获取通知详情
api.getNoticeInfo = admin + 'general/GetNoticeInfo'; // 根据编号获取通知详情
/**
 * app 下载
 **/
api.downloadApp = admin + 'general/downloadApp'; // 获取通知详情

/**
 * 商城相关api
 */
api.getBanner = admin + '/banner/bannerList'; // 获取banner
api.saveBanner = admin + '/banner/save'; // 保存banner
api.batchUpdateBanner = admin + '/banner/batchUpdate'; // 批量修改banner
api.delBanner = admin + '/banner/delBanner'; // 删除banner
api.getShopColor = admin + '/banner/getShopColor'; // 删除banner
api.afterSale = admin + 'WapSettings/getAfterSaleContent';
api.shopAbout = admin + 'WapSettings/getAboutContent';
api.shopAgreement = admin + 'WapSettings/getAgreementContent';
api.SaveConfig = admin + 'WapSettings/SaveConfig';

// 溯源相关api
api.addSource = admin + 'commodityTrace/addTraceSource'; //新增溯源
api.sourceSearch = admin + 'commodityTrace/getCommodityTrace'; //溯源查询
api.editSource = admin + 'commodityTrace/saveTraceSource'; //编辑溯源
api.queryCommodity = admin + 'commodityTrace/getCommodity'; //搜索商品
api.sourceList = admin + 'commodityTrace/ajaxList'; //溯源列表
api.sourceImport = admin + 'commodityTrace/import'; // 导入溯源检测报告
api.sourceTemplate = admin + 'commodityTrace/template'; // 检测报告导入模板
api.sourceListDetail = admin + 'commodityTrace/getTraceInfo'; //溯源详情
api.sourceCommodityList = admin + 'commodityTrace/getTraceCommodityList';
api.removeSource = admin + 'commodityTrace/deleteTraceSource';
api.checkSource = admin + 'commodityTrace/updateTraceSourceStatus'; //审核溯源
api.getProviderSelect = admin + 'commoditySuper/ajaxGetPurchaseType';
api.getLastSourceInfo = admin + 'commodityTrace/getLastSourceInfo'; // 获取最后一次溯源信息
api.saveDevice = admin + 'commodityTrace/saveDevice'; // 保存设备账号
api.getDevice = admin + 'commodityTrace/getDevice'; // 查看设备账号
api.getCustomizeFieldList =
  admin + 'commodityCustomizeField/customizeFieldList'; // 获取自定义字段列表
api.addCustomizeField = admin + 'commodityCustomizeField/addCustomizeField'; // 添加
api.updateCustomizeField =
  admin + 'commodityCustomizeField/updateCustomizeField'; // 修改
api.deleteCustomizeField =
  admin + 'commodityCustomizeField/deletecustomizeField'; // 删除自定义字段
api.updateCustomizeField =
  admin + 'commodityCustomizeField/updateCustomizeField'; // 修改自定义字段

api.getActivity = admin + 'subject/getSubjectTypeThree'; // 获取抢购信息
api.getSubject = admin + 'subject/list'; // 获取专题列表
api.saveSubject = admin + 'subject/save'; // 保存专题
api.batchSaveSubject = admin + 'subject/batchSave'; // 批量保存专题
api.delSubject = admin + 'subject/delSubject'; // 删除专题
api.delSubjectGoods = admin + 'subject/delCommodity'; // 删除专题商品
api.getShopColor = admin + 'subject/getShopColor'; // 删除banner
api.saveShopColor = admin + 'subject/saveShopColor'; // 删除banner

//旧云商品库
let sdpcloud_url = 'https://sdpcloud.sdongpo.com';
api.cloudGoodsList = sdpcloud_url + `/api/commodity/commodity-list`;
api.cloudCategory1 = sdpcloud_url + '/api/super-category/category-list1';
api.cloudCategory2 = sdpcloud_url + '/api/super-category/category-list2';
api.reCloudGoodsList = sdpcloud_url + '/api/commodity/get-save-data';
api.cloudGoodDetail = sdpcloud_url + '/api/commodity/view';
api.cloudCategoryTree = sdpcloud_url + '/api/super-category/query';
api.saveCloudGoods = admin + 'appCenter/batchAddCommodity';
api.saveLocalCategory = admin + 'appCenter/batchAddCategory';
api.editLocalGood = admin + 'appCenter/updateBaseInfoFromCenter';
api.cloudToken = admin + 'appCenter/getSdpCloudToken'; //云商品库token
api.clearCloudLog = sdpcloud_url + '/api/commodity/clear-commodity-info-log';
api.imageGalleryList = sdpcloud_url + '/api/image-gallery/image-gallery-list'; //图库列表
api.categoryList = sdpcloud_url + '/api/image-gallery/category-list';
api.clearCommodityLogByIds =
  sdpcloud_url + `/api/commodity/clear-commodity-log-by-ids`;
// 新云商品库
api.getCloudCategoryList = admin + 'appCenter/getCloudCategoryTree'; //云商品库分类列表
api.getCloudGoodsList = admin + 'appCenter/getCloudCommodityList'; //云商品商品列表
api.importCloudGoods = admin + 'appCenter/batchAddCommodity'; //导入云商品
api.bindCloudGoods = admin + 'appCenter/batchBindCloudCommodity'; //批量绑定云商品
api.unBindCloudGoods = admin + 'appCenter/unBatchBindCloudCommodity'; //批量解绑云商品

//报表相关
api.planTotalData = admin + 'purchasePlanStatistic/total'; // 计划统计合计数据
api.planStatement = admin + 'purchasePlanStatistic/list'; // 计划统计报表
api.exportPlanReport = admin + 'purchasePlanStatistic/export'; // 导出计划统计报表
api.getProfitListReport = admin + 'report/profitList'; // 获得毛利列表、详情(stat_type = 10)数据
api.exportProfitListReport = admin + 'report/exportProfitList'; // 账务毛利详情导出
api.getProfitChartReport = admin + 'report/profitStatData'; // 获得毛利统计数据ajaxGetPurchaseType
api.getProfitDetail = admin + 'report/commodityProfitList'; // 获得毛利统计数据详情
api.getCommoditySaleList = admin + 'report/commoditySaleList'; // 商品销量列表
api.exportProfitList = admin + 'report/exportProfitList'; // 导出毛利列表
api.exportCommodityProfitList = admin + 'report/exportCommodityProfitList'; // 导出商品毛利列表
api.exportCommoditySale = admin + 'report/exportCommoditySale'; // 导出商品销量
api.getGoodsChartData = admin + 'report/CommoditySaleData'; // 商品销量饼图数据
api.getBusinessList = admin + 'report/businessList'; // 商品营业数据
api.getBusinessDetail = admin + 'report/businessDetail'; // 商品营业数据详情
api.purchaseStatDetail = admin + 'report/purchaseStatDetail'; //采购汇总按明细
api.purchaseStatAgent = admin + 'report/purchaseStatAgent'; //采购汇总按采购员
api.purchaseStatProvider = admin + 'report/purchaseStatProvider'; //采购汇总按供应商
api.purchaseStatCommodity = admin + 'report/purchaseStatCommodity'; //采购汇总按商品
api.purchaseStatCategory = admin + 'report/getListGroupByCategory'; //采购汇总按商品分类
api.purchaseStatData = admin + 'report/purchaseStatData'; //采购汇总统计数据
api.commoditySalesUserList = admin + 'report/commoditySalesUserList'; //商品销量-客户明细
api.exportPurchaseStatDetail = admin + 'report/exportPurchaseStatDetail'; //导出按明细
api.exportPurchaseStatCommodity = admin + 'report/exportPurchaseStatCommodity'; //导出按商品
api.exportPurchaseStatAgent = admin + 'report/exportPurchaseStatAgent'; //导出按采购员
api.exportPurchaseStatProvider = admin + 'report/exportPurchaseStatProvider'; //导出按供应商
api.getSalePriceList = admin + 'priceReport/salePriceList'; //获取销售价格波动表
api.getPurchasePriceList = admin + 'priceReport/purchasePriceList'; //获取采购价格波动表
api.commodityPurchasePriceTop = admin + 'priceReport/commodityPurchasePriceTop'; // 获取价格波动表 采购金额排行
api.getCommodityPriceDetail = admin + 'priceReport/commodityPriceDetail'; //获取价格波动图表
api.getInventoryReport = admin + 'InventoryReport/data'; //获取价格波动图表
api.exportInventoryReport = admin + 'InventoryReport/exportData'; //导出价格波动图表
api.exportsOrder = admin + 'statSuper/exportOrder'; // 订单导出
api.statDaliyOrder = admin + 'statSuper/statDaliyOrder'; //订单统计详情
api.statOrder = admin + 'statSuper/statOrder'; //订单统计和列表
api.exports = admin + 'userStatistics/export'; // 导出客户统计表
api.exportReceiveByGoods =
  admin + 'statSuper/OfflineExportModifyCommoditySummary'; // 导出收货统计表（按商品）
api.exportReceiveByReason =
  admin + 'statSuper/OfflineExportModitItemReasonSummary'; // 导出收货统计表（按收货差异原因）
api.getdata = admin + 'userStatistics/getData'; //客户统计列表
api.getDataStatistics = admin + 'salesStatistics/getData'; // 销售员列表
api.exportStatistics = admin + 'salesStatistics/export'; // 销售列表导出
api.getStoreLossList = admin + 'report/StoreLoss'; // 库房盘点列表
api.getStoreLossDetail = admin + 'report/storeLossDetail'; // 库房盘点详情
api.exportStoreLoss = admin + 'report/exportStoreLoss'; // 库房盘点导出列表
api.exportStoreDetail = admin + 'report/exportStoreLossDetail'; // 库房盘点导出详情
api.storeLossSummary = admin + 'report/storeLossSummary'; // 损耗趋势
api.purchaseCommodityLoss = admin + 'report/PurchaseCommodityLoss'; // 采购损耗-按商品
api.purchaseProviderLoss = admin + 'report/PurchaseProviderLoss'; // 采购损耗-按供应商或采购员
api.purchaseComDetail = admin + 'report/purchaseComDetail'; // 采购损耗-按商品详情
api.purchaseProviderDetail = admin + 'report/PurchaseProviderDetail'; // 采购损耗-按供应商或采购员-详情
api.ReturnLoss = admin + 'report/ReturnLoss'; // 退货损耗-列表
api.ReturnLossDetail = admin + 'report/ReturnLossDetail'; // 退货损耗-详情
api.exportCommoditySaleList = admin + 'report/exportCommoditySaleList';
api.InventoryReportDetail = admin + '/InventoryReport/detail'; // 进销存报表明细
api.InventoryReportBatchDetail = admin + '/InventoryReport/batchDetail'; // 进销存报表-批次-明细
api.estimateProfitTotal = admin + 'estimateProfit/total'; // 预估毛利顶部合计
api.estimateProfitExport = admin + 'estimateProfit/export'; // 预估毛利导出
api.ExportUserProfitByUser = admin + 'report/ExportUserProfitByUser'; // 客户毛利导出
api.ExportUserProfitByCommodity = admin + 'report/ExportUserProfitByCommodity'; // 商品毛利导出
api.purchaseStatisticSum = admin + 'userStore/purchaseStatisticSum'; // 客户采购统计导出任务
api.purchaseStatisticExport = admin + 'userStore/purchaseStatisticExport'; // 客户采购统计导出任务
// 协议价
api.enableDetail = admin + 'protocolPrice/EnableDetail'; // 修改状态
api.getAgreementPrice = admin + 'protocolPrice/detail'; // 协议价详情
api.getExpireAgreementPrice = admin + 'protocolPrice/expire'; // 协议价详情
api.getAgreementPriceList = admin + 'protocolPrice/list'; // 协议价列表
api.getAgreementPriceGoodsList = admin + 'protocolPrice/protocolCommodityList'; // 协议价商品列表
api.getUserAgreementPriceList = admin + 'protocolPrice/user'; // 客户协议价列表
api.addAgreementPrice = admin + 'protocolPrice/new'; // 新增协议价
api.updateAgreementPrice = admin + 'protocolPrice/edit'; // 修改协议单
api.closeAgreementPrice = admin + 'protocolPrice/close'; // 关闭协议单
api.deleteAgreementPrice = admin + 'protocolPrice/delete'; // 删除协议单
api.checkAgreementPrice = admin + 'protocolPrice/audit'; // 审核协议单
api.exportAgreementPrice = admin + 'protocolPrice/export'; // 导出协议单
api.exportMulti = admin + 'protocolPrice/exportMulti'; // 导出多列协议单
api.agreementPriceStatus = admin + 'protocolPrice/statusList'; // 获取协议单状态
api.exportAgreementPriceTpl = admin + 'protocolPrice/template'; //下载协议单模版
api.syncBillPrice = admin + 'protocolPrice/syncBillPrice'; //协议价同步对账价格
api.getProtocolPrintData = admin + 'protocolPrice/getPrintData'; //获取协议单打印
api.batchRefresh = admin + 'protocolPrice/batchRefresh'; // 更新协议价
api.batchUpdateProtocolCommodity =
  admin + 'protocolPrice/batchUpdateProtocolCommodity'; //批量修改协议单商品
api.checkProtocolCommodity = admin + 'protocolPrice/checkProtocolCommodity'; // 检查要修改的协议单商品
api.protocolBatchClose = admin + 'protocolPrice/batchClose'; // 批量关闭协议单
// 设置
api.getDeliveryTime = admin + 'deliveryTime/GetDeliveryTimeList'; //送货时间列表
api.deleteDeliveryTime = admin + 'deliveryTime/delete'; //删除送货时间
api.saveDeliveryTime = admin + 'deliveryTime/addDeliveryTime'; //送货时间编辑新增

api.getAllPower = admin + 'adminSuper/GetPowerList'; //角色列表
api.getRoleList = admin + 'adminSuper/getUserGroupList'; //角色列表
api.getRolePower = admin + 'adminSuper/view'; //查看角色权限
api.deleteRole = admin + 'adminSuper/del'; //删除角色
api.saveRole = admin + 'adminSuper/add'; //新增编辑角色
api.getSelectRoleList = admin + 'adminSuper/GetAdminList'; //后台角色列表供操作员下拉选择使用
api.getStoreList = admin + 'adminSuper/Storage'; //获取库房列表
api.saveOperatorStore = admin + 'adminSuper/SetStorage'; //操作员分配库房权限

api.getOperatorList = admin + 'adminSuper/Admin'; //新增操作员
api.saveOperator = admin + 'adminSuper/SaveAdmin'; //新增编辑操作员店
api.deleteOperator = admin + 'adminSuper/DelAdmin'; //删除操作员

api.getWechatInfo = admin + 'wecaht/GetCode'; //获取公众号信息
api.saveWechatInfo = admin + 'wecaht/Save'; //保存公众号信息
api.getWechatConfigInfo = admin + 'wecaht/GetWxConfig'; //获取公众号配置信息
api.saveWechatConfig = admin + 'wecaht/SaveWeixinSecret'; //保存公众号配置信息
api.saveWechatMenu = admin + 'wecaht/SaveButton'; //保存公众号菜单
api.getWechatMenu = admin + 'wecaht/GetWeiXinMenu'; //保存公众号菜单
api.genWechatMenu = admin + 'wecaht/CreateButton'; //生成公众号菜单

api.getCompanyInfo = admin + 'company/GetCompany'; //获取公司信息
api.saveCompanyInfo = admin + 'company/SetCompany'; //保存公司信息
api.getSysLog = admin + 'log/LogList'; //获取系统日志
api.getOperateFailureLog = admin + 'log/getOperateFailureLog'; //批量执行失败日志
api.exportSysLog = admin + 'log/exportYunLog'; // 导出系统日志
api.initSystemData = admin + 'db/init'; //初始化数据
api.showTables = admin + 'db/showTables'; //显示系统初始化当前表模块设置
api.saveTables = admin + 'db/saveTables'; //系统初始化设置相应模块

api.getSignatureList = admin + 'sms/smsSignList'; //获取签名列表
api.authorizationSignature = admin + 'sms/smsSignAuth'; //授权签名
api.authorizationSignatureV2 = admin + 'sms/smsSignAuthV2'; //授权签名
api.smsRemoveSign = admin + 'sms/smsRemoveSign'; //删除签名

//分拣
api.markStockOut = admin + 'sortSuper/remarkStockout'; //标记缺货
api.allMarkStockOut = admin + 'sortSuper/AllRemarkStockOut'; //批量标记缺货
api.getSortOrderCommodity = admin + 'sortSuper/OrderCommodityList'; //获取分拣商品
api.getSortOrderCommodityStat = admin + 'sortSuper/SingleCommodityList'; //获取单品列表
api.getSortOrderCommodityStatDetail = admin + 'sortSuper/SingleCommodityDetail'; //获取单品列表查看详情
api.getUserOrderCommodity = admin + 'sortSuper/UserOrderCommodityList'; //获取摘果打印数据
api.getUserOrderCommodityStat = admin + 'sortSuper/UserCommodityList'; //获取门店列表
api.getUserOrderCommodityDetail = admin + 'sortSuper/UserCommodityDetail'; //获取门店列表
api.getSortSearchConfig = admin + 'sortSuper/SearchConfig'; //获取分拣商品
api.saveSortInfo = admin + 'sortSuper/submit'; //保存分拣信息
api.saveSortPrintInfo = admin + 'sortSuper/saveLabelPrint'; //保存打印历史
api.getSortPrintHistory = admin + 'sortSuper/getLabelPrint'; //保存打印历史
api.saveSortMany = admin + 'sortSuper/SubmitMany'; //保存多次分拣
api.resetSort = admin + 'sortSuper/reset'; //重置分拣
api.sortProcess = admin + 'sortSuper/SortProcess'; //重置分拣
api.sortRecord = admin + 'sortSuper/sortRecord'; //获取分拣记录
api.getSortRecordsList = admin + 'sortSuper/SortLog'; //获取分拣记录列表
api.sortBatchPrintCheck = admin + 'sortSuper/CheckOneTouchPrint'; //检测一键打印
api.checkUserOneTouchPrint = admin + 'sortSuper/CheckUserOneTouchPrint'; //检测一键打印
api.sortBatchPrint = admin + 'sortSuper/OneTouchPrint'; //一键分拣打印
api.onlyOneTouchPrint = admin + 'sortSuper/oneTouchPrint'; // 一键打印
api.userOneTouchPrint = admin + 'sortSuper/UserOneTouchPrint'; // 一键打印获取商品id
api.exportSortSum = admin + 'sortSuper/ExportSummary'; //导出汇总表
api.summaryPick = admin + 'sortSuper/summaryPickPrintData'; //汇总拣货打印数据
api.getSorter = admin + 'sorterHistory/getSorterList'; //获取分拣员
api.getSortHistory = admin + 'sorterHistory/getPeriodData'; //获取分拣历史数据
api.getSortHistoryDetail = admin + 'sorterHistory/getPeriodDetailData'; //获取分拣历史详情数据
api.exportSortHistory = admin + 'sorterHistory/export'; //导出分拣历史详情数据
api.getSorterList = admin + 'sorterSuper/GetIndexData'; //获取分拣员列表
api.getSorterDetail = admin + 'sorterSuper/GetSorterInfo'; //获取分拣员详情
api.getSorterGoods = admin + 'sorterSuper/selectedCommodity'; //获取分拣员商品
api.getSorterUser = admin + 'sorterSuper/selectedUser'; //获取分拣员客户
api.sorterDisable = admin + 'sorterSuper/Disable';
api.addSorter = admin + 'sorterSuper/add'; //新增拣员信息
api.saveSorter = admin + 'sorterSuper/edit'; //修改拣员信息
api.getSorterConfig = admin + 'sorterSuper/addConfig'; //获取新增修改分拣员页面数据
api.getSorterIndexData = admin + 'sorterSuper/getIndexData'; //获取新增修改分拣员页面数据
api.submitDate = admin + 'sortSuper/submitDate'; //保存生产日期
api.genSortCount = admin + 'sortSuper/genSortCount'; // 生成获取分拣条数任务
api.listCount = admin + 'general/listCount'; // 根据任务获取列表总条数
api.getPrintData = admin + 'sortSuper/PrintData'; // 获取单独打印mulit_pick 为false时
api.getManyPrintData = admin + 'sortSuper/ManyPrintData'; // 获取多次打印mulit_pick 为true时
api.getPickPrintData = admin + 'sortSuper/PickPrintData'; // 获取拣货单打印数据
api.getCateSortProcessList = admin + 'sortSuper/categorySortProcessList'; // 一级分类分拣进度列表
api.exportUserSort = admin + 'sortSuper/exportUserSort'; // 摘果称重导出

// 缺货商品页面
api.cancelMarkStockOut = admin + 'sortSuper/DelRemarkStockOut'; // 取消标记缺货
api.checkPurchaseData = admin + 'sortSuper/CheckPurchaseData'; // 检测待生成采购单的数据
api.createPurchaseOrder = admin + 'sortSuper/CreatePurchaseOrder'; // 生成采购单
api.stockOutExport = admin + 'sortSuper/StockOutExport'; // 缺货明细/汇总导出

//打印
api.getPrintTemplate = admin + 'printerConfig/data'; //获取打印模版
api.getPrintTemplateType = admin + '/printerConfig/GetPrinter'; //获取打印模版
api.exportPrintTemplate = admin + 'printerConfig/ExportToFile'; //导出打印模版
api.deletePrintTemplate = admin + 'printerConfig/del'; //删除打印模版
api.getYunpianConfig = admin + 'sms/GetSmsConfig'; //获取云片配置
api.saveYunpianConfig = admin + 'sms/SaveSmsConfig'; //保存云片配置
api.downloadHttpsUrl = 'https://appdown.sdongpo.com/driver/CLodop_https.zip';
api.downloadHttpUrl = 'https://appdown.sdongpo.com/driver/CLodop.zip';
api.saveTplSel = admin + 'printerConfig/saveTplSel'; //设置打印模板
api.getTplSel = admin + 'printerConfig/getTplSel'; //查询打印模板
api.getTpl = admin + 'printerConfig/getTpl'; //查询打印模板
api.getTplGroup = admin + 'printerConfig/getTplGroup'; //查询打印模板组
api.saveGroup = admin + 'printerConfig/saveGroup'; //保存打印模板组
// 供应商打印模板配置
api.getTplRelationMap = admin + 'printerConfig/tplRelationMap'; //查询供应商打印模板
api.saveTplRelation = admin + 'printerConfig/saveTplRelation'; //保存供应商打印模板

// 库房
api.getWarehouseList = admin + 'Warehouse/getWarehouseList'; //库房列表
api.addWarehouse = admin + 'Warehouse/addWarehouse'; // 添加库房
api.deleteWarehouse = admin + 'Warehouse/deleteWarehouse'; // 删除库房
api.updateWarehouse = admin + 'Warehouse/updateWarehouse'; // 编辑库房
api.getReservoirList = admin + 'Inventory/GetStoreAreaList'; // 获取所有库区库位列表
api.getStorageAreaList = admin + 'StorageArea/getStorageAreaList'; // 获取库区列表
api.getStorageList = admin + 'StorageLocation/getStorageList'; // 获取库位列表
api.addStorageArea = admin + 'StorageArea/addStorageArea'; // 新增库区
api.updateStorageArea = admin + 'StorageArea/updateStorageArea'; // 编辑库区
api.deleteStorageArea = admin + 'StorageArea/deleteStorageArea'; // 删除库区
api.addStorage = admin + 'StorageLocation/addStorage'; // 新增库位
api.deleteStorage = admin + 'StorageLocation/deleteStorage'; // 删除库位
api.updateStorage = admin + 'StorageLocation/updateStorage'; // 更新库位
api.batchGeneration = admin + 'StorageLocation/batchGeneration'; // 批量生成库位
api.addBatchGenerationLocation =
  admin + 'StorageLocation/addBatchGenerationLocation'; // 添加批量生成的库位数据
api.getAreaMap = admin + 'StorageAddress/getAreaMap'; // 获得所属地绑定的仓库
api.unsetRelation = admin + 'StorageAddress/unsetRelation'; // 解除仓库与区域关系
api.addAreaRelations = admin + 'StorageAddress/addAddress'; // 新增 归属地、仓库绑定关系
api.StockInManagementList = admin + 'StockInManagement/StockInManagementList'; // 入库管理列表(搜索分页)
api.getDeliveryData = admin + 'orderDelivery/deliveryData'; // 发货出库列表页(搜索分页)
api.exportDdeliveryData = admin + 'orderDelivery/exportDeliveryData'; // 发货出库导出
api.exportStockInList = admin + 'StockInManagement/exportStockInList'; // 单据导出
api.exportStockInRecordList =
  admin + 'StockInManagement/exportStockInRecordList'; // 单据明细导出
api.setWarehouseLocation = admin + 'Warehouse/SetWarehouseLocation'; // 设置仓库坐标

api.saveIssueConfig = admin + 'issueOrder/saveIssueConfig'; // 保存单据设置
api.getIssueConfig = admin + 'issueOrder/getIssueConfig'; // 获取单据设置
api.exportStockOutList = admin + 'StockOutManagement/exportStockOutList'; // 单据导出
api.exportStockOutRecordList =
  admin + 'StockOutManagement/exportStockOutRecordList'; // 单据明细导出
api.userDeliveryDetail = admin + 'orderDelivery/userDeliveryDetail'; // 列表页查看详情
api.getStockDiffData = admin + 'orderDelivery/StockDiffData'; // 列表页查看详情
api.assignOrderProvider = admin + 'orderDelivery/setBindProviderBatch'; // 发货出库批量设置指定供应商
api.sendOut = admin + 'orderDelivery/sendOut'; // 列表页查看详情
api.checkDeliveryData = admin + 'orderDelivery/checkDeliveryData'; // 发货前检测
api.exportOrderDeliveryData = admin + 'orderDelivery/exportDeliveryData'; // 发货前检测
api.checkOrderDeliveryData = admin + 'orderDelivery/checkOrderDeliveryData'; // 发货前检测
api.createStockIn = admin + 'StockInManagement/createStockIn'; // 创建入库单
api.getStockInDetail = admin + 'StockInManagement/StockInDetail'; // 创建入库单
api.getStockInQueryList = admin + 'StockInManagement/StockInQueryList'; // 入库查询列表（搜索分页）
api.getStockPrintData = admin + 'StockInManagement/GetPrintData'; // 入库单打印数据
api.auditConfirmIn = admin + 'StockInManagement/confirmIn'; // 获得入库单审核页面数据
api.updateAudit = admin + 'StockInManagement/updateAudit'; // 编辑“审核单据”
api.deleteGodownEntry = admin + 'StockInManagement/deleteGodownEntry'; // 删除入库单
api.getUserWarehouseArea = admin + 'StockInManagement/getUserWarehouseArea'; // 获取库房库区信息
api.StockOutManagementList =
  admin + 'StockOutManagement/StockOutManagementList'; // 出库管理列表（搜索分页）
api.StockOutQueryList = admin + 'StockOutManagement/StockOutQueryList'; // 出库查询列表（搜索分页）
api.createStockOut = admin + 'StockOutManagement/createStockOut'; // 创建出库单
api.getOutBoundAudit = admin + 'StockOutManagement/getAudit'; // 获得出库单审核页面数据
api.auditConfirmOut = admin + 'StockOutManagement/confirmIn'; // 审核出库单
api.getStockOutPrintData = admin + 'StockOutManagement/GetPrintData'; // 获取出库单打印信息

api.getCheckCommodityList = admin + 'inventoryCheck/getCommodityList'; //获取指定时刻库存商品数据
api.mergeInventory = admin + 'inventoryCheck/mergeInventory'; // 合并盘点单
api.deleteStockOutOrder = admin + 'StockOutManagement/deleteStockOutOrder'; // 删除出库单
api.checkOrderPage = admin + 'inventoryCheck/checkOrderPage'; // 盘点单列表页面
api.auditOrder = admin + 'inventoryCheck/audit'; // 盘点单审核
api.exportCheckOrderDetails = admin + 'inventoryCheck/exportCheckOrderDetails'; // 盘点单离线批量导出
api.updateOutAudit = admin + 'StockOutManagement/updateAudit'; // 编辑 出库单
api.getCheckOrderDetail = admin + 'inventoryCheck/orderDetail'; // 编辑 出库单
api.createInventoryCheckOrder = admin + 'inventoryCheck/createOrder'; // 编辑 出库单
api.confirmCheck = admin + 'inventoryCheck/confirmCheck'; // 审核确认盘点单
api.confirmCheckSave = admin + 'inventoryCheck/save'; // 保存盘点单
api.delCheckOrder = admin + 'inventoryCheck/delCheckOrder'; // 删除盘点单
api.getExistingStock = admin + 'inventory/stockPage';
api.occupyInventoryItemList = admin + '/inventory/occupyInventoryItemList'; // 占用库存明细
api.getBatchStock = admin + 'inventory/batchStockPage';
api.setThreshold = admin + 'inventory/setThreshold';
api.getInBoundAudit = admin + 'StockInManagement/getAudit';
api.getReportLPorderPage = admin + 'lossOver/orderPage'; // 损益单列表页面
api.lossOverBatchConfirm = admin + 'lossOver/BatchConfirm'; // 批量报损报溢
api.createReportLpOrder = admin + 'lossOver/createOrder'; // 手动创建损益单
api.batchConvert = admin + 'OrderDelivery/batchConvert'; // 批量转换
api.delReportLPOrder = admin + 'lossOver/delOrder'; // 删除损益单
api.getReportLPOrderDetail = admin + 'lossOver/orderDetail'; // 损益单详情
api.reportLPOrderAudit = admin + 'lossOver/confirm'; // 审核损益单
api.saveLPOrderAudit = admin + 'lossOver/save'; // 保存损益单
api.lossOverAllOver = admin + 'lossOver/allOver'; // 全部报溢
api.getCostChangeOrderList = admin + 'CostChange/costChangeOrderList'; // 成本调整单列表页（搜索分页）
api.createCostChangeOrder = admin + 'CostChange/createCostChangeOrder'; // 新增 成本调整单
api.getCostChangeOrderDetail = admin + 'CostChange/getCostChangeOrderDetail'; // 获得 成本调整单 详情
api.auditCostChangeOrder = admin + 'CostChange/auditCostChangeOrder'; // 审核 成本调整单
api.updateCostChangeOrder = admin + 'CostChange/updateCostChangeOrder'; // 编辑 成本调整单
api.getTransferOrderList = admin + 'TransferOrder/transferOrderList'; // 编获取商品调拨列表
api.createOrderTransfer = admin + 'TransferOrder/createOrderTransfer'; // 新增 调拨单
api.exportOpeningTemplate = admin + 'inventory/exportOpeningTemplate'; //期初库存下载模板
api.addOpeningStock = admin + 'inventory/addOpeningStock'; //增加期初库存
api.getTransferOrderDetail = admin + 'TransferOrder/getTransferOrderDetail'; // 获得 调拨单详情
api.getAuditTransferOrder = admin + 'TransferOrder/getAuditTransferOrder'; // 获得 审核调拨单的数据
api.doAuditTransferOrder = admin + 'TransferOrder/doAuditTransferOrder'; // 审核 调拨单
api.updateAuditTransferOrder = admin + 'TransferOrder/updateAuditTransferOrder'; // 编辑 调拨单
api.deleteTransferOrder = admin + 'TransferOrder/deleteTransferOrder'; // 删除 调拨单
api.commodityLocationTransfer =
  admin + 'TransferOrder/commodityLocationTransfer'; // 调整库位
api.getTransferOrderPrintData = admin + 'TransferOrder/printdata'; // 删除 调拨单
api.batchStockInoutList = admin + 'inventory/batchStockInoutList'; // 批次库存流水
api.antiAuditBtnCheck = admin + 'StockInManagement/antiAuditBtnCheck'; // 反审核按钮检查
api.antiAudit = admin + 'StockInManagement/antiAudit'; // 反审核

// 仓内移库
api.storeroomTransferList = admin + 'TransferOrder/LocationOrderList'; // 获取移库订单列表
api.exportTransferList = admin + 'TransferOrder/ExportLocationOrderList'; // 导出移库单
api.deleteStoreroomTransfer = admin + 'TransferOrder/DelOrder'; // 删除移库订单列表
api.getStoreroomTransferDetail = admin + 'TransferOrder/GetLocationOrderDetail'; // 移库单详情
api.auditStoreroomTransfer = admin + 'TransferOrder/AuditOrder'; // 审核移库单
api.saveStoreroomTransfer = admin + 'TransferOrder/LocationOrderSave'; // 新增、编辑移库单
api.getStoreroomTransferRecords = admin + 'TransferOrder/GetOrderRecordList'; // 获取移库记录
api.exportTransferRecords = admin + 'TransferOrder/ExportRecordList'; // 导出移库记录

// 库位库存
api.getLocationCommodityTotal = admin + 'Inventory/GetLocationCountData'; // 获取某个库位商品总计信息
api.getLocationCommodityList = admin + 'Inventory/GetLocationInfo'; // 获取指定库位的商品列表信息
api.getLocationCommodityDetail = admin + 'Inventory/GetDetail'; // 获取库位指定商品的批次列表

api.getCostLog = admin + 'inventory/costLog'; // 成本记录
api.inOutHistoryList = admin + 'InOutHistory/InOutHistoryList'; //出入库历史
api.getStockOutDetail = admin + 'StockOutManagement/StockOutDetail'; // 获得出库单详情
api.inOutHistoryDetailList = admin + 'InOutHistory/InOutHistoryDetailList'; //出入库历史详情
api.checkIsMultiStorage = admin + 'Warehouse/checkIsMultiStorage'; // 判断是否是多仓库
api.getLocationMaintenanceList =
  admin + 'LocationMaintenance/locationMaintenanceList'; // 库位维护列表（搜索分页）
api.updateLocation = admin + 'LocationMaintenance/updateLocation'; // 修改库位
api.searchLocationName = admin + 'LocationMaintenance/searchLocationName'; // 模糊搜索库位名称
api.getLocationList = admin + 'LocationMaintenance/getLocationList'; // 查看库位状态
api.editStockInNum = admin + 'StockInManagement/editNum'; // 查看库位状态
api.downloadExcelTemplate = admin + 'LocationMaintenance/downloadExcelTemplate'; // 商品 库位维护 动态模板下载
api.importLocationTemplate = admin + 'LocationMaintenance/importExcelTemplate'; // 商品库位导入
api.getUserWarehouse = admin + 'StockInManagement/getUserWarehouse'; // 获取库房列表
api.exportCheckTemplate = admin + 'inventoryCheck/exportCheckTemplate'; // 获取库房列表
api.getUnitChangeData = admin + 'inventory/unitChangeData'; // 获得单位转换数据
api.unitChange = admin + 'inventory/unitChange'; // 获得单位转换数据
api.exportInventoryCheckOrder = admin + 'inventoryCheck/ExportCheckOrder'; // 导出盘点单
api.importInventoryCheckOrder = admin + 'inventoryCheck/uploadCheckData'; // 导入盘点单
api.exportStockDiffData = admin + 'orderDelivery/exportStockDiffData'; // 导出库存差异
api.deleteCostChangeOrder = admin + 'CostChange/deleteCostChangeOrder'; // 删除 成本调整单
api.exportStock = admin + 'inventory/exportStock'; //导出现有库存
api.exportBatchStock = admin + 'inventory/exportBatchStock'; //导出现有库存
api.inOutHistoryListExcel = admin + 'InOutHistory/inOutHistoryListExcel'; //导出现有库存
api.exportOrderDetail = admin + 'lossOver/exportOrderDetail'; //导出损益单
api.getTransferOrderDetailExcel =
  admin + 'TransferOrder/getTransferOrderDetailExcel'; //导出 调拨单到 Excel
api.downloadTransferOrderImportTemplate =
  admin + 'transferOrder/batchImportTemplate'; // 导入调拨单模板
api.importTransferOrder = admin + 'transferOrder/importTransferOrderListExcel';
api.exportStockIn = admin + 'StockInManagement/exportStockIn'; //导出 调拨单到 Excel
api.InOutHistoryDetailListExcel =
  admin + 'InOutHistory/InOutHistoryDetailListExcel'; //出入库历史详情 导出 Excel
api.getStoreCommodityInfo = admin + 'inventory/getCommodityInfo'; //库存商品详情
api.ExportStockOutSearch = admin + 'StockOutManagement/ExportStockOutSearch'; //出库查询导出 Excel
api.ajaxStoreList = admin + 'Warehouse/ajaxStoreList';
api.StockOutDetailExcel = admin + 'StockOutManagement/StockOutDetailExcel'; // 出库管理 “列表页 导出 Excel”（获得出库单详情）
api.getBaseCommoditys = admin + 'commoditySuper/getBaseCommoditys';
api.getOrderDeliveryDetail = admin + 'orderDelivery/orderDeliveryDetail';
api.sendOutByOrder = admin + 'orderDelivery/sendOutByOrder';
api.getCommodityBatch = admin + 'inventory/batchStockPage';
api.setDeliveryBatch = admin + 'orderDelivery/setDeliveryBatch';
api.inOutHistoryDetailAllList =
  admin + 'InOutHistory/InOutHistoryDetailAllList'; // 出入库-出入库详情列表
api.inOutHistoryDetailListExcel =
  admin + 'InOutHistory/InOutHistoryDetailListExcel'; // 出入库-出入库详情列表导出
// 单据报表
api.behalfgetList = admin + 'issueReport/getList'; //单据报表
api.getDetails = admin + 'issueReport/getDetails'; //单据详情报表
api.ExportListDetails = admin + 'issueReport/offlineExportListDetails'; // 单据报表明细导出
//公众号
api.getWechatInfo = admin + 'wecaht/GetCode'; //获取公众号信息
api.saveWechatInfo = admin + 'wecaht/Save'; //保存公众号信息
api.getWechatConfigInfo = admin + 'wecaht/GetWxConfig'; //获取公众号配置信息
api.saveWechatConfig = admin + 'wecaht/SaveWeixinSecret'; //保存公众号配置信息
api.saveWechatMenu = admin + 'wecaht/SaveButton'; //保存公众号菜单
api.getWechatMenu = admin + 'wecaht/GetWeiXinMenu'; //保存公众号菜单
api.genWechatMenu = admin + 'wecaht/CreateButton'; //生成公众号菜单
api.cancelWechatAuthorize = admin + 'wecaht/unauthorized'; //微信取消授权
api.earlyRepertory = admin + 'inventory/openingStockPage'; //期初库存页面列表

// gio用户信息相关
api.getProjectInfo = admin + 'frame/projectInfo'; //期初库存页面列表

//应用中心
api.loadHistoryCertificate = admin + 'accountingCertificate/syncHistoryRefer'; //加载历史凭证

api.newSyncDeliveryOrder = admin + 'yongYou/syncDeliveryOrder'; //同步销货单
api.syncOrderReturnList = admin + 'SyncSuper/OrderReturnList'; //订单退回列表
api.syncReturnDeliveryOrder = admin + 'yongYou/syncReturnDeliveryOrder'; //同步退货销货单
api.userCommodityRemarkSave = admin + 'userCommodityRemark/save'; // 客户指定备注保存备注信息
api.userCommodityRemarkList = admin + 'userCommodityRemark/list'; // 客户指定备注列表
api.userCommodityRemarkDetail = admin + 'userCommodityRemark/detail'; // 客户指定备注详情
api.userCommodityRemarkDelete = admin + 'userCommodityRemark/delete'; // 客户指定备注删除
api.userCommodityRemarkImport = admin + 'userCommodityRemark/import'; // 客户指定备注导入
api.userCommodityRemarkDown = admin + 'userCommodityRemark/excelTemplate'; // 客户指定备注模板下载

api.syncAccountAdd = admin + 'SyncAccountSuper/SyncAccountAdd'; //保存同步账套信息
api.syncYYCommodity = admin + 'syncSuper/syncCommodity'; //同步用友商品
api.syncLogList = admin + 'SyncSuper/SyncLogList'; //同步日志
api.versionList = admin + 'SyncAccountSuper/VersionList'; //版本列表
api.syncDeliveryOrder = admin + 'syncSuper/syncDeliveryOrder'; //同步销货单
api.syncSaleOrder = admin + 'syncSuper/syncSaleOrder'; //同步销售单
api.syncUser = admin + 'syncSuper/syncUser'; //同步客户
api.getUnSyncId = admin + 'syncSuper/getUnSyncId'; //获取未同步的商品或者用户数据
api.defaultAccount = admin + 'SyncSuper/DefaultAccount'; //默认账套
api.aliasGetList = admin + 'alias/getList'; //客户商品列表
api.aliasAdd = admin + 'alias/add'; //客户商品别名添加
api.aliasDelete = admin + 'alias/del'; //客户商品别名删除
api.getAliasInfo = admin + 'alias/getInfo'; //客户商品别名详情
api.aliasEdit = admin + 'alias/edit'; //客户商品别名编辑
api.aliasExportData = admin + 'Alias/ExportData'; // 导出
api.aliasExportTemplate = admin + 'Alias/ExportTemplate'; //客户商品别名导入模板下载
api.aliasImportData = admin + 'Alias/ImportData'; //客户商品别名导入
api.generalEditSysConfig = admin + 'General/EditSysConfig'; // 是否在商城显示客户别名
api.geMeituanExternalList = admin + 'externalPos/getExternalList'; // 获取美团同步退单列表
api.getPosDragonLogDetail = admin + 'externalPos/bncjOrderInfo'; // 获取同步日志明细

// 用友T+对接接口
api.YYLinkNew = {
  syncYYCommodity: admin + 'YongYou/SyncCommodity', // 同步商品
  getPurchaseOrder: admin + 'SyncSuper/PurchaseList', // 采购单列表
  syncPurchase: admin + 'yongYou/syncPurchase', // 同步采购单
  getPurchaseReturnOrder: admin + 'SyncSuper/PurchaseReturnList', // 采购退回单列表
  syncPurchaseReturn: admin + 'yongYou/syncPurchaseReturn', // 同步采购退回单
};
api.defaultAccount = admin + 'SyncSuper/DefaultAccount'; //默认账

api.yongYouSyncUser = admin + '/yongYou/syncUser'; // 同步用友
api.yongYouSyncProvider = admin + '/yongYou/syncProvider'; // 同步供应商

// 用友T + Cloud同步
api.YYCloud = {
  auth: admin + 'externalFinance/yytpAuth', // 用友T + Cloud授权接口
  yytpAuthStatus: admin + 'externalFinance/yytpAuthStatus', // 查询授权状态接口
  addWarehouse: admin + 'externalFinance/addWarehouse', // 同步仓库
  addUser: admin + 'externalFinance/saveUser', // 同步客户
  addProvider: admin + 'externalFinance/saveProvider', // 同步供应商
  addAgent: admin + 'externalFinance/saveAgent', // 同步采购员
  addCommodity: admin + 'externalFinance/saveCommodity', // 同步存货(商品)
  addCommodityCate: admin + 'externalFinance/addCommodityCate', // 同步存货分类(商品分类),
  addSaleOrder: admin + 'externalFinance/addSaleOrder', // 同步销货单-订单对账单
  addSaleReturnOrder: admin + 'externalFinance/addSaleReturnOrder', // 同步销货单-订单退货对账单
  addPurchaseOrder: admin + 'externalFinance/addPurchaseOrder', // 同步进货单-采购对账单
  addPurchaseReturnOrder: admin + 'externalFinance/addPurchaseReturnOrder', // 同步进货单-采购单退货账单
  resaveEntity: admin + 'externalFinance/resaveEntity', // 日志列表重新同步
  getConfig: admin + 'externalFinance/getConfig', // 获取配置
  saveConfig: admin + 'externalFinance/saveConfig', // 保存配置
  yytpInit: admin + 'externalFinance/yytpInit', // 用友初始化
  dataStat: admin + 'externalFinance/dataStat', // 数据统计
  addStockOutPurchaseReturnOrder:
    admin + 'externalFinance/addStockOutPurchaseReturnOrder', // 同步采购退货的出库单 1
  addStockOutSaleOrder: admin + 'externalFinance/addStockOutSaleOrder', // 同步销售出库的出库单 2
  addStockInPurchaseOrder: admin + 'externalFinance/addStockInPurchaseOrder', // 出入库同步筛选结果内的所有条目 3
  AddStockInSaleReturnOrder:
    admin + 'externalFinance/AddStockInSaleReturnOrder', // 出入库同步筛选结果内的所有条目 4

  addInOutOrder: admin + 'externalFinance/addInOutOrder', // 同步出入库筛选条件下所有条目 - 新 - 新增参数: 类型 替代以上 1-4
  addAssembledOrder: admin + 'externalFinance/addAssembledOrder', // 同步分割单（筛选条件下所有条目）
  /**
   *  新增参数
   * ADD_STOCK_IN_ORDER_OTHER 其他入库单
   * ADD_STOCK_IN_ORDER_GAIN 报溢入库单
   * ADD_STOCK_OUT_ORDER_OTHER 其他出库单
   * ADD_STOCK_OUT_ORDER_LOSS 报损出库单
   **/
};

// 金蝶云星空同步
api.kdcs = {
  kdcsAuth: admin + 'externalFinance/kdcsAuth', // 授权接口
  unitList: admin + 'externalFinance/unitList', // 计量单位列表
  addUnit: admin + 'externalFinance/addUnit', // 同步计量单位
  kdcsAuthStatus: admin + 'externalFinance/kdcsAuthStatus', // 授权状态查询
  kdcsInit: admin + 'externalFinance/kdcsInit', // 金蝶初始化,
  kdcsPayWayTemplate: admin + 'externalFinance/kdcsPayWayTemplate', // 下载付款方式模版,
};

// 央厨互联同步
api.ck = {
  syncLogList: admin + 'centralKitchen/logList', // 同步日志列表
  syncOrderList: admin + 'centralKitchen/orderList', // 订单同步列表
  syncOrderAction: admin + 'centralKitchen/syncOrder', // 订单同步操作
};

// 采购结算
api.purchaseAuditListSearchConfig = admin + 'purchaseBill/searchConfig'; //采购结算列表搜索配置
api.purchaseAuditList = admin + 'purchaseBill/page'; //采购对账单列表
api.auditListByPurchaseOrder = admin + 'purchaseBill/listGroupBySourceNo'; //采购结算-按采购单列表
api.purchaseAuditTotal = admin + 'purchaseBill/total'; //采购对账单总合计
api.purchaseAuditSave = admin + 'purchaseBill/save'; //保存对账单
api.purchaseAuditDetail = admin + 'purchaseBill/detail'; //对账单详情
api.getListBySourceNo = admin + 'purchaseBill/getListBySourceNo'; //采购单对应对账单列表
api.getPurchaseSettlementData = admin + 'purchaseSettle/preSettle'; //获得预结算数据
api.createPurchaseSettlement = admin + 'purchaseSettle/create'; //创建结算单
api.sendBTwoSms = admin + '/supplyChainFinance/sendBTwoSms'; // 发送验证码
api.getPurchaseSettlementList = admin + 'purchaseSettle/page'; //采购结算单列表
api.getPurchaseSettlementPayWay = admin + 'purchaseSettle/page'; //获取采购结算单支付方式
api.getPurchaseSettlementSearchConfig = admin + 'purchaseSettle/searchConfig'; //采购结算单列表搜索条件
api.getPurchaseSettlementDetail = admin + 'purchaseSettle/detail'; //采购结算单详情
api.getPurchasePaySum = admin + 'purchaseSettle/payableSummary'; //应付账款汇总
api.getPurchaseSettleFlowing = admin + 'purchaseSettle/flow'; //采购结算流水
api.getpurchaseSettlePage = admin + 'purchaseSettle/page'; // 采购结算相关搜索优化
api.getPurchaseBillPage = admin + 'purchaseBill/page'; // 单据类型
api.batchMarkBill = admin + 'purchaseBill/batchMarkBill'; // 批量标记为已对账
api.batchLockCustomerBill = admin + 'accountBill/batchMarkLock'; // 批量锁定客户对账单
api.batchLockPurchaseBill = admin + 'purchaseBill/batchMarkLock'; // 批量锁定采购对账单
api.getAccountSettleData = admin + 'accountSettle/printData'; // 客户结算打印数据
api.purchaseBillBatchSetTag = admin + 'purchaseBill/batchSetTag'; // 批量设置标签
// 客户结算
api.userAuditListSearchConfig = admin + 'accountBill/searchConfig'; //客户结算列表搜索配置
api.userAuditList = admin + 'accountBill/list'; //客户结算列表
api.userAuditSave = admin + 'accountBill/confirmBill'; //保存客户对账数据
api.userAuditDetail = admin + 'accountBill/detail'; //客户结算详情
api.userAuditPrintList = admin + 'accountBill/printList'; //客户打印
api.userAuditLock = admin + 'accountBill/lockAccountBill'; //客户结算锁定
api.userAuditUnlock = admin + 'accountBill/unlockAccountBill'; //客户结算反锁定
api.userSettlemenRecord = admin + 'accountBill/settleDetail'; //客户结算记录
api.userSettlementData = admin + 'accountBill/batchSettleInfo'; //批量获取结算列表
api.userSettlementSave = admin + 'accountBill/confirmSettle'; //确认结算
api.userSettlementList = admin + 'accountSettle/list'; //客户结算单列表
api.userSettlementDetail = admin + 'accountSettle/detail'; //客户结算单详情
api.userAuditListExport = admin + 'accountBill/conditionExport'; //客户结算按照条件导出
api.userAuditListExportNew = admin + 'accountBill/newExport'; //新版客户结算按照条件导出
api.userSettlementExport = admin + 'accountSettle/singleExport'; //客户单个结算单导出
api.userAuditExport = admin + 'accountBill/singleExport'; //单个客户结算导出
api.accountBillOrderPrintData = admin + 'accountBill/AccountBillOrderPrintData'; // 客户结算订单流水打印
api.userBalanceExport = admin + 'accountBalance/export'; //客户余额导出
api.userFlowingExport = admin + 'accountCredit/settleFlowExport'; //客户结算流水导出
api.userPaySumExport = admin + 'accountCredit/receivableSummaryExport'; //客户应收账款汇总导出
api.userTotalPaySumExport =
  admin + 'accountCredit/exportReceivableSummaryStatList'; // 财务-客户账款-总应收帐款汇总导出
api.exportReceivableSummaryBeginning =
  admin + 'accountCredit/exportReceivableSummaryBeginning'; //客户应收未收账款
api.exportBillList = admin + 'accountBalance/exportBillList'; //余额账款导出
api.getBalanceSum = admin + 'accountBalance/getBalanceSum'; // 列表客户余额总计
api.printerConfigData = admin + 'printerConfig/data'; // 获取所有模版
api.billSearchConfig = admin + 'accountBill/searchConfig';
api.batchConfirmSettle = admin + 'accountBill/batchConfirmSettle'; // 一键结算按客户轮询结算

api.purchaseSettleBatchExport = admin + 'purchaseSettle/batchExport';
api.accountSettleBatchExport = admin + 'accountSettle/batchExport';
// 打印商品分类汇总
api.accountBillCategorySummaryPrintData =
  admin + 'accountBill/AccountBillCategorySummaryPrintData';
// 打印商品分类汇总-新
api.accountBillCategorySummaryPrintDataNew =
  admin + '/accountBill/newCategorySummaryPrintData';
api.userBalanceListSearchConfig = admin + 'accountBalance/listSearchConfig'; //客户充值列表搜索配置
api.userBillRecordSearchConfig =
  admin + 'accountBalance/billRecordSearchConfig'; //收支明细搜索配置
api.userBalnaceList = admin + 'accountBalance/list'; //余额列表
api.userRechargeRecord = admin + 'accountBalance/rechargeRecord'; //客户充值纪录
api.userBalnaceFlowing = admin + 'accountBalance/BillList'; //余额账款
api.userBillRecord = admin + 'accountBalance/billRecord'; //客户收支明细
api.rechargeUserInfo = admin + 'accountBalance/userInfo'; //获取充值客户信息
api.userRecharge = admin + 'accountBalance/confirmRecharge'; // 客户充值
api.userCharge = admin + 'accountBalance/confirmDebit'; // 客户扣款
api.userCreditSearchConfig = admin + 'accountCredit/searchConfig'; //客户账款搜索配置
api.sendDunningSms = admin + 'accountCredit/sendDunningSms'; // 发送欠款催收短信
api.userSettlementFlowing = admin + 'accountCredit/settleFlow'; // 客户结算流水
api.userReceiptSum = admin + 'accountCredit/receivableSummary'; // 应收账款汇总
api.userTotalReceiptSum = admin + 'accountCredit/receivableSummaryStatList'; // 财务-客户账款-总应收帐款汇总列表
api.userAuditCompleteOrder = admin + 'accountBill/OrderComplete'; // 对账自动完成订单
api.batchMarkAsReconciled = admin + 'accountBill/batchBill'; // 批量标记为已对账状态
api.purchaseAuditListExport = admin + 'purchaseBill/exportList'; // 采购对账单列表导出
api.purchaseAuditDetailExport = admin + 'purchaseBill/exportDetail'; // 采购对账单详情
api.purchaseAuditLock = admin + 'purchaseBill/lockPurchaseBill'; // 采购结算锁定
api.purchaseAuditUnlock = admin + 'purchaseBill/unlockPurchaseBill'; // 采购结算反锁定
api.purchaseSettlementExport = admin + 'purchaseSettle/exportDetail'; // 采购结算单详情导出
api.hedged = admin + 'purchaseSettle/hedged'; // 冲销
api.recordInvoice = admin + 'purchaseSettle/recordInvoice'; // 登记开票
api.purchaseSumPaySumExport = admin + 'purchaseSettle/exportPayableSummary'; // 采购结算单详情导出
api.purchaseSumPaySumExport = admin + 'purchaseSettle/exportPayableSummary'; // 采购应付账款汇总导出
api.purchaseSumFlowingExport = admin + 'purchaseSettle/exportFlow'; // 采购结算流水导出
api.purchaseBillCategorySummaryPrintData =
  admin + 'purchaseBill/PurchaseBillCategorySummaryPrintData'; // 采购结算流水导出
api.getPurchaseBillPrintData = admin + 'purchaseBill/printData'; // 获取采购对账单打印数据

api.purchaseExpenseList = admin + 'PurchaseExpense/list'; // 采购费用列表接口
api.purchaseExpenseDetail = admin + 'PurchaseExpense/detail'; // 编辑
api.purchaseExpenseAudit = admin + 'PurchaseExpense/audit'; // 审核
api.purchaseExpenseAdd = admin + 'PurchaseExpense/add'; // 新增
api.purchaseExpenseEdit = admin + 'PurchaseExpense/edit'; // 保存编辑
api.purchaseExpenseDiscard = admin + 'PurchaseExpense/discard'; // 作废
api.purchaseExpenseDel = admin + 'PurchaseExpense/del'; // 删除
api.prePaidList = admin + 'PurchaseExpense/prePaidList'; //预付款流水
api.purchaseExpenseExportList = admin + 'purchaseExpense/exportList'; //导出

// 资金账号相关接口
api.capitalAccountList = admin + 'journalAccount/capitalAccountList'; // 资金账号列表
api.saveCapitalAccount = admin + 'journalAccount/saveCapitalAccount'; // 保存资金账号
api.initCapitalAccount = admin + 'journalAccount/initCapitalAccount'; // 初始化资金账号
api.switchCapitalAccount = admin + 'journalAccount/switchCapitalAccount'; // 开关资金账号
api.capitalAccountOtherBusiness =
  admin + 'journalAccount/capitalAccountOtherBusiness'; // 其他收入与其他支出
api.capitalAccountRecord = admin + 'journalAccount/capitalAccountRecord'; // 记账流水

api.purchaseOrderCheckReturn = admin + 'purchaseOrder/CheckReturn'; // 采购退回校验

// 智能预采购
api.prePurchaseCheckPurchaseOrder = admin + 'prePurchase/checkPurchaseOrder';

api.commodityTagPrintData = admin + 'process/commodityTagPrintData'; // 完工入库单加打印
api.reCaclNum = admin + 'process/reCaclNum'; // 出成率替换
// 加工汇总导出
api.SummaryExport = admin + 'process/conditionExport';
// 加工汇总导出
api.ProcessingSummary = admin + 'process/conditionExport';

// 工序管理
api.workingProcedureList = admin + 'WorkingProcedure/list'; // 工序管理列表
api.workingProcedureAdd = admin + 'WorkingProcedure/add'; // 工序管理新增
api.workingProcedureEdit = admin + 'WorkingProcedure/edit'; // 工序管理修改
api.workingProcedureDelete = admin + 'WorkingProcedure/delete'; // 删除
api.workingProcedureAll = admin + 'WorkingProcedure/all'; // 所有工序
api.getCommodityList = admin + 'process/commodityList'; // 获取商品列表
api.getCheckDetail = admin + 'process/checkDetail'; // 验收单详情
api.ConfirmCheckFinish = admin + 'process/ConfirmCheckFinish'; // 完成验收
api.acceptance = admin + 'process/confirmCheck'; // 工序验收
api.procedureStatistics = admin + 'process/procedureStatistics'; // 工序验收统计
api.procedureStatisticsDetail = admin + 'process/procedureStatisticsDetail'; // 工序统计详情
api.getWorkerStatistics = admin + 'process/workerStatistics'; // 工人统计
api.getWorkerStatisticsDetail = admin + 'process/workerStatisticsDetail'; // 工人业绩统计详情
api.getProcedureList = admin + 'process/procedureList'; // 工序记录单
api.procedureDetail = admin + 'process/procedureDetail'; // 工序单详情
api.processSyncCommodity = admin + 'process/syncCommodity'; // 同步销售商品到加工品池
api.processStatRaw = admin + 'process/statRaw'; // 反算原料
api.orderImportTemplateList = admin + 'OrderImportTemplate/list'; // 订单导入模板
api.orderImportTemplatePreAdd = admin + 'OrderImportTemplate/preAdd'; // 预导入
api.orderImportTemplateUploadTpl = admin + 'OrderImportTemplate/uploadTpl';
api.orderImportTemplateAdd = admin + 'OrderImportTemplate/add'; // 新增模板
api.orderImportTemplateDetail = admin + 'OrderImportTemplate/detail'; // 获取编辑信息
api.orderImportTemplateEdit = admin + 'OrderImportTemplate/edit';
api.orderImportTemplateDel = admin + 'OrderImportTemplate/del';
api.orderImportTemplateGetConfig = admin + 'OrderImportTemplate/getConfig';
api.batchFlagCompletion = admin + 'process/batchFlagCompletion'; // 批量标记完工

api.workerCompletionStatistics = admin + 'process/workerCompletionStatistics';

api.accountExpenseAdd = admin + 'AccountExpense/add';
api.getPayWayList = admin + 'accountExpense/getPayWayList';
api.accountExpenseDetail = admin + 'AccountExpense/detail';
api.accountExpenseAudit = admin + 'AccountExpense/audit';
api.accountExpenseEdit = admin + 'AccountExpense/edit';
api.accountExpenseDiscard = admin + 'AccountExpense/discard';
api.accountExpenseExportList = admin + 'accountExpense/exportList';
api.accountSettleListExport = admin + 'accountSettle/settleListExport';
api.purchaseSettleListExport = admin + 'purchaseSettle/settleListExport';
api.accountExpensedel = admin + 'AccountExpense/del';

// 获取分拣数量
api.getProcessSortAmount = admin + 'process/processSortAmount';

// 条码
api.Barcode = {
  list: admin + 'barcode/list', // 条码配置列表
  add: admin + 'barcode/save', //新增
  delete: admin + 'barcode/del', // 删除
  edit: admin + 'barcode/edit', // 编辑
};

api.BarcodeOpen = admin + 'barcode/open'; // 开关
api.BarcodePrint = admin + 'barcode/print'; // 打印

// 客户批量对账
api.batchBill = {
  list: admin + 'accountBatchBill/list', // 列表
  updatePrice: admin + 'accountBatchBill/updatePrice', // 更新价格
  updateItemPrice: admin + 'accountBatchBill/UpdateItemPrice', // 更新对账明细价格
};

// 采购批量对账
api.purchaseBatchBill = {
  list: admin + 'purchaseBill/list', // 采购批量对账列表
  updateUnitPrice: admin + 'purchaseBill/batchBill', // 修改单价
};

// 客户档案-合同管理
api.contract = {
  saveContractInfo: '/superAdmin/userSuper/contractUpdate', // 保存合同信息
  getContractInfo: '/superAdmin/userSuper/getContract', // 获取合同信息
};

// 开票信息
api.invoice = {
  companyGetInvoice: admin + 'company/getInvoice', // 获取公司信息
  invoiceUpdate: admin + 'company/invoiceUpdate',
  userSuperInvoiceUpdate: admin + 'userSuper/invoiceUpdate',
  userSuperGetInvoice: admin + 'userSuper/getInvoice', // 获取用户发票配置信息
  groupInvoiceUpdate: admin + 'userGroup/invoiceUpdate', // 保存集团发票配置
  groupGetInvoice: admin + 'userGroup/getInvoice', // 获取集团发票配置
  invoiceOrderList: admin + 'invoice/orderList', // 待开票列表
  noInvoiceList: admin + 'invoice/noInvoiceList', // 未开发票
  InvoiceList: admin + 'invoice/InvoiceList', // 已开发票列表
  createInvoice: admin + 'invoice/create', // 创建发票单据
  orderInfo: admin + 'invoice/orderInfo', // 开具详情
  getOrderRelation: admin + 'invoice/getOrderRelation', // 开具订单详情
  getTaxClassCode: admin + 'invoice/getTaxClassCode', // 查询税务分类编码
  updateTaxClassCode: admin + 'invoice/updateTaxClassCode', // 保存税务编码
  postcreateInvoice: admin + 'invoice/createInvoice', // 开发票
  make: admin + 'invoice/make', // 开单据
  createInvoices: admin + 'invoice/createInvoice', // 开具发票
  cancelInvoiceOrder: admin + 'invoice/cancelInvoiceOrder', // 关闭未开发票
  updateInvoice: admin + 'invoice/update',
  savelnvoiceSysConfig: admin + 'invoice/saveInvoiceSysConfig', // 保存发票开具的系统配置
  getlnvoiceSysConfig: admin + 'invoice/getInvoiceSysConfig', // 获取发票开具的系统配置
};

api.getInvoiceEncrypt = admin + 'invoice/getInvoiceEncrypt'; // 发票打印

// 生鲜管家
// eslint-disable-next-line no-undef
let sxgjPrefix =
  process.env.NODE_ENV === 'development'
    ? '/api'
    : 'https://sxgj.sdongpo.com/api';
// let sxgjPrefix = process.env.NODE_ENV === 'development' ? '/api' : 'https://test-sxgj.sdongpo.com/api';
api.shengxianGuanjia = {
  qrcode: `${sxgjPrefix}/qr/get-qr`,
  wechatUser: {
    getUnionId: `${sxgjPrefix}/wechat-user/get-unionid`,
    loopUserStatus: `${sxgjPrefix}/wechat-user/loop-user-status`,
    setUser: `${sxgjPrefix}/wechat-user/set-user`,
    userList: `${sxgjPrefix}/wechat-user/user-manage`,
    setUserStatus: `${sxgjPrefix}/wechat-user/change-disable`,
    getCode: `${sxgjPrefix}/wechat-user/get-code`,
  },
  sms: {
    send: `${sxgjPrefix}/sms/send`,
    check: `${sxgjPrefix}/sms/check-sms`,
  },
};

api.generalFreshAdmin = admin + 'general/freshAdmin'; // 生鲜管家权限
api.remoteAssistanceInfo = admin + 'general/remoteAssistanceInfo'; // 获取授权码

// 溯源平台接入
api.deAuth = admin + 'Traceability/deAuth'; // 授权
api.getAuthConfig = admin + 'Traceability/getAuthConfig'; // 获取授权配置
api.bindEntityMap = admin + 'Traceability/bindEntityMap'; // 绑定/解绑实体映射关系
api.syncEntity = admin + 'Traceability/syncEntity'; // 同步
api.TraceabilityOrderList = admin + 'Traceability/orderList'; // 同步
api.entitySyncLog = admin + 'Traceability/entitySyncLog'; // 同步
api.pullExteriorEntity = admin + 'Traceability/pullExteriorEntity'; // 拉取平台实体
api.pullExteriorOrder = admin + 'Traceability/pullExteriorOrder'; // 拉取平台实体
api.getEntityList = admin + 'Traceability/getEntityList'; // 获取实体列表
api.cancelSyncEntity = admin + 'Traceability/cancelSyncEntity'; // 取消实体同步
api.editEntity = admin + 'Traceability/editEntity'; // 编辑实体
api.regionList = admin + 'Traceability/regionList'; // 获取行政区域
api.inStorageList = admin + 'Traceability/inStorageList'; // 入库单上报列表
api.outStorageList = admin + 'Traceability/outStorageList'; // 出库列表
api.storageList = admin + 'Traceability/storageList'; // 库存列表
api.getExteriorEntityList = admin + 'Traceability/getExteriorEntityList'; //  获取浙食链商品
api.ticketTypeList = admin + 'Traceability/ticketTypeList'; //  获取凭证类型列表
api.cancelAuth = admin + 'Traceability/cancelAuth'; //  取消授权
api.getExteriorCategory = admin + 'Traceability/getExteriorCategory'; //  获取外部分类
api.traceOrderCommodityList = admin + 'Traceability/orderCommodityList'; //  出库商品上报列表
api.editAndSyncEntity = admin + 'Traceability/editAndSyncEntity'; //  编辑同步实体-入库
api.getKeepingAddress = admin + 'Traceability/getKeepingAddress'; //  获取冷库列表
api.saveSyncConfig = admin + 'Traceability/saveSyncConfig'; //  保存同步配置
api.getSyncConfig = admin + 'Traceability/getSyncConfig'; //  获取同步配置
api.setTimingTask = admin + 'Traceability/setTimingTask'; //  保存定时任务配置
api.getTimingTask = admin + 'Traceability/getTimingTask'; //  获取定时任务配置
api.entityDetails = admin + 'Traceability/entityDetails'; //  获取客户详情/供应商详情
api.exteriorCommodityList = admin + 'Traceability/exteriorCommodityList'; //  外部商品列表

// pos对接相关
api.palAuth = admin + 'externalPos/palAuth'; // 银豹获取授权
api.palAuthStatus = admin + 'externalPos/palAuthStatus'; // 银豹获取授权状态
api.logList = admin + 'externalPos/logList'; // 银豹获取授权状态
api.externalList = admin + 'externalPos/getExternalList'; //银豹获取退货单列表
api.commodityList = admin + 'externalPos/commodityList'; // 商品列表
api.bindEntity = admin + 'externalPos/bindEntity'; // 绑定外部实体
api.reSaveEntity = admin + 'externalPos/reSaveEntity'; // 重试
api.reSaveExternalEntity = admin + 'externalPos/ReSync'; // 退货单重试
api.cancelAuthForPal = admin + 'externalPos/cancelAuthForPal'; // 取消授权
api.batchReSave = admin + 'externalPos/batchReSave'; // 批量重试同步
api.FieldList = admin + 'externalPos/FieldList'; // 获取所有id
api.mblTcslSelectList = admin + 'externalPos/mblTcslSelectList'; // 获取客户下拉系统选择列表

// posDragon 天财商龙相关
api.tcslAuth = admin + 'externalPos/tcslAuth'; // 授权
api.tcslAuthStatus = admin + 'externalPos/tcslAuthStatus'; // 获取授权状态
api.cancelAuthForTcsl = admin + 'externalPos/cancelAuthForTcsl'; // 取消授权

// posDragon 天财商龙标准版相关
api.tcslStandardAuth = admin + 'externalPos/tcslStandardAuth'; // 授权
api.tcslStandardAuthStatus = admin + 'externalPos/tcslStandardAuthStatus'; // 获取授权状态
api.cancelAuthForTcslStandard = admin + 'externalPos/cancelAuthForTcslStandard'; // 取消授权
api.posSelectList = admin + 'externalPos/selectList'; // 获取门店、供货商筛选项
api.posOrderList = admin + 'externalPos/orderList'; // 门店订货
api.batchCreateOrder = admin + 'externalPos/batchCreateOrder'; // 同步下单数据
api.batchCreateModifyOrder = admin + 'externalPos/batchCreateModifyOrder'; // 同步实收数据

// 美团对接
api.meituanAuthStatus = admin + 'externalPos/meituanAuthStatus'; // 获取授权状态
api.meituanAuth = admin + 'externalPos/meituanAuth'; // 授权
api.cancelMeituanAuth = admin + 'externalPos/cancelAuthForMeituan'; // 美团取消授权
api.getMeituanResources = admin + 'externalPos/mtSelectList'; // 获取资源下拉列表
api.getMtResources = admin + 'externalPos/mtResources';
api.saveOrgId = admin + 'externalPos/saveOrgId'; // 保存组织id
api.getMtOrderList = admin + 'externalPos/getMtOrderList'; // 订单列表
api.batchCreateMtOrder = admin + 'externalPos/batchCreateMtOrder'; // 批量订单同步
api.batchMtReSave = admin + 'externalPos/batchReSave'; // 批量重试接口(美团批量通知发货)
api.batchPullRefund = admin + 'externalPos/pullRefund'; // 手动批量拉取美团退货

// 加工api
api.process = {
  goodsBoom: {
    list: admin + 'bom/page', // bom列表页面
    searchProcessedCommodity: admin + 'bom/searchProcessedCommodity', // 搜索bom主商品（加工品）
    rawCommodityList: admin + 'bom/rawCommodityList', // bom组成商品列表（原料）
    bomMakeup: admin + 'bom/rawCommodityList', // bom组成商品列表（原料）
    parentCommodityList: admin + 'bom/processedCommodityList', // 父商品列表
    add: admin + 'bom/new', // 新增bom
    save: admin + 'bom/save', // 保存bom
    detail: admin + 'bom/detail', // bom详情
    delete: admin + 'bom/del', // 删除bom
    bomCalLastInPrice: admin + 'bom/calLastInPrice', // 反算加工品最近一次进货价
  },
  productLine: {
    list: admin + 'productLine/getProductList', // 生产线列表
    add: admin + 'productLine/insertProductLine', // 新增生产线
    save: admin + 'productLine/changeName', // 修改生产线
    delete: admin + 'productLine/delProductLine', // 删除生产线
  },
  worker: {
    list: admin + 'productWorker/getProductWorker', // 工人列表
    add: admin + 'productWorker/addWorker', // 新增工人
    save: admin + 'productWorker/modifyWorker', // 修改工人
    delete: admin + 'productWorker/delProductWorker', // 删除工人
    disable: admin + 'productWorker/Disable',
  },
  processOrder: {
    searchConfig: admin + 'process/searchConfig', // 加工单列表
    list: admin + 'process/list', // 加工单列表
    add: admin + 'process/add', // 加工单新增
    save: admin + 'process/save', // 加工单保存
    detail: admin + 'process/detail', // 加工单详情
    audit: admin + 'process/audit', // 加工单审核
    cleanRaw: admin + 'process/cleanRaw', // 清除反算
    prePicking: admin + 'process/prePicking', // 预领料
    confirmPicking: admin + 'process/confirmPicking', // 确认领料
    preCompletion: admin + 'process/preCompletion', // 预完工
    confirmCompletion: admin + 'process/confirmCompletion', // 确认完工
    confirmFoodCompletion: admin + 'process/confirmFoodCompletion', // 确认食品完工
    checkFlagCompletion: admin + 'process/checkFlagCompletion', // 标记完工检查
    flagCompletion: admin + 'process/flagCompletion', // 标记完工
    preReturnPicking: admin + 'process/preReturnPicking', // 预退料
    confirmReturnPicking: admin + 'process/confirmReturnPicking', // 确认退料
    summary: admin + 'process/getCommodityPool', // 加工商品汇总
    genProcessOrder: admin + 'process/genProcessOrder', // 生成加工单
    checkProcessOrder: admin + 'process/CheckProcessOrder', // 检查生成加工单
    itemBom: admin + 'process/itemBom',
    export: admin + 'process/export',
    close: admin + 'process/close',
  },
  completion: {
    searchConfig: admin + 'completion/searchConfig', // 完工搜索配置
    list: admin + 'completion/list', // 完工列表
    detail: admin + 'completion/detail', // 完工详情
    save: admin + 'completion/save', // 完工保存
    audit: admin + 'completion/audit', // 完工审核
    export: admin + 'completion/export',
  },
  picking: {
    list: admin + 'picking/list', // 领料列表
    searchConfig: admin + 'picking/searchConfig', // 领料列表搜索配置
    detail: admin + 'picking/detail', // 领料详情
    save: admin + 'picking/save', // 领料单保存
    audit: admin + 'picking/audit', // 领料单审核
    export: admin + 'picking/export',
    delete: admin + 'picking/Delete',
  },
  pickingReturn: {
    list: admin + 'returnPicking/list', // 退料列表
    searchConfig: admin + 'returnPicking/searchConfig', // 退料列表搜索配置
    detail: admin + 'returnPicking/detail', // 退料详情
    save: admin + 'returnPicking/save', // 退料单保存
    audit: admin + 'returnPicking/audit', // 退料单审核
    export: admin + 'returnPicking/export',
  },
  reports: {
    yieldRate: admin + 'process/yieldRate', // 出成率报表
    yieldRateDetail: admin + 'process/yieldRateDetail', // 出成率报表详情
    yieldRateExport: admin + 'process/yieldRateExport', // 出成率报表
    yieldRateDetailExport: admin + 'process/yieldRateDetailExport', // 出成率报表详情
  },
};
api.getProcessBatchStockPage = admin + 'process/ProcessBatchStockPage';
// 配送api
api.delivery = {
  driver: {
    list: admin + 'driver/list', // 司机列表
    add: admin + 'driver/add', // 司机新增
    edit: admin + 'driver/edit', // 司机编辑
    delete: admin + 'driver/delete', // 司机删除
    disable: admin + 'driver/Disable',
  },
  area: {
    list: admin + 'area/list', // 区域列表
    add: admin + 'area/add', // 区域新增
    edit: admin + 'area/edit', // 区域编辑
    delete: admin + 'area/delete', // 区域删除
  },
  line: {
    searchConfig: admin + 'line/searchConfig', // 线路列表
    list: admin + 'line/list', // 线路列表
    detail: admin + 'line/GetListByCategory', // 线路列表查看下单和分拣的商品分类金额
    add: admin + 'line/add', // 线路新增
    edit: admin + 'line/edit', // 线路编辑
    delete: admin + 'line/delete', // 线路删除
    orderList: admin + 'line/orderList',
    userList: admin + 'line/orderUserList',
    orderListDetail: admin + 'line/OrderListDetail',
    preUserPrint: admin + 'line/preUserPrint',
    preOrderPrint: admin + 'line/preOrderPrint',
    totalPrintData: admin + 'line/totalPrintData',
    genSortCode: admin + 'line/genSortCode',
    exportHandover: admin + 'line/exportHandover', // 交接单导出
    unbindAllLine: admin + 'line/unbindAllLine',
    shareInfo: admin + 'line/getLineAddr',
    orderCommodityDetailList: admin + 'line/orderCommodityDetailList',
  },
  setLine: {
    allotUserConfig: admin + 'line/allotUserConfig', // 排线参数
    searchBeAllotUser: admin + 'line/searchBeAllotUser', // 线路待分配用户
    searchLineUser: admin + 'line/searchLineUser', // 线路已分配用户
    bindUser: admin + 'line/bindUser', // 绑定用户
    unbindUser: admin + 'line/unbindUser', // 解绑用户
    saveLineSequence: admin + 'line/saveLineSequence', // 保存线路排序
    updateDriver: admin + 'line/updateDriver',
    batchDecision: admin + 'lineDecisionTable/batchDecision', // 批量决策
    batchSetOrderLine: admin + 'line/batchSetOrderLine', // 线路订单列表-批量调度
    batchSetUserOrderLine: admin + 'line/batchSetUserOrderLine', //线路客户列表-批量调度
  },
  template: {
    getUserTplGroup: admin + 'printerConfig/getUserTplGroup', //获取用户模板列表
    saveUserGroup: admin + 'printerConfig/saveUserGroup', //保存
    getTplGroupDetail: admin + 'printerConfig/getTplGroupDetail', //获取模板详情
    getTplGroupUserList: admin + 'printerConfig/getTplGroupUserList', //获取用户列表
    getInitTplGroup: admin + 'printerConfig/getInitTplGroup', //获取默认模板
    delUserGroup: admin + 'printerConfig/delUserGroup', //删除
  },
};

// 线路以及订单状态
api.printSearchConfig = admin + 'issueReport/printSearchConfig';

// 运费
api.freight = {
  add: '/superAdmin/freight/add',
  preEdit: '/superAdmin/freight/preEdit',
  edit: '/superAdmin/freight/edit',
  delete: '/superAdmin/freight/delete',
  templateDelete: '/superAdmin/freight/getList',
  templateList: '/superAdmin/freight/getList',
  setRules: '/superAdmin/freight/setRule',
  userList: '/superAdmin/freight/userList',
  setUserFreight: '/superAdmin/freight/setUserTemplateFreight',
  setSelfPickupTemplateFreight:
    '/superAdmin/freight/setSelfPickupTemplateFreight',
  templateUser: '/superAdmin/freight/show',
  cancelUserTemplate: '/superAdmin/freight/deleteTemplateFreight',
  confirmFreightBill: '/superAdmin/accountBill/confirmFreightBill',
  getDistrictList: admin + 'freight/GetDistrictList', // 获取省市区列表
  bindDistricts: admin + 'freight/BindDistricts', // 运费模板绑定省市区
};

// 预包装
api.prePackage = {
  add: admin + 'prePackage/add',
  edit: admin + 'prePackage/edit',
  detail: admin + 'prePackage/detail',
  delete: admin + 'prePackage/del',
  list: admin + 'prePackage/page',
  package: admin + 'prePackage/package',
  searchConfig: admin + 'prePackage/searchConfig',
  exportSum: admin + 'prePackage/exportExcel',
  getPrintData: admin + 'prePackage/getPrintData',
};

// 销售管理api
api.sales = {
  searchConfig: '/superAdmin/salesSuper/searchConfig',
  salesList: '/superAdmin/salesSuper/getSalesList',
  salesDetail: '/superAdmin/salesSuper/detailsSales',
  saveSales: '/superAdmin/salesSuper/saveSales',
  lockSales: '/superAdmin/salesSuper/salesLock',
  invitationUser: '/superAdmin/salesSuper/getInvitationUesr',
  statisticsSellerList: '/superAdmin/orderStatistics/getSellerList',
  commoditySellStatistics: '/superAdmin/orderStatistics/getData',
  commoditySellStatisticsExport: '/superAdmin/orderStatistics/ExportExcel', // 列表
  getAjaxTeamList: '/superAdmin/salesSuper/GetAjaxTeamList', // 下拉框 业务员选团队
  getTeamSelectAdmin: '/superAdmin/salesSuper/GetTeamSelectAdmin', // 下拉框 获取团队列表
  getTeamList: '/superAdmin/salesSuper/GetTeamList', // 列表
  delTeam: '/superAdmin/salesSuper/DelTeam', // 删除
  saveTeam: '/superAdmin/salesSuper/SaveCrmTeam', // 编辑 || 新增
  getTeam: '/superAdmin/salesSuper/GetTeam', // 团队详情
  getUnselectAreas: '/superAdmin/salesSuper/GetUnselectAreas', // 获取未选择区域
  saveWhiteUsers: '/superAdmin/salesSuper/SaveWhiteUsers', // 编辑客户白名单
};

// 优惠券api
api.coupon = {
  editOnline: '/superAdmin/baseCoupon/editOnline', // 优惠券上下架
  searchConfig: '/superAdmin/baseCoupon/searchConfig',
  list: '/superAdmin/baseCoupon/list',
  detail: '/superAdmin/baseCoupon/detail',
  add: '/superAdmin/baseCoupon/add',
  edit: '/superAdmin/baseCoupon/edit',
  send: '/superAdmin/baseCoupon/grant',
  close: '/superAdmin/baseCoupon/close',
  createCodeExport: '/superAdmin/baseCoupon/createCodeExport', // 生成券码导出
  exportCode: '/superAdmin/baseCoupon/exportCode', // 导出查看所有券码
  receiveRecord: '/superAdmin/baseCoupon/receiveRecord',
  cancel: '/superAdmin/baseCoupon/cancel',
  conditionGrant: '/superAdmin/baseCoupon/conditionGrant',
  conditionBatchGrant: '/superAdmin/baseCoupon/conditionBatchGrant',
  batchGrant: '/superAdmin/baseCoupon/batchGrant',
  preSellList: '/superAdmin/commodityPreSale/list', // 商品预售列表
  savepreSellList: '/superAdmin/commodityPreSale/save', // 保存预售商品
  deletepreSell: '/superAdmin/commodityPreSale/delete', // 删除预售商品
  presellProductImport: '/superAdmin/commodityPreSale/import', // 预售商品导入
  presellProductExport: '/superAdmin/commodityPreSale/export', // 预售商品导出
  exportReceiveRecord: '/superAdmin/baseCoupon/exportReceiveRecord',
  importReceiveGoods: '/superAdmin/baseCoupon/importCommodity', // 导入优惠券商品
  // /superAdmin/baseCoupon/ImportGrant
  importGrant: '/superAdmin/baseCoupon/ImportGrant', // 优惠券批量发放,导入客户
};

// 商城api
api.shop = {
  getModuleCustomDecorate: '/superAdmin/mall/getModuleCustomDecorate',
  saveModuleCustomDecorate: '/superAdmin/mall/saveModuleCustomDecorate',
  mallConfig: '/superAdmin/mall/mallConfig',
  homeModal: {
    homePagePopup: '/superAdmin/mall/homePagePopup',
    saveHomePagePopup: '/superAdmin/mall/saveHomePagePopup',
  },
  banner: {
    list: '/superAdmin/mall/bannerList',
    save: '/superAdmin/mall/saveBanner',
    delete: '/superAdmin/mall/delBanner',
  },
  button: {
    getButton: '/superAdmin/mall/buttonMenu',
    saveButton: '/superAdmin/mall/saveButtonMenu',
  },
  subject: {
    getSubject: '/superAdmin/mall/recommendSubject',
    saveSubject: '/superAdmin/mall/saveRecommendSubject',
  },
  recommendCommodity: {
    list: '/superAdmin/mall/recommendCommodity',
    save: '/superAdmin/mall/saveRecommendCommodity',
    delete: '/superAdmin/mall/delRecommendCommodity',
  },
  recommendSubject: {
    list: '/superAdmin/mall/FloorCommodity',
    save: '/superAdmin/mall/saveFloorCommodity',
    delete: '/superAdmin/mall/DelFloorCommodity',
  },
  buylimit: {
    getBuylimit: '/superAdmin/mall/getMallModule',
    saveBuylimit: '/superAdmin/mall/saveActivities',
  },
  topSearch: {
    save: '/superAdmin/mall/saveTopSearch'
  },
  bottomNavigation: {
    save: '/superAdmin/mall/saveBottomNavigation'
  }
};

// 分拣员绩效
api.sorterPerformance = {
  statBySorter: {
    list: '/superAdmin/sorterHistory/accordingSorter',
    export: '/superAdmin/sorterHistory/exportAccordingSorter',
  },
  statByCommodity: {
    list: '/superAdmin/sorterHistory/accordingCommodity',
    listExport: '/superAdmin/sorterHistory/exportAccordingCommodity',
    detail: '/superAdmin/sorterHistory/accordingCommodityDetail',
    detailExport: '/superAdmin/sorterHistory/exportAccordingCommodityDetail',
  },
};

// 分拣员绩效
api.credit = {
  list: '/superAdmin/pointActivity/list',
  detail: '/superAdmin/pointActivity/detail',
  add: '/superAdmin/pointActivity/add',
  edit: '/superAdmin/pointActivity/edit',
  close: '/superAdmin/pointActivity/close',
  obtained: '/superAdmin/pointActivity/obtained',
  searchConfig: '/superAdmin/pointActivity/searchConfig',
};

// 配置
api.settings = {
  aliOpen: {
    list: '/superAdmin/alipayOpen/menuList',
    createMenu: '/superAdmin/alipayOpen/createMenu',
  },
};

// 周转框
api.basket = {
  record: {
    list: '/superAdmin/basket/getAllBasket',
    selectList: '/superAdmin/basket/selectAllBasket',
    add: '/superAdmin/basket/addBasket',
    edit: '/superAdmin/basket/updateBasket',
    delete: '/superAdmin/basket/del',
    toggle: '/superAdmin/basket/isOpenBasket',
    export: '/superAdmin/basket/exportCommonBasket',
    getAllDepositBasket: '/superAdmin/basket/getAllDepositBasket',
  },
  circulationManage: {
    list: '/superAdmin/basket/borrowRecordList',
    add: '/superAdmin/basket/addBorrowRecord',
    edit: '/superAdmin/basket/updateInfo',
    batchAudit: '/superAdmin/basket/batchUpdate',
    delete: '/superAdmin/basket/delBorrowRecord',
    export: 'superAdmin/basket/exportBorrowRecordList',
    import: 'superAdmin/basket/importBorrowRecordList',
  },
  circulationStatistics: {
    list: '/superAdmin/basket/borrowStatistics',
    detail: '/superAdmin/basket/borrowStatisticsDetail',
  },
  bindCommodity: '/superAdmin/basket/commodityByBasket',
  commodityByBasket: '/superAdmin/basket/commodityByBasketDetail',
  // 获取供港框列表
  getHKBasketList: '/superAdmin/basket/getHKBasketList',
  // 保存客户供港框装框数
  saveUserHkBasket: '/superAdmin/basket/saveUserHkBasket',
  // 获取客户供港框装框数
  getUserHkBasket: '/superAdmin/basket/getUserHkBasket',
  // 导出线路客户订单
  exportLineUserOrderList: '/superAdmin/line/exportLineUserOrderList',
  // 导出线路-客户下单商品数量汇总
  exportLineUserOcAmountSum: '/superAdmin/line/exportLineUserOcAmountSum',
};

// 溯源大屏
api.trace = {
  // 商户当天溯源列表
  getUserTraceCommodity: '/commonApi/wapData/getUserTraceCommodity',
  // 用户当天溯源列表详情
  getUserTraceCommodityInfo: '/commonApi/wapData/getUserTraceCommodityInfo',
  // 当天溯源商品排行/供应商/用户/检测商品
  getTodayTrace: '/superAdmin/commodityTrace/getTodayTrace',
  // 大屏趋势
  getTraceTrend: '/superAdmin/commodityTrace/getTraceTrend',
  // 详情
  getScreenTraceCommodityInfo:
    '/superAdmin/commodityTrace/getScreenTraceCommodityInfo',
};

// 税率
api.taxRate = {
  editConfig: '/superAdmin/taxRate/editSysConfig',
  rules: {
    searchConfig: '/superAdmin/taxRate/searchConfig',
    list: '/superAdmin/taxRate/list',
    add: '/superAdmin/taxRate/add',
    edit: '/superAdmin/taxRate/edit',
    changeStatus: '/superAdmin/taxRate/changeStatus',
    detail: '/superAdmin/taxRate/detail',
    exportTemplate: '/superAdmin/taxRate/exportTemplate',
    import: '/superAdmin/taxRate/import',
    exportDetail: '/superAdmin/taxRate/exportDetail',
  },
  user: {
    list: '/superAdmin/taxRate/userList',
    checkUserRule: '/superAdmin/taxRate/CheckUserRule',
    setUserRule: '/superAdmin/taxRate/setUserRule',
    clearUserRule: '/superAdmin/taxRate/clearUserRule',
  },
  group: {
    list: '/superAdmin/taxRate/groupList',
    checkGroupRule: '/superAdmin/taxRate/CheckGroupRule',
    setGroupRule: '/superAdmin/taxRate/setGroupRule',
    clearGroupRule: '/superAdmin/taxRate/clearGroupRule',
  },
};

// 供应商进项税
api.getProviderList = admin + 'taxRate/providerList'; // 列表数据
api.clearProviderRule = admin + 'taxRate/ClearProviderRule'; // 清除供应商规则
api.setProviderRule = admin + 'taxRate/SetProviderRule'; //批量设置用户税率规则
api.checkProviderRule = admin + 'taxRate/CheckProviderRule'; // 供应商税率规则检查

api.reports = {
  purchase: {
    sales: '/superAdmin/reportStatistic/saleReportData',
    salesExport: '/superAdmin/reportStatistic/exportSaleReportData',
  },
  provider: {
    sales: '/superAdmin/reportStatistic/providerReportData',
    salesExport: '/superAdmin/reportStatistic/exportProviderData',
  },
  list: '/superAdmin/salesSuper/searchConfig',
  detail: '/superAdmin/salesSuper/getSalesList',
  salesDetail: '/superAdmin/salesSuper/detailsSales',
  saveSales: '/superAdmin/salesSuper/saveSales',
  lockSales: '/superAdmin/salesSuper/salesLock',
  invitationUser: '/superAdmin/salesSuper/getInvitationUesr',
  statisticsSellerList: '/superAdmin/orderStatistics/getSellerList',
  commoditySellStatistics: '/superAdmin/orderStatistics/getData',
  commoditySellStatisticsExport: '/superAdmin/orderStatistics/ExportExcel',
  userStat: {
    getData: '/superAdmin/userStatistics/getData',
  },
  salesStat: {
    getData: '/superAdmin/salesStatistics/getData',
  },
};

// 客户积分
api.userCredit = {
  searchConfig: '/superAdmin/userSuper/getConfig',
  list: '/superAdmin/userSuper/pointList',
  record: '/superAdmin/userSuper/userPointInfo',
  addIncreaseRecord: '/superAdmin/userSuper/addUserpoint',
  addDecreaseRecord: '/superAdmin/userSuper/delUserpoint?',
};

// 押金筐
api.depositBasket = {
  searchConfig: '/superAdmin/depositBasket/searchConfig',
  list: '/superAdmin/depositBasket/list',
  detail: '/superAdmin/depositBasket/detail',
  audit: '/superAdmin/depositBasket/audit',
  batchAudit: '/superAdmin/depositBasket/batchAudit',
  statisticsList: '/superAdmin/depositBasket/statisticsList',
  statisticsDetail: '/superAdmin/depositBasket/statisticsDetail',
  batchReturnAudit: '/superAdmin/depositBasket/auditById',
  export: '/superAdmin/depositBasket/exportDepositTurnoverBasket',
};

// 营销
api.checkFullReductionGoods = admin + 'fullReductionGift/commodityActivity'; //满减满赠商品检查
api.fullReductionList = admin + 'fullReductionGift/list'; //满减列表
api.addFullReductionGift = admin + 'fullReductionGift/addFullReductionGift'; //新增满减
api.fullReductionConfig = admin + 'fullReductionGift/searchConfig'; //搜索配置
api.closeFullReductionGift = admin + 'fullReductionGift/closeFullReductionGift'; //关闭满减
api.fullReductionDetail = admin + 'fullReductionGift/getDetail'; //满减详情
api.timerPurchaseList = admin + 'timePromotion/ajaxList'; //限时抢购列表
api.timePromotionExport = admin + 'timePromotion/export'; //限时抢购导出
api.timePromotionExportForList =
  '/superAdmin/timePromotion/exportActivityCommodity'; //列表限时抢购导出
api.closePromotion = admin + 'timePromotion/closePromotion'; //关闭限时抢购
api.editOnline = admin + 'TimePromotion/editOnline'; //促销活动上下架
api.getTimerDetail = admin + 'timePromotion/getDetail'; //获取限时抢购详情
api.addPromotion = admin + 'timePromotion/addPromotion'; //新增抢购
api.saveSpecial = admin + 'timePromotion/saveSpecial'; //特价活动新增、编辑
api.updatePromotion = admin + 'timePromotion/updatePromotion'; //编辑抢购
api.updateFullReductionGift =
  admin + 'fullReductionGift/updateFullReductionGift'; //编辑满减
api.timerGetConfig = admin + 'timePromotion/getConfig'; //限时请购配置
api.getCommodityMinPrice = admin + 'timePromotion/getCommodityMinPrice'; //获取商品最低价
api.getEditPrice = admin + 'commoditySuper/editPrice';
api.GetUserPromotionCommodityList =
  admin + 'timePromotion/GetUserPromotionCommodityList';
api.getFullDecreasePartList = '/superAdmin/fullReductionGift/orderActivityList'; // 获取满减活动参与列表
api.exportOrderActivityList =
  '/superAdmin/fullReductionGift/exportOrderActivity'; // 导出满减活动参与列表
api.importMaxOutGoods = '/superAdmin/fullReductionGift/importCommodity'; // 导入满减活动参与商品
api.importFlashSaleGoods = '/superAdmin/timePromotion/importCommodity'; // 导入限时抢购活动参与商品
api.ExportUserPromotionCommodityList =
  admin + 'timePromotion/ExportUserPromotionCommodityList';

// 短信充值相关api
(api.GetAmountInfo = admin + 'sms/GetAmountInfo'), //获取账号信息
  (api.SetSmsStatus = admin + 'sms/SetSmsStatus'), //设置项目状态
  (api.RechargeMoney = admin + 'sms/RechargeMoney'), //账号充值
  (api.SendTestSms = admin + 'sms/SendTestSms'), //发送测试短信
  (api.GetSendRecords = admin + 'sms/GetSendRecords'), //获取发送记录
  (api.GetWxPayStatus = admin + 'sms/GetWxPayStatus'), //获取支付状态
  (api.GetRechargeRecords = admin + 'sms/GetRechargeRecords'), //获取支付状态
  (api.GetAutograph = admin + 'sms/CheckProject'),
  (api.setInitAutograph = admin + 'sms/InitProject'),
  (api.updateSign = admin + 'sms/updateSign'); // 修改短信签名
api.smsBatchSend = admin + 'SmsBatchSend/send'; // 发送营销短信
api.cancelSend = admin + 'SmsBatchSend/cancelSend'; // 取消营销短信
api.addTemplate = admin + 'SmsBatchSend/addTemplate'; // 取消营销短信
api.deleteTemplate = admin + 'SmsBatchSend/deleteTemplate'; // 取消营销短信
// 离线任务列表（导入/导出）
api.taskCenterList = admin + 'offline/getList';
api.taskCenterClear = admin + 'offline/clearList';
api.taskCenterDownload = admin + 'offline/download';
api.taskResult = '/superAdmin/general/taskResult';

api.reportOffLineExportCommoditySale =
  admin + 'report/OffLineExportCommoditySale';
api.machineList = admin + 'commodityTrace/machineList'; // 设备检测记录-列表
api.printReport = admin + 'commodityTrace/printReport'; // 打印检测报告
// 反馈建议
api.getSuggestList = admin + 'userSuper/getSuggestList'; // 获取列表
api.getSuggestDetail = admin + 'userSuper/getSuggestDetail'; // 获取详情
api.suggestReply = admin + 'userSuper/suggestReply'; // 回复反馈建议
api.delSuggest = admin + 'userSuper/delSuggest'; // 删除

api.getUserTagList = admin + 'userSuper/getUserTagList'; // 获取所有客户标签列表
api.saveUserTag = admin + 'userSuper/saveUserTag'; // 保存客户标签
api.delUserTag = admin + 'userSuper/delUserTag'; // 删除客户标签

api.centralPurchaseImportBtn =
  admin + 'commoditySuper/centralPurchaseImportBtn'; //集采平台导入商品按钮信息
api.centralPurchaseRefreshOauthCode =
  admin + 'sysconfig/centralPurchaseRefreshOauthCode'; // 重新生成授权码
api.centralPurchaseUpdateOauth = admin + 'sysconfig/centralPurchaseUpdateOauth'; // 启用禁用【系统参数-集采平台tab】

// 套餐
api.goodsPackage = {
  list: '/superAdmin/commodityPackage/list',
  add: '/superAdmin/commodityPackage/add',
  edit: '/superAdmin/commodityPackage/edit',
  detail: '/superAdmin/commodityPackage/detail',
  delete: '/superAdmin/commodityPackage/delete',
  setConfig: '/superAdmin/commodityPackage/editSysConfig',
  batchCloseOrder: '/superAdmin/commodityPackage/list',
  GetNutrientElementList: '/superAdmin/commodityPackage/GetNutrientElementList',
  weekCookbook: '/superAdmin/weekCookbook/list', // 食谱列表
  weekCookbookAdd: '/superAdmin/weekCookbook/add', // 添加食谱
  weekCookbookDetail: '/superAdmin/weekCookbook/detail', //食谱详情
  weekCookbookAudit: '/superAdmin/weekCookbook/audit', //食谱审核
  /* 新加2022年02月24日16:58:32 */
  weekCookbookOrderDetail: '/superAdmin/recipeOrder/detail', //食谱订单详情
  weekCookbookOrderAdd: '/superAdmin/recipeOrder/add', //食谱订单添加
  weekCookbookOrderEdit: '/superAdmin/recipeOrder/edit', //食谱订单编辑
  weekCookbookOrderAudit: '/superAdmin/recipeOrder/audit', //食谱订单审核
  weekCookbookOrderClose: '/superAdmin/recipeOrder/close', //食谱订单关闭
  weekCookbookOrderExport: '/superAdmin/recipeOrder/export', // 食谱订单导出
  /* 新加2022年02月24日16:58:32 */
  weekCookbookList: '/superAdmin/weekCookbook/list', // 食谱列表
  weekCookbookRecipelist: '/superAdmin/weekCookbook/recipelist', //
  weekCookbookAddOrder: '/superAdmin/weekCookbook/addOrder', // 新增食谱订单
  weekCookbookCopy: '/superAdmin/weekCookbook/copy', // 复制图片
  weekCookbookStatus: '/superAdmin/weekCookbook/status', // 状态更改
  weekCookbookEdit: '/superAdmin/weekCookbook/edit', // 编辑
  // 新版套餐食谱
  // mealTypeList: admin +
};

api.goodsPackageOrder = {
  searchConfig: '/superAdmin/packageOrder/SearchConfig',
  list: '/superAdmin/packageOrder/list',
  detail: '/superAdmin/orderSuper/AjaxDetail',
  edit: '/superAdmin/packageOrder/edit',
  add: '/superAdmin/packageOrder/add',
  close: '/superAdmin/packageOrder/close',
  exportList: '/superAdmin/packageOrder/exportList',
  exportDetail: '/superAdmin/packageOrder/exportDetail',
  importOrderTemplate: '/superAdmin/packageOrder/importOrderTemplate', // 下载模板
  importOrder: '/superAdmin/packageOrder/importOrder', // 导入模板
};

api.goodsPackageReport = {
  list: '/superAdmin/packageOrder/reportList',
  detail: '/superAdmin/packageOrder/reportDetail',
  exportList: '/superAdmin/packageOrder/exportReportList',
  exportDetail: '/superAdmin/packageOrder/exportReportDetail',
};

// 原料定价模式套餐食谱
api.packageRecipe = {
  list: admin + 'Cookbook/list',
  add: admin + 'Cookbook/add',
  detail: admin + 'Cookbook/detail',
  edit: admin + 'Cookbook/edit',
  status: admin + 'Cookbook/status',
  createOrder: admin + 'Cookbook/addOrder',
  exportTemplate: admin + 'Cookbook/exportTemplate', // 按套餐导入订单的模板
  import: admin + 'Cookbook/import', // 按套餐导入订单
};

api.common = {
  checkTodoList: admin + 'General/checkTodoList',
  toggleNoticeRing: '/superAdmin/adminSuper/editRing',
  checkProjectExpire: '/superAdmin/General/getProjectExpireDate',
  checkSortTask: admin + 'general/sortingMission', // 检查分拣任务分配情况
};

api.shopNotice = {
  list: '/superAdmin/shopNotice/list',
  add: '/superAdmin/shopNotice/add',
  edit: '/superAdmin/shopNotice/edit',
  delete: '/superAdmin/shopNotice/del',
  detail: '/superAdmin/shopNotice/info',
};

// 同步总站点商品
api.syncMainSiteGoods = {
  goodsList: '/superAdmin/commoditySuper/getSiteCommodity',
  syncGoods: '/superAdmin/commoditySuper/syncSitesCommodity',
};

api.providerContractPrice = {
  list: '/superAdmin/purchaseAgreementPrice/list',
  add: '/superAdmin/purchaseAgreementPrice/add',
  edit: '/superAdmin/purchaseAgreementPrice/edit',
  close: '/superAdmin/purchaseAgreementPrice/close',
  detail: '/superAdmin/purchaseAgreementPrice/detail',
  expireList: '/superAdmin/purchaseAgreementPrice/expire',
  searchConfig: '/superAdmin/purchaseAgreementPrice/searchConfig',
  exportDetail: '/superAdmin/purchaseAgreementPrice/exportInfo',
  downloadImportTemplate: '/superAdmin/purchaseAgreementPrice/exportTemplate',
  offlineImport: '/superAdmin/purchaseAgreementPrice/offlineImport',
  audit: '/superAdmin/purchaseAgreementPrice/audit',
};

api.userContractPrice = {
  update: '/superAdmin/protocolPrice/update',
  checkEffectiveOrder: '/superAdmin/protocolPrice/userOrderValidAndAudit',
};

api.operator = {
  changePassword: '/superAdmin/adminSuper/modifyUserPass',
};

api.goodsMap = {
  list: '/superAdmin/commoditySuper/ajaxList',
  import: '/superAdmin/orderSuper/checkBatchImport',
  downloadTemplate: '/superAdmin/orderSuper/checkBatchImport',
};

api.smartPrice = {
  clearFormula: '/superAdmin/commodityIntelligentPrice/clearFormula',
};

api.bills = {
  order: {
    list: '/superAdmin/NewBehalfSuper/ajaxGetHistoryOrder',
    getPrintData: '/superAdmin/IssueOrder/getPrintData',
    getReportPrintData: '/superAdmin/issueReport/getPrintData',
  },
  finance: {
    list: '/superAdmin/NewBehalfSuper/AjaxList',
    detail: '/superAdmin/NewBehalfSuper/AjaxDetail',
    editDetail: '/superAdmin/NewBehalfSuper/getModifyOrderCommodity',
    add: '/superAdmin/NewBehalfSuper/AjaxAdd',
    edit: '/superAdmin/NewBehalfSuper/AjaxSaveModifiedOrder',
    close: '/superAdmin/NewBehalfSuper/AjaxDelete',
    finish: '/superAdmin/NewBehalfSuper/complete',
    export: '/superAdmin/NewBehalfSuper/AjaxExport',
    orderInitData: '/superAdmin/behalfSuper/getBehalfOrderStatus',
  },
  financeReports: {
    categoryStat: '/superAdmin/NewBehalfSuper/GetCategoryReport',
    categoryStatExport: '/superAdmin/NewBehalfSuper/GetCategoryReportExcle',
    goodsStat: '/superAdmin/NewBehalfSuper/GetGoodsReport',
    goodsStatExport: '/superAdmin/NewBehalfSuper/GetGoodsReportExcle',
  },
};

api.behalf = {
  delete: '/superAdmin/behalfSuper/deleteOrder', // 删除订单'
  getPrintData: '/superAdmin/behalfSuper/getPrintData',
};

api.mall = {
  getColor: '/superAdmin/mall/getShopColor',
  saveColor: '/superAdmin/mall/saveShopColor',
};

api.sms = {
  sendStatistics: '/superAdmin/sysconfig/sendSmsInfoList',
};

api.rechargeRules = {
  list: '/superAdmin/rechargeRule/list',
  save: '/superAdmin/rechargeRule/create',
};

api.purchasePlan = {
  list: '/superAdmin/purchaseTask/list',
  syncOrder: '/superAdmin/purchaseTask/sync',
  export: '/superAdmin/userSuper/userExport',
  genPurchaseOrder: '/superAdmin/purchaseTask/genOrder',
  orderItems: '/superAdmin/purchaseTask/orderItems',
  detail: '/superAdmin/purchaseTask/detail',
  allotDetail: '/superAdmin/purchaseTask/allotDetail',
  allot: '/superAdmin/purchaseTask/allot',
  getAllotOrder: '/superAdmin/purchaseTask/getAllotOrder',
  updateProvider: '/superAdmin/purchaseTask/updateProvider',
  updateProviderSupervisor: '/superAdmin/purchaseTask/updateProviderSupervisor',
  batchUpdateProvider: '/superAdmin/purchaseTask/batchUpdateProvider',
  updateItem: '/superAdmin/purchaseTask/updateItem',
  delItem: '/superAdmin/purchaseTask/delItem',
  allotOrderList: '/superAdmin/orderSuper/AjaxList',
  batchUpdatePurchasePerson:
    '/superAdmin/purchaseTask/batchUpdatePurchasePerson',
  saveOrderTask: '/superAdmin/purchaseTask/saveOrderTask',
  taskAllocate: '/superAdmin/purchaseTask/allocate',
  genInquiryOrder: '/superAdmin/rfq/genInquiryOrder',
  resetPurchaseTaskItem: admin + 'purchaseTask/resetPurchaseTaskItem',
  getMinPlanDateByOrderCreateTime:
    admin + 'purchaseTask/GetMinPlanDateByOrderCreateTime',
};

api.purchaseOrder = {
  getPrintData: '/superAdmin/purchaseOrder/printData',
  batchPrintData: '/superAdmin/purchaseOrder/batchPrintData',
  mergeOrder: '/superAdmin/purchaseOrder/mergeOrder',
  batchCloseOrder: '/superAdmin/purchaseOrder/cancel',
  getMergeOrders: '/superAdmin/purchaseOrder/getMergeOrderList',
  batchReceipt: '/superAdmin/purchaseOrder/batchReceipt',
};

api.purchaseReturn = {
  getPrintData: '/superAdmin/purchaseReturn/printData',
};

api.orderSuper = {
  getPrintData: '/superAdmin/orderSuper/getPrintData',
};

api.purchaseReportPrice = {
  list: '/superAdmin/rfq/list',
  add: '/superAdmin/rfq/new',
  edit: '/superAdmin/rfq/edit',
  editNum: '/superAdmin/rfq/editNum',
  detail: '/superAdmin/rfq/detail',
  close: '/superAdmin/rfq/close',
  offerOrder: '/superAdmin/rfq/offerOrder',
  chooseOffer: '/superAdmin/rfq/chooseOffer',
  reportDetail: '/superAdmin/rfq/offerDetail',
  providerList: '/superAdmin/rfq/providerList',
  updateOfferUser: '/superAdmin/rfq/updateOfferUser',
  exportInquiryOrders: '/superAdmin/rfq/exportInquiryOrders',
};

api.user = {
  reminder: '/superAdmin/statUser/reminder',
};

api.userProvider = {
  downloadTemplate: '/superAdmin/customProvider/getImportTemplate',
  importExcel: '/superAdmin/customProvider/binImport',
  exportCommodityProviders:
    '/superAdmin/customProvider/exportCommodityProviders',
};

// 原料分割
api.partition = {
  list: '/superAdmin/commodityParting/list',
  delete: '/superAdmin/commodityParting/delete',
  add: '/superAdmin/commodityParting/add',
  edit: '/superAdmin/commodityParting/edit',
  detail: '/superAdmin/commodityParting/detail',
  confirmPartition: '/superAdmin/commodityParting/ConfirmParting',
  export: '/superAdmin/commodityParting/export',
  getSortData: '/superAdmin/commodityParting/getSortData',
};

// 原料分割记录
api.partitionRecord = {
  searchConfig: '/superAdmin/commodityParting/searchConfig',
  list: '/superAdmin/commodityParting/recordList',
  cancel: '/superAdmin/commodityParting/recordCancel',
  detail: '/superAdmin/commodityParting/recordDetail',
  audit: '/superAdmin/commodityParting/recordAudit',
  export: '/superAdmin/commodityParting/recordExport',
  exportRecordList: '/superAdmin/commodityParting/exportRecordList',
};

api.billsOrder = {
  list: '/superAdmin/issueOrder/getList',
  save: '/superAdmin/issueOrder/save',
  detail: '/superAdmin/issueOrder/getDetails',
  listExport: '/superAdmin/issueOrder/offlineExport',
  close: '/superAdmin/issueOrder/OpenClose',
  recovery: '/superAdmin/issueOrder/OpenClose',
  detailExport: '/superAdmin/issueOrder/offlineExportDetails',
  batchOrder: '/superAdmin/issueOrder/batchOrder',
};

api.orderRefund = {
  list: '/superAdmin/OrderRefundRecord/list',
  searchConfig: '/superAdmin/OrderRefundRecord/searchConfig',
  relaunch: '/superAdmin/OrderRefundRecord/relaunch',
};

api.batchSummary = {
  searchConfig: '/superAdmin/orderSuper/orderPoolSearchConfig',
};

api.store = {
  deliverySearchConfig: '/superAdmin/orderDelivery/searchConfig',
};

api.orderModify = {
  list: '/superAdmin/orderModify/list',
  add: '/superAdmin/orderModify/add',
  edit: '/superAdmin/orderModify/edit',
  audit: '/superAdmin/orderModify/audit',
  antiAudit: '/superAdmin/orderModify/reAudit',
  detail: '/superAdmin/orderModify/detail',
  close: '/superAdmin/orderModify/close',
  searchConfig: '/superAdmin/orderModify/searchConfig',
  printData: '/superAdmin/orderModify/printData',
};

api.sortOneTouch = {
  getOneTouchSortRecord: '/superAdmin/sortSuper/getOneTouchSortRecordList',
  recovery: '/superAdmin/sortSuper/resetOneTouchSort',
};

// 报告营销
api.annualBill = {
  switchs: '/superAdmin/annualBill/switch', // 开启关闭账单
  getList: '/superAdmin/annualBill/getList', // 获取全部参数
  switchPopup: '/superAdmin/annualBill/switchPopup', // 开关首页弹窗
  switchPurchaseAmount: '/superAdmin/annualBill/switchPurchaseAmount', // 开关采购金额
};

// 问题反馈
api.saveQuestion = admin + 'Company/AddFeedBack';

api.getThirdApp = '/superAdmin/general/customApp';
// /superAdmin/General/CheckRemoteAssistanceCode
api.CheckRemoteAssistanceCode =
  '/superAdmin/LoginSuper/CheckRemoteAssistanceCode';
api.synAppcUser = '/superAdmin/userSuper/SyncUserCenterInit';

// 高德地图 API
api.amap = {
  getUserLocation: 'https://restapi.amap.com/v3/ip',
  getWeatherInfo: 'https://restapi.amap.com/v3/weather/weatherInfo',
  getAddressByLocation: 'https://restapi.amap.com/v3/geocode/regeo',
};

// 数据大屏 API
api.dataScreen = {
  commodityStatistics: '/superAdmin/dataScreen/commodityStatistics',
  sortStatistics: '/superAdmin/dataScreen/SortStatistics',
  distributionStatistics: '/superAdmin/dataScreen/distributionStatistics',
  getSiteList: '/siteAdmin/site/list',
  switchSite: '/siteAdmin/frame/switchSite',
};

// 金蝶凭证导出
api.exportKingdeeProof = '/superAdmin/accountSettle/jinDieExport';

api.siteAdmin = siteAdminApi;

// 应用中心自动打印相关
api.getprintedlist = '/superAdmin/AutomaticOrdering/getlist'; // 已打印订单列表
api.pollingPrinting = '/superAdmin/AutomaticOrdering/PollingPrinting'; // 轮询接口

// 新报表
api.getProfitTable = '/superAdmin/journalAccount/profitTable';
api.getExportProfitTable = '/superAdmin/journalAccount/exportProfitTable';

// 订单商品
api.orderCommodityList = '/superAdmin/orderSuper/orderCommodityList';
api.orderCommodityReplaceList =
  '/superAdmin/orderSuper/orderCommodityReplaceList';
api.orderCommodityReplace = '/superAdmin/orderSuper/orderCommodityReplace';
api.orderCommodityReplaceFailureList =
  '/superAdmin/orderSuper/orderCommodityReplaceFailureList';
api.exportOrderCommodityReplaceFailureList =
  '/superAdmin/orderSuper/exportOrderCommodityReplaceFailureList';
api.getOrderCommodityDeleteData =
  '/superAdmin/orderSuper/getOrderCommodityDeleteData';
api.getOrderCommodityEditData =
  '/superAdmin/orderSuper/getOrderCommodityEditData';
api.orderCommodityEdit = '/superAdmin/orderSuper/orderCommodityEdit';
api.orderCommodityDelete = '/superAdmin/orderSuper/orderCommodityDelete';
api.orderCommodityModify = '/superAdmin/orderSuper/orderCommodityModify';
api.orderBatchBrushOcPrice = '/superAdmin/orderSuper/BatchBrushOcPrice';

api.getVideoSignUrl = '/superAdmin/userScale/getVideoSignUrl';

// 保存商品服务费
api.serviceChargeSave = '/superAdmin/serviceCharge/save';
// 批量保存商品服务费
api.serviceChargeBatchSave = '/superAdmin/serviceCharge/BatchSave';
// 获取商品服务费列表
api.getServiceChargeList = '/superAdmin/serviceCharge/List';
// 删除商品服务费
api.deleteServiceCharge = '/superAdmin/serviceCharge/delete';
// 导出商品服务费
api.exportServiceCharge = '/superAdmin/serviceCharge/Export';
// 导入商品服务费
api.importServiceCharge = '/superAdmin/serviceCharge/Import';
// 获取服务费模板列表
api.getServiceChargeTemplateList = '/superAdmin/serviceCharge/TemplateList';
// 设置默认服务费模板
api.setServiceChargeDefaultTemplate =
  '/superAdmin/serviceCharge/SetDefaultTemplate';
// 删除服务费模板
api.deleteServiceChargeTemplate = '/superAdmin/serviceCharge/DelTemplate';
// 客户服务费列表
api.getUserTemplateRelationList =
  '/superAdmin/serviceCharge/UserTemplateRelationList';
// 客户绑定模板
api.bindUserTemplateRelation =
  '/superAdmin/serviceCharge/BindUserTemplateRelation';
// 客户删除模板
api.delUserTemplateRelation =
  '/superAdmin/serviceCharge/DelUserTemplateRelation';

api.getSalarySet = '/superAdmin/performance/getSalarySet'; // 获取司机/分拣员工资配置
api.setSalarySet = '/superAdmin/performance/setSalarySet'; // 设置司机/分拣员工资配置
api.salarySetList = '/superAdmin/performance/salarySetList'; // 获取司机/分拣员工资配置列表
api.addSalarySetItem = '/superAdmin/performance/addSalarySetItem'; // 获取司机/分拣员工资配置
api.delSalarySetItem = '/superAdmin/performance/delSalarySetItem'; // 删除司机/分拣员工资配置详情
api.editSalarySetItem = '/superAdmin/performance/editSalarySetItem'; // 编辑司机/分拣员工资配置详情
api.importSalarySet = '/superAdmin/performance/importSalarySet'; // 司机/分拣员工资配置导入
api.exportSalarySet = '/superAdmin/performance/exportSalarySet'; // 司机/分拣员工资配置导出

// 分拣阈值管理
api.sortingThreshold = {
  sortThresholdList: admin + 'Threshold/sortThresholdList', // 按分类设置的分拣阈值列表
  updateSortThreshold: admin + 'Threshold/updateSortThreshold', // 修改分拣阈值
};

// 修改 openApiSecret
api.updateOpenApiSecret = '/superAdmin/company/updateOpenApiSecret';
// 获取 openApi
api.getOpenApiInfo = '/superAdmin/company/getOpenApiInfo';
api.getRemoteAssistanceCode = '/superAdmin/company/remoteAssistanceCode';
// 演示数据  生成数据得接口
api.generalGenBusinessData = admin + 'general/genBusinessData';

// 扫码装框接口
// 获取列表
api.userBasketList = '/superAdmin/userBasket/list';
api.userBasketDetail = '/superAdmin/userBasket/detail';
api.unloadBaskket = '/superAdmin/userBasket/unload';

// 拆单客户设置相关
api.splitOrderUserList = admin + 'orderSuper/splitOrderUserList';
api.addSplitUser = admin + 'orderSuper/addSplitUser';
api.deleteSplitUser = admin + 'orderSuper/deleteSplitUser';

// 添加商品自定义字段
api.addCustomizeField = admin + 'commodityCustomizeField/addCustomizeField';
// 修改商品自定义字段
api.updateCustomizeField =
  admin + 'commodityCustomizeField/updateCustomizeField';
// 更新自定义字段排序
api.updateSortNum = admin + 'commodityCustomizeField/updateSortNum';
// 删除商品自定义字段
api.deletecustomizeField =
  admin + 'commodityCustomizeField/deletecustomizeField';
// 获取商品自定义字段列表
api.customizeFieldList = admin + 'commodityCustomizeField/customizeFieldList';
// 获取供应商自定义字段列表
api.providerCustomizeFieldList = admin + 'provider/GetInitData';
// 获取自定义字段键值列表（受开关影响）
api.customizeFieldKeys = admin + 'commodityCustomizeField/customizeFieldKeys';

// 财务
api.finance = {
  capitalFlowList: admin + 'accountBalance/capitalFlowList', // 支付流水页面数据
  exportCapitalFlow: admin + 'accountBalance/exportCapitalFlow', // 导出支付流水页面数据
  onlinePaymentConfig: admin + 'accountBalance/onlinePaymentConfig', // 在线支付流水下拉框配置
  auditJournalAccount: admin + 'journalAccount/auditJournalAccount', // 资金流水审核
  editJournalAccount: admin + 'journalAccount/editJournalAccount', //编辑资金流水
  closeJournalAccount: admin + 'journalAccount/closeJournalAccount', //关闭资金流水
  AbnormalPayOrderList: admin + 'accountBalance/AbnormalPayOrderList', // 异常支付订单
};
// 支付方式
api.getPayWayListNew = admin + 'payWay/list'; // 获取支付方式列表
api.postPayWayAdd = admin + 'payWay/add'; // 添加支付方式
api.setPayWayDisable = admin + 'payWay/disable'; // 禁用
api.setPayWayEnable = admin + 'payWay/enable'; // 禁用
api.deletePayWay = admin + 'payWay/delete'; // 删除
(api.offlinePayWayEdit = admin + 'payWay/edit'), // 线下支付方式编辑
  (api.onLinePayWayEdit = admin + 'payWay/editOnline'), // 线上支付方式编辑
  (api.payWaySelect = admin + 'accountBalance/editPayWay'), // 客户余额 余额账款 选择 支付方式
  // 生鲜行情
  (api.freshQuotation = {
    getProvinceList: admin + 'appCenter/listProvinceInfo',
    getCategoryList: admin + 'appCenter/listCategoryInfo',
    list: admin + 'appCenter/pagePriceFresh',
    historyChart: admin + 'appCenter/getPriceGraph',
    historyList: admin + 'appCenter/pagePriceHistorical',
  });
// -------------------appcenter_begin-----------------------------

// 招标信息
api.getGeneralBiddingAuthorizeInfo = admin + 'general/biddingAuthorizeInfo';

// -------------------appcenter_end-------------------------------
// 线上支付报表
api.reportOnlinePaymentFlowStat = admin + 'report/OnlinePaymentFlowStat';
// 导出线上支付报表
api.exportOnlinePaymentFlowStat = admin + 'report/ExportOnlinePaymentFlowStat';
// 导出支付报表每日明细
api.exportOnlinePaymentStatDetail =
  admin + 'report/ExportOnlinePaymentStatDetail';
// 线上支付流水
api.onlinePaymentFlowList = admin + 'report/OnlinePaymentFlowList';
// 导出线上支付流水
api.exportOnlinePaymentFlowList = admin + 'report/ExportOnlinePaymentFlowList';

api.subsidiary = {
  tree: admin + 'subsidiary/tree', // 子机构树形结构数据列表 (单个用户)
  list: admin + 'subsidiary/list', // 子机构列表
  add: admin + 'subsidiary/add', // 子机构新增
  edit: admin + 'subsidiary/edit', // 子机构新增
  delete: admin + 'subsidiary/delete', // 子机构新增
};

// 收货统计
api.getReceivingStatisticsByGoods =
  admin + 'statSuper/ModifyCommoditySummaryList';
api.getReceivingStatisticsByDiff =
  admin + 'statSuper/ModifyItemReasonSummaryList';

/*
  食谱餐次
*/
api[RECIPE_MEAL_TIME_API.key] = RECIPE_MEAL_TIME_API.create(admin);

// 业务工资配置详情
api.saleSalaryConfig = admin + 'salesSuper/SaleSalaryConfig';
// 业务工资配置编辑
api.saveSaleSalaryConfig = admin + 'salesSuper/SaveSaleSalaryConfig';
// 业务工资条目删除
api.delSaleSalaryConfig = admin + 'salesSuper/DelSaleSalaryConfig';
// 导入业务工资配置
api.importSaleSalaryConfig = admin + 'salesSuper/ImportSaleSalaryConfig';
// 导出业务工资配置
api.exportSaleSalaryConfig = admin + 'salesSuper/ExportSaleSalaryConfig';
// 获取业务工资模板分页列表
api.saleSalaryTemplatePaginate =
  admin + 'salesSuper/saleSalaryTemplatePaginate';
// 业务工资模板详情
api.saleSalaryTemplateDetail = admin + 'salesSuper/saleSalaryTemplateDetail';
// 设置业务工资默认模板
api.setDefaultSaleSalaryTemplate =
  admin + 'salesSuper/setDefaultSaleSalaryTemplate';
// 删除业务工资模板
api.deleteSaleSalaryTemplate = admin + 'salesSuper/deleteSaleSalaryTemplate';
// 业务工资客户列表
api.userTemplateRelationList = admin + 'salesSuper/userTemplateRelationList';
// 绑定客户业务工资模板
api.bindSalesSuperUserTemplateRelation =
  admin + 'salesSuper/bindUserTemplateRelation';
// 删除客户业务工资模板
api.delSaleSalaryUserTemplateRelation =
  admin + 'salesSuper/delUserTemplateRelation';

// 获取rpa生成采购单配置
api.getGenPurchaseOrderConfig = admin + 'cron/getGenPurchaseOrderConfig';
// 新增rpa生成采购单配置
api.addGenPurchaseOrderConfig = admin + 'cron/addGenPurchaseOrderConfig';
// 编辑rpa生成采购单配置
api.updateGenPurchaseOrderConfig = admin + 'cron/updateGenPurchaseOrderConfig';
// 定时任务记录列表
api.getCronHandleRecordList = admin + 'cron/getCronHandleRecordList';
// 获取定时生成详情
api.getCronHandleRecordDetail = admin + 'cron/getCronHandleRecordDetail';

api.shopTheme = {
  getCurrentTheme: admin + 'mall/currentTheme', // 获取当前主题
  applyTheme: admin + 'mall/applyTheme', // 应用主题
};

// 批发市场价格智能定价
api[wholesaleMarket_API.key] = wholesaleMarket_API.create(admin);

// 商城必订品
api.merchantMustOrderRules = admin + 'receStyle/MerchantMustOrderRules';
api.getFreeReceivableStyle = admin + 'receStyle/GetFreeReceivableStyle';
api.merchantMustOrderDetail = admin + 'receStyle/MerchantMustOrderDetail';
api.saveMerchantMustOrder = admin + 'receStyle/SaveMerchantMustOrder';
api.merchantMustOrderRules = admin + 'receStyle/MerchantMustOrderRules';
api.delMerchantMustOrderRules = admin + 'receStyle/DelMerchantMustOrderRules';

// 商品阶梯定价
api.getPriceGrads = admin + 'commoditySuper/getPriceGrads'; // 商品阶梯定价详情
api.updatePriceGrads = admin + 'commoditySuper/updatePriceGrads'; // 保存商品阶梯定价

//全局过滤器保存配置文件
api.saveListSearchFieldsConfig = admin + 'general/saveListSearchFieldsConfig'; //保存配置
api.getListSearchFieldsConfig = admin + 'general/getListSearchFieldsConfig'; //保存配置

//应用中心系统对接接口
api.getABConfig = admin + 'consortium/getConfig'; //获取配置
api.getSyncLog = admin + 'consortium/getSyncLog'; //获取日志
api.retrySyncLog = admin + 'consortium/retrySyncLog'; //重试日志
api.setCommodityAuth = admin + 'consortium/setCommodityAuth'; //重试日志
api.manualSync = admin + 'consortium/manualSync'; //重试实体
api.batchBindCPID = admin + 'consortium/batchBindCPID'; //设置供应商
api.getCentreCommodityList = admin + 'consortium/getCentreCommodityList'; //获取商品
api.getCentreCommodityCate = admin + 'consortium/getCentreCommodityCate'; //获取商品分类
api.downLoadCommodity = admin + 'consortium/downLoadCommodity'; //获取商品分类

// 自提管理
api.SelfPickupPointPaginate = admin + 'SelfPickupPoint/paginate'; // 自提点列表
api.SelfPickupPointList = admin + 'SelfPickupPoint/list'; // 自提点列表
api.SelfPickupPointSave = admin + 'SelfPickupPoint/save'; // 自提点保存
api.SelfPickupPointDelete = admin + 'SelfPickupPoint/delete'; // 自提点删除

// 收支管理
api.addManner = admin + 'IncomeAndExpend/addManner'; // 新增收支
api.delManner = admin + 'IncomeAndExpend/delManner'; // 删除收支
api.editManner = admin + 'IncomeAndExpend/editManner'; // 编辑收支
api.getMannerList = admin + 'IncomeAndExpend/getMannerList'; // 收支列表
api.delRecord = admin + 'IncomeAndExpend/delRecord'; // 删除收支登记
api.addRecord = admin + 'IncomeAndExpend/addRecord'; // 新增/编辑收支登记
api.conditionList = admin + 'IncomeAndExpend/conditionList'; // 收支类型、收支方式下拉数据

// 容器管理
api.containerList = admin + 'standardManage/containerList'; // 容器列表
api.addContainer = admin + 'standardManage/addContainer'; // 新增容器
api.containerItem = admin + 'standardManage/containerItem'; // 容器内存量商品
api.delContainer = admin + 'standardManage/delContainer'; // 删除容器
api.containerOperateLog = admin + 'standardManage/containerOperateLog'; // 容器上下架流水
api.commodityItem = admin + 'standardManage/commodityItem'; // 商品容器库存明细
api.exportLocationSort = admin + 'standardManage/exportLocationSort'; // 导出库区库位排序

api.sellMode = admin + 'commoditySuper/SellMode';
api.addSellUser = admin + 'commoditySuper/addSellUser';
api.sellUserList = admin + 'commoditySuper/sellUserList';
api.orderDesignateSplit = admin + 'orderSuper/orderDesignateSplit';
api.disableMode = admin + 'userSuper/disableMode';
api.disableModesDesc = admin + 'userSuper/disableModesDesc'; // 获取当前客户的屏蔽售卖文案

api.mealPlanDetail = admin + 'mealPlan/detail';
api.mealPlanAudit = admin + 'mealPlan/audit';
api.auditPriceOffer = admin + 'commoditySuper/auditPriceOffer';
api.getRealTimeMonitorList = admin + 'deliveryMap/GetRealTimeMonitorList';
api.GetRealTimeMonitor = admin + 'deliveryMap/GetRealTimeMonitor';
api.GetGpsVideoCars = admin + 'deliveryMap/GetGpsVideoCars';
api.accountAntiBill = admin + 'accountBill/antiBill'; // 客户结算反对账:
api.purchaseAntiBill = admin + 'purchaseBill/antiBill'; // 采购结算反对账
api.userOrderDesignateSplit = admin + 'orderSuper/userOrderDesignateSplit'; // 拆分指定客户的订单

// 地址定位获取POI
api.searchPoint = admin + 'userSuper/SearchPoi';

// 商品评价
api.commentAudit = admin + 'comment/audit';
api.delProviderComment = admin + 'provider/delProviderComment';
api.replyProviderComment = admin + 'provider/replyProviderComment';
api.saveProviderCommentGradeCols =
  admin + 'provider/saveProviderCommentGradeCols';
api.getProviderCommentGradeColsList =
  admin + 'provider/getProviderCommentGradeColsList';
api.createProviderPeriodComment =
  admin + 'provider/createProviderPeriodComment';
api.editProviderPeriodComment = admin + 'provider/editProviderPeriodComment';

// oa审批接口
// 1. 用户管理模块
api.OaGetUserList = admin + 'Oa/getUserList'; // 获取用户列表
api.OaDelUser = admin + 'Oa/delUser'; // 删除用户
api.OaAddUser = admin + 'Oa/addUser'; // 添加用户
api.OaEditUser = admin + 'Oa/editUser'; // 编辑用户
// 2.审批列表模拟
api.OaAuditList = admin + 'Oa/auditList'; // 审批列表
api.OaAuditInfo = admin + 'Oa/auditInfo'; // 审批详情
// 3. 模板管理模块
api.OaSaveOaAuditTemplate = admin + 'Oa/SaveOaAuditTemplate'; // 保存OA审批模板
api.OaAuditTemplateList = admin + 'Oa/OaAuditTemplateList'; // OA审批模板列表
api.DelAuditTemplate = admin + 'Oa/DelAuditTemplate'; // 删除oa审批模板
api.AuditTemplateDetail = admin + 'Oa/AuditTemplateDetail'; // OA审批模板详情

// 菜谱订单早发货商品
api.preDeliverylist = admin + 'userSuper/preDeliverylist';
api.importPreDeliveryTemplate = admin + 'userSuper/importPreDeliveryTemplate';
api.importPreDelivery = admin + 'userSuper/importPreDelivery';
api.addPreDeliveryCommodity = admin + 'userSuper/addPreDeliveryCommodity';
api.deletePreDelivery = admin + 'userSuper/deletePreDelivery';
api.exportPreDelivery = admin + 'userSuper/exportPreDelivery';

api.receivableSummaryMonthExport =
  admin + 'accountCredit/receivableSummaryMonthExport';
api.purchaseAgreementPriceRefresh = admin + 'purchaseAgreementPrice/refresh';
api.lockOrderCommodity = admin + 'orderSuper/lockOrderCommodity';
api.lockOrder = admin + 'orderSuper/lockOrder';
api.getOrderCommodityTagList = admin + 'orderTag/getOrderCommodityTagList';
api.releaseContainerItem = admin + 'standardManage/releaseContainerItem';
api.lastReceiptPrice = admin + 'purchaseOrder/lastReceiptPrice';

api.splitTemplateList = admin + 'orderSuper/splitTemplateList';
api.addSplitTemplate = admin + 'orderSuper/addSplitTemplate';
api.editSplitTemplate = admin + 'orderSuper/editSplitTemplate';
api.delSplitTemplate = admin + 'orderSuper/delSplitTemplate';
api.splitTemplateData = admin + 'orderSuper/splitTemplateData';
api.setUserTemplate = admin + 'orderSuper/setUserTemplate';
api.getUserTemplate = admin + 'orderSuper/getUserTemplate';
api.editSplitOrderMode = admin + 'orderSuper/editSplitOrderMode';
api.getSplitRuleList = admin + 'performance/getSplitRuleList';

api.getCustomHrefList = admin + 'appCenter/getCustomHrefList';
api.saveCustomHref = admin + 'appCenter/saveCustomHref';
api.delCustomHref = admin + 'appCenter/delCustomHref';

api.exportPoint = admin + 'userSuper/exportPoint';
api.getCommodityTaxRateProvider =
  admin + 'commoditySuper/GetCommodityTaxRateProvider';
api.quickCheck = admin + 'inventory/quickCheck';
api.unbindProtocol = admin + 'userSuper/unbindProtocol';
api.bindProtocol = admin + 'userSuper/bindProtocol';
api.commodityPackageImportTemplate = admin + 'commodityPackage/importTemplate';
api.getCommodityTaxUser = admin + 'commoditySuper/getCommodityTaxUser';

// 用友U8C相关
api.accreditU8C = admin + 'externalFinance/yyu8cAuth'; //授权
api.getAccreditU8C = admin + 'externalFinance/yyu8cAuthStatus'; //获取授权信息
api.cancelAccreditU8C = admin + 'externalFinance/yyu8cInit'; //取消授权
api.getFixedFields = admin + 'externalFinance/getFixedField'; //获取固定字段
api.saveFixedFields = admin + 'externalFinance/saveFixedField'; //保存固定字段

// 期初账款
api.getBeginAccountList = admin + 'OpeningAccountsPayable/list'; //获取期初账款列表
api.transferToBeginAccount = admin + 'OpeningAccountsPayable/turn'; //期初账款转入
api.importBeginTemplate = admin + 'OpeningAccountsPayable/importTemplate'; //导入期初账款模板
api.importBeginAccount = admin + 'OpeningAccountsPayable/import'; //导入期初账款
api.editBeginAccount = admin + 'OpeningAccountsPayable/edit'; //编辑期初账款

api.recipeImportTemplate = admin + 'recipe/importTemplate';
api.getCommodityRecordDetail =
  admin + 'wholesaleMarket/GetCommodityRecordDetail';

api.getTempCommodityList = admin + 'commoditySuper/getTempCommodityList';
api.saveTempCommodity = admin + 'commoditySuper/saveTempCommodity';
api.delTempCommodity = admin + 'commoditySuper/delTempCommodity';
api.orderSnapshot = admin + 'orderSuper/orderSnapshot';
api.submitOrderDirectPurchaseItemsExport =
  admin + 'orderSuper/submitOrderDirectPurchaseItemsExport';
api.exportOrderDirectPurchaseInfo =
  admin + 'orderSuper/exportOrderDirectPurchaseInfo';
api.purchaseCommodityList = admin + 'purchaseOrder/purchaseCommodityList';
api.getOrderChangeRemindList = admin + 'orderSuper/getOrderChangeRemindList';
api.saveSingleRecord = admin + 'purchaseOrder/saveSingleRecord';

api.assignSearchConfig = admin + 'orderSuper/assignSearchConfig';
api.operatorNumber = admin + 'adminSuper/operatorNumber';
api.savePurchaseRemark = admin + 'purchaseOrder/SaveRemark';
api.commodityExportExcel = admin + 'commoditySuper/exportExcel';
api.auditCommodity = admin + 'commoditySuper/auditCommodity'

export { api };
export default api;
