import { api } from './api.js';
import { get, post } from './request.js';
import jifenIcon from '@assets/images/icon/jifen.png';
import manjianIcon from '@assets/images/icon/manjian.png';
import zengpinIcon from '@assets/images/icon/zengpin.png';
import manzengIcon from '@assets/images/icon/manzeng.png';
import qianggouIcon from '@assets/images/icon/qianggou.png';
import jiajiagou from '@assets/images/icon/jiajiagou.png';
import jiajiapin from '@assets/images/icon/jiajiapin.png';
import tejia from '@assets/images/icon/tejia.png';
const ez = window.ez;

let goods = {};

goods.const = {
  ONLINE: 'Y', // 上架
  OFFLINE: 'N', // 下架
  SKU_CAN_SELL: 1, // 单位可售卖
  SKU_CAN_NOT_SELL: 0, // 单位不可售卖
};

goods.type = {
  credit: 6,
};

goods.getGoodsList = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  return post(api.getGoodsList, obj);
};

goods.searchCommodity = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  return get(api.searchCommodity, obj);
};

goods.getGoodsCategory = (p) => {
  let data = {};
  data.category_id = p || '';
  data.token = ez.getCookie('token');
  return get(api.getGoodsCategory, data);
};

// 新增分类
goods.addCategory = (p) => {
  return post(api.addCategory, p);
};

// 新增分类
// goods.addNewCategory = (p) => {
//   let data = {
//     name: p,
//     token: ez.getCookie('token')
//   };
//   return post(api.addMainCategory, data);
// }
//
// // 新增子分类
// goods.addSubCategory = (p1, p2) => {
//   let data = {
//     pid: p1,
//     name: p2,
//     token: ez.getCookie('token')
//   };
//   return post(api.addMainCategory, data);
// }

// 修改商品主类
goods.editCategory = (value) => {
  return post(api.editCategory, value);
};

// 修改商品子类
// goods.modifySubCategory = (value) => {
//   let data = {
//     pid: value.level,
//     id: value.id,
//     name: value.name,
//     sequence: value.sort,
//     token: ez.getCookie('token')
//   }
//
//   return post(api.modifyMainCategory, data);
// }

// 删除分类
// goods.delCategory = (p) => {
//   let data = {
//     category_id: p,
//     token: ez.getCookie('token')
//   }
//   return post(api.delCategory, data);
// }

// 获取采购方式
goods.getPurchaseType = (param, config) => {
  return get(api.getPurchaseType, param, config);
};

goods.getCommodityStorePurchase = (param) => {
  return get(api.getCommodityStorePurchase, param);
};

goods.saveCommodityStorePurchase = (param) => {
  return post(api.saveCommodityStorePurchase, param);
};

goods.getPurchaseArr = function (param) {
  return get(api.getPurchaseType, param);
};

/**
 * 商品上下架
 * @param { ids } 商品id
 * @param { is_online } 上下架状态
 */
goods.editOnline = ({ ids, is_online }) => {
  return post(api.changeShelvesStatus, {
    ids,
    is_online,
  });
};

// 修改上下架状态
goods.changeShelvesStatus = (p1, p2) => {
  let data = {
    ids: p1,
    is_online: p2,
    token: ez.getCookie('token'),
  };

  return post(api.changeShelvesStatus, data);
};
// 修改可售卖状态
goods.ModifyStock = (p1) => {
  let data = {
    commodity_id: p1,
    token: ez.getCookie('token'),
  };

  return post(api.ModifyStock, data);
};
goods.searchGoods = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  let data = {
    query: obj.query,
    token: ez.getCookie('token'),
  };

  return get(api.searchGoods, data);
};

goods.delGoods = (p1) => {
  let data = {
    ids: p1,
    token: ez.getCookie('token'),
  };
  return post(api.delGoods, data);
};

goods.checkIsExistStock = (p1) => {
  let data = {
    ids: p1,
    token: ez.getCookie('token'),
  };
  return get(api.checkIsExistStock, data);
};

goods.checkSkuIsExistStock = (p1) => {
  let data = {
    id: p1,
    token: ez.getCookie('token'),
  };
  return get(api.checkSkuIsExistStock, data);
};

// 获取商品分类树
goods.getGoodsCategoryTree = () => {
  return get(api.getGoodsCategoryTree, {});
};

// 删除商品分类
goods.delGoodsCategory = (p) => {
  let data = { id: p };
  return post(api.delCategory, data);
};

// 获取采购和供应商信息
goods.getPurchaseInfo = () => {
  return get(api.getPurchaseInfo, {});
};

// 保存商品信息
goods.saveGoods = (
  commodity_id,
  commodity,
  skuList,
  workingProcedureList,
  detail_img,
  picpath,
  deleteSku,
  video_path,
  tax_rate_list,
) => {
  let data = {
    tax_rate_list,
    commodity_id: commodity_id,
    commodity: JSON.stringify(commodity),
    skuList: JSON.stringify(skuList),
    workingProcedureList: JSON.stringify(workingProcedureList),
    detail_img: JSON.stringify(detail_img),
    picpath: JSON.stringify(picpath),
    token: ez.getCookie('token'),
    delete_sku: deleteSku,
    video_path: JSON.stringify([video_path]),
  };

  return post(api.saveGoods, data);
};
// 保存商品其他信息
goods.saveOthers = (
  commodity_id,
  commodity,
  workingProcedureList,
  providerList,
) => {
  let data = {
    commodity_id: commodity_id,
    commodity: JSON.stringify(commodity),
    working_procedure_list: JSON.stringify(workingProcedureList),
    spare_provider_list: JSON.stringify(providerList),
  };

  return post(api.saveCommodityOther, data);
};

goods.saveEditGoods = (p1, p2, p3) => {
  let data = {
    commodity: JSON.stringify(p1),
    skuList: JSON.stringify(p2),
    commodity_id: p3.cid,
    token: ez.getCookie('token'),
  };

  return post(api.saveGoods, data);
};

// 获取新增商品相关的信息
goods.getGoodsData = (params = {}) => {
  return get(api.getGoodsData, params);
};

// 商品批量刷价
goods.goodsBrushPrice = (params = {}) => {
  return post(api.brushPrice, params);
};

goods.getGoodsDetail = (p) => {
  let data = { commodity_id: p };
  return get(api.getGoodsDetail, data);
};

goods.getGoodsImg = (p) => {
  let data = { commodity_id: p };
  return get(api.getGoodsImg, data);
};

goods.getFormatDetail = (p) => {
  let data = { parent_id: p };
  return get(api.getFormatDetail, data);
};

goods.getGoodsUnit = () => {
  return get(api.getGoodsUnit, {});
};

goods.getGoodsTag = () => {
  return get(api.getGoodsTag, {});
};

goods.saveGoodsUnit = (p1, p2, p3) => {
  let data = {
    name: p1,
    id: p2,
    unit_code: p3,
    token: ez.getCookie('token'),
  };
  return post(api.saveGoodsUnit, data);
};

goods.saveGoodsTag = (p1, p2, p3) => {
  let data = {
    name: p1,
    id: p2,
    is_show: p3,
    token: ez.getCookie('token'),
  };
  return post(api.saveGoodsTag, data);
};

goods.delGoodsUnit = (p) => {
  let data = {
    id: p,
    token: ez.getCookie('token'),
  };
  return post(api.delGoodsUnit, data);
};

goods.delGoodsTag = (p) => {
  let data = {
    id: p,
    token: ez.getCookie('token'),
  };
  return post(api.delGoodsTag, data);
};

goods.getGoodsImgList = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  let data = {
    page: obj.page,
    pageSize: obj.pageSize,
    name: obj.name,
  };
  data = Object.assign(data, obj);
  return get(api.getGoodsImgList, data);
};
goods.preOneClickMatchCloudImg = (value) => {
  return post(api.preOneClickMatchCloudImg, value);
};

goods.autoCode = (p) => {
  let data = {
    name: p,
    token: ez.getCookie('token'),
  };
  return post(api.autoCode, data);
};

goods.getGoodsImg = (p) => {
  let data = { commodity_id: p };
  return get(api.getGoodsImg, data);
};

goods.delGoodsImg = (p1, p2) => {
  let data = {
    id: p1,
    commodity_id: p2,
    token: ez.getCookie('token'),
  };
  return post(api.delGoodsImg, data);
};

goods.getAllCommodity = (params) => {
  return get(api.getAllCommodity, params);
};

/**
 * 是否是协议价商品
 * @param goods
 * @returns {boolean}
 */
goods.isProtocolGoods = (goods) => {
  if (goods.new_price_type !== undefined) {
    return +goods.new_price_type === 1;
  }
  return parseInt(goods.is_price_type) === 1;
};

/**
 * 是否是客户折扣率商品
 * @param goods
 * @returns {boolean}
 */
goods.isDiscountGoods = (goods) => {
  return parseInt(goods.is_price_type) === 5;
};

/**
 * 是否是积分商品
 * @param goodsInfo
 * @returns {boolean}
 */
goods.isCreditGoods = (goodsInfo) => {
  return parseInt(goodsInfo.activity_type) === goods.type.credit;
};

// 是否为阶梯定价商品
goods.isStepPricingGoods = (goods) => {
  return (
    +goods.is_price_type === 3 &&
    goods.price_grads_list &&
    goods.price_grads_list.length &&
    +goods.is_time_promotion_price !== 1 // 非抢购商品
  );
};

/**
 * 获取满减满赠图标
 * @param goodsInfo
 * @returns {string}
 */
goods.getActivityIcon = (goodsInfo) => {
  let icon = '';
  let activityType = goodsInfo.activity_type_desc
    ? goodsInfo.activity_type_desc.trim()
    : '';
  switch (activityType) {
    case '积分':
      icon = jifenIcon;
      break;
    case '满减':
      icon = manjianIcon;
      break;
    case '满赠':
      icon = manzengIcon;
      break;
    case '赠品':
      icon = zengpinIcon;
      break;
    case '抢购':
      icon = qianggouIcon;
      break;
    case '加价品':
      icon = jiajiapin;
      break;
    case '加价购':
      icon = jiajiagou;
    case '特价':
      icon = tejia;
      break;
  }
  return icon;
};

goods.exportImgNameData = (params) => {
  return get(api.exportImgNameData, params);
};

// 获取自定义字段键值列表（受开关影响）
goods.getCustomizeFieldKeys = (params) => {
  return get(api.getCustomizeFieldKeys, params);
};
// 获取自定义字段键值列表（受开关影响）
goods.getCustomizeFieldList = (params) => {
  return get(api.getCustomizeFieldList, params);
};
// 添加自定义字段
goods.addCustomizeField = (params) => {
  return post(api.addCustomizeField, params);
};
// 删除自定义字段
goods.deleteCustomizeField = (params) => {
  return post(api.deleteCustomizeField, params);
};
// 修改自定义字段
goods.updateCustomizeField = (params) => {
  return post(api.updateCustomizeField, params);
};

goods.getOrderGoodsTagList = (params) => {
  return get(api.batchSummary.searchConfig, params).then((res) => {
    let data;
    if (res.status) data = res.data.order_commodity_tag_list || [];
    else data = [];
    return data;
  });
};

goods.saveUnitSort = (params) => {
  return post(api.saveUnitSort, params);
};

goods.checkHasExistBussinessData = (params) => {
  return get(api.checkHasExistBussinessData, params);
};
// 初始化商品最后一次下单时间
goods.initLastOrderTime = (params) => {
  return post(api.initLastOrderTime, params);
};

goods.getCommodityTaxRateProvider = (params) => {
  return get(api.getCommodityTaxRateProvider, params);
};

export default goods;
