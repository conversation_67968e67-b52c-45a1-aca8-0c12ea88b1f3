/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2021-02-28 18:49:26
 * @FilePath: \sdpbase-pro\src\api\weixin.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let weixin = {}

  /**
   * 获取公众号信息
   * @param params
   */
  weixin.getWechatInfo = (params) => {
    return get(api.getWechatInfo, params);
  };

  /**
   * 获取公众号信息
   * @param params
   */
  weixin.saveWechatInfo = (params) => {
    return post(api.saveWechatInfo, params);
  };

  /**
   * 获取公众号配置信息
   * @param params
   */
  weixin.getWechatConfig = (params) => {
    return get(api.getWechatConfigInfo, params);
  };

  /**
   * 保存公众号配置信息
   * @param params
   */
  weixin.saveWechatConfig = (params) => {
    return post(api.saveWechatConfig, params);
  };

  /**
   * 获取公众号菜单
   * @param params
   */
  weixin.getWechatMenu = (params) => {
    return get(api.getWechatMenu, params);
  };

  /**
   * 保存公众号菜单
   * @param params
   */
  weixin.saveWechatMenu = (params) => {
    return post(api.saveWechatMenu, params);
  };

  /**
   * 生成公众号菜单
   * @param params
   */
  weixin.genWechatMenu = (params) => {
    return post(api.genWechatMenu, params);
  };

  /**
   * 保存公众号菜单
   * @param params
   */
  weixin.genWechatMenu = (params) => {
    return post(api.genWechatMenu, params);
  };

  /**
   * 取消公众号授权
   * @param params
   */
  weixin.cancelWechatAuthorize = (params) => {
    return get(api.cancelWechatAuthorize, params);
  };

export default weixin;
