/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2021-02-28 18:45:41
 * @FilePath: \sdpbase-pro\src\api\distribution.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let delivery = {};

  /**
   * @description 获取配送信息
   * <AUTHOR> chen
   * @param params 请求参数
   */
  delivery.getDeliveryMapList = (params) => {
    return get(api.getDeliveryMapList, params);
  };

  delivery.getTrace = (params) => {
    return get(api.getTrace, params);
  };


export default delivery;
