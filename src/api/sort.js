import { api } from './api.js';
import { get, post } from './request.js';

let sort = {};

  // eslint-disable-next-line no-undef
  sort.errorCode = {
    user_no_area: 1000, // 客户未设置区域
    user_area_deleted: 1001, // 客户区域被删除
    area_no_match_line: 1002 // 客户对应的区域未分配到线路
  };
  // eslint-disable-next-line no-undef
  sort.sortStatus = {
    sorted: '1', // 已分拣
    sort: '0' // 待分拣
  };

  /**
   * 分拣员任务分配方式
   * @type {{}}
   */
  // eslint-disable-next-line no-undef
  sort.taskMode = {
    categoriesAndCustomerType: '7', // 分类以及客户类型
    commoditiesAndCustomers: '5', // 单品以及客户
    categoriesAndCustomers: '4', // 分类以及客户
    user: '3', // 按客户分配
    goods: '2', // 按商品分配
    category: '1' // 按分类分配
  };

  // 分拣可以打印小票
  // eslint-disable-next-line no-undef
  sort.canPrintTickt = '1';
  // eslint-disable-next-line no-undef
  sort.amountWarningDelay = 5000; // 阈值告警延迟关闭时间

  /**
   * 汇总拣货打印数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.summaryPick = params => {
    return get(api.summaryPick, params);
  };

  /**
   * //获取分拣商品
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortOrderCommodity = params => {
    return get(api.getSortOrderCommodity, params);
  };

  /**
   * 获取摘果打印数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getUserOrderCommodity = params => {
    return get(api.getUserOrderCommodity, params);
  };

sort.exportUserSort = params => {
  return post(api.exportUserSort, params);
};

  /**
   * 获取分拣搜索配置
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortSearchConfig = params => {
    return post(api.getSortSearchConfig, params);
  };
  //保存生产日期
  // eslint-disable-next-line no-undef
  sort.submitDate = params => {
    return post(api.submitDate, params);
  };
  // eslint-disable-next-line no-undef
  sort.checkAmountWarning = (orderAmount, amount, warningRate, commodity) => {
    if (!orderAmount || !warningRate || !amount) {
      return true;
    }
    if (commodity && commodity.unit_convert == 'Y') {
      orderAmount = orderAmount * commodity.unit_num;
    }
    let dAmount = ((amount) / orderAmount) * 100;
    if (+amount > (orderAmount + (orderAmount * warningRate / 100))) {
      return '您设置的数量已超过阈值';
    }
    if (+amount < (orderAmount - (orderAmount * warningRate / 100))) {
      return '您设置的数量已低于阈值';
    }
    if (
      Math.abs(((amount) / orderAmount) * 100).toFixed(2) -
        warningRate !==
      0
    ) {
      if (Math.abs(((amount) / orderAmount) * 100).toFixed(2) - warningRate > 0) {
        return '您设置的数量已超过阈值';
      } else {
        return '您设置的数量已低于阈值';
      }
    }
    return true;
  };

  /**
   * 保存分拣
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.saveSortInfo = params => {
    return post(api.saveSortInfo, params);
  };

  /**
   * 保存打印历史
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.savePrintInfo = params => {
    return post(api.saveSortPrintInfo, params);
  };

  /**
   * 获取打印历史
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortPrintHistory = params => {
    return post(api.getSortPrintHistory, params);
  };

  /**
   * 保存多次分拣
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.saveSortMany = params => {
    return post(api.saveSortMany, params);
  };

  /**
   * 重置分拣
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.resetSort = params => {
    return post(api.resetSort, params);
  };

  /**
   * 获取分拣进度
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortProgress = params => {
    return get(api.sortProcess, params);
  };

  /**
   * 获取分拣记录
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortRecord = params => {
    return get(api.sortRecord, params);
  };

  /**
   * 一键打印检测
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.batchPrintCheck = params => {
    return get(api.sortBatchPrintCheck, params);
  };

  /**
   * 检测用户分拣一键打印
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.checkUserOneTouchPrint = params => {
    return post(api.checkUserOneTouchPrint, params);
  };

  /**
   * 一键打印
   */
  // eslint-disable-next-line no-undef
  sort.onlyOneTouchPrint = params => {
    return get(api.onlyOneTouchPrint, params);
  };

  /**
   * 一键打印获取商品id
   */
  // eslint-disable-next-line no-undef
  sort.userOneTouchPrint = params => {
    return post(api.userOneTouchPrint, params);
  };
  /**
   * 获取商品分拣记录
   */
  // eslint-disable-next-line no-undef
  sort.getSortRecordsList = params => {
    return get(api.getSortRecordsList, params);
  };
  /**
   * 一键分拣打印
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.batchPrint = params => {
    return post(api.sortBatchPrint, params);
  };
  /**
   * 拣货单打印数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getPickPrintData = params => {
    return post(api.getPickPrintData, params);
  };

  /**
   * 倒出汇总表
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.exportSortSum = params => {
    return get(api.exportSortSum, params);
  };

  /**
   * 获取单品列表查看详情
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortOrderCommodityStatDetail = params => {
    return get(api.getSortOrderCommodityStatDetail, params);
  };

  /**
   * 获取门店列表查看详情
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getUserOrderCommodityDetail = params => {
    return get(api.getUserOrderCommodityDetail, params);
  };

  /**
   * 获取分拣员
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSorter = params => {
    return get(api.getSorter, params);
  };

  /**
   * 获取分拣历史数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortHistory = params => {
    return get(api.getSortHistory, params);
  };

  /**
   * 获取分拣历史详情数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSortHistoryDetail = params => {
    return get(api.getSortHistoryDetail, params);
  };

  /**
   * 获取新增修改分拣员页面数据
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSorterConfig = params => {
    return get(api.getSorterConfig, params);
  };

  /**
   * 获取分拣员信息
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSorterInfo = params => {
    return get(api.getSorterDetail, params);
  };

  /**
   * 获取分拣员信息
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.getSorterGoods = params => {
    return get(api.getSorterGoods, params);
  };

  /**
   * 保存分拣员信息
   * @param params
   */
  // eslint-disable-next-line no-undef
  sort.saveSorterInfo = params => {
    let apiUrl = params.id ? api.saveSorter : api.addSorter;
    return post(apiUrl, params);
  };

  /**
   * 生成获取分拣总条数任务
   * @param params 当前筛选条件
   */
  // eslint-disable-next-line no-undef
  sort.genSortCount = params => {
    return post(api.genSortCount, params);
  };

  /**
   * 根据任务获取当前分拣总条数
   * @param taskNo 任务号
   */
  // eslint-disable-next-line no-undef
  sort.getSortCount = taskNo => {
    let params = {
      task_no: taskNo,
      type: 'sort'
    };
    return get(api.listCount, params);
  };
  /**
   * 标记 缺货 / 部分缺货
   * @param {commodity_id} 订单商品id, 是否部分缺货(传1表示标记部分缺货)
   * @param {is_part} 1: 标记部分缺货， 2： 标记缺货
   */
  sort.markStockOut = params => {
    return post(api.markStockOut, params);
  };
  /**
   * 删除缺货标记
   * @param {commodity_id} 订单商品id
   */
  sort.cancelMarkStockOut = params => {
    return post(api.cancelMarkStockOut, params);
  };
  /**
   * 检测待生成采购订单的数据
   */
  sort.checkPurchaseData = params => {
    return post(api.checkPurchaseData, params);
  };
  /**
   * 生成采购单
   */
  sort.createPurchaseOrder = params => {
    return post(api.createPurchaseOrder, params);
  };
  /**
   * 缺货明细/汇总导出
   */
  sort.stockOutExport = params => {
    return get(api.stockOutExport, params);
  };
  /**
   * @description: 获取单独打印mulit_pick 为false时
   */
  sort.getPrintData = params => {
    return get(api.getPrintData, params);
  };
  /**
   * @description: 获取多次打印mulit_pick 为true时
   */
  sort.getManyPrintData = params => {
    return get(api.getManyPrintData, params);
  };
  // eslint-disable-next-line no-undef
// eslint-disable-next-line no-undef

  /**
   * @description: 分拣员账号禁用/恢复
   * @author: lizi
   */
  sort.sorterDisable = params => {
    return post(api.sorterDisable, params)
  }
export default sort;
