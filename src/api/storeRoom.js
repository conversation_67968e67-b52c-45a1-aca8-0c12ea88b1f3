import { api } from './api.js';
import { get, post } from './request.js';
import goods from './goods';

let ware = {};

ware.getWarehouseList = (params) => {
  return get(api.getWarehouseList, params);
};

ware.addWarehouse = (params) => {
  return post(api.addWarehouse, params);
};

ware.deleteWarehouse = (params) => {
  return post(api.deleteWarehouse, params);
};

ware.updateWarehouse = (params) => {
  return post(api.updateWarehouse, params);
};

ware.getReservoirList = (params) => {
  // 页面初始化时还未获取取仓库id时，取消多余请求
  if (!params.store_id) {
    return Promise.resolve({ status: 0 });
  }
  return new Promise((resolve, reject) => {
    get(api.getReservoirList, params).then((res) => {
      const { status, data } = res;
      if (status) {
        res.data = data.map((item) => {
          return {
            label: item.name,
            value: item.id,
            children: item.locations.map((subitem) => {
              return {
                label: subitem.location_name,
                value: subitem.id,
              };
            }),
          };
        });
        return resolve(res);
      } else {
        return reject(res);
      }
    });
  });
};

ware.getStorageAreaList = (params) => {
  return get(api.getStorageAreaList, params);
};

ware.getUserWarehouseArea = (params) => {
  return get(api.getUserWarehouseArea, params);
};

ware.getStorageList = (params) => {
  return get(api.getStorageList, params);
};

ware.addStorageArea = (params) => {
  return post(api.addStorageArea, params);
};

ware.updateStorageArea = (params) => {
  return post(api.updateStorageArea, params);
};

ware.addStorage = (params) => {
  return post(api.addStorage, params);
};

ware.updateStorage = (params) => {
  return post(api.updateStorage, params);
};

ware.deleteStorageArea = (params) => {
  return post(api.deleteStorageArea, params);
};

ware.batchGeneration = (params) => {
  return post(api.batchGeneration, params);
};

ware.addBatchGenerationLocation = (params) => {
  return post(api.addBatchGenerationLocation, params);
};

ware.deleteStorage = (params) => {
  return post(api.deleteStorage, params);
};

ware.getAreaMap = () => {
  let params = { token: ez.getCookie('token') };
  return post(api.getAreaMap, params);
};

ware.unsetRelation = (params) => {
  return post(api.unsetRelation, params);
};

ware.addAreaRelations = (params) => {
  return post(api.addAreaRelations, params);
};

ware.getDeliveryData = (params) => {
  return get(api.getDeliveryData, params);
};

ware.userDeliveryDetail = (params) => {
  return get(api.userDeliveryDetail, params);
};

ware.getStockDiffData = (params) => {
  return get(api.getStockDiffData, params);
};

ware.getOrderDeliveryDetail = function (params) {
  return get(api.getOrderDeliveryDetail, params);
};

ware.sendOut = (params) => {
  return post(api.sendOut, params);
};

ware.sendOutByOrder = (params) => {
  return post(api.sendOutByOrder, params);
};

ware.checkDeliveryData = (params) => {
  return post(api.checkDeliveryData, params);
};
ware.checkOrderDeliveryData = (params) => {
  return post(api.checkOrderDeliveryData, params);
};

ware.createStockIn = (params) => {
  return post(api.createStockIn, params);
};

ware.getStockOutPrintData = (params) => {
  return get(api.getStockOutPrintData, params);
};
ware.getStockPrintData = (params) => {
  return get(api.getStockPrintData, params);
};
ware.getStockInDetail = (params) => {
  return get(api.getStockInDetail, params);
};
ware.auditConfirmIn = (params) => {
  return post(api.auditConfirmIn, params);
};

ware.updateAudit = (params) => {
  return post(api.updateAudit, params);
};

ware.deleteGodownEntry = (params) => {
  return get(api.deleteGodownEntry, params);
};

ware.SΩExistingStock = (params) => {
  return get(api.getExistingStock, params);
};

ware.getBatchStock = (params) => {
  return get(api.getBatchStock, params);
};

ware.setThreshold = (params) => {
  return get(api.setThreshold, params);
};

ware.getInBoundAudit = (params) => {
  return get(api.getInBoundAudit, params);
};

ware.StockOutManagementList = (params) => {
  return get(api.StockOutManagementList, params);
};

ware.StockOutQueryList = (params) => {
  return get(api.StockOutQueryList, params);
};

ware.createStockOut = (params) => {
  return post(api.createStockOut, params);
};

ware.getOutBoundAudit = (params) => {
  return get(api.getOutBoundAudit, params);
};

ware.auditConfirmOut = (params) => {
  return post(api.auditConfirmOut, params);
};

ware.deleteStockOutOrder = (params) => {
  return get(api.deleteStockOutOrder, params);
};

ware.auditOrder = (params) => {
  return post(api.auditOrder, params);
};

ware.updateOutAudit = (params) => {
  return post(api.updateOutAudit, params);
};

ware.getCheckOrderDetail = (params) => {
  return get(api.getCheckOrderDetail, params);
};

ware.createInventoryCheckOrder = (params) => {
  return post(api.createInventoryCheckOrder, params);
};

ware.confirmCheck = (params) => {
  return post(api.confirmCheck, params);
};

ware.confirmCheckSave = (params) => {
  return post(api.confirmCheckSave, params);
};

ware.delCheckOrder = (params) => {
  return post(api.delCheckOrder, params);
};

ware.getReportLPorderPage = (params) => {
  return get(api.getReportLPorderPage, params);
};

ware.delReportLPOrder = (params) => {
  return post(api.delReportLPOrder, params);
};

/**
 * 批量转换
 * @param params
 */
ware.batchConvert = (params) => {
  return post(api.batchConvert, params);
};

ware.createReportLpOrder = (params) => {
  return post(api.createReportLpOrder, params);
};

ware.getReportLPOrderDetail = (params) => {
  return get(api.getReportLPOrderDetail, params);
};

ware.reportLPOrderAudit = (params) => {
  return post(api.reportLPOrderAudit, params);
};

ware.saveLPOrderAudit = (params) => {
  return post(api.saveLPOrderAudit, params);
};
ware.lossOverAllOver = (params) => {
  return post(api.lossOverAllOver, params);
};

ware.getCostChangeOrderList = (params) => {
  return get(api.getCostChangeOrderList, params);
};

ware.createCostChangeOrder = (params) => {
  return post(api.createCostChangeOrder, params);
};

ware.getCostChangeOrderDetail = (params) => {
  return get(api.getCostChangeOrderDetail, params);
};

ware.auditCostChangeOrder = (params) => {
  return post(api.auditCostChangeOrder, params);
};

ware.updateCostChangeOrder = (params) => {
  return post(api.updateCostChangeOrder, params);
};

ware.createOrderTransfer = (params) => {
  return post(api.createOrderTransfer, params);
};

ware.getTransferOrderDetail = (params) => {
  return get(api.getTransferOrderDetail, params);
};

ware.getAuditTransferOrder = (params) => {
  return get(api.getAuditTransferOrder, params);
};

ware.doAuditTransferOrder = (params) => {
  return post(api.doAuditTransferOrder, params);
};

ware.updateAuditTransferOrder = (params) => {
  return post(api.updateAuditTransferOrder, params);
};

ware.deleteTransferOrder = (params) => {
  return get(api.deleteTransferOrder, params);
};

ware.commodityLocationTransfer = (params) => {
  return post(api.commodityLocationTransfer, params);
};

// ware.storeroomTransferList = params => {
//   return get(api.storeroomTransferListk, params);
// }

ware.deleteStoreroomTransfer = (params) => {
  return get(api.deleteStoreroomTransfer, params);
};

ware.getStoreroomTransferDetail = (params) => {
  return get(api.getStoreroomTransferDetail, params);
};

ware.auditStoreroomTransfer = (params) => {
  return post(api.auditStoreroomTransfer, params);
};

ware.saveStoreroomTransfer = (params) => {
  return post(api.saveStoreroomTransfer, params);
};

ware.getLocationCommodityTotal = (params) => {
  return get(api.getLocationCommodityTotal, params);
};

ware.getLocationCommodityList = (params) => {
  return get(api.getLocationCommodityList, params);
};

ware.getLocationCommodityDetail = (params) => {
  return get(api.getLocationCommodityDetail, params);
};

ware.getCostLog = (params) => {
  return get(api.getCostLog, params);
};

ware.getInOutHistoryList = (params) => {
  return get(api.inOutHistoryList, params);
};

ware.getStockOutDetail = (params) => {
  return get(api.getStockOutDetail, params);
};

ware.inOutHistoryDetailList = (params) => {
  return get(api.inOutHistoryDetailList, params);
};
/**
 * 判断是否是多仓库
 * @param params
 */
ware.checkIsMultiStorage = (params) => {
  return get(api.checkIsMultiStorage, params);
};

ware.getLocationMaintenanceList = (params) => {
  return get(api.getLocationMaintenanceList, params);
};

ware.updateLocation = (params) => {
  return get(api.updateLocation, params);
};

ware.searchLocationName = (params) => {
  return get(api.searchLocationName, params);
};

ware.getLocationList = (params) => {
  return get(api.getLocationList, params);
};

ware.editStockInNum = (params) => {
  return post(api.editStockInNum, params);
};

ware.downloadExcelTemplate = (params) => {
  return get(api.downloadExcelTemplate, params);
};

ware.addOpeningStock = (params) => {
  return post(api.addOpeningStock, params);
};

ware.getUserWarehouse = (params, config) => {
  return get(api.getUserWarehouse, params, config);
};

ware.exportCheckTemplate = (params) => {
  return get(api.exportCheckTemplate, params);
};

ware.getUnitChangeData = (params) => {
  return get(api.getUnitChangeData, params);
};

ware.unitChange = (params) => {
  return post(api.unitChange, params);
};

ware.exportStockDiffData = (params) => {
  return get(api.exportStockDiffData, params);
};

ware.deleteCostChangeOrder = (params) => {
  return get(api.deleteCostChangeOrder, params);
};

ware.exportStock = (params) => {
  return get(api.exportStock, params);
};

ware.exportBatchStock = (params) => {
  return get(api.exportBatchStock, params);
};

ware.inOutHistoryListExcel = (params) => {
  return get(api.inOutHistoryListExcel, params);
};

ware.exportOrderDetail = (params) => {
  return get(api.exportOrderDetail, params);
};

ware.getGoodsCategory = (params) => {
  return get(api.getGoodsCategory, params || {});
};

ware.exportOpeningTemplate = (params) => {
  return get(api.exportOpeningTemplate, params);
};

ware.earlyRepertory = (params) => {
  return get(api.earlyRepertory, params);
};

ware.exportStockIn = (params) => {
  return get(api.exportStockIn, params);
};

ware.InOutHistoryDetailListExcel = (params) => {
  return get(api.InOutHistoryDetailListExcel, params);
};

ware.getCommodityInfo = (params) => {
  return get(api.getStoreCommodityInfo, params);
};

ware.ExportStockOutSearch = (params) => {
  return get(api.ExportStockOutSearch, params);
};

ware.ajaxStoreList = (value) => {
  let obj = {};
  value ? (obj = value) : undefined;

  // 设置默认值
  obj.page = obj.page || 1;
  obj.pageSize = obj.pageSize || 10;

  obj.token = ez.getCookie('token');

  return get(api.ajaxStoreList, obj);
};

ware.StockOutDetailExcel = (params) => {
  return get(api.StockOutDetailExcel, params);
};

ware.getBaseCommoditys = (params) => {
  return get(api.getBaseCommoditys, params);
};
/**
 * @description: 分割记录页面
 */
// 获取状态筛选项配置
ware.getPartitionRecordSearchConfig = (params) => {
  return get(api.partitionRecord.searchConfig, params);
};
// 审核
ware.partitionRecordAudit = (params) => {
  return post(api.partitionRecord.audit, params);
};
// 取消
ware.partitionRecordCancel = (params) => {
  return post(api.partitionRecord.cancel, params);
};
// 导出
ware.partitionRecordExport = (params) => {
  return post(api.partitionRecord.export, params);
};

/**
 * @description: 反审核检测
 * @param {Object} params 请求参数
 * @return {Promise}
 * @author: lizi
 */
ware.antiAuditBtnCheck = (params) => {
  return post(api.antiAuditBtnCheck, params);
};

/**
 * @description: 反审核
 * @param {Object} params 请求参数
 * @return {Promise}
 * @author: lizi
 */
ware.antiAudit = (params) => {
  return post(api.antiAudit, params);
};

ware.setWarehouseLocation = (params) => {
  return post(api.setWarehouseLocation, params);
};

export default ware;
