import { api } from './api.js'
import { get, post } from './request.js'

let report = {};

  //获取毛利列表数据
  report.getProfitListReport = (params) => {
    return get(api.getProfitListReport, params);
  };
  // 导出毛利详情数据
  report.exportProfitListReport = params => {
    return get(api.exportProfitListReport, params)
  }
  //获取毛利统计数据
  report.getProfitChartReport = (params) => {
    return get(api.getProfitChartReport, params);
  };
  // 获取毛利统计详情
  report.getProfitDetail = (params) => {
    return get(api.getProfitDetail, params);
  };
  // 获取商品销量列表
  report.getCommoditySaleList = (params) => {
    return get(api.getCommoditySaleList, params);
  };
  // 导出商品销量
  report.exportCommoditySale = (params) => {
    return get(api.exportCommoditySale, params);
  };
  // 离线导出商品销量
  report.reportOffLineExportCommoditySale = (params) => {
    return get(api.reportOffLineExportCommoditySale, params);
  };
  // 导出毛利列表
  report.exportProfitList = (params) => {
    return get(api.exportProfitList, params);
  };
  // 导出商品毛利列表
  report.exportCommodityProfitList = (params) => {
    return get(api.exportCommodityProfitList, params);
  };
  // 获取商品销量饼图数据
  report.getGoodsChartData = (params) => {
    return get(api.getGoodsChartData, params);
  };
  // 获取营业数据
  report.getBusinessList = (params) => {
    return get(api.getBusinessList, params);
  };
  // 获取营业数据详情
  report.getBusinessDetail = (params) => {
    return get(api.getBusinessDetail, params);
  }

  report.commoditySalesUserList = (params) => {
    return get(api.commoditySalesUserList, params);
  }
  //获取销售价格波动表
  report.getSalePriceList = (params) => {
    return get(api.getSalePriceList, params);
  }
  //获取采购价格波动表
  report.getPurchasePriceList = (params) => {
    return get(api.getPurchasePriceList, params);
  }
  //获取价格波动图表
  report.getCommodityPriceDetail = (params) => {
    return get(api.getCommodityPriceDetail, params);
  }
  //获取价格波动图表
  report.getInventoryReport = (params) => {
    return get(api.getInventoryReport, params);
  }
  // 获取库房盘点列表
  report.getStoreLossList = (params) => {
    return get(api.getStoreLossList, params);
  }
  // 获取库房盘点详情
  report.getStoreLossDetail = (params) => {
    return get(api.getStoreLossDetail, params);
  }
  // 损耗趋势
  report.storeLossSummary = (params) => {
    return get(api.storeLossSummary, params);
  }
  // 采购损耗，按商品
  report.purchaseCommodityLoss = (params) => {
    return get(api.purchaseCommodityLoss, params);
  }
  // 采购损耗，按供应商或采购员
  report.purchaseProviderLoss = (params) => {
    return get(api.purchaseProviderLoss, params);
  }
  // 采购损耗-按商品详情
  report.purchaseComDetail = (params) => {
    return get(api.purchaseComDetail, params);
  }
  // 采购损耗-按供应商或采购员-详情
  report.purchaseProviderDetail = (params) => {
    return get(api.purchaseProviderDetail, params);
  }
  // 退货损耗-列表
  report.ReturnLoss = (params) => {
    return get(api.ReturnLoss, params);
  }
  // 退货损耗-详情
  report.ReturnLossDetail = (params) => {
    return get(api.ReturnLossDetail, params);
  }
  // 进销存报表-明细
  report.InventoryReportDetail = (params) => {
    return get(api.InventoryReportDetail, params);
  }
  // 进销存报表-批次-明细
  report.InventoryReportBatchDetail = (params) => {
    return get(api.InventoryReportBatchDetail, params)
  }
  // 预估毛利顶部合计
  report.estimateProfitTotal = (params) => {
    return get(api.estimateProfitTotal, params)
  }
  // 预估毛利导出
  report.estimateProfitExport = (params) => {
    return get(api.estimateProfitExport, params);
  };
  // 客户采购统计导出任务
  report.purchaseStatisticExport = (params) => {
    return get(api.purchaseStatisticExport, params);
  };
  // 客户采购统计合计
  report.purchaseStatisticSum = (params) => {
    return get(api.purchaseStatisticSum, params);
  };
export default report;
