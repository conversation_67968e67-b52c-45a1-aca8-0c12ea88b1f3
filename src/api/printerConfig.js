/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizhiwei😊
 * @LastEditTime: 2022-03-30 17:35:31
 * @FilePath: \sdpbase-pro\src\api\power.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let printerConfig = {};


  printerConfig.getTplGroup = (params) => {
    return get(api.getTplGroup, params);
  };
  printerConfig.saveGroup = (params) => {
    return post(api.saveGroup, params);
  }

export default printerConfig;
