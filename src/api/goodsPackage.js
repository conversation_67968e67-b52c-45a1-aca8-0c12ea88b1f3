import { api } from './api.js'
import { get, post } from './request.js'

const getOrderDetail = params => get(api.goodsPackageOrder.detail, params);
const addOrder = params => post(api.goodsPackageOrder.add, params);
const editOrder = params => post(api.goodsPackageOrder.edit, params);
const closeOrder = params => post(api.goodsPackageOrder.close, params);
const syncOrder = params => post(api.goodsPackageOrder.sync, params);
const getOrderSearchConfig = params => get(api.goodsPackageOrder.searchConfig, params);
export const getPackageList = params => get(api.goodsPackage.list, params);

export default {
  getOrderSearchConfig,
  getOrderDetail,
  addOrder,
  editOrder,
  closeOrder,
  syncOrder
}
