/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-11-25 10:36:28
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-11 17:49:12
 * @FilePath: /sdpbase-pro/src/api/agreementPrice.js
 */
import { api } from "./api.js";
import { get, post } from "./request.js";

let agreementPrice = {};

agreementPrice.addAgreementPrice = params => {
  return post(api.addAgreementPrice, params);
};

agreementPrice.updateAgreementPrice = params => {
  return post(api.updateAgreementPrice, params);
};

agreementPrice.getExpireList = params => {
  return post(api.getExpireAgreementPrice, params);
};

agreementPrice.getAgreementPrice = params => {
  return get(api.getAgreementPrice, params);
};

agreementPrice.getAgreementPriceList = params => {
  return post(api.getAgreementPriceList, params);
};

agreementPrice.getUserAgreementPriceList = params => {
  return post(api.getUserAgreementPriceList, params);
};

agreementPrice.closeAgreementPrice = params => {
  return get(api.closeAgreementPrice, params);
};

agreementPrice.getStatus = params => {
  return get(api.agreementPriceStatus, params);
};

agreementPrice.deleteAgreementPrice = params => {
  return get(api.deleteAgreementPrice, params);
};

agreementPrice.checkAgreementPrice = params => {
  return get(api.checkAgreementPrice, params);
};

agreementPrice.exportOrder = params => {
  return get(api.exportAgreementPrice, params);
};

agreementPrice.exportTpl = params => {
  return get(api.exportAgreementPriceTpl, params);
};

agreementPrice.syncBillPrice = params => {
  return post(api.syncBillPrice, params);
};

export default agreementPrice;
