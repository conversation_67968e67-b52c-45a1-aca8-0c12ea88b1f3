// 拓客crm相关的api
import { post } from './request.js'
import jsCookie from 'js-cookie';

const tcHost = 'https://tc-tenant-dev.movee.cn/api';

export let api = {
  saveTenantUser: tcHost + '/sys/base/saveTenantUser',
  getTenantUser: tcHost + '/sys/base/getTenantUser',
  getEnterpriseOrganizeTree: tcHost + '/sys/base/getEnterpriseOrganizeTree',
  delTenantUser: tcHost + '/sys/base/delTenantUser',
  getTenantUserResourceTree: tcHost + '/sys/base/getTenantUserResourceTree'
}

const getHeaderParams = function() {
  return {
    'Content-Type': 'application/json',
    Token: localStorage.getItem('admin_token'),
    Ccode: jsCookie.get('ccode') || location.host.split('.')[0]
  }
}
export default {
  saveTenantUser(params) {
    return post(api.saveTenantUser, params, getHeaderParams())
  },
  getTenantUser(params) {
    return post(api.getTenantUser, params, getHeaderParams())
  },
  getEnterpriseOrganizeTree(params) {
    return post(api.getEnterpriseOrganizeTree, params, getHeaderParams())
  },
  delTenantUser(params) {
    return post(api.delTenantUser, params, getHeaderParams())
  },
  getTenantUserResourceTree(params) {
    return post(api.getTenantUserResourceTree, params, getHeaderParams())
  },
}
