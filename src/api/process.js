/*
 * @Author: l<PERSON><PERSON><PERSON> 🙂
 * @Date: 2021-06-10 16:45:03
 * @LastEditTime: 2021-11-09 15:35:44
 */
import { api } from './api.js'
import { get, post } from './request.js'
export default {
  getProcessPrintData: (params) => {
    return get(api.getProcessPrintData, params);
  }
};
// 获取预完工数据
export const getPreCompletionInfo = async (params) => {
  return await get(api.process.processOrder.preCompletion, params).catch(e => {
    console.log(e)
  })
}
// 食品完工入库
export const savePreCompletionInfo = async (params) => {
  return await post(api.process.processOrder.confirmFoodCompletion, params).catch(e => {
    console.log(e)
  })
}
// 食品完工入库
export const bomCalLastInPrice = async (params) => {
  return await post(api.process.goodsBoom.bomCalLastInPrice, params).catch(e => {
    console.log(e)
  })
}


