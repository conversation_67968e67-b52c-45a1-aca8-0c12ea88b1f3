/* eslint-disable no-undef */
import { api } from './api.js'
import { get, post } from './request.js'

let behalfOrder = {};



  // 获取订单商品详情
  behalfOrder.getOrderCommodity = function (p1, p2) {
    var data = { user_id: p1, commodity_id: p2, token: ez.getCookie('token') };
    return get(api.getBehalfOrderCommodity, data);
  }

  // 完成订单
  behalfOrder.finishOrder = function (p) {
    var data = { order_id: p, token: ez.getCookie('token') };
    return post(api.finishBehalf, data);
  }



  // 创建订单
  behalfOrder.createOrder = function () {
    // 处理传参格式
    var arr = arguments[1],
      commodityArr = [],
      obj = {};

    for (var i = 0, length = arr.length; i < length; i++) {
      obj = {
        commodity_id: arr[i].id,
        amount: arr[i].amount,
        actual_amount: arr[i].actual_amount,
        price: arr[i].price,
        remark: arr[i].remark
      };
      commodityArr.push(obj);
    }
    commodityArr = JSON.stringify(commodityArr);
    // 非必须参数设置为空
    arguments[2] = arguments[2] || '';
    arguments[3] = arguments[3] || '';

    let data = {
      user_id: arguments[0],
      commodity_list: commodityArr,
      delivery_date: arguments[2],
      remark: arguments[3],
      mode: arguments[4],
      is_pay: arguments[5],
      source: arguments[6],
      token: ez.getCookie('token')
    };
    if (arguments[7]) {
      data.order_no = arguments[7]
    }
    return post(api.createBehalfOrder, data);
  }

  behalfOrder.getOrderList = function (value) {
    var obj = {};
    value ? obj = value : undefined;

    // 设置默认值
    obj.page = obj.page || 1;
    obj.pageSize = obj.pageSize || 10;

    var data = {
      page: obj.page,
      pageSize: obj.pageSize,
      searchValue: obj.searchValue,
      payStatus: obj.payStatus,
      modeStatus: obj.modeStatus,
      startTime: obj.startTime,
      endTime: obj.endTime,
      token: ez.getCookie('token')
    };

    return get(api.getBehalfOrderList, data);
  }

  behalfOrder.getOrderData = function (value) {
    var obj = {};
    value ? obj = value : undefined;

    var data = {
      searchValue: obj.searchValue,
      payStatus: obj.payStatus,
      startTime: obj.startTime,
      endTime: obj.endTime,
      token: ez.getCookie('token')
    }
    return get(api.getOrderData, data);
  }

  behalfOrder.getOrderDetail = function () {
    var data = {
      order_id: arguments[0],
      order_no: arguments[1],
      token: ez.getCookie('token')
    };
    return get(api.getBehalfOrderDetail, data);
  }

  behalfOrder.getReturnInfo = function (p) {
    var data = {
      order_id: p,
      token: ez.getCookie('token')
    };
    return get(api.getReturnInfo, data);
  }

  // 获取历史订单
  behalfOrder.getHistoryOrder = function (p) {
    var data = {
      user_id: p.id,
      page: p.page,
      pageSize: p.pageSize,
      token: ez.getCookie('token')
    };
    return get(api.getHistoryOrder, data);
  }

  // 获取历史订单商品列表
  behalfOrder.getBehalfHistoryGoodsList = function (p1, p2) {
    var data = {
      user_id: p1,
      order_id: p2,
      token: ez.getCookie('token')
    };
    return get(api.getBehalfHistoryGoodsList, data);
  }

  // 获取用于修改的订单详情
  behalfOrder.getModifyOrderDetail = function (p) {
    var data = {
      order_id: p,
      token: ez.getCookie('token')
    };
    return get(api.getModityBehalfOrder, data);
  };

  // 保存修改后的订单
  behalfOrder.saveModifyOrder = function () {
    var old = arguments[0],
      add = arguments[1],
      del = arguments[2],
      orderId = arguments[3],
      date = arguments[4],
      remarks = arguments[5],
      mode = arguments[6],
      is_pay = arguments[7],
      source = arguments[8],
      oldItemsObj = {},
      newItemsObj = {},
      itemsArr = [];

    if (old && !!old.length) {
      for (var i = 0, length = old.length; i < length; i++) {
        oldItemsObj.commodity_id = old[i].id;
        oldItemsObj.order_commodity_id = old[i].orderCommodity_id;
        oldItemsObj.remark = old[i].remark;
        oldItemsObj.amount = old[i].order_amount;
        oldItemsObj.actual_amount = old[i].amount;
        oldItemsObj.price = old[i].unit_price;
        itemsArr.push(oldItemsObj);
        oldItemsObj = {};
      }
    }
    if (add && !!add.length) {
      for (var k = 0, newLength = add.length; k < newLength; k++) {
        newItemsObj.commodity_id = add[k].id;
        newItemsObj.remark = add[k].remark;
        newItemsObj.amount = add[k].order_amount;
        newItemsObj.actual_amount = add[k].amount;
        newItemsObj.price = add[k].unit_price;
        itemsArr.push(newItemsObj);
        newItemsObj = {};
      }
    }

    var newObj = {
      items: itemsArr,
      delItems: del,
      delivery_date: date,
      remark: remarks,
      orderId: orderId,
      mode, is_pay, source,
      token: ez.getCookie('token')
    };

    let data = { data: JSON.stringify(newObj) }

    return post(api.saveModityBehalfOrder, data);
  };

  // 删除订单
  behalfOrder.delOrder = function (p) {
    var data = {
      order_no: p,
      token: ez.getCookie('token')
    };
    return get(api.delBehalfOrder, data);
  };

  // 创建订单退款
  behalfOrder.createOrderReturn = function (value) {
    var data = {
      order_id: value.orderId,
      goods_list: JSON.stringify(value.goodsList),
      return_type: value.type,
      token: ez.getCookie('token')
    };

    return post(api.orderReturn, data);
  };

  // 订单按照条件导出
  behalfOrder.exportConditionData = function (data) {
    return get(api.exportConditionBehalfData, data);
  };

  // 获取订单汇总列表
  behalfOrder.getOrderSummaryList = function (param) {
    return get(api.getOrderSummaryList, param)
  };

  // 获得需要采购商商品的订单列表
  behalfOrder.getCommodityOrderList = function (param) {
    return get(api.getCommodityOrderList, param)
  };

  // 生成采购单检查
  behalfOrder.checkPurchaseOrder = function (param) {
    return post(api.checkPurchaseOrder, param)
  };

  //生成采购单
  behalfOrder.genPurchaseOrder = function (param) {
    return post(api.genPurchaseOrder, param)
  };
  // 订单汇总导出
  behalfOrder.orderPoolExport = function (param) {
    return get(api.orderPoolExport, param)
  };

  behalfOrder.getBehalfOrderStatus = function (param) {
    return get(api.getBehalfOrderStatus, param)
  };
  /**
   * @description 核算
   * <AUTHOR>
   * @param data 核算数据
   */
  behalfOrder.approval = (data) => {
    data.commodity = JSON.stringify(data.commodity);
    return post(api.approval, data);
  }

export default behalfOrder;
