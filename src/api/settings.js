import store from '../vuex';
import { api } from './api.js';
import { get, post } from './request.js';

let settings = {};

settings.getSystemConfig = () => {
  return get(api.getSystemConfig, {});
};

settings.saveSystemConfig = (p,c) => {
  let configMap = {};
  const storeSysConfig = store.state.sysConfig;
  p.map(item => {
    configMap[item.key] = item.value;
  });
  if (storeSysConfig) {
    configMap = {
      ...storeSysConfig,
      ...configMap
    }
  }
  let data = {
    config_list: JSON.stringify(p)
  };
  if(c){
    data.wechat_mini_list =JSON.stringify(c)
  }
  return new Promise((resolve, reject) => {
    post(api.saveSystemConfig, data)
      .then(res => {
        store.commit('setSysConfig', configMap);
        resolve(res);
      })
      .catch(reject);
  });
};

settings.postregisterSave = params => {
  return post(api.postregisterSave, params);
};

settings.getAccountCard = params => {
  return get(api.getAccountCard, params);
};

settings.EditSysConfig = params => {
  return post(api.EditSysConfig, params);
};

settings.getsysConfigRegisterConfig = () => {
  return get(api.getsysConfigRegisterConfig);
};

settings.commodityAttrConfig = () => {
  return get(api.commodityAttrConfig);
};

settings.attrConfigSave = (params) => {
  return post(api.attrConfigSave, params);
};

settings.getIsMallApp = () => {
  return get(api.getIsMallApp, {});
};

settings.getDownloadApp = () => {
  return get(api.downloadApp);
};

settings.getOperatingTimeList = () => {
  return get(api.getOperatingTimeList);
};

settings.addOperatingTime = params => {
  return post(api.addOperatingTime, params);
};
settings.updateOperatingTime = params => {
  return post(api.updateOperatingTime, params);
};
settings.deleteOperatingTime = params => {
  return post(api.deleteOperatingTime, params);
};

settings.getCompanyInfo = params => {
  return post(api.getCompanyInfo, params);
};

settings.getLoginCompanyInfo = params => {
  return post(api.getLoginCompanyInfo, params);
};

settings.saveCompanyInfo = params => {
  return post(api.saveCompanyInfo, params);
};
settings.initSystemData = params => {
  return post(api.initSystemData, params);
};

settings.getYunpianConfig = params => {
  return post(api.getYunpianConfig, params);
};

settings.saveYunpianConfig = params => {
  return post(api.saveYunpianConfig, params);
};

settings.clearCloudLog = params => {
  return get(api.clearCloudLog, params);
};

settings.showTables = () => {
  return get(api.showTables);
};

settings.saveTables = params => {
  return post(api.saveTables, params);
};

//短信充值
settings.GetAmountInfo = () => {
  return get(api.GetAmountInfo);
};

settings.SetSmsStatus = params => {
  return post(api.SetSmsStatus, params);
};

settings.RechargeMoney = params => {
  return post(api.RechargeMoney, params);
};

settings.SendTestSms = params => {
  return post(api.SendTestSms, params);
};

settings.GetSendRecords = params => {
  return get(api.GetSendRecords, params);
};

settings.GetWxPayStatus = params => {
  return get(api.GetWxPayStatus, params);
};

settings.GetRechargeRecords = params => {
  return get(api.GetRechargeRecords, params);
};

/**
 * 短信充值 / 签名
 */
settings.GetAutograph = () => {
  return get(api.GetAutograph);
};
settings.SetInitAutograph = params => {
  return post(api.setInitAutograph, params);
};
settings.updateSign = params => {
  return post(api.updateSign, params);
}

/**
 * 获取打印模版类型
 * @param params
 */
settings.getPrintTemplateType = params => {
  return get(api.getPrintTemplateType, params);
};

settings.getPrintTemplate = params => {
  return new Promise((resolve, reject) => {
    get(api.getTpl, params)
      .then(res => {
        let { status, data } = res;
        if (status === 'error') {
          reject(res);
        } else if (status) {
          const templateData = data.INCLU_ITEM;
          templateData.tpl_data = JSON.parse(templateData.tpl_data);
          resolve(templateData);
        } else {
          reject();
        }
      })
      .catch(reject);
  });
};

/**
 * @description: 查询供应商打印模板
 */
settings.getTplRelationMap = params => {
  return get(api.getTplRelationMap, params);
};
/**
 * @description: 保存供应商打印模板
 */
settings.saveTplRelation = params => {
  return post(api.saveTplRelation, params);
};
/**
 * 判断是否是新版打印模版
 */
settings.isNewTemplate = template => {
  if (!template) {
    return false;
  }
  if (Number(template.version) === 2) {
    return true;
  }
  return false;
};

/**
 * 获取版本列表
 * @param  params
 */
settings.getVersionList = params => {
  return get(api.getVersionList, params);
};

settings.saveVersion = params => {
  return post(api.saveVersion, params);
};

/**
 * 消息配置
 */
/**
 * 获取公众号二维码
 * @param {*} params
 */
settings.getNoticeQr = params => {
  return get(api.getNoticeQr, params);
};
/**
 * 获取消息节点列表
 * @param {*} params
 */
settings.getNoticeNodeList = params => {
  return get(api.getNoticeNodeList, params);
};
/**
 * 保存消息节点启用开关
 * @param {*} params
 */
settings.saveNoticeNodeEnable = params => {
  return post(api.saveNoticeSaveNodeEnable, params);
};
/**
 * 获取消息节点详情
 * @param {*} params
 */
settings.getNoticeNodeDesc = params => {
  return get(api.getNoticeNodeDesc, params);
};
/**
 * 保存消息节点详情
 * @param {*} params
 */
settings.saveNoticeNodeDesc = params => {
  return post(api.saveNoticeNodeDesc, params);
};

// 支付方式页面
settings.getPayWayList = params => {
  return get(api.getPayWayList, params);
};
settings.postPayWayAdd = params => {
  return post(api.postPayWayAdd, params);
};
settings.setPayWayDisable = params => {
  return post(api.setPayWayDisable, params);
};
settings.setPayWayEnable = params => {
  return post(api.setPayWayEnable, params);
};
settings.deletePayWay = params => {
  return post(api.deletePayWay, params);
};

/**
 * @description: 获取供应商/采购员短信配置
 * @param {*}
 * @return {*}
 * @author: lizi
 */
settings.getProviderVsBuyerNodeList = params => {
  return get(api.getProviderVsBuyerNodeList, params)
}

/**
 * @description: 保存供应商/采购员短信配置
 * @param {*}
 * @return {*}
 * @author: lizi
 */
settings.saveProviderVsBuyerNodeEnable = params => {
  return post(api.saveProviderVsBuyerNodeEnable, params)
}

settings.smsBatchSend = params =>{
  return post(api.smsBatchSend, params)
}

settings.cancelSend = params =>{
  return post(api.cancelSend, params)
}

settings.saleSalaryConfig = params =>{
  return get(api.saleSalaryConfig, params)
}

settings.saveSaleSalaryConfig = params =>{
  return post(api.saveSaleSalaryConfig, params)
}

settings.delSaleSalaryConfig = params =>{
  return post(api.delSaleSalaryConfig, params)
}

settings.getSignatureList = params =>{
  return get(api.getSignatureList, params)
}

settings.getConfigLastEditInfo = params =>{
  return get(api.getConfigLastEditInfo, params)
}

settings.authorizationSignature = params =>{
  return post(api.authorizationSignature, params)
}

settings.authorizationSignatureV2 = params =>{
  return post(api.authorizationSignatureV2, params)
}

settings.smsRemoveSign = params =>{
  return post(api.smsRemoveSign, params)
}

settings.addTemplate = params =>{
  return post(api.addTemplate, params)
}

settings.deleteTemplate = params =>{
  return post(api.deleteTemplate, params)
}

export default settings;
