/*
 * @Description: 配送相关api
 * @Autor: lizi
 * @Date: 2020-04-20 10:33:31
 * @LastEditors: lizi
 * @LastEditTime: 2022-04-18 10:11:12
 * @FilePath: \sdpbase-pro\src\api\delivery.js
 */
import {api} from './api.js'
import {get,post} from './request.js'

let delivery = {
  getCarList (params) { 
    return get(api.getCarList, params);
  },
  /**
   * @description: 保存车辆信息
   * @param {Object} params 请求参数
   * @return {Promise} 返回promise对象
   * @author: lizi
   */
  saveCar(params) {
    return post(api.saveCar, params)
  },
  delCar(params) {
    return get(api.delCar, params)
  },
  getCarDetail (params) { 
    return get(api.getCarDetail, params)
  },
  getLineList(params){
    return get(api.delivery.line.list, params)
  },
  saveBatchDecision(params){
    return post(api.delivery.setLine.batchDecision, params)
  },
  batchSetOrderLine(params){
    return post(api.delivery.setLine.batchSetOrderLine, params)
  },
  batchSetUserOrderLine(params){
    return post(api.delivery.setLine.batchSetUserOrderLine, params)
  }
};
export default delivery;
