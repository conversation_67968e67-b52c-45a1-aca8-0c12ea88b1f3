/**
 * @description 绑定事件 on(element, event, handler)
 */
export const on = (function () {
  if (document.addEventListener) {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false)
      }
    }
  } else {
    return function (element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler)
      }
    }
  }
})();

/**
 * @description 解绑事件 off(element, event, handler)
 */
export const off = (function () {
  if (document.removeEventListener) {
    return function (element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false)
      }
    }
  } else {
    return function (element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler)
      }
    }
  }
})();

export const onScrollBottom = (element, callback, offset = 0) => {
  on(element, 'scroll', (event) => {
    let scrollHeight = element.scrollHeight;
    let scrollTop = element.scrollTop;
    let offsetHeight = element.offsetHeight;
    console.log('scrollHeight', scrollHeight)
    console.log('scrollTop', scrollTop)
    console.log('offsetHeight', offsetHeight)
    console.log('offset', offset)
    // 滚动到底部 | 滚动到离底部 offset
    if (scrollHeight - scrollTop - offsetHeight - offset === 0) {
      callback && callback();
    }
  });
};
