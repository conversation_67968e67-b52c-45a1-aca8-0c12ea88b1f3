/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2021-02-20 10:29:17
 * @LastEditors: lizi
 * @LastEditTime: 2022-04-19 10:33:20
 * @FilePath: \sdpbase-pro\src\api\basic.js
 */
import { api } from './api.js'
import { get, post } from './request.js'

let basic = {};


  /**
   * @description 获取基础数据
   * <AUTHOR> chen
   * @param params 请求参数
   */
  basic.login = (params, headerParams) => {
    return post(api.login, params, headerParams);
  };
  basic.getCode = (params) => {
    return get(api.getCode, params);
  };
  basic.logOut = (params) => {
    return post(api.logOut, params);
  };
  basic.getLoginCompanyInfo = (params) => {
    return get(api.getLoginCompanyInfo, params);
  };
  basic.CheckRemoteAssistanceCode = (params) => {
    return post(api.CheckRemoteAssistanceCode, params);
  };
  // 获取所有库房 已与后端确认没有此接口
  // basic.getStores = (params) => {
  //   return get(api.getStores, params);
  // };

  // 获取导出字段
  basic.getFields = (params) => {
    return get(api.getFields, params);
  };
  // 更新导出字段
  basic.updateFields = (params) => {
    return get(api.updateFields, params);
  };
  // 恢复默认导出字段
  basic.initCustomExportColumn = function (param) {
    return get(api.initCustomExportColumn, param)
  };
  //获取首页基础数据
  basic.getIndexData = function (param) {
    return get(api.getIndexData, param)
  };
  //获取首页图标数据
  basic.getChartData = function (param) {
    return get(api.getChartData, param)
  };
  //获取云商品库token
  basic.cloudToken = () => {
    return get(api.cloudToken);
  };

  basic.SendVerifyCode = params => {
    return post(api.SendVerifyCode, params)
  }

  basic.BindMobile = params =>{
    return post(api.BindMobile, params)
  }

  basic.ResetPassword = params => {
    return post(api.ResetPassword, params)
  }
  basic.getUserByName = params => {
    return get(api.getUserByName, params)
  }
export default basic;
