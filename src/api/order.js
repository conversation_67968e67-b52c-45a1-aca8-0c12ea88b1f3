import { api } from './api.js';
import { get, post } from './request.js';

const admin = '/superAdmin/';

const receiptOrderUrl = admin + 'OrderSuper/receiptOrder';
const batchEditUrl = admin + 'orderSuper/batchEdit';
const orderDesignateCombineUrl = admin + 'orderSuper/orderDesignateCombine';
const getOcrUploadCount = admin + 'orderSuper/ocrUploadCount';
const ocrCount = admin + 'orderSuper/AiForOcr';
const recipeSnapshot = admin + 'orderSuper/recipeSnapshot';

// 订单刷价相关api
const batchBrushPriceUrl = admin + 'OrderSuper/BatchBrushPrice'; //批量刷价
const getBrushHistory = admin + 'orderSuper/getBrushOrderPriceLog'; //获取刷价历史
const getBrushNo = admin + 'orderSuper/getBrushNo'; //获取批量刷价批次号
const getBrushOrderLog =
  admin + 'orderSuper/getBrushOrderPriceLog_1699436616921'; //获取刷价日志
const recoverBrushPrice = admin + 'OrderSuper/recoverOrderBrushPrice'; //恢复刷价
const getRecoverFailList = admin + 'orderSuper/getRecoverFailOrderLog'; //获取恢复失败列表

let order = {};
/**
 * 客户生效的协议单检查
 * @param user_id
 */
order.checkValidUserContractPriceOrder = (user_id, delivery_date) => {
  let params = {
    user_id,
    delivery_date,
  };
  return get(api.userContractPrice.checkEffectiveOrder, params);
};

// 通过搜索获取相关的商品列表
order.getCommodityBySearch = function (p1, p2, p3) {
  var data = {
    query: p1,
    token: ez.getCookie('token'),
  };
  p2 ? (data.choose_active = p2) : undefined;
  p3 ? (data.choose_parent = p3) : undefined;

  return get(api.getCommodityBySearch, data);
};

// 通过搜索获取相关的商品列表
order.getCommodity = function (param) {
  return get(api.getCommodity, param);
};

// 通过搜索获取客户数据
order.getUserBySearch = function (p, consortium_search_type) {
  var data = {
    user: p,
    token: ez.getCookie('token'),
  };
  if (consortium_search_type) {
    data.consortium_search_type = consortium_search_type;
  }
  // if(!p){
  //   data.pageSize=1000
  // }
  return get(api.getUserInfo, data);
};

// 获取完整的用户信息，手机号等数据不会脱敏
order.getUserFull = function (p, consortium_search_type) {
  var data = {
    user: p,
    token: ez.getCookie('token'),
  };
  if (consortium_search_type) {
    data.consortium_search_type = consortium_search_type;
  }
  return get('/superAdmin/userApi/getUser', data);
};

// 获取订单商品详情
order.getOrderCommodity = function (p1, p2, delivery_date, extraParams = {}) {
  console.log(extraParams);
  var data = {
    user_id: p1,
    commodity_id: p2,
    token: ez.getCookie('token'),
    delivery_date,
    ...extraParams,
  };
  return get(api.getOrderCommodity, data);
};

// 完成订单
order.finishOrder = function (p) {
  var data = {
    order_id: p,
    token: ez.getCookie('token'),
  };
  return post(api.finish, data);
};

// 删除订单
order.delOrder = function (p) {
  var data = {
    order_no: p,
    token: ez.getCookie('token'),
  };
  return get(api.delOrder, data);
};

order.createOrderV2 = function (params) {
  return post(api.createOrder, params);
};

// 创建订单
order.createOrder = function () {
  // 处理传参格式
  var arr = arguments[1],
    commodityArr = [],
    obj = {};

  for (var i = 0, length = arr.length; i < length; i++) {
    obj = {
      commodity_id: arr[i].id,
      amount: arr[i].amount,
      remark: arr[i].remark,
      price: arr[i].price,
      in_price: arr[i].in_price,
      org_price: arr[i].org_price,
      discount: arr[i].discount,
    };
    commodityArr.push(obj);
  }
  commodityArr = JSON.stringify(commodityArr);
  // 非必须参数设置为空
  arguments[2] = arguments[2] || '';
  arguments[3] = arguments[3] || '';
  arguments[4] = arguments[4] || '';

  let data = {
    user_id: arguments[0],
    commodity_list: commodityArr,
    delivery_date: arguments[2],
    remark: arguments[3],
    delivery_time: arguments[4],
    token: ez.getCookie('token'),
  };

  return post(api.createOrder, data);
};
order.getSysConfig = function () {
  let data = {
    token: ez.getCookie('token'),
  };
  return get(api.getSystemConfig, data);
};
// 创建现场采购订单
order.addSceneOrder = function () {
  // 处理传参格式
  var arr = arguments[1],
    commodityArr = [],
    obj = {};
  for (var i = 0, length = arr.length; i < length; i++) {
    obj = {
      commodity_id: arr[i].id,
      num: arr[i].amount,
      purchase_unit_price: arr[i].purchase_price,
      order_unit_price: arr[i].price,
      remark: arr[i].remark,
    };
    commodityArr.push(obj);
  }
  commodityArr = JSON.stringify(commodityArr);
  // 非必须参数设置为空
  arguments[2] = arguments[2] || '';
  arguments[3] = arguments[3] || '';

  let data = {
    user_id: arguments[0],
    commodity_string: commodityArr,
    delivery_date: arguments[2],
    remark: arguments[3],
    purchase_agent_id: arguments[4],
    purchase_provider_id: arguments[5],
    token: ez.getCookie('token'),
  };
  return post(api.addSceneOrder, data);
};
order.saveSceneOrder = (data) => {
  return post(api.addSceneOrder, data);
};
// 获取发货时间段
order.getDeliveryTimeList = function (params) {
  var data = {
    ...params,
    token: ez.getCookie('token'),
  };
  return get(api.getDeliveryTime, data);
};
//获取采购员
order.getPurchaseAgent = function (params) {
  let data = {
    token: ez.getCookie('token'),
    ...params,
  };
  return get(api.getPurchaseAgent, data);
};
//获取供应商
order.getProvider = function (params) {
  let data = {
    token: ez.getCookie('token'),
    page: 1,
    pageSize: 200,
    ...params,
  };
  return get(api.getProvider, data);
};
// 获取采购方式
order.getPurchaseType = (param) => {
  return get(api.getPurchaseType, param);
};
order.getOrderList = function (params) {
  return get(api.getOrderList, params);
};
// 获取异常订单接口
order.getAbnormalList = function (params) {
  return get(api.getAbnormalList, params);
};
// 获取异常订单详情
order.getAbnormalOrderDetail = function (params) {
  return get(api.getAbnormalOrderDetail, params);
};
order.getOrderData = function (value) {
  var obj = {};
  value ? (obj = value) : undefined;

  var data = {
    searchValue: obj.searchValue,
    payStatus: obj.payStatus,
    startTime: obj.startTime,
    endTime: obj.endTime,
    token: ez.getCookie('token'),
  };
  return get(api.getOrderData, data);
};

order.getOrderDetail = function () {
  var data = {
    order_id: arguments[0],
    order_no: arguments[1],
    is_edit: arguments[2],
    is_reverse_audit: arguments[3] || '0',
    token: ez.getCookie('token'),
  };
  return get(api.getOrderDetail, data);
};

order.getReturnInfo = function (p) {
  var data = {
    order_id: p,
    token: ez.getCookie('token'),
  };
  return get(api.getReturnInfo, data);
};

// 获取历史订单
order.getHistoryOrder = function (p) {
  var data = {
    user_id: p.id,
    page: p.page,
    pageSize: p.pageSize,
    token: ez.getCookie('token'),
  };
  return get(api.getHistoryOrder, data);
};

// 获取历史订单商品列表
order.getHistoryGoodsList = function (p1, p2) {
  var data = {
    user_id: p1,
    order_id: p2,
    token: ez.getCookie('token'),
  };
  return get(api.getHistoryGoodsList, data);
};

// 获取用于修改的订单详情
order.getModifyOrderDetail = function (p, fromCopy = false, extraParams = {}) {
  var data = {
    order_id: p,
    token: ez.getCookie('token'),
    ...extraParams
  };
  if (fromCopy) {
    data.is_copy = 1;
  }
  return get(api.getModityOrder, data);
};

/**
 * @param params {items}
 * @param params {delItems}
 * @param params {delivery_date}
 * @param params {delivery_time}
 * @param params {remark}
 * @param params {orderId}
 */
order.saveModifyOrderV2 = (params, extra = {}) => {
  let data = {
    data: JSON.stringify(params),
    ...extra,
  };
  return post(api.saveModityOrder, data);
};

// 保存修改后的订单
order.saveModifyOrder = function () {
  var old = arguments[0],
    add = arguments[1],
    del = arguments[2],
    orderId = arguments[3],
    date = arguments[4],
    remarks = arguments[5],
    delivery_time = arguments[6],
    oldItemsObj = {},
    newItemsObj = {},
    itemsArr = [];

  if (old && !!old.length) {
    for (var i = 0, length = old.length; i < length; i++) {
      oldItemsObj.commodity_id = old[i].id;
      oldItemsObj.order_commodity_id = old[i].orderCommodity_id;
      oldItemsObj.remark = old[i].remark;
      oldItemsObj.amount = old[i].order_amount;
      itemsArr.push(oldItemsObj);
      oldItemsObj = {};
    }
  }
  if (add && !!add.length) {
    for (var k = 0, newLength = add.length; k < newLength; k++) {
      newItemsObj.commodity_id = add[k].id;
      newItemsObj.remark = add[k].remark;
      newItemsObj.amount = add[k].order_amount;
      itemsArr.push(newItemsObj);
      newItemsObj = {};
    }
  }

  itemsArr = itemsArr.map((item, index) => {
    item.sort_num = index;
    return item;
  });
  var newObj = {
    items: itemsArr,
    delItems: del,
    delivery_date: date,
    delivery_time: Number(delivery_time),
    remark: remarks,
    orderId: orderId,
    token: ez.getCookie('token'),
  };
  let data = {
    data: JSON.stringify(newObj),
  };

  return post(api.saveModityOrder, data);
};

// 删除订单
order.delOrder = function (p) {
  var data = {
    order_no: p,
    token: ez.getCookie('token'),
  };
  return get(api.delOrder, data);
};

// 创建订单退款
order.createOrderReturn = function (value) {
  var data = {
    order_id: value.orderId,
    goods_list: JSON.stringify(value.goodsList),
    return_type: value.type,
    token: ez.getCookie('token'),
  };

  return post(api.orderReturn, data);
};

// 订单按照条件导出
order.exportConditionData = function (data) {
  return get(api.exportConditionData, data);
};

// 获取订单汇总列表
order.getOrderSummaryList = function (param) {
  return get(api.getOrderSummaryList, param);
};

// 获得需要采购商商品的订单列表
order.getCommodityOrderList = function (param) {
  return get(api.getCommodityOrderList, param);
};

// 生成采购单检查
order.checkPurchaseOrder = function (param) {
  return post(api.checkPurchaseOrder, param);
};

//生成采购单
order.genPurchaseOrder = function (param) {
  return post(api.genPurchaseOrder, param);
};
// 订单汇总导出
order.orderPoolExport = function (param) {
  return get(api.orderPoolExport, param);
};
//批量处理异常（导入订单）
order.batchAbnormal = function (param) {
  return get(api.batchAbnormal, param);
};
order.batchComplete = function (param) {
  param.type = 'complete';
  return post(api.batchComplete, param);
};
order.batchClose = function (param) {
  param.type = 'close';
  return post(api.batchClose, param);
};
//
order.batchAudit = function (param) {
  return get(api.batchAudit, param);
};
//获取订单列表搜索配置
order.getSearchConfig = function (param) {
  return get(api.getSearchConfig, param);
};

// 获取子账号列表
order.getsubaccountList = function (param) {
  return get(api.getsubaccountList, param);
};
// 添加子账号
order.addSubaccount = function (param) {
  return post(api.addSubaccount, param);
};
// 删除子账号
order.deleteSubaccount = function (param) {
  return post(api.deleteSubaccount, param);
};
// 编辑子账号
order.updateSubaccount = function (param) {
  return post(api.updateSubaccount, param);
};

//批量导入模板下载
order.batchImportTemplate = function (param) {
  return post(api.batchImportTemplate, param);
};
/**
 * @description 核算
 * <AUTHOR>
 * @param data 核算数据
 */
order.approval = (data) => {
  data.commodity = JSON.stringify(data.commodity);
  return post(api.approval, data);
};
/**
 * @description 核算
 * <AUTHOR>
 * @param data 核算数据
 */
order.editApproval = (data) => {
  data.commodity = JSON.stringify(data.commodity);
  return post(api.editApproval, data);
};
/**
 * @description 追加修改
 * @param data 数据
 */
order.reverseAudit = (param) => {
  return post(api.reverseAudit, param);
};
/**
 * @description 智能定价
 */
//获取智能定价商品分类
order.getCommodityCategory = () => {
  return get(api.CommodityCategory);
};
//获取批量设置配置
order.getBatchSetConfig = function (param) {
  return get(api.batchSetConfig, param);
};
//获取智能定价列表数据
order.getIntelligentPriceList = function (param) {
  return get(api.getIntelligentPriceList, param);
};
//获取智能定价列表数据
order.getExecList = function (param) {
  return get(api.getExecList, param);
};
//根据商品id获取公式详情
order.getFormulaDetail = function (param) {
  return get(api.FormulaDetail, param);
};
//批量设置公式
order.batchSetFormula = function (param) {
  return post(api.batchSetFormula, param);
};
//获取智能定价历史
order.getPriceHistory = function (param) {
  return get(api.getPriceHistory, param);
};
// 同步商品价格
order.syncCommodity = function (param) {
  return post(api.syncCommodity, param);
};
//同步订单价格
order.syncOrderPrice = function (param) {
  return post(api.syncOrderPrice, param);
};
// 重试同步
order.retrySync = function (param) {
  return post(api.retrySync, param);
};
//更新商品最近一次进价
order.editCommodityInPrice = function (param) {
  return post(api.editCommodityInPrice, param);
};
//获取定价历史详情
order.priceHistoryDetail = function (param) {
  return post(api.priceHistoryDetail, param);
};
// 同步预设公式
order.syncDefaultFormula = function (param) {
  return post(api.syncDefaultFormula, param);
};
//  // 获取预设公式详情
// order.getFormulaDetail = function(param) {
//   return get(api.FormulaDetail, param);
// }
// order.setBatchSetFormula = function(param) {
//   return post(api.batchSetFormula, param);
// }
//获取退货单列表
order.getOrderReturnList = function (param) {
  return get(api.getOrderReturnList, param);
};
//获取历史退货单列表
order.getOrderHistoryList = function (param) {
  return get(api.getOrderHistoryList, param);
};
//获取历史退货单详情
order.getOrderHistoryDetail = function (param) {
  return get(api.getOrderHistoryDetail, param);
};
//新增退货单
order.addNewOrderReturn = function (param) {
  return post(api.addNewOrderReturn, param);
};
order.editOrderReturn = function (param) {
  return post(api.editOrderReturn, param);
};
//退货单详情
order.getOrderReturnDetail = function (params) {
  return get(api.getOrderReturnDetail, params);
};
//退货单审核
order.orderReturnAudit = function (params) {
  return post(api.orderReturnAudit, params);
};
//退货单拒绝审核
order.orderRefuseAudit = function (params) {
  return post(api.orderRefuseAudit, params);
};
//退货单退款
order.orderConfirmRefund = function (params) {
  return post(api.orderConfirmRefund, params);
};
//退货单导出
order.orderReturnExport = function (params) {
  return get(api.orderReturnExport, params);
};
//退货单打印数据
order.getOrderReturnPrintData = function (params) {
  return get(api.getOrderReturnPrintData, params);
};
//操作历史
order.orderLog = function (params) {
  var data = {
    module: params.module,
    table_id: params.order_id,
    subject: params.subject,
    token: ez.getCookie('token'),
  };
  return get(api.orderLog, data);
};
order.orderLogDetail = function (params) {
  var data = {
    log_id: params.log_id,
    token: ez.getCookie('token'),
  };
  return get(api.orderLogDetail, data);
};
//图片识别
order.ocrRecognise = (params) => post(api.ocrRecognise, params);
order.getImgCommonditData = (params) => post(api.getImgCommonditData, params);

// 订单--添加标签
order.addOrderTag = function (name) {
  return post(api.addOrderTag, {
    name,
  });
};

// 订单--查询标签列表
order.qryOrderTagList = function (params) {
  return get(api.qryOrderTagList, params);
};

// 订单--修改标签
order.editOrderTag = function (name, id) {
  return post(api.editOrderTag, {
    name,
    id,
  });
};

// 订单--删除标签
order.delOrderTag = function (id) {
  return post(api.delOrderTag, {
    id,
  });
};

// 订单--查询标签状态
order.getOrderTag = function () {
  return post(api.getOrderTag);
};

// 订单--切换标签状态
order.openOrderTag = function (value) {
  return post(api.openOrderTag, {
    value,
  });
};

// 订单--保存订单标签
order.saveOrderTags = function (order_id, tag_ids) {
  return post(api.saveOrderTags, {
    order_id,
    tag_ids,
  });
};
// 订单关联快递100
order.AddExpressNo = function (params) {
  return get(api.AddExpressNo, params);
};
// 订单--获取客户状态
order.accountRemind = function (params) {
  return post(api.accountRemind, params);
};
order.IntelligentPriceExport = function (params) {
  return get(api.IntelligentPriceExport, params);
};
//订单合并检查
order.orderCombineCheck = function (params) {
  return post(api.orderCombineCheck, params);
};
//供应商绑定拆单标签
order.providerBindSplitOrderTag = function (params) {
  return post(api.providerBindSplitOrderTag, params);
};
//订单合并
order.orderCombine = function (params) {
  return post(api.orderCombine, params);
};
// 订单详情修改订单回单状态
order.receiptOrder = (params) => {
  return post(receiptOrderUrl, params);
};
// 批量修改订单标签/回单状态
order.batchEdit = (params) => {
  return get(batchEditUrl, params);
};
order.orderDesignateCombine = (params) => {
  return post(orderDesignateCombineUrl, params);
};
order.getOcrUploadCount = (params) => {
  return get(getOcrUploadCount, params);
};
order.ocrCount = (params) => {
  return get(ocrCount, params);
};
order.recipeSnapshot = (params) => {
  return get(recipeSnapshot, params);
};
// 刷价相关接口方法
order.getBrushNo = (params) => {
  return get(api.getBrushNo, params);
};
order.batchBrushPrice = async (params) => {
  return post(batchBrushPriceUrl, params);
};
// 获取可恢复的刷价id
order.getBrushRestorabilityIds = async (params) => {
  return get(api.getCanRecoverObplId, params);
};
// 恢复刷价
order.recoverBrushPrice = async (params) => {
  return post(api.recoverBrushPrice, params);
};

// 菜品复核
order.mealPlanAudit = (param) => {
  return post(api.mealPlanAudit, param);
};

// 就餐详情
order.mealPlanDetail = (param) => {
  return get(api.mealPlanDetail, param);
};
export default order;
