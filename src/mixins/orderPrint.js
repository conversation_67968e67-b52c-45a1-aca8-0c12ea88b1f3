import settings from '@api/settings';
import _webPrint, { setOrderPrintConfig, formatOrderData, formatCommodityClass , oldVersionPrint , exportExcel } from '@/util/print';
import { handleDataSourceByTemplate } from '@/util/print/@extraConfig';
import { num2RmbCaption } from '@/util/common';
import { handlePaginationData } from '@/util/print/order-pagination';
const { ORDER_TAG, CUSTOM_CATE,CUSTOM_MERGE_CATE } = require('@components/common/BatchMergePrint/constant.js').default

let webPrint = _webPrint


const flatMapCustomFieldLineType = (config) => {
  if(!config.customField || config.customField.length <=0 ) return [];
  // 多行的情况是个二维数组
  if (Array.isArray(config.customField[0])) {
    return config.customField.flatMap(item => item.flatMap(e => e.lineType || []));
  }
  return config.customField.flatMap(item => item.lineType || []);
}

export default {
  data() {
    return {
      exportLoading: false
    }
  },
  methods: {
    /**
     * 打印发货单
     * @param {string} id 订单id
     * @param {string} is_merge 是否合并
     * @param {string} is_bill
     */
    async _printOrder({
      id,
      is_merge = 1,
      template_id = '',
      type = 'sales',
      is_bill = 0,
      is_preview = true,
      record_print_times = true,
      way_print = '', // 打印方式, ORDER_TAG | CUSTOM_CATE
      is_export_excel = false,
      _templates,
      _data,
      printType,
      callback = () => {},
      getPrintDataCallBack = () => {},
      exportSheetMode,
      beforePrint,
      seqFields,
      rangeParams,
      is_order_split,
      sheetNameKey,
      sort_by,
      requestUrl = '/superAdmin/orderSuper/getPrintData'
    }) {
      if(+is_order_split === 1) {
        is_merge = 0
      }
      if (typeof beforePrint === 'function') {
        webPrint = async (printData) => {
          printData = await beforePrint(printData);
          if(printData){
            _webPrint(printData);
          }
        }
      }
      if (is_export_excel) {
        this.exportLoading = true;
      }
      if (type === 'behalf' || type === 'bills') {
        const params = { id, is_merge, template_id, is_preview }
        type === 'behalf' && this._printBehalfOrder(params); // 老单据管理打印
        type === 'bills' && this._printBillsOrder(params); // 新单据管理打印
        return false;
      }
      // 按集团打印需要先获取打印模板 再根据模板里是否配置客户名称 来传参
      let select_prt_tpl_id = template_id
      if(is_merge==2&&printType==3){
        if(select_prt_tpl_id){
        }else{
          let printTemplateData = await settings.getPrintTemplate({
            type: 'GROUP_ORDER',
            id:template_id
          })
          select_prt_tpl_id = printTemplateData.id
        }
      }
      // 拿到打印数据
      const data = await this.$_getPrintData({
        id, 
        is_bill, 
        is_merge, 
        record_print_times, 
        _data, 
        is_export_excel,
        printType,
        select_prt_tpl_id,
        seqFields,
        rangeParams, 
        is_order_split, 
        sort_by,
        requestUrl
      });
      // 拿到打印数据后, 更新打印次数, 刷新列表更新
      getPrintDataCallBack && typeof getPrintDataCallBack === 'function' && getPrintDataCallBack()

      // 处理order订单信息数据,以及把对应的商品信息从item添加到order.items中
      const orders = this._formatPrintData(data, is_merge,printType);
      if (!orders || !Array.isArray(orders)) return

      const printClass = (orders.length > 1 || is_preview === false) ? 'tpl_print_order' : 'tpl_print_view_order';
      // 拿到templatesMap
      const templatesMap = await this.$_getTemplatesMap({orders, template_id, _templates,printType}) // object

      const isNeedActualSubPriceEl = (config) => { // 自定义设置是否需要实价空元素 计算本页小计
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_actual_sub_price_total') || lineTypes.includes('is_show_actual_sub_price_total_capital');
      }
      // 自定义设置是否需要下单折前小计空元素，计算本页下单折前小计
      const isNeedSubProtocolOrgPriceEl = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_sub_protocol_org_price_total') || lineTypes.includes('is_show_sub_protocol_org_price_total_capital');
      }
      // 自定义设置是否需要折前小计空元素，计算本页折前小计
      const isNeedProtocolCurPageTotal = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_pre_protocol_org_price_total') || lineTypes.includes('is_show_pre_protocol_org_price_total_capital');
      }
      // 自定义设置是否需要税额空元素，计算本页税额小计
      const isNeedOrderTaxRatePrice = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_order_tax_rate_price');
      }
      // 自定义设置是否需要税额空元素，计算本页税额小计
      const isNeedActualSubPriceExcludingTax = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_actual_sub_price_excluding_tax');
      }
      if(!is_export_excel){
        for(let i in templatesMap){
          let nodeIndex =templatesMap[i].tpl_data.children&&templatesMap[i].tpl_data.children.findIndex(e=>e.type === 'Table')
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_total ||
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_total_capital ||
              isNeedActualSubPriceEl(templatesMap[i].tpl_data.config)
            )
          ){
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='actual_sub_price')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'actual_sub_price',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if (nodeIndex >=0) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='_commodity_count')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'_commodity_count',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_sub_protocol_org_price_total ||
              templatesMap[i].tpl_data.config.is_show_sub_protocol_org_price_total_capital ||
              isNeedSubProtocolOrgPriceEl(templatesMap[i].tpl_data.config)
            )
          ){
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='sub_protocol_org_price')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'sub_protocol_org_price',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_pre_protocol_org_price_total ||
              templatesMap[i].tpl_data.config.is_show_pre_protocol_org_price_total_capital ||
              isNeedProtocolCurPageTotal(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='protocol_org_price_cur_page_total')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'protocol_org_price_cur_page_total',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_order_tax_rate_price ||
              isNeedOrderTaxRatePrice(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='tax_rate_price')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'tax_rate_price',
                title:'',
                width:0,
                hidden:true
              })
            }

          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_excluding_tax ||
              isNeedActualSubPriceExcludingTax(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='actual_sub_price_excluding_tax')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'actual_sub_price_excluding_tax',
                title:'',
                width:0,
                hidden:true
              })
            }
          }

         }
      }
      if (!orders || orders.length === 0) {
        this.errorMessage("没有打印的数据");
      }
      // ----------------------------------------------------------------
      const newTemplateOrders = {};
      const oldTemplateOrderIdArr = [];
      // 填充 newTemplateOrders, oldTemplateOrderIdAr  把数据处理好了
      this.$_fillTemplateOrders({orders, newTemplateOrders, oldTemplateOrderIdArr, templatesMap, way_print, is_preview});
      if(Object.keys(newTemplateOrders).length>=2&&is_preview){
        this.errorMessage('仅打印单据为相同模板时支持打印预览');
        return
      }
      this.$_printNewTemplateOrders({newTemplateOrders, id, callback, is_export_excel, exportSheetMode, sheetNameKey, sort_by})
      this.$_printOldTemplateOrders({oldTemplateOrderIdArr, is_merge, template_id, printClass, is_bill, is_export_excel})
    },
    // 协议价打印
    async _printProtocolPrice({
      id,
      is_merge = 1,
      template_id = '',
      is_bill = 0,
      is_preview = true,
      way_print = '', // 打印方式, ORDER_TAG | CUSTOM_CATE
      is_export_excel = false,
      _templates,
      printType,
      callback = () => {},
      exportSheetMode
    }) {
      if (is_export_excel) {
        this.exportLoading = true;
      }
      // 拿到打印数据
      const res = await this._getProtocolPrintData({pp_id: id,  is_merge });
      let { status, data, message } = res;
      data.item.forEach(e=>{
        e.category_name = e.category1_name
      })
      data.order.forEach(e=>{
        e.start_time_end_time = `${ e.start_time } ~ ${ e.end_time }`
      })
      // 处理order订单信息数据,以及把对应的商品信息从item添加到order.items中
      const orders = this._formatPrintData(data, is_merge);
      if (!orders || !Array.isArray(orders)) return
      // 拿到templatesMap
      const templatesMap = await this.$_getTemplatesMap({orders, template_id, _templates,printType,type:'PROTOCOL_PRICE'}) // object
      // ----------------------------------------------------------------
      const newTemplateOrders = {};
      const oldTemplateOrderIdArr = [];
      // 填充 newTemplateOrders, oldTemplateOrderIdAr  把数据处理好了
      this.$_fillTemplateOrders({orders, newTemplateOrders, oldTemplateOrderIdArr, templatesMap, way_print, is_preview,idKey: 'pp_id'});
      if(Object.keys(newTemplateOrders).length>=2&&is_preview){
        this.errorMessage('仅打印单据为相同模板时支持打印预览');
        return
      }
      this.$_printNewTemplateOrders({newTemplateOrders, id, callback, is_export_excel, exportSheetMode,idKey: 'pp_id',type:'PROTOCOL_PRICE'})
    },
    /**
     * 单据管理--单据打印
     * @param {string} id 订单id
     * @param {string} is_merge 是否合并
     * @param {string} is_bill
     */
     async _printBillsReports({
      id,
      is_merge = 1,
      template_id = '',
      is_bill = 0,
      way_print = '',
      is_preview = true,
      is_export_excel = false,
      printType,
      _templates,
      _data,
      callback = () => {},
      seqFields,
      rangeParams,
      is_order_split,
    }) {
      if(+is_order_split === 1) {
        is_merge = 0
      }
      let select_prt_tpl_id = template_id
      if(is_merge==2&&printType==3){
        if(select_prt_tpl_id){
        }else{
          let printTemplateData = await settings.getPrintTemplate({
            type: 'GROUP_ORDER',
            id:template_id
          })
          select_prt_tpl_id = printTemplateData.id
        }
      }
      const data = await this.$_getBillsReportPrintData({id, is_bill, is_merge, _data,printType,select_prt_tpl_id, seqFields,rangeParams, is_order_split})

      const orders = this._formatPrintData(data, is_merge,printType);
      if (!orders || !Array.isArray(orders)) return
      // const is_preview = orders.length === 1;
      const printClass = is_preview ? 'tpl_print_view_bills' : 'tpl_print_bills';

      // 拿到templatesMap
      const templatesMap = await this.$_getTemplatesMap({orders, template_id, _templates,printType}) // object

      const isNeedActualSubPriceEl = (config) => { // 自定义设置是否需要实价空元素 计算本页小计
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_actual_sub_price_total') || lineTypes.includes('is_show_actual_sub_price_total_capital');
      }

      // 自定义设置是否需要下单折前小计空元素，计算本页下单折前小计
      const isNeedSubProtocolOrgPriceEl = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_sub_protocol_org_price_total') || lineTypes.includes('is_show_sub_protocol_org_price_total_capital');
      }
      // 自定义设置是否需要折前小计空元素，计算本页折前小计
      const isNeedProtocolCurPageTotal = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_pre_protocol_org_price_total') || lineTypes.includes('is_show_pre_protocol_org_price_total_capital');
      }
      // 自定义设置是否需要税额空元素，计算本页税额小计
      const isNeedOrderTaxRatePrice = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_order_tax_rate_price');
      }
      // 自定义设置是否需要税额空元素，计算本页税额小计
      const isNeedActualSubPriceExcludingTax = (config) => {
        const lineTypes = flatMapCustomFieldLineType(config);
        return lineTypes.includes('is_show_actual_sub_price_excluding_tax');
      }
      if(!is_export_excel){
        for(let i in templatesMap){
          let nodeIndex =templatesMap[i].tpl_data.children&&templatesMap[i].tpl_data.children.findIndex(e=>e.type === 'Table')
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_total ||
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_total_capital ||
              isNeedActualSubPriceEl(templatesMap[i].tpl_data.config)
            )
          ){
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='actual_sub_price')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'actual_sub_price',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if (nodeIndex >=0) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='_commodity_count')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'_commodity_count',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_sub_protocol_org_price_total ||
              templatesMap[i].tpl_data.config.is_show_sub_protocol_org_price_total_capital ||
              isNeedSubProtocolOrgPriceEl(templatesMap[i].tpl_data.config)
            )
          ){
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='sub_protocol_org_price')
            if(index==-1){
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'sub_protocol_org_price',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_pre_protocol_org_price_total ||
              templatesMap[i].tpl_data.config.is_show_pre_protocol_org_price_total_capital ||
              isNeedProtocolCurPageTotal(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='protocol_org_price_cur_page_total')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'protocol_org_price_cur_page_total',
                title:'',
                width:0,
                hidden:true
              })
            }
          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_order_tax_rate_price ||
              isNeedOrderTaxRatePrice(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='tax_rate_price')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'tax_rate_price',
                title:'',
                width:0,
                hidden:true
              })
            }

          }
          if(
            nodeIndex>=0 &&
            (
              templatesMap[i].tpl_data.config.is_show_actual_sub_price_excluding_tax ||
              isNeedActualSubPriceExcludingTax(templatesMap[i].tpl_data.config)
            )
          ) {
            let index =  templatesMap[i].tpl_data.children[nodeIndex].props.columns.findIndex(res=>res.key=='actual_sub_price_excluding_tax')
            if(index === -1) {
              templatesMap[i].tpl_data.children[nodeIndex].props.columns.push({
                key:'actual_sub_price_excluding_tax',
                title:'',
                width:0,
                hidden:true
              })
            }
          }

         }
      }

      const newTemplateOrders = {};
      const oldTemplateOrderIdArr = [];
      // 填充 newTemplateOrders, oldTemplateOrderIdAr
      this.$_fillTemplateOrders({orders, newTemplateOrders, oldTemplateOrderIdArr, templatesMap, way_print, is_preview, idKey: 'ir_id'});
      if(Object.keys(newTemplateOrders).length>=2&&is_preview){
        this.errorMessage('仅打印单据为相同模板时支持打印预览');
        return
      }
      this.$_printNewTemplateOrders({newTemplateOrders, id, callback, is_export_excel, idKey: 'ir_id'})
      this.$_printBillsReportOldTemplateOrders({oldTemplateOrderIdArr, is_merge, template_id, printClass, is_export_excel})
    },
    /**
     * 单据管理打印
     * @param {string} id 订单id
     * @param {string} is_merge 是否合并
     * @param {string} is_bill
     */
    async _printBehalfOrder({
      id,
      is_merge = 1,
      template_id = '',
      is_bill = 0,
      is_preview = true
    }) {
      let res = await this._getBehalfOrderPrintData({
        id,
        is_bill,
        is_merge
      });
      let { status, data, message } = res;
      if (!status) {
        this.errorMessage(message);
        return;
      }
      let orders = this._formatPrintData(data, is_merge);
      let printClass = orders.length > 1 ? 'tpl_print_order' : 'tpl_print_view_order';
      if (is_preview === false) {
        printClass = 'tpl_print_order';
      }
      const printTemplateMap = {};
      if (orders && Array.isArray(orders)) {
        orders.forEach(async (order) => {
          let template = null;
          if (order.prt_tpl_id && printTemplateMap[Number(order.prt_tpl_id)]) {
            template = printTemplateMap[order.prt_tpl_id];
          } else {
            // 获取打印模版重复请求待优化
            template = await settings
              .getPrintTemplate({
                type: 'ORDER',
                id: template_id || order.prt_tpl_id
              })
              .catch(() => false);
            printTemplateMap[Number(template.id)] = template;
          }
          if (!template) {
            this.errorMessage('找不到打印模版');
            return;
          }
          // 使用新模版打印
          if (settings.isNewTemplate(template)) {
            const templateData = template.tpl_data;
            templateData.config.preview = is_preview;
            templateData.config.type = template.type
            let printConfig = {
              template: templateData,
              data: this._formatOrderData(order, templateData.config)
            };
            printConfig = setOrderPrintConfig(printConfig);
            printConfig.data = handleDataSourceByTemplate(printConfig.data, printConfig.template);
            webPrint(printConfig);
          } else {
            const printAttrs = {
              'order_id': order.ids,
              'is_merge': Number(is_merge) ? 1 : 0
            }
            if (template_id) {
              printAttrs['tpl_id'] = template_id;
            }
            // 使用老模版打印
            oldVersionPrint({
              cssClass: printClass,
              attrs: printAttrs
            });
          }
        });
      }
    },
    /**
     * 单据管理打印
     * @param {string} id 订单id
     * @param {string} is_merge 是否合并
     * @param {string} is_bill
     */
    async _printBillsOrder({
      id,
      is_merge = 1,
      template_id = '',
      is_bill = 0,
      is_export_excel = false,
      is_preview = true,
      exportSheetMode,
      _templates,
      printType,
      is_export,
      way_print = '',
      exclude_print_times = 0,
      callback = () => {}
    }) {
      let res = await this._getBillsOrderPrintData({
        id,
        exclude_print_times,
        is_bill,
        is_merge,
        is_export
      });
      let { status, data, message } = res;
      if (!status) {
        this.errorMessage(message);
        return;
      }

      let orders = this._formatPrintData(data, is_merge);
      let printClass = orders.length > 1 ? 'tpl_print_behalf_order' : 'tpl_print_view_behalf_order';
      if (is_preview === false) {
        printClass = 'tpl_print_behalf_order';
      }
      const printTemplateMap = {};
         if (is_export_excel) {
          // 拿到templatesMap
          const templatesMap = await this.$_getTemplatesMap({orders, template_id, _templates,printType}) // object
          // ----------------------------------------------------------------
          const newTemplateOrders = {};
          const oldTemplateOrderIdArr = [];
          this.$_fillTemplateOrders({orders, newTemplateOrders, oldTemplateOrderIdArr, templatesMap, way_print, is_preview});
          this.$_printNewTemplateOrders({newTemplateOrders, id, callback, is_export_excel,idKey: "id", exportSheetMode})
          return
        }
      if (orders && Array.isArray(orders)) {
        orders.forEach(async (order) => {
          let template = null;
          if (order.prt_tpl_id && printTemplateMap[Number(order.prt_tpl_id)]) {
            template = printTemplateMap[order.prt_tpl_id];
          } else {
            // 获取打印模版重复请求待优化
            template = await settings
              .getPrintTemplate({
                type: 'ORDER',
                id: template_id || order.prt_tpl_id
              })
              .catch(() => false);
            printTemplateMap[Number(template.id)] = template;
          }
          if (!template) {
            this.errorMessage('找不到打印模版');
            return;
          }
          // 使用新模版打印

          if (settings.isNewTemplate(template)) {
            const templateData = template.tpl_data;
            templateData.config.preview = is_preview;
            templateData.config.type = template.type
            let formatData;
            if (templateData.config['is_commodity_class_level'] === 'Y' || templateData.config['is_commodity_class_level'] === 'YY') {
              formatData = formatCommodityClass(order, templateData.config);
              templateData.config.preview = false;
            } else {
              formatData = this._formatOrderData(order, templateData.config)
            }
            let printConfig = {
              template: templateData,
              data: formatData
            };
            const templateDataCopy = JSON.parse(JSON.stringify(templateData));
            printConfig = setOrderPrintConfig(printConfig);
            if (order.is_hkd) {
              printConfig.data = JSON.parse(__translate.ToTraditionalChinese(JSON.stringify(printConfig.data)));
              printConfig.template = JSON.parse(__translate.ToTraditionalChinese(JSON.stringify(printConfig.template)));
            }

            const isNeedActualSubPriceEl = (config) => { // 自定义设置是否需要实价空元素 计算本页小计
              const lineTypes = flatMapCustomFieldLineType(config);
              return lineTypes.includes('is_show_actual_sub_price_total') || lineTypes.includes('is_show_actual_sub_price_total_capital')
            }

            // 自定义设置是否需要下单折前小计空元素，计算本页下单折前小计
            const isNeedSubProtocolOrgPriceEl = (config) => {
              const lineTypes = flatMapCustomFieldLineType(config);
              return lineTypes.includes('is_show_sub_protocol_org_price_total') || lineTypes.includes('is_show_sub_protocol_org_price_total_capital');
            }

            let nodeIndex = printConfig.template.children && printConfig.template.children.findIndex(e => e.type === 'Table')
            if (
              nodeIndex >= 0 &&
              (
                printConfig.template.config.is_show_actual_sub_price_total ||
                printConfig.template.config.is_show_actual_sub_price_total_capital ||
                isNeedActualSubPriceEl(printConfig.template.config)
              )
            ) {
              let index =  printConfig.template.children[nodeIndex].props.columns.findIndex(res=>res.key == 'actual_sub_price')
              if (index == -1){
                printConfig.template.children[nodeIndex].props.columns.push({
                  key: 'actual_sub_price',
                  title: '',
                  width: 0,
                  hidden: true
                })
              }
            }

            if (
              nodeIndex >= 0 &&
              (
                printConfig.template.config.is_show_sub_protocol_org_price_total ||
                printConfig.template.config.is_show_sub_protocol_org_price_total_capital ||
                isNeedSubProtocolOrgPriceEl(printConfig.template.config)
              )
            ) {
              let index =  printConfig.template.children[nodeIndex].props.columns.findIndex(res=>res.key == 'sub_protocol_org_price')
              if (index == -1){
                printConfig.template.children[nodeIndex].props.columns.push({
                  key: 'sub_protocol_org_price',
                  title: '',
                  width: 0,
                  hidden: true
                })
              }
            }

            printConfig.data = handleDataSourceByTemplate(printConfig.data, printConfig.template);
            // if (is_export_excel) {
            //   exportExcel(printConfig, { exportSheetMode }).finally(() => {
            //     this.exportLoading = false;
            //   });
            // } else {
            //   webPrint(printConfig);
            // }
              handlePaginationData(printConfig.data, templateDataCopy);
              webPrint(printConfig);

          } else {
            const printAttrs = {
              'order_id': order.print_ids,
              'is_merge': is_merge ? 1 : 0
            }
            if (template_id) {
              printAttrs['tpl_id'] = template_id;
            }
            // 使用老模版打印
            oldVersionPrint({
              cssClass: printClass,
              attrs: printAttrs
            });
          }
        });
      }
    },
    /**
     * 格式化订单数据
     * @param {object} order 订单数据
     * @param {object} printConfig 打印配置
     */
    _formatOrderData(order, printConfig) {
      return formatOrderData(order, printConfig);
    },
    /**
     * 获取打印数据
     * @param {object} params 请求参数
     */
    _getPrintData(params, requestUrl = '/superAdmin/orderSuper/getPrintData') {
      return this.$request.get(requestUrl, params);
    },
    /**
     * 辅助函数
     */
    async $_getPrintData({
      id, is_bill, is_merge, record_print_times, _data, is_export_excel,printType ,select_prt_tpl_id,seqFields,rangeParams, is_order_split, sort_by,
      requestUrl
    }) {
      let params = { id, is_bill, is_merge, exclude_print_times: 1, is_export_excel, is_order_split, sort_by }
      select_prt_tpl_id && (params.select_prt_tpl_id = select_prt_tpl_id)
      seqFields && (params.sequence_field = seqFields)
      if(rangeParams&&rangeParams.customize_scope_type==1){
        params.customize_scope_type = 1
        params.customize_scope_ids = rangeParams.customize_scope_ids
      }else if(rangeParams&&rangeParams.customize_scope_type==2){
        params.customize_scope_type = 2
        params.customize_scope_ids = rangeParams.customize_scope_ids
      }else if(rangeParams&&rangeParams.customize_scope_type=='custom'){
        params.print_scope_id = rangeParams.print_scope_id
      }
      if(printType=='3'){
        params.merge_type = 2
      }else{
        delete params.merge_type
      }
      record_print_times && (delete params.exclude_print_times)
      let data
      if (_data) {
        data = _data
      } else {
        let res = await this._getPrintData(params, requestUrl);
        let { status, message } = res;
        if (!status) {
          this.errorMessage(message);
          return;
        }
        data = res.data
        data.item.forEach(item => {
          item._commodity_count = 1;
        });
        if (data.items) {
          data.items.forEach(item => {
            item._commodity_count = 1;
          });
        }
      }
      return data;
    },
    async $_getTemplatesMap({orders, template_id, _templates, printType,type}) {
      const templateIds = [];
      orders.forEach(order => {
        if(printType!=3){
        if (template_id) {
          order.prt_tpl_id = template_id;
        }
        if (order.prt_tpl_id && !templateIds.includes(order.prt_tpl_id)) {
          templateIds.push(order.prt_tpl_id);
        }
      }else{
        if(template_id){
          order.prt_tpl_id = template_id;
          if(!templateIds.includes(order.prt_tpl_id)){
           templateIds.push(template_id)
          }
        }
      }
      });
      // 总是获取默认模版
      templateIds.push('');
      let templates
      if (_templates) {
        // 传入的模板,线路客户列表用的
        templates = _templates;
      } else {
        templates = await Promise.all(
          templateIds.map(id => settings.getPrintTemplate({
            type: type || (printType=='3'?'GROUP_ORDER':'ORDER'),
            id
          }))
        ).catch(error=>{
          this.$Modal.confirm({
          title: '提示',
          content: '未配置打印模板，请先配置打印模板！点击确定，将跳转至打印配置页面。',
          onOk: () => {
            this.$router.push('/printConfig')
          },
          onCancel: () => {

          }
          })
        })
      }
      const templatesMap = {};
      templates.forEach(tpl => {
        templatesMap[Number(tpl.id)] = tpl;
      });
      templatesMap['_default_'] = templates[templates.length - 1]
      return templatesMap
    },
    async $_fillTemplateOrders({orders, newTemplateOrders, oldTemplateOrderIdArr, templatesMap, way_print, is_preview, idKey = 'order_ids'}) {
      orders.forEach(async (order) => {
        // 按商品分类分组打印,后端直接将对应对应的商品信息放到了order的item里面. 所以以item为准
        if (order.item) {
          order.items = order.item;
          order._commodity_count = order.item.length;
        }
        let template = null;
        if (order.prt_tpl_id && templatesMap[Number(order.prt_tpl_id)]) {
          template = templatesMap[Number(order.prt_tpl_id)];
        } else {
          template = templatesMap['_default_'];
        }
        // =======如果按标签打印且是老版打印模板是则需要赋值为默认新版模板
        if (!settings.isNewTemplate(template) && (way_print === ORDER_TAG || way_print === CUSTOM_CATE || way_print ==CUSTOM_MERGE_CATE)) {
          template = templatesMap['_default_']
        }
        // =======
        if (!template) {
          this.errorMessage('找不到打印模版');
          return;
        }
        // 新模版
        if (settings.isNewTemplate(template)) {
          const templateData = template.tpl_data;
          templateData.config.preview = is_preview;
          templateData.config.wayPrint = way_print;
          templateData.config.type = template.type
          // =======如果是按订单标签打印 如果打印样式设置的分类汇总样式需要改成默认样式, 分类汇总样式暂时没做适配;
          const printStyle = templateData.config['print_style']
          templateData.config['print_style'] = ((way_print === ORDER_TAG || way_print === CUSTOM_CATE || way_print == CUSTOM_MERGE_CATE)&&printStyle=='multi_column_3') ? "default" : printStyle
          // =======
          if (!newTemplateOrders[template.id]) {
            newTemplateOrders[template.id] = {
              template: templateData,
              orders: []
            }
          }
          let formatData;
          // 开启按商品分类打印需要拆单,如果是自定义分类的情况不用拆单,后端拆好了
          if ((way_print !== CUSTOM_CATE || way_print!==CUSTOM_MERGE_CATE) && (templateData.config['is_commodity_class_level'] === 'Y' || templateData.config['is_commodity_class_level'] === 'YY')) {
            formatData = formatCommodityClass(order, templateData.config)
          } else {
            // 自定义分组名称
            if((!templateData.config.print_format&&templateData.config.print_style=="category_group") || templateData.config.print_format=="category_group"){
              order.items.forEach(res=>{
                res.category_name = res.prt_tpl_group_category_name ||res.category_name
              })
            }
            formatData = this._formatOrderData(order, templateData.config)
          }
           // _formatCommodityClass 返回的是数组
          formatData = Array.isArray(formatData) ? formatData : [formatData]
          newTemplateOrders[template.id].orders.push(
            ...formatData
          );
        } else {
          if (!oldTemplateOrderIdArr.includes(order[idKey])) {
            oldTemplateOrderIdArr.push(order[idKey]);
          }
        }
      });
    },
    $_printNewTemplateOrders({newTemplateOrders, id, callback, is_export_excel, idKey = "order_id", exportSheetMode,type, sheetNameKey, sort_by}) {
      let idArr = id.split(",");
        // 把相同模版的分组打印, 提高打印性能
        for (let templateId in newTemplateOrders) {
          let templateOrders = [];
          // 按线路再按客户名称去打印的话,  newTemplateOrders中的order已经排序好了, 不需要再根据id去排序
          if (sort_by === 'line_user') {
            templateOrders = newTemplateOrders[templateId].orders
          } else {
            idArr.forEach(id => {
              // 因为现在加入了按商品一级分类打印样式,需要把一个订单拆成多个订单,会有多个匹配值.
              // 而上面的find方法只会返回第一个匹配值, 故采用forEach
              newTemplateOrders[templateId].orders.forEach(order => {
                // 按商品分类分组打印对order_id进行了处理, 拆成了45089-0 45089-1 45089-2 这种形式
                const order_id = order[idKey].split("-")[0];
                if (+order_id === +id) {
                  templateOrders.push(order);
                }
              })
            });
          }

          let simplifiedPrintArr = [];
          let traditionalPrintArr = [];
          templateOrders.map(order => {
            if (+order.is_hkd === 1) {
              traditionalPrintArr.push(order);
            } else {
              simplifiedPrintArr.push(order);
            }
          });
          // 简体打印
          if (simplifiedPrintArr.length) {
            handlePaginationData(simplifiedPrintArr, newTemplateOrders[templateId].template);
            let printConfigForSimplified = {
              taskSize: 300,
              template: newTemplateOrders[templateId].template,
              data: simplifiedPrintArr,
              is_export_excel,
              callback
            };
            printConfigForSimplified = setOrderPrintConfig(printConfigForSimplified,type);
            printConfigForSimplified.data = handleDataSourceByTemplate(printConfigForSimplified.data, printConfigForSimplified.template);
            // 分组占用了category字段 统一用category1
            printConfigForSimplified.template.children.forEach(res=>{
              if(res.type=='Table'){
                res.props.columns.forEach(item=>{
                  if(item.key=='category_name'){
                    item.key='category1'
                  }
                })
              }
            })
            // 区分导出还是打印
            if (is_export_excel) {
              exportExcel(printConfigForSimplified, { exportSheetMode, sheetNameKey }).finally(() => {
                this.exportLoading = false;
              });
            } else {
              webPrint(printConfigForSimplified);
            }
          }
          // 繁体打印
          if (traditionalPrintArr.length) {
            let printConfigForTraditionald = {
              taskSize: 300,
              template: newTemplateOrders[templateId].template,
              data: traditionalPrintArr,
              is_export_excel,
              callback
            };
            printConfigForTraditionald = setOrderPrintConfig(printConfigForTraditionald);
            // 将模板已经字段名称转换为繁体字
            printConfigForTraditionald.data.forEach(item => {
              item.is_hkd = 1;
            })
            printConfigForTraditionald.data = JSON.parse(__translate.ToTraditionalChinese(JSON.stringify(printConfigForTraditionald.data)));
            printConfigForTraditionald.template = JSON.parse(__translate.ToTraditionalChinese(JSON.stringify(printConfigForTraditionald.template)));
            printConfigForTraditionald.data = handleDataSourceByTemplate(printConfigForTraditionald.data, printConfigForTraditionald.template);
            // 区分导出还是打印
            if (is_export_excel) {
              exportExcel(printConfigForTraditionald, { exportSheetMode }).finally(() => {
                this.exportLoading = false;
              })
            } else {
              webPrint(printConfigForTraditionald);
            }
          }
        }
    },
    $_printOldTemplateOrders({oldTemplateOrderIdArr, is_merge, template_id, printClass, is_bill, is_export_excel}) {
      if (is_export_excel) return

      oldTemplateOrderIdArr.forEach(orderId => {
        const printAttrs = {
          'order_id': orderId,
          'is_merge': is_merge ? 1 : 0,
          'is_bill': Number(is_bill) ? 1 : 0,
          'record_print_times': 0
        }
        if (template_id) {
          printAttrs['tpl_id'] = template_id;
        }
        // 使用老模版打印
        oldVersionPrint({
          cssClass: printClass,
          attrs: printAttrs
        });
      });
    },
    /**
     * 单据管理-单据打印数据 辅助函数
     */
    async $_getBillsReportPrintData({id, is_bill, is_merge,printType, _data,select_prt_tpl_id,seqFields,rangeParams, is_order_split}) {
      // @TODO 需要将records_ids也返回
      let params = {
        record_ids: id,
        is_bill,
        is_merge,
        is_order_split,
      }
      select_prt_tpl_id && (params.select_prt_tpl_id = select_prt_tpl_id)
      seqFields && (params.sequence_field = seqFields)
      if(rangeParams) {
        if(rangeParams.customize_scope_type==1){
          params.customize_scope_type = 1
          params.customize_scope_ids = rangeParams.customize_scope_ids
        }else if(rangeParams.customize_scope_type==2){
          params.customize_scope_type = 2
          params.customize_scope_ids = rangeParams.customize_scope_ids
        }else if(rangeParams.customize_scope_type=='custom'){
          params.print_scope_id = rangeParams.print_scope_id
        }
      }
      if(printType=='3'){
        params.merge_type = 2
      }else{
        delete params.merge_type
      }
      let data
      if (_data) {
        data = _data
      } else {
        let res = await this._getBillsReportPrintData(params);
        let { status, message } = res;
        if (!status) {
          this.errorMessage(message);
          return;
        }
        data = res.data
        data.item.forEach(item => {
          item._commodity_count = 1;
        });
        if (data.items) {
          data.items.forEach(item => {
            item._commodity_count = 1;
          });
        }        
      }
      return data
    },
    $_printBillsReportOldTemplateOrders({oldTemplateOrderIdArr, is_merge, template_id, printClass, is_export_excel}) {
      if (is_export_excel) return
      oldTemplateOrderIdArr.forEach(ir_id => {
        const printAttrs = {
          'order_id': ir_id,
          'is_merge': is_merge ? 1 : 0,
        }
        if (template_id) {
          printAttrs['tpl_id'] = template_id;
        }
        // 使用老模版打印
        oldVersionPrint({
          cssClass: printClass,
          attrs: printAttrs
        });
      });
    },
    /**
     * 获取打印数据
     * @param {object} params 请求参数
     */
    _getBehalfOrderPrintData(params) {
      return this.$request.get(this.apiUrl.behalf.getPrintData, params);
    },
    // 获取协议单打印单
    _getProtocolPrintData(params) {
      return this.$request.get(this.apiUrl.getProtocolPrintData, params);
    },
    /**
     * 获取打印数据
     * @param {object} params 请求参数
     */
    _getBillsOrderPrintData(params) {
      return this.$request.get(this.apiUrl.bills.order.getPrintData, params);
    },
    /**
     * 获取打印数据
     * @param {object} params 请求参数
     */
    _getBillsReportPrintData(params) {
      return this.$request.get(this.apiUrl.bills.order.getReportPrintData, params);
    },
    /**
     * 打印数据格式化
     * @param {Array} data 打印的数据
     * @param {Boolean} is_merge 是否根据发货日期合并
     * @returns {Array} data 格式化之后的数据
     */
      handleCutZero(num) {
        if(!num){
          return num
        }
            //拷贝一份 返回去掉零的新串
        let newstr = num;
        //循环变量 小数部分长度
        console.log(num.indexOf('.') - 1);
        let leng = num.length - num.indexOf('.') - 1;
        //判断是否有效数
        if (num.indexOf('.') > -1) {
          //循环小数部分
          for (let i = leng; i > 0; i--) {
            //如果newstr末尾有0
            if (
              newstr.lastIndexOf('0') > -1 &&
              newstr.substr(newstr.length - 1, 1) == 0
            ) {
              let k = newstr.lastIndexOf('0');
              //如果小数点后只有一个0 去掉小数点
              if (newstr.charAt(k - 1) == '.') {
                return newstr.substring(0, k - 1);
              } else {
                //否则 去掉一个0
                newstr = newstr.substring(0, k);
              }
            } else {
              //如果末尾没有0
              return newstr;
            }
          }
        }
        return num;
    },
    _formatPrintData(data, is_merge,printType) {
      // 判断收货地址是否相同
      const isSameReceiveAddress = (order, orderItem) => {
        // 商品信息中不返回order_address_detail时，不做判断
        if (orderItem.order_address_detail === undefined) {
          return true;
        }
        return order.receive_name === orderItem.receive_name &&
        order.receive_tel === orderItem.receive_tel &&
        order.order_address_detail === orderItem.order_address_detail;
      }

      let item = data.item;
      let user = data.user;
      let shop = data.shop;
      for (var i in data.order) {
        var v = data.order[i];
        v.actual_total_pay_str = (v.actual_price) + '元';
        v.order_service_price = v.service_price;
        delete v.service_price;
        v.order_ids = [v.order_id];
        v.print_ids = [v.id];
        v.alia_protocol_org_total_price = v.protocol_org_total_price;
        v.alia_protocol_org_total_price_UpperCase = num2RmbCaption(v.protocol_org_total_price);
        v.order_info_qrcode = decodeURIComponent(encodeURIComponent(v.user_name)) + '@@@' + v.delivery_date;
        if (v.order_tag) {
          var order_tags = v.order_tag;
          var tags = [];
          // delete v.order_tag;
          for (var m in order_tags) {
            tags.push(order_tags[m].name);
          }
          v.order_tags = tags.join(',');
        }
        v.actual_total_pay_str_capital = num2RmbCaption(v.actual_price);
        v.should_pay_price_capital = num2RmbCaption(v.should_pay_price);
        if (v.is_pay == 'Y') {
          v.pay_status_str = '已支付' + v.pay_price + '元';
        } else {
          v.pay_status_str = '未支付';
        }
        for (var j in item) {
          item[j].row_money = this.handleCutZero(item[j].row_money)

          function isMatchFormat(str) {
            return str.includes('-')
          }

          // 匹配商品编码是否为1-2这种格式, 避免导出到excel展示为日期,  追加个空格
          if (isMatchFormat(item[j].commodity_code) && item[j].commodity_code) {
            // 先去掉空格, 再追加
            const commodity_code = item[j].commodity_code.replace(/\s+/g, '')
            item[j].commodity_code = `　${commodity_code}`
          }

          if (is_merge > 0) {
            // 按集团打印 需要合并相同集团的单
            if(printType==3){
              if (
                item[j].group_id == v.group_id &&
                item[j].delivery_date == v.delivery_date
              ) {
                if (!v.items) {
                  v.items = new Array();
                }
                // 两个取值相同的字段，前端做处理
                item[j].alia_protocol_org_price = item[j].protocol_org_price;
                v.items.push(item[j]);
                if (!v.order_ids.includes(item[j].order_id)) {
                  v.order_ids.push(item[j].order_id);
                  v.print_ids.push(item[j].order_id);
                }
              }
            }else{
              if (
                item[j].user_id == v.user_id &&
                item[j].delivery_date == v.delivery_date &&
                isSameReceiveAddress(v, item[j])
              ) {
                if (!v.items) {
                  v.items = new Array();
                }
                // 两个取值相同的字段，前端做处理
                item[j].alia_protocol_org_price = item[j].protocol_org_price;
                v.items.push(item[j]);
                if (!v.order_ids.includes(item[j].order_id)) {
                  v.order_ids.push(item[j].order_id);
                  v.print_ids.push(item[j].order_id);
                }
              }
            }
          } else {
            if (item[j].order_id == v.id) {
              if (!v.items) {
                v.items = new Array();
              }
              // 两个取值相同的字段，前端做处理
              item[j].alia_protocol_org_price = item[j].protocol_org_price;
              v.items.push(item[j]);
            }
          }
        }
        for (let j in user) {
          if (user[j].id == v.user_id) {
            v.email = user[j].email || '';
            v.user_tag_text = user[j].user_tag_text || '';
            v.cus_name = user[j].contact_name || '';
            v.user_code = user[j].user_code || '';
            v.account_tel = user[j].account_tel || '';
            v.address_detail = this.$store.state.sysConfig.is_open_user_multi_address==1?v.address_detail:(user[j].address_detail || '');
            v.seller_name = user[j].seller_name || '';
            v.seller_mobile = user[j].seller_mobile || '';
            v.delivery_time = user[j].delivery_time || '';
            v.receive_time = user[j].receive_time || '';
            v.user_trace_url = user[j].user_trace_url || '';
            v.area_name = user[j].area_name || '';
            v.current_month_unpaid_price = user[j].current_month_unpaid_price || '';
            v.seller_attachment_link = user[j].seller_attachment_link || '';
            v.special_remark = user[j].special_remark || '';
            v.balance_due = user[j].balance_due || '';
          }
        }
        for (let j in shop) {
          if (shop[j].line_id == v.shop_id) {
            v.stopgap_code = shop[j].stopgap_code || shop[j].line_code;
            v.name = shop[j].name || '';
            v.driver = shop[j].driver_name || shop[j].driver || '';
            v.driver_mobile = shop[j].driver_mobile || '';
            v.shop_name = shop[j].shop_name || shop[j].name || '';
          }
        }
        if (data.driver) {
          const driverInfo = data.driver[v.driver_id] || {}
          v.driver = driverInfo.name
          v.driver_mobile  = driverInfo.driver_tel
        }
        // if (v.items) {
        //   v.items.forEach((item, index) => {
        //     // 商品序号
        //     item.no = index + 1;
        //     // 负数下浮率
        //     item.minus_price_float_rate = item.price_float_rate ?(item.price_float_rate.indexOf('-') >= 0 ? item.price_float_rate.replace('-', '') : '-' + item.price_float_rate) : '';
        //   });
        // }
        v.order_ids = v.order_ids.join(",");
        v.print_ids = v.print_ids.join(",");

        if (data.group && data.group[v.group_id]) {
          v.group_trace_url = data.group[v.group_id].group_trace_url || '';
        }

      }

      data.order.forEach(order => {
        (order.item || []).forEach(item => {
          item.alia_protocol_org_price = item.protocol_org_price
        });
        (order.items || []).forEach(item => {
          item.alia_protocol_org_price = item.protocol_org_price
        });
        if (order.items) {
          order._commodity_count = order.items.length;
        } else {
          order._commodity_count = 0;
        }
      })
      // 过滤掉item为空的订单
      data.order = data.order.filter(order => order.items && order.items.length > 0);
      return data.order;
    },
  }
}
