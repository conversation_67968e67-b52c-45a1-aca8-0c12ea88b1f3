/*
 * @Author: lizhiwei😊
 * @Date: 2021-09-17 14:28:58
 * @Description: 订单发货单
 */
import { num2RmbCaption } from '../common';
import jsbarcode from 'jsbarcode';
import { cloneDeep, uniq } from 'lodash-es';
import { _addReceiverSign } from './@common';
import {DECIMAL_COMPLETION, SHOW_DISCOUNT_100, genSettingConfig, DEFAULT_DATE_FORMAT, NOT_ALIAS, WHOLESALE_MARKET_PRICE_PLACEHOLDER} from './@extraConfig';
import store from '@/vuex';
import { getOrderShareUrl } from '../common';
import { renderPaginationTr } from './order-pagination';
const ORDER_TAG = 'order_tag';
const MULTI_COLUMN = 'multi_column';
const MULTI_COLUMN_2 = 'multi_column_2';
const MULTI_COLUMN_3 = 'multi_column_3';
const CATEGORY_GROUP = 'category_group';
const SORT_VERTICAL = 'sort_vertical'; // 垂直排列
const SORT_HORIZONTAL = 'sort_horizontal'; // 水平排列
const CATEGORY_NAME = 'category_name';
const ROW_NUM = 'row_num';
const SORT_TYPE = 'sort_type';
const IS_AUTO_FILL_ROW = 'is_auto_fill_row'; // 是否自动填充行
// 商品名称对齐方式
const NAME_TEXT_ALIGN = 'name_text_align';

function hasMutiCol(config,isBoolean){
  // 新的字段
    if(['column_2','column_3'].includes(config.print_line)){
      return isBoolean ? true : (+ config.print_line.replace(/column_/, ''))
    }
     if([MULTI_COLUMN_2,MULTI_COLUMN_3].includes(config.print_style)){
    return isBoolean ? true : (+config.print_style.replace(/multi_column_/, ''))
    }
    return false
}

// 获取配置了dataKey的分页栏项
export const getAllPageSumItems = () => {
  const paginationConfig = _ORDER.config.dataSourceList.find(item => item.title === '分栏区字段');
  if (!paginationConfig) {
    return [];
  }
  return paginationConfig.items.filter(item => item.dataKey && item.sumType === 'all');
}

export const _setOrderPrintConfig = (config,type) => {
  const printConfig = cloneDeep(config);
  const { template } = printConfig;
  const inAllPageItems = template.config.inAllPageItems;
  // 分页栏是否显示页码
  const isShowPageNo = template.config.is_show_page_no;

  // 分页栏是否显示订单号
  const isShowOrderNo = template.config.is_show_order_no;

  // 是否显示当前页发货数量合计
  const isCurrentPageActualAmount = template.config.is_show_num_total;

  // 是否显示当前页下单数量合计
  const isCurrentPageOrderAmount = template.config.is_show_order_num_total;

  // 是否显示所有页发货数量合计
  const isAllPageActualAmount = template.config.is_show_all_num_total;
  // 是否显示所有页下单数量合计
  const isAllPageOrderAmount = template.config.is_show_order_total_amount;

  // 是否显示当前页合计
  const isCurrentPageActualPrice = template.config.is_show_pre_total;
  // 是否显示当前页下单金额合计
  const isCurrentPageOrderPrice = template.config.is_show_order_total;

  // 是否显示本页小计(字样)
  const isCurrentPageActualPriceLabel = template.config.is_show_pre_total_txt;

  // 是否显示当前页合计大写
  const isCurrentPageActualPriceCapital =
    template.config.is_show_pre_total_capital;
  // 是否显示当前页下单金额合计大写
  const isCurrentPageOrderPriceCapital =
    template.config.is_show_order_total_capital;

  // 是否显示所有页合计
  const isAllPageActualPrice = template.config.is_show_all_total;
  // 是否显示所有页下单金额合计
  const isAllPageOrderPrice = template.config.is_show_all_total_price;
  // 是否显示所有页下单金额合计大写
  const isAllPageOrderPriceCapital = template.config.is_show_all_total_price_capital;

  // 总计配置对象
  const isAllPageActualPriceSetting = template.config.is_show_all_total_setting;

  // 是否显示所有页合计大写
  const isAllPageActualPriceCapital = template.config.is_show_all_total_capital;

  // 是否显示本列发货小计
  const isShowCurrentColumnTotal = template.config.is_show_current_column_total;

  // 是显示本页商品数
  const isShowCurrentPageCommodityCount = template.config.is_show_current_page_commodity_count;
  // 是显示所有页商品数
  const isShowAllPageCommodityCount = template.config.is_show_all_page_commodity_count;

  // 是否显示表格边框
  printConfig.template.config.tableBordered =
    template.config.tableBordered;
  // 是否每页显示表尾
  // printConfig.template.config.footerInAllPage = template.config.footerInAllPage === 'Y';
  const pageInfoInAllPage =
    inAllPageItems && inAllPageItems.includes('pageInfoInAllPage');
  // 每页固定行数
  const rowNum = +template.config.row_num;

  // 商品名称对齐方式
  const nameTextAlign = template.config[NAME_TEXT_ALIGN];
  // 是否显示本页折前小计(小写)
  const isShowPreProtocolOrgPriceTotal = template.config.is_show_pre_protocol_org_price_total;
  // 是否显示本页折前小计(大写)
  const isShowPreProtocolOrgPriceTotalCapital = template.config.is_show_pre_protocol_org_price_total_capital;
  // 是否显示本页下单折前小计(小写)
  const isShowSubProtocolOrgPriceTotal = template.config.is_show_sub_protocol_org_price_total;
  // 是否显示本页下单折前小计(大写)
  const isShowSubProtocolOrgPriceTotalCapital = template.config.is_show_sub_protocol_org_price_total_capital;
  // 是否显示本页实际总价小写
  const isShowActualPriceTotal = template.config.is_show_actual_sub_price_total;
  const isShowActualSubPriceExcludingTax = template.config.is_show_actual_sub_price_excluding_tax
  const isShowActualPriceTotalCapital = template.config.is_show_actual_sub_price_total_capital;
  const isShowTaxRatePrice = template.config.is_show_order_tax_rate_price;
  const isShowDeliverReductionPrice = template.config.is_show_deliver_reduction_price;
  const isShowDeliverReductionPriceCapital = template.config.is_show_deliver_reduction_price_capital;
  const isShowDeliveryTotalDiscountPrice = template.config.is_show_delivery_total_discount_price;
  const isShowDeliveryTotalDiscountPriceCapital = template.config.is_show_delivery_total_discount_price_capital;
  const labelMap = {};
  const columnArea = template.config.columnArea || []
  columnArea.forEach(res=>{
    labelMap[res.key] = res.labelShow;
  });

  const allPageSumItems = getAllPageSumItems();
  const selectedAllPageSumItems = allPageSumItems.filter(pItem => template.config[pItem.key]);

  if (pageInfoInAllPage) {
    // 自定义叶脚信息需去掉模版中的每页显示分页信息
    printConfig.template.config.inAllPageItems.splice(
      printConfig.template.config.inAllPageItems.findIndex(
        _ => _ === 'pageInfoInAllPage'
      ),
      1
    );
  }
  printConfig.hook = printConfig.hook || {};
  // 订单自定义底部信息
  printConfig.hook.items = {
    columns: ({ columns }) => {
      if (!hasMutiCol(template.config)) {
        return columns;
      }
      // 多列打印配置
      const newColumns = [];
      const repeatCount = hasMutiCol(template.config)
      for (let i = 0; i < repeatCount; i++) {
        columns.forEach(col => {
          newColumns.push({
            title: col.title,
            key: col.key + '_' + i,
            width: col.width,
            hidden: col.hidden
          });
        });
      }
      return newColumns;
    },
    row: ({ h, row, borderStyle, columns, cellStyle, mainData, index, rows }) => {
      if (row._is_pagination) {
        return renderPaginationTr({
          h,
          row: cloneDeep(row),
          borderStyle: cloneDeep(borderStyle),
          columns: cloneDeep(columns),
          cellStyle: cloneDeep(cellStyle),
          mainData: cloneDeep(mainData),
          index,
          rows: cloneDeep(rows),
          template: cloneDeep(template),
          rowConfig: cloneDeep(row._pagination_config),
          onlyInLastPage: true
        });
      }
      const _style = {
        borderRight: borderStyle,
        borderBottom: borderStyle,
        padding: '5px',
        ...cellStyle
      };
      const copyColumns = [...columns];
      // 去除掉末尾的隐藏列
      while (copyColumns[copyColumns.length - 1].hidden) {
        copyColumns.pop();
      }
      let colspan = copyColumns.length;
      // 按分类汇总的分类行
      if (row._is_category) {
        const categoryNameArr = [row.category_name];
        if (template.config.category_group_fields) {
          if (template.config.category_group_fields.includes('count')) {
            categoryNameArr.push(`【${row.count}】`);
          }
          if (template.config.category_group_fields.includes('price')) {
            row.price = +row.actual_amount ? (row.total_price / row.actual_amount).toFixed(2) : 0
            categoryNameArr.push(`单价: ${(row.price)}`);
          }
          if (template.config.category_group_fields.includes('total_price')) {
            categoryNameArr.push(`发货金额小计: ${row.total_price}`);
          }
          if (template.config.category_group_fields.includes('actual_sub_price')) {
            categoryNameArr.push(`实际金额小计: ${row.actual_sub_price}`);
          }
        } else {
          row.price = +row.actual_amount ? (row.total_price / row.actual_amount).toFixed(2) : 0
          categoryNameArr.push(`【${row.count}】`);
          categoryNameArr.push(`单价: ${row.price}`);
          categoryNameArr.push(`发货金额小计: ${row.total_price}`);
        }
        let categoryName = categoryNameArr.join(' ');
        if(type=='PROTOCOL_PRICE'){
          categoryName =  `${row.category_name}【${row.count}】`
        }
        return h('tr', [
          h(
            'td',
            {
              style: _style,
              attrs: {
                colspan
              }
            },
            categoryName
          )
        ]);
      } else if (row._is_summary) {
        // 按订单标签拆分--总和行
        // 找到实收金额行 - 对应index
        let index;
        if (row._multi_column_num) {
          index = columns.findIndex(
            column =>
              column.key === `actual_sub_price_${row._multi_column_num - 1}`
          );
        } else {
          index = columns.findIndex(
            column => column.key === 'actual_sub_price'
          );
        }
        const renderDom = columns.map((item, _index) => {
          if (index === _index) {
            return <td style={_style}>{row.total_price}</td>;
          }
          if (index - 1 === _index) {
            const nowStyle = { ..._style, textAlign: 'center' };
            return <td style={nowStyle}>小计: </td>;
          }
          const tdStyle = { ..._style };
          if (item.hidden) {
            tdStyle.border = 'none';
            tdStyle.overflow = 'hidden';
          }
          return <td style={tdStyle}></td>;
        });
        const style = index === -1 ? { display: 'none' } : {};

        return <tr style={style}>{renderDom}</tr>;
      } else if (row._is_single_row) {
        // 按订单标签拆分--标签行,
        return h('tr', [
          h(
            'td',
            {
              style: { ..._style, textAlign: 'center', wordWrap: 'break-word' },
              attrs: {
                colspan
              }
            },
            `${row.content}`
          )
        ]);
      } else {
        return false;
      }
    },
    col: ({ h, row, col, borderStyle, cellStyle }) => {
      const _style = {
        borderRight: borderStyle,
        borderBottom: borderStyle,
        padding: '5px',
        ...cellStyle
      };
      // if(col.key=='actual_sub_price'){
      //   const style = { ..._style, display: 'none' };
      //   return <td style={style}>{row[col.key]}</td>;
      // }
      // if(col.key=='actual_sub_price'&&col.hidden){
      //   const style = { ..._style, padding:0,paddingRight:0,border:'none'};
      //   return <td style={style}> <div style="width:0;overflow:hidden;position:absolute;top:0px;left:100px;height:0px;"> {row[col.key]}
      // </div></td>;
      // }
      if(col.hidden !== void 0 && col.hidden) {
        const style = { ..._style, padding:0,paddingRight:0,border:'none', width: 0};
        return <td style={style}> <div style="width:0;overflow:hidden;position:absolute;top:0px;left:100px;height:0px;"> {row[col.key]}
      </div></td>;
      }
      if (col && col.key.includes('commodity_code_bar_code')) {
        let barcodeData = '';
        if (row[col.key]) {
          let canvas = document.createElement('canvas');
          jsbarcode(canvas, row[col.key], {
            displayValue: false,
            errorCorrectionLevel: 'H',
            format: 'CODE128',
            type: 'image/jpeg',
            height: 15,
            width: 1,
            margin: 0,
            quality: 1
          });
          barcodeData = canvas.toDataURL('image/jpeg');
        }
        return h(
          'td',
          {
            style: _style
          },
          [
            barcodeData
              ? h('img', {
                  style: {width: '100%'},
                  attrs: {
                    src: barcodeData
                  }
                })
              : ''
          ]
        );
      }
      // 处理序号加粗的情况
      if (col && col.key.includes('no') && row && row._is_bold_0) {
        const style = { ..._style, fontWeight: 'bold' };
        return <td style={style}>{row[col.key]}</td>;
      }
      // 差异金额的情况
      if (col && col.key.includes('difference_money')) {
        return <td style={_style}>{((row.row_money || 0) - row.row_money2).toFixed(2)}</td>;
      }
      // 按模板导出, 批次号会被转换为科学计数法
      // 尝试了 mso-number-format:'\@'; 或者vnd.ms-excel.numberformat:@, 都会在渲染html的时候没了
      // 最终加了给空白字符, 可以解决
      if (config.is_export_excel && col && col.key.includes('batch_no')) {
        const style = { ..._style };
        return <td style={style}>&nbsp;{row[col.key]}</td>;
      }
      // 双列的时候商品名称固定宽度, 不然会随机压缩
      // if (col && col.title === '商品名称' && row) {
      //   const style = { ..._style, width: `${col.width}px` };
      //   const overflowStyle = {
      //     width: '100%',
      //     wordBreak: 'keep-all',
      //     whiteSpace: 'nowrap',
      //     overflow: 'hidden',
      //     height: '30px',
      //     width: (col.width - 8) + 'px',
      //     display: 'block',
      //     'white-space': 'normal',
      //     'word-break': 'normal',
      //     'text-align': 'inherit'
      //   };
      //   let tdContent = row[col.key];
      //   // 固定模式
      //   if (
      //     config.template.config &&
      //     config.template.config.is_auto_adaptive_table_size === 'N'
      //   ) {
      //     tdContent =
      //     (
      //       <div style={{ ...overflowStyle, textAlign: nameTextAlign }}>
      //         {tdContent}
      //       </div>
      //     );
      //   }
      //   nameTextAlign && (style.textAlign = nameTextAlign);
      //   return <td style={style}>{tdContent}</td>;
      // }
      // 如果nameTextAlign有值,则name的textAlign覆盖
      // if (col && col.title === '商品名称' && nameTextAlign) {
      //   const style = {..._style, textAlign: nameTextAlign}
      //   return (
      //     <td style={style}>
      //       {row[col.key]}
      //     </td>
      //   )
      // }
      return false;
    },
    footer: ({ h, columns, borderStyle, cellStyle, mainData, rows }) => {
      const footer = [];
      const actualPriceColumnIndex = columns.findIndex(
        item => item.key === 'row_money'
      );
      const orderPriceColumnIndex = columns.findIndex(
        item => item.key === 'row_money2'
      );
      const actualNumColumnIndex = columns.findIndex(
        item => item.key === 'actual_amount'
      );
      const orderNumColumnIndex = columns.findIndex(
        item => item.key === 'amount'
      );
      const protocolOrgPriceColumnIndex = columns.findIndex(
        item => item.key === 'protocol_org_price_cur_page_total'
      );
      const actualColumnIndex = columns.findIndex(
        item => item.key === 'actual_sub_price'
      );
      const subProtocolOrgPriceColumnIndex = columns.findIndex(
        item => item.key === 'sub_protocol_org_price'
      );
      const actualSubPriceExcludingTaxColumnIndex = columns.findIndex(
        item => item.key === 'actual_sub_price_excluding_tax'
      )
      const taxRatePriceColumnIndex = columns.findIndex(
        item => item.key === 'tax_rate_price'
      )
      const deliverReductionPriceColumnIndex = columns.findIndex(
        item => item.key === 'deliver_reduction_price'
      )
      const deliverTotalDiscountPriceColumnIndex = columns.findIndex(
        item => item.key === 'delivery_total_discount_price'
      )
      let thNum = 1
      if (
        isCurrentPageActualPrice ||
        isCurrentPageActualPriceCapital ||
        isCurrentPageOrderPrice ||
        isCurrentPageOrderPriceCapital ||
        isCurrentPageActualAmount ||
        isCurrentPageOrderAmount
      ) {
        thNum++
        // firstColumnColspan -= 3;
      }
      if (isAllPageActualPrice || isAllPageActualPriceCapital || isAllPageOrderPrice || isAllPageOrderPriceCapital || isAllPageActualAmount || isAllPageOrderAmount) {
        thNum++
        // firstColumnColspan -= 3;
      }
      selectedAllPageSumItems.forEach(pItem => {
        thNum++
      });
      // 显示折前价合计的情况
      if (
        isShowSubProtocolOrgPriceTotal ||
        isShowSubProtocolOrgPriceTotalCapital ||
        isShowPreProtocolOrgPriceTotal ||
        isShowPreProtocolOrgPriceTotalCapital ||
        isShowActualPriceTotal ||
        isShowActualPriceTotalCapital ||
        isShowActualSubPriceExcludingTax ||
        isShowTaxRatePrice ||
        isShowDeliverReductionPrice ||
        isShowDeliverReductionPriceCapital ||
        isShowDeliveryTotalDiscountPrice ||
        isShowDeliveryTotalDiscountPriceCapital
      ) {
        thNum++
        // firstColumnColspan -= 2
      }
      if (isShowCurrentColumnTotal) {
        thNum++
      }
      if (isShowAllPageCommodityCount) {
        thNum++
      }
      let showColNum = columns.filter(col => col.hidden !== true).length;
      // 如果是多列模式,需要考虑中间有隐藏列的情况
      if (hasMutiCol(template.config)) {
        const copyColumns = [...columns];
        // 去除掉末尾的隐藏列
        while (copyColumns[copyColumns.length - 1].hidden) {
          copyColumns.pop();
        }
        showColNum = copyColumns.length;
      }
      let everyColspan = Math.floor(showColNum/thNum)
      let other = showColNum%thNum
      let firstColumnColspan = everyColspan+other

      // 繁体打印
      let traditionalPrint = mainData.is_hkd;
      let page = traditionalPrint
        ? __translate.ToTraditionalChinese('页')
        : '页';

      const renderAllPageCommodityCount = () => {
        let label = '所有页商品总数';
        if (Array.isArray(template.config.columnArea)) {
          template.config.columnArea.forEach(item => {
            if (item.key === 'is_show_all_page_commodity_count') {
              label = item.label;
            }
          });
        }
        return h('span', `${label}${mainData._commodity_count}`)
      }

      const renderCurrentPageCommodityCount = () => {
        const tindex = columns.findIndex(col => col.key === '_commodity_count') + 1;
        let countEl = h('span', {
          attrs: {
            tdata: 'SubSum',
            format: '0',
            tindex
          }
        }, '#');
        // 如果是多列
        const multiColSumEls = [];
        if (hasMutiCol(template.config)) {
          const multiColumnNum = +template.config.print_line.replace(/column_/, '');
          let tdataItems = [];
          for (let i = 0; i < multiColumnNum; i++) {
            const rowMoneyColumnIndex = columns.findIndex(col => col.key === `_commodity_count_${i}`);
            multiColSumEls.push(h('div', {
              style: {
                position: 'absolute',
                color: '#fff',
                zIndex: -1,
              },
              attrs: {
                tdata: 'SubSum',
                format: '0',
                id: `_commodity_count_${i}`,
                class: 'outTable',
                zIndex: -1,
                tindex: rowMoneyColumnIndex + 1
              }
            }, '######'));
            tdataItems.push(`_commodity_count_${i}`);
          }
          countEl = h('span', {
            attrs: {
              tdata: `( ${tdataItems.join('+')} )`,
              format: '0'
            }
          }, '#');
        }
        let label = '本页商品总数';
        if (Array.isArray(template.config.columnArea)) {
          template.config.columnArea.forEach(item => {
            if (item.key === 'is_show_current_page_commodity_count') {
              label = item.label;
            }
          });
        }
        return h('div', [
          h('span', label),
          countEl,
          ...multiColSumEls
        ]);
      }

      /**
       * 渲染当前列发货小计
       * 独占一行
       */
      function renderCurrentColumnTotal() {
        if (!hasMutiCol(template.config)) {
          return '';
        }
        const multiColumnNum = +template.config.print_line.replace(/column_/, '');
        const tds = [];
        const columnCount = columns.length;
        let label = '本列发货小计';
        if (Array.isArray(template.config.columnArea)) {
          template.config.columnArea.forEach(item => {
            if (item.key === 'is_show_current_column_total') {
              label = item.label;
            }
          });
        }
        for(let i = 0; i < multiColumnNum; i++) {
          const rowMoneyColumnIndex = columns.findIndex(col => col.key === `row_money_${i}`);
          tds.push(
            h(
              'td',
              {
                style: {
                  borderRight: borderStyle,
                  borderBottom: borderStyle,
                  ...cellStyle
                },
                attrs: {
                  colspan: columnCount / multiColumnNum
                }
              },
              [
                h(
                  'span',
                  {
                    style: {
                      textAlign: 'center'
                    }
                  },
                  label
                ),
                h(
                  'span',
                  {
                    style: {
                      textAlign: 'center'
                    },
                    attrs: {
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: rowMoneyColumnIndex + 1
                    }
                  },
                  '######'
                )
              ]
            )
          );
        }
        return h('tr', tds);
      }

      function rowMoney2SubSum({h,key,format,summary},show) {
        let SubSumformat = '0.00'
          if(!show){
            return ''
          }
          let columnNum = hasMutiCol(template.config)
          if(columnNum){
            const rowMoney2_0 = columns.findIndex(item => item.key === key+'_0');
            const rowMoney2_1 = columns.findIndex(item => item.key === key+'_1');
            const rowMoney2_2 = columns.findIndex(item => item.key === key+'_2');

            if(rowMoney2_0==-1){
              return ''
            }
            return h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: `(${key}_0+${key}_1+${key}_2)`,
                  format: format||SubSumformat,
                }
              },
              [h('div',{
                style:{position:'absolute',color:'#fff'},
                attrs: {
                  tdata: 'SubSum',
                  format: SubSumformat,
                  class:'outTable',
                  id:key+'_0',
                  tindex: rowMoney2_0 + 1
                }
              },'######'),
              rowMoney2_1>=0?h('div',{
                style:{position:'absolute',color:'#fff'},
                attrs: {
                  tdata: 'SubSum',
                  class:'outTable',
                  format: SubSumformat,
                  id:key+'_1',
                  tindex: rowMoney2_1 + 1
                }
              },'######'):'',
              rowMoney2_2>=0?h('div',{
                style:{position:'absolute',color:'#fff'},
                attrs: {
                  tdata: 'SubSum',
                  format: SubSumformat,
                  id:key+'_2',
                  tindex: rowMoney2_2 + 1
                }
              },'######'):'',
              format=='UpperMoney'?'#': summary + '#']
            )
          }else{
            return ''
          }
      }
      function isHasPage() {
        return (
          selectedAllPageSumItems.length > 0 ||
          isShowPageNo ||
          isShowOrderNo ||
          isCurrentPageActualAmount ||
          isCurrentPageOrderAmount ||
          isCurrentPageActualPrice ||
          isCurrentPageActualPriceLabel ||
          isCurrentPageActualPriceCapital ||
          isCurrentPageOrderPrice ||
          isCurrentPageOrderPriceCapital ||
          isAllPageOrderAmount ||
          isAllPageActualAmount ||
          isAllPageOrderPrice ||
          isAllPageOrderPriceCapital ||
          isAllPageActualPrice ||
          // isAllPageActualPriceSetting ||
          isAllPageActualPriceCapital ||
          isShowActualPriceTotal ||
          isShowActualPriceTotalCapital||
          isShowActualSubPriceExcludingTax ||
          isShowTaxRatePrice ||
          isShowPreProtocolOrgPriceTotalCapital ||
          isShowPreProtocolOrgPriceTotal ||
          isShowSubProtocolOrgPriceTotalCapital ||
          isShowSubProtocolOrgPriceTotal ||
          isShowDeliverReductionPrice ||
          isShowDeliverReductionPriceCapital ||
          isShowDeliveryTotalDiscountPrice ||
          isShowDeliveryTotalDiscountPriceCapital ||
          isShowCurrentPageCommodityCount ||
          isShowAllPageCommodityCount
        );
      }
      // 获取每栏显示的字段
      function getTdList(lineType,list) {
        let data = []
        if ((rowNum > 0 && hasMutiCol(template.config))||template.config.share) {
          let rowOrderNumSum = 0
          let rowNumSum = 0
          let rowOrderMoneySum = 0
          let rowMoneySum = 0
          let rowMoneySumCaption = '';
          let rowMoneyOrderSumCaption = '';
          let rowProtocolSum = 0
          let rowActualSum = 0
          let rowSubProtocolOrgPrice = 0
          let rowActualSumExcludingTax = 0
          let rowTaxRatePrice = 0
          let rowDeliverReductionPrice = 0
          let rowDeliveryTotalDiscountPrice = 0
          rows.forEach(row => {
            Object.keys(row).forEach(key => {
              if (template.config.share?key.match(/amount$/):key.match(/^amount_\d.*/)) {
                rowOrderNumSum += +row[key];
              }
              if (template.config.share?key.match(/actual_amount$/):key.match(/^actual_amount_\d.*/)) {
                rowNumSum += +row[key];
              }
              if (template.config.share?key.match(/row_money$/):key.match(/row_money_.*/)) {
                rowMoneySum += +row[key];
              }
              if (template.config.share?key.match(/row_money2$/):key.match(/row_money2_.*/)) {
                rowOrderMoneySum += +row[key];
              }
              if (key.match(/((^protocol_org_price_cur_page_total$)|(^protocol_org_price_cur_page_total_\d+))/)) {
                rowProtocolSum += row[key] === '时价' ? 0 : +row[key];
              }
              if (key.match(/((^actual_sub_price$)|(^actual_sub_price_\d+))/)) {
                rowActualSum += row[key] ? +row[key] : 0;
              }
              if (key.match(/((^sub_protocol_org_price$)|(^sub_protocol_org_price_\d+))/)) {
                rowSubProtocolOrgPrice += row[key] ? +row[key] : 0;
              }
              if (key.match(/((^actual_sub_price_excluding_tax$)|(^actual_sub_price_excluding_tax_\d+))/)) {
                rowActualSumExcludingTax += row[key] ? +row[key] : 0;
              }
              if (key.match(/((^tax_rate_price$)|(^tax_rate_price_\d+))/)) {
                rowTaxRatePrice += row[key] ? +row[key] : 0;
              }
              if (key.match(/((^deliver_reduction_price$)|(^deliver_reduction_price_\d+))/)) {
                rowDeliverReductionPrice  += row[key] ? +row[key] : 0;
              }
              if (key.match(/((^delivery_total_discount_price$)|(^delivery_total_discount_price_\d+))/)) {
                rowDeliveryTotalDiscountPrice += row[key] ? +row[key] : 0;
              }
            });
          });
          rowActualSum = rowActualSum.toFixed(2);
          rowSubProtocolOrgPrice = rowSubProtocolOrgPrice.toFixed(2);
          rowNumSum = rowNumSum.toFixed(2);
          rowOrderNumSum = rowOrderNumSum.toFixed(2);
          rowMoneySum = rowMoneySum.toFixed(2);
          rowOrderMoneySum = rowOrderMoneySum.toFixed(2);
          if(store.state.sysConfig.delivery_total_decimal==0){
            rowMoneySum = rowMoneySum.toFixed(0);
            rowOrderMoneySum = rowOrderMoneySum.toFixed(0);
          }else if(store.state.sysConfig.delivery_total_decimal==1) {
            rowMoneySum = rowMoneySum.toFixed(1);
            rowOrderMoneySum = rowOrderMoneySum.toFixed(1);
          }else if(store.state.sysConfig.delivery_total_decimal==2) {
            rowMoneySum = rowMoneySum.toFixed(2);
            rowOrderMoneySum = rowOrderMoneySum.toFixed(2);
          }
          rowMoneySumCaption = num2RmbCaption(rowMoneySum);
          rowMoneyOrderSumCaption = num2RmbCaption(rowOrderMoneySum);
          // if (isCurrentPageActualPriceLabel) {
          //   rowMoneySum = summary + rowMoneySum;
          // }
          lineType&&lineType.forEach(line=>{
          let lineItem = list.find(e=>e.key==line)
          if (line === 'is_show_current_page_commodity_count') {
            data.push(renderCurrentPageCommodityCount());
          }
          if (line === 'is_show_all_page_commodity_count') {
            data.push(renderAllPageCommodityCount());
          }
          if(line=='is_show_order_no'){
            data.push(h('span', mainData.order_no + ' '))
          }else if(line=='is_show_page_no'){
            data.push(h('span',
            {
              attrs: {
                tdata: 'pageNo'
              }
            },
              '第#' + page
            ))
            data.push(h('span',
              {
                attrs: {
                  tdata: 'pageCount'
                }
              },
              ' / 共#' + page
            ))
          }else if(line=='is_show_order_num_total'){
            data.push(h(
              'div',
              {},
              lineItem.labelShow + rowOrderNumSum
            ))
          }else if(line=='is_show_num_total') {
            data.push(h(
              'div',
              {},
              lineItem.labelShow + rowNumSum
            ))
          }else if(line=='is_show_order_total') {
            data.push(h(
              'div',
              {},
              lineItem.labelShow + rowOrderMoneySum
            ))
          }else if(line=='is_show_order_total_capital'){
            data.push(h(
              'div',
              {},
              rowMoneyOrderSumCaption
            ))
          }else if(line=='is_show_order_total_capital'){
            data.push(h('div', {}, rowMoneyOrderSumCaption))
          }else if(line=='is_show_pre_total'){
            data.push(h('div', {}, lineItem.labelShow + rowMoneySum))
          }else if(line=='is_show_pre_total_capital'){
            data.push(h('div', {}, rowMoneySumCaption))
          }else if(line=='is_show_pre_protocol_org_price_total'){
            data.push(h('div', {}, lineItem.labelShow + rowProtocolSum))
          }else if(line=='is_show_pre_protocol_org_price_total_capital'){
            data.push(h('div', {}, `${num2RmbCaption(rowProtocolSum)}`))
          }else if(line=='is_show_actual_sub_price_total'){
            data.push(h('div', {}, lineItem.labelShow + `${rowActualSum}`))
          }else if(line=='is_show_actual_sub_price_total_capital'){
            data.push(h('div', {}, `${num2RmbCaption(rowActualSum)}`))
          }else if(line=='is_show_sub_protocol_org_price_total'){
            data.push(h('div', {}, lineItem.labelShow + `${rowSubProtocolOrgPrice}`))
          }else if(line=='is_show_sub_protocol_org_price_total_capital'){
            data.push(h('div', {}, `${num2RmbCaption(rowSubProtocolOrgPrice)}`))
          }else if(line=='is_show_order_total_amount'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.order_total_amount}`))
          }else if(line=='is_show_all_num_total'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.total_actual_amount}`))
          }else if(line=='is_show_all_total_price'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.price}`))
          }else if(line=='is_show_all_total_price_capital') {
            data.push(h('div', num2RmbCaption(mainData.price)))
          }else if(line=='is_show_all_total'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.actual_price}`))
          }else if(line=='is_show_all_total_capital') {
            data.push(h('div', num2RmbCaption(mainData.actual_price)))
          }else if(line.includes('customFieldTextKey')) {
            data.push(h('div', {}, lineItem.labelShow))
          }else if(line=='is_show_actual_sub_price_excluding_tax'){
            data.push(h('div', {}, lineItem.labelShow + `${rowActualSumExcludingTax}`))
          }else if(line=='is_show_order_tax_rate_price'){
            data.push(h('div', {}, lineItem.labelShow + `${rowTaxRatePrice}`))
          }else if(line === 'is_show_deliver_reduction_price'){
            data.push(h('div', {}, lineItem.labelShow + `${rowDeliverReductionPrice}`))
          }else if(line === 'is_show_deliver_reduction_price_capital'){
            data.push(h('div', {}, `${num2RmbCaption(rowDeliverReductionPrice)}`))
          } else if(line === 'is_show_delivery_total_discount_price'){
            data.push(h('div', {}, lineItem.labelShow + `${rowDeliveryTotalDiscountPrice}`))
          } else if(line === 'is_show_delivery_total_discount_price_capital'){
            data.push(h('div', {}, `${num2RmbCaption(rowDeliveryTotalDiscountPrice)}`))
          }
          console.log('000000000000000000000000000000000000000000000000000000000000000',)
          selectedAllPageSumItems.forEach(item=>{
            if(item.key === line){
              data.push(h('div', {},  `${labelMap[line] || item.label}${mainData[item.dataKey]}`))
            }
          })
        })
        return data
      }else{
        let SubSumformat = '0.00'
      lineType&&lineType.forEach(line=>{
          let lineItem = list.find(e=>e.key==line)
          if (line === 'is_show_current_page_commodity_count') {
            data.push(renderCurrentPageCommodityCount());
          }
          if (line === 'is_show_all_page_commodity_count') {
            data.push(renderAllPageCommodityCount());
          }
          if(line=='is_show_order_no'){
            data.push(h('span', mainData.order_no + ' '))
          }else if(line=='is_show_page_no'){
            data.push(h('span',
            {
              attrs: {
                tdata: 'pageNo'
              }
            },
              '第#' + page
          ))
            data.push(h('span',
              {
                attrs: {
                  tdata: 'pageCount'
                }
              },
              ' / 共#' + page
            ))
          }else if(line=='is_show_order_num_total'){
            data.push(+orderNumColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: orderNumColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                )
              : rowMoney2SubSum({h,key:'amount',format: '0.00',summary:lineItem.labelShow},true))
          }else if(line=='is_show_num_total') {
            data.push(+actualNumColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: actualNumColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                )
                // 发货数量本页小计
              : rowMoney2SubSum({h,key:'actual_amount',format: '0.00',summary:lineItem.labelShow},true))
          }else if(line=='is_show_order_total') {
            data.push( +orderPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: SubSumformat,
                      tindex: orderPriceColumnIndex + 1
                    }
                  },
                  lineItem.labelShow + '#'
                )
              : rowMoney2SubSum({h,key:'row_money2',summary:lineItem.labelShow},true))
          }else if(line=='is_show_order_total_capital'){
            data.push( +orderPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: orderPriceColumnIndex + 1
                    }
                  },
                  '#'
                )
              : rowMoney2SubSum({h,key:'row_money2',format:'UpperMoney',summary:''},true))
          }else if(line=='is_show_pre_total'){
            data.push(+actualPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: actualPriceColumnIndex + 1
                    }
                  },
                  (isCurrentPageActualPriceLabel ? lineItem.labelShow : '') + '#'
                )
                // 发货金额本页小计
              : rowMoney2SubSum({h,key:'row_money',format:SubSumformat,summary:(isCurrentPageActualPriceLabel ? lineItem.labelShow : '')},true))
          }else if(line=='is_show_pre_total_capital'){
            data.push(+actualPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: actualPriceColumnIndex + 1
                    }
                  },
                  '#'
                )
              :  rowMoney2SubSum({h,key:'row_money',format:'UpperMoney',summary:''},true))
          }else if(line=='is_show_pre_protocol_org_price_total'){
            data.push(+protocolOrgPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: protocolOrgPriceColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                ):rowMoney2SubSum({h,key:'protocol_org_price_cur_page_total',format: '0.00',summary:lineItem.labelShow},true))
          }else if(line=='is_show_pre_protocol_org_price_total_capital'){
            data.push(+protocolOrgPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: protocolOrgPriceColumnIndex + 1
                    }
                  }, '#'
                ):rowMoney2SubSum({h,key:'protocol_org_price_cur_page_total',format: 'UpperMoney',summary:''},true))
          }else if(line=='is_show_actual_sub_price_total'){
            data.push(h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: actualColumnIndex + 1
                }
              }, lineItem.labelShow + '#'
            ))
          }else if(line=='is_show_actual_sub_price_total_capital'){
            data.push(h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: 'UpperMoney',
                  tindex: actualColumnIndex + 1
                }
              }, '#'
            ))
          }else if(line=='is_show_sub_protocol_org_price_total'){
            data.push(h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: subProtocolOrgPriceColumnIndex + 1
                }
              }, lineItem.labelShow + '#'
            ))
          }else if(line=='is_show_sub_protocol_org_price_total_capital'){
            data.push(h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: 'UpperMoney',
                  tindex: subProtocolOrgPriceColumnIndex + 1
                }
              }, '#'
            ))
          }else if(line=='is_show_order_total_amount'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.order_total_amount}`))
          }else if(line=='is_show_all_num_total'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.total_actual_amount}`))
          }else if(line=='is_show_all_total_price'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.price}`))
          }else if(line=='is_show_all_total_price_capital') {
            data.push(h('div', num2RmbCaption(mainData.price)))
          }else if(line=='is_show_all_total'){
            data.push(h('div', {}, lineItem.labelShow + `${mainData.actual_price}`))
          }else if(line=='is_show_all_total_capital') {
            data.push(h('div', num2RmbCaption(mainData.actual_price)))
          }else if(line.includes('customFieldTextKey')) {
            data.push(h('div', {}, lineItem.labelShow))
          }else if(line=='is_show_actual_sub_price_excluding_tax'){
            data.push(+actualSubPriceExcludingTaxColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: actualSubPriceExcludingTaxColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                ):rowMoney2SubSum({h,key:'actual_sub_price_excluding_tax',format: '0.00',summary:lineItem.labelShow},true))
          }else if(line=='is_show_order_tax_rate_price'){
            data.push(+taxRatePriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: taxRatePriceColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                ):rowMoney2SubSum({h,key:'tax_rate_price',format: '0.00',summary:lineItem.labelShow},true))
          } else if(line=='is_show_deliver_reduction_price'){
            data.push(+deliverReductionPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: deliverReductionPriceColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                ):rowMoney2SubSum({h,key:'deliver_reduction_price',format: '0.00',summary:lineItem.labelShow},true))
          } else if(line=='is_show_deliver_reduction_price_capital'){
            data.push(+deliverReductionPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: deliverReductionPriceColumnIndex + 1
                    }
                  }, '#'
                ):rowMoney2SubSum({h,key:'deliver_reduction_price',format: 'UpperMoney',summary:''},true))
          } else if(line=='is_show_delivery_total_discount_price'){
            data.push(+deliverTotalDiscountPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: deliverTotalDiscountPriceColumnIndex + 1
                    }
                  }, lineItem.labelShow + '#'
                ):rowMoney2SubSum({h,key:'delivery_total_discount_price',format: '0.00',summary:lineItem.labelShow},true))
          } else if(line=='is_show_delivery_total_discount_price_capital'){
            data.push(+deliverTotalDiscountPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: deliverTotalDiscountPriceColumnIndex + 1
                    }
                  }, '#'
                ):rowMoney2SubSum({h,key:'delivery_total_discount_price',format: 'UpperMoney',summary:''},true))
          }
          selectedAllPageSumItems.forEach(item=>{
            if(item.key === line){
              data.push(h('div', {},  `${labelMap[line] || item.label}${mainData[item.dataKey]}`))
            }
          });
        })
        return data
      }

      }
      if(template.config.customFieldArea){
        const renderPaginationTr = (customField) => {
          let tds = [];
          customField.forEach(field=>{
            let list = getTdList(field.lineType,[...template.config.areaList, ...(template.config.customFieldText || [])])
            tds.push( h(
                'td',
                {
                  attrs: {
                    colspan: field.lineSum
                  },
                  style: {
                    borderRight: borderStyle,
                    borderBottom: borderStyle,
                    ...cellStyle
                  }
                },
                list
              )
           )
          })
          return h('tr', tds);
        }
        const customFieldOnlyInLastPage = template.config.customFieldOnlyInLastPage;
        const customField = template.config.customField.filter((_, index) => {
          if (customFieldOnlyInLastPage && customFieldOnlyInLastPage[index] === true) {
            return false;
          }
          return true;
        });
        if (pageInfoInAllPage && customField) {
          // 多行模式
          if (Array.isArray(customField[0])) {
            const copyColumns = [...columns];
            // 去除掉末尾的隐藏列
            while (copyColumns[copyColumns.length - 1].hidden) {
              copyColumns.pop();
            }
            customField.forEach((field) => {
              let totalLineSum = field.reduce((prev, next) => prev + next.lineSum, 0);
              if (totalLineSum < copyColumns.length) {
                field[field.length - 1].lineSum += copyColumns.length - totalLineSum;
              }
              const isCurrentColumnTotalRow = field.some((item) => item.lineType.includes('is_show_current_column_total'));
              if (isCurrentColumnTotalRow) {
                footer.push(renderCurrentColumnTotal());
              } else {
                footer.push(renderPaginationTr(field));
              }
            });
          } else {
            footer.push(renderPaginationTr(customField));
          }
        }
        if (inAllPageItems && inAllPageItems.includes('_receiver_sign')) {
          footer.push(
            _addReceiverSign({
              h,
              columns,
              borderStyle,
              cellStyle,
              traditionalPrint
            })
          );
        }
        return footer;
      } else {

      const tds = [
        h(
          'td',
          {
            attrs: {
              colspan: firstColumnColspan
            },
            style: {
              borderRight: borderStyle,
              borderBottom: borderStyle,
              ...cellStyle
            }
          },
          [
            isShowOrderNo ? h('span', mainData.order_no + ' ') : '',
            isShowPageNo
              ? h(
                  'span',
                  {
                    attrs: {
                      tdata: 'pageNo'
                    }
                  },
                  '第#' + page
                )
              : '',
            isShowPageNo
              ? h(
                  'span',
                  {
                    attrs: {
                      tdata: 'pageCount'
                    }
                  },
                  ' / 共#' + page
                )
              : ''
          ]
        )
      ];
      let thisSummary = '本页合计'
      let thisOrderSummary = '本页下单金额小计'
      let thisOrderGoods = '本页下单数量小计'
      let thisGoods = '本页发货数量小计'
      let deliverTotal = '发货数量总计'
      let orderTotal = '下单数量总计'
      let allOrderPriceTotal = '总计'
      let allTotal = '总计'
      let actualPrice = '本页实际金额(小写)'
      let subProtocalOrgPrice = '本页下单折前小价(小写)'
      let protocolSummary = '折前价：'
      let actualSumExcludingTaxPre = '本页实际金额(不含税)小计'
      let orderTaxRatePricePre = '本页税额小计'
      let deliverReductionPricePre = '本页抹零金额合计'
      let deliveryTotalDiscountPricePre = '本页合计发货折扣金额'
      columnArea.forEach(res=>{
        // 本页合计
        if(res.key=='is_show_pre_total'){
          thisSummary = res.labelShow
        }
        // 本页下单金额合计
        if(res.key=='is_show_order_total'){
          thisOrderSummary = res.labelShow
        }
        // 本页发货数量小计
        if(res.key == 'is_show_num_total'){
          thisGoods = res.labelShow
        }
        // 本页下单数量小计
        if(res.key == 'is_show_order_num_total'){
          thisOrderGoods = res.labelShow
        }
        // 下单数量总计
        if(res.key == 'is_show_order_total_amount'){
          orderTotal = res.labelShow
        }
        // 下单金额总计
        if(res.key == 'is_show_all_total_price'){
          allOrderPriceTotal = res.labelShow
        }
        // 发货数量总计
        if(res.key == 'is_show_all_num_total'){
          deliverTotal = res.labelShow
        }
                 // 发货数量总计
        if(res.key == 'is_show_all_total'){
          allTotal = res.labelShow
        }
        if(res.key == 'is_show_actual_sub_price_total'){
          actualPrice = res.labelShow
        }
        if(res.key == 'is_show_sub_protocol_org_price_total'){
          subProtocalOrgPrice = res.labelShow
        }
        if(res.key == 'is_show_pre_protocol_org_price_total') {
          protocolSummary = res.labelShow
        }
        // 本页实际金额(不含税)小计
        if(res.key == 'is_show_actual_sub_price_excluding_tax') {
          actualSumExcludingTaxPre = res.labelShow
        }
        // 本页税额小计
        if(res.key == 'is_show_order_tax_rate_price') {
          orderTaxRatePricePre = res.labelShow
        }
        // 本页抹零金额合计
        if(res.key == 'is_show_deliver_reduction_price') {
          deliverReductionPricePre = res.labelShow
        }
        if(res.key == 'is_show_delivery_total_discount_price') {
          deliveryTotalDiscountPricePre = res.labelShow
        }
      })

      // 显示大写时，不显示label
      if (isShowDeliverReductionPriceCapital) {
        deliverReductionPricePre = '';
      }

      // 显示大写时，不显示label
      if (isShowDeliveryTotalDiscountPriceCapital) {
        deliveryTotalDiscountPricePre = '';
      }

      if (
        isCurrentPageActualPrice ||isCurrentPageActualPriceCapital || isCurrentPageOrderPrice || isCurrentPageOrderPriceCapital || isCurrentPageActualAmount || isCurrentPageOrderAmount) {
        let summary = traditionalPrint ? __translate.ToTraditionalChinese(thisSummary) : thisSummary;
        let orderSummary = traditionalPrint ? __translate.ToTraditionalChinese(thisOrderSummary) : thisOrderSummary;
        let numSummary = traditionalPrint ? __translate.ToTraditionalChinese(thisGoods) : thisGoods;
        let orderNumSummary = traditionalPrint ? __translate.ToTraditionalChinese(thisOrderGoods) : thisOrderGoods;
        let pageActualPriceList = [];
        // 开启了双列打印，并且设置了每页行数的情况下，本页小计动态计算
        if ((rowNum > 0 && hasMutiCol(template.config))||template.config.share) {
          let rowMoneySum = 0;
          let rowOrderMoneySum = 0;
          let rowMoneySumCaption = '';
          let rowMoneyOrderSumCaption = '';
          let rowNumSum = 0;
          let rowOrderNumSum = 0;
          if (
            isCurrentPageActualPrice ||
            isCurrentPageActualPriceCapital ||
            isCurrentPageOrderPrice ||
            isCurrentPageOrderPriceCapital ||
            isShowDeliverReductionPrice ||
            isShowDeliverReductionPriceCapital ||
            isShowDeliveryTotalDiscountPrice ||
            isShowDeliveryTotalDiscountPriceCapital
          ) {
            rows.forEach(row => {
              Object.keys(row).forEach(key => {
                if (template.config.share?key.match(/row_money$/):key.match(/row_money_.*/)) {
                  rowMoneySum += +row[key];
                }
              });
              Object.keys(row).forEach(key => {
                if (template.config.share?key.match(/row_money2$/):key.match(/row_money2_.*/)) {
                  rowOrderMoneySum += +row[key];
                }
              });
            });
            rowMoneySum = rowMoneySum.toFixed(2);
            rowOrderMoneySum = rowOrderMoneySum.toFixed(2);
            if(store.state.sysConfig.delivery_total_decimal==0){
              rowMoneySum = rowMoneySum.toFixed(0);
              rowOrderMoneySum = rowOrderMoneySum.toFixed(0);
            }else if(store.state.sysConfig.delivery_total_decimal==1) {
              rowMoneySum = rowMoneySum.toFixed(1);
              rowOrderMoneySum = rowOrderMoneySum.toFixed(1);
            }else if(store.state.sysConfig.delivery_total_decimal==2) {
              rowMoneySum = rowMoneySum.toFixed(2);
              rowOrderMoneySum = rowOrderMoneySum.toFixed(2);
            }
            rowMoneySumCaption = num2RmbCaption(rowMoneySum);
            rowMoneyOrderSumCaption = num2RmbCaption(rowOrderMoneySum);

            rowOrderMoneySum = orderSummary + rowOrderMoneySum;
            if (isCurrentPageActualPriceLabel) {
              rowMoneySum = summary + rowMoneySum;
            }
          }
          if (isCurrentPageActualAmount) {
            rows.forEach(row => {
              Object.keys(row).forEach(key => {
                if (template.config.share?key.match(/actual_amount$/):key.match(/^actual_amount_\d.*/)) {
                  rowNumSum += +row[key];
                }
              });
            });
            rowNumSum = rowNumSum.toFixed(2);
          }
          if (isCurrentPageOrderAmount) {
            rows.forEach(row => {
              Object.keys(row).forEach(key => {
                if (template.config.share?key.match(/amount$/):key.match(/^amount_\d.*/)) {
                  rowOrderNumSum += +row[key];
                }
              });
            });
            rowOrderNumSum = rowOrderNumSum.toFixed(2);
          }
          pageActualPriceList = [
            isCurrentPageOrderPrice
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  rowOrderMoneySum
                )
              : '',
              isCurrentPageOrderPriceCapital
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  rowMoneyOrderSumCaption
                )
              : '',
            isCurrentPageActualPrice
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  rowMoneySum
                )
              : '',
            isCurrentPageActualPriceCapital
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  rowMoneySumCaption
                )
              : '',
              isCurrentPageOrderAmount
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  orderNumSummary + rowOrderNumSum
                )
              : '',
            isCurrentPageActualAmount
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3
                    }
                  },
                  numSummary + rowNumSum
                )
              : '',
          ];
        } else {
          // 根据配置项显示几位小数
          let SubSumformat = '0.00'
          pageActualPriceList = [
            // 下单金额小计
            isCurrentPageOrderPrice && +orderPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: SubSumformat,
                      tindex: orderPriceColumnIndex + 1
                    }
                  },
                  orderSummary + '#'
                )
              : rowMoney2SubSum({h,key:'row_money2',summary:orderSummary},isCurrentPageOrderPrice),
              // 下单金额大写合计
              isCurrentPageOrderPriceCapital && +orderPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: orderPriceColumnIndex + 1
                    }
                  },
                  '#'
                )
              : rowMoney2SubSum({h,key:'row_money2',format:'UpperMoney',summary:''},isCurrentPageOrderPriceCapital),
              // 发货金额
            isCurrentPageActualPrice && +actualPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: SubSumformat,
                      tindex: actualPriceColumnIndex + 1
                    }
                  },
                  (isCurrentPageActualPriceLabel ? summary : '') + '#'
                )
                // 发货金额本页小计
              : rowMoney2SubSum({h,key:'row_money',format:SubSumformat,summary:(isCurrentPageActualPriceLabel ? summary : '')},isCurrentPageActualPrice),
            isCurrentPageActualPriceCapital && +actualPriceColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: actualPriceColumnIndex + 1
                    }
                  },
                  '#'
                )
              :  rowMoney2SubSum({h,key:'row_money',format:'UpperMoney',summary:''},isCurrentPageActualPriceCapital),
            isCurrentPageOrderAmount && +orderNumColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: orderNumColumnIndex + 1
                    }
                  }, orderNumSummary + '#'
                )
              : rowMoney2SubSum({h,key:'amount',format: '0.00',summary:orderNumSummary},isCurrentPageOrderAmount),
            isCurrentPageActualAmount && +actualNumColumnIndex >= 0
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: actualNumColumnIndex + 1
                    }
                  }, numSummary + '#'
                )
                // 发货数量本页小计
              : rowMoney2SubSum({h,key:'actual_amount',format: '0.00',summary:numSummary},isCurrentPageActualAmount),
          ];
        }
        tds.push(
          h(
            'td',
            {
              style: {
                borderRight: borderStyle,
                borderBottom: borderStyle,
                ...cellStyle
              },
              attrs: {
                colspan: everyColspan || 3
              }
            },
            pageActualPriceList
          )
        );
      }
      // 折前价,只有在设置了本页固定行数的情况下才展示 现在都展示
      if (
        isShowSubProtocolOrgPriceTotal ||
        isShowSubProtocolOrgPriceTotalCapital ||
        isShowPreProtocolOrgPriceTotal ||
        isShowPreProtocolOrgPriceTotalCapital ||
        isShowActualPriceTotal ||
        isShowActualPriceTotalCapital ||
        isShowActualSubPriceExcludingTax ||
        isShowTaxRatePrice ||
        isShowDeliverReductionPrice ||
        isShowDeliverReductionPriceCapital ||
        isShowDeliveryTotalDiscountPrice ||
        isShowDeliveryTotalDiscountPriceCapital
      ) {
        let list = [];
        if(rowNum > 0) {
        let rowMoneySum = 0;
        console.log('rows---', rows)
        rows.forEach(row => {

          Object.keys(row).forEach(key => {
            if (key.match(/((^protocol_org_price_cur_page_total$)|(^protocol_org_price_cur_page_total_\d+))/)) {
              rowMoneySum += row[key] === '时价' ? 0 : +row[key];
            }
          });
        })
        let rowActualSum = 0,
          rowActualSumExcludingTax = 0,
          rowTaxRatePrice = 0,
          rowSubProtocolOrgPrice = 0,
          rowDeliverReductionPrice = 0,
          rowDeliveryTotalDiscountPrice = 0;
        rows.forEach(row => {
          Object.keys(row).forEach(key => {
            if (key.match(/((^actual_sub_price$)|(^actual_sub_price_\d+))/)) {
              rowActualSum += row[key] ? +row[key] : 0;
            }
            if (key.match(/((^sub_protocol_org_price)|(^sub_protocol_org_price_\d+))/)) {
              rowSubProtocolOrgPrice += row[key] ? +row[key] : 0;
            }
            if (key.match(/((^actual_sub_price_excluding_tax$)|(^actual_sub_price_excluding_tax_\d+))/)) {
              rowActualSumExcludingTax += row[key] ? +row[key] : 0;
            }
            if (key.match(/((^tax_rate_price$)|(^tax_rate_price_\d+))/)) {
              rowTaxRatePrice += row[key] ? +row[key] : 0;
            }
            if (key.match(/((^deliver_reduction_price$)|(^deliver_reduction_price_\d+))/)) {
              rowDeliverReductionPrice += row[key] ? +row[key] : 0;
            }
            if (key.match(/((^delivery_total_discount_price$)|(^delivery_total_discount_price_\d+))/)) {
              rowDeliveryTotalDiscountPrice += row[key] ? +row[key] : 0;
            }
          });
        })
          rowMoneySum = rowMoneySum.toFixed(2);
          if (isShowPreProtocolOrgPriceTotal) {
            list.push(h('div', `${protocolSummary}${rowMoneySum}`));
          }
          if (isShowPreProtocolOrgPriceTotalCapital) {
            list.push(h('div', `${num2RmbCaption(rowMoneySum)}`));
          }
          rowSubProtocolOrgPrice = rowSubProtocolOrgPrice.toFixed(2);
          if (isShowSubProtocolOrgPriceTotal) {
            list.push(h('div', `${subProtocalOrgPrice}${rowSubProtocolOrgPrice}`));
          }
          if (isShowSubProtocolOrgPriceTotalCapital) {
            list.push(h('div', `${num2RmbCaption(rowSubProtocolOrgPrice)}`));
          }
          rowActualSum = rowActualSum.toFixed(2);
          if (isShowActualPriceTotal) {
            list.push(h('div', `${actualPrice}${rowActualSum}`));
          }
          if (isShowActualPriceTotalCapital) {
            list.push(h('div', `${num2RmbCaption(rowActualSum)}`));
          }
          rowActualSumExcludingTax = rowActualSumExcludingTax.toFixed(2);
          if(isShowActualSubPriceExcludingTax) {
            list.push(h('div', `${actualSumExcludingTaxPre}${rowActualSumExcludingTax}`));
          }
          if(isShowTaxRatePrice) {
            list.push(h('div', `${orderTaxRatePricePre}${rowTaxRatePrice}`));
          }
          if(isShowDeliverReductionPrice) {
            list.push(h('div', `${deliverReductionPricePre}${rowDeliverReductionPrice}`));
          }
          if (isShowDeliverReductionPriceCapital) {
            list.push(h('div', `${deliverReductionPricePre}${num2RmbCaption(rowDeliverReductionPrice)}`));
          }
          if(isShowDeliveryTotalDiscountPrice) {
            list.push(h('div', `${deliveryTotalDiscountPricePre}${rowDeliveryTotalDiscountPrice}`));
          }
          if(isShowDeliveryTotalDiscountPriceCapital) {
            list.push(h('div', `${deliveryTotalDiscountPricePre}${num2RmbCaption(rowDeliveryTotalDiscountPrice)}`));
          }
        }else{
          list =[
          isShowPreProtocolOrgPriceTotal && +protocolOrgPriceColumnIndex >= 0
          ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: protocolOrgPriceColumnIndex + 1
                }
              }, protocolSummary + '#'
            ):rowMoney2SubSum({h,key:'protocol_org_price_cur_page_total',format: '0.00',summary:protocolSummary},isShowPreProtocolOrgPriceTotal),
            isShowPreProtocolOrgPriceTotalCapital && +protocolOrgPriceColumnIndex >= 0
            ? h(
                'div',
                {
                  attrs: {
                    colspan: 3,
                    tdata: 'SubSum',
                    format: 'UpperMoney',
                    tindex: protocolOrgPriceColumnIndex + 1
                  }
                }, '#'
              ):rowMoney2SubSum({h,key:'protocol_org_price_cur_page_total',format: 'UpperMoney',summary:''},isShowPreProtocolOrgPriceTotalCapital),
              isShowActualPriceTotal
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: actualColumnIndex + 1
                    }
                  }, actualPrice + '#'
                ):rowMoney2SubSum({h,key:'actual_sub_price',format: '0.00',summary:actualPrice},isShowActualPriceTotal),
                isShowActualPriceTotalCapital
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: actualColumnIndex + 1
                    }
                  }, '#'
                ):rowMoney2SubSum({h,key:'actual_sub_price',format: 'UpperMoney',summary:''},isShowActualPriceTotalCapital),
                isShowSubProtocolOrgPriceTotal
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: '0.00',
                      tindex: subProtocolOrgPriceColumnIndex + 1
                    }
                  }, subProtocalOrgPrice + '#'
                ):rowMoney2SubSum({h,key:'sub_protocol_org_price',format: '0.00',summary:subProtocalOrgPrice},isShowSubProtocolOrgPriceTotal),
                isShowSubProtocolOrgPriceTotalCapital
              ? h(
                  'div',
                  {
                    attrs: {
                      colspan: 3,
                      tdata: 'SubSum',
                      format: 'UpperMoney',
                      tindex: subProtocolOrgPriceColumnIndex + 1
                    }
                  }, '#'
                ):rowMoney2SubSum({h,key:'sub_protocol_org_price',format: 'UpperMoney',summary:''},isShowSubProtocolOrgPriceTotalCapital),
                isShowActualSubPriceExcludingTax
              ? h(
                'div',
                {
                  attrs: {
                    colspan: 3,
                    tdata: 'SubSum',
                    format: '0.00',
                    tindex: actualSubPriceExcludingTaxColumnIndex + 1
                  }
                }, actualSumExcludingTaxPre + '#'
              ):rowMoney2SubSum({h,key:'actual_sub_price_excluding_tax',format: '0.00',summary:''},isShowActualSubPriceExcludingTax),
              isShowTaxRatePrice
            ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: taxRatePriceColumnIndex + 1
                }
              }, orderTaxRatePricePre + '#'
            ):rowMoney2SubSum({h,key:'tax_rate_price',format: '0.00',summary:''},isShowTaxRatePrice),
            isShowDeliverReductionPrice && deliverReductionPriceColumnIndex >=0 ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: deliverReductionPriceColumnIndex + 1
                }
              }, deliverReductionPricePre + '#'
            ):rowMoney2SubSum({h,key:'deliver_reduction_price',format: '0.00',summary:deliverReductionPricePre},isShowDeliverReductionPrice),
            isShowDeliverReductionPriceCapital && deliverReductionPriceColumnIndex >=0 ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: 'UpperMoney',
                  tindex: deliverReductionPriceColumnIndex + 1
                }
              }, deliverReductionPricePre + '#'
            ):rowMoney2SubSum({h,key:'deliver_reduction_price',format: 'UpperMoney',summary:deliverReductionPricePre},isShowDeliverReductionPriceCapital),
            isShowDeliveryTotalDiscountPrice && deliverTotalDiscountPriceColumnIndex >=0 ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: '0.00',
                  tindex: deliverTotalDiscountPriceColumnIndex + 1
                }
              }, deliveryTotalDiscountPricePre + '#'
            ):rowMoney2SubSum({h,key:'delivery_total_discount_price',format: '0.00',summary:deliveryTotalDiscountPricePre},isShowDeliveryTotalDiscountPrice),
            isShowDeliveryTotalDiscountPriceCapital && deliverTotalDiscountPriceColumnIndex >=0 ? h(
              'div',
              {
                attrs: {
                  colspan: 3,
                  tdata: 'SubSum',
                  format: 'UpperMoney',
                  tindex: deliverTotalDiscountPriceColumnIndex + 1
                }
              }, deliveryTotalDiscountPricePre + '#'
            ):rowMoney2SubSum({h,key:'delivery_total_discount_price',format: 'UpperMoney',summary:deliveryTotalDiscountPricePre},isShowDeliveryTotalDiscountPriceCapital),
          ]
        }
        tds.push(
          h(
            'td',
            {
              style: {
                borderRight: borderStyle,
                borderBottom: borderStyle,
                ...cellStyle
              },
              attrs: {
                colspan:everyColspan || 2
              }
            },
            list
          )
        );
      }
      if (isAllPageActualPriceCapital || isAllPageActualPrice || isAllPageOrderPrice || isAllPageOrderPriceCapital ||  isAllPageActualAmount || isAllPageOrderAmount) {
        const allSummary = traditionalPrint ? __translate.ToTraditionalChinese(allTotal): allTotal;
        const allOrderPriceSummary = traditionalPrint ? __translate.ToTraditionalChinese(allOrderPriceTotal): allOrderPriceTotal;
        const allNumSummary = traditionalPrint ? __translate.ToTraditionalChinese(deliverTotal) : deliverTotal;
        const allOrderNumSummary = traditionalPrint ? __translate.ToTraditionalChinese(orderTotal) : orderTotal;
        tds.push(
          h(
            'td',
            {
              style: {
                borderRight: borderStyle,
                borderBottom: borderStyle,
                ...cellStyle
              },
              attrs: {
                colspan: everyColspan||3
              }
            },
            [
              isAllPageOrderPrice
                ? h('div', `${allOrderPriceSummary}${mainData.price}`)
                : '',
              isAllPageOrderPriceCapital
                ? h('div', num2RmbCaption(mainData.price))
                : '',
              isAllPageActualPrice
                ? h('div', `${allSummary}${mainData.actual_price}`)
                : '',
              isAllPageActualPriceCapital
                ? h('div', num2RmbCaption(mainData.actual_price))
                : '',
              isAllPageOrderAmount
              ? h('div', `${allOrderNumSummary}${mainData.order_total_amount}`)
              : '',
              isAllPageActualAmount
              ? h('div', `${allNumSummary}${mainData.total_actual_amount}`)
              : '',
            ]
          )
        );
      }

      selectedAllPageSumItems.forEach(pItem => {
        if (template.config[pItem.key]) {
          tds.push(
            h(
              'td',
              {
                style: {
                  borderRight: borderStyle,
                  borderBottom: borderStyle,
                  ...cellStyle
                },
                attrs: {
                  colspan: everyColspan
                }
              },
              [
                h('div', `${labelMap[pItem.key] || pItem.label}${mainData[pItem.dataKey]}`)
              ]
            )
          );
        }
      });

      // 本页商品数
      if (isShowCurrentPageCommodityCount) {
        let colspan = everyColspan;
        if (!isShowOrderNo && !isShowPageNo) {
          tds.shift();
          if (tds.length === 1) {
            colspan = firstColumnColspan + everyColspan;
          }
        }
        // 有一个隐藏的商品数列，所以+1
        colspan += 1;
        tds.push(
          h(
            'td',
            {
              style: {
                borderRight: borderStyle,
                borderBottom: borderStyle,
                ...cellStyle
              },
              attrs: {
                colspan
              }
            },
            [renderCurrentPageCommodityCount()]
          )
        );
      }

      if (isShowAllPageCommodityCount) {
        console.log('xxxxxxxxxxxxxxxxxxxxx', everyColspan)
        tds.push(
          h('td', {
            style: {
              borderRight: borderStyle,
              borderBottom: borderStyle,
              ...cellStyle
            },
            attrs: {
              colspan: everyColspan
            }
          }, [renderAllPageCommodityCount()])
        )
      }

      if (pageInfoInAllPage && isHasPage()) {
        footer.push(h('tr', tds));
      }
      if (inAllPageItems && inAllPageItems.includes('_receiver_sign')) {
        footer.push(
          _addReceiverSign({
            h,
            columns,
            borderStyle,
            cellStyle,
            traditionalPrint
          })
        );
      }
      if (pageInfoInAllPage && isShowCurrentColumnTotal) {
        footer.unshift(renderCurrentColumnTotal());
      }
      return footer;
      }
    }
  };
  return printConfig;
};

export const _formatOrderData = (order, printConfig) => {
  order.user_code_barcode = order.user_code;
  order.items = order.items === undefined ? [] : order.items;

  // 重新计算发货金额
  order.delivery_price = 0;
  order.items.forEach(item => {
    order.delivery_price += Number(item.row_money);
  });
  order.delivery_price = order.delivery_price.toFixed(2);
  const kgUnit = printConfig.kg_print_unit;

   //发货合计
  // 如果开启了按公斤打印 下单和发货数量合计取不同字段
   if(printConfig.is_kg_print === 'Y'){
    order.total_actual_amount =  order.total_actual_amount_kg_mode
    order.order_total_amount =  order.order_total_amount_kg_mode
    order.total_origin_delivery_amount =  order.total_origin_delivery_amount_kg_mode
    order.total_shipped_quantity = order.total_shipped_quantity_kg_mode
   }
  order.alia_sub_protocol_org_price = 0
  order.items.forEach((goods, index) => {
    // 下单折前价小计
    if (goods.sub_protocol_org_price) {
      order.alia_sub_protocol_org_price += Number(goods.sub_protocol_org_price)
    }
    // 序号
    goods.no = index + 1;
    // 负数下浮率
    goods.minus_price_float_rate = goods.price_float_rate
      ? goods.price_float_rate.indexOf('-') >= 0
        ? goods.price_float_rate.replace('-', '')
        : '-' + goods.price_float_rate
      : '';
    let unitText = printConfig.unit_show_kg === 'Y' ? '千克' : '公斤';
    // 使用了新配置，优先取新配置
    if (kgUnit) {
      unitText = kgUnit;
    }

    // 开启了公斤打印  下单单位为斤
    if (printConfig.is_kg_print === 'Y' && goods.unit === '斤') {
      // 如果开启了单位显示为千克, 公斤显示为千克
      goods.unit = unitText;
      goods.amount = parseFloat(Number(goods['amount'] / 2).toFixed(2));
      // 下单单价
      if (!isNaN(Number(goods.unit_price))) {
        // if (+goods['amount'] === 0) {
          goods['unit_price'] = (goods['unit_price'] * 2).toFixed(2);
        // } else {
        //   goods['unit_price'] = parseFloat(Number(goods['row_money2'] / goods['amount']).toFixed(2));
        //
      }
      //下单单价不含税
      if (!isNaN(Number(goods.unit_price_no_tax))) goods.unit_price_no_tax = parseFloat(Number(goods['unit_price_no_tax'] * 2).toFixed(2));
      goods['amount_with_unit'] = goods['amount_with_unit1'] = goods['amount'] + goods['unit'];
      goods['amount1'] = goods['amount']
            // 开启了公斤打印，换为公斤，所以要*2
      // 市场价
      if (goods['price']) {
        goods['price'] = (goods['price'] * 2).toFixed(2);
      }
      // 协议市场价
      if (!isNaN(Number(goods.protocol_org_price))) goods['protocol_org_price'] = goods['protocol_org_price'] ? (goods['protocol_org_price'] * 2).toFixed(2):'';
      goods['alia_protocol_org_price'] = goods['protocol_org_price']
      // 折前价不含税
      if (!isNaN(Number(goods.protocol_org_price_no_tax))) goods['protocol_org_price_no_tax'] = goods['protocol_org_price_no_tax']?(goods['protocol_org_price_no_tax'] * 2).toFixed(2):'';
      if (goods['in_price']) {
        goods['in_price'] = (goods['in_price'] * 2).toFixed(2);
      }
      goods.origin_delivery_amount = `${(goods.origin_delivery_amount_exclusive_unit / 2).toFixed(2)}${goods.unit}`;
    }
    if (
      printConfig.is_kg_print === 'Y' &&
      goods['unit_sell'] == '斤' &&
      goods['actual_amount']
    ) {
      goods['unit_sell'] = unitText;
      goods['actual_amount'] = parseFloat( Number(goods['actual_amount'] / 2)).toFixed(2);
      if (!+goods['actual_amount']) {
        goods['actual_amount'] = '';
      }
      goods.receive_actual_amount = parseFloat( Number(goods['receive_actual_amount'] / 2)).toFixed(2);
      // 发货单价
      if (!isNaN(Number(goods.actual_price))) {
        // 兼容下该场景, 发货数量为“0”时显示发货单价
        // 发货数量为0的情况下, row_money为空, 即便开了上述开关也计算不出来, 但此计算方式无法考究, 遵循打补丁原则不动之前的代码, 补上该场景
        if (!goods['actual_amount'] && +store.state.sysConfig.delivery_amount_zero_show_delivery_unit_price === 1) {
          goods['actual_price'] = (goods['actual_price'] * 2).toFixed(2)
        } else {
          // goods['actual_price'] = parseFloat( Number(goods['row_money'] / goods['actual_amount']).toFixed(2) ) || '';
          goods.actual_price = (goods['actual_price'] * 2).originTofixed(2);
        }
      }
      // 发货单价不含税
      if (!isNaN(Number(goods.actual_price_no_tax))) goods.actual_price_no_tax = parseFloat(Number(goods['actual_price_no_tax'] * 2).toFixed(2));
      // 发货折前价
      if (!isNaN(Number(goods.delivery_price))) goods.delivery_price =goods['delivery_price']? parseFloat(Number(goods['delivery_price'] * 2).toFixed(2)):'';
      // 发货折前价不含税
      if (!isNaN(Number(goods.delivery_price_no_tax))) goods.delivery_price_no_tax =goods['delivery_price_no_tax']?parseFloat(Number(goods['delivery_price_no_tax'] * 2).toFixed(2)):'';

      if (+goods['actual_amount'] !== 0) {
        goods['actual_amount_with_unitsell'] =  goods['actual_amount'] + goods['unit_sell'];
      } else {
        goods['actual_amount_with_unitsell'] = '';
      }
      goods['return_amount'] = parseFloat(
        Number(goods['return_amount'] / 2).toFixed(2)
      );
      if (goods['return_amount'] > 0) {
        goods['return_info_price'] = parseFloat(Number(goods['return_total_price'] / goods['return_amount']).toFixed(2));
      }
    }
    if (!+goods['actual_amount']) {
      goods['actual_amount'] = '';
    }
    // 大写
    goods['actual_sub_price_str_capital'] = store.state.sysConfig.print_time_price_com_view === '1' && goods['actual_sub_price'] === ''  ?
      '' : num2RmbCaption(goods['actual_sub_price'])
    goods['protocol_discount'] =goods['protocol_discount']?(goods['protocol_discount'] + '%'):'';
    goods['discount'] = goods['discount']? (goods['discount']+ '%') :'';
    goods['protocol_org_price_cur_page_total'] =  goods.delivery_total_price;
  });
  formatMutiData(order,printConfig)

  order.alia_sub_protocol_org_price = order.alia_sub_protocol_org_price.toFixed(2);
  order.alia_sub_protocol_org_price_UpperCase = num2RmbCaption(order.alia_sub_protocol_org_price);
  order.order_price = order.price;
  order.difference_money = ((order.delivery_price || 0) - order.order_price).toFixed(2);
  order.delivery_price_capital = num2RmbCaption(order.delivery_price);
  order.delivery_total_discount_price_capital = num2RmbCaption(order.delivery_total_discount_price);
  order.order_price_capital = num2RmbCaption(order.order_price);
  order.difference_money_capital = num2RmbCaption(order.difference_money);
  order.order_remark = order.remark;
  order.total_deliver_reduction_price_capital = num2RmbCaption(order.total_deliver_reduction_price);
  if (order.user_summary) {
    order.sum_pay_price = order.user_summary.pay_price;
    order.sum_reduction_price = order.user_summary.reduction_price;
    order.sum_should_price = order.user_summary.should_price;
    order.sum_unpaid_price = order.user_summary.unpaid_price;
  }

  // 分享二维码
  order.share_code = getOrderShareUrl(order.encrypt_data || '');
  return order;
};
export const formatMutiData = (order, printConfig) => {
    // 处理默认双列打印数据
    if (
      ((printConfig.print_style&&printConfig.print_style.includes(MULTI_COLUMN)) || (hasMutiCol(printConfig)&& printConfig.print_format != CATEGORY_GROUP)) &&
      printConfig.wayPrint !== ORDER_TAG &&
      printConfig.print_format !== 'user_group'
    ) {
      order.items = _formatOrderDataMethods[MULTI_COLUMN](order, printConfig);
    }
    // 处理默认分类汇总数据, 分类汇总展示不支持按订单标签打印样式
    if (
      (printConfig.print_style === CATEGORY_GROUP || printConfig.print_format == CATEGORY_GROUP)  &&
      printConfig.wayPrint !== ORDER_TAG
    ) {
      order.items = _formatOrderDataMethods[CATEGORY_GROUP](order, printConfig);
    }
    // 如果是按商品汇总打印,按订单标签拆分打印
    if (printConfig.wayPrint === ORDER_TAG) {
      // 订单标签拆分打印
      order.items = _formatOrderDataMethods[ORDER_TAG](order, printConfig);
    }
    if (printConfig.print_format === 'user_group') {
      order.items = _formatOrderDataMethods.commonSummaryFormat(order, printConfig, 'user_name');
    }
    return order
}
/**
 * @description: 打印数据方法抽离
 */
const _formatOrderDataMethods = {
  /**
   * @description: 通用的汇总数据格式化方法
   * @param {*} order 单据信息
   * @param {*} field 汇总字段
   */
  commonSummaryFormat(order, printConfig, field, extraConfig = {}) {
    const { itemSort, groupSort, commonFields } = extraConfig;
    const groupInfo = {};
    order.items.forEach(goods => {
      const fieldValue = goods[field];
      if (!groupInfo[fieldValue]) {
        groupInfo[fieldValue] = {
          [field]: fieldValue,
          total_price: 0,
          items: []
        };
        if (Array.isArray(commonFields)) {
          commonFields.forEach(_ => {
            groupInfo[fieldValue][_] = goods[_];
          });
        }
      }
      groupInfo[fieldValue].items.push(goods);
      groupInfo[fieldValue].total_price += goods.row_money
        ? parseFloat(goods.row_money, 2)
        : 0;
    });
    let groupList = [];
    Object.keys(groupInfo).forEach(_ => {
      groupList.push(groupInfo[_]);
    });

    if (typeof groupSort === 'function') {
      groupList = groupSort(groupList);
    }

    let newItems = [];
    let index = 0;
    groupList.forEach(_ => {
      newItems.push({
        category_name: _[field],
        total_price: _.total_price.toFixed(2),
        _is_category: true,
        count: _.items.length,
        index: index + 1,
        items: _.items
      });
      _.items.forEach(item => {
        item.no = ++index;
      });
      let _groupItems = _.items;
      // 自定义排序
      if (typeof itemSort === 'function') {
        _groupItems = _.items.sort(itemSort);
      }
      newItems = newItems.concat(_groupItems);
    });
    function multiColumnFormat(data, count, sortType) {
      let colNewItems = []
      data.forEach(res => {
        let groupItems = []
        let formatItems = []

        if (sortType == SORT_HORIZONTAL) {  //横向
          for (let i = 0; i < res.items.length; i += count) {
            groupItems.push(res.items.slice(i, i + count))
          }
          groupItems.forEach(groupRow => {
            const formatRow = {};
            groupRow.forEach((groupCol, index) => {
              Object.keys(groupCol).forEach(columnKey => {
                formatRow[`${columnKey}_${index}`] = groupCol[columnKey];
              });
            });
            formatItems.push(formatRow);

          });
        } else {  //纵向
          let repeatNum = Math.ceil(res.items.length / count);
          for (let i = 0; i < res.items.length; i += repeatNum) {
            groupItems.push(res.items.slice(i, i + repeatNum));
          }
          for (let i = 0; i < groupItems[0].length; i++) {
            let newItem = {};
            let index = -1;
            groupItems.forEach(group => {
              if (group[i]) {
                index += 1;
                Object.keys(group[i]).forEach(key => {
                  newItem[`${key}_${index}`] = group[i][key];
                });
              }
            });
            formatItems.push(newItem);
          }
        }
        res.children = formatItems
      })
      data.forEach(_ => {
        colNewItems.push({
          category_name: _[field],
          total_price: _.total_price.toFixed(2),
          actual_amount: (_.actual_amount || 0).toFixed(2),
          _is_category: true,
          count: _.items.length,
          items: _.items,
          index: index + 1
        });
        _.children.forEach(item => {
          item.no = ++index;
        });
        colNewItems = colNewItems.concat(_.children);
      });
      return colNewItems
    }

    // 多列计算
    if (['column_2', 'column_3', 'column_4'].includes(printConfig.print_line)) {
      newItems = multiColumnFormat(groupList, +printConfig.print_line.replace(/column_/, ''), printConfig[SORT_TYPE] || SORT_HORIZONTAL)
    }
    return newItems;
  },
  /**
   * @description: 处理多列打印数据
   * @param {*} order 数据
   * @param {*} printConfig 配置
   * @return {*}
   */
  [MULTI_COLUMN]: (order, printConfig) => {
    const formatFuncs = {
      [SORT_VERTICAL]: (order, printConfig) => {
        let rowNum = +printConfig.row_num;
        let columnNum = hasMutiCol(printConfig)
        // 类型2, 默认
        // 1 2 3 4 5 6 7
        //    ↓
        // 1 2 3
        // 4 5 6
        // 7
        // -----------------
        if (!rowNum) {
          const formatItems = [];
          const groupItems = [];
          let length = order.items.length;
          let repeatNum = Math.ceil(length / columnNum);
          for (let i = 0; i < length; i += repeatNum) {
            groupItems.push(order.items.slice(i, i + repeatNum));
          }
          length = groupItems[0].length;
          for (let i = 0; i < length; i++) {
            let newItem = {};
            let index = -1;
            groupItems.forEach(group => {
              if (group[i]) {
                index += 1;
                Object.keys(group[i]).forEach(key => {
                  newItem[`${key}_${index}`] = group[i][key];
                });
              }
            });
            formatItems.push(newItem);
          }
          return formatItems;
        } else {
          order.items.forEach((item, index) => {
            item.no = index + 1;
          });
          let items = [];
          let page = -1;
          while (order.items.length > 0) {
            page = page + 1;
            for (var o = 0; o < columnNum; o++) {
              for (var i = 0; i < rowNum; i++) {
                if (o === 0) {
                  let newItem = {};
                  let obj = order.items.shift();
                  if (!obj) {
                    continue;
                  }
                  for (const key in obj) {
                    newItem[`${key}_${o}`] = obj[key];
                  }
                  items.push(newItem);
                } else {
                  let obj = order.items.shift();
                  if (!obj) {
                    continue;
                  }
                  for (const key in obj) {
                    items[page * rowNum + i][`${key}_${o}`] = obj[key];
                  }
                }
              }
            }
          }
          return items;
        }
      },
      [SORT_HORIZONTAL]: (order, printConfig) => {
        let columnNum = hasMutiCol(printConfig)
        const formatItems = [];
        const groupItems = [];
        const length = order.items.length;
        for (let i = 0; i < length; i += columnNum) {
          groupItems.push(order.items.slice(i, i + columnNum));
        }
        groupItems.forEach(groupRow => {
          const formatRow = {};
          groupRow.forEach((groupCol, index) => {
            Object.keys(groupCol).forEach(columnKey => {
              formatRow[`${columnKey}_${index}`] = groupCol[columnKey];
            });
          });
          formatItems.push(formatRow);
        });
        return formatItems;
      }
    };
    // todo
    const sortType = printConfig[SORT_TYPE] || SORT_HORIZONTAL; // 默认横向
    return formatFuncs[sortType](order, printConfig);
  },
  /**
   * @description: 分类汇总
   * 分类汇总打印分类排序规则
    订单发货单打印设置为【分类汇总】打印模式时，订单商品按照分类排序和id进行打印
    优先按照分类设置的排序，从小到大进行排序 category_sequence_1
    如果未设置分类排序的，则按照分类id按照从小到大进行排序 category_id
   */
  [CATEGORY_GROUP]: (order,printConfig) => {
    const categoryItems = {};
    order.items.forEach(goods => {
      if (!categoryItems[goods.category_name]) {
        categoryItems[goods.category_name] = {
          category_name: goods.category_name,
          category_id: goods.category_id,
          category_sequence_1: goods.category_sequence_1,
          total_price: 0,
          actual_sub_price: 0,
          actual_amount: 0,
          items: []
        };
      }
      categoryItems[goods.category_name].items.push(goods);
      categoryItems[goods.category_name].total_price += goods.row_money
        ? parseFloat(goods.row_money, 2)
        : 0;
      categoryItems[goods.category_name].actual_sub_price += goods.actual_sub_price
        ? parseFloat(goods.actual_sub_price, 2)
        : 0;
      categoryItems[goods.category_name].actual_amount += goods.actual_amount
        ? parseFloat(goods.actual_amount, 2)
        : 0;
    });
    let categoryList = [];
    Object.keys(categoryItems).filter(_ => {
      categoryList.push(categoryItems[_]);
    });
    if(categoryList[0].items[0].prt_tpl_group_category_name){
      // 假如是分组模板按分组模板的排序 否则按商品分类的排序
    } else if (store.state.sysConfig.is_invoice_print_sort == '3') {
      // 不处理数据，使用后端处理好的数据
    }
    else{
    // __过滤出设置了分类排序的分类, 数组, 并排序
    const hasSeqCategoryList = categoryList
      .filter(_ => {
        return +_.category_sequence_1 !== 0;
      })
      .sort((a, b) => {
        return +a.category_sequence_1 - +b.category_sequence_1;
      });
    // __过滤出没有设置分类排序的分类, 数组, 并按照id排序
    const noSeqCategoryList = categoryList
      .filter(_ => {
        return +_.category_sequence_1 === 0;
      })
      .sort((a, b) => {
        return +a.category_id - +b.category_id;
      });
    // __合并分类数组
    categoryList = hasSeqCategoryList.concat(noSeqCategoryList);
    }
    let newItems = [];
    let index = 0;
    categoryList.forEach(_ => {
      newItems.push({
        category_name: _.category_name,
        total_price: _.total_price.toFixed(2),
        actual_sub_price: _.actual_sub_price.toFixed(2),
        actual_amount: _.actual_amount.toFixed(2),
        _is_category: true,
        count: _.items.length,
        index: index + 1
      });
      _.items.forEach(item => {
        item.no = ++index;
      });
      newItems = newItems.concat(_.items);
    });
    function categoryFormat(data,count, sortType){
      let colnewItems = []
        data.forEach(res=>{
          let groupItems = []
          let formatItems =[]

          if(sortType==SORT_HORIZONTAL){  //横向
            for (let i = 0; i < res.items.length; i += count) {
              groupItems.push(res.items.slice(i, i + count))
            }
            groupItems.forEach(groupRow => {
              const formatRow = {};
              groupRow.forEach((groupCol, index) => {
                Object.keys(groupCol).forEach(columnKey => {
                  formatRow[`${columnKey}_${index}`] = groupCol[columnKey];
                });
              });
              formatItems.push(formatRow);

            });
          }else{  //纵向
            let repeatNum = Math.ceil(res.items.length / count);
            for (let i = 0; i < res.items.length; i += repeatNum) {
              groupItems.push(res.items.slice(i, i + repeatNum));
            }
            for (let i = 0; i < groupItems[0].length; i++) {
              let newItem = {};
              let index = -1;
              groupItems.forEach(group => {
                if (group[i]) {
                  index += 1;
                  Object.keys(group[i]).forEach(key => {
                    newItem[`${key}_${index}`] = group[i][key];
                  });
                }
              });
              formatItems.push(newItem);
            }
            }
          res.children = formatItems
        })
        data.forEach(_ => {
          colnewItems.push({
            category_name: _.category_name,
            total_price: _.total_price.toFixed(2),
            actual_sub_price: _.actual_sub_price.toFixed(2),
            actual_amount: _.actual_amount.toFixed(2),
            _is_category: true,
            count: _.items.length,
            index: index + 1
          });
          _.children.forEach(item => {
            item.no = ++index;
          });
          colnewItems = colnewItems.concat(_.children);
        });
        return colnewItems
    }







    // 多列计算
     if(['column_2','column_3'].includes(printConfig.print_line)) {
       newItems =   categoryFormat(categoryList,+printConfig.print_line.replace(/column_/,''), printConfig[SORT_TYPE] || SORT_HORIZONTAL)
     }
    return newItems;
  },

  /**
   * @description: 按订单标签打印
   */
  [ORDER_TAG]: (order, printConfig) => {
    const { print_style } = printConfig;
    // 得到标签id name映射关系
    const tagNameMap = {};
    Array.isArray(order.order_tag) &&
      order.order_tag.map(item => {
        !tagNameMap[item.id] && (tagNameMap[item.id] = item.name);
      });
    // 处理标签顺序,以及映射name
    order.items.forEach(goods => {
      goods.tag = goods.tag || '';
      goods.tag = goods.tag
        .split(',')
        .sort((a, b) => a - b)
        .join();
      const tagName = [];
      goods.tag.split(',').map(tag => {
        tagNameMap[tag] && tagName.push(tagNameMap[tag]);
      });
      goods.tagName = tagName.join();
    });

    const tagItems = {};
    order.items.forEach(goods => {
      if (!tagItems[goods.tagName]) {
        tagItems[goods.tagName] = {
          tagName: goods.tagName,
          total_price: 0,
          items: []
        };
      }
      tagItems[goods.tagName].items.push(goods);
      tagItems[goods.tagName].total_price += goods.actual_sub_price
        ? parseFloat(goods.actual_sub_price, 2)
        : 0;
    });
    let newItems = [];
    Object.keys(tagItems).forEach(tag => {
      // 拿到订单号, 去重后的...
      const orderNos = uniq(
        tagItems[tag].items.map(item => item.order_no)
      ).join('，');
      if(printConfig.type=="GROUP_ORDER"){
        // 集团打印不显示订单号
      }else{
        newItems.push({
          content: `订单号：${orderNos}`, // 订单号
          _is_single_row: true // 用作判断是单行显示
        });
      }
      // 添加标签名称行数据
      newItems.push({
        content: tag || '无', // 标签名称
        _is_single_row: true // 用作判断是单行显示
      });
      // 将商品的序号重新赋值
      tagItems[tag].items.forEach((item, index) => {
        // 双列样式的时候,序号需要加粗
        item._is_bold = hasMutiCol(printConfig,true);
        item.no = index + 1;
      });
      // 如果是双列打印,需要将商品信息进行处理
      if (hasMutiCol(printConfig,true)) {
        let items = _formatOrderDataMethods[MULTI_COLUMN](
          tagItems[tag],
          printConfig
        );
        // let length = items.length;
        // for (let i = 0; i < length; i++) {
        //   const e = items[i];
        //   e.no_0 = i + 1;
        //   // 有才赋值,
        //   e.no_1 && (e.no_1 = i + 1 + length)
        // }
        newItems.push(...items);
      } else {
        newItems.push(...tagItems[tag].items);
      }
      // 添加小计数据
      newItems.push({
        _is_summary: true, // 用作判断是否是标签订单拆分样式- 小计行
        _multi_column_num:hasMutiCol(printConfig)||0,
        total_price: tagItems[tag].total_price.toFixed(2) // 分类金额小计
      });
    });
    return newItems;
  }
};

/**
 * @description: 处理商品分类打印样式
 * @param {Array} 订单数据
 * @return {Array} 处理后的订单数据
 */
export const _formatCommodityClass = (order, printConfig) => {
  const CATEGORY_ID = 'category_id',
    CATEGORY_ID2 = 'category_id2';
  const newOrders = [];
  // 拿到商品数据
  const items = cloneDeep(order.items);
  // 订单数据
  const orderInfo = cloneDeep(order);
  const categoryMap = {};
  // 创建分类id映射名字关系对象
  const category1NameMap = {};
  const categoryList = [];
  // is_commodity_class_level: Y 按一级分类打印，YY 按二级分类打印
  let category =
    printConfig.is_commodity_class_level === 'Y' ? CATEGORY_ID : CATEGORY_ID2;
  // 拿到同一个分类的商品数据
  items.forEach(e => {
    category === CATEGORY_ID &&
      (category1NameMap[e[category]] = e[CATEGORY_NAME]);
    // 没有则创建
    !categoryMap[e[category]] && (categoryMap[e[category]] = []);
    categoryMap[e[category]].push(e);
    if (!categoryList.some(cateItem => cateItem.id === e[category])) {
      categoryList.push({
        id: e[category],
        name: e[CATEGORY_NAME]
      });
    }
  });
  // 遍历分类map,拿到同一个分类下的商品数据
  categoryList.forEach(category => {
    const commodity = categoryMap[category.id];
    // 重新处理序号
    commodity.forEach((e, i) => {
      e.no = i + 1;
    });
    orderInfo[CATEGORY_NAME] = category.name;
    orderInfo.items = commodity;

    const orderInfoCopy = cloneDeep(orderInfo);
    /*
     * 按分类计算数据
     */
    // 下单数量合计
    orderInfoCopy.order_total_amount = 0;
    // 发货数量合计
    orderInfoCopy.total_actual_amount = 0;
    // 下单金额
    orderInfoCopy.price = 0;
    // 发货金额
    orderInfoCopy.delivery_price = 0;
    // 实际金额
    orderInfoCopy.actual_sub_price = 0;
    // 折前总价
    orderInfoCopy.alia_protocol_org_total_price = 0;
    commodity.forEach(e => {
      orderInfoCopy.order_total_amount += Number(e.amount);
      orderInfoCopy.total_actual_amount += Number(e.actual_amount);
      orderInfoCopy.price += Number(e.row_money2);
      orderInfoCopy.delivery_price += Number(e.row_money);
      orderInfoCopy.alia_protocol_org_total_price += Number(e.protocol_org_total_price);
      orderInfoCopy.actual_sub_price += Number(e.actual_sub_price);
    });
    orderInfoCopy.order_total_amount = orderInfoCopy.order_total_amount.toFixed(2);
    orderInfoCopy.total_actual_amount = orderInfoCopy.total_actual_amount.toFixed(2);
    orderInfoCopy.price = orderInfoCopy.price.toFixed(2);
    orderInfoCopy.delivery_price = orderInfoCopy.delivery_price.toFixed(2);
    orderInfoCopy.alia_protocol_org_total_price = orderInfoCopy.alia_protocol_org_total_price.toFixed(2);
    orderInfoCopy.alia_protocol_org_total_price_UpperCase = num2RmbCaption(orderInfoCopy.alia_protocol_org_total_price);
    orderInfoCopy.actual_sub_price = orderInfoCopy.actual_sub_price.toFixed(2);
    newOrders.push(_formatOrderData(orderInfoCopy, printConfig));
  });
    return newOrders;
};

/**
 * @description: 处理配置联动函数
 * @param {*} n 最新的模板配置
 * @param {*} o 所有的模板数据,包含dataSource
 * @return {*} 处理后的模板配置,
 */
export function _handleOrderConfigLinkage(n, o) {
  const getFlatExtraConfig = (extraConfig) => {
    if (!extraConfig) {
      return []
    }
    if (Array.isArray(extraConfig)) {
      return extraConfig
    }
    let flatConfig = []
    for (const extraKey in extraConfig) {
      if (Array.isArray(extraConfig[extraKey])) {
        flatConfig = flatConfig.concat(extraConfig[extraKey])
      }
    }
    return flatConfig
  }
  const flatExtraConfig = getFlatExtraConfig(o.extraConfig);

  // 同步画布数据
  o.children = n.children;

  // 同步配置数据
  // 同步前判断一下, 如果old  print_style是CATEGORY_GROUP,就默认置为0
  (o.config.print_style === CATEGORY_GROUP ||o.config.print_format===CATEGORY_GROUP)  && (n.config[ROW_NUM] == 0);

  // 打印设计器里面用的是numberInput组件,所以要给0
  n.config[ROW_NUM] = n.config[ROW_NUM] || 0;

  // 同步配置
  for (const key in n.config) {
    if (Object.hasOwnProperty.call(n.config, key)) {
      o.config[key] = n.config[key];
    }
  }
  let findItem;
  // 处理联动, 当打印样式选择为分类汇总时,需要隐藏选择行数配置项,
  findItem = flatExtraConfig.find(_ => _.value === ROW_NUM);
  if (o.config.print_style === CATEGORY_GROUP||o.config.print_format === CATEGORY_GROUP) {
    o.config[ROW_NUM] = 0;
    // 隐藏
    findItem && (findItem.hidden = true);
  } else {
    // 显示
    findItem && (findItem.hidden = false);
  }
  // -----------------------------===============================-----------------------------===============================

  // 处理打印样式不为多列打印时时,需要隐藏表排序方式sort_type配置项,
  findItem = flatExtraConfig.find(_ => _.value === SORT_TYPE);
  if (!hasMutiCol( o.config)) {
    findItem && (findItem.hidden = true);
  } else {
    findItem && (findItem.hidden = false);
  }

  // -----------------------------===============================-----------------------------===============================

  // 处理按一级分类打印的时候,需要显示主体信息中的商品分类字段
  const getDatasourceItem = (items, fieldKey, rootGroupName) => {
    if (!items) {
      return
    }
    let field = null
    // 递归顶层
    if (rootGroupName) {
      items.forEach(item => {
        if (item.title === rootGroupName && item.items) {
          getDatasourceItem(item.items, fieldKey)
        }
      })
    } else {
      items.forEach(item => {
        if (item.key && item.key === fieldKey) {
          field = item
        }
        if (item.items) {
          field = getDatasourceItem(item.items, fieldKey)
        }
      })
    }
    return field
  }
  findItem = getDatasourceItem(o.config.dataSourceList, CATEGORY_NAME, '表头/表尾区字段');
  if (o.config.is_commodity_class_level !== 'Y') {
    // 如果画布中有商品分类字段,就删除
    const findI = o.children.findIndex(_ => _.props.dataKey === CATEGORY_NAME);
    findI !== -1 && o.children.splice(findI, 1);
    // 隐藏主体信息中的商品分类字段
    findItem && (findItem.hidden = true);
  } else {
    findItem && (findItem.hidden = false);
  }
  return cloneDeep(o);
}
export const _ORDER = {
         data: orderDemoData(60),
         inAllPageItems: [
           {
             label: '收货人签名',
             value: '_receiver_sign'
           },
           {
             label: '分页栏',
             value: 'pageInfoInAllPage'
           },
           {
             label: '商品信息表头',
             value: 'theadInAllPage'
           },
           {
             label: '主体信息表头',
             value: 'headerInAllPage'
           },
           {
             label: '主体信息表尾',
             value: 'footerInAllPage'
           }
         ],
         extraConfig: {
           printSettings: [
             // todo
             //  {
             //    size: 'm',
             //    label: '表排序方式',
             //    value: 'sort_type',
             //    type: 'Select',
             //    data: [
             //      {
             //        label: '纵向',
             //        value: SORT_VERTICAL
             //      },
             //      {
             //        label: '横向',
             //        value: SORT_HORIZONTAL
             //      }
             //    ]
             //  }
           ],
           styleSettings: [
             {
               size: 'm',
               label: '打印样式',
               value: 'print_format',
               type: 'Select',
               data: [
                 {
                   label: '默认样式',
                   value: 'default'
                 },
                 {
                   label: '分类汇总样式',
                   value: CATEGORY_GROUP
                 }
               ]
             },
             {
              size: 'm',
              label: '字段配置',
              value: 'category_group_fields',
              type: 'CheckboxGroup',
              tips: '分类名称固定显示',
              data: [
                {
                  label: '商品数',
                  value: 'count'
                },
                {
                  label: '单价',
                  value: 'price',
                  tips: '取值为发货小计除以商品数'
                },
                {
                  label: '发货小计',
                  value: 'total_price'
                },
                {
                  label: '实际金额小计',
                  value: 'actual_sub_price'
                },
              ]
            },
             {
               size: 'm',
               label: '打印列数',
               value: 'print_line',
               type: 'Select',
               data: [
                 {
                   label: '单列',
                   value: 'column_1'
                 },
                 {
                   label: '双列',
                   value: 'column_2'
                 },
                 {
                   label: '三列',
                   value: 'column_3'
                 }
               ]
             },
             {
               size: 'm',
               label: '表排序方式',
               value: 'sort_type',
               type: 'Select',
               data: [
                 {
                   label: '纵向',
                   value: SORT_VERTICAL
                 },
                 {
                   label: '横向',
                   value: SORT_HORIZONTAL
                 }
               ]
             },
             {
               size: 'm',
               label: '是否显示边框',
               value: 'tableBordered',
               type: 'Select',
               data: [
                 {
                   label: '是',
                   value: 'Y'
                 },
                 {
                   label: '否',
                   value: 'N'
                 }
               ]
             },
             {
               size: 'm',
               label: '按公斤打印',
               value: 'kg_print_mode',
               type: 'Select',
               data: [
                 {
                   label: '无',
                   value: '无'
                 },
                 {
                   label: '公斤',
                   value: '公斤'
                 },
                 {
                   label: '千克',
                   value: '千克'
                 },
                 {
                   label: 'KG',
                   value: 'KG'
                 },
               ]
             },
            //  {
            //    size: 'm',
            //    label: '公斤打印',
            //    value: 'is_kg_print',
            //    type: 'Select',
            //    data: [
            //      {
            //        label: '开启',
            //        value: 'Y'
            //      },
            //      {
            //        label: '关闭',
            //        value: 'N'
            //      }
            //    ]
            //  },
            //  // 单位显示为千克
            //  {
            //    size: 'm',
            //    label: '单位显示为千克',
            //    value: 'unit_show_kg',
            //    type: 'Select',
            //    data: [
            //       {
            //         label: '是',
            //         value: 'Y'
            //       },
            //       {
            //         label: '否',
            //         value: 'N'
            //       }
            //     ]
            //  },
             {
               size: 'm',
               label: '按分类打印',
               value: 'is_commodity_class_level',
               type: 'Select',
               data: [
                 {
                   label: '关闭',
                   value: 'N'
                 },
                 {
                   label: '一级分类',
                   value: 'Y'
                 },
                 {
                   label: '二级分类',
                   value: 'YY'
                 }
               ]
             },
             {
               size: 'm',
               label: '自动填充剩余行数',
               value: IS_AUTO_FILL_ROW,
               type: 'Select',
               data: [
                 {
                   label: '填充空白行',
                   value: 'Y'
                 },
                 {
                   label: '填充空白行并展示序号',
                   value: 'YY'
                 },
                 {
                   label: '不填充',
                   value: 'N'
                 }
               ]
             },
             // 数字输入框记得配默认值
             {
               size: 'm',
               label: '每页显示行数',
               value: ROW_NUM,
               type: 'InputNumber',
               hidden: false
             }
           ]
         },
         config: {
           name: '默认发货单[新版]',
           paperWidth: 210,
           paperHeight: 297,
           offsetTop: '',
           offsetLeft: '',
           printDirection: 1,
           print_style: '',
           print_format: 'default',
           print_line: 'column_1',
           lineSum: 'line1',
           [ROW_NUM]: 0,
           paperDirection: '1',
           [SORT_TYPE]: SORT_HORIZONTAL,
           is_commodity_class_level: 'N',
           pageSizeOptions: [
             {
               label: '三联单[一等分]',
               width: 241.3,
               height: 279
             },
             {
               label: '三联单[二等分]',
               width: 241.3,
               height: 139.7
             },
             {
               label: '三联单[三等分]',
               width: 241.3,
               height: 93.1
             },
             {
               label: 'A4纸',
               width: 210,
               height: 297
             },
             {
               label: 'A5纸',
               width: 148,
               height: 210
             }
           ],
           dataSourceList: [
             {
               title: '表格区字段',
               type: 'Table',
               items: [
                 {
                   title: '商品信息',
                   items: [
                     {
                       title: '基础信息',
                       items: [
                         {
                           label: '商品编码',
                           key: 'commodity_code'
                         },
                         {
                           label: '商品名称',
                           key: 'name',
                           setting: genSettingConfig([
                             { key: NOT_ALIAS, value: false }
                           ])
                         },
                         {
                           label: '一级分类',
                           key: 'category_name'
                         },
                         {
                           label: '二级分类',
                           key: 'category_name2'
                         },
                         {
                           label: '三级分类',
                           key: 'category_name3',
                           show: 'is_open_commodity_category_three === 1'
                         },
                         {
                           label: '商品品牌',
                           key: 'brand'
                         },
                         {
                           label: '条形码',
                           tips: '该字段仅展示商品条形码的条形图片',
                           key: 'commodity_code_bar_code',
                           type: 'barcode'
                         },
                         {
                           label: '条形码（编码）',
                           tips: '该字段仅展示商品条形码的编码',
                           key: 'bar_code'
                         },
                         {
                           label: '保质期',
                           key: 'durability_period'
                         },
                         {
                           label: '批次号',
                           key: 'batch_no',
                           show: 'is_batch === 1'
                         },
                         {
                           label: '过期日期(批次)',
                           key: 'expired_date',
                           show: 'is_batch === 1'
                         },
                         {
                           label: '生产日期',
                           tips: '该字段仅展示分拣时录入的生产日期',
                           key: 'mfg_date'
                         },
                         {
                           label: '生产日期(批次)',
                           tips: '该字段展示为发货出库时批次对应的生产日期',
                           key: 'production_date'
                         },
                         {
                           label: '产地',
                           key: 'product_place'
                         },
                         {
                           label: '描述',
                           key: 'summary'
                         },
                         {
                           label: '备注',
                           key: 'remark'
                         },
                         {
                           label: '内部备注',
                           key: 'inner_remark',
                           show: 'open_order_commodity_inner_remark === 1'
                         },
                         {
                           label: '默认库区库位',
                           key: 'default_area_location'
                         },
                         {
                           label: '批次库区库位',
                           key: 'batch_area_location_list',
                           tips: '取值发货出库是批次对应的库区库位，排序为自动生成'
                         },
                         {
                          label: '默认供应商',
                          key: 'default_provider'
                         },
                         {
                            label: '发货日期',
                            key: 'order_delivery_date',
                            tips: '展示订单商品所属发货日期'
                          }
                       ]
                     },
                     {
                       title: '下单信息',
                       items: [
                         {
                           label: '下单数量',
                           tips: '该字段展示为下单数量+单位',
                           key: 'amount_with_unit'
                         },
                         {
                          label: '下单数量(1)',
                          tips: '该字段展示为下单数量+单位',
                          key: 'amount_with_unit1'
                         },
                         {
                           label: '下单数量(无单位)',
                           tips: '该字段仅展示下单数量的数值',
                           key: 'amount',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '下单数量(无单位)(1)',
                           tips: '该字段仅展示下单数量的数值',
                           key: 'amount1',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '下单单位',
                           key: 'unit'
                         },
                         {
                           label: '下单单价',
                           key: 'unit_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '下单单价(不含税)',
                           key: 'unit_price_no_tax',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '下单小计',
                           key: 'row_money2',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '下单折前小计',
                           tips: '下单折前小计 = 折前价*下单数量',
                           key: 'sub_protocol_org_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                       ]
                     },
                     {
                       title: '发货信息',
                       items: [
                         {
                           label: '发货数量',
                           tips: '该字段展示为发货数量+单位',
                           key: 'actual_amount_with_unitsell'
                         },

                         {
                           label: '发货数量(无单位)',
                           tips: '该字段展示为发货数量的数值',
                           key: 'actual_amount',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '发货数量(原)',
                           tips: '该字段展示按下单单位计算发货数量+下单单位',
                           key: 'origin_delivery_amount'
                         },
                         {
                           label: '发货数量(原)(无单位)',
                           tips: '该字段展示按下单单位计算发货数量的数值',
                           key: 'origin_delivery_amount_exclusive_unit'
                         },
                         {
                           label: '发货单位',
                           key: 'unit_sell'
                         },
                         {
                           label: '发货单价',
                           key: 'actual_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '发货单价(不含税)',
                           key: 'actual_price_no_tax',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '发货折前价',
                           key: 'delivery_price',
                           tips: '取值： 发货单价/折扣率=发货折前价（根据发货单位换算折前价）'
                         },
                         {
                           label: '发货折前价（不含税）',
                           key: 'delivery_price_no_tax'
                         },
                         {
                           label: '发货折扣率',
                           key: 'delivery_discount'
                         },
                         {
                           label: '发货折扣金额',
                           key: 'delivery_total_discount_price'
                         },
                         {
                           label: '发货折前小计',
                           key: 'delivery_total_price',
                           tips: '取值发货折前价x发货数量'
                         },
                         {
                           label: '发货折前小计（不含税）',
                           key: 'delivery_total_price_no_tax'
                         },
                         {
                           label: '发货金额',
                           key: 'row_money',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '差异金额',
                           tips: '取值差异金额 = 发货金额-下单金额',
                           key: 'difference_money'
                         },
                         {
                           label: '抹零金额',
                           key: 'deliver_reduction_price',
                           tips: '抹零金额 = 发货单价*发货数量 （小数点后一位向下取整）'
                         },
                         {
                          label: '基准单位',
                          key: 'standard_unit_name',
                          show: 'is_open_standard_unit === 1'
                         },
                         {
                          label: '基准单位数量',
                          key: 'standard_unit_sort_amount',
                          show: 'is_open_standard_unit === 1'
                         },
                       ]
                     },
                     {
                       title: '退货信息',
                       items: [
                         {
                           label: '退货数量',
                           tips: '取值关联订单的退货退款单中该商品的最终审批退货数',
                           key: 'return_amount',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '退货单价',
                           tips: '退货单价 = 小计金额/已审核数量',
                           key: 'return_info_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '退货金额',
                           tips: '取值关联订单的退货退款单中该商品的退货金额',
                           key: 'return_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         }
                       ]
                     },
                     {
                       title: '实际信息',
                       items: [
                         {
                           label: '实际数量',
                           key: 'receive_actual_amount',
                           tips: '取值发货数量-退货数量',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },

                         {
                           label: '实际金额',
                           tips: '取值发货小计-退货金额',
                           key: 'actual_sub_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '实际金额（不含税）',
                           tips: '取值公式（发货小计-退货金额） / (1+税率)',
                           key: 'actual_sub_price_excluding_tax',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '大写实际金额',
                           tips: '取值发货小计-退货金额所得大写展示',
                           key: 'actual_sub_price_str_capital'
                         }
                       ]
                     },
                     {
                       title: '押金筐信息',
                       items: [
                         {
                           label: '押金筐名称',
                           key: 'deposit_basket_name'
                         },
                         {
                           label: '押金筐数量',
                           key: 'deposit_basket_amount',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '押金筐金额',
                           key: 'item_deposit_basket_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         }
                       ]
                     },
                     {
                       title: '价格信息',
                       items: [
                         {
                           label: '折前价',
                           key: 'protocol_org_price',
                           tips: '取值： 根据下单单位算的折前价 （客户类型折扣：折前价 = 价格/折扣率、客户折扣：折前价 = 价格/客户折扣率、客户商品折扣：市场价、客户协议单折扣：协议市场价）',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true },
                             {
                               key: SHOW_DISCOUNT_100,
                               value: false,
                               label: '折扣率为100%时，打印为空'
                             }
                           ])
                         },
                         {
                           label: '折前价（不含税）',
                           key: 'protocol_org_price_no_tax',
                           tips: '取值下单单价（不含税）除以折扣率',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '折前价（1）',
                           key: 'alia_protocol_org_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '折前价小计',
                           key: 'protocol_org_price_cur_page_total',
                           tips: '（发货数量*换算系数）x折前价'
                         },
                         {
                           label: '市场价',
                           key: 'price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '折扣率',
                           key: 'protocol_discount',
                           tips: '取值优先级 客户类型折扣< 客户折扣<客户商品折扣<协议单折扣',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true },
                             {
                               key: SHOW_DISCOUNT_100,
                               value: false,
                               label: '折扣率为100%时，打印为空'
                             }
                           ])
                         },
                         {
                           label: '下浮率',
                           key: 'price_float_rate',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true },
                             {
                               key: SHOW_DISCOUNT_100,
                               value: false,
                               label: '下浮率为0时，打印为空'
                             }
                           ])
                         },
                         {
                           label: '负数下浮率',
                           key: 'minus_price_float_rate',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                          label: '商品成本价',
                          key: 'in_price',
                          tips: '订单商品的商品成本价'
                         },
                         {
                          label: '成本金额',
                          key: 'in_total_price',
                          tips: '订单商品的成本金额'
                         },
                       ]
                     },
                     {
                       title: '菜谱信息',
                       items: [
                         {
                           label: '餐次',
                           key: 'meal_type_name'
                         },
                         {
                           label: '菜谱套餐',
                           key: 'raw_recipe_package_names',
                           show:
                             'commodity_package_mode === 1 && is_new_recipe === 1'
                         }
                       ]
                     },
                     {
                       title: '其他信息',
                       items: [
                         {
                           label: '序号',
                           key: 'no'
                         },
                         {
                           label: '税率',
                           key: 'tax_rate',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '税额',
                           key: 'tax_rate_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '计算公式',
                           key: 'remark2'
                         },
                         {
                           label: '协议市场价小计',
                           key: 'protocol_org_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '服务费',
                           key: 'service_charge'
                         },
                         {
                           label: '商品重量(kg)',
                           key: 'commodity_weight'
                         },
                         {
                           label: '订单标签',
                           key: 'order_tags'
                         },
                         {
                           label: '转换系数',
                           key: 'unit_convert_description'
                         },
                         {
                           label: '联营供应商',
                           key: 'pool_provider_name'
                         },
                         {
                           label: '分拣状态',
                           key: 'is_sorting_text'
                         },
                         {
                           label: '缺货状态',
                           key: 'is_stockout_text'
                         },
                         {
                          label: '农批价格',
                          key: 'wholesale_market_avg_price',
                          show: 'wholesale_market_price_print === 1',
                          tips: '农批市场价的取值来源于“农批价格应用-打印设置-农批市场设置”',
                          setting: genSettingConfig([
                            {
                              key: WHOLESALE_MARKET_PRICE_PLACEHOLDER,
                              value: '',
                            }
                          ])
                         },
                         {
                           label: '出净率',
                           key: 'net_output_rate',
                           show: 'is_show_net_output_rate === 1'
                         },
                         {
                           label: '净菜数量',
                           key: 'net_amount',
                           show: 'is_show_net_output_rate === 1'
                         },
                         {
                           label: '订单商品标签',
                           key: 'order_commodity_tag_text'
                           // show: '',
                         },
                       ]
                     }
                   ]
                 }
               ]
             },
             {
               title: '表头/表尾区字段',
               items: [
                 {
                   title: '主体信息',
                   type: 'Normal',
                   items: [
                     {
                       title: '订单信息',
                       items: [
                         {
                           label: '页码',
                           key: 'custom_page',
                           setting: {
                             config: [],
                             pageType: '0'
                           }
                         },
                         {
                           label: '订单号',
                           key: 'order_no'
                         },
                         {
                           label: '订单类型',
                           key: 'order_type_txt'
                         },
                         {
                           label: '下单时间',
                           key: 'create_time'
                         },
                         {
                           label: '下单时间(带星期)',
                           key: 'create_time_week'
                         },
                         {
                           label: '配送时间',
                           key: 'delivery_date',
                           setting: genSettingConfig([
                             {
                               key: DEFAULT_DATE_FORMAT,
                               value: false
                             }
                           ])
                         },
                         {
                           label: '配送时间(带星期)',
                           key: 'delivery_date_week',
                           setting: genSettingConfig([
                             {
                               key: DEFAULT_DATE_FORMAT,
                               value: false
                             }
                           ])
                         },
                         {
                           label: '支付方式',
                           key: 'pay_way_txt'
                         },
                         {
                           label: '支付状态',
                           key: 'pay_status_desc'
                         },
                         {
                           label: '订单备注',
                           key: 'order_remark'
                         },
                         {
                           label: '订单标签',
                           key: 'order_tags'
                         },
                         {
                           label: '操作人',
                           key: 'op_name'
                         },
                         {
                           label: '收货时间段',
                           key: 'order_delivery_time'
                         },
                         {
                           label: '打印时间',
                           key: 'print_time'
                         },
                         {
                           label: '分享二维码',
                           key: 'share_code',
                           type: 'QrCode'
                         },
                         {
                           label: '订单信息二维码',
                           key: 'order_qrcode',
                           type: 'QrCode'
                         },
                         {
                           label: '订单商品二维码',
                           key: 'order_commodity_qrcode',
                           type: 'QrCode'
                         }
                       ]
                     },
                     {
                       title: '配送信息',
                       items: [
                         {
                           label: '区域',
                           key: 'area_name'
                         },
                         {
                           label: '业务员',
                           key: 'seller_name'
                         },
                         {
                           label: '业务员手机',
                           key: 'seller_mobile'
                         },
                         {
                           label: '业务员图片',
                           key: 'seller_attachment_link',
                           type: 'Image',
                           width: 100,
                           height: 100
                         },
                         {
                           label: '司机',
                           key: 'driver'
                         },
                         {
                           label: '司机手机',
                           key: 'driver_mobile'
                         },
                         {
                           label: '线路名称',
                           key: 'shop_name'
                         },
                         {
                           label: '线路临时编码',
                           key: 'stopgap_code'
                         },
                         {
                           label: '车辆号',
                           key: 'license_plate'
                         },
                         {
                            label: '自提点',
                            key: 'self_pickup_point_address',
                            tips: '取值订单所属自提点地址'
                          }
                       ]
                     },
                     {
                       title: '数量信息',
                       items: [
                         {
                           label: '下单数量合计',
                           key: 'order_total_amount',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '发货数量合计',
                           key: 'total_shipped_quantity',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                          label: '订单重量(kg)',
                          key: 'order_weight'
                        },
                        {
                          label: '发货数量（原）合计',
                          key: 'total_origin_delivery_amount'
                        }
                       ]
                     },
                     {
                       title: '金额信息',
                       items: [
                         {
                           label: '运费',
                           key: 'freight_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '下单折前总价',
                           key: 'alia_sub_protocol_org_price',
                           tips: '取值商品中所有下单折前小计的总计',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '下单折前总价(大写)',
                           key: 'alia_sub_protocol_org_price_UpperCase'
                         },
                         {
                           label: '折前总价',
                           key: 'alia_protocol_org_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '折前总价(大写)',
                           key: 'alia_protocol_org_total_price_UpperCase'
                         },
                         {
                           label: '实际总价',
                           tips: '取值订单的发货小计+运费+押金筐+参与结算的服务费',
                           key: 'actual_total_pay_str'
                         },
                         {
                           label: '大写实际总价',
                           key: 'actual_total_pay_str_capital'
                         },

                         {
                           label: '下单金额',
                           key: 'order_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '下单金额大写',
                           key: 'order_price_capital'
                         },
                         {
                           label: '发货金额',
                           key: 'delivery_price'
                           // setting: genSettingConfig([
                           //   { key: DECIMAL_COMPLETION, value: true }
                           // ])
                         },
                         {
                           label: '发货金额大写',
                           key: 'delivery_price_capital'
                         },
                         {
                           label: '合计发货折扣金额',
                           key: 'delivery_total_discount_price',
                           tips: '合计发货折扣金额 = 总发货折前小计-总发货金额'
                         },
                         {
                           label: '大写合计发货折扣金额',
                           key: 'delivery_total_discount_price_capital'
                         },
                         {
                           label: '合计差异金额',
                           key: 'difference_money'
                         },
                         {
                           label: '大写合计差异金额',
                           key: 'difference_money_capital'
                         },
                         {
                           label: '已付金额',
                           key: 'pay_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '应付金额',
                           key: 'should_pay_price',
                           tips: '取值发货金额+运费+押金筐-退货金额-支付有礼',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '未付金额',
                           key: 'unpaid_price'
                         },
                         {
                           label: '大写应付金额',
                           key: 'should_pay_price_capital'
                         },
                         {
                           label: '多退少补金额',
                           key: 'diff_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '优惠金额',
                           key: 'dec_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '退货总额',
                           key: 'return_price',
                           tips: '已审核后退货金额的合计（退货退款+仅退款）',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },

                         {
                           label: '押金总额',
                           key: 'deposit_basket_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '税额',
                           key: 'order_tax_rate_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '不含税金额',
                           key: 'order_no_tax_actual_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '服务费合计',
                           key: 'order_service_charge',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '应付金额（服务费）',
                           key: 'should_pay_price_contain_service_charge',
                           tips: '取值发货金额+运费+押金筐+参与结算服务费-退货金额-支付有礼',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '协议市场价合计',
                           key: 'protocol_org_total_price',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: true }
                           ])
                         },
                         {
                           label: '总应收金额',
                           key: 'sum_should_price',
                           show: 'is_open_account_summary_report === 1'
                         },
                         {
                           label: '总已收金额',
                           key: 'sum_pay_price',
                           show: 'is_open_account_summary_report === 1'
                         },
                         {
                           label: '总抹零金额',
                           key: 'sum_reduction_price',
                           show: 'is_open_account_summary_report === 1'
                         },
                         {
                           label: '总欠款',
                           key: 'sum_unpaid_price',
                           show: 'is_open_account_summary_report === 1'
                         },
                         {
                          label: '抹零金额合计',
                          key: 'total_deliver_reduction_price'
                         },
                         {
                          label: '抹零金额大写',
                          key: 'total_deliver_reduction_price_capital'
                         },
                         {
                          label: '发货折前总计',
                          key: 'delivery_total_price',
                          tips: '所有商品发货折前小计的合计金额'
                         }
                       ]
                     },
                     {
                       title: '菜谱信息',
                       items: [
                         {
                           label: '就餐日期',
                           key: 'diner_dates'
                         },
                         {
                           label: '就餐日期(含周次)',
                           key: 'diner_dates_with_week'
                         },
                         {
                           label: '菜谱名称',
                           key: 'recipe_name'
                         },
                         {
                           label: '餐次',
                           key: 'meal_type_name'
                         },
                         {
                           label: '就餐人数',
                           key: 'number_of_diners'
                         },
                        {
                          label: '陪餐人数',
                          key: 'accompanying_of_diners',
                          show:
                            'is_open_accompanying_of_diners === 1 && is_new_recipe === 1'
                        },
                         {
                           label: '餐标',
                           key: 'meal_price'
                         },
                         {
                           label: '餐标合计',
                           key: 'total_meal_price'
                         }
                       ]
                     },

                     {
                       title: '其他信息',
                       items: [
                         {
                           label: '订单识别二维码',
                           key: 'order_info_qrcode',
                           type: 'QrCode'
                         },
                         {
                           label: '订单回单状态二维码',
                           key: 'receipt_order_qr',
                           type: 'QrCode'
                         },
                         {
                          label: '苏源e码通二维码',
                          key: 'traceability_e_code_qrcode',
                          show: 'traceability_platform_e_code === 1',
                          type: 'QrCode'
                        },
                         {
                           label: '客户溯源码',
                           key: 'user_trace_url',
                           type: 'QrCode'
                         },
                         {
                          label: '订单溯源码',
                          key: 'order_trace_url',
                           type: 'QrCode',
                           tips: '使用该溯源码，溯源信息中仅展示订单中的商品，合并多个订单打印订单数过多时会存在遗漏，不建议使用在合并订单场景。'
                         },
                         {
                           label: '客户签名图片',
                           key: 'signature_pic',
                           type: 'Image'
                         },
                         {
                           label: '本月应付',
                           key: 'current_month_unpaid_price',
                           show: 'is_open_exchange_rate === 0',
                           tips: '默认取值打印当月自然日1号开始的未收数据'
                         },
                         {
                           label: '装筐数',
                           key: 'hk_basket_desc',
                           show: 'is_open_exchange_rate === 1',
                           setting: genSettingConfig([
                             { key: DECIMAL_COMPLETION, value: false }
                           ])
                         },
                         {
                           label: '商品分类',
                           key: CATEGORY_NAME
                         },
                         {
                           label: '子机构',
                           key: 'subsidiary',
                           show:
                             'is_commodity_package === 1 && commodity_package_mode === 2 && is_new_recipe === 1 && is_open_subsidiary === 1'
                         },
                         {
                           label: '餐次',
                           key: 'meal_type_name',
                           show:
                             'is_commodity_package === 1 && commodity_package_mode === 2 && is_new_recipe === 1'
                         },
                         {
                           label: '制单人',
                           key: 'cur_op_name'
                         },
                         {
                           label: '制单人中文名',
                           key: 'op_cn_name'
                         },
                         {
                           label: '周转筐名称',
                           key: 'basket_name'
                         },
                         {
                           label: '周转筐数量',
                           key: 'basket_borrow_num'
                         },
                         {
                           label: '供应商名称',
                           key: 'split_provider_name',
                           show:
                             'is_open_split_order === 1 && split_order_mode  === 3'
                         },
                         {
                           label: '供应商联系人',
                           key: 'split_provider_company_name',
                           show:
                             'is_open_split_order === 1 && split_order_mode  === 3'
                         },
                         {
                           label: '供应商联系电话',
                           key: 'split_provider_tel',
                           show:
                             'is_open_split_order === 1 && split_order_mode  === 3'
                         },
                         {
                           label: '供应商地址',
                           key: 'split_provider_address_detail',
                           show:
                             'is_open_split_order === 1 && split_order_mode  === 3'
                         },
                         {
                           label: '实际总量',
                           key: 'total_actual_amount'
                         },
                         {
                           label: '特殊备注',
                           key: 'special_remark'
                         }
                       ]
                     }
                   ]
                 },
                 {
                   title: '收货信息',
                   items: [
                     {
                       label: '客户名称',
                       key: 'email'
                     },
                     {
                       label: '子账号',
                       key: 'sub_user_name'
                     },
                     {
                       label: '客户标签',
                       key: 'user_tag_text'
                     },
                     {
                       label: '联系人',
                       key: 'receive_name'
                     },
                     {
                       label: '收货手机',
                       key: 'receive_tel'
                     },
                     {
                       label: '店铺手机',
                       key: 'account_tel'
                     },
                     {
                       label: '客户编码',
                       key: 'user_code'
                     },
                     {
                       label: '客户编码条码',
                       type: 'Barcode',
                       key: 'user_code_barcode'
                     },
                     {
                       label: '客户临时编码',
                       key: 'sort_code'
                     },
                     {
                       label: '客户序号编码',
                       key: 'user_serial_code'
                     },
                     {
                       label: '集团名称',
                       key: 'user_group_name'
                     },
                     {
                       label: '收货地址',
                       key: 'address_detail'
                     },
                     {
                        label: '车牌号',
                        key: 'license_plate',
                        tips: '取值订单所属司机关联的车辆车牌号'
                      },
                   ]
                 }
               ]
             },
             {
               title: '分栏区字段',
               type: 'Normal',
               items: [
                 {
                   label: '订单号',
                   key: 'is_show_order_no',
                   type: 'Config',
                   disabled: true
                 },
                 {
                   label: '页码',
                   key: 'is_show_page_no',
                   type: 'Config',
                   disabled: true
                 },
                 {
                   label: '本页下单数量小计',
                   key: 'is_show_order_num_total',
                   type: 'Config'
                 },
                 {
                   label: '本页发货数量小计',
                   key: 'is_show_num_total',
                   type: 'Config'
                 },
                 {
                   label: '本页下单金额小计(小写)',
                   key: 'is_show_order_total',
                   type: 'Config'
                 },
                 {
                   label: '本页下单金额小计(大写)',
                   key: 'is_show_order_total_capital',
                   type: 'Config'
                   // disabled: true,
                 },
                 {
                   label: '本页发货金额小计(小写)',
                   key: 'is_show_pre_total',
                   type: 'Config'
                 },
                 {
                   label: '本页发货金额小计(大写)',
                   key: 'is_show_pre_total_capital',
                   type: 'Config'
                   //  disabled:true,
                 },
                 {
                   label: '本页折前小计(小写)',
                   key: 'is_show_pre_protocol_org_price_total',
                   type: 'Config'
                 },
                 {
                   label: '本页折前小计(大写)',
                   key: 'is_show_pre_protocol_org_price_total_capital',
                   type: 'Config'
                   //  disabled:true,
                 },
                 {
                   label: '本页合计发货折扣金额',
                   key: 'is_show_delivery_total_discount_price',
                   type: 'Config'
                 },
                 {
                   label: '本页合计发货折扣金额(大写)',
                   key: 'is_show_delivery_total_discount_price_capital',
                   type: 'Config'
                 },
                 {
                   label: '本页下单折前小计(小写)',
                   key: 'is_show_sub_protocol_org_price_total',
                   type: 'Config'
                 },
                 {
                   label: '本页下单折前小计(大写)',
                   key: 'is_show_sub_protocol_org_price_total_capital',
                   type: 'Config'
                   //  disabled:true,
                 },
                 {
                   label: '本页实际金额(小写)',
                   tips: '取值本页中所有实际金额的合计所得小写数值展示',
                   key: 'is_show_actual_sub_price_total',
                   type: 'Config'
                 },
                 {
                   label: '本页实际金额(大写)',
                   tips: '取值本页中所有实际金额的合计所得大写展示',
                   key: 'is_show_actual_sub_price_total_capital',
                   type: 'Config'
                   // disabled:true,
                 },
                 {
                   label: '下单数量总计',
                   key: 'is_show_order_total_amount',
                   type: 'Config'
                 },
                 {
                   label: '发货数量总计',
                   key: 'is_show_all_num_total',
                   type: 'Config'
                 },
                 {
                   label: '下单金额合计',
                   key: 'is_show_all_total_price',
                   type: 'Config'
                 },
                 {
                   label: '下单金额合计(大写)',
                   key: 'is_show_all_total_price_capital',
                   type: 'Config'
                   //  disabled: true,
                 },
                 {
                   label: '实际总价',
                   tips: '取值订单的发货小计+运费+押金筐+参与结算的服务费',
                   key: 'is_show_all_total',
                   type: 'Config',
                   setting: genSettingConfig([
                     { key: DECIMAL_COMPLETION, value: true }
                   ])
                 },
                 {
                  label: '本列发货小计',
                  key: 'is_show_current_column_total',
                  tips: '多列样式模式才生效，取值单独一列在本页中的发货金额合计',
                  type: 'Config',
                },
                 {
                   label: '实际总价(大写)',
                   tips: '取值订单的发货小计+运费+押金筐+参与结算的服务费的合计所得大写展示',
                   key: 'is_show_all_total_capital',
                   //  disabled:true,
                   type: 'Config'
                 },
                 {
                   label: '本页小计(字样)',
                   key: 'is_show_pre_total_txt',
                   type: 'Config'
                   //  disabled:true,
                 },
                 {
                   label: '本页实际金额(不含税)小计',
                   tips: '取值本页中所有实际金额（不含税）的合计所得小写数值展示',
                   key: 'is_show_actual_sub_price_excluding_tax',
                   type: 'Config'
                 },
                 {
                   label: '本页税额小计',
                   key: 'is_show_order_tax_rate_price',
                   type: 'Config'
                 },
                {
                  label: '本页抹零金额合计',
                  key: 'is_show_deliver_reduction_price',
                  type: 'Config'
                },
                {
                  label: '本页抹零金额合计(大写)',
                  key: 'is_show_deliver_reduction_price_capital',
                  type: 'Config'
                },
                {
                  label: '本页商品总数',
                  key: 'is_show_current_page_commodity_count',
                  tips: '取值为当前打印单据中当前页商品总数，一行商品为一个',
                  type: 'Config'
                },
                {
                  label: '所有页商品总数',
                  key: 'is_show_all_page_commodity_count',
                  tips: '取值为当前打印单据中所有的商品总数，一行商品为一个',
                  type: 'Config'
                },
                {
                  label: '全页下单折前总价',
                  key: 'is_show_all_page_alia_sub_protocol_org_price',
                  sumType: 'all',
                  dataKey: 'alia_sub_protocol_org_price',
                  tips: '所有页商品下单折前总价',
                  type: 'Config'
                },
                {
                  label: '全页折前总价',
                  key: 'is_show_all_page_alia_protocol_org_total_price',
                  sumType: 'all',
                  dataKey: 'alia_protocol_org_total_price',
                  tips: '所有页商品折前小计的合计金额',
                  type: 'Config'
                },
                {
                  label: '全页发货折前总价',
                  key: 'is_show_all_page_delivery_total_price',
                  sumType: 'all',
                  dataKey: 'delivery_total_price',
                  tips: '所有页商品发货折前小计的合计金额',
                  type: 'Config'
                },
                {
                  label: '全页商品成本金额合计',
                  key: 'is_show_all_page_order_in_total_price',
                  sumType: 'all',
                  dataKey: 'order_in_total_price',
                  type: 'Config'
                },
               ]
             }
           ]
         }
       };

/**
 * 生成订单预览数据
 * @param {number} count 商品数量
 * @returns {object} printData 预览的数据
 */
function orderDemoData(count = 20) {
  const printData = [];
  const order = {
    order_in_total_price: 24,
    delivery_total_price: 24,
    _commodity_count: 20,
    delivery_total_discount_price: '10.00',
    delivery_total_discount_price_capital: '壹拾元整',
    total_deliver_reduction_price: '122.22',
    total_deliver_reduction_price_capital: '壹佰贰拾贰元贰角贰分',
    order_no: 'DD220726164037850123',
    create_time: '2020-11-24 19:16:26',
    create_time_week: '2020-11-24 19:16:26 星期一',
    delivery_date: '2020-11-26',
    delivery_date_week: '2020-11-26 星期二',
    order_remark: '货到了麻烦通知一下',
    order_total_amount: '12.00',
    order_total_amount_kg_mode:'6',
    total_actual_amount: '21.00',
    total_actual_amount_kg_mode:'10.5',
    total_shipped_quantity: '21.00',
    alia_protocol_org_total_price: '21.00',
    alia_protocol_org_total_price_UpperCase: '',
    alia_sub_protocol_org_price: '21.00',
    alia_sub_protocol_org_price_UpperCase: '',
    actual_total_pay_str: '100.00',
    actual_total_pay_str_capital: '',
    freight_price: '5.00',
    print_time: '2020-11-24 19:16:26',
    seller_name: '业务员小张',
    seller_mobile: '18888888888',
    driver: '司机老王',
    driver_mobile: '**********',
    area_name: '区域',
    shop_name: '五一广场线',
    stopgap_code: 'TC00001',
    license_plate: 'A00001',
    op_name: '操作人',
    cur_op_name: '制单人',
    pay_way_txt: '微信支付',
    is_pay:'已付款',
    price: '110.00',
    order_price: '110.00',
    pay_price: '110.00',
    should_pay_price: '100.00',
    should_pay_price_capital: num2RmbCaption(20.2),
    diff_price: '4.00',
    dec_price: '5.00',
    return_price: '10.00',
    order_delivery_time: '13:00~15:00',
    deposit_basket_total_price: '12.00',
    order_tax_rate_price: '100.00',
    order_no_tax_actual_price: '110.00',
    protocol_org_total_price: '110.00',
    current_month_unpaid_price: '110.00',
    total_origin_delivery_amount: '100.00',
    hk_basket_desc: '12',
    order_tags: '教师餐',
    order_category_name: '一级分类',
    order_qrcode: '订单信息二维码',
    order_commodity_qrcode: '订单商品二维码',
    order_info_qrcode: 'ORDER_QRCODE_01',
    receipt_order_qr: 'ORDER_QRCODE_02',
    traceability_e_code_qrcode: 'ORDER_QRCODE_03',
    user_trace_url: 'https://www.sdongpo.com',
    signature_pic: 'https://img.shudongpoo.com/common/sdp-logo.png',
    seller_attachment_link:'https://img.shudongpoo.com/common/sdp-logo.png',
    email: '客户小张',
    user_tag_text:'客户标签',
    cus_name: '联系人小李',
    user_code: 'UC001',
    sort_code: 'A-1',
    user_serial_code: '001',
    account_tel: '***********',
    category_name: '一级分类',
    receive_tel: '***********',
    sub_user_name: '张三',
    address_detail: '湖南长沙',
    user_group_name: '文和友',
    order_service_charge: '110.00',
    should_pay_price_contain_service_charge: '110.00',
    split_provider_name:'供应商名称',
    split_provider_company_name:'刘总',
    split_provider_tel:'110',
    split_provider_address_detail:'地址',
    actual_price:'100',
    basket_name: '周转筐名称',
    basket_borrow_num: '6.00',
    delivery_price:'11',
    items: [],
    diner_dates: '2023-12-12',
    diner_dates_with_week: '2023-12-12(周日)',
    number_of_diners: '5',
    difference_money: 1,
    accompanying_of_diners: 12,
    order_trace_url: 'https://img.shudongpoo.com/common/sdp-logo.png?url=https://img.shudongpoo.com/common/sdp-logo.png&src=https://img.shudongpoo.com/common/sdp-logo.png'
  };
  order.actual_total_pay_str_capital = num2RmbCaption(
    order.actual_total_pay_str
  );
  let index = 1;
  for (let i = 0; i < count; i++) {
    index = i + 1;
    const goodsItem = {
      no: index,
      name: '泡发海带丝' + index,
      amount_with_unit: '10斤',
      amount: '10',
      amount_with_unit1: '10斤',
      amount1: '10',
      unit: '斤',
      durability_period: '10天',
      mfg_date: '2020-11-26',
      production_date: '2020-11-26',
      expired_date: '2020-11-26',
      batch_no: '123123',
      product_place: '长沙',
      actual_amount_with_unitsell: '12.00斤',
      actual_amount: '12.00',
      unit_sell: '斤',
      unit_price: '12.00',
      price_float_rate: '5',
      actual_price: '12.00',
      summary: '薄一点的',
      tax_rate: '6.00',
      tax_rate_price: '8.00',
      remark: '要老一点的',
      remark2: '计算公式',
      category_name: '一级分类'+(i%9),
      category_name2: '二级分类',
      commodity_code: 'G0001',
      row_money2: '20.00',
      row_money: '21.00',
      return_amount: '10.10',
      return_info_price: '20.00',
      return_total_price: '22.00',
      deposit_basket_name: '5.00',
      deposit_basket_amount: '6.00',
      item_deposit_basket_total_price: '8.00',
      protocol_org_price: '10.23',
      alia_protocol_org_price: '10.23',
      protocol_discount: '60',
      brand: '商品品牌',
      commodity_code_bar_code: (index + '').padStart(5, 0),
      price: '20.00',
      order_price: '20.00',
      actual_sub_price: '20.20',
      actual_sub_price_str_capital: num2RmbCaption(20.2),
      actual_sub_price_excluding_tax: '23.00',
      delivery_price: '1',
      origin_delivery_amount: '2斤',
      origin_delivery_amount_exclusive_unit:'2',
      unit_convert_description: '0.1',
      pool_provider_name: '供应商',
      minus_price_float_rate: '16',
      receive_actual_amount: '12.22',
      unit_price_no_tax: '5.06',
      actual_price_no_tax: '5.06',
      protocol_org_total_price: '45.06',
      service_charge: '45.06',
      bar_code: 'C001',
      order_tags: 'VIP',
      is_stockout_text:'缺货',
      is_sorting_text:'未分拣',
      net_output_rate:'1',
      net_amount:2,
      protocol_org_price_no_tax:'3',
      delivery_price_no_tax:'2',
      delivery_total_price:'11',
      delivery_total_price_no_tax:'10',
      sub_protocol_org_price: '9',
      difference_money: 1,
      default_provider: '默认供应商',
      deliver_reduction_price: 2,
      delivery_total_discount_price: 1,
      batch_area_location_list: 'A区,B区',
    };
    order.items.push(goodsItem);
  }
  printData.push(order);
  return printData;
}

export const formatOrderDataMethods = _formatOrderDataMethods;
