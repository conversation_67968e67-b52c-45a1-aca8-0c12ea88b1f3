import storageUtil from './storage';
const EDITION_STORAGE_KEY = 'project_edition';
const COMMON_THEME = {
  '--common-primary-color': 'rgb(3 172 84)',
  '--common-green': 'rgb(3,172,84)' // 遇到需要固定此绿色值，而不根据主题色变化的情况时，使用此变量
};
// 精简版本需要排除的功能
const editionConfig = {
  // 基础版本
  6: {
    excludeFeature: [
      'config_register', // 客户配置->注册
      'protocol_price_discount', // 客户协议单折扣
      // 'is_user_edit', // 客户修改信息
      // 'lowest_order_price_mode', // 起订价模式
      'open_mall_order_modify', // 商城订单可编辑
      'automatically_jump_to_next_category', // 自动跳转至下一分类
      'open_mall_default_delivery_date', // 商城发货日期默认填写
      'config_process', // 业务配置->加工配置
      'config_source_tracing', // 业务配置->溯源配置
      'is_order_audit', // 订单审核
      // 'group_order_audit', // 集团审核门店订单
      'order_detail_show_total_amount', // 订单详情显示下单数量合计
      'is_order_quick_open', // 订单编辑是否启用键盘快捷键
      'is_open_purchase_task', // 生成采购单模式
      'min_stock_limit_flag', // 采购下限取值
      'max_stock_limit_flag', // 采购上限取值
      'last_few_days', // 日均销量取值
      'remember_pre_choice', // 预采分配取值
      'in_mode', // 入库模式
      'is_app_show_price', // 配送APP是否显示价格
      'open_order_unit_sorting', // 按下单单位分拣
      'use_new_sort_commodity', // 使用新版商品分拣页面
      'in_price_audit', // 审核入库金额
      'is_open_store_area_location', // 开启库位管理
      'open_picking_audit', // 领料出库模式
      'delivery_user_list_ordering_rule', // 配送助手客户排序
      'can_many_gift', // 支持多次满赠
      'point_enable', // 启用积分
      'point_lower_order_price', // 兑换积分商品，需购买商品起定价
      'point_ratio', // 积分兑换系数
      'refund_original_way_back', // 退款原路返回
      'wechat_app_is_enabled', // APP商城-微信支付
      'alipay_is_enabled', // APP商城-支付宝支付
      'account_bill_allow_mistake', // 客户对账允许误差值
      'purchase_bill_allow_mistake', // 采购对账允许误差值
      'goods_tax_class_code', // 商品税收分类编码
      'map_mode', // 地图模式
      'goods_volume', // 商品体积和重量
      'provider_contact_price', // 采购协议价
      'user_credit', // 客户积分
      'user_kg_print', // 客户公斤打印
      'invoice_enabled', // 发票开具
      'inspection_reports', // 检测单
      'traceability_platform_docking', // 溯源平台对接
      'enterprise_wechat_SCRM', // 企业微信SCRM
      // 'open_ali_life', // 支付宝生活号商城
      'service_charge', // 服务费
      'basket_manage', // 周转筐管理
      // 'group_manage', // 集团客户管理
      'goods_presell', // 商品预售
      'is_application_commodity_package', // 套餐管理
      'is_open_rfq', // 询价报价
      'commodity_parting', // 原料分割
      // 'is_user_trace', // 溯源与检测
      'miniprogram_live', // 小程序直播
      'sorting_and_boxing', // 分拣装框
      'is_commodity_barcode', // 条码对照表
      'is_sync_user_center', // 商城APP
      'is_enabled_processed_vegetables', // 加工移动端
      // 'is_open_storage_app', // 掌上库房
      'jcpt', // 集采平台
      'PRE_PACKAGE', // 预打包打印魔板
      'ORDER_CHECK', // 检测单打印魔板
      'PACKAGE', // 套餐打印魔板
      'allot', // 库房调拨
      'purchase_plan', // 采购任务
      'pre_purchase', // 预采购
      'log_before_510', // 5.10之前的操作日志
      'newVersionHelp', // top栏显示新功能
    ],
    // 付费功能
    payFeature: ['is_open_storage_app'],
    // 限时免费功能
    limitedPayFeature: [],
    theme: {
      ...COMMON_THEME,
      //
      '--primary-color': '#1468FF',
      '--primary-color-active': '#1468F0',
      '--primary-color-second': '#4D8DFF',
      '--primary-color-2': '#EEF4FF', // 主题色20%透明度
      '--primary-color-5': '#1468ff80', // 主题色50%透明度
      '--primary-color-input-hover': '#1468FF',
      '--primary-message-border': '#1468FF',
      '--primary-message-text': '#1468FF',
      '--primary-tr-hover': '#f2f8ff', // 主题色50%透明度
      '--primary-charts-gradient-light': '#1468F0',
      '--primary-charts-gradient-dark': '#1468FF',
      '--primary-charts': '#1468FF', // 图标主题色
      '--primary-charts-3': '#CAD9FF',
      '--primary-charts-2': '#EEF4FF',
      '--primary-charts-0': 'rgb(20, 103, 255, 0)',
    },
    // menuIconPrefix: 'basic-', // 图标前缀
    menuIconPrefix: '', // 图标前缀
    imageSuffix: '-basic', // 图片后缀
    // imageSuffix: '', // 图片后缀
    containerCssClass: 'basic-container'
  }
};
const SIMPLE_DEFAULT_THEME =  {
  ...COMMON_THEME,
  //
  '--primary-color': '#1468FF',
  '--primary-color-active': '#1468F0',
  '--primary-color-second': '#4D8DFF',
  '--primary-color-2': '#EEF4FF', // 主题色20%透明度
  '--primary-color-5': '#1468ff80', // 主题色50%透明度
  '--primary-color-input-hover': '#1468FF',
  '--primary-message-border': '#1468FF',
  '--primary-message-text': '#1468FF',
  '--primary-tr-hover': '#f2f8ff', // 主题色50%透明度
  '--primary-charts-gradient-light': '#1468F0',
  '--primary-charts-gradient-dark': '#1468FF',
  '--primary-charts': '#1468FF', // 图标主题色
  '--primary-charts-3': '#CAD9FF',
  '--primary-charts-2': '#EEF4FF',
  '--primary-charts-0': 'rgb(20, 103, 255, 0)',
}
const DEFAULT_THEME = {
  ...COMMON_THEME,
  '--primary-color': 'rgb(3 172 84)',
  '--primary-color-active': 'rgb(3 163 80)',
  '--primary-color-second': 'rgb(53 189 118)',
  '--primary-color-2': 'rgb(234 251 242)', // 浅主题色背景
  '--primary-color-5': 'rgba(29,202,112,.5)', // 浅主题色边框
  '--primary-color-input-focus': '#03ac5433',
  '--primary-message-border': 'rgb(122 201 144)',
  '--primary-message-text': 'rgb(51 172 84)',
  '--primary-tr-hover': 'rgb(230 247 236)',
  '--primary-charts': 'rgba(3, 172, 84, 1)', // 图表主题色
  '--primary-charts-4': 'rgba(3, 172, 84, 0.4)',
  '--primary-charts-3': 'rgba(3, 172, 84, 0.3)',
  '--primary-charts-2': 'rgba(3, 172, 84, 0.2)',
  '--primary-charts-0': 'rgba(3, 172, 84, 0)',
  '--primary-charts-gradient-light': '#00BA33',
  '--primary-charts-gradient-dark': '#00A54F',
};

/**
 * 将16进制颜色值转换为rgb
 * @param {string} color 16进制颜色值
 */
function color2Rgb(color) {
  if (color.startsWith('rgb')) {
    return color;
  }
  const colorNumber = color.substring(1, 7);
  const R = parseInt(colorNumber.substring(0, 2), 16);
  const G = parseInt(colorNumber.substring(2, 4), 16);
  const B = parseInt(colorNumber.substring(4, 6), 16);
  return `rgb(${R}, ${G}, ${B})`;
}

/**
 * 设置主题变量
 * @param {object} themeInfo 主题信息
 */
const _setTheme = themeInfo => {
  const themeContainer = document.body;
  Object.keys(themeInfo).forEach(themeItem => {
    themeContainer.style.setProperty(themeItem, themeInfo[themeItem]);
  });
};

/**
 * 根据项目版本设置项目主题
 */
const setEditionTheme = () => {
  const currentEditionConfig = _getEdition();
  if (!currentEditionConfig) {
    _setTheme(DEFAULT_THEME);
  } else {
    _setTheme(currentEditionConfig.theme);
  }
};

/**
 * 保存项目版本
 * @param {number|string} edition 项目版本
 */
const setEdition = edition => {
  // 这里正常情况下会传数字
  // 避免存入无意义的值（undefined），导致前端运行报错
  if (edition !== 0 && !edition) {
    return;
  }
  storageUtil.setLocalStorage(EDITION_STORAGE_KEY, edition);
  setEditionTheme();
};

/**
 * 获取当前项目对应的项目版本
 */
const _getEdition = () => {
  const edition = storageUtil.getLocalStorage(EDITION_STORAGE_KEY);
  if (isNaN(edition) || edition === null) {
    return null;
  }
  return editionConfig[Number(edition)];
};

/**
 * 获取项目版本对应的容器css类，用于做特殊样式处理
 * @returns string cssClass 类名
 */
const getContainerCssClass = () => {
  const edition = _getEdition();
  return edition ? edition.containerCssClass : '';
};

/**
 * 获取项目版本对应的图片后缀
 * @returns string suffix 版本图片对应后缀
 */
const getImageSuffix = () => {
  const edition = _getEdition();
  if (!edition || !edition.imageSuffix) {
    return '';
  }
  return edition.imageSuffix;
};

/**
 * 获取项目版本对应的图片地址
 * @param {string} img图片地址
 * @returns string img 处理后的图片地址
 */
const getImage = img => {
  const edition = _getEdition();
  if (!edition || !edition.imageSuffix) {
    return img;
  }
  img.replace(
    /(.*)\.(png|jpg|jpeg|gif|svg)/g,
    ($0, $1, $2) => `${$1}${edition.imageSuffix}.${$2}`
  );
  return img;
};

/**
 * 处理版本对应的菜单数据
 * @param {object} menuData 菜单数据
 * @returns menuData 处理后的菜单数据
 */
const setMenuMeta = menuData => {
  const edition = _getEdition();
  if (!edition || !edition.menuIconPrefix) {
    return menuData;
  }
  menuData.forEach(item => {
    item.icon = edition.menuIconPrefix + item.icon;
  });
  return menuData;
};

/**
 * 判断当前项目版本是否有传入功能模块
 * @param {string} moduleId 模块|配置id
 */
const hasModule = moduleId => {
  const currentEditionConfig = _getEdition();
  if (!currentEditionConfig) {
    return true;
  }
  if (
    currentEditionConfig &&
    currentEditionConfig.excludeFeature.includes(moduleId)
  ) {
    return false;
  }
  return true;
};

/**
 * 判断当前项目版本是否有传入功能模块是否是付费模块
 * @param {string} moduleId 模块|配置id
 */
const isPayModule = moduleId => {
  const currentEditionConfig = _getEdition();
  if (!currentEditionConfig) {
    return false;
  }
  if (
    currentEditionConfig &&
    currentEditionConfig.payFeature.includes(moduleId)
  ) {
    return true;
  }
  return false;
};

/**
 * 判断当前项目版本是否有传入功能模块是否是限时免费模块
 * @param {string} moduleId 模块|配置id
 */
const isLimitedPayModule = moduleId => {
  const currentEditionConfig = _getEdition();
  if (!currentEditionConfig) {
    return false;
  }
  if (
    currentEditionConfig &&
    currentEditionConfig.limitedPayFeature.includes(moduleId)
  ) {
    return true;
  }
  return false;
};

/**
 * @returns {object} 主题颜色
 */
const getEditionThemeColor = () => {
  const edition = _getEdition();
  if (!edition || !edition.imageSuffix) {
    return DEFAULT_THEME;
  }
  const themeColor = {};
  Object.keys(edition.theme).map(colorKey => {
    themeColor[colorKey] = color2Rgb(edition.theme[colorKey]);
  });
  return themeColor;
};
const getSimpleEditionThemeColor = () => {
  return SIMPLE_DEFAULT_THEME;
}
// 版本功能模块指令
const moduleDirective = {
  inserted: (el, { value }) => {
    if (!hasModule(value)) {
      el.remove();
    }
  }
};

export {
  setEdition,
  setEditionTheme,
  hasModule,
  isPayModule,
  isLimitedPayModule,
  moduleDirective,
  setMenuMeta,
  getContainerCssClass,
  getImage,
  getImageSuffix,
  getEditionThemeColor,
  getSimpleEditionThemeColor
};
