const index = resolve => require(['@/pages/appCenter/index'], resolve);
// import index from '@/pages/appCenter/index';
const cloudCommodityList = resolve =>
  require(['@/pages/appCenter/cloudCommodityList'], resolve);
const yyLinkIndex = resolve =>
  require(['@/pages/appCenter/YYLink/index'], resolve);
const yyLinkIndexNew = resolve =>
  require(['@/pages/appCenter/YYLinkNew/index'], resolve);
const appCenterList = resolve =>
  require(['@/pages/appCenter/appCenterList'], resolve);
const cloudGoodDetail = resolve =>
  require(['@/pages/appCenter/cloudGoodDetail'], resolve);
const sceneOrder = resolve =>
  require(['@/pages/appCenter/sceneOrder'], resolve);
const list = resolve => require(['@/pages/appCenter/list'], resolve);
// import list from '@/pages/appCenter/list';
const orderImportTemplate = resolve =>
  require(['@/pages/orderImportTemplate/list'], resolve);
const costCalculation = resolve =>
  require(['@/pages/appCenter/costCalculation/index'], resolve);
const customizeField = resolve =>
  require(['@/pages/appCenter/customizeField/index'], resolve);
const templateSettings = resolve =>
  require(['@/pages/orderImportTemplate/templateSettings'], resolve);
const details = resolve =>
  require(['@/pages/orderImportTemplate/details'], resolve);
const posIndex = resolve => require(['@/pages/appCenter/pos/index'], resolve);
const posDragonIndex = resolve =>
  require(['@/pages/appCenter/posDragon/index'], resolve);
const posSlowcowIndex = resolve =>
  require(['@/pages/appCenter/posSlowcow/index'], resolve);
const posDragonStandardIndex = resolve =>
  require(['@/pages/appCenter/posDragonStandard/index'], resolve);
const meituanIndex = resolve =>
  require(['@/pages/appCenter/meituan/index'], resolve);
const orderCustomizeField = resolve =>
  require(['@/pages/appCenter/orderCustomizeField/index'], resolve);
const printReceipt = resolve =>
  require(['@/pages/appCenter/printReceipt/index'], resolve);

const Index = resolve => require(['@/pages/appCenter/yongyouU8/index.vue'], resolve);
const Auth = resolve => require(['@/pages/appCenter/yongyouU8/auth.vue'], resolve);
const Sync = resolve => require(['@/pages/appCenter/yongyouU8/sync.vue'], resolve);
const SyncUnit = resolve =>require(['@/pages/appCenter/yongyouU8/sync-unit/index.vue'], resolve);
const Certificate = resolve =>require(['@/pages/appCenter/yongyouU8/certificate/index.vue'], resolve);
const Logs = resolve =>require(['@/pages/appCenter/yongyouU8/logs/index.vue'], resolve);

export default {
  path: '/appCenter',
  component: index,
  meta: [{ title: '应用中心', url: '/appCenter/index' }],
  children: [
    {
      path: 'sort-verify',
      component: resolve =>
        require(['@/pages/appCenter/sort-verify/index'], resolve),
    },
    {
      path: '/yongyouU8',
      component: Index,
      children: [
        {
          path: 'auth',
          component: Auth,
          meta: {
            title: '初始化',
            noBreadCrumbs: true
          }
        },
        {
          path: 'sync',
          component: Sync,
          meta: {
            title: '同步配置',
            noBreadCrumbs: true
          }
        },
        {
          path: 'sync-unit/:type',
          component: SyncUnit,
          meta: {
            title: '往来单位',
            noBreadCrumbs: true
          }
        },
        {
          path: 'certificate',
          component: Certificate,
          meta: {
            title: '凭证',
            noBreadCrumbs: true
          }
        },
        {
          path: 'logs',
          component: Logs,
          meta: {
            title: '同步日志',
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: 'beginningBalance',
      component: resolve =>
        require(['@/pages/appCenter/beginningBalance/index'], resolve),
      meta: [{ title: '期初账款', url: '/appCenter/beginningBalance' }],
    },
    {
      path: 'provider-splitting-bill',
      component: resolve =>
        require(['@/pages/appCenter/provider-splitting-bill/index'], resolve),
      children: [
        {
          path: 'audit/list',
          component: resolve => require(['@/pages/appCenter/provider-splitting-bill/audit/list'], resolve),
          meta: [{ title: '分账审核', noBreadCrumbs: true }]
        },
        {
          path: 'refund-fail/list',
          component: resolve => require(['@/pages/appCenter/provider-splitting-bill/refund-fail/list'], resolve),
          meta: [{ title: '退回失败', noBreadCrumbs: true }]
        },
        {
          path: 'flowing/list',
          component: resolve => require(['@/pages/appCenter/provider-splitting-bill/flowing/list'], resolve),
          meta: [{ title: '分账流水', noBreadCrumbs: true }]
        },
      ]
    },
    {
      path: 'gps',
      component: resolve => require(['@/pages/appCenter/gps/index'], resolve),
      meta: [{ title: '车辆GPS定位', url: '' }]
    },
    {
      path: 'kingdee-proof',
      component: resolve =>
        require(['@/pages/appCenter/kingdee-proof/index'], resolve),
      meta: [{ title: '金蝶EAS同步', url: '' }]
    },
    {
      path: 'system',
      component: resolve =>
        require(['@/pages/appCenter/system/index'], resolve),
      meta: [{ title: '蔬东坡数据互联', url: '/appCenter/system' }],
      children: [
        { path: '', redirect: 'logs' },
        {
          path: 'logs',
          name: 'logs',
          component: resolve =>
            require(['@/pages/appCenter/system/logs.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        },
        {
          path: 'goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/system/goods.vue'], resolve),
          meta: [{ title: '商品同步', url: '' }]
        },
        {
          path: 'customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/system/customer.vue'], resolve),
          meta: [{ title: '客户同步', url: '' }]
        },
        {
          path: 'print',
          name: 'print',
          component: resolve =>
            require(['@/pages/appCenter/system/print.vue'], resolve),
          meta: [{ title: '打印模板', url: '' }]
        },
        {
          path: 'order',
          name: 'order',
          component: resolve =>
            require(['@/pages/appCenter/system/order.vue'], resolve),
          meta: [{ title: '订单同步', url: '' }]
        }
      ]
    },
    {
      path: 'providerComment',
      component: resolve => require(['@/pages/appCenter/providerComment/index'], resolve),
      meta: [{ title: '供应商评价', url: '/appCenter/providerComment' }],
      children: [
        { path: '', redirect: 'setting' },
        {
          path: 'list',
          name: 'list',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/list.vue'], resolve),
          meta: [{ title: '评价管理', url: '' }]
        },
        {
          path: 'stat',
          name: 'stat',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/stat.vue'], resolve),
          meta: [{ title: '评价统计', url: '' }]
        },
        {
          path: 'setStar',
          name: 'setStar',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/setStar.vue'], resolve),
          meta: [{ title: '打星维度', url: '' }]
        },
        {
          path: 'statManage',
          name: 'statManage',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/statManage.vue'], resolve),
          meta: [{ title: '评价管理', url: '' }]
        },
        {
          path: 'statStatistics',
          name: 'statStatistics',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/statStatistics.vue'], resolve),
          meta: [{ title: '评价统计', url: '' }]
        },
        {
          path: 'setting',
          name: 'setting',
          component: resolve =>
            require(['@/pages/appCenter/providerComment/setting.vue'], resolve),
          meta: [{ title: '设置', url: '' }]
        },
      ]
    },
    {
      path: 'crm',
      component: resolve => require(['@/pages/appCenter/crm/index'], resolve),
      meta: [{ title: '客户CRM', url: '/appCenter/crm' }],
      children: [
        { path: '', redirect: 'list' },
        {
          path: 'list',
          name: 'list',
          component: resolve =>
            require(['@/pages/appCenter/crm/list.vue'], resolve),
          meta: [{ title: '人员管理', url: '' }]
        },
        {
          path: 'visit',
          name: 'visit',
          component: resolve =>
            require(['@/pages/appCenter/crm/visit'], resolve),
          meta: [{ title: '拜访信息', url: '/appCenter/crm/visit' }],
          children: [
            { path: '', redirect: 'customer' },
            {
              path: 'customer',
              name: 'customer',
              component: resolve =>
                require(['@/pages/appCenter/crm/visit/customer'], resolve),
              meta: [{ title: '客户维度', url: '' }]
            },
            {
              path: 'detail',
              name: 'detail',
              component: resolve =>
                require(['@/pages/appCenter/crm/visit/detail'], resolve),
              meta: [{ title: '拜访明细', url: '' }]
            },
          ]
        },
        {
          path: 'bpm',
          name: 'bpm',
          component: resolve =>
            require(['@/pages/sales/businessPerformance.vue'], resolve),
          meta: [{ title: '业务绩效', url: '' }]
        },
        {
          path: 'task',
          name: 'task',
          component: resolve =>
            require(['@/pages/appCenter/crm/taskManage.vue'], resolve),
          meta: [{ title: '任务管理', url: '' }]
        },
        {
          path: 'setting',
          name: '设置',
          component: resolve =>
            require(['@/pages/appCenter/crm/setting.vue'], resolve),
          meta: [{ title: '设置', url: '' }]
        },
        {
          path: 'info',
          name: '使用指南',
          component: resolve =>
            require(['@/pages/appCenter/crm/info.vue'], resolve),
          meta: [{ title: '使用指南', url: '' }]
        },
        {
          path: 'team-add',
          name: 'team-add',
          component: resolve =>
            require(['@/pages/appCenter/crm/team-add.vue'], resolve)
        },
        {
          path: 'team-edit',
          name: 'team-edit',
          component: resolve =>
            require(['@/pages/appCenter/crm/team-add.vue'], resolve)
        },
        {
          path: 'team-detail',
          name: 'team-detail',
          component: resolve =>
            require(['@/pages/appCenter/crm/team-add.vue'], resolve)
        },
        {
          path: 'sales-add',
          name: 'sales-add',
          component: resolve =>
            require(['@/pages/appCenter/crm/form.vue'], resolve)
        },
        {
          path: 'sales-edit',
          name: 'sales-edit',
          component: resolve =>
            require(['@/pages/appCenter/crm/form.vue'], resolve)
        },
        {
          path: 'task-add',
          name: 'task-add',
          component: resolve =>
            require(['@/pages/appCenter/crm/taskAdd.vue'], resolve)
        },
        {
          path: 'task-edit',
          name: 'task-edit',
          component: resolve =>
            require(['@/pages/appCenter/crm/taskAdd.vue'], resolve)
        },
        {
          path: 'task-detail',
          name: 'task-detail',
          component: resolve =>
            require(['@/pages/appCenter/crm/taskDetail.vue'], resolve),
          meta: { showFootLogo: false }
        }
      ]
    },
    {
      path: 'mall-inventory',
      component: resolve => require(['@/pages/appCenter/mall-inventory/index'], resolve),
      meta: [{ title: '商城库存管理', url: '/appCenter/mall-inventory' }],
      children: [
        { path: '', redirect: 'typeList' },
        {
          path: 'typeList',
          name: 'typeList',
          component: resolve =>
            require(['@/pages/appCenter/mall-inventory/typeList.vue'], resolve)
        },
        {
          path: 'dataList',
          name: 'dataList',
          component: resolve =>
            require(['@/pages/appCenter/mall-inventory/dataList.vue'], resolve)
        },
        {
          path: 'videoList',
          name: 'videoList',
          component: resolve =>
            require(['@/pages/appCenter/mall-inventory/videoList.vue'], resolve)
        },
        {
          path: 'setting',
          name: 'setting',
          component: resolve =>
            require(['@/pages/appCenter/mall-inventory/setting.vue'], resolve)
        },
      ]
    },
    {
      path: '/traceabilityPlatform/apollo',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/apollo/index'], resolve),
      meta: [{ title: '教育交易监管平台', url: '/appCenter/traceabilityPlatform/apollo' }],
      children: [
        {
          path: '/traceabilityPlatform/apollo/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo/order',
          name: 'order',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo/order.vue'], resolve),
          meta: [{ title: '订单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/anxianxing',
      component: () => import('@/pages/appCenter/traceabilityPlatform/anxianxing/index.vue'),
      meta: [{ title: '安鲜行', url: '/appCenter/traceabilityPlatform/anxianxing' }],
      children: [
        {
          path: '/traceabilityPlatform/anxianxing/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/init'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/asyncConfig',
          name: 'asyncConfig',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/asyncConfig'], resolve),
          meta: [{ title: '同步设置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/goods'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/customer'], resolve),
          meta: [{ title: '客户', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/supplier',
          name: 'supplier',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/supplier'], resolve),
          meta: [{ title: '供应商', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/order',
          name: 'order',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/order'], resolve),
          meta: [{ title: '订单上报', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/standing-book',
          name: 'standing-book',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/standing-book'], resolve),
          meta: [{ title: '台账上报', url: '' }]
        },
        {
          path: '/traceabilityPlatform/anxianxing/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/anxianxing/log'], resolve),
          meta: [{ title: '上报日志', url: '' }]
        },
      ]
    },
    {
      path: '/traceabilityPlatform/apollo-v2',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/apollo-v2/index'], resolve),
      meta: [{ title: '教育交易监管平台', url: '/appCenter/traceabilityPlatform/apollo-v2' }],
      children: [
        {
          path: '/traceabilityPlatform/apollo-v2/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo-v2/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo-v2/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo-v2/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/apollo-v2/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/apollo-v2/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
      ]
    },
    {
      path: '/traceabilityPlatform/shaoxing',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/shaoxing/index'], resolve),
      meta: [{ title: '绍兴校园食材配送溯源管理系统', url: '/appCenter/traceabilityPlatform/shaoxing' }],
      children: [
        {
          path: '/traceabilityPlatform/shaoxing/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/shaoxing/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/shaoxing/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/shaoxing/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/shaoxing/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/shaoxing/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/shaoxing/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/shaoxing/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/suyuan',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/suyuan/index'], resolve),
      meta: [{ title: '苏源e码通平台', url: '/appCenter/traceabilityPlatform/suyuan' }],
      children: [
        {
          path: '/traceabilityPlatform/suyuan/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/suyuan/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/suyuan/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/suyuan/goods.vue'], resolve),
          meta: [{ title: '商品设置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/suyuan/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/suyuan/customer.vue'], resolve),
          meta: [{ title: '客户设置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/suyuan/order',
          name: 'order',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/suyuan/order.vue'], resolve),
          meta: [{ title: '订单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/suyuan/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/suyuan/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/hainan',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/hainan/index'], resolve),
      meta: [{ title: '海南省平价菜管理平台', url: '/appCenter/traceabilityPlatform/hainan' }],
      children: [
        {
          path: '/traceabilityPlatform/hainan/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/provider',
          name: 'provider',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/provider.vue'], resolve),
          meta: [{ title: '供应商映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/in',
          name: 'in',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/in.vue'], resolve),
          meta: [{ title: '入库单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/out',
          name: 'out',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/out.vue'], resolve),
          meta: [{ title: '出库单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/stock',
          name: 'stock',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/stock.vue'], resolve),
          meta: [{ title: '库存单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/hainan/asyncConfig',
          name: 'stock',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/hainan/asyncConfig/index.vue'], resolve),
          meta: [{ title: '同步设置', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/zhejiang',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/zhejiang/index'], resolve),
      meta: [{ title: '浙食链', url: '/appCenter/traceabilityPlatform/zhejiang' }],
      children: [
        {
          path: '/traceabilityPlatform/zhejiang/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/asyncConfig',
          name: 'asyncConfig',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/asyncConfig.vue'], resolve),
          meta: [{ title: '同步配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/provider',
          name: 'provider',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/provider.vue'], resolve),
          meta: [{ title: '供应商映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/in',
          name: 'in',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/in.vue'], resolve),
          meta: [{ title: '入库单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/zhejiang/out',
          name: 'out',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/zhejiang/out.vue'], resolve),
          meta: [{ title: '出库单同步', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/chongqing',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/chongqing/index'], resolve),
      meta: [{ title: '渝溯源', url: '/appCenter/traceabilityPlatform/chongqing' }],
      children: [
        {
          path: '/traceabilityPlatform/chongqing/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/customer',
          name: 'customer',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/customer.vue'], resolve),
          meta: [{ title: '客户映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/provider',
          name: 'provider',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/provider.vue'], resolve),
          meta: [{ title: '供应商映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/in',
          name: 'in',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/in.vue'], resolve),
          meta: [{ title: '入库单同步', url: '' }]
        },
        {
          path: '/traceabilityPlatform/chongqing/out',
          name: 'out',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/chongqing/out.vue'], resolve),
          meta: [{ title: '出库单同步', url: '' }]
        }
      ]
    },
    {
      path: '/traceabilityPlatform/guizhou',
      component: resolve => require(['@/pages/appCenter/traceabilityPlatform/guizhou/index'], resolve),
      meta: [{ title: '贵州食安追溯平台', url: '/appCenter/traceabilityPlatform/guizhou' }],
      children: [
        {
          path: '/traceabilityPlatform/guizhou/init',
          name: 'init',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/guizhou/init.vue'], resolve),
          meta: [{ title: '授权配置', url: '' }]
        },
        {
          path: '/traceabilityPlatform/guizhou/goods',
          name: 'goods',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/guizhou/goods.vue'], resolve),
          meta: [{ title: '商品映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/guizhou/provider',
          name: 'provider',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/guizhou/provider.vue'], resolve),
          meta: [{ title: '供应商映射', url: '' }]
        },
        {
          path: '/traceabilityPlatform/guizhou/log',
          name: 'log',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/guizhou/log.vue'], resolve),
          meta: [{ title: '同步日志', url: '' }]
        },
        {
          path: '/traceabilityPlatform/guizhou/in',
          name: 'in',
          component: resolve =>
            require(['@/pages/appCenter/traceabilityPlatform/guizhou/in.vue'], resolve),
          meta: [{ title: '入库单同步', url: '' }]
        },
      ]
    },
    {
      path: 'exact_marketing',
      component: resolve =>
        require(['@/pages/appCenter/exactMarketing/index'], resolve),
      meta: [{ title: '精准营销', url: '/appCenter/exact_marketing' }]
    },
    {
      path: 'group-manage',
      component: resolve =>
        require(['@/pages/appCenter/group-manage/index'], resolve),
      meta: [{ title: '集团客户管理', url: '' }]
    },
    {
      path: 'express100',
      component: resolve =>
        require(['@/pages/appCenter/express100/index'], resolve),
      meta: [{ title: '快递100', url: '' }]
    },
    {
      path: 'orderTagManage',
      component: resolve =>
        require(['@/pages/appCenter/orderTagManage/index'], resolve),
      meta: [{ title: '订单标签', url: '' }]
    },
    {
      path: 'selfPickUp',
      component: resolve =>
        require(['@/pages/appCenter/selfPickUp/index'], resolve),
      meta: [{ title: '自提点', url: '' }]
    },
    {
      path: 'store',
      component: resolve => require(['@/pages/appCenter/store/index'], resolve),
      meta: [{ title: '掌上库房', url: '' }]
    },
    {
      path: 'presell',
      component: resolve =>
        require(['@/pages/appCenter/presell/index'], resolve),
      meta: [{ title: '商品预售', url: '' }]
    },
    {
      path: 'affordableGoodsManagement',
      component: resolve =>
        require(['@/pages/appCenter/affordableGoodsManagement/index'], resolve),
      meta: [{ title: '平进平出商品管理', url: '' }]
    },

    {
      path: 'customerCommodityAlias',
      component: resolve =>
        require(['@/pages/appCenter/customerCommodityAlias/index'], resolve),
      children: [
        {
          path: '',
          redirect: 'goodsAlias'
        },
        {
          path: 'goodsAlias',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/goodsAlias/index'
            ], resolve),
          meta: [{ title: '商品别名', url: '' }]
        },
        {
          path: 'goodsAlias/new',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/goodsAlias/new'
            ], resolve),
          meta: [{ title: '新增商品别名', url: '' }]
        },
        {
          path: 'goodsAlias/edit',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/goodsAlias/new'
            ], resolve),
          meta: [{ title: '编辑商品别名', url: '' }]
        },
        {
          path: 'goodsAlias/detail',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/goodsAlias/detail'
            ], resolve),
          meta: [{ title: '商品别名详情', url: '' }]
        },
        {
          path: 'categoryAlias',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/categoryAlias/index'
            ], resolve),
          meta: [{ title: '分类别名', url: '' }]
        },
        {
          path: 'categoryAlias/new',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/categoryAlias/new'
            ], resolve),
          meta: [{ title: '新增分类别名', url: '' }]
        },
        {
          path: 'categoryAlias/edit',
          component: resolve =>
            require([
              '@/pages/appCenter/customerCommodityAlias/categoryAlias/new'
            ], resolve),
          meta: [{ title: '编辑分类别名', url: '' }]
        },
      ]
    },
    { path: '', redirect: 'index' },
    { path: 'index', redirect: 'list' },
    { path: 'list', component: list },
    { path: 'commodityList', component: appCenterList },
    {
      path: 'sceneOrder',
      component: sceneOrder,
      meta: [{ title: '现场采购订单', url: '' }]
    },
    { path: 'commodityDetail', component: cloudGoodDetail },
    {
      path: 'tender',
      component: resolve => require(['@/pages/appCenter/tender'], resolve),
      meta: [{ title: '招标信息', url: '' }]
    },
    {
      path: 'freshQuotation',
      component: resolve => require(['@/pages/appCenter/freshQuotation/index.vue'], resolve),
      meta: [{ title: '生鲜行情', url: '/appCenter/freshQuotation' }],
      children: [
        { path: '', redirect: 'binding-management' },
        {
          path: 'binding-management',
          name: 'binding-management',
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '商品绑定', url: '' }]
          },
          component: resolve =>
            require(['@/pages/appCenter/freshQuotation/BindingManagement.vue'], resolve)
        },
        {
          path: 'market-goods',
          name: 'market-goods',
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '农批价格', url: '' }]
          },
          component: resolve =>
            require(['@/pages/appCenter/freshQuotation/MarketGoods.vue'], resolve)
        },
        {
          path: 'list',
          name: 'list',
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '菜价行情', url: '' }]
          },
          component: resolve =>
            require(['@/pages/appCenter/freshQuotation/newIndex.vue'], resolve)
        },
        {
          path: 'analysis',
          name: 'analysis',
          meta: { noBreadCrumbs: true },
          component: resolve =>
            require(['@/pages/appCenter/freshQuotation/analysis.vue'], resolve)
        },
      ]
    },
    {
      path: 'cloudCommodityList',
      component: cloudCommodityList,
      meta: [{ title: '云商品库', url: '' }]
    },
    {
      path: 'appDetail',
      component: resolve =>
        require(['@/pages/appCenter/appDetail/index'], resolve),
      meta: [{ title: '获取应用', url: '' }]
    },
    {
      path: 'appPurchase',
      component: resolve =>
        require(['@/pages/appCenter/appPurchase/index'], resolve),
      meta: {
        noBreadCrumbs: true,
        _meta: [{ title: '采购助手', url: '' }]
      }
      // meta: {noBreadCrumbs: true}
    },
    {
      path: 'purchaseCosts',
      component: resolve =>
        require(['@/pages/appCenter/purchaseCosts/index'], resolve),
      meta: [{ title: '费用分摊', url: '' }]
    },
    {
      path: 'WarehouseManagement',
      component: resolve =>
        require(['@/pages/appCenter/WarehouseManagement/list'], resolve),
      meta: [{ title: '条码对照表', url: '' }]
    },
    {
      path: 'customerSource',
      component: resolve =>
        require(['@/pages/appCenter/customerSource/list'], resolve),
      meta: [{ title: '客户溯源', url: '' }]
    },
    {
      path: 'mobileOrder',
      component: resolve =>
        require(['@/pages/appCenter/mobileOrder/list'], resolve),
      meta: [{ title: '订单助手', url: '' }]
    },
    {
      path: 'live',
      component: resolve => require(['@/pages/appCenter/live/index'], resolve),
      meta: [{ title: '小程序直播', url: '' }]
    },
    {
      path: 'print',
      component: resolve =>
        require(['@/pages/appCenter/autoPrint/index'], resolve),
      meta: [{ title: '自动打印', url: '' }]
    },
    {
      path: 'smzk',
      component: resolve => require(['@/pages/appCenter/smzk/index'], resolve),
      meta: [{ title: '扫码装框', url: '' }]
    },
    {
      path: 'standard-warehouse',
      component: resolve =>
        require(['@/pages/appCenter/standard-warehouse/index'], resolve),
      meta: { noBreadCrumbs: true, }
    },
    {
      path: 'separateOrder',
      component: resolve =>
        require(['@/pages/appCenter/separateOrder/index'], resolve),
      children: [
        {
          path: '',
          redirect: 'settings'
        },
        {
          path: 'settings',
          component: resolve =>
            require([
              '@/pages/appCenter/separateOrder/settings/index'
            ], resolve),
          meta: [{ title: '拆单设置', url: '' }]
        },
        {
          path: 'settings/new',
          component: resolve =>
            require([
              '@/pages/appCenter/separateOrder/settings/new'
            ], resolve),
          meta: [{ title: '新增拆单模板', url: '' }]
        },
        {
          path: 'settings/edit',
          component: resolve =>
            require([
              '@/pages/appCenter/separateOrder/settings/new'
            ], resolve),
          meta: [{ title: '编辑拆单模板', url: '' }]
        },
        {
          path: 'customer-settings',
          component: resolve =>
            require([
              '@/pages/appCenter/separateOrder/customer-settings/index'
            ], resolve),
          meta: [{ title: '拆单客户', url: '' }]
        },
        {
          path: 'merge-settings',
          component: resolve =>
            require([
              '@/pages/appCenter/separateOrder/merge-settings/index'
            ], resolve),
          meta: [{ title: '合单设置', url: '' }]
        }
      ]
    },
    {
      path: 'exchange_rate',
      meta: [{ title: '汇率管理', url: '' }],
      component: resolve => require(['@/pages/appCenter/exchangeRate'], resolve)
    },
    {
      path: 'wholesale-market-price-intelligent-pricing',
      component: resolve =>
        require([
          '@/pages/appCenter/wholesale-market-price-intelligent-pricing/index'
        ], resolve),
      redirect:
        '/appCenter/wholesale-market-price-intelligent-pricing/binding-management',
      meta: { noBreadCrumbs: true },
      children: [
        {
          path: 'binding-management',
          component: resolve =>
            require([
              '@/pages/appCenter/wholesale-market-price-intelligent-pricing/BindingManagement'
            ], resolve),
          meta: { noBreadCrumbs: true }
        },
        {
          path: 'market-goods',
          component: resolve =>
            require([
              '@/pages/appCenter/wholesale-market-price-intelligent-pricing/MarketGoods'
            ], resolve),
          meta: { noBreadCrumbs: true }
        },
        {
          path: 'market-setting',
          component: resolve =>
            require([
              '@/pages/appCenter/wholesale-market-price-intelligent-pricing/MarketSetting'
            ], resolve),
          meta: { noBreadCrumbs: true }
        }
      ]
    },
    {
      path: 'mall-must-order',
      component: resolve =>
        require(['@/pages/appCenter/mall-must-order/index'], resolve),
      redirect: 'mall-must-order/main',
      children: [
        {
          path: 'main',
          component: resolve =>
            require(['@/pages/appCenter/mall-must-order/main'], resolve),
          meta: { noBreadCrumbs: true }
        },
        {
          path: 'form',
          component: resolve =>
            require(['@/pages/appCenter/mall-must-order/form'], resolve),
          meta: { noBreadCrumbs: true }
        }
      ]
    },
    {
      path: 'onlineService',
      component: resolve =>
        require(['@/pages/appCenter/onlineService/index'], resolve),
      meta: [{ title: '在线客服', url: '' }]
    },
    {
      path: '/YYLink',
      component: yyLinkIndex,
      children: [
        {
          path: '/YYLink/Management',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/Management'], resolve)
        },
        {
          path: '/YYLink/syncSetting',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/syncSetting'], resolve)
        },
        {
          path: '/YYLink/syncLog',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/syncLog'], resolve)
        },
        {
          path: '/YYLink/syncGoods',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/syncGoods'], resolve)
        },
        {
          path: '/YYLink/syncOrders',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/syncOrders'], resolve)
        },
        {
          path: '/YYLink/syncUsers',
          component: resolve =>
            require(['@/pages/appCenter/YYLink/syncUsers'], resolve)
        }
      ]
    },
    {
      path: '/pos',
      component: posIndex,
      children: [
        {
          path: '/pos/init',
          component: resolve =>
            require(['@/pages/appCenter/pos/init'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/pos/syncLog',
          component: resolve =>
            require(['@/pages/appCenter/pos/syncLog'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/pos/goods',
          component: resolve =>
            require(['@/pages/appCenter/pos/goods'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/pos/customer',
          component: resolve =>
            require(['@/pages/appCenter/pos/customer'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: '/posDragon',
      component: posDragonIndex,
      children: [
        {
          path: '/posDragon/init',
          component: resolve =>
            require(['@/pages/appCenter/posDragon/init'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragon/syncLog',
          component: resolve =>
            require(['@/pages/appCenter/posDragon/syncLog'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragon/goods',
          component: resolve =>
            require(['@/pages/appCenter/posDragon/goods'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragon/customer',
          component: resolve =>
            require(['@/pages/appCenter/posDragon/customer'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: '/posSlowcow',
      component: posSlowcowIndex,
      children: [
        {
          path: '/posSlowcow/init',
          component: resolve =>
            require(['@/pages/appCenter/posSlowcow/init'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posSlowcow/syncLog',
          component: resolve =>
            require(['@/pages/appCenter/posSlowcow/syncLog'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posSlowcow/goods',
          component: resolve =>
            require(['@/pages/appCenter/posSlowcow/goods'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posSlowcow/customer',
          component: resolve =>
            require(['@/pages/appCenter/posSlowcow/customer'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: '/posDragonStandard',
      component: posDragonStandardIndex,
      children: [
        {
          path: '/posDragonStandard/init',
          component: resolve =>
            require(['@/pages/appCenter/posDragonStandard/init'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragonStandard/goods',
          component: resolve =>
            require(['@/pages/appCenter/posDragonStandard/goods'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragonStandard/customer',
          component: resolve =>
            require(['@/pages/appCenter/posDragonStandard/customer'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragonStandard/provider',
          component: resolve =>
            require(['@/pages/appCenter/posDragonStandard/provider'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/posDragonStandard/order',
          component: resolve =>
            require(['@/pages/appCenter/posDragonStandard/order'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: '/meituan',
      component: meituanIndex,
      children: [
        {
          path: '/meituan/init',
          component: resolve =>
            require(['@/pages/appCenter/meituan/init'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/meituan/syncLog',
          component: resolve =>
            require(['@/pages/appCenter/meituan/syncLog'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/meituan/goods',
          component: resolve =>
            require(['@/pages/appCenter/meituan/goods'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/meituan/customer',
          component: resolve =>
            require(['@/pages/appCenter/meituan/customer'], resolve),
          meta: {
            noBreadCrumbs: true
          }
        }
      ]
    },
    {
      path: '/YYLinkNew',
      component: yyLinkIndexNew,
      children: [
        {
          path: 'sync-goods',
          name: 'sync-goods',
          component: resolve =>
            require(['@/pages/appCenter/YYLinkNew/SyncGoods'], resolve)
        },
        {
          path: 'sync-purchase',
          name: 'sync-purchase',
          component: resolve =>
            require([
              '@/pages/appCenter/YYLinkNew/sync-purchase/index'
            ], resolve),
          redirect: 'sync-purchase-order',
          children: [
            {
              path: '/YYLinkNew/sync-purchase-order',
              name: 'sync-purchase-order',
              component: resolve =>
                require([
                  '@/pages/appCenter/YYLinkNew/sync-purchase/SyncPurchaseOrder'
                ], resolve)
            },
            {
              path: '/YYLinkNew/sync-purchase-return-order',
              name: 'sync-purchase-return-order',
              component: resolve =>
                require([
                  '@/pages/appCenter/YYLinkNew/sync-purchase/SyncPurchaseReturnOrder'
                ], resolve)
            }
          ]
        },
        {
          path: '/YYLinkNew/syncSetting',
          component: resolve =>
            require(['@/pages/appCenter/YYLinkNew/syncSetting'], resolve)
        },
        {
          path: '/YYLinkNew/SyncOrder',
          component: resolve =>
            require(['@/pages/appCenter/YYLinkNew/syncOrder/index'], resolve)
        },
        {
          path: '/YYLinkNew/SyncLog',
          component: resolve =>
            require(['@/pages/appCenter/YYLinkNew/syncLog'], resolve)
        },
        {
          path: '/YYLinkNew/newSyncUsers',
          component: resolve =>
            require(['@/pages/appCenter/YYLinkNew/newSyncUsers'], resolve)
        },
        {
          path: '/YYLinkNew/newSynchronousSupplier',
          component: resolve =>
            require([
              '@/pages/appCenter/YYLinkNew/newSynchronousSupplier'
            ], resolve)
        }
      ]
    },
    {
      path: 'orderImportTemplate',
      component: orderImportTemplate,
      meta: [{ title: '订单导入模板', url: '' }]
    },
    {
      path: 'costCalculation',
      component: costCalculation,
      meta: [{ title: '费用计算', url: '' }],
      children: [
        {
          path: 'service-charge',
          name: 'service-charge',
          component: resolve =>
            require([
              '@/pages/appCenter/costCalculation/serviceCharge/index'
            ], resolve),
          children: [
            {
              path: 'service-charge-template-list',
              name: 'service-charge-template-list',
              component: resolve =>
                require([
                  '@/pages/appCenter/costCalculation/serviceCharge/chargeTemplateList'
                ], resolve)
            },
            {
              path: 'customer-list',
              name: 'customer-list',
              component: resolve =>
                require([
                  '@/pages/appCenter/costCalculation/serviceCharge/customerList'
                ], resolve)
            }
          ]
        },
        {
          path: 'new',
          name: 'new',
          component: resolve =>
            require([
              '@/pages/appCenter/costCalculation/serviceCharge/new'
            ], resolve)
        },
        {
          path: 'sorter-salary',
          name: 'sorter-salary',
          component: resolve =>
            require(['@/pages/appCenter/costCalculation/sorterSalary'], resolve)
        },
        {
          path: 'driver-salary',
          name: 'driver-salary',
          component: resolve =>
            require(['@/pages/appCenter/costCalculation/driverSalary'], resolve)
        },
        {
          path: 'business-salary',
          name: 'business-salary',
          component: resolve =>
            require([
              '@/pages/appCenter/costCalculation/business-salary/index'
            ], resolve),
          children: [
            {
              path: 'business-salary-template',
              name: 'business-salary-template',
              component: resolve =>
                require([
                  '@/pages/appCenter/costCalculation/business-salary/businessSalaryTemplate'
                ], resolve)
            },
            {
              path: 'customer-manage',
              name: 'customer-manage',
              component: resolve =>
                require([
                  '@/pages/appCenter/costCalculation/business-salary/customerManage'
                ], resolve)
            }
          ]
        },
        {
          path: 'salaryNew',
          name: 'salaryNew',
          component: resolve =>
            require([
              '@/pages/appCenter/costCalculation/business-salary/new'
            ], resolve)
        }
      ]
    },
    {
      path: 'customizeField',
      component: customizeField,
      meta: [{ title: '自定义字段', url: '' }]
    },
    {
      path: 'orderCustomizeField',
      component: orderCustomizeField,
      meta: [{ title: '自定义字段', url: '' }]
    },
    {
      path: 'userCustomizeGoodsRemark',
      component: resolve =>
        require(['@/pages/appCenter/userCustomizeGoodsRemark/index'], resolve),
      children: [
        { path: '', redirect: 'index' },
        {
          path: 'index',
          name: 'userCustomizeGoodsRemark',
          meta: [{ title: '客户指定备注', url: '' }],
          component: resolve =>
            require([
              '@/pages/appCenter/userCustomizeGoodsRemark/list'
            ], resolve)
        },
        {
          path: 'add',
          name: 'userCustomizeGoodsRemarkAdd',
          meta: [{ title: '客户指定备注', url: '' }],
          component: resolve =>
            require(['@/pages/appCenter/userCustomizeGoodsRemark/add'], resolve)
        }
      ]
    },
    {
      path: 'sorting-threshold',
      component: resolve =>
        require(['@/pages/appCenter/sorting-threshold'], resolve),
      meta: { noBreadCrumbs: true, showFootLogo: false }
    },
    {
      path: 'templateSettings',
      component: templateSettings,
      meta: [{ title: '模板设置', url: '' }]
    },
    {
      path: 'details',
      component: details,
      meta: [{ title: '模板设置', url: '' }]
    },
    {
      path: 'auto-purchase-order',
      component: resolve =>
        require(['@/pages/appCenter/auto-purchase-order/index.vue'], resolve),
      meta: [{ title: '自动生成采购单' }],
      children: [
        {
          path: 'config',
          component: resolve =>
            require([
              '@/pages/appCenter/auto-purchase-order/config.vue'
            ], resolve)
        },
        {
          path: 'list',
          component: resolve =>
            require(['@/pages/appCenter/auto-purchase-order/list.vue'], resolve)
        }
      ]
    },
    {
      path: 'certificate',
      component: resolve =>
        require(['@/pages/appCenter/certificate/index.vue'], resolve),
      meta: { noBreadCrumbs: true },
      children: [
        {
          path: 'subject',
          component: resolve =>
            require(['@/pages/appCenter/certificate/subject.vue'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '科目配置', url: '' }]
          }
        },
        {
          path: 'template',
          component: resolve =>
            require(['@/pages/appCenter/certificate/template.vue'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '凭证模板', url: '' }]
          }
        },
        {
          path: 'template-edit',
          name: 'template-edit',
          component: resolve =>
            require([
              '@/pages/appCenter/certificate/template-edit.vue'
            ], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '凭证模板', url: '' }]
          }
        },
        {
          path: 'list',
          component: resolve =>
            require(['@/pages/appCenter/certificate/list.vue'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '凭证管理', url: '' }]
          }
        },
        {
          path: 'create',
          component: resolve =>
            require(['@/pages/appCenter/certificate/create.vue'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '凭证生成', url: 'appCenter/certificate/create' }]
          }
        },
        {
          path: 'setting',
          component: resolve =>
            require(['@/pages/appCenter/certificate/setting.vue'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '导出配置', url: 'appCenter/certificate/setting' }]
          }
        }
      ]
    },
    {
      path: 'quick-convert',
      component: resolve =>
        require(['@/pages/appCenter/convert/index'], resolve),
      meta: [{ title: '一键转换', url: '/appCenter/quick-convert' }]
    },
    {
      path: 'printReceipt',
      component: printReceipt,
      meta: [{ title: '打印小票', url: '/appCenter/printReceipt' }]
    },
    {
      path: 'order-goods-tag',
      redirect: 'order-goods-tag/list',
      component: resolve =>
        require(['@/pages/appCenter/orderGoodsTag'], resolve),
      children: [
        {
          path: 'list',
          component: resolve =>
            require(['@/pages/appCenter/orderGoodsTag/list.vue'], resolve),
          meta: { noBreadCrumbs: true }
        },
      ]
    },
    {
      path: 'quickLinks',
      component: resolve => require(['@/pages/appCenter/quickLinks'], resolve),
      meta: [{ title: '快捷链接', url: '/appCenter/quickLinks' }],
      children: [
        {
          path: '',
          redirect: 'list'
        },
        {
          path: 'list',
          component: resolve => require(['@/pages/appCenter/quickLinks/list'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '链接设置', url: '' }]
          }
        },
      ]
    },
    {
      path: 'temporary',
      component: resolve => require(['@/pages/appCenter/temporary'], resolve),
      meta: [{ title: '临时商品', url: '/appCenter/temporary' }],
      children: [
        {
          path: '',
          redirect: 'goods'
        },
        {
          path: 'goods',
          component: resolve => require(['@/pages/appCenter/temporary/goods'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [{ title: '临时商品', url: '' }]
          }
        },
      ]
    },
    {
      path: 'topSellPrice',
      component: resolve => require(['@/pages/appCenter/topSellPrice'], resolve),
      meta: [{ title: '最高销售价', url: '/appCenter/topSellPrice' }]
    }
  ]
};
