import Vue from 'vue';
import Router from 'vue-router';
import store from '../vuex';
import { isRuntimePro } from '@/util/index';
/* eslint-disable */
const orderApproval = resolve =>
  require(['@/pages/order/orderApproval'], resolve);
const orderDirectPurchaseList = resolve =>
  require(['@/pages/order/orderDirectPurchaseList'], resolve);
const orderDetail = resolve => require(['@/pages/order/OrderDetail'], resolve);
const newOrder = resolve => require(['@/pages/order/NewOrder'], resolve);
const orderEdit = resolve => require(['@/pages/order/OrderEdit'], resolve);
const addSceneOrder = resolve =>
  require(['@/pages/order/addSceneOrder'], resolve);
const addDirectPurchaseOrder = resolve =>
  require(['@/pages/order/addDirectPurchaseOrder'], resolve);
const viewDirectPurchaseOrder = resolve => require(['@/pages/order/viewDirectPurchaseOrder'], resolve)
const returnOrder = resolve => require(['@/pages/order/returnOrder'], resolve);
const returnDetail = resolve =>
  require(['@/pages/order/returnDetail'], resolve);
const newReturnOrder = resolve =>
  require(['@/pages/order/newReturnOrder'], resolve);
const smartPricing = resolve =>
  require(['@/pages/order/smartPricing'], resolve); //智能定价
const smartPricingNew = resolve =>
  require(['@/pages/order/smartPricingNew'], resolve); //新智能定价
const orderTag = resolve => require(['@/pages/order/orderTag'], resolve); // 订单标签
const priceHistoryDetail = resolve =>
  require(['@/pages/order/priceHistoryDetail'], resolve); //智能定价历史详情

const commodityList = resolve => require(['@/pages/goods/index'], resolve);
const newGoods = resolve => require(['@/pages/goods/newCommodity'], resolve);
const newGoods2 = resolve => require(['@/pages/goods/NewGoods'], resolve);
const goodsDetail = resolve => require(['@/pages/goods/GoodsDetail'], resolve);
const category = resolve => require(['@/pages/goods/Category'], resolve);
const profile = resolve => require(['@/pages/goods/Profile'], resolve);
const goodsImg = resolve => require(['@/pages/goods/GoodsImg'], resolve);
const systemConfig = resolve =>
  require(["@/pages/settings/SystemConfig"], resolve);
const importImg = resolve => require(["@/pages/goods/ImportImg"], resolve);
const matchImg = resolve => require(["@/pages/goods/matchImg"], resolve);
const userType = resolve => require(["@/pages/user/UserType"], resolve);
const _userList = resolve => require(["@/pages/user/list"], resolve);
const CustomerLabel = resolve =>
  require(['@/pages/user/CustomerLabel'], resolve);
const userProfit = resolve => require(['@/pages/user/userProfit'], resolve);
const commoditySource = resolve =>
  require(['@/pages/goods/commoditySource'], resolve);
const sourceSearch = resolve =>
  require(['@/pages/goods/sourceSearch'], resolve);
const sourceList = resolve => require(['@/pages/goods/sourceList'], resolve);
const addSourceList = resolve =>
  require(['@/pages/goods/addSourceList'], resolve);
const sourceDetail = resolve =>
  require(['@/pages/goods/sourceDetail'], resolve);
const newEditionIndexPage = resolve => require(['@/pages/index/newEditionIndex.vue'], resolve);
const indexMini = resolve => require(['@/pages/index/indexMini'], resolve);
const InspectionRecords = resolve =>
  require(['@/pages/goods/InspectionRecords'], resolve);
const AccountSetting = resolve =>
  require(['@/pages/goods/AccountSetting'], resolve);
const CustomizeField = resolve =>
  require(['@/pages/goods/CustomizeField'], resolve);
const ExpireStatistics = resolve =>
  require(['@/pages/goods/expireStatistics'], resolve);
const ExpireStatisticsByGoods = resolve =>
  require(['@/pages/goods/expireStatisticsByGoods.vue'], resolve);

const abnormalOrdersList = resolve =>
  require(['@/pages/order/abnormalOrdersList'], resolve);

const orderChangeRecord = resolve =>
  require(['@/pages/order/orderChangeRecord'], resolve);

const user = resolve => require(['@/pages/user/user'], resolve);
const userBaseInfo = resolve => require(['@/pages/user/baseInfo'], resolve);
const _userBaseInfo = resolve => require(['@/pages/user/base-info'], resolve);
const userBaseInfoView = resolve =>
  require(['@/pages/user/baseInfoView'], resolve);
const usualGoods = resolve => require(['@/pages/user/usualGoods'], resolve);
const unavailableGoods = resolve =>
  require(['@/pages/user/unavailableGoods'], resolve);
const userGoodsProvider = resolve =>
  require(['@/pages/user/userGoodsProvider'], resolve);
const purchaseTaskProvider = resolve =>
  require(['@/pages/user/purchaseTaskProvider'], resolve);
const specialPrice = resolve => require(['@/pages/user/specialPrice'], resolve);
const goodDiscount = resolve => require(['@/pages/user/goodDiscount'], resolve);
const payment = resolve => require(['@/pages/user/payment'], resolve);
const shieldOrderList = resolve =>
  require(['@/pages/user/shieldOrderList'], resolve);
const EnterpriseWeChat = resolve =>
  require(['@/pages/user/EnterpriseWeChat'], resolve);
const followUp = resolve => require(['@/pages/user/followUp'], resolve);

const userPanel = resolve => require(['@/pages/user/UserPanel'], resolve);
const orderState = resolve => require(['@/pages/user/OrderState'], resolve);
const orderHistory = resolve => require(['@/pages/user/OrderHistory'], resolve);
const subaccountManagement = resolve =>
  require(['@/pages/user/subaccountManagement'], resolve);
const addressMg = resolve => require(['@/pages/user/addressMg'], resolve);
const contractMg = resolve => require(['@/pages/user/contractMg.vue'], resolve);
const repcipeGoods = resolve => require(['@/pages/user/repcipeGoods.vue'], resolve);
const informationOfMakeOutAnInvoice = resolve =>
  require(['@/pages/user/informationOfMakeOutAnInvoice'], resolve);

import UserCredit from '@/pages/user-credit/list';

//运营时间配置
const openingHoursConfig = resolve =>
  require(['@/pages/settings/openingHoursConfig'], resolve);
//运营时间配置新页面
const openingHoursConfigEdit = resolve =>
  require(['@/pages/settings/openingHoursConfigEdit'], resolve);
//分拣框配置
const sortBoxConfig = resolve =>
  require(['@/pages/settings/sortBoxConfig'], resolve);
//修改密码路由
const changePaw = resolve => require(['@/pages/settings/changePaw'], resolve);
//售后服务
const afterSale = resolve => require(['@/pages/sale/afterSale'], resolve);
const newNeed = resolve => require(['@/pages/sale/newNeed'], resolve);
//批量汇总
const batchSummary = resolve =>
  require(['@/pages/order/batchSummary'], resolve);
//批量核价
const orderChangePrice = resolve =>
  require(['@/pages/order/orderChangePrice'], resolve);
const newOrderChangePrice = resolve =>
  require(['@/pages/orderChangePrice/new'], resolve);
const orderChangePriceRecord = resolve =>
  require(['@/pages/order/orderChangePriceRecord'], resolve);
const priceRecordDetail = resolve =>
  require(['@/pages/order/priceRecordDetail'], resolve);
const index = resolve => require(['@/pages/purchase/index'], resolve);

//代开模块路由
const behalfOrder = resolve => require(['@/pages/behalf/OrderList'], resolve);
const behalfOrderInvoices = resolve =>
  require(['@/pages/behalf/OrderInvoices'], resolve);
const NewOrderReport = resolve =>
  require(['@/pages/behalf/NewOrderReport'], resolve);
const billsOrderSet = resolve =>
  require(['@/pages/bills/order/billSet'], resolve);
const documentsPrint = resolve =>
  require(['@/pages/behalf/documentsPrint'], resolve);
const behalfOrderApproval = resolve =>
  require(['@/pages/behalf/orderApproval'], resolve);
const behalfOrderDetail = resolve =>
  require(['@/pages/behalf/OrderDetail'], resolve);
const behalfNewOrder = resolve => require(['@/pages/behalf/NewOrder'], resolve);
const behalfOrderEdit = resolve =>
  require(['@/pages/behalf/OrderEdit'], resolve);
const orderMerge = resolve =>
  require(["@/pages/order/orderMerge/index.vue"], resolve);
const orderAlter = resolve =>
  require(["@/pages/order/order-modify/alter.vue"], resolve)
import printEditor from './printEditor';
// 打印编辑器

import purchaseRouter from './purchase.js';
// 公告
import notice from './notice.js';

//应用中心
import appCenter from './appCenter.js';

//回收站
import recycle from './recycle.js';

//配送服务
const deliveryMap = resolve =>
  require(['@/pages/distribution/deliveryMap'], resolve);

// 报表
import reports from './reports.js';

// 商城
import shop from './shop.js';

// 协议价
import agreementPrice from './agreementPrice.js';

// 设置
import settings from './settings';
// 库房
import storeRoom from './storeRoom.js';
// 分拣
import sort from './sort.js';
// 新分拣
import sorting from './sorting.js';
//分拣员
import sorter from './sorter.js';

// 生鲜管家
import sxgj from './shengxianGuanjia.js';

// 财务
import finance from './finance.js';

//运费
import freight from './freight';

//营销
import marketing from './marketing.js';

//销售管理
import sales from './sales.js';

// 加工
import process from './process';

// 配送
import delivery from './delivery';

// 优惠券
import coupon from './coupon';

// 周转筐
import basket from './basket';

// 分拣员绩效
import sorterPerformance from './sorterPerformance';

// 积分商城
import credit from './credit';

// 税率
import taxRate from './taxRate';

// 集团管理
import groupManage from './group-manage';

// 套餐
import goodsPackage from './goodsPackage';
import goodsPackageOrder from './goodsPackageOrder';
import goodsPackageReports from './goodsPackageReports';
import packageNutrition from './packageNutrition'; // 营养分析报表
import goodsPackageRecipe from './goodsPackageRecipe'; // 套餐食谱（按套餐定价）
import packageRecipe from './packageRecipe'; // 套餐食谱(按原料比例)

// 单据管理
import bills from './bills';

import demo from './demo';

import orderModify from './order';

import EnterpriseWeChats from './EnterpriseWeChat.js';

import goods from './goods';

import orderRefactor from './order-refactor';

import videoPresentation from './video-presentation.js';

// 用友T+Cloud同步
import yongyou from './yongyou';

// 金蝶云星空同步
import kingdee from './kingdee';

import kingdeeCloudStarts from './kingdeeCloudStarts'
import kingdeeCloudXinghan from './kingdeeCloudXinghan'

import centralKitchen from './centralKitchen';

// 食谱
import recipe from './recipe';

// 菜谱
import newRecipe from './newRecipe/index';

// 订单管理
import orderManage from './order-manage';

// oa审批
import oaAudit from './oaAudit';

// 月账报表
import monthlyBills from './monthly-bills';

// 分拣单模版
import sortTpl from './sortTpl';

// 采销联动定价
import caixiaoDingjia from './caixiao-dingjia';
import selfCollectionCabinet from './self-collection-cabinet';
import tuancan from './tuancan'

// 数据大屏
const dataScreen = resolve =>
  require(['@/pages/data-screen/indexNew.vue'], resolve);
// 分拣大屏
const deliverScreen = resolve =>
  require(['@/pages/data-screen/deliver.vue'], resolve);
// 溯源大屏
const traceScreen = resolve => require(['@/pages/trace-screen'], resolve);
// 配送大屏
const distributionScreen = resolve =>
  require(['@/pages/data-screen/distribution.vue'], resolve);
const traceScreenForCustomer = resolve =>
  require(['@/pages/trace-screen/trace-screen-for-customer'], resolve);
// 新手引导
const guide = resolve => require(['@/pages/guide'], resolve);
const lossProfitReport = resolve =>
  require(['@/pages/storeRoom/loss-profit-report/list'], resolve);
const lossProfitReportDetail = resolve =>
  require(['@/pages/storeRoom/loss-profit-report/detail'], resolve);
const orderGoodsReplace = resolve =>
  require(['@/pages/order/goods-replace'], resolve);
const customerBindingLabel = resolve =>
  require(['@/pages/order/customerBindingLabel'], resolve);

const scrollMap = {};

Vue.use(Router);

// 处理路由切换报错问题
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location, onResolve, onReject) {
  if (onResolve || onReject) {
    return originalPush.call(this, location, onResolve, onReject);
  }
  return originalPush.call(this, location).catch(err => err);
};

const extractPrefix = (url) => {
  const match = url.match(/^((\/cc_[^/]+)?\/superAdmin\/(viewCenter|view))/);
  return match ? match[1] : null;
};

let serverPrefix = '';
// 如果是生产环境前缀是'/superAdmin/view/
if (isRuntimePro || window.__POWERED_BY_QIANKUN__) {
  serverPrefix = extractPrefix(window.location.pathname);
}

const BASE = window.__POWERED_BY_QIANKUN__ ? '/v1/' : '/';

const routes = new Router({
  mode: window.__POWERED_BY_QIANKUN__ ? 'history' : 'hash',
  base: `${serverPrefix}${BASE}`,
  routes: [
    caixiaoDingjia,
    selfCollectionCabinet,
    monthlyBills,
    sortTpl,
    orderManage,
    printEditor,
    orderRefactor,
    goods,
    orderModify,
    purchaseRouter,
    shop,
    notice,
    appCenter,
    reports,
    settings,
    agreementPrice,
    storeRoom,
    sort,
    sorting,
    sorter,
    sxgj,
    finance,
    freight,
    sales,
    marketing,
    process,
    recycle,
    delivery,
    {
      path: '/delivery/line/template/form',
      component: resolve =>
        require(['@/pages/delivery/line/group-template/form'], resolve)
    },
    coupon,
    basket,
    demo,
    sorterPerformance,
    credit,
    taxRate,
    goodsPackage,
    goodsPackageOrder,
    goodsPackageReports,
    goodsPackageRecipe,
    packageRecipe,
    bills,
    EnterpriseWeChats,
    packageNutrition,
    videoPresentation,
    yongyou,
    kingdee,
    kingdeeCloudStarts,
    kingdeeCloudXinghan,
    centralKitchen,
    ...recipe,
    newRecipe,
    oaAudit,
    tuancan,
    {
      path: '/print-test',
      component: resolve => require(['@/pages/print-test'], resolve)
    },
    {
      path: '/blank',
      component: resolve => require(['@/pages/blank'], resolve)
    },
    {
      name: 'sortCodeList',
      path: '/sort-code-list',
      component: resolve => require(['@/pages/sort-code/list'], resolve),
      meta: [
        { title: '核验报表', url: '/sort-code-list' },
      ],
    },
    {
      path: '/loss-profit-report',
      name: 'loss-profit-report-list',
      component: lossProfitReport,
      meta: {
        noBreadCrumbs: true,
        keepScroll: true,
        _meta: [
          { title: '库房', url: '/sorting/commodity' },
          { title: '报损报溢', url: '/loss-profit-report' }
        ]
      }
    },
    {
      path: '/loss-profit-report/detail',
      name: 'loss-profit-report-detail',
      component: lossProfitReportDetail,
      meta: {
        noBreadCrumbs: true,
        showFootLogo: false,
        _meta: [
          { title: '库房', url: '/sorting/commodity' },
          { title: '报损报溢', url: '/loss-profit-report' },
          { title: '详情', url: '/loss-profit-report/detail' }
        ]
      }
    },
    {
      name: 'sms-statistics',
      path: '/sms-statistics',
      component: resolve =>
        require(['@/pages/settings/sms/statistics'], resolve),
      meta: [
        { title: '系统参数', url: '/sysConfig?tab=name1' },
        { title: '短信充值', url: '' }
      ]
    },
    {
      name: 'goods-map',
      path: '/goods-map',
      component: resolve => require(['@/pages/goods/map/list'], resolve),
      meta: [
        { title: '商品', url: '/commodityList/baseCommodity' },
        { title: '对照关系', url: '' }
      ]
    },
    {
      name: 'changePassword',
      path: '/changePassword',
      component: resolve =>
        require(['@/pages/settings/operator/changePassword'], resolve)
    },
    {
      name: 'user-credit',
      path: '/user-credit',
      component: () => import('@/pages/user-credit/list'),
      meta: [
        { title: '客户', url: '/userPanel' },
        { title: '客户积分', url: '' }
      ]
    },
    {
      name: 'prePack',
      path: '/prePack',
      component: resolve => require(['@/pages/prePack/index'], resolve),
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '库房', url: '/sort/sortByCommodity' },
          { title: '预打包', url: 'prePack' }
        ]
      }
    },
    {
      name: 'login',
      path: '/login',
      component: resolve => require(['@/pages/login/login'], resolve)
    },
    {
      name: 'auth-login',
      path: '/auth-login',
      component: resolve => require(['@/pages/login/authLogin'], resolve)
    },
    {
      name: 'unique-login',
      path: '/unique-login',
      component: resolve => require(['@/pages/login/uniqueLogin'], resolve)
    },
    {
      name: 'auto-login',
      path: '/auto-login',
      component: resolve => require(['@/pages/login/autoLogin'], resolve)
    },
    {
      name: 'resetPass',
      path: '/resetPass',
      component: resolve => require(['@/pages/login/resetPass'], resolve)
    },
    {
      name: 'sortByShop',
      path: '/sortByShop',
      component: resolve => require(['@/pages/sort/sortByShop'], resolve),
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '库房', url: '/sorting/commodity' },
          { title: '客户分拣', url: 'sortByShop' }
        ]
      }
    },
    {
      name: 'sortHistory',
      path: '/sortHistory',
      component: resolve => require(['@/pages/sort/sortHistory'], resolve)
    },
    {
      path: '/index',
      component: newEditionIndexPage
    },
    {
      path: '/indexMini',
      component: indexMini
    },
    {
      path: '/commodityDemand',
      component: newNeed,
      name: '新品需求',
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '营销', url: '/coupon/list' },
          { title: '新品需求', url: '' }
        ]
      }
    },
    {
      path: '/order',
      redirect: '/order/list',
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '' }
      ]
    },
    {
      path: '/order/goods-replace',
      name: 'order-goods-replace',
      component: orderGoodsReplace,
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '订单', url: '/order' },
          { title: '订单商品', url: '/order/goods-replace' }
        ]
      }
    },
    {
      path: '/orderApproval',
      component: orderApproval,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '/order' },
        { title: '核算', url: '' }
      ]
    },
    {
      path: '/order/merge',
      component: orderMerge,
      meta: [
        { title: '订单', url: '/order' },
        { title: '合并订单', url: '/order' }
      ]
    },
    {
      path: '/order/alter',
      component: orderAlter,
      meta: [{ title: '追加修改', url: '/order/alter' }]
    },
    {
      path: '/order/orderDirectPurchaseList',
      component: orderDirectPurchaseList,
      meta: [{ title: '订单直采', url: '/orderDirectPurchaseList' }]
    },
    {
      path: '/orderDetail',
      component: orderDetail,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '/order' },
        { title: '订单详情', url: '' }
      ]
    },
    {
      path: '/returnDetail',
      component: returnDetail,
      meta: [
        { title: '订单', url: '/order' },
        { title: '退货退款', url: '/returnOrder' },
        { title: '详情', url: '/returnDetail' }
      ]
    },
    {
      path: '/abnormalOrdersList',
      component: abnormalOrdersList,
      meta: [
        { title: '订单', url: '/order' },
        { title: '异常订单', url: '/abnormalOrdersList' }
      ]
    },
    {
      path: '/orderChangeRecord',
      component: orderChangeRecord,
    },
    {
      path: '/newOrder',
      component: newOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '/order' },
        { title: '新增', url: '' }
      ]
    },
    {
      path: '/batchSummary',
      component: batchSummary,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单汇总', url: '/batchSummary' }
      ]
    },
    {
      path: '/orderEdit',
      component: orderEdit,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '/order' },
        { title: '编辑', url: '' }
      ]
    },
    {
      path: '/addSceneOrder',
      component: addSceneOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单列表', url: '/order' },
        { title: '新增现场采购单', url: '' }
      ]
    },
    {
      path: '/addDirectPurchaseOrder',
      component: addDirectPurchaseOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单直采', url: '/order/orderDirectPurchaseList' },
        { title: '新增订单直采单', url: '' }
      ]
    },
    {
      path: '/editDirectPurchaseOrder',
      component: addDirectPurchaseOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单直采', url: '/order/orderDirectPurchaseList' },
        { title: '编辑订单直采单', url: '' }
      ]
    },
    {
      path: '/viewDirectPurchaseOrder',
      component: viewDirectPurchaseOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单直采', url: '/order/orderDirectPurchaseList' },
        { title: '查看订单直采单', url: '' }
      ]
    },
    {
      path: '/returnOrder',
      name: 'ReturnOrder',
      component: returnOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '退货退款', url: '' }
      ]
    },
    {
      path: '/newReturnOrder',
      component: newReturnOrder,
      meta: [
        { title: '订单', url: '/order' },
        { title: '退货退款', url: '/returnOrder' },
        { title: '新增', url: '' }
      ]
    },
    {
      path: '/smartPricing',
      component: smartPricing,
      meta: [
        { title: '订单', url: '/order' },
        { title: '智能定价', url: '/smartPricing' }
      ]
    },
    {
      path: '/smartPricingNew',
      component: smartPricingNew,
      meta: [
        { title: '订单', url: '/order' },
        { title: '智能定价', url: '/smartPricingNew' }
      ]
    },
    {
      path: '/orderTag',
      component: orderTag,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单标签', url: '/orderTag' }
      ]
    },
    {
      path: '/customerBindingLabel',
      component: customerBindingLabel,
      meta: [
        { title: '订单', url: '/order' },
        { title: '订单标签', url: '/orderTag' },
        { title: '批量选择客户', url: '/customerBindingLabel' }
      ]
    },
    {
      path: '/priceHistoryDetail',
      component: priceHistoryDetail,
      meta: {
        noBreadCrumbs: true
      }
    },
    {
      path: '/commodityList',
      component: commodityList,
      meta: [{ title: '商品', url: '/commodityList/baseCommodity' }],
      children: [
        { path: '', redirect: 'commodity' },
        {
          redirect: '/goods/list',
          path: 'baseCommodity',
          meta: [
            { title: '商品档案', url: 'baseCommodity' },
            { title: '基础商品', url: 'baseCommodity' }
          ]
        },
        {
          path: 'processCommodity',
          redirect: '/goods/list',
          meta: [
            { title: '商品档案', url: 'baseCommodity' },
            { title: '加工品', url: 'processCommodity' }
          ]
        },
        {
          path: 'newCommodity',
          component: newGoods,
          meta: [
            { title: '商品档案', url: 'baseCommodity' },
            { title: '商品信息', url: 'newCommodity' }
          ]
        },
        {
          path: 'newCommodity2',
          component: newGoods2,
          meta: [
            { title: '商品档案', url: 'baseCommodity' },
            { title: '商品信息', url: 'newCommodity' }
          ]
        },
        {
          path: 'commodityDetail',
          component: goodsDetail,
          meta: [
            { title: '商品档案', url: 'baseCommodity' },
            { title: '基本信息', url: 'commodityDetail' }
          ]
        }
      ]
    },
    {
      path: '/category',
      component: category,
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '商品', url: '/commodityList/baseCommodity' },
          { title: '商品分类', url: '' }
        ]
      }
    },
    {
      path: '/profile',
      component: profile,
      meta: [
        { title: '商品', url: '/commodityList/baseCommodity' },
        { title: '辅助资料', url: '' }
      ]
    },
    {
      path: '/goodsImg',
      component: goodsImg,
      meta: [
        { title: '商品', url: '/commodityList/baseCommodity' },
        { title: '商品图片', url: '' }
      ]
    },
    {
      path: '/commoditySource',
      component: commoditySource,
      meta: [{ title: '商品', url: '/commodityList/baseCommodity' }],
      children: [
        { path: '', redirect: 'list' },
        {
          path: 'list',
          component: sourceList,
          meta: { noBreadCrumbs: true }
        },
        {
          path: 'search',
          component: sourceSearch,
          meta: [
            { title: '商品溯源', url: 'search' },
            { title: '溯源查询', url: '' }
          ]
        },
        {
          path: 'list/add',
          component: addSourceList,
          meta: [
            { title: '商品溯源', url: '/commoditySource/list' },
            { title: '检测报告', url: '' }
          ]
        },
        {
          path: 'list/copy',
          component: addSourceList,
          meta: [
            { title: '商品溯源', url: '/commoditySource/list' },
            { title: '检测报告', url: '' }
          ]
        },
        {
          path: 'list/edit',
          component: addSourceList,
          meta: [
            { title: '商品溯源', url: '/commoditySource/list' },
            { title: '检测报告', url: '' }
          ]
        },
        {
          path: 'list/detail',
          component: sourceDetail,
          meta: [
            { title: '商品溯源', url: '/commoditySource/list' },
            { title: '检测报告', url: '' }
          ]
        },
        {
          path: 'InspectionRecords',
          component: InspectionRecords,
          meta: [
            { title: '商品溯源', url: 'list' },
            { title: '设备检测记录', url: '' }
          ]
        },
        {
          path: 'AccountSetting',
          component: AccountSetting,
          meta: [
            { title: '商品溯源', url: 'AccountSetting' },
            { title: '溯源配置', url: '' }
          ]
        },
        {
          path: 'CustomizeField',
          component: CustomizeField,
          meta: [
            { title: '商品溯源', url: 'CustomizeField' },
            { title: '检测属性自定义', url: '' }
          ]
        },
        {
          path: 'expireStatistics',
          component: ExpireStatistics,
          meta: [
            { title: '商品溯源', url: 'expireStatistics' },
            { title: '到期统计', url: '' }
          ]
        },
        {
          path: 'expireStatisticsByGoods',
          component: ExpireStatisticsByGoods,
          meta: [
            { title: '商品溯源', url: 'expireStatisticsByGoods' },
            { title: '到期统计', url: '' }
          ]
        },
      ]
    },
    {
      path: '/sysconfig',
      component: systemConfig,
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '首页', url: '/index' },
          { title: '系统参数', url: '' }
        ]
      }
    },
    {
      path: '/openingHoursConfig',
      component: openingHoursConfig,
      meta: [
        { title: '首页', url: '/index' },
        { title: '运营时段', url: '' }
      ]
    },
    {
      path: '/openingHoursConfigEdit',
      component: openingHoursConfigEdit,
      meta: {
        noBreadCrumbs: true
      }
    },
    {
      path: '/sortBoxConfig',
      component: sortBoxConfig,
      meta: [
        { title: '首页', url: '/index' },
        { title: '分拣框', url: '' }
      ]
    },
    {
      path: '/changePaw',
      component: changePaw
    },
    {
      path: '/importImg',
      component: importImg,
      meta: [
        { title: '商品', url: '/commodityList/baseCommodity' },
        { title: '商品图片', url: 'goodsImg' },
        { title: '图片导入', url: '' }
      ]
    },
    {
      path: '/matchImg',
      name: 'matchImg',
      component: matchImg,
      meta: [
        { title: '商品', url: '/commodityList/baseCommodity' },
        { title: '商品图片', url: 'goodsImg' },
        { title: '匹配图片', url: '' }
      ]
    },
    {
      path: '/userType',
      component: userType,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户类型', url: '' }
      ]
    },
    {
      path: '/userTypeDesignatedProvider',
      component: resolve =>
        require(['@/pages/user/UserTypeDesignatedProvider.vue'], resolve),
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户类型', url: '/userType' },
        { title: '指定供应商', url: '' }
      ]
    },
    {
      path: '/userTypePurchaseDesignatedProvider',
      component: resolve =>
        require(['@/pages/user/UserTypePurchaseDesignatedProvider.vue'], resolve),
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户类型', url: '/userType' },
        { title: '采购任务指定供应商', url: '' }
      ]
    },
    {
      path: '/userList',
      redirect: '/user/list',
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户档案', url: '/userList' }
      ]
    },
    {
      path: '/CustomerLabel',
      component: CustomerLabel,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '微信标签', url: '' }
      ]
    },
    {
      path: '/userPanel',
      component: userPanel,
      meta: {
        noBreadCrumbs: true,
        _meta: [
          { title: '客户', url: '/userPanel' },
          { title: '客户看板', url: '' }
        ]
      }
    },
    {
      path: '/orderState',
      component: orderState,
      meta: [
        { title: '客户', url: '/userPanel' },
        { title: '下单情况', url: '' }
      ]
    },
    {
      path: '/orderHistory',
      component: orderHistory,
      meta: [
        { title: '客户', url: '/userPanel' },
        { title: '订货历史', url: '' }
      ]
    },
    groupManage,
    {
      path: '/groupManage',
      redirect: '/group-manage/profile',
    },
    {
      path: '/user/tag',
      component: resolve => require(['@/pages/user/CustomerTag'], resolve),
      meta: { noBreadCrumbs: true }
    },
    {
      path: '/user/tagRecommendCommodityList',
      component: resolve =>
        require(['@/pages/user/tagRecommendCommodityList'], resolve),
      meta: { noBreadCrumbs: true }
    },
    {
      path: '/user/list',
      component: _userList,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户档案', url: '/userList' }
      ]
    },
    {
      path: '/user/base-info',
      component: _userBaseInfo,
      meta: {
        noBreadCrumbs: true,
        showFootLogo: false
      }
    },
    {
      path: '/user/organizationManagement',
      component: resolve =>
        require(['@/pages/user/OrganizationManagement'], resolve),
      meta: { noBreadCrumbs: true }
    },
    {
      path: '/user/exclusiveOrderLabel',
      component: resolve =>
        require(['@/pages/user/exclusiveOrderLabel'], resolve),
      meta: { noBreadCrumbs: true }
    },
    {
      path: '/user/baseInfoView',
      component: userBaseInfoView,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户档案', url: '/userList' },
        { title: '详情', url: 'baseInfoView' }
      ]
    },
    {
      path: '/user/EnterpriseWeChat',
      component: EnterpriseWeChat,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户档案', url: '/userList' },
        { title: '企业微信', url: 'EnterpriseWeChat' }
      ]
    },
    {
      path: '/user/followUp',
      component: followUp,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '客户档案', url: '/userList' },
        { title: '客户跟进', url: 'followUp' }
      ]
    },
    {
      path: '/userProfit',
      component: userProfit,
      meta: [
        { title: '报表', url: '/reports/tradeData' },
        { title: '客户毛利', url: '' }
      ]
    },
    {
      path: '/shieldOrderList',
      component: shieldOrderList,
      meta: [
        { title: '客户', url: '/userList' },
        { title: '屏蔽/售卖商品', url: '' }
      ]
    },
    {
      path: '/user',
      component: user,
      meta: [{ title: '客户', url: '/userList' }],
      children: [
        {
          path: '/user/baseInfo', // 目前基础档案跳转：user/base-info
          component: userBaseInfo,
          meta: [
            { title: '客户档案', url: '/userList' },
            { title: '编辑', url: 'baseInfo' }
          ]
        },
        {
          path: '/user/usualGoods',
          component: usualGoods,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '常用商品', url: '' }
            ]
          }
        },
        {
          path: '/user/protocolOrder',
          component: resolve => require(['@/pages/user/protocolOrder'], resolve),
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '所属协议单', url: '' }
            ]
          }
        },
        {
          path: '/user/unavailableGoods',
          component: unavailableGoods,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '屏蔽/售卖商品', url: '' }
            ]
          }
        },
        {
          path: '/user/specialPrice',
          component: specialPrice,
          meta: [
            { title: '客户档案', url: '/userList' },
            { title: '客户协议价', url: '' }
          ]
        },
        {
          path: '/user/specialPrice',
          component: specialPrice
        },
        {
          path: '/user/goodDiscount',
          component: goodDiscount,
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/user/payment',
          component: payment,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '客户账期', url: '' }
            ]
          }
        },
        {
          path: '/user/userGoodsProvider',
          component: userGoodsProvider,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '指定供应商', url: '' }
            ]
          }
        },
        {
          path: '/user/purchaseTaskProvider',
          component: purchaseTaskProvider,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '采购任务指定供应商', url: '' }
            ]
          }
        },
        {
          path: '/user/subaccountManagement',
          component: subaccountManagement,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '子账号管理', url: '' }
            ]
          }
        },
        {
          path: '/user/addressMg',
          component: addressMg,
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/user/contractMg',
          component: contractMg,
          meta: {
            noBreadCrumbs: true
          }
        },
        {
          path: '/user/repcipeGoods',
          component: repcipeGoods,
          meta: { noBreadCrumbs: true }
        },
        {
          path: '/user/informationOfMakeOutAnInvoice',
          component: informationOfMakeOutAnInvoice,
          meta: {
            noBreadCrumbs: true,
            _meta: [
              { title: '客户档案', url: '/userList' },
              { title: '开票信息', url: '' }
            ]
          }
        }
      ]
    },
    {
      path: '/afterSale',
      component: afterSale,
      name: '售后服务'
    },
    {
      path: '/orderChangePrice',
      component: orderChangePrice,
      name: '批量核价',
      meta: [{ title: '订单', url: '/order' }],
      children: [
        { path: '', redirect: 'new' },
        {
          path: 'new',
          component: newOrderChangePrice,
          meta: [{ title: '批量核价', url: 'new' }]
        },
        {
          path: 'record',
          component: orderChangePriceRecord,
          meta: [{ title: '改价记录', url: 'record' }]
        }
      ]
    },
    {
      path: '/orderChangePrice/detail',
      component: priceRecordDetail
    },
    {
      path: '/deliveryMap',
      component: deliveryMap,
      meta: [
        { title: '配送', url: '/delivery/line/list' },
        { title: '配送地图', url: 'deliveryMap' }
      ]
    },
    {
      path: '/behalfOrder',
      meta: [
        { title: '应用中心', url: '/appCenter/index' },
        { title: '单据管理', url: '/behalfOrder' }
      ],
      component: behalfOrder
    },
    {
      path: '/behalfOrderInvoices',
      meta: [
        { title: '应用中心', url: '/appCenter/index' },
        { title: '单据报表', url: '/behalfOrderInvoices' }
      ],
      component: behalfOrderInvoices
    },
    {
      path: '/NewOrderReport',
      meta: [
        { title: '应用中心', url: '/appCenter/index' },
        { title: '单据报表-新版', url: '/NewOrderReport' }
      ],
      component: NewOrderReport
    },
    {
      path: '/billsOrderSet',
      meta: [
        { title: '应用中心', url: '/appCenter/index' },
        { title: '单据设置', url: '/billsOrderSet' }
      ],
      component: billsOrderSet
    },
    {
      path: '/documentsPrint',
      component: documentsPrint,
      meta: [{ title: '单据打印', url: '/documentsPrint' }]
    },
    {
      path: '/behalfOrderApproval',
      component: behalfOrderApproval
    },
    {
      path: '/behalfOrderDetail',
      component: behalfOrderDetail
    },
    {
      path: '/behalfNewOrder',
      component: behalfNewOrder
    },
    {
      path: '/behalfOrderEdit',
      component: behalfOrderEdit
    },
    {
      path: '/data-screen',
      name: 'data-screen',
      component: dataScreen
    },
    {
      path: '/deliver-screen',
      name: 'deliver-screen',
      component: deliverScreen
    },
    {
      path: '/distribution-screen',
      name: 'distribution-screen',
      component: distributionScreen
    },
    {
      path: '/trace-screen',
      name: 'data-trace',
      component: traceScreen
    },
    {
      path: '/trace-screen-for-customer',
      name: 'trace-screen-for-customer',
      component: traceScreenForCustomer
    },
    {
      path: '/guide',
      name: 'guide',
      component: guide
    },
    {
      path: '/empty',
      name: 'empty',
      component: resolve => require(['@/pages/empty/empty.vue'], resolve)
    },
    {
      path: '/',
      name: 'default',
      redirect: '/login'
    }
  ],
  scrollBehavior(to, from, savedPosition) {
    // return 期望滚动到哪个的位置
    if (from.query.keep_scroll || to.meta.keepScroll) {
      Vue.nextTick(() => {
        document.documentElement.scrollTop = scrollMap[to.path] || 0;
        scrollMap[to.path] = null;
      });
    } else {
      return { x: 0, y: 0 };
    }
  }
});
routes.afterEach((to, from) => {
  if (to.query.keep_scroll || from.meta.keepScroll) {
    scrollMap[from.path] = document.documentElement.scrollTop;
  }
  store.commit('setShowFootLogo', true);
  if (to.meta.showFootLogo === false) {
    store.commit('setShowFootLogo', false);
  }
  Vue.prototype.$sintro && Vue.prototype.$sintro.reset();
  to.query.source === 'guide' && Vue.prototype.$sintro.start();
});
export default routes;
