<template>
  <Checkbox
    v-bind="$attrs"
    :value="value"
    :meta="meta"
    @on-change="handleChange"
  ></Checkbox>
</template>

<script>
import { Checkbox } from 'view-design';

export default {
  components: { Checkbox },
  props: {
    meta: {
      default: null
    },
    value: {
      type: Boolean
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  methods: {
    handleChange(checked) {
      this.$emit('on-change', checked, this.meta);
      this.$emit('input', checked);
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="less"></style>
