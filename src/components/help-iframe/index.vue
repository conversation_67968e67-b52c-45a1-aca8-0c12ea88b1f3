<template>
    <transition name="slide-fade">
    <div class="help-iframe" v-if="showHelp">
      <span class="mask" @click="hiddenMenu"></span>
      <div  class="content" id="iframeContainer"></div>
    </div>
    </transition>
</template>

<script>
export default {
  data() {
    return {
      baseSrc:
        'https://helpcenter.sdongpo.com/embed-new?category_id=35&category_name='
    };
  },
  computed: {
    showHelp() {
      let show = this.$store.state.help.showIframe;
      setTimeout(() => {
        this.createIframe();
      })
      console.log(show);
      return show;
    }
  },
  created() {
    this.handleMessage = e => {
      if (e.data === 'showQuestionModal') {
        this.showQuestionModal();
      } else if (e.data === 'jumpToGuide') {
        this.jumpToGuide();
      }else if(e.data.imgs){
        this.viewImage(e.data.imgs, e.data.viewIndex);
      }
    }
    window.addEventListener(
      'message',
      this.handleMessage,
      false
    );
  },
  beforeDestroy () {
    this.removeIframe();
    window.removeEventListener('message', this.handleMessage);
  },
  methods: {
    jumpToGuide() {
      this.hiddenMenu();
      this.$emit('jump-to-guide');
    },
    showQuestionModal() {
      this.hiddenMenu();
      this.$emit('show-question-modal');
    },
    hiddenMenu() {
      this.$store.commit('SET_HELP_SHOW', false);
    },
    createIframe() {
      var moduleName = this.$store.state.help.currentMenu.name;
      var src = this.baseSrc + moduleName;
      var iframe = document.createElement('iframe');
      iframe.src = src;
      iframe.frameborder = 0;
      iframe.style = 'width:100%;height:100%;border: 0;';
      iframe.allowFullscreen = true;
      iframe.webkitallowFullscreen = true;
      iframe.mozallowFullscreen = true;

      let iframeContainer = document.getElementById('iframeContainer');
      if (iframeContainer) {
        iframeContainer.innerHTML = '';
        iframeContainer.appendChild(iframe);
      }
    },
    removeIframe() {
      const iframeContainer = document.getElementById('iframeContainer');
      if (iframeContainer) {
        const iframe = iframeContainer.querySelector("iframe");
        if (iframe && iframe.remove) {
          iframe.remove();
        }
      }
    }
  }
};
</script>

<style lang="less" scoped>
.slide-fade-enter-active {
  transition: all .8s ease;
}
.slide-fade-leave-active {
  transition: all .8s ease;
}
.slide-fade-enter, .slide-fade-leave-to{
  transform: translateX(500px);
}
.help-iframe {
  z-index: 999;
  position: fixed;
  right: 0;
  top: 0;
  bottom: 0;
  width: calc(100vw + 500px);
  height: 100%;
  .mask {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: inline-block;
  }

  .content {
    position: absolute;
    right: 0px;
    top: 0;
    bottom: 0;
    width: 500px;
    border-left: 1px solid #e8e8e8;
    height: 100%;
    background-color: white;
  }
  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
