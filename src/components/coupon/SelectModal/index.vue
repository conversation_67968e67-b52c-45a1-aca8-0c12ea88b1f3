<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="onCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1100">
      <list-table
        :class="{'hide-table-check-all': !showCheckAll}"
        ref="list"
        :table-height="getTableHeight() - 80"
        :api="apiUrl.coupon.list"
        :auto-load-data="false"
        :filter-items="filterItems"
        :filters="filters"
        :columns="cols"
        :row-class-name="rowClassName"
        :after-load-list="afterLoadList"
        @after-render="afterRender"
        @on-select="onSelect"
        @on-row-click="onRowClick"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-selection-change="onSelectionChange">
      </list-table>
      <div slot="footer">
        <Button   @click="onCancel">{{cancelText}}</Button>
        <Button  type="primary"  @click="onOk" v-if="showOk">{{okText}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import ListTable from '@components/common/list-table/list'
  const LIST_KEY = 'id';
  export default {
    name: "CouponModal",
    components: {
      ListTable
    },
    props: {
      show: {
        type: Boolean,
        default: false
      },
      showCheckAll: {
        type: Boolean,
        default: true
      },
      /**
       * 是否显示多选
       */
      showSelect: {
        type: Boolean,
        default: true
      },
      maxSelectCount: {
        type: Number,
        default: 0,
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      closable: {
        type: Boolean,
        default: true
      },
      sendDiasbled: {
        type: Boolean,
        default: false
      },
      title: {
        type: String,
        default: '优惠券赠送'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      okText: {
        type: String,
        default: '确 定'
      },
      showOk: {
        type: Boolean,
        default: true
      },
      // 默认选中的上优惠券
      defaultSelectedRows: {
        type: Array,
        default: () => []
      },
      disabledRows: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      columns: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      filterParams: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      show(newValue, oldValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.setFilters();
          this.selectedRows = this.cloneObj(this.defaultSelectedRows);
          this.loadList();
        }
      },
      defaultSelectedRows: {
      	deep: true,
        handler(newValue) {
          this.selectedRows = this.cloneObj(newValue);
        }
      }
    },
    computed: {
    },
    data() {
      return {
        modal: {
          show: false,
          className: 'vertical-center-modal common-coupon-modal'
        },
        tableHeight: this.getTableHeight() * 0.8,
        filterItems: [
          // {
          //   key: 'status',
          //   type: 'select',
          //   placeholder: '活动状态',
          //   data: [],
          //   style: {
          //     width: '100px',
          //     textAlign: 'left'
          //   }
          // },
          {
            key: 'name',
            showIcon: true,
            placeholder: '请输入优惠券名称',
          }
        ],
        filters: {
          is_online: 'Y',
          category_id: '',
          category_id2: '',
        },
        storeList: [],
        goodsList: [],
        selectedRow: [],
        selectedRows: [],
        cols: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          },
          {
            title: "优惠券名称",
            key: "name",
          },
          {
            title: "面值",
            key: "price",
          },
          {
            title: "使用条件",
            key: 'condition_desc'
          },
          {
            title: "状态",
            key: "status_desc",
          },
          {
            title: "单次赠送数量",
            align: "center",
            render: (h, params) => {
              let row = params.row;
              return h('InputNumber', {
                props: {
                  value: row.gift_num,
                  min: 1,
                  precision: 0,
                  max: 9999,
                  disabled: this.sendDiasbled || this.isDisabled(row)
                },
                on: {
                  'on-change': e => {
                    row.gift_num = e;
                    this.$refs.list.updateRow(params.index, row);
                  }
                }
              })
            }
          },
          {
            title: "有效期",
            key: "validity_desc",
          },
        ],
      }
    },
    created() {
    	this.getSearchConfig();
      this.setFilters();
      this.modal.show = this.show;
      if (!this.showFooter) {
        this.modal.className += ' hide-footer';
      }
      if (this.columns && this.columns.length > 0) {
        this.cols = this.columns;
      }
      if (this.show) {
        this.$nextTick(() => {
          this.$refs.list.loadListData();
        });
      }
    },
    methods: {
      setFilters() {
        this.filters = {
          ...this.filters,
          ...this.filterParams
        };
      },
      afterRender() {
        this.checkMaxSelectCount();
        this.setDisabledRows();
      },
      loadList() {
        this.$refs.list.loadListData();
      },
      updateRow(row) {
      	let index = this.storeList.findIndex(item => item[LIST_KEY] === row[LIST_KEY]);
      	if (index !== -1) {
      		this.storeList[index] = this.deepClone(row);
        }
      },
      getList() {
        return this.$refs.list.getListData();
      },
      setList(list) {
        return this.$refs.list.setListData(list);
      },
      syncList() {
      	let list = this.deepClone(this.getList());
      	list = list.map(item => {
      		let storeItem = this.storeList.find(findItem => findItem[LIST_KEY] === list[LIST_KEY]);
      		if (storeItem) {
      			return storeItem
          }
          item.gift_num = item.gift_num || 1
          return item;
        });
      	this.storeList = this.deepClone(list);
      	this.setList(list);
      },
      afterLoadList(list) {
        list.forEach((item) => {
          if (this.isSelected(item)) {
            item._checked = true;
          }
        });
        this.syncList();
      },
      onSelectAll (selection) {
        selection.forEach((row) => {
          this.selectRow(row);
        });
      },
      onSelect(selection, row) {
        this.selectRow(row);
      },
      onSelectionChange (selection) {
        selection.forEach((item) => {
          this.selectRow(item);
        });
        if (selection.length === 0) {
          this.$refs.list.list.forEach((item) => {
            this.cancelSelectRow(item);
          });
        }
        this.checkMaxSelectCount();
      },
      onSelectCancel(selection, row) {
        this.cancelSelectRow(row);
      },
      onRowClick(row, index) {
        if (this.isDisabled(row)) {
          return false;
        }
        if (this.isSelected(row)) {
          this.cancelSelectRow(row);
          this.cancelCheckStatus(index);
        } else {
          this.selectRow(row);
          this.setCheckStatus(index);
        }
        this.checkMaxSelectCount();
      },
      isSelected(item) {
        return this.selectedRows.some((findItem) => findItem[LIST_KEY] === item[LIST_KEY]);
      },
      isDisabled(item) {
        return this.disabledRows.some((findItem) => findItem[LIST_KEY] === item[LIST_KEY]);
      },
      setDisabledRows() {
        let list = this.getList();
        list.forEach((item) => {
          const target = this.disabledRows.find((findItem) => findItem[LIST_KEY] === item[LIST_KEY])
          if (target) {
            item._disabled = true;
            item.gift_num = target.gift_num
          }
        });
        this.setList(list);
      },
      checkMaxSelectCount() {
        let list = this.getList();
        let selectedLen = this.selectedRows.length;
        if (!this.maxSelectCount) {
          return false;
        }
        list.forEach((item, index) => {
          let selected = this.isSelected(item);
          if (selectedLen >= this.maxSelectCount && !selected) {
            item._disabled = true;
          } else {
            item._disabled = false;
          }
          if (this.isDisabled(item)) {
            item._disabled = true;
          }
          item._checked = selected;
        });
        this.setList(list);
      },
      /**
       * 设置选中效果
       * @param index 选中行的索引
       */
      setCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 设置选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom && !checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.add('ivu-checkbox-wrapper-checked');
          checkBoxDom && !checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.add('ivu-checkbox-checked');
        }
      },
      /**
       * 取消选中效果
       * @param index 取消选中行的索引
       */
      cancelCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 取消选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.remove('ivu-checkbox-wrapper-checked');
          checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.remove('ivu-checkbox-checked');
        }
      },
      /**
       * 将优惠券添加到选中优惠券中
       * @param row
       */
      selectRow(row) {
        if (!this.selectedRows.some((item) => item[LIST_KEY] === row[LIST_KEY])) {
          this.selectedRows.push(row);
        }
      },
      /**
       * 从选中优惠券中删除优惠券
       * @param row
       */
      cancelSelectRow(row) {
        if (this.selectedRows.some((item) => item[LIST_KEY] === row[LIST_KEY])) {
          this.selectedRows.splice(this.selectedRows.findIndex((item) => item[LIST_KEY] === row[LIST_KEY]), 1);
        }
      },
      rowClassName (row, index) {
      },
      onCancel() {
        this.$emit('on-cancel');
      },
      onOk() {
        if (this.selectedRows.length === 0) {
          this.modalError('请选择优惠券', 0);
          return false;
        }
        if (this.maxSelectCount && this.maxSelectCount < this.selectedRows.length) {
          this.modalError(`最多只能选择${this.maxSelectCount}张优惠券！`, 0);
          return false;
        }
        this.syncList();
        let selectedItems = this.getSelectedItems();
        console.log(selectedItems)
        this.$emit('on-ok', this.cloneObj(selectedItems));
      },
      getSelectedItems() {
      	return this.storeList.filter(item => this.selectedRows.some(selectedItem => selectedItem[LIST_KEY] === item[LIST_KEY]));
      },
      getSearchConfig() {
        this.$request.get(this.apiUrl.coupon.searchConfig).then((res) => {
          let { status, data } = res;
          if (status) {
            this.filterItems.forEach((filterItem) => {
              switch (filterItem.key) {
                case 'status':
                  filterItem.data = data.status.map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                    }
                  });
                  break;
                case 'type':
                  filterItem.data = data.type.map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                    }
                  });
                  break;
              }
            });
          }
        });
      }
    }
  }
</script>

<style lang="less">
  .common-coupon-modal {
    .ivu-modal-body {
      padding-bottom: 0;
      .ivu-table-row {
        cursor: pointer;
      }
    }
    .hide-table-check-all {
      th {
        .ivu-checkbox-wrapper {
          display: none;
        }
      }
    }
  }
</style>
<style lang="less" scoped>
</style>
