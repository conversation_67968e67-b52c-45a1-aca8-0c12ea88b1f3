const LEFT = 37
const UP = 38
const RIGHT = 39
const DOWN = 40
const ENTER = 13

/**
 * 
 * @param options {event} 键盘事件
 * 
 * @param options {ref} 操作的dom元素ref名称
 */
/**
 * @description: 处理表格输入框热键事件
 * @param {Object} options event 键盘事件
 * @param {Object} options index 表格行下标index
 * @param {Object} options ref 操作的表格ref名称
 * @param {Object} options vm 当前组件vue实例
 */
const handleTableRowsHotKey = options => {
  switch (options.event.keyCode) {
    case LEFT:
      handleLeft(options)
      break
    case UP:
      handleUp(options)
      break
    case RIGHT:
      handleRight(options)
      break
    case DOWN:
      handleDown(options)
      break
    case ENTER:
      if (options._ENTER_AS_DOWN) {
        handleDown(options)
      }
  }
}

const handleLeft = (options) => {
  handleMoveLeftAndRight(options, 'left')
}
const handleUp = (options) => {
  handleMoveUpAndDown(options, 'up')
}
const handleRight = (options) => {
  handleMoveLeftAndRight(options, 'right')
}
const handleDown = (options) => {
  handleMoveUpAndDown(options)
}
const handleMoveLeftAndRight = (options, direction = 'left') => {
  const indexCursor = direction === 'left' ? -1 : 1
  const {
    event,
    index,
    ref,
    vm
  } = options
  if (!isSelectAll(event)) return
  const $tableRows = vm.$refs[ref].$el.querySelectorAll('tbody tr')
  if (!$tableRows) return false
  const $currentRow = $tableRows[index]
  // 去除禁用项
  const $hotKeyItems = $currentRow.querySelectorAll('input:enabled,textarea:enabled')
  const hotKeyItems = Array.from($hotKeyItems)
  const itemIndex = hotKeyItems.indexOf(event.target)
  const $nextTarget = hotKeyItems[itemIndex + indexCursor]
  if ($nextTarget) {
    setTimeout(() => { // 避开默认事件影响，需要在下一个事件循环中执行才能使select()生效
      $nextTarget.select()
      $nextTarget.scrollIntoView({ block: 'center', inline: 'center' })
    })
  }
}
const handleMoveUpAndDown = (options, direction = 'down') => {
  const indexCursor = direction === 'down' ? 1 : -1
  const {
    event,
    index,
    ref,
    vm,
    callback
  } = options
  
  const $tableRows = vm.$refs[ref].$el.querySelectorAll('tbody tr')
  if (!$tableRows) return false
  const $currentRow = $tableRows[index]
  const $nextRow = $tableRows[index + indexCursor]
  if (!$currentRow || !$nextRow) return false

  const $hotKeyItems = $currentRow.querySelectorAll('input,textarea')
  const hotKeyItems = Array.from($hotKeyItems)
  const $nextRowHotKeyItems = $nextRow.querySelectorAll('input,textarea')
  const nextRowHotKeyItems = Array.from($nextRowHotKeyItems)
  const itemIndex = hotKeyItems.indexOf(event.target)
  const $nextTarget = nextRowHotKeyItems[itemIndex]
  if ($nextTarget) {
    setTimeout(() => {
      $nextTarget.select()
      $nextTarget.scrollIntoView({ block: 'center', inline: 'center' })
      callback && callback(index + indexCursor)
    })
  }
}

// 判断input框内容是否全选
const isSelectAll = event => {
  const input = event.target
  return input.value === '' || (input.value && input.value.length && input.selectionStart === 0 && input.selectionEnd === input.value.length)
}

export default {
  handleTableRowsHotKey
}

