@prefix: editable-table;

.@{prefix} {
  .icon-record-editor {
    display: block;
    color: #B2B2B2;
    cursor: pointer;
    & + .icon-record-editor {
      margin-top: 7px;
    }
    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
    }
  }
  &__action {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    &-divider {
      display: block;
      width: 1px;
      height: 13px;
      background-color: rgba(233, 233, 233, 1);
      margin: 0 8px;
    }
    & &-dropdown__icon {
      &.sui-icon {
        font-size: 12px;
        color: var(--primary-color);
        margin-left: 3px;
        margin-bottom: 2px;
      }
    }

    &-dropdown {
      margin-bottom: -10px;
      min-width: 106px;
      &__item {
        display: block;
        padding: 7px 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        .ivu-poptip,
        .ivu-poptip-rel {
          display: block;
        }
        span {
          display: block;
        }
        &:hover {
          color: var(--primary-color);
          background-color: rgb(239, 250, 244);
        }
      }
    }
  }

  .after-table {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    padding: 15px;
    background-color: #fcfcfd;
    color: #303030;
    text-align: right;
    border: 1px solid #f0f2f0;
    border-top: none;

    &-left {
      display: flex;
      align-items: center;

      .ivu-icon {
        font-size: 19px;
      }

      &-hotkey {
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.65);
      }
    }

    &-right {
      color: #000;
      font-weight: 500;
    }
  }
}
