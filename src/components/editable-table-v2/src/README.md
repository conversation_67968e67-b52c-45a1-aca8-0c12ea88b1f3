# EditableTable

## Props

<!-- @vuese:EditableTable:props:start -->
|Name|Description|Type|Required|Default|
|---|---|---|---|---|
|columns|表格列的配置描述|`Array`|`false`|[]|
|data|显示的结构化数据|`Array`|`false`|[]|
|rowKey|row-key, 默认为 'id'|`String`|`false`|id|
|params|用于 fetchData 查询的额外参数|`Object`|`false`|-|
|dataProvider|获取 tableData 的方法 / 接口地址|`String` /  `Function`|`false`|-|
|beforeRequest|获取数据的请求前置操作，return false 可以终止请求|`Function`|`false`|params => params|
|afterRequest|请求结束后的钩子函数、返回处理后的数据|`Function`|`false`|data => data|
|beforeSetData|设置渲染数据前钩子函数、返回处理后的数据;参数:data, res|`Function`|`false`|params => params|
|tableId|同一个路由中有多个listtable时,用这个区分.不然表头设置会混乱|`String` /  `Number`|`false`|-|
|outerBorder|是否显示外边框|`Boolean`|`false`|true|
|isShowRecordEditor|是否显示内置的增加删除操作列（显示于左侧第一列）|`Boolean`|`false`|true|
|recordEditorConfig|表格左侧内置的增删操作列的相关配置（配合isShowRecordEditor为true时生效）|`Object`|`false`|-|

<!-- @vuese:EditableTable:props:end -->


## Events

<!-- @vuese:EditableTable:events:start -->
|Event Name|Description|Parameters|
|---|---|---|
|on-ready|-|-|
|on-row-click|-|-|

<!-- @vuese:EditableTable:events:end -->


## Slots

<!-- @vuese:EditableTable:slots:start -->
|Name|Description|Default Slot Content|
|---|---|---|
|before-table|-|-|
|after-table|-|-|

<!-- @vuese:EditableTable:slots:end -->


## Methods

<!-- @vuese:EditableTable:methods:start -->
|Method|Description|Parameters|
|---|---|---|
|insertAt|添加一行数据|-|
|deleteAt|删除某行数据|-|
|getParams|返回当前的请求参数|-|
|setData|手动设置表格数据|{Array} data 需要设置的数据 {Object} res 完整的接口返回数据|
|fetchData|获取表格数据data的方法|-|
|_onRowClick|on-row-click 点击某行时触发<br >`this.$emit('on-row-click', cloneDeep(row), index)`|{Object} row {Number} index|

<!-- @vuese:EditableTable:methods:end -->


