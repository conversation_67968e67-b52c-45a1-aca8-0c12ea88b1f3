<!--
 * @Author: <PERSON>
 * @Date: 2021-12-02 15:58:11
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-25 15:48:47
 * @Description: 可编辑表格 EditableTable
-->
<template>
  <div class="editable-table" ref="wrap">
    <div v-show="$slots['before-table']" class="before-table">
      <slot class="before-table" name="before-table"></slot>
    </div>
    <Table
      ref="table"
      :data="tableData"
      :columns="columnsFilter"
      :rowKey="rowKey"
      :outerBorder="outerBorder"
      :needAnimation="false"
      :width="tableWidth"
      :loading="selfLoading"
      @on-column-width-resize="_onColumnWidthResize"
      @on-row-click="_onRowClick"
      @on-row-keydown="_onRowKeydown"
      @on-row-keyup="_onRowKeyup"
      @on-draggable-data="_onDraggableData"
      v-bind="attrsCopy"
      v-on="listenersCopy"
    ></Table>
    <slot name="after-table">
      <div class="after-table">
        <div class="after-table-left">
          <slot name="after-table-left">
            <div class="after-table-left-hotkey">
              <SelfIcon icon="tips" :size="12" class="mr6" />
              <span>支持键盘操作，</span>
              <Icon type="ios-arrow-round-back" />
              <Icon type="ios-arrow-round-forward" />
              <span>左右切换，</span>
              <Icon type="ios-arrow-round-up" />
              <Icon type="ios-arrow-round-down" />
              <span>上下换行，</span>
              <span>Enter 键新增一行</span>
            </div>
          </slot>
        </div>
        <div class="after-table-right">
          <slot name="after-table-right"></slot>
        </div>
      </div>
    </slot>
  </div>
</template>

<script>
import Table from '@/components/table-v2'
import TitleCfg from '@/components/title-config'
import basic from '@/api/basic.js'
import Button from '@/components/button'
import SelfIcon from '@/components/icon'
import Dropdown from '@/components/dropdown'
import HotKey from './HotKey.js'
import { cloneDeep, isEqual, uniqueId } from 'lodash-es'

import {
  getActionItemClass,
  getActionItemAttrs,
  getActionItemStyle,
  getActionItemProps,
  hasChildAction,
  getActionItemName,
  formatActions,
  isNewRecordExclude
} from './utils'

export default {
  name: 'EditableTable',
  components: {
    Table,
    SelfIcon
  },
  props: {
    // 表格列的配置描述
    columns: {
      type: Array,
      default: () => []
    },
    // 显示的结构化数据
    data: {
      type: Array,
      default: () => []
    },
    // 表格是否加载中, 支持.sync修饰符
    loading: {
      type: Boolean,
      default: false
    },
    // row-key, 默认为 'id'
    rowKey: {
      type: String,
      default: 'id'
    },
    // 用于 fetchData 查询的额外参数
    params: {
      type: Object,
      default: () => ({})
    },
    // 获取 tableData 的方法 / 接口地址
    dataProvider: {
      type: [String, Function],
      default: null,
    },
    // 获取数据的请求前置操作，return false 可以终止请求
    beforeRequest: {
      type: Function,
      default: params => params
    },
    // 请求结束后的钩子函数、返回处理后的数据
    afterRequest: {
      type: Function,
      default: data => data
    },
    // 设置渲染数据前钩子函数、返回处理后的数据;参数:data, res
    beforeSetData: {
      type: Function,
      default: params => params
    },
    // 同一个路由中有多个listtable时,用这个区分.不然表头设置会混乱
    tableId: {
      type: [String, Number],
      default: ''
    },
    // 是否显示外边框
    outerBorder: {
      type: Boolean,
      default: true
    },
    // 是否显示内置的增加删除操作列（显示于左侧第一列）
    isShowRecordEditor: {
      type: Boolean,
      default: false
    },
    isHiddenDel: {
      type: Boolean,
      default: false
    },
    isHiddenAdd: {
      type: Boolean,
      default: false
    },
    // 表格左侧内置的增删操作列的相关配置（配合isShowRecordEditor为true时生效）
    recordEditorConfig: {
      type: Object,
      default: () => ({
        autoInsertDelete: true,
        record: null,
        deleteConfirm: false
      })
    },
    // 输入回车键时光标是否向下聚焦
    enterAsDown: {
      type: Boolean,
      default: false
    },
    // 是否禁用默认热键(可编辑表格默认支持热键(上下左右、回车),也可以自己控制)
    stopHotKey:{
      type:Boolean,
      default: false
    },
    storePath: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      tableWidth: 0,
      titleConfig: null,
      titleCol: [],
      allColKeys: [],
      copyColumns: [],
      hasTitleCfg: false,
      tableData: [],

      attrsCopy: {},
      listenersCopy: {}
    }
  },
  computed: {
    selfLoading: {
      get () {
        return this.loading
      },
      set (newValue) {
        this.$emit('update:loading', newValue)
      }
    },
    showColumns () {
      return this.copyColumns.filter(item => this._isDefCol(item) || item.type)
    },
    columnsFilter () {
      const result = this.hasTitleCfg
        ? this.showColumns
          .filter(item => item.checked !== false)
          .sort((a, b) => a.index - b.index)
        : this.copyColumns
      return result
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler () {
        this.tableData = this.data
      }
    },
    columns: {
      deep: true,
      handler () {
        this._initColumns()
      }
    },
    $attrs: {
      immediate: true,
      handler (newValue, oldValue) {
        if (!isEqual(newValue, oldValue)) {
          this.attrsCopy = cloneDeep(newValue)
        }
      }
    },
    $listeners: {
      immediate: true,
      handler (newValue, oldValue) {
        if (!isEqual(newValue, oldValue)) {
          this.listenersCopy = cloneDeep(newValue)
        }
      }
    }
  },
  created () {
    this._initColumns()
    this._initTableData()
  },
  methods: {
    _onDraggableData (data) {
      this.$emit('on-draggable-data', data);
      this.tableData = data;
    },
    _onColumnWidthResize () {
      const path = this.$route.path
      const columnWidth = []
      this.copyColumns.forEach(({ key, width }) => {
        columnWidth.push({ key, width })
      })
      localStorage.setItem(path + '&columnWidth' + this.tableId, JSON.stringify(columnWidth))
    },
    _handleReady () {
      this.$nextTick(() => {
        if (this.$refs.wrap) {
          this.tableWidth = this.$refs.wrap.clientWidth
          this.$emit('on-ready')
        }
      })
    },
    _initTableData () {
      if (this.dataProvider) {
        this.fetchData()
      } else {
        if (this.isShowRecordEditor && this.tableData.length === 0) {
          this.insertAt(-1) // 默认无数据时，加在第一行
        }
      }
    },
    _initColumns () {
      if (!this.columns.length) return
      const copyColumns = cloneDeep(this.columns)
      if (this.isShowRecordEditor) {
        const renderFunc = (h, params) => {
          const { index } = params
          return h('div', [
            this.recordEditorConfig.deleteConfirm ?
              h('Poptip', {
                class: 'icon-record-editor icon-record-editor--delete',
                props: {
                  title: this.recordEditorConfig.deleteConfirm, // 删除按钮气泡文案
                  confirm: true,
                  transfer: true,
                  placement: 'right'
                },
                on: {
                  'on-ok': () => {
                    this.deleteAt(index)
                  }
                },
                nativeOn: {
                  click: $event => $event.stopPropagation()
                }
              }, [
                h(SelfIcon, {
                  props: {
                    icon: 'jian',
                    size: 16
                  },
                  class: 'icon-record-editor icon-record-editor--delete',
                })
              ]) :
              h(SelfIcon, {
                props: {
                  icon: 'jian',
                  size: 16
                },
                style:{display:this.isHiddenDel?((!params.row.purchase_status||params.row.purchase_status==1)? 'block':'none'):'block'},
                class: 'icon-record-editor icon-record-editor--delete',
                on: {
                  click: ($event) => {
                    this.deleteAt(index)
                    $event.stopPropagation()
                  }
                },
              }),
            h(SelfIcon, {
              props: {
                icon: 'jia1',
                size: 16
              },
              style:{display:this.isHiddenAdd? 'none' : 'block'},
              class: 'icon-record-editor icon-record-editor--insert',
              on: {
                click: event => {
                  this.insertAt(index)
                  event.stopPropagation()
                }
              }
            })
          ])
        }
        if (copyColumns[0].titleType) {
          if (!copyColumns[0].render) {
            copyColumns[0].render = renderFunc
          }
        } else {
          copyColumns.unshift({
            title: '',
            key: 'column_RecordEditor',
            width: 52,
            align: 'center',
            fixed: 'left',
            render: renderFunc
          })
        }
      }

      copyColumns.forEach(col => {
        if (col.titleType) {
          this.titleCol = []
          this.getTitleData(col)
          col.renderHeader = () => {
            return (
              <TitleCfg
                data={this.titleCol}
                onChange={this._colsChange.bind(this)}
              >
                <SelfIcon style="font-size:16px;" icon="config" size="sm" />
              </TitleCfg>
            )
          }
          return
        } else if (col.type === 'action') { // 操作列
          col.resizable = false
          col.title = col.title || '操作'
          col.fixed = 'right'
          col.width = col.width || 170
          col.key = col.key || 'action'
          col.renderHeader = h => h('span', col.title)
          col.render = (h, params) => {
            let actions =
              typeof col.actions === 'function'
                ? col.actions(params)
                : col.actions || []
            actions = formatActions({
              actions,
              limit: col.actionCountLimit || 3
            })
            const actionCount = actions.length
            return h(
              'div',
              {
                class: ['editable-table__action']
              },
              actions.map((action, index) => {
                let actionConfirm = ''
                if (typeof action.confirm === 'function') {
                  actionConfirm = action.confirm(params, h)
                } else {
                  actionConfirm = action.confirm
                }
                let actionItem = [
                  h(Button, {
                    style: getActionItemStyle({
                      action,
                      defaultStyle: {
                        cursor: 'pointer',
                        color: 'var(--primary-color)'
                      },
                      params
                    }),
                    class: getActionItemClass({
                      action,
                      params
                    }),
                    attrs: getActionItemAttrs({
                      action,
                      params
                    }),
                    props: getActionItemProps({
                      action,
                      params,
                      defaultProps: {
                        type: 'text',
                        text: getActionItemName({ action, params }),
                        confirm: !!actionConfirm,
                        title: actionConfirm,
                        placement: 'top-end',
                        offset: '11',
                        openPoptipOption: false
                      }
                    }),
                    on: {
                      click: () => {
                        if (hasChildAction(action)) {
                          return false
                        }
                        this.handleAction(action, params)
                      },
                      'on-cancel': () => {
                        if (
                          action.actionCancel &&
                          typeof action.actionCancel === 'function'
                        ) {
                          action.actionCancel(params)
                        }
                      }
                    }
                  })
                ]
                if (hasChildAction(action)) {
                  actionItem = [
                    h(
                      Dropdown,
                      {
                        props: {
                          transfer: true,
                          trigger: 'hover',
                          placement: 'bottom-end'
                        }
                      },
                      [
                        actionItem,
                        h(SelfIcon, {
                          class: ['editable-table__action-dropdown__icon'],
                          props: {
                            icon: 'arrow-down'
                          }
                        }),
                        h(
                          'div',
                          {
                            class: ['editable-table__action-dropdown'],
                            slot: 'list'
                          },
                          action.children.map(childAction => {
                            let actionConfirm = ''
                            if (typeof childAction.confirm === 'function') {
                              actionConfirm = childAction.confirm(params, h)
                            } else {
                              actionConfirm = childAction.confirm
                            }
                            return h(Button, {
                              props: {
                                type: 'text',
                                text: getActionItemName({ action: childAction, params }),
                                confirm: !!actionConfirm,
                                title: actionConfirm,
                                placement: 'top-end',
                                offset: childAction.offset || -36,
                                openPoptipOption: false
                              },
                              style: getActionItemStyle({
                                action: childAction,
                                params,
                                defaultStyle: {
                                  display: 'block'
                                }
                              }),
                              class: getActionItemClass({
                                action: childAction,
                                defaultClass: [
                                  'editable-table__action-dropdown__item'
                                ],
                                params
                              }),
                              attrs: getActionItemAttrs({
                                action: childAction,
                                params
                              }),
                              on: {
                                click: () => {
                                  this.handleAction(childAction, params)
                                }
                              }
                            })
                          })
                        )
                      ]
                    )
                  ]
                }
                if (index < actionCount - 1) {
                  actionItem.push(
                    h('div', {
                      class: ['editable-table__action-divider']
                    })
                  )
                }
                return actionItem
              })
            )
          }
          return
        }


        const { type, key } = col

        const predefinedColumns = ['Input', 'NumberInput', 'Switch', 'DatePicker', 'TimePicker', 'Select', 'custom']
        if (type && predefinedColumns.includes(type)) {
          if (col.render === undefined) {
            col.render = (h, params) => {
              const { row, index } = params
              let { props = {} } = col
              let listeners = {}
              let nativeListeners = {}
              let component = type
              let childComponents = []

              if (typeof props === 'function') {
                props = props({ row, index })
              }

              switch (type) {
                case 'Input': // 输入框, 传入props.type可支持不同类型，可选值为 text、password、textarea、url、email、date、number、tel
                  break
                case 'NumberInput': // 数字输入框：使用项目内的NumberInput组件
                  {
                    if (props.value === undefined) {
                      props.value = row[key]
                    }
                  }
                  break
                case 'Switch': // 开关
                  {
                    component = 'i-switch'
                  }
                  break
                case 'DatePicker': // 日期选择器：多行弹出框渲染问题
                  {
                    props.transfer = true
                  }
                  break
                case 'TimePicker':  // 时间选择器：多行弹出框渲染问题
                  {
                    props.transfer = true
                  }
                  break
                case 'Select': // 选择器：多行弹出框渲染问题；远程搜索默认值问题
                  {
                    props.transfer = true
                    const data = props.data || col.data
                    childComponents = data.map(dataItem =>
                      h('Option', {
                        props: dataItem
                      })
                    )
                  }
                  break
                case 'custom': // 其它自定义组件
                  {
                    component = col.component
                  }
                  break
              }

              if (props.value === undefined) {
                if (typeof key === 'string') {
                  props.value = row[key]
                } else if (Array.isArray(key) && key.length > 0) {
                  props.value = key.map(keyItem => row[keyItem])
                }
              }

              // const handleChange = (...args) => {
              //   if (typeof key === 'string') {
              //     if (type === 'Input') { // 输入框组件的参数为event
              //       this.tableData[index][key] = row[key] = args[0].target.value
              //     } else {
              //       this.tableData[index][key] = row[key] = args[0]
              //     }
              //   } else if (Array.isArray(key) && key.length > 0) {
              //     key.forEach(keyItem => this.tableData[index][keyItem] = row[keyItem] = args[0])
              //   }
              // }
              // listeners['on-change'] = (...args) => {
              //   handleChange(...args) // 默认on-change事件更新表格绑定数据
              //   if (props.on['on-change']) props.on['on-change'](...args)
              // }
              if (props && props.on) { // 透传事件
                Object.keys(props.on).forEach(onKey => {
                  // if (typeof props.on[onKey] === 'function' && onKey !== 'on-change') {
                  //   listeners[onKey] = props.on[onKey]
                  // }
                  if (typeof props.on[onKey] === 'function') {
                    listeners[onKey] = props.on[onKey]
                  }
                })
              }

              if (props && props.nativeOn) { // 透传原生事件
                Object.keys(props.nativeOn).forEach(nativeOnKey => {
                  if (typeof props.nativeOn[nativeOnKey] === 'function') {
                    nativeListeners[nativeOnKey] = (...args) => {
                      props.nativeOn[nativeOnKey](...args)
                    }
                  }
                })
              }

              const { style = {}, attrs = {} } = props

              return h(component, {
                class: props.class,
                style,
                props,
                attrs,
                on: listeners,
                nativeOn: nativeListeners
              }, childComponents)
            }
          }
        }
      })
      const path = this.$route.path
      let columnWidth = []
      columnWidth = JSON.parse(localStorage.getItem(path + '&columnWidth' + this.tableId))
      if (columnWidth !== null) {
        copyColumns.forEach((item) => {
          let data = columnWidth.find(val => val.key === item.key)
          if (data && data.width) {
            this.$set(item, 'width', data.width)
          }
        })
      }
      this.copyColumns = copyColumns
    },
    /**
     * @vuese
     * 清清空行数据至默认值（defaultValue或'')
     * @param {Number} index 添加数据的位置（行下标）
     */
    clearRowData (index) {
      const newRow = this._getDefaultRow()
      if (this.rowKey) newRow[this.rowKey] = uniqueId('row_')
      this.$set(this.tableData, index, newRow)
    },
    /**
     * @vuese
     * 添加一行数据
     * @param {Number} index 添加数据的位置（行下标），数据将添加至index行后面
     */
    insertAt (index) {
      let newRow = null
      if (this.recordEditorConfig.autoInsertDelete !== false) {
        const { record } = this.recordEditorConfig // 自定义添加数据
        if (record) {
          newRow = record
        } else {
          /**
           * 1. 过滤掉index/selection/titleCfg/expand/action等列无需生成字段
           * 2. 行唯一key
           */
          newRow = this._getDefaultRow()
        }
        if (this.rowKey) newRow[this.rowKey] = uniqueId('row_')
        this.tableData.splice(index + 1, 0, newRow) // 在当前行之后添加
        this.$emit('on-insert', newRow, index) // 点击插入一行成功后触发（配合recordEditorConfig中autoInsertDelete有效，初始化默认行时index为-1）
      }
      this.$emit('on-click-insert', index) // 点击加号按钮时触发
    },
    _getDefaultRow () {
      let newRow = {}
      this.columns.forEach(col => {
        const { type, key, defaultValue } = col
        if (!isNewRecordExclude(type)) {
          if (typeof key === 'string') {
            newRow[key] = defaultValue !== undefined ? defaultValue : ''
          } else if (Array.isArray(key) && key.length) {
            if (defaultValue !== undefined && Array.isArray(defaultValue) && defaultValue.length) {
              key.forEach((keyItem, index) => newRow[keyItem] = defaultValue[index] !== undefined ? defaultValue[index] : '')
            } else {
              key.forEach(keyItem => newRow[keyItem] = '')
            }
          }
        }
      })
      // 添加一行标记是新加的row
      newRow._isNew = true;
      return newRow
    },
    /**
     * @vuese
     * 删除某行数据
     * @param {Number} index 行下标
     */
    deleteAt (index) {
      let deleteRow = null
      if (this.recordEditorConfig.autoInsertDelete !== false) {
        if (this.tableData.length > 1) {
          deleteRow = this.tableData.splice(index, 1)[0]
        } else {
          deleteRow = this.tableData[index]
          this.clearRowData(index)
        }
        this.$emit('on-delete', deleteRow) // 点击删除一行成功后触发（配合recordEditorConfig中autoInsertDelete有效）
      }
      this.$emit('on-click-delete') // 点击减号按钮时触发
    },
    // /**
    //  * @description: 修改行数据的方法
    //  * @param {String} rowId 行唯一标识，必须配合rowKey使用
    //  * @param {Object} rowData 整行数据，合并覆盖原行数据
    //  */
    // setRow (rowId, rowData) {
    //   const findIndex = this.tableData.findIndex(item => item[this.rowKey] === rowId)
    //   if (findIndex) Object.assign(this.tableData[findIndex], rowData)
    // },
    handleAction (actionItem, params) {
      const { action } = actionItem
      // action是函数，控制权交给函数
      if (action && typeof action === 'function') {
        action(params)
      }
    },
    async getTitleData (col) {
      const titleData = this.getTitleList()
      if (!this.titleConfig) {
        const res = await basic.getFields({ type: col.titleType })
        if (res.status) {
          this.titleConfig = res.data
          this.formatColumns(res.data)
          titleData.length && this.formatTitle(titleData)
        }
      } else {
        this.$nextTick(() => {
          this.formatColumns(this.titleConfig)
          titleData.length && this.formatTitle(titleData)
        })
      }
      this.hasTitleCfg = true
      this.$emit('on-cols-change', titleData)
    },
    getTitleList () {
      const title = localStorage.getItem(this.storePath || `title-${this.$route.path}${this.tableId}`)
      return title ? JSON.parse(title) : []
    },
    formatColumns (data = {}) {
      const { all_column, default_column, selected_column } = data
      this.titleCol = []
      this.allColKeys = Object.keys(all_column)
      this.showColumns.forEach((item, index) => {
        if (this._isDefCol(item)) {
          this.$set(item, 'checked', selected_column ? selected_column.indexOf(item.key) !== -1 : true)
          this.$set(item, 'required', default_column ? default_column.indexOf(item.key) !== -1 : false)
          this.$set(item, 'index', index)
          this.titleCol.push(item)
        }
      })
    },
    formatTitle (curTitle) {
      let i = 0
      this.showColumns.forEach((item, index) => {
        const colIdx = curTitle.indexOf(item.key)
        const isDefCol = this._isDefCol(item)
        if (colIdx !== -1) {
          item.index = colIdx + i
        } else if (!isDefCol) {
          i++
          item.index = index
        } else {
          item.index = -1
        }
        if (isDefCol) {
          item.checked = colIdx !== -1
        }
      })
    },
    _isDefCol (item) {
      return this.allColKeys.indexOf(item.key) !== -1
    },
    _colsChange (cols) {
      const data = cols.map(item => item.key)
      localStorage.setItem(this.storePath || `title-${this.$route.path}${this.tableId}`, JSON.stringify(data))
      this.formatTitle(data)
      this.$emit('on-cols-change', data)
    },
    /**
     * @vuese
     * 返回当前的请求参数
     */
    getParams () {
      const params = {
        ...this.params
      }
      return params
    },
    /**
     * @vuese
     * 手动设置表格数据
     * @arg {Array} data 需要设置的数据
     * @arg {Object} res 完整的接口返回数据
     */
    setData (data, res) {
      data = this.beforeSetData(data, res)
      this.tableData = data
    },
    /**
     * @vuese
     * 获取表格数据data的方法
     */
    fetchData () {
      let dataProvider = this.dataProvider
      let params = this.getParams()

      if (typeof dataProvider === 'string') {
        dataProvider = params => {
          return this.$request.get(this.dataProvider, params)
        }
      }
      params = this.beforeRequest(params)
      if (params === false) {
        return false
      }

      this.selfLoading = true
      dataProvider(params).then(res => {
        res = this.afterRequest(res)
        const { status, data: { list } } = res
        if (status) {
          this.setData(list, res)
        } else {
          this.tableData = []
          this.setData([], res)
        }
        this._handleReady()
      }).catch(() => {
        this.tableData = []
      }).finally(() => {
        this.selfLoading = false
      })
    },
    /**
     * on-row-click 点击某行时触发<br >`this.$emit('on-row-click', cloneDeep(row), index)`
     * @arg {Object} row
     * @arg {Number} index
     */
    _onRowClick (row, index) {
      this.$emit('on-row-click-only', row, index)
      this.$emit('on-row-click', row, index)
    },
    _onRowKeydown (event, index) {
      if(this.stopHotKey) return;
      if (event.keyCode !== 13) { // 除回车键之外，回车键使用keyup
        this._handleKotKey(event, index)
      }
    },
    _onRowKeyup (event, index) {
      if(this.stopHotKey) return;
      // 回车Enter事件，需要单独使用keyup事件处理，因为需要保证页面中表单控件的on-enter之类的事件在keyup事件之前触发，防止移动光标后导致页面内表单控件事件取得的index值不正确
      if (event.keyCode === 13 && this.enterAsDown && index < this.tableData.length - 1) {
        this._handleKotKey(event, index)
      }
    },
    _handleKotKey(event, index) {
      HotKey.handleTableRowsHotKey({
        event,
        index,
        ref: 'table',
        vm: this,
        _ENTER_AS_DOWN: this.enterAsDown,
        callback: nextIndex => {
          this._onRowClick(this.tableData[nextIndex], nextIndex)
        }
      })
    }
  }
}
</script>
