<!--
 * @Descripttion:
 * @Autor: Jeff
 * @Date: 2020-11-17 12:06:37
 * @LastEditTime: 2023-03-16 17:20:02
 * @FilePath: \sdpbase-pro\src\components\CheckboxGroup\src\main.vue
-->
<template>
  <CheckboxGroup v-model="selected" @on-change="checkboxGroupChange">
    <Checkbox v-for="(item, index) in data" :label="item.id" :key="index" :disabled="disabled || item.disabled">
      <span>{{ item.name }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>

<script>
import { Checkbox, CheckboxGroup } from 'view-design';

export default {
  name:'check_box_group',
  components: {
    Checkbox,
    CheckboxGroup
  },
  props: {
    value: {
      default: () => {
        return []
      }
    },
    defaultValue: {
      default() {
        return [];
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      data: [],
      selected: []
    };
  },
  inject: ['handleUpdateFilterCol'],
  watch: {
    value: {
      handler (val) {
        const value = val || []
        this.selected = Array.isArray(value) ? value : value.split(',');
      },
      // deep: true,
      immediate: true,
    }
  },
  computed: {},
  methods: {
    getValue(){
      return this.selected
    },
    resetValue(e){
      this.selected = e.defaultValue || []
    },
    checkboxGroupChange() {
      this.$emit('on-change', this.selected.join(','));
    },
    findSelectedItem() {
      let result = [];
      this.selected.map(label => {
        let find = this.data.find(item => {
          return item.label === label;
        });
        if (find) {
          result.push(find);
        }
      });
      return result;
    },
    render () {
      this.$request.get(this.apiUrl.qryOrderTagList).then(res => {
        if (res.status && res.data && res.data.length) {
          this.data = res.data
        } else {
          // 没有数据时, 不展示
          // 传递信息给上层父组件
          this.handleUpdateFilterCol && this.handleUpdateFilterCol({ key: this.name, attr: 'show', value: false })
        }
      })
    }
  },
  created() {
    if (this.defaultValue && this.defaultValue.length) {
      this.selected = JSON.parse(JSON.stringify(this.defaultValue));
    }
  },
  mounted() {
    this.render()
  }
}
</script>
<style lang="less"></style>
