<!--
 * @Description: 客户选择弹窗
 * @Date: 2023-01-28 11:04:31
 * @LastEditors: hgj
 * @LastEditTime: 2023-09-01 15:58:15
 * @FilePath: /sdpbase-pro/src/components/clientSelectModal/src/main.vue
-->
<template>
  <div class="">
    <Modal
      :title="title"
      v-model="modalShow"
      @on-cancel="closeClientModal"
      width="1086"
      class="user-filter-modal"
    >
      <ListTable
        v-if="modalShow"
        tableId="clientList-01"
        :showTableSelectionPanel="false"
        ref="clientListRef"
        :border="false"
        :outer-border="true"
        :columns="clientColumns"
        data-provider="/superAdmin/userSuper/list"
        :filters="filters"
        :before-request="beforeRequest"
        :after-request="afterRequest"
        :filter-items="clientFilterItems"
        @on-selection-change="handleSelectionChange"
        :height="getViewportHeight() * 0.75 - 300"
        :initChecked="false"
        :elevatorIsShow="true"
      >
      </ListTable>
      <template slot="footer">
        <div v-if="isEdit">
          <Button @click="closeClientModal">取消</Button>
          <Button type="primary" @click="selectAll">选择筛选客户</Button>
          <Button type="primary" @click="selectCur">选择勾选客户</Button>
        </div>
        <div v-else>
          <Button @click="closeClientModal">返回</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import { getViewportHeight } from '@/util/common';

import ListTable from '@components/list-table';
import UserTypeSelectMultiple from '@components/user/UserTypeSelectMultiple'

export default {
  name: '',
  components: {
    ListTable,
    UserTypeSelectMultiple
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    selectedIds: {
      type: Array,
      default: []
    },
    title: {
      type: String,
      default: '选择客户'
    },
    isEdit: {
      type: Boolean,
      default: true,
    }
  },

  data () {
    return {
      modalShow: false,
      selectedClientIdList: [],
      clientColumns: [
        {
          align: 'left',
          type: 'selection',
          width: 40,
          fixed: 'left',
        },
        {
          title: '客户名称',
          key: 'email'
        },
        {
          title: '客户编码',
          key: 'user_code'
        },
        {
          title: '客户类型',
          key: 'receivable_style'
        },
        {
          title: '区域',
          key: 'area_id'
        },
        {
          title: '联系人',
          key: 'name',
        },
      ],
      clientFilterItems: [
        {
          type: 'custom',
          component: UserTypeSelectMultiple,
          key: 'receStyle',
          label: '',
          defaultValue: [],
          props: {
            placeholder: '选择客户类型',
            showAll: true,
            maxTagCount: 1,
          }
        },
        {
          label: '',
          key: 'searchKey',
          type: 'Input',
          style: {
            width: '255px'
          },
          props:{
            placeholder: '输入客户名称/客户账号/客户编码/联系人'
          }
        },
      ],
    }
  },

  computed: {
    filters() {
      let params = {}
      if(!this.isEdit) {
        params = {
          filter_type: '1',
          user_ids: this.selectedIds.join(',')
        }
      }
      return params
    }
  },

  watch: {
    show: {
      handler(newValue) {
        this.modalShow = newValue
        if(newValue) {
          this.refresh()
        } else {
          this.selectedClientIdList = []
        }
      },
      immediate: true,
    },
  },

  created () {
  },

  mounted () {

  },

  methods: {
    getViewportHeight,
    paramsFormat(params) {
      if(Array.isArray(params.receStyle)) {
        params.receStyle = params.receStyle.join(',')
      }
      return params
    },
    getParams() {
      let params = this.$refs.clientListRef.getParams()
      return this.paramsFormat(params)
    },
    beforeRequest(params) {
      return this.paramsFormat(params)
    },
    afterRequest(res) {
      if(res.status) {
        res.data.list.forEach(item => {
          if(this.selectedClientIdList.includes(item.id)) {
            item._checked = true
          } else {
            item._checked = false
          }
        });
      }
      return res
    },
    closeClientModal() {
      this.$emit('cancel');
    },
    confirmClientModal() {
      this.$emit('confirm', this.selectedClientIdList);
    },
    handleSelectionChange(selection) {
      if(!this.$refs.clientListRef) return
      const tableData = this.$refs.clientListRef.getRebuildData()
      console.log("🚀 ~ file: main.vue:183 ~ handleSelectionChange ~ tableData:", tableData)

      tableData.forEach(item => {
        if(item._checked && !this.selectedClientIdList.includes(item.id)) {
          this.selectedClientIdList.push(item.id)
        }
        if(!item._checked && this.selectedClientIdList.includes(item.id)) {
          this.selectedClientIdList = this.selectedClientIdList.filter(id => id !== item.id)
        }
      })
      console.log("🚀 ~ file: main.vue:189 ~ handleSelectionChange ~ this.selectedClientIdList:", this.selectedClientIdList)
    },
    resetFilter() {
      this.$refs.clientListRef && this.$refs.clientListRef.resetFilter()
    },
    fetchData() {
      this.$refs.clientListRef && this.$refs.clientListRef.fetchData()
    },
    refresh() {
      this.selectedClientIdList = this.selectedIds;
      this.resetFilter()
      this.fetchData()
    },
    async selectAll() {
      const url = '/superAdmin/userSuper/list'
      const params = this.getParams()

      params.only_id = '1'
      const { status, message, data } = await this.$request.get(url, params)
      if(!status) {
        this.errorMessage(message)
        return
      }
      this.selectedClientIdList = data
      this.confirmClientModal()
    },
    selectCur() {
      this.confirmClientModal()
    },
  }
}

</script>
<style lang='less'>
.user-filter-modal {
  .ivu-modal-body {
    .list-table {
      .filter__col {
        display: flex;
        align-items: center;
        margin-right: 36px;
      }
      .filter__col:last-of-type {
        margin-right: 0;
      }
      .filter__operation {

        margin-left: 12px;
      }
    }
  }
}
</style>
<style lang='less' scoped>
</style>
