import Tooltip from './src/main.vue';
export default Tooltip;
// import { Tooltip } from 'view-design';
// export default {
//   extends: Tooltip,
//   methods: {
//     handleClosePopper() {
//       console.log('close');
//       if (this.timeout) {
//         clearTimeout(this.timeout);
//         if (!this.controlled) {
//           this.timeout = setTimeout(() => {
//             this.visible = false;
//           }, 0);
//         }
//       }
//     }
//   }
// };
