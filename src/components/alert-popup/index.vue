<template>
  <Alert type="warning">
    <div class="alert-popup">
      <SIcon :size="12" style="color: #FF6E00" icon="zengzhitishi"></SIcon>
      <span class="alert-popup__text">{{text}}</span>
      <span class="alert-popup__link" @click="onGoLink" v-if="link">{{linkText}}<SIcon class="alert-popup__link__icon" :size="14" icon="arrow-down"></SIcon></span>
      <Poptip
        confirm
        width="240"
        :title="closeTip"
        :cancel-text="cancelText"
        :ok-text="okText"
        @on-cancel="onCancel"
        @on-ok="onSub"
        v-if="closeable"
      >
        <span class="alert-popup__close">x</span>
      </Poptip>
    </div>
  </Alert>
</template>
<script>
import SIcon from "@/components/icon";
import { reportCSM } from "@/api/csm";
export default {
  props: {
    text: {
      type: String,
      default: ''
    },
    // 是否显示链接
    link: {
      type: Boolean,
      default: false,
    },
    linkText: {
      type: String,
      default: ''
    },
    linkUrl: {
      type: String,
      default: ''
    },
    // 是否显示关闭按钮
    closeable: {
      type: Boolean,
      default: false,
    },
    closeTip: {
      type: String,
      default: '关闭后不再提示？'
    },
    okText: {
      type: String,
      default: '仍需展示'
    },
    cancelText: {
      type: String,
      default: '不再展示'
    },
    // csm上报用的服务名
    serviceName: {
      type: String,
      default: ''
    },
  },
  components: {
    SIcon,
  },
  methods: {
    onGoLink() {
      if (this.serviceName) {
        reportCSM(this.serviceName);
      }
      window.open(this.linkUrl);
    },
    onCancel() {
      this.$emit('cancel');
    },
    onSub() {
      this.$emit('ok');
    }
  }
}
</script>
<style lang="less" scoped>
.ivu-alert-warning {
  border: 0.5px solid #FFC86D;
}
.ivu-alert {
  padding: 7px 48px 7px 16px;
}
.ivu-poptip {
  margin-left: auto;
  position: relative;
  left: 36px;
}
.alert-popup {
  display: flex;
  align-items: center;
  color: #FF6E00;
  &__text {
    margin-left: 5px;
    color: #FF6E00;
  }
  &__link {
    margin-top: 1px;
    margin-left: 42px;
    color: #FF6E00;
    cursor: pointer;
    &__icon {
      position: relative;
      bottom: 2px;
      margin-left: 3px;
      transform: rotate(-90deg);
    }
  }
  &__close {
    padding: 0 10px;
    color: #FF6E00;
    font-size: 14px;
    cursor: pointer;
  }
}
</style>
