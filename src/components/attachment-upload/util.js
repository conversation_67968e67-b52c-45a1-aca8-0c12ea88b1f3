/*
 * @Description: 
 * @Autor: lizi
 * @Date: 2022-05-16 16:57:34
 * @LastEditors: lizi
 * @LastEditTime: 2022-05-16 16:59:44
 * @FilePath: \sdpbase-pro\src\components\attachment-upload\util.js
 */

/**
 * 获取两个数之间的随机数(默认0-100)
 * @param {Number} min 最小值(默认0)
 * @param {Number} max 最大值(默认100)
 * @returns 
 */
export function getRandomInt (min = 0, max = 100) {
    min = Math.ceil(min);
    max = Math.floor(max);
    return Math.floor(Math.random() * (max - min)) + min; //不含最大值，含最小值
}