<!--
 * @Description: 附件上传组件
 * @Autor: lizi
 * @Date: 2022-05-16 11:18:21
 * @LastEditors: lizi
 * @LastEditTime: 2022-05-17 15:18:15
 * @FilePath: \sdpbase-pro\src\components\attachment-upload\attachment-upload.vue
-->
<template>
  <div class="attach-upload">
    <template v-if="add">
      <div class="attach-upload__add">
        <Upload
          :show-upload-list="false"
          :accept="c_accept"
          :multiple="multiple"
          :max-size="maxSize"
          :before-upload="preBeforeUpload"
          :action="action"
          :on-exceeded-size="handleExceedeMaxSize"
          :data="postParams"
          :on-success="onSuccess"
          :format="c_format"
          :on-format-error="onFormatError"
        >
          <slot>
            <div class="attach-upload__add__btn">
              上传文件
            </div>
          </slot>
        </Upload>
        <p class="attach-upload__add__help">
          <span v-if="showSizeLimit">文件大小不得超过{{Math.floor(maxSize/1024)}}M，</span><span v-if="showMax">最多上传{{max}}个文件 ( 支持格式：{{c_accept_text}} )</span>
        </p>
      </div>
    </template>

    <div class="attach-upload__file-list" v-if="showFiles">
      <ul>
        <li
          v-for="(item, index) in fileList"
          :key="index"
        >
          <p class="attach-upload__file-list--file-name">
            <span :title="item.name">{{item.name}}</span>
            <Icon
              icon="input_delete"
              size="13"
              class="icon__delete"
              v-show="remove"
              @click="$_onRemove(index)"
            />
            <Icon
              icon="xiazai"
              size="13"
              class="icon__down"
              v-show="item.url"
              @click="$_onDownload(item.url)"
            />
          </p>
          <!-- 进度条 -->
          <div
            class="attach-upload__file-list--file-progress"
            v-show="item.url===''"
          >
            <div
              :class="['attach-upload__file-list--file-progress__fill', item.error? 'attach-upload__file-list--file-progress__fill--error' : 'attach-upload__file-list--file-progress__fill--active']"
              :style="{'width': item.loadingPercent+'%'}"
            ></div>
          </div>
          <p
            v-show="item.error"
            class="attach-upload__file-list--file-error"
          >{{item.error}}</p>
        </li>
      </ul>

    </div>

  </div>
</template>

<script>
import Icon from '@/components/icon'
import * as util from './util';

let inter = null;

export default {
  name: 'attachment-upload',
  props: {
    // 是否能添加文件
    add: {
      type: Boolean,
      default: true,
    },
    // 是否能删除文件(详情页不能删除)
    remove: {
      type: Boolean,
      default: true
    },
    // 附件列表([{name:'文件名', url:'文件路径'}])
    value: {
      type: Array,
      default: new Array()
    },
    // 最多支持上传多少个文件
    max: {
      type: Number,
      default: 10
    },
    accept: {
      type: String,
      default: 'rar,zip,doc,docx,xls,xlsx,pdf,jpg,png,jpeg'
    },
    // 文件大小限制(默认10mb)
    maxSize: {
      type: Number,
      default: 1024 * 10
    },
    showFiles: {
      type: Boolean,
      default: true
    },
    showSizeLimit: {
      type: Boolean,
      default: true
    },
    showMax: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: true
    },
    beforeUpload: {
      type: Function,
      default: () => {
        return true;
      }
    }
  },
  components: {
    Icon,
  },
  data () {
    return {
      fileList: [],
      // action动态获取
      action: '',
      postParams: {},
      postParamsMap: {}
    }
  },
  computed: {
    // 支持的文件格式转换成.格式
    c_accept () {
      const { accept } = this;
      // 加上.png、.jpg等文件的.前缀
      const acceptList = accept.split(',').map(item => '.' + item)
      return acceptList.join(',')
    },
    c_accept_text () {
      const { accept } = this;
      const acceptList = accept.split(',')
      return acceptList.join('.')
    },
    c_format () {
      const { accept } = this;
      const formatList = accept.split(',')
      return formatList
    },
  },
  watch: {
    value: {
      immediate: true,
      handler (newVal) {
        this._adapterToFileList(newVal);
      }
    }
  },
  methods: {
    preBeforeUpload (files) {
      const check = this.fileList.length < this.max;
      if (!check) {
        this.errorMessage(`最多上传${this.max}个文件`);
        return check;
      }

      if (!this.beforeUpload(files)) {
        return false;
      }
      // 截取文件名和文件后缀
      const fileName = files.name;
      const lastIndex = fileName.lastIndexOf('.')
      const name = fileName.substr(0, lastIndex);
      const extName = fileName.substr(lastIndex + 1);

      // 组装新的文件对象
      const newFile = {
        // 文件名(这里要保存完整的文件名，而不是截取的文件)
        name: fileName,
        // 扩展名，用于限定同类型的文件只能上传一个
        extName,
        // 上传进度随机数
        loadingPercent: util.getRandomInt(0, 50),
        // 上传错误信息
        error: '',
        // 服务器文件路径
        url: ''
      }

      // 判断是否有重名的文件
      const existFileIndex = this.fileList.findIndex(item => item.name === fileName)
      if (existFileIndex > -1) {
        // 有重复的直接删掉，新增加的放在最后
        this.fileList.splice(existFileIndex, 1)
      }
      // 文件信息存入文件列表
      this.fileList.push(newFile);

      // 更新虚拟的进度条
      if (inter === null)
        this._updateVirtualProcess();

      let p = this.$request.post('/superAdmin/General/GetImagesUploadOssToken', {
        name,
        ext_name: extName
      }).then(res => {
        this.action = res.data.url.replace('http:', 'https:');
        // 合并业务传入的请求参数、阿里云文件预上传返回的参数(例如token这些)
        this.postParams = Object.assign(this.postParams, this.data, res.data.params)
        // 通过file的name作为key，存储请求参数；方便后面获取参数
        this.postParamsMap[files.name] = this.deepClone(this.postParams);
      }).catch(err => {
        throw err;
      });
      return p;
    },
    onSuccess (response, file) {
      let postParams = this.postParamsMap[file.name] || {};
      const currentFile = this.fileList.find(item => item.name === file.name);
      if (currentFile) {
        let url = this.action + '/' + postParams.key;
        url = url.replace('http:', 'https:');
        currentFile.url = url;
        currentFile.loadingPercent = 100;
        currentFile.path = postParams.key;
        this._updateVmodel();
      }
    },
    onFormatError (file) {
      const { name } = file;
      const currentFile = this.fileList.find(item => item.name === name)
      if (currentFile) {
        const msg = `选择文件格式不符合，请修改文件格式`;
        currentFile.error = msg;
        currentFile.loadingPercent = 100;
      }
    },
    handleExceedeMaxSize (file) {
      const { name } = file;
      const currentFile = this.fileList.find(item => item.name === name)
      if (currentFile) {
        const msg = `文件太大, 超过${this.maxSize / 1024}M限制`;
        currentFile.error = msg;
      }

    },
    $_onRemove (index) {
      this.fileList.splice(index, 1);
      this._updateVmodel()
    },
    $_onDownload (url) {
      window.open(url, '_target')
    },
    /**
     * @description: 更新虚拟进度条(进度到90%就停止掉，等文件上传结束后直接隐藏)
     * @author: lizi
     */
    _updateVirtualProcess () {
      inter = setInterval(() => {
        let canStop = true;
        this.fileList.forEach(file => {
          let { loadingPercent, error } = file;
          if (loadingPercent < 90 && error === '') {
            loadingPercent = util.getRandomInt(file.loadingPercent, 90);
            file.loadingPercent = loadingPercent;
            // 还存在进度条小于90的就继续轮训
            if (loadingPercent < 90) {
              canStop = false;
            }
          }
        })
        if (canStop) {
          clearInterval(inter);
        }
      }, 1000)
    },
    _updateVmodel () {
      // 更新告诉业务组件最新的文件改变(只传递已经上传完的、并且没有错误信息的文件)
      const fileList = [];
      // 判断是否文件都处理完了(上传完、完成报错),只有都上传完了才能进行emit
      let canEmit = true;
      this.fileList.forEach(file => {
        const { name, url, error, loadingPercent, path } = file;
        if (url && error === '') {
          fileList.push({
            name,
            url,
            path
          })
        }
        // 没报错+ 没返回 + 进度未达到100% 代表还有数据进行中
        if (error === '' && url === '' && loadingPercent !== 100) {
          canEmit = false;
        }
      })
      canEmit && this.$emit('input', fileList)
      // 这里业务保存的时候可能还需要判断是有文件正在上传
    },
    _adapterToFileList (files) {
      if (files.length === 0) {
        this.fileList = []
        return;
      }
      this.deepClone(files).forEach(item => {
        const existFile = this.fileList.find(exist => exist.name === item.name);
        // 传递过来的文件信息不存在就添加
        if (!existFile) {
          item.loadingPercent = '';
          item.error = '';
          this.fileList.push(item);
        }
      });
    }
  },
  beforeDestroy () {
    inter && clearInterval(inter);
  }

}
</script>

<style lang="less" scoped>
.attach-upload {
  font-size: 13px;
  &__add {
    &__btn {
      cursor: pointer;
      width: 84px;
      border: 1px solid #d8d8d8;
      text-align: center;
      line-height: 30px;
      height: 30px;
      border-radius: 2px;
    }
    &__help {
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.3);
      line-height: 14px;
      margin-top: 8px;
      margin-bottom: 16px;
      word-break: break-all;
    }
  }
  &__file-list {
    margin-top: 9px;
    ul{
      list-style: none;
    }
    li {
      &:not(:first-of-type) {
        margin-top: 10px;
      }
    }
    &--file-name {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.5);
      line-height: 14px;
      font-size: 0;
      span {
        font-size: 13px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 260px;
        display: inline-block;
      }
      i {
        margin-left: 12px;
        line-height: 14px;
        vertical-align: top;
        cursor: pointer;
      }
    }
    &--file-progress {
      width: 234px;
      height: 5px;
      background: #e8e8e8;
      border-radius: 4px;
      margin-top: 6px;
      &__fill {
        height: 100%;
        border-radius: 4px;
        &--active {
          background: #03ac54;
        }
        &--error {
          background: #f13130;
        }
      }
    }
    &--file-error {
      color: #f13130;
      margin-top: 5px;
    }
  }
  .icon {
    &__down {
      color: var(--primary-color);
    }
    &__delete {
      color: rgba(0, 0, 0, 0.2);
    }
  }
}
</style>
