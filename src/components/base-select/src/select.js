import { Select } from 'view-design';
export default {
  extends: Select,
  props: {
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      focusIndex: 0
    };
  },
  methods: {
    reset() {
      this.query = '';
      this.focusIndex = 0;
      this.unchangedQuery = true;
      this.values = [];
      this.filterQueryChange = false;
    },
    onQueryChange(query) {
      if (query.length > 0 && query !== this.query) {
        // in 'AutoComplete', when set an initial value asynchronously,
        // the 'dropdown list' should be stay hidden.
        // [issue #5150]
        if (this.autoComplete) {
          let isInputFocused =
            document.hasFocus &&
            document.hasFocus() &&
            document.activeElement === this.$el.querySelector('input');
          this.visible = isInputFocused;
        } else {
          this.visible = true;
        }
      }
      this.query = query;
      this.unchangedQuery = this.visible;
      this.filterQueryChange = true;
      this.focusIndex = 0;
    }
  }
};
