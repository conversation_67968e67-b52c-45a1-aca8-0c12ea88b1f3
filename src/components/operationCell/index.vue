<template>
  <div class="box">
    <div
      class="primary-color mr16"
      v-for="(item, index) in normalConfig"
      :key="item.key"
      :class="{divider: index!==normalConfig.length-1 || dropdownConfig.length>0}"
    >
      <Button
        type="text"
        class="cursor"
        placement="top-end"
        :text="item.txt"
        :confirm="!!item.confirmTips"
        :title="item.confirmTips"
        :openPoptipOption="false"
        @click="click(item)"
      >
      </Button>
    </div>

    <Dropdown
      transfer
      trigger="hover"
      placement="bottom-end"
      v-if="dropdownConfig.length>0"
      >
      <span class="primary-color">
        <span>更多</span>
        <Icon type="ios-arrow-down"></Icon>
      </span>
      <div class="dropdown-box" slot="list">
        <li
          v-for="item in dropdownConfig"
          class="dropdown-item"
          :key="item.key"
        >
          <Poptip
            confirm
            transfer
            placement='top-end'
            v-if="item.confirmTips"
            :title="item.confirmTips"
            :offset="item.offset || -36"
            :options="poptipOption"
            @on-ok="click(item)"
          >
            <span>{{item.txt}}</span>
          </Poptip>
          <span v-else @click="click(item)">{{item.txt}}</span>
        </li>
      </div>
    </Dropdown>
  </div>
</template>

<script>
import Dropdown from '@/components/dropdown'
import Button from '@components/button'
export default {
  name: 'operationCell',
  components: {
    Dropdown,
    Button
  },

  props: {
    config: {
      type: Array,
      default: () => []
    },
    row: {
      type: Object,
      default: () => {}
    }
  },

  data () {
    return {
      normalConfig: [],
      dropdownConfig: [],
      poptipOption: {
        modifiers: {
          offset: {
            offset: '0,0'
          },
          preventOverflow: {
            boundariesElement: 'scrollParent',
            enabled: false
          }
        },
        //阻止页面滚动sroll事件频繁触发进行pop重新定位计算
        eventsEnabled: false,
      }
    }
  },

  computed: {},

  watch: {},

  created () {
    this.init()
  },

  mounted () {},

  methods: {
    init() {
      this.initConfig()
    },
    initConfig() {
      let configShow = this.config.filter(item => item.isShow)
      if(configShow.length > 4) {
        this.normalConfig = configShow.splice(0,3)
        this.dropdownConfig = configShow
      } else {
        this.normalConfig = configShow
      }
    },
    click(item) {
      console.log('click-item', item.click)
      // 有可能因为闭包导致作用域内的row为上一次调用的row，所以手动传最新的row过去操作
      item.click && item.click(this.row);
    },
  }
}

</script>
<style lang='less' scoped>
.box {
  display: flex;
  flex-direction: row;
  align-items: center;
  font-size: 13px;
  font-weight: 400;
}
.primary-color {
  color: var(--primary-color);
  flex-shrink: 0;
}
.cursor {
  cursor: pointer;
}
.divider:after {
  content: '';
  background-color: #e9e9e9;
  vertical-align: middle;
  display: inline-block;
  transform: translateX(8px);
  height: 13px;
  width: 1px;
}
.dropdown-box{
  margin-bottom: -10px;
  min-width: 106px;
  .dropdown-item {
    display: block;
    padding: 7px 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    cursor: pointer;
    /deep/.ivu-poptip{
      display: block;
      .ivu-poptip-rel {
        display: block;
        span {
          display: block;
        }
      }
    }
    span {
      display: block;
    }
  }
  .dropdown-item:hover {
    color: var(--primary-color);
    background-color: rgb(239, 250, 244);
  }
}
</style>
