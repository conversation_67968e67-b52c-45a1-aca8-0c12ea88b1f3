<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :disabled="disabled"
    :multiple="multiple"
    :clearable="clearable"
    :placeholder="placeholder"
    :filterByLabel="filterByLabel"
    :maxTagCount="maxTagCount"
  >
    <Option :value="item.value" v-for="item in list" :key="item.value">{{
      item.label
    }}</Option>
  </Select>
</template>

<script>
import goodsHttp from '@api/goods.js';
export default {
  name: 'GoodTagSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    },
    remote: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      default: '选择商品标签'
    },
    multiple: {
      type: Boolean,
      default: true
    },
    params: {
      type: Object,
      default: () => {}
    },
    filterByLabel: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: <PERSON>olean,
      default: true
    },
    maxTagCount: {
      type: Number,
      default: 1
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        if (this.default === null) {
          this.default = newValue;
        }
        this.selfValue = newValue;
      }
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      }
    },
    params: {
      deep: true,
      handler() {
        if (this.remote) {
          this.getList();
        }
      }
    }
  },
  data() {
    return {
      selfValue: '',
      list: [],
      default: null,
    };
  },
  created() {
    this.selfValue = this.value;
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data;
    }
  },
  methods: {
    async getList() {
      let data = []
      let res = await goodsHttp.getGoodsTag();
      if (res.status) {
        data = res.data.map(item => {
          return {
            label: item.name,
            value: item.name
          }
        })
      } else {
        data = []
      }
      console.log('goodsTag', data)
      this.list = data
    },
    setQuery(query) {
      this.$refs.select.setQuery(query);
    },
    updateValue() {
      this.$emit('input', this.selfValue);
      this.$emit('on-change', this.selfValue);
    },
    getSelectedItem() {
      return this.list.find(item => item.line_id === this.selfValue);
    },
    resetValue () {
      this.selfValue = this.default;
    },
  }
};
</script>

<style scoped></style>
