@upload-prefix: base-upload;

.@{upload-prefix} {
  .ivu-upload-select {
    vertical-align: top;
  }
  &__item {
    width: 90px;
    height: 90px;
    padding: 2px;
    border: 1px solid rgba(216, 216, 216, 0.8);
    float: left;
    margin: 0 22px 10px 0;
    position: relative;
    &:hover {
      .@{upload-prefix}__mask {
        visibility: visible;
        opacity: 1;
      }
    }
  }
  &__mask {
    position: absolute;
    left: -1px;
    right: -1px;
    top: 0;
    bottom: -2px;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
    visibility: hidden;
    opacity: 0;
    transition: 0.3s all;
    .@{upload-prefix}__close-icon {
      color: #b2b2b2;
      background: #fff;
      position: absolute;
      right: -8px;
      top: -8px;
      border-radius: 50%;
      line-height: 1;
      cursor: pointer;
    }
    .@{upload-prefix}__view-icon {
      position: absolute;
      bottom: 20px;
      right: 12px;
      color: #fff;
      cursor: pointer;
    }
  }

  &__content {
    background: #f6f8f9;
    height: 100%;
    text-align: center;
    color: rgba(0, 0, 0, 0.5);
    line-height: 30px;
    font-size: 14px;
    cursor: pointer;
  }
  
  &__view {
    text-align: center;
    img {
      padding: 15px 0;
      max-width: 100%;
      max-height: 500px;
    }
  }
  &__info {
    margin-top: 10px;
  }
  &__explain {
    color: rgba(0, 0, 0, 0.4);
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 2px;
  }
  &__error {
    color: #f13130;
    font-size: 12px;
  }
}
