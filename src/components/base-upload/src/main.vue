<template>
  <div class="base-upload">
    <div class="base-upload__images img-upload-list">
      <template v-if="showFiles">
        <div class="base-upload__item demo-upload-list" :key="index" v-for="(item, index) in imgs">
          <div class="base-upload__content">
            <div class="img"><img :src="item + '!100x100'" /></div>
          </div>
          <div class="base-upload__mask">
            <s-icon
              class="base-upload__close-icon"
              icon="solid-close"
              :size="16"
              v-if="!disabled"
              @click="del(index)"
            />
            <s-icon
              class="base-upload__view-icon"
              icon="img-view"
              :size="22"
              @click="view(item, index)"
            />
          </div>
        </div>
      </template>
      <UploadForAliyun
        v-if="!disabled && !disableAddition"
        :show-upload-list="false"
        :format="format"
        :accept="accept"
        :max-size="maxSize"
        :before-upload="beforeUpload"
        @on-success="successHandle"
        :on-error="errorHandle"
        :on-exceeded-size="exceededHandle"
      >
        <slot>
          <div class="change-upload-type">
            <div class="base-upload__content">
              <s-icon icon="add" class="load_img" :size="20" />
              <p class="load_text">上传</p>
            </div>
          </div>
        </slot>
      </UploadForAliyun>
    </div>
    <div v-if="!disabled && !disableAddition" class="base-upload__info">
      <div class="base-upload__explain">{{ explain }}</div>
      <div class="base-upload__error">{{ errorText }}</div>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import { Upload } from 'view-design';
import { SIcon, SModal } from '@sdp/ui';
export default {
  name: 'base-upload',
  components: {
    Upload,
    SIcon,
    SModal
  },
  props: {
    value: {
      type: Array,
      default() {
        return [];
      }
    },
		showFiles: {
			type: Boolean,
			default: true
		},
		disableAddition: {
			type: Boolean,
			default: false
		},
    action: {
      type: String,
      default: '/superAdmin/general/upload'
    },
    format: {
      type: Array,
      default() {
        return ['JPG', 'PNG', 'JPEG'];
      }
    },
    accept: {
      type: String,
      default: 'image/jpg, image/png, image/jpeg,'
    },
    maxSize: {
      type: Number,
      default: 5120
    },
    max: {
      type: Number,
      default: 10
    },
    disabled: {
      type: Boolean
    },
    uploadBefore: Function
  },
  data() {
    return {
      imgs: [],
      errorText: '',
      explain: '',
    };
  },
  created() {
    this.explain = `支持格式：${this.format.join('、')}，图片大小≤${this
      .maxSize / 1024}M，最多${this.max}张`;
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.imgs = val;
      }
    }
  },
  computed: {},
  methods: {
    resetErr() {
      this.explain = '';
      this.errorText = '';
    },
    beforeUpload(files) {
      if (!this.max || this.imgs.length < this.max) {
        this.errorText = '';
        if (this.uploadBefore) {
          return this.uploadBefore(files);
        }
        return true;
      } else {
        this.errorText = `上传数量不能超过${this.max}张`;
        return false;
      }
    },
    successHandle(res, file, fileList) {
			if (this.imgs.length >= this.max) {
				this.errorText = `上传数量不能超过${this.max}张`;
				this.imgs.length = this.max;
				return false;
			}
      this.imgs.push(res.data.upyun_url);
      this.$emit('input', this.imgs);
      this.$smessage({ type: 'success', text: '上传成功' });
    },
    errorHandle(err) {
      this.errorText = err.message ? '上传失败：' + err.message : '上传失败';
    },
    exceededHandle() {
      this.errorText = `图片大小不能超过${this.maxSize / 1024}M`;
    },
    del(index) {
      this.imgs.splice(index, 1);
      this.$emit('input', this.imgs);
    },
    view(img, _viewIndex) {
      const images = this.imgs.map(item => item);
      this.viewImage(images, _viewIndex)
    }
  }
};
</script>
<style lang="less" scoped>
.img-upload-list {
  & > .ivu-upload {
    margin-top: 10px;
  }
  & > div:nth-child(-n + 2) {
    margin-top: 0;
  }
}
</style>
