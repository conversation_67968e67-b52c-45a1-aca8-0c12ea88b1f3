<template>
  <div class="print__template-choose">
    <Modal v-model="show" :title="title" :width="560">
      <template v-if="btnText.includes('导出')">
        <div>导出设置:</div>
        <RadioGroup v-model="exportSheetMode" class="mt5">
          <Radio class="print__template-choose__item fz13" :label="excelSheetMode.EXCEL_SHEET_SINGLE">单个sheet导出</Radio>
          <Radio class="print__template-choose__item fz13" v-if="export_excel_via_print_template" :label="excelSheetMode.EXCEL_SHEET_MULTI">按订单多sheet导出</Radio>
          <Radio class="print__template-choose__item fz13" v-if="export_excel_via_print_template" :label="excelSheetMode.EXCEL_SHEET_MULTI_BY_USER">按客户分sheet导出</Radio>
        </RadioGroup>
      </template>
      <Row align="middle" justify="space-between">
        <Col class="fw6">按照默认指定模板:</Col>
        <Col v-if="showPreview">
          <Checkbox v-model="previewChecked" :disabled="previewDisabled" size="small" style="margin-right: -3px;">
            <span>打印前预览</span>
          </Checkbox>
          <Tooltip placement="top">
            <span slot="content">
              仅在选择自定义模板时，才能进行打印预览；固定表尾模板不支持打印预览
            </span>
            <!-- <Icon style="cursor: pointer; vertical-align: baseline; margin-left: -8px" type="ios-help-circle"></Icon> -->
            <SIcon :size="12" style="margin-top: -1px;" icon="help1"/>
          </Tooltip>
        </Col>
      </Row>
      <RadioGroup v-model="currentTemplate" class="mt5" style="margin-top: -4px;">
        <Radio class="print__template-choose__item fz13" label="">按照客户或者配置的默认打印模板进行{{btnText}}</Radio>
      </RadioGroup>
      <!-- 订单列表才有的这个选项 -->
      <template v-if="source === 'lineList'">
        <div class="mt5">选择打印顺序:</div>
        <RadioGroup v-model="sort_by" class="mt5">
          <Radio class="print__template-choose__item fz13" label="line_order">按线路再按订单下单顺序打印</Radio>
          <Radio class="print__template-choose__item fz13" label="line_user">按线路再按客户名称打印</Radio>
        </RadioGroup>
      </template>
      <div class="mt5 fw6">自定义选择模板:</div>
      <div class="print__template-choose__content">
        <RadioGroup v-model="currentTemplate" >
          <!-- 不知道哪个测试把自定义选择模板名字搞得这么长UI还要对齐，只能用tooltip -->
          <Tooltip :content="template.name" v-for="(template, i) in templateData" :key="template.id" :max-width="190">
          <Radio
            class="print__template-choose__item2 fz13"
            :key="i"
            :label="template.id"
            ><SIcon
              class="icon--new"
              icon="xin"
              v-if="isNewTemplate(template)"
            />{{ template.name }}</Radio
          >
          </Tooltip>
        </RadioGroup>
      </div>
      <Row slot="footer" type="flex" justify="center" :gutter="10" align="middle" >
        <Col> <Button :disabled="disabled" @click="_onExport" type="primary">{{btnText}}</Button></Col>
        <Col><Button @click="closeModal">取消</Button></Col>
      </Row>
    </Modal>
  </div>
</template>

<script>
import settings from "@api/settings";
import { RadioGroup, Radio, Row, Col } from "view-design";
import Button from "../../button";
import SIcon from "@components/icon";
import { EXCEL_SHEET_MULTI, EXCEL_SHEET_SINGLE, EXCEL_SHEET_MULTI_BY_USER } from '@/util/print';
import ConfigMixin from '@/mixins/config';
export default {
  components: {
    RadioGroup,
    Radio,
    Row,
    Col,
    Button,
    SIcon,
  },
  props: {
    title: {
      type: String,
      default: "导出选择模板",
    },
    type: {
      type: String,
      default: "ORDER",
    },
    // 是否显示打印前预览勾选项
    showPreview: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    btnText: {
      type: String,
      default: "导出",
    },
    source: {
      type: String,
    },
    filterOld: {
      type: Boolean,
      default: true,
    },
  },
  mixins: [ConfigMixin],
  data() {
    return {
      sort_by: 'line_order',
      show: false,
      previewChecked: false,
      previewDisabled: true,
      currentTemplate: "",
      exportSheetMode: EXCEL_SHEET_SINGLE,
      excelSheetMode: {
        EXCEL_SHEET_MULTI,
        EXCEL_SHEET_SINGLE,
        EXCEL_SHEET_MULTI_BY_USER
      },
      templateData: [],
      callback: () => {},
    };
  },
  watch: {
    currentTemplate(val) {
      if(!val) {
        this.previewChecked = false;
        this.previewDisabled = true;
      } else {
        const template = this.templateData.find((_) => _.id === val);
        if (template && settings.isNewTemplate(template)) {
          const tplData = JSON.parse(template.tpl_data);
          // 底部固定时不能预览
          if (tplData.config.fixedFooter === 'Y') {
            this.previewChecked = false;
            this.previewDisabled = true;
          } else {
            this.previewDisabled = false;
          }
        } else {
          this.previewDisabled = false;
        }
      }
    },
  },
  methods: {
    openModal() {
      this.show = true;
      this.currentTemplate = ''
    },
    closeModal() {
      this.show = false;
      this.currentTemplate = ''
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    _onExport() {
      this.callback(this.currentTemplate, this.previewChecked, this.exportSheetMode, {
        sort_by: this.source === 'lineList' ? this.sort_by : ''
      });
    },
    open(callback) {
      this.callback = callback;
      this.fetchTemplateData();
      this.openModal();
    },
    async fetchTemplateData() {
      const res = await this.$request.get(this.apiUrl.getPrintTemplate, {
        type: this.type,
      });
      const { list } = res.data;
      this.templateData = Array.isArray(list) ? list : [];
      // 过滤老模板
      if (!this.filterOld) return
      this.templateData = this.templateData.filter((_) => {
        return settings.isNewTemplate(_);
      });
    },
  }
};
</script>
<style lang="less" scoped>
/deep/ .ivu-modal .ivu-modal-body {
  padding: 11px 24px 30px 24px;
}
.fz13 {
  font-size: 13px;
}
.fw6 {
  font-weight: 600;
}
.print {
  &__template-choose {
    &__item {
      margin-right: 50px;
    }
  }
}
.icon--new {
  font-size: 16px;
  margin-right: 5px;
  color: #ff9f00;
}
/deep/.ivu-tooltip-inner {
    max-width: inherit;
    box-shadow: none;
  }
  .print__template-choose__content{
      max-height: 500px;
      overflow-y: auto;
      line-height:1;
      padding:3px 0;
  }
  /deep/.ivu-radio{
      overflow: hidden;
  }
  .print__template-choose__item{
    overflow: hidden;
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    // width: 200px;
    // white-space: nowrap; 
    // text-overflow: ellipsis;
  }
  .print__template-choose__item2 {
    overflow: hidden;
    // display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
    width: 200px;
    white-space: nowrap; 
    text-overflow: ellipsis;
    margin-right: 50px;
  }
</style>
