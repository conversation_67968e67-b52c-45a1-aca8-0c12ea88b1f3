<template>
  <div class="import-box">
    <!--导入按钮-->
    <Dropdown v-if="data.length > 1" @on-click="toggleDialog">
      <div class="import-more">
        <Button class="import-btn"
          >{{btnName}}<Icon size="mini" icon="arrow-down"
        /></Button>
      </div>
      <DropdownMenu slot="list">
        <DropdownItem
          :key="index"
          :name="index"
          v-for="(item, index) in data"
          >{{ item.text || item.title }}</DropdownItem
        >
      </DropdownMenu>
    </Dropdown>
    <Button v-else @click="toggleDialog">{{btnName}}</Button>
    <!--导入弹出框-->
    <Modal
      :class-name="'modal-for-importBtn ' + modalClassName"
      v-model="show"
      :width="modalWidth ? modalWidth : 520"
      :closable="false"
      :title="curData.title"
    >
      <div class="import-box__dialog__header" slot="header">
        <div class="import-box__dialog__border"></div>
        {{ curData.title }}
        <span class="import-box__dialog__delete" @click="cancel">
          <Icon icon="close" class="import-box__dialog__delete__icon" />
        </span>
      </div>
      <div class="import-box__dialog__body">
        <slot name="custom-area" :curData="curData"></slot>
        <div
          v-if="
            this.$slots['custom-area-left'] || this.$slots['custom-area-right']
          "
          class="import-box__dialog__body__import__description marginT20"
        >
          <div class="import-box__dialog__body__download__text__left">
            <slot
              v-if="this.$slots['custom-area-left']"
              name="custom-area-left"
            ></slot>
          </div>
          <div v-if="this.$slots['custom-area-right']">
            <slot name="custom-area-right"></slot>
          </div>
        </div>
        <div
          class="import-box__dialog__body__download__description margin20"
          v-if="curData.download.text"
        >
          <span class="import-box__dialog__body__download__text__left">{{curData.download.label || '下载模板：'}}</span>
          <span class="ml12" v-if="curData.download.textTips !== false">点击下载 </span>
          <span
            @click="downloadTemplate"
            class="import-box__dialog__body__download__text"
          >{{ curData.download.text }}</span
          >
        </div>
        <div class="import-box__dialog__body__import__description margin20">
          <span class="import-box__dialog__body__download__text__left"
            >选择上传文件：</span
          >
          <Upload
            class="import-box__dialog__body__import__upload__box ml12"
            :action="curData.post.url"
            :data="curData.post.extraParams || {}"
            :show-upload-list="false"
            :on-format-error="onFormatError"
            :on-error="onError"
            :on-success="onSuccess"
            :on-exceeded-size="onExceededSize"
            :on-visible-change="onVisibleChange"
            :on-progress="onProgress"
            :before-upload="beforeUpload"
            :mask-closable="false"
            :accept="curData.post.accept"
            :format="curData.post.format"
            :max-size="curData.post.maxSize"
          >
            <Button
              :disabled="importing"
              :loading="loadingStatus"
              class="import-box__dialog__body__select"
              type="primary"
              >{{ importing ? '导入中...' : '选择文件' }}</Button
            >
          </Upload>
          <span
            v-show="fileName"
            class="import-box__dialog__body__import__file__name ml12"
          >
            {{ fileName }}
            <span @click="removeFile">
              <Icon
                icon="solid-close"
                size="mini"
                class="import-box__dialog__body__delete__file__icon"
              />
            </span>
          </span>
        </div>
        <div
          v-show="errorTip"
          class="import-box__dialog__body__error__description mt14 mb27"
        >
          <span class="import-box__dialog__body__download__text__left"></span>
          <span class="ml12">{{ errorTip }}</span>
          <a :href="toUrl" v-show="toUrl" style="color:rgb(3 172 84)" target="_blank">【点击前往】</a>
        </div>
      </div>
      <div class="import-box__dialog__footer" slot="footer">
        <Button @click="cancel" class="import-box__dialog__footer__cancel"
          >取消</Button
        >
        <Button
          @click="confirm"
          class="import-box__dialog__footer__confirm"
          type="primary"
          >确定</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import {
  Modal,
  Upload,
  Dropdown,
  DropdownMenu,
  DropdownItem
} from 'view-design';
import Button from '../../button/index';
import Icon from '../../icon/index';
import { importLoop } from '@/components/common/import-btn/util.js'

export default {
  components: {
    Button,
    Modal,
    Upload,
    Icon,
    Dropdown,
    DropdownMenu,
    DropdownItem
  },
  props: {
    data: {
      type: Array,
      default() {
        return [];
      },
      required: true
    },
    btnName: {
      type: String,
      default: '批量导入',
    },
    // 是否用notice通知的方式展示错误提示
    isErrorNotice: {
      type: Boolean,
      default:  false
    },
    beforeUploadFlag: {
      type: Function,
      default: () => true
    },
    modalWidth: {
      type: Number,
      default: 0,
    },
    // 弹框自定义类名
    modalClassName: {
      required: false,
      type: String,
      default: () => ''
    },
  },
  data() {
    return {
      createDownloadUrlFnExecuting: false,
      importSuccess: false,
      importing: false,
      show: false,
      errorTip: '',
      fileName: '',
      file: '',
      loadingStatus: false,
      upload: () => {},
      curIndex: 0,
      toUrl: '', // 需要跳转的路由地址
    };
  },
  watch: {},
  computed: {
    curData() {
      return this.data[this.curIndex];
    }
  },
  created() {},
  methods: {
    // 显示、隐藏弹出框
    toggleDialog(index = 0) {
      this.$emit('click', index, this.data[index])
      this.curIndex = index;
      this.fileName = '';
      this.errorTip = '';
      this.toUrl = '';
      this.show = !this.show;
    },
    // 下载模板
    async downloadTemplate() {
      this.$emit('downloadTemplate', this.curData.download.url, this.curData);
      if (!this.curData.download.url && !this.curData.download['before-download']){
        return;
      }
      let url = '';
      if (this.curData.download['before-download']) {
        if (this.createDownloadUrlFnExecuting) return;
        this.createDownloadUrlFnExecuting = true;
        url = await new Promise(resolve => {
          this.curData.download['before-download'](resolve);
        });
        this.createDownloadUrlFnExecuting = false;
        if (!url) {
          return;
        }
      } else {
        url = this.curData.download.url;
      }
      if(this.curData.download.http) {
        let { status, message, data }= await this.$request.get(url)
        if (status) {
          if (data.startsWith("http://")) {
            data =  "https://" + data.slice(7);
          }
          window.location.href = data
        } else {
          this.errorMessage(message)
        }
        return
      }
      window.location.href = url;
    },
    // 点击确定
    confirm() {
      if (!this.file) {
        this.errorTip = '请选择文件';
        return;
      }
      
      // 上传前的验证方法
      if (!this.beforeUploadFlag()) return

      if (this.errorTip) {
        return;
      }
      
      this.upload();
      this.loadingStatus = true;
    },
    // 点击取消
    cancel() {
      if (this.importing) {
        return false;
      }
      this.removeFile();
      this.show = false;
    },
    // 文件格式校验失败
    onFormatError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `文件格式校验失败，请上传 ${this.curData.post.format +
        ''} 文件。`;
    },
    // 文件上传失败
    onError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件失败，请重试。`;
    },
    // 文件超过大小限制
    onExceededSize() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件超过大小限制，文件不能超过 ${this.curData.post.maxSize}kb。`;
    },
    // 文件开始上传
    onProgress() {
      this.importing = true;
      this.importSuccess = false;
    },
    beforeUpload(file) {
      this.fileName = file.name;
      this.file = file;
      this.errorTip = '';
      return new Promise(resolve => {
        this.upload = resolve;
      });
    },
    // 文件上传成功
    onSuccess(response) {
      this.importing = false;
      this.loadingStatus = false;
      let { status, message, data } = response;
      if (status) {
        // 离线导入
        if(this.curData.offline){
          this.infoMessage(message || '操作成功，导入任务执行中')
          importLoop(data.exec_no)
        }else{
          this.importSuccess = true;
          this.$emit('on-completed', this.importSuccess, data);
        }
        this.errorTip = '';
        this.removeFile();
        this.show = false;
      } else {
        if(this.curData.offline){
          this.errorMessage(message || '导入失败')
        }else{
          this.importSuccess = true;
          this.errorTip = response.message || '';
          // 如果是导入客户的情况，出现错误，需要更换模板并且跳转
          if (this.errorTip.includes( '回收站' )) {
            this.toUrl = '#/recycle/userRecycle'
          }
        }
      }
    },
    onVisibleChange() {
      this.fileName = '';
      this.errorTip = '';
    },
    removeFile() {
      this.fileName = '';
      this.file = '';
      this.errorTip = '';
      this.loadingStatus = false;
    }
  },
  mounted() {}
};
</script>
<style lang="less">
@box-prefix: import-box;
.@{box-prefix} {
  vertical-align: top;
  display: inline-block;

  .import-more {
    overflow: hidden;
    display: inline-block;
    vertical-align: top;

    .sui-icon {
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover {
      .sui-icon {
        transform: rotate(180deg);
      }
    }
  }
}

.ivu-modal-wrap {
  .margin20 {
    margin: 20px 0;
  }
  .marginT20 {
    margin-top: 20px;
  }
  .ml12 {
    margin-left: 12px;
  }
  .mt14 {
    margin-top: 14px;
  }
  .mb27 {
    margin-bottom: 27px;
  }
  .@{box-prefix}__dialog__header {
    font-size: 14px;
    color: #303030;
    text-align: left;
    line-height: 14px;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center;
  }
  .@{box-prefix}__dialog__border {
    width: 3px;
    height: 12px;
    background: #505050;
    border-radius: 0.5px;
    border-radius: 0.5px;
    display: inline-block;
    margin-left: 10px;
    margin-right: 6px;
  }
  .@{box-prefix}__dialog__delete {
    position: absolute;
    right: 25px;
  }
  .@{box-prefix}__dialog__delete__icon {
    font-size: 10px;
    cursor: pointer;
  }
  .@{box-prefix}__dialog__body {
    margin: -16px;
    &__import__description {
      width: 100%;
      display: inline-flex;
      align-items: center;
    }
    &__download__description {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      margin-bottom: 0px;
    }
    &__download__text__left {
      display: inline-block;
      width: 23%;
      text-align: right;
    }
    &__download__text {
      color: #03ac54;
      cursor: pointer;
    }
    &__import__upload__box {
      display: inline-block;
    }
    &__select {
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
    }
    &__import__file__name {
      font-size: 13px;
      color: #505050;
      text-align: left;
      line-height: 14px;
      display: inline-flex;
      align-items: center;
      width: 200px;
    }
    &__delete__file__icon {
      margin-left: 16px;
      color: rgba(0, 0, 0, 0.2);
      cursor: pointer;
    }
    &__error__description {
      font-size: 13px;
      color: red;
      text-align: left;
      line-height: 14px;
      padding: 0 20px;
    }
  }
  .@{box-prefix}__dialog__footer {
    &__cancel {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      background: #ffffff;
      // border: 1px solid #d8d8d8;
      border-radius: 2px;
      border-radius: 2px;
      margin-right: 4px;
    }
    &__confirm {
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
    }
  }
}
</style>
