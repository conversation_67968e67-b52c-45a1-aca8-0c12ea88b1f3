<!--
 * @Description:
 * @Autor: lizi
 * @Date: 2021-10-26 10:02:37
 * @LastEditors: lizi
 * @LastEditTime: 2021-10-26 10:50:27
 * @FilePath: \sdpbase-pro\src\components\RadioGroup\src\RadioGroup.vue
-->
<template>
  <div>
    <RadioGroup v-model="value" @on-change="$_onRadioChange">
      <Radio
        v-for="item in data"
        :label="item.value"
        :size="size"
        :key="item.value"
        >{{ item.label }}</Radio
      >
    </RadioGroup>
  </div>
</template>
<script>
export default {
  name: 'radio_group',
  props: {
    data: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
    size: {
      type: String,
      default: 'default',
    },
    defaultValue: {
      type: [String, Number],
      default: '',
    },
  },

  data() {
    return {
      value: '',
    };
  },
  methods: {
    $_onRadioChange() {
      this.$emit('on-change', this.value);
    },
    resetValue() {
      this.value = this.defaultValue;
      // 调用父级reset方法(可能还有其它操作，例如订单列表标签筛选重置之后需要恢复订单标签的使用)
      this.$emit('on-reset', this.value);
    },
  },
  watch: {
    defaultValue: {
      immediate: true,
      handler(newVal) {
        this.value = newVal;
      },
    },
  },
};
</script>
