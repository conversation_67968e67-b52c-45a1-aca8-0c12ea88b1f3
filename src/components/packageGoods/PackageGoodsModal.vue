<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="onCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      v-bind="modalProps"
      width="840">
      <list-table
        ref="list"
        :table-height="getTableHeight() - 80"
        :api="apiUrl.goodsPackage.list"
        :auto-load-data="false"
        :filter-items="filterItems"
        :filters="filters"
        :columns="cols"
        :row-class-name="rowClassName"
        :after-load-list="afterLoadList"
        :before-request="beforeRequest"
        @after-render="afterRender"
        @on-select="onSelect"
        @on-row-click="onRowClick"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-selection-change="onSelectionChange">
      </list-table>
      <div slot="footer">
        <Button   @click="onCancel">{{cancelText}}</Button>
        <Button  type="primary"  @click="onOk">{{okText}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import ListTable from '@components/common/list-table/list'
  import { packageMode } from './util';
  import { formatGoodsThumbnailPath } from '@/util/common'
  const LIST_KEY = 'id';
  export default {
    name: "PackageGoodsModal",
    components: {
      ListTable
    },
    props: {
      modalProps: {
        type: Object,
        default: () => ({})
      },
      // 是否支持跨页选择
      selectionOtherPage: {
        type: Boolean,
        default: false
      },
      // 是否必须选择商品
      required: {
        type: Boolean,
        default: true
      },
      // 已添加的id
      addedIds: {
        type: Array,
        default: () => []
      },
      showAddStatus: {
        type: Boolean,
        default: false
      },
      showModeFilter: {
        type: Boolean,
        default: false
      },
      show: {
        type: Boolean,
        default: false
      },
      /**
       * 是否显示多选
       */
      showSelect: {
        type: Boolean,
        default: true
      },
      maxSelectCount: {
        type: Number,
        default: 0,
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      closable: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: '请选择套餐'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      okText: {
        type: String,
        default: '确 定'
      },
      // 默认选中的上商品
      defaultSelectedGoods: {
        type: Array,
        default: () => []
      },
      disabledGoods: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      columns: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      filterParams: {
        type: Object,
        default: () => {}
      },
      defaultValue: {
        type: Boolean,
        default: false
      },
    },
    watch: {
      show(newValue, oldValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.setFilters();
          this.selectedGoods = this.cloneObj(this.defaultSelectedGoods);
          this.loadGoods();
        }
      }
    },
    computed: {
    },
    data() {
      return {
        modal: {
          show: false,
          className: 'vertical-center-modal common-goods-modal'
        },
        tableHeight: this.getTableHeight() * 0.8,
        filterItems: [
          {
            key: 'name',
            placeholder: '请输入套餐名称',
            showIcon: true,
          }
        ],
        filters: {
          is_bom: '1',
        },
        goodsList: [],
        selectedRow: [],
        selectedGoods: [],
        cols: [
          {
            width: 60,
            type: "selection",
          },
          {
            title: "图片",
            width: 80,
            render: (h, params) => {
              let { row } = params;
              let key = 'pic_url';
              return h('img', {
                style: {
                  width: '40px'
                },
                attrs: {
                  src: row[key] ? formatGoodsThumbnailPath(row[key]) : ''
                }
              });
            }
          },
          {
            title: "套餐名称",
            key: "name",
          },
          {
            title: "单位",
            key: "unit",
            width: 100,
          },
          {
            title: "描述",
            key: "summary",
          },
        ],
      }
    },
    created() {
      if (this.showAddStatus) {
        if (!this.filterItems.find((item) => item.key === 'filter_type')) {
          this.filterItems.unshift({
            type: 'select',
            key: 'filter_type',
            placeholder: '全部状态',
            data: [
              {
                label: '全部状态',
                value: ''
              },
              {
                label: '已添加',
                value: '1'
              },
              {
                label: '未添加',
                value: '2'
              }
            ]
          });
        }
        if (!this.cols.find((item) => item.key === 'add_status')) {
          this.cols.splice(this.cols.findIndex((item) => item.key === 'unit') + 1, 0, {
            title: "状态",
            key: "add_status",
            width: 100,
            render: (h, params) => {
              let { row } = params;
              const isSelected = this.addedIds.includes(row[LIST_KEY]);
              return h('span', {
                style: {
                  color: isSelected ? '#FF6E00' : 'inherit'
                }
              }, isSelected ? '已添加' : '未添加');
            }
          });
        }
      }
    	if (this.showModeFilter) {
    		if (!this.filterItems.find((item) => item.key === 'mode')) {
    			let modeList = [
            {
            	label: '全部类型',
              value: ''
            },
            ...Object.values(packageMode)
          ];
    			this.filterItems.unshift({
            type: 'select',
            key: 'mode',
            data: modeList,
            placeholder: '全部类型'
          });
        }
      }
      this.setFilters();
      this.modal.show = this.show;
      if (!this.showFooter) {
        this.modal.className += ' hide-footer';
      }
      if (this.columns && this.columns.length > 0) {
        this.cols = this.columns;
      }
      if (this.show) {
        this.$nextTick(() => {
          this.$refs.list.loadListData();
        });
      }
    },
    methods: {
      beforeRequest(filters) {
        if (this.showAddStatus) {
          filters.package_ids = this.addedIds.join(',');
        }
        return filters;
      },
      setFilters() {
        this.filters = {
          ...this.filters,
          ...this.filterParams
        };
      },
      afterRender() {
        this.setDisabledGoods();
        this.checkMaxSelectCount();
      },
      changeCategory(category) {
        this.filters.category_id = category[0];
        this.filters.category_id2 = category[1];
        this.loadGoods();
      },
      loadGoods() {
        let selectedGoodsIdArr = this.defaultSelectedGoods.map((goods) => goods[LIST_KEY]);
        this.filters.commodity_string = JSON.stringify(selectedGoodsIdArr);
        this.$refs.list.loadListData();
      },
      afterLoadList(list) {
        list.forEach((goods) => {
          if (this.defaultValue) {
            goods.amount = 1;
            goods.order_amount = 1;
          }
          if (this.isSelected(goods)) {
            goods._checked = true;
          }
        });
      },
      onSelectAll (selection) {
        selection.forEach((row) => {
          this.selectGoods(row);
        });
      },
      onSelect(selection, row) {
        this.selectGoods(row);
      },
      onSelectionChange (selection) {
        const allSelection = this.selectionOtherPage ?  [...selection, ...this.selectedGoods] : [...selection];
        allSelection.forEach((goods) => {
          this.selectGoods(goods);
        });
        if (selection.length === 0) {
          this.$refs.list.list.forEach((item) => {
            this.cancelSelectGoods(item);
          });
        }
        this.checkMaxSelectCount();
      },
      onSelectCancel(selection, row) {
        this.cancelSelectGoods(row);
      },
      onRowClick(row, index) {
        if (this.isDisabled(row)) {
          return false;
        }
        if (this.isSelected(row)) {
          this.cancelSelectGoods(row);
          this.cancelCheckStatus(index);
        } else {
          this.selectGoods(row);
          this.setCheckStatus(index);
        }
        this.checkMaxSelectCount();
      },
      isSelected(goods) {
        return this.selectedGoods.some((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]);
      },
      isDisabled(goods) {
        return this.disabledGoods.some((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]) || goods._disabled;
      },
      setDisabledGoods() {
        let list = this.getList();
        list.forEach((goods) => {
          if (this.disabledGoods.find((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY])) {
            goods._disabled = true;
          }
        });
        this.setList(list);
      },
      checkMaxSelectCount() {
        let list = this.getList();
        let selectedLen = this.selectedGoods.length;
        if (!this.maxSelectCount) {
          return false;
        }
        list.forEach((goods, index) => {
          let selected = this.isSelected(goods);
          if (selectedLen >= this.maxSelectCount && !selected) {
            goods._disabled = true;
          } else {
            goods._disabled = false;
          }
          goods._checked = selected;
        });
        this.setList(list);
      },
      getList() {
        return this.$refs.list.getListData();
      },
      getSelectedGoods () {
        return this.deepClone(this.selectedGoods)
      },
      setList(list) {
        return this.$refs.list.setListData(list);
      },
      /**
       * 设置选中效果
       * @param index 选中行的索引
       */
      setCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 设置选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom && !checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.add('ivu-checkbox-wrapper-checked');
          checkBoxDom && !checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.add('ivu-checkbox-checked');
        }
      },
      /**
       * 取消选中效果
       * @param index 取消选中行的索引
       */
      cancelCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 取消选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.remove('ivu-checkbox-wrapper-checked');
          checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.remove('ivu-checkbox-checked');
        }
      },
      /**
       * 将商品添加到选中商品中
       * @param row
       */
      selectGoods(row) {
        if (!this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.push(row);
        }
      },
      /**
       * 从选中商品中删除商品
       * @param row
       */
      cancelSelectGoods(row) {
        if (this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.splice(this.selectedGoods.findIndex((goods) => goods[LIST_KEY] === row[LIST_KEY]), 1);
        }
      },
      rowClassName (row, index) {
      },
      onCancel() {
        this.$emit('on-cancel');
      },
      onOk() {
        if (this.required && this.selectedGoods.length === 0) {
          this.modalError('请选择商品', 0);
          return false;
        }
        if (this.maxSelectCount && this.maxSelectCount < this.selectedGoods.length) {
          this.modalError(`最多只能选择${this.maxSelectCount}条商品！`, 0);
          return false;
        }
        const list = this.getList();
        const selectedGoods = this.selectionOtherPage ? [...this.selectedGoods] : list.filter(item => this.isSelected(item));
        this.$emit('on-ok', this.cloneObj(selectedGoods));
      }
    }
  }
</script>

<style lang="less">
  .common-goods-modal {
    .ivu-modal .ivu-modal-header-inner:before {
      position: relative;
      top: -1px;
    }
    .ivu-modal-body {
      padding-bottom: 0;
      .list-table .list-table-filter {
        padding-top: 0;
        padding-bottom: 14px;
      }
      .ivu-table-row {
        cursor: pointer;
      }
      .ivu-table:after {
        display: none;
      }
      .ivu-table:before {
        display: none;
      }
      .ivu-table-wrapper {
        border: 1px solid #e8e8e8;
      }
      .ivu-table-header thead tr th {
        height: 42px;
        padding: 0;
      }
      .ivu-table .ivu-table-row:last-child td {
        border-bottom: none;
      }
      .list-table .ivu-page {
        padding: 12px 0 14px;
      }
    }
  }
</style>
<style lang="less" scoped>
</style>
