<!--
 * @Date: 2022-06-21 09:55:44
 * @LastEditors: hgj
 * @LastEditTime: 2022-11-23 15:56:20
 * @FilePath: /sdpbase-pro/src/components/order-price-filter/src/main.vue
-->
<template>
  <Row type="flex" align="middle">
    <Col class="flex-con">
      <NumberInput @on-change="handleChange" v-model="minPrice" v-bind="$attrs"/>
    </Col>
    <Col align="center" class="divider">—</Col>
    <Col class="flex-con">
      <NumberInput @on-change="handleChange" v-model="maxPrice" v-bind="$attrs"/>
    </Col>
  </Row>
</template>

<script>
import { Row, Col } from 'view-design';
import NumberInput from '@components/basic/NumberInput';
export default {
  components: {
    Row,
    Col,
    NumberInput
  },
  props: {
    value: {
      type: Array,
      default: () => ['', '']
    }
  },
  data() {
    return {
      priceValue: this.value,
      minPrice: '',
      maxPrice: ''
    };
  },
  watch: {
    value(newValue) {
      if (Array.isArray(newValue)) {
        this.minPrice = newValue[0] || '';
        this.maxPrice = newValue[1] || '';
      } else {
        this.minPrice = '';
        this.maxPrice = '';
      }
    }
  },
  computed: {},
  methods: {
    handleChange() {
      this.$emit('on-change', [this.minPrice, this.maxPrice]);
    },
    resetValue(e) {
      this.minPrice = e.defaultValue[0]
      this.maxPrice = e.defaultValue[1]
    }
  },
  created() {},
  mounted() {}
};
</script>
<style lang="less" scoped>
.divider {
  width: 30px;
}
.flex-con {
  flex: 1;
}
</style>
