<template>
  <div class="print__template-choose">
    <Modal v-model="show" :title="title" :width="width">
      <div class="print__template-choose__content">
        <RadioGroup v-model="currentTemplate">
          <!-- 不知道哪个测试把自定义选择模板名字搞得这么长UI还要对齐，只能用tooltip -->
          <Tooltip v-for="(template, index) in templateData" :key="template.id"  :content="template.name" :max-width="190">
            <Radio
            class="print__template-choose__item fz13"
            :key="index"
            :label="template.id"
            ><SIcon
              class="icon--new"
              icon="xin"
              v-if="isNewTemplate(template)"
            />
            <span class="labelText">{{ template.name }}</span>
            </Radio
          >
          </Tooltip>
        </RadioGroup>
      </div>
      <Row
        v-if="show"
        slot="footer"
        type="flex"
        justify="end"
        :gutter="10"
        align="middle"
      >
        <Col><Button @click="closeModal">取消</Button></Col>
        <Col
          ><Button
            @click="_onPrint"
            type="primary"
            >打印</Button
          ></Col
        >
      </Row>
    </Modal>
  </div>
</template>

<script>
import settings from '@api/settings';
import { RadioGroup, Radio, Row, Col } from 'view-design';
import Button from '../../button';
import printMixin from '@/mixins/orderPrint';
import SIcon from '@components/icon';
import { httpToHttps } from '@/util/common'
export default {
  mixins: [printMixin],
  components: {
    RadioGroup,
    Radio,
    Row,
    Col,
    Button,
    SIcon
  },
  props: {
    title: {
      type: String,
      default: '打印模版选择'
    },
    width: {
      type: Number,
      default: 800
    },
    labelWidth: {
      type: Number
    },
  },
  data() {
    return {
      show: false,
      currentTemplate: '',
      templateData: [],
      printClass: '',
      printAttrs: {},
      params: {},
      callback: null,
      getPrintDataCallBack: null,
      recordPrintTimes: false
    };
  },
  methods: {
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    /**
     * 执行打印
     */
    _onPrint() {
      this.$emit('printTimesChange', this.params)
      this._printOrder({
        id: this.printAttrs.order_id,
        template_id: this.currentTemplate,
        type: this.params.order_type,
        is_preview: this.params.is_preview,
        record_print_times: this.recordPrintTimes,
        is_merge: this.params.is_merge !== undefined ? this.params.is_merge : 1,
        getPrintDataCallBack: this.getPrintDataCallBack,
        requestUrl: this.requestUrl
      });
    },
    open({ printClass, printAttrs, onOk, getPrintDataCallBack, params, recordPrintTimes = true, requestUrl }) {
      this.requestUrl = requestUrl;
      if (this.isShowHttp2HttpsHint) {
        httpToHttps(this)
        return
      }
      this.printClass = printClass || '';
      this.printAttrs = printAttrs || {};
      this.callback = onOk || null;
      this.getPrintDataCallBack = getPrintDataCallBack || null
      this.recordPrintTimes = recordPrintTimes;
      this.params = params || {};
      this.fetchTemplateData();
      this.openModal();
    },
    handlePrint() {
      if (this.callback && typeof this.callback === 'function') {
        this.callback({
          temaplte_id: this.currentTemplate
        });
      }
    },
    openModal() {
      this.show = true;
    },
    closeModal() {
      this.show = false;
    },
    async fetchTemplateData() {
      let res = await this.$request.get(this.apiUrl.getPrintTemplate, {
        type: this.params.type
      });
      let { list } = res.data;
      if (list && list.length > 0) {
        this.templateData = list;
        // 获取用户选中的模板
        if (this.params.user_id) {
          let userDetailRes = await this.$request.get(
            this.apiUrl.getUserDetail,
            {
              user_id: this.params.user_id
            }
          );
          this.currentTemplate = (userDetailRes.data || {}).prt_tpl_id;
        }
        let find = this.templateData.find(item => {
          return item.id === this.currentTemplate;
        });
        // 如果没有用户选中的模板，取配置里面的默认模板
        if (!find) {
          let tplSelRes = await this.$request.get(this.apiUrl.getTplSel);
          if (tplSelRes.status) {
            let list = (tplSelRes.data || {}).list || [];
            list.map(item => {
              if (item.type === 'ORDER' && item.is_selected) {
                this.currentTemplate = item.id;
              }
            });
          }
        }
        find = this.templateData.find(item => {
          return item.id === this.currentTemplate;
        });
        // 如果再没找到，则默认取第一个模板
        if (!find) {
          this.currentTemplate = list[0].id;
        }
      } else {
        this.list = [];
        this.currentTemplate = '';
      }
    }
  },
  created() {}
};
</script>
<style lang="less" scoped>
.mpr12 {
  // vertical-align: top;
  font-weight: 500;
}
.fz13 {
  font-size: 13px;
}
/deep/ .ivu-modal .ivu-modal-body {
  padding: 20px 0px 30px 20px;
}
.print {
  &__template-choose {
    &__item {
      margin-right: 50px;
      width: 200px;
      // display: inline-flex;
      .labelText {
        width: 100%;
        vertical-align: middle;
        display: inline-block;
        white-space: nowrap; 
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
.icon--new {
  font-size: 16px;
  margin-right: 5px;
  color: #03AC54;
  vertical-align: text-bottom;
}
</style>
