<!--
 * @Description: 
 * @Date: 2023-03-07 18:06:40
 * @LastEditors: hgj
 * @LastEditTime: 2023-04-03 17:49:13
 * @FilePath: /sdpbase-pro/src/pages/newRecipe/list/components/intro.vue
-->
<template>
  <div>
    <div @click="show">
      <slot></slot>
    </div>
    <div v-show="showIntroGuide" class="novice-guide" @click="hide">
      <div
        @click.stop
        class="s-intro right-top"
        :style="{
          top: top + 'px',
          display: showIntroGuide ? 'block' : 'none',
          left: left + 'px',
        }"
      >
        <div class="s-intro__wrap">
          <div
            class="s-intro__item"
            v-for="(stepItem, index) in stepInfo"
            :key="index"
          >
            <div class="s-intro__hd">
              <h3 style="font-size: 18px">{{ stepItem.title }}：</h3>
              <s-icon
                icon="close"
                v-if="index === 0"
                style="cursor: pointer"
                :size="14"
                @click="hide"
              />
            </div>
            <div class="s-intro__bd">
              <Steps status="process">
                <Step
                  :title="step.title"
                  v-for="(step, stepIndex) in stepItem.steps"
                  :key="stepIndex"
                >
                  <div slot="content">
                    <template v-if="!Array.isArray(step.desc)">
                      <p>{{ step.desc }}</p>
                    </template>
                    <template v-else>
                      <ul>
                        <li
                          class="s-intro__desc-item"
                          v-for="(desc, descIndex) in step.desc"
                          :key="descIndex"
                        >
                          {{ desc }}
                        </li>
                      </ul>
                    </template>
                    <p
                      class="btn"
                      v-if="step.redirect"
                      @click="redirect(step.redirect)"
                    >
                      点击前往
                    </p>
                  </div>
                </Step>
              </Steps>
            </div>
          </div>
          <Checkbox
            style="margin-top: -6px"
            v-model="defaultHide"
            @on-change="defaultShowOnChange"
            >默认不展示</Checkbox
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'intro',
  props: {
    stepInfo: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      showIntroGuide: false,
      top: 0,
      left: 0,
      defaultHide:
        window.localStorage.getItem(this.$route.path + 'showIntroGuide') == '1'
          ? true
          : false,
    };
  },
  mounted() {
    setTimeout(() => {
      this._initNoviceGuide();
    }, 800);
  },
  methods: {
    hide() {
      this.showIntroGuide = false;
    },
    show() {
      this.showIntroGuide = true;
    },
    defaultShowOnChange() {
      if (this.defaultHide) {
        window.localStorage.setItem(this.$route.path + 'showIntroGuide', '1');
      } else {
        window.localStorage.setItem(this.$route.path + 'showIntroGuide', '0');
      }
    },
    _initNoviceGuide() {
      const initShow =
        window.localStorage.getItem(this.$route.path + 'showIntroGuide') == '1'
          ? true
          : false;

      let introElement = null;
      if (this.$slots.default && this.$slots.default.length > 0) {
        introElement = this.$slots.default[0].elm;
      }

      if (introElement && introElement.offsetTop && introElement.offsetLeft) {
        this.top = introElement.offsetTop - 50;
        const introElementRect = introElement.getBoundingClientRect();
        console.log({ introElementRect });
        this.left = introElementRect.left - 1000 - 10;
        if (initShow) return;
        this.showIntroGuide = true;
      }
    },
    redirect(path) {
      if (typeof path === 'function') {
        path();
        return;
      }
      let routeUrl = this.$router.resolve({
        path: path,
      });
      window.open(routeUrl.href, '_blank');
    },
  },
};
</script>

<style lang="less" scoped>
.novice-guide {
  position: fixed;
  background-color: rgba(0, 0, 0, 0.4);
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  .s-intro__wrap {
    padding: 26px 32px 20px;
  }
  .s-intro {
    width: 1000px;
    background: #fff !important;
    color: rgba(0, 0, 0, 0.75) !important;
    &__item:not(:last-child) {
      position: relative;
      margin-bottom: 16px;
    }
    &__desc-item::before {
      content: '●';
      position: absolute;
      left: -10px;
    }
    .s-intro__hd {
      margin-bottom: 16px !important;
    }
  }
  .s-intro.right-top::before {
    border-color: transparent #fff transparent transparent !important;
  }
  .s-intro__ft {
    justify-content: flex-end;
  }
  .s-intro.right-top:before {
    top: 50px;
    left: 1000px;
    transform: rotate(180deg);
  }
  /deep/.ivu-steps {
    .ivu-steps-item {
      .btn {
        color: #03ac54;
        cursor: pointer;
        margin-top: 6px;
        font-weight: bold;
      }
      .ivu-steps-tail > i {
        background-color: #03ac54;
      }
    }

    .ivu-steps-head-inner {
      border-color: #03ac54;
      background-color: #03ac54;
      span {
        color: #fff;
      }
    }
    .ivu-steps-title {
      color: #03ac54;
      padding-top: 1px;
    }
    .ivu-steps-head-inner {
      font-weight: 500;
    }
    .ivu-steps-content {
      color: rgba(0, 0, 0, 0.75);
      font-size: 13px;
      p {
        padding-right: 16px;
      }
    }
  }
}
/deep/ .ivu-steps-item:last-child {
  flex: 1;
}
/deep/ .ivu-steps-item {
  padding-right: 28px;
  &:last-child {
    padding-right: 0;
  }
}
/deep/ .ivu-steps-content {
  margin-top: 6px;
}
</style>
