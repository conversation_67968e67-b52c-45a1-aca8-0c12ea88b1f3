<!--
 * @Descripttion:
 * @Autor: Jeff
 * @Date: 2020-11-17 12:06:37
 * @LastEditTime: 2023-03-16 17:20:02
 * @FilePath: \sdpbase-pro\src\components\CheckboxGroup\src\main.vue
-->
<template>
  <CheckboxGroup
    v-model="selected"
    @on-change="checkboxGroupChange"
    class="sdpCheckboxGroup"
  >
    <Checkbox
      v-for="(item, index) in data"
      :label="item.id"
      :key="index"
      :disabled="disabled || item.disabled"
    >
      <span class="sdp-no-replace-text">{{ item.name }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>

<script>
import { Checkbox, CheckboxGroup } from 'view-design';

export default {
  name: 'check_box_group',
  components: {
    Checkbox,
    CheckboxGroup,
  },
  props: {
    value: {
      default: () => {
        return [];
      },
    },
    defaultValue: {
      default() {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
    },
    // 数据准备好后调用的回调函数，有特殊场景需要【报表-商品销量-按商品统计-高级筛选】
    dataReady: {
      type: Function,
      default: undefined,
    },
    api: String,
  },
  data() {
    return {
      data: [],
      selected: [],
    };
  },
  inject: ['handleUpdateFilterCol'],
  watch: {
    value: {
      handler(val) {
        const value = val || [];
        this.selected = Array.isArray(value) ? value : value.split(',');
      },
      // deep: true,
      immediate: true,
    },
  },
  computed: {},
  methods: {
    getValue() {
      return this.selected;
    },
    resetValue(e) {
      this.selected = e.defaultValue || [];
    },
    checkboxGroupChange() {
      const labels = [];
      this.data.forEach((item) => {
        if (this.selected.includes(item.id)) {
          labels.push(item.name);
        }
      });
      this.$emit(
        'on-change',
        this.selected.join(',') || '',
        labels.join(',') || '',
      );
    },
    findSelectedItem() {
      let result = [];
      this.selected.map((label) => {
        let find = this.data.find((item) => {
          return item.label === label;
        });
        if (find) {
          result.push(find);
        }
      });
      return result;
    },
    render() {
      this.$request.get(this.api || this.apiUrl.getGoodsTag).then((res) => {
        if (res.status && res.data && res.data.length) {
          this.data = res.data;
          this.dataReady && this.dataReady(res.data);
        } else {
          // 没有数据时, 不展示
          // 传递信息给上层父组件
          this.handleUpdateFilterCol &&
            this.handleUpdateFilterCol({
              key: this.name,
              attr: 'show',
              value: false,
            });
        }
      });
    },
  },
  created() {
    if (this.defaultValue && this.defaultValue.length) {
      this.selected = JSON.parse(JSON.stringify(this.defaultValue));
    }
  },
  mounted() {
    this.render();
  },
};
</script>
<style lang="less">
.sdpCheckboxGroup {
  label {
    height: 30px;
    line-height: 30px;
  }
}
</style>
