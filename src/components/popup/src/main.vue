<!--
 * @Description: 
 * @Autor: lizi
 * @Date: 2020-08-27 10:12:06
 * @LastEditors: lizi
 * @LastEditTime: 2021-03-31 15:45:12
 * @FilePath: \sdpbase-pro\src\components\popup\src\main.vue
-->
<template>
  <div class="sui-popup" @click="stop">
    <slot></slot>
  </div>
</template>

<script>
// @ is an alias to /src

export default {
  name: 'sui-popup',
  props: {},
  data() {
    return {};
  },
  mounted() {
    this.event  = this.close.bind(this)
    document.addEventListener('click', this.event);
  },
  watch: {},
  computed: {},
  methods: {
    stop(event) {
      event.stopPropagation();
    },
    close(event) {
      this.$emit('close', event);
    }
  },
  beforeDestroy() {
    let soltChilren = this.$slots.default;
    soltChilren.map((item,index)=>{
      soltChilren[index] = undefined;
    })
    document.removeEventListener('click', this.event);
  }
};
</script>
