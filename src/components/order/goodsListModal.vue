<template>
  <div>
    <slot
      ><SButton styleType="btnStyleForAdd" @click="openModel"
        >批量添加</SButton
      ></slot
    >
    <Modal
      v-model="showModal"
      title="请选择商品"
      :mask-closable="false"
      :scrollable="scrollable"
      class-name="vertical-center-modal goodDialog"
      width="1200"
      @on-cancel="hideModal"
      v-bind="modalProps"
    >
      <Row class="selectRow mb10" type="flex" justify="start" :gutter="10">
        <slot name="beforefilter"></slot>
        <Col span="4"
          v-if="filterPurchaseType"
          ><PurchaseType
            v-if="showModal"
            :disabled="!!splitProviderId"
            ref="purchaseTypeRef"
            :mode="['agent', 'direct_provider']"
            :value="purchaseTypeValue"
            @on-change="handlePurchaseTypeChange"
        /></Col>
        <Col span="4">
          <Cascader
            ref="cascaderRef"
            :data="categoryList"
            change-on-select
            clearable
            v-model="categoryData"
            placeholder="全部分类"
            @on-change="changeCategory"
          ></Cascader>
        </Col>
        <Col span="3" v-if="showAdded">
          <Select
            v-model="filtersParams.commodity_type"
            placeholder="请选择"
            @on-change="loadGoodsList(1)"
          >
            <Option value="" label="全部"></Option>
            <Option value="1" label="已添加"></Option>
            <Option value="2" label="未添加"></Option>
          </Select>
        </Col>
        <Col span="3" v-if="filterIsOnline">
          <Select
            v-model="filtersParams.is_online"
            placeholder="上下架状态"
            @on-change="loadGoodsList(1)"
          >
            <Option value="0" label="全部"></Option>
            <Option value="Y" label="上架"></Option>
            <Option value="N" label="下架"></Option>
          </Select>
        </Col>
        <Col span="3" v-if="filterTag">
          <Select
            v-model="selectedTag"
            multiple
            placeholder="商品标签"
            @on-change="loadGoodsList(1)"
          >
            <Option
              :key="tag.id"
              :value="tag.name"
              :label="tag.name"
              v-for="tag in tagList"
            ></Option>
          </Select>
        </Col>
        <Col span="4" v-if="showAreaLocation">
          <store-and-location-select
            clearable
            :storeId="storeId"
            :filterable="false"
            :changeOnSelect="false"
            :formatData="formatAreaLocationData"
            v-model="areaLocation"
          />
        </Col>
        <Col span="5">
          <Input
            placeholder="请输入商品名称/编码进行搜索"
            v-model="filtersParams.no"
            @on-enter="handleSearchGoodsList(1)"
            clearable
          >
            <Button
              slot="append"
              icon="ios-search"
              @click="loadGoodsList(1)"
            ></Button>
          </Input>
        </Col>
        <slot name="afterfilter"></slot>
      </Row>
      <Row type="flex" justify="start" :gutter="10">
        <Col>
          <p class="_tips">小贴士：鼠标单击表格行即可直接选中或修改</p>
        </Col>
        <Col style="flex: 1">
          <slot name="extra"></slot>
        </Col>
        <Col v-if="modalType === 'storeGoods'">
          <Checkbox
            :true-value="1"
            :false-value="0"
            v-model="storeGoodsFilters.is_show_zero"
            @on-change="loadGoodsList(1)"
            >显示库存为"0"的商品</Checkbox
          >
        </Col>
        <Col v-if="modalType !== 'storeGoods' && showUsed">
          <Checkbox v-model="showCollectionOnly" @on-change="loadGoodsList(1)"
            >只显示常用商品</Checkbox
          >
        </Col>
      </Row>
      <Table
        :columns="cols"
        :data="goodsList"
        ref="goodsTable"
        :loading="loading"
        row-key="_rowKey"
        height="400"
        border
        @on-row-click="selectGoods"
        :row-class-name="rowClassName"
        @on-selection-change="handleSelectionChange"
      ></Table>
      <Row justify="end" style="height: 54px; align-items: center">
        <Col>
          <Page
            class="list-table__pagination-wrap"
            :total="totalPage"
            :current="currentPage"
            :page-size="pageSize"
            :page-size-opts="pageSizeOpts"
            @on-change="changePage"
            @on-page-size-change="changePageSize"
            placement="top"
            show-elevator
            show-total
            show-sizer
          >
          </Page>
        </Col>
      </Row>
      <div slot="footer">
        <Button @click="hideModal">取 消</Button>
        <Button type="primary" :loading="submitLoading" @click="addConfirm"
          >确 认</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
import { debounce, uniqueId } from 'lodash-es';
import PurchaseType from '@components/purchase-type';
import goods from '@api/goods.js';
import { get, post } from '@/api/request.js';
import ConfigMixin from '@/mixins/config';
import SButton from '@/components/button';
import columnsMixins from './mixins/columns.js';
import StoreAndLocationSelect from '@/components/common/storeAndLocationSelect.vue';

export default {
  name: 'goods-list-modal',
  components: {
    PurchaseType,
    SButton,
    StoreAndLocationSelect,
  },
  mixins: [columnsMixins, ConfigMixin],
  props: {
    modalProps: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '/superAdmin/commoditySuper/ajaxList',
      // /superAdmin/Warehouse/ajaxStoreList
    },
    customer: {
      type: String,
      default: '',
    },
    uid: {
      type: [Number, String],
      default: null,
    },
    storeId: {
      type: [Number, String],
      default: null,
    },
    isShowStockStoreId: {
      type: [Number, String],
      default: null,
    },
    noInput: {
      type: Boolean,
      default: false,
    },
    selectedGoods: {
      type: Array,
      default: () => [],
    },
    disableSelect: {
      type: Boolean,
      default: true,
    },
    // 是否支持跨页选择
    multiPageSelect: {
      type: Boolean,
      default: false,
    },
    // 选中商品是否禁用
    disabledGoods: {
      type: Array,
      default: () => [],
    },
    showUsed: {
      type: Boolean,
      default: true,
    },
    modalType: {
      type: String,
      default: 'newGoods',
    },
    // 自定义column
    columns: {
      type: Array,
      default: () => [],
    },
    types: {
      type: [Number, String],
      default: null,
    },
    params: {
      type: Object,
      default: () => {},
    },
    filterIsOnline: {
      type: Boolean,
      default: false,
    },
    filterPurchaseType: {
      type: Boolean,
      default: true,
    },
    filterTag: {
      type: Boolean,
      default: true,
    },
    splitProviderId: {
      type: String,
      default: '',
    },
    addCols: {
      type: Array,
      default: () => [],
    },
    pageSizeOpts: {
      type: Array,
      default: () => [10, 20, 30, 40],
    },
    showAdded: {
      type: Boolean,
      default: false,
    },
    showAreaLocation: {
      type: Boolean,
      default: false,
    },
    // 是否可添加重复商品，与配置无关。
    isSameCommodity: {
      type: Boolean,
      default: true,
    },
    // 是否显示价格
    isShowPrice: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      selectedTag: [],
      areaLocation: [],
      tagList: [],
      categoryData: [],
      showModal: false,
      overflowStyle: '',
      scrollable: false,
      filtersParams: {
        no: '',
        category1: '',
        category2: '',
        category_id3: '',
        AreaId: '',
        channel_type: '',
        provider_id: '',
        agent_id: '',
        is_online: '',
        commodity_type: '',
      },
      storeGoodsFilters: {
        is_show_zero: 1,
      },
      showCollectionOnly: false,
      totalPage: 0,
      currentPage: 1,
      pageSize: 10,
      cols: [],
      goodsList: [],
      categoryList: [],
      selectedRow: [],
      loading: false,
      newOrders: [],
      isFocus: false,
      addedNum: 0,
      purchaseTypeValue: [],
      submitLoading: false,
    };
  },
  created() {
    let el = document.querySelector('body');
    this.overflowStyle = el.style['overflow-y'];
    const convertToChildren = (treeData) => {
      if (Array.isArray(treeData)) {
        return treeData.map(convertToChildren);
      }

      if (typeof treeData === 'object' && treeData !== null) {
        const children = treeData.items
          ? convertToChildren(treeData.items)
          : [];
        return {
          ...treeData,
          value: treeData.id,
          label: treeData.name,
          children: children.map(convertToChildren),
        };
      }

      return treeData;
    };
    goods.getGoodsCategoryTree().then((res) => {
      if (res.status) {
        if (this.isOpenCommodityCategoryThree) {
          res.data.forEach((item) => {
            if (item.items && item.items.length > 0) {
              item.items.forEach((item2) => {
                item2.items = item2.items || [];
                item2.items.unshift({
                  id: '0',
                  name: '未设置',
                  level: '3',
                });
              });
            }
          });
        }
        this.categoryList = convertToChildren(res.data);
        this.categoryList.unshift({ value: 0, label: '全部分类' });
      }
    });
    this.cols = this.getCols();
    if (this.filterIsOnline) {
      this.cols.splice(
        Math.max(this.cols.findIndex((item) => item.title === '价格') + 1, 3),
        0,
        {
          title: '商品状态',
          key: 'is_online_text',
        },
      );
    }
    if (this.splitProviderId) {
      this.purchaseTypeValue = ['2', this.splitProviderId];
    }
    this.getTagList();
  },
  watch: {
    value(value) {
      this.showModal = value;
    },
    showModal(val) {
      this.$emit('input', val);
      if (val) {
        if (this.multiPageSelect) {
          this.newOrders = [...this.selectedGoods];
        }
        this.resetFilters();
        this.loadGoodsList();
        this.scrollable = false;
      } else {
        // 取消选中的状态
        this.$refs.goodsTable.selectAll(false);
        this.scrollable = true;
        this.$nextTick(() => {
          let el = document.querySelector('body');
          el.style['overflow-y'] = this.overflowStyle
            ? this.overflowStyle
            : 'auto';
          console.log('overflow', el.style['overflow-y']);
        });
      }
    },
  },
  methods: {
    getTagList() {
      get('/superAdmin/commoditySuper/getTag').then((res) => {
        if (res.status) {
          this.tagList = res.data;
        }
      });
    },
    closeModel() {
      this.showModal = false;
    },
    openModel() {
      this.showModal = true;
    },
    resetFilters() {
      this.filtersParams = {
        no: '',
        category1: '',
        category2: '',
        category_id3: '',
        AreaId: '',
        channel_type: '',
        provider_id: '',
        agent_id: '',
        is_online: '',
      };
      if (this.splitProviderId) {
        this.filtersParams.channel_type = '2';
        this.filtersParams.provider_id = this.splitProviderId;
      } else {
        this.purchaseTypeValue = [];
      }
      this.currentPage = 1;
      this.categoryData = [];
    },
    getCols() {
      if (this.columns && this.columns.length > 0) return this.columns;
      let targetCols;
      switch (this.modalType) {
        case 'newGoods':
          targetCols = this.columns1;
          break;
        case 'shieldGoods':
          this.columns2.forEach((item, index) => {
            if (item.key === 'unit' && this.types == 1) {
              this.columns2.splice(index, 1);
            }
          });
          targetCols = this.columns2;
          break;
        case 'issueOrder':
          targetCols = this.columns5;
          break;
        case 'storeGoods':
          if (this.noInput) {
            targetCols = this.columns4;
          } else {
            targetCols = this.columns3;
          }
          break;
      }
      if (this.addCols.length && targetCols) {
        this.handleAddCols(this.addCols, targetCols);
      }
      return targetCols.filter(col => col.isShow !== false);;
    },
    handlePurchaseTypeChange: debounce(function (value, info, filtersParams) {
      this.filtersParams.channel_type = filtersParams.type || '';
      this.filtersParams.agent_id = filtersParams.agent_id;
      this.filtersParams.provider_id = filtersParams.provider_id;
      this.loadGoodsList();
    }, 1000),
    isGoodsSelected(goods) {
      return this.newOrders.some((item) => item.id === goods.id || item.commodity_id === goods.id);
    },
    //全选取消状态处理
    handleSelectionChange(selection) {
      if (this.addedNum === selection.length) {
        this.selectedRow.forEach((item) => {
          if (this.newOrders.length) {
            this.newOrders.forEach((_item, _index) => {
              if (item.id === _item.id && !item._disabled) {
                this.newOrders.splice(_index, 1);
              }
            });
          }
        });
      }
      // 选中的数据处理
      selection.forEach(item => {
        if (!this.isGoodsSelected(item)) {
          const selectedItem = this.selectedRow.find(i => i.id === item.id || i.commodity_id === item.id);
          this.newOrders.push(selectedItem);          
        }
      });
      // 当前页没有选中的商品从newOrders中删除
      this.goodsList.forEach((item) => {
        if (!selection.some((i) => i.id === item.id || i.commodity_id === item.id)) {
          this.newOrders = this.newOrders.filter((i) => i.id !== item.id && i.commodity_id !== item.id);
        }
      });
      const unselectedGoods = this.goodsList.filter((item) => {
        return !selection.some((i) => i.id === item.id || i.commodity_id === item.id);
      });
      // newOrders中删除当前页没有选中的商品
      this.newOrders = this.newOrders.filter((item) => {
        return !unselectedGoods.some((i) => i.id === item.id || i.commodity_id === item.id);
      });
    },
    // 获取已经添加的商品在当页所占的数量
    getAddedNum() {
      let num = 0;
      this.goodsList.forEach((item) => {
        if (item._checked) {
          num += 1;
        }
      });
      return num;
    },
    //获取行所在的索引
    getIndex(row) {
      let idx = '';
      this.goodsList.forEach((item, index) => {
        if (item.id === row.id) {
          idx = index;
        }
      });
      return idx;
    },
    // 单击行状态处理
    selectGoods(row, index) {
      if (row._disabled) return;
      let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
      let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
      let checked = selectRow.querySelector('.ivu-checkbox-checked');
      if (checked && this.isFocus) {
        this.isFocus = false;
        return;
      }
      selectRow.querySelector('.ivu-checkbox-wrapper').className = checked
        ? 'ivu-checkbox-wrapper'
        : 'ivu-checkbox-wrapper ivu-checkbox-wrapper-checked';
      selectRow.querySelector('.ivu-checkbox').className = checked
        ? 'ivu-checkbox'
        : 'ivu-checkbox ivu-checkbox-checked';
      if (
        this.modalType === 'newGoods' ||
        (this.modalType === 'storeGoods' && !this.noInput)
      ) {
        selectRow.querySelector('.ivu-input-number-input') &&
          selectRow.querySelector('.ivu-input-number-input').focus();
        selectRow.querySelector('.ivu-input-number-input') &&
          selectRow.querySelector('.ivu-input-number-input').select();
      }
      if (!checked) {
        this.newOrders.push(this.selectedRow[index]);
      } else {
        if (this.newOrders.length) {
          this.newOrders.forEach((item, index) => {
            if (item.id === row.id) {
              this.newOrders.splice(index, 1);
            }
          });
        }
      }
    },
    rowClassName(row) {
      if (row._disabled) {
        return 'table-close-row';
      }
      return 'normalRow';
    },
    hideModal() {
      this.showModal = false;
      this.scrollable = true;
      this.newOrders = [];
      this.$emit('on-cancel');
    },
    changePage(pageNo) {
      this.currentPage = pageNo;
      this.loadGoodsList();
    },
    changePageSize(size) {
      this.currentPage = 1;
      this.pageSize = size;
      this.loadGoodsList();
    },
    changeCategory(value) {
      this.currentPage = 1;
      this.filtersParams.category1 = value[0];
      this.filtersParams.category2 = value[1];
      this.filtersParams.category_id3 = value[2];
      this.loadGoodsList();
    },
    // loadSubCategoryData(item, callback) {
    //   item.loading = true;
    //   goods.getGoodsCategory(item.value).then((res) => {
    //     if (res.status) {
    //       item.children = res.data.map((_item) => {
    //         return {
    //           value: _item.id,
    //           label: _item.name,
    //         };
    //       });
    //       item.loading = false;
    //       callback();
    //     }
    //   });
    // },
    getFilters() {
      return {
        searchValue: this.filtersParams.no, //搜索关键词
        category_id: this.filtersParams.category1, //一级分类id
        category_id2: this.filtersParams.category2, //二级分类id
        category_id3: this.filtersParams.category_id3, // 三级分类id
        provider_id: this.filtersParams.provider_id, // 供应商id
        agent_id: this.filtersParams.agent_id, // 采购员id
      };
    },
    getFiltersAll() {
      let params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        // area_id: this.filtersParams.AreaId,
        store_id: this.storeId ? this.storeId : '',
        searchValue: this.filtersParams.no, //搜索关键词
        category_id: this.filtersParams.category1, //一级分类id
        category_id2: this.filtersParams.category2, //二级分类id
        category_id3: this.filtersParams.category_id3, // 三级分类id
        showAll: true,
        is_show_stock: this.modalType === 'storeGoods' ? 1 : 0,
        is_show_stock_store_id: this.isShowStockStoreId
          ? this.isShowStockStoreId
          : '',
        is_collection: this.showCollectionOnly ? 1 : 0,
        channel_type: this.filtersParams.channel_type,
        provider_id: this.filtersParams.provider_id,
        agent_id: this.filtersParams.agent_id,
        is_online: this.filtersParams.is_online,
      };
      if (this.uid && this.customer !== 'userType') {
        params.user_id = this.uid;
      }
      if (this.modalType === 'newGoods') {
        params.is_online = 'Y';
      } else if (this.modalType === 'shieldGoods') {
        params.showAll = false;
        if (this.customer === 'userType') {
          params.rece_style = this.uid;
        }
      }
      return params;
    },
    cancelAllCheckStatus() {
      let selectedItem = this.$refs.goodsTable.$el.querySelectorAll(
        '.ivu-table-tbody .ivu-checkbox-wrapper-checked',
      );
      if (selectedItem) {
        Array.from(selectedItem).map((item) => {
          item.classList.remove('ivu-checkbox-wrapper-checked');
          let checkbox = item.querySelector('.ivu-checkbox-checked');
          if (checkbox) {
            checkbox.classList.remove('ivu-checkbox-checked');
          }
        });
      }
    },
    formatAreaLocationData(data) {
      const defaultData = [
        {
          label: '全部',
          value: 0,
        },
        {
          label: '无库区',
          value: -1,
        },
      ];
      return defaultData.concat(
        data.map((i) => {
          const newChildren = [
            {
              label: '全部',
              value: '',
            },
          ];
          return {
            ...i,
            children: newChildren.concat(i.children),
          };
        }),
      );
    },

    handleSearchGoodsList: debounce(function (value) {
      this.loadGoodsList(value);
    }, 100),

    async loadGoodsList(page) {
      // this.cancelAllCheckStatus();
      this.loading = true;
      console.log({ areaLocation: this.areaLocation });
      const selectedGoods = this.multiPageSelect ? this.newOrders : this.selectedGoods;
      let params = {
        page: this.currentPage,
        pageSize: this.pageSize,
        // area_id: this.filtersParams.AreaId,
        store_id: this.storeId ? this.storeId : '',
        searchValue: this.filtersParams.no, //搜索关键词
        category_id: this.filtersParams.category1, //一级分类id
        category_id2: this.filtersParams.category2, //二级分类id
        category_id3: this.filtersParams.category_id3, // 三级分类id
        showAll: true,
        is_show_stock: this.modalType === 'storeGoods' ? 1 : 0,
        is_show_stock_store_id: this.isShowStockStoreId
          ? this.isShowStockStoreId
          : '',
        is_collection: this.showCollectionOnly ? 1 : 0,
        channel_type: this.filtersParams.channel_type,
        provider_id: this.filtersParams.provider_id,
        agent_id: this.filtersParams.agent_id,
        is_online: this.filtersParams.is_online,
        commodity_type: this.filtersParams.commodity_type,
        goods_tag_names: this.selectedTag.join(','),
      };
      if (this.showAreaLocation) {
        params.area_id =
          this.areaLocation && this.areaLocation.length
            ? this.areaLocation[0]
            : '';
        params.location_id =
          this.areaLocation && this.areaLocation.length > 1
            ? this.areaLocation[1]
            : '';
      }
      if (this.showAdded) {
        params.commodity_string = JSON.stringify(
          (selectedGoods || []).map((i) => i.id),
        );
      }
      if (this.uid && this.customer !== 'userType') {
        params.user_id = this.uid;
      }
      if (this.modalType === 'newGoods') {
        params.is_online = 'Y';
      } else if (this.modalType === 'shieldGoods') {
        params.showAll = false;
        if (this.customer === 'userType') {
          params.rece_style = this.uid;
        }
      }
      if (page !== undefined && !isNaN(page)) {
        params.page = page;
      }
      let res = '';
      params = {
        ...params,
        ...this.params,
      };
      let url = this.url;
      if (this.modalType === 'storeGoods') {
        params = {
          ...params,
          ...this.storeGoodsFilters,
        };
        url = '/superAdmin/Warehouse/ajaxStoreList';
      }
      // 设置默认值
      params.page = params.page || 1;
      res = await post(url, params); //根据传入的url调用接口
      if (!res.status) {
        this.modalError(res.message);
        return;
      }
      this.loading = false;
      this.goodsList = [];
      let data = res.data;
      let Goods = data.list.map((item) => {
        item._rowKey = uniqueId('row_');
        // 筛选已经添加的商品
        item.amount = 1;
        item.extra_amount = 1;
        item.agreement_price = item.price;
        item.commodity_alias = '';
        if (
          this.selectedGoods.length > 0 &&
          (!this.isDuplicateCommodity ||
            !this.is_open_order_add_same_commodity ||
            !this.isSameCommodity)
        ) {
          item._checked = false;
          item._disabled = false;
          selectedGoods.forEach((d) => {
            if (item.id === d.id || item.id === d.commodity_id) {
              item._checked = true;
              if (this.disableSelect) {
                item._disabled = true;
              }
              item.amount = d.amount ? d.amount : 0;
              item.agreement_price = d.agreement_price;
              item.commodity_alias = d.commodity_alias ? d.commodity_alias : '';
            }
          });
          item.amount *= 1;
          if (isNaN(item.amount)) {
            item.amount = 1;
          }
        }
        if (this.disabledGoods.length > 0) {
          this.disabledGoods.forEach((d) => {
            if (item.id === d.id || item.id === d.commodity_id) {
              item._disabled = true;
            }
          });
        }
        return item;
      });
      // 已经选择的置顶
      Goods.forEach((item) => {
        // 筛选已经选取的商品
        this.newOrders.forEach((order) => {
          if (item.id === order.id) {
            item._checked = true;
          }
        });
      });
      this.goodsList = Goods;
      this.selectedRow = this.cloneObj(this.goodsList);
      this.currentPage = parseInt(data.pageParams.page);
      this.totalPage = parseInt(data.pageParams.count);
      this.addedNum = this.getAddedNum();
    },
    addConfirm() {
      if (this.newOrders.length) {
        this.submitLoading = true;
        let addGoods = [...this.newOrders];
        if (this.disableSelect) {
          addGoods = addGoods.filter((item) => !item._disabled);
        }
        this.$emit('on-add', addGoods);
        this.newOrders = [];
        this.showModal = false;
        this.scrollable = true;
        setTimeout(() => {
          this.submitLoading = false;
        });
      } else {
        this.modalError('请选择需要添加的商品');
      }
    },
    handleAddCols(cols, target) {
      cols.map(({ addBeforeKey, col }) => {
        let addIndex = target.findIndex((item) => item.key === addBeforeKey);
        if (~addIndex) target.splice(addIndex, 0, col);
      });
    },
  },
};
</script>
<style>
.goodDialog .ivu-modal .ivu-modal-body {
  padding: 20px 24px 0px;
}
</style>
<style scoped>
._tips {
  text-align: center;
  line-height: 30px;
  color: rgba(0, 0, 0, 0.5);
}
</style>
