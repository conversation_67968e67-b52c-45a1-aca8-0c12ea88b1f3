<template>
  <Modal
    @on-cancel="onCancel"
    title="导出订单明细"
    :width="650"
    v-model="showModal">
    <div class="title">选择导出时间</div>
    <Row
      class="margin-bottom"
      :gutter="15"
      align="middle"
      type="flex">
      <Col>
        <Select v-model="selfParams.timeType" style="width:100px">
          <Option value="1" >发货时间</Option>
          <Option value="2" >下单时间</Option>
        </Select>
      </Col>
      <Col>
        <DatePicker
          v-if="showModal"
          style="width: 300px"
          :placeholder="+selfParams.timeType === 1? '选择发货时间': '选择下单时间'"
          :type="+selfParams.timeType === 1 ? 'daterange' : 'datetimerange'"
          :format="+selfParams.timeType === 1 ? 'yyyy-MM-dd' : 'yyyy-MM-dd HH:mm'"
          @on-change="onDateChange"
          :value="defaultDate"></DatePicker>
      </Col>
      <Col v-if="showModal">
      <Provider
        style="width: 100px"
        filterable
        v-model="selfParams.provider_id"
        :transfer="false" />
      </Col>
      <Col>
      <Tooltip
        placement="right">
        <p
          style="white-space: normal; width: 200px"
          slot="content">
          注意：按照供应商筛选导出，不会进行订单的合计统计
        </p>
        <Icon style="cursor: pointer" type="ios-help-circle"></Icon>
      </Tooltip>
      </Col>
    </Row>
    <div class="title">排序方式</div>
    <RadioGroup v-model="selfParams.sort_type">
        <Radio label="order">
            <span>按下单顺序</span>
        </Radio>
        <Radio label="category">
            <span>按商品顺序</span>
        </Radio>
				<Radio label="order_list">
					<span>按列表顺序</span>
				</Radio>
    </RadioGroup>
    <div class="title">选择导出字段</div>
    <CheckboxGroup v-model="selectedColumns">
      <Row type="flex">
        <Col
          :key="value"
          :span="6"
          v-for="(label, value) in allColumns">
        <Checkbox
          :disabled="defaultColumns.includes(value)"
          :label="value">
          {{label}}
        </Checkbox>
        </Col>
      </Row>
    </CheckboxGroup>
    <div
      slot="footer"
      class="btn-list">
      <Button
        @click="onCancel"
        >关闭</Button>
      <Button
        @click="toggleSelectAll"
        >{{allSelected ? '取消全选' : '全选'}}</Button>
      <ExportButton
        text="确认导出"
        type="primary"
        @on-click="confirmExport"
        @on-success="onCancel"
        :offline="true"
        :param-getter="getExportParams"
        :api="apiUrl.exportConditionData"
        style="margin-left: 10px;" />
    </div>
  </Modal>
</template>

<script>
  import Provider from '@components/common/providerSelect'
  const EXPORT_COLUMN_STORAGE_KEY = 'order_export_column';
  import { mapState } from 'vuex';
  import DateUtil from '@/util/date';
  export default {
    name: "ExportExcel",
    components: {
    	Provider
    },
    props: {
    	show: {
    		type: Boolean,
        default: false
      },
      params: {
    		type: Object,
        default: () => {}
      }
    },
    watch: {
    	show (show) {
    		if (show) {
    			this.init();
          this.getFields();
        }
        this.showModal = show;
      },
      sysConfig () {
        this.selfParams.sort_type = +this.sysConfig.is_invoice_print_sort === 1 ? 'category': 'order';
      },
      defaultDate (v) {
        this.onDateChange(v);
      },
    },
    computed: {
      ...mapState({
        sysConfig: "sysConfig",
      }),
    	allSelected () {
    		return this.selectedColumns.length === Object.keys(this.allColumns).length;
      },
      // defaultDate () {
      //   return this.selfParams.timeType == 1 ?
      //   [this.selfParams.startTime, this.selfParams.endTime] :
      //   [this.selfParams.createStartTime, this.selfParams.createEndTime];
      // }
    },
    data() {
      return {
      	showModal: false,
        selfParams: {
      		provider_id: '',
          sort_type: 'order',
          timeType: '1'
        },
        allColumns: [],
        selectedColumns: [],
        defaultColumns: [],
        defaultDate: [],
      }
    },
    created () {
    	this.showModal = this.show;
      this.selfParams.sort_type = +this.sysConfig.is_invoice_print_sort === 0 ? 'order': 'category';
    },
    methods: {
    	init () {
    		this.selfParams = {
          ...{
            provider_id: '',
            sort_type: +this.sysConfig.is_invoice_print_sort === 0 ? 'order': 'category',
            timeType: '1'
          },
    			...this.params
    		};
        if (this.selfParams.startTime) {
            this.selfParams.timeType = '1'
        } else if (this.selfParams.createStartTime) {
          this.selfParams.timeType = '2'
        }
        this.defaultDate = this.selfParams.timeType == 1 ? [this.selfParams.startTime, this.selfParams.endTime] : [this.selfParams.createStartTime, this.selfParams.createEndTime];
        this.selfParams.startTime = ''
        this.selfParams.createStartTime = ''
      },
      confirmExport () {
    		this.storage.setLocalStorage(EXPORT_COLUMN_STORAGE_KEY, this.selectedColumns);
      },
      toggleSelectAll () {
    		if (this.allSelected) {
    			this.selectedColumns = this.defaultColumns;
        } else {
    			this.selectedColumns = Object.keys(this.allColumns);
        }
      },
      onDateChange (date) {
    		this.selfParams.startTime = date[0];
        this.selfParams.endTime = date[1];
      },
      getFields () {
      	this.$request.get(this.apiUrl.getFields, { type: 'order_export' }).then(res => {
      		let { status, data } = res;
      		if (status) {
      			this.allColumns = data.all_column;
            this.selectedColumns = this.storage.getLocalStorage(EXPORT_COLUMN_STORAGE_KEY) || data.selected_column;
            this.defaultColumns = data.default_column;
          } else {
            this.allColumns = [];
            this.selectedColumns = [];
            this.defaultColumns = [];
          }
        });
      },
      getExportParams () {
    		const excludeColumns = Object.keys(this.allColumns).filter(column => !this.selectedColumns.includes(column));
        this.selfParams.hide_columns = JSON.stringify(excludeColumns);
        let selfParams = this.deepClone(this.selfParams);
        if (+selfParams.timeType === 1) {
          if(selfParams.startTime) {
            selfParams.startTime = DateUtil.format(selfParams.startTime, 'YYYY-MM-DD');
          } else {
            selfParams.startTime = ''
          }
          if(selfParams.endTime) {
            selfParams.endTime = DateUtil.format(selfParams.endTime, 'YYYY-MM-DD');
          } else {
            selfParams.endTime = ''
          }
          delete selfParams.createStartTime;
          delete selfParams.createEndTime;
        } else if (+selfParams.timeType === 2) {
          if(selfParams.startTime) {
            selfParams.createStartTime = DateUtil.format(selfParams.startTime, 'YYYY-MM-DD HH:mm');
          } else {
            selfParams.createStartTime = ''
          }
          if(selfParams.endTime) {
            selfParams.createEndTime = DateUtil.format(selfParams.endTime, 'YYYY-MM-DD HH:mm');
          } else {
            selfParams.createEndTime = ''
          }

          delete selfParams.startTime;
          delete selfParams.endTime;
        }
    		return selfParams;
      },
      onCancel () {
    		this.$emit('on-cancel');
      }
    }
  }
</script>

<style lang="less" scoped>
  .title {
    font-size: 14px;
    color: #222127;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .btn-list {
    .ivu-btn {
      margin-left: 10px;
    }
  }
</style>
