<template>
<div id="sdp-history-add-order" style="width: 600px" :class="{isFullscreen: isFullscreen}">
      <p class="sdp-history-add-order-title">
        <span @click="close">
          <Icon
            size="20"
            type="md-close"></Icon>
         </span>
        从历史订单复制新增
      </p>
      <div class="sdp-history-add-order-lists">
        <span class="sdp-history-lists-header">
          <p>日期</p>
          <p>订单</p>
          <p>下单时间</p>
          <p>下单金额</p>
          <p>发货金额</p>
          <p>操作</p>
        </span>
        <ul>
          <li v-for="info in historyList" :key="info.id">
            <div class="sdp-history-lists-item">
              <span>
                <p>{{ info.delivery_date }}</p>
              </span>
              <span>{{ info.order_no }}</span>
              <span>{{ info.create_time }}</span>
              <span>{{ info.order_price }}</span>
              <span>{{ info.delivery_price }}</span>
              <Poptip
                placement="left"
                confirm
                title="确定复制订单中的商品信息？（原来的商品将被清空）"
                style="width: 20%;"
                @on-ok="getHistoryGoodsList(info)">
                <span
                  class="text-green pointer"
                  style="font-size: 13px">复制新增</span>
              </Poptip>
            </div>
          </li>
        </ul>
      </div>
      <Page :total="totalPage" :current='currentPage' :page-size='pageSize'
          style="position: fixed; bottom: 0;"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          placement="top"
          show-total
          show-sizer>
      </Page>
    </div>
</template>

<script>
  import common from '@api/order.js';
  import '@assets/scss/mixin.scss';

  export default {
    name: 'sdp-history-add-order',
    props: ['user', 'goods', 'isFullscreen'],
    data () {
      return {
        totalPage: 0,
        currentPage: 1,
        pageSize: 20,
        historyList: []
      }
    },
    created () {
      this.user.id ? this.getHistoryOrder() : undefined;
    },
    methods: {
      changePage(pageNo) {
        this.currentPage = pageNo;
        this.getHistoryOrder();
      },
      changePageSize(size) {
        this.pageSize = size;
        this.getHistoryOrder();
      },
      getHistoryOrder() {
        let params = {
          id : this.user.id,
          page: this.currentPage,
          pageSize: this.pageSize
        };
        common.getHistoryOrder(params).then((res) => {
          if (res.status) {
            this.historyList = res.data.list;
            this.currentPage = parseInt(res.data.pageParams.page);
            this.totalPage = parseInt(res.data.pageParams.count);
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
      },
      getHistoryGoodsList(value) {
        let orderId = value.id;
        sessionStorage['order_no'] = value.order_no;
        common.getHistoryGoodsList(this.user.id, orderId).then((res) => {
          if (res.status) {
            let data = res.data; 
            data.forEach((ite,index) => {  
              for (const key in ite) {
                if(key !== 'inner_remark') {
                  ite.inner_remark = '' 
                }
              }
            })
            this.$emit('history', data);
          }
        })
      },
      close () {
        this.$emit('close');
      }
    }
  }
</script>

<style lang="scss">
  #sdp-history-add-order {
    position: fixed;
    z-index: 1000;
    top: 0;
    right: 0;
    width: 600px;
/*    height: 100%;*/
    bottom: 0;
    overflow-y: auto;
    font-size: 16px;
    border-left: 1px solid #EEE;
    background-color: #FFF;
    &.isFullscreen {
      bottom: 46px;
      .ivu-page {
        bottom: 56px !important;
      }
    }
  }

  .sdp-history-add-order-title {
    width: 100%;
    padding: 10px;
    text-align: center;
  }

  .sdp-history-add-order-title i {
    float: left;
  }

  .sdp-history-add-order-title i:hover {
    cursor: pointer;
  }

  .sdp-history-add-order-lists {
/*    height: 89%;*/
    height: calc(100% - 26px - 44px - 20px);
    overflow-y: scroll;
    width: 100%;
  }

  .sdp-history-lists-header {
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    width: 100%;
    height: 40px;
    background-color: #f8f8f9;
  }

  .sdp-history-lists-header>p {
    text-align: center;
    font-size: 12px;
    font-weight: 600;
  }

  .sdp-history-lists-header>p:nth-child(1) {
    width: 15%;
  }
  .sdp-history-lists-header>p:nth-child(2) {
    width: 20%;
  }
  .sdp-history-lists-header>p:nth-child(3) {
    width: 15%;
  }
  .sdp-history-lists-header>p:nth-child(4) {
    width: 15%;
  }
  .sdp-history-lists-header>p:nth-child(5) {
    width: 15%;
  }
  .sdp-history-lists-header>p:nth-child(6) {
    width: 20%;
  }

  .sdp-history-lists-item {
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    width: 100%;
    /* height: 40px; */
    padding: 10px 0;
    border-bottom: 1px solid #EEE;
  }

  .sdp-history-lists-item>span {
    text-align: center;
    font-size: 13px;
  }

  .sdp-history-lists-item>span:nth-child(1) {
    width: 15%;
  }
  .sdp-history-lists-item>span:nth-child(2) {
    width: 20%;
  }
  .sdp-history-lists-item>span:nth-child(3) {
    width: 15%;
  }
  .sdp-history-lists-item>span:nth-child(4) {
    width: 15%;
  }
  .sdp-history-lists-item>span:nth-child(5) {
    width: 15%;
  }
  .text-green {
    color: #03ac54;
  }
  .sdp-history-lists-item>span:nth-child(6):hover {
    cursor: pointer;
  }
</style>
