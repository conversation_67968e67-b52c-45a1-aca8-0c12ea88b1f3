<template>
  <div id="sdp-new-order-review" @click.stop="close">
    <div class="sdp-review-table">
      <i-table highlight-row :columns="columns3" :data="newOrderList"></i-table>
    </div>
    <div class="sdp-review-operation">
      <i-button type="error" @click.stop="close">关闭</i-button>
    </div>
    <!-- <i-button type="error" @click="close" class="close-btn">关闭</i-button> -->
  </div>
</template>

<script>
  export default {
    name: 'sdp-new-order-review',
    props: ['newOrderList', 'active', 'amountArr'],
    data () {
      return {
        columns3: [
          {
            type: 'index',
            width: 60,
            align: 'center'
          },
          {
            title: '商品名称',
            key: 'name',
            align: 'center',
          },
          {
            title: '商品单位',
            key: 'unit',
            align: 'center',
          },
          {
            title: '订购数',
            key: 'amount',
            width: 100,
            align: 'center',
            props: {
              amountArr: ''
            },
            render: (h, params) => {
              var data = params.row;
              return h('i-input', {
                'class': {
                  'review-amount': true
                },
                attrs: {
                  value: data.amount,
                  type: 'number'
                },
                nativeOn: {
                  change: () => {
                    let index = data._index,  // 每行商品的索引
                        amount = document.getElementsByClassName('review-amount')[index],
                        value = amount.getElementsByTagName('input')[0].value,
                        arr = [],  // 返回的商品数组
                        obj = {};

                    obj.amount = value;
                    obj.id = index;
                    obj.cid = data.id;
                    arr.push(obj);
                    this.getOrderList(arr);
                  }
                }
              })
            }
          },
          {
            title: '订购单价（元）',
            key: 'price',
            align: 'center'
          }
        ],
      }
    },
    created () {
      // 商品核单按商品加入时间顺序排序
      this.newOrderList = this.newOrderList.reverse();
    },
    mounted () {
      getHeight();
    },
    methods: {
      getOrderList (value) {
        let [id, cid, amount] = [''];
        for (let i = 0, length = value.length; i < length; i++) {
          id = value[i].id;
          cid = value[i].cid;
          amount = value[i].amount;
          this.newOrderList[id].amount = amount;
        }
        
        this.$emit('review', this.newOrderList);
      },
      close () {
        this.$emit('close');
      },
      submit () {
        this.$emit('submit');
      },
    }
  }

  window.onresize = function() {
    getHeight();
  }
  function getHeight() {
    var review = document.getElementsByClassName('sdp-review-table')[0];
    var value = document.getElementsByClassName('sdp-review-operation')[0];
    var width = review.clientWidth;
    var height = review.clientHeight;
    var top = review.offsetTop;
    value.style.width = width + 'px';
    value.style.top = height + top + 'px';
  }
  // function stop(e) {
  //   e.stopPropagation();
  // }
</script>

<style lang="scss">
  #sdp-new-order-review {
    position: fixed;
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
    z-index: 3;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    padding-left: 150px;
    background-color: rgba(0,0,0,.7);
  }
  #sdp-new-order-review .sdp-review-table {
    position: relative;
    width: 80%;
    height: 80%;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 5px;
    // padding-bottom: 50px;
    /* border-radius: 5px; */
    background-color: #FFF;
  }
  .ivu-table {
    width: auto;
  }
  .ivu-table-cell img {
    max-width: 65px;
    height: auto;
  }
  .ivu-table-cell input {
    text-align: center;
  }
  .sdp-review-operation {
    position: fixed;
    background-color: #FFF;
    z-index: 99;
    bottom: 0;
    width: 100%;
    height: 50px;
    padding: 10px;
    border-top: 1px solid #D7DDE4;
    text-align: center;
  }
  .close-btn {
    z-index: 29;
    position: fixed;
    bottom: 20px;
  }
</style>