<template>
  <Modal
    @on-cancel="onCancel"
    title="导出"
    :width="650"
    v-model="showModal">
    <div class="title">选择导出字段</div>
    <CheckboxGroup v-model="selectedColumns">
      <Row type="flex">
        <Col
          :key="value"
          :span="6"
          v-for="(label, value) in allColumns">
        <Checkbox
          :disabled="defaultColumns.includes(value)"
          :label="value">
          {{label}}
        </Checkbox>
        </Col>
      </Row>
    </CheckboxGroup>
    <div v-if="exportTips" style="color:red;font-size:12px;padding-top: 12px;">{{ exportTips }}</div>
    <div
      slot="footer"
      class="btn-list">
      <Button
        @click="onCancel"
        >关闭</Button>
      <Button
        @click="toggleSelectAll"
        >{{allSelected ? '取消全选' : '全选'}}</Button>
      <ExportButton
        text="确认导出"
        type="primary"
        @on-click="confirmExport"
        @on-success="onCancel"
        :offline="offline"
        :param-getter="getExportParams"
        :api="apiUrl[exportApi]" />
    </div>
  </Modal>
</template>

<script>
  import { mapState } from 'vuex';
  export default {
    name: "ExportNormalExcel",
    props: {
      show: {
        type: Boolean,
        default: false
      },
      params: {
        type: Object,
        default: () => {}
      },
      offline: {
        type: Boolean,
        default: true
      },
      exportType: {
        type: String,
        default: 'order_list_export'
      },
      exportApi: {
        type: String,
        default: 'exportOrderList'
      },
      EXPORT_COLUMN_STORAGE_KEY: {
        type: String,
        default: 'order_list_export_column'
      },
      exportTips: {
        type: String
      },
    },
    watch: {
      show (show) {
        if (show) {
          this.init();
          this.getFields();
        }
        this.showModal = show;
      }
    },
    computed: {
      ...mapState({
        sysConfig: "sysConfig",
      }),
      allSelected () {
        return this.selectedColumns.length === Object.keys(this.allColumns).length;
      },
    },
    data() {
      return {
        showModal: false,
        selfParams: {},
        allColumns: [],
        selectedColumns: [],
        defaultColumns: []
      }
    },
    created () {
      this.showModal = this.show;
    },
    methods: {
      init () {
        this.selfParams = {
          ...this.params
        };
      },
      confirmExport () {
        this.storage.setLocalStorage(this.EXPORT_COLUMN_STORAGE_KEY, this.selectedColumns);
      },
      toggleSelectAll () {
        if (this.allSelected) {
          this.selectedColumns = this.defaultColumns;
        } else {
          this.selectedColumns = Object.keys(this.allColumns);
        }
      },
      getFields () {
        this.$request.get(this.apiUrl.getFields, { type: this.exportType }).then(res => {
          let { status, data } = res;
          if (status) {
            this.allColumns = data.all_column;
            this.selectedColumns = this.storage.getLocalStorage(this.EXPORT_COLUMN_STORAGE_KEY) || data.selected_column;
            this.defaultColumns = data.default_column;
          } else {
            this.allColumns = [];
            this.selectedColumns = [];
            this.defaultColumns = [];
          }
        });
      },
      getExportParams () {
        const excludeColumns = Object.keys(this.allColumns).filter(column => !this.selectedColumns.includes(column));
        this.selfParams.hide_columns = JSON.stringify(excludeColumns);
        const selfParams = this.deepClone(this.selfParams);
        return selfParams;
      },
      onCancel () {
        this.$emit('on-cancel');
      }
    }
  }
</script>

<style lang="less" scoped>
  .title {
    font-size: 14px;
    color: #222127;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .btn-list {
    .ivu-btn {
      margin-left: 10px;
    }
  }
</style>
