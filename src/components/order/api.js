import axios from 'axios';

// 创建一个新的 Axios 实例
const cleanAxiosInstance = axios.create({
  // baseURL: 'https://qianfan.baidubce.com/v2/app/conversation',
  baseURL:
    process.env.NODE_ENV === 'production'
      ? 'https://base-ocr-test.sdongpo.com'
      : // ? 'https://base-ocr.sdongpo.com'
        'https://base-ocr-test.sdongpo.com',

  headers: {
    'Content-Type': 'application/json',
    Authorization:
      'Bearer bce-v3/ALTAK-3ckJfMKH7vwiUSQiDRNWU/199f35013ee7528bf90510167e2d00e9cba68ee1',
    // Host: 'qianfan.baidubce.com',
  },
});

export const postForOcrLlm = function (url, params, headerParams = {}) {
  let _params;
  let _headerParams;
  if (params && params.file) {
    let formdata = new FormData();
    for (let key in params) {
      formdata.append(key, params[key]);
    }
    _params = formdata;
    _headerParams = {
      ...headerParams,
      ...{ 'Content-Type': 'mutlipart/form-data' },
    };
  } else {
    _params = params;
    _headerParams = headerParams;
  }
  return new Promise(function (resolve, reject) {
    cleanAxiosInstance
      .post(url, _params, {
        headers: _headerParams,
      })
      .then(
        function (response) {
          resolve(response.data);
        },
        function (err) {
          reject(err);
        },
      )
      .catch(function (error) {
        reject(error);
      });
  });
};
