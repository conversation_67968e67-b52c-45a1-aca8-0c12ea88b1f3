<template>
  <div id="sdp-user-handmade-order" :class="{ isFullscreen: isFullscreen }">
    <p class="sdp-user-handmade-order-title" style="margin-bottom: 5px">
      <span @click="close">
        <Icon type="md-close"></Icon>
      </span>
      智能录单
    </p>
    <div class="sdp-user-handmade-order-content">
      <div class="btn-box" v-show="!identify">
        <Button :disabled="!identifyValue" class="mr10" @click="clearText"
          >清除内容</Button
        >
        <Button type="success" @click="identifyfoot" :disabled="!identifyValue"
          >一键识别</Button
        >
      </div>

      <div class="btn-box" v-show="identify">
        <Button
          type="success"
          @click="AddOrder"
          v-show="identifydata && identifydata.length > 0"
          class="mr10"
          >添加至订单</Button
        >
        <Button type="success" @click="ToEdit">重新编辑</Button>
      </div>

      <!-- 识别文字录入区域 -->
      <div v-show="!identify">
        <div v-if="loading" style="margin-top: 50px; text-align: center">
          识别中，请稍后
        </div>
        <template v-else>
          <Input
            style="margin: 7px 0"
            :rows="10"
            v-model="identifyValue"
            type="textarea"
            placeholder="请输入内容 例如：茄子50斤"
            wrap="hard"
          />
          <div
            v-show="noticeInfo && +mode === 3"
            style="font-size: 13px"
            v-html="noticeHTML"
          ></div>
          <div style="font-size: 13px; display: flex; align-items: center">
            <span style="margin-right: 10px">文字来源</span>
            <RadioGroup @on-change="typeChange" v-model="mode">
              <Radio label="2">
                <span>表格复制</span>
              </Radio>
              <Radio label="1">
                <span>纯文本</span>
              </Radio>
              <Radio label="3">
                <div style="display: inline-flex">
                  <span>手工单图片</span>
                  <!-- <Upload
                    v-show="mode === '3'"
                    class="ml10"
                    :max-size="6144"
                    action="/superAdmin/general/upload"
                    :before-upload="beforeUpload"
                    :on-success="handleSuccess"
                    :on-error="handleError"
                    :on-format-error="handleFormatError"
                    :on-exceeded-size="handleMaxSize"
                    :show-upload-list="false"
                  >
                    <Button type="success" :loading="ocrLoading"
                      >上传图片</Button
                    >
                  </Upload> -->
                  <div class="upload-container" v-show="mode === '3'">
                    <input
                      type="file"
                      ref="fileInput"
                      @change="onFileChange"
                      accept="image/*"
                      style="display: none"
                    />
                    <Button
                      style="margin-left: 10px"
                      type="primary"
                      :loading="ocrLoading"
                      @click="triggerFileInput"
                    >
                      + 上传图片
                    </Button>
                  </div>
                  <span v-show="mode === '3'">
                    （试用次数：
                    <span class="primary-color"
                      >{{ usedCount }}/{{ totalCount }}</span
                    >
                    ，开通多次请联系售后客服）
                  </span>
                </div>
              </Radio>
            </RadioGroup>
          </div>
          <div v-show="+mode === 2" style="font-size: 13px">
            <p>录入规则：</p>
            <p>
              1、单条商品：按商品名称、下单数量、商品单位顺序录入，如西瓜10斤
            </p>
            <p>2、多条商品：每条商品间使用换行区分，如</p>
            <p>白菜 10 斤</p>
            <p>菠菜 10 斤</p>
          </div>
          <div v-show="+mode === 1" style="font-size: 13px">
            <p>录入规则：</p>
            <p>1、单条商品：按商品名称、下单数量、商品单位录入，如西瓜10斤</p>
            <p>
              2、多条商品：每条商品间使用空格、逗号（，）、分号（；）进行区分，如
            </p>
            <p>空格：白菜10斤 菠菜10斤</p>
            <p>逗号：白菜10斤，菠菜10斤</p>
            <p>分号：白菜10斤；菠菜10斤</p>
          </div>
          <div v-show="+mode === 3" style="font-size: 13px">
            <p>录入规则：</p>
            <p>1.上传图片格式请参照商品名+数量+单位，如白菜5斤，土豆2斤</p>
            <p>2.上传图片字迹请保持工整，否则将会影响识别效果</p>
            <p>3.识别图片文字后，若要新增商品请换行新增，如：</p>
            <p>白菜 10 斤</p>
            <p>菠菜 10 斤</p>
          </div>
        </template>
      </div>

      <!-- 识别表格 -->
      <div
        v-show="identify"
        :style="{
          height: getTableHeight() + 100 + 'px',
          overflowY: 'auto',
          fontSize: '13px',
        }"
      >
        <p class="Identify_content">
          识别结果：成功{{ this.successful.length }}个， 失败{{
            this.failure.length
          }}个(标红的为失败)
        </p>
        <p class="Identify_content">
          识别内容： <span v-html="showInputString"></span>
        </p>
        <div>
          <Table
            :disabled-hover="true"
            :row-class-name="rowClassName"
            border
            :columns="columns"
            :data="identifydata"
            :height="getTableHeightNew() + 100"
          ></Table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import '@/assets/css/cropper.css';
import order from '@/api/order.js';
import Goods from '@api/goods.js';
import '@assets/scss/mixin.scss';
import { api } from '@/api/api';
import initJquery from '@/init/init-jquery.js';
import ConfigMixin from '@/mixins/config.js';
import StepPricingPoptip from '@/pages/order/components/StepPricingPoptip.vue';
import SIcon from '@components/icon';
import { postForOcrLlm } from './api';
import { Sentry } from '../../init/init-sentry';

export default {
  name: 'sdp-user-handmade-order',
  components: {
    StepPricingPoptip,
  },
  mixins: [ConfigMixin],
  data() {
    return {
      transactionTextInstance: null,
      mode: '2',
      carousIndex: 0,
      successful: [],
      failure: [],
      IdentifySearch: true,
      identifydata: [],
      noticeInfo: '',
      noticeHTML: '',
      // identifyFailure: '',
      disable: false,
      identifyValue: '',
      showInputString: '',
      identifyList: [],
      identify: false,
      notIdentify: [],
      uploadFiles: [],
      cutImgModel: false,
      recgResCol: null,
      recgList: [],
      cloneRecgList: [],
      cutModelLoad: false,
      columns: [
        {
          title: '商品名称',
          key: 'name',
          width: 200,
          render: (h, params) => {
            let row = params.row;
            let index = params.index;
            let generateRecommendGoodItem = (goods) => {
              return h(
                'span',
                {
                  style: {
                    marginRight: '10px',
                    display: 'inline-block',
                    height: '20px',
                    padding: '0 10px',
                    background: 'var(--primary-color)',
                    color: '#ffffff',
                    cursor: 'pointer',
                  },
                  on: {
                    click: () => {
                      this.identifydata[index] = Object.assign(
                        this.identifydata[index],
                        goods,
                      );
                    },
                  },
                },
                `${goods.name} ${goods.unit}`,
              );
            };
            return h(
              'div',
              {
                style: {
                  height: row.recommend_list.length ? '80px' : '40px',
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '200px',
                },
              },
              [
                h(
                  'span',
                  {
                    style: {
                      whiteSpace: 'nowrop',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      width: '167px',
                      'text-overflow': '-o-ellipsis-lastline',
                      overflow: 'hidden', //溢出内容隐藏
                      'text-overflow': 'ellipsis', //文本溢出部分用省略号表示
                      display: '-webkit-box', //特别显示模式
                      'line-clamp': '2', //行数
                      'white-space': 'break-spaces',
                      'word-break': 'break-all',
                      '-webkit-box-orient': 'vertical', //盒子中内容竖直排列
                    },
                  },
                  `${row.name}(${row.raw_name})`,
                ),
                h(
                  'div',
                  {
                    style: {
                      position: 'absolute',
                      left: '0',
                      bottom: '-18px',
                      zIndex: '9999',
                      display: 'inline-flex',
                      alignItems: 'center',
                      // paddingBottom: '20px',
                      height: '40px',
                    },
                  },
                  [
                    h(
                      'span',
                      {
                        style: {
                          marginRight: '20px',
                          display: 'inline-block',
                          height: '40px',
                        },
                      },
                      row.recommend_list.length
                        ? '商品未完全识别，为您推荐：'
                        : '',
                    ),
                    h(
                      'div',
                      {
                        style: {
                          width: '800px',
                          overflowX: 'auto',
                          height: '40px',
                          display:
                            row.recommend_list.length > 0 ? 'block' : 'none',
                        },
                      },
                      [
                        ...row.recommend_list.map((item) => {
                          return generateRecommendGoodItem(item);
                        }),
                      ],
                    ),
                  ],
                ),
              ],
            );
          },
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          render: (h, params) => {
            return h(
              'span',
              {
                style: {
                  whiteSpace: 'nowrop',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  width: '167px',
                  'text-overflow': '-o-ellipsis-lastline',
                  overflow: 'hidden', //溢出内容隐藏
                  'text-overflow': 'ellipsis', //文本溢出部分用省略号表示
                  display: '-webkit-box', //特别显示模式
                  'line-clamp': '2', //行数
                  'white-space': 'break-spaces',
                  'word-break': 'break-all',
                  '-webkit-box-orient': 'vertical', //盒子中内容竖直排列
                },
              },
              params.row.commodity_code,
            );
          },
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '描述',
          key: 'summary',
          render: (h, params) => {
            return h(
              'div',
              {
                style: {
                  width: '66px',
                  'text-overflow': '-o-ellipsis-lastline',
                  overflow: 'hidden', //溢出内容隐藏
                  'text-overflow': 'ellipsis', //文本溢出部分用省略号表示
                  display: '-webkit-box', //特别显示模式
                  'line-clamp': '2', //行数
                  'white-space': 'break-spaces',
                  'word-break': 'break-all',
                  '-webkit-box-orient': 'vertical', //盒子中内容竖直排列
                },
              },
              params.row.summary,
            );
          },
        },
        {
          title: '下单数量',
          key: 'num',
          width: 150,
          render: (h, params) => {
            return h('div', [
              h('InputNumber', {
                attrs: {},
                props: {
                  value: params.row.amount,
                },
                style: {
                  marginTop: '-4px',
                },
                on: {
                  'on-change': (val) => {
                    this.identifydata[params.index].amount = val;
                    this._updateStepPricing(this.identifydata[params.index]);
                  },
                  'on-focus': (event) => {},
                },
                nativeOn: {
                  keyup: (e) => {},
                },
              }),
              Goods.isStepPricingGoods(params.row) ? (
                <StepPricingPoptip goods={params.row}></StepPricingPoptip>
              ) : null,
            ]);
          },
        },
        {
          title: '备注',
          key: 'remark',
          render: (h, params) => {
            return h('Input', {
              props: {
                value: params.row.remark,
              },
              on: {
                'on-change': (val, val2) => {
                  this.identifydata[params.index].remark =
                    val.target.value || '';
                },
              },
            });
          },
        },
        {
          title: '下单单价',
          key: 'price',
          render: function (h, params) {
            let { row } = params;
            let key = 'price';
            let template = [h('span', row[key])];
            if (Goods.isProtocolGoods(row)) {
              template.push(
                h(SIcon, {
                  props: {
                    icon: 'xie',
                    size: 16,
                  },
                  class: 'mr5',
                  style: {
                    marginLeft: '5px',
                    position: 'relative',
                    top: '-2px',
                  },
                }),
              );
            }
            return h('div', template);
          },
        },
        {
          title: '操作',
          width: 100,
          render: (h, params) => {
            let { row, index } = params;
            return h('icon', {
              props: {
                type: 'md-close',
              },
              style: {
                cursor: 'pointer',
              },
              on: {
                click: () => {
                  this.identifydata.splice(index, 1);
                },
              },
            });
          },
        },
      ],
      //所有图片数据
      allRecg: null,
      loading: false,
      totalCount: 0,
      usedCount: 0,
      ocrLoading: false,
    };
  },
  props: ['userId', 'deliveryDate', 'isFullscreen'],
  beforeMount() {
    initJquery();
  },
  created() {
    this.getOcrUploadCount();
    this.initData();
  },
  mounted() {
    import('@/assets/js/cropper.js');
  },
  methods: {
    typeChange(e) {
      if (e === '1') {
        this.operationTextStart();
      }
    },
    // 点击文字识别
    operationTextStart() {
      const transaction = Sentry.startTransaction({
        op: 'transaction',
        name: '纯文本识别',
      });
      Sentry.configureScope((scope) => {
        scope.setSpan(transaction);
      });
      this.transactionTextInstance = transaction;
    },

    AddOrder() {
      try {
        let params = {
          user_id: this.userId,
          ids: this.identifydata.map((item) => item.id).join(','),
          showAll: true,
        };
        params.pageSize = '999999';
        let span2 = null;
        try {
          if (this.transactionTextInstance) {
            console.log('添加至订单 start');
            span2 = this.transactionTextInstance.startChild({
              op: 'step',
              description: '添加至订单',
            });
          }
        } catch (error) {}
        this.$request.get(this.apiUrl.getGoodsList, params).then((res) => {
          let { data, status, message } = res;
          if (status) {
            // 相同的商品进行汇总
            let identifydataMap = {};
            let identifydataList = [];
            let goodsList = [];
            this.identifydata.forEach((item) => {
              if (
                !identifydataMap[item.id] ||
                this.is_open_order_add_same_commodity
              ) {
                // identifydataMap 是标记去重用的
                identifydataMap[item.id] = item;
                identifydataList.push(item);
              } else {
                // 如果是出现了相同的商品，就数量和备注进行汇总
                let goods = identifydataList.find(
                  (goods) => goods.id === item.id,
                );
                goods.amount += item.amount;
                if (item.remark) {
                  goods.remark += ';' + item.remark;
                }
              }
            });
            identifydataList.forEach((item) => {
              let goods = data.list.find(
                (goodsItem) => goodsItem.id === item.id,
              );
              goodsList.push(Object.assign({}, goods, item));
            });
            this.$emit('addOrder', goodsList);
            this.$emit('close');
            try {
              if (span2 && this.transactionTextInstance) {
                span2.finish();
                this.transactionTextInstance.finish();
                console.log('添加至订单 finish');
                console.log('整个事件 finish');
              }
            } catch (error) {}
          } else {
            throw message || '没有商品数据';
          }
        });
      } catch (err) {
        this.errorNotice(err);
      }
    },
    addRecommendGoodsToGoodsList(goods, amount, recommendItem, goodsIndex) {
      goods['amount'] = amount || 1;
      recommendItem.recommend.splice(goodsIndex, 1);

      this.addGoodsToList([goods]);
    },
    rowClassName() {
      return 'new-order-user-handle-row';
    },
    ToEdit() {
      // 重新编辑
      this.identify = false;
      this.IdentifySearch = true;
      this.identifydata = [];
      this.successful = [];
      this.failure = [];
    },

    triggerFileInput() {
      if (this.usedCount >= this.totalCount) {
        this.errorMessage('免费试用次数超限， 请联系客服');
        return;
      }
      this.$refs.fileInput.click();
    },
    onFileChange(event) {
      const file = event.target.files[0];
      if (file && file.type.startsWith('image/')) {
        this.handleOcrLlm(file);
        this.$refs.fileInput.value = '';
      } else {
        this.errorMessage('请选择图片文件！');
      }
    },

    handleOcrLlm(file) {
      this.ocrLoading = true;
      let retryCount = 0;
      const maxRetries = 3;
      const delay = 1000; // 延迟1秒

      const attemptOcrLlm = () => {
        postForOcrLlm('/ocr-llm', { file })
          .then((v) => {
            if (!v.result) {
              if (retryCount < maxRetries) {
                retryCount++;
                setTimeout(attemptOcrLlm, delay);
              } else {
                this.errorMessage(`处理过程多次失败，请稍后重试!`);
              }
              return;
            }
            const skuDataStr = this.extractPurchaseInfo(v.result);
            const noticeInfoStr = this.extractNoticeInfo(v.result);
            this.identifyValue += this.identifyValue
              ? `\n${skuDataStr}`
              : skuDataStr;
            this.noticeInfo += noticeInfoStr;
            this.noticeHTML = this.processNotice(this.noticeInfo);
          })
          .catch((error) => {
            this.errorMessage(`${error.message || '识别出错请重试!'}`);
          })
          .finally(() => {
            this.ocrLoading = false;
            this.ocrCount();
          });
      };

      attemptOcrLlm();
    },

    extractPurchaseInfo(inputString) {
      if (!inputString) return '';
      // 正则表达式匹配“商品信息开始”和“商品信息结束”之间的内容，允许有灵活的空格和换行
      const regex1 = /商品信息开始\s*[:：]?\s*([\s\S]*?)\s*商品信息结束/;
      const regex2 = /商品信息开始\s*[:：]?\s*([\s\S]*?)\s*请注意/;

      const match1 = inputString.match(regex1);
      const match2 = inputString.match(regex2);
      // 返回匹配的内容，如果没有匹配则返回空字符串
      const rst1 = match1 ? match1[1].trim() : '';
      const rst2 = match2 ? match2[1].trim() : '';

      return rst1 || rst2;
    },

    extractNoticeInfo(text) {
      const noticeStartIndex = text.indexOf('请注意：');
      if (noticeStartIndex === -1) {
        return null;
      }
      const noticeText = text.substring(noticeStartIndex + 4).trim();
      return noticeText;
    },

    processNotice(noticeContent) {
      if (!noticeContent) return '';
      // 分割每个序号条目
      const items = noticeContent
        .split(/\d+\.\s+/)
        .filter((item) => item.trim() !== '');

      // 创建 HTML 内容
      let htmlContent = `
  <div style="background-color: #fff3cd; border-left: 6px solid #ffecb5; padding: 10px; margin: 10px 0; font-family: Arial, sans-serif;">
    <p><strong>请注意：</strong></p>
    <ol>`;
      items.forEach((item) => {
        // 查找商品名称并标记为重要
        const importantWords = item.match(/(["“”][^"“”]*["“”])/g);
        if (importantWords) {
          importantWords.forEach((word) => {
            item = item.replace(
              word,
              `<span style="font-weight: bold; color: #d9534f;">${word}</span>`,
            );
          });
        }
        htmlContent += `<li>${item}</li>`;
      });
      htmlContent += '</ol></div>';
      return htmlContent;
    },

    identifyfoot() {
      if (this.loading) {
        return;
      }
      this.loading = true;
      let span1 = null;
      if (this.mode === '1') {
        try {
          if (this.transactionTextInstance) {
            span1 = this.transactionTextInstance.startChild({
              op: 'step',
              description: '一键识别',
            });
            console.log('一键识别 start');
          }
        } catch (error) {}
      }
      // 文字识别
      if (this.identifyValue.length <= 0) {
        this.errorNotice('请输入内容!');
        return;
      }

      let params = {
        delivery_date: this.deliveryDate,
        search_key: this.identifyValue,
        user_id: this.userId,
        mode: this.mode,
      };
      if (this.mode === '3') {
        params.mode = '2';
      }
      this.$request
        .post(api.getOrderSuperAaiSearchOlder, params)
        .then((res) => {
          this.loading = false;
          if (res.status == 1) {
            let successList = [];
            let data = [];
            let recommendList = [];
            let showInputString = this.identifyValue;
            res.data.forEach((item, index) => {
              this.IdentifySearch = false;
              this.identify = true;
              if (item.status == 1) {
                item.commodity.recommend_list = [];
                item.commodity.amount = +item.num;
                item.commodity.raw_name = item.raw_name;
                this.successful.push(item.status);
                showInputString = showInputString
                  .split(item.raw_name)
                  .join(`<span class="text-green">${item.raw_name}</span>`);
                successList.push(item.commodity);
              } else if (item.status == 2) {
                showInputString = showInputString
                  .split(item.raw_name)
                  .join(`<span class="text-green">${item.raw_name}</span>`);
                let goods = item.recommend.shift();
                goods.recommend_list = item.recommend;
                goods.amount = +item.num;
                goods.raw_name = item.raw_name;
                this.successful.push(item.status);
                successList.push(goods);
              } else if (item.status == 0) {
                this.failure.push(item.status);
                data.push(item.str);
                // showInputString = showInputString.replace(new RegExp(`(${item.str})`, 'g'), `<span style="color: red">$1</span>`);
                showInputString = showInputString
                  .split(item.raw_name)
                  .join(`<span style="color: red">${item.raw_name}</span>`);
              }
            });
            this.showInputString = showInputString;
            this.addGoodsToList(successList);
            this.identifyList = recommendList;
            try {
              if (span1) {
                span1.finish();
                console.log('一键识别 finish');
              }
            } catch (error) {}
          } else {
            this.errorNotice(res.message);
          }
        });
    },
    addGoodsToList(goodsList) {
      const isGoodsExist = (goods) => {
        return this.identifydata.some((item) => item.id === goods.id);
      };
      goodsList.forEach((goods) => {
        // if (!isGoodsExist(goods)) {
        this.identifydata.push(goods);
        // }
      });
    },
    async recgBegin() {
      const params = {
        img_url: query,
        version: '1',
      };
      // OCRTODO
      let { status, message, data } = await order.ocrRecogniseWithllm(params);
      let dataStr = '';
      if (status && data && data.length > 0) {
        // data结构为: [{sku, num, unit}], 将这个结构转换为字符串
        dataStr = this.extractPurchaseInfo(data);
        this.identifyValue += dataStr;
        console.log('ocrRecogniseWithllm: ', dataStr);
      } else {
        this.errorMessage(message);
      }
      await this.getOcrUploadCount();
      this.ocrLoading = false;
    },

    initData() {
      this.recgResCol = [
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            let row = params.row;
            return h(
              'div',
              {
                style: {
                  width: '80px',
                  height: '80px',
                  overFlow: 'hidden',
                  'vertical-align': 'middle',
                  display: 'table-cell',
                },
              },
              [
                h('img', {
                  attrs: {
                    src: row.logo,
                  },
                  style: {
                    width: '100%',
                  },
                }),
              ],
            );
          },
        },
        {
          title: '商品ID',
          key: 'id',
        },
        {
          title: '名称编码',
          render: (h, params) => {
            let row = params.row;
            return h('div', [h('p', row.name), h('p', row.commodity_code)]);
          },
        },
        {
          title: '单位',
          key: 'unit',
        },
        {
          title: '价格',
          key: 'price',
        },
        {
          title: '识别数量',
          render: (h, params) => {
            let row = params.row;
            return h('div', [
              h('InputNumber', {
                attrs: {
                  id: 'recg' + row.id,
                },
                props: {
                  value: row.amount,
                },
                on: {
                  'on-change': (val) => {
                    params.row.amount = val;
                    this.cloneRecgList[params.index].amount = val;
                    this.recgList[params.index] = this.cloneObj(
                      this.cloneRecgList[params.index],
                    );
                  },
                  'on-focus': (event) => {
                    event.target.select();
                  },
                },
                nativeOn: {
                  keyup: (e) => {
                    if (e.keyCode == 13) {
                      this.recgList.forEach((e, i, arr) => {
                        if (e.id == row.id && i < arr.length - 1) {
                          document
                            .getElementById('recg' + arr[i + 1].id)
                            .getElementsByClassName('ivu-input-number-input')[0]
                            .select();
                        }
                      });
                    }
                  },
                },
              }),
            ]);
          },
        },
        {
          title: '操作',
          render: (h, params) => {
            return h('div', [
              h(
                'span',
                {
                  on: {
                    click: () => {
                      this.recgList.splice(params.index, 1);
                      this.cloneRecgList.splice(params.index, 1);
                    },
                  },
                  style: {
                    cursor: 'pointer',
                    color: 'red',
                  },
                },
                '删除',
              ),
            ]);
          },
        },
      ];
    },
    async getOcrUploadCount() {
      const { status, message, data } = await order.getOcrUploadCount();
      if (status) {
        console.log('getOcrUploadCount', data);
        const { used_count, total_count } = data;
        this.totalCount = total_count;
        this.usedCount = Number(used_count);
      } else {
        this.errorMessage(message || '网络异常');
      }
    },

    async ocrCount() {
      const { status, message } = await order.ocrCount();
      if (!status) {
        this.errorMessage(message || '网络异常');
      } else {
        this.getOcrUploadCount();
      }
    },

    beforeUpload(file) {
      this.ocrLoading = true;
      return true;
    },
    handleSuccess(res, file) {
      if (res.status) {
        console.log('uploadFiles-res', res);
        console.log('uploadFiles-file', file);
        var data = res.data;
        this.uploadFiles.push({
          name: file.name,
          server: data.server_url,
          upyun: data.upyun_url,
          isCut: false,
        });
        this.carousIndex = this.uploadFiles.length - 1;
        this.recgBegin();
        // this.isUpload = false;
      } else {
        this.ocrLoading = false;
        this.errorMessage(res.message || '上传失败');
      }
    },
    handleError(error, file) {
      this.errorMessage(error, '上传失败');
      this.ocrLoading = false;
    },
    handleFormatError(file) {
      this.$Notice.warning({
        title: '文件格式不正确',
        desc:
          '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。',
      });
    },
    handleMaxSize(file) {
      this.$Notice.warning({
        title: '超出文件大小限制',
        desc: '文件 ' + file.name + ' 太大，不能超过 6M。',
      });
    },
    cutImg() {
      let imgArr = [];
      let ROW = 22;
      let COL = 8;
      let c = $('#cut-img').cropper('getCroppedCanvas');
      let ROWLEN = c.height / ROW;
      let COLLEN = c.width / COL;
      let ctx = c.getContext('2d');
      ctx.save();
      for (let i = 0; i < ROW; i++) {
        for (let j = 0; j < COL; j++) {
          var tempC = document.createElement('canvas');
          tempC.width = COLLEN;
          tempC.height = ROWLEN;
          var tctx = tempC.getContext('2d');
          var imageData = ctx.getImageData(
            j * COLLEN,
            i * ROWLEN,
            COLLEN,
            ROWLEN,
          );
          tctx.putImageData(imageData, 0, 0);
          imgArr.push(tempC.toDataURL());
        }
      }
      return imgArr;
    },
    confirmHandOrderList(list) {
      list.forEach((e) => {
        if (e.detect_result == '未识别') {
          this.$set(e, 'name', '未识别');
          this.$set(e, 'commodity_code', '');
        }
      });
      let arr = list;
      return arr;
    },
    close: function () {
      this.$emit('close');
    },
    diff(a, b) {
      if (!(a instanceof Array) || !(b instanceof Array)) {
        return false;
      }
      let result = [];
      for (let i = 0; i < b.length; i++) {
        let arr = a.filter((e) => e.id == b[i].id);
        if (arr.length == 0) {
          result.push(b[i]);
        }
      }
      return result;
    },
    // 阶梯定价 根据 下单数量 更新 下单单价
    _updateStepPricing(row) {
      const { amount } = row;
      if (Goods.isStepPricingGoods(row)) {
        const stepPriceItem = row.price_grads_list.find(
          (item) =>
            +amount >= +item.min_order_num &&
            (item.max_order_num ? +amount < +item.max_order_num : true),
        );
        if (stepPriceItem) {
          row.price = stepPriceItem.price;
        }
      }
    },
    clearText() {
      this.identifyValue = '';
      this.noticeContent = '';
      this.noticeHTML = '';
      this.noticeInfo = '';
    },
  },
};
</script>

<style lang="less">
.new-order-user-handle-row {
  td {
    position: relative;
  }
  .ivu-table-cell {
    display: inline-flex;
    align-items: flex-start;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
</style>

<style scoped>
.ivu-carousel >>> .ivu-carousel-arrow {
  -webkit-transform: translateY(0);
  transform: translateY(0);
  bottom: -64px;
  top: auto;
}

.ivu-carousel >>> .ivu-carousel-arrow.left {
  left: 130px;
}

.ivu-carousel >>> .ivu-carousel-arrow.right {
  right: 130px;
}
</style>

<style lang="scss" scoped>
.Identify_content {
  margin: 5px 0;
}
.btn-box {
  margin: 24px 0px 9px 0px;
  display: flex;
  justify-content: flex-end;
}
.primary-color {
  color: var(--primary-color);
}
#sdp-user-handmade-order {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  width: 1000px;
  /*			height: 100%;*/
  bottom: 0;
  overflow: hidden;
  font-size: 16px;
  border-left: 1px solid #eee;
  background-color: #fff;
  .sdp-user-handmade-order-content {
    margin: 0 12px;
  }
  &.isFullscreen {
    bottom: 46px;
  }
}

.sdp-user-handmade-order-title {
  width: 100%;
  padding: 10px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.spd-reload {
  float: right;
}

.spd-reload:hover {
  cursor: pointer;
}

.sdp-user-handmade-order-title i {
  float: left;
  margin-top: 5px;
}

.sdp-user-handmade-order-title i:hover {
  cursor: pointer;
}
#sdp-user-handmade-order .ivu-icon-ios-cloud-upload {
  font-size: 100px !important;
  color: #eee !important;
}

.recg-flag {
  position: absolute;
  right: 10px;
  line-height: 32px;
}

#sdp-user-handmade-order /deep/ .ivu-table-cell {
  overflow: visible;
}
.upload-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}
</style>
