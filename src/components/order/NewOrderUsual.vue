<template>
  <div class="sdp-usual-order">
    <p class="sdp-usual-order-title">
      <span @click="close">
        <Icon type="ios-arrow-forward"></Icon>
       </span>
      常用商品清单
    </p>
    <div class="sdp-usual-order-lists">
      <span class="lists-header">
        <p>商品</p>
        <p>图片</p>
        <p>数量</p>
      </span>
      <ul>
        <li>
          <div class="lists-item">
            <span>
              <p>新鲜花菜</p>
              <p>000000001</p>
            </span>
            <span>斤</span>
            <span><i-input placeholder="输入订购数(回车确定)" style="width: 90%"></i-input></span>
          </div>
        </li>
        <li>
          <div class="lists-item">
            <span>
              <p>新鲜花菜</p>
              <p>000000001</p>
            </span>
            <span>斤</span>
            <span><i-input placeholder="输入订购数(回车确定)" style="width: 90%"></i-input></span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'sdp-usual-order',
    data () {
      return {

      }
    },
    created () {

    },
    mounted () {

    },
    methods: {
      close () {
        this.$emit('close');
      }
    }
  }
</script>

<style lang="scss">
  .sdp-usual-order {
    position: fixed;
    z-index: 199;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    font-size: 16px;
    border-left: 1px solid #EEE;
    background-color: #FFF;
  }
  .sdp-usual-order-title {
    width: 100%;
    padding: 10px;
    text-align: center;
  }
  .sdp-usual-order-title i {
    float: left;
    margin-top: 5px;
  }
  .sdp-usual-order-title i:hover {
    cursor: pointer;
  }
  .sdp-usual-order-lists {
    width: 100%;
  }
  .sdp-usual-order-lists .lists-header {
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    width: 100%;
    height: 40px;
    background-color: #f8f8f9;
  }
  .sdp-usual-order-lists .lists-header > p {
    text-align: center;
    font-size: 12px;
    font-weight: 600;
  }
  .sdp-usual-order-lists .lists-header > p:nth-child(1) { width: 30%; }
  .sdp-usual-order-lists .lists-header > p:nth-child(2) { width: 20%; }
  .sdp-usual-order-lists .lists-header > p:nth-child(3) { width: 50%; }

  .sdp-usual-order-lists .lists-item {
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    width: 100%;
    /* height: 40px; */
    padding: 5px 0;
    border-bottom: 1px solid #EEE;
  }
  .sdp-usual-order-lists .lists-item > span {
    text-align: center;
    font-size: 12px;
  }

  .sdp-usual-order-lists .lists-item > span:nth-child(1) { width: 30%; }

  .sdp-usual-order-lists .lists-item > span:nth-child(2) { width: 20%; }

  .sdp-usual-order-lists .lists-item > span:nth-child(3) { width: 50%; }

</style>
