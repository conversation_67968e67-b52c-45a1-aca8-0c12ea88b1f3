import goods from '@api/goods.js';
export default {
  data() {
    return {
      columns1: [
        {
          type: 'selection',
          width: 60,
          align: 'left'
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '名称编码',
          key: 'name',
          width: 250,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('div', {}, data.name),
              h(
                'a',
                {
                  style: {
                    color: '#333'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '商品分类',
          key: 'category_name'
        },
        {
          title: '起订量',
          key: 'order_quantity'
        },
        {
          title: '价格',
          key: 'price',
          isShow: this.isShowPrice,
          render: function(h, params) {
            let { row } = params;
            let key = 'price';
            let template = [h('span', row[key])];
            if (goods.isProtocolGoods(row)) {
              template.push(
                h(
                  'span',
                  {
                    class: {
                      'tag-protocol-price': true
                    }
                  },
                  '协'
                )
              );
            }
            return h('div', template);
          }
        },
        {
          title: '下单数量',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.amount),
                min: 0,
                // precision: 2,
                disabled: data._disabled
              },
              style: {
                width: '100%'
              },
              nativeOn: {
                click: () => {
                  this.isFocus = true;
                }
              },
              on: {
                'on-change': val => {
                  params.row.amount = val ? val : '';
                  if (isNaN(params.row.amount) || !params.row.amount) {
                    params.row.amount = 0;
                  }
                  this.selectedRow[params.index].amount = params.row.amount;
                },
                'on-focus': event => {
                  event.target.select();
                }
              }
            });
          }
        }
      ],
      columns2: [
        {
          type: 'selection',
          width: 60,
          align: 'left',
          className: 'table-select'
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '名称编码',
          key: 'name',
          width: 250,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('div', {}, data.name),
              h(
                'a',
                {
                  style: {
                    color: '#333'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '描述',
          key: 'summary'
        },
        {
          title: '商品分类',
          key: 'category_name'
        }
      ],
      columns3: [
        {
          type: 'selection',
          width: 60,
          align: 'left'
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '名称编码',
          key: 'name',
          width: 250,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('div', {}, data.name),
              h(
                'a',
                {
                  style: {
                    color: '#333'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '商品分类',
          key: 'category_name',
          width: 140
        },
        {
          title: '仓库',
          key: 're_store_id_name'
        },
        {
          title: '价格',
          key: 'average_price',
          isShow: this.isShowPrice,
        },
        {
          title: '库存',
          key: 'existing'
        },
        {
          title: '数量',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.amount),
                min: 0,
                // precision: 2,
                disabled: data._disabled
              },
              style: {
                width: '100%'
              },
              nativeOn: {
                click: () => {
                  this.isFocus = true;
                }
              },
              on: {
                'on-change': val => {
                  params.row.amount = val ? val : 0;
                  this.selectedRow[params.index].amount = params.row.amount;
                },
                'on-focus': event => {
                  event.target.select();
                }
              }
            });
          }
        }
      ],
      columns4: [
        {
          type: 'selection',
          width: 60,
          align: 'left'
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '名称编码',
          key: 'name',
          width: 250,
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h('div', {}, data.name),
              h(
                'a',
                {
                  style: {
                    color: '#333'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '商品分类',
          key: 'category_name',
          width: 140
        },
        {
          title: '价格',
          key: 'show_average_price',
          isShow: this.isShowPrice,
        },
        {
          title: '库存',
          key: 'existing'
        }
      ],
      columns5: [
        {
          type: 'selection',
          width: 60,
          align: 'left'
        },
        {
          title: '商品图片',
          key: 'logo',
          render: (h, params) => {
            var obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '名称编码',
          key: 'name',
          width: 250,
          render: (h, params) => {
            let data = params.row;
            let hText = [h('span', {}, data.name)]
            if (data.is_temp_c == 1) {
              hText = [
                h('img', {
                  attrs: {
                    src: require('@assets/images/appCenter/lin.png')
                  },
                  style: {
                    width: '16px',
                    marginRight: '5px'
                  }
                }),
                h('span', {}, data.name)
              ]
            }
            return h('div', [
              h('div', {
                style: {
                  display: 'flex',
                  alignItems: 'center'
                }
              }, hText),
              h(
                'a',
                {
                  style: {
                    color: '#333'
                  }
                },
                data.commodity_code
              )
            ]);
          }
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '商品分类',
          key: 'category_name'
        },
        {
          title: '起订量',
          key: 'order_quantity'
        },
        {
          title: '价格',
          key: 'price',
          isShow: this.isShowPrice,
          render: function(h, params) {
            let { row } = params;
            let key = 'price';
            let template = [h('span', row[key])];
            if (goods.isProtocolGoods(row)) {
              template.push(
                h(
                  'span',
                  {
                    class: {
                      'tag-protocol-price': true
                    }
                  },
                  '协'
                )
              );
            }
            return h('div', template);
          }
        },
        {
          title: '加单数量',
          align: 'center',
          render: (h, params) => {
            let data = params.row;
            return h('InputNumber', {
              props: {
                value: parseFloat(data.extra_amount),
                min: 1,
                // precision: 2,
                disabled: data._disabled
              },
              style: {
                width: '100%'
              },
              nativeOn: {
                click: () => {
                  this.isFocus = true;
                }
              },
              on: {
                'on-change': val => {
                  params.row.extra_amount = val ? val : '';
                  if (
                    isNaN(params.row.extra_amount) ||
                    !params.row.extra_amount
                  ) {
                    params.row.extra_amount = 1;
                  }
                  this.selectedRow[params.index].extra_amount =
                    params.row.extra_amount;
                },
                'on-focus': event => {
                  event.target.select();
                }
              }
            });
          }
        }
      ]
    };
  }
};
