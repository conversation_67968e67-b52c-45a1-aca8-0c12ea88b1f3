<template>
  <div class="smart-order-creator">
    <Modal width="780" height="516" v-model="show" :draggable="false">
      <template #header>
        <div class="ai-title" style="vertical-align: middle">
          <img
            width="100%"
            draggable="false"
            style="margin-top: 16px"
            height="16px"
            :src="modalTitle"
          />
        </div>
        <!-- 添加切换tab -->
        <div class="ai-tabs">
          <div
            class="ai-tab-item"
            :class="{ active: activeTab === 'text' }"
            @click="activeTab = 'text'"
          >
            文字识别
          </div>
          <div
            class="ai-tab-item"
            :class="{ active: activeTab === 'image' }"
            @click="activeTab = 'image'"
          >
            图片识别
          </div>
        </div>
      </template>

      <template #close>
        <div class="ai-close" style="vertical-align: middle; cursor: pointer">
          <img
            width="18px"
            height="18px"
            draggable="false"
            style="margin-top: 6px; margin-right: 11px"
            :src="modalClose"
          />
        </div>
      </template>

      <div class="ai-content">
        <!-- 文字识别内容 -->
        <div v-show="activeTab === 'text'">
          <Input
            :rows="10"
            width="732"
            height="413"
            v-model="originalData"
            type="textarea"
            :maxlength="500"
            placeholder="请输入内容 例如：茄子50斤, 或者，我要2斤苹果， 20个花卷"
            wrap="hard"
          />
          <p class="line-count">
            行数: {{ lineCount }}
            <span style="margin-left: 15px">字数{{ textCount }} / 500</span>
          </p>
        </div>

        <!-- 图片识别内容 -->
        <div v-show="activeTab === 'image'" class="image-upload">
          <Upload
            ref="upload"
            :show-upload-list="false"
            :before-upload="handleImageUpload"
            :max-size="2048"
            accept=".jpg,.jpeg,.png"
            :action="null"
          >
            <div class="upload-area">
              <div v-if="!imageUrl" class="upload-placeholder">
                <Icon type="ios-camera" size="40" />
                <p>点击或拖拽图片到这里上传</p>
                <p class="upload-tip">支持 jpg、png 格式，大小不超过2M</p>
              </div>
              <img v-else :src="imageUrl" class="preview-image" />
            </div>
          </Upload>
        </div>

        <!-- Loading遮罩 -->
        <div
          :style="{
            height: auto,
            top: '0px',
            bottom: '-50px',
            left: '-5px',
            right: '-5px',
            position: 'absolute',
          }"
          class="s-loading s-loading--default"
          v-if="processing"
        >
          <span class="s-loading-content" style="margin-top: -60px"></span>
          <p class="s-loading-text">AI识别中，请耐心等候...</p>
        </div>
      </div>
      <template slot="footer">
        <div
          class="ai-btn-wrap"
          style="
            width: 84px;
            height: 30px;
            border-radius: 15px;
            cursor: pointer;
          "
        >
          <img
            class="ai-btn"
            @click="save"
            height="30px"
            width="84px"
            :src="modalBtn"
            draggable="false"
            :class="{ active: isActive }"
            @mousedown="isActive = true"
            @mouseup="isActive = false"
            @mouseleave="isActive = false"
            style="cursor: pointer"
          />
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
import Button from '@components/button';
import SIcon from '@components/icon';
import ConfigMixin from '@/mixins/config';
import { api } from '@/api/api';
import SLoading from '@components/s-global-loading';

const modalBtn = require('../../assets/images/aiOrderCreate/modal-btn.png');
const modalTitle = require('../../assets/images/aiOrderCreate/modal-title.png');
const modalClose = require('../../assets/images/aiOrderCreate/tag-close.png');

export default {
  name: 'smart-order-creator',
  components: {
    Button,
    SIcon,
    SLoading,
  },
  // 建议添加组件销毁时的清理
  beforeDestroy() {
    if (this.eventSource) {
      this.eventSource.close();
    }
    this.uploading = false;
    this.processing = false;
  },
  props: ['userId', 'delivery_date'],
  mixins: [ConfigMixin],
  computed: {
    // 计算文本行数
    lineCount() {
      return this.originalData.split('\n').length;
    },
    textCount() {
      return this.originalData.length;
    },
  },
  data() {
    return {
      imageUrl: '',
      activeTab: 'text',
      show: false,
      processing: false,
      isActive: false,
      disabled: false,
      originalData: '',
      modalBtn: modalBtn,
      modalTitle: modalTitle,
      modalClose: modalClose,
      formatGoodList: [],
      recommandGoodsList: [],
      goodsList: [],
      modalStyle: {
        heigth: '516px',
        background: 'red',
      },
      eventSource: null,
    };
  },
  methods: {
    checkInput(event) {
      const maxLength = 500;
      if (this.originalData.length > maxLength) {
        this.originalData = this.originalData.substring(0, maxLength); // 通过响应式系统更新值
      }
    },
    open() {
      this.show = true;
    },
    closeModal(flag) {
      this.show = false;
      if (flag || this.eventSource) {
        this.$emit('smart-order-end', true);
        this.eventSource.close();
        this.processing = false;
      }
    },
    close(flag) {
      this.closeModal(flag);
    },
    async save() {
      if (this.processing) {
        this.$Message.warning('请等待AI识别完成！');
        return;
      }
      try {
        this.$emit('orginal-data', this.originalData);
        if (
          !(this.originalData && this.originalData.trim()) ||
          !this.originalData
        ) {
          this.$Message.warning('输入不能为空！');
          return;
        }
        this.processing = true;
        await this.getOrderSuperAaiSearch(this.originalData);
      } catch (error) {
        this.handleAiError('识别出错！', 'error');
        return;
      }
    },

    handleAiError(text, type) {
      this.$Message[type](text);
      this.processing = false;
      this.close(true);
    },
    async handleImageUpload(file) {
      const isImage = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isImage) {
        this.$Message.error('只能上传jpg或png格式的图片！');
        return false;
      }
      if (!isLt2M) {
        this.$Message.error('图片大小不能超过2MB！');
        return false;
      }

      try {
        this.processing = true;
        const formData = new FormData();
        formData.append('image', file);
        formData.append('delivery_date', this.delivery_date);
        formData.append('user_id', this.userId);
        const token =
          sessionStorage['token'] || localStorage['admin_token'] || '';
        formData.append('token', token);
        formData.append('stream', 'true');

        // 使用 Fetch API 发送 POST 请求
        const response = await fetch(api.orderImageOcr, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 创建流读取器和解码器
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        // 用于存储未完整的数据
        let buffer = '';

        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            // 处理最后剩余的数据
            if (buffer.trim()) {
              const jsonStr = buffer.replace(/^data:\s*/, '').trim();
              if (jsonStr) {
                this.processEventStream(jsonStr);
              }
            }
            break;
          }

          // 解码新的数据块并与buffer拼接
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 查找完整的行
          let newlineIndex;
          while ((newlineIndex = buffer.indexOf('\n')) !== -1) {
            const line = buffer.slice(0, newlineIndex).trim();
            buffer = buffer.slice(newlineIndex + 1);

            if (line) {
              // 移除 "data: " 前缀并处理单行数据
              const jsonStr = line.replace(/^data:\s*/, '').trim();
              if (jsonStr) {
                this.processEventStream(jsonStr);
              }
            }
          }
        }
      } catch (error) {
        console.error('图片上传或处理错误：', error);
        this.handleAiError('图片上传失败！', 'error');
      } finally {
        this.uploading = false; // 重置上传状态
        this.processing = false;
        this.close(true);
        this.$emit('tag-label-show', false);
      }
    },
    async getOrderSuperAaiSearch(search_key) {
      const token =
        sessionStorage['token'] || localStorage['admin_token'] || '';
      const params = {
        delivery_date: this.delivery_date,
        search_key: search_key,
        user_id: this.userId,
        mode: 1,
        token,
        stream: true,
      };
      const startTime = Date.now();

      console.log('开始', startTime);
      // 使用 EventSource 来处理流数据
      const url =
        api.getOrderSuperAaiSearch + '?' + new URLSearchParams(params);
      const eventSource = new EventSource(url);
      this.eventSource = eventSource;

      this.eventSource.onopen = () => {
        console.log('连接已建立');
        this.$emit('smart-order-start', true);
      };

      this.eventSource.onmessage = (event) => {
        let chunkTime = null;
        // 记录每一次收到chunk的时间
        if (!chunkTime) {
          chunkTime = Date.now();
          console.log('chunk received at:', chunkTime);
          console.log(
            'Time to received chunk (seconds):',
            (chunkTime - startTime) / 1000,
            'S',
          );
        }
        console.log('收到数据：', event.data);
        if (this.show) {
          this.show = false;
        }
        this.processEventStream(event.data, startTime);
      };

      // 监听服务器发送的 'errorEvent'
      this.eventSource.addEventListener('errorEvent', (event) => {
        try {
          // 解析事件数据
          const eventData = JSON.parse(event.data);
          this.$Message.error(eventData.message);
          // 在此处处理错误消息，例如显示在 UI 上
        } catch (e) {
          console.error('解析事件数据时发生错误：', e);
          this.$Message.error('解析数据时发生错误');
        }
        this.close();
      });

      this.eventSource.onerror = () => {
        this.close();
        // 自然关闭的情况下, 完成后，清空录入框数据
        this.originalData = '';
      };
    },
    processEventStream(streamData) {
      // 按行分割事件流数据
      const events = streamData.split('\n');
      events.forEach((event) => {
        try {
          // 解析每条 message 数据
          const data = JSON.parse(event);
          if (Array.isArray(data)) {
            const commodityList = data.map((v) => {
              if (v.recommend && v.recommend[0]) {
                return {
                  ...v,
                  ...v.recommend[0],
                  _isSmart: true,
                  _smartSuccess: true,
                };
              } else {
                return { ...v, _isSmart: true };
              }
            });

            console.log('commodityList-----', commodityList);
            // 每次收到一个商品列表，逐条返回

            this.$emit('smart-goods-list', commodityList);
          }
        } catch (error) {
          console.error('数据解析错误', error);
          this.processing = false;
          this.close();
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.ai-content {
  position: relative;
}

.ai-tabs {
  display: flex;
  margin-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.ai-tab-item {
  padding: 8px 16px;
  cursor: pointer;
  position: relative;
  color: #666;
  height: 48px;
  margin-top: 11px;
  margin-left: -1px;

  &.active {
    color: #2d8cf0;
    font-weight: 500;

    &:after {
      content: '';
      position: absolute;
      bottom: -1px;
      left: 0;
      right: 0;
      height: 2px;
      background: #2d8cf0;
    }
  }
}

.image-upload {
  height: 413px;

  .upload-area {
    cursor: pointer;
    width: 100%;
    height: 395px;
    position: absolute;
    align-items: center;
    justify-content: center;
    background: linear-gradient(260deg, #e1fdff 0%, #f7f6ff 100%);

    &:hover {
      border-color: #2d8cf0;
    }
  }

  .upload-placeholder {
    text-align: center;
    color: #666;

    .upload-tip {
      font-size: 12px;
      color: #999;
      margin-top: 8px;
    }
  }

  .preview-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
.line-count {
  position: absolute;
  right: 5px;
  bottom: 5px;
  font-size: 14px;
  color: rgb(94, 89, 89);
}
/deep/.ivu-modal-body {
  padding: 2px 26px;
}
/deep/.ivu-modal-content {
  height: 516px;
  width: 780px;
  background-image: url('../../assets/images/aiOrderCreate/modal-bg.png');
  background-size: cover;
  background-position: center center;
}

/deep/.ivu-input-wrapper {
  display: inline-block;
  position: relative;
  vertical-align: middle;
  line-height: normal;
  border: none;
}

/deep/ .ivu-modal-header {
  border: none !important;
}

/deep/ textarea {
  background: linear-gradient(260deg, #e1fdff 0%, #f7f6ff 100%);
  border-image: linear-gradient(
      37deg,
      rgba(253, 97, 255, 1),
      rgba(50, 161, 255, 1),
      rgba(0, 238, 255, 1),
      rgba(72, 255, 160, 1)
    )
    2 2;
  backdrop-filter: blur(10px);
  height: 409px;
  padding: 8px 10px;
  resize: none;
}

/deep/ .ivu-modal-footer {
  padding: 14px 24px;
}

/deep/ .ai-btn.active {
  transform: scale(0.95); /* 缩放效果 */
  opacity: 0.8; /* 透明度变化 */
  transition:
    transform 0.1s ease,
    opacity 0.1s ease; /* 动画效果 */
}
/deep/.s-loading {
  border-radius: 6px;
}

/deep/.ivu-modal-close {
  z-index: 1000;
}
</style>
