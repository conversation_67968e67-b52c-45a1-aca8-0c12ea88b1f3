<template>
  <div>
    <Modal v-model="showModal" title="请选择商品" :mask-closable="false"
           class-name="vertical-center-modal" width="1100">
      <div slot="close"><Icon type="close" @click.native="hideModal"></Icon></div>
      <Row class="selectRow mb10" type="flex" justify="start" :gutter="10">
        <Col span="5">
            <Cascader :data="categoryList" change-on-select :load-data="loadSubCategoryData" clearable
                placeholder="全部分类" @on-change="changeCategory" style="width: 100%"></Cascader>
        </Col>
        <!--<Col span="5" v-if="modalType === 'storeGoods' && AreaList.length > 0">-->
          <!--<Select v-model="filters.AreaId" @on-change="changeArea">-->
            <!--<Option v-for="area in AreaList" :value="area.id" :key="area.id">{{area.name}}</Option>-->
          <!--</Select>-->
        <!--</Col>-->
        <Col span="5">
          <Input placeholder="请输入商品名称/编码进行搜索" v-model="filters.no" @on-enter="loadGoodsList(1)" clearable>
            <Button slot="append" icon="ios-search" @click="loadGoodsList(1)"></Button>
          </Input>
        </Col>
        <Col v-if="modalType !== 'storeGoods'">
        <Checkbox v-model="showCollectionOnly" @on-change="loadGoodsList(1)">只显示常用商品</Checkbox>
        </Col>
        <Col span="8">
          <p class="_tips">小贴士：鼠标单击表格行即可直接选中或修改</p>
        </Col>
        <Col style="flex: 1">
        <slot name="extra"></slot>
        </Col>
      </Row>
      <Table :columns="cols" :data="goodsList" width="1070" ref="goodsTable" :loading="loading"
         height="450" border @on-row-click="selectGoods" :row-class-name="rowClassName"
             @on-select="checkBoxCheck" @on-select-cancel="checkBoxCancel" @on-select-all="selectAll"
             @on-selection-change="cancelAll"></Table>
      <Page :total="totalPage" :current='currentPage' :page-size='pageSize'
            @on-change="changePage"
            @on-page-size-change="changePageSize"
            placement="top"
            show-elevator
            show-total
            show-sizer>
      </Page>
      <div slot="footer">
        <Button   @click="hideModal">取 消</Button>
        <Button  type="primary"  @click="addConfirm">确 认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import goods from '@api/goods.js';
  import ware from '@api/storeRoom.js';
  import apiUtil from '@/api/util';
  import ConfigMixin from '@/mixins/config';
  export default {
    name: "goods-list-modal",
    mixins: [ConfigMixin],
    props: {
      // 保存必须勾选商品
      checkRequired: true,
      customer: {
        type: String,
        default: ''
      },
      uid: {
        type: [Number, String],
        default: null
      },
      storeId: {
        type: [Number, String],
        default: null
      },
      isShowStockStoreId: {
        type: [Number, String],
        default: null
      },
      showModal: {
        type: Boolean,
        default: false
      },
      noInput: {
        type: Boolean,
        default: false
      },
      selectedGoods: {
        type: Array,
        default: []
      },
      modalType: {
        type: String,
        default: 'newGoods'
      },
      params: {
      	type: Object,
        default: () => {}
      }
    },
    data() {
      return {
        filters: {
          no: '',
          category1: '',
          category2: '',
          AreaId:''
        },
        showCollectionOnly: false,
        totalPage: 0,
        currentPage: 1,
        pageSize: 10,
        cols: [],
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          },
          {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          },
          {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          },
          {
            title: "单位",
            key: "unit",
          },
          {
            title: "商品分类",
            key: "category_name",
          },
          {
            title: "起订量",
            key: "order_quantity",
          },
          {
            title: "价格",
            key: "price",
            render: function (h, params) {
              let { row } = params;
              let key = 'price';
              let template = [
                h('span', row[key]),
              ];
              if (goods.isProtocolGoods(row)) {
                template.push(h('span', {
                  class: {
                    'tag-protocol-price': true
                  }
                }, '协'));
              }
              return h('div', template);
            }
          },
          {
            title: "商品别名",
            key: "commodity_alias",
            render: (h, params) => {
              let {row} = params;
              return h('Input', {
                props: {
                  value: row.commodity_alias,
                  disabled: row._disabled
                },
                nativeOn: {
                  'click': () => {
                    this.isFocus = true;
                  }
                },
                on: {
                  'on-change': (event) => {
                    let value = event.target.value;
                    params.row.commodity_alias = value;
                    this.selectedRow[params.index].commodity_alias = params.row.commodity_alias;
                  }
                //   'on-focus': (event) => {
                //     console.log(event)
                //     event.target.focus();
                //   }
                }
              });
            }
          }],
        columns2: [
          {
            type: 'selection',
            width: 60,
            align: 'left',
            className: 'table-select',
          }, {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          }, {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          }, {
            title: "单位",
            key: "unit",
          }, {
            title: "描述",
            key: "summary",
          }, {
            title: "商品分类",
            key: "category_name"
          }],
        columns3: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          }, {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          }, {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          }, {
            title: "单位",
            key: "unit",
          }, {
            title: "商品分类",
            key: "category_name",
            width: 140
          }, {
            title: "仓库",
            key: "re_store_id_name",
          }, {
            title: "库区",
            width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
            key: apiUtil.getIsOpenStoreMGT() ? "re_area_id_name" : '',
          }, {
            title: "库位",
            width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
            key: apiUtil.getIsOpenStoreMGT() ? "re_location_id_name" : '',
          }, {
            title: "价格",
            key: "average_price"
          }, {
            title: "库存",
            key: "existing"
          }, {
            title: '数量',
            align: 'center',
            render: (h, params) => {
              let data = params.row;
              return h('InputNumber', {
                props: {
                  value: parseFloat(data.amount),
                  min: 0,
                  // precision: 2,
                  disabled: data._disabled
                },
                style: {
                  width: '100%'
                },
                nativeOn: {
                  'click': () => {
                    this.isFocus = true;
                  }
                },
                on: {
                  'on-change': (val) => {
                    params.row.amount = val ? val : 0;
                    this.selectedRow[params.index].amount = params.row.amount;
                  },
                  'on-focus': (event) => {
                    event.target.select();
                  }
                }
              });
            }
          }],
        columns4: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          }, {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          }, {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          }, {
            title: "单位",
            key: "unit",
          }, {
            title: "商品分类",
            key: "category_name",
            width: 140
          }, {
            title: "库区",
            width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
            key: apiUtil.getIsOpenStoreMGT() ? "re_area_id_name" : '',
          }, {
            title: "库位",
            width: apiUtil.getIsOpenStoreMGT() ? '' : 1,
            key: apiUtil.getIsOpenStoreMGT() ? "re_location_id_name" : '',
          }, {
            title: "价格",
            key: "average_price"
          }, {
            title: "库存",
            key: "existing"
          }],
        goodsList: [],
        categoryList: [],
        selectedRow: [],
        AreaList: [],
        loading: false,
        newOrders: [],
        isFocus: false,
        addedNum: 0,
      }
    },
    created() {
      goods.getGoodsCategory().then((res) => {
        if (res.status) {
          this.categoryList = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: [],
              loading: false
            }
          });
          this.categoryList.unshift({'value': 0, 'label': '全部分类'});
        }
      });
      switch (this.modalType) {
        case 'newGoods':
          this.cols = this.columns;
          break;
        case 'shieldGoods':
          this.cols = this.columns2;
          break;
        case 'storeGoods':
          if (this.noInput) {
            this.cols = this.columns4;
          } else {
            this.cols = this.columns3;
          }
          break;
      }
    },
    watch: {
      showModal(val) {
        if (val) {
          this.loadGoodsList();
        }
      }
    },
    methods: {
      getAreaList() {
        let params = {
          storage_id: this.storeId
        };
        ware.getStorageAreaList(params).then(res => {
          if (res.status) {
            this.AreaList = [];
            this.AreaList = res.data.list;
            this.filters.AreaId = res.data.list[0].id;
          } else {
            this.$Modal.error({
              title: '错误',
              content: res.message
            });
          }
        })
      },
      //全选取消状态处理
      cancelAll(selection) {
        if (this.addedNum === selection.length) {
          this.selectedRow.forEach((item, index) => {
            if (this.newOrders.length) {
              this.newOrders.forEach((_item, _index) => {
                if (item.id === _item.id && !item._disabled) {
                  this.newOrders.splice(_index, 1);
                }
              });
            }
          });
        }
      },
      // checkbox全选中状态
      selectAll(selection) {
        let me = this;
        this.selectedRow.forEach(item => {
          let selected = false;
          if (this.newOrders.length) {
            this.newOrders.forEach((_item, index) => {
              if (item.id === _item.id) {
                selected = true;
              }
            });
            setTimeout(function () {
              if (!selected && !item._disabled) {
                me.newOrders.push(item);
              }
            }, 100);
          } else {
            if (!selected && !item._disabled) {
              me.newOrders.push(item);
            }
          }
        });
      },
      // CheckBox 单个取消状态
      checkBoxCancel(selection, row) {
        if (this.newOrders.length) {
          this.newOrders.forEach((item, index) => {
            if (item.id === row.id) {
              this.newOrders.splice(index, 1);
            }
          });
        }
      },
      //CheckBox单个选中种状态
      checkBoxCheck(selection, row) {
        let index = this.getIndex(row);
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        if (!checked) {
          this.newOrders.push(this.selectedRow[index]);
        }
      },
      // 获取已经添加的商品在当页所占的数量
      getAddedNum() {
        let num = 0;
        this.goodsList.forEach((item, index) => {
          if (item._checked) {
            num += 1;
          }
        });
        return num;
      },
      //获取行所在的索引
      getIndex(row) {
        let idx = '';
        this.goodsList.forEach((item, index) => {
          if (item.id === row.id) {
            idx = index;
          }
        });
        return idx;
      },
      // 单击行状态处理
      selectGoods(row, index) {
        if (row._disabled) return;
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        if (checked && this.isFocus) {
          this.isFocus = false;
          return;
        }
        selectRow.querySelector('.ivu-checkbox-wrapper').className =
          checked ? "ivu-checkbox-wrapper" : "ivu-checkbox-wrapper ivu-checkbox-wrapper-checked";
        selectRow.querySelector('.ivu-checkbox').className = checked ? "ivu-checkbox" : "ivu-checkbox ivu-checkbox-checked";
        if (this.modalType === 'newGoods' || (this.modalType === 'storeGoods' && !this.noInput)) {
          selectRow.querySelector('.ivu-input').focus();
          selectRow.querySelector('.ivu-input').select();
        }
        if (!checked) {
          this.newOrders.push(this.selectedRow[index]);
        } else {
          if (this.newOrders.length) {
            this.newOrders.forEach((item, index) => {
              if (item.id === row.id) {
                this.newOrders.splice(index, 1);
              }
            });
          }
        }
      },
      rowClassName (row, index) {
        if (row._disabled) {
          return 'table-close-row';
        }
        return 'normalRow';
      },
      hideModal() {
        this.$parent.showGoodsListModal = false;
      },
      changePage(pageNo) {
        this.currentPage = pageNo;
        this.loadGoodsList();
      },
      changePageSize(size) {
        this.pageSize = size;
        this.loadGoodsList();
      },
      changeCategory(value) {
        this.currentPage = 1;
        this.filters.category1 = value[0];
        this.filters.category2 = value[1];
        this.loadGoodsList();
      },
      changeArea(value) {
        this.filters.AreaId = value;
        this.loadGoodsList();
      },
      loadSubCategoryData(item, callback) {
        item.loading = true;
        goods.getGoodsCategory(item.value).then((res) => {
          if (res.status) {
            item.children = res.data.map((_item) => {
              return {
                value: _item.id,
                label: _item.name,
              }
            });
            item.loading = false;
            callback();
          }
        });
      },
      getFilters () {
        return {
          searchValue: this.filters.no, //搜索关键词
          category_id: this.filters.category1, //一级分类id
          category_id2: this.filters.category2, //二级分类id
        }
      },
      async loadGoodsList(page) {
        this.loading = true;
        let params = {
          ...this.params,
          page: this.currentPage,
          pageSize: this.pageSize,
          // area_id: this.filters.AreaId,
          store_id: this.storeId ? this.storeId : '',
          searchValue: this.filters.no, //搜索关键词
          category_id: this.filters.category1, //一级分类id
          category_id2: this.filters.category2, //二级分类id
          is_show_stock: this.modalType === 'storeGoods' ? 1 : 0,
          is_show_stock_store_id : this.isShowStockStoreId ? this.isShowStockStoreId : '',
          is_collection: this.showCollectionOnly ? 1 : 0,
          showAll: this.userCommodityAliasSupportUnit,
        };
        if (this.uid && this.customer !== 'userType') {
          params.user_id = this.uid
        }
        if (this.modalType === 'newGoods') {
          params.is_online = 'Y';
        } else if (this.modalType === 'shieldGoods') {
          if (this.customer === 'userType') {
            params.rece_style = this.uid;
          }
        }
        if (page !== undefined && !isNaN(page)) {
          params.page = page;
        }
        let res = '';
        if (this.modalType === 'storeGoods') {
          res = await ware.ajaxStoreList(params);
        } else {
          res = await goods.getGoodsList(params);
        }
        if (!res.status) {
          this.modalError(res.message);
          return;
        }
        this.loading = false;
        this.goodsList = [];
        let data = res.data;
        let Goods = data.list.map(item => {
          // 筛选已经添加的商品
          item.commodity_alias = '';
          if (this.selectedGoods.length > 0) {
            item._checked = false;
            item._disabled = false;
            this.selectedGoods.forEach(d => {
              if (item.id === d.id || item.id === d.commodity_id) {
                item._checked = true;
                item._disabled = true;
                item.commodity_alias = d.commodity_alias ? d.commodity_alias : '';
              }
            });
          }
          return item;
        });
        // 已经选择的置顶
        Goods.forEach(item => {
          // 筛选已经选取的商品
          this.newOrders.forEach(order => {
            if (item.id === order.id) {
              item._checked = true;
            }
          });
           if (item._disabled) {
             this.goodsList.unshift(item);
           } else {
             this.goodsList.push(item);
           }
        });
        this.selectedRow = this.cloneObj(this.goodsList);
        this.currentPage = parseInt(data.pageParams.page);
        this.totalPage = parseInt(data.pageParams.count);
        this.addedNum = this.getAddedNum();
      },
      addConfirm() {
        if (this.newOrders.length || this.checkRequired === false) {
          this.newOrders.forEach((item) => {
              item.remark = '';
          });
          this.$emit('on-add', this.newOrders);
          this.newOrders = [];
          this.$parent.showGoodsListModal = false;
        } else {
          this.modalError('请选择需要添加的商品');
        }
      }
    }
  }
</script>

<style scoped>
  ._tips {
    text-align: center;
    line-height: 30px;
    color: #03ac54;
  }
</style>
