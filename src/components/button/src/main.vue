<template>
  <div :class="[buttonPrefix]" style="display: inline-block">
    <template v-if="confirm">
      <SdpTableStaticPoptip
        :poptipProps="{
          transfer: true,
          placement: placement,
          offset: offset,
          options: poptipOption,
          title: '',
          confirmType: computeConfirmType,
          transferClassName: 'poptipClass'
        }"
      >
        <div slot="content" :class="[`${buttonPrefix}__content`]">
          <div :class="[`${buttonPrefix}__poptip__title`]" class="poptip-title" v-html="title"></div>
          <div :class="[`${buttonPrefix}__poptip__button__area`]">
            <Button
              @click="handleCancel"
              :class="[`${buttonPrefix}__poptip__button__cancel`]"
              class="poptip-button-cancel"
              >取消</Button
            >
            <Button
              @click="handleOk"
              class="poptip-button-confirm ml10"
              :class="[`${buttonPrefix}__poptip__button__confirm`, 'ml10']"
              :type="computeConfirmType"
              >确定</Button
            >
          </div>
        </div>
        
        <Button 
          :class="{'sdp-button__styleforadd': styleType === 'btnStyleForAdd'}" 
          @click="handleClick" 
          v-click-pulse 
          v-if="type !== 'text'"
        >
          <slot>{{ text }}</slot>
        </Button>
        <span 
          @click="handleClick" 
          v-click-pulse 
          v-if="type === 'text'"
        >
          <slot>{{ text }}</slot>
        </span>
      </SdpTableStaticPoptip>
    </template>
    <template v-if="!confirm">
      <Button
        v-click-pulse
        v-if="type !== 'text'"
        :type="type"
        v-bind="$attrs"
        :class="{
          'sdp-button__btnstylefornormal':
            styleType !== 'btnStyleForAdd' && styleType !== 'btnStyleForMore',
          'sdp-button__styleforadd': styleType === 'btnStyleForAdd',
          'sdp-button__styleformore': styleType === 'btnStyleForMore'
        }"
        @click="handleClick"
      >
        <slot v-if="styleType !== 'btnStyleForMore'">{{ text }}</slot>
        <Icon icon="more" size="sm" v-if="styleType === 'btnStyleForMore'" />
      </Button>
      <span
        v-click-pulse
        v-if="type === 'text'"
        v-bind="$attrs"
        @click="handleClick"
        ><slot>{{ text }}</slot></span
      >
    </template>
  </div>
</template>

<script>
import { Button } from 'view-design';
import SdpTableStaticPoptip from '@/components/standard/sdp-table-static-poptip'
import Icon from '../../icon';
export default {
  // name: 'Button',
  components: {
    SdpTableStaticPoptip,
    Button,
    Icon
  },
  data() {
    return {
      buttonPrefix: 'sdp-button',
      poptipOption: {
        modifiers: {
          offset: {
            offset: '0,0'
          },
          preventOverflow: {
            boundariesElement: 'scrollParent'
          }
        },
        //阻止页面滚动sroll事件频繁触发进行pop重新定位计算
        eventsEnabled: false,
      }
    };
  },
  autoRegister: false,
  props: {
    styleType: {
      type: String,
      default: 'default'
    },
    type: {
      type: String,
      default: 'default'
    },
    confirm: {
      type: Boolean,
      default: false
    },
    // 使用气泡确认弹框时，确认按钮的样式，可选值为：primary(主色)/error(红色)
    confirmType: {
      type: String,
      validator (value) {
        return ['primary', 'error'].includes(value)
      }
    },
    title: {
      type: String,
      default: '确认'
    },
    text: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: 'left'
    },
    beforeAction: {
      type: Function,
      default: () => true
    },
    offset: {
      type: [String, Number],
      default: '0'
    },
    openPoptipOption:{
      type:Boolean,
      default: true
    },
    poptipClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    computeConfirmType () {
      const deleteNameKey = '删除'
      return this.confirmType || ((String(this.text).includes(deleteNameKey) || String(this.title).includes(deleteNameKey)) ? 'error' : 'primary')
    }
  },
  created(){
      if(!this.openPoptipOption){
        // this.poptipOption.modifiers.preventOverflow.boundariesElement = 'window'
        this.poptipOption.modifiers.preventOverflow.enabled = false
      }
  },
  methods: {
    handleOk() {
      document.body.click();
      this.$emit('click');
    },
    handleCancel() {
      document.body.click();
      this.$emit('on-cancel');
    },
    handleClick($event) {
      if (this.beforeAction() !== true) {
        if (this.confirm) {
          $event.preventDefault();
          $event.stopPropagation();
        }
        return false;
      }
      if (!this.confirm) {
        this.$emit('click');
      }
    }
  }
};
</script>

<style lang="less" scoped></style>
