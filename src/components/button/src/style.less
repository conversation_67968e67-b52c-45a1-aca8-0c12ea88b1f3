@btn-prefix: sdp-button;

.@{btn-prefix} {
  & &__content {
    width: 124px;
    height: 73px;
    display: inline-flex;
    justify-content: center;
    flex-wrap: wrap;
  }
  & &__btnstylefornormal {
    font-size: 13px;
    // font-family: @font-family;
    font-weight: 400;
    line-height: 15px;
  }
  & &__styleforadd {
    font-size: 13px;
    color: rgba(3, 172, 84, 1);
    border-radius: 2px;
    border: 1px solid rgba(3, 172, 84, 1);
    line-height: 15px;
  }
  & &__styleforadd[disabled] {
    background: #f7f7f7;
    color: rgba(0, 0, 0, 0.4);
    border: 1px solid rgba(0, 0, 0, 0.4);
  }
  & &__styleformore {
    width: 31px;
    height: 30px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(216, 216, 216, 1);
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #909090;
  }
  .ml10 {
    margin-left: 10px;
  }
}

.ivu-poptip-content {
  .@{btn-prefix}__poptip__title {
    // width: 110px;
    // height: 30px;
    font-size: 13px;
    // font-family: @font-family;
    font-weight: 400;
    color: rgba(48, 48, 48, 1);
    line-height: 18px;
    display: flex;
    align-items: center;
    max-width: 150px;
    justify-content: center;
    white-space: normal;
    padding: 10px 0;

  }
  .@{btn-prefix}__poptip__button__cancel {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 26px;
    background: rgba(255, 255, 255, 1);
    border: 1px solid rgba(216, 216, 216, 1);
    font-size: 13px;
    // font-family: @font-family;
    font-weight: 400;
    color: rgba(48, 48, 48, 1);
    line-height: 13px;
  }
  .@{btn-prefix}__poptip__button__confirm {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 26px;
    border-radius: 2px;
    font-size: 13px;
    // font-family: @font-family;
    font-weight: 400;
    color: rgba(255, 255, 255, 1);
    line-height: 13px;
  }
  .@{btn-prefix}__poptip__button__area {
    // width: 110px;
    height: 43px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .ml10 {
    margin-left: 10px;
  }
}
