<template>
  <div class="amap-page-container" id="user-position">
    <div class="search-container" flex="main:left cross:center">
      <input type="text" id="user-map-search">
      <Button type="primary" class="c-green-btn" @click="confirmLocation">打点保存</Button>
      {{position}}
    </div>
    <div class="amap-demo" id="user-amap" style="height: 100%">
    </div>
  </div>
</template>

<script>
  import 'flex.css';
  import '@/init/init-map.js';
  export default {
    data() {
      return {
        poi: '',
        map: null,
        zoom: 14,
        markers: [],
        searchOption: {
          city: '上海',
          citylimit: true
        },
        lng: 0,
        lat: 0,
        currentPOI: {}
      };
    },
    props: {
      position: {
        type: Object,
      },
      banDot: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      position(position) {
        if (this.map) {
          this.map.clearMap();
        }
        if (this.hasPosition(position)) {
          this.lng = position.longitude;
          this.lat = position.latitude;
          this.setMarker(this.lng, this.lat);
          this.setCenter();
        }
      }
    },
    created() {
    },
    activated() {
      this.judgeMap();
    },
    mounted() {
      this.judgeMap();
      this.timer = setTimeout(() => {
        this.autoSearch();
        this.setPosition();
      }, 900);
    },
    beforeDestroy() {
      this.map.destroy();
      this.map = null;
      if (this.timer) {
        clearTimeout(this.timer)
      }
    },
    methods: {
      // 判断地图是否加载
      judgeMap(){
        /*eslint-disable*/
        if(typeof AMap !== 'undefined'){
          this.initMap();
        }else{
          setTimeout(()=>{
            this.initMap();
          }, 800)
        }
      },
      initMap() {
        let self = this;
        this.map = new AMap.Map('user-amap', {
          zoom: this.zoom
        });
        if (this.lng && this.lng > 0 && this.lat && this.lat > 0) {
          this.setCenter();
        }
        if (this.banDot) {
          return;
        }
        let clickEventListener = this.map.on('click', (e) => {
          this.lng = e.lnglat.getLng();
          this.lat = e.lnglat.getLat();
          self.setMarker(this.lng, this.lat);
        });
      },
      hasPosition(position) {
        return position &&
          position.latitude
          && position.longitude
          && parseInt(position.latitude) !== 0
          && parseInt(position.longitude) !== 0
          && !isNaN(position.latitude)
          && !isNaN(position.longitude);
      },
      setCenter(position) {
        position || (position = [this.lng, this.lat]);
        setTimeout(() => {
          if (!this.map) {
            return;
          }
          this.map.setZoomAndCenter(this.zoom, position);
        }, 500);
      },
      setPosition() {
        /***************************************
         由于Chrome、IOS10等已不再支持非安全域的浏览器定位请求，为保证定位成功率和精度，请尽快升级您的站点到HTTPS。
         ***************************************/
        let map, geolocation;
        //加载地图，调用浏览器定位服务
        map = this.map;
        let self = this;
        map.plugin('AMap.Geolocation', function() {
          geolocation = new AMap.Geolocation({
            enableHighAccuracy: true,//是否使用高精度定位，默认:true
            timeout: 20 * 1000,          //超过10秒后停止定位，默认：无穷大
            buttonOffset: new AMap.Pixel(10, 20),//定位按钮与设置的停靠位置的偏移量，默认：Pixel(10, 20)
            zoomToAccuracy: true,      //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
            buttonPosition:'RB'
          });
          map.addControl(geolocation);
          geolocation.getCurrentPosition();
          AMap.event.addListener(geolocation, 'complete', onComplete);//返回定位信息
          AMap.event.addListener(geolocation, 'error', onError);      //返回定位出错信息
        });
        //解析定位结果
        function onComplete(data) {
          if (!self.lat || !self.lng || self.lat == '0' || self.lng == '0') {
            self.setCenter([data.position.getLng(), data.position.getLat()]);
          }
        }
        //解析定位错误信息
        function onError(data) {
          //实例化城市查询类
          let citysearch = new AMap.CitySearch();
          //自动获取用户IP，返回当前城市
          citysearch.getLocalCity(function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              if (result && result.city && result.bounds) {
                let citybounds = result.bounds;
                //地图显示当前城市
                map.setBounds(citybounds);
              }
            } else {
              console.error("定位失败,status:" + status + ", error:" + JSON.stringify(result))
            }
          });
        }
      },
      autoSearch() {
        //输入提示
        let self = this;
        let autoOptions = {
          input: "user-map-search"
        };
        let auto = new AMap.Autocomplete(autoOptions);
        let placeSearch = new AMap.PlaceSearch({
          map: this.map
        });  //构造地点查询类
        AMap.event.addListener(auto, "select", select);//注册监听，当选中某条记录时会触发
        AMap.event.addListener(placeSearch, "selectChanged", this._changePOI);//注册监听，当搜索结果中选中的POI改变时触发
        function select(e) {
          placeSearch.setCity(e.poi.adcode);
          placeSearch.search(e.poi.name, (status, result) => {
            if (status === 'complete' && result.info === 'OK') {
              if (result && result.poiList && result.poiList.pois && result.poiList.pois.length > 0) {
                let poi = result.poiList.pois[0];
                self.poi = poi;
                const { name, address, district } = e.poi
                const { lng, lat } = poi.location
                self.lng = lng;
                self.lat = lat;
                self.map.setCenter([lng, lat]);
                self.currentPOI = { name, address, district: district || '', selected: true }
              }
            } else {
              console.error("查询失败,status:" + status + ", error:" + JSON.stringify(result))
            }
          });
        }
      },
      _changePOI (e) {
        const { location } = e.selected.data
        this.lng = location.lng;
        this.lat = location.lat;
      },
      confirmLocation() {
        if (!this.lng || !this.lat) {
          this.$Message.error('请选择地点');
          return;
        }
        this.$emit('on-complete', {
          longitude: this.lng,
          latitude: this.lat,
          poi: this.poi,
          address: !(this.currentPOI.address || this.currentPOI.district)? '' : `${this.currentPOI.district}${this.currentPOI.address}`,
        })
      },
      /**
       * @description 添加marker
       * <AUTHOR>
       */
      setMarker: function (lng, lat) {
        this.map.clearMap();
        new AMap.Marker({
          map: this.map,
          position: [lng, lat],
        });
      }
    }
  }
</script>

<style lang="less">
  #user-map-search {
    z-index: 1000;
    width: 250px;
  }
</style>

<style lang="scss" scoped>
  .amap-demo {
    .search-box {
      position: absolute;
      top: 25px;
      left: 20px;
    }
    .amap-page-container {
      position: relative;
    }
  }

  .search-container {
    position: absolute;
    top: 75px;
    left: 35px;
    .ivu-btn {
      z-index: 255;
      margin-left: 15px;
    }
  }

  .vl-notify .vl-notify-content {
    padding: 0 !important;
  }

  .amap-page-container {
    .search-box {
      height: 35px;
    }
    .search-box-wrapper {
      .search-btn {
        color: #495060 !important;
      }
    }
  }
</style>
