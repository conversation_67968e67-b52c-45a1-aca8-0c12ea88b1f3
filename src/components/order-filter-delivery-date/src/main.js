import DateUtil from '@/util/date';
import { DatePicker } from 'view-design';
import Tooltip from '@components/base/tooltip';
import Bus from '@api/bus.js';
import ConfigMixin from '@/mixins/config.js'
import StorageUtil from '@util/storage.js'

export default {
  mixins:[ConfigMixin],
  components: {},
  props: {
    rangeConfig: {
      type: [Number, String],
      default: 0
    },
    setDefaultStartTime: {
      type: [String, Date],
      default: undefined
    },
    setDefaultEndTime: {
      type: [String, Date],
      default: undefined
    }
  },
  data() {
    return {
      isDeliveryDaysValid: true,
      startTime: '',
      endTime: '',
      openDate: false,
      defaultStartTime: '',
      defaultEndTime: ''
    };
  },
  watch: {
    rangeConfig() {
      this.init();
    },
    maxOrderDeliveryDateDays() {
      this.init();
    },
    setDefaultEndTime: {
      deep: true,
      handler(n) {
        this.defaultEndTime = n
        this.init();
      }
    }
  },
  methods: {
    resetValue() {
      this.startTime = this.defaultStartTime;
      this.endTime = this.defaultEndTime;
    },
    init() {
      let defaultEndTime = DateUtil.getTomorrow();
      let defaultStartTime = DateUtil.subTract(28, defaultEndTime);
      this.defaultStartTime = this.setDefaultStartTime !== undefined ? this.setDefaultStartTime : defaultStartTime;
      this.defaultEndTime = this.setDefaultEndTime !== undefined ? this.setDefaultEndTime : defaultEndTime;
      this.startTime = this.defaultStartTime;
      this.endTime = this.defaultEndTime;
      if (this.maxOrderDeliveryDateDays !== 0 && this.endTime) {
        defaultStartTime = DateUtil.subTract(
          this.maxOrderDeliveryDateDays - 1,
          this.endTime
        );
        this.startTime = defaultStartTime;
      }
      const filterConfig = StorageUtil.getLocalStorage('order_list_01_filter_config')
      if(filterConfig && filterConfig.length>0 && this.endTime) {
        const deliveryDateRangeConfig = filterConfig.find(item => item.key === 'delivery_date_filter_range')
        if (deliveryDateRangeConfig) {
          const value = deliveryDateRangeConfig.value || 30
          defaultStartTime = DateUtil.subTract(
            value - 1,
            this.endTime
          );
          this.startTime = defaultStartTime;
        }
      }
      
      // 这个发货日期太绕了, 业务页面写一套, 组件里再写一套, 骚就骚点, 再在屎上继续堆把
      if (this.$route.query.from === 'otherPage' && this.$route.query.startTime && this.$route.query.endTime) {
        this.startTime = this.$route.query.startTime
        this.endTime = this.$route.query.endTime
      }

      this.emitChange();
    },
    // 选择日期
    checkDate: function(res) {
      // 开启了发货日期天数选择限制
      if (
        this.maxOrderDeliveryDateDays &&
        Number(this.maxOrderDeliveryDateDays) > 0
      ) {
        if (
          !res ||
          !res[0] ||
          DateUtil.diffDay(res[0], res[1]) + 1 - this.maxOrderDeliveryDateDays >
            0
        ) {
          this.isDeliveryDaysValid = false;
          this.$forceUpdate();
          return false;
        }
      }
      this.isDeliveryDaysValid = true;
      this.openDate = false;
      this.startTime = res[0];
      this.endTime = res[1];
      this.emitChange();
    },
    getDefaultValue() {
      return [this.defaultStartTime, this.defaultEndTime];
    },
    emitChange() {
      this.$emit('on-change', [this.startTime, this.endTime]);
    },
    $_onTabsChange () {
      this.openDate = false;
    }
  },
  created() {
    this.init();
    Bus.$on('tabsChange', this.$_onTabsChange);
  },
  beforeDestroy() {
    Bus.$off('tabsChange', this.$_onTabsChange);
    this.openDate = false;
  },
  deactivated() {
    this.openDate = false;
  },
  render() {
    const dateTemplate = (
      <DatePicker
        style="display: block; width: 100%;"
        value={[this.startTime, this.endTime]}
        options={	DateUtil.dateQuickOptions }
        onOn-change={date => this.checkDate(date)}
        nativeOnClick={() => {
          this.openDate = true;
          this.isDeliveryDaysValid = true;
        }}
        transfer={true}
        // 会有展示问题，先去掉
        // open={this.openDate}
        type="daterange"
        placeholder="请选择发货日期"
      />
    );
    if (!this.isDeliveryDaysValid) {
      return (
        <Tooltip
          style="display: block; width: auto;"
          always={true}
          theme="danger"
          placement="top"
          max-width={300}
          content={`一次最多可筛选${this.maxOrderDeliveryDateDays}天数据，如需一次查看超过${this.maxOrderDeliveryDateDays}天数据，您可尝试导出Excel后查看`}
        >
          {dateTemplate}
        </Tooltip>
      );
    }
    return dateTemplate;
  }
};
