<template>
  <div>
    <Modal
      title='在线图库'
      v-model="showModal"
      @on-cancel="handleCancel"
      width="800">
      <Tabs @on-click='changeTabs' v-model="tab.current">
        <TabPane v-for='list in imgCategoryList' :key='list.id' :label="list.name" :name="list.id"></TabPane>
      </Tabs>
      <div class="cloud-img">
        <Row type="flex" class="img-list" :class="listClass">
          <Col
            align="center"
            class="cloud-img-item"
            v-for='list in imgList'
            :style="itemStyle"
            :key='list.id'>
          <div class="cloud-img-item-content">
            <img :src="list.upyun_url.split('!')[0]">
            <div class="bgcover">
              <p class="import-btn" @click='importImg(list)'>导入</p>
            </div>
          </div>
          </Col>
        </Row>
        <div style="line-height: 400px; text-align: center;" v-if='isData'>暂无数据</div>
        <Spin size="large" v-show='tbLoad' fix></Spin>
      </div>
      <Page
        :page-size-opts='pageSizeOpts'
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        :total="+pageParams.count"
        :current='+pageParams.page'
        :page-size='+pageParams.page_size'
        placement='top'
        show-total
        show-elevator
        show-sizer></Page>
      <div slot="footer">
      </div>
    </Modal>
  </div>
</template>

<script>
  import Shop from '@api/shop.js';
  import cloudId from '@/components/mixin/cloudToken.js';
  import {
    categoryType,
    imgType,
    subject
  } from './util';
  export default {
    name: "cloudImg",
    props: {
      rowCount: {
        type: [String, Number],
        default: 3
      },
      itemGutter: {
        type: [String, Number],
        default:  10
      },
      type: {
        type: [String, Number],
        default: imgType.normal.value
      },
      show: {
        type: Boolean,
        default: false
      },
      pageSizeOpts: {
        type: Array,
        default: () => [9, 18, 27, 36]
      },
      defaultCategory: {
        type: [String, Number],
        default: ''
      },
      orderRevert: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      show(newValue) {
        this.showModal = newValue;
        if (this.showModal) {
          this.initData();
        }
      }
    },
    data() {
      return {
        showModal: false,
        imgType,
        category_id: '',
        cloud_token: '',
        tab: {
          current: ''
        },
        pageParams: {
          count: "0",
          page: 1,
          page_size: this.pageSizeOpts[0]
        },
        imgList: [],
        imgCategoryList: [],
        tbLoad: false,
        isData: false,
        importCloudModel: false
      }
    },
    computed: {
      itemStyle() {
        let rowCount = this.rowCount;
        if (this.type !== imgType.icon.value) {
          rowCount = 3;
          let currentCategory = this.imgCategoryList.find((cate) => cate.id === this.category_id);
          if (currentCategory) {
            switch (currentCategory.name) {
              case subject.two.label:
              case subject.three.label:
                rowCount = 4;
                break;
            }
          }
        }
        let style = {
          width: `${100 / rowCount}%`,
          padding: this.itemGutter + 'px'
        };
        return style;
      },
    },
    created() {
      this.showModal = this.show;
      if (this.showModal) {
        this.initData();
      }
    },
    mixins: [cloudId],
    methods: {
      listClass() {
        let listClass = 'banner-list';
        switch(this.type) {
          case imgType.icon.value:
            listClass = 'icon-list';
            break;
        }
        return listClass;
      },
      handleCancel() {
        this.$emit('on-close');
      },
      initData() {
        this.getCloudToken().then(res => {
          if (res == 1) {
            this.cloud_token = sessionStorage.getItem('cloudToken');
            let filters = this.getCategoryFilters();
            this.getCategoryList(filters).then(res => {
              if (res.success) {
                res.result.unshift({
                  id: '',
                  name: '全部分类'
                });
                this.imgCategoryList = res.result;
                this.setDefaultCategory();
              }
            });

          } else {
            this.modalError(res);
          }
        });
      },
      setDefaultCategory() {
        if (!this.defaultCategory) {
          this.getCloudImgList();
          return
        }
        let defaultCategory = this.imgCategoryList.find((cate) => cate.name === this.defaultCategory);
        if (defaultCategory) {
          this.category_id = defaultCategory.id;
          if (this.orderRevert) {
            this.tab.current = this.imgCategoryList[this.imgCategoryList.length - 1].id;
            this.$nextTick(() => {
              this.tab.current = defaultCategory.id;
              this.getCloudImgList();
            })
          } else {
            this.tab.current = defaultCategory.id;
            this.getCloudImgList();
          }
        }
      },
      getCategoryFilters() {
        let filters = {
          category_type: categoryType.normal.value
        };
        if (this.type === imgType.icon.value) {
          filters.category_type = categoryType.icon.value;
        }
        return filters;
      },
      getImgFilters() {
        let filters = {
          type: imgType.normal.value
        };
        if (this.type === imgType.icon.value) {
          filters.type = imgType.icon.value;
        }
        return filters;
      },
      changePage(page) {
        this.pageParams.page = page;
        this.getCloudImgList();
      },
      changePageSize(s) {
        this.pageParams.page_size = s;
        this.getCloudImgList();
      },
      resetPage() {
        this.pageParams.page = 1;
      },
      changeTabs(id) {
        this.category_id = id ? id : '';
        this.resetPage();
        this.getCloudImgList();
      },
      importImg(data) {
        data.upyun_url = data.upyun_url.split('!')[0];
        this.$Modal.confirm({
          title: '提示',
          content: '确定要导入这张图片吗？',
          loading: true,
          onOk: () => {
          	this.successNotice('添加图片成功');
            this.$emit('on-import', data.upyun_url, data);
            this.$Modal.remove();
          }
        });
      },
      getCloudImgList() {
        this.isData = false;
        this.tbLoad = true;
        this.imgList = [];
        let defaultFilters = this.getImgFilters();
        let params = {
          ...defaultFilters,
          category_id: this.category_id,
          cloud_token: this.cloud_token,
          page: this.pageParams.page,
          page_size: this.pageParams.page_size
        };

        Shop.imageGalleryList(params).then(res => {
          if (res.success) {
            this.imgList = res.result.list;
            if (this.imgList.length == 0) this.isData = true;
            this.pageParams = res.result.pageParams;
          } else {
            this.modalError(res.msg);
          }
          this.tbLoad = false;
        });
      },
      getCategoryList(filters) {
        filters = {
          ...filters,
          cloud_token: this.cloud_token,
        };
        return Shop.categoryList(filters);
      },
    }
  }
</script>

<style scoped>
  .modify-btn {
    display: inline-block;
  }
  .modify-btn >>> .ivu-upload-drag {
    border: none;
    background-color: transparent;
  }
</style>

<style scoped lang="less">
  @iconCount: 8;
  @bannerCount: 3;
  .cloud-img {
    height: 420px;
    overflow: scroll;
    position: relative;
    .img-list {
      .cloud-img-item {
        .cloud-img-item-content {
          position: relative;
          img {
            width: 100%;
          }
          .bgcover {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(120, 120, 120, 0.9);
            display: none;
            .import-btn {
              cursor: pointer;
              width: 100%;
              background-color: #1b1010;
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              padding: 10px;
              text-align: center;
              color: #fff;
              font-size: 16px;
            }
          }
          &:hover {
            .bgcover {
              display: block;
            }
          }
        }
      }
      &.icon-list {
      }
      &.banner-list {
      }
    }
  }
</style>
