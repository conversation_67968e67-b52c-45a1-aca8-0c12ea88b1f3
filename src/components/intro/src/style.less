@intro-prefix: s-intro;
@arrow-color: #30d2a2;
@arrow-size: 6px;

.@{intro-prefix} {
  position: absolute;
  width: 278px;
  z-index: @zindex-intro;
  top: 0;
  display: none;
  line-height: normal;
  background: linear-gradient(
    90deg,
    rgba(53, 213, 158, 1) 0%,
    rgba(16, 192, 188, 1) 100%
  );
  box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
  border-radius: 4px;
  color: #fff;
  &::before {
    content: '';
    border: @arrow-size solid;
    border-color: transparent transparent @arrow-color transparent;
    position: absolute;
  }
  &.bottom-left,
  &.bottom-right {
    &::before {
      top: -@arrow-size * 2;
    }
  }
  &.bottom-left {
    &::before {
      left: @arrow-size * 2;
    }
  }
  &.bottom-right {
    &::before {
      right: @arrow-size * 2;
    }
  }
  &.top-left,
  &.top-right {
    &::before {
      bottom: -@arrow-size * 2;
      border-color: @arrow-color transparent transparent transparent;
    }
  }
  &.top-left {
    &::before {
      left: @arrow-size * 2;
    }
  }
  &.top-right {
    &::before {
      right: @arrow-size * 2;
    }
  }
  &.left-top,
  &.left-bottom {
    &::before {
      right: -@arrow-size * 2;

      border-color: transparent transparent transparent @arrow-color;
    }
  }
  &.left-top {
    &::before {
      top: @arrow-size * 2;
    }
  }
  &.left-bottom {
    &::before {
      bottom: @arrow-size * 2;
    }
  }
  &.right-top,
  &.right-bottom {
    &::before {
      left: -@arrow-size * 2;
      border-color: transparent @arrow-color transparent transparent;
    }
  }
  &.right-top {
    &::before {
      top: @arrow-size * 2;
    }
  }
  &.right-bottom {
    &::before {
      bottom: @arrow-size * 2;
    }
  }
  &.@{intro-prefix}--show {
    display: block;
  }
  &__mask {
    position: fixed;
    z-index: @zindex-intro - 1;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.3);
  }
  &__window {
    position: absolute;
    z-index: @zindex-intro - 1;
    background-color: rgba(255, 255, 255, 0.3);
  }
  &__wrap {
    padding: 15px 20px;
  }
  &__hd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 7px;
    h5 {
      font-size: 14px;
      line-height: 18px;
      margin: 0;
    }
  }
  &__bd {
    p {
      font-size: 13px;
      line-height: 20px;
    }
  }
  &__ft {
    background: rgba(0, 0, 0, 0.08);
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .s-button {
      background: rgba(255, 255, 255, 0.2);
      color: #fff;
      border: none;
    }
  }
  &__dots {
    i {
      display: inline-block;
      width: 6px;
      height: 6px;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 50%;
      margin-right: 6px;
      vertical-align: middle;
      &.cur {
        width: 8px;
        height: 8px;
        background: #fff;
      }
    }
  }
}
