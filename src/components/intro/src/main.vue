<template>
  <div
    class="s-intro"
    :class="[isShow ? 's-intro--show' : null, position]"
    :style="styles"
  >
    <div class="s-intro__wrap">
      <div class="s-intro__hd">
        <h5>{{ title }}</h5>
        <s-icon icon="close" :size="12" @click="close" />
      </div>
      <div class="s-intro__bd">
        <p>{{ content }}</p>
      </div>
    </div>
    <div class="s-intro__ft">
      <div class="s-intro__dots" v-if="total > 1">
        <i
          :key="item"
          v-for="item in total"
          :class="{ cur: index + 1 === item }"
        ></i>
      </div>
			<!--	用来占位		-->
			<div v-if="total <= 1"></div>
      <s-button @click="next" size="mini" circle>{{
        total > index + 1 ? '下一步' : '我知道了'
      }}</s-button>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import SButton from '../../button';
import SIcon from '../../icon';
export default {
  name: 's-intro',
  components: { SButton, SIcon },
  props: {
    title: {
      type: String,
    },
    content: {
      type: String,
    },
    total: {
      type: Number,
    },
    index: {
      type: Number,
    },
    position: {
      type: String,
    },
    styles: {
      type: Object,
    },
  },
  data() {
    return {
      isShow: false,
    };
  },
  created() {},
  watch: {},
  computed: {},
  methods: {
    next() {
      this.$sintro.next();
    },
    prev() {
      this.$sintro.prev();
    },
    close() {
      this.$sintro.close();
    },
    hide() {
      this.isShow = false;
    },
    show() {
      this.isShow = true;
    },
  },
};
</script>
