<template>
  <div>
  <CommonSelect
    v-model="selfValue"
    @on-change="onChange"
    :url="apiUrl.shopNotice.list"
    :multiple="multiple"
    :disabled="disabled"
    :all-label="allLabel"
    :all-value="allValue"
    :filterable="false"
    :clearable="false"
    :show-all="false"
    label-key="main_title"
    value-key="id"
    placeholder="选择公告"></CommonSelect>
    </div>
</template>

<script>
  import SelectMixin from '@/mixins/select'
  export default {
    name: "NoticeSelect",
    autoRegister: true,
    mixins: [SelectMixin],
    data () {
      return {}
    },
  }
</script>

<style scoped>

</style>
