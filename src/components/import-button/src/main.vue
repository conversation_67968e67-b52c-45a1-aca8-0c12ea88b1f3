<template>
  <div class="import-box">
    <!--导入按钮1-->
    <Button @click="toggleDialog"><slot>批量导入</slot></Button>
    <!--导入弹出框-->
    <Modal
      :class-name="'modal-for-importBtn ' + modalClassName"
      v-model="show"
      :closable="false"
      :title="title"
    >
      <div class="import-box__dialog__header" slot="header">
        <div class="import-box__dialog__border"></div>
        {{ title }}
        <span class="import-box__dialog__delete" @click="cancel">
          <Icon icon="close" class="import-box__dialog__delete__icon" />
        </span>
      </div>
      <div class="import-box__dialog__body">
        <div
          v-if="
            this.$slots['custom-area-left'] || this.$slots['custom-area-right']
          "
          class="import-box__dialog__body__import__description marginT20"
        >
          <div class="import-box__dialog__body__download__text__left">
            <slot
              v-if="this.$slots['custom-area-left']"
              name="custom-area-left"
            ></slot>
          </div>
          <div v-if="this.$slots['custom-area-right']">
            <slot name="custom-area-right"></slot>
          </div>
        </div>
        <slot name="extra-items"></slot>
        <div
          class="import-box__dialog__body__download__description margin20"
          v-if="(download.text && download.url) || Array.isArray(download)"
        >
          <span class="import-box__dialog__body__download__text__left"
            >下载模板：</span
          >
          <span class="ml12">点击下载 </span>
          <template v-if="Array.isArray(download)">
            <span
              v-for="(item, index) in download"
              :key="index"
              @click="downloadTemplateForMutiStyle(item)"
              class="import-box__dialog__body__download__text"
              >{{ item.text }}</span>
          </template>
          <template v-else>
            <span
              @click="downloadTemplate"
              class="import-box__dialog__body__download__text"
              >{{ download.text }}</span>
          </template>
          </div>
        <div class="import-box__dialog__body__import__description margin20">
          <span class="import-box__dialog__body__download__text__left"
            >选择上传文件：</span
          >
          <Upload
            class="import-box__dialog__body__import__upload__box ml12"
            v-bind="$attrs"
            :action="post.url"
            :show-upload-list="false"
            :on-format-error="onFormatError"
            :on-error="onError"
            :on-success="onSuccess"
            :on-exceeded-size="onExceededSize"
            :on-visible-change="onVisibleChange"
            :on-progress="onProgress"
            :before-upload="beforeUpload"
            :mask-closable="false"
            :accept="post.accept"
            :format="post.format"
            :max-size="post.maxSize"
          >
            <Button
              :disabled="importing"
              :loading="loadingStatus"
              class="import-box__dialog__body__select"
              type="primary"
              >{{ importing ? '导入中...' : '选择文件' }}</Button
            >
          </Upload>
          <span
            v-show="fileName"
            class="import-box__dialog__body__import__file__name ml12"
          >
            {{ fileName }}
            <span @click="removeFile">
              <Icon
                icon="solid-close"
                size="mini"
                class="import-box__dialog__body__delete__file__icon"
              />
            </span>
          </span>
        </div>
        <slot name="custom-error-tip" :error-table="errorTable">
          <div
            v-show="errorTip"
            class="import-box__dialog__body__error__description mt14 mb27"
          >
            <span class="import-box__dialog__body__download__text__left"></span>
            <span class="ml12">{{ errorTip }}</span>
          </div>
        </slot>
      </div>
      <div class="import-box__dialog__footer" slot="footer">
        <Button @click="cancel" class="import-box__dialog__footer__cancel"
          >取消</Button
        >
        <Button
          @click="confirm"
          class="import-box__dialog__footer__confirm"
          type="primary"
          >确定</Button
        >
      </div>
    </Modal>
  </div>
</template>
<script>
import { Modal, Upload } from 'view-design';
import Button from '../../button/index';
import Icon from '../../icon/index';
import { importLoop } from '@/components/common/import-btn/util.js'


export default {
  components: {
    Button,
    Modal,
    Upload,
    Icon
  },
  props: {
    // 弹出框标题
    title: {
      required: false,
      type: String,
      default: () => {
        return '导入';
      }
    },
    // 下载模板
    download: {
      required: false,
      type: [Object, Array],
      default: () => {
        return {
          url: '',
          text: ''
        };
      }
    },
    // 上传相关
    post: {
      required: true,
      type: Object,
      default: () => {
        return {
          url: '',
          accept: '',
          maxSize: ''
        };
      }
    },
    // 弹框自定义类名
    modalClassName: {
      required: false,
      type: String,
      default: () => ''
    },
    // 是否离线导入
    offline:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      createDownloadUrlFnExecuting: false,
      importSuccess: false,
      importing: false,
      show: false,
      errorTip: '',
      errorTable: [],
      fileName: '',
      file: '',
      loadingStatus: false,
      upload: () => {}
    };
  },
  watch: {},
  computed: {},
  created() {},
  methods: {
    // 显示、隐藏弹出框
    toggleDialog() {
      this.fileName = '';
      this.errorTip = '';
      this.errorTable = [];
      this.show = !this.show;
    },
    // 下载模板
    async downloadTemplate() {
      
      if (!this.download.url && !this.download['before-download']) return;
      let url = '';
      if (this.download['before-download']) {
        if (this.createDownloadUrlFnExecuting) return;
        this.createDownloadUrlFnExecuting = true;
        url = await new Promise((resolve, reject) => {
          this.download['before-download'](resolve,reject);
        }).catch(()=>{
          this.createDownloadUrlFnExecuting = false;
        });
        this.createDownloadUrlFnExecuting = false;
        if (!url) {
          return;
        }
      } else {
        url = this.download.url;
      }
      window.location.href = url;
    },
    async downloadTemplateForMutiStyle(item) {
      if (!item.url && !item['before-download']) return;
      let url = '';
      if (item['before-download']) {
        if (this.createDownloadUrlFnExecuting) return;
        this.createDownloadUrlFnExecuting = true;
        url = await new Promise((resolve, reject) => {
          item['before-download'](resolve,reject);
        }).catch(()=>{
          this.createDownloadUrlFnExecuting = false;
        });
        this.createDownloadUrlFnExecuting = false;
        if (!url) {
          return;
        }
      } else {
        url = item.url;
      }
      window.location.href = url;
    },
    // 点击确定
    confirm() {
      if (!this.file) {
        this.errorTip = '请选择文件';
        return;
      }
      if (this.errorTip) {
        return;
      }
      this.upload();
      this.loadingStatus = true;
    },
    // 点击取消
    cancel() {
      if (this.importing) {
        return false;
      }
      this.removeFile();
      this.show = false;
    },
    // 文件格式校验失败
    onFormatError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `文件格式校验失败，请上传 ${this.post.format +
        ''} 文件。`;
    },
    // 文件上传失败
    onError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件失败，请重试。`;
    },
    // 文件超过大小限制
    onExceededSize() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件超过大小限制，文件不能超过 ${this.post.maxSize}kb。`;
    },
    // 文件开始上传
    onProgress() {
      this.importing = true;
      this.importSuccess = false;
    },
    beforeUpload(file) {
      this.fileName = file.name;
      this.file = file;
      this.errorTip = '';
      this.errorTable = [];
      return new Promise(resolve => {
        this.upload = resolve;
      });
    },
    // 文件上传成功
    onSuccess(response) {
      this.importing = false;
      this.loadingStatus = false;
      let { status, message, data } = response;
      if (status) {
        this.importSuccess = true;
        this.errorTip = '';
        this.errorTable = [];
        this.removeFile();
        this.show = false;
        if(this.offline){
          this.infoMessage(message || '操作成功，导入任务执行中')
          importLoop(data.exec_no)
        }else{
          this.$emit('on-completed', this.importSuccess);
        }
      } else {
        if (response.data && Number(response.data.failure_download) === 1) {
          const noticeConfig = {
            type: 'error',
            title: `商品导入失败`,
            text: response.message || '',
            duration: 0,
            btnTxt: '下载表格',
            btnClick: () => {
              window.open(response.data.failure_download_uri || '', '_self');
            }
          };
          this.show = false;
          this.$snotice(noticeConfig);
          return;
        }
        this.importSuccess = true;
        this.errorTip = response.message || '';
        this.errorTable = response.data || [];
      }
    },
    onVisibleChange() {
      this.fileName = '';
      this.errorTip = '';
      this.errorTable = []
    },
    removeFile() {
      this.fileName = '';
      this.file = '';
      this.errorTip = '';
      this.errorTable = [];
      this.loadingStatus = false;
    }
  },
  mounted() {}
};
</script>
<style lang="less" scoped>
@box-prefix: import-box;

.@{box-prefix} {
  display: inline-block;
}

.ivu-modal-wrap {
  .margin20 {
    margin: 20px 0;
  }
  .marginT20 {
    margin-top: 20px;
  }
  .ml12 {
    margin-left: 12px;
  }
  .mt14 {
    margin-top: 14px;
  }
  .mb27 {
    margin-bottom: 27px;
  }
  .@{box-prefix}__dialog__header {
    font-size: 14px;
    color: #303030;
    text-align: left;
    line-height: 14px;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center;
    font-weight: 500;
  }
  .@{box-prefix}__dialog__border {
    width: 3px;
    height: 12px;
    background: #505050;
    border-radius: 0.5px;
    border-radius: 0.5px;
    display: inline-block;
    margin-left: 10px;
    margin-right: 6px;
  }
  .@{box-prefix}__dialog__delete {
    position: absolute;
    right: 25px;
  }
  .@{box-prefix}__dialog__delete__icon {
    font-size: 10px;
    cursor: pointer;
  }
  .@{box-prefix}__dialog__body {
    margin: -16px;
    font-weight: 500;
    &__import__description {
      width: 100%;
      display: inline-flex;
      align-items: center;
    }
    &__download__description {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      margin-bottom: 0px;
    }
    &__download__text__left {
      display: inline-block;
      width: 23%;
      text-align: right;
    }
    &__download__text {
      color: #03ac54;
      cursor: pointer;
      margin-right: 15px;
    }
    &__import__upload__box {
      display: inline-block;
    }
    &__select {
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
    }
    &__import__file__name {
      font-size: 13px;
      color: #505050;
      text-align: left;
      line-height: 14px;
      display: inline-flex;
      align-items: center;
      width: 260px;
      word-break: break-all;
    }
    &__delete__file__icon {
      margin-left: 16px;
      color: rgba(0, 0, 0, 0.2);
      cursor: pointer;
    }
    &__error__description {
      font-size: 13px;
      color: red;
      text-align: left;
      line-height: 14px;
    }
  }
  .@{box-prefix}__dialog__footer {
    &__cancel {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      background: #ffffff;
      // border: 1px solid #d8d8d8;
      border-radius: 2px;
      border-radius: 2px;
      margin-right: 4px;
    }
    &__confirm {
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
    }
  }
}
/deep/ .row-item {
  display: flex;
  align-items: center;
}
/deep/ .row-item__label {
  width: 23%;
  text-align: right;
}
/deep/ .row-item__content {
  flex: 1;
  padding-left: 12px;
}
</style>
