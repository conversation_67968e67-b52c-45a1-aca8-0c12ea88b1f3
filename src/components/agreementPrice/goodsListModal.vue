<template>
  <div>
    <Modal v-model="showModal" title="请选择商品" :mask-closable="false" :closable="false"
           class-name="vertical-center-modal" width="1100">
      <Row class="selectRow mb10" style="margin: 0 0 16px 0!important;" type="flex" justify="start" :gutter="10">
        <Col span="6" style="padding-left: 0;">
        <Cascader :data="categoryList" change-on-select :load-data="loadSubCategoryData" clearable
                  placeholder="全部分类" @on-change="changeCategory" style="width: 100%"></Cascader>
        </Col>
        <Col span="6">
        <Input placeholder="请输入商品名称/编码进行搜索" v-model="filters.no" @on-enter="loadGoodsList(1)" clearable>
        <Button slot="append" icon="ios-search" @click="loadGoodsList(1)"></Button>
        </Input>
        </Col>
        <Col span="6" style="margin-left: auto;padding: 0;">
        <p class="_tips">小贴士：鼠标单击表格行即可直接选中或修改</p>
        </Col>
      </Row>
      <Table :columns="columns" :data="goodsList" ref="goodsTable" :loading="loading"
             height="450" border @on-row-click="selectGoods" :row-class-name="rowClassName"
             @on-select="checkBoxCheck" @on-select-cancel="checkBoxCancel" @on-select-all="selectAll"
             @on-selection-change="cancelAll"
             v-if="modalType === 'newGoods'"></Table>
      <!--添加屏蔽商品-->
      <Table :columns="columns2" :data="goodsList"  ref="goodsTable" :loading="loading"
             height="450" border @on-row-click="selectGoods" :row-class-name="rowClassName"
             @on-select="checkBoxCheck" @on-select-cancel="checkBoxCancel" @on-select-all="selectAll"
             @on-selection-change="cancelAll" v-if="modalType === 'shieldGoods'"></Table>
      <!--添加屏蔽商品-->
      <Page :total="totalPage" :current='currentPage' :page-size='pageSize'
            @on-change="changePage"
            @on-page-size-change="changePageSize"
            placement="top"
            show-elevator
            show-total
            show-sizer>
      </Page>
      <div slot="footer">
        <Button   @click="hideModal">取 消</Button>
        <Button  type="primary"  @click="addConfirm">确 认</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import goods from '@api/goods.js';
  export default {
    name: "goods-list-modal",
    props: {
      showModal: {
        type: Boolean,
        default: false
      },
      selectedGoods: {
        type: Array,
        default: []
      },
      params: {
        type: Object,
        default: () => {}
      },
      modalType: {
        type: String,
        default: 'newGoods'
      }
    },
    data() {
      return {
        filters: {
          no: '',
          category1: '',
          category2: ''
        },
        totalPage: 0,
        currentPage: 1,
        pageSize: 10,
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          }, {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          }, {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          },
          {
            title: "单位",
            key: "unit",
          },
          {
            title: "商品分类",
            key: "category_name",
          },
          {
            title: "起订量",
            key: "order_quantity",
          },
          {
            title: "最近一次进货价",
            key: "in_price"
          },
          {
            title: "市场价",
            key: "price"
          },
          {
            title: '协议价',
            align: 'center',
            render: (h, params) => {
              let data = params.row;
              return h('InputNumber', {
                props: {
                  value: data.agreement_price ? parseFloat(data.agreement_price) : null,
                  min: 0,
                  // precision: 2,
                  disabled: data._disabled
                },
                style: {
                  width: '100%'
                },
                nativeOn: {
                  'click': () => {
                    this.isFocus = true;
                  }
                },
                on: {
                  'on-change': (val) => {
                    params.row.agreement_price = val;
                    this.selectedRow[params.index].agreement_price = val;
                  },
                  'on-focus': (event) => {
                    event.target.select();
                  }
                }
              });
            }
          }],
        columns2: [
          {
            type: 'selection',
            width: 60,
            align: 'left',
            className: 'table-select',
          }, {
            title: "商品图片",
            key: "logo",
            render: (h, params) => {
              var obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          }, {
            title: "名称编码",
            key: "name",
            width: 250,
            render: (h, params) => {
              let data = params.row;
              return h('div', [
                h('div', {},data.name),
                h('a', {
                  style: {
                    color: "#333"
                  },
                }, data.commodity_code)
              ])
            }
          }, {
            title: "单位",
            key: "unit",
          }, {
            title: "描述",
            key: "summary",
          }, {
            title: "商品分类",
            key: "category_name"
          }],
        goodsList: [],
        categoryList: [],
        selectedRow: [],
        loading: false,
        newSelectedGoods: [],
        isFocus: false,
        addedNum: 0,
      }
    },
    created() {
      goods.getGoodsCategory().then((res) => {
        if (res.status) {
          this.categoryList = res.data.map((item) => {
            return {
              value: item.id,
              label: item.name,
              children: [],
              loading: false
            }
          });
          this.categoryList.unshift({'value': 0, 'label': '全部分类'});
        }
      });
    },
    watch: {
      showModal:{
        immediate: true,
        handler(val) {
          if (val) {
            this.loadGoodsList();
          }
        }
      }
    },
    methods: {
      //全选取消状态处理
      cancelAll(selection) {
        if (this.addedNum === selection.length) {
          this.selectedRow.forEach((item, index) => {
            if (this.newSelectedGoods.length) {
              this.newSelectedGoods.forEach((_item, _index) => {
                if (item.id === _item.id && !item._disabled) {
                  this.newSelectedGoods.splice(_index, 1);
                }
              });
            }
          });
        }
      },
      // checkbox全选中状态
      selectAll(selection) {
        let me = this;
        this.selectedRow.forEach(item => {
          let selected = false;
          if (this.newSelectedGoods.length) {
            this.newSelectedGoods.forEach((_item, index) => {
              if (item.id === _item.id) {
                selected = true;
              }
            });
            setTimeout(function () {
              if (!selected && !item._disabled) {
                me.newSelectedGoods.push(item);
              }
            }, 100);
          } else {
            if (!selected && !item._disabled) {
              me.newSelectedGoods.push(item);
            }
          }
        });
      },
      // CheckBox 单个取消状态
      checkBoxCancel(selection, row) {
        if (this.newSelectedGoods.length) {
          this.newSelectedGoods.forEach((item, index) => {
            if (item.id === row.id) {
              this.newSelectedGoods.splice(index, 1);
            }
          });
        }
      },
      //CheckBox单个选中种状态
      checkBoxCheck(selection, row) {
        let index = this.getIndex(row);
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        if (!checked) {
          this.newSelectedGoods.push(this.selectedRow[index]);
        }
      },
      // 获取已经添加的商品在当页所占的数量
      getAddedNum() {
        let num = 0;
        this.goodsList.forEach((item, index) => {
          if (item._checked) {
            num += 1;
          }
        });
        return num;
      },
      //获取行所在的索引
      getIndex(row) {
        let idx = '';
        this.goodsList.forEach((item, index) => {
          if (item.id === row.id) {
            idx = index;
          }
        });
        return idx;
      },
      // 单击行状态处理
      selectGoods(row, index) {
        if (row._disabled) return;
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        if (checked && this.isFocus) {
          this.isFocus = false;
          return;
        }
        selectRow.querySelector('.ivu-checkbox-wrapper').className =
          checked ? "ivu-checkbox-wrapper" : "ivu-checkbox-wrapper ivu-checkbox-wrapper-checked";
        selectRow.querySelector('.ivu-checkbox').className = checked ? "ivu-checkbox" : "ivu-checkbox ivu-checkbox-checked";
        if (this.modalType === 'newGoods') {
          selectRow.querySelector('.ivu-input-number-input').focus();
          selectRow.querySelector('.ivu-input-number-input').select();
        }
        if (!checked) {
          this.newSelectedGoods.push(this.selectedRow[index]);
        } else {
          if (this.newSelectedGoods.length) {
            this.newSelectedGoods.forEach((item, index) => {
              if (item.id === row.id) {
                this.newSelectedGoods.splice(index, 1);
              }
            });
          }
        }
      },
      rowClassName (row, index) {
        if (row._disabled) {
          return 'table-close-row';
        }
        return 'normalRow';
      },
      hideModal() {
        if(typeof this.$parent.showGoodsListModal === 'object' && this.$parent.showGoodsListModal.show){
          this.$parent.showGoodsListModal.show = false;
        }else{
          this.$parent.showGoodsListModal = false;
        }
      },
      changePage(pageNo) {
        this.currentPage = pageNo;
        this.loadGoodsList();
      },
      changePageSize(size) {
        this.pageSize = size;
        this.loadGoodsList();
      },
      changeCategory(value) {
        this.currentPage = 1;
        this.filters.category1 = value[0];
        this.filters.category2 = value[1];
        this.loadGoodsList();
      },
      loadSubCategoryData(item, callback) {
        item.loading = true;
        goods.getGoodsCategory(item.value).then((res) => {
          if (res.status) {
            item.children = res.data.map((_item) => {
              return {
                value: _item.id,
                label: _item.name,
              }
            });
            item.loading = false;
            callback();
          }
        });
      },
      cancelAllCheckStatus () {
        let cb = () =>{
          let selectedItem = this.$refs.goodsTable.$el.querySelectorAll('.ivu-table-tbody .ivu-checkbox-wrapper-checked');
          if (selectedItem) {
            Array.from(selectedItem).map(item => {
              item.classList.remove('ivu-checkbox-wrapper-checked');
              let checkbox = item.querySelector('.ivu-checkbox-checked');
              if (checkbox) {
                checkbox.classList.remove('ivu-checkbox-checked');
              }
            });
          }
        }
        if(this.$refs.goodsTable){
          cb.call(this)
        }else{
          this.$nextTick(()=>{
            cb.call(this)
          })
        }
      },
      async loadGoodsList(page) {
      	this.cancelAllCheckStatus();
        this.loading = true;
        let params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          searchValue: this.filters.no, //搜索关键词
          category_id: this.filters.category1, //一级分类id
          category_id2: this.filters.category2, //二级分类id
          showAll: true,
          ...this.params
        };
        if (this.modalType === 'newGoods') {
          params.is_online = 'Y';
        } else if (this.modalType === 'shieldGoods') {
          params.showAll = false;
        }
        if (page) {
          params.page = page;
        }
        let res = await goods.getGoodsList(params);
        if (!res.status) {
          this.modalError(res.message);
          return;
        }
        this.loading = false;
        this.goodsList = [];
        let data = res.data;
        let Goods = data.list.map(item => {
          // 筛选已经添加的商品
          item.agreement_price = item.price;
          if (this.selectedGoods.length > 0) {
            item._checked = false;
            item._disabled = false;
            this.selectedGoods.forEach(d => {
              if (item.id === d.id || item.id === d.commodity_id) {
                item._checked = true;
                item._disabled = true;
                item.agreement_price = d.agreement_price;
              }
            });
          }
          return item;
        });
        // 已经选择的置顶
        Goods.forEach(item => {
          // 筛选已经选取的商品
          this.newSelectedGoods.forEach(order => {
            if (item.id === order.id) {
              item._checked = true;
            }
          });
        });
        this.goodsList = Goods;
        this.selectedRow = this.cloneObj(this.goodsList);
        this.currentPage = parseInt(data.pageParams.page);
        this.totalPage = parseInt(data.pageParams.count);
        this.addedNum = this.getAddedNum();
      },
      addConfirm() {
        if (this.newSelectedGoods.length) {
          this.$emit('on-add', this.newSelectedGoods);
          this.newSelectedGoods = [];
          if(this.$parent.showGoodsListModal.show){
            this.$parent.showGoodsListModal.show = false;
          }else{
            this.$parent.showGoodsListModal = false;
          }
        } else {
          this.modalError('请选择需要添加的商品');
        }
      }
    }
  }
</script>

<style lang="less" scoped>
  ._tips {
    font-size: 12px;
    text-align: right;
    line-height: 30px;
    color: rgba(0, 0, 0, .5);
  }
  /deep/ .ivu-modal .ivu-modal-header-inner:before{
    vertical-align: middle;
  }
  /deep/ .ivu-input-group .ivu-input {
    border-radius: 2px;
  }
  /deep/ .ivu-page {
    margin-top: 8px;
    text-align: right;
  }
</style>
