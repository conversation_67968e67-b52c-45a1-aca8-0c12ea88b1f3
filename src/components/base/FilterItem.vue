<template>
  <Col style="padding-right: 10px">
  <template v-if="label">
    <Row
      align="middle"
      type="flex">
      <Col style="padding-right: 5px">{{label}}</Col>
      <Col><slot></slot></Col>
    </Row>
  </template>
  <template v-else>
    <slot></slot>
  </template>
  </Col>
</template>
<script>
  export default {
  	autoRegister: true,
    props: {
    	label: {
    		type: String,
        default: ''
      }
    }
  };
</script>
