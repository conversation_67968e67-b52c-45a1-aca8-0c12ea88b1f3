<template>
  <Poptip
    @on-ok="handleOk"
    @on-cancel="handleCancel"
    :confirm="confirm"
    :confirmType="confirmType"
    :transfer="true"
    :placement="placement"
    :title="title">
    <div
      slot="content"
      class="content"></div>
    <Button
      :disabled="disabled"
      :type="type"><slot></slot></Button>
  </Poptip>
</template>

<script>
  export default {
    name: "PopButton",
    autoRegister: true,
    props: {
      confirm: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
    	title: {
        type: String,
        default: '确认'
      },
      type: {
    		type: String,
        default: 'default'
      },
      placement: {
    		type: String,
        default: 'left'
      }
    },
    computed: {
      confirmType () {
        const deleteNameKey = '删除'
        return String(this.title).includes(deleteNameKey) ? 'error' : 'primary'
      }
    },
    methods: {
      handleOk () {
      	this.$emit('on-ok');
      },
      handleCancel () {
        this.$emit('on-cancel');
      }
    }
  }
</script>

<style scoped>
  .content {
    width: 240px;
    height: 150px;
  }
</style>
