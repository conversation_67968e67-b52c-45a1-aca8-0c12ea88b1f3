<template>
  <div>
    <pdf :src="src" v-bind="$attrs" @page-loaded="onPageLoaded"  @num-pages="onNumPages"></pdf>
    <pdf :src="src" v-for="page in extraPages"  @page-loaded="onPageLoaded" :page="page + 1" :key="page + 1"></pdf>
  </div>
</template>

<script>
import pdf from './core/vuePdfNoSss.vue'
export default {
  name: 'PdfRender',
  components: {
    pdf,
  },
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  computed: {
    src() {
      // 替换成https
      return this.url.replace('http://', 'https://');
    },
    extraPages() {
      return this.numPages - 1;
    },
  },
  data() {
    return {
      numPages: 1,
    };
  },
  methods: {
    onPageLoaded(page) {
      if (page === this.numPages) {
        this.$emit('all-pages-loaded');
      }
    },
    onNumPages(num) {
      this.numPages = num;
    },
  }
};
</script>
