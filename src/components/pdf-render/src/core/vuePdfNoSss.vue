<style src="./annotationLayer.css"></style>
<script>
import componentFactory from './componentFactory.js';

var pdfjsWrapper = require('./pdfjsWrapper.js').default;
var PDFJS = require('pdfjs-dist/es5/build/pdf.js');

if (
  typeof window !== 'undefined' &&
  'Worker' in window &&
  navigator.appVersion.indexOf('MSIE 10') === -1
) {
  // 如果是开发环境
  if (process.env.NODE_ENV === 'development') {
    var PdfjsWorker = require('worker-loader!pdfjs-dist/es5/build/pdf.worker.js');
    PDFJS.GlobalWorkerOptions.workerPort = new PdfjsWorker();
  } else {
    PDFJS.GlobalWorkerOptions.workerSrc = `/pdfjs-dist@2.6.347/pdf.worker.min.js`;
  }
}

var component = componentFactory(pdfjsWrapper(PDFJS));

export default component;
</script>
