<script>
export default {
	name: 'circling-guidance',
	data() {
		return {
			prominent: false
		}
	},
	props: {
		buttonText: {
			type: String,
			default: ''
		},
		title: {
			type: String,
			default: ''
		},
		content: {
			type: String,
			default: ''
		},
		/**
		 * @description 引导框位置
		 */
		position: {
			type: String,
			default: 'bottom'
		}
	},
	methods: {
		switchProminentHandle(flag) {
			this.prominent = flag;
			if (flag) {
				this.$sintro.start({
					onClose: () => {
						this.prominent = false
						this.$emit('close')
					}
				});
			}
		},
	}
}
</script>

<template>
	<div class="circling-guidance">
		<div v-if="prominent" class="guide-mask"></div>
		<div
			v-guide="{title, content, position}"
			class="stand-out-part"
		>
			<slot></slot>
		</div>
	</div>
</template>

<style scoped lang="less">
.circling-guidance {
	position: relative;
	.guide-mask {
		position: fixed;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
		background: rgba(0, 0, 0, .5);
		transition: all .3s;
		z-index: 200;
	}
	.stand-out-part {
		position: relative;
		z-index: 201;
	}
	/deep/.custom-intro {
		position: absolute;
		top: 50px;
		right: 200px;
	}
	/deep/.s-intro {
		right: 200px;
	}
}
</style>
