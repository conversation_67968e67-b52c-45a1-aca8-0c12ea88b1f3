<template>
  <Cascader
    transfer
    filterable
    :data="data"
    v-on="$listeners"
    :load-data="loadData"
  ></Cascader>
</template>
<script>
import { get } from '@/api/request';
import { GOODS_TAX_TYPE_SALE, GOODS_TAX_TYPE_PURCHASE } from '@/util/const';
import { mapState } from 'vuex';
export default {
  name: 'GoodsTaxRateTemplate',
  computed: {
    ...mapState({
      sysConfig: 'sysConfig',
    }),
    taxRate() {
      return +this.sysConfig.tax_rate === 1;
    },
    inputTaxRate() {
      return +this.sysConfig.input_tax_rate === 1;
    },
  },
  data() {
    return {
      data: [],
    };
  },
  watch: {
    taxRate() {
      this.setData();
    },
    inputTaxRate() {
      this.setData();
    },
  },
  created() {
    this.setData();
  },
  methods: {
    setData() {
      const data = [];
      if (this.taxRate) {
        data.push({
          label: '销项税率',
          value: GOODS_TAX_TYPE_SALE,
          loading: false,
          children: [],
        });
      }
      if (this.inputTaxRate) {
        data.push({
          label: '进项税率',
          value: GOODS_TAX_TYPE_PURCHASE,
          loading: false,
          children: [],
        });
      }
      this.data = data;
    },
    async loadData(item, callback) {
      const params = {
        pageSize: 999,
        page: 1,
        status: 1,
        type: 1,
      };
      params.tax_type = item.value;
      item.loading = true;
      const { status, data } = await get('/superAdmin/taxRate/list', params);
      if (status) {
        item.children = data.list.map((item) => {
          return {
            ...item,
            label: item.name,
            value: item.id,
          };
        });
      } else {
        item.children = [];
      }
      item.loading = false;
      callback();
    },
  },
};
</script>
