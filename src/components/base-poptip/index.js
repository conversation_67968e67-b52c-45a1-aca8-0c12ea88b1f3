
import { Poptip } from "view-design"
import './style.less'

/**
 * 修改PopTip组件的默认配置
 */
export default {
  extends: Poptip,
  props: {
    confirmType: {
      type: String,
      validator (value) {
        return ['primary', 'error'].includes(value)
      }
    }
  },
  computed: {
    popperClasses () {
      const { prefixCls } = this
      const confirmType = this.confirmType || (String(this.title).includes('删除') ? 'error' : 'primary')
      return [
        `${prefixCls}-popper`,
        `${prefixCls}-confirm-type__${confirmType}`,
        {
          [`${prefixCls}-confirm`]: this.transfer && this.confirm,
          [`${this.popperClass}`]: !!this.popperClass,
          [prefixCls + '-transfer']: this.transfer,
          [this.transferClassName]: this.transferClassName,
        }
      ]
    }
  }
}

