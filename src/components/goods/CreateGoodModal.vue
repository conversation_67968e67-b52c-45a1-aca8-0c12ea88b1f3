<!--
 * @Description: 新增商品弹窗
 * @Date: 2023-08-18 14:36:48
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-31 10:28:42
 * @FilePath: /sdpbase-pro/src/components/goods/CreateGoodModal.vue
-->
<template>
	<div class="create-good-modal-wrapper">
		<Modal
			class-name="create-good-modal"
			v-model="show"
			@on-cancel="show = false"
			title="新增商品"
			:width="1180"
		>
			<base-block title="基础信息" class="base-info new-goods__operation">
				<Form
					ref="form"
          inline
					:model="goodsData"
					:rules="rules"
					class="custom_form"
					label-position="right"
					:label-width="110"
				>
					<div class="custom_form_item">
						<FormItem prop="category" label="商品分类：">
							<Cascader
								:data="initData.categoryTree"
								trigger="hover"
                @on-change="setCommodityCodeWidthRules"
								v-model="goodsData.category"
							></Cascader>
						</FormItem>
					</div>
					<div class="custom_form_item">
						<FormItem prop="name" label="商品名称：">
							<Input
								style="position: relative"
								show-word-limit
								maxlength="30"
								v-model="goodsData.name"
								@on-focus="inputGoodsFocus = true"
								@on-blur="autoSetCode"
								@on-change="searchGoodsList"
								placeholder="长度限30个字，如：白菜"
							>
							</Input>
							<!-- 新增才有选择云商品功能 -->
							<div
								class="input-select"
								@mouseover="showGoodsMouse = true"
								@mouseout="showGoodsMouse = false"
								v-show="
									!commodity_id &&
										(showGoodsMouse || (inputGoodsFocus && goodsData.name)) &&
										goodsSearchData &&
										goodsSearchData.length > 0
								"
							>
								<div
									class="select-item"
									v-for="(goodsSearch, index) in goodsSearchData"
									:key="index"
									@click="selectGoods(goodsSearch)"
								>
									<span class="item-left">{{ goodsSearch.spuName }}</span>
									<span class="item-right">云商品库</span>
								</div>
							</div>
						</FormItem>
					</div>
					<div class="custom_form_item">
						<FormItem prop="commodity_code" label="商品编码：">
							<Input
								show-word-limit
								maxlength="30"
								@on-change="changeInput"
								v-model="goodsData.commodity_code"
								placeholder="长度<30,必须填"
							/>
						</FormItem>
					</div>
					<div class="custom_form_item">
						<FormItem label="商品别名：">
							<Input
								suffix
								show-word-limit
								maxlength="20"
								v-model="goodsData.alias"
								placeholder="长度限20个字"
							/>
						</FormItem>
					</div>
					<div class="custom_form_item purchase_type">
						<FormItem prop="channel_type" label="采购类型：">
							<RadioGroup
								@on-change="validateForm"
								v-model="goodsData.channel_type"
								type="button"
                :class="isEnablePurchaseTask ? 'radio_channel_type' : ''"
							>
								<Radio label="2">供应商送货</Radio>
								<Radio label="1">市场自采</Radio>
							</RadioGroup>
							<Checkbox
                v-if="!isEnablePurchaseTask"
								v-model="goodsData.allow_change_channel"
								style="margin-left:5px;"
							>
								临时指定
							</Checkbox>
						</FormItem>
					</div>
					<div class="custom_form_item" v-show="goodsData.channel_type == 2">
						<FormItem prop="channel_type_value" label="默认供应商：">
							<Select
								filterable
								v-model="goodsData.provider_id"
								placeholder="请选择"
							>
								<Option
									:value="info.id"
									v-for="info in initData.providers"
									:key="info.id"
									>{{ info.name }}</Option
								>
							</Select>
						</FormItem>
					</div>
					<div class="custom_form_item" v-show="goodsData.channel_type == 1">
						<FormItem prop="channel_type_value" label="采购员：">
							<Select
								filterable
								v-model="goodsData.agent_id"
								placeholder="请选择"
							>
								<Option
									:value="info.id"
									v-for="info in initData.agents"
									:key="info.id"
									>{{ info.name }}</Option
								>
							</Select>
						</FormItem>
					</div>

					<div class="custom_form_item">
						<FormItem label="助记码：">
							<Input
								show-word-limit
								maxlength="128"
								v-model="goodsData.pinyin"
                disabled
								placeholder="支持128个字符"
							/>
							<Tooltip
								placement="right"
								max-width="400"
								transfer
								content="首次创建时，默认助记码为商品拼音全拼和简拼字母"
							>
								<i class="icon-help1 iconfont icontip"></i>
							</Tooltip>
						</FormItem>
					</div>
					<div class="custom_form_item" v-if="isEnablePurchaseTask">
						<FormItem prop="provider_supervisor" label="采购负责人：">
							<Select
								filterable
								v-model="goodsData.provider_supervisor"
								placeholder="请选择"
							>
								<Option
									:value="info.id"
									v-for="info in initData.agents"
									:key="info.id"
									>{{ info.name }}</Option
								>
							</Select>
						</FormItem>
					</div>
					<div class="custom_form_item" v-if="initData.is_open_unit_group > 0">
						<FormItem label="单位组：">
							<div class="original-input">
								<Input
									v-model="goodsData.unit_group"
									style="width: 232px"
									placeholder="输入单位组"
								/>
							</div>
						</FormItem>
					</div>
					<div
						class="custom_form_item"
						v-if="central_purchase_flag_can_show == 1"
					>
						<FormItem label="可采集商品：">
							<div class="radio_flex">
								<RadioGroup v-model="goodsData.central_purchase_flag">
									<Radio :label="true">集采商品</Radio>
									<Radio :label="false">非集采商品</Radio>
								</RadioGroup>
							</div>
						</FormItem>
					</div>
					<div class="custom_form_item">
						<FormItem label="保质期：">
							<template v-if="!isBatch">
								<Input
									v-model="goodsData.durability_period"
									style="width: 232px"
									placeholder="输入保质期"
								/>
							</template>
							<template v-if="isBatch">
								<NumberInput
									v-model="goodsData.shelf_life"
									style="width: 232px"
									placeholder="输入保质期"
								/>
								<span class="icontip"> 天</span>
							</template>
						</FormItem>
					</div>
					<div class="custom_form_item" v-if="isBatch && comCap != 2">
						<FormItem label="批次管理：">
							<Select
								filterable
								v-model="goodsData.is_batch"
								placeholder="请选择"
							>
								<Option value="1" :key="1">启用</Option>
								<Option value="0" :key="0">不启用</Option>
							</Select>
							<Tooltip
								placement="right"
								content="开启之后，商品收货入库的时候需要填写保质期和生产日期 "
								transfer
								max-width="400"
							>
								<i class="icon-help1 iconfont icontip"></i>
							</Tooltip>
						</FormItem>
					</div>
					<div class="number-input-box" v-if="goodsData.is_batch == 1">
						<FormItem label="过期预警：">
							<span class="label_name">距到期日期剩余：保质期 *</span>
							<Input
								key="earlyWarnDayPercent"
								v-model="earlyWarnDayPercent"
								style="width: 68px"
								ref="updateEarlyWarnDayPercentRef"
								placeholder=""
								@on-focus="resetEarlyWarnDayPercent"
								@on-change="updateEarlyWarnDay"
							/>
						</FormItem>

						<FormItem prop="earlyWarnDayCheck" :label-width="0">
							<span class="label_name" style="padding: 0 4px">
								%=
							</span>
							<Input
								key="updateEarlyWarnDayPercent"
								number
								v-model="goodsData.early_warn_day"
								style="width: 68px"
								on-blur="checkEarlyWarnDay"
								@on-change="updateEarlyWarnDayPercent"
								ref="updateEarlyWarnDayRef"
								@on-focus="resetEarlyWarnDay"
							/>
							<span>天预警</span>
						</FormItem>
					</div>
				</Form>
			</base-block>
			<base-block style="margin-top: 4px;" class="custom_unit">
				<template #title>
					<div class="flex aic">
						<h5>单位设置</h5>
						<span class="base_sub_title"
							>对于一个客户，优先执行指定价，其次类型价，最后市场价</span
						>
					</div>
				</template>
				<template #head-right>
					<div class="flex basic_check" style="align-items: center">
						<Checkbox
							:disabled="skuList.length == 1"
							v-model="goodsData.unit_convert"
						>
							<span style="margin:0 2px;">分拣时按基础单位</span>
						</Checkbox>
						<Tooltip placement="bottom" max-width="249">
							<div slot="content">
								<div
									v-if="!goodsData.unit_change_disabled"
									class="flex"
									style="flex-direction: column; font-size: 12px"
								>
									<div>不勾选：</div>
									<div>适用于下单单位与分拣单位一致的商品，如：客户下单购买1瓶酱油，分拣时也按“瓶”进行分拣。</div>
									<div>勾选：</div>
									<div>适用于下单单位与分拣单位不一致的商品，如：客户下单购买1只鸡，分拣时需要按“斤”秤重进行分拣结算。</div>
								</div>
								<div v-else>当前商品辅助单位存在业务数据，无法修改该配置</div>
							</div>
							<i class="s-icon icon-tips ml3"></i>
						</Tooltip>
						<Tooltip
							v-if="skuList[0]"
							class="use_price"
							placement="bottom"
							max-width="250"
						>
							<div slot="content">
								<div
									v-if="!is_open_receivable_rate"
									class="flex"
									style="flex-direction: column; font-size: 12px;"
								>
									<div>勾选：</div>
									<div>商品在订单、定价模块不同客户类型均取值市场价，客户类型价不再支持修改；适用商品针对不同客户类型价格一致的场景</div>
									<div>不勾选：</div>
									<div>市场价和客户类型价均独立维护价格；适用商品针对不同客户类型价格不一致的场景</div>
								</div>
								<div v-else>
									<div>开启客户类型折扣率不支持勾选取值市场价</div>
								</div>
							</div>
							<Checkbox
								v-model="skuList[0].style_use_price"
                :disabled="is_open_receivable_rate"
								:true-value="1"
								:false-value="0"
							>
								<span style="margin: 0 2px 0 0px;">取值市场价</span>
							</Checkbox>
							<i class="s-icon icon-tips" style="margin-left:0;"></i>
						</Tooltip>
						<div class="show_btn" @click="showOldCustomerFn">
							{{ showOldCustomer ? '收起客户类型价' : '展开客户类型价' }}
						</div>
					</div>
				</template>
				<div style="width:100%;position:relative;">
					<div :class="{ 'sdp-table__divider': !showOldCustomer }"></div>

					<div class="new-goods__table">
						<table class="table" :class="{ table2: !showOldCustomer }">
							<thead>
								<tr>
									<th style="width:40px;min-width:40px;"></th>
									<th style="min-width: 300px;max-width:400px;">
										<i class="required-pot">*</i>单位
									</th>
									<th style="min-width:160px;">描述</th>
									<th style="min-width: 70px;width:70px;">起订量</th>
									<th style="min-width: 70px;width:70px;">
										增量订购
										<Tooltip
											style="font-weight: 500"
											max-width="170"
											transfer
											placement="top"
											content="商城客户下单时仅支持按照起订量数值倍数订购"
										>
											<i
												class="iconfont icon-tishifu icontip"
												style="margin-left:0;"
											></i>
										</Tooltip>
									</th>
									<th style="min-width: 70px">最近一次进价</th>
									<th style="min-width: 190px">售卖库存</th>
									<th v-show="comCap == 2">
										<i class="required-pot">*</i>生产线
									</th>
									<th v-if="printLabelMode === 2">包装规格数量</th>
									<th style="min-width: 120px">
										<i class="required-pot">*</i>市场价
									</th>
									<th style="max-width: 80px" v-show="goodsData.is_time_price == 2">是否时价</th>
									<th style="max-width: 80px">是否可售卖</th>
									<template v-if="showOldCustomer">
										<th style="min-width: 70px">客户类型</th>
										<template v-if="!is_open_receivable_rate">
											<th>
												<div class="layout-flex">
													<div style="min-width: 120px">客户类型价</div>
													<template v-if="commodity_disabled_mode_3">
														<div style="min-width: 80px">是否上架</div>
													</template>
												</div>
											</th>
										</template>
										<template v-else>
											<th>
												<div class="layout-flex">
													<template v-if="is_open_receivable_rate">
														<div style="min-width: 100px">折前价</div>
														<div style="min-width: 100px">折扣率</div>
														<div style="min-width: 120px">折后价</div>
													</template>
													<template v-if="commodity_disabled_mode_3">
														<div style="min-width: 80px;text-align:center;">
															是否上架
														</div>
													</template>
												</div>
											</th>
										</template>
									</template>
                  <th style="min-width: 100px">初始销量</th>
                  <th style="min-width: 120px" v-if="sysConfig.provider_in_price_low_rate == 1">采购下浮率</th>
                  <template v-if="!showOldCustomer">
										<th class="table_fixed_right can_click_head">
											<div style="width: 80px" class="fixed_div">操作</div>
										</th>
									</template>
								</tr>
							</thead>
							<tbody>
								<tr v-for="(item, index) in skuList" :key="item.id + item.uuid">
									<td style="font-size: 16px">
										<div
											style="
                              text-align: center;
                              display: flex;
                              flex-direction: column;
                              line-height:20px;
                            "
										>
											<i
												class="iconfont pointer icon-jian sku_class"
												:key="item.id + index + '-'"
												v-show="index > 0"
												@click="removeSku(item, index)"
											></i>
											<i
												class="iconfont pointer icon-jia1 sku_class"
												:key="item.id + index"
												@click="
													() => {
														addSku();
													}
												"
											></i>
										</div>
									</td>
									<td v-show="index === 0">
										<Select
											filterable
											transfer
											v-if="
												!goodsData.base_unit_change_disabled ||
													modifyCommodityWithoutLimit
											"
											:value="item.unit"
											placeholder="选择单位"
											style="width: 90px; position: relative"
											@on-change="chooseUnit($event, index)"
										>
											<Option
												v-for="(unit, index) in initData.units"
												:value="unit.name"
												:key="index"
												>{{ unit.name }}</Option
											>
										</Select>
										<span v-else style="margin:0 10px">
											{{ item.unit }}
										</span>
										<span class="table-original">基础单位</span>
									</td>
									<td v-show="index !== 0">
										<!-- <p>辅助单位{{ index }}</p> -->
										<Select
											filterable
											:value="item.unit"
											transfer
											placeholder="选择单位"
											style="width: 83px; position: relative"
											@on-change="chooseUnit($event, index)"
										>
											<Option
												v-for="(unit, index) in initData.units"
												:value="unit.name"
												:key="index"
												>{{ unit.name }}</Option
											>
										</Select>
										<span> = </span>
										<InputNumber
											:min="0"
											style="width: 64px"
											v-model="item.unit_num"
											@on-change="readonlyNum(item, index)"
										>
										</InputNumber>
										<Tooltip
											v-show="
												goodsData.unit_change_disabled &&
													item.id &&
													!modifyCommodityWithoutLimit
											"
											placement="right"
											:max-width="250"
											content="当前商品辅助单位存在业务数据，无法修改该配置"
										>
											<i class="icon-help1 iconfont icontip"></i>
										</Tooltip>
										<span> {{ skuList[0].unit }} </span>
										<!--换算成基础单位价格-->
										<p
											v-show="!isNaN(item.price) && !isNaN(item.unit_num)"
											style="color: #03ac54; text-indent: 99px;font-size:12px;"
										>
											{{
												(
													(item.unit_num ? item.price / item.unit_num : 0) || 0
												).toFixed(2)
											}}元/{{ skuList[0].unit }}
										</p>
									</td>
									<td>
										<Input
											type="textarea"
											:rows="2"
											class="textarea_goods"
											placeholder="长度<20个字"
											v-model="item.summary"
										/>
									</td>
									<td>
										<InputNumber
											:min="0"
											style="width: 80px"
											v-model="item.order_quantity"
											:precision="2"
										/>
									</td>
									<td>
										<!-- 商城下单按起订量倍数订购  增量订购-->
										<div class="layout-flex__body__switch">
											<Switch
												v-model="item.order_quantity_type"
												:true-value="2"
												:false-value="1"
											>
											</Switch>
										</div>
									</td>
									<td>{{ item.in_price }}</td>
									<td>
										<Select
											transfer
											v-model="item.is_sell_stock"
											style="
                              width: 80px;
                              margin-right: 5px;
                              position: relative;
                            "
											@on-change="changeMethod(item)"
										>
											<Option value="0">不限制</Option>
											<Option value="1">自定义</Option>
											<Option v-if="isOpenRealTimeSellStock" value="2"
												>实时售卖库存</Option
											>
										</Select>
										<InputNumber
											style="width: 80px"
											:min="0"
											:max="99999"
											:step="0.01"
											:disabled="
												item.is_sell_stock == 0 ||
													(goodsData.unit_convert && index != 0) ||
													+item.is_sell_stock === 2
											"
											v-model="item.sell_stock"
											@on-change="changeNum(item)"
										/>
									</td>
									<td v-show="comCap == 2">
										<Select
											style="width: 100px"
											transfer
											placeholder="生产线"
											@on-change="changeProcessLine(item)"
											v-model="item.product_line_id"
										>
											<Option
												v-for="info in initData.productLines"
												:value="info.id"
												:key="info.id"
												>{{ info.name }}</Option
											>
										</Select>
									</td>
									<!-- 包装规格数量 -->
									<td v-if="printLabelMode === 2">
										<InputNumber
											:min="0"
											style="width: 80px"
											v-model="item.pack_num"
											:precision="2"
										/>
									</td>
									<td>
										<InputNumber
											style="width: 80px"
											:step="0.01"
											:min="0"
											:precision="4"
											v-model="item.price"
										/>
										<ToolTip-Price
											:transfer="true"
											:oldPrice="item.in_price"
											:newPrice="item.price"
										></ToolTip-Price>
									</td>
                  <!-- 是否时价 选择【部分时价】时才显示-->
                  <td v-show="goodsData.is_time_price == 2">
                    <Switch
                      v-model="item.is_time_price"
                      true-value="1"
                      false-value="0"
                    >
                      <span slot="open">是</span>
                      <span slot="close">否</span>
                    </Switch>
                  </td>
									<td>
										<div
											class="layout-flex__body__switch"
											style="justify-content:flex-start !important;"
										>
											<Poptip
												@on-ok="handleIsSellChange()"
												@on-cancel="item.is_sell = !item.is_sell"
												title="设置为不可售卖后，商品将会下架"
												placement="left"
												:transfer="true"
												:confirm="true"
											>
												<Switch
													v-show="isOnlyOneSkuCanSell && item.is_sell"
													v-model="item.is_sell"
												>
													<span slot="open">开</span>
													<span slot="close">关</span>
												</Switch>
											</Poptip>
											<Switch
												v-show="!item.is_sell || !isOnlyOneSkuCanSell"
												v-model="item.is_sell"
											>
												<span slot="open">开</span>
												<span slot="close">关</span>
											</Switch>
										</div>
									</td>
									<template v-if="showOldCustomer">
										<td>
											<div
												class="layout-flex layout-flex__body"
												v-for="customer in item.receivable_style"
												:key="customer.id + 'customer'"
											>
												<div :title="customer.name" class="ellipsis_word">
													{{ customer.name }}
												</div>
											</div>
										</td>
										<template v-if="!is_open_receivable_rate">
											<!-- 折前价 -->
											<td
												style="
                          max-width: 300px;
                          text-align: left;
                          padding: 10px 0px;
                        "
											>
												<div
													class="layout-flex layout-flex__body"
													v-for="customer in item.receivable_style"
													:key="customer.id + 'receivable_rate'"
												>
													<div style="min-width: 120px">
														<!-- <span style="margin: 0 5px;">{{ customer.name }}</span> -->
														<InputNumber
															:min="0"
															:step="0.01"
															:precision="4"
															style="width: 95px"
															v-model="customer.price"
														/>
														<ToolTip-Price
															:transfer="true"
															:oldPrice="item.in_price"
															:newPrice="customer.price"
														></ToolTip-Price>
													</div>
													<template v-if="commodity_disabled_mode_3">
														<div class="layout-flex__body__switch">
															<Switch
																v-model="customer.receivable_is_online"
																true-value="1"
																false-value="0"
															>
															</Switch>
														</div>
													</template>
												</div>
											</td>
										</template>
										<template v-else>
											<td>
												<div
													class="layout-flex layout-flex__body"
													v-for="customer in item.receivable_style"
													:key="customer.id + 'is_open_receivable_rate'"
												>
													<template v-if="is_open_receivable_rate">
														<div class="layout-flex__right">
															<InputNumber
																:min="0"
																:step="0.01"
																:precision="4"
																style="width: 95px; margin-right: 5px"
																v-model="customer.receivable_org_price"
																@on-change="$_onCompuntePrice(1, customer)"
															/>
														</div>
														<!-- 折扣率 -->
														<div>
															<InputNumber
																:min="0"
																:step="0.01"
																style="width: 95px; margin-right: 5px"
																v-model="customer.receivable_price_rate"
																:precision="2"
																@on-change="$_onCompuntePrice(2, customer)"
															/>
														</div>
														<!-- 折后价 -->
														<div style="min-width:120px;">
															<InputNumber
																:min="0"
																:step="0.01"
																:precision="4"
																style="width: 90px;"
																v-model="customer.price"
																@on-change="$_onCompuntePrice(3, customer)"
															/>
															<ToolTip-Price
																:transfer="true"
																:oldPrice="item.in_price"
																:newPrice="customer.price"
															></ToolTip-Price>
														</div>
													</template>
													<!-- 是否上架 -->
													<template v-if="commodity_disabled_mode_3">
														<div class="layout-flex__body__switch">
															<Switch
																v-model="customer.receivable_is_online"
																true-value="1"
																false-value="0"
															>
															</Switch>
														</div>
													</template>
												</div>
											</td>
										</template>
									</template>
                  <td>
                    <InputNumber
                      style="width: 80px"
                      :min="0"
                      :max="999999"
                      :precision="0"
                      v-model="item.init_sale_num"
                    />
                  </td>
                  <td v-if="sysConfig.provider_in_price_low_rate == 1">
                    <InputNumber
                      style="width: 80px"
                      :min="0"
                      :max="100"
                      :precision="2"
                      v-model="item.in_price_low_rate"
                    />%
                  </td>
                  <template v-if="!showOldCustomer">
										<td
											style="width: 80px"
											@click="openSetCustomer(item, index)"
											class="can_click table_fixed_right"
										>
											<div
												class="fixed_div"
												v-if="
													item.receivable_style &&
														item.receivable_style.length > 0
												"
											>
												客户类型价
											</div>
										</td>
									</template>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="flex goods__table_box">
					<div>
						注意事项
						<i class="iconfont icon-tishifu icontip" style="margin-left:0;"></i
						>：
					</div>
					<div class="goods__table_footer2">
						<div class="line_text">
							1.多单位商品仅用基础单位进行库存管理时，建议勾选【分拣按基础单位】
						</div>
						<div class="line_text">
							2.多单位商品每个单位库存独立管理时，不建议勾选按【分拣按基础单位】
						</div>
						<div class="line_text">
							3. 辅助单位转换系数和基础单位设定后，生成采购单或订单后不支持修改
						</div>
					</div>
				</div>
			</base-block>
			<base-block
				title="其他信息"
				class="new-goods__operation"
				style="margin-top:20px;"
			>
				<div class="other-info">
					<Form
						label-position="right"
						:label-width="110"
						:rules="rules"
						:model="goodsData"
						class="custom_form"
					>
						<div class="custom_form_item">
							<FormItem label="商品排序：">
								<InputNumber
									v-model="goodsData.sequence"
									:min="0"
									style="width: 232px"
								/>
								<Tooltip
									placement="right"
									content="数值越小在商城中展示越靠前"
									transfer
									max-width="200"
								>
									<i class="s-icon icon-tips ml10"></i>
								</Tooltip>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="是否标品：">
								<div class="radio_flex">
									<RadioGroup v-model="goodsData.is_rough">
										<Radio label="Y">标品</Radio>
										<Radio label="N">非标品</Radio>
									</RadioGroup>
								</div>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="是否时价：">
								<template #label>
									是否时价
									<Tooltip
										placement="right"
										content="开启之后，该商品在商城显示为“时价”"
									>
										<i
											class="icon-help1 iconfont icontip"
											style="margin-left:1px;"
										></i>
									</Tooltip>
                ：
								</template>
								<div class="flex">
									<div class="radio_flex">
										<RadioGroup v-model="goodsData.is_time_price">
											<Radio label="1"><span>时价</span></Radio>
											<Radio label="2"><span>部分时价</span></Radio>
											<Radio label="0"><span>非时价</span></Radio>
										</RadioGroup>
									</div>
								</div>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="商品品牌：">
								<Input
									show-word-limit
									maxlength="20"
									v-model="goodsData.brand"
									placeholder="长度限20个字"
								/>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="损耗率：">
								<InputNumber
									style="width: 232px"
									v-model="goodsData.loss_rate"
								></InputNumber>
								<span class="icontip"> % </span>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="商品产地：">
								<Input
									show-word-limit
									maxlength="20"
									v-model="goodsData.product_place"
									placeholder="长度限20个字"
								/>
							</FormItem>
						</div>
						<div
							class="custom_form_item"
							v-if="goodsData.unit_convert && comCap == 0"
						>
							<FormItem label="统一采购单位：">
								<Select
									v-model="purchaseUnite"
									:disabled="comCap == 2"
									style="width: 232px"
								>
									<Option
										v-for="(item, index) in unites"
										:value="item.name"
										:key="index"
										>{{ item.name }}</Option
									>
								</Select>
							</FormItem>
						</div>
						<div class="custom_form_item" v-if="purchaseTaskUnitSelectShow">
							<FormItem label="采购任务指定采购单位：">
								<Select v-model="purchaseTaskUnit" style="width: 200px">
									<Option
										v-for="(item, index) in unites"
										:value="item.name"
										:key="index"
										>{{ item.name }}</Option
									>
								</Select>
							</FormItem>
						</div>
						<div
							class="custom_form_item"
							v-if="$store.state.sysConfig.input_tax_rate == 1"
						>
							<FormItem label="进项税率规则：">
								<div
									v-if="inputTaxRates && inputTaxRates.length > 0"
									class="canClick"
									@click="
										goToDetail('#/taxRate/inputTax/detail', inputTaxRates[0])
									"
								>
									<span v-if="inputTaxRates.length == 1">{{
										inputTaxRates[0].tax_rate_name
									}}</span>
									<span v-else>{{ inputTaxRates[0].tax_rate_name }} 等</span>
								</div>
								<div v-else class="flex_around">
									<span class="none_class">无</span>
									<span
										class="canClick"
										@click="goToPage('#/taxRate/inputTax/list')"
										>设置进项税率</span
									>
								</div>
							</FormItem>
						</div>
						<div
							class="custom_form_item"
							v-if="$store.state.sysConfig.tax_rate == 1"
						>
							<FormItem label="销项税率规则：">
								<div
									v-if="taxRates && taxRates.length > 0"
									class="canClick"
									@click="goToDetail('#/taxRate/rules/detail', taxRates[0])"
								>
									<span v-if="taxRates.length == 1">{{
										taxRates[0].tax_rate_name
									}}</span>
									<span v-else>{{ taxRates[0].tax_rate_name }} 等</span>
								</div>
								<div v-else class="flex_around">
									<span class="none_class">无</span>
									<span
										class="canClick"
										@click="goToPage('#/taxRate/rules/list')"
										>设置销项税率</span
									>
								</div>
							</FormItem>
						</div>
						<div
							class="custom_form_item"
							v-for="item of customizeFields"
							:key="item.id"
						>
							<FormItem :label="item.name + '：'">
								<Input
									placeholder="请输入"
									v-model="customizeFields[item.index].value"
								/>
							</FormItem>
						</div>
						<div class="custom_form_item">
							<FormItem label="新品标记天数：">
								<InputNumber
									v-model="goodsData.new_days"
									:precision="0"
									:min="0"
									:max="99"
									style="width: 232px"
								/>
							</FormItem>
						</div>
						<div class="number-input-box">
							<FormItem
								v-has-module="'goods_tax_class_code'"
								label="税收分类编码："
								prop="tax_class_code"
							>
								<Input
									show-word-limit
									maxlength="19"
									@on-blur="InformationChange"
									v-model="goodsData.tax_class_code"
									placeholder="长度限19位数字"
								></Input>
								<span
									style="display: block;font-size:12px;"
									v-if="goodsData.tax_class_code !== '' && TaxClassification"
									>税收编码分类：{{ TaxClassification }}</span
								>
							</FormItem>
						</div>
						<div class="number-input-box tabs_tip">
							<FormItem label="标签：">
								<CheckboxGroup
									v-model="goodsData.tag"
									v-if="initData.tags && initData.tags.length > 0"
								>
									<Checkbox
										:label="info.name"
										v-for="info in initData.tags"
										:key="info.id"
									>
										<span style="margin: 0 24px 0 2px">
											{{ info.name }}</span
										></Checkbox
									>
								</CheckboxGroup>
								<span v-else>无标签可用</span>
							</FormItem>
						</div>
					</Form>
				</div>
			</base-block>
			<base-block style="margin-top: 20px" title="图片上传">
				<p class="admin-title2" style="margin-top: 2px;">
					商品主图
					<span
						>（图片尺寸：800x800px，大小≤3M，最多上传{{
							maxMainImgCount
						}}张图，支持JPG、PNG、JPEG）</span
					>
				</p>
				<div :class="dragging ? 'dragging' : ''">
					<Draggable
						v-model="goodsData.mainImages"
						@start="onDragStart"
						@end="onDragEnd"
						:options="dragOptions"
						class="upload img-upload-list"
					>
						<div
							class="demo-upload-list"
							v-for="(item, index) in goodsData.mainImages"
							:key="index"
						>
							<div class="img"><img :src="item.cdn_url + '!100x100'" /></div>
							<div class="demo-upload-list-cover">
								<template v-if="goodsData.can_edit_img">
									<i
										class="delete sui-icon icon-solid-close"
										@click="removeMainImage(item, index)"
									/>
									<img src="@/assets/images/icon/drag.png" class="drag" />
								</template>
								<i
									class="icon-img-view sui-icon view-icon"
									@click="
										previewImage(
											item.cdn_url + '!400x400',
											goodsData.mainImages,
											index
										)
									"
								/>
							</div>
						</div>
						<div slot="footer">
							<div
								class="change-upload-type"
								v-show="
									goodsData.can_edit_img && goodsData.mainImages.length < 10
								"
								@click="onUploadType('main')"
							>
								<div class="load_box">
									<i
										class="iconfont icon-add load_img"
										style="color:#909090;font-size:20px;"
									></i>
									<span class="load_text"> 上传</span>
								</div>
							</div>
							<p
								class="c-red-color"
								style="margin-top: 5px;"
								v-if="uploadError && imgType === 'main'"
							>
								{{ uploadError }}
							</p>
						</div>
					</Draggable>
				</div>
				<p class="admin-title2">
					商品主图视频上传
					<span
						>（视频尺寸比例建议为16:9，长度建议10s-60s之间，大小≤20MB，支持MP4格式，显示为商品详情主图）</span
					>
				</p>
				<div class="">
					<div
						class="videoBox videoDiv"
						v-show="goodsVideo"
						style="vertical-align: middle;"
					>
						<video
							:src="goodsVideo"
							controls="controls"
							width="200"
							height="200"
						>
							您的浏览器不支持 video 标签。
						</video>
						<div class="videoDiv-cover">
							<Icon type="ios-trash-outline" @click.native="removeVideo"></Icon>
						</div>
					</div>
					<CommonUpload
						v-if="!goodsVideo"
						ref="upload"
						:multiple="false"
						:before-upload="handleBeforeUpload"
						:max-size="20 * 1024"
						:manualHandleResponse="true"
						@on-exceeded-size="handleExceededSize"
						@on-success="videoUploadSuccess"
						:accept="mineType.video.join(',')"
						action="/superAdmin/general/upload"
						style="display: inline-block; width: 80px;position:relative;"
					>
						<div class="change-upload-type">
							<div class="load_box">
								<i
									class="iconfont icon-add load_img"
									style="color:#909090;font-size:20px;"
								></i>
								<span class="load_text" style="width: 80px;">上传</span>
							</div>
						</div>
					</CommonUpload>
				</div>
				<!-- end -->
				<!-- description begin -->
				<p class="admin-title2">
					图文详情描述
					<span
						>（图片尺寸：800x800px，大小≤3M，最多上传25张图，支持JPG、PNG、JPEG）</span
					>
				</p>
				<div :class="dragging ? 'dragging' : ''">
					<Draggable
						v-model="goodsData.detailImages"
						@start="onDragStart"
						@end="onDragEnd"
						:options="dragOptions"
						class="description img-upload-list"
					>
						<div
							class="demo-upload-list"
							v-for="(item, index) in goodsData.detailImages"
							:key="item.id"
						>
							<div class="img"><img :src="item.cdn_url + '!100x100'" /></div>
							<div class="demo-upload-list-cover">
								<template v-if="goodsData.can_edit_img">
									<i
										class="delete sui-icon icon-solid-close"
										@click="removeDetailImage(item, index)"
									/>
									<img src="@/assets/images/icon/drag.png" class="drag" />
								</template>
								<i
									class="icon-img-view sui-icon view-icon"
									@click="
										previewImage(
											item.cdn_url + '!400x400',
											goodsData.detailImages,
											index
										)
									"
								/>
							</div>
						</div>
						<div slot="footer">
							<div
								class="change-upload-type"
								v-show="
									goodsData.can_edit_img && goodsData.detailImages.length < 25
								"
								@click="onUploadType('detail')"
							>
								<div class="load_box">
									<i
										class="iconfont icon-add load_img"
										style="color:#909090;font-size:20px;"
									></i>
									<span class="load_text"> 上传</span>
								</div>
							</div>
						</div>
						<span style="position: relative; order: 3;">
							<Input
								:show-word-limit="true"
								maxlength="1000"
								class="description__textarea"
								type="textarea"
								:rows="4"
								placeholder="请输入..."
								v-model="goodsData.notice"
							/>
						</span>
					</Draggable>
					<!-- description end -->
				</div>
			</base-block>
			<div slot="footer" class="flex" style="justify-content: center">
        <Button @click="show = false">取消</Button>
				<Button type="primary" :loading="saving" @click="checkSaveData(1)">保存</Button>
			</div>
		</Modal>
		<Modal title="查看图片" width="400px" v-model="modal_preview_image">
			<img :src="preview_image" style="width: 100%" />
		</Modal>

		<Modal v-model="uploadType" title="请选择上传类型" :width="400">
			<div style="display: flex; justify-content: space-around">
				<CommonUpload
					ref="upload"
					:before-upload="handleBeforeUpload"
					:max-size="maxUploadSize"
					:manualHandleResponse="true"
					@on-exceeded-size="handleExceededSize"
					@on-success="mainImageUploadSuccess"
					:accept="mineType.image.join(',')"
					action="/superAdmin/general/upload"
					style="display: inline-block; width: 80px;position:relative;"
				>
					<div class="change-upload-type">
						<div class="load_box">
							<i
								class="iconfont icon-add load_img"
								style="color:#909090;font-size:20px;"
							></i>
							<span class="load_text" style="width: 80px;"> 本地上传</span>
						</div>
					</div>
				</CommonUpload>
				<div class="change-upload-type" @click="importOnlineImg">
					<div class="load_box">
						<i
							class="iconfont icon-add load_img"
							style="color:#909090;font-size:20px;"
						></i>
						<span class="load_text" style="width: 80px;"> 在线图库</span>
					</div>
				</div>
			</div>
		</Modal>
		<!-- 添加云商品弹框 -->
		<!-- <cloud-goods
			v-model="showCloudModel"
			@on-success="importSuccess"
		></cloud-goods> -->
		<online-img-list
			@closeOnlineImgList="closeOnlineImgList"
			@exportImg="exportImg"
			:showOnlineImgList="showOnlineImgList"
		></online-img-list>
		<setCustomModal
			@saveCustomer="saveCustomer"
			:is_open_receivable_rate="is_open_receivable_rate"
			:commodity_disabled_mode_3="commodity_disabled_mode_3"
			:changePriceDisabled="skuList[0] && skuList[0].style_use_price === 1"
			v-model="showSetModal"
			:data="modalData"
		></setCustomModal>
	</div>
</template>

<script>
	import ToolTipPrice from '@/pages/goods/components/tooltip-price.vue';
	import Draggable from 'vuedraggable';
	import setCustomModal from '@/pages/goods/components/setCustomModal.vue';
	import cloudGoods from '@/pages/goods/cloudGoods';
	import onlineImgList from '@components/common/onlineImgList';

	import { MINE_TYPE } from '@/util/const';
	import common from '@api/goods.js';
  import ConfigMixin from '@/mixins/config';
  import NumberInput from '@components/basic/NumberInput';

	export default {
		name: 'CreateGoodModal',
		mixins: [ConfigMixin],
		components: {
			ToolTipPrice,
			Draggable,
			setCustomModal,
			cloudGoods,
			onlineImgList
		},

		props: {
			searchName: {
				type: String,
				default: ''
			},
      extraParams: {
        type: Object,
        default: () => ({})
      }
		},

		data() {
			return {
        inputTaxRates:[],
        purchaseTaskUnit: '',
				taxRates: [],
				show: false,
				TaxClassification: '',
				mineType: MINE_TYPE,
				initData: {},
				commodity_id: 0,
				earlyWarnDayPercent: 0,
				central_purchase_flag_can_show: false,
				central_purchase_flag_can_edit: false,
				product_line_id: '',
				inputGoodsFocus: false, // 商品名输入框是否获取焦点
				showGoodsMouse: false, // 商品下拉列表是否鼠标移入
				goodsSearchData: [],
				goodsData: {
					is_online: true,
					central_purchase_flag: false,
					category: [],
					commodity_code: '',
					name: '',
					alias: '',
					pinyin: '',
					channel_type: '2',
					provider_id: '',
					agent_id: '',
					tag: [],
					notice: '',
					allow_change_channel: true,
					sequence: 0,
					new_days: 0,
					unit_convert: false,
					unit_change_disabled: false, //是否禁止修改 分拣时按基础单位
					hide: false,
					is_rough: 'N',
					brand: '',
					product_place: '',
					is_time_price: '0',
					mainImages: [],
					detailImages: [],
					loss_rate: 0,
					durability_period: '',
					shelf_life: 0,
					// 开启批次管理时，商品默认启用批次管理
					is_batch: this.isBatch ? '1' : '0',
					original_is_batch: 0,
					unit_group: '',
					skusPrice: '',
					tax_class_code: '',
					provider_supervisor: '',
					can_edit_img: true,
					early_warn_day: 0
				},
				rules: {},
				skuList: [], // sku列表
				remoteSkuList: [], // 服务端的sku数据
				modifyCommodityWithoutLimit: false,
				comCap: 0,
				showOldCustomer: true,
				isInput: '', // 判断是否输入编码
				settingNum: [], // 自定义库存需要的数组
				dragOptions: {
					dragClass: 'dragging',
					ghostClass: 'dragging-ghost',
					chosenClass: 'dragging-chosen',
					handle: '.drag'
				},
				maxUploadSize: 3 * 1024,
				maxMainImgCount: 10,
				maxDetailImgCount: 25,
				saving: false,
				dragging: false,
				uploadError: '',
				goodsVideo: '', // 商品主图视频
				customizeFields: [],
				workingProcedureList: [
					{
						working_procedure_id: '',
						product_line_id: '',
						cost: ''
					}
				],
				orgUnitInfo: [],
				volume: {
					columns: [
						{
							title: '单位',
							key: 'unit'
						},
						{
							title: '体积设置',
							width: 600,
							render: (h, params) => {
								let { row } = params;
								let inputStyle = {
									width: '120px',
									display: 'inline-block',
									margin: '0 5px'
								};
								return h(
									'Row',
									{
										props: {
											type: 'flex',
											align: 'middle',
											gutter: 5
										}
									},
									[
										h(
											'Col',
											{
												props: {
													span: 8
												}
											},
											[
												h('span', '长'),
												h(NumberInput, {
													props: {
														value: Number(row.length),
														span: 8
													},
													style: inputStyle,
													on: {
														'on-change': value => {
															params.row.length = value;
															this.volume.storeData[params.index].length = value;
														}
													}
												}),
												h('span', 'cm')
											]
										),
										h(
											'Col',
											{
												props: {
													span: 8
												}
											},
											[
												h('span', '宽'),
												h(NumberInput, {
													props: {
														span: 8,
														value: Number(row.width)
													},
													style: inputStyle,
													on: {
														'on-change': value => {
															params.row.width = value;
															this.volume.storeData[params.index].width = value;
														}
													}
												}),
												h('span', 'cm')
											]
										),
										h(
											'Col',
											{
												props: {
													span: 8
												}
											},
											[
												h('span', '高'),
												h(NumberInput, {
													props: {
														value: Number(row.height)
													},
													style: inputStyle,
													on: {
														'on-change': value => {
															params.row.height = value;
															this.volume.storeData[params.index].height = value;
														}
													}
												}),
												h('span', 'cm')
											]
										)
									]
								);
							}
						},
						{
							title: '重量',
							render: (h, params) => {
								let { row } = params;
								let kgStyle = {
									margin: '0 5px'
								};
								return h('div', {}, [
									h(NumberInput, {
										props: {
											value: Number(row.weight)
										},
										on: {
											'on-change': value => {
												params.row.weight = value;
												this.volume.storeData[params.index].weight = value;
											}
										}
									}),
									h(
										'span',
										{
											style: kgStyle
										},
										'kg'
									)
								]);
							}
						}
					],
					data: [],
					storeData: []
				},
				uploadType: false,
				showOnlineImgList: false,
				imgType: '',
				skuIndex: 0,
				showSetModal: false,
				modalData: {},
				showCloudModel: false,
				modal_preview_image: false,
				preview_image: '',
			};
		},

		computed: {
      isIncreaseMode() {
        return Number(this.sysConfig.commodity_code_gen_mode) === 1;
      },
			purchaseTaskUnitSelectShow() {
				return (
					this.isOpenPurchaseTaskUnit &&
					this.isEnablePurchaseTask &&
					!this.goodsData.unit_convert
				);
			},
			unites() {
				let result = [];
				let find = false;
				this.skuList.map(item => {
					if (item.unit) {
						result.push({
							name: item.unit
						});
					}
					if (item.unit === this.purchaseUnite) {
						find = true;
					}
				});

				if (!find && result[0]) {
					this.purchaseUnite = result[0].name;
				}

				return result;
			},
			// 只有一个单位可售卖
			isOnlyOneSkuCanSell() {
				let canSellItems = this.skuList.filter(item => item.is_sell);
				return canSellItems.length === 1;
			},
			enableBasket() {
				return this.isEnableBasket;
			},
			isBatch() {
				return this.isEnableBatch;
			},
			isBatchEditAble() {
				return this.goodsData.original_is_batch === 0;
			}
		},

		watch: {
      show(show) {
        if (!show) {
          this.$emit('on-close');
        }
      },
			isEnablePurchaseTask() {
				this.initRules();
			},
      skuList: {
        deep: true,
        handler() {
          // 采购任务指定采购单位默认取首个辅助单位
          if (this.skuList.length >= 2) {
            this.purchaseTaskUnit = this.skuList[1].unit;
          } else {
            // 如果没有辅助单位，则取基础单位
            this.purchaseTaskUnit = this.skuList[0] && this.skuList[0].unit;
          }
        },
      },
		},

		created() {
			this.init();
		},
		mounted() {
			if (this.storage.getLocalStorage('showOldCustomerModal') == false) {
				this.showOldCustomer = this.storage.getLocalStorage(
					'showOldCustomerModal'
				);
			} else {
				this.showOldCustomer = true;
			}
		},

		methods: {
			init() {
				this.initRules();
			},
			/**
			 * 关键字搜索商品列表
			 * @param value
			 */
			searchGoodsList(e) {
				this.$request
					.get(this.apiUrl.getCloudGoodsList, {
						importStatus: 2,
						spuNameLike: e.target.value
					})
					.then(res => {
						if (res.status) {
							this.goodsSearchData = res.data.list;
						} else {
							this.$Notice.error({ title: res.message });
						}
					});
				// const cloud = this.goodsSearchData.filter(goods => goods.isCloud);
				// if (cloud && cloud.length > 0) {
				// }
			},
			/**
			 * 选中具体的商品
			 */
			selectGoods(goods) {
				this.isInput = 1;
				this.cloudGoods = goods;
				this.goodsData.name = goods.spuName;
				this.goodsData.commodity_code = goods.spuCode;
				this.goodsData.is_rough = Number(goods.spuType) === 1 ? 'Y' : 'N';
				this.goodsData.alias = goods.goodsAlias
					.map(alias => alias.spuAliasName)
					.join('，');
				this.goodsData.mainImages = goods.goodsPictures.map(p => {
					p.url = p.pic.substr(p.pic.indexOf('com/') + 4);
					p.cdn_url = p.pic;
					return p;
				});
				this.showGoodsMouse = false;
				const unitIndex = this.initData.units.findIndex(
					unit => unit.name === goods.unitName
				);
				if (unitIndex !== -1) {
					// 有该单位，直接添加一个对应单位的sku
					this.skuList = [];
					this.addSku(goods.unitName);
				} else {
					// 没有该单位，先系统新增这个单位，然后再添加对应单位的sku
					this.$request
						.post(this.apiUrl.saveGoodsUnit, { name: goods.unitName })
						.then(res => {
							if (res.status) {
								// this.getInitData(); // 新增单位成功后，重新请求单位列表
								this.initData.units.push(res.data);
								this.skuList = [];
								this.addSku(goods.unitName);
							} else {
								this.$Notice.error({ title: res.message });
							}
						});
				}
			},
			changeInput() {
				this.isInput = 1;
			},
			validateForm() {
				this.$refs.form.validate();
			},
			initRules() {
				let rules = {
					category: [
						{
							required: true,
							validator: (rule, value, callback) => {
								if (!value || value.length < 2) {
									callback(new Error('请选择分类'));
								} else {
									callback();
								}
							}
						}
					],
					name: [
						{
							required: true,
							message: '请填写商品名称',
							trigger: 'change'
						}
					],
					channel_type_value: [
						{
							required: true,
							validator: (rule, value, callback) => {
								const CHANNEL_TYPE_AGENT = 1;
								const CHANNEL_TYPE_PROVIDER = 2;
								if (
									Number(this.goodsData.channel_type) === CHANNEL_TYPE_AGENT &&
									!this.goodsData.agent_id
								) {
									callback(new Error('请选择采购员'));
								} else if (
									Number(this.goodsData.channel_type) === CHANNEL_TYPE_PROVIDER &&
									!this.goodsData.provider_id
								) {
									callback(new Error('请选择供应商'));
								} else {
									callback();
								}
							}
						}
					],
					commodity_code: [
						{
							required: true,
							message: '请填写商品编码',
							trigger: 'change'
						}
					],
					channel_type: [
						{
							required: true,
							message: '请选择采购类型',
							trigger: 'change'
						}
					],
					tax_class_code: [
						{
							required: false,
							message: '税收分类编码为19位数字',
							trigger: 'change'
						},
						{
							type: 'string',
							min: 19,
							message: '税收分类编码为19位数字',
							trigger: 'change'
						}
					],
					earlyWarnDayCheck: [
						{
							validator: (rule, value, callback) => {
								if (
									!this.goodsData.shelf_life &&
									this.goodsData.early_warn_day > 0
								) {
									callback(new Error('保质期为0,不能设置过期预警天数'));
								}
								if (this.goodsData.early_warn_day > this.goodsData.shelf_life) {
									callback(new Error('预警天数不能大于保质期'));
								}
								callback();
							},
							trigger: 'blur'
						}
					]
				};
				if (this.isEnablePurchaseTask) {
					rules.provider_supervisor = [
						{
							required: true,
							message: '请选择采购负责人',
							trigger: 'change'
						}
					];
				}
				this.rules = rules;
			},
			async getInitData() {
				const convertToChildren = treeData => {
					if (Array.isArray(treeData)) {
						return treeData.map(convertToChildren);
					}

					if (typeof treeData === 'object' && treeData !== null) {
						const children = treeData.items
							? convertToChildren(treeData.items)
							: [];
						return {
							...treeData,
							value: treeData.id,
							label: treeData.name,
							children: children.map(convertToChildren)
						};
					}

					return treeData;
				};
				let res = await common.getGoodsData();
				if (res.status) {
					this.initData = res.data || {};
					const customize_fields = this.initData.customize_fields || [];
					this.customizeFields = customize_fields.map((item, index) => ({
						...item,
						value: '',
						index, // 记录序号
						show: true
					}));
					this.central_purchase_flag_can_show =
						res.data.central_purchase_flag_data.central_purchase_flag_can_show;
					this.central_purchase_flag_can_edit =
						res.data.central_purchase_flag_data.central_purchase_flag_can_edit;
					this.initData.productLines.forEach(pro => {
						if (pro.is_default == 1) {
							this.product_line_id = pro.id;
						}
					});
					//商品分类控件要求特殊的格式，重新组装下
					this.initData.categoryTree = [];
					if (this.isOpenCommodityCategoryThree) {
						this.initData.categoryList.forEach(item => {
							if (item.items && item.items.length > 0) {
								item.items.forEach(item2 => {
									item2.items = item2.items || [];
									item2.items.unshift({
										id: '0',
										name: '未设置',
										level: '3'
									});
								});
							}
						});
					}
					this.initData.categoryTree = convertToChildren(
						this.initData.categoryList
					);
					this.addSku();
				}
			},
      async setCommodityCodeWidthRules(category) {
        if (!this.isIncreaseMode) return;
        const res = await this.$request.get(this.apiUrl.genCommodityCodeIncreaseMode, {
          category_id2: category[1],
        })
        if (res.status) {
          const { commodity_code } = res.data;
          this.goodsData.commodity_code = commodity_code;
        } else {
          this.errorMessage(res.message || '获取商品编码失败');
        }
      },
			async autoSetCode() {
				this.inputGoodsFocus = false;
        if (this.isIncreaseMode) return;
				// 如果没有输入商品名称并且没有输入编码
				if (!this.goodsData.name && !this.isInput) {
					this.goodsData.commodity_code = '';
					return;
				}
				if (this.showGoodsMouse) return;
				if (this.isInput) return;
				let res = await common.autoCode(this.goodsData.name);
				if (res.status) {
					let data = res.data;
					this.goodsData.commodity_code = data.commodityCode;
				} else {
					this.errorNotice(res.message);
				}
			},
			resetInput() {
				this.goodsData = {
					category: [],
					commodity_code: '',
          name: '',
					alias: '',
					pinyin: '',
					channel_type: '2',
					provider_id: '',
					agent_id: '',
					tag: [],
					notice: '',
					allow_change_channel: true,
					sequence: 0,
					new_days: 0,
					unit_convert: false,
					unit_change_disabled: false, //是否禁止修改 分拣时按基础单位
					hide: false,
					is_rough: 'N',
					brand: '',
					product_place: '',
					is_time_price: '0',
					mainImages: [],
					detailImages: [],
					is_online: true,
					loss_rate: 0,
					durability_period: '',
					shelf_life: 0,
					// 开启批次管理时，商品默认启用批次管理
					is_batch: this.isBatch ? '1' : '0',
					original_is_batch: 0,
					unit_group: '',
					skusPrice: '',
					tax_class_code: '',
					provider_supervisor: '',
					can_edit_img: true,
					early_warn_day: 0
				};

        console.log('name----', this.goodsData.name)
				this.commodity_id = 0;
				//sku列表
				this.skuList = [];
				this.initData = {};
				this.modal_preview_image = false;
				this.preview_image = '';
				this.comCap = 0;
				this.getInitData();
				// 加工品不支持无限制修改
				this.modifyCommodityWithoutLimit =
					this.isCanModifyCommodityWithoutLimit && +this.comCap !== 2;
			},
			addSku(unit) {
				unit = unit instanceof MouseEvent ? '' : unit;
				let sku = {
					id: 0,
					uuid: Math.random(),
					unit: unit || '',
					unit_num: 0,
					summary: '',
					order_quantity: 0,
					order_quantity_type: 1,
					price: 0,
					receivable_style: [],
					sell_stock: 0,
					is_sell_stock: '0',
					is_sell: true,
					product_line_id: this.product_line_id,
          style_use_price: 0,
          init_sale_num: '',
          in_price_low_rate: '',
          is_time_price: '0'
				};
				if (this.printLabelMode === 2) sku.pack_num = 0;
				//加载客户类型，价格默认为0
				for (let i = 0; i < this.initData.receStyle.length; i++) {
					sku.receivable_style.push({
						id: this.initData.receStyle[i].id,
						name: this.initData.receStyle[i].name,
						price: 0,
						// 折前价
						receivable_org_price: '',
						// 折扣率
						receivable_price_rate: '',
						// 是否上下架(默认上架)
						receivable_is_online: '1'
					});
				}
				if (this.goodsData.unit_convert) {
					sku.is_sell_stock = this.skuList[0].is_sell_stock;
					sku.sell_stock = (this.skuList[0].sell_stock / 1).toFixed(2);
					sku.product_line_id = this.skuList[0].product_line_id;
				}
				this.skuList.push(sku);
			},
			resetEarlyWarnDayPercent() {
				let value = this.earlyWarnDayPercent;
				if (value > 100) {
					this.earlyWarnDayPercent = 0;
					this.$set(this.goodsData, 'early_warn_day', 0);
				}
			},
			resetEarlyWarnDay() {
				let value = this.goodsData.early_warn_day;
				if (value > this.goodsData.shelf_life) {
					this.earlyWarnDayPercent = 0;
					this.$set(this.goodsData, 'early_warn_day', 0);
				}
			},
			updateEarlyWarnDayPercent() {
				let cutValue = this.$refs.updateEarlyWarnDayRef.currentValue;
				if (this.goodsData.early_warn_day < 0) {
					this.$set(this.goodsData, 'early_warn_day', 0);
					this.$refs.updateEarlyWarnDayRef.currentValue = 0;
				}
				this.$set(
					this.goodsData,
					'early_warn_day',
					String(cutValue).replace(/[^\.^\d]/g, '')
				);
				this.$refs.updateEarlyWarnDayRef.currentValue = String(cutValue).replace(
					/[^\.^\d]/g,
					''
				);
				if (!this.goodsData.shelf_life) {
					this.earlyWarnDayPercent = 0;
				} else {
					this.earlyWarnDayPercent = Number(
						(
							(this.goodsData.early_warn_day / this.goodsData.shelf_life) *
							100
						).toFixed(2)
					);
				}
				this.$set(
					this.goodsData,
					'early_warn_day',
					Number(String(cutValue).replace(/[^\.^\d]/g, ''))
				);
			},
			updateEarlyWarnDay() {
				if (this.earlyWarnDayPercent < 0 || isNaN(this.earlyWarnDayPercent)) {
					this.earlyWarnDayPercent = 0;
					this.$refs.updateEarlyWarnDayPercentRef.currentValue = 0;
				}
				this.goodsData.early_warn_day = Math.ceil(
					(this.goodsData.shelf_life * this.earlyWarnDayPercent) / 100
				);
			},
			async removeSku(item, index) {
				if (
					item.unit === this.purchaseUnite &&
					this.purchaseUnite !== '' &&
					this.goodsData.unit_convert
				) {
					this.$Modal.error({
						title: '错误',
						content: '已经设置为统一采购单位，不能删除！'
					});
					return;
				}
				let self = this;
				if (item.id != 0) {
					// 检查是否有库存
					const res = await common.checkSkuIsExistStock(item.id);
					//修改时删除，先调用接口删除.
					this.$Modal.confirm({
						title: '确定',
						content: `<p>${
							+res.data === 1
								? '当前商品单位存在系统库存，请确认删除操作'
								: '确定删除该单位'
						}</p>`,
						async onOk() {
							let res = await common.delGoods(item.id);
							if (res.status) {
								self.skuList.splice(index, 1);
								self.successNotice('单位删除成功');
							} else {
								self.errorNotice({
									title: '删除失败',
									desc: res.message
								});
							}
						},
						onCancel: () => {}
					});
				} else this.skuList.splice(index, 1);

				if (this.skuList.length == 1 && !this.commodity_id) {
					this.goodsData.unit_convert = false;
				}
			},
			chooseUnit(unit, skuIndex) {
				// 基础单位
				if (skuIndex === 0) {
					this.goodsData.unit = unit;
					this.purchaseUnite = this.purchaseUnite ? this.purchaseUnite : unit;
				}
				this.initVolumeData();
				this.skuList[skuIndex].unit = unit;
			},
			initVolumeData() {
				console.log(this.skuList, '--/');
				this.volume.data = this.skuList;
				this.syncVolumeData();
			},
			syncVolumeData() {
				let list = this.cloneObj(this.volume.data);
				list.forEach(item => {
					let storeItem = this.volume.storeData.find(
						storeItem => storeItem.unit === item.unit
					);
					if (storeItem) {
						item.length = storeItem.length;
						item.width = storeItem.width;
						item.height = storeItem.height;
						item.weight = storeItem.weight;
					}
				});
				this.volume.data = this.cloneObj(list);
				this.volume.storeData = this.cloneObj(list);
			},
			async InformationChange() {
				let param = {
					tax_class_code: this.goodsData.tax_class_code
				};
				let { status, data } = await get(api.invoice.getTaxClassCode, param);
				if (status == 1) {
					if (data.length > 0) {
						this.TaxClassification = data[0].name ? data[0].name : '';
					} else {
						this.TaxClassification = '未匹配到该税收分类编码对应的分类，请确认';
					}
				} else {
					this.TaxClassification = '未匹配到该税收分类编码对应的分类，请确认';
				}
			},
			onDragStart() {
				this.dragging = true;
			},
			onDragEnd() {
				this.dragging = false;
			},
			handleBeforeUpload() {
				this.uploadError = '';
				return true;
			},
			handleExceededSize() {
				this.uploadError = `仅支持小于${this.maxUploadSize / 1024}M文件`;
			},
			handleImageUploadFormatError() {
				this.errorNotice(
					'上传的图片文件格式不正确,请选择[jpg,jpeg,png]图片文件.'
				);
			},
			videoUploadSuccess(res, meta, file, fileList) {
				console.log(res, meta, file, fileList);

				if (!res.status) {
					this.errorNotice({
						title: '视频上传失败',
						desc: res.message
					});
					return;
				}
				this.successMessage('视频上传成功');
				this.goodsVideo = res.data.upyun_url;
			},
			removeVideo() {
				this.goodsVideo = '';
			},
			mainImageUploadSuccess(response, meta, file, fileList) {
				this.uploadType = false;
				if (!response.status) {
					this.errorNotice({
						title: '图片上传失败',
						desc: response.message
					});
					return;
				}

				file.upload_info = response;
				const allUploadSuccess = fileList.every(item => !!item.upload_info);
				if (!allUploadSuccess) {
					return false;
				}
				fileList.forEach(item => {
					let res = item.upload_info;
					if (this.imgType === 'main') {
						if (this.goodsData.mainImages.length >= this.maxMainImgCount) {
							this.errorNotice({
								title: '图片上传失败',
								desc: `最多只能上传${this.maxMainImgCount}张主图`
							});
							return false;
						}
						this.goodsData.mainImages.push({
							url: res.data.server_url,
							cdn_url: res.data.upyun_url
						});
					} else {
						if (this.goodsData.detailImages.length >= this.maxDetailImgCount) {
							this.errorNotice({
								title: '图片上传失败',
								desc: `最多只能上传${this.maxDetailImgCount}张详情图`
							});
							return false;
						}
						this.goodsData.detailImages.push({
							url: res.data.server_url,
							cdn_url: res.data.upyun_url
						});
					}
				});
				this.successNotice('图片上传成功.');
			},
			detailImageUploadSuccess(res) {
				if (!res.status) {
					this.errorNotice({
						title: '图片上传失败',
						desc: res.message
					});
					return;
				}
				this.successNotice('图片上传成功.');
				this.goodsData.detailImages.push({
					url: res.data.server_url,
					cdn_url: res.data.upyun_url
				});
			},
			mainLocalImageUploadSuccess(res) {
				if (!res.status) {
					this.errorNotice({
						title: '图片上传失败',
						desc: res.message
					});
					return;
				}
				this.successNotice('图片上传成功.');
				this.goodsData.mainImages.push({
					url: res.data.server_url,
					cdn_url: res.data.upyun_url
				});
			},

			removeMainImage(item, index) {
				this.goodsData.mainImages.splice(index, 1);
			},

			removeDetailImage(item, index) {
				this.goodsData.detailImages.splice(index, 1);
			},

			previewImage(image, _images, _viewIndex = 0) {
				// this.preview_image = image;
				// this.modal_preview_image = true;
				const images = _images.map(item => {
					return item.cdn_url + '!400x400';
				});
				this.viewImage(images, _viewIndex);
			},
			readonlyNum(item, index) {
				if (this.goodsData.unit_convert) {
					if (item.unit_num) {
						this.skuList[index].sell_stock = (
							this.skuList[0].sell_stock / item.unit_num
						).toFixed(2);
					}
				}
			},
			onUploadType(type) {
				this.imgType = type;
				this.uploadType = true;
				this.$refs.upload.clearFiles();
			},
			importOnlineImg() {
				this.uploadType = false;
				this.showOnlineImgList = true;
			},
			closeOnlineImgList() {
				this.showOnlineImgList = false;
			},
			exportImg(img) {
				console.log(img);
				this.showOnlineImgList = false;
				if (this.imgType === 'main') {
					this.goodsData.mainImages.push({
						cdn_url: img.host + '/' + img.path,
						url: img.path
					});
				} else {
					this.goodsData.detailImages.push({
						url: img.path,
						cdn_url: img.host + '/' + img.path
					});
				}
			},
			saveCustomer(row) {
				this.skuList[this.skuIndex] = row;
			},
			openSetCustomer(item, index) {
				if (item.receivable_style && item.receivable_style.length > 0) {
					this.skuIndex = index;
					this.modalData = this.deepClone(item);
					this.showSetModal = true;
				}
			},
			changeMethod(item) {
				// if(this.commodity_id==0){
				//   return
				// }
				switch (item.is_sell_stock) {
					case '0':
						for (let i = 0; i < this.skuList.length; i++) {
							if (item.id == this.skuList[i].id) {
								this.skuList[i].sell_stock = 0;
							}
						}
						break;
					case '1':
						for (let i = 0; i < this.skuList.length; i++) {
							if (item.id == this.skuList[i].id) {
								this.skuList[i].sell_stock = Number(
									typeof this.settingNum[i] === 'undefined'
										? 0
										: this.settingNum[i]
								);
							}
						}
						break;
					case '2':
						for (let i = 0; i < this.skuList.length; i++) {
							if (item.id == this.skuList[i].id) {
								this.skuList[i].sell_stock = 0;
							}
						}
						break;
				}
				if (this.goodsData.unit_convert) {
					for (let i = 0; i < this.skuList.length; i++) {
						this.skuList[i].is_sell_stock = this.skuList[0].is_sell_stock;
						if (this.skuList[i].unit_num && this.skuList[0].sell_stock) {
							this.skuList[i].sell_stock = (
								(this.skuList[0].sell_stock * 100) /
								this.skuList[i].unit_num /
								100
							).toFixed(2);
						}
					}
				}
			},
			changeProcessLine(item) {
				if (this.goodsData.unit_convert) {
					for (let i = 0; i < this.skuList.length; i++) {
						this.skuList[i].product_line_id = this.skuList[0].product_line_id;
					}
				}
			},
			/**
			 * @description: 折前价、折扣率、折后价变更
			 * @param {*} type
			 * @param {*} row
			 * @return {*}
			 * @author: lizi
			 */
			$_onCompuntePrice(type, row) {
				if (this.is_open_receivable_rate) {
					const {
						receivable_org_price = '',
						receivable_price_rate = '',
						price = ''
					} = row;
					if (type === 1) {
						// 折前价修改
						if (receivable_price_rate) {
							// 折扣率有值，计算折后价
							if (receivable_org_price == null) {
								row.price = '';
							} else {
								row.price = (
									receivable_org_price *
									(receivable_price_rate / 100)
								).toFixed(4);
							}
						} else {
							// 折扣率没有值
							if (price) {
								// 折后价有值，计算折扣率
								if (receivable_org_price == null || receivable_org_price == 0) {
									// 折扣价为空、折扣率为空
									row.receivable_price_rate = '';
								} else {
									row.receivable_price_rate = (
										(price / receivable_org_price) *
										100
									).toFixed(4);
								}
							}
						}
					} else if (type === 2) {
						// 折扣率修改
						if (receivable_price_rate) {
							if (price) {
								// 折扣率、折后价都有值，计算折前价
								row.receivable_org_price = (
									price /
									(receivable_price_rate / 100)
								).toFixed(4);
							} else {
								row.price = (
									receivable_org_price *
									(receivable_price_rate / 100)
								).toFixed(4);
							}
						}
					} else if (type === 3) {
						// 折后价修改
						if (receivable_price_rate) {
							// 折扣率有值，计算折前价
							if (price === null || price === '') {
								row.receivable_org_price = '';
							} else {
								row.receivable_org_price = (
									price /
									(receivable_price_rate / 100)
								).toFixed(4);
							}
						}
					}
				}
			},
			/**
			 * @description: 校验sku价格数据填写规则
			 * @param {*}
			 * @return {*}
			 * @author: lizi
			 */
			checkSkuPrice() {
				if (!this.is_open_receivable_rate) return true;
				let errorInfo = '';
				this.skuList.forEach(row => {
					const { receivable_style = [] } = row;
					for (var i = 0, len = receivable_style.length; i < len; i++) {
						const {
							receivable_org_price = '',
							receivable_price_rate = '',
							price = ''
						} = receivable_style[i];
						if (receivable_org_price && receivable_price_rate === '') {
							// 折前价有值，折扣率无值
							errorInfo = '商品中有客户类型折前价没有填写折扣率无法保存';
							break;
						} else if (
							receivable_price_rate &&
							(receivable_org_price === '' || price === '')
						) {
							// 折扣率有值，折前、折后其中一个无值
							errorInfo =
								'商品中有客户类型仅填写有折扣率没有折前或者折后价无法保存';
							break;
						} else if (
							!receivable_org_price &&
							!receivable_price_rate &&
							(price === '' || price === null)
						) {
							// 都没有值
							// errorInfo = '商品中有客户类型折后价没有填写无法保存'
							break;
						}
					}
				});
				if (errorInfo) {
					this.errorNotice({
						title: '保存失败',
						desc: errorInfo
					});
					return false;
				}
				return true;
			},
			showOldCustomerFn() {
				this.showOldCustomer = !this.showOldCustomer;
				this.storage.setLocalStorage(
					'showOldCustomerModal',
					this.showOldCustomer
				);
			},

			scrollToError() {
				this.$nextTick(() => {
					let firstError = document.querySelector('.ivu-form-item-error');
					if (firstError) {
						firstError.scrollIntoView();
					}
				});
			},
			checkSaveData(type) {
				// console.log('form----', this.$refs.form.validate)
				this.$refs.form.validate(valid => {
					console.log('valid----', valid);
					if (valid) {
						this.saveData(type);
					} else {
						this.scrollToError();
					}
				});
			},
			async saveData(type) {
				if (!this.checkInput()) {
					return;
				}
				if (!this.checkPrice()) {
					return false;
				}
				if (Number(this.goodsData.loss_rate) < 0) {
					this.errorNotice('损耗率必须设置大于或者等于0');
					return;
				}
				if (!this.checkSkuPrice()) {
					return;
				}
				let saveData = {
					category: this.goodsData.category,
					commodity_code: this.goodsData.commodity_code,
					name: this.goodsData.name,
					alias: this.goodsData.alias,
					tax_class_code: this.goodsData.tax_class_code,
					pinyin: this.goodsData.pinyin,
					loss_rate: this.goodsData.loss_rate,
					channel_type: this.goodsData.channel_type,
					provider_id: this.goodsData.provider_id,
					agent_id: this.goodsData.agent_id,
					tag: this.goodsData.tag.join(','),
					notice: this.goodsData.notice,
					sequence: this.goodsData.sequence,
					new_days: this.goodsData.new_days,
					is_rough: this.goodsData.is_rough,
					brand: this.goodsData.brand,
					product_place: this.goodsData.product_place,
					is_time_price: this.goodsData.is_time_price,
					allow_change_channel: this.goodsData.allow_change_channel ? 1 : 0,
					hide: this.goodsData.hide ? 1 : 0,
					unit_convert: this.goodsData.unit_convert ? 'Y' : 'N',
					is_online: this.goodsData.is_online ? 'Y' : 'N',
					durability_period: this.goodsData.durability_period,
					shelf_life: this.goodsData.shelf_life,
					is_process: this.comCap == 2 ? 1 : 0,
					is_batch: this.goodsData.is_batch,
					unit_group: this.goodsData.unit_group,
					provider_supervisor: this.goodsData.provider_supervisor,
					central_purchase_flag:
						this.goodsData.central_purchase_flag == true ? 1 : 0,
					early_warn_day: this.goodsData.early_warn_day
				};
				this.earlyWarnDayPercent = 0;
				// 加工品暂不支持批次
				if (this.comCap == 2) {
					saveData.is_batch = '0';
				}
				if (this.purchaseTaskUnitSelectShow) {
					saveData.purchase_task_unit = this.purchaseTaskUnit;
				}
				// 如果自定义字段至少存在一个
				if (this.customizeFields.length && this.customizeFields[0].show) {
					saveData.customize_fields = [];
					this.customizeFields.map(item => {
						if (item.show) {
							let obj = Object.assign({}, item);
							delete obj.index;
							delete obj.show;
							obj.value = obj.value || '';
							saveData.customize_fields.push(obj);
						}
					});
				}
				if (this.goodsData.unit_convert) {
					saveData.purchase_unit =
						this.comCap == 0 ? this.purchaseUnite || '' : '';
				} else {
					saveData.purchase_unit = '';
				}

				// 加工品已经勾选分拣时按基础单位时，才传 purchase_unit
				if (+this.comCap === 2 && this.goodsData.unit_convert) {
					saveData.purchase_unit = this.purchaseUnite;
				}

				// 商品编码检查
				if (saveData.commodity_code) {
					saveData.commodity_code = saveData.commodity_code.trim();
				}

				let valid = true,
					error = '';
				let deleteSku = [];
				const saveSkuList = this.deepClone(this.skuList);
				saveSkuList.forEach((sku, index) => {
					if (this.cloudGoods && !this.commodity_id) {
						// 选择的云商品并且是新增
						sku.commodity_cloud_id = this.cloudGoods.spuCode;
						sku.commodity_sku_id = this.cloudGoods.unitCode;
					}
					if (sku.unit_num <= 0 && index != 0) {
						error = `辅助单位${sku.unit}转换数量必须大于0`;
						valid = false;
					}
					if (index != 0) {
						sku.style_use_price = saveSkuList[0].style_use_price;
					}
					sku.is_sell = sku.is_sell
						? common.const.SKU_CAN_SELL
						: common.const.SKU_CAN_NOT_SELL;
					if (sku.id) {
						let remoteSku = this.remoteSkuList.find(item => item.id === sku.id);
						// 修改了单位
						if (
							remoteSku &&
							remoteSku.unit !== sku.unit &&
							this.goodsData.unit_change_disabled
						) {
							deleteSku.push(sku.id);
							sku.id = '';
						}
						// 基础单位
						if (this.remoteSkuList.length === 1) {
							deleteSku = [];
						}
					}
					sku.receivable_style &&
						sku.receivable_style.forEach(item => {
							item.price = item.price === null ? '' : item.price;
							item.receivable_org_price =
								item.receivable_org_price === null
									? ''
									: item.receivable_org_price;
							item.receivable_price_rate =
								item.receivable_price_rate === null
									? ''
									: item.receivable_price_rate;
            });

          if (this.sysConfig.provider_in_price_low_rate == 0 || sku.in_price_low_rate === '') {
            delete sku.in_price_low_rate
          }
          if (this.goodsData.is_time_price != 2) {
            sku.is_time_price = this.goodsData.is_time_price
          }
				});
				// 没有产生业务数据直接修改原来单位、不执行单位删除逻辑
				if (!this.goodsData.unit_change_disabled) {
					deleteSku = [];
				}
				if (!valid) {
					this.errorNotice(error);
					return false;
				}

				let detailImages = [];
				for (let i = 0; i < this.goodsData.detailImages.length; i++) {
					detailImages.push(this.goodsData.detailImages[i].url);
				}
				let mainImages = [];
				this.goodsData.mainImages.forEach(item => {
					mainImages.push(item.url);
				});

				let title = '新增';
				if (this.commodity_id > 0) title = '修改';

				if (this.saving) {
					return false;
				}
				this.saving = true;
				let workingProcedureList = this.workingProcedureList;
				// eslint-disable-next-line no-unused-vars
				this.workingProcedureList.forEach((ites, inde) => {
					if (
						ites.working_procedure_id == '' ||
						(ites.working_procedure_id == undefined && ites.cost == '')
					) {
						workingProcedureList = '';
					}
				});
				let res = await common.saveGoods(
					this.commodity_id,
					saveData,
					saveSkuList,
					workingProcedureList,
					detailImages,
					mainImages.length > 0 ? mainImages : [''],
					deleteSku.join(','),
					this.goodsVideo
				);
				if (res.status) {
					if (type === 1) {
            const commodityId = res.data.id;
            this.getSearchCommodity(commodityId);
          } else {
            this.resetInput();
            this.$refs.form.resetFields();
            this.isInput = ''; // 继续新增允许商品编码自动生成
          }
				} else {
					this.saving = false;
					this.errorNotice({
						title: '保存失败',
						desc: res.message
					});
				}
			},
			checkInput() {
				try {
					if (
						this.goodsData.channel_type == 1 &&
						(!this.goodsData.agent_id || this.goodsData.agent_id == 0)
					) {
						throw '请选择采购员.';
					}

					if (
						this.goodsData.channel_type == 2 &&
						(!this.goodsData.provider_id || this.goodsData.provider_id == 0)
					) {
						throw '请选择供应商.';
					}
					//检查单位是否正确
					if (!this.skuList || this.skuList.length == 0) {
						throw '请至少设置一个单位.';
					}
					if (!this.skuList[0].unit) {
						throw '基础单位名称不能为空.';
					}
					for (let i = 0; i < this.skuList.length; i++) {
						if (!this.skuList[i].unit) {
							if (i == 0) throw '基础单位名称不能为空.';
							else throw '辅助单位' + i + '名称不能为空.';
						}

						if (this.skuList[i].price == 0) {
							if (i == 0) throw '基础单位的市场价不能为0.';
							else throw '辅助单位' + i + '的市场价不能为0.';
						}
					}
				} catch (err) {
					this.errorNotice({
						title: '保存失败',
						desc: err
					});
					return false;
				}
				return true;
			},
			checkPrice() {
				try {
					this.skuList.map(sku => {
						Object.keys(sku.receivable_style).map(key => {
							const userType = sku.receivable_style[key];
							if (!userType.price === '') {
								throw `单位${sku.unit}下的客户类型${userType.name}价格不能为空！`;
							}
							//if (!userType.price || Number(userType.price) <= 0) {
							//  throw `单位${sku.unit}下的客户类型${userType.name}必须大于0`;
							//}
						});
					});
				} catch (err) {
					this.errorNotice(err);
					return false;
				}
				return true;
			},

			async getSearchCommodity(id) {
				const url = this.apiUrl.getCommodity;
				const params = {
					commodity_id: id,
          ...this.extraParams,
				};
				const { status, message, data } = await this.$request.get(url, params);
				if (!status) {
					this.errorMessage(message);
          this.saving = false;
					return;
				}
				if (!data.commodities[0]) {
					this.errorMessage('该客户不可下单此商品！');
				}
        this.saving = false;
				this.emitCreateGood(data.commodities[0]);
			},
			showModal() {
				this.resetInput();
        this.$nextTick(() => {
          if (this.$refs.form) {
            this.$refs.form.resetFields();
          }
          this.goodsData.name = this.searchName
          this.autoSetCode()
        })
				this.show = true;
			},
			closeModal() {
				this.show = false;
			},
			emitCreateGood(newGood) {
				this.successMessage(`【${this.goodsData.name}】商品快速创建成功，录单完成后前往商品档案可编辑更多信息`);
				this.closeModal();
				this.$emit('createGood', newGood);
			}
		}
	};
</script>
<style lang="scss">
.create-good-modal {
	.ivu-modal-body {
		padding: 0px 24px 0px 24px !important;
    height: 60vh;
    overflow-y: auto;
	}
  .ivu-form-item-content {
		line-height: 30px;
	}
	.ivu-form-item-label {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		font-size: 13px !important;
		padding: 0;
		height: 30px;
		font-weight: 400;
		line-height: 15px;
		color: rgba(0, 0, 0, 0.85);
	}
	.ivu-form-item {
		margin-right: 0px;
		margin-bottom: 16px;
	}
  .ivu-form-item-error-tip {
		position: absolute;
    font-size: 12px;
  }
  .ivu-checkbox-wrapper {
    margin-right: 0px !important;
  }
  .custom_unit {
    .base-block__hd {
      padding-bottom: 10px;
    }
  }
  .custom_form_item input[maxlength] {
		padding-right: 45px;
	}
}
</style>
<style lang='scss' scoped>
.create-good-modal {
	.videoDiv {
		display: inline-block;
		width: 200px;
		height: 200px;
		text-align: center;
		line-height: 80px;
		border: 1px solid transparent;
		border-radius: 4px;
		overflow: hidden;
		background: #fff;
		position: relative;
		box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
		margin-right: 4px;
	}
	.videoDiv-cover {
		display: none;
		position: absolute;
		top: 0;
		bottom: 150px;
		left: 0;
		right: 0;
		background: rgba(0, 0, 0, 0.6);
	}

	.videoDiv:hover .videoDiv-cover {
		display: block;
	}

	.videoDiv-cover i {
		color: #fff;
		font-size: 25px;
		cursor: pointer;
		display: block;
		margin-top: 10px;
	}

	.use_price {
		margin-left: 16px;
	}
	.show_btn {
		margin-left: 24px;
		width: 104px;
		height: 22px;
    text-align: center;
    line-height: 22px;
		cursor: pointer;
		border-radius: 2px;
		border: 1px solid #d8d7d9;
		font-size: 12px;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.85);
	}
	/deep/ .base-block__hd {
		align-items: center;
	}
	@import '../../assets/scss/pagesCommon.scss';
	.table thead {
		height: 44px;
		line-height: 44px;
		background: #f6f8f9;
		// table-layout: fixed;
	}
	.layout-flex__right {
		text-align: left !important;
	}
	.layout-flex__body__switch {
		height: 30px;
		min-width: 80px !important;
		justify-content: center !important;
	}
	.base_sub_title {
		color: rgba(0, 0, 0, 0.5);
		font-size: 12px;
		text-indent: 1em;
		font-weight: 400;
	}
	.icontip {
		cursor: pointer;
    color: rgba(0, 0, 0, 0.6);
		font-size: 13px;
		margin-left: 4px;
	}
  .icon-tips {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.6);
  }
	.number-input-box {
		display: flex;
		width: 100%;
		.ivu-form-item-error-tip {
			position: absolute !important;
			width: 205px !important;
		}
	}
	.custom_form {
		display: flex;
		flex-wrap: wrap;
		.custom_form_item {
			width: 375px;
		}
	}

	.new-goods {
		padding: 10px 0 63px 0;
		background-color: #fff;
	}
	.new-goods__operation {
		margin-top: 20px;
		position: relative;
		width: 100%;
		.ivu-form {
			text-align: left;
		}
		.ivu-select-placeholder,
		.ivu-select-selected-value,
		.ivu-input-wrapper {
			width: 232px;
		}

		.ivu-btn {
			background-color: #03ac54;
		}
		.ivu-icon-ios-search {
			font-size: 20px;
			color: #fff;
		}
	}
	.radio_flex {
		display: flex;
		align-items: center;
		label {
			padding: 0 20px 0 0;
		}
	}
	/* iview custom */
	.ivu-page {
		margin: 10px;
		text-align: right;
	}

	.original-input {
		span {
			margin-right: 18px;
		}
		.ivu-cascader {
			display: inline-block;
		}
	}

	.ivu-cascader,
	.ivu-select {
		width: 232px;
	}

	.ivu-checkbox-group {
		display: inline-block;
		// margin-left: 10px;
	}

	.new-goods__select-middle {
		text-align: left;
		padding: 10px 0;
	}

	.goodslist__add-btn {
		position: absolute;
		top: 10px;
		right: 0;
	}
	.sdp-table__divider {
		box-shadow: inset -20px 0 40px -40px rgba(0, 0, 0, 0.45);
		right: 80px;
	}
	.new-goods__table {
		min-width: 800px;
		position: relative;
		border: 1px solid #e8e8e8;
		border-bottom: none;
		overflow: auto;
		width: 100%;

		// min-height: 260px;
		table {
			th {
				text-align: left;
			}
		}
	}
	.goods__table_box {
		padding: 12px 20px;
		border: 1px solid #e8e8e8;
		border-radius: 0px 0px 1px 1px;
		background: rgba(246, 248, 249, 0.3);
		border-top: none;
		font-size: 12px;
		color: rgba(0, 0, 0, 0.65);
		.goods__table_footer2 {
			width: 100%;
			flex: 1;
			border-top: none;
			.line_text {
				font-size: 12px;
				font-weight: 400;
				margin-right: 20px;
				float: left;
				color: rgba(0, 0, 0, 0.65);
			}
		}
	}

	.new-goods__btn {
		margin-right: 10px;
		float: right;
	}

	.c-red-btn {
		margin-right: 0;
	}

	.table-button {
		color: #03ac54;
		background-color: #fff !important;
		border: none;
		&:hover {
			background-color: transparent !important;
			color: #19be6b;
		}
	}

	.operation__item {
		// height: 34px;
		padding: 10px 0;
	}

	.tag {
		display: inline-block;
		margin-left: 10px;
		height: 30px;
		line-height: 30px;
		vertical-align: middle;
	}

	.newGoods-feature {
		display: flex;
		position: absolute;
		left: 50%;
		z-index: 2;
		bottom: 0;
		padding: 15px;
		text-align: left;
		// border-top: 1px solid #eee;
		background-color: #fff;
		transform: translateX(-50%);
	}

	.newGoods-feature button {
		margin-right: 5px;
	}

	.newGoods-feature > span {
		color: #03ac54;
		margin-right: 5px;
	}

	/* 商品主图和图文详情描述 */
	.goods-form .upload,
	.description {
		width: 100%;
		padding: 0 0 10px;
		text-align: left;
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 10px;
		.description__textarea {
			display: inline-block;
			vertical-align: top;
			width: 318px;
			height: 80px;
			margin: 10px 0 0;
			/deep/ textarea.ivu-input {
				height: 90px;
			}
			/deep/ .ivu-input-word-count {
				bottom: -26px;
				right: 0;
			}
		}
	}
	.videoBox {
		margin-top: 10px;
	}
	.admin-title2 {
		height: 16px;
		font-size: 13px;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.7);
		line-height: 16px;
    margin-top: 16px;
	}
	.admin-title2 > span {
		font-size: 12px;
		font-weight: 400;
		color: rgba(0, 0, 0, 0.4);
		line-height: 14px;
	}
	.admin-title > span {
		font-size: 14px;
		margin-left: 20px;
		color: #80848f;
	}

	.other-info {
		padding: 0px;
		text-align: left;
	}

	.table {
		width: 100%;
	}

	.table-original {
		font-size: 13px;
    margin:0 10px;
	}

	.table__select {
		margin-left: 10px;
		/*width: 70px;*/
		padding: 3px;
		& + .ivu-checkbox-wrapper {
			display: block;
		}
	}

	.ivu-icon-plus,
	.ivu-icon-minus {
		display: block;
	}

	.ivu-icon-minus {
		margin-top: 10px;
	}

	.purchase {
		display: inline-block;
		margin-top: 10px;
		.span {
			margin-right: 10px;
		}
	}
	.table tbody tr td {
		padding: 10px 10px;
		text-align: left;
		vertical-align: top;
    /deep/ .ivu-select-input {
      height: 28px !important;
      line-height: 28px !important;
    }
    /deep/ .ivu-select {
      height: 28px !important;
      line-height: 28px !important;
    }
    /deep/ .ivu-input-number-input-wrap {
      height: 28px !important;
      line-height: 28px !important;
    }

	}
	.table2 tbody tr td {
		vertical-align: middle;
		// padding: 5px;
	}
	$cRed: #ed3f14;
	.required-pot {
		color: $cRed;
	}
	/deep/.volume-info .ivu-table th {
		background-color: #f6f8f9;
	}
	/deep/.volume-info .ivu-table-header th {
		height: 44px;
	}
	.input-select {
		position: absolute;
		top: 36px;
		left: 0;
		width: 232px;
		max-height: 180px;
		padding: 0 10px;
		line-height: initial !important;
		border: 1px #dfdfdf solid;
		border-radius: 3px;
		box-shadow: 0 2px 6px 0 #f2f0fa;
		background: white;
		z-index: 999;
		overflow-y: auto;
		.select-item {
			display: flex;
			align-items: center;
			flex-direction: row;
			padding: 4px 0;
			.item-left {
				flex: 1;
			}
			.item-right {
				display: inline-block;
				padding: 2px 4px;
				margin-left: 10px;
				color: #03ac54;
				border: 1px solid #03ac54;
				border-radius: 3px;
			}
		}
	}
	.ellipsis_word {
		max-width: 100%;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
	}
	.layout-flex {
		display: flex;
		// justify-content: center;
		align-items: center;
		&__right {
			text-align: right;
			&--header {
				text-align: center;
			}
		}
		&__body {
			margin-bottom: 4px;
			height: 30px;
			max-width: 200px;

			justify-content: flex-start;
			&__switch {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				min-width: 80px !important;
			}
		}
	}
	.intro-description {
		left: 225px;
	}
	.label_name {
		display: inline-block;
		height: 30px;
		line-height: 30px;
	}
	.can_click_head {
		background: #f7f7f7;
		min-width: 70px;
	}
	.can_click {
		height: 30px;
		line-height: 30px;
		font-size: 13px;
		font-weight: 400;
		color: #03ac54;
		cursor: pointer;
		background: #fff;
	}
	.ivu-tabs-tabpane {
		padding: 0;
	}
	.displayNo /deep/.ivu-tabs-bar {
		display: none;
	}
  .ivu-radio-wrapper {
    font-size: 13px;
    margin-right: 0;
  }
	/deep/.ivu-form-item {
		margin: 0 0 16px 0 !important;
	}
	.table_fixed_right {
		padding: 0 !important;
		position: sticky;
		z-index: 1;
		right: 0px;
	}
	.fixed_div {
		height: 100%;
		width: 100%;
		padding: 0 5px;
		display: flex;
		align-items: center;
		// box-shadow: -2px 0 6px -2px rgba(0,0,0,0.2);
	}
	tr:hover .can_click {
		background: #ecf5f1;
	}
	.fixed_buttom {
		position: absolute;
		width: 100%;
		padding: 15px;
		font-size: 12px;
		position: absolute;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
	}
	/deep/.good_page .sdp-detail-page__bd {
		padding: 0 24px 0 24px;
	}
	/deep/.custom_unit .ivu-checkbox-inner {
		height: 15px;
		width: 15px;
	}
	/deep/ .good_page .ivu-select-input {
		line-height: 32px;
	}
	.sku_class {
		color: #b2b2b2;
		&.icon-disabled {
			cursor: not-allowed;
		}
	}
	.sku_class:not(.icon-disabled):hover {
		color: #03ac54;
	}
	.edit_class {
		overflow: auto;
	}
	.tabs_tip {
		margin-bottom: -20px;
	}
	/deep/ .basic_check .ivu-checkbox-wrapper {
		margin-right: 0px;
	}
	/deep/.tabs_tip .ivu-checkbox-wrapper {
		margin-right: 24px;
	}
	/deep/.textarea_goods .ivu-input {
		line-height: 15px;
	}
}
.change-upload-type {
	width: 90px;
	height: 90px;
	display: inline-block;
	margin: 10px 20px 0 0;
	border: 1px solid rgba(216, 216, 216, 0.8);
	border-radius: 1px;
	padding: 2px;
	text-align: center;
	position: relative;
	cursor: pointer;
}
.load_box {
	position: relative;
	height: 100%;
	width: 100%;
	background: #f6f8f9;
}
.load_img {
	position: absolute;
	top: calc(50% - 10px);
	left: 50%;
	transform: translate(-50%, -50%);
}
.load_text {
	position: absolute;
	top: calc(50% + 20px);
	left: 50%;
	transform: translate(-50%, -50%);
	color: rgba(0, 0, 0, 0.5);
	font-size: 14px;
}
.change-upload-type:hover {
	border: 1px dashed #03ac54;
}
.aic {
  display: flex;
  align-items: center;
}
.radio_channel_type {
  label {
    width: 116px;
    text-align: center;
  }
}
</style>
