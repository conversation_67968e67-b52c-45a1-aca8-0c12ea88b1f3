<template>
  <Modal
    v-model="showModal"
    width="1200"
    title="绑定系统商品"
    @on-cancel="cancel">
    <div>
      <span>分类：</span>
      <CategorySelect class="category-select" placeholder="请选择分类" @on-change="changeCategory"/>
      <span class="ml10">搜索：</span>
      <Input class="input-enter" placeholder="请输入商品编码/名称/助记码" @on-enter="inputChange" @on-search="inputChange" v-model="searchValue">
        <Button @click="inputChange" slot="append" icon="ios-search"></Button>
      </Input>
    </div>
    <Table height="400" size="small" class="list-table" :columns="columns" :data="data">
      <template slot-scope="{ row }" slot="logo">
        <img class="pic" :src="row.logo"/>
      </template>
      <template slot-scope="{ row }" slot="action">
        <span class="bind" @click="bindCloudGoods(row)">绑定</span>
      </template>
    </Table>
    <Page class="list-page" :total="Number(total)" show-elevator show-total show-sizer :page-size-opts="[10, 20, 50]" :current="page" :page-size="page_size" @on-change="pageChange" @on-page-size-change="pageSizeChange"/>
    <div slot="footer">
      <span class="tips">没有找到系统商品？</span>
      <Button type="primary"  @click="save">添加商品并绑定</Button>
    </div>
  </Modal>
</template>

<script>
  /**
   * author: 夏金
   * time: 2021/5/25 17:59
   * use: ->应用中心-云商品库，绑定
   */
  import CategorySelect from "@components/common/categorySelect";

  export default {
    components: {
      CategorySelect
    },
    props: {
      show: {
        type: Boolean,
        default: false
      },
      cloudGoods: {
        type: Object,
        default: null
      }
    },
    data() {
      return {
        hideImportedGoods: true,
        useSystemCategory: false,
        showModal: false,
        searchValue: '',
        category_id: '',
        category_id2: '',
        page: 1,
        page_size: 10,
        total: 0,
        columns: [],
        columnsAll: [
          {
            title: '商品图片',
            key: 'logo',
            slot: 'logo'
          },
          {
            title: '系统商品名称',
            key: 'name'
          },
          {
            title: '单位',
            key: 'unit'
          },
          {
            title: '描述',
            key: 'summary'
          },
          {
            title: '商品分类',
            key: 'category_name',
          },
          {
            title: '价格',
            key: 'price'
          },
          {
            title: '操作',
            key: 'action',
            slot: 'action'
          },
        ],
        data: []
      }
    },
    watch: {
      show(value) {
        this.showModal = value;
        if (value) {
          this.selGoods = {};
          this.searchList();
        }
      }
    },
    created() {
      this.columns = this.columnsAll.filter(column => column.key !== 'category');
    },
    methods: {
      hideImportChange(val) {
        console.log(val);
      },
      useSystemCategoryChange(val) {
        if (val) {
          this.columns = this.columnsAll;
        } else {
          this.columns = this.columnsAll.filter(column => column.key !== 'category');
        }
      },
      /**
       * 分类切换
       */
      changeCategory(val) {
        const [c1 = '', c2 = ''] = val;
        this.category_id = c1;
        this.category_id2 = c2;
        this.page = 1;
        this.searchList();
      },
      /**
       * page改变
       */
      pageChange(page) {
        this.page = page;
        this.searchList();
      },
      /**
       * page_size改变
       */
      pageSizeChange(page_size) {
        this.page_size = page_size;
        this.searchList();
      },
      /**
       * 关键字输入
       */
      inputChange() {
        this.page = 1;
        this.searchList();
      },
      /**
       * 请求列表
       */
      searchList() {
        let params = {
          searchValue: this.searchValue,
          category_id: this.category_id,
          category_id2: this.category_id2,
          page: this.page,
          page_size: this.page_size,
          cloud_commodity_bind_status: 2
        };
        this.$request.post(this.apiUrl.goodsMap.list, params).then(res => {
          if (res.status) {
            this.data = res.data.list;
            this.total = res.data.pageParams.count;
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      bindCloudGoods(goods) {
        const commodities = [{
          id: goods.commodity_id,
          commodity_cloud_id: this.cloudGoods.spuCode,
          commodity_sku_id: this.cloudGoods.unitCode
        }];
        this.$request.post(this.apiUrl.bindCloudGoods, {com_arr: commodities}).then(res => {
          if (res.status) {
            this.$Notice.success({title: '绑定成功'});
            this.$emit('save');
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      save() {
        let pics = [];
        this.cloudGoods.goodsPictures.forEach(pic => {
          if (pic && pic.pic && Number(pic.picType) === 0) {
            pics.push(pic.pic.substr(pic.pic.indexOf('com/') + 4));
          }
        });
        const commodities = [{
          commodity: {
            name: this.cloudGoods.spuName,
            commodity_code: this.cloudGoods.spuCode,
            category_id_text: this.cloudGoods.categoryIdText,
            category_id2_text: this.cloudGoods.categoryId2Text,
            channel_type: this.cloudGoods.channel_type,
            provider_id: this.cloudGoods.provider_id,
            agent_id: this.cloudGoods.agent_id,
            is_rough: Number(this.cloudGoods.spuType === 1) ? 'Y' : 'N',
          },
          skuList: [{
            commodity_cloud_id: this.cloudGoods.spuCode,
            commodity_sku_id: this.cloudGoods.unitCode,
            unit: this.cloudGoods.unitName,
          }],
          picpath: pics
        }];
        this.$request.post(this.apiUrl.importCloudGoods, {com_arr: commodities}).then(res => {
          if (res.status) {
            this.$Notice.success({title: '导入成功'});
            this.$emit('save');
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      cancel() {
        this.$emit('cancel');
      }
    }
  }
</script>

<style lang="less" scoped>
  .category-select {
    display: inline-block;
    width: 200px;
  }
  .input-enter {
    display: inline-block;
    width: 240px;
  }
  .list-table {
    margin-top: 10px;
    .pic {
      height: 36px;
      width: 36px;
    }
    .bind {
      color: #03ac54;
      cursor: pointer;
    }
  }
  .list-page {
    margin-top: 20px;
    text-align: right;
  }
  .tips {
    color: #999;
    cursor: pointer;
  }
</style>
