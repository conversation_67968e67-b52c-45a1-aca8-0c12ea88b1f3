<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="onCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1100"
    >
      <list-table
        ref="list"
        :table-height="getTableHeight() - 80"
        :api="apiUrl.syncMainSiteGoods.goodsList"
        :auto-load-data="false"
        :filter-items="filterItems"
        :filters="filters"
        :columns="cols"
        :row-class-name="rowClassName"
        :after-load-list="afterLoadList"
        @after-render="afterRender"
        @on-select="onSelect"
        @on-row-click="onRowClick"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-selection-change="onSelectionChange"
      >
        <category-select
          v-if="modal.show"
          :url="apiUrl.siteAdmin.goodsCategory.tree"
          slot="category"
          @on-change="changeCategory"
        ></category-select>
      </list-table>
      <div slot="footer">
        <Button @click="onCancel">取消</Button>
        <Button type="primary" @click="syncSelected">{{
          syncing ? '导入中...' : '导入当前选中'
        }}</Button>
        <Button type="primary" @click="syncAll">{{
          syncingAll ? '导入中...' : '导入全部页面'
        }}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import CategorySelect from '@components/common/categorySelect';
import ListTable from '@components/common/list-table/list';
const LIST_KEY = 'id';
export default {
  name: 'MainSiteGoods',
  components: {
    CategorySelect,
    ListTable
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示多选
     */
    showSelect: {
      type: Boolean,
      default: true
    },
    maxSelectCount: {
      type: Number,
      default: 0
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    closable: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: '请选择商品'
    },
    // 默认选中的上商品
    defaultSelectedGoods: {
      type: Array,
      default: () => []
    },
    disabledGoods: {
      type: Array,
      default: () => []
    },
    /**
     * 表格字段
     */
    columns: {
      type: Array,
      default: () => []
    },
    /**
     * 表格字段
     */
    filterParams: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    // eslint-disable-next-line no-unused-vars
    show(newValue, oldValue) {
      this.modal.show = newValue;
      if (newValue) {
        this.setFilters();
        this.selectedGoods = this.cloneObj(this.defaultSelectedGoods);
        this.loadGoods();
      }
    },
    defaultSelectedGoods: {
      deep: true,
      handler(newValue) {
        this.selectedGoods = this.cloneObj(newValue);
      }
    }
  },
  computed: {},
  data() {
    return {
      syncing: false,
      syncingAll: false,
      modal: {
        show: false,
        className: 'vertical-center-modal common-goods-modal'
      },
      tableHeight: this.getTableHeight() * 0.8,
      filterItems: [
        {
          key: 'category',
          type: 'slot'
        },
        {
          key: 'is_sync',
          default: '1',
          type: 'select',
          data: [
            {
              label: '未添加',
              value: '1'
            },
            {
              label: '已添加',
              value: '2'
            }
          ]
        },
        {
          key: 'searchValue',
          placeholder: '请输入商品名称/编码进行搜索',
          showIcon: true
        }
      ],
      filters: {
        category_id: '',
        category_id2: '',
        category_id3: '',
      },
      goodsList: [],
      selectedRow: [],
      selectedGoods: [],
      cols: [
        {
          type: 'selection',
          width: 60,
          align: 'left'
        },
        {
          title: '商品图片',
          render: (h, params) => {
            let obj = params.row;
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              },
              style: {
                background: obj._disabled ? '#000' : '',
                opacity: obj._disabled ? '0.3' : 1
              }
            });
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code'
        },
        {
          title: '商品名称',
          key: 'name',
          render: (h, params) => {
            let data = params.row;
            return h('div', [
              h(
                'span',
                {
                  style: {
                    margin: '10px 3px 0'
                  },
                  class: {
                    dn: params.row.central_purchase_flag == 1 ? false : true,
                    'common-tag': true,
                    'tag-green': true
                  }
                },
                '集'
              ),
              h('span', data.name)
            ]);
          }
        },
        {
          title: '商品分类',
          key: 'category_name'
        },
        {
          title: '单位',
          key: 'unit'
        },
        {
          title: '描述',
          key: 'summary'
        }
      ]
    };
  },
  created() {
    this.setFilters();
    this.modal.show = this.show;
    if (!this.showFooter) {
      this.modal.className += ' hide-footer';
    }
    if (this.columns && this.columns.length > 0) {
      this.cols = this.columns;
    }
    if (this.show) {
      this.$nextTick(() => {
        this.$refs.list.loadListData();
      });
    }
  },
  methods: {
    setFilters() {
      this.filters = {
        ...this.filters,
        ...this.filterParams
      };
    },
    afterRender() {
      this.setDisabledGoods();
      this.checkMaxSelectCount();
    },
    changeCategory(category) {
      this.filters.category_id = category[0];
      this.filters.category_id2 = category[1];
      this.filters.category_id3 = category[2];
      this.loadGoods();
    },
    loadGoods() {
      // let selectedGoodsIdArr = this.defaultSelectedGoods.map((goods) => goods[LIST_KEY]);
      // this.filters.commodity_string = JSON.stringify(selectedGoodsIdArr);
      this.$refs.list.loadListData();
    },
    afterLoadList(list) {
      list.forEach(goods => {
        if (this.isSelected(goods)) {
          goods._checked = true;
        }
      });
    },
    onSelectAll(selection) {
      selection.forEach(row => {
        this.selectGoods(row);
      });
    },
    onSelect(selection, row) {
      this.selectGoods(row);
    },
    onSelectionChange(selection) {
      selection.forEach(goods => {
        this.selectGoods(goods);
      });
      if (selection.length === 0) {
        this.$refs.list.list.forEach(item => {
          this.cancelSelectGoods(item);
        });
      }
      this.checkMaxSelectCount();
    },
    onSelectCancel(selection, row) {
      this.cancelSelectGoods(row);
    },
    onRowClick(row, index) {
      if (this.isDisabled(row)) {
        return false;
      }
      if (this.isSelected(row)) {
        this.cancelSelectGoods(row);
        this.cancelCheckStatus(index);
      } else {
        this.selectGoods(row);
        this.setCheckStatus(index);
      }
      this.checkMaxSelectCount();
    },
    isSelected(goods) {
      return this.selectedGoods.some(
        findGoods => findGoods[LIST_KEY] === goods[LIST_KEY]
      );
    },
    isDisabled(goods) {
      return this.disabledGoods.some(
        findGoods => findGoods[LIST_KEY] === goods[LIST_KEY]
      );
    },
    setDisabledGoods() {
      let list = this.getList();
      list.forEach(goods => {
        if (
          this.disabledGoods.find(
            findGoods => findGoods[LIST_KEY] === goods[LIST_KEY]
          )
        ) {
          goods._disabled = true;
        }
      });
      this.setList(list);
    },
    checkMaxSelectCount() {
      let list = this.getList();
      let selectedLen = this.selectedGoods.length;
      if (!this.maxSelectCount) {
        return false;
      }
      // eslint-disable-next-line no-unused-vars
      list.forEach((goods, index) => {
        let selected = this.isSelected(goods);
        if (selectedLen >= this.maxSelectCount && !selected) {
          goods._disabled = true;
        } else {
          goods._disabled = false;
        }
        goods._checked = selected;
      });
      this.setList(list);
    },
    getList() {
      return this.$refs.list.getListData();
    },
    setList(list) {
      return this.$refs.list.setListData(list);
    },
    /**
     * 设置选中效果
     * @param index 选中行的索引
     */
    setCheckStatus(index) {
      let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
      let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
      // 设置选中样式
      if (selectRow) {
        let checkBoxWrapperDom = selectRow.querySelector(
          '.ivu-checkbox-wrapper'
        );
        let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
        checkBoxWrapperDom &&
          !checkBoxWrapperDom.classList.contains(
            'ivu-checkbox-wrapper-checked'
          ) &&
          checkBoxWrapperDom.classList.add('ivu-checkbox-wrapper-checked');
        checkBoxDom &&
          !checkBoxDom.classList.contains('ivu-checkbox-checked') &&
          checkBoxDom.classList.add('ivu-checkbox-checked');
      }
    },
    /**
     * 取消选中效果
     * @param index 取消选中行的索引
     */
    cancelCheckStatus(index) {
      let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
      let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
      // 取消选中样式
      if (selectRow) {
        let checkBoxWrapperDom = selectRow.querySelector(
          '.ivu-checkbox-wrapper'
        );
        let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
        checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') &&
          checkBoxWrapperDom.classList.remove('ivu-checkbox-wrapper-checked');
        checkBoxDom.classList.contains('ivu-checkbox-checked') &&
          checkBoxDom.classList.remove('ivu-checkbox-checked');
      }
    },
    /**
     * 将商品添加到选中商品中
     * @param row
     */
    selectGoods(row) {
      if (
        !this.selectedGoods.some(goods => goods[LIST_KEY] === row[LIST_KEY])
      ) {
        this.selectedGoods.push(row);
      }
    },
    /**
     * 从选中商品中删除商品
     * @param row
     */
    cancelSelectGoods(row) {
      if (this.selectedGoods.some(goods => goods[LIST_KEY] === row[LIST_KEY])) {
        this.selectedGoods.splice(
          this.selectedGoods.findIndex(
            goods => goods[LIST_KEY] === row[LIST_KEY]
          ),
          1
        );
      }
    },
    // eslint-disable-next-line no-unused-vars
    rowClassName(row, index) {},
    syncSelected() {
      if (this.selectedGoods.length === 0) {
        this.modalError('请选择商品', 0);
        return false;
      }
      if (this.syncing) {
        return false;
      }
      this.syncing = true;
      this.syncGoods();
    },
    syncAll() {
      if (this.syncingAll) {
        return false;
      }
      this.syncingAll = true;
      this.syncGoods();
    },
    getSyncParams() {
      let params = {
        ids: this.selectedGoods.map(goods => goods[LIST_KEY]),
        ...this.$refs.list.getFilters()
      };
      params.ids = JSON.stringify(params.ids);
      return params;
    },
    syncGoods() {
      this.$request
        .post(this.apiUrl.syncMainSiteGoods.syncGoods, this.getSyncParams())
        .then(
          res => {
            this.syncingAll = false;
            this.syncing = false;
            let { status, message } = res;
            if (status) {
              this.modalSuccess('导入成功', 0);
              this.onOk();
            } else {
              this.modalError(message, 0);
            }
          },
          () => {
            this.syncingAll = false;
            this.syncing = false;
          }
        );
    },
    onCancel() {
      this.$emit('on-cancel');
    },
    onOk() {
      this.$emit('on-ok');
    }
  }
};
</script>

<style lang="less">
.main-site-goods-modal {
  .ivu-modal-body {
    padding-bottom: 0;
    .ivu-table-row {
      cursor: pointer;
    }
  }
}
</style>
<style lang="less" scoped></style>
