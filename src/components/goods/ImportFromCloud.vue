<template>
  <Modal
    v-model="showModal"
    width="1200"
    @on-cancel="cancel"
    title="从云商品库导入">
    <div>
      <CommonCascader :value="categoryValue" labelKey="categoryName" valueKey="categoryCode" :data="cloudCategoryList" class="category-select" placeholder="请选择分类" @on-change="changeCategory"/>
      <Input class="input-enter" placeholder="请输入商品名称" @on-enter="inputChange" @on-search="inputChange" v-model="params.spuNameLike">
        <Button @click="inputChange" slot="append" icon="ios-search"></Button>
      </Input>
      <div style="float: right;">
        <Checkbox v-model="hideImportedGoods" class="margin-left" @on-change="inputChange">隐藏已导入商品</Checkbox>
        <Checkbox v-model="useSystemCategory" class="margin-left">使用系统分类</Checkbox>
      </div>
    </div>
    <Table
      height="400"
      size="small"
      class="list-table"
      @on-selection-change="tableSelect"
      :columns="columns"
      :data="data">
      <template slot-scope="{ row }" slot="pics">
        <div class="pic-layout">
          <img class="pic" :src="row.goodsPictures[0].pic" v-if="row.goodsPictures && row.goodsPictures.length > 0"/>
        </div>
      </template>
      <template slot-scope="{ row }" slot="importStatus">
        <span :class="row.importStatus !== 1 ? 'import-status-no' : 'import-status'">{{row.importStatus === 1 ? '已导入' : '未导入'}}</span>
      </template>
      <template slot-scope="{ row }" slot="categoryPath">
        <span v-if="row.importStatus === 1">{{getCategoryName(row.baseCommodity)}}</span>
        <template v-else>
          <span v-if="!useSystemCategory">{{row.categoryPath}}</span>
          <CommonCascader
            v-else
            :value="row.importStatus === 1 ? [row.baseCommodity.category_id, row.baseCommodity.category_id2] : []"
            :transfer="true"
            :disabled="row.importStatus === 1"
            labelKey="name"
            valueKey="id"
            childrenKey="items"
            :data="systemCategoryList"
            class="category-select"
            placeholder="请选择分类"
            @on-change="(value, info) => selectGoodsCategory(row, value, info)"/>
        </template>
      </template>
      <template slot-scope="{ row }" slot="channel_type">
        <span v-if="row.importStatus === 1">{{getPurchaseTypeName(row.baseCommodity)}}</span>
        <CommonCascader
          v-else
          :value="[]"
          :transfer="true"
          :filterable="true"
          :disabled="row.importStatus === 1"
          labelKey="name"
          valueKey="id"
          childrenKey="children"
          :data="purchaseTypeList"
          class="category-select"
          placeholder="请选择采购类型"
          @on-change="(value, info) => selectePurchaseType(row, value, info)"/>
      </template>
    </Table>
    <Page
      class="list-page"
      :total="Number(total)"
      show-elevator show-total
      show-sizer
      :page-size-opts="[10, 20, 50]"
      :current="params.pageNumber"
      :page-size="params.pageSize"
      @on-change="pageChange"
      @on-page-size-change="pageSizeChange"/>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary"  @click="save">一键导入</Button>
    </div>
  </Modal>
</template>

<script>
  /**
   * author: 夏金
   * time: 2021/5/25 17:59
   * use: ->商品档案，从云商品库导入
   */
  import CommonCascader from '@components/common/common-cascader';
  // import { Table } from 'view-design';
  export default {
    components: {
      CommonCascader,
      // Table,
    },
    props: {
      show: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        showModal: false,
        // 是否隐藏已导入商品
        hideImportedGoods: true,
        // 是否使用系统分类
        useSystemCategory: false,
        categoryValue: [],
        // 系统分类
        systemCategoryList: [],
        // 云商品库分类
        cloudCategoryList: [],
        // 采购员/供应商列表
        purchaseTypeList: [],
        // 已选中的商品列表，使用Map方便去重
        selectedSpuCodeList: new Map(),
        // 请求参数
        params: {
          spuNameLike: '',
          pageNumber: 1,
          pageSize: 10,
          categoryCode: '',
          importStatus: '2', // 传2表示过滤已导入的
        },
        total: 0,
        columns: [
          {
            type: 'selection',
            width: 60,
            align: 'center'
          },
          {
            title: '商品图片',
            key: 'pics',
            slot: 'pics',
            width: 90
          },
          {
            title: '云商品名称',
            key: 'spuName',
            minWidth: 220,
            render: (h, params) => {
              let data = params.row;
              return h('div', {
                style: {
                  margin: '2px 0'
                }
              },[
                h('div', {
                  style: {
                    'margin-bottom': '1px'
                  }
                } , data.spuName),
                h('div', {
                  style: {
                    'color': 'rgb(153, 153, 153)'
                  }
                } , data.spuCode),
              ])
            }
          },
          {
            title: '单位',
            width: 100,
            key: 'unitName'
          },
          {
            title: '商品分类',
            minWidth: 220,
            key: 'categoryPath',
            slot: 'categoryPath'
          },
          {
            title: '采购类型',
            minWidth: 220,
            key: 'channel_type',
            slot: 'channel_type'
          },
          // {
          //   title: '市场参考价',
          //   key: 'price',
          //   width: 120,
          // },
          {
            title: '状态',
            width: 100,
            key: 'importStatus',
            slot: 'importStatus'
          },
        ],
        data: []
      }
    },
    watch: {
      show(value) {
        if (value) {
          // 每次打开窗口重置数据
          Object.assign(this.$data,this.$options.data());
          this.getCloudCategoryList();
          this.getSysCategoryList();
          this.getPurchaseTypeList();
          this.searchList();
        }
        this.showModal = value;
      }
    },
    methods: {
      getCategoryName(item) {
        const index1 = this.systemCategoryList.findIndex(i => Number(item.category_id) === Number(i.id));
        if (index1 === -1) return '-/-';
        const index2 = this.systemCategoryList[index1].items.findIndex(i => Number(item.category_id2) === Number(i.id));
        if (index2 === -1) return `${this.systemCategoryList[index1].name}/-`;
        return `${this.systemCategoryList[index1].name}/${this.systemCategoryList[index1].items[index2].name}`;
      },
      getPurchaseTypeName(item) {
        const index1 = this.purchaseTypeList.findIndex(i => Number(item.channel_type) === Number(i.id));
        if (index1 === -1) return '-/-';
        const index2 = this.purchaseTypeList[index1].children.findIndex(i => (Number(item.channel_type) === 1 ? Number(item.agent_id) : Number(item.provider_id)) === Number(i.id));
        if (index2 === -1) return `${this.purchaseTypeList[index1].name}/-`;
        return `${this.purchaseTypeList[index1].name}/${this.purchaseTypeList[index1].children[index2].name}`;
      },
      hideImportChange(val) {
        this.params.importStatus = val ? '2' : '';
      },
      useSystemCategoryChange(val) {
        this.useSystemCategory = val;
      },
      /**
       * 表格勾选变动，包括全选、单选的操作
       * selection 选中的数据列表
       */
      tableSelect(selection) {
        // 遍历当前列表数据，被包含在已选中selection中代表选中，反之未选中
        this.data.forEach(item => {
          // 判断已选中项中是否包含该项，包含的话说明该项是选中，反之未选中
          const index = selection.findIndex(sel => sel.spuCode === item.spuCode);
          if (index !== -1) {
            this.selectedSpuCodeList.set(item.spuCode, item);
          } else {
            this.selectedSpuCodeList.delete(item.spuCode);
          }
        });
      },
      /**
       * 分类切换
       */
      changeCategory(val) {
        this.params.categoryCode = val && val.length > 0 ? val[val.length - 1] : '';
        this.searchList();
      },
      /**
       * 选中分类，然后将分类赋值给当前数据
       */
      selectGoodsCategory(row, val, selData) {
        this.data.forEach(item => {
          if (item.spuCode === row.spuCode) {
            item.category1Name = selData && selData.length > 0 ? selData[0].label : '';
            item.category2Name = selData && selData.length > 1 ? selData[1].label : '';
          }
        });
      },
      /**
       * 选中采购类型，然后将类型赋值给当前数据
       */
      selectePurchaseType(row, val, selData) {
        this.data.forEach(item => {
          if (item.spuCode === row.spuCode) {
            item.channel_type = selData && selData.length > 0 ? selData[0].value : '';
            item.agent_id = +item.channel_type === 1 ? selData[1].value : '';
            item.provider_id = +item.channel_type === 2 ? selData[1].value : '';
          }
        });
      },
      /**
       * page改变
       */
      pageChange(val) {
        this.params.pageNumber = val;
        this.searchList();
      },
      /**
       * page_size改变
       */
      pageSizeChange(val) {
        this.params.pageSize = val;
        this.searchList();
      },
      /**
       * 关键字输入
       */
      inputChange() {
        this.params.pageNumber = 1;
        this.searchList();
      },
      /**
       * 获取云商品库分类
       */
      getCloudCategoryList() {
        this.$request.get(this.apiUrl.getCloudCategoryList).then(res => {
          if (res.status) {
            this.cloudCategoryList = res.data;
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      /**
       * 获取系统分类
       */
      getSysCategoryList() {
        this.$request.get(this.apiUrl.getGoodsCategoryTree).then(res => {
          if (res.status) {
            this.systemCategoryList = res.data;
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      /**
       * 获取采购员/供应商列表
       */
      getPurchaseTypeList() {
        this.$request.get(this.apiUrl.getPurchaseType).then(res => {
          let purchaseType = [];
          if (res.status) {
            purchaseType.push({
              name: '市场自采',
              id: '1',
              children: res.data.agents
            });
            purchaseType.push({
              name: '供应商直供',
              id: '2',
              children: res.data.providers
            });
            this.purchaseTypeList = purchaseType;
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      /**
       * 请求云商品列表
       */
      searchList() {
        this.params.importStatus = this.hideImportedGoods ? '2' : '';
        this.$request.get(this.apiUrl.getCloudGoodsList, this.params).then(res => {
          if (res.status) {
            let list = res.data.list;
            list.forEach(item => {
              item._checked = this.selectedSpuCodeList.has(item.spuCode);
              item._disabled = Number(item.importStatus) === 1;
            });
            this.data = list;
            this.total = res.data.total;
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      /**
       * 执行导入
       */
      save() {
        let commodities = [];
        this.selectedSpuCodeList.forEach(goods => {
          let pics = [];
          goods.goodsPictures.forEach(pic => {
            if (pic && pic.pic && Number(pic.picType) === 0) {
              pics.push(pic.pic.substr(pic.pic.indexOf('com/') + 4));
            }
          });
          let alias = [];
          goods.goodsAlias.forEach(item => {
            alias.push(item.spuAliasName);
          });
          commodities.push({
            commodity: {
              name: goods.spuName,
              commodity_code: goods.spuCode,
              category_id_text: this.useSystemCategory ? goods.category1Name : goods.categoryIdText,
              category_id2_text: this.useSystemCategory ? goods.category2Name : goods.categoryId2Text,
              channel_type: goods.channel_type,
              provider_id: goods.provider_id,
              agent_id: goods.agent_id,
              is_rough: Number(goods.spuType === 1) ? 'Y' : 'N',
              alias: alias.join(',')
            },
            skuList: [{
              commodity_cloud_id: goods.spuCode,
              commodity_sku_id: goods.unitCode,
              unit: goods.unitName,
            }],
            picpath: pics
          });
        });
        this.$request.post(this.apiUrl.importCloudGoods, {com_arr: commodities}).then(res => {
          if (res.status) {
            this.$Notice.success({title: '导入成功'});
            this.$emit('save');
          } else {
            this.$Notice.error({title: res.message});
          }
        });
      },
      cancel() {
        this.$emit('cancel');
      },
    }
  }
</script>

<style lang="less" scoped>
  .category-select {
    display: inline-block;
    position: relative;
  }
  .input-enter {
    display: inline-block;
    margin-left: 10px;
    width: 240px;
  }
  /deep/ .ivu-checkbox {
    margin-right: 4px;
    line-height: 1.2;
  }
  .list-table {
    margin-top: 10px;
    .pic-layout {
      display: flex;
      align-items: center;
      .pic {
        height: 36px;
        width: 36px;
      }
    }
    .import-status {
      color: #03ac54
    }
    .import-status-no {
      color: red
    }
  }
  .list-page {
    margin-top: 20px;
    text-align: right;
  }
</style>
