<template>
  <div id="category-select">
    <Cascader
      v-if="level - 1 > 0"
      v-model="selfValue"
      :data="categoryList"
      change-on-select
      clearable
      :placeholder="placeholder"
      @on-change="changeCategory"
      :render-format="renderFormat"
      style="width: 100%">
    </Cascader>
    <Select v-if="parseInt(level) === 1" v-model="selfValue" @on-change="changeFirsCategory">
      <Option v-for="(item, index) in categoryList" :key="index" :value="item.value">{{item.label}}</Option>
    </Select>
  </div>
</template>

<script>
  export default {
    name: "MainSiteCategory",
    props: {
      value: {
        type: [Array, String],
        default: () => []
      },
      meta: {
        type: Object,
        default: null
      },
      level: {
        default: 2
      },
      placeholder: {
        default: '全部分类'
      },
      renderFormat: {
        type: Function,
        default: label => label.join(' / ')
      }
    },
    watch: {
      value(newValue) {
        this.selfValue = newValue;
      }
    },
    data() {
      return {
        selfValue: [],
        categoryList: [],
      }
    },
    created() {
      this.selfValue = this.value;
      this.$request.get(this.apiUrl.siteAdmin.goodsCategory.tree).then((res) => {
        let {status, data} = res;
        if (status) {
          let cascaderData = [];
          data.forEach((category) => {
            let cascaderItem = {
              value: category.id,
              label: category.name,
              children: [],
              loading: false
            };
            if (category.items) {
              cascaderItem.children = category.items.map((secondCategory) => {
                return {
                  value: secondCategory.id,
                  label: secondCategory.name,
                }
              });
            }
            cascaderData.push(cascaderItem);
          });
          this.categoryList = cascaderData;
          this.categoryList.unshift({'value': '', 'label': '全部分类'});
        }
      });
    },
    methods: {
      changeCategory(value, selectedData) {
        this.$emit('input', value);
        this.$emit('on-change', value, selectedData, this.meta);
      },
      changeFirsCategory() {
        this.$emit('input', this.selfValue);
        this.$emit('on-change', this.selfValue);
      }
    }
  }
</script>

<style scoped>

</style>
