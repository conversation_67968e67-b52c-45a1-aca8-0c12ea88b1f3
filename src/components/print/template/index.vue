<template>
  <CommonSelect
    v-model="selfValue"
		ref="select"
    @on-change="onChange"
    :url="apiUrl.getPrintTemplate"
    :params="params"
    :multiple="multiple"
    :disabled="disabled"
    :all-label="allLabel"
    :all-value="allValue"
    :filterable="filterable"
    :clearable="clearable"
		:filterHandle="filterHandle()"
    :show-all="false"
    :placeholder="placeholder"
  >
    <template slot-scope="{item}">
      <slot :item="item">{{item[labelKey]}}</slot>
    </template>
  </CommonSelect>
</template>

<script>
import SelectMixin from '../../../mixins/select';
import settings from '@/api/settings';
export default {
  name: 'PrintTemplate',
  autoRegister: true,
  mixins: [SelectMixin],
  props: {
    filterable: {
      type: Boolean
    },
    clearable:{
      type:Boolean,
      default:false
    },
		onlyNew: {
			type: Boolean,
			default: false
		}
  },
  data() {
    return {
      list: []
    };
  },
  created() {
	},
  methods: {
		filterHandle() {
			if (this.onlyNew){
				return (item) => {
					return this.isNewTemplate(item)
				}
			}
			return null
		},
		isNewTemplate(template) {
			console.log(template, '测试')
			return settings.isNewTemplate(template);
		},
	}
};
</script>

<style scoped></style>
