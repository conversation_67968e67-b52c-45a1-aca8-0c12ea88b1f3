<template>
  <RemoteSelect
    :remote="remote"
    :filterable="true"
    :value="value"
    :placeholder="placeholder"
    :renderOption="renderOption"
    :optionLabel="optionLabel"
    optionValue="commodity_id"
    dataKey="commodities"
    v-bind="$attrs"
    v-on="$listeners"
  />
</template>

<script>
import RemoteSelect from '@sdptest/base/lib/select/src/remote';
import order from '@api/order.js';

export default {
  name: 'goods-select',
  components: { RemoteSelect },
  props: {
    value: {
      type: [String, Number],
    },
    placeholder: {
      type: String,
    },
  },
  methods: {
    remote(query) {
      return order.getCommodity({
        query,
        pageSize: 20
      });
    },
    optionLabel(item) {
      return `${item.commodity_name} ${item.unit} ${item.summary}`;
    },
    renderOption(item) {
      return `<p>${item.commodity_name} ${item.unit}<span>${
        item.summary ? `(${item.summary})` : ''
      }</span></p><p style="color: #999;">${item.commodity_code}</p>`;
    },
  },
};
</script>

<style></style>
