<template>
  <Modal
    width="720"
    v-model="showModal"
    title="选择分类"
    ok-text="批量添加"
    @on-cancel="cancel"
    @on-ok="confirm">
    <div>
      <Checkbox
        class="ml20"
        :indeterminate="checkboxIndeterminate"
        :value="isCheckAll"
        @click.prevent.native="handleCheckAll"
      >
        <span class="ml9">全部</span>
      </Checkbox>
      <Tree
        class="tree"
        v-model="selectCateIds"
        :data="categories"
        @on-check-change="selectChanged"
        show-checkbox>
      </Tree>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: 'categorySelectModal',
    props: {
      show: {
        type: Boolean,
        default: false
      },
      selectedIds: { // 已选中的二级分类的id
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        showModal: false,
        categories: [], // 全部分类
        selectCateIds: [], // 勾选的所有二级分类的id
        selectedCate: [], // 勾选的二级分类
        checkboxIndeterminate: false,
        isCheckAll: false,
      }
    },
    watch: {
      show(value) {
        this.showModal = value;
        if (value) {
          this.getCategoryList();
        }
      },
      selectedIds(value) {
        this.selectCateIds = value;
        this.initChecked()
      },
    },
    methods: {
      confirm() {
        this.$emit('confirm', this.selectCateIds, this.selectedCate);
        this.handleReset()
      },
      cancel() {
        this.$emit('cancel');
        this.handleReset()
      },
      handleReset() {
        // 将数据重置为初始状态
        Object.assign(this.$data, this.$options.data.call(this))
      },
      /**
       * 触发勾选
       */
      selectChanged() {
        let selectCateIds = [];
        let selectedCate = [];
        this.categories.forEach(first => {
          first.children.forEach(second => {
            if (second.checked) {
              selectCateIds.push(second.id);
              selectedCate.push({
                category_id: second.pid,
                category_id2: second.id,
                category_name1: first.title,
                category_name2: second.title,
              })
            }
          });
        });
        this.selectCateIds = selectCateIds;
        this.selectedCate = selectedCate
        this.handleCheckAll()
      },
      /**
       * 获取分类列表并转换成Tree控件需要的数据格式
       */
      getCategoryList() {
        this.$request.get(this.apiUrl.getGoodsCategoryTree).then(res => {
          let categoryList = [];
          const list = res.data;
          list.forEach((first, index) => {
            let firstItem = {
              title: first.name,
              pid: first.pid,
              id: first.id,
              level: first.level,
              children: []
            };
            first.items.forEach(second => {
              const checked = this.selectCateIds.findIndex(id => id === second.id) !== -1;
              let secondItem = {
                title: second.name,
                pid: second.pid,
                id: second.id,
                level: second.level,
                checked
              };
              firstItem.children.push(secondItem);
              if(checked) {
                this.selectedCate.push({
                  category_id: second.pid,
                  category_id2: second.id,
                  category_name1: first.name,
                  category_name2: second.name,
                })
              }
            });
            firstItem.checked = this.categories[index] ? this.categories[index].checked : false
            firstItem.indeterminate = this.categories[index] ? this.categories[index].indeterminate : false
            categoryList.push(firstItem);
          });
          this.categories = categoryList;
          this.$nextTick(()=> {
            if (this.selectCateIds.length) this.handleCheckAll()
          })
        });
      },
      handleCheckAll (isChangeChecked) {
        let notCheckAll = this.categories.some(item => !item.checked)
        if (isChangeChecked) this.handleAllCheckedStatus(notCheckAll)
        else {
          this.isCheckAll = !notCheckAll
          this.checkboxIndeterminate = notCheckAll && !!this.selectCateIds.length
        }
      },
      handleAllCheckedStatus (toCheckAll) {
        this.isCheckAll = toCheckAll
        let selectedIds = []
        let selectedCate = []
        this.categories.forEach(first => {
          first.checked = this.isCheckAll
          first.indeterminate = false
          first.children.forEach(second => {
            second.checked = this.isCheckAll
            if (second.checked) {
              selectedIds.push(second.id)
              selectedCate.push({
                category_id: second.pid,
                category_id2: second.id,
                category_name1: first.title,
                category_name2: second.title,
              })
            }
          })
        })
        this.selectCateIds = selectedIds
        this.selectedCate = selectedCate;
        this.checkboxIndeterminate = !this.isCheckAll && !!this.selectCateIds.length
      },
      initChecked() {
        if(this.selectCateIds.length <=0) {
          this.isCheckAll = false
        }
        this.categories.forEach(first => {
        const secondCategories = first.children;
        const isAnySecondCategorySelected = secondCategories.some(second => {
          return this.selectCateIds.includes(second.id) && second.checked;
        });
        first.checked = isAnySecondCategorySelected;
        first.indeterminate = false;
        secondCategories.forEach(second => {
          if (this.selectCateIds.includes(second.id)) {
            second.checked = true;
          } else {
            second.checked = false;
          }
        });
      });
      },
    }
  }
</script>

<style lang="less" scoped>
  /deep/ .ivu-tree-title {
    margin-top: 6px;
  }
  .tree {
    max-height: 500px;
    overflow-y: auto;
  }
</style>
