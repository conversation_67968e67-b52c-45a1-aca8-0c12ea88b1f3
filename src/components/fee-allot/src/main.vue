<template>
  <div>
    <EditableTable
      :columns="columns"
      :data="allotItems"
      :isShowRecordEditor="!readonly"
      :recordEditorConfig="{ autoInsertDelete: false }"
      @on-click-insert="showAddModal"
      @on-click-delete="delItem">
      <template #after-table>
        <div v-show="false"></div>
      </template>
    </EditableTable>
    <Modal v-model="addModal" title="添加费用分摊">
      <div style="height: 300px">
        <Form ref="form" :model="allotFormData" :rules="rules" :label-width="100" label-colon>
          <FormItem label="分摊项目" prop="item">
            <Select v-model="allotFormData.item">
              <Option :value="item.id" :key="item.id" v-for="item in feeList">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem label="分摊类型" prop="type">
            <Select v-model="allotFormData.type">
              <Option :value="item.value" :key="item.value" v-for="item in typeList">{{ item.label }}</Option>
            </Select>
          </FormItem>
          <FormItem label="分摊方式" prop="costsType">
            <RadioGroup v-model="allotFormData.costsType">
              <Radio :label="item.value" :key="item.value" v-for="item in allotType">
                <span>{{ item.label }}</span>
              </Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="分摊金额" prop="price">
            <NumberInput
              v-model="allotFormData.price"
              :min="0"
              :precision="2"
              placeholder="请输入分摊金额"
              style="width: 100%;"
            />
          </FormItem>
          <FormItem label="备注" prop="remark">
            <Input v-model="allotFormData.remark" :rows="4" maxlength="50" :showWordLimit="true" type="textarea"
              placeholder="请输入备注信息" />
          </FormItem>
        </Form>
      </div>
      <div slot="footer">
        <Button @click.native="closeAddModal">取消</Button>
        <Button @click.native="addItem" type="primary">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import EditableTable from '@/components/editable-table/index.js'
import { get } from '@/api/request';
const allotFormData = {
  item: '',
  price: '',
  type: '1',
  costsType: '',
  remark: ''
}
const emptyItem = {
  empty: true,
}
const typeList = [
  {
    value: '1',
    label: "增加费用",
  },
  {
    value: '2',
    label: "减少费用",
  }
]
const typeMap = {
  '1': '增加费用',
  '2': '减少费用'
}
const allot = (list, allotInfo) => {
  const allotPrice = allotInfo.price;
  const operator = Number(allotInfo.type) === 1 ? 1 : -1;
  const total = list.reduce((pre, cur) => {
    return pre.add(Number(cur));
  }, 0);
  let allotList = [];
  let alloted = 0;
  const allotItemCount = list.length;
  list.forEach((item, index) => {
    // 最后一个用总数减去前面的
    if (index === allotItemCount - 1) {
      allotList[index] = Number(allotPrice).sub(alloted);
    } else {
      allotList[index] = (Number(item) === 0 ? 0 : (Number(item).div(total)).mul(allotPrice)).toFixed(2);
      alloted = alloted.add(allotList[index]);
    }
  });
  allotList = allotList.map(item => item * operator);
  return allotList;
}
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    readonly: {
      type: Boolean,
      default: false
    },
    // 单据类型 1：采购单，2： 入库单
    type: {
      type: String,
      default: '1'
    },
    allotType: {
      type: Array,
      default: () => {
        return [
          {
            value: '1',
            label: '按金额分配'
          },
          {
            value: '2',
            label: '按数量分配'
          }
        ]
      }
    },
    beforeAllot: {
      type: Function,
      default: () => true
    },
    maxItems: {
      type: Number,
      default: 3
    }
  },
  components: {
    EditableTable
  },
  computed: {
    feeMap() {
      return this.feeList.reduce((acc, cur) => {
        acc[cur.id] = cur.name;
        return acc;
      }, {});
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val && val.length > 0) {
          this.allotItems = val;
        }
      },
    }
  },
  data() {
    return {
      feeList: [],
      typeList,
      columns: [
        {
          type: 'index',
          title: '序号',
          width: 80,
          align: 'center'
        },
        {
          title: '分摊项目',
          render: (h, { row }) => {
            return h('span', this.feeMap[row.item] || '--');
          },
        },
        {
          title: '分摊类型',
          render: (h, params) => {
            let { row } = params;
            return h('span', typeMap[row.type] || '--');
          },
        },
        {
          title: '分摊金额',
          key: 'price',
        },
        {
          title: '操作人',
          key: 'create_user',
        },
        {
          title: '备注',
          key: 'remark',
        }
      ],
      allotFormData: {
        ...allotFormData
      },
      allotItems: [ { ...emptyItem } ],
      addModal: false,
      rules: {
        item: [
          {
            required: true,
            message: '请选择分摊项目',
          }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        costsType: [
          {
            required: true,
            message: '请选择分摊方式',
          }
        ],
        price: [
          {
            required: true,
            message: '请输入分摊金额',
          }
        ],
      },
      resetModal: false,
    };
  },
  created() {
    if (this.value.length > 0) {
      this.allotItems = this.value;
    }
    this.getFeeList();
  },
  methods: {
    getAllotItems() {
      return this.allotItems.filter(item => !item.empty);
    },
    async getFeeList() {
      const { status, data } = await get('/superAdmin/feeCost/list', {
        available_document: this.type
      });
      if (status) {
        this.feeList = data;
      } else {
        this.feeList = [];
      }
    },
    closeAddModal() {
      this.allotFormData = { ...allotFormData };
      this.$refs.form.resetFields();
      this.addModal = false;
    },
    showAddModal() {
      if (this.allotItems.length >= this.maxItems) {
        this.$smessage({ type: 'error', text: `最多只能添加${this.maxItems}条分摊记录` });
        return;
      }
      if (this.allotType.length > 0) {
        this.allotFormData.costsType = this.allotType[0].value;
      }
      this.addModal = true;
    },
    delItem() {
      this.$smodal({
          title: '确认',
          text: '所有分摊记录将被重置，确定删除？',
          type: 'warning',
          btns: 2,
          okTxt: '继续',
          quitTxt: '取消',
          onOk: () => {
            this.allotItems = [ { ...emptyItem } ]
            this.$emit('on-delete');
          },
        })
    },
    async addItem() {
      if (!this.beforeAllot(this.allotFormData)) {
        return;
      }
      const valid = await this.$refs.form.validate();
      if (!valid) {
        return;
      }
      let accountInfo = this.$store.state[this.globalConfig.store.stateKey.accountInfo];
      const create_user = accountInfo.username
      if (this.allotItems.length === 1 && this.allotItems[0].empty) {
        this.allotItems = [
          {
            ...this.allotFormData,
            create_user
          }
        ];
      } else {
        this.allotItems.push({
          ...this.allotFormData,
          create_user
        });
      }
      this.$emit('on-allot', { ...this.allotFormData }, allot);
      this.closeAddModal();
    }
  }
};
</script>
