export default {
  computed: {
    isHideSecondMenu() {
      // 兼容老的不显示二级导航的方案
      let noSecondMenuPages = [
        '/appCenter/index',
        '/index',
        '/behalfNewOrder',
        '/behalfOrderEdit',
        '/behalfOrderDetail',
        '/freight',
        '/appCenter/sceneOrder',
        '/appCenter/purchaseCosts',
        '/appCenter/appDetail',
        '/appCenter/commodityList',
      ];
      if (noSecondMenuPages.includes(this.$route.path)) {
        return true;
      }
      if (this.$store.state.showSecondMenu === false) {
        return true;
      }
      return false;
    }
  },
  methods: {
    hideSecondMenu() {
      this.$store.commit('toggleSecondMenu', false);
    },
    showSecondMenu() {
      this.$store.commit('toggleSecondMenu', true);
    },
  }
}
