import basic from '@api/basic.js';

export default {
    methods: {
        getCloudToken() {
            const cloudToken = sessionStorage.getItem('cloudToken');
            if (cloudToken && cloudToken!== 'null') {
                return new Promise((resolve, reject) => {
                    resolve(1);
                });
            } else {
                return new Promise((resolve, reject) => {
                    basic.cloudToken().then(res => {
                        if(res.status ==1 ) {
                            sessionStorage.setItem('cloudToken', res.data.cloud_token);
                            resolve(1);
                        } else {
                            reject(res.message);
                        }
                    }).catch(err => {
                        reject(err);
                    });
                });
            
            }
        }
    }
}