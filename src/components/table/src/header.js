import {
  getTextAlign,
  getCellStyle,
  isResizableExclude,
  getAllColumns,
} from './utils';
import { throttle } from 'lodash-es';
import Icon from '../../icon';
import { Tooltip } from 'view-design';
import Checkbox from '../../checkbox';

export default {
  name: 'Header',
  data() {
    return {
      dragging: false,
      draggingColumn: null,
      dragState: {},
      sortOrder: '',
      sortKey: '',
    };
  },
  props: {
    /** 表格高度, 用 getTableHeight()设置,手动调整为页面刚刚好无滚动条*/
    height: [String, Number],
    tablePrefix: String,
    columns: Array,
    resizable: Boolean,
    showVerticalScrollBar: Boolean,
    scrollBarWidth: Number,
    stickyData: {
      default: () => ({
        right: {},
        left: {},
      }),
    },
    checkAll: Boolean,
    showCheckAll: Boolean,
    getColumnStyle: Function,
    handleToggleCheckAll: Function,
    isOpenGroupingTableHead: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    tableHeadStyle() {
      return {
        position: this.height ? 'sticky' : 'relative',
        zIndex: 4,
        top: 0,
      };
    },
  },
  methods: {
    /**
     * @param order 排序顺序：asc | desc
     */
    handleSortChange(column, key, order) {
      if (!order) {
        order = this.getCorrectSortOrder(column);
      }
      this.sortOrder = order;
      column._sortOrder = order;
      this.sortKey = key;
      this.$parent.$emit('on-sort-change', column, key, order);
    },
    getCorrectSortOrder(column) {
      let order = column._sortOrder;
      if (column.asc) {
        if (order === 'desc') {
          order = '';
        } else if (order === 'asc') {
          order = 'desc';
        } else if (!order) {
          order = 'asc';
        }
      } else {
        if (order === 'desc') {
          order = 'asc';
        } else if (order === 'asc') {
          order = '';
        } else if (!order) {
          order = 'desc';
        }
      }
      return order;
    },
    isActiveSort(key, order) {
      return this.sortOrder === order && this.sortKey === key;
    },
    handleDraghandlerMouseout() {
      document.body.style.cursor = '';
    },
    handleDraghandlerMousedown(column) {
      if (this.draggingColumn) {
        this.dragging = true;
        const tableEl = this.$parent.$refs.bodyTable;
        const tableLeft = tableEl.getBoundingClientRect().left;
        const columnEl = event.target.parentNode;
        const columnRect = columnEl.getBoundingClientRect();
        this.$parent.showResizeLine = true;
        this.dragState = {
          startMouseLeft: event.clientX,
          startLeft: columnRect.right - tableLeft,
          startColumnLeft: columnRect.left - tableLeft,
          tableLeft,
          startWidth: columnRect.width,
        };
        document.onselectstart = function () {
          return false;
        };
        document.ondragstart = function () {
          return false;
        };
        const handleMouseMove = (event) => {
          document.body.style.cursor = 'col-resize';
          const { startMouseLeft, startWidth } = this.dragState;
          const endMouseLeft = event.clientX;
          const deltaX = endMouseLeft - startMouseLeft;
          let newWidth = startWidth + deltaX;
          if (column.minWidth && newWidth - column.minWidth < 0) {
            newWidth = column.minWidth;
          }
          if (column.width === undefined) {
            this.$set(column, 'width', newWidth);
          }
          column.width = isNaN(newWidth) ? column.width : newWidth;
          this.$parent.$emit(
            'on-column-width-resize',
            column.width,
            newWidth,
            event,
          );
        };
        const fn = throttle(handleMouseMove, 100);

        const handleMouseUp = () => {
          document.body.style.cursor = '';
          this.dragging = false;
          this.draggingColumn = null;
          this.dragState = {};
          this.$parent.showResizeLine = false;
          document.removeEventListener('mousemove', fn);
          document.removeEventListener('mouseup', handleMouseUp);
          document.onselectstart = null;
          document.ondragstart = null;
        };
        document.addEventListener('mousemove', fn);
        document.addEventListener('mouseup', handleMouseUp);
      }
    },
    handleDraghandlerMousemove(column, $event) {
      const columnEl = $event.target.parentNode;
      if (!columnEl) {
        return false;
      }
      if (!this.dragging) {
        let rect = columnEl.getBoundingClientRect();
        const bodyStyle = document.body.style;
        if (rect.width > 12 && rect.right - event.pageX < 8) {
          bodyStyle.cursor = 'col-resize';
          this.draggingColumn = column;
        } else if (!this.dragging) {
          bodyStyle.cursor = '';
          this.draggingColumn = null;
        }
      }
    },

    renderThCell(h, column) {
      let title =
        typeof column.title === 'function'
          ? column.title(h, column)
          : column.title;
      if (isResizableExclude(column)) {
        column.resizable = false;
      }
      const { type } = column;
      if (type === 'index') {
        if (column.title === undefined) column.title = '序号';
        return column.title;
      } else if (column.type === 'selection' && this.showCheckAll) {
        return (
          <Checkbox
            onOn-change={(checked) => this.handleToggleCheckAll(checked)}
            value={this.checkAll}
          />
        );
      }
      if (column.renderHeader) {
        return column.renderHeader(
          h,
          column,
          column.sortable && (() => this.handleSortChange(column, column.key)),
        );
      }

      if (column.tip) {
        const tipProps = column.tipProps || {};
        const tip =
          typeof column.tip === 'function' ? column.tip(h, column) : column.tip;
        return (
          <span
            class={column.require ? 'column__required' : ''}
            style={{ cursor: column.sortable ? 'pointer' : 'initial' }}
            onClick={() =>
              column.sortable && this.handleSortChange(column, column.key)
            }
          >
            {title}
            <Tooltip
              maxWidth={tipProps.maxWidth}
              transfer={true}
              placement={tipProps.placement || 'top'}
              class={`${this.tablePrefix}__tr-tip`}
            >
              <Icon size={12} style="cursor: pointer;" icon="tips" />
              <div slot="content" style="white-space: pre-wrap">
                {tip}
              </div>
            </Tooltip>
          </span>
        );
      }
      if (column.sortable) {
        return (
          <span
            style="cursor: pointer"
            onClick={() => this.handleSortChange(column, column.key)}
          >
            {title}
          </span>
        );
      }
      return title;
    },
    // 多级表头处理 columns
    convertToRows(originColumns) {
      let maxLevel = 1;
      const traverse = (column, parent) => {
        if (parent) {
          column.level = parent.level + 1;
          if (maxLevel < column.level) {
            maxLevel = column.level;
          }
        }
        if (column.children) {
          let colspan = 0;
          column.children.forEach((subColumn) => {
            traverse(subColumn, column);
            colspan += subColumn.colspan;
          });
          column.colspan = colspan;
        } else {
          column.colspan = 1;
          column.resizable = true;
        }
      };

      originColumns.forEach((column) => {
        column.level = 1;
        column.resizable = false;
        traverse(column);
      });

      const rows = [];
      for (let i = 0; i < maxLevel; i++) {
        rows.push([]);
      }

      const allColumns = getAllColumns(originColumns);

      allColumns.forEach((column) => {
        if (!column.children) {
          column.rowspan = maxLevel - column.level + 1;
        } else {
          column.rowspan = 1;
        }
        rows[column.level - 1].push(column);
      });

      return rows;
    },
  },
  render(h) {
    if (this.isOpenGroupingTableHead) {
      const columnsNew = this.convertToRows(this.columns, this.columns);
      const header = (
        <thead style={this.tableHeadStyle} class={`${this.tablePrefix}__thead`}>
          {columnsNew.map((columns) => {
            return (
              <tr class={`${this.tablePrefix}__tr`} key="">
                {columns.map((column) => {
                  // console.log('column', column.width)
                  let columnStyle = this.getColumnStyle(column);
                  if (
                    column.fixed &&
                    column.fixed === 'right' &&
                    columnStyle.right
                  ) {
                    columnStyle.right =
                      parseInt(columnStyle.right) +
                      (this.showVerticalScrollBar ? this.scrollBarWidth : 0) +
                      'px';
                  }
                  const thStyle = {
                    ...columnStyle,
                    textAlign: getTextAlign(column),
                    borderTop: 'none',
                  };
                  const thClass = {
                    [this.tablePrefix + '__th']: true,
                    [this.tablePrefix + '__th--fixed']: column.fixed,
                    [column.className]: !!column.className,
                  };
                  const cellStyle = getCellStyle(column);
                  const headerSort = (
                    <div class={`${this.tablePrefix}__sort`}>
                      <Icon
                        nativeOnClick={() =>
                          this.handleSortChange(column, column.key, 'asc')
                        }
                        icon="arrow-solid-up"
                        class={[
                          `${this.tablePrefix}__sort__item ${this.tablePrefix}__sort__up`,
                          { active: this.isActiveSort(column.key, 'asc') },
                        ]}
                      />
                      <Icon
                        nativeOnClick={() =>
                          this.handleSortChange(column, column.key, 'desc')
                        }
                        icon="arrow-solid-up"
                        class={[
                          `${this.tablePrefix}__sort__item ${this.tablePrefix}__sort__down`,
                          { active: this.isActiveSort(column.key, 'desc') },
                        ]}
                      />
                    </div>
                  );
                  // 给每列都设置一个最小宽度，防止列拖拽宽度时被拖动到隐藏不见
                  // 最小值设置为title的宽度，没有title则设置为左右padding + 一个字符的宽度： 18 + 18 + 12 = 48
                  column.minWidth = column.minWidth
                    ? column.minWidth
                    : column.title
                      ? column.title.length * 12 + 36
                      : 48;
                  return (
                    <th
                      class={thClass}
                      style={thStyle}
                      rowspan={column.rowspan}
                      colspan={column.colspan}
                    >
                      <div
                        style={cellStyle}
                        class={`${this.tablePrefix}__cell`}
                      >
                        {this.renderThCell(h, column)}
                        {column.sortable ? headerSort : ''}
                      </div>
                      {(this.resizable && column.resizable !== false) ||
                      (!this.resizable && column.resizable === true) ? ( // resizable参数默认打开，需要单独控制时将table的resizable设置为false,再单独设置列的resizable为true
                        <div
                          draggable="false"
                          class={`${this.tablePrefix}__th__drag-handler`}
                          onMouseout={($event) =>
                            this.handleDraghandlerMouseout(column, $event)
                          }
                          onMousedown={($event) =>
                            this.handleDraghandlerMousedown(column, $event)
                          }
                          onMousemove={($event) =>
                            this.handleDraghandlerMousemove(column, $event)
                          }
                        ></div>
                      ) : (
                        ''
                      )}
                    </th>
                  );
                })}
              </tr>
            );
          })}
        </thead>
      );
      return header;
    } else {
      const header = (
        <thead style={this.tableHeadStyle} class={`${this.tablePrefix}__thead`}>
          <tr class={`${this.tablePrefix}__tr`}>
            {this.columns.map((column) => {
              let columnStyle = this.getColumnStyle(column);
              if (
                column.fixed &&
                column.fixed === 'right' &&
                columnStyle.right
              ) {
                columnStyle.right =
                  parseInt(columnStyle.right) +
                  (this.showVerticalScrollBar ? this.scrollBarWidth : 0) +
                  'px';
              }
              const thStyle = {
                ...columnStyle,
                textAlign: getTextAlign(column),
              };
              const thClass = {
                [this.tablePrefix + '__th']: true,
                [this.tablePrefix + '__th--fixed']: column.fixed,
                [column.className]: !!column.className,
              };
              const cellStyle = getCellStyle(column);
              const headerSort = (
                <div class={`${this.tablePrefix}__sort`}>
                  <Icon
                    nativeOnClick={() =>
                      this.handleSortChange(column, column.key, 'asc')
                    }
                    icon="arrow-solid-up"
                    class={[
                      `${this.tablePrefix}__sort__item ${this.tablePrefix}__sort__up`,
                      { active: this.isActiveSort(column.key, 'asc') },
                    ]}
                  />
                  <Icon
                    nativeOnClick={() =>
                      this.handleSortChange(column, column.key, 'desc')
                    }
                    icon="arrow-solid-up"
                    class={[
                      `${this.tablePrefix}__sort__item ${this.tablePrefix}__sort__down`,
                      { active: this.isActiveSort(column.key, 'desc') },
                    ]}
                  />
                </div>
              );
              // 给每列都设置一个最小宽度，防止列拖拽宽度时被拖动到隐藏不见
              // 最小值设置为title的宽度，没有title则设置为左右padding + 一个字符的宽度： 18 + 18 + 12 = 48
              column.minWidth = column.minWidth
                ? column.minWidth
                : column.title
                  ? column.title.length * 12 + 36
                  : 48;
              return (
                <th class={thClass} style={thStyle}>
                  <div style={cellStyle} class={`${this.tablePrefix}__cell`}>
                    {this.renderThCell(h, column)}
                    {column.sortable ? headerSort : ''}
                  </div>
                  {/* 滚动条占位元素 */}
                  {column.fixed === 'right' && this.showVerticalScrollBar && (
                    <div
                      class={`${this.tablePrefix}__scrollbar_placeholder`}
                      style={{
                        width: `${this.scrollBarWidth + 2}px`,
                        right: `-${this.scrollBarWidth + 1}px`,
                      }}
                    ></div>
                  )}
                  {(this.resizable && column.resizable !== false) ||
                  (!this.resizable && column.resizable === true) ? ( // resizable参数默认打开，需要单独控制时将table的resizable设置为false,再单独设置列的resizable为true
                    <div
                      draggable="false"
                      class={`${this.tablePrefix}__th__drag-handler`}
                      onMouseout={($event) =>
                        this.handleDraghandlerMouseout(column, $event)
                      }
                      onMousedown={($event) =>
                        this.handleDraghandlerMousedown(column, $event)
                      }
                      onMousemove={($event) =>
                        this.handleDraghandlerMousemove(column, $event)
                      }
                    ></div>
                  ) : (
                    ''
                  )}
                </th>
              );
            })}
          </tr>
        </thead>
      );
      return header;
    }
  },
};
