<!--
 * @Description: 司机筛选组件
 * @Date: 2022-09-02 10:08:38
 * @LastEditors: hgj
 * @LastEditTime: 2023-01-30 13:49:51
 * @FilePath: /sdpbase-pro/src/components/delivery/driverSelect.vue
-->
<template>
  <Select
    v-model="selfValue"
    @on-change="updateValue"
    :transfer="true"
    :className="'line-modal'"
    :disabled="disabled"
    :filterable="filterable"
    :clearable="true"
    :placeholder="placeholder"
  >
    <Option
      :value="item.driver_id"
      v-for="item in list"
      :key="item.driver_id"
      >{{ item.driver_name }}</Option
    >
  </Select>
</template>

<script>
export default {
  name: 'driverSelect',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    cache: {
      type: Boolean,
      default: true,
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '选择司机',
    },
  },
  watch: {
    value(newValue) {
      this.selfValue = newValue;
    },
  },
  data() {
    return {
      selfValue: '',
      list: [],
    };
  },
  created() {
    this.selfValue = this.value;
    this.getList();
  },
  activated() {
    this.selfValue = this.value;
    this.getList();
  },
  deactivated() {
    this.list = [];
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        driver_id: '',
        driver_name: '全部',
      };
      let params = {
        pageSize: 99999,
      };

      this.$request
        .get(this.apiUrl.delivery.driver.list, params, { cache: this.cache })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            let list = data.list;
            if (this.showAll) {
              if (
                this.list &&
                this.list.find(
                  (v) => v.driver_name === '全部' && v.driver_id === '',
                )
              ) {
                return;
              }
              this.list.unshift(defaultItem);
            }
            this.list = list;
          } else {
            this.list = [];
          }
        });
    },
    updateValue() {
      this.$emit('input', this.selfValue);
      let currentDriver = this.list.find(
        (driver) => driver.driver_id === this.selfValue,
      );
      this.$emit('on-change', this.selfValue, currentDriver);
    },
  },
};
</script>

<style scoped></style>
