<!--
 * @Author: <PERSON>
 * @Date: 2021-08-18 10:53:49
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-15 18:21:52
 * @Description: 区域选择器（关联仓库）
-->
<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :clearable="true"
    :multiple="multiple"
    :placeholder="placeholder"
    :max-tag-count="1"
    :max-tag-placeholder="num => `+ ${num}`"
  >
    <Option :value="item.area_id" v-for="item in list" :key="item.area_id">{{item.area_name}}</Option>
  </Select>
</template>

<script>
import storeRoom from '@/api/storeRoom'
import user from '@api/user.js'

export default {
  name: "AreaSelect",
  props: {
    value: {
      type: [String, Number, Array],
      default: ''
    },
    storeId: {
      type: [String, Number],
      default: ''
    },
    filterable: {
      type: <PERSON>olean,
      default: true
    },
    placeholder: {
      default: '选择区域'
    },
    showAll: {
      type: Boolean,
      default: false
    },
    noCheck: {
      type: Boolean,
      default: false
    },
    initChange: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    apiName: {
      type: String,
      default: 'getAreaMap'
    }
  },
  watch: {
    value (newValue) {
      this.selfValue = newValue
    },
    storeId () {
      this.getFilterList()
    }
  },
  data () {
    return {
      selfValue: '',
      list: [],
      data: {}
    }
  },
  created () {
    this.getList()
  },
  methods: {
    getList () {
      this.list = []
      if (this.noCheck) {
        user.getAreaList().then(res => {
          if (res.status) {
            if (this.showAll) res.data.unshift({ area_id: '', area_name: '全部区域' })
            this.list = res.data
          } else this.list = []
        })
        return
      }
      storeRoom[this.apiName]().then(res => {
        const { status, data } = res
        if (status) {
          this.data = data
          this.getFilterList()
        } else {
          this.list = []
        }
      })
    },
    getFilterList () {
      const data = this.data
      let list = []
      let defaultItem = {
        area_id: '',
        area_name: '全部区域',
      }
      if (data.no_check_area && data.no_check_area.length) {
        list = data.no_check_area.map(({ id, name }) => ({ area_id: id, area_name: name }))
      }
      if (this.showAll) {
        list.unshift(defaultItem)
      }
      if (data.user_warehouse && data.user_warehouse.length) {
        data.user_warehouse.forEach(item => {
          if (item.area_info && item.area_info.length) {
            if (this.storeId && item.id) {
              if (this.storeId.toString().split(',').includes(item.id.toString())) {
                list.push(...item.area_info.map(({ area_id, area_name }) => ({ area_id, area_name })))
              }
            } else {
              list.push(...item.area_info.map(({ area_id, area_name }) => ({ area_id, area_name })))
            }
          }
        })
      }
      this.list = list
      if (this.selfValue || this.initChange) {
        this.selfValue = ''
        this.$emit('on-change', this.selfValue)
      }
    },
    updateValue () {
      this.$emit('input', this.selfValue)
      this.$emit('on-change', this.selfValue)
    }
  }
}
</script>

<style scoped>
</style>
