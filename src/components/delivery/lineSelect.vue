<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :disabled="disabled"
    :multiple="multiple"
    :clearable="true"
    :placeholder="placeholder"
  >
    <Option :value="item.line_id" v-for="item in list" :key="item.line_id">{{
      item.line_name
    }}</Option>
  </Select>
</template>

<script>
export default {
  name: 'lineSelect',
  props: {
    forAutoPrintPage: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Number, Array],
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    remote: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    showAll: {
      type: Boolean,
      default: true
    },
    placeholder: {
      default: '选择线路'
    },
    params: {
      type: Object,
      default: () => {}
    },
    customerItem: {
      type: Object,
      default: () => {}
    },
    defaultValue: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value(newValue) {
      this.selfValue = newValue;
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      }
    },
    params: {
      deep: true,
      handler() {
        if (this.remote) {
          this.getList();
        }
      }
    }
  },
  data() {
    return {
      selfValue: '',
      list: []
    };
  },
  created() {
    this.selfValue = this.value;
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data;
    }
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        line_id: '',
        line_name: '所有线路'
      };
      if (this.forAutoPrintPage) {
        defaultItem = {
          line_id: '0',
          line_name: '所有线路'
        };
      }
      let params = {
        pageSize: 99999,
        ...this.params
      };
      this.$request.get(this.apiUrl.delivery.line.list, params).then(res => {
        let { status, data } = res;
        if (status) {
          let list = data.list;
          if (this.showAll) {
            list.unshift(defaultItem);
          }
          if (this.customerItem && this.customerItem.line_id) {
            list.unshift(this.customerItem);
          }
          this.list = list;
          if (this.forAutoPrintPage) {
            setTimeout(() => {
              this.selfValue = ['0'];
            });
          }
          // 如果线路设置了默认线路 新增客户时默认取该线路 {param} defaultValue (Boolean)
          if (this.defaultValue) {
            data.list.forEach(item => {
              if (+item.is_default === 1) {
                this.selfValue = [item.line_id];
              }
            });
          }
        } else {
          this.list = [];
        }
      });
    },
    setQuery(query) {
      this.$refs.select.setQuery(query);
    },
    updateValue() {
      let selectedItem = this.list.find(
        item => item.line_id === this.selfValue
      );
      // eslint-disable-next-line no-unused-vars
      let label = '';
      if (selectedItem) {
        label = selectedItem.line_name;
      }
      this.$emit('input', this.selfValue);
      this.$emit('on-change');
    },
    getSelectedItem() {
      return this.list.find(item => item.line_id === this.selfValue);
    }
  }
};
</script>

<style scoped></style>
