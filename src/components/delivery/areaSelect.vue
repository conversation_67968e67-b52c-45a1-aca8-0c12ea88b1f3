<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :clearable="true"
    :placeholder="placeholder">
    <Option :value="item.area_id" v-for="item in list" :key="item.area_id">{{item.area_name}}</Option>
  </Select>
</template>

<script>
  export default {
    name: "areaSelect",
    props: {
      value: {
        type: [String, Number],
        default: ''
      },
      filterable: {
        type: Boolean,
        default: true
      },
      placeholder: {
        default: '选择区域'
      }
    },
    watch: {
      value(newValue) {
        this.selfValue = newValue;
      }
    },
    data() {
      return {
        selfValue: '',
        list: [],
      }
    },
    created() {
      this.selfValue = this.value;
      this.getList();
    },
    methods: {
      getList() {
        this.list = [];
        let defaultItem = {
          area_id: '',
          area_name: '所有区域',
        };
        let params = {
          pageSize: 99999
        };
        this.$request.get(this.apiUrl.delivery.area.list, params).then((res) => {
          let {status, data} = res;
          if (status) {
            let list = data.list;
            list.unshift(defaultItem);
            this.list = list;
          } else {
            this.list = [];
          }
        });
      },
      setQuery(query) {
        this.$refs.select.setQuery(query);
      },
      updateValue() {
        this.$emit('input', this.selfValue);
        this.$emit('on-change', this.selfValue || '');
      }
    }
  }
</script>

<style scoped>

</style>
