<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :disabled="disabled"
    :multiple="multiple"
    :clearable="true"
    :max-tag-count="maxTagCount"
    :placeholder="placeholder"
  >
    <Option
      :value="item.line_id"
      v-for="(item, index) in list"
      :key="`line-${item.line_id || 'unknown'}-${index}`"
      >{{ item.line_name }}</Option
    >
  </Select>
</template>

<script>
export default {
  name: 'lineSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: '',
    },
    maxTagCount: {
      type: Number,
      default: 1,
    },
    data: {
      type: Array,
      default: () => [],
    },
    remote: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      default: '选择线路',
    },
    multiple: {
      type: <PERSON><PERSON>an,
      default: false,
    },
    params: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        if (this.default === null) {
          this.default = newValue;
        }
        this.selfValue = newValue;
      },
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      },
    },
    params: {
      deep: true,
      handler() {
        if (this.remote) {
          this.getList();
        }
      },
    },
  },
  data() {
    return {
      selfValue: '',
      list: [],
      default: null,
    };
  },
  created() {
    this.selfValue = this.value;
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data;
    }
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        line_id: '',
        line_name: '所有线路',
      };
      let params = {
        pageSize: 99999,
        ...this.params,
      };
      this.$request
        .get(this.apiUrl.delivery.line.list, params, { cache: true })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            let list = data.list;
            if (this.showAll) {
              if (
                !(
                  list &&
                  list.some(
                    (v) =>
                      v.line_id === defaultItem.line_id &&
                      v.line_name === defaultItem.line_name,
                  )
                )
              ) {
                list.unshift(defaultItem);
              }
            }
            this.list = list;
          } else {
            this.list = [];
          }
        });
    },
    setQuery(query) {
      this.$refs.select.setQuery(query);
    },
    updateValue() {
      // let selectedItem = this.list.find(item => item.line_id === this.selfValue);
      // let label = '';
      // if (selectedItem) {
      //   label = selectedItem.line_name
      // }
      this.$emit('input', this.selfValue);
      this.$emit('on-change', this.selfValue);
    },
    getSelectedItem() {
      return this.list.find((item) => item.line_id === this.selfValue);
    },
    resetValue() {
      this.selfValue = this.default;
    },
  },
};
</script>

<style scoped></style>
