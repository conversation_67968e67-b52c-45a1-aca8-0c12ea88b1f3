<style lang="less" scoped>
.deliveryTimeSelect {
  width: 230px;
  /deep/ .ivu-select-placeholder {
    padding-left: 8px;
  }
}
</style>

<template>
  <div class="deliveryTimeSelect">
    <Select
      v-model="selfValue"
      @on-change="updateValue"
      :multiple="multiple"
      :max-tag-count="maxTagCount"
      :max-tag-placeholder="(num) => `+ ${num}`"
      :filterable="filterable"
      :clearable="clearable"
      :placeholder="placeholder"
    >
      <Option value="" v-if="showAll">{{ label }}</Option>
      <Option :value="item.id" v-for="item in list" :key="item.id"
        >{{ item.name || '' }} {{ item.start_time }}-{{ item.end_time }}</Option
      >
    </Select>
  </div>
</template>

<script>
export default {
  name: 'deliveryTimeSelect',
  props: {
    value: {
      type: [String, Number, Array],
      default: '',
    },
    data: {
      type: Array,
      default: () => [],
    },
    remote: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '全部送货时间',
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      default: '选择送货时间',
    },
    maxTagCount: {
      type: Number,
      default: 2,
    },
  },
  watch: {
    value(newValue) {
      this.setSelfValue(newValue);
    },
    data: {
      deep: true,
      handler(newValue) {
        if (!this.remote) {
          this.list = newValue;
        }
      },
    },
  },
  data() {
    return {
      selfValue: '',
      list: [],
    };
  },
  created() {
    this.setSelfValue(this.value);
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data || [];
    }
  },
  methods: {
    setSelfValue(value) {
      if (this.multiple && !Array.isArray(value)) {
        value = [];
      }
      this.selfValue = value;
    },
    getList() {
      this.list = [];
      let params = {
        pageSize: 99999,
      };
      this.$request
        .get(this.apiUrl.getDeliveryTime, params, { cache: true })
        .then((res) => {
          let { status, data } = res;
          if (status) {
            let list = data.list;
            this.list = list;
          } else {
            this.list = [];
          }
        });
    },
    updateValue() {
      let selectedItem = this.list.find((item) => item.id === this.selfValue);
      let label = '';
      if (selectedItem) {
        label = selectedItem.start_time + '-' + selectedItem.end_time;
      }
      this.$emit('input', this.selfValue);
      this.$emit('on-change', label, selectedItem);
    },
    getSelectedItem() {
      return this.list.find((item) => item.id === this.selfValue);
    },
  },
};
</script>

<style scoped></style>
