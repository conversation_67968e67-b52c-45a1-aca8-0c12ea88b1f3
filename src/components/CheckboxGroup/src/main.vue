<!--
 * @Descripttion:
 * @Autor: <PERSON>
 * @Date: 2020-11-17 12:06:37
 * @LastEditTime: 2023-03-16 17:20:02
 * @FilePath: \sdpbase-pro\src\components\CheckboxGroup\src\main.vue
-->
<template>
  <CheckboxGroup v-model="selected" @on-change="checkboxGroupChange">
    <Checkbox
      v-for="(item, index) in data"
      :label="item.value"
      :key="index"
      :disabled="disabled || item.disabled"
    >
      <span class="sdp-no-replace-text">{{ item.label }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>

<script>
import { Checkbox, CheckboxGroup } from 'view-design';

export default {
  name: 'check_box_group',
  components: {
    Checkbox,
    CheckboxGroup,
  },
  props: {
    value: {
      default: () => {
        return [];
      },
    },
    data: {
      default() {
        return [];
      },
    },
    defaultValue: {
      default() {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      selected: [],
    };
  },
  watch: {
    value: {
      handler(val) {
        this.selected = val || [];
      },
      // deep: true,
      immediate: true,
    },
  },
  computed: {},
  methods: {
    getValue() {
      return this.selected;
    },
    resetValue(e = {}) {
      this.selected = e.defaultValue || [];
    },
    checkboxGroupChange() {
      this.$emit('on-change', this.selected);
    },
    findSelectedItem() {
      let result = [];
      this.selected.map((label) => {
        let find = this.data.find((item) => {
          return item.label === label;
        });
        if (find) {
          result.push(find);
        }
      });
      return result;
    },
  },
  created() {
    if (this.defaultValue && this.defaultValue.length) {
      this.selected = JSON.parse(JSON.stringify(this.defaultValue));
    }
    // this.$emit('on-change', this.selected);
  },
  mounted() {},
};
</script>
<style lang="less"></style>
