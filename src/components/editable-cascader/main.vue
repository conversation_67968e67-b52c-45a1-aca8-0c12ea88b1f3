<template>
  <div class="s-cascader">
    <template v-if="editStatus">
      <el-cascader
        ref="cascader"
        v-model="valueCopy"
        :options="options"
        :data="options"
        :placeholder="placeholder"
        @change="changeVal"
        :filterable="filterable"
        :collapse-tags="true"
        size="mini"
        clearable
        :props="{ multiple: multiple, expandTrigger: expandTrigger }"
        :default-expanded-values="editStatus ? _expandedValues : []"
      ></el-cascader>
    </template>
    <template v-if="!editStatus">
      <Input
        name="s-edit-cascader-input"
        class="inner-input s-edit-cascader-input"
        :value="showValue"
      ></Input>
    </template>
  </div>
</template>

<script>
import { Cascader } from 'element-ui';

export default {
  name: 's-edit-cascader',
  components: {
    elCascader: Cascader,
  },
  props: {
    data: {
      type: [Array],
      default: () => [],
    },
    value: {
      type: [Array],
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择商品分类',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    expandTrigger: {
      type: String,
      default: 'click',
    },
    editStatus: {
      type: Boolean,
      default: false,
    },
    showValue: {
      type: String,
      default: '',
    },
    expandedValues: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      valueCopy: [],
      options: [],
      _expandedValues: [],
    };
  },

  watch: {
    value: {
      handler(newVal) {
        this.valueCopy = newVal;
      },
      deep: true,
      immediate: true,
    },
    treeData: {
      handler(newVal) {
        this.options = newVal;
      },
      deep: true,
      immediate: true,
    },
    expandedValues: {
      handler(newVal) {
        this._expandedValues = newVal;
      },
      deep: true,
      immediate: true,
    },
    editStatus: {
      handler(newVal) {
        if (newVal) {
          this.openCascader();
        }
      },
      immediate: true,
    },
  },

  methods: {
    changeVal(val) {
      this.$emit('input', val);
      this.$emit('change', val);
    },
    openCascader() {
      // 延迟执行，确保 DOM 渲染完成后展开下拉框
      this.$nextTick(() => {
        const cascader = this.$refs.cascader;
        if (cascader && cascader.toggleDropDownVisible) {
          cascader.toggleDropDownVisible(true);
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
.s-cascader {
  /deep/ .el-cascader {
    width: 232px;
    .el-input__inner {
      height: 30px !important;
      line-height: 30px;
      border: 1px solid #d8d8d8;
      border-radius: 2px;
      font-size: 13px;
      font-weight: 400;
      color: #333333 !important;
    }
    .el-cascader__tags {
      .el-cascader__search-input {
        margin: 0 0 0px 10px;
        min-width: 110px;
      }
      .el-tag {
        span {
          max-width: 160px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .el-tag:last-of-type {
        margin-left: 1px;
      }
      input::-webkit-input-placeholder {
        font-size: 13px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5) !important;
      }
    }
  }
  /deep/ .el-cascader.max-input {
    .el-input__inner {
      height: 58px !important;
    }
  }
  .inner-input {
    width: 230px;
  }
}
</style>
