<template>
  <div class="layout-main">
    <LayoutMenu v-if="menuData" :menuData="menuData" />
    <div class="layout-main__content" :class="{ hasBread: showBreadCrumbs }">
      <div v-show="showBreadCrumbs">
        <slot name="breadCrumbs"></slot>
      </div>
      <div
        class="layout-main__view"
        :style="{
          minHeight,
          marginBottom: showFootLogo ? null : 0,
          background: $route.path === '/index' ? 'none' : 'white',
        }"
      >
        <slot name="router"></slot>
        <div
          :style="{ height: minHeight, position: 'relative' }"
          class="s-loading s-loading--default"
          v-show="$store.state.routeLoading"
        >
          <span class="s-loading-content" style="margin-top: -60px"></span>
          <p class="s-loading-text">当前网络较慢，请耐心等待...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import LayoutMenu from './menu';
import common from '@api/main.js';
import ConfigMixin from '@/mixins/config';
import MenuMixin from '@/mixins/menu';

// 遍历层级控制
const MAX_LV = 1;
export default {
  name: 'layout-main',
  mixins: [ConfigMixin, MenuMixin],
  components: { LayoutMenu },
  data() {
    return {
      remoteConfig: null,
      menuData: null,
      menuMode: 0,
      minHeight: 0,
    };
  },
  created() {
    this.menuModeChangeEvent = (menuMode) => {
      this.menuMode = menuMode;
    };
    this.$root.$on('menuModeChange', this.menuModeChangeEvent);
    this.createDo();
    window.addEventListener('resize', () => {
      if (document.body) {
        this._setMinHeight();
      }
    });
  },
  beforeDestroy() {
    this.$root.$off('menuModeChange', this.menuModeChangeEvent);
  },
  mounted() {
    this._setMinHeight();
    // 打印js可以在主页内容挂载完成之后再加载
    setTimeout(() => {
      // 加载打印 js
      import('@/util/printJs').then((print) => {
        print.loadPrintJs();
      });
      // 挂载打印
      import('@/util/print').then(({ selectPrinterAndContinuePrint }) => {
        window.__selectPrinterAndContinuePrint = selectPrinterAndContinuePrint;
      });
    }, 3000);
  },
  watch: {
    $route() {
      this._setMinHeight();
    },
  },
  computed: {
    showBreadCrumbs() {
      const path = this.$route.path;
      const meta = this.$route.meta;
      if (path === '/') {
        return false;
      }
      return (
        !meta.noBreadCrumbs &&
        this.$store.state.hideBreadCrumbsList.indexOf(path) === -1
      );
    },
    needReduceheight() {
      return this.showBreadCrumbs ? 130 : 85;
    },
    showFootLogo() {
      return this.$store.state.showFootLogo;
    },
  },
  methods: {
    _setMinHeight() {
      this.minHeight =
        document.body.clientHeight - this.needReduceheight + 'px';
    },
    createDo() {
      // 创建做的
      const menuData = localStorage.getItem('menuData');
      try {
        menuData && (this.menuData = JSON.parse(menuData));
      } catch (e) {
        console.log(e);
      }
      // 本地有菜单数据时延迟执行请求接口
      if (menuData) {
        setTimeout(() => {
          this.menuInit();
        }, 3000);
      } else {
        this.menuInit();
      }
    },
    menuInit() {
      // 菜单初始
      common.getMenu().then((res) => {
        this.remoteConfig = res.data;
        if (res.status === 1) {
          this.menuCreate(res.data);
        }
      });
    },
    menuCreate(data) {
      // 根据数据创建菜单
      if (data && data.length > 0 && data[0]) {
        localStorage.setItem('firstMenu', data[0].index_key);
        localStorage.setItem('first_page', data[0].index_key);
      }
      this.$store.commit('setAuthConfig', this._getAuthConfig(data));
      this.menuData = this.menuFormat(data);
      localStorage.setItem('menuData', JSON.stringify(this.menuData));
      // 存储一份原始菜单数据，这里存储的格式化数据在一些更细致的场景没法满足
      localStorage.setItem('originalMenuData', JSON.stringify(data));
      // 判断当前用户是否有添加现场采购单权限，在订单列表页面调用 menu 接口消耗较大，所以放到这里来判断
      let canAddSceneOrder;
      if (data && data.length) {
        let orderMenu = data.find((item) => item.id === 'M002');
        if (orderMenu && orderMenu.son && orderMenu.son.length) {
          let orderListMenu = orderMenu.son.find(
            (item) => item.id === 'M002001',
          );
          if (orderListMenu && orderListMenu.son && orderListMenu.son.length) {
            canAddSceneOrder = orderListMenu.son.find(
              (item) => item.id === 'M002010',
            );
          }
        }
      }
      if (canAddSceneOrder) {
        this.$store.commit(
          this.globalConfig.store.stateKey.canAddSceneOrder,
          true,
        );
      } else {
        this.$store.commit(
          this.globalConfig.store.stateKey.canAddSceneOrder,
          false,
        );
      }
    },
    /**
     * @description: 递归获取所有菜单项code
     * @param {Array} data 待处理的菜单数理
     * @return {Array}
     */
    _getAuthConfig(data) {
      return data.reduce((result, item) => {
        return result.concat(
          Array.isArray(item.son) && item.son.length > 0
            ? this._getAuthConfig(item.son)
            : item.code,
        );
      }, []);
    },
  },
};
</script>

<style lang="less">
.layout-main {
  background: #f2f3f5;
  height: 100%;
  &__content {
    padding-top: 48px;
    overflow-x: hidden;
    height: 100%;
    &.hasBread {
      padding-top: 94px;
    }
  }
  &__view {
    background: #fff;
    margin: 14px;
  }
  &__footer {
    text-align: center;
    padding-bottom: 14px;
    font-weight: 700;
    font-size: 16px;
    line-height: 16px;
  }
  &__link,
  &__link:hover {
    color: #d0d0d0;
  }
}
/deep/.list-table__pagination-wrap {
  padding: 10px 0;
}
.s-loading-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: rgba(0, 0, 0, 0.75);
  font-size: 12px;
}
</style>
