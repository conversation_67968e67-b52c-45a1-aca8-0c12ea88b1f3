<template>
  <div class="layout-menu-item">
    <ul class="layout-menu-item__list">
      <li
        class="layout-menu-item__list-item"
        :class="{
          close: toggleData[index],
          divider: index != 0 && +item.is_group_end,
        }"
        :key="item.power_code"
        @click="handleClick(item, index)"
        v-for="(item, index) in menuItemData"
      >
        <router-link
          class="layout-menu-item__link"
          :class="{ active: isActive(item) }"
          v-if="item.path"
          :to="item.path"
          >{{ item.name }}</router-link
        >
        <div class="layout-menu-item__title" @click="toggleItem(index)" v-else>
          <s-icon icon="arrow-down" :size="12" /><span>{{ item.name }}</span>
        </div>
        <ul
          class="layout-menu-item__sublist"
          v-if="item.children && item.children.length"
          :style="{
            height: toggleData[index] ? 0 : 40 * item.children.length + 'px',
          }"
          :class="{ trans }"
        >
          <li
            @click.stop="handleClick(itm, index, idx)"
            class="layout-menu-item__sublist-item"
            :key="idx"
            v-for="(itm, idx) in item.children"
          >
            <router-link
              class="layout-menu-item__link"
              :class="{ active: isActive(itm) }"
              :to="itm.path"
              >{{ itm.name }}</router-link
            >
          </li>
        </ul>
      </li>
    </ul>
    <div
      v-show="showToggleBtn"
      class="layout-menu-item__status"
      @click="toggleMode"
    >
      <span class="s-icon icon-toggle" :class="{ scale: !showMode }"></span
      ><span>{{ showMode ? '固定' : '收起' }}</span>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import { SIcon } from '@sdp/ui';
import storage from '@util/storage';
// import performanceLogger from '@util/performanceLogger';
export default {
  name: 'layout-menu-item',
  components: { SIcon },
  props: {
    menuItemData: {
      type: Array,
      required: true,
    },
    subMenuMode: {
      type: Number,
    },
    curPath: {
      type: String,
      default: '',
    },
    showToggleBtn: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      menuItemDataInited: true,
      showMode: 0,
      toggleData: {},
      trans: false,
      timer: null,
      selfCurPath: '',
    };
  },
  created() {
    this.selfCurPath = this.$route.path;
    this.showMode = this.subMenuMode;
    this.$root.$emit('menuModeChange', this.showMode);
  },
  watch: {
    '$route.path': function (path) {
      this.selfCurPath = path;
    },
    curPath(newValue) {
      this.selfCurPath = newValue;
    },
    menuItemData: {
      immediate: true,
      handler() {
        this.trans = false;
        this.menuItemData.forEach((item, index) => {
          item.path || this.$set(this.toggleData, index, false);
          if (item.id === 'M009-1') {
            if (Array.isArray(item.children) && item.children.length >= 1) {
              item.children.forEach((item2) => {
                if (item2.code === 'M009002') {
                  let type =
                    storage && storage.getLocalStorage('audit-list-type');
                  if (type === 1) {
                    item2.path = '/finance/user/audit-list-new';
                  } else {
                    item2.path = '/finance/user/audit-list';
                  }
                }
              });
            }
          }
        });
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
          this.trans = true;
        });
      },
    },
  },
  computed: {},
  methods: {
    handleClick(item, index, childIdx) {
      const action = () => {
        // try {
        //   performanceLogger.pageLoadStart(item.path);
        // } catch (err) {
        //   console.error(err);
        // }
        if (item.code === 'M009002') {
          let type = storage && storage.getLocalStorage('audit-list-type');
          if (type === 1) {
            this.menuItemData[index].children[childIdx].path =
              '/finance/user/audit-list-new';
          } else {
            this.menuItemData[index].children[childIdx].path =
              '/finance/user/audit-list';
          }
        }
        this.selfCurPath = item.path;
        if (item.path === '/finance/user/audit-list-new') {
          this.selfCurPath = '/finance/user/audit-list';
        }
      };

      // 哪些页面位置发生变更
      const changePgae = [
        {
          changeName: '客户',
          code: 'reports_sales_receive-statistics',
        },
        {
          changeName: '客户',
          code: 'M010019',
        },
        {
          changeName: '客户',
          code: 'M010031',
        },
      ];
      const isChangePage = changePgae.find((k) => k.code === item.code);
      if (isChangePage) {
        this.$smodal({
          title: '页面位置变更提示',
          text: `本页面已调整至"${isChangePage.changeName}"模块,点击"立即前往"立即打开`,
          type: 'warning',
          btns: 2,
          okTxt: '立即前往',
          quitTxt: '取消',
          onOk: () => {
            // 手动跳转
            const nextPage = item.path.replace('OldPage', '');
            // item.path = nextPage
            this.$router.push(nextPage);

            action();
          },
        });
      } else {
        action();
      }
    },
    toggleItem(index) {
      this.toggleData[index] = !this.toggleData[index];
    },
    toggleMode() {
      // 0:固定模式 1:收起模式
      this.showMode = this.showMode === 0 ? 1 : 0;
      this.$root.$emit('menuModeChange', this.showMode);
      this.$store.commit('toggleSecondMenu', this.showMode !== 1);
    },
    isActive(item) {
      const curPath = this.selfCurPath;
      return (
        item.path === curPath ||
        (item.path_list && item.path_list.indexOf(curPath) !== -1)
      );
    },
  },
};
</script>

<style lang="less">
.layout-menu-item {
  height: 100%;
  width: 104px;
  padding-top: 50px;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &__list {
    overflow-y: auto;
    padding: 8px 0;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
  &__list-item {
    padding: 0 12px;
    &.close {
      .s-icon {
        transform: rotate(0);
        margin-top: -2px;
      }
    }
    &.divider {
      position: relative;
      padding-top: 24px;
      &::before {
        content: '';
        position: absolute;
        top: 12px;
        left: 50%;
        margin-left: -35px;
        width: 70px;
        // border-top: 1px solid #e8e8e8;
        height: 1px;
        transform: scaleY(0.5);
        background-color: #e8e8e8;
      }
    }
  }
  // &__list-item:nth-child(1n + 2) {
  //   position: relative;
  //   .layout-menu-item__title {
  //     margin-top: 20px;
  //     &::before {
  //       content: '';
  //       position: absolute;
  //       top: 15px;
  //       left: 50%;
  //       margin-left: -35px;
  //       width: 70px;
  //       // border-top: 1px solid #e8e8e8;
  //       height: 1px;
  //       transform: scaleY(0.5);
  //       background-color: #e8e8e8;
  //     }
  //   }
  // }
  .trans {
    transition: 0.3s height;
  }
  &__list-item,
  &__sublist-item {
    list-style: none;
    overflow: hidden;
  }
  &__link,
  &__title {
    display: block;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin: 4px 0;
    overflow: hidden;
  }
  &__link {
    text-align: left;
    padding: 0 12px;
    color: rgba(0, 0, 0, 0.75);
    transition: 0.5s all;
  }
  &__link:hover {
    color: #03ac54;
  }
  &__link.active {
    color: rgba(0, 0, 0, 0.85);
    background: #eceef0;
    border-radius: 4px;
  }
  &__title {
    color: #202020;
    cursor: pointer;
    text-align: left;
    .s-icon {
      transform: rotate(180deg);
      transition: 0.3s all;
      font-size: 10px;
      margin: -4px 3px 0 0;
    }
  }
  &__status {
    text-align: center;
    height: 50px;
    line-height: 50px;
    cursor: pointer;
    .s-icon {
      font-size: 13px;
      margin-right: 10px;
      &.scale {
        transform: scaleX(-1);
      }
    }
  }
}
</style>
