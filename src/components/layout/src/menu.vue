<template>
  <div
    class="layout-menu"
    :class="{ 'float-mode': subMenuMode, 'thin-mode': !menuItemData.length }"
    @mouseenter="clear"
    @mouseleave="showSubMenu(false)"
  >
    <ul class="layout-menu__list">
      <li
        class="layout-menu__item"
        :class="{
          'layout-menu__item--active': isActive(item.path, index),
        }"
        :key="item.code"
        @click="menuClick($event, item)"
        @mouseenter="showSubMenu(true, index)"
        v-for="(item, index) in showMenuData"
        :data-url="item.path"
      >
        <!--	注意, 此处对应用的图标单独做了处理,且选中图标也不是由后端传过来的,后期需要优化			-->
        <a :href="'#' + item.path" class="layout-menu__link">
          <template v-if="['101', '102', '105', '106'].includes(currentEdition)">
            <template v-if="item.name !== '应用' || isActive(item.path, index)">
              <s-icon
                v-if="item.name !== '团餐'"
                :style="{
                  color: isActive(item.path, index)
                    ? 'rgba(255, 255, 255, 0.95)'
                    : 'rgba(255, 255, 255, 0.85)',
                  transition: 'all .2s ease-in-out',
                }"
                :icon="
                  isActive(item.path, index) || hoverIndex === index
                    ? `${item.icon}_s`
                    : item.icon
                "
                :size="22"
              />
              <img
                v-else
                :style="{
                  color: isActive(item.path, index)
                    ? 'rgba(255, 255, 255, 0.95)'
                    : 'rgba(255, 255, 255, 0.85)',
                  transition: 'all .2s ease-in-out',
                }"
                style="width: 22px; display: block; margin: 0 auto"
                :src="
                  item.icon
                    ? require(
                        `./images/${item.icon.replace(/_n$/, '').replace(/_p$/, '')}_${isActive(item.path, index) || hoverIndex === index ? 'p' : 'n'}@2x.png`,
                      )
                    : ''
                "
                :size="22"
                :data-icon="item.icon"
              />
            </template>
            <img
              v-else
              style="width: 22px; opacity: 0.85; display: block; margin: 0 auto"
              src="@/assets/images/icon/ic_appcenter_n.png"
              alt=""
            />
          </template>
          <template v-else>
            <img
              v-if="item.name !== '应用' || isActive(item.path, index)"
              :style="{
                color: isActive(item.path, index)
                  ? 'rgba(255, 255, 255, 0.95)'
                  : 'rgba(255, 255, 255, 0.85)',
                transition: 'all .2s ease-in-out',
              }"
              style="width: 22px; display: block; margin: 0 auto"
              :src="
                item.icon
                  ? require(
                      `./../images/${item.icon.replace(/_n$/, '').replace(/_p$/, '')}_${isActive(item.path, index) || hoverIndex === index ? 'p' : 'n'}@2x.png`,
                    )
                  : ''
              "
              :size="22"
              :data-icon="item.icon"
            />
            <img
              v-else
              style="width: 22px; display: block; margin: 0 auto"
              src="./../images/<EMAIL>"
              alt=""
            />
          </template>
          <!--					<i class="iconfont icon-ic_home"></i>-->
          <p class="layout-menu__text">{{ item.name }}</p>
        </a>
      </li>
    </ul>
    <LayoutMenuItem
      class="layout-menu__submenu"
      :class="{
        'hide-menu': !menuItemData.length || (!isShowSubMenu && subMenuMode),
      }"
      :menuItemData="menuItemData"
      :curPath="curSubPath"
      :subMenuMode="subMenuMode"
      :showToggleBtn="showToggleBtn"
    />
  </div>
</template>

<script>
// @ is an alias to /src
import LayoutMenuItem from './menu-item';
import { SIcon } from '@sdp/ui';
import Bus from '@api/bus.js';
import { setMenuMeta } from '@/util/editionUtil';
import storage from '@util/storage';
import ConfigMixin from '@/mixins/config';
import tuancan from '@api/tuancan.js';
import { cloneDeep } from 'lodash-es'

export default {
  mixins: [ConfigMixin],
  name: 'layout-menu',
  components: { LayoutMenuItem, SIcon },
  props: {
    menuData: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      pathData: {},
      curPath: null,
      curSubPath: '',
      subMenuMode: +localStorage.getItem('subMenuMode') ? 1 : 0,
      isShowSubMenu: false,
      hoverIndex: null,
      timer: null,
      localMenuItem: null,
      // 为了避免多次快速点击菜单造成的卡顿
      debounceChangeRouter: this.debounce(
        (menu) => {
          console.timeLog('menuCLick');
          requestAnimationFrame(() => {
            if (menu.code === 'M009' && menu.path.includes('audit-list')) {
              let type = storage && storage.getLocalStorage('audit-list-type');
              if (type === 1) {
                menu.path = '/finance/user/audit-list-new';
              } else {
                menu.path = '/finance/user/audit-list';
              }
            }
            console.timeEnd('menuCLick');
            this.$router.push({ path: menu.path });
          });
        },
        400,
        {
          leading: true,
        },
      ),
      showToggleBtn: true,
      tcMenu: []
    };
  },
  beforeDestroy() {
    this.$root.$off('menuModeChange', this.modeChange);
    Bus.$off('update-local-submenu', this.$_onUpdateLocalSubmenu);
  },
  created() {
    this.$root.$on('menuModeChange', this.modeChange);
    Bus.$on('update-local-submenu', this.$_onUpdateLocalSubmenu);
    this.getTcMenuData()
  },
  watch: {
    '$route.path': {
      immediate: true,
      handler() {
        this.curPath = this.pathData[this.$route.path];
        //设置全局当前菜单信息
        this.setCurrentMenu();
        this.setActiveMenu();
      },
    },
    menuData: {
      deep: true,
      immediate: true,
      handler() {
        this.menuData.forEach((item) => {
          this.flatData(this.pathData, item.path, item.children, 'path');
          this.pathData[item.path] || (this.pathData[item.path] = item.path);
        });
        // 必须在此处 调用一遍，因为 pathData 更改，watch '$route.path',不会触发了，刷新页面可观察
        this.curPath = this.pathData[this.$route.path];
        //设置全局当前菜单信息
        this.setCurrentMenu();
      },
    },
    'sysConfig.tc_platform'() {
      this.getTcMenuData()
    }
  },
  computed: {
    // 获取子菜单数据
    menuItemData() {
      const curPath =
        this.curPath === undefined
          ? this.pathData[this.$route.path]
          : this.curPath;
      let activeMenu =
        this.subMenuMode && this.hoverIndex !== undefined
          ? this.showMenuData[this.hoverIndex]
          : this.menuData.find((item) => item.path === curPath) || this.tcMenu.find((item) => this.$route.path.indexOf(item.path) !== -1) ||
            this.localMenuItem;
      // 未激活时触发localMenuItem为[] 会触发计算属性 页面会有闪烁 为空时
      if (activeMenu && activeMenu.length != 0) {
        this.storage.setSessionStorage('activeMenu', activeMenu);
      } else {
        activeMenu = this.storage.getSessionStorage('activeMenu');
      }
      // 无二级菜单页面(应用)需要隐藏二级菜单
      // 特殊处理如下路径，含有二级菜单
      if (
        (curPath === void 0 &&
          this.$route.path.includes('appCenter') &&
          !this.$route.path.includes('certificate') &&
          !this.$route.path.includes('separateOrder') &&
          !this.$route.path.includes('costCalculation') &&
          !this.$route.path.includes('crm') &&
          !this.$route.path.includes('providerComment') &&
          !this.$route.path.includes('mall-inventory') &&
          !this.$route.path.includes('system') &&
          !this.$route.path.includes('order-goods-tag') &&
          !this.$route.path.includes('auto-purchase-order') &&
          !this.$route.path.includes(
            'wholesale-market-price-intelligent-pricing',
          ) &&
          !this.$route.path.includes('provider-splitting-bill') &&
          !this.$route.path.includes('quickLinks') &&
          !this.$route.path.includes('freshQuotation') &&
          !this.$route.path.includes('customerCommodityAlias') &&
          !this.$route.path.includes('temporary')) ||
        this.$route.path.includes('freight/template')
      ) {
        console.log('隐藏二级菜单', this.$route.path);
        this.storage.removeSessionStorage('activeMenu');
        activeMenu = [];
      }
      return activeMenu ? activeMenu.children || activeMenu : [];
    },
    showMenuData() {
      let menu = setMenuMeta(
        this.menuData.filter((item) => item.is_show === 1),
      );
      return this.setDividerActive(menu);
    },
  },
  methods: {
    // 有一些需要设置分割栏的标签因为配置关系，没有返回
    // 对于这种情况将分割栏移动到下一个组内的标签
    setDividerActive(menu) {
      menu.forEach((item) => {
        // 采购
        if (item.code === 'M003') {
          // 询价报价没有返回就将分割栏放到询价记录
          if (!item.children.find((sun) => sun.code === 'M003008')) {
            let nextSun = item.children.find((sun) => sun.code === 'M003010');
            nextSun && (nextSun.is_group_end = 1);
          }
        }
      });
      const menuList = this.deepClone(menu)
      if (this.tcMenu.length && !menuList.find(item => ['tuancan'].includes(item.code))) {
        let lastIndex = menuList.findLastIndex(item => ['A013','M016','M001','A000'].includes(item.code))
        menuList.splice(Math.max(0, lastIndex + 1), 0, ...this.tcMenu)
        return menuList;
      }
      return menuList;
    },
    // 团餐菜单
    async getTcMenuData() {
      if (this.sysConfig.tc_platform == 1) {
        try {
          const resp = await tuancan.getTenantUserResourceTree()
          this.tcMenu = resp.data.children[0].children.filter(item => item.key === 'tenant-manager:tuancan').map(item => {
            const children = item.children.map(ite => {
              const path = ite.key.replace('tenant-manager', '').replace(/:/g, '/')
              return {
                name: ite.title,
                code: ite.key,
                is_show: 1,
                path,
              }
            })
            return {
              name: item.title,
              code: item.key,
              icon: 'ic_groupmeal_n',
              is_show: 1,
              path: children[0].path,
              children
            }
          });
        } catch(e) {}
      }
    },
    $_onUpdateLocalSubmenu(data) {
      console.log('更新本地菜单', data);
      if (data && data.length > 0) {
        this.storage.setSessionStorage('activeMenu', data);
        this.localMenuItem = data.map((item) => {
          item.path = item.url;
          return item;
        });
      }
    },
    // 打平数据的path 通过path确定激活的菜单
    addPathList(o, key, list) {
      list &&
        list.forEach((item) => {
          // if (item.indexOf('superAdmin') === -1 && item.startsWith('/')) {
          o[item] = key;
          // }
        });
    },
    flatData(o, p, c, key) {
      c.forEach((item) => {
        item[key] && (o[item[key]] = p);
        this.addPathList(o, p, item.path_list);
        if (item.children && item.children.length) {
          this.flatData(o, p, item.children, key);
        }
      });
    },
    // 是否当前菜单激活
    isActive(path, index) {
      return this.subMenuMode && this.hoverIndex !== undefined
        ? index === this.hoverIndex
        : this.pathData[this.$route.path] === path;
    },
    modeChange(mode) {
      this.subMenuMode = mode;
      this.hoverIndex = undefined;
      this.isShowSubMenu = false;
      localStorage.setItem('subMenuMode', mode);
    },
    clear() {
      clearTimeout(this.timer);
    },
    showSubMenu(isShow, index) {
      if (isShow) {
        this.isShowSubMenu = isShow;
        this.hoverIndex = index;
      } else {
        this.timer = setTimeout(() => {
          this.isShowSubMenu = isShow;
          this.hoverIndex = index;
          this.clear();
        }, 150);
      }
    },
    setCurrentMenu() {
      let path = this.curPath;
      if (!path) return;
      let currentMenu = {};
      this.showMenuData.forEach((menu) => {
        if (menu.path === path) {
          currentMenu = menu;
        }
      });
      if (currentMenu.name) {
        this.$store.commit('SET_CURRENT_MENU', { ...currentMenu });
      }
      if (Object.keys(currentMenu).length === 0) {
        // 隐藏收起按钮
        this.showToggleBtn = false;
        // 同时展开侧边栏
        this.subMenuMode = 0;
      } else {
        // 显示收起按钮
        this.showToggleBtn = true;
        // 是否展开侧边栏
        this.subMenuMode = +localStorage.getItem('subMenuMode') ? 1 : 0;
      }
    },
    /**
     * 路由跳转设置激活菜单
     */
    setActiveMenu() {
      const path = this.curPath;
      const targetLi = document.querySelector(`[data-url="${path}"]`);
      if (targetLi) {
        this.clearMenuActive();
        targetLi.classList.add('layout-menu__item--active');
      }
    },
    /**
     * 菜单点击设置激活菜单(获取当前点击的li，直接设置样式，路由异步跳转，减少对菜单的渲染影响)
     */
    menuClick($event, menu) {
      console.time('menuCLick');
      if (menu.children && menu.children.length > 0) {
        this.curSubPath = menu.children[0].path;
      }
      const targetLi = this.getLi($event.target);
      const isActive = targetLi.classList.contains('layout-menu__item--active');

      if (!isActive) {
        this.clearMenuActive();
        targetLi.classList.add('layout-menu__item--active');
        // this.curPath = menu.path;
        this.debounceChangeRouter(menu);
      }
    },

    getLi(el) {
      if (el.localName.toLocaleLowerCase() === 'li') {
        return el;
      } else if (el.parentNode) {
        return this.getLi(el.parentNode);
      }
    },
    /**
     * 移除其它激活态的菜单样式
     */
    clearMenuActive() {
      Array.from(
        document.querySelectorAll('.layout-menu__item--active'),
      ).forEach((activeItem) => {
        activeItem.className = 'layout-menu__item';
      });
    },
  },
};
</script>

<style lang="less">
// 基础版本特殊样式
.basic-container {
  .layout-menu {
    &__item {
      font-size: 13px;
      &--active .layout-menu__link {
        color: #fff;
      }
      &:hover {
        background: rgba(18, 22, 28, 0.5);
        transition: background 0.2s ease-in-out;
      }
    }
    &__list {
      background: #2f3341;
      .layout-menu__item--active {
        background: var(--primary-color);
      }
    }
    &__link {
      color: rgba(255, 255, 255, 0.7);
      transition: color 0.2s ease-in-out;
      &:hover {
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }
}
.layout-menu {
  font-size: 14px;
  user-select: none;
  width: 170px;
  float: left;
  height: 1px;
  &.thin-mode {
    width: 66px;
    .layout-menu__submenu {
      display: none;
    }
  }
  &__submenu {
    position: fixed;
    top: 0;
    left: 66px;
    z-index: 300;
  }
  &__text {
    margin-top: 4px;
    line-height: 1;
  }
  &.float-mode {
    width: 66px;
    .layout-menu__submenu {
      z-index: 300;
      box-shadow: 2px 0 10px 0 #eee;
      transition: 0.5s left;
      &.hide-menu {
        left: -40px;
        display: flex;
      }
    }
  }
  &__list {
    height: 100%;
    width: 66px;
    text-align: center;
    position: fixed;
    z-index: 303;
    background: #1f242d;
    overflow-y: auto;
    &::-webkit-scrollbar {
      width: 0;
    }
  }
  &__item {
    margin: 2px 4px;
    border-radius: 10px;
    &:hover {
      background: #303845;
    }
  }
  & &__item {
    transition: none;
  }
  &__item--active {
    background: #03ac54;
    &:hover {
      background: #03ac54;
    }
  }
  &__link {
    display: block;
    box-sizing: border-box;
    padding-top: 7px;
    height: 56px;
    color: #fff;
    .iconfont,
    &:hover {
      color: #fff;
    }
    .s-icon {
      line-height: 1;
    }
  }
}
</style>
