<template>
  <div class="base-upload">
    <div class="base-upload__item demo-upload-list" :key="index" v-for="(item, index) in imgs">
      <div class="base-upload__content">
        <div class="img"><img :src="item" /></div>
      </div>
      <div class="base-upload__mask">
        <s-icon
          class="base-upload__close-icon"
          icon="solid-close"
          :size="16"
          v-if="!disabled"
          @click="del(index)"
        />
        <s-icon
          class="base-upload__view-icon"
          icon="img-view"
          :size="22"
          @click="view(item, index)"
        />
      </div>
    </div>
    <Upload
      :multiple="multiple"
      ref="upload"
      :data="params"
      :show-upload-list="false"
      :before-upload="preBeforeUpload"
      :on-success="onSuccess"
      :action="action"
      :accept="accept"
      :format="format"
      :on-format-error="onFormatError"
      :on-error="onUploadError"
      :max-size="maxSize"
      :on-exceeded-size="handleExceedeMaxSize"
      :on-progress="onProgress"
      v-bind="$attrs"
    >
      <div class="change-upload-type" v-if="!disabled && imgs.length < max">
        <div class="base-upload__content">
          <s-icon icon="add" class="load_img" :size="20" />
          <p class="load_text">上传</p>
          <div class="loading" v-if="uploading">上传中...</div>
        </div>
      </div>
    </Upload>
  </div>
</template>

<script>
import { Notice } from 'view-design';
export default {
  autoRegister: true,
  props: {
    value: {
      type: Array,
      default() {
        return [];
      }
    },
    action: {
      type: String,
      default: ''
    },
    max: {
      type: Number,
      default: 5
    },
    maxSize: {
      type: Number,
      default: 10240
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png'
    },
    format: {
      type: Array,
      default: () => {
        return ['jpg','jpeg','png'];
      }
    },
    params: {
      type: Object,
      default: () => {
        return {};
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持多选
    multiple:{
      type: Boolean,
      default: true
    },
    beforeUpload: {
      type: Function,
      default: () => {
        return true;
      }
    },
    onFormatImgError: {
      type: Function,
      default: (file) => {
        Notice.error({
          title: '图片上传失败',
          desc: '仅支持jpg，jpeg，png格式'
        });
      }
    },
    onError: {
      type: Function,
      default: (file) => {
        Notice.error({
          title: '图片上传失败',
          desc: '请重试'
        });
      }
    },
    onExceededSize: {
      type: Function,
      default: undefined
    },
  },
  data() {
    return {
      imgs: [],
      uploading: false,
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.imgs = val;
      }
    }
  },
  methods: {
    del(index) {
      this.imgs.splice(index, 1);
      this.$emit('input', this.imgs);
    },
    preBeforeUpload(files) {
      if (this.uploading) return false;
      if (!this.beforeUpload(files)) {
        return false;
      }
      console.log(this.max, this.imgs.length);
      if (this.max && this.imgs.length >= this.max) {
        this.errorNotice({
          title: `上传数量不能超过${this.max}张`,
        })
        return false;
      }
      this.uploading = true;
      return true;
    },
    onSuccess(res) {
      this.uploading = false;
      if (res.status) {
        this.imgs.push(res.data.url);
        this.$emit('input', this.imgs);
        this.$smessage({ type: 'success', text: '上传成功' });
      } else {
        this.errorNotice({
          title: res.message,
        });
      }
      // console.log(response, file, fileList);
    },
    onUploadError(...args) {
      this.uploading = false;
      this.onError(...args);
    },
    onFormatError(...args) {
      this.uploading = false;
      this.onFormatImgError(...args);
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
    },
    handleExceedeMaxSize(file, fileList) {
      this.uploading = false;
      if (this.onExceededSize && typeof this.onExceededSize === 'function') {
        this.onExceededSize(file, fileList)
        return;
      }
      const msg = `文件${file.name}太大, 超过${this.maxSize / 1024}M限制.`;
      this.errorNotice({
        title: '上传失败',
        desc: msg
      });
      this.$emit('on-exceeded-size', msg);
    },
    onProgress(event, file, fileList) {
      this.$emit('on-progress', event, file, fileList, this.meta);
    },
    view(img, _viewIndex) {
      const images = this.imgs.map(item => item);
      this.viewImage(images, _viewIndex)
    }
  }
};
</script>
<style lang="less" scoped>
@upload-prefix: base-upload;

.@{upload-prefix} {
  .ivu-upload-select {
    vertical-align: top;
  }
  &__item {
    width: 90px;
    height: 90px;
    padding: 2px;
    border: 1px solid rgba(216, 216, 216, 0.8);
    float: left;
    margin: 0 22px 10px 0;
    position: relative;
    &:hover {
      .@{upload-prefix}__mask {
                         visibility: visible;
                         opacity: 1;
                       }
    }
  }
  &__mask {
    position: absolute;
    left: -1px;
    right: -1px;
    top: 0;
    bottom: -2px;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
    visibility: hidden;
    opacity: 0;
    transition: 0.3s all;
    .@{upload-prefix}__close-icon {
                       color: #b2b2b2;
                       background: #fff;
                       position: absolute;
                       right: -8px;
                       top: -8px;
                       border-radius: 50%;
                       line-height: 1;
                       cursor: pointer;
                     }
    .@{upload-prefix}__view-icon {
                       position: absolute;
                       bottom: 20px;
                       right: 12px;
                       color: #fff;
                       cursor: pointer;
                     }
  }

  &__content {
    background: #f6f8f9;
    height: 100%;
    text-align: center;
    color: rgba(0, 0, 0, 0.5);
    line-height: 30px;
    font-size: 14px;
    cursor: pointer;
    .img {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      img {
        max-width: 100%;
        max-height: 100%;
      }
    }
  }
  &__info {
    margin-top: 10px;
  }
  &__explain {
    color: rgba(0, 0, 0, 0.4);
    font-size: 12px;
    line-height: 14px;
    margin-bottom: 2px;
  }
  &__error {
    color: #f13130;
    font-size: 12px;
  }
}

.change-upload-type {
  position: relative;
  display: inline-block;
  width: 90px;
  height: 90px;
  padding: 2px;
  text-align: center;
  border: 1px solid rgba(216,216,216,0.8);
  border-radius: 1px;
  cursor: pointer;
  &:hover {
    border: 1px dashed #03ac54;
  }
}
.load_box{
  position: relative;
  height: 100%;
  width: 100%;
  background: #F6F8F9;;
}
.load_img{
  position: absolute;
  top: calc(50% - 10px);
  left: 50%;
  transform: translate(-50%,-50%);
}
.load_text{
  position: absolute;
  top: calc(50% + 20px);
  left: 50%;
  transform: translate(-50%,-50%);
  color: rgba(0,0,0,0.5);
  font-size: 14px;
}
.loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding-top: 30%;
  text-align: center;
  color: white;
  font-size: 13px;
  background: rgba(0,0,0,0.5);
  z-index: 99;
}
.ivu-upload-drag {
  height: 90px;
  border-radius: 2px;
  border: none;
}

</style>
