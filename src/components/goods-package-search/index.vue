<template>
  <div class="wrap goods-package-search" :class="{ icon: icon }">
    <RemoteSelect
      ref="goodsInput"
      clearable
      filterable
      remote
      v-model="goodsId"
      :transfer="transfer"
      :placeholder="placeholder"
      :remote-method="remoteSearch"
      :loading="selectLoading"
      @on-query-change="onQueryChange"
      @on-change="updateValue"
    >
      <Option
        v-for="(option, index) in remoteList"
        :value="option[valueKey]"
        :label="option[labelKey]"
        :key="index"
      >{{option[labelKey]}}</Option>
    </RemoteSelect>
    <Icon
      :type="icon"
      v-if="icon"
      class="icon"
      @click.native="handleClick"
    ></Icon>
    <slot></slot>
  </div>
</template>

<script>
const GOODS_KEY = "id";
export default {
  name: "GoodsPackageSearch",
  props: {
    value: {
      default: 0
    },
    transfer: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: "输入套餐名称"
    },
    icon: {
      type: String,
      default: ""
    },
    labelKey: {
      type: String,
      default: "name"
    },
    valueKey: {
      type: String,
      default: "id"
    },
    filters: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: "" // 搜索api地址
    }
  },
  data() {
    return {
      defaultApi: "",
      goodsId: "",
      remoteList: [],
      lastChange: "",
      selectLoading: false
    };
  },
  methods: {
    onQueryChange() {
      this.$emit("on-query-change");
    },
    remoteSearch(query) {
      query = query.trim();
      if (query !== "") {
        if (this.selectLoading) {
          this.lastChange = query;
          return false;
        }
        this.selectLoading = true;
        let api = this.api || this.apiUrl.goodsPackage.list;
        this.$request.get(api, { name: query, ...this.filters }).then(res => {
          let { status, data } = res;
          this.selectLoading = false;
          if (status) {
            this.remoteList = data.list;
          } else {
            this.remoteList = [];
          }
          if (this.lastChange) {
            this.remoteSearch(this.lastChange);
            this.lastChange = "";
          }
        });
      } else {
        this.goodsId = "";
        this.remoteList = [];
      }
    },
    setQuery(query) {
      if (!query) {
        this.$refs.goodsInput.clearSingleSelect();
      } else {
        this.$refs.goodsInput.setQuery(query);
      }
    },
    updateValue() {
      let selectedGoods = this.remoteList.find(
        findGoods => findGoods[GOODS_KEY] === this.goodsId
      );
      this.$emit("input", this.goodsId);
      this.$emit("on-change", selectedGoods);
    },
    handleClick() {
      this.$emit("on-click");
    }
  }
};
</script>

<style lang="less">
.goods-search {
  &.icon {
    .ivu-icon:not(.icon) {
      right: 30px !important;
    }
  }
}
</style>
<style lang="less" scoped>
.wrap {
  display: inline-block;
  width: 100%;
  position: relative;
  .icon {
    position: absolute;
    font-size: 20px;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    cursor: pointer;
  }
}
</style>
