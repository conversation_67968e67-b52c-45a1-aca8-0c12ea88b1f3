<!--
 * @Date: 2022-09-02 10:08:38
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-18 16:05:28
 * @FilePath: /sdpbase-pro/src/components/list-filter/src/filter-col.vue
-->
<template>
  <div
    class="filter__col"
    :class="{
      'tag-top-start': tagTopStart,
    }"
    :style="{ width: block ? '100%' : 'inherit' }"
  >
    <div align="right" :style="labelStyle" v-if="label">
      {{ autoWrapLabel
      }}<Tooltip
        :transfer="true"
        v-bind="filterItem.tooltip"
        v-if="filterItem.tooltip"
      >
        <Icon class="filter__tips" type="ios-alert-outline" /> </Tooltip
      >：
    </div>
    <slot></slot>
    <Tooltip
      :transfer="true"
      v-bind="filterItem.tooltip"
      v-if="filterItem.tooltip && filterItem.tooltip.afterItem"
    >
      <Icon class="filter__tips" type="ios-alert-outline" />
    </Tooltip>
  </div>
</template>

<script>
import { Icon } from 'view-design';
import Tooltip from '../../base-tooltip';
export default {
  name: 'BaseFilterCol',
  components: {
    Icon,
    Tooltip,
  },
  props: {
    // 标签类筛选是否顶部对齐而非垂直居中对齐
    tagTopStart: {
      type: Boolean,
      default: false,
    },
    block: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
    labelWidth: {
      type: [String, Number],
      default: '',
    },
    filterItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {};
  },
  computed: {
    labelStyle() {
      const style = {};
      if (this.labelWidth) {
        style.width = `${this.labelWidth}px`;
      } else {
        style.width = '65px';
      }
      style.marginRight = '2px';
      style.color = 'rgba(0,0,0, 0.85)';
      style.lineHeight = '15px';
      // if (this.label && this.label.length > 4) {
      //   style.marginTop = '1px';
      // }

      return style;
    },
    autoWrapLabel() {
      // 给 label 加入空格，以达到4个字自动换行的效果
      if (!this.label) {
        return '';
      }
      let newLabel = this.label;
      if (this.label.length > 4) {
        newLabel =
          this.label.substr(0, 4) +
          ' ' +
          this.label.substr(4, this.label.length);
      }
      return newLabel;
    },
  },
  created() {},
  methods: {},
};
</script>

<style lang="less">
.tag-top-start {
  align-items: flex-start !important;
  .ivu-checkbox-group {
    margin-top: -3px;
    .ivu-checkbox-group-item {
      line-height: unset;
    }
  }
}
</style>
