import RangeInput from '@components/order-price-filter';
import GroupFilter from '@components/common/GroupFilter/index.js';
export default {
  name: 'FilterRender',
  components: {
    RangeInput,
    GroupFilter,
  },
  // functional: true,
  props: {
    config: {
      type: Object,
    },
    value: {
      type: [String, Array, Number, Date],
    },
    categoryData: {
      type: [Array],
      default: () => [],
    },
    // on-change事件是否延迟到mounted之后执行
    lazy: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    this.firstOnChange = true;
  },
  inject: ['smartOnChange'],
  methods: {
    resetValue() {
      let refId = this.getRefId(this.config);
      if (this.$refs[refId] && this.$refs[refId].resetValue) {
        this.$refs[refId].resetValue(this.config);
      }
    },
    getValue() {
      let refId = this.getRefId(this.config);
      if (this.$refs[refId] && this.$refs[refId].getValue) {
        return this.$refs[refId].getValue();
      }
    },
    getDefaultValue() {
      let { component, defaultValue, key: filterKey } = this.config;
      let refId = this.getRefId(this.config);
      let filterData = {};
      defaultValue = defaultValue === undefined ? '' : defaultValue;
      // 自定义组件如果暴露了getDefaultValue方法，优先从自定义组件暴露的方法中获取默认值
      if (component && this.$refs[refId] && this.$refs[refId].getDefaultValue) {
        defaultValue = this.$refs[refId].getDefaultValue();
      }
      if (Array.isArray(filterKey)) {
        filterKey.forEach((keyItem, index) => {
          filterData[keyItem] =
            defaultValue && defaultValue[index] !== undefined
              ? defaultValue[index]
              : '';
        });
      } else {
        filterData[filterKey] = defaultValue;
      }
      return {
        defaultValue,
        filterData,
      };
    },
    getRefId(filterConfig) {
      return `filter_${
        Array.isArray(filterConfig.key)
          ? filterConfig.key.join('_')
          : filterConfig.key
      }`;
    },
  },
  render(h) {
    let component = '';
    let childComponent = [];
    const config = this.config;
    let { attrs = {}, style = {}, props = {}, block = false } = config;
    let data = config.data || [];
    const handleChange = ({ value, isDefault, triggerFilter = true, stop }) => {
      // 首次触发不触发on-change事件的处理逻辑
      // if (this.smartOnChange) {
      //   this.firstOnChange = false;
      //   if (JSON.stringify(value) === JSON.stringify(this.value)) {
      //     return;
      //   }
      // }
      const filterData = {};
      const dataKey = config.key;
      if (Array.isArray(dataKey)) {
        dataKey.forEach((keyItem, index) => {
          filterData[keyItem] = value[index];
        });
      } else {
        filterData[dataKey] = value;
      }
      if (stop) {
        triggerFilter = false;
      }
      this.$emit('on-change', {
        value,
        filterData,
        isDefault,
        triggerFilter,
        filterItem: config,
      });
    };
    let listeners = {
      'on-change': (value) => {
        // 多选情况下, 塞了默认值依然会触发on-change, iview设计如此, 避免多次发起请求, 加个判断
        if (this.smartOnChange) {
          this.firstOnChange = false;
          if (JSON.stringify(value) === JSON.stringify(this.value)) {
            return;
          }
        }
        if (
          (config.props && config.props.multiple) ||
          (config.attrs && config.attrs.multiple)
        ) {
          value = value && Array.isArray(value) && value.join(',');
        }
        handleChange({ value: value });
      },
    };
    switch (config.type) {
      case 'Input':
        component = config.type;
        listeners = {
          'on-enter': (evt) => {
            handleChange({ value: evt.target.value.trim() });
          },
          'on-change': (evt) => {
            handleChange({
              value: evt.target.value.trim(),
              triggerFilter: false,
            });
          },
        };
        break;
      case 'Select': {
        component = config.type;
        childComponent = data.map((dataItem) =>
          h('Option', {
            props: dataItem,
          }),
        );
        break;
      }
      case 'RangeInput': {
        component = config.type;
        listeners = {
          'on-change': (value) => {
            handleChange({ value: value, stop: true });
          },
        };
      }
      case 'GroupFilter': {
        component = config.type;
        let stop = false;
        if (
          !config.props ||
          !config.props.type ||
          config.props.type === 'input'
        ) {
          stop = true;
        }
        listeners = {
          'on-change': (value) => {
            handleChange({ value: value, stop: stop });
          },
          'on-enter': (value) => {
            handleChange({ value: value });
          },
        };
      }
      case 'RadioGroup':
        component = config.type;
        childComponent = data.map((dataItem) =>
          h('Radio', {
            props: dataItem,
          }),
        );
        break;
      case 'CheckboxGroup':
        component = config.type;
        childComponent = data.map((dataItem) =>
          h('Checkbox', {
            props: dataItem,
          }),
        );
        break;
      case 'custom':
        if (config.props && config.props.on) {
          Object.keys(config.props.on).forEach((key) => {
            listeners[key] = config.props.on[key];
          });
        }
        component = config.component;
        if (component.name == 'SelectAndInput') {
          listeners['on-enter'] = (value) => {
            handleChange({ value: value });
          };
        }
        break;
      default:
        component = config.type;
    }

    if (config.onChange && typeof config.onChange === 'function') {
      listeners['on-change'] = (...args) => {
        let changeData = config.onChange(...args);
        if (changeData === false || changeData.stop) {
          // 如果返回自定义的onchange 事件返回 false，则静默处理
          handleChange({
            value: changeData.value,
            isDefault: changeData.isDefault,
            stop: true,
          });
          return;
        }
        if (changeData) {
          handleChange({
            value: changeData.value,
            isDefault: changeData.isDefault,
          });
        }
      };
    }
    listeners.input = (...args) => {
      this.$emit('input', ...args);
    };
    if (component !== 'CheckboxGroup') {
      props.value = this.value;
      props.currentModel = this.value;
    }
    props.defaultValue = config.defaultValue;
    let refIdd = this.getRefId(config);
    return h(
      component,
      {
        class: config['class'],
        style: block ? { ...style, width: 0, flex: 1 } : style,
        attrs: attrs,
        props: props,
        on: listeners,
        ref: refIdd,
        key: refIdd,
      },
      childComponent,
    );
  },
};
