<template>
  <div class="base-filter-row">
    <Row :align="align" type="flex">
      <slot></slot>
    </Row>
  </div>
</template>

<script>
export default {
  name: 'BaseFilterRow',
  components: {},
  props: {
    label: {
      type: String,
      default: ''
    },
    labelWidth: {
      type: [Number, String],
      default: 70
    },
    align: {
      type: String,
      default: 'top' 
    }
  },
  data() {
    return {};
  },
  computed: {
    rowLabelWidth() {
      return this.label ? this.labelWidth : 0;
    }
  },
  created() {},
  methods: {}
};
</script>

<style lang="less" scoped>
.base-filter-row {
  margin-bottom: 0;
}
</style>
