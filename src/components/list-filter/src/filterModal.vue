<template>
  <div id="form-modal">
    <Modal
      :width="width"
      :class-name="currentClassName"
      v-model="show"
      :title="title"
    >
      <div class="filter_sub_title">
        <div
          class="filter_sub_title__l"
          :style="{
            'flex-basis': showConfigCopy ? '576px' : '1'
          }"
        >
          可选条件
        </div>
        <div
          v-if="showConfigCopy"
          class="filter_sub_title__r"
        >
          取值规则
        </div>
      </div>
      <div :style="{ maxHeight: maxHeight, height:'310px', overflow: showConfigCopy ? 'hidden' :'auto'}">
        <div
          class="filter-content"
          :style="{display: showConfigCopy ? 'flex' : 'block'}"
        >
          <draggable
            :style="{
              'flex-basis': showConfigCopy ? '600px' : '1',
              'overflow': 'auto',
            }"
            class="draggable_box"
            v-model="selfFilterData"
            handle=".draggable_item"
            animation="300"
            filter=".holdings"
            @end="onEnd"
            :move="onMove"
            @start="onChoose"
          >
            <template v-for="(item, index) in selfFilterData" >
              <div
                v-if="!(item.hideId)"
                :class="[isFixed(item) ? 'draggable_item' : 'holdings']"
                :style="{
                  width: showConfigCopy ? '33.33%' : '25%'
                }"
                :key="item.label + item.type + index"
                @click="clickCheck($event,item)"
              >
                <i class="sui-icon icon-sort filter_icon_sort"></i>
                <Checkbox
                  v-model="item.checked"
                  :disabled="item.required"
                  @input="titleChange(item, selfFilterData)"
                >
                </Checkbox>
                    <Tooltip placement="top">
                    <span
                      v-show="item.relation"
                      class="icon-lian filter_icon_realtion"
                    ></span>
                    <div slot="content">
                      <p>勾选/取消勾选该条件，其相关</p>
                      <p>条件会自动被勾选/取消勾选。</p>
                    </div>
                  </Tooltip>
                      <span class="text" v-if="item.name">
                    {{ item.name }}
                  </span>
                  <span class="text" v-else-if="item.props && item.props.selectData">{{
                    item.props.selectData[0].label
                  }}</span>
                  <span
                    class="text"
                    v-else-if="!item.label && item.props && item.props.data"
                    >{{ item.props.data[0].label }}</span
                  >
                  <span class="text" v-else> {{ item.label }}</span>
              </div>
            </template>
          </draggable>
          <div class="filter_config_box" v-if="showConfigCopy">
            <div v-for="(config, index) in filterConfigCopy" :key="config.key" class="filter_config">
              <p class="mb10">
                {{config.label}}
                <Tooltip v-if="config.tips" max-width="300" :content="config.tips" placement="top" >
                  <Icon :size="13" icon="tishifu"/>
                </Tooltip>
              </p>
              <RenderFilterConfig
                :config="config"
                @on-change="filterConfigOnChange($event, index)"
              />
              
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <Button @click="handleCancel()">{{ cancelText }}</Button>
        <Button type="primary" @click="checkSave('form')">{{ okText }}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import common from '@api/main.js';
import draggable from 'vuedraggable';
import Icon from '@components/icon';
import RenderFilterConfig from './components/RenderFilterConfig'
export default {
  name: 'filterModal',
  components: {
    draggable,
    Icon,
    RenderFilterConfig
  },
  props: {
    tableId: {
      type: [String, Number],
      default: '',
    },
    showCancelBtn: {
      type: Boolean,
      default: false,
    },
    filterData: {
      type: [Array],
      default: () => {
        return [];
      },
    },
    width: {
      type: [String, Number],
      default: 750,
    },
    title: {
      type: String,
      default: '自定义筛选条件',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okText: {
      type: String,
      default: '确定',
    },
    labelWidth: {
      type: [Number, String],
      default: 80,
    },
    height: {
      type: Number,
      default: 0,
    },
    value: {
      type: Boolean,
      default: false,
    },
    showConfig: {
      type: Boolean,
      default: false
    },
    filterConfig: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      arr:[],
      temporaryData:[],
      selfFilterData: [],
      maxHeight: '300px',
      show: false,
      selfFormData: {},
      currentClassName: ' vertical-center-modal body-no-padding ',
      filterConfigCopy: [],
      filterConfigCopyTemporary: [],
      showConfigCopy: false,
    };
  },
  watch: {
    filterData: {
      handler(n) {
        this.selfFilterData = this.deepClone(n).filter(e=>e.show!==false);
        this.temporaryData = this.deepClone(this.selfFilterData)
      },
      deep: true,
    },
    filterConfig: {
      handler(filterConfig) {
        this.filterConfigCopy = this.deepClone(filterConfig)
      },
      deep: true,
    },
    showConfig: {
      handler(value) {
        this.showConfigCopy = value
      },
      deep: true,
    },
    value(newValue) {
      this.show = newValue;
    },
    show(newValue) {
      this.$emit('input', newValue);
    },
  },
  created() {
    this.show = this.value;
  },
  mounted() {
    let data = this.deepClone(this.filterData)
    data = data.filter(e=>e.show!==false)
    data.forEach(item=>{
      item.checked =  item.required || item.checked
    })
    this.selfFilterData = data
    this.temporaryData = this.deepClone(this.selfFilterData)
    this.filterConfigCopy = this.filterConfig
    this.filterConfigCopyTemporary = this.deepClone(this.filterConfigCopy)
    this.showConfigCopy = this.showConfig
    this.maxHeight = this.getTableHeight() - 100 + 'px';
    this.triggerCustomFilter();
  },
  methods: {
    onEnd(e,originalEvent){
      let nowItem = this.selfFilterData.filter(r=>!r.hideId)[e.newIndex]
      if(nowItem.hideSource){
        this.arr = []
        this.selfFilterData.forEach((res,index)=>{
          // 假如操作源的hideSoure与需要展示的hideId相对应 一起操作修改
          if(res.hideId==nowItem.hideSource){
              this.arr.push(res)
          }
        })
        this.arr.forEach(item=>{
          let i = this.selfFilterData.findIndex(all=>all.key.toString()==item.key.toString())
            this.selfFilterData.splice(i, 1)
        })
        // let newIndex = this.selfFilterData.findIndex(e=>e.key.toString()==nowItem.key.toString())
        // this.selfFilterData.splice(newIndex, 0, ...this.arr)
      }else{
        this.arr = []
      }
      if(nowItem.hideSource){
        let newIndex = this.selfFilterData.findIndex(e=>e.key.toString()==nowItem.key.toString())
        this.selfFilterData.splice(newIndex+1, 0, ...this.arr)
      }
    },
    clickCheck(e,item){
      if(item.required){
        return
      }
      item.checked = !item.checked
      if (item.relation) {
        this.selfFilterData.forEach((res) => {
          if (res.relation == item.relation) {
            res.checked = item.checked;
          }
        });
      }
    },
    isFixed(e) {
      let noDrag = ['check_box_group', 'radio_group','CategorySelectForMultiple', 'userTag', 'goodsTag', 'orderTag'];
      if (e.component && noDrag.includes(e.component.name)) return false;
      return true;
    },
    onChoose(e){
 
    },
    onMove(e, originalEvent) {
      //不允许拖拽
      let noDrag = ['check_box_group', 'radio_group','CategorySelectForMultiple', 'userTag', 'goodsTag', 'orderTag'];
      if (
        e.relatedContext.element.component &&
        noDrag.includes(e.relatedContext.element.component.name)
      )
        return false;
      return true;
    },
    titleChange(item, data) {
      // 点击选中关联
      if (item.relation) {
        data.forEach((res) => {
          if (res.relation == item.relation) {
            res.checked = item.checked;
          }
        });
      }
    },
    handleCancel() {
      this.show = false;
      this.$emit('saveTemporary',this.temporaryData)
      this.$emit('saveTemporaryConfig','cancel',this.filterConfigCopyTemporary)
    },
    filterConfigOnChange({
      value
    }, index) {
      this.filterConfigCopy[index].value = value
    },
    triggerCustomFilter() {
      let data = this.selfFilterData
        .filter((e) => e.required || e.checked)
      this.$emit('customFilter', data);
    },
    checkSave() {
      let localdata = {
        filter_array: this.deepClone(this.selfFilterData),
        filter_config: this.deepClone(this.filterConfigCopy)
      };
      localdata.filter_array.forEach((e) => {
        delete e.component;
        delete e.props;
        delete e.style;
        delete e.data;
      });
      localdata.filter_config.forEach((e) => {
        delete e.component;
        delete e.props;
        delete e.style;
        delete e.tips;
      })
      localStorage.setItem(this.tableId, JSON.stringify(localdata.filter_array));
      localStorage.setItem(this.tableId+ '_filter_config', JSON.stringify(localdata.filter_config));
      common.saveSearchConfig({
        key: this.tableId,
        value: JSON.stringify(localdata),
      });
      this.$emit('saveTemporary',this.selfFilterData)
      this.triggerCustomFilter();
      this.$emit('saveTemporaryConfig', 'confirm', this.filterConfigCopy)
      this.show = false;
    },
  },
};
</script>
<style lang="less">
#form-modal {
}
.body-no-padding {
  .ivu-modal-body {
    padding: 0;
    overflow: hidden !important;
  }
  .modal-form {
    padding: 16px;
  }
}
</style>
<style lang="less" scoped>
.extra {
  color: #999;
  line-height: 18px;
  margin-top: 15px;
}
.filter_sub_title {
  padding-left: 24px;
  background: rgba(246, 248, 249, 0.5);
  height: 36px;
  line-height: 36px;
  border-bottom: 0.5px solid #e8e8e8;
  font-weight: bold;
  display: flex;

  &__l {
  }
  &__r {
    border-left: 0.5px solid #e8e8e8;
    padding: 0 14px;
  }
}
/deep/.ivu-checkbox-inner{
  height: 15px;
  width: 15px;
}
.filter-content {
  height: 310px;
}
.draggable_box {
  display: flex;
  flex-wrap: wrap;
  padding:14px 24px 24px 24px;
  .filter_icon_sort {
    color: #909090;
    padding-right: 12px;
    font-size: 12px;
    // width: 8px;
    // height: 12px;
    // display: inline-block;
  }
  .filter_icon_realtion {
    font-family: _iconfont;
    font-size: 16px;
    color: var(--primary-color);
    padding-left: 8px;
  }
  .holdings {
    width: 25%;
    padding: 2px 0; 
    display: flex;
    border-radius: 2px;
    align-items: center;
    cursor: no-drop;

    /deep/.ivu-checkbox-wrapper {
      display: flex;
      align-items: center;
      padding-top: 2px;
       margin-left: 2px;
      margin-right: 2px;
    }
    .text {
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 8px;
      max-width: 110px;
      overflow: hidden;
    }
  }
  .draggable_item {
    width: 25%;
    padding: 2px 0; 
    display: flex;
    border-radius: 2px;
    align-items: center;
    cursor: move;
    &:hover {
      background: var(--primary-tr-hover);
    }
    /deep/.ivu-checkbox-wrapper {
      display: flex;
      align-items: center;
      margin-left: 2px;
      margin-right: 2px;
      padding-top: 2px;
    }
    .text {
      display: inline-block;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-left: 8px;
      max-width: 110px;
      overflow: hidden;
    }
  }
}
.filter_config_box {
  padding: 14px; 
  overflow: auto;
  border-left: 0.5px solid #e8e8e8;
  .filter_config {
    margin-bottom: 14px;
  }
}
</style>
