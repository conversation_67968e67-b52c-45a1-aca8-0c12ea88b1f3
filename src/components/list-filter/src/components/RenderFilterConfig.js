export default {
  name: 'RenderFilterConfig',
  components: {
  },
  props: {
    config: {
      type: Object
    },
  },
  methods: {
    getRefId(filterConfig) {
      return `filter_config_${
        Array.isArray(filterConfig.key)
          ? filterConfig.key.join('_')
          : filterConfig.key
      }`;
    },
    
  },
  render(h) {
    let component = '';
    let childComponent = [];    
    let config = this.config;
    config.props.value = config.value || config.defaultValue || ''
    const handleChange = ({ value }) => {
      this.$emit('on-change', 
        {
          value
        }
      );
    }
    
    let listeners = {
      'on-change': (value) => {
        if((config.props && config.props.multiple) || (config.attrs && config.attrs.multiple)){
          value = value.join(',')
        }
        handleChange({ value: value });
      }
    };
    switch (config.component) {
      case 'TimePicker':
        component = config.component;
        listeners = {
          'on-change': value => {
            handleChange({ value: value });
          }
        };
        break;
      default:
        component = config.component;
    }
    let { attrs = {}, style = {}, props = {} } = config;
    let refIdd = this.getRefId(config);
    return h(
      component,
      {
        'class': config['class'],
        style,
        attrs: attrs,
        props: props,
        on: listeners,
        ref: refIdd,
        key:refIdd,
      },
      childComponent
    );
  }
};