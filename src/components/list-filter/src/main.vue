<template>
  <div
    ref="baseFilter"
    class="base-filter filter"
    :class="{ 'filter--advance': isShowAdvance }"
  >
    <slot name="content" :filters="filters">
      <FilterRow
        type="flex"
        align="top"
        :style="
          hasAdvanceItems && !isShowAdvance
            ? { height: '34px', overflow: 'hidden' }
            : null
        "
      >
        <div
          :style="{
            width: fixedWidth + 'px',
            order: MAX_ITEM_NUM,
            display: 'flex',
          }"
        >
          <div
            class="filter__operation"
            :class="{ 'filter__operation--advance': hasAdvanceItems }"
            :style="{
              width: hasAdvanceItems ? '324px' : '',
              'max-width': '324px',
            }"
          >
            <div>
              <div
                v-show="!isShowAdvance && showAdvanceBtn"
                class="filter__operation__btn-wrap"
              >
                <Button
                  @click="handleSearch"
                  class="filter__search-button"
                  type="primary"
                  >{{ searchText }}</Button
                >
                <slot name="button"></slot>
              </div>
            </div>
            <div>
              <div
                style="float: right"
                v-show="hasAdvanceItems && showAdvanceTrigger && widthReady"
                @click="toggleAdvance"
                class="filter__advance-trigger text--main-color"
              >
                <span class="noselect">
                  {{ isShowAdvance ? '收起高级筛选' : '高级筛选' }}
                </span>
                <Icon
                  class="filter__advance-icon filter__advance-icon-down"
                  :class="{ epxaned: isShowAdvance }"
                  :rotate="isShowAdvance ? 180 : 0"
                  icon="arrow-down"
                />
              </div>
            </div>
          </div>
          <div style="padding-left: 20px">
            <Poptip
              v-model="visible"
              v-show="showCustom"
              content="自定义筛选条件"
            >
              <span
                v-show="showCustom"
                @mouseover="filterMouseOver"
                @mouseleave="filterMouseLeave"
                @click="clickIcon"
                class="iconfont icon-zidingyishaixuantiaojian text--main-color filter__advance-trigger"
              ></span>
            </Poptip>
          </div>
        </div>
        <template v-for="(filterItem, index) in allFilters">
          <FilterCol
            v-if="isShowFilterCol(filterItem, index)"
            :key="filterItem.label"
            :block="filterItem.block"
            :tag-top-start="filterItem.tagTopStart"
            :label="filterItem.label"
            :label-width="getLabelWidth(filterItem, index)"
            :filter-item="filterItem"
            :style="{ order: filterItem.flexOrder }"
          >
            <FilterRender
              ref="filter_ref"
              :key="filterItem.label + 'filter_ref'"
              v-model="modelData[getModelKey(filterItem)]"
              :config="filterItem"
              :lazy="lazy"
              @on-change="handleChange"
            />
          </FilterCol>
        </template>
      </FilterRow>
    </slot>
    <div v-if="isShowAdvance" class="filter__button-wrap flex-row">
      <Button @click="handleSearch('trigger')" class="filter__button" type="primary">{{
        searchText
      }}</Button>
      <Button @click="handleReset" class="filter__button" type="default"
        >重置</Button
      >
      <slot name="button"></slot>
    </div>
    <filterModal
      v-if="isOpenCustom"
      v-model="showFilterSet"
      :tableId="tableId"
      @saveTemporary="saveTemporary"
      @saveTemporaryConfig="saveTemporaryConfig"
      :filterData="temporaryFilter"
      @customFilter="customFilter"
      :showConfig="showConfig"
      :filterConfig="filterConfigCopy"
    ></filterModal>
  </div>
</template>
<script>
import common from '@api/main.js';
import filterModal from './filterModal';
import Button from '../../button';
import Icon from '../../icon';
import FilterRow from './filter-row';
import FilterCol from './filter-col';
import FilterRender from './render';
import { Col } from 'view-design';
import elementResizeDetectorMaker from 'element-resize-detector';
import { debounce } from 'lodash-es';
import { get } from '@api/request.js';
import api from '@api/api.js';
import Bus from '@api/bus';
import { mapState } from 'vuex';

// import goods from '@api/goods.js';
// import Util from '@api/util.js';
// let initFilterData = {};

const ITEM_WIDTH = 335;
const ITEM_MARGIN = 36;
const MIN_ITEM_NUM = 2;
const MAX_ITEM_NUM = 3;
const SWAP_WIDTH = 1339;

export default {
  name: 'BaseFilter',
  components: {
    Button,
    Icon,
    Col,
    FilterRow,
    FilterCol,
    FilterRender,
    filterModal,
  },
  props: {
    useDefaultFilerOrder: {
      type: Boolean,
      default: false,
    },
    updateWidthOnAllFiltersChange: {
      type: Boolean,
      default: false,
    },
    tableId: {
      type: [String, Number],
      default: '',
    },
    allFliter: {
      type: Array,
      default: () => [],
    },
    filters: {
      type: Object,
      default: () => {},
    },
    filterItems: {
      type: Array,
      default: () => [],
    },
    advanceItems: {
      type: Array,
      default: () => [],
    },
    showAdvanceBtn: {
      type: Boolean,
      default: true,
    },
    showAdvance: {
      type: Boolean,
      default: false,
    },
    afterFilterRender: {
      type: Function,
    },
    lazy: {
      type: Boolean,
      default: false,
    },
    filterConfig: {
      type: Array,
      default: () => [],
    },
    isOpenCustom: {
      type: Boolean,
      default: false,
    },
    searchText: {
      type: String,
      default: '查询',
    },
  },
  provide: function () {
    return {
      // 更新filter属性
      handleUpdateFilterCol: this.handleUpdateFilterCol,
    };
  },
  watch: {
    allFilters() {
      if (this.updateWidthOnAllFiltersChange) {
        this.$nextTick(() => {
          this.getFixedWidth();
        });
      }
    },
    filterItems: {
      deep: true,
      handler(n, o) {
        this.updateFilter(n, o, 'filter');
        this.getTemporaryData();
        this.setFilterItemsWidth();
        this._initResizeHandler();
      },
    },
    advanceItems: {
      deep: true,
      handler(n, o) {
        this.updateFilter(n, o);
        this.getTemporaryData();
        this.setAdvanceItemsWidth();
        this._initResizeHandler();
      },
    },
    showAdvance(value) {
      this.isShowAdvance = value;
    },
  },
  data() {
    return {
      advanceItemsTemporary: [],
      filterItemTempory: [],
      showCustom: false,
      newFilter: false,
      temporaryFilter: [], //备份所有的数据
      customFiltersData: [], //自定义过滤后的组件
      showFilterSet: false,
      visible: true,
      MAX_ITEM_NUM,
      filterComponentsCount: 0, // 筛选组件的数量、用于统计是否所有组件都加载完
      isShowAdvance: false,
      firstColWidth: 65,
      modelData: {},
      defaultModelData: {},
      filterData: {},
      defaultFilterData: {},
      asyncComponentKeys: [],
      onReadyIsEmit: false,
      showAdvanceTrigger: true,
      observer: null,
      fixedWidth: 'auto',
      widthReady: false,
      resizeSwapped: false,
      delKey: [],
      filterConfigCopy: [],
      showConfig: false,
    };
  },
  computed: {
    hasAdvanceItems() {
      return !!this.advanceItems.length;
    },

    allFilters() {
      // 获取刚刚筛选过后的数据
      if (this.customFiltersData && this.customFiltersData.length > 0) {
        return this.customFiltersData;
      } else {
        // 版本12.1.0之后的取设置值 之前的老用户默认全部的筛选器
        if (
          this.compareVersion(
            this.$store.state.sysConfig.construct_version,
            '12.1.0',
          ) <= 0
        ) {
          let filter = this.deepClone(this.filterItems);
          let advance = this.advanceItems;
          filter.forEach((res) => {
            if (res.checked === undefined) {
              res.checked = true;
            }
          });
          advance.forEach((res) => {
            if (res.checked === undefined) {
              res.checked = true;
            }
          });
          this.temporaryFilter = [...filter, ...advance];
          const _filters = [...this.filterItems,...this.advanceItems];
          if (this.useDefaultFilerOrder) {
            _filters.forEach((item, index) => {
              if (item.flexOrder && item.flexOrder !== MAX_ITEM_NUM) {
                if (index < MAX_ITEM_NUM - 1) {
                  item.flexOrder = index + 1;
                } else {
                  item.flexOrder = index + 2;
                }
              }
            });
          }
          return _filters;
        }
        this.temporaryFilter = [...this.filterItems, ...this.advanceItems];
        if (this.isOpenCustom) {
          return [...this.filterItems, ...this.advanceItems].filter(
            (res) => res.checked,
          );
        } else {
          const _filters = [...this.filterItems,...this.advanceItems];
          if (this.useDefaultFilerOrder) {
            _filters.forEach((item, index) => {
              if (item.flexOrder && item.flexOrder !== MAX_ITEM_NUM) {
                if (index < MAX_ITEM_NUM - 1) {
                  item.flexOrder = index + 1;
                } else {
                  item.flexOrder = index + 2;
                }
              }
            });
          }
          return _filters;
        }
      }
    },
  },
  created() {
    this.initShowAdvance = true;
    this.modifyNew();
    this.initFilterData();
    this.initFilterConfig();
    this.isShowAdvance = this.showAdvance;
    // 等宽度稳定后再显示，避免闪烁
    this.setWidthReady = debounce(() => {
      this.widthReady = true;
    }, 20);

    this.$nextTick(() => {
      // 监听tab键盘事件, 按下tab的时候且组件处于当前页面的时候, 展开高级筛选
      this.$refs.baseFilter.addEventListener('keydown', (e) => {
        if (e.keyCode === 9 && this.$el.contains(document.activeElement)) {
          this.isShowAdvance = true;
        }
      });
    });
  },
  mounted() {
    this.customFiltersData = [];
    this.observer = elementResizeDetectorMaker();
    this.observer.listenTo(this.$refs.baseFilter, this._handleResizeFilter);
    this._initResizeHandler();
    if (typeof this.afterFilterRender === 'function') {
      this.afterFilterRender();
    }
    this.getTemporaryData(true);
  },
  beforeDestroy() {
    this.hasAdvanceItems && this.observer.uninstall(this.$refs.baseFilter);
    // 移除监听事件
    this.$refs.baseFilter.removeEventListener('keydown', () => {});
  },
  methods: {
    handleUpdateFilterCol(col) {
      const index = this.temporaryFilter.findIndex((k) => k.key === col.key);
      this.temporaryFilter[index][col.attr] = col.value;
    },
    isShowFilterCol(filterItem, index) {
      // 初始化只加载3个 如果点了展开就全部渲染 收起还是全部
      if (this.lazy && this.initShowAdvance) {
        if (index - 3 >= 0) {
          return false;
        }
      }
      return filterItem.show !== false && !filterItem.hide;
    },
    numberShow() {
      if (!this.$refs.baseFilter) {
        return MAX_ITEM_NUM;
      }
      const width = this.$refs.baseFilter.clientWidth;
      if (width > SWAP_WIDTH) {
        return MAX_ITEM_NUM;
      } else {
        return MIN_ITEM_NUM;
      }
    },
    // 版本1大于版本2则返回1 小于则返回-1 等于返回0
    compareVersion(version1, version2) {
      const newVersion1 =
        `${version1}`.split('.').length < 3
          ? `${version1}`.concat('.0')
          : `${version1}`;
      const newVersion2 =
        `${version2}`.split('.').length < 3
          ? `${version2}`.concat('.0')
          : `${version2}`;
      //计算版本号大小,转化大小
      function toNum(a) {
        const c = a.toString().split('.');
        const num_place = ['', '0', '00', '000', '0000'],
          r = num_place.reverse();
        for (let i = 0; i < c.length; i++) {
          const len = c[i].length;
          c[i] = r[len] + c[i];
        }
        return c.join('');
      }
      //检测版本号是否需要更新
      function checkPlugin(a, b) {
        const numA = toNum(a);
        const numB = toNum(b);
        return numA > numB ? 1 : numA < numB ? -1 : 0;
      }
      return checkPlugin(newVersion1, newVersion2);
    },
    // 获取本地或者接口的数据
    async getTemporaryData(init) {
      let filterArrayLocalKey = this.tableId;
      let filterConfigLocalKey = this.tableId + '_filter_config';
      let filter_array_local = this.tableId
        ? localStorage.getItem(filterArrayLocalKey)
          ? JSON.parse(localStorage.getItem(filterArrayLocalKey))
          : null
        : null;
      let filter_config_local = this.tableId
        ? localStorage.getItem(filterConfigLocalKey)
          ? JSON.parse(localStorage.getItem(filterConfigLocalKey))
          : null
        : null;
      let filter_array = [];
      let filter_config = [];
      if (this.tableId && init) {
        // 只调用一次接口
        const { data, status, message } = await common.getSearchConfig({
          key: this.tableId,
        });
        if (status) {
          if (data && data.hasOwnProperty('filter_array')) {
            filter_array = data.filter_array;
            filter_config = data.filter_config;
          } else {
            // 旧版做兼容
            filter_array = data;
          }
        } else {
          console.error('getTemporaryData-errorMessage', message);
        }
        if (filter_array && filter_array.length > 0) {
          localStorage.setItem(
            filterArrayLocalKey,
            JSON.stringify(filter_array),
          );
        }
        if (filter_config && filter_config.length > 0) {
          localStorage.setItem(
            filterConfigLocalKey,
            JSON.stringify(filter_config),
          );
        }
      }
      if (filter_array_local && filter_array_local.length > 0) {
        // 跟缓存的值比较 排序过滤所有的值 以传进来的值为准
        this.setCustomData(filter_array_local);
      } else {
        // 本地无缓存 则用接口数据
        if (filter_array && filter_array.length > 0) {
          this.setCustomData(filter_array);
        }
      }
      if (filter_config_local && filter_config_local.length > 0) {
        // 跟缓存的值比较
        this.setCustomConfig(filter_config_local);
      } else {
        if (filter_config.length > 0) {
          this.setCustomConfig(filter_config);
        }
      }
    },
    saveTemporary(data) {
      // 当checked为false时, 删掉已有的key
      const unChecked = data.filter(k => !k.checked && !k.required)
      const keys = unChecked.flatMap(item => Array.isArray(item.key) ? item.key : [item.key]);
      this.delKeyHandle(keys, (key) => {
        // 在render.js里面change时有判断值是否一致, 一致就retur掉了, 所以这里也要把值置空
        if (this.modelData[key]) this.modelData[key] = ''
      })

      this.temporaryFilter = data;
    },
    saveTemporaryConfig(type, data) {
      this.filterConfigCopy = data;
      if (type === 'confirm') {
        this.$emit('filterConfigChange', this.filterConfigCopy);
      }
    },
    ifFixed(e) {
      let noDrag = ['check_box_group', 'radio_group', 'userTag', 'goodsTag', 'orderTag'];
      if (e.component && noDrag.includes(e.component.name)) {
        return true;
      }
      return false;
    },
    // 根据缓存的数据，设置
    setCustomConfig(data) {
      this.filterConfigCopy = this.filterConfigCopy.map((config, index) => {
        return {
          ...config,
          ...data[index],
        };
      });
    },
    //根据缓存的数据 设置自定义数据
    setCustomData(data) {
      this.temporaryFilter = [...this.filterItems, ...this.advanceItems];
      this.temporaryFilter.sort((a, b) => {
        let aIndex = data.findIndex((e) => {
          if (e.key.toString() == a.key.toString()) {
            a.checked = e.checked;
          }
          return e.key.toString() == a.key.toString();
        });
        let bIndex = data.findIndex((e) => {
          if (e.key.toString() == b.key.toString()) {
            b.checked = e.checked;
          }
          return e.key.toString() == b.key.toString();
        });
        if (aIndex == -1 && this.ifFixed(a)) {
          return 1;
        }
        return aIndex - bIndex;
      });
      // 过滤显示的值
      this.customFiltersData = (this.temporaryFilter)
        .filter((e) => e.required || e.checked)
        .map((res, index) => {
          res.flexOrder = index;
          if (index >= this.numberShow()) {
            res.flexOrder = index + 1;
          }
          return res;
        });
    },
    customFilter(data) {
      this.customFiltersData = data.map((res, index) => {
        res.flexOrder = index;
        if (index >= this.numberShow()) {
          res.flexOrder = index + 1;
        }
        return res;
      });
      this.showAdvanceTrigger =
        this.customFiltersData.length > this.numberShow();
      this.$nextTick(() => {
        this.getFixedWidth();
      });
    },
    modifyNew() {
      let newFilters = [
        '/orderChangePrice/new',
        '/appCenter/system/logs',
        '/agreementPrice/list',
        '/NewOrderReport',
        '/commoditySource/list',
      ];
      this.newFilter = newFilters.includes(this.$route.path);
      this.showCustom = this.isOpenCustom;
      if (localStorage.getItem('filter_new')) {
        this.visible = false;
      } else {
        this.visible = true;
      }
    },
    clickIcon() {
      this.showFilterSet = true;
      localStorage.setItem('filter_new', true);
    },
    filterMouseOver() {
      this.visible = true;
    },
    filterMouseLeave() {
      if (localStorage.getItem('filter_new')) {
        this.visible = false;
      } else {
      }
    },
    _initResizeHandler() {
      this.resizeSwapped = this.$refs.baseFilter.clientWidth <= SWAP_WIDTH;
      this._handleResizeFilter();
    },
    _handleResizeFilter() {
      if (!this.showCustom && !this.newFilter) {
        this._handleResizeFilter2();
        return;
      }
      if (this.hasAdvanceItems) {
        // 只影响含高级筛选且能展开和收缩的页面
        if (this.allFilters.length <= this.numberShow()) {
          this.allFilters.forEach((item, index) => (item.flexOrder = index));
          this.advanceItems[0].flexOrder =
            this.advanceItems[0].filterOrder || 0;
        } else {
          let tmpNum = 0;
          for (let i = 0; i < this.allFilters.length; i++) {
            if (this.allFilters[i].hide) {
              continue;
            }
            if (this.allFilters[i].show != false) {
              this.allFilters[i].flexOrder = tmpNum;
              if (tmpNum >= this.numberShow()) {
                this.allFilters[i].flexOrder = tmpNum + 1;
              }
              tmpNum++;
            }
          }
        }
        this.resizeSwapped = true;
        this.showAdvanceTrigger = this.allFilters.length > this.numberShow();
        if (this.allFilters.length <= this.numberShow())
          this.isShowAdvance = false;
        this.$nextTick(() => {
          this.getFixedWidth();
          this.setWidthReady();
        });
      } else {
        this.widthReady = true;
      }
    },
    _handleResizeFilter2() {
      const width = this.$refs.baseFilter.clientWidth;
      if (this.hasAdvanceItems) {
        // 只影响含高级筛选且能展开和收缩的页面
        if (width > SWAP_WIDTH) {
          if (!this.resizeSwapped) {
            const findStoreSelectorIndex = this.allFilters.findIndex(
              (item) =>
                ['仓库', '库房'].includes(item.label) &&
                (item.component
                  ? item.component.name !== 'storeSelectMultiple'
                  : true),
            );
            if (this.allFilters.length <= MAX_ITEM_NUM) {
              this.allFilters.forEach(
                (item, index) => (item.flexOrder = index),
              );
              if (~findStoreSelectorIndex)
                this.allFilters[findStoreSelectorIndex].flexOrder = 0;
              else this.advanceItems[0].flexOrder = 0;
            } else {
              let tmpNum = 0;
              if (findStoreSelectorIndex === 0) tmpNum++;
              for (let i = 0; i < this.allFilters.length; i++) {
                if (i !== findStoreSelectorIndex) {
                  if (tmpNum >= MAX_ITEM_NUM) {
                    this.allFilters[i].flexOrder = MAX_ITEM_NUM + i - 1;
                  } else {
                    this.allFilters[i].flexOrder = tmpNum++;
                  }
                }
              }
              if (~findStoreSelectorIndex) {
                this.allFilters[findStoreSelectorIndex].flexOrder =
                  MAX_ITEM_NUM + 1;
                if (
                  findStoreSelectorIndex === MAX_ITEM_NUM - 1 &&
                  this.allFilters[findStoreSelectorIndex + 1]
                )
                  this.allFilters[findStoreSelectorIndex + 1].flexOrder = 0;
                else this.advanceItems[0].flexOrder = 0;
              } else {
                this.advanceItems[0].flexOrder =
                  this.advanceItems[0].filterOrder || 0;
              }
            }
            this.resizeSwapped = true;
            this.showAdvanceTrigger = this.allFilters.length > MAX_ITEM_NUM;
            if (this.allFilters.length <= MAX_ITEM_NUM)
              this.isShowAdvance = false;
          }
        } else {
          if (this.resizeSwapped) {
            const findStoreSelectorIndex = this.allFilters.findIndex(
              (item) =>
                ['仓库', '库房'].includes(item.label) &&
                (item.component
                  ? item.component.name !== 'storeSelectMultiple'
                  : true),
            );
            if (this.allFilters.length <= MIN_ITEM_NUM) {
              this.allFilters.forEach(
                (item, index) => (item.flexOrder = index),
              );
            } else {
              let tmpNum = 0;
              for (let i = 0; i < this.allFilters.length; i++) {
                if (i !== findStoreSelectorIndex) {
                  if (tmpNum === MIN_ITEM_NUM + 1) {
                    tmpNum++;
                  }
                  this.allFilters[i].flexOrder = tmpNum;
                  tmpNum++;
                }
              }
              if (~findStoreSelectorIndex) {
                this.allFilters[findStoreSelectorIndex].flexOrder =
                  MAX_ITEM_NUM + 1;
                if (
                  findStoreSelectorIndex === MAX_ITEM_NUM - 1 &&
                  this.allFilters[findStoreSelectorIndex + 1]
                )
                  this.allFilters[findStoreSelectorIndex + 1].flexOrder =
                    MAX_ITEM_NUM + 2;
                else this.advanceItems[0].flexOrder = MAX_ITEM_NUM + 2;
              } else this.advanceItems[0].flexOrder = MAX_ITEM_NUM + 2;
            }
            this.resizeSwapped = false;
            this.showAdvanceTrigger = this.allFilters.length > MIN_ITEM_NUM;
            if (this.allFilters.length <= MIN_ITEM_NUM)
              this.isShowAdvance = false;
          }
        }

        this.$nextTick(() => {
          this.getFixedWidth();
          this.setWidthReady();
        });
      } else {
        this.widthReady = true;
      }
    },
    getFixedWidth() {
      const width = this.$refs.baseFilter.clientWidth;
      const count = Math.max(
        MIN_ITEM_NUM,
        Math.min(parseInt(width / ITEM_WIDTH) - 1, MAX_ITEM_NUM),
      );
      // this.fixedWidth = width - ITEM_MARGIN - ITEM_WIDTH * count + 'px';
      // ITEM_WIDTH可能有自定义组件在页面中有更改导致有一定误差，因此需要获取元素实际宽度来计算
      const filterItemEls =
        this.$refs.baseFilter.getElementsByClassName('filter__col');
      if (filterItemEls.length) {
        let fixedWidth = width;
        for (let i = 0; i < count; i++) {
          fixedWidth -=
            (filterItemEls[i] ? filterItemEls[i].clientWidth : 0) + ITEM_MARGIN;
        }
        this.fixedWidth = fixedWidth - 10; // -10：预留一个较小的宽度，消除页面缩放与扩大时的误差
      }
    },
    openAdvance() {
      this.isShowAdvance = true;
    },
    closeAdvance() {
      this.isShowAdvance = false;
    },
    toggleAdvance() {
      this.initShowAdvance = false;
      this.isShowAdvance = !this.isShowAdvance;
      this.$emit('advanceChange', this.isShowAdvance);
      this.$nextTick(() => {
        Bus.$emit('tableScroll');
      });
    },
    handleSearch(event) {
      this.$emit('filter', this.getFilters());

      // 手动点击按钮查询的话, 场景: 查询改导出,  点击按钮导出
      if (event) {
        this.$emit('on-search', this.getFilters());
      }
    },
    handleReset() {
      let filterData = this.deepClone(this.defaultFilterData);
      let modelData = this.deepClone(this.defaultModelData);
      this.$refs['filter_ref'].forEach((comp) => {
        if (comp.config.noReset) {
          const defaultValue =
            (comp.getValue && comp.getValue()) || comp.config.defaultValue;
          filterData[comp.config.key] =
            defaultValue || this.filterData[comp.config.key];
          modelData[comp.config.key] =
            defaultValue || this.modelData[comp.config.key];
          return;
        }
        if (comp.getDefaultValue) {
          const defaultValueMeta = comp.getDefaultValue();
          const modelKey = this.getModelKey(comp.config);
          filterData = {
            ...filterData,
            ...defaultValueMeta.filterData,
          };
          modelData = {
            ...modelData,
            [modelKey]: defaultValueMeta.defaultValue,
          };
        }
        if (comp.config.key == 'commodity_unit_price') {
          comp.config.props.disabled = true;
        }
        if (comp.config.noreset) {
          const defaultValue = comp.config.defaultValue;
          filterData[comp.config.key] =
            defaultValue || this.filterData[comp.config.key];
          modelData[comp.config.key] =
            defaultValue || this.modelData[comp.config.key];
        }
        comp.resetValue();
      });
      this.filterData = filterData;
      this.modelData = modelData;

      this.$emit('reset', this.getFilters());
    },
    async selectCategory1(filterItem, value) {
      let subCategoryList = [];
      if (value) {
        let res = await get(api.getGoodsCategory, { category_id: value });
        subCategoryList = res.data.map((item) => {
          return {
            id: item.id,
            name: item.name,
          };
        });
      } else {
      }
      this.$refs['filter_ref'].forEach((comp) => {
        if (filterItem.changeItem.includes(comp.config.key)) {
          let category2 =
            comp.$refs['filter_' + filterItem.changeItem[0]].currentModel;
          let result = [];
          // 如果删除了一级分类，剔除该一级分类下的二级分类
          if (category2 && category2.length > 0) {
            category2.forEach((id) => {
              if (subCategoryList.find((item) => item.id === id)) {
                result.push(id);
              }
            });
          }
          category2 = result.join(',');
          comp.$refs['filter_' + filterItem.changeItem[0]].optionList =
            subCategoryList;
          comp.$refs['filter_' + filterItem.changeItem[0]].currentModel =
            result;
        }
      });
    },
    async selectPurchaseType(filterItem, value) {
      let subList = [];
      if (value) {
        let { data } = await get(api.getPurchaseType);
        switch (value) {
          case '1':
            subList = data.agents.map((item) => ({
              id: item.id,
              name: item.name,
            }));
            break;
          case '2':
          case '3':
            subList = data.providers.map((item) => ({
              id: item.id,
              name: item.name,
            }));
            break;
          case '5':
            const jointProvidersArr = data.providers.filter(
              (item) => item.provider_type === '2',
            );
            subList = jointProvidersArr.map((item) => ({
              id: item.id,
              name: item.name,
            }));
            break;
        }
      }
      this.$refs['filter_ref'].forEach((comp) => {
        if (comp.config.key == filterItem.changeItem[0]) {
          const changeFilter = comp.$refs['filter_' + filterItem.changeItem[0]];
          if (changeFilter) {
            changeFilter.optionList = subList;
            changeFilter.currentModel = '';
          }
        }
      });
    },
    async handleChange({
      filterData,
      isDefault,
      filterItem,
      value,
      triggerFilter,
    }) {
      if (
        filterItem.type === 'GroupFilter' ||
        filterItem.hasType === 'GroupFilter'
      ) {
        let type = value[0] || '';
        let filterDataValueArr = Object.values(filterData).slice(1);
        let keys = [];
        filterItem.props.selectData.forEach((item) => {
          if (type === item.value) {
            if (Array.isArray(item.key)) {
              keys = [...item.key];
            } else {
              keys.push(item.key);
            }
          }
          // 删除 多余key
          for (const filterKey in this.filterData) {
            if (
              this.filterData.hasOwnProperty.call(this.filterData, filterKey)
            ) {
              if (Array.isArray(item.key)) {
                item.key.forEach((key) => {
                  if (filterKey === key) {
                    delete this.filterData[filterKey];
                  }
                });
              } else {
                if (filterKey === item.key) {
                  delete this.filterData[filterKey];
                }
              }
            }
          }
        });
        filterData = {};
        filterDataValueArr.forEach((item, index) => {
          filterData[keys[index]] = item;
        });
      }
      this.filterData = {
        ...this.filterData,
        ...filterData,
      };
      // 多个联级筛选 第二个筛选项基于第一个
      if (filterItem.changeItem && filterItem.changeItem.length > 0) {
        filterItem.changeItem.includes('category2') &&
          (await this.selectCategory1(filterItem, value));
        filterItem.changeItem.includes('purchaseValue') &&
          (await this.selectPurchaseType(filterItem, value));
      }
      if (filterItem.label == '发货日期' && this.$refs['filter_ref']) {
        this.$refs['filter_ref'].forEach((comp) => {
          if (comp.config.key == 'commodity_unit_price') {
            comp.config.props.disabled = value[0] && value[0] !== value[1];
          }
        });
      }
      let modelKey = this.getModelKey(filterItem);
      if (isDefault) {
        for (let dataKey in filterData) {
          this.defaultFilterData[dataKey] = filterData[dataKey];
        }
        this.defaultModelData[modelKey] = value;
      }
      // 统计异步组件完成情况
      let newAsyncComponentKeys = [];
      this.asyncComponentKeys.map((item) => {
        // 在 initFilterData 的时候，已经将所有 asyncComponent 为 true 的异步组件放到 this.asyncComponentKeys 数组中
        // 异步组件的第一次 onchange 时间，视为组件准备完成的标记
        // 这里在每次 onchange 的时候，查找发出 onchange 的组件，是不是异步组件
        // 如果是，如果是，则视为该异步组件已经完成，将该组件从 this.asyncComponentKeys 中剔除
        // 剔除判断方式为当前发出 onchange 的组件是否是异步组件，也就是 modelKey !== item 判断
        if (modelKey !== item) {
          newAsyncComponentKeys.push(item);
        } else {
          // 异步组件初始化保存默认值
          if (Array.isArray(item)) {
            item.forEach(
              (keyItem) =>
                (this.defaultFilterData[keyItem] = filterData[keyItem]),
            );
          } else {
            this.defaultFilterData[item] = filterData[item];
          }
          this.defaultModelData[modelKey] = value;
        }
      });
      this.asyncComponentKeys = newAsyncComponentKeys;
      // 如果不含异步组件，发出 on-ready 事件
      if (this.checkIfAllAsyncComponentOnReady()) {
        this.onReadyIsEmit = true;
        this.$emit('on-ready', this.getFilters());
      }
      if (!triggerFilter) {
        return false;
      }
      this.$nextTick(() => {
        this.onReadyIsEmit && this.handleSearch();
      });
    },
    delKeyHandle(keys = this.delKey, ckOk) {
      if (!keys.length) {
        return;
      }
      for (const key in this.filterData) {
        keys.forEach((item) => {
          if (item === key) {
            typeof ckOk === 'function' && ckOk(key)
            delete this.filterData[key];
          }
        });
      }
    },
    filterFormat() {
      this.delKeyHandle();
    },
    getFilters() {
      this.filterFormat();
      return {
        ...this.filterData,
        ...this.filters,
      };
    },
    setValue(key, value, stop) {
      if (!key) {
        return;
      }
      if (value === undefined) {
        return;
      }
      // 设置绑定值
      let modelKey = Array.isArray(key) ? key.join('__') : key;
      this.modelData[modelKey] = value;
      // 设置属性值
      if (Array.isArray(key)) {
        if (!Array.isArray(value)) {
          value = [];
        }
        key.map((keyItem, index) => {
          this.filterData[keyItem] = value[index];
        });
      } else {
        this.filterData[key] = value;
      }
      if (!stop) {
        this.$emit('filter', this.getFilters());
      }
    },
    updateFilter(newData, oldData, filter) {
      // 新老数据判断是否有默认值变化 一开始取默认值
      const initFilerData = (filterCol) => {
        let defaultValue =
          filterCol.defaultValue === undefined ? '' : filterCol.defaultValue;
        let filterKey = filterCol.key;
        if (Array.isArray(filterKey)) {
          if (!Array.isArray(defaultValue)) {
            defaultValue = [];
          }
          filterKey.map((keyItem, index) => {
            this.filterData[keyItem] = defaultValue[index] || '';
            this.defaultFilterData[keyItem] = defaultValue[index] || '';
          });
        } else {
          this.filterData[filterKey] = defaultValue;
          this.defaultFilterData[filterKey] = defaultValue;
        }
        const modelKey = this.getModelKey(filterCol);
        this.defaultModelData[modelKey] = defaultValue;
        this.$set(this.modelData, modelKey, defaultValue);
      };
      let filterData = filter ? 'filterItemTempory' : 'advanceItemsTemporary';
      newData.forEach((res) => {
        this[filterData].forEach((e) => {
          if (res.key.toString() == e.key.toString()) {
            if (
              res.type == 'DatePicker' &&
              (res.defaultValue && res.defaultValue.toString()) !=
                (e.defaultValue && e.defaultValue.toString())
            ) {
              initFilerData(res);
            }
          }
        });
      });
      this[filterData] = JSON.parse(JSON.stringify(newData));
    },

    initFilterData() {
      const initFilerData = (filterCol) => {
        if (
          filterCol.type === 'GroupFilter' ||
          filterCol.hasType === 'GroupFilter'
        ) {
          this.delKey = Array.from(new Set([filterCol.key[0], ...this.delKey]));
        }
        let defaultValue =
          filterCol.defaultValue === undefined ? '' : filterCol.defaultValue;
        let filterKey = filterCol.key;
        if (Array.isArray(filterKey)) {
          if (!Array.isArray(defaultValue)) {
            defaultValue = [];
          }
          filterKey.map((keyItem, index) => {
            this.filterData[keyItem] = defaultValue[index] || '';
            this.defaultFilterData[keyItem] = defaultValue[index] || '';
          });
        } else {
          this.filterData[filterKey] = defaultValue;
          this.defaultFilterData[filterKey] = defaultValue;
          if (
            filterCol.type === 'Select' &&
            filterCol.arrayToString &&
            Array.isArray(defaultValue)
          ) {
            this.filterData[filterKey] = this.defaultFilterData[filterKey] =
              defaultValue.join(',');
          }
        }

        const modelKey = this.getModelKey(filterCol);

        this.defaultModelData[modelKey] = defaultValue;
        this.$set(this.modelData, modelKey, defaultValue);
        if (filterCol.asyncComponent && filterCol.show !== false) {
          this.asyncComponentKeys.push(filterKey);
        }
      };
      this.filterItems.map((filterCol) => {
        initFilerData(filterCol);
      });

      this.advanceItems.forEach((filterCol) => {
        initFilerData(filterCol);
      });

      this.setFilterItemsWidth();
      this.setAdvanceItemsWidth();

      // 如果不含异步组件，直接发出 on-ready 事件
      if (this.checkIfAllAsyncComponentOnReady()) {
        this.onReadyIsEmit = true;
        this.$emit('on-ready', this.getFilters());
      }
    },
    initFilterConfig() {
      this.filterConfigCopy = this.filterConfig;
      if (this.filterConfigCopy.length > 0) {
        this.showConfig = true;
      }
    },
    setFilterItemsWidth() {
      this.filterItems.forEach((filterCol) => {
        // 给组件赋值默认宽度
        if (filterCol.style && filterCol.style.width) {
          return;
        } else {
          filterCol.style = filterCol.style || {};
          filterCol.style.width = (filterCol.width || 232) + 'px';
        }
      });
    },
    /**
     * 设置高级筛选选项宽度
     */
    setAdvanceItemsWidth() {
      this.advanceItems.forEach((filterCol) => {
        // 给组件赋值默认宽度
        if (filterCol.style && filterCol.style.width) {
          return;
        } else {
          filterCol.style = filterCol.style || {};
          filterCol.style.width = (filterCol.width || 232) + 'px';
        }
      });
    },
    // 检查所有异步组件是否都已返回数据
    checkIfAllAsyncComponentOnReady() {
      return this.asyncComponentKeys.length === 0 && !this.onReadyIsEmit;
    },
    getLabelWidth(item, index) {
      if (item.labelWidth) {
        return item.labelWidth;
      }
      if (index === 0) {
        return this.firstColWidth;
      }
      return 0;
    },
    getModelKey(filterItem) {
      if (!filterItem.key) {
        return '';
      }
      return Array.isArray(filterItem.key)
        ? filterItem.key.join('__')
        : filterItem.key;
    },
  },
};
</script>
<style scoped lang="less">
/deep/ .ivu-poptip-popper {
  min-width: 120px;
}
/deep/ .ivu-poptip-body {
  max-width: 120px;
  min-height: 34px;
  padding: 8px 12px;
  text-align: center;
  color: #fff;
  text-align: left;
  text-decoration: none;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  .ivu-poptip-body-content-inner {
    text-align: center;
    color: #fff;
  }
}
/deep/.ivu-poptip-popper .ivu-poptip-arrow:after {
  border-top-color: rgba(0, 0, 0, 0.7);
}
</style>
<style lang="less">
@filter-prefix: filter;

/deep/ .ivu-modal-body {
  .list-table {
    .filter__col {
      display: flex;
      align-items: center;
    }
    .filter__operation {
      margin-left: 12px;
    }
  }
}
.layout-main {
  .base-filter {
    position: relative;
    padding: 0;
    font-size: 13px;
    height: 34px;
    &.filter--advance {
      height: auto;
    }
    .advance-content {
      max-width: 100%;
    }
    .filter__button-wrap {
      margin: 2px 0 14px 67px;
      .filter__button {
        margin-right: 10px;
      }
    }
  }
  .@{filter-prefix} {
    &__col {
      margin-right: 36px;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
    }
    &__operation {
      max-width: 306px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin-left: -24px;
      &--advance {
        max-width: 324px;
        width: 306px;
      }
    }
    &__operation__btn-wrap {
      display: flex;
      align-items: center;
      flex-direction: row;
    }
    &__search-button {
      margin-right: 10px;
    }
    &__advance-trigger {
      position: relative;
      cursor: pointer;
      text-align: right;
      display: flex;
      flex-direction: row;
      align-items: center;
      color: #03ac54;
      line-height: 30px;
    }
    &__advance-icon-down {
      &.sui-icon {
        width: auto;
        height: auto;
        line-height: 1;
        transition: transform 500ms ease 0s;
        font-size: 10px;
        margin-left: 2px;
      }
      &.epxaned {
        transform: rotate(180deg);
      }
    }
    // &--advance {
    //   margin-bottom: 12px;
    // }
    &--advance::after {
      content: '';
      display: block;
      width: 100%;
      height: 1px;
      transform: scaleY(0.5);
      background-color: #e8e8e8;
    }
    &__tips {
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
    }
  }

  .base-filter-row {
    .filter-row__label {
      padding: 0;
      margin-right: -10px;
    }
  }
}
</style>
