import { InputNumber } from "view-design";

/**
 * 继承Input组件解决内存泄露问题
 */
export default {
  extends: InputNumber,
  methods: {
    destroyInput() {
      this._vnode.children.forEach(vnode => {
        if (vnode.data && vnode.data.class === this.inputWrapClasses) {
          if (!vnode.children) {
            return;
          }
          vnode.children.forEach(childVnode => {
            if (childVnode.data && childVnode.data.on) {
              Object.keys(childVnode.data.on).forEach(event => {
                childVnode.elm.removeEventListener(
                  event,
                  childVnode.data.on[event]._wrapper
                );
              });
              if (childVnode.elm.remove) {
                childVnode.elm.remove();
              }
            }
          });
        }
      });
    }
  },
  beforeDestroy() {
    this.destroyInput();
    if (this.$el && this.$el.remove) {
      this.$el.remove();
    }
  }
};
