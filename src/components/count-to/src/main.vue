<template>
<span :id="counterId" :class="[`decimal--${decimal}`]"></span>
</template>

<script>
import { CountUp } from "countup.js";
export default {
  name: "CountTo",
  props: {
    options: {
      type: Object,
      default: () => ({})
    },
    end: {
      type: [Number, String],
      required: true
    },
    decimal: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      counter: null,
    };
  },
  computed: {
    counterId() {
      return `count_to_${this._uid}`;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.counter = new CountUp(
        this.counterId,
        this.end,
        {
          decimalPlaces: this.decimal,
          // 由于全局的toFixed方法被改写导致小数位数显示有问题，这个地方用原始的toFixed方法做特殊处理
          formattingFn: (val) => Number(val).originTofixed(this.decimal),
          duration: 0.5,
          ...this.options
        }
      );
      setTimeout(() => {
        if (!this.counter.error) this.counter.start();
      }, this.delay);
    });
  },
  unmounted() {
    this.counter = null;
  },
  watch: {
    end(newVal) {
      this.counter.update(newVal);
    }
  }
};
</script>
<style lang="less">
.decimal--2 {
  .odometer-numbers > span:nth-last-child(3) {
    width: 0.25em;
  }
}
</style>
