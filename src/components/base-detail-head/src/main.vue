<!--
 * @Date: 2022-09-09 15:10:14
 * @LastEditTime: 2023-07-19 11:45:06
 * @FilePath: /sdpbase-pro/src/components/base-detail-head/src/main.vue
-->
<template>
  <div class="base-detail-head">
    <div style="font-weight: bold;font-size: 13px;">{{ title }}</div>
    <div class="base-detail-head__back" v-show="backTxt" @click="back">
      <s-icon icon="arrow-down" :size="12" /><span>{{ backTxt }}</span>
    </div>
    <div class="base-detail-head__right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import { SIcon } from '@sdp/ui';
export default {
  name: 'base-detail-head',
  components: {
    SIcon
  },
  props: {
    title: {
      type: String
    },
    backTxt: {
      type: String,
      default: '返回'
    },
    backTo: {
      type: [String, Function]
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  methods: {
    back() {
      if (typeof this.backTo === 'function') {
        this.backTo();
      } else if (typeof this.backTo === 'string') {
        this.$router.push(this.backTo);
      } else {
        if ((history.length && history.length <=1) || this.$route.query.back === 'close') {
          window.close();
        } else {
          this.$router.go(-1)
        }
      }
      // console.log('back', this.backTo)
      // if (typeof this.backTo === 'function') {
      //   this.backTo();
      // } else if (typeof this.backTo === 'string') {
      //   this.$router.push(this.backTo);
      // } else {
      //   if (window.opener) {
      //     // 如果是新开页面，则关闭当前页面
      //     window.close();
      //   } else if (this.$router) {
      //     // 如果可以跳转上一页，则跳转上一页
      //     this.$router.go(-1);
      //   } else {
      //     // 其他情况，例如只有一页或无法跳转上一页，可以执行其他操作
      //     window.close();
      //   }
      // }
    }
  }
};
</script>
