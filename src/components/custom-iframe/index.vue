<template>
  <iframe
    v-if="token"
    :src="`${hostUrl}${src}?token=${token}&ccode=${ccode}`"
    style="width: 100%; height: calc(100vh - 90px); border: none"
  ></iframe>
</template>

<script>
import jsCookie from 'js-cookie';

// 团餐域名 / 前端通用服务域名
export default {
  name: 'custom-iframe',
  props: {
    host: {
      type: String,
      default: 'common',
    },
    src: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      hostUrl: '',
      token: localStorage.getItem('admin_token') || '',
      ccode: jsCookie.get('ccode') || location.host.split('.')[0]
    };
  },
  mounted() {
    // 根据 host 属性设置不同的域名
    if (this.host === 'common') {
      if (location.host.indexOf('sdongpo.com') !== -1) {
        this.hostUrl = location.pathname.replace('/view', '/viewCommon')
      } else {
        // 开发环境
        this.hostUrl = 'http://localhost:8000/superAdmin/viewCommon'; // 替换为实际的通用服务域名
      }
    } else if (this.host === 'tuancan') {
      this.hostUrl = 'https://tc-tenant-dev.movee.cn'; // 替换为实际的团餐域名
    } else {
      console.error('Invalid host specified');
    }
  },
};
</script>

<style lang="less"></style>
