<!--
 * @Description:
 * @Date: 2023-07-05 09:27:12
 * @LastEditors: hgj
 * @LastEditTime: 2023-07-07 10:37:23
 * @FilePath: /sdpbase-pro/src/components/cascader/index.vue
-->
<template>
  <div class="s-cascader">
    <el-cascader
      :class="{
        'max-input': valueCopy.length > 0,
      }"
      v-model="valueCopy"
      :options="options"
      :placeholder="placeholder"
      @change="changeVal"
      :filterable="filterable"
      size="mini"
      :collapse-tags="true"
      clearable
      :props="{ multiple: multiple, expandTrigger: expandTrigger }"
      :popperClass="popperClass"
    ></el-cascader>
  </div>
</template>

<script>
import { Cascader } from 'element-ui';

export default {
  name: 's-cascader',
  components: {
    elCascader: Cascader,
  },

  props: {
    value: {
      type: [Array],
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择商品分类',
    },
    multiple: {
      type: Boolean,
      default: true,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    expandTrigger: {
      type: String,
      default: 'click',
    },
    popperClass: {
      type: String,
    },
  },

  data() {
    return {
      valueCopy: [],
      options: [],
    };
  },

  computed: {},

  watch: {
    value: {
      handler(newVal) {
        this.valueCopy = newVal;
        console.log('valueCopy', newVal);
      },
      deep: true,
      immediate: true,
    },
    treeData: {
      handler(newVal) {
        this.options = newVal;
      },
      deep: true,
      immediate: true,
    },
  },

  created() {},

  mounted() {},

  methods: {
    changeVal(val) {
      this.$emit('input', val);
      this.$emit('change', val);
    },
  },
};
</script>
<style lang="less" scoped>
.s-cascader {
  /deep/ .el-cascader {
    width: 232px;
    .el-input__inner {
      height: 30px !important;
      line-height: 30px;
      border: 1px solid #d8d8d8;
      border-radius: 2px;
    }
    .el-cascader__tags {
      .el-cascader__search-input {
        margin: 0 0 0px 10px;
        min-width: 110px;
      }
      .el-tag {
        span {
          max-width: 160px;
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; //溢出不换行
        }
      }
      .el-tag:last-of-type {
        margin-left: 1px;
      }
      input::-webkit-input-placeholder {
        font-size: 13px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.5) !important;
      }
    }
  }
  /deep/ .el-cascader.max-input {
    .el-input__inner {
      height: 58px !important;
    }
  }
}
</style>
