<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="handleCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1100">
      <list-table
        ref="list"
        request-type="post"
        :table-height="getTableHeight() - 80"
        :api="apiUrl.getGoodsList"
        :auto-load-data="false"
        :filter-items="filterItems"
        :filters="filters"
        :columns="cols"
        :row-class-name="rowClassName"
        :after-load-list="afterLoadList"
        :page-size-opts="pageSizeOpts"
        @on-select="onSelect"
        @on-row-click="onRowClick"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-selection-change="onSelectionChange">
        <category-select slot="category" @on-change="changeCategory"></category-select>
        <Select
          slot="commodity_type"
          v-model="filters.commodity_type"
          @on-change="loadGoods">
          <Option v-for="(type, index) in commodityType" :key="index" :value="type.value">{{type.label}}</Option>
        </Select>
      </list-table>
      <div slot="footer">
        <Button   @click="handleCancel">{{cancelText}}</Button>
        <Button type="primary"  @click="addConfirm">{{isAdd ? '批量添加' : '批量删除'}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import CategorySelect from '@components/common/categorySelect';
  import ListTable from '@components/common/list-table/list'
  const LIST_KEY = 'id';
  const commodityType = {
    added: {
      label: '已添加',
      value: 1,
    },
    add: {
      label: '未添加',
      value: 2,
    }
  };
  export default {
    name: "GoodsSelectModal",
    components: {
      CategorySelect,
      ListTable
    },
    props: {
      addGoodsOnly: {
        type: Boolean,
        default: false
      },
      show: {
        type: Boolean,
        default: false
      },
      /**
       * 是否显示多选
       */
      showSelect: {
        type: Boolean,
        default: true
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      closable: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: '请选择商品'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      // 默认选中的上商品
      addedGoods: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      columns: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      filterParams: {
        type: Object,
        default: () => {}
      },
      pageSizeOpts: {
        type: Array,
        default: () => [10, 20, 30, 40]
      }
    },
    watch: {
      show(newValue, oldValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.loadGoods();
        }
      }
    },
    computed: {
      isAdd() {
        return this.filters.commodity_type === commodityType.add.value;
      }
    },
    data() {
      return {
        modal: {
          show: false,
          className: 'vertical-center-modal coupon-goods-modal'
        },
        commodityType: commodityType,
        tableHeight: this.getTableHeight() * 0.8,
        filterItems: [
          {
            key: 'category',
            type: 'slot'
          },
          {
            key: 'commodity_type',
            type: 'slot'
          },
          {
            key: 'searchValue',
            placeholder: '请输入商品名称/编码进行搜索',
            showIcon: true,
          }
        ],
        filters: {
          commodity_type: commodityType.add.value,
          commodity_string: '',
          category_id: '',
          category_id2: '',
          showAll: '1',
          is_batch: '0',
        },
        goodsList: [],
        selectedRow: [],
        selectedGoods: [],
        cols: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          },
          {
            title: "商品名称",
            key: "name",
          },
          {
            title: "商品分类",
            key: "category_name",
          },
          {
            title: "单位",
            key: "unit",
          },
          {
            title: "描述",
            key: "summary",
          },
          {
            title: "最近一次进价",
            key: "in_price",
          },
          {
            title: "市场价",
            key: "price",
          },
          {
            title: "操作",
            width: 80,
            render: (h, params) => {
              let text = this.isAdd ? '添加' : '删除';
              let color = '#03ac54';
              let isSelected = this.isSelected(params.row);
              if (isSelected) {
                text = '-';
                color = 'inherit';
              }
              if (params.row.cantDelete && this.addGoodsOnly) {
                return '';
              }
              return h('span', {
                style: {
                  color: color,
                  cursor: 'pointer'
                },
                on: {
                  click: (evt) => {
                    let eventName = this.isAdd ? 'on-add' : 'on-delete';
                    evt.stopPropagation();
                    this.$emit(eventName, [params.row]);
                  }
                }
              }, text);
            }
          }
        ],
      }
    },
    created() {
      this.modal.show = this.show;
      if (!this.showFooter) {
        this.modal.className += ' hide-footer';
      }
      if (this.columns && this.columns.length > 0) {
        this.cols = this.columns;
      }
      if (this.show) {
        this.$nextTick(() => {
          this.$refs.list.loadListData();
        });
      }
    },
    methods: {
      setFilters() {
        // 未添加商品
        if (this.filters.commodity_type === commodityType.add.value) {
          this.filters.is_online = 'Y';
          this.filters.is_sell = '1';
        } else {
          this.filters.is_online = undefined;
          this.filters.is_sell = undefined;
        }
      },
      changeCategory(category) {
        this.filters.category_id = category[0];
        this.filters.category_id2 = category[1];
        this.loadGoods();
      },
      loadGoods() {
        this.setFilters();
        let selectedGoodsIdArr = this.addedGoods.map((goods) => goods[LIST_KEY]);
        this.filters.commodity_string = JSON.stringify(selectedGoodsIdArr);
        this.$refs.list.loadListData();
      },
      afterLoadList(list) {
        list.forEach((goods) => {
          if (this.isSelected(goods)) {
            goods._checked = true;
          }
          if (this.isCantDelete(goods) && this.addGoodsOnly) {
            goods.cantDelete = true;
            goods._disabled = true;
          }
        });
      },
      onSelectAll (selection) {
        selection.forEach((row) => {
          this.selectGoods(row);
        });
      },
      onSelect(selection, row) {
        this.selectGoods(row);
      },
      onSelectionChange (selection) {
        selection.forEach((goods) => {
          this.selectGoods(goods);
        });
        if (selection.length === 0) {
          this.$refs.list.list.forEach((item) => {
            this.cancelSelectGoods(item);
          });
        }
      },
      onSelectCancel(selection, row) {
        this.cancelSelectGoods(row);
      },
      onRowClick(row, index) {
        if (row._disabled && this.addGoodsOnly) {
          return;
        }
        if (this.isSelected(row)) {
          this.cancelSelectGoods(row);
          this.cancelCheckStatus(index);
        } else {
          this.selectGoods(row);
          this.setCheckStatus(index);
        }
      },
      isSelected(goods) {
        return this.selectedGoods.some((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]);
      },
      isCantDelete(goods) {
        let find = this.addedGoods.find((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]);
        if (find) {
          return find.cantDelete;
        }
        return false;
      },
      /**
       * 获取行索引
       */
      getRowIndex(row) {
        return this.$refs.list.list.findIndex((goods) => goods[LIST_KEY] === row[LIST_KEY]);
      },
      /**
       * 设置选中效果
       * @param index 选中行的索引
       */
      setCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 设置选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          !checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.add('ivu-checkbox-wrapper-checked');
          !checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.add('ivu-checkbox-checked');
        }
      },
      /**
       * 取消选中效果
       * @param index 取消选中行的索引
       */
      cancelCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 取消选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.remove('ivu-checkbox-wrapper-checked');
          checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.remove('ivu-checkbox-checked');
        }
      },
      /**
       * 将商品添加到选中商品中
       * @param row
       */
      selectGoods(row) {
        if (!this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.push(row);
        }
      },
      /**
       * 从选中商品中删除商品
       * @param row
       */
      cancelSelectGoods(row) {
        if (this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.splice(this.selectedGoods.findIndex((goods) => goods[LIST_KEY] === row[LIST_KEY]), 1);
        }
      },
      rowClassName (row, index) {
      },
      handleCancel() {
        this.$emit('on-cancel');
      },
      addConfirm() {
        if (this.selectedGoods.length === 0) {
          this.modalError('请选择商品', 0);
          return false;
        }
        if (this.isAdd) {
          this.$emit('on-add', this.selectedGoods);
        } else {
          this.$emit('on-delete', this.selectedGoods);
        }
        this.selectedGoods = [];
      }
    }
  }
</script>

<style lang="less">
  .coupon-goods-modal {
    .ivu-modal-body {
      padding-bottom: 0;
      .ivu-table-row {
        cursor: pointer;
      }
    }
  }
</style>
<style lang="less" scoped>
</style>
