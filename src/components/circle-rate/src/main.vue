<template>
  <span
    class="s-circle-rate"
    :style="{ height: size + 'px', lineHeight: size + 'px' }"
  >
    <svg :width="size" :height="size" :viewbox="`0 0 ${size} ${size}`">
      <circle
        :cx="halfSize"
        :cy="halfSize"
        :r="halfSize - width"
        :stroke-width="width"
        :stroke="color[0]"
        fill="none"
      ></circle>
      <circle
        :cx="halfSize"
        :cy="halfSize"
        :r="halfSize - width"
        :stroke-width="width"
        :stroke="color[1]"
        fill="none"
        :transform="`matrix(0,-1,1,0,0,${size})`"
        :stroke-dasharray="angle"
      ></circle>
    </svg>
  </span>
</template>

<script>
// @ is an alias to /src

export default {
  name: 's-circle-rate',
  props: {
    rate: {
      type: Number,
      default: 0
    },
    size: {
      type: Number,
      default: 16
    },
    width: {
      type: Number,
      default: 2
    },
    color: {
      type: Array,
      default() {
        return ['#E8E8E8', '#03AC54'];
      }
    }
  },
  data() {
    return {
      angle: null
    };
  },
  created() {
    this.angle = `0 ${Math.PI * 2 * (this.halfSize - this.width)}`;
  },
  watch: {
    rate: {
      immediate: true,
      handler() {
        const percent = this.rate / 100;
        const perimeter = Math.PI * 2 * (this.halfSize - this.width);
        this.$nextTick(() => {
          this.angle = perimeter * percent + ' ' + perimeter;
        });
      }
    }
  },
  computed: {
    halfSize() {
      return this.size / 2;
    }
  }
};
</script>

<style lang="less" scoped>
circle {
  transition: stroke-dasharray 0.8s;
}
</style>
