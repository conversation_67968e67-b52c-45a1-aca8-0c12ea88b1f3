<!--
 * @Author: lizhiwei😊
 * @Date: 2021-12-30 09:53:58
 * @LastEditors: lizhiwei
 * @Description: 订单标签select
-->

<template>
  <Select
    ref="select"
    v-bind="$attrs"
    v-model="selfValue"
    :placeholder="placeholder"
    @on-change="updateValue"
  >
    <Option :value="item.value" v-for="item in list" :key="item.value">{{
      item.label
    }}</Option>
  </Select>
</template>

<script>
import order from '@api/order.js'
export default {
  name: 'BaseSelectOrderTag',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    showAll: {
      type: Boolean,
      default: true
    },
    placeholder: {
      default: '选择订单标签'
    },
    params: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    value(newValue) {
      this.selfValue = newValue;
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      }
    },
    params: {
      deep: true,
      handler() {
        this.getList();
      }
    }
  },
  data() {
    return {
      selfValue: '',
      list: []
    };
  },
  created() {
    this.selfValue = this.value;
    this.getList();
  },
  methods: {
    getList () {
      order.qryOrderTagList().then(res => {
        const { status, data } = res
        if (!status) return
        let list = data.map(item => {
          return {
            label: item.name,
            value: item.id
          }
        })
        this.showAll && list.unshift({
          label: '全部',
          value: ''
        });
        this.list = list;
      })
    },
    setQuery(query) {
      this.$refs.select.setQuery(query);
    },
    updateValue() {
      this.$emit('input', this.selfValue);
      this.$emit('on-change', this.selfValue);
    },
    getSelectedItem() {
      return this.list.find(item => item.id === this.selfValue);
    }
  }
};
</script>

<style scoped></style>
