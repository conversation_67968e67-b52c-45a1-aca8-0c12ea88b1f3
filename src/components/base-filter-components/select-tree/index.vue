<!--
 * @Description:
 * @Date: 2023-06-16 14:14:55
 * @LastEditors: hgj
 * @LastEditTime: 2023-07-05 09:57:20
 * @FilePath: /sdpbase-pro/src/components/base-filter-components/select-tree/index.vue
-->
<template>
	<Select :value="showSelectedVal" placeholder="请选择" style="width:176px" :transfer="transfer" v-bind="$attrs">
		<Option
			v-for="item of list"
			:value="item.value"
			:key="item.value"
			style="display: none;"
		>
			{{ item.label }}
		</Option>
		<Tree
			:data="treeData"
			@on-check-change="handleCheckChange"
			show-checkbox
			multiple
		></Tree>
	</Select>
</template>

<script>
	import { mapState } from 'vuex';
	export default {
		name: 'select-tree',
		components: {},

		props: {
			data: {
				type: [Array],
				default: () => []
			},
			value: {
				type: [Array],
				default: () => []
			},
			placeholder: {
				type: String,
				default: '请选择商品分类'
			},
			labelKey: {
				type: String,
				default: 'name'
			},
			valueKey: {
				type: String,
				default: 'id'
      },
      transfer: {
        type: Boolean
      }
		},

		data() {
			return {
				treeData: [],
				list: [],
				selectedVal: [],
				showSelectedVal: '',
				showSelectedLabel: ''
			};
		},

		computed: {
			...mapState({
				sysConfig: 'sysConfig'
			})
		},

		watch: {},

		created() {
			this.getCateData();
		},

		mounted() {},

		methods: {
			async getCateData() {
				const initCateData = data => {
					if (+this.sysConfig.is_open_commodity_category_three === 1) {
						data.forEach(item => {
							if (item.items && item.items.length > 0) {
								item.items.forEach(item2 => {
									item2.items = item2.items || [];
									item2.items.unshift({
										id: item2.id + '-' + '0',
										name: '未设置',
										level: '3'
									});
								});
							}
						});
					}
					return data;
				};
				const convertToChildren = treeData => {
					return treeData.map(item => {
						const { id, name, level, items } = item;
						const children = items ? convertToChildren(items) : [];
						return {
							value: id,
							label: name,
							title: name,
							level,
							children,
							checked: false,
							expand: false
						};
					});
				};
				let { status, message, data } = await this.$request.post(
					this.apiUrl.getGoodsCategoryTree
				);

				if (status) {
					data = initCateData(data);
					console.log('cateData---origin', data);
					this.treeData = convertToChildren(data);
					console.log('cateData--tree', this.treeData);
				} else {
					this.errorMessage(message);
				}
			},
			convertDataByLevel(data) {
				let result = [
					[], // level 1
					[], // level 2
					[] // level 3
				];
				data.forEach(item => {
					if (item.level === '1') {
						result[0].push(item.value);
					} else if (item.level === '2') {
						result[1].push(item.value);
					} else if (item.level === '3') {
						result[2].push(item.value);
					}
				});
				result = result.map(item => item.join(','));
				return result;
			},
			handleCheckChange(value) {
				console.log('handleCheckChange--', value);
				this.selectedVal = this.convertDataByLevel(value);
				console.log('handleCheckChange--selectedVal', this.selectedVal);
				this.list = [];
				this.showSelectedVal = '';
				this.showSelectedLabel = '';
				value.map(item => {
					this.showSelectedVal += `${item.value},`;
					this.showSelectedLabel += `${item.label},`;
				});
				this.list.push({
					value: this.showSelectedVal,
					label: this.showSelectedLabel
				});
				this.$emit('on-change', this.selectedVal);
			},
			getValue() {
				return this.selectedVal;
			},

			resetValue(e) {
				this.showSelectedVal = '';
				this.showSelectedLabel = '';
				this.selectedVal = e.defaultValue || [];
				const resetTreeData = treeData => {
					return treeData.map(item => {
						// const { children } = item;
						// if(children.length > 0) {
						// 	item.children = resetTreeData(children);
						// }
						const children =
							item.children.length > 0 ? resetTreeData(item.children) : [];
						return {
							...item,
							children,
							checked: false,
							expand: false
						};
					});
				};
				this.treeData = resetTreeData(this.treeData);
				console.log('treeData', this.treeData);
			}
		}
	};
</script>
<style lang='less' scoped>
/deep/ .ivu-select-dropdown-list {
	padding-left: 5px;
}
/deep/ .ivu-tree-title {
	vertical-align: baseline;
}
/deep/ .ivu-tree-children {
	.ivu-checkbox-default {
		.ivu-checkbox-inner::after {
			top: 4px;
			left: 1px;
		}
	}
	.ivu-checkbox-checked {
		.ivu-checkbox-inner::after {
			top: 1px;
			left: 4px;
		}
	}
}
</style>
