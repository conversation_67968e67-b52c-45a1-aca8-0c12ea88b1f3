<!--
 * @Description:
 * @Date: 2023-07-05 09:34:08
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-10 19:09:34
 * @FilePath: /sdpbase-pro/src/components/base-filter-components/commodityCascader/index.vue
-->
<template>
  <div class="commodity-cascader">
    <div class="label s-filter__item-label" v-if="label">{{ label }}：</div>
    <SCascader
      v-if="!noMaxHeight"
      @change="selectedValChange"
      :treeData="treeData"
      v-model="selectedVal"
      :popper-class="popperClass"
    ></SCascader>
    <NoMaxSCascader
      v-else
      @change="selectedValChange"
      :treeData="treeData"
      v-model="selectedVal"
      :filterable="false"
      :popper-class="popperClass"
    >
    </NoMaxSCascader>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import SCascader from '@/components/cascader/index.vue';
import NoMaxSCascader from '@/components/cascader/noMaxIndex.vue';

export default {
  name: 'commodityCascader',
  components: {
    SCascader,
    NoMaxSCascader,
  },

  props: {
    label: {
      type: String,
      default: '商品分类',
    },
    value: {
      type: [Array],
      default: () => [],
    },
    placeholder: {
      type: String,
      default: '请选择商品分类',
    },
    labelKey: {
      type: String,
      default: 'name',
    },
    valueKey: {
      type: String,
      default: 'id',
    },
    // 默认true，保持以前的逻辑，现在有特殊地方用到，用于和v-model填充，此时要设为false
    formatValue: {
      type: Boolean,
      default: true,
    },
    // 是否需要大高度版本
    noMaxHeight: {
      type: Boolean,
      default: false,
    },
    // 是否需要三级筛选的数据
    needThreeLevel: {
      type: Boolean,
      default: true,
    },
    popperClass: String,
  },

  data() {
    return {
      treeData: [],
      selectedVal: [],
    };
  },

  computed: {
    ...mapState({
      sysConfig: 'sysConfig',
    }),
    hasThreeLevel() {
      return (
        +this.sysConfig.is_open_commodity_category_three === 1 &&
        this.needThreeLevel
      );
    },
  },

  watch: {
    value: {
      handler(newVal) {
        this.selectedVal = newVal;
      },
      deep: true,
      immediate: true,
    },
  },

  created() {
    this.getCateData();
  },

  mounted() {},

  methods: {
    async getCateData() {
      const initCateData = (data) => {
        data.forEach((item) => {
          if (item.items && item.items.length > 0) {
            item.items.forEach((item2) => {
              if (this.hasThreeLevel) {
                item2.items = item2.items || [];
                item2.items.unshift({
                  id: item2.id + '-' + '0',
                  name: '未设置',
                  level: '3',
                });
              } else {
                item2.items = null;
              }
            });
          }
        });
        return data;
      };
      const convertToChildren = (treeData) => {
        return treeData.map((item) => {
          const { id, name, level, items } = item;
          const children = items ? convertToChildren(items) : undefined;
          return {
            value: id,
            label: name,
            level,
            ...(children && { children }),
          };
        });
      };
      let { status, message, data } = await this.$request.post(
        this.apiUrl.getGoodsCategoryTree,
      );

      if (status) {
        data = initCateData(data);
        console.log('cateData---origin', data);
        this.treeData = convertToChildren(data);
        console.log('cateData--tree', this.treeData);
      } else {
        this.errorMessage(message);
      }
    },
    formatSelectedVal(data) {
      if (data.length <= 0) return ['', '', ''];
      let newArray = [[], [], []];

      for (var i = 0; i < data.length; i++) {
        var subArray = data[i];
        newArray[0].push(subArray[0]);
        newArray[1].push(subArray[1]);
        newArray[2].push(subArray[2]);
      }

      return newArray.map(function (subArray) {
        let uniqueItems = [...new Set(subArray)];
        return uniqueItems.join(',');
      });
    },
    selectedValChange(value) {
      this.selectedVal = value;
      if (this.formatValue) {
        const formatSelectedValue = this.formatSelectedVal(value);
        console.log('selectedValChange--formatValue', formatSelectedValue);
        this.$emit('input', this.selectedVal);
        this.$emit('on-change', formatSelectedValue);
      } else {
        console.log('selectedValChange--selectedVal', this.selectedVal);
        this.$emit('input', this.selectedVal);
        this.$emit('on-change', this.selectedVal);
      }
    },
    getValue() {
      return this.selectedVal;
    },
    resetValue(e) {
      this.selectedVal = e.defaultValue || [];
    },
  },
};
</script>
<style lang="less" scoped>
.commodity-cascader {
  display: flex;
  .label {
    color: rgba(0, 0, 0, 0.85);
    margin-right: 2px;
    display: flex;
    align-items: center;
  }
  /deep/ .el-input__inner {
    padding-left: 10px !important;
    // border: 1px solid red;
    &::placeholder {
      color: #909090;
      font-size: 13px;
    }
  }
}
</style>
