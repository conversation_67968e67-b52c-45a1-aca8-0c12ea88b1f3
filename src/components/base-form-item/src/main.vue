<template>
  <div :class="classes">
    <label
      :class="[prefixCls + '-label']"
      :for="labelFor"
      :style="labelStyles"
      v-if="label || $slots.label"
      ><slot name="label"
        >{{ label }}
        <Tooltip v-if="tip" max-width="300" :transfer="true" :content="tip">
          <s-icon style="margin-top: -1px" :size="12" icon="tips" /> <div v-if="slotContent" v-html="tip" slot="content"></div></Tooltip
        >{{ FormInstance.colon }}</slot
      ></label
    >
    <div :class="[prefixCls + '-content']" :style="contentStyles">
      <slot></slot>
      <transition name="fade">
        <div
          :class="[prefixCls + '-error-tip']"
          v-if="
            validateState === 'error' && showMessage && FormInstance.showMessage
          "
        >
          {{ validateMessage }}
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
// @ is an alias to /src
import { FormItem, Tooltip } from 'view-design';
import { SIcon } from '@sdp/ui';
export default {
  extends: FormItem,
  components: {
    SIcon,
    Tooltip
  },
  props: {
    tip: {
      type: String
    },
    slotContent: {
      type: Boolean,
      default: false
    }
  }
};
</script>
