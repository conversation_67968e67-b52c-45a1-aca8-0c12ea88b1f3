import DecimalSetting from './src/main';
import { floor, round } from 'lodash-es';


// 格式化精度
export const formatPrecision = (value, mode = LOGIC_ROUND, precision) => {
  if (Number(mode) === Number(LOGIC_ROUND)) {
    return round(value, precision);
  } else {
    return floor(value, precision);
  }
}

// 四舍五入
export const LOGIC_ROUND = '1';

// 向下取整
export const LOGIC_FLOOR = '2';

export default DecimalSetting;