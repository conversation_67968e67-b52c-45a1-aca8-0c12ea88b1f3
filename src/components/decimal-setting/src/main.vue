<template>
  <Modal title="设置" v-model="show">
    <Form label-colon :label-width="120">
      <FormItem label="小数位数">
        <RadioGroup v-model="bit">
          <Radio label="2">2位小数</Radio>
          <Radio label="1">1位小数</Radio>
          <Radio label="0">整数</Radio>
        </RadioGroup>
      </FormItem>
      <FormItem label="取值逻辑" tip="仅对系统通过公式计算得来的数值生效，若手动填写字段则不生效取值逻辑设置">
        <RadioGroup v-model="logic">
          <Radio :label="LOGIC_ROUND">四舍五入</Radio>
          <Radio :label="LOGIC_FLOOR">
            向下取整
            <Tooltip
              :transfer="true"
              :delay="0"
              :maxWidth="246"
              content="当数值计算后的位数大于设定的位数时，把超出的位数做抹零处理"
              placement="top">
              <SIcon style="position: relative; top: -1px;" icon="tips" :size="12"/>
            </Tooltip>
          </Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button type="text" @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleOk">确定</Button>
    </div>
  </Modal>
</template>
<script>
import { LOGIC_ROUND, LOGIC_FLOOR } from '../index';
import FormItem from '@components/base-form-item';
import SIcon from '@components/icon';
export default {
  name: 'DecimalSetting',
  components: {
    FormItem,
    SIcon
  },
  data() {
    return {
      show: false,
      bit: '',
      logic: '',
      LOGIC_FLOOR,
      LOGIC_ROUND
    };
  },
  methods: {
    handleOk() {
      if (this.confirmResolve) {
        this.confirmResolve({
          bit: this.bit,
          logic: this.logic,
        });
      }
      this.handleCancel();
    },
    open({
      bit,
      logic,
    }) {
      this.bit = bit;
      this.logic = logic;
      this.show = true;
      return new Promise(resolve => {
        this.confirmResolve = resolve;
      });
    },
    handleCancel() {
      this.bit = '';
      this.logic = '';
      this.show = false;
    },
  }
}
</script>