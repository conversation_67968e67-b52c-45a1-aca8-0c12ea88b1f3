# Modal 组件

模态弹框，会在屏幕中间位置显示，引导用户进行相关操作

## 使用说明

有模版生成弹框和命令生成两种模式，模版生成适合更多个性化需求的场景

## 示例

::: demo

```html
<template>
  <div class="demo">
    <s-modal ref="modal" title="你好" @ok="ok" @quit="quit">
      <div>大家好才是真的好</div>
    </s-modal>
    <s-button @click="clickHandle">命令模式</s-button>
    <s-button type="primary" @click="showModal">自定义模版模式</s-button>
  </div>
</template>
<script>
  export default {
    methods: {
      showModal() {
        this.$refs.modal.open();
      },
      ok() {
        alert('你点击了ok');
      },
      quit() {
        alert('你点击了quit');
      },
      clickHandle() {
        this.$smodal({
          title: '给你看个大宝贝',
          text: '是不是很大',
          type: 'info',
          btns: 2,
          okTxt: '一秒后消失',
          quitTxt: '取消',
          onOk(resolve) {
            setTimeout(() => {
              resolve();
            }, 1000);
          },
          onQuit() {},
          async: true,
          mask: false,
        });
      },
    },
  };
</script>
```

:::

## API

| 参数    | 说明                                                                      | 类型    | 默认值 | 可选值                     |
| ------- | ------------------------------------------------------------------------- | ------- | ------ | -------------------------- |
| title   | 设置标题                                                                  | string  | -      | -                          |
| text    | 设置文字，可用标签                                                        | string  | -      | -                          |
| type    | 设置类型，设置此选，将会出现对应 icon                                     | string  | -      | info/success/error/warning |
| btns    | 按钮数量，可设置为 0,最多 2 个                                            | number  | 2      | 0/1/2                      |
| okTxt   | 确定操作的文案                                                            | string  | 确定   | -                          |
| quitTxt | 取消操作的文案                                                            | string  | 取消   | -                          |
| async   | 是否异步事件，开启后在 ok 事件上会返回一个 resolve 参数，主动调用继续操作 | boolean | false  | true/false                 |
| mask    | 是否出现蒙版                                     | boolean | true   | true/false                 |
| @ok     | 点击确定时触发 ok 事件                                                    | event   | -      | -                          |
| @quit   | 点击取消时触发 quit 事件                                                  | event   | -      | -                          |
