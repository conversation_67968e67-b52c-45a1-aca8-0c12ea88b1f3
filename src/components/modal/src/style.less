@modal-prefix: s-modal-new;

.@{modal-prefix} {
  &__mask {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1000;
  }
  &__content {
    padding: 20px;
    position: fixed;
    top: 35%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 500px;
    overflow-y: auto;
    width: 360px;
    background: #fff;
    box-shadow: 0px 2px 20px 1px rgba(15, 33, 27, 0.15);
    border-radius: 4px;
    z-index: 1001;
  }
  &__body {
    display: flex;
  }
  &__icon {
    width: 24px;
    .s-icon, .sui-icon {
      vertical-align: top;
      line-height: 16px;
      font-size: 16px;
      &.info {
        color: #0879ff;
      }
      &.success {
        color: var(--primary-message-text);
      }
      &.error {
        color: #f13130;
      }
      &.warning {
        color: #ff9f00;
      }
    }
  }
  &__info {
    flex: 1;
  }
  &__title {
    font-size: 14px;
    line-height: 16px;
    margin: 0  0 10px 0;
  }
  &__text {
    font-size: 13px;
    line-height: 20px;
    margin: 0  0 24px 0;
    text-align: justify;
  }
  &__action {
    display: flex;
    justify-content: space-between; 
  }
  &__checkbox__box {
    // margin-left: -4px;
  }
  &__btn__ok{
    margin-left: 12px;
  }
}
