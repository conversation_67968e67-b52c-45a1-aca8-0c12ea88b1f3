<template>
  <div class="s-modal-new" v-show="show">
    <div class="s-modal-new__mask" v-show="mask"></div>
    <div class="s-modal-new__content">
      <div class="s-modal-new__body">
        <div class="s-modal-new__icon" v-if="type">
          <s-icon :icon="typeMap[type]" :class="type" />
        </div>
        <div class="s-modal-new__info">
          <h5 class="s-modal-new__title" v-if="title">{{ title }}</h5>
          <p class="s-modal-new__text" v-html="text" v-if="text"></p>
          <div class="s-modal-new__slot">
            <slot></slot>
          </div>
        </div>
      </div>
      <slot name="footer"
        ><FooterRender
          :renderFn="_renderFooter"
          v-if="renderFooter"
        ></FooterRender
      ></slot>
      <div class="s-modal-new__action" v-if="btns && !renderFooter">
        <div class="s-modal-new__checkbox__box">
          <slot name="footer-left"></slot>
        </div>
        <div class="s-modal-new__btn__box">
          <s-button @click="quit" v-if="btns > 1">{{ quitTxt }}</s-button>
          <s-button class="s-modal-new__btn__ok" @click="ok" type="primary">{{
            okTxt
          }}</s-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SButton from '../../button';
import SIcon from '../../s-icon';

const FooterRender = {
  props: ['renderFn'],
  render(h) {
    if (typeof this.renderFn === 'function') {
      return this.renderFn(h);
    } else {
      return h('div');
    }
  },
};

export default {
  name: 's-modal-new',
  components: { SButton, SIcon, FooterRender },
  props: {
    renderFooter: {
      type: Function,
      required: false,
    },
    btns: {
      type: Number,
      default: 2,
    },
    type: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
    text: {
      type: String,
      default: '',
    },
    okTxt: {
      type: String,
      default: '确定',
    },
    quitTxt: {
      type: String,
      default: '取消',
    },
    mode: {
      type: String,
      default: 'template',
    },
    async: {
      type: Boolean,
      default: false,
    },
    mask: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      show: false,
      typeMap: {
        info: 'solid-notice',
        success: 'solid-success',
        error: 'solid-close',
        warning: 'solid-notice',
      },
    };
  },
  methods: {
    _renderFooter(h) {
      if (this.renderFooter) {
        return this.renderFooter.call(this, h);
      } else {
        return '';
      }
    },
    open() {
      this.show = true;
    },
    close() {
      this.$emit('close');
      if (this.mode === 'template') {
        this.show = false;
      } else {
        this.$el.remove();
      }
    },
    cb(fn, emit) {
      if (this.async) {
        return new Promise((resolve) => {
          this.$emit(emit, resolve);
          typeof fn === 'function' && fn(resolve);
        }).then(() => {
          this.close();
        });
      } else {
        this.$emit(emit);
        typeof fn === 'function' && fn();
        this.close();
      }
    },
    quit() {
      this.$emit('quit');
      this.onQuit && this.onQuit();
      this.close();
    },
    ok() {
      this.cb(this.onOk, 'ok');
    },
  },
};
</script>
<style lang="less" scoped>
.s-modal-new__mask {
  z-index: 9999;
}
.s-modal-new__content {
  z-index: 10000;
}
/deep/.ivu-checkbox-wrapper {
  display: flex;
  align-items: center;
  .ivu-checkbox {
    margin-right: 8px;
  }
}
</style>
