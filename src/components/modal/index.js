import SModal from './src/main.vue';
import './src/style.less';
export { SModal };

let instance = null;
export default {
  install(Vue, options) {
    Vue.prototype.$closeSmodal = () => {
      const Modal = Vue.extend(SModal);
      instance && instance.$el.remove();
    }

    Vue.prototype.$smodal= opts => {
      const Modal = Vue.extend(SModal);
      instance && instance.$el.remove();
      instance = new Modal({
        el: document.createElement('div')
      });
      opts = Object.assign({}, opts, options);
      for (let key in opts) {
        instance[key] = opts[key];
      }
      instance.mode = 'cmd';
      document.body.appendChild(instance.$el);
      instance.open();
      return instance;
    };
  }
};
