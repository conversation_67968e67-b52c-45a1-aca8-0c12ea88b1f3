<!--
 * @Author: <PERSON>
 * @Date: 2022-02-15 19:32:02
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-03-02 20:34:13
 * @Description: 子机构级联选择器
-->
<template>
  <Cascader
    ref="cascader"
    v-model="selfValue"
    clearable
    :disabled="disabled"
    :data="data"
    :load-data="_loadData"
    :placeholder="placeholder"
    :change-on-select="changeOnSelect"
    :renderFormat="renderFormat"
    :class="{ 'ivu-cascader-not-found': data.length === 0 }"
    @on-change="_updateValue"
  ></Cascader>
</template>

<script>
export default {
  name: 'SuborganizationCascader',
  props: {
    value: {
      type: String,
      default: ''
    },
    // 是否动态加载
    dynamicLoading: {
      type: Boolean,
      default: false
    },
    // 客户id
    userId: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '选择子机构'
    }
  },
  watch: {
    userId () {
      if (!this.dynamicLoading) {
        this._getData()
      }
    },
    value: {
      immediate: true,
      handler (newValue) {
        this._setSelectValue(newValue)
      }
    }
  },
  data () {
    return {
      selfValue: [],
      data: [],
      changeOnSelect: false
    }
  },
  created () {
    this._getData()
  },
  methods: {
    /**
     * @description: 获取筛选项列表数据 (动态加载时只获取第一级)
     */
    async _getData () {
      if (this.dynamicLoading) { // 用于列表页筛选项时, 动态加载数据
        this.changeOnSelect = true // 动态加载时使用changeOnSelect
        let { data } = await this.$request.get(this.apiUrl.subsidiary.list, { pid: 0 })
        data = data.map(item => {
        let child=  { label: item.name, value: item.id, level: item.level, children:  [] }
        if(item.is_has_children==0){
          delete child.children
        }else{
          child.loading=false
        }
          return child
        })
        data.unshift({ label: '全部', value: '' })
        this.data = data
      } else { // 用于新增编辑页时，获取全部树形数据
        if (this.userId) {
          const { status, data } = await this.$request.get(this.apiUrl.subsidiary.tree, { user_id: this.userId })
          if (status && data && data.length) {
            this.DFS(data[0])
            this.data = data[0].children || []
            this._setSelectValue(this.value)
          } else {
            this.data = []
          }
        }
      }
    },
    /**
     * @description: 动态获取数据，数据源需标识 loading
     * 若按客户id获取列表，则直接获取客户下所有子机构的树形数据
     * 若用于列表筛选，则根据子机构id, 动态加载每一级的子机构列表
     * @param {object} item 当前点击选择的项
     * @param {function} callback 回调，如果执行，则会自动展开当前项的子列表
     */
    async _loadData (item, callback) {
      if (this.dynamicLoading && item.loading !== undefined) {
        item.loading = true
        const { data } = await this.$request.get(this.apiUrl.subsidiary.list, { pid: item.value })
        if (data && data.length) {
          item.loading = false
          item.children = data.map(subitem => {
            const isLastLevel = +subitem.level >= 3 // 子机构目前最多三级，省去确定为最后一级时的请求
            let child =  {
              label: subitem.name,
              value: subitem.id,
              level: subitem.level,
              ...isLastLevel ? {} : {
                children: []
              }
            }
            if(subitem.is_has_children==0){
              delete child.children
            }else{
              child.loading= false
            }
            return child
          })
          callback()
        } else {
          delete item.loading
          // callback()
          // 源码可能还不够完善，callback回调中只有children有值时才会触发handleTriggerItem
          // 但源码中的Caspanel为递归组件，这里直接调用造成内部代码this指向有问题，无法正确地拿到当前的labels
          // 因此搭配renderFormat及内部使用的__label属性使用
          this.$refs.cascader.$refs.caspanel.handleTriggerItem(item)
        }
      }
    },
    renderFormat (labels, selectedData) {
      const index = labels.length - 1
      const data = selectedData[index] || false
      return data ? data.__label || data.label : labels[index] // 回显按所有层级显示的label
    },
    // /**
    //  * @description: 递归处理获取的客户下所有子机构树形数据
    //  * @param {Array} data
    //  */
    // _getRecursionData (data = []) {
    //   return data.map(item => {
    //     return {
    //       label: item.name,
    //       value: item.id,
    //       level: item.level,
    //       ...item.children ? { children: this._getRecursionData(item.children) } : {}
    //     }
    //   })
    // },
    /**
     * @description: 深度优先搜索 Depth-First-Search
     * @param {object} node 树的根节点
     */
    DFS (node) {
      const stack = []
      if (node) {
        stack.push(node)
        while (stack.length) {
          const item = stack.pop()
          item.label = item.name
          item.value = item.id
          const children = item.children || []
          children.forEach(childItem => stack.push(childItem))
        }
      }
    },
    _setSelectValue (value) {
      if (!value) {
        this.selfValue = []
        this._updateValue('')
        return
      }
      const stack = []
      if (this.data && this.data.length) {
        this.data.forEach(item => stack.push(item))
        while (stack.length) {
          const item = stack.pop()
          if (item.id === value) {
            this.selfValue = [...item.path.split(',').slice(1), value]
            return
          }
          const children = item.children || []
          children.forEach(childItem => stack.push(childItem))
        }
      }
    },
    _updateValue (value) {
      const selectId = (Array.isArray(value) ? value.slice(-1)[0] : value) || ''
      this.$emit('input', selectId)
      this.$emit('on-change', selectId)
    }
  }
}
</script>

<style lang="less" scoped>
.ivu-cascader {
  width: 100%; // 配合.ivu-cascader-not-found类名，使无数据时的下拉框样式与组件同宽
}
// /deep/ .ivu-cascader-not-found-tip {
//   min-width: 232px;
// }
</style>
