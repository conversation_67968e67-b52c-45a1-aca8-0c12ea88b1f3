<template>
  <DatePicker
    ref="date"
    :transfer="true"
    :value="value"
    :placeholder="placeholder"
    :type="type"
    :options="options"
    @on-change="onChange"
    transfer></DatePicker>
</template>
<script>
  export default {
  	autoRegister: true,
    name: "CommonDatePicker",
    props: {
      value: {
        type: [String, Array],
        default: ''
      },
      meta: {
        type: [String, Array, Object],
        default: ''
      },
      type: {
        type: String,
        default: ''
      },
      placeholder: {
        type: String,
        default: ''
      },
      options: {
        type: Object,
        default: () => {}
      },
    },
    data() {
      return {
      }
    },
    created() {
    },
    methods: {
      onChange(date) {
        this.$emit('on-change', date, this.meta);
      },
      handleClear () {
      	this.$refs.date.handleClear();
      }
    }
  };
</script>
<style scoped lang="less">
</style>
