<template>
  <Cascader
    :filterable="filterable"
    :clearable="clearable"
    :transfer="transfer"
    :value="showValue"
    @on-change="updateValue"
    :data="standardData"
    :placeholder="placeholder"
    :disabled="disabled">
  </Cascader>
</template>

<script>
  import standard from "@util/standard";
  export default {
    name: "CommonCascader",
    props: {
      // 是否将弹层放置于 body 内，在 Tabs、带有 fixed 的 Table 列内使用时，建议添加此属性，它将不受父级样式影响，从而达到更好的效果
      transfer: {
        type: Boolean,
        default: false
      },
      value: {
        default: () => [],
      },
      clearable: {
        type: Boolean,
        default: true
      },
      filterable: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      labelKey: {
        type: String,
        default: 'label'
      },
      valueKey: {
        type: String,
        default: 'value'
      },
      childrenKey: {
        type: String,
        default: 'children'
      },
      data: {
        type: Array,
        default: () => [],
      },
      placeholder: {
        default: '请选择'
      }
    },
    data() {
      return {
        showValue: [],
        standardData: []
      }
    },
    watch: {
      value (newValue) {
        this.showValue = newValue;
      },
      data(newValue) {
        this.standardData = standard.transformCascaderData(newValue, this.labelKey, this.valueKey, this.childrenKey);
      }
    },
    created() {
    	this.showValue = this.value;
    	this.standardData = standard.transformCascaderData(this.data, this.labelKey, this.valueKey, this.childrenKey);
    },
    methods: {
      updateValue(value, selectedData) {
        this.$emit('on-change', value, selectedData);
      }
    }
  }
</script>

<style scoped>

</style>
