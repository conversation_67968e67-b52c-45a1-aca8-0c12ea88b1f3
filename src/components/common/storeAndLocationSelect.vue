<template>
  <div id="category-select">
    <Cascader
      v-bind="$attrs"
      v-model="selfValue"
      :data="categoryList"
      :change-on-select="changeOnSelect"
      clearable
      filterable
      :placeholder="placeholder"
      @on-change="changeCategory"
      style="width: 100%"
    >
    </Cascader>
  </div>
</template>

<script>
import storeRoom from '@api/storeRoom.js';
export default {
  name: 'StoreAndLocationSelect',
  autoRegister: true,
  props: {
    storeId: {
      required: true,
      type: String,
      default: '',
    },
    value: {
      type: [Array, String],
      default: () => [],
    },
    changeOnSelect: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      default: '请选择库区库位',
    },
    formatData: {
      type: Function,
      default: (data) => {
        return data;
      }
    }
  },
  watch: {
    value(newValue) {
      if (newValue[0] === '' && newValue[1] === '') {
        this.selfValue = [];
      } else {
        this.selfValue = newValue;
      }
    },
    storeId() {
      this.getStoreAreaList();
    },
  },
  data() {
    return {
      selectedItem: null,
      selfValue: [],
      categoryList: [],
      OriginCategoryList: [],
    };
  },
  created() {
    this.getStoreAreaList();
  },
  methods: {
    // 获取库区库位
    getStoreAreaList() {
      if (!this.storeId) {
        this.categoryList = [];
        return;
      }
      const params = {
        store_id: this.storeId,
      };
      storeRoom.getReservoirList(params).then((res) => {
        const { status, data } = res;
        if (status) {
          this.categoryList = this.formatData(data);
          this.selectedData = [];
          this.selfValue = [];
          this.$emit('input', ['', '']);
          this.$emit('on-change', ['', ''], [{}, {}]);
        }
      });
    },
    changeCategory(value, selectedData) {
      this.$emit('input', value);
      this.$emit('on-change', value, selectedData);
      this.selectedItem = selectedData;
    },
  },
};
</script>

<style scoped></style>
