<template>
  <div class="export-task-wrap">
    <div class="modal-mask" @click="closeModal" v-if="show"></div>
    <transition name="panelFade">
      <div class="selectColsPanel export-task" v-if="show">
        <div class="panelHeader">
          <div class="title">导出列表</div>
          <div class="close" @click="closeModal">
            <Icon type="android-close"></Icon>
          </div>
        </div>
        <div class="panelBody">
          <div class="content">
            <p
              class="no-data"
              style="text-align: center"
              v-if="!list || list.length === 0"
            >
              暂无导出任务
            </p>
            <Row
              type="flex"
              align="top"
              v-for="(item, index) in list"
              class="download-item"
              :class="item.is_read == status.download ? 'light-gray' : ''"
              :key="index"
            >
              <Col :span="1" class="status">
                <Icon
                  type="record"
                  color="#03ac54"
                  v-if="item.is_read != status.download"
                ></Icon>
              </Col>
              <Col style="flex: 1" class="download-item-name">
                <span>{{ item.contents }}</span>
                <span :style="{ color: item.status ? '' : 'red' }">{{
                  item.msg
                }}</span>
                <p class="light-gray task-time">{{ item.operate_time }}</p>
              </Col>
              <Col
                v-if="parseInt(item.status) === exportStatus.process"
                style="flex: 1"
                align="left"
              >
                <Icon size="24" type="ios-loading" class="icon-load"></Icon>
              </Col>
              <Col :span="1" class="download-wrap">
                <Icon
                  type="ios-cloud-download-outline"
                  @click.native="download(item)"
                  v-if="parseInt(item.status) === exportStatus.complete"
                ></Icon>
              </Col>
            </Row>
          </div>
        </div>
        <div class="panelFooter">
          <p class="tips">导出文件有效期24小时，过期自动删除</p>
          <p class="clear" @click="clearTask">清空已完成任务</p>
        </div>
      </div>
    </transition>
    <div class="export-task-btn" @click="openModal">
      任务
    </div>
  </div>
</template>

<script>
import Bus from '@api/bus.js';
const status = {
  download: 2 // 已下载
};
const exportStatus = {
  process: 2,
  complete: 1
};
export default {
  name: 'ExportTask',
  autoRegister: true,
  created() {
    if (this.show) {
      this.getData();
    }
    Bus.$on('refresh-export-task',this.getData);
  },
  beforeDestroy () {
    Bus.$off('refresh-export-task',this.getData);
  },
  computed: {
    show() {
      let show = this.$store.state.showExportTask;
      if (show) {
        this.getData();
      }
      return this.$store.state.showExportTask;
    }
  },
  data() {
    return {
      exportStatus,
      status,
      list: []
    };
  },
  methods: {
    closeModal() {
      this.$store.commit('showExportTask', false);
      this.$emit('on-close');
    },
    openModal() {
      this.$store.commit('showExportTask', true);
    },
    clearTask() {
      this.$Modal.confirm({
        content: '确定清空已完成任务？',
        onOk: () => {
          this.$request.get(this.apiUrl.exportTaskClear).then(res => {
            let { status, message } = res;
            if (status) {
              this.modalSuccess('清空成功');
              this.getData();
            } else {
              this.modalError(message);
            }
          });
        }
      });
    },
    download(item) {
      this.$request
        .get(this.apiUrl.exportDownload, { id: item.id })
        .then(res => {
          let { status, message, data } = res;
          if (status) {
            this.getData();
            location.href = data;
          } else {
            this.modalError(message);
          }
        });
    },
    getData() {
      this.$request.get(this.apiUrl.exportTask, { pageSize: 999 }).then(res => {
        let { status, data } = res;
        if (status) {
          this.list = data.list;
        } else {
          this.list = [];
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
@z-index: 1030;
@gutter: 15px;
@width: 420px;
.export-task-wrap {
  font-size: 14px;
  line-height: 18px;
  text-align: left;
  .panelHeader {
    position: fixed;
    top: 0;
    right: 0;
    width: @width;
    background: #03ac54;
    color: #fff;
    z-index: @z-index;
  }
  .export-task {
    width: @width;
    .panelBody {
      z-index: @z-index;
      padding: 0;
      height: 100%;
      overflow: hidden;
      .content {
        height: 100%;
        overflow-y: auto;
        position: relative;
        .download-item {
          margin-bottom: 15px;
          .status {
            .ivu-icon {
              font-size: 12px;
            }
          }
          .download-item-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-right: @gutter;
            &:hover {
              white-space: normal;
            }
          }
          .download-wrap {
            text-align: right;
            cursor: pointer;
            font-size: 18px;
          }
        }
        padding: 55px 10px 100px;
        .no-data {
          text-align: center;
          color: #aeaeae;
          position: absolute;
          width: 100%;
          top: 50%;
          transform: translateY(-50%);
        }
        .task-time {
          font-size: 13px;
        }
      }
    }
    .panelFooter {
      z-index: @z-index;
      width: @width;
      text-align: center;
      background: #fff;
      bottom: 0;
      padding-top: @gutter;
      .tips {
        color: #aeaeae;
      }
      .clear {
        cursor: pointer;
        padding: @gutter 0;
      }
    }
  }
  .export-task-btn {
    cursor: pointer;
    position: fixed;
    z-index: 256;
    right: 0;
    bottom: 40px;
    width: 36px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: rgba(31, 36, 46, 0.8);
    border-radius: 2px 0px 0px 2px;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
  }
}
.light-gray {
  color: #aeaeae;
}
.icon-load {
  animation: ani-circle 1s linear infinite;
}
@keyframes ani-circle {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
