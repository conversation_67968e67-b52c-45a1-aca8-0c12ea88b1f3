<template>
  <div class="cross-day-delivery-date-tips" v-if="show">
    注意：开启跨天运营时段默认当日送货后，客户在跨天运营时段的次日时间内下单，指定送货日期为当天。
  </div>
</template>

<script>
  import common from '@api/main.js';
  export default {
    name: "crossDayDeliveryDateTips",
    data () {
      return {
        show: false,
      }
    },
    created() {
      this.getConfig();
    },
    methods: {
      getConfig() {
        common.getConfig().then((config) => {
          this.show = parseInt(config.multi_operation_cross_day_default_today) === 1;
        });
      }
    }
  }
</script>

<style scoped>
  .cross-day-delivery-date-tips {
    color: red;
    font-size: 12px;
  }
</style>
