<template>
  <div>
    <span class="CategorySelectForMultiple-label">一级分类：</span>
    <Select placeholder="请选择一级分类" :filterable="true" class="control" @on-change="selectCategory1" multiple v-model="category1" :max-tag-count="1">
      <Option v-for="item in categoryList" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>
    <div v-if="twoLine" style="height: 10px;" ></div>
    <span class="CategorySelectForMultiple-label" :class="{last: !twoLine}">二级分类：</span>
    <Select placeholder="请选择二级分类" :filterable="true" @on-change="selectCategory2" class="control" v-model="category2" multiple :max-tag-count="1">
      <Option v-for="item in subCategoryList" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>
  </div>
</template>
<script>
import goods from '@api/goods.js';
export default {
  name: 'CategorySelectForMultiple',
  data () {
    return {
      categoryList: [],
      subCategoryList: [],
      category1: [],
      category2: [],
      doNotClearSubCategoryList: false
    }
  },
  props: {
    value: {
      type: Array,
      default: () => ['', '']
    },
    // 是否分两行展示
    twoLine: {
      type: Boolean,
      default: false
    }
  },
  created () {
    this.category1 = this.value[0].split(',')
    this.category2 = this.value[1].split(',')
    if (this.value[1]) {
      this.doNotClearSubCategoryList = true;
    }
    this.getSearchConfig();
  },
  methods: {
    getSearchConfig() {
      this.$request.get(this.apiUrl.batchSummary.searchConfig).then(res => {
        let { status, data } = res;
        if (status) {
          this.categoryList = data.category.map(item => {
            return {
              value: item.id,
              label: item.name,
            };
          });
        }
      });
    },
    async selectCategory1 (item) {
      if (item.length) {
        let res = await goods.getGoodsCategory(item.join(','));
        this.subCategoryList = res.data.map(item => {
          return {
            value: item.id,
            label: item.name
          };
        });
      } else {
        this.subCategoryList = [];
      }
      if (this.doNotClearSubCategoryList) {
        this.doNotClearSubCategoryList = false;
      } else {
        // 如果删除了一级分类，剔除该一级分类下的二级分类
        let category2 = this.deepClone(this.category2);
        let result = [];
        category2.forEach(id => {
          if (this.subCategoryList.find(item => item.value === id)) {
            result.push(id);
          }
        })
        this.category2 = result;
      }

      this.$emit('input', [this.category1.join(','), this.category2.join(',')]);
      this.$emit('on-change', [this.category1.join(','), this.category2.join(',')]);
    },
    selectCategory2 () {
      this.$emit('input', [this.category1.join(','), this.category2.join(',')]);
      this.$emit('on-change', [this.category1.join(','), this.category2.join(',')]);
    },
    // 重置
    resetValue () {
      this.category1 = [];
      this.category2 = [];
    }
  },
}
</script>
<style lang="less" scoped>
.CategorySelectForMultiple-label {
  width: 65px;
  color: rgba(0, 0, 0, 0.85);
  margin-right: -2px;
  display: inline-block;
  text-align: right;
}
.control {
  width:232px;
  line-height: 30px;
  display: inline-block;
}
.last {
  margin-left: 33px;
}
/deep/ .ivu-select-input {
  top: 0px;
  padding-left: 10px;
}
/deep/ .ivu-select-multiple .ivu-select-selection {
  height: 30px;
  .ivu-tag-checked {
    margin-top: -2px;
  }
}
</style>
