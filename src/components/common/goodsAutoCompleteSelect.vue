<template>
  <div class="wrap goods-search" :class="{ icon: icon }">
    <RemoteSelect
      ref="goodsInput"
      clearable
      filterable
      remote
      v-model="goodsId"
      :transfer="transfer"
      :placeholder="placeholder"
      :remote-method="remoteSearch"
      :loading="selectLoading"
      :placement="placement"
      @on-query-change="onQueryChange"
      @on-change="updateValue"
    >
      <Option
        v-for="(option, index) in remoteList"
        :value="option[valueKey]"
        :label="option[labelKey]"
        :key="index"
      >
        <p>
          <span
            class="tag-protocol-price"
            v-if="showProtocol && isProtocolGoods(option)"
            >协</span
          >
          {{ option[labelKey] }} {{ option.unit }}
          <span v-if="option.summary">({{ option.summary }})</span>
        </p>
        <p style="color: #aaa">{{ option.commodity_code }}</p>
      </Option>
    </RemoteSelect>
    <Icon
      :type="icon"
      v-if="icon"
      class="icon"
      @click.native="handleClick"
    ></Icon>
    <slot></slot>
  </div>
</template>

<script>
const GOODS_KEY = 'commodity_id';
import Goods from '@api/goods.js';
export default {
  name: 'goodsAutoCompleteSelect',
  props: {
    value: {
      default: 0
    },
    transfer: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '请选商品名/助记码/编码/别名/关键字'
    },
    icon: {
      type: String,
      default: ''
    },
    labelKey: {
      type: String,
      default: 'commodity_name'
    },
    valueKey: {
      type: String,
      default: 'commodity_id'
    },
    filters: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: '' // 搜索api地址
    },
    // 在商品编码后添加已配置的商品税率，编辑税率页面专用配置
    showTax: {
      type: Boolean,
      default: false
    },
    taxData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    //搜索框是否根据商品协议价显示'协字'
    showProtocol: {
      type: Boolean,
      default: false
    },
    // 需要隐藏的商品
    needHideGoods: {
      type: Array,
      default: () => {
        return [];
      }
    },
    // 弹窗的展开方向，可选值为 top、bottom、top-start、bottom-start、top-end、bottom-end
    placement: {
      type: String,
      default: 'bottom-start'
    }
  },
  data() {
    return {
      defaultApi: '',
      goodsId: '',
      remoteList: [],
      lastChange: '',
      selectLoading: false
    };
  },
  methods: {
    onQueryChange() {
      this.$emit('on-query-change');
    },
    remoteSearch(query) {
      query = query.trim();
      if (query !== '') {
        if (this.selectLoading) {
          this.lastChange = query;
          return false;
        }
        this.selectLoading = true;
        let api = this.api || this.apiUrl.searchGoods;
        this.$request.get(api, { query, ...this.filters }).then(res => {
          let { status, data } = res;
          this.selectLoading = false;
          if (status) {
            this.remoteList = [];
            data.commodities.map(item => {
              let find = this.needHideGoods.find(
                goods => goods.id === item.commodity_id
              );
              if (!find) {
                this.remoteList.push(item);
              }
            });
          } else {
            this.remoteList = [];
          }
          // 在商品编码后添加已配置的商品税率
          if (this.showTax) {
            this.remoteList.map(item => {
              let find = this.taxData.find(element => {
                return element.foreign_id === item.commodity_id;
              });
              if (find) {
                item.commodity_code += ` [${find.tax_rate}%]`;
              }
            });
          }
          if (this.lastChange) {
            this.remoteSearch(this.lastChange);
            this.lastChange = '';
          }
        });
      } else {
        this.goodsId = '';
        this.remoteList = [];
      }
    },
    setQuery(query) {
      if (!query) {
        this.$refs.goodsInput.clearSingleSelect();
      } else {
        this.$refs.goodsInput.setQuery(query);
      }
    },
    updateValue() {
      let selectedGoods = this.remoteList.find(
        findGoods => findGoods[GOODS_KEY] === this.goodsId
      );
      this.$emit('input', this.goodsId);
      this.$emit('on-change', selectedGoods);
    },
    handleClick() {
      this.$emit('on-click');
    },
    isProtocolGoods(goodsInfo) {
      return Goods.isProtocolGoods(goodsInfo);
    }
  }
};
</script>

<style lang="less">
.goods-search {
  &.icon {
    .ivu-icon:not(.icon) {
      right: 30px !important;
    }
  }
}
</style>
<style lang="less" scoped>
.wrap {
  display: inline-block;
  width: 100%;
  position: relative;
  .icon {
    position: absolute;
    font-size: 20px;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    cursor: pointer;
  }
}
</style>
