<template>
  <Modal
    title="可合并采购单列表"
    v-model="showModal"
    @on-cancel="handleCancel"
    :mask-closable="false"
    :closable="!merging"
    width="1000"
    class-name="vertical-center-modal"
  >
    <div
      :style="{
        height: 'auto',
        maxHeight: getTableHeight() + 'px',
        overflowY: 'auto',
        overflowX: 'hidden'
      }"
    >
      <Row
        :gutter="50"
        class="merge-item"
        v-for="(item, index) in mergeList"
        :key="index"
        align="middle"
        type="flex"
      >
        <Col>
          <p>单号：{{ item.main_no }}</p>
          <p>采购模式：{{ item.purchase_type_name }}</p>
          <p>采购员：{{ item.agent_name || item.provider_name }}</p>
        </Col>
        <Col :span="4" style="font-size: 16px" class="text-green">
          <i
            v-if="!merging && !error && !done"
            class="iconfont sdpicon-jiantou2"
          ></i>
          <i
            v-if="merging"
            class="iconfont sdpicon-jiazai loading"
            style="display: inline-block"
          ></i>
          <span v-if="done && !error">已完成</span>
          <span style="color: red; font-size: 14px" v-if="done && error">{{
            error
          }}</span>
        </Col>
        <Col>
          <p>单号：{{ item.other_no[0] }}</p>
          <p>采购模式：{{ item.other_purchase_type_name }}</p>
          <p>采购员：{{ item.agent_name || item.provider_name }}</p>
        </Col>
      </Row>
    </div>
    <div slot="footer">
      <Button v-if="!merging" @click="handleCancel">取消</Button>
      <Button v-if="done && !error" type="primary" @click="handleCancel"
        >确定({{ countDown }})</Button
      >
      <Button
        v-if="!done || this.error"
        :disabled="merging"
        type="primary"
        @click="mergeOrder"
        >{{ merging ? '合并中...' : '确定合并' }}</Button
      >
    </div>
  </Modal>
</template>
<script>
import Purchase from '@api/purchase';
export default {
  name: 'PurchaseOrderMergeCheck',
  props: {},
  data() {
    return {
      showModal: false,
      merging: false,
      done: false,
      error: '',
      timer: 0,
      countDown: 0,
      mergeList: []
    };
  },
  created() {
    this.getOrders();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  watch: {},
  methods: {
    handleCancel() {
      this.showModal = false;
    },
    mergeOrder() {
      if (this.merging) {
        return false;
      }
      this.merging = true;
      let mergeList = this.mergeList.map(item =>
        [item.main_no, item.other_no[0]].join(',')
      );
      Purchase.mergeOrder(mergeList).then(res => {
        let { status, message } = res;
        this.done = true;
        this.merging = false;
        if (status) {
          this.$emit('on-success');
          this.setCountDown();
        } else {
          this.error = message || '合并失败';
        }
      });
    },
    setCountDown() {
      this.countDown = 5;
      this.timer = setInterval(() => {
        if (this.countDown) {
          this.countDown--;
        } else {
          this.handleCancel();
        }
      }, 1000);
    },
    getOrders() {
      let params = {};
      this.$request
        .get(this.apiUrl.purchaseOrder.getMergeOrders)
        .then(params)
        .then(res => {
          let { status, data } = res;
          if (status) {
            this.mergeList = data;
            console.log('this.mergeListthis.mergeList', this.mergeList);
            if (data && data.length) {
              this.showModal = true;
            }
          }
          // if (!data || data.length === 0) {
          //   this.showModal = false;
          // }
          // } else {
          //   this.showModal = false;
          // }
        });
    }
  }
};
</script>

<style lang="less" scoped>
.merge-item {
  margin-bottom: 10px;
  p {
    margin-bottom: 5px;
  }
  .iconfont {
    font-size: 30px;
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.loading {
  animation: rotate 2s linear infinite;
}
</style>
