<template>
  <Select
    :transfer="true"
    :multiple="multiple"
    :disabled="disabled"
    :filterable="filterable"
    :clearable="filterable"
    :show-all="showAll"
    v-model="providerId"
    @on-change="updateValue"
    :placeholder="placeholder"
    v-bind="$attrs"
  >
    <Option :value="item[valueKey]" :label="item.name" v-for="item in list" :key="item.id">
      {{item.name}}
      <div class="code" v-if="showCode">{{ item.provider_code }}</div>
    </Option>
  </Select>
</template>

<script>
import goods from '@api/goods.js';
export default {
  name: 'storeSelect',
  props: {
    valueKey: {
      default: 'id',
      type: String
    },
    value: {
      default: ''
    },
    data: {
      type: Array,
      default: () => []
    },
    showCode: {
      type: Boolean,
      default: false
    },
    remote: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: <PERSON>olean,
      default: true
    },
    filterable: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      default: '全部'
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    value(newValue) {
      this.providerId = newValue;
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      }
    }
  },
  data() {
    return {
      providerId: '',
      list: []
    };
  },
  created() {
    this.providerId = this.value;
    if (this.multiple) {
      this.providerId = this.providerId || [];
    }
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data;
    }
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        id: '',
        name: '全部'
      };
      goods.getPurchaseType(this.params).then(res => {
        if (res.status) {
          this.list = res.data.providers;
        }
        if (this.showAll) {
          this.list.splice(0, 0, defaultItem);
        }
      });
    },
    updateValue() {
      this.$emit('input', this.providerId);
      // let currentProvider = this.list.find(item => item.id === this.providerId);
      this.$emit('on-change', this.providerId);
    }
  }
};
</script>

<style scoped>
.code {
  font-size: 13px;
  color: #aaa;
}
</style>
