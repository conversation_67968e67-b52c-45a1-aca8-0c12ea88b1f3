<template>
    <div>
        <Modal width="1100"
                class-name="vertical-center-modal"
                v-model="showOnlineImgList"
                :title="title"
                :transfer="true"
                @on-cancel="handleCancel"
                @on-ok="handleOk">
            <div class="img-list-search">
                <Select @on-change='changeCategory1' clearable placeholder="一级分类" style="width: 130px">
                    <Option v-for='list in categoryData1' :key='list.id' :value="list.id">{{list.name}}</Option>
                </Select>
                <Select v-if='queryparams.category_id' @on-change='changeCategory2' clearable placeholder="二级分类" style="width: 130px">
                    <Option v-for='list in categoryData2' :key='list.id' :value="list.id">{{list.name}}</Option>
                </Select>
                <Input @on-enter='query' v-model='queryparams.name' placeholder="请输入要搜索的商品名称" style="width: 275px">
                    <Button @click='query' slot="append" icon="ios-search"></Button>
                </Input>
            </div>
            <div style="margin-top: 10px;">
                <table class="form-table fixed-thead" cellpadding="0" cellspacing="0">
                     <tr>
                         <th width="200px">商品名称</th>
                         <th width="120px">主图图片</th>
                         <th>详情图图片</th>
                     </tr>
                  </table>
                <!--<Table :columns="tbCol" :data="tbList" width="1070" height="400" border></Table>-->
                <div class="scroll-box">
                    <table class="form-table" cellpadding="0" cellspacing="0">
                        <tr v-for="com in tbList">
                            <td width="200px">{{com.name}}</td>
                            <td width="120px">
                                <div class="demo-upload-list">
                                    <img :src="com.pic_url"/>
                                    <div class="demo-upload-list-cover">
                                        <span @click="exportImg(com.pic)">导入图片</span>
                                    </div>
                                </div>
                            </td>
                            <td style="display: flex;">
                                <div class="demo-upload-list" v-for="detailImg in com.detail_arr" style="margin: 0 10px">
                                    <img :src="detailImg.detail_url"/>
                                    <div class="demo-upload-list-cover">
                                        <a @click="exportImg(detailImg)">导入图片</a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="clear">
                    <Page class="fr" :total="+pageParams.count" :current.sync="+pageParams.page" :page-size="+pageParams.page_size" @on-change="changePage"
                          @on-page-size-change="changePageSize" placement="top" show-elevator show-total show-sizer></Page>
                </div>
            </div>
        </Modal>
    </div>
</template>

<script>
    import { api } from '@api/api';
    import { get, post } from '@/api/request';
    import cloudToken from '@/components/mixin/cloudToken.js';
    export default {
        name: "onlineImgList",
        props: {
            showOnlineImgList: Boolean,
        },
        mixins: [cloudToken],
        data() {
            return {
                title: '在线图库',
                content: '',
                cancelText: '取消',
                categoryData1: [],
                categoryData2: [],
                //查询参数
                queryparams: {
                    name: '',
                    category_id: '',
                    category_id2: '',
                },
                //分页
                pageParams: {
                    page: 1,
                    page_size: 10,
                    count: 0
                },
                tbList:[],

            }
        },
        watch: {
            showOnlineImgList(val) {
                if (val) {
                    this.getCloudToken().then(res => {
                        this.getCategory1();
                        if(res == 1) {
                            this.getTbList();
                        }
                    });
                }
            }
        },
        methods:{
            handleOk() {
                this.$emit('closeOnlineImgList', false);
            },
            handleCancel() {
                this.$emit('closeOnlineImgList', false);
            },
            changeCategory1(value) {
                console.log(value)
                this.pageParams.page = 1;
                this.queryparams.category_id = value;
                this.getCategory2(value);
                this.getTbList();
            },
            changeCategory2(value) {
                this.pageParams.page = 1;
                this.queryparams.category_id2 = value;
                this.getTbList();
            },
            getCategory1() {
                get(api.cloudCategory1, {cloud_token: sessionStorage.getItem('cloudToken')}).then(res => {
                    if (res.success) {
                        this.categoryData1 = res.result;
                    }
                });
            },
            getCategory2(id) {
                get(api.cloudCategory2, { id: id ,cloud_token: sessionStorage.getItem('cloudToken')}).then(res => {
                    if (res.success) {
                        this.categoryData2 = res.result;
                    }
                });
            },
            query() {
                this.pageParams.page = 1;
                this.getTbList();
            },
            changePage(pageNo) {
                this.pageParams.page = pageNo;
                this.getTbList();
            },
            changePageSize(size) {
                this.pageParams.page_size = size;
                this.getTbList();
            },
            //表格列表
            getTbList() {
                this.tbList = [];

                let params = Object.assign({}, this.pageParams, this.queryparams);
                params.cloud_token = sessionStorage.getItem('cloudToken');
                get(api.cloudGoodsList, params).then(res => {
                    if (res.success) {
                        this.tbList = res.result.list;
                        this.pageParams = res.result.pageParams;
                        // this.selectRows = this.uniqueArr(this.selectRows);
                        // this.setDisableAndSelected(this.tbList);
                    } else {
                        this.modalError(res.message);
                    }
                });
            },
            exportImg(img){
                this.$emit('exportImg',img);
            }
        }
    }
</script>

<style scoped>
    .img-list-search{
        text-align: left;
    }
    table th{
        height: 58px;
        white-space: nowrap;
        overflow: hidden;
        background-color: #f3f5f3;
    }
    .form-table{
        width: 100%;
        margin: 0 auto;
        text-align: center;
        table-layout: fixed;
    }
    .form-table th{
        background: #f3f5f3;
    }
    .form-table td{
        border-bottom: 1px solid #f0f2f0;
    }
    .fixed-thead tr th{
        height: 50px;
        text-align: left;
        padding-left: 40px;
    }
    .scroll-box{
        width: 100%;
        height: 420px;
        overflow: auto;
        overflow-x:hidden;
    }
    .scroll-box tr{
        width: 100%;
        height: 40px;
        line-height: 20px;
    }
    .scroll-box tr td{
        padding: 5px;
    }
    .demo-upload-list-cover {
        display: none;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, .6);
        color: white;
    }
    .demo-upload-list {
        display: inline-block;
        width: 80px;
        height: 80px;
        text-align: center;
        line-height: 80px;
        border: 1px solid transparent;
        border-radius: 4px;
        overflow: hidden;
        background: #fff;
        position: relative;
        box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
        margin-right: 4px;
    }
    .demo-upload-list:hover .demo-upload-list-cover {
        display: block;
    }
</style>
