<template>
  <Modal
    v-model="showModal"
    :title="title"
    :width="650"
    @on-cancel="onCancel"
  >
    <slot name="header"></slot>
    <div class="pl24 pr24">
      <div class="title">选择导出字段</div>
      <CheckboxGroup v-model="selectedColumns">
        <Row type="flex">
          <Col
            :key="value"
            :span="6"
            v-for="(label, value) in allColumns">
          <Checkbox
            :disabled="defaultColumns.includes(value)"
            :label="value">
            {{label}}
          </Checkbox>
          </Col>
        </Row>
      </CheckboxGroup>
    </div>
    <slot name="extra-fields"></slot>
    <div slot="footer" class="btn-list">
      <Button @click="onCancel">关闭</Button>
      <Button @click="toggleSelectAll">{{allSelected ? '取消全选' : '全选'}}</Button>
      <ExportButton
        :download="download"
        text="确认导出"
        type="primary"
        :api="api"
        :offline="offline"
        :param-getter="getExportParams"
        @on-click="confirmExport"
        @on-success="onCancel"
      />
    </div>
  </Modal>
</template>

<script>
export default {
  name: "ExportExcel",
  props: {
    title: {
      type: String,
      default: '导出'
    },
    show: {
      type: Boolean,
      default: false
    },
    api: {
      type: String,
      default: ''
    },
    params: {
      type: Object,
      default: () => {}
    },
    storeKey: {
      type: String,
      default: ''
    },
    fieldsType: {
      type: String,
      default: ''
    },
    handleParams: {
      type: Function,
    },
    offline: {
      type: Boolean,
      default: true
    },
    // 直接下载 window.location
    download: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    show (show) {
      if (show) {
        this.init();
        this.getFields();
      } else this.clearCols();
      this.showModal = show;
    },
  },
  computed: {
    allSelected () {
      return this.selectedColumns.length === Object.keys(this.allColumns).length;
    },
  },
  data() {
    return {
      showModal: false,
      selfParams: {},
      allColumns: [],
      selectedColumns: [],
      defaultColumns: [],
    }
  },
  created () {
    this.showModal = this.show;
  },
  methods: {
    init () {
      this.selfParams = { ...this.params };
    },
    clearCols () {
      this.allColumns = [];
      this.selectedColumns = [];
      this.defaultColumns = [];
    },
    confirmExport () {
      if (this.storeKey) this.storage.setLocalStorage(this.storeKey, this.selectedColumns);
    },
    toggleSelectAll () {
      if (this.allSelected) this.selectedColumns = this.defaultColumns;
      else this.selectedColumns = Object.keys(this.allColumns);
    },
    getFields () {
      this.$request.get(this.apiUrl.getFields, { type: this.fieldsType }).then(res => {
        let { status, data } = res;
        if (status) {
          this.allColumns = data.all_column;
          this.selectedColumns = (this.storeKey && this.storage.getLocalStorage(this.storeKey)) || data.selected_column;
          this.defaultColumns = data.default_column;
        } else this.clearCols()
      });
    },
    getExportParams () {
      const excludeColumns = Object.keys(this.allColumns).filter(column => !this.selectedColumns.includes(column));
      this.selfParams.hide_columns = JSON.stringify(excludeColumns);
      let selfParams = this.deepClone(this.selfParams);
      if (this.handleParams) {
        selfParams = this.handleParams(selfParams);
      }
      return selfParams;
    },
    onCancel () {
      this.$emit('on-cancel');
    },
  }
}
</script>

<style lang="less" scoped>
  .title {
    font-size: 14px;
    color: #222127;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .btn-list {
    .ivu-btn {
      margin-left: 10px;
    }
  }
  /deep/ .ivu-modal .ivu-modal-body {
    padding: 20px 0 30px;
  }
</style>
