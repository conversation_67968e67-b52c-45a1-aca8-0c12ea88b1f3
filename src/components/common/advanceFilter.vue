<template>
  <div class="advance-filter">
    <slot></slot>
    <transition name="dropdown-fade">
      <div class="advancedPanel" v-show="showAdvanceFilter">
        <Form style="text-align: left" :label-width="labelWidth">
          <slot name="content">
            <FormItem :label="row.label" v-for="(row, index) in selfFilterConfig.rows" :key="index">
              <Row type="flex" class="filter" align="middle" style="margin-bottom: 15px" :gutter="10">
                <Col span="24">
                <template v-for="(filterItem, i) in row.cols">
                  <Select
                    filterable
                    clearable
                    v-model="advanceFilters[filterItem.key]"
                    v-if="filterItem.type === 'select'"
                    :key="i"
                    :style="filterItem.style"
                    style="width: auto"
                    :placeholder="filterItem.placeholder">
                    <Option v-for="option in filterItem.data" :key="option.value" :value="option.value">{{option.label}}</Option>
                  </Select>
                  <DatePicker type="date" v-if="filterItem.type === 'date'" @on-change="handleAdvanceDateChange" :key="i" :placeholder="filterItem.placeholder" :style="filterItem.style"></DatePicker>
                  <DatePicker type="daterange" v-if="filterItem.type === 'daterange'" @on-change="handleAdvanceDateRangeChange" :key="i" :placeholder="filterItem.placeholder" :style="filterItem.style"></DatePicker>
                  <Input
                    style="width: 220px"
                    v-model="advanceFilters[filterItem.key]"
                    v-if="!filterItem.type || filterItem.type === 'input'"
                    :key="i"
                    :style="filterItem.style"
                    :placeholder="filterItem.placeholder"
                    @keydown.enter.native.stop.prevent="handleEnter"
                  />
                  <CheckboxGroup v-model="advanceFilters[filterItem.key]" v-if="filterItem.type === 'checkboxGroup'" :key="i">
                    <Checkbox :label="checkBox.value" v-for="(checkBox) in filterItem.items" :key="index + checkBox.value">
                      <span>{{checkBox.label}}</span>
                    </Checkbox>
                  </CheckboxGroup>
                  <RadioGroup v-if="filterItem.type === 'radioGroup'" v-model="advanceFilters[filterItem.key]" :key="i">
                    <Radio :label="item.value" :key="index + item.value" v-for="item in filterItem.items">{{item.label}}</Radio>
                  </RadioGroup>
                  <slot :name="filterItem.key" v-if="filterItem.type === 'slot'"></slot>
                </template>
                </Col>
              </Row>
            </FormItem>
          </slot>
          <FormItem label="">
            <Button @click="reset">重 置</Button>
            <Button type="primary" @click="search">搜 索</Button>
          </FormItem>
        </Form>
        <p class="ishide" @click="close">收 起 <Icon type="chevron-up" size="16"></Icon></p>
      </div>
    </transition>
  </div>
</template>
<script>
  export default {
    name: "advanceFilter",
    loading: false,
    props: {
      // label宽度，单位px
      show: {
        type: Boolean,
        default: false
      },
      labelWidth: {
        type: [Number],
        default: 80
      },
      filterConfig: {
        type: Object,
        default: () => {
          return {
            dataSource: {},
            /**
             * label: '',
             * cols: [
             *   {
             *     type: '',
             *     key: ''
             *   }
             * ]
             */
            rows: []
          }
        }
      },
    },
    data() {
      return {
        showAdvanceFilter: true,
        selfFilterConfig: {
          rows: [],
          dataSource: [],
        },
        advanceFilters: {}
      }
    },
    watch: {
      filterConfig: {
        deep: true,
        handler(newValue,) {
          this.selfFilterConfig = newValue;
          this.initFilterData();
        }
      },
      show(newValue) {
        this.showAdvanceFilter = newValue;
      }
    },
    created() {
      this.showAdvanceFilter = this.show;
      this.selfFilterConfig = this.filterConfig;
      this.initFilterData();
    },
    methods: {
      initFilterData () {
        let filtersKeys = Object.keys(this.advanceFilters);
        if (!this.selfFilterConfig || !this.selfFilterConfig.rows) {
          return false;
        }
        this.selfFilterConfig.rows.map((row) => {
          if (!row.cols) {
            return false;
          }
          row.cols.map((filterCol) => {
            let filterKey = filterCol.key;
            // 设置响应式数据
            if (!filtersKeys.includes(filterKey)) {
              // 区间日期单独处理
              if (filterCol.type === 'daterange') {
                this.$set(this.advanceFilters, filterCol.keyArr[0], '');
                this.$set(this.advanceFilters, filterCol.keyArr[1], '');
              } else {
                this.$set(this.advanceFilters, filterKey, '');
              }
            }
            // 初始化筛选条件默认值
            this.advanceFilters[filterKey] = filterCol.default !== undefined ? filterCol.default : '';
            if (filterCol.type === 'checkboxGroup' && !Array.isArray(filterCol.default)) {
              this.advanceFilters[filterKey] = [];
            }
          });
        });
      },
      getFilters() {
        return this.advanceFilters;
      },
      close() {
        this.$emit('on-close');
      },
      reset() {
        this.initFilterData();
        this.$emit('on-reset', this.advanceFilters);
        this.close();
      },
      search() {
        this.$emit('on-search', this.advanceFilters);
        this.close();
      },
      handleEnter () {
        this.$emit('on-search', this.advanceFilters);
      }
    }
  };
</script>
<style scoped lang="less">
  .advance-filter {
    .filter {
      .ivu-select {
        width: auto;
      }
      .ivu-input-group {
        width: 240px;
      }
      .advancedSearch {
        float: none;
        background: transparent;
      }
    }
    .advancedPanel {
      .ivu-form-item {
        margin-bottom: 0;
      }
    }
  }
</style>
