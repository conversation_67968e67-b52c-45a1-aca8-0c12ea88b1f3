<template>
  <div class="check-project-expire">
    <Modal :width="450" :mask-closable="false" :closable="false"  v-model="showExpire">
      <div class="renew_title_box">
       <s-icon icon="solid-notice" class="renew_tip_icon" />
          <h4 class="renew_title">温馨提示</h4>
      </div>
      <p style="font-size: 15px;" class="renew_content">
          {{showText}}
      </p>
      <div slot="footer" style="text-align: right">
       <div style="cursor:not-allowed;display: inline-block;"> <Button :class="[canClick?'renew_confirm':'renew_confirm_no']" style="width:120px" type="success" @click="knowThis">我知道了 <span v-if="!canClick">({{waitTime}}s)</span></Button></div>
      </div>
    </Modal>
  </div>
</template>

<script>
import Bus from "@api/bus";
import { isCanReminder, showReminder } from './util';
export default {
  name: 'CheckProjectExpire',
  watch: {
    $route() {
     this.initShow()
    }
  },
  data() {
    return {
      callBack:null,
      waitTime:10,
      canClick:false,
      showText:'尊敬的客户您好，检测到您的系统还有维护年费尚未支付，为了避免影响您的正常使用，请尽快联系客户经理支付完剩余费用。如有疑问可随时联系蔬东坡客户经理（王经理：13317454343），感谢您的支持。',
      showExpire: false
    };
  },
  beforeDestroy() {
    Bus.$off("on-renew-countdown");
  },
  created() {
    this.initShow(true)
    Bus.$on('on-renew-countdown', this.showTip);
  },
  methods: {
    initShow(first){
         if(['/index', '/indexNew'].includes(this.$route.path)){
          if(first){
               setTimeout(()=>{
             this.showTip(this.$store.state.sysConfig.expiration_reminder)
            },1000)
          }else{
            this.showTip(this.$store.state.sysConfig.expiration_reminder)
          }
         }
    },
    countDownStart(){
         this.canClick= false
          this.showExpire = true
          this.countDown(60)
    },
    knowThis(){
      this.showExpire=false
      if(typeof this.callBack == 'function'){
        this.callBack()
      }
    },
      showTip(a,callBack){
          // this.$request.get(this.apiUrl.common.checkTodoList).then(res => {
          // let { data } = res;
          // 不通过接口获取
          let has_tail_payment = this.$store.state.sysConfig.expiration_reminder;
          if(has_tail_payment==0){
          if(typeof callBack == 'function'){
             callBack()
          }
            return
          }
          if(has_tail_payment=='1'){
              this.showText='尊敬的客户您好，检测到您的系统还有维护年费尚未支付，为了避免影响您的正常使用，请尽快联系客户经理支付完剩余费用。如有疑问可随时联系蔬东坡24小时售后热线4006 888 750，感谢您的理解与支持。'
          }else if((has_tail_payment=='2')||(has_tail_payment=='3')){
              this.showText='尊敬的客户您好，检测到您的系统还有尾款费用尚未支付，为了避免影响您的正常使用，请尽快联系客户经理支付完剩余费用。如有疑问可随时联系蔬东坡客户经理，感谢您的支持。'
          }else{
            return
          }
          this.callBack=callBack
          this.countDownStart()
          // })
      },
     countDown(time) {
        this.waitTime = time
        let _this = this
        if (this.waitTime == 0) {
            this.canClick= true
            this.waitTime = 60;// 恢复计时
        } else {
            setTimeout(function () {
                _this.waitTime--
                _this.countDown(_this.waitTime)// 关键处-定时循环调用
            }, 1000)
        }
    },
    checkProjectExpire() {
      const hasRoute = {
        '/unique-login': true,
        '/login': true,
        '/auto-login': true,
        '/auth-login': true,
      }
      if (hasRoute[this.$route.path]) {
        return;
      }
      this.$request.get(this.apiUrl.common.checkProjectExpire).then(res => {
        let { data } = res;
        let { in_90, is_expire, expire_time, is_admin } = data;
        const willExpire = Number(in_90) === 1;
        const expired = Number(is_expire) === 1;
        const isAdmin = Number(is_admin) === 1;
        let noticeName = 'project_expire';
        if (expired) {
          this.showText='尊敬的客户您好，检测到您的系统还有维护年费用尚未支付，为了避免影响您的正常使用，请尽快联系客户经理支付完剩余费用。如有疑问可随时联系蔬东坡客户经理（王经理：13317454343），感谢您的支持。'
          this.showExpire = true;
          return false;
        }
        // 无需续费提醒
        if (!willExpire) {
          return false;
        }
        // 无需续费提醒
        if (!willExpire) {
          return false;
        }
        // 即将到期的当前已经提醒过的不再提醒，非管理员不提醒
        if (!isCanReminder() || !isAdmin) {
          return false;
        }
        showReminder();
        this.$Notice.error({
          name: noticeName,
          duration: 0,
          render: h => {
            return h(
              'div',
              {
                style: {
                  fontSize: '16px',
                  color: 'red',
                  lineHeight: '24px'
                }
              },
              [
                h(
                  'p',
                  {
                    style: {
                      position: 'relative',
                      top: '-2px',
                      fontWeight: 'bolder',
                      textAlign: 'center'
                    }
                  },
                  '系统即将到期'
                ),
                h('span', '尊敬的客户，您的系统将于'),
                h(
                  'span',
                  {
                    style: {
                      fontWeight: 'bolder'
                    }
                  },
                  expire_time
                ),
                h('span', '到期，请及时联系客户经理或售后人员进行续费。')
              ]
            );
          }
        });
      });
    }
  }
};
</script>

<style lang="less" scoped>
.renew_title_box{
  height: 50px;
  display: flex;
  align-items: center;
  .renew_tip_icon{
    color:#f59b1e;
    font-size: 26px;
  }
  .renew_title{
    font-size: 16px;
    text-indent: 1em;
  }
}
.renew_content{
  color:#606060;
}
.renew_confirm{

}
.renew_confirm_no{
  color:#fff;
  background: #abacac;
  border-color:#abacac !important;
  pointer-events: none;
  cursor: not-allowed;
}

</style>
