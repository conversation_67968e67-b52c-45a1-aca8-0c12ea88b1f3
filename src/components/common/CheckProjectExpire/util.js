/**
 * Created by ddcoder on 2019/9/26.
 */
import storage from '../../../util/storage';
import DateUtil from '../../../util/date'

const STORAGE_KEY = 'renew_reminder';

/**
 * 是否可以进行续费提醒
 * @returns {boolean}
 */
const isCanReminder = () => {
  // 今天没有提醒过
  return storage.getLocalStorage(STORAGE_KEY) !== DateUtil.getTodayDate();
};

/**
 * 设置当前提醒日期
 */
const showReminder = () => {
  storage.setLocalStorage(STORAGE_KEY, DateUtil.getTodayDate());
};

export {
  isCanReminder,
  showReminder
}
