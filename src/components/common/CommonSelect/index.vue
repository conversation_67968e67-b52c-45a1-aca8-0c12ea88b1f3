<template>
  <Select
    :transfer="true"
    v-model="selfValue"
    :disabled="disabled"
    :filterable="filterable"
    :clearable="clearable"
    :placeholder="placeholder"
  >
    <Option :value="item[valueKey]" :key="item[valueKey]" v-for="item in list">
      <slot :item="item">{{ item[labelKey] }}</slot>
    </Option>
  </Select>
</template>

<script>
import SelectMixin from '../../../mixins/select';
export default {
  name: 'CommonSelect',
  autoRegister: true,
  mixins: [SelectMixin],
  props: {
    // 控制是否初始化数据
    shouldInitData: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      list: [],
    };
  },

  watch: {
    value(newValue) {
      this.selfValue = newValue;
    },
    data: {
      deep: true,
      handler(newValue) {
        if (!this.remote) {
          this.setList(newValue);
        }
      },
    },
    selfValue: {
      deep: true,
      handler() {
        this.updateValue();
      },
    },
    shouldInitData(val) {
      if (val) {
        this.getList();
      }
    },
  },
  created() {
    this.selfValue = this.value;
    this.list = this.data;
    if (this.remote && this.shouldInitData) {
      this.getList();
    }
  },
  methods: {
    setList(list) {
      this.list = list;
      if (this.selectFirst && list && list.length > 0) {
        this.selfValue = list[0][this.valueKey];
      }
    },
    getList() {
      if (!this.url || (this.data && this.data.length > 0)) {
        return false;
      }
      this.list = [];
      let defaultItem = {
        [this.valueKey]: this.allValue,
        [this.labelKey]: this.allLabel,
      };
      let params = {
        pageSize: 99999,
        ...this.params,
      };
      this.$request.get(this.url, params).then(
        (res) => {
          let { data, status } = res;
          let list = [];
          if (status) {
            list = Array.isArray(data) ? data : data.list;
          }
          if (this.showAll) {
            list.unshift(defaultItem);
          }

          if (this.filterHandle) {
            list = list.filter(this.filterHandle);
          }
          this.setList(list);
        },
        () => {
          this.list = [];
        },
      );
    },
    updateValue() {
      let currentItem = this.list.find(
        (item) => item[this.valueKey] === this.selfValue,
      );
      this.$emit('input', this.selfValue);
      this.$emit('on-change', currentItem);
    },
  },
};
</script>

<style scoped></style>
