<template>
  <Select
    ref="sorter"
    v-model="sorterId"
    @on-change="updateValue"
    :disabled="disabled"
    :filterable="filterable"
    :clearable="true"
    :placeholder="placeholder">
    <Option value="" v-if="showAll">全部</Option>
    <Option :value="item.id" :key="index" v-for="(item, index) in list">{{item.username}}</Option>
  </Select>
</template>

<script>
  import sort from '@api/sort.js'
  export default {
    name: "sorterSelect",
    props: {
      value: {
        default: ''
      },
      placeholder: {
        default: '选择分拣员'
      },
      // 默认选中第一个
      defaultFirst: {
        type: Boolean,
        default: false
      },
      showAll: {
        type: Boolean,
        default: false
      },
      filterable: {
        type: Boolean,
        default: true
      },
      disabled: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        loading: false,
        sorterId: '',
        list: [],
      }
    },
    created() {
      this.getList();
    },
    watch: {
      value (val) {
        this.sorterId = val;
      }
    },
    methods: {
      getList() {
        this.loading = true;
        this.list = [];
        const allItem = {
          id: '',
          username: '全部仓库',
        };
        sort.getSorter().then((res) => {
          this.loading = false;
          if (res.status) {
            this.list = res.data.sorter_list;
            if (this.defaultFirst === true) {
              this.sorterId = this.list[0].id;
              this.updateValue()
            }
          } else {
            this.list = [];
          }
          if (this.showAll) {
            this.list.unshift(allItem);
          }
        });
      },
      updateValue() {
        this.$emit('input', this.sorterId);
        this.$emit('on-change', this.sorterId);
      }
    }
  }
</script>

<style scoped>

</style>
