<template>
  <Select
    :filterable="filterable"
    :clearable="clearable"
    v-model="agentsId"
    @on-change="updateValue"
    :placeholder="placeholder"
  >
    <Option :value="item.id" v-for="item in list" :key="item.id">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
import goods from '@api/goods.js';
export default {
  name: 'purchaseAgentSelect',
  props: {
    value: {
      default: ''
    },
    filterable: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      default: '选择采购员'
    }
  },
  data() {
    return {
      agentsId: '',
      list: []
    };
  },
  created () {
    this.getList();
  },
  activated() {
    this.getList();
  },
  deactivated(){
    this.list = [];
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        id: '',
        name: '全部采购员'
      };
      goods.getPurchaseType().then(res => {
        if (res.status) {
          this.list = res.data.agents;
        }
        this.list.splice(0, 0, defaultItem);
      });
    },
    updateValue() {
      this.$emit('input', this.agentsId);
      this.$emit('on-change', this.agentsId);
    }
  }
};
</script>

<style scoped></style>
