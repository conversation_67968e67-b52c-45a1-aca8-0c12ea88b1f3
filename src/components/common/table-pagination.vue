<template>
  <div class="pagination-table">
    <Row class="header">
      <slot name="head"></slot>
      <slot name="before-filter"></slot>
      <slot name="filter">
        <div class="filter-wrap" v-show="showFilter">
          <Row type="flex" class="filter" align="middle" :gutter="10">
            <slot name="filter-content"></slot>
            <Col style="text-align: left" v-show="showFilterContent">
              <template
                v-for="(filterItem, index) in selfFilterConfig.filterCols"
              >
                <Select
                  :key="index"
                  :filterable="
                    filterItem.filterable !== undefined
                      ? filterItem.filterable
                      : true
                  "
                  :clearable="
                    filterItem.clearable !== undefined
                      ? filterItem.clearable
                      : true
                  "
                  v-model="filters[filterItem.key]"
                  @on-change="getList"
                  v-if="filterItem.type === 'select'"
                  :style="filterItem.style"
                  :placeholder="filterItem.placeholder"
                >
                  <template
                    v-if="
                      selfFilterConfig.filterDataSource[filterItem.key] &&
                        selfFilterConfig.filterDataSource[filterItem.key].data
                    "
                  >
                    <Option
                      v-for="option in selfFilterConfig.filterDataSource[
                        filterItem.key
                      ].data"
                      :key="filterItem.key + option.value"
                      :value="option.value"
                      >{{ option.label }}</Option
                    >
                  </template>
                </Select>
                <DatePicker
                  :key="index"
                  :transfer="true"
                  type="date"
                  v-model="filters[`${dateModelPrefix}${filterItem.key}`]"
                  v-if="filterItem.type === 'date'"
                  @on-change="handleDateChange"
                  :placeholder="filterItem.placeholder"
                  :style="filterItem.style"
                ></DatePicker>
                <DatePicker
                  :key="index"
                  :transfer="true"
                  type="daterange"
                  v-model="filters[filterItem.key]"
                  v-if="filterItem.type === 'daterange'"
                  @on-change="handleDateRangeChange"
                  :placeholder="filterItem.placeholder"
                  :style="filterItem.style"
                ></DatePicker>
                <Input
                  :key="index"
                  @on-enter="getList"
                  v-model="filters[filterItem.key]"
                  v-if="!filterItem.type || filterItem.type === 'input'"
                  :style="filterItem.style"
                  :placeholder="filterItem.placeholder"
                >
                  <Button
                    :key="index"
                    slot="append"
                    icon="ios-search"
                    @click="getList"
                  ></Button>
                </Input>
                <slot
                  :name="filterItem.key"
                  v-if="filterItem.type === 'slot'"
                ></slot>
              </template>
              <strong
                class="advancedSearch"
                v-if="filterConfig.showAdvance"
                :class="{ noBg: !showAdvanceFilter }"
                @click="showAdvanceFilter = !showAdvanceFilter"
                >高级筛选 <Icon type="ios-arrow-down" size="16"></Icon
              ></strong>
            </Col>
            <Col style="flex: 1">
              <slot name="filter-extra"></slot>
            </Col>
          </Row>
          <transition name="dropdown-fade">
            <div class="advancedPanel" v-show="showAdvanceFilter">
              <Row
                style="margin-bottom: 10px"
                :gutter="15"
                v-for="(row, index) in selfFilterConfig.advanceFilterRows"
                :key="'advance-row' + index"
                align="middle"
                type="flex"
              >
                <Col
                  align="right"
                  :style="{ width: selfFilterConfig.advanceLabelWidth + 'px' }"
                  v-if="row.label"
                  >{{ row.label }}</Col
                >
                <template v-for="(filterItem, colIndex) in row.cols">
                  <Col :key="colIndex" v-if="filterItem.label">{{
                    filterItem.label
                  }}</Col>
                  <Col :key="'advance-col' + colIndex">
                    <Select
                      :key="colIndex"
                      filterable
                      clearable
                      v-model="advanceFilters[filterItem.key]"
                      v-if="filterItem.type === 'select'"
                      :style="filterItem.style"
                      :placeholder="filterItem.placeholder"
                    >
                      <Option
                        v-for="option in selfFilterConfig.filterDataSource[
                          filterItem.key
                        ].data"
                        :key="index + filterItem.key + option.value"
                        :value="option.value"
                        >{{ option.label }}</Option
                      >
                    </Select>
                    <DatePicker
                      :key="colIndex"
                      type="date"
                      v-if="filterItem.type === 'date'"
                      @on-change="handleAdvanceDateChange"
                      :placeholder="filterItem.placeholder"
                      :style="filterItem.style"
                    ></DatePicker>
                    <DatePicker
                      :key="colIndex"
                      type="daterange"
                      :ref="'advance-' + filterItem.key"
                      v-if="filterItem.type === 'daterange'"
                      :data-key="filterItem.key"
                      @on-change="handleAdvanceDateRangeChange"
                      :placeholder="filterItem.placeholder"
                      style="width: 180px"
                      :style="filterItem.style"
                    ></DatePicker>
                    <Input
                      :key="colIndex"
                      v-model="advanceFilters[filterItem.key]"
                      v-if="!filterItem.type || filterItem.type === 'input'"
                      :style="filterItem.style"
                      :placeholder="filterItem.placeholder"
                    />
                    <CheckboxGroup
                      :key="colIndex"
                      v-model="advanceFilters[filterItem.key]"
                      v-if="filterItem.type === 'checkboxGroup'"
                    >
                      <Checkbox
                        :label="checkBox.value"
                        v-for="(checkBox, index) in filterItem.list"
                        :key="index"
                      >
                        <span>{{ checkBox.label }}</span>
                      </Checkbox>
                    </CheckboxGroup>
                    <RadioGroup
                      :key="colIndex"
                      v-model="advanceFilters[filterItem.key]"
                      v-if="filterItem.type === 'radioGroup'"
                    >
                      <Radio
                        :label="radio.value"
                        v-for="(radio, index) in filterItem.list"
                        :key="index"
                      >
                        <span>{{ radio.label }}</span>
                      </Radio>
                    </RadioGroup>
                    <slot
                      :name="filterItem.key"
                      v-if="filterItem.type === 'slot'"
                    ></slot>
                  </Col>
                </template>
              </Row>
              <Row align="middle" type="flex">
                <Col
                  :style="{ width: selfFilterConfig.advanceLabelWidth + 'px' }"
                ></Col>
                <Col>
                  <Button @click="resetAdvance">重 置</Button>
                  <Button type="primary" @click="advanceSearch">搜 索</Button>
                </Col>
              </Row>
              <p class="ishide" @click="showAdvanceFilter = false">
                收 起 <Icon type="chevron-up" size="16"></Icon>
              </p>
            </div>
          </transition>
        </div>
      </slot>
      <slot name="after-filter"></slot>
    </Row>
    <Table
      :columns="selfColumns"
      :data="list"
      :loading="loading"
      :height="selfheight"
      :no-data-text="noDataText"
      @on-column-width-resize="handleResizeColumn"
      @on-sort-change="handleSortChange"
      @on-select="handleSelect"
      @on-select-cancel="handleSelectCancel"
      @on-selection-change="handleSelectionChange"
    ></Table>
    <slot name="after-table"></slot>
    <Page
      v-show="showPagination"
      :total="page.total"
      :current="page.currentPage"
      :page-size="page.pageSize"
      class="buttomPage js-after-table"
      style="text-align: right"
      @on-change="changePage"
      @on-page-size-change="changePageSize"
      placement="top"
      show-elevator
      show-total
      :page-size-opts="page.pageSizeOpts"
      show-sizer
    >
    </Page>
  </div>
</template>
<script>
import { get, post } from '@api/request.js';
import DatePicker from './list-table/date-picker.vue';
import * as TableUtil from '@/util/table';
let MIN_PAGE_SIZE = 10;
export default {
  name: 'tablePagination',
  components: {
    DatePicker
  },
  props: {
    // eslint-disable-next-line vue/require-prop-type-constructor
    api: '',
    cols: Array,
    autoSort: {
      type: Boolean
    },
    rowBtnWrapClass: {
      type: String,
      default: 'btnsPanel_muti'
    },
    tableHeight: {
      type: [String, Number],
      default: ''
    },
    manualGetData: {
      // 为true需要手动调用获取数据的方法才会向后段发送请求
      type: Boolean,
      default: false
    },
    showPagination: {
      type: Boolean,
      default: true
    },
    showFilter: {
      type: Boolean,
      default: true
    },
    resizable: {
      type: Boolean,
      default: true
    },
    showFilterContent: {
      type: Boolean,
      default: true
    },
    filterConfig: {
      type: Object,
      default: () => {
        return {
          width: 16,
          showAdvance: false,
          filterDataSource: {},
          filterCols: [],
          advanceFilterRows: [
            {
              label: '',
              cols: [
                {
                  type: '',
                  key: ''
                }
              ]
            }
          ]
        };
      }
    },
    listFilters: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      loading: false,
      dateModelPrefix: '__',
      advanceFilters: {},
      filters: {},
      selfColumns: [],
      list: [],
      showAdvanceFilter: false,
      page: {
        pageSizeOpts: [MIN_PAGE_SIZE, 20, 30, 40, 100],
        pageSize: MIN_PAGE_SIZE,
        totalPage: 0,
        total: 0,
        currentPage: 1
      }
    };
  },
  watch: {
    filterConfig: {
      deep: true,
      handler(newValue) {
        this.selfFilterConfig = newValue;
        this.initFilterConfig();
        this.initFilters();
        this.initAdvanceFilters();
      }
    },
    cols: {
      deep: true,
      handler(cols) {
        this.setSelfColumns(cols);
      }
    }
  },
  computed: {
    selfheight: function() {
      return this.tableHeight || this.getTableHeight() - 20;
    },
     noDataText(){
      if(this.loading){
        return ' '
      }else{
        return  "暂无数据"
      }
    }
  },
  created() {
    this.loading = true;
    this.initFilterConfig();
    this.initFilters();
    this.initAdvanceFilters();
    if (!this.manualGetData) {
      this.getList();
    }
    this.setSelfColumns(this.cols);
  },
  methods: {
    handleResizeColumn() {
      this.$emit('on-column-width-resize');
    },
    setSelfColumns(cols) {
      cols.forEach(col => {
        /**
         * actionList格式 ［
         *     confirm: "", // 是否需要确认操作
         *     name: "", //操作按钮名称
         *     type: "", // redirect|normal redirect: 页面跳转，normal: 普通操作, event: 事件
         *     eventName: "", //事件名， 仅type为event时有效
         *     url: "", // API地址｜跳转地址
         *     params: [], // 参数，如果是跳转作为query参数
         * ］
         */
        if (col.actionList && col.actionList.length > 0) {
          col.resizable = false;
          col.render = (h, params) => {
            let data = params.row;
            let actionList = col.actionList.map(action => {
              // 按钮名称
              let actionName = action.name;
              // 按钮类名
              let actionClass = {};
              // 按钮自定义属性
              let attrs = {};
              if (action.render) {
                return action.render(h, params);
              }
              // 按钮名称灵活定义
              if (action.name && typeof action.name == 'function') {
                actionName = action.name(params);
              }
              if (action.class && typeof action.class == 'function') {
                actionClass = action.class(params);
              } else {
                actionClass = action.class;
              }
              if (action.attrs) {
                if (typeof action.attrs == 'function') {
                  attrs = action.attrs(params);
                } else {
                  attrs = action.attrs;
                }
              }
              return h(
                'span',
                {
                  class: actionClass,
                  attrs: attrs,
                  on: {
                    click: () => {
                      if (action.action) {
                        action.action(params);
                      } else {
                        let actionParams = {};
                        if (action.params && action.params.length > 0) {
                          if (typeof action.params == 'string') {
                            if (data[action.params]) {
                              actionParams[action.params] = data[action.params];
                            }
                          } else {
                            action.params.map(p => {
                              if (data[p]) {
                                actionParams[p] = data[p];
                              }
                            });
                          }
                        }
                        if (action.extraParams) {
                          let extraParams = {};
                          if (
                            action.extraParams &&
                            typeof action.extraParams == 'function'
                          ) {
                            extraParams = action.extraParams(params, action);
                          } else {
                            extraParams = action.extraParams;
                          }
                          actionParams = Object.assign(
                            {},
                            actionParams,
                            extraParams
                          );
                        }
                        switch (action.type) {
                          // 页面跳转
                          case 'redirect': {
                            this.router.push({
                              path: action.url,
                              query: actionParams
                            });
                            break;
                          }
                          // 导出
                          case 'export': {
                            get(action.url, actionParams).then(res => {
                              let { status, message, data } = res;
                              if (status) {
                                let filePath =
                                  typeof data === 'object' && data.url
                                    ? data.url
                                    : data;
                                location.href = filePath;
                              } else {
                                this.errorNotice({
                                  title: '操作失败',
                                  desc: message
                                });
                              }
                            });
                            break;
                          }
                          // 事件
                          case 'event': {
                            if (action.eventName) {
                              this.$emit(action.eventName, params);
                            }
                            break;
                          }
                          // 普通ajax操作
                          default: {
                            if (action.confirm) {
                              let confirm =
                                typeof action.confirm === 'function'
                                  ? action.confirm(h, params)
                                  : action.confirm;
                              let modeConfig = {
                                onOk: () => {
                                  post(action.url, actionParams).then(res => {
                                    let { status, message } = res;
                                    if (status) {
                                      this.getList(false);
                                      this.successNotice(
                                        message ? message : '操作成功'
                                      );
                                    } else {
                                      this.errorNotice({
                                        title: '操作失败',
                                        desc: res.message
                                      });
                                    }
                                  });
                                }
                              }
                              if (typeof action.confirm === 'function') {
                                modeConfig.render = (h) => {
                                  return action.confirm(h, params);
                                };
                              } else {
                                modeConfig.content = confirm;
                              }
                              this.$Modal.confirm(modeConfig);
                            } else {
                              post(action.url, actionParams).then(res => {
                                if (res.status) {
                                  this.getList(false);
                                } else {
                                  this.errorNotice({
                                    title: '操作失败',
                                    desc: res.message
                                  });
                                }
                              });
                            }
                          }
                        }
                      }
                    }
                  }
                },
                actionName
              );
            });
            return h(
              'ul',
              {
                class: {
                  'show-edit-btn': true,
                  [this.rowBtnWrapClass]: true
                },
                style: {
                  'text-align': 'left'
                }
              },
              actionList
            );
          };
        }
      });
      let columns = cols;
      if (this.resizable) {
        columns = TableUtil.initColumnsWidth({
          columns: cols,
          tableId: ''
        });
      }
      this.selfColumns = columns;
    },
    getSelfColumns() {
      return this.selfColumns;
    },
    initFilters() {
      let filtersKeys = Object.keys(this.filters);
      this.selfFilterConfig.filterCols.map(filterCol => {
        let filterKey = filterCol.key;
        let defaultValue =
          this.selfFilterConfig.filterDataSource &&
          this.selfFilterConfig.filterDataSource[filterKey]
            ? this.selfFilterConfig.filterDataSource[filterKey].defaultValue
            : '';
        // 设置响应式数据
        if (!filtersKeys.includes(filterKey)) {
          // 区间日期单独处理
          if (filterCol.type === 'daterange') {
            if (!defaultValue || !Array.isArray(defaultValue)) {
              defaultValue = [];
            }
            this.$set(
              this.filters,
              filterCol.keyArr[0],
              defaultValue[0] ? defaultValue[0] : ''
            );
            this.$set(
              this.filters,
              filterCol.keyArr[1],
              defaultValue[1] ? defaultValue[1] : ''
            );
            this.$set(this.filters, filterKey, defaultValue);
          } else if (filterCol.type === 'date') {
            this.$set(
              this.filters,
              `${this.dateModelPrefix}${filterKey}`,
              defaultValue
            );
          } else {
            this.$set(this.filters, filterKey, '');
          }
        }
        // 初始化筛选条件默认值
        this.filters[filterKey] = defaultValue;
      });
    },
    initAdvanceFilters() {
      let filtersKeys = Object.keys(this.filters);
      let advanceFiltersKeys = Object.keys(this.advanceFilters);
      this.selfFilterConfig.advanceFilterRows.map(row => {
        row.cols.map(filterCol => {
          let filterKey = filterCol.key;
          let defaultValue =
            this.selfFilterConfig.filterDataSource &&
            this.selfFilterConfig.filterDataSource[filterKey]
              ? this.selfFilterConfig.filterDataSource[filterKey].defaultValue
              : '';
          // 设置响应式数据
          if (!advanceFiltersKeys.includes(filterKey)) {
            // 区间日期单独处理
            if (filterCol.type === 'daterange') {
              this.$set(this.advanceFilters, filterCol.keyArr[0], '');
              this.$set(this.advanceFilters, filterCol.keyArr[1], '');
            } else {
              this.$set(this.advanceFilters, filterKey, '');
            }
          } else {
            if (filterCol.type === 'daterange') {
              this.advanceFilters[filterCol.keyArr[0]] = '';
              this.advanceFilters[filterCol.keyArr[1]] = '';
            } else {
              this.advanceFilters[filterKey] = '';
            }
          }
          // 初始化筛选条件默认值
          this.advanceFilters[filterKey] = defaultValue;
          if (filtersKeys.includes(filterKey)) {
            this.filters[filterKey] = this.advanceFilters[filterKey];
          }
          if (filterCol.type === 'checkboxGroup') {
            this.advanceFilters[filterKey] = [];
          }
        });
      });
    },
    initFilterConfig() {
      let defaultConfig = {
        width: 16, // ivue珊格
        advanceLabelWidth: 120, // 单位px
        showAdvance: false,
        filterDataSource: {},
        filterCols: [],
        advanceFilterRows: []
      };
      this.selfFilterConfig = Object.assign(defaultConfig, this.filterConfig);
    },
    /**
     * 筛选条件中的日期变化处理函数 TODO 目前筛选条件中支持吃直接配置一个日期筛选条件，多个日期筛选条件请通过slot的方式处理
     * @param date
     */
    handleDateChange(date) {
      this.selfFilterConfig.filterCols.map(item => {
        if (item.type === 'date') {
          this.filters[item.key] = date;
          this.getList();
        }
      });
    },
    /**
     * 筛选条件中的日期变化处理函数 TODO 目前筛选条件中支持吃直接配置一个日期区间筛选条件，多个日期筛选条件请通过slot的方式处理
     * @param date
     */
    handleDateRangeChange(date) {
      this.selfFilterConfig.filterCols.map(item => {
        if (item.type === 'daterange') {
          this.filters[item.keyArr[0]] = date[0];
          this.filters[item.keyArr[1]] = date[1];
        }
      });
      this.getList();
    },
    /**
     * 筛选条件中的日期变化处理函数 TODO 目前筛选条件中支持吃直接配置一个日期筛选条件，多个日期筛选条件请通过slot的方式处理
     * @param date
     */
    handleAdvanceDateChange(date) {
      this.selfFilterConfig.advanceFilterRows.map(row => {
        row.cols.map(item => {
          if (item.type === 'date') {
            this.advanceFilters[item.key] = date;
          }
        });
      });
    },
    /**
     * 筛选条件中的日期变化处理函数 TODO 目前筛选条件中支持吃直接配置一个日期区间筛选条件，多个日期筛选条件请通过slot的方式处理
     * @param date
     */
    handleAdvanceDateRangeChange(date, dataKey) {
      this.selfFilterConfig.advanceFilterRows.map(row => {
        row.cols.map(item => {
          if (item.type === 'daterange' && item.key === dataKey) {
            if (item.key && Array.isArray(item.key)) {
              this.advanceFilters[item.key[0]] = date[0];
              this.advanceFilters[item.key[1]] = date[1];
            }
            if (item.keyArr && Array.isArray(item.keyArr)) {
              this.advanceFilters[item.keyArr[0]] = date[0];
              this.advanceFilters[item.keyArr[1]] = date[1];
            }
          }
        });
      });
    },
    resetDateRange() {
      this.selfFilterConfig.advanceFilterRows.map(row => {
        row.cols.map(item => {
          if (item.type === 'daterange') {
            let refKey = 'advance-' + item.key;
            if (Array.isArray(this.$refs[refKey])) {
              this.$refs[refKey].map(dateComp => {
                dateComp.handleClear();
              });
            } else {
              this.$refs['advance-' + item.key].handleClear();
            }
          }
        });
      });
    },
    resetAdvance() {
      this.resetDateRange();
      this.$emit('on-reset');
      this.initAdvanceFilters();
      this.getList();
      this.showAdvanceFilter = false;
    },
    advanceSearch() {
      this.$emit('on-advance');
      Object.keys(this.advanceFilters).map(key => {
        if (Array.isArray(this.advanceFilters[key])) {
          this.filters[key] = this.advanceFilters[key].join(',');
        } else {
          this.filters[key] = this.advanceFilters[key];
        }
      });
      this.getList();
      this.showAdvanceFilter = false;
    },
    /**
     * @description: 获取筛选参数
     * @param {boolean} needPage  是否需要page筛选
     * @return {Object} params
     */
    getFilters(needPage = false) {
      // 获取高级筛选项
      Object.keys(this.advanceFilters).map(key => {
        if (Array.isArray(this.advanceFilters[key])) {
          this.filters[key] = this.advanceFilters[key].join(',');
        } else {
          this.filters[key] = this.advanceFilters[key];
        }
      });

      let params = this.cloneObj(this.filters);
      let listFilters = this.cloneObj(this.listFilters);
      let filters;
      if (needPage) {
        filters = Object.assign(params, listFilters, {page: this.page.currentPage, page_size: this.page.pageSize});
      } else {
        filters = Object.assign(params, listFilters);
      }

      return filters;
    },
    getList(resetPage) {
      this.list = [];
      let params = this.getFilters();
      if (resetPage !== false) {
        this.resetPage();
      }
      params.page = this.page.currentPage;
      params.pageSize = this.page.pageSize;
      this.$emit('on-changeData', params);
      // 没有分页
      if (!this.showPagination) {
        delete params.page;
        delete params.pageSize;
      }
      this.loading = true;
      get(this.api, params).then(res => {
        this.loading = false;
        let { status, message } = res;
        if (status) {
          let data = res.data;
          let pageParams = data.pageParams;
          let list = this.cloneObj(data.list);
          list.forEach(item => {
            item._expanded = false;
            item._checked = false;
            item._disabled = false;
          });
          this.list = list;
          if (this.showPagination) {
            this.page.totalPage = pageParams.total_page;
            this.page.total = parseInt(pageParams.count);
          }
        } else {
          this.list = [];
          this.resetPage();
          this.errorNotice(message);
        }
        this.$nextTick(() => {
          this.$emit('after-render', this.list, res);
        });
      });
    },
    getListData() {
      return this.list;
    },
    setListData(list) {
      this.list = list;
    },
    handleSortChange(sortData) {
      let { column, key, order } = sortData;
      this.$emit('on-sort-change', column, key, order);
    },
    handleSelect(selection, row) {
      this.$emit('on-select', selection, row);
    },
    handleSelectCancel() {},
    handleSelectionChange(selection) {
      // eslint-disable-next-line no-unused-vars
      this.list.forEach(item => {});
      this.$emit('on-selection-change', selection);
    },
    setCheckDisable(data, key) {
      this.list.forEach(item => {
        item._disabled = false;
        if (data.includes(item[key])) {
          item._disabled = true;
        }
      });
    },
    setChecked(data, key) {
      this.list.forEach(item => {
        item._checked = false;
        if (data && data.length > 0) {
          if (data.includes(item[key])) {
            item._checked = true;
          }
        }
      });
    },
    cancelChecked(data, key) {
      this.list.forEach(item => {
        if (data && data.length > 0) {
          if (data.includes(item[key])) {
            item._checked = false;
          }
        }
      });
    },
    cancelAllChecked() {
      this.list.forEach(item => {
        item._checked = false;
      });
    },
    getCheckedRows() {
      return this.list.filter(item => item._checked === true);
    },
    setCheckAble(data, key) {
      this.list.forEach(item => {
        item._disabled = true;
        if (data && data.length > 0) {
          if (data.includes(item[key])) {
            item._disabled = false;
          }
        }
        // else {
        //   item._disabled = false;
        // }
      });
    },
    setCheckDisableByRowIndex(rows) {
      this.list.forEach((item, index) => {
        if (rows.includes(index)) {
          item._disabled = true;
        }
      });
    },
    setAllExpand(exceptRows = []) {
      this.list.forEach((item, index) => {
        item._expanded = true;
        if (exceptRows.includes(index)) {
          item._expanded = false;
        }
      });
    },
    cancelAllExpand() {
      this.list.forEach(item => {
        item._expanded = false;
      });
    },
    resetPage() {
      this.page.totalPage = 0;
      this.page.total = 0;
      this.page.currentPage = 1;
    },
    changePage(page) {
      this.page.currentPage = page;
      this.getList(false);
    },
    changePageSize(pageSize) {
      // this.page.currentPage = 1;
      this.page.pageSize = pageSize;
      this.getList();
    }
  }
};
</script>
<style lang="less">
.pagination-table {
  .ivu-table-cell {
    &.ivu-table-hidden {
      /*visibility: visible !important;*/
    }
  }
}
</style>
<style scoped lang="less">
.pagination-table {
  .filter {
    margin: 8px 0;
    .ivu-select {
      width: auto;
    }
    .ivu-input-group {
      width: 240px;
    }
    .advancedSearch {
      float: none;
      background: transparent;
    }
  }
  .advancedPanel {
    .ivu-form-item {
      margin-bottom: 0;
    }
  }
}
.buttomPage {
  padding: 0;
}
</style>
