<template>
  <div class="task-center-wrap">
    <div class="modal-mask" @click="closeModal" v-if="show"></div>
    <transition name="panelFade">
      <div class="selectColsPanel task-center" v-if="show">
        <div class="panelHeader">
          <div class="title">任务列表</div>
          <div class="close" @click="closeModal">
            <Icon type="android-close"></Icon>
          </div>
        </div>
        <div class="panelBody">
          <div class="content">
            <p
              class="no-data"
              style="text-align: center"
              v-if="!list || list.length === 0"
            >
              暂无导入/导出任务
            </p> 
            <Row
              type="flex"
              align="top"
              v-for="(item, index) in list"
              class="download-item"
              :class="item.is_read == status.download ? 'light-gray' : ''"
              :key="index"
              @click.native="download(item)"
              :style="{
                cursor: parseInt(item.status) === taskStatus.complete && (+item.offline_type === 1 || (+item.offline_type === 2 && item.failure_file_url)) ? 'pointer' : '',
              }"
            >
              <!-- <Col :span="1" class="status">
                <Badge
                  color="#03ac54"
                  v-if="item.is_read != status.download"
                ></Badge>
              </Col> -->
              <Col>
                <div class="download-item-type">
                  {{
										(item.offline_type === 1 && '导出') ||
											(item.offline_type === 2 && '导入') ||
											(item.offline_type === 3 && '菜谱')
									}}
                </div>
              </Col>
              <Col style="flex: 1" class="download-item-name">
                <span
                  :class="{
                    'click-enable': parseInt(item.status) === taskStatus.complete && (+item.offline_type === 1 || (+item.offline_type === 2 && item.failure_file_url)),
                  }"
                >{{ item.contents }}</span>
                <span :style="{
                  color: item.status ? '' : 'red',
                  textDecoration: parseInt(item.status) === taskStatus.complete && (+item.offline_type === 1 || (+item.offline_type === 2 && item.failure_file_url)) ? 'underline' : ''
                }">{{
                  item.msg
                }}</span>
                <p class="light-gray task-time">{{ item.operate_time }}</p>
              </Col>
              <Col
                v-if="parseInt(item.status) === taskStatus.process"
                style="flex: 1"
                align="left"
              >
                <Icon size="24" type="ios-loading" class="icon-load"></Icon>
              </Col>
              <Col :span="1" class="download-wrap">
                <s-icon
                  icon="xiazaidaochu"
                  :size="16"
                  @click.native="download(item)"
                  v-if="parseInt(item.status) === taskStatus.complete && (+item.offline_type === 1 || (+item.offline_type === 2 && item.failure_file_url))"
                />
              </Col>
            </Row>
          </div>
        </div>
        <div class="panelFooter">
          <!-- <p class="tips">导出文件有效期24小时，过期自动删除</p> -->
          <p class="clear" @click="clearTask">清空全部任务</p>
        </div>
      </div>
    </transition>
    <div class="task-center-btn" @click="openModal">
      任务
    </div>
  </div>
</template>

<script>
import Bus from '@api/bus.js';
const status = {
  download: 2 // 已下载
};
const taskStatus = {
  process: 2,
  complete: 1
};
export default {
  name: 'TaskCenter',
  autoRegister: true,
  created() {
    if (this.show) {
      this.getData();
    }
    Bus.$on('refresh-task-center', this.getData);
    Bus.$on('task-error', this.showError);
  },
  beforeDestroy () {
    Bus.$off('refresh-task-center', this.getData);
    Bus.$off('task-error', this.showError);
  },
  computed: {
    show() {
      let show = this.$store.state.showTaskCenter;
      if (show) {
        this.getData();
      }
      return this.$store.state.showTaskCenter;
    }
  },
  data() {
    return {
      taskStatus,
      status,
      list: []
    };
  },
  methods: {
    showError() {

    },
    closeModal() {
      this.$store.commit('showTaskCenter', false);
      this.$emit('on-close');
    },
    openModal() {
      this.$store.commit('showTaskCenter', true);
    },
    clearTask() {
      this.$Modal.confirm({
        content: '确定清空已完成任务？',
        onOk: () => {
          this.$request.get(this.apiUrl.taskCenterClear).then(res => {
            let { status, message } = res;
            if (status) {
              this.$Message.success('清空成功');
              this.getData();
            } else {
              this.modalError(message);
            }
          });
        }
      });
    },
    download(item) {
      if(!(parseInt(item.status) === taskStatus.complete && (+item.offline_type === 1 || (+item.offline_type === 2 && item.failure_file_url)))) {
        // console.log('阻断下载')
        return 
      }
      if (+item.offline_type === 1) {
        this.$request
          .get(this.apiUrl.taskCenterDownload, { id: item.id })
          .then(res => {
            let { status, message, data } = res;
            if (status) {
              this.getData();
              location.href = data;
            } else {
              this.modalError(message);
            }
          });
      } else {
        window.open(item.failure_file_url, '_self')
      }
      
    },
    getData() {
      this.$request.get(this.apiUrl.taskCenterList, { pageSize: 999 }).then(res => {
        let { status, data } = res;
        if (status) {
          this.list = data.list;
        } else {
          this.list = [];
        }
      });
    }
  }
};
</script>

<style scoped lang="less">
@z-index: 1030;
@gutter: 15px;
@width: 420px;
.task-center-wrap {
  font-size: 14px;
  line-height: 18px;
  text-align: left;
  .panelHeader {
    position: fixed;
    top: 0;
    right: 0;
    width: @width;
    background: #03ac54;
    color: #fff;
    z-index: @z-index;
  }
  .task-center {
    width: @width;
    .panelBody {
      z-index: @z-index;
      padding: 0;
      height: 100%;
      overflow: hidden;
      .content {
        height: calc(~'100% - 44px - 68px');
        margin-top: 44px;
        padding: 10px 10px 0;
        overflow-x: hidden;
        overflow-y: auto;
        position: relative;
        .download-item {
          margin-bottom: 15px;
          &-type {
            width: 35px;
            height: 25px;
            line-height: 25px;
            text-align: center;
            color: var(--primary-color);
            border: solid 1px var(--primary-color);
            font-size: 13px;
            margin-right: 10px;
          }
          .status {
            .ivu-icon {
              font-size: 12px;
            }
          }
          &-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            padding-right: @gutter;
            &:hover {
              white-space: normal;
            }
          }
          .download-wrap {
            text-align: right;
            cursor: pointer;
            font-size: 18px;
          }
        }
        .no-data {
          text-align: center;
          color: #aeaeae;
          position: absolute;
          width: 100%;
          top: 50%;
          transform: translateY(-50%);
        }
        .task-time {
          font-size: 13px;
        }
      }
    }
    .panelFooter {
      z-index: @z-index;
      width: @width;
      text-align: center;
      background: #fff;
      bottom: 0;
      padding-top: @gutter;
      .tips {
        color: #aeaeae;
      }
      .clear {
        cursor: pointer;
        padding: @gutter 0;
      }
    }
  }
  .task-center-btn {
    cursor: pointer;
    position: fixed;
    z-index: 256;
    right: 0;
    bottom: 40px;
    width: 36px;
    height: 28px;
    line-height: 28px;
    text-align: center;
    background: rgba(31, 36, 46, 0.8);
    border-radius: 2px 0px 0px 2px;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
  }
  .modal-mask {
    left: 176px;
  }
}
.light-gray {
  color: #aeaeae;
}
.icon-load {
  animation: ani-circle 1s linear infinite;
}
@keyframes ani-circle {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.click-enable {
  color: #03AC54;
  cursor: pointer;
  text-decoration: underline;
}
</style>
