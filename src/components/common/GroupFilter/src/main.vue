<template>
  <div class="group-filter-wrap">
    <Select
      class="groupFilter-select"
      v-model="selectValue"
      @on-change="selectOnChange"
    >
      <Option
        v-for="item in selectData"
        :value="item.value"
        :key="item.value"
      >
      {{ item.label }}
      </Option>
    </Select>
    <Input
      v-if="selfType === 'input'"
      :placeholder="curPlaceholder"
      @on-change="changeInputData"
      @on-enter="onEnter"
      v-model="inputValue"
      class="groupFilter-input"
    />
    <DatePicker
      v-else-if="selfType === 'datePicker'"
      class="date"
      :type="datePickerType"
      :value="datePickerValue"
      :clearable="clearable"
      :transfer="transfer"
      :placeholder="curPlaceholder"
      @on-change="dateOnChange"
    ></DatePicker>
    <template v-else-if="customType">
      <component
        v-show="selfType === 'custom'"
        :is="customComp()"
        :value="customCompValue"
        v-bind="customBind"
        :placeholder="curPlaceholder"
        class="groupFilter-custom"
        @on-change="changeCustomCompData"
        @on-enter="handleCustomCompEnter"
        @on-focus="handleFocus"
      ></component>
    </template>
  </div>
</template>
<script>
import { debounce } from 'lodash-es'
export default {
  name: 'GroupFilter',
  props: {
    type: {
      type: String,
      default: 'input'
    },
    value: {
      type: Array,
      default: () => [],
    },
    selectData: {
      type: Array,
      required: true
    },
    placeholder: {
      type: String,
      default: ''
    },
    // datePicker
    datePickerType: {
      type: String,
      default: 'daterange'
    },
    format:{
      type:String,
      default: "yyyy-MM-dd",
    },
    clearable: {
      type: Boolean,
      default: false
    },
    transfer: {
      type: Boolean,
      default: false
    },
    customType: {
      type: String
    },
    customComp: Function,
    customBind: Object
  },
  data() {
    return {
      selfType: '',
      selectValue: '',
      inputValue: '',
      curPlaceholder: '',
      defaultInputValue: '',
      defaultDatePicker: [],
      originDatePicker: [],
      datePickerValue: [],
      customCompValue: '',
      selectDefaultValue: '',
      defaultCustomCompValue: '',
    };
  },
  watch: {
    selectValue(val) {
      // 如果selectData里面的对象传递了placeholder属性，则使用该属性
      const findIndex = this.selectData.findIndex(item => item.value === val);
      if (findIndex !== -1) {
        this.curPlaceholder =
          this.selectData[findIndex].placeholder || this.placeholder;
      }
    },
    value (val) {
      this.init()
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      this.selfType = this.customType === this.value[0] ? 'custom' : this.type
      console.log('init', this.selfType)
      if(this.selfType === 'input') {
        this.selectValue = this.value[0];
        this.inputValue = this.value[1];
        this.defaultInputValue = this.deepClone(this.inputValue);
      } else if(this.selfType === 'datePicker') {
        const [date_type, start_date, end_date, ] = this.value;
        this.selectValue = date_type;
        this.datePickerValue = [start_date, end_date];
        this.originDatePicker = this.deepClone(this.datePickerValue);
        this.defaultDatePicker = this.deepClone(this.datePickerValue);
      } else if (this.selfType === 'custom') {
        this.selectValue = this.value[0]
        this.customCompValue = this.value[1]
        this.defaultCustomCompValue = this.deepClone(this.customCompValue)
      }
      this.selectDefaultValue = this.deepClone(this.selectValue)
    },
    changeInputData() {
      this.inputEmit();
    },
    changeCustomCompData(val) {
      this.customCompValue = val
      this.$emit('on-change', [this.selectValue, val]);
    },
    handleCustomCompEnter (val) {
      this.$emit('on-enter', [this.selectValue, val])
    },
    handleFocus () {
      this.$emit('on-focus')
    },
    handleClear () {
      this.$emit('on-clear')
    },
    onEnter() {
      this.$emit('on-enter', [this.selectValue, this.inputValue]);
    },
    selectOnChange(val) {
      this.selfType = this.customType === val ? 'custom' : this.type
      // 切换类型时清空数据
      if(this.selfType === 'input') {
        this.inputValue = '';
        this.inputEmit();
      } else if(this.selfType === 'datePicker') {
        this.datePickerEmit();
      }
      if (this.customCompValue) {
        this.customCompValue = ''
      }
      this.handleClear()
    },
    inputEmit: debounce(function () {
      this.$emit('on-change', [this.selectValue, this.inputValue]);
    }, 300),
    resetValue() {
      this.inputValue = this.defaultInputValue;
      this.selectValue = this.selectDefaultValue;
      this.datePickerValue = this.defaultDatePicker;
      this.customCompValue = this.defaultCustomCompValue;
      this.selfType = this.customType === this.selectValue ? 'custom' : this.type
      this.handleClear()
    },
    datePickerEmit() {
      this.$emit('on-change', [ this.selectValue, ...this.datePickerValue]);
    },
    dateOnChange (value) {
      if (this.maxRange && DateUtil.diffDay(value[0], value[1]) > this.maxRange) {
        this.datePickerValue = this.originDatePicker
        this.modalError({
          content: `最多支持${this.maxRangeText || `${this.maxRange}天`}，请重新选择`,
          onOk: () => {
            this.datePickerValue = this.originDatePicker
            this.datePickerEmit();
          }
        })
        return
      } else {
        this.datePickerValue = value
        this.originDatePicker = this.deepClone(value)
      }
      this.datePickerEmit();
    },
  }
};
</script>

<style lang="less" scoped>
.group-filter-wrap {
  display: flex;
  .date {
    width: 232px;
  }
}
.groupFilter-select {
  flex-basis: 0px;
}
.groupFilter-input {
  flex: 2;
  // width: 232px;
}
.groupFilter-select /deep/.ivu-select-selection {
  border-color: transparent !important;
}
/deep/.ivu-select-selected-value {
  padding: 0 13px 0 0 !important;
  color: rgba(0, 0, 0, 0.85) !important;
}
/deep/.ivu-select-arrow {
  right: 0 !important;
}
/deep/.ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
</style>
