<style lang="less" scoped>
.cropper {
  display: flex;
  &__left {
    flex: 1;
    .area {
      width: 100%;
      height: 200px;
      margin-bottom: 12px;
      position: relative;
      .defalut {
        background: #F2F2F2;
        position: absolute;
        width: 100%;
        top: 0;
        bottom: 0;
        display: flex;
        flex-flow: column;
        justify-content: center;
        align-items: center;
        z-index: 2;
        img {
          width: 61px;
          height: 56px;
          display: block;
          margin: 0 auto 18px;
        }
        .text {
          text-align: center;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #9E9E9E;
        }
      }
      /deep/ .vue-cropper {
        .cropper-view-box {
          outline: 1px solid #3EC348;
          outline-color: #3EC348;
        }
        .crop-point {
          background-color: #3EC348;
          width: 6px;
          height: 6px;
          border-radius: 0;
        }
      }
    }
    .operate {
      display: flex;
      .uploadSubmit {
        font-size: 13px;
      }
      .icons {
        margin-left: auto;
        height: 30px;
        display: flex;
        align-items: center;
        .jia, .jian {
          display: flex;
          cursor: pointer;
          margin-left: 20px;
          &:hover {
            .origin {
              display: none;
            }
            .active {
              display: block;
            }
          }
          img {
            width: 20px;
            height: 20px;
            &.active {
              display: none;
            }
          }
        }
      }
    }
  }
  &__right {
    margin-left: 12px;
    .previewBox {
      border: 1px solid #E8E8E8;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      &.active {
        background: #F2F2F2;
      }
      > div {
        overflow: hidden;
      }
      .defalut {
        width: 33px;
        height: 30px;
      }
    }
    .labelText {
      padding-top: 12px;
      text-align: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: rgba(0,0,0,0.6);
    }
  }
}
</style>

<template>
  <Modal
    @on-cancel="onCancel"
    title="编辑图片"
    :width="width"
    :mask-closable="false"
    v-model="showModal">
    <div class="cropper">
      <div class="cropper__left">
        <div class="area">
          <div class="defalut" v-if="!blobFile">
            <img src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/base_1530/upload_pic/%E9%BB%98%E8%AE%A4@2x_com_thumb_20240806155243d4f84be366b1d64b7bd58.png" alt="">
            <div class="text">支持JPG、PNG、JPEG</div>
          </div>
          <vueCropper
            ref="cropper"
            :img="blobFile"
            :info="false"
            :autoCrop="true"
            outputType="png"
            @realTime="hanldeRealTime"
            v-bind="options"
          ></vueCropper>

        </div>
        <div class="operate">
          <UploadForAliyun
            :intercept="true"
            @on-success="handleSuccess"
            @before-upload-intercept="handleIntercept"
            :show-upload-list="false"
          >
            <Button class="uploadSubmit">{{ !blobFile ? '上传图片' : '重新上传' }}</Button>
          </UploadForAliyun>
          <div class="icons">
            <div class="jia" @click="handleChangeScale(1)">
              <img class="origin" src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/sdpdev/upload_pic/%E6%94%BE%E5%A4%A7_com_thumb_202407261727412ce84b4d66a36c0d252c1.png" alt="">
              <img class="active" src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/base_1530/upload_pic/%E6%94%BE%E5%A4%A7%20(1)_com_thumb_202408061613057e3d6b4066b1db11d5e26.png" alt="">
            </div>
            <div class="jian" @click="handleChangeScale(-1)">
              <img class="origin" src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/sdpdev/upload_pic/%E7%BC%A9%E5%B0%8F_com_thumb_2024072617274075d39f1666a36c0c81a43.png" alt="">
              <img class="active" src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/base_1530/upload_pic/%E7%BC%A9%E5%B0%8F%20(1)_com_thumb_20240806161305c1fc504366b1db11d66b7.png" alt="">
            </div>
          </div>
        </div>
      </div>
      <div class="cropper__right">
        <div class="previewBox" :style="previewBox" :class="{active: !blobFile}">
          <img class="defalut" v-if="!blobFile" src="https://sdongpo-base-image.oss-cn-beijing.aliyuncs.com/base_1530/upload_pic/%E9%BB%98%E8%AE%A4@2x_com_thumb_20240806155243d4f84be366b1d64b7bd58.png" alt="">
          <div :style="previews.wrapStyle" v-if="previews.wrapStyle">
            <img :src="previews.url" :style="previews.img">
          </div>
        </div>
        <div class="labelText">图片预览</div>
      </div>
    </div>
    <div
      slot="footer"
      class="btn-list">
      <Button @click="onCancel">取消</Button>
      <Button type="primary" @click="handleConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { VueCropper }  from 'vue-cropper' 
// img	裁剪图片的地址	空	url 地址, base64, blob
// outputSize	裁剪生成图片的质量	1	0.1 ~ 1
// outputType	裁剪生成图片的格式	jpg (jpg 需要传入jpeg)	jpeg, png, webp
// info	裁剪框的大小信息	true	true, false
// canScale	图片是否允许滚轮缩放	true	true, false
// autoCrop	是否默认生成截图框	false	true, false
// autoCropWidth	默认生成截图框宽度	容器的 80%	0 ~ max
// autoCropHeight	默认生成截图框高度	容器的 80%	0 ~ max
// fixed	是否开启截图框宽高固定比例	false	true, false
// fixedNumber	截图框的宽高比例, 开启fixed生效	[1, 1]	[ 宽度 ,  高度 ]
// full	是否输出原图比例的截图	false	true, false
// fixedBox	固定截图框大小	不允许改变	false
// canMove	上传图片是否可以移动	true	true, false
// canMoveBox	截图框能否拖动	true	true, false
// original	上传图片按照原始比例渲染	false	true, false
// centerBox	截图框是否被限制在图片里面	false	true, false
// high	是否按照设备的dpr 输出等比例图片	true	true, false
// infoTrue	true 为展示真实输出图片宽高 false 展示看到的截图框宽高	false	true, false
// maxImgSize	限制图片最大宽度和高度	2000	0 ~ max
// enlarge	图片根据截图框输出比例倍数	1	0 ~ max(建议不要太大不然会卡死的呢)
// mode	图片默认渲染方式	contain	contain , cover, 100px, 100% auto
// limitMinSize	裁剪框限制最小区域	10	Number, Array, String
// fillColor	导出时背景颜色填充	空	#ffffff, white

export default {
  name: "sdpCropper",
  components: {
    VueCropper
  },
  props: {
    width: {
      type: String,
      default: '500px'
    },
    show: {
      type: Boolean,
      default: false
    },
    previewBox: {
      type: Object,
      default: () => { return {} } 
    },
    options: {
      type: Object,
      default: () => { return {} }
    }
  },
  watch: {
    show (show) {
      if (show) {
        this.handleInit();
      }
      this.showModal = show;
    }
  },
  data() {
    return {
      file: null, // 原始文件
      blobFile: '',
      previews: {},
      handleUploadResolve: () => {},
      showModal: false,
    }
  },
  created () {
    this.showModal = this.show;
  },
  methods: {
    handleInit() {
      // 将数据重置为初始状态
      Object.assign(this.$data, this.$options.data.call(this))
    },
    // 放大缩小
    handleChangeScale(num) {
      num = num || 1
      this.$refs.cropper.changeScale(num)
    },
    // 确定, 完成上传
    handleConfirm() {
      if (!this.blobFile) {
        this.errorMessage('请选上传图片')
        return
      }
      this.$refs.cropper.getCropBlob(blob => {
        // 将 Blob 对象转换为 File 对象
        const file = new File([blob], this.file.name, {
          type: blob.type,
          lastModified: Date.now()
        });
        this.handleUploadResolve(file)
      })
    },
    // 实时预览
    hanldeRealTime(data) {
      this.previews = {
        ...data,
        wrapStyle: {
          width: `${ data.w }px`,
          height: `${ data.h }px`,
          zoom: this.previewBox.width.replace('px', '') / data.w
        }
      }
    },
    // 裁剪图片上传成功
    handleSuccess(res) {
      if (res.status) {
        this.$emit('on-success', res.data.upyun_url);
      } else {
        this.modalError({
          content: res.message
        });
      }
    },
    // 阻断上传组件下一步, 返回文件流
    handleIntercept(cbOk, file) {
      this.file = file
      if (file) {
        const blob = new Blob([file], { type: file.type });
        this.blobFile = URL.createObjectURL(blob);
      }

      // 裁剪之后, 完成后续的上传动作
      this.handleUploadResolve = cbOk
    },
    onCancel () {
      this.$emit('on-cancel');
    }
  }
}
</script>
