<template>
  <Upload
    :multiple="multiple"
    ref="upload"
    :data="postParams"
    :show-upload-list="false"
    :before-upload="preBeforeUpload"
    :on-success="onSuccess"
    :action="action"
    :accept="accept"
    :format="format"
    :on-format-error="handleFormatError"
    :on-error="_onError"
    :max-size="maxSize"
    :on-exceeded-size="handleExceedeMaxSize"
    :on-progress="onProgress"
    v-bind="$attrs"
  >
    <slot></slot>
  </Upload>
</template>

<script>
import { Notice } from 'view-design';
export default {
  name: 'UploadForAliyun',
  autoRegister: true,
  props: {
    manualHandleResponse: {
      type: Boolean,
      default: true,
    },
    // 是否阻断上传, 去裁剪图片再执行上传
    intercept: {
      type: Boolean,
      default: false,
    },
    beforeUpload: {
      type: Function,
      default: () => {
        return true;
      },
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png',
    },
    format: {
      type: Array,
      default: () => {
        return ['jpg', 'jpeg', 'png'];
      },
    },
    onFormatImgError: {
      type: Function,
      default: (file) => {
        Notice.error({
          title: '图片上传失败',
          desc: '仅支持jpg，jpeg，png格式',
        });
      },
    },
    onError: {
      type: Function,
      default: (file) => {
        Notice.error({
          title: '图片上传失败',
          desc: '请重试',
        });
      },
    },
    maxSize: {
      type: Number,
      default: 10240,
    },
    onExceededSize: '',
    // 是否支持多选
    multiple: {
      type: Boolean,
      default: true,
    },
    // 最大上传数量
    maxCount: {
      type: Number,
      default: 52, // 默认最大上传数量为5个
    },
  },
  data() {
    return {
      action: '',
      postParams: {},
      postParamsMap: {},
      hasExceededLimit: false, // 标志位，防止多次提示
      uploadedCount: 0,
    };
  },
  created() {},
  mounted() {},
  methods: {
    handleFormatError() {
      this.uploadedCount--
      this.onFormatImgError()
    },
    preBeforeUpload(files) {
      // 检查当前文件数量
      if (this.uploadedCount >= this.maxCount) {
        // 仅当没有提示过时才显示错误提示
        if (!this.hasExceededLimit) {
          this.errorNotice({
            title: '上传失败',
            desc: `最多只能上传 ${this.maxCount} 个文件`,
          });
          this.hasExceededLimit = true; // 设置标志位
        }
        return false; // 阻止上传
      }
      // 上传前文件数量+1
      this.uploadedCount++;
      const handleGetUploadParams = () => {
        const lastCommaIndex = files.name.lastIndexOf('.');
        const name = files.name.substring(0, lastCommaIndex);
        const extName = files.name.substring(lastCommaIndex + 1);
        // const name = files.name.split('.')[0];
        // const extName = files.name.split('.')[1];
        const res = this.$request
          .post('/superAdmin/General/GetImagesUploadOssToken', {
            name,
            ext_name: extName,
          })
          .then((res) => {
            this.action = res.data.url.replace('http:', 'https:');
            this.postParams = Object.assign(
              this.postParams,
              this.data,
              res.data.params,
            );
            this.postParamsMap[files.name] = this.deepClone(this.postParams);
          })
          .catch((err) => {
            throw err;
          });
        return res;
      };
      if (this.intercept) {
        const upload = async (file) => {
          await handleGetUploadParams();
          this.$nextTick(() => {
            this.$refs.upload.post(file);
          });
        };
        this.$emit('before-upload-intercept', upload, files);
        return false;
      }
      if (!this.beforeUpload(files)) {
        return false;
      }
      return handleGetUploadParams();
    },
    onSuccess(response, file, fileList) {
      let postParams = this.postParamsMap[file.name] || {};
      let url = [this.action, '/' + postParams.key];
      let data = {
        server_url: postParams.key,
        upyun_url: url.join(''),
        origin_file_name: file.name,
      };
      fileList.forEach((item) => {
        if (item.name === file.name) {
          item.data = data;
        }
      });
      // this.$emit('on-success', data, this.meta, file, fileList);
      if (this.manualHandleResponse) {
        this.$emit(
          'on-success',
          {
            status: 1,
            message: '上传成功',
            data,
          },
          this.meta,
          file,
          fileList,
        );
      } else {
        this.$emit('on-success', data);
      }
    },
    _onError() {
      if (this.onError) {
        this.onError();
      }
      this.uploadedCount--;
      this.$emit('on-error', file, fileList);
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
      this.uploadedCount = 0;
      this.hasExceededLimit = false;
    },
    handleExceedeMaxSize(file, fileList) {
      if (this.onExceededSize && typeof this.onExceededSize === 'function') {
        this.onExceededSize(file, fileList);
        return;
      }
      const msg = `文件${file.name}太大, 超过${this.maxSize / 1024}M限制.`;
      this.errorNotice({
        title: '上传失败',
        desc: msg,
      });
      this.uploadedCount--;
      this.$emit('on-exceeded-size', msg);
    },
    onProgress(event, file, fileList) {
      this.$emit('on-progress', event, file, fileList, this.meta);
    },
  },
};
</script>
<style lang="less" scoped></style>
