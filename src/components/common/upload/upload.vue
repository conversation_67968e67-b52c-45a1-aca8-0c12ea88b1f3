<template>
  <Upload
    multiple
    ref="upload"
    :data="data"
    :format="format"
    :show-upload-list="false"
    :before-upload="beforeUpload"
    :on-progress="onProgress"
    :on-success="onSuccess"
    :on-error="onError"
    :action="action"
    :accept="accept"
    :on-exceeded-size="handleExceedeMaxSize"
    :max-size="maxSize"
    :on-format-error="onFormatError"
  >
    <slot></slot>
  </Upload>
</template>

<script>
export default {
  name: 'CommonUpload',
  autoRegister: true,
  props: {
    action: {
      type: String,
      default: '/superAdmin/general/upload'
    },
    data: {
      type: Object,
      default: () => {}
    },
    accept: {
      type: String,
      default: ''
    },
    meta: {
      type: Object,
      default: null
    },
    manualHandleResponse: {
      type: Boolean,
      default: false
    },
    format: {
      type: Array,
      default: () => []
    },
    maxSize: {
      type: Number,
      default: 1024 * 2 // 2M
    },
    beforeUpload: {
      type: Function,
      default: () => {
        return true;
      }
    }
  },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {
    onSuccess(response, file, fileList) {
      let { message, status, data } = response;
      if (this.manualHandleResponse === true) {
        this.$emit('on-success', response, this.meta, file, fileList);
        return false;
      }
      if (status) {
        this.$emit('on-success', data, this.meta, file, fileList);
      } else {
        this.errorNotice({
          title: '上传失败',
          desc: message || '上传失败！'
        });
        this.$emit('on-error', message, file, fileList, this.meta);
      }
    },
    onProgress(event, file, fileList) {
      this.$emit('on-progress', event, file, fileList, this.meta);
    },
    onError(error, file, fileList) {
      const myError = error.toString()

      // 504
      if (myError.indexOf('504') > -1) {
        this.errorNotice({
          title: '上传失败',
          desc: '系统繁忙，请减少同步数据的条数或稍后重试！'
        })
      }

      this.$emit('on-error', error, file, fileList, this.meta);
    },
    onFormatError() {
      this.errorNotice({
        title: '上传失败',
        desc: `文件格式错误，只支持${this.format.join('，')}`
      });
    },
    handleExceedeMaxSize(file) {
      const msg = `文件${file.name}太大, 超过${this.maxSize / 1024}M限制.`;
      this.errorNotice({
        title: '上传失败',
        desc: msg
      });
      this.$emit('on-exceeded-size', msg);
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
    }
  }
};
</script>
<style lang="less" scoped></style>
