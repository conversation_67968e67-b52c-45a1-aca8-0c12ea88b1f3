<template>
  <Upload
    :multiple="multiple"
    ref="upload"
    :data="postParams"
    :format="format"
    :show-upload-list="false"
    :before-upload="preBeforeUpload"
    :on-progress="onProgress"
    :on-success="onSuccess"
    :on-error="onError"
    :action="action"
    :accept="accept"
    :on-exceeded-size="handleExceedeMaxSize"
    :max-size="maxSize"
    :on-format-error="onFormatError"
  >
    <slot></slot>
  </Upload>
</template>

<script>
export default {
  name: 'CommonUploadNew',
  autoRegister: true,
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    accept: {
      type: String,
      default: ''
    },
    meta: {
      type: Object,
      default: null
    },
    manualHandleResponse: {
      type: Boolean,
      default: false
    },
    format: {
      type: Array,
      default: () => []
    },
    maxSize: {
      type: Number,
      default: 1024 * 2 // 2M
    },
    multiple: {
      type: Boolean,
      default: true
    },
    beforeUpload: {
      type: Function,
      default: () => {
        return true;
      }
    }
  },
  data() {
    return {
      action: '',
      postParams: {},
      postParamsMap: {}
    };
  },
  created() {},
  mounted() {},
  methods: {
    preBeforeUpload(files) {
      if (!this.beforeUpload(files)) {
        return false;
      }
      const name = files.name.split('.')[0];
      const extName = files.name.split('.')[1];
      let p =  this.$request.post('/superAdmin/General/GetImagesUploadOssToken', {
        name,
        ext_name: extName
      }).then(res => {
        this.action = res.data.url.replace('http:', 'https:');
        this.postParams = Object.assign(this.postParams, this.data, res.data.params)
        this.postParamsMap[files.name] = this.deepClone(this.postParams);
      }).catch(err => {
        throw err;
      });
      return p;
    },
    onSuccess(response, file, fileList) {
      let postParams = this.postParamsMap[file.name] || {};
      // let { message, status, data } = response;
      let url = [this.action, '/' + postParams.key];
      let data = {
        server_url: postParams.key,
        upyun_url: url.join('')
      };
      if (this.manualHandleResponse === true) {
        this.$emit('on-success', {
          status: 1,
          message: '上传成功',
          data,
        }, this.meta, file, fileList);
        return false;
      }
      // if (status) {
      this.$emit('on-success', data, this.meta, file, fileList);
      // } else {
      //   this.errorNotice({
      //     title: '上传失败',
      //     desc: message || '上传失败！'
      //   });
      //   this.$emit('on-error', message, file, fileList, this.meta);
      // }
    },
    onProgress(event, file, fileList) {
      this.$emit('on-progress', event, file, fileList, this.meta);
    },
    onError(error, file, fileList) {
      this.$emit('on-error', error, file, fileList, this.meta);
    },
    onFormatError() {
      this.errorNotice({
        title: '上传失败',
        desc: `文件格式错误，只支持${this.format.join('，')}`
      });
    },
    handleExceedeMaxSize(file) {
      const msg = `文件${file.name}太大, 超过${this.maxSize / 1024}M限制.`;
      this.errorNotice({
        title: '上传失败',
        desc: msg
      });
      this.$emit('on-exceeded-size', msg);
    },
    clearFiles() {
      this.$refs.upload.clearFiles();
    }
  }
};
</script>
<style lang="less" scoped></style>
