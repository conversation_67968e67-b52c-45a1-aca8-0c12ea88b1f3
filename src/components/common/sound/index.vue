<template>
  <div>
    <audio
      id="playSound"
      ref="createOrder"
      src="https://base-image.shudongpoo.com/common/audio/create_order.mp3"
    ></audio>
    <audio
      id="playSound"
      ref="cancelOrder"
      src="https://img.shudongpoo.com/common/audio/cancel_order.mp3"
    ></audio>
    <audio
      id="playSound"
      ref="system"
      src="https://img.shudongpoo.com/common/audio/system_upgrade.mp3"
    ></audio>
  </div>
</template>
<script>
  import { mapState } from 'vuex';
  export default {
    name: "Sound",
    props: {
      soundType: {
        type: Object,
        default: () => {
          return {
            create_order: false,
            cancel_order: false,
            system_upgrade: false
          };
        }
      },
      noticeRing: {
        type: Boolean,
        default: true
      }
    },
    watch: {
      soundType: {
        deep: true,
        handler() {
          this.playSound();
        }
      }
    },
    computed: {
      ...mapState({
        sysConfig: "sysConfig",
      }),
      newOrderNotice() {
        return +this.sysConfig.new_order_notice === 1;
      }
    },
    data() {
      return {
        audio_src: ""
      };
    },
    mounted() {
      this.playSound();
    },
    methods: {
      playSound() {
        if (!this.noticeRing || !this.newOrderNotice) {
          return false;
        }
        if (this.soundType.system_upgrade) {
          this.$refs.system.play();
          return false;
        }
        if (this.soundType.create_order) {
          this.$refs.createOrder.play();
          return false;
        }
        if (this.soundType.cancel_order) {
          this.$refs.cancelOrder.play();
          return false;
        }
      }
    }
  };
</script>
<style scoped>
</style>
