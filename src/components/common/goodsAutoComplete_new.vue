<template>
  <div class="goods-auto-complete">
    <Input
      ref="searchWord"
      clearable
      v-model="searchWord"
      :placeholder="placeholder"
      @on-change="updateValue"
      @on-keyup="handleKeyup"
      @on-enter="handleEnter"
      @on-focus="handleFocus"
      @on-blur="handleBlur"
      @on-clear="handleClear"
    />
    <div
      class="dropdown"
      :class="showDropDown ? 'dropdown-visible' : ''"
      id="dropdown-wrap"
    >
      <!--<transition name="dropdown-fade">-->
      <div>
        <ul class="commodity-list">
          <li
            :key="index"
            class="dropdown-items"
            style="height: auto !important;"
            :class="index == activeIndex ? 'active' : ''"
            v-for="(info, index) in remoteList"
            :data-index="index"
            @click="selectGoods(info)"
          >

            <strong class="dropdown-items-strong">{{
              info[labelKey]
            }}</strong>
            <span class="dropdown-items-span">({{ info.unit }})</span>
            <span v-if="info.summary">({{ info.summary }})</span>
            <SIcon icon="xiajia" :size="16" class="ml5" v-show="info.is_online === 'N'" />
            <p class="dropdown-items-p">{{ info.commodity_code }}</p>
          </li>
        </ul>
      </div>
      <!--</transition>-->
      <!--<transition name="dropdown-fade">-->
      <div class="dropdown-content" v-show="remoteList.length === 0">
        <p class="dropdown-empty">
          {{ this.selectLoading ? '加载中...' : '暂无数据' }}
        </p>
      </div>
      <!--</transition>-->
    </div>
  </div>
</template>

<script>
import SIcon from '@components/icon'
// import goods from '@api/goods.js';
export default {
  name: 'goodsAutoComplete',
  components: {
    SIcon
  },
  props: {
    value: {
      default: ''
    },
    placeholder: {
      type: String,
      default: '商品名/助记码/编码/别名/条形码'
    },
    api: {
      type: String,
      default: '' // 搜索api地址
    },
    labelKey: {
      type: String,
      default: 'commodity_name'
    },
    filters: {
      type: Object,
      default: () => {}
    },
    isHideDrop: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.searchWord = newValue;
      },
      immediate: true
    }
  },
  data() {
    return {
      itemClass: 'dropdown-items',
      searchWord: '',
      // labelKey: 'commodity_name',
      goodsId: '',
      remoteList: [],
      lastChange: '',
      activeIndex: -1,
      disableDropDown: false,
      showDropDown: true,
      selectLoading: false,
      timer: null
    };
  },
  methods: {
    updateValue() {
      this.$emit('input', this.searchWord);
      this.$emit('on-change', this.searchWord);
    },
    setQuery(query) {
      this.searchWord = query;
    },
    handleFocus () {
      this.disableDropDown = false;
      this.$emit('on-focus');
    },
    handleEnter() {
      this.showDropDown = false;
      this.$emit('input', this.searchWord);
      if(this.activeIndex=='-1'){
        this.$nextTick(() => {
         this.$emit('on-enter', this.searchWord);
        });
      }else{
        this.$nextTick(() => {
        this.$emit('on-enter', this.remoteList[this.activeIndex]);
        });
      }
    },
    handleBlur() {
      this.disableDropDown = true;
      setTimeout(() => {
        // 避免影响点击下拉项目
        this.showDropDown = false;
      }, 300);
    },
    handleKeyup(event) {
      let keyCodeArr = [
        38, // 上
        40, // 下
        13 // 回车
      ];
      if (!keyCodeArr.includes(event.keyCode)) {
        this.remoteSearch();
      }
    },
    handleClear () {
      this.$emit('on-clear')
    },
    selectGoods(goods) {
      this.searchWord = goods[this.labelKey];
      this.showDropDown = false;
      this.$emit('input', this.searchWord);
      this.$emit('on-change', this.searchWord);
      this.$nextTick(() => {
        this.$emit('on-enter', goods);
      });
    },
    remoteSearch() {
      // if (this.selectLoading) {
      //   this.lastChange = query;
      //   return false;
      // }
      let query = this.searchWord;
      query = query.trim();
      if (query) {
        this.selectLoading = true;
        clearTimeout(this.timer);
        this.timer = setTimeout(() => {
        let api = this.api || this.apiUrl.searchGoods;
        // console.log( this.filters );
          this.$request.get(api, { query, ...this.filters }).then(res => {
            this.selectLoading = false;
            if (res.status) {
              this.remoteList = res.data.commodities;
            } else {
              this.remoteList = [];
            }
            if (!(this.isHideDrop && this.disableDropDown)) {
              this.showDropDown = true;
            }
            if (this.lastChange) {
              this.remoteSearch(this.lastChange);
              this.lastChange = '';
            }
            this.$nextTick(() => {
              this.keyControl();
            });
          });
        }, 300);
      } else {
        this.temp = '';
        this.remoteList = [];
      }
    },
    setActiveItem() {
      if (this.remoteList[this.activeIndex]) {
        this.searchWord = this.remoteList[this.activeIndex][this.labelKey];
      }
    },
    keyControl: function() {
      this.activeIndex = -1;
      if (this.remoteList.length <= 0) {
        return false;
      }
      let self = this,
        length = self.remoteList.length,
        dropdownDom = document.querySelector('#dropdown-wrap');
      if (!dropdownDom) {
        return false;
      }
      document.onkeydown = function(event) {
        let e = event || window.event || arguments.callee.caller.arguments[0];
        let activeItemSelector = `.${self.itemClass}[data-index="${self.activeIndex}"]`;
        let currentActiveItem = dropdownDom.querySelector(activeItemSelector);
        let scrollHeight = currentActiveItem
          ? currentActiveItem.offsetHeight
          : 50;
        scrollHeight *= 1;
        // 如果点击向上按钮
        if (e && e.keyCode === 38 && self.showDropDown) {
          dropdownDom.scrollTop -= scrollHeight;
          self.activeIndex--;
          if (self.activeIndex < 0) {
            self.activeIndex = -1;
          }
          self.setActiveItem();
        }
        // 如果点击向下按钮
        if (e && e.keyCode === 40 && self.showDropDown) {
          self.activeIndex++;
          if (self.activeIndex > length - 1) {
            self.activeIndex = length - 1;
          }
          if (self.activeIndex > 0) {
            dropdownDom.scrollTop += scrollHeight;
          }
          if (
            dropdownDom.scrollTop + dropdownDom.offsetHeight + 50 >
            dropdownDom.scrollHeight
          ) {
            dropdownDom.scrollTop = dropdownDom.scrollHeight;
          }
          self.setActiveItem();
        }
      };
    },
    resetValue(e) {
      this.$nextTick(() => {
        this.searchWord = e.defaultValue || '';
      });
    },
  }
};
</script>

<style scoped lang="less">
.goods-auto-complete {
  .dropdown {
    position: absolute;
    z-index: -1;
    background: #fff;
    box-shadow: 0px 0px 5px #ccc;
    max-height: 200px !important;
    overflow-y: auto;
    opacity: 0;
    &.dropdown-visible {
      z-index: 255;
      opacity: 1;
    }
    .dropdown-content {
      position: absolute;
      z-index: 199;
      left: 0;
      margin-top: 5px;
      padding-left: 10px;
      padding-right: 10px;
      background-color: #fff;
      border-radius: 2px;
      width: 100%;
    }
    .active {
      p {
        color: #03ac54;
      }
      color: #03ac54;
      background-color: #ebf7ff;
    }
  }
  .dropdown-items {
    text-align: left;
    width: 214px;
    padding: 5px 10px !important;
  }
  .dropdown-items:hover {
    cursor: pointer;
    background-color: #ebf7ff;
    p {
      color: #03ac54;
    }
    color: #03ac54;
  }
  .dropdown-items-strong {
    font-weight: 500;
    font-size: 13px;
    margin-right: 10px;
    line-height: 19.2px;
  }

  .dropdown-items-span {
    font-size: 12px;
    color: #aaa;
  }
  .dropdown-items-p {
    height: 20px;
    line-height: 20px;
    font-size: 13px;
    color: #aaa;
  }
}
</style>
