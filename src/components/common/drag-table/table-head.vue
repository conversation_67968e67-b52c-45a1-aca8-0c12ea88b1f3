<template>
    <table cellspacing="0" cellpadding="0" border="0" :style="styles">
        <colgroup>
            <col v-for="(column, index) in columns" :width="setCellWidth(column)">
            <col v-if="$parent.showVerticalScrollBar" :width="$parent.scrollBarWidth"/>
        </colgroup>
        <thead>
            <tr v-for="(cols, rowIndex) in headRows">
                <th
                    v-for="(column, index) in cols"
                    :colspan="column.colSpan"
                    :rowspan="column.rowSpan"
                    :class="alignCls(column)"
                    @mousemove="mouseMoveHandler($event, index)"
                    @mousedown="mouseDownHandler($event,index)"
                    @mouseleave="mouseLeaveHandler">
                    <div :class="cellClasses(column)">
                        <template v-if="column.type === 'expand'">
                            <span v-if="!column.renderHeader">{{ column.title || '' }}</span>
                            <render-header v-else :render="column.renderHeader" :column="column" :index="index"></render-header>
                        </template>
                        <template v-else-if="column.type === 'selection'"><Checkbox :value="isSelectAll" :disabled="!data.length" @on-change="selectAll"></Checkbox></template>
                        <template v-else>
                            <span v-if="!column.renderHeader" :class="{[prefixCls + '-cell-sort']: column.sortable}" @click="handleSortByHead(getColumn(rowIndex, index)._index)">{{ column.title || '#' }}</span>
                            <render-header v-else :render="column.renderHeader" :column="column" :index="index"></render-header>
                            <span :class="[prefixCls + '-sort']" v-if="column.sortable">
                                <i class="ivu-icon ivu-icon-arrow-up-b" :class="{on: getColumn(rowIndex, index)._sortType === 'asc'}" @click="handleSort(getColumn(rowIndex, index)._index, 'asc')"></i>
                                <i class="ivu-icon ivu-icon-arrow-down-b" :class="{on: getColumn(rowIndex, index)._sortType === 'desc'}" @click="handleSort(getColumn(rowIndex, index)._index, 'desc')"></i>
                            </span>
                            <Poptip
                                v-if="isPopperShow(column)"
                                v-model="getColumn(rowIndex, index)._filterVisible"
                                placement="bottom"
                                popper-class="ivu-table-popper"
                                transfer
                                @on-popper-hide="handleFilterHide(getColumn(rowIndex, index)._index)">
                                <span :class="[prefixCls + '-filter']">
                                    <i class="ivu-icon ivu-icon-funnel" :class="{on: getColumn(rowIndex, index)._isFiltered}"></i>
                                </span>

                                <div slot="content" :class="[prefixCls + '-filter-list']" v-if="getColumn(rowIndex, index)._filterMultiple">
                                    <div :class="[prefixCls + '-filter-list-item']">
                                        <checkbox-group v-model="getColumn(rowIndex, index)._filterChecked">
                                            <checkbox v-for="(item, index) in column.filters" :key="index" :label="item.value">{{ item.label }}</checkbox>
                                        </checkbox-group>
                                    </div>
                                    <div :class="[prefixCls + '-filter-footer']">
                                        <i-button type="text" size="small" :disabled="!getColumn(rowIndex, index)._filterChecked.length" @click.native="handleFilter(getColumn(rowIndex, index)._index)">{{ t('i.table.confirmFilter') }}</i-button>
                                        <i-button type="text" size="small" @click.native="handleReset(getColumn(rowIndex, index)._index)">{{ t('i.table.resetFilter') }}</i-button>
                                    </div>
                                </div>
                                <div slot="content" :class="[prefixCls + '-filter-list']" v-else>
                                    <ul :class="[prefixCls + '-filter-list-single']">
                                        <li
                                            :class="itemAllClasses(getColumn(rowIndex, index))"
                                            @click="handleReset(getColumn(rowIndex, index)._index)">{{ t('i.table.clearFilter') }}</li>
                                        <li
                                            :class="itemClasses(getColumn(rowIndex, index), item)"
                                            v-for="item in column.filters"
                                            @click="handleSelect(getColumn(rowIndex, index)._index, item.value)">{{ item.label }}</li>
                                    </ul>
                                </div>
                            </Poptip>
                        </template>
                    </div>
                </th>

                <th v-if="$parent.showVerticalScrollBar && rowIndex===0" :class='scrollBarCellClass()' :rowspan="headRows.length"></th>
            </tr>
        </thead>
    </table>
</template>
<script>
    import CheckboxGroup from 'view-design/src/components/checkbox/checkbox-group.vue';
    import Checkbox from 'view-design/src/components/checkbox/checkbox.vue';
    import Poptip from 'view-design/src/components/poptip/poptip.vue';
    import iButton from 'view-design/src/components/button/button.vue';
    import renderHeader from './header';
    import Mixin from './mixin';
    import Locale from 'view-design/src/mixins/locale';

    export default {
        name: 'TableHead',
        mixins: [ Mixin, Locale ],
        components: { CheckboxGroup, Checkbox, Poptip, iButton, renderHeader },
        data(){
            return {
                isDragging : false,
            }
        },
        props: {
            prefixCls: String,
            styleObject: Object,
            columns: Array,
            objData: Object,
            data: Array,    // rebuildData
            columnsWidth: Object,
            fixed: {
                type: [Boolean, String],
                default: false
            },
            columnRows: Array,
            fixedColumnRows: Array,
            draggable: {
                type:Boolean,
                default:false
            }
        },
        computed: {
            styles () {
                const style = Object.assign({}, this.styleObject);
                const width = parseInt(this.styleObject.width) ;
                style.width = `${width}px`;
                return style;
            },
            isSelectAll () {
                let isSelectAll = true;
                if (!this.data.length) isSelectAll = false;
                if (!this.data.find(item => !item._disabled)) isSelectAll = false;    // #1751
                for (let i = 0; i < this.data.length; i++) {
                    if (!this.objData[this.data[i]._index]._isChecked && !this.objData[this.data[i]._index]._isDisabled) {
                        isSelectAll = false;
                        break;
                    }
                }

                return isSelectAll;
            },
            headRows () {
                const isGroup = this.columnRows.length > 1;
                if (isGroup) {
                    return this.fixed ? this.fixedColumnRows : this.columnRows;
                } else {
                    return [this.columns];
                }
            }
        },
        methods: {
            cellClasses (column) {
                return [
                    `${this.prefixCls}-cell`,
                    {
                        [`${this.prefixCls}-hidden`]: !this.fixed && column.fixed && (column.fixed === 'left' || column.fixed === 'right')
                    }
                ];
            },
            scrollBarCellClass(){
                let hasRightFixed = false;
                for(let i in this.headRows){
                    for(let j in this.headRows[i]){
                        if(this.headRows[i][j].fixed === 'right') {
                            hasRightFixed=true;
                            break;
                        }
                        if(hasRightFixed) break;
                    }
                }
                return [
                    {
                        [`${this.prefixCls}-hidden`]: hasRightFixed
                    }
                ];
            },
            itemClasses (column, item) {
                return [
                    `${this.prefixCls}-filter-select-item`,
                    {
                        [`${this.prefixCls}-filter-select-item-selected`]: column._filterChecked[0] === item.value
                    }
                ];
            },
            itemAllClasses (column) {
                return [
                    `${this.prefixCls}-filter-select-item`,
                    {
                        [`${this.prefixCls}-filter-select-item-selected`]: !column._filterChecked.length
                    }
                ];
            },
            selectAll () {
                const status = !this.isSelectAll;
                this.$parent.selectAll(status);
            },
            handleSort (index, type) {
                const column = this.columns[index];
                const _index = column._index;

                if (column._sortType === type) {
                    type = 'normal';
                }
                this.$parent.handleSort(_index, type);
            },
            handleSortByHead (index) {
                const column = this.columns[index];
                if (column.sortable) {
                    const type = column._sortType;
                    if (type === 'normal') {
                        this.handleSort(index, 'asc');
                    } else if (type === 'asc') {
                        this.handleSort(index, 'desc');
                    } else {
                        this.handleSort(index, 'normal');
                    }
                }
            },
            handleFilter (index) {
                this.$parent.handleFilter(index);
            },
            handleSelect (index, value) {
                this.$parent.handleFilterSelect(index, value);
            },
            handleReset (index) {
                this.$parent.handleFilterReset(index);
            },
            handleFilterHide (index) {
                this.$parent.handleFilterHide(index);
            },
            // 因为表头嵌套不是深拷贝，所以没有 _ 开头的方法，在 isGroup 下用此列
            getColumn (rowIndex, index) {
                const isGroup = this.columnRows.length > 1;

                if (isGroup) {
                    const id = this.headRows[rowIndex][index].__id;
                    return this.columns.filter(item => item.__id === id)[0];
                } else {
                    return this.headRows[rowIndex][index];
                }
            },
            mouseMoveHandler(e, index){
                // 表格拖曳，鼠标移入表头
                if (!this.draggable) {return}
                // 禁止单项拖曳
                if(this.columns[index].stopdrag) {return}

                const bodyStyle = document.body.style;
                let target = e.target;
                while (target && target.tagName !== 'TH') {
                    target = target.parentNode;
                }

                if (target) {
                    let rect = target.getBoundingClientRect();
                    if (rect.width > 12 && rect.right - e.pageX < 10) {
                      bodyStyle.cursor = 'col-resize';
                      this.isDragging = true;
                    } else {
                      bodyStyle.cursor = '';
                      this.isDragging = false;
                    }
                }else{
                    bodyStyle.cursor = '';
                    this.isDragging = false;
                }
            },
            mouseLeaveHandler () {
                document.body.style.cursor = '';
            },
            mouseDownHandler (e, index) {
                if (!this.draggable || !this.isDragging) {return}
                if(this.columns[index].stopdrag) {return}

                const table = this.$parent.$el;

                // 禁止选取
                document.onselectstart = () => false;
                document.ondragstart = () => false;

                let target = event.target;
                while (target && target.tagName !== 'TH') {
                    target = target.parentNode;
                    console.log(target.tagName)
                }

                let startX = e.pageX;
                let columns = this.columns;
                let columnsWidth = this.columnsWidth;
                let leftColWidth = columns[index].width ? columns[index].width : columnsWidth[index].width;
                let rightColWidth = columns[index + 1].width ? columns[index + 1].width : columnsWidth[index + 1].width;
                let rect = target && target.getBoundingClientRect();
                let x = table.getBoundingClientRect().left;
                let minX = rect.left - x;
                let maxX = rect.right + rightColWidth - x;
                const handleMouseMove = (event) => {
                    let borderLeft = event.pageX - x;
                    if (borderLeft < minX + 50) {
                        borderLeft = minX + 50
                    }
                    if (borderLeft > maxX - 50) {
                        borderLeft = maxX - 50;
                    }
                    document.body.style.cursor = 'col-resize';
                    this.$emit('emitDrag', borderLeft);
                };
                const handleMouseUp = (event)=>{
                    let deltaX = event.pageX - startX;
                    if (deltaX < 0 && deltaX < -leftColWidth + 50) {
                        deltaX = -leftColWidth + 50;
                    }
                    if (deltaX > 0 && deltaX > rightColWidth - 50) {
                        deltaX = rightColWidth - 50;
                    }
                    this.$emit('emitDrag' , false , deltaX , index);
                    document.removeEventListener('mousemove', handleMouseMove);
                    document.removeEventListener('mouseup', handleMouseUp);
                }

                document.addEventListener('mousemove' , handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
            },
        }
    };
</script>
