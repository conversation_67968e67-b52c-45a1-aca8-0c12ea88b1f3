<template>
  <Select
    v-model="selfValue"
    :placeholder="placeholder"
    @on-change="change"
    :disabled="disabled"
    transfer
    :multiple="multiple"
    :max-tag-count="2"
  >
    <Option v-for="item in list" :value="item.id" :key="item.id">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
import { Select, Option } from 'view-design';
import { getList } from '@/api/recipeMealTime';
export default {
  components: { Select, Option },
  model: {
    prop: 'value',
    event: 'on-change',
  },
  props: {
    value: {
      default: '',
    },
    options: {
      type: Array,
    },
    placeholder: {
      type: String,
      default: '选择餐次',
    },
    disabled: {
      default: false,
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    showNo: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value: {
      immediate: true,
      handler() {
        this.selfValue = this.value;
      },
    },
  },
  data() {
    return {
      selfValue: '',
      mealTimeList: [],
    };
  },
  computed: {
    list() {
      if (this.options && this.options.length) {
        return this.options;
      }
      return this.mealTimeList;
    },
  },
  created() {
    this.init();
  },
  methods: {
    init() {
      if (this.options) {
        this.mealTimeList = this.options;
      } else {
        // 请求接口
        this.getMealTimeList();
      }
    },
    async getMealTimeList() {
      const res = await getList({}, { cache: true }).catch((e) => {
        console.log(e);
      });
      if (!res) return;
      const list = res.data;
      if (this.showNo) {
        if (!(list && list.find((v) => v.id === 0 && v.name === '无'))) {
          list.unshift({ id: 0, name: '无' });
        }
      }
      if (this.showAll) {
        if (!(list && list.some((v) => v.id === '' && v.name === '全部'))) {
          list.unshift({ id: '', name: '全部' });
        }
      }
      this.mealTimeList = list;
    },
    change(value) {
      this.$emit('on-change', value);
    },
    resetValue(e) {
      this.selfValue = e.defaultValue || '';
    },
  },
};
</script>

<style></style>
