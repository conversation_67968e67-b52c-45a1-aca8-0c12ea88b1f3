<template>
  <Modal
    v-model="show"
    :title="title"
    @on-ok="handleOk">
    <div v-html="content"></div>
    <div slot="footer" style="text-align: right">
      <Button type="primary" @click="show=false">{{cancelText}}</Button>
      <Button type="primary" @click="handleOk">{{okText}}</Button>
    </div>
  </Modal>
</template>

<script>
  import BusinessException from '@api/BusinessException.js';
  export default {
    name: "",
    props: {
      dateType: {
        type: [String, Number],
        default: 1
      },
      deliveryDate: {
        type: [String, Array],
        default: ''
      }
    },
    data() {
      return {
        show: false,
        title: '',
        content: '',
        okText: '确定',
        cancelText: '取消',
        path: '',
      }
    },
    watch: {
      deliveryDate (newValue) {
        if (!newValue) {
          this.show = false;
          return false;
        }
        this.checkDeliveryUser();
      }
    },
    created() {
      this.checkDeliveryUser();
    },
    methods: {
      handleOk() {
        this.$router.push({
          path: this.path
        });
      },
      checkDeliveryUser () {
        BusinessException.orderException(this.deliveryDate, this.dateType).then((res) => {
          let data = res.data;
          if (res.status) {
            if (!data.message) {
              return false;
            }
            if (!this) {
              return false;
            }
            this.title = data.title ? data.title : '异常';
            this.content = data.message;
            this.cancelText = data.cancelText ? data.cancelText : '继续操作';
            this.okText = data.okText ? data.okText : '异常订单处理';
            this.path = data.path ? data.path : '/abnormalOrdersList';
            this.show = true;
          }
        });
      }
    }
  }
</script>

<style scoped>

</style>
