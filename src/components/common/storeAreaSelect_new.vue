<template>
  <Select
    ref="storeArea"
    v-model="storeAreaId"
    @on-change="updateValue"
    :filterable="filterable"
    :clearable="true"
    :placeholder="placeholder"
  >
    <Option value="">全部</Option>
    <Option :value="item.id" :key="index" v-for="(item, index) in list">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
import store from '@api/storeRoom.js';
export default {
  name: 'storeSelect',
  props: {
    value: {
      default: '',
    },
    placeholder: {
      default: '选择库区',
    },
    storeId: {
      default: '',
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      loading: false,
      storeAreaId: '',
      storeAreaName: '',
      list: [],
    };
  },
  created () {
    this.getList();
  },
  activated() {
    this.getList();
  },
  deactivated(){
    this.list = [];
  },
  watch: {
    storeId() {
      this.getList();
      this.$refs.storeArea.setQuery('');
    },
  },
  methods: {
    remoteSearch(query) {
      this.storeAreaName = query;
      this.getList();
    },
    getList() {
      this.loading = true;
      this.list = [];
      if (!this.storeId) {
        return false;
      }
      let params = {
        storage_id: this.storeId,
        area_name: this.storeAreaName,
        pageSize: 99999,
      };
      store.getStorageAreaList(params).then((res) => {
        this.loading = false;
        if (res.status) {
          this.list = res.data.list;
        } else {
          this.list = [];
        }
      });
    },
    updateValue() {
      this.$emit('input', this.storeAreaId);
      this.$emit('on-change', this.storeAreaId);
    },
    resetValue () {
      this.storeAreaId = '';
    }
  },
};
</script>

<style scoped></style>
