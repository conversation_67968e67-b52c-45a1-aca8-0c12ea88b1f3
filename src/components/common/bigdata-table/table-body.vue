<template>
  <div class="body-wrap" ref="outer" style="height: 100%; overflow: auto; width: 100%"  @DOMMouseScroll="handleScroll" @scroll="handleScroll">
    <div :style="{height: `${topPlaceholderHeight}px`}"></div>
    <Render-Dom :render="renderTable"></Render-Dom>
    <div :style="{height: `${bottomPlaceholderHeight}px`}"></div>
  </div>
</template>
<script>
  import RenderDom from './renderDom'
  import DataMixin from './data-handle'
  import StyleMixin from './style-compute'

  export default {
    name: 'TableBody',
    mixins: [ DataMixin, StyleMixin ],
    components: { RenderDom },
    props: {
      prefixCls: String,
      styleObject: Object,
      columns: Array,
      data: Array,    // rebuildData
      objData: Object,
      columnsWidth: Object,
      fixed: {
        type: [Boolean, String],
        default: false
      },
      /**
       * @description 根据表格容器高度计算内置单个表格（1/3）渲染的行数基础上额外渲染的行数，行数越多表格接替渲染效果越好，但越耗性能
       */
      appendNum: {
        type: Number,
        default: 15
      },
      /**
       * @description 表头高度
       */
      headerHeight: {
        type: Number,
        default: 58
      },
    },
    computed: {
    },
    watch: {
      data () {
        this.$nextTick(() => {
          this.insideTableData = this.setInitIndex(this.data);
        });
      },
      insideTableData () {
        this._tableResize();
      },
    },
    mounted () {
      this.$nextTick(() => {
        this.insideTableData = this.setInitIndex(this.data);
        this.resize();
      });
    },
    methods: {
      // 涉及到表格容器尺寸变化或数据变化的情况调用此方法重新计算相关值
      resize (changeInitIndex) {
        // this.insideTableData = [...this.value]
        this.$nextTick(() => {
          if (changeInitIndex) this.insideTableData = this.setInitIndex(this.data);
          else this.insideTableData = [...this.data];
          // this._initMountedHandle();
        });
        // this._tableResize();
      },
    }
  };
</script>
