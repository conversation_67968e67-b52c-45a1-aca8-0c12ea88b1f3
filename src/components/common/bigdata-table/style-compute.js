export default {
	data () {
		return {
			wrapperHeight: 0,
			scrollTop: 0,
			moduleHeight: 0, // 三个tr块中的一块的高度
			topPlaceholderHeight: 0, // 顶部占位容器高度
			totalRowHeight: 0, // 如果全量渲染应该是多高，用于计算占位
		};
	},
	computed: {
		tableWidthStyles () {
			return {width: this.tableWidth + 'px'};
		},
		placeholderHeight () {
			return this.totalRowHeight - this.moduleHeight * 3; // 占位容器的总高度(上 + 下)
		},
		bottomPlaceholderHeight () {
			return (this.placeholderHeight - this.topPlaceholderHeight) < 0 ? 0 : this.placeholderHeight - this.topPlaceholderHeight;
		},
		itemRowHeight () {
			return this.rowHeight === undefined ? 63 : this.rowHeight;
		},
	},
	watch: {
	},
	methods: {
		_tableResize () {
			this.$nextTick(() => {
				this.updateHeight();
				this.setComputedProps();
			});
		},
		updateHeight () {
      this.$nextTick(() => {
        let wrapperHeight = this.$refs.outer.offsetHeight;
				this.itemNum = Math.ceil((wrapperHeight - this.headerHeight) / this.itemRowHeight) + this.appendNum;
				this.moduleHeight = this.itemNum * this.itemRowHeight;
				this.wrapperHeight = wrapperHeight;
        if (this.totalRowHeight - wrapperHeight > 0) {
          this.$parent.showVerticalScrollBar = true;
        }
        this.setTopPlace();
			});
		},
		setComputedProps () {
			const len = this.insideTableData.length;
			this.totalRowHeight = len * this.itemRowHeight;
		},
		setTopPlace () {
			let scrollTop = this.scrollTop;
			let t0 = 0;
			let t1 = 0;
			let t2 = 0;
			if (scrollTop > this.moduleHeight) {
				switch (this.currentIndex) {
					case 0: t0 = parseInt(scrollTop / (this.moduleHeight * 3)); t1 = t2 = t0; break;
					case 1: t1 = parseInt((scrollTop - this.moduleHeight) / (this.moduleHeight * 3)); t0 = t1 + 1; t2 = t1; break;
					case 2: t2 = parseInt((scrollTop - this.moduleHeight * 2) / (this.moduleHeight * 3)); t0 = t1 = t2 + 1;
				}
			}
			this.times0 = t0;
			this.times1 = t1;
			this.times2 = t2;
			this.topPlaceholderHeight = parseInt(scrollTop / this.moduleHeight) * this.moduleHeight;
			this.setTableData();
		},
	}
};
