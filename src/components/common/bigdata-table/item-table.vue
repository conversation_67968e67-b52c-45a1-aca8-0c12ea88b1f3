<template>
  <table cellspacing="0" cellpadding="0" border="0" :style="styleObject">
    <colgroup>
      <col v-for="(column, index) in columns" :width="setCellWidth(column)" :key="index">
    </colgroup>
    <tbody :class="[prefixCls + '-tbody']">
    <template v-for="(row, index) in data">
      <table-tr
        :row="row"
        :key="row[_rowKey]"
        :prefix-cls="prefixCls"
        @mouseenter.native.stop="handleMouseIn(row._index)"
        @mouseleave.native.stop="handleMouseOut(row._index)"
        @click.native="clickCurrentRow(row._index)"
        @dblclick.native.stop="dblclickCurrentRow(row._index)">
        <td v-for="column in columns" :class="alignCls(column, row)">
          <Cell
            :fixed="fixed"
            :prefix-cls="prefixCls"
            :row="row"
            :key="column._columnKey"
            :column="column"
            :natural-index="index"
            :index="row._index"
            :checked="rowChecked(row._index)"
            :disabled="rowDisabled(row._index)"
            :expanded="rowExpanded(row._index)"
          ></Cell>
        </td>
      </table-tr>
      <tr v-if="rowExpanded(row._index)" :class="{[prefixCls + '-expanded-hidden']: fixed}">
        <td :colspan="columns.length" :class="prefixCls + '-expanded-cell'">
          <Expand :key="row._rowKey" :row="row" :render="expandRender" :index="row._index"></Expand>
        </td>
      </tr>
    </template>
    </tbody>
  </table>
</template>
<script>
  // todo :key="row"
  import TableTr from './table-tr.vue';
  import Cell from './cell.vue';
  import Expand from './expand.js';
  import Mixin from './mixin';

  export default {
    name: 'ItemTable',
    mixins: [ Mixin ],
    components: { Cell, Expand, TableTr },
    props: {
      prefixCls: String,
      styleObject: Object,
      columns: Array,
      data: Array,    // rebuildData
      objData: Object,
      columnsWidth: Object,
      fixed: {
        type: [Boolean, String],
        default: false
      },
      _rowKey: {default: 'id' }
    },
    computed: {
      expandRender () {
        let render = function () {
          return '';
        };
        for (let i = 0; i < this.columns.length; i++) {
          const column = this.columns[i];
          if (column.type && column.type === 'expand') {
            if (column.render) render = column.render;
          }
        }
        return render;
      }
    },
    methods: {
      rowChecked (_index) {
        return this.objData[_index] && this.objData[_index]._isChecked;
      },
      rowDisabled(_index){
        return this.objData[_index] && this.objData[_index]._isDisabled;
      },
      rowExpanded(_index){
        return this.objData[_index] && this.objData[_index]._isExpanded;
      },
      handleMouseIn (_index) {
        // 防抖,减少没必要的性能消耗
        this.objData[_index].timer = setTimeout(() => {
          this.objData[_index].timer = '';
          this.$parent.$parent.handleMouseIn(_index);
        },100)
      },
      handleMouseOut (_index) {
        if (this.objData[_index].timer) {
          clearTimeout(this.objData[_index].timer)  
          return
        }
        this.$parent.$parent.handleMouseOut(_index);
      },
      clickCurrentRow (_index) {
        this.$parent.$parent.clickCurrentRow(_index);
      },
      dblclickCurrentRow (_index) {
        this.$parent.$parent.dblclickCurrentRow(_index);
      }
    }
  };
</script>
