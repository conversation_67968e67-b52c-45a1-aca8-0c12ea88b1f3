<!--
 * @Author: lizhiwei😊
 * @Date: 2021-07-19 11:49:21
 * @LastEditTime: 2021-07-19 15:55:16
 * @LastEditors: lizhiwei😊
 * @Description: 表头排序小组件
 * 示例页面 http://sdpdev.sdongpo.com/superAdmin/view#/finance/user/audit-list
-->
<template>
  <div class="sortIcon" :id="id" @click="_handleClick">
      <Icon :style="{lineHeight: space}" :size="size" :color="upColor" type="md-arrow-dropup" />
      <Icon :style="{lineHeight: space}" :size="size" :color="downColor" type="md-arrow-dropdown" />
  </div>
</template>

<script>
  export default {
    name: 'sortIcon',
    props: {
      id: {
        type: String,
        default: Math.random() + ''
      },
      size: {
        type: [Number],
        default: 16
      },
      // 默认颜色
      color: {
        type: String,
        default: '#b6b6b6'
      },
      // 俩图标上下间隔
      space: {
        type: [Number],
        default: .5
      },
      asc:{
        type:Boolean,
        default:false,
      },
      // 活跃颜色
      activeColor: {
        type: String,
        default: '#1da250'
      },
      // 排序规则, 0 上下都灰, 1 下绿上灰 ,2 下灰上绿
      sortRule: {
        type: [ Number],
        default: 0
      }
    },
    created() {
      this.selfSortRule = this.sortRule
    },
    data(){ 
      return {
        selfSortRule : 0
      }
    },
    computed: {
      upColor() {
        return  +this.selfSortRule === 2 ? this.activeColor : this.color
      },
      downColor() {
        return  +this.selfSortRule === 1 ? this.activeColor : this.color
      }
    },
    watch: {
      sortRule(newVal) {
        this.selfSortRule = newVal
      }
    },
    methods: {
      _handleClick() {
        let sortMap = [1,2,0];
        if(this.asc){  //产品要求文字类先升序后降序
          sortMap = [2,0,1];
        }
        this.selfSortRule = sortMap[this.selfSortRule];
        this.$emit('onChange', this.selfSortRule);
      }
    }
  }
</script>
<style scoped>
.sortIcon {
  cursor: pointer;
}
i {
  display: block;
}
</style>