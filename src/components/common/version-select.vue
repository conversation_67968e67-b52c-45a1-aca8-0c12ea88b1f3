<template>
  <div>
    <Modal
      v-model="showVersionSelect"
      title="版本切换"
      width="660"
      @on-cancel="close"
    >
      <div class="version-list">
        <ul>
          <li
            v-for="(type, index) in typeList"
            :key="index"
            :class="{ active: activeIndex === index }"
            @click="filterConfigs(index)"
          >
            {{ type.name }}
          </li>
        </ul>
      </div>

      <div class="config-list">
        <ul>
          <li v-for="(config, index) in configList" :key="index">
            <template v-if="config.type === 'number'">
              <div>
                <label>{{ config.name }}:</label>
                <InputNumber v-model="config.value" :min="0" :precision="0" />
                <span class="input-append">个</span>
              </div>
            </template>
            <template v-else-if="config.type === 'radio'">
              <div @click="handelClick(config)">
                <label>{{ config.name }}:</label>
                <RadioGroup v-model="config.value">
                  <Radio
                    :disabled="config.disabled"
                    :label="radio.value"
                    v-for="(radio, radio_index) in config.redios"
                    :key="radio_index"
                  >
                    <span>{{ radio.label }}</span>
                  </Radio>
                </RadioGroup>
              </div>
            </template>

            <template v-else>
              <div @click="handelClick(config)">
                <label>{{ config.name }}:</label>
                <Switch
                  v-model="config.value"
                  :disabled="config.disabled"
                  :true-value="1"
                  :false-value="0"
                >
                  <span slot="open">开</span>
                  <span slot="close">关</span>
                </Switch>
              </div>
            </template>
          </li>
        </ul>
      </div>

      <div slot="footer">
        <Button @click="close">取消</Button>
        <Button type="primary" @click="saveVersion">切换</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import Settings from '@api/settings.js';
import { setEdition } from '@/util/editionUtil';
import ConfigMixin from '@/mixins/config';
export default {
  mixins: [ConfigMixin],
  model: {
    prop: 'showVersionSelect',
  },
  props: {
    showVersionSelect: {
      type: Boolean,
      default: false,
    },
    versionList: {
      type: Array,
      default: () => {
        return [];
      },
    },
    currentVersionId: {
      type: Number,
      default: 0,
    },
  },
  data: function () {
    return {
      activeIndex: 0,
      typeList: [
        // {
        //   name: '基础版',
        //   id: 6,
        //   configs: [],
        // },
        // {
        //   name: '专业版',
        //   id: 1,
        //   configs: [
        //     'is_open_sites',
        //     'site_amount_limit',
        //     'multi_warehouse_max',
        //     'is_batch',
        //     'is_open_rfq',
        //     'multi_lines_formula',
        //     'summary_days_order_pool',
        //     'summary_stores_order_pool',
        //     'is_open_purchase_task',
        //     'provider_supplier_pool_switch',
        //     'is_open_user_store',
        //   ],
        // },

        // {
        //   name: '旗舰版',
        //   id: 3,
        //   configs: [
        //     'is_open_sites',
        //     'site_amount_limit',
        //     'multi_warehouse_max',
        //     'is_batch',
        //     'is_open_rfq',
        //     'multi_lines_formula',
        //     'summary_days_order_pool',
        //     'summary_stores_order_pool',
        //     'is_open_purchase_task',
        //     'provider_supplier_pool_switch',
        //     'is_open_user_store',
        //   ],
        // },
        // {
        //   name: '团膳版',
        //   id: 2,
        //   configs: [
        //     'is_open_sites',
        //     'site_amount_limit',
        //     'multi_warehouse_max',
        //     'is_batch',
        //     'is_open_rfq',
        //     'multi_lines_formula',
        //     'summary_days_order_pool',
        //     'summary_stores_order_pool',
        //     'is_enabled_processed_vegetables',
        //     'commodity_package_mode',
        //     'is_open_purchase_task',
        //     'provider_supplier_pool_switch',
        //     'is_open_user_store',
        //   ],
        // },
        // {
        //   name: '净菜专版',
        //   id: 4,
        //   configs: [
        //     'is_open_sites',
        //     'site_amount_limit',
        //     'multi_warehouse_max',
        //     'is_batch',
        //     'is_open_rfq',
        //     'multi_lines_formula',
        //     'summary_days_order_pool',
        //     'summary_stores_order_pool',
        //     'is_application_commodity_package',
        //     'commodity_package_mode',
        //     'is_open_purchase_task',
        //     'provider_supplier_pool_switch',
        //     'is_open_user_store',
        //   ],
        // },
        // {
        //   name: '生鲜门店专版',
        //   id: 5,
        //   configs: [],
        // },
        {
          name: '专业版',
          id: 101,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
        {
          name: '解决方案版',
          id: 102,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
        {
          name: '企业版',
          id: 103,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
        {
          name: '旗舰版（2025）',
          id: 104,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
        {
          name: '高校版',
          id: 105,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
        {
          name: '团餐版',
          id: 106,
          configs: [
            'is_open_sites',
            'site_amount_limit',
            'multi_warehouse_max',
            'is_batch',
            'is_open_rfq',
            'multi_lines_formula',
            'summary_days_order_pool',
            'summary_stores_order_pool',
            'is_enabled_processed_vegetables',
            'commodity_package_mode',
            'is_open_purchase_task',
            'provider_supplier_pool_switch',
            'is_open_user_store',
          ],
        },
      ],
      configs: [
        {
          name: '多站点',
          key: 'is_open_sites',
          value: 0,
        },
        {
          name: '站点数',
          key: 'site_amount_limit',
          type: 'number',
          value: 0,
        },
        {
          name: '仓库数量',
          key: 'multi_warehouse_max',
          type: 'number',
          value: 0,
        },
        {
          name: '批次库存',
          key: 'is_batch',
          value: 0,
        },
        {
          name: '询价报价',
          key: 'is_open_rfq',
          value: 0,
        },
        {
          name: '阶梯定价',
          key: 'multi_lines_formula',
          value: 0,
        },
        {
          name: '多天汇总',
          key: 'summary_days_order_pool',
          value: 0,
        },
        {
          name: '多仓汇总',
          key: 'summary_stores_order_pool',
          value: 0,
        },
        {
          name: '净菜模块',
          key: 'is_enabled_processed_vegetables',
          value: 0,
        },
        {
          name: '套餐模块',
          key: 'is_application_commodity_package',
          value: 0,
        },
        {
          name: '套餐定价模式',
          key: 'commodity_package_mode',
          type: 'radio',
          redios: [
            {
              label: '按原料比例',
              value: 1,
            },
            {
              label: '按套餐定价',
              value: 2,
            },
          ],
        },
        {
          name: '生成采购单模式',
          key: 'is_open_purchase_task',
          disabled: false,
          type: 'radio',
          value: 0,
          redios: [
            {
              label: '订单汇总',
              value: 0,
            },
            {
              label: '采购任务',
              value: 1,
            },
          ],
        },
        {
          name: '供应商联营',
          disabled: false,
          key: 'provider_supplier_pool_switch',
          value: 0,
        },
        {
          name: '商城库存管理',
          key: 'is_open_user_store',
          value: 0,
        },
      ],
      configList: [],
    };
  },
  computed: {},
  methods: {
    /**
     * 获取配置项信息
     */
    initConfigData() {
      this.configs.forEach((config) => {
        let { key } = config;
        let value = this.sysConfig[key] ? Number(this.sysConfig[key]) : 0;
        config.value = value;
        // 开启供应商联营之后，不再支持切换模式
        if (key === 'is_open_purchase_task' && this.isOpenProviderDeliver) {
          config.disabled = true;
        }
      });
      this.autoSetConfigs();
    },
    close() {
      this.$emit('input', false);
    },
    saveVersion() {
      let params = {
        edition: this.typeList[this.activeIndex].id,
        list: this.getConfigList(),
      };

      Settings.saveVersion(params).then((res) => {
        if (res.status) {
          setEdition(params.edition);
          this.close();
          this.successNotice('切换成功');
          location.reload();
        } else {
          this.errorNotice({
            title: '切换失败',
            desc: res.message,
          });
        }
      });
    },
    /**
     * 根据不同的版本过滤配置项
     */
    filterConfigs(_index) {
      this.activeIndex = _index;
      let currentConfig = this.typeList[_index].configs;
      let configList = [];
      this.configs.forEach((config) => {
        let { key } = config;
        if (currentConfig.includes(key)) {
          configList.push(config);
        }
      });
      console.log('configListconfigList', configList);
      this.configList = configList;
    },
    /**
     * 获取保存配置信息
     */
    getConfigList() {
      let saveObj = {};
      this.configList.forEach((config) => {
        let { key, value } = config;
        saveObj[key] = value;
      });
      return JSON.stringify(saveObj);
    },
    autoSetConfigs() {
      let purchaseTaskConfig = '';
      let providerConfig = '';
      this.configs.map((item) => {
        if (item.key === 'provider_supplier_pool_switch') {
          providerConfig = item;
        }
        if (item.key === 'is_open_purchase_task') {
          purchaseTaskConfig = item;
        }
      });
      if (purchaseTaskConfig.value === 0) {
        providerConfig.disabled = true;
        providerConfig.value = 0;
      } else {
        providerConfig.disabled = false;
      }
      if (providerConfig.value === 0) {
        purchaseTaskConfig.disabled = false;
      } else {
        purchaseTaskConfig.disabled = true;
      }
    },
    handelClick(config) {
      if (
        config.key === 'provider_supplier_pool_switch' &&
        config.disabled === true
      ) {
        this.$Message.warning('只有生成采购单模式为采购任务的时候才可以开启');
      }
      if (config.key === 'is_open_purchase_task' && config.disabled === true) {
        this.$Message.warning('开启了供应商联营之后，不再支持切换模式');
      }
    },
  },
  watch: {
    /**
     * 弹框出现时获取配置信息
     */
    showVersionSelect(newVal) {
      if (newVal) {
        this.initConfigData();
        let currentVersionIndex = 0;
        currentVersionIndex = this.typeList.findIndex(
          (item) => item.id === this.currentVersionId,
        );
        //如果找不到版本，就默认第一个专业版
        currentVersionIndex =
          currentVersionIndex === -1 ? 0 : currentVersionIndex;
        this.filterConfigs(currentVersionIndex);
      }
    },
    configs: {
      deep: true,
      handler() {
        this.autoSetConfigs();
      },
    },
  },
};
</script>

<style lang="less">
.version-list {
  ul {
    display: flex;
    // justify-content: space-between;
    li {
      display: inline-block;
      border: 1px solid #e1e3e1;
      padding: 6px 18px;
      cursor: pointer;
      border-radius: 4px;
      margin-right: 10px;
      &.active {
        color: #03ac54;
        border-color: #03ac54;
      }
    }
  }
}
.config-list {
  li {
    width: 50%;
    display: inline-block;
    margin-top: 15px;
    label {
      vertical-align: middle;
    }
  }
}
.input-append {
  background-color: #e3dcdc;
  height: 30px;
  display: inline-block;
  line-height: initial;
  vertical-align: middle;
  padding: 6px 8px;
  margin-left: -6px;
  z-index: 2;
  border-radius: 0px 4px 4px 0px;
}
</style>
