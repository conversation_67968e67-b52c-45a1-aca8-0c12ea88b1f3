<template>
  <div class="list-advance-filter">
    <div class="advancedPanel">
      <Form style="text-align: left" :label-width="labelWidth">
        <FormItem :label="row.label" v-for="(row, index) in items" :key="index">
          <Row type="flex" class="filter" align="middle" style="margin-bottom: 15px" :gutter="row.gutter ? row.gutter : 30">
            <Col v-for="(filterItem, colIndex) in row.items" :key="colIndex">
            <span v-if="filterItem.label">{{filterItem.label}}</span>
            <Select
              :filterable="filterItem.filterable !== undefined ? filterItem.filterable : true"
              :clearable="filterItem.clearable !== undefined ? filterItem.clearable : true"
              v-model="filters[filterItem.key]"
              v-if="filterItem.type === filterType.select"
              :style="filterItem.style"
              :placeholder="filterItem.placeholder"
              @on-change="search">
              <template v-if="filterItem.data">
                <Option v-for="option in filterItem.data" :key="filterItem.key + option.value" :value="option.value">{{option.label}}</Option>
              </template>
            </Select>
            <list-table-date-picker
              class="date-range"
              v-if="[filterType.daterange, filterType.datetimerange].includes(filterItem.type)"
              v-model="filters[getExcludeFilterKey(filterItem)]"
              :value="filterItem.default && Array.isArray(filterItem.default) ? filterItem.default : []"
              :placeholder="filterItem.placeholder ? filterItem.placeholder : '请选择时间段'"
              :data-key="filterItem.key"
              :type="filterItem.type"
              @on-change="dateRangeChange"
            ></list-table-date-picker>
            <list-table-date-picker
              class="date-range"
              v-if="filterItem.type === filterType.date"
              v-model="filters[getExcludeFilterKey(filterItem)]"
              :value="filterItem.default"
              :placeholder="filterItem.placeholder ? filterItem.placeholder : '请选择日期'"
              :data-key="filterItem.key"
              type="date"
              @on-change="dateChange"></list-table-date-picker>
            <slot :name="getSlotName(filterItem)" v-if="filterItem.type === filterType.slot"></slot>
            <CheckboxGroup v-model="filters[filterItem.key]" v-if="filterItem.type === filterType.checkboxGroup">
              <Checkbox :label="checkBox.value" v-for="(checkBox, index) in filterItem.data" :key="index">
                <span>{{checkBox.label}}</span>
              </Checkbox>
            </CheckboxGroup>
            <RadioGroup v-model="filters[filterItem.key]" v-if="filterItem.type === filterType.radioGroup">
              <Radio :label="radio.value" v-for="(radio, index) in filterItem.data" :key="index">
                <span>{{radio.label}}</span>
              </Radio>
            </RadioGroup>
            <Input
              class="search-input"
              v-if="!filterItem.type || filterItem.type === filterType.input"
              v-model="filters[filterItem.key]"
              :style="filterItem.style"
              :placeholder="filterItem.placeholder"
              @on-enter="search">
            <Button slot="append" icon="ios-search" v-if="filterItem.showIcon" @click="search"></Button>
            </Input>
            </Col>
          </Row>
        </FormItem>
        <FormItem label="">
          <Button @click="reset">重 置</Button>
          <Button type="primary" @click="search">搜 索</Button>
        </FormItem>
      </Form>
      <p class="ishide text-center" @click="close">收 起 <Icon type="ios-arrow-up" size="16"></Icon></p>
    </div>
  </div>
</template>
<script>
  import mixin from './mixin';
  import ListTableDatePicker from './date-picker.vue';
  export default {
    name: "AdvanceFilter",
    components: {
      ListTableDatePicker
    },
    mixins: [mixin],
    props: {
      labelWidth: {
        type: Number,
        default: 90
      },
      /**
       * 列表筛选条件
       */
      items: {
        type: Array,
        default: () => {
          return []
        }
      }
    },
    data() {
      return {
        init: true,
        filters: {
        }
      }
    },
    watch: {
      items: {
        deep: true,
        handler() {
          this.initFilters();
        }
      },
      filters: {
        deep: true,
        handler(newValue) {
        }
      }
    },
    created() {
      this.initFilters();
    },
    methods: {
      search() {
        this.$emit('on-search', this.getFilters());
      },
      reset() {
        this.$emit('on-reset');
        this.initFilters();
      },
      close() {
        this.$emit('on-close');
      },
      emitChange() {
        this.$emit('on-change', this.filters);
      },
      getFilters() {
        return this.filters;
      },
      dateChange(date, dataKey) {
        this.filters[dataKey] = date;
      },
      dateRangeChange(dateRange, dataKey) {
        this.filters[dataKey[0]] = dateRange[0];
        this.filters[dataKey[1]] = dateRange[1];
      },
      initFilters () {
        this.init = true;
        this.items.forEach((row) => {
          if (!row.items || !Array.isArray(row.items)) {
            return false;
          }
          this.initFilterData('filters', row.items);
        });
        this.$nextTick(() => {
          this.init = false;
        });
      },
    }
  };
</script>
<style scoped lang="less">
  .list-filter {
    .date-range {
      width: 190px;
    }
    .search-input {
      width: 240px;
    }
  }
  .ivu-form-item {
    margin-bottom: 0;
  }
  .ivu-radio-group {
    margin-top: -7px;
  }
</style>
