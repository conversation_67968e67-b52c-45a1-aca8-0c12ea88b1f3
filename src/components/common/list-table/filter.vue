<template>
  <div class="list-filter">
    <Row type="flex" align="middle" :gutter="10">
      <Col v-for="(filterItem, index) in items" :key="index">
        <Select
          :filterable="
            filterItem.filterable !== undefined ? filterItem.filterable : true
          "
          :clearable="
            filterItem.clearable && filterItem.filterable ? true : false
          "
          v-model="filters[filterItem.key]"
          v-if="filterItem.type === filterType.select"
          :style="filterItem.style"
          :placeholder="filterItem.placeholder"
          @on-change="search"
        >
          <template v-if="filterItem.data">
            <Option
              v-for="option in filterItem.data"
              :key="filterItem.key + option.value"
              :value="option.value"
              >{{ option.label }}</Option
            >
          </template>
        </Select>
        <list-table-date-picker
          class="date-range"
          v-if="filterItem.type === filterType.daterange"
          v-model="filters[getExcludeFilterKey(filterItem)]"
          :value="
            filterItem.default && Array.isArray(filterItem.default)
              ? filterItem.default
              : []
          "
          :placeholder="
            filterItem.placeholder ? filterItem.placeholder : '请选择时间段'
          "
          :data-key="filterItem.key"
          type="daterange"
          @on-change="dateRangeChange"
        ></list-table-date-picker>
        <list-table-date-picker
          class="date-range"
          v-if="filterItem.type === filterType.date"
          v-model="filters[getExcludeFilterKey(filterItem)]"
          :value="filterItem.default"
          :placeholder="
            filterItem.placeholder ? filterItem.placeholder : '请选择日期'
          "
          :data-key="filterItem.key"
          type="date"
          @on-change="dateChange"
        ></list-table-date-picker>
        <Input
          class="search-input"
          v-if="!filterItem.type || filterItem.type === filterType.input"
          v-model="filters[filterItem.key]"
          :style="filterItem.style"
          :class="filterItem.showIcon && 'search-input-with-append'"
          :placeholder="filterItem.placeholder"
          clearable
          @on-enter="search"
          @on-clear="search"
        >
          <Button
            slot="append"
            icon="ios-search"
            v-if="filterItem.showIcon"
            @click="search"
          ></Button>
        </Input>
        <slot
          :name="getSlotName(filterItem)"
          v-if="filterItem.type === filterType.slot"
        ></slot>
      </Col>
    </Row>
  </div>
</template>
<script>
import mixin from "./mixin";
import ListTableDatePicker from "./date-picker.vue";
export default {
  name: "ListFilter",
  components: {
    ListTableDatePicker
  },
  mixins: [mixin],
  props: {
    /**
     * 列表筛选条件
     */
    items: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      init: true,
      filters: {}
    };
  },
  watch: {
    items: {
      deep: true,
      handler() {
        this.initFilters();
      }
    },
    filters: {
      deep: true,
      handler(newValue) {}
    }
  },
  created() {
    this.initFilters();
  },
  methods: {
    search() {
      if (!this.init) {
        this.emitChange();
      }
    },
    emitChange() {
      this.$emit("on-change", this.filters);
    },
    dateChange(date, dataKey) {
      this.filters[dataKey] = date;
      this.search();
    },
    dateRangeChange(dateRange, dataKey) {
      this.filters[dataKey[0]] = dateRange[0];
      this.filters[dataKey[1]] = dateRange[1];
      this.search();
    },
    initFilters() {
      this.init = true;
      this.initFilterData("filters", this.items);
      this.$nextTick(() => {
        this.init = false;
        // this.search();
      });
    }
  }
};
</script>
<style scoped lang="less">
.list-filter {
  .date-range {
    width: 190px;
  }
  .search-input {
    width: 240px;
    &-with-append /deep/ .ivu-input-icon-clear {
      right: 40px;
    }
  }
}
</style>
