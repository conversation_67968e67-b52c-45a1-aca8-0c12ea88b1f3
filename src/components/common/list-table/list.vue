<template>
  <div class="list-table">
    <div class="list-table-filter" v-if="showFilter">
      <slot name="filter">
        <Row type="flex" align="middle" :gutter="0">
          <Col>
            <list-filter :items="filterItems" @on-change="handleChangeFilter">
              <template v-for="filterItem in filterItems">
                <slot
                  :name="getSlotName(filterItem)"
                  :slot="getSlotName(filterItem)"
                ></slot>
              </template>
            </list-filter>
          </Col>
          <Col :style="{ paddingLeft: '10px' }">
            <strong
              class="advanced-search-btn"
              v-if="showAdvance"
              :class="{ active: selfShowAdvance }"
              @click="selfShowAdvance = !selfShowAdvance"
              >高级筛选 <Icon type="ios-arrow-down" size="16"></Icon
            ></strong>
          </Col>
          <Col class="filter-right"><slot name="filter-right"></slot></Col>
        </Row>
      </slot>
      <transition name="dropdown-fade">
        <advance-filter
          v-show="selfShowAdvance"
          class="advance-filter"
          :items="advanceFilterItems"
          @on-close="closeAdvance"
          @on-reset="resetAdvance"
          @on-search="searchAdvance"
        >
          <template v-for="row in advanceFilterItems">
            <template v-for="filterItem in row.items">
              <slot
                :name="getSlotName(filterItem)"
                :slot="getSlotName(filterItem)"
              ></slot>
            </template>
          </template>
        </advance-filter>
      </transition>
    </div>
    <slot name="before-table"></slot>
    <Table
      ref="table"
      :class="tableClass"
      :columns="selfColumns"
      :data="list"
      :loading="loading"
      :height="selfHeight"
      :no-data-text="noDataText"
      :before-load-list="beforeLoadListData"
      :after-load-list="afterLoadListData"
      @on-sort-change="onSortChange"
      @on-select="onSelect"
      @on-row-click="onRowClick"
      @on-select-cancel="onSelectCancel"
      @on-select-all="onSelectAll"
      @on-selection-change="onSelectionChange"
    >
      <slot slot="footer" name="footer"></slot>
    </Table>
    <slot name="after-table"></slot>
    <Row type="flex" align="middle" v-show="showPagination">
      <Col class="flex-con"><slot name="page-left"></slot></Col>
      <Col align="right">
        <Page
          show-elevator
          show-total
          show-sizer
          placement="top"
          class="js-after-table"
          :total="page.total"
          :current="page.currentPage"
          :page-size="page.pageSize"
          :page-size-opts="pageSizeOpts"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
        >
        </Page>
      </Col>
    </Row>
  </div>
</template>
<script>
import listFilter from "./filter.vue";
import advanceFilter from "./advance-filter.vue";
import { get, post } from "@api/request.js";
import mixin from "./mixin";
import * as TableUtil from "@/util/table";
import commontUtil from "@/util/common";

let MIN_PAGE_SIZE = 10;
export default {
  name: "ListTable",
  autoRegister: true,
  mixins: [mixin],
  components: {
    listFilter,
    advanceFilter
  },
  props: {
    api: "",
    requestType: {
      type: String,
      default: "get"
    },
    columns: {
      type: Array,
      default: () => []
    },
    tableHeight: {
      type: [String, Number],
      default: ""
    },
    /**
     * 是否自动加载数据, 为false需要手动调用获取数据的方法才会向后段发送请求
     */
    autoLoadData: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示分页
     */
    showPagination: {
      type: Boolean,
      default: true
    },
    paginationWidth: {
      type: Number,
      default: 18
    },
    /**
     * 筛选项目
     * [
     *   {
     *     type: '',
     *     data: '',
     *     key: '',
     *     default: '',
     *     data: '',
     *   }
     * ]
     */
    filterItems: {
      type: Array,
      default: () => []
    },
    /**
     * 高级筛选项目
     * [
     *   {
     *     label: '',
     *     items: [
     *       {
     *         type: '',
     *         data: '',
     *         key: '',
     *         default: '',
     *         data: '',
     *       }
     *     ]
     *   }
     * ]
     */
    advanceFilterItems: {
      type: Array,
      default: () => []
    },
    /**
     * 是否显示列表筛选
     */
    showFilter: {
      type: Boolean,
      default: true
    },
    /**
     * 是否显示高级筛选
     */
    showAdvance: {
      type: Boolean,
      default: false
    },
    /**
     * 固定右侧操作列
     */
    fixedOp: {
      type: Boolean,
      default: false
    },
    /**
     * 列表筛选配置
     */
    filterConfig: {
      type: Object,
      default: () => {
        return {
          width: 16,
          filterCols: []
        };
      }
    },
    /**
     * 列表筛选条件
     */
    filters: {
      type: Object,
      default: () => {
        return {};
      }
    },
    rowClassName: {
      type: Function,
      default: () => {}
    },
    //  将来会被废弃、用beforeRequest代替
    beforeLoadList: {
      type: Function,
      default: () => {}
    },
    beforeRequest: {
      type: Function,
      default: params => params
    },
    afterLoadList: {
      type: Function,
      default: () => {}
    },
    firstLoadingData: {
      type: Boolean,
      default: false
    },
    pageSizeOpts: {
      type: Array,
      default: () => [MIN_PAGE_SIZE, 20, 30, 40]
    },
  },
  data() {
    return {
      selfColumns: [],
      loading: false,
      advanceFilters: {},
      listFilters: {},
      list: [],
      selfShowAdvance: false,
      selfHeight: 0,
      page: {
        pageSize: MIN_PAGE_SIZE,
        totalPage: 0,
        total: 0,
        currentPage: 1
      }
    };
  },
  watch: {
    filterConfig: {
      deep: true,
      handler(newValue) {
        this.selfFilterConfig = newValue;
        this.initFilterConfig();
        this.initFilters();
        this.initAdvanceFilters();
      }
    },
    filters: {
      deep: true,
      handler(filters) {
        this.loadListData();
      }
    },
    filterItems: {
      deep: true,
      handler(filters) {
        this.initFilters();
        this.loadListData();
      }
    },
    columns: {
      deep: true,
      handler() {
        this.initOpCol();
      }
    }
  },
  mounted() {
    this.setHeight();
  },
  created() {
    this.page.pageSize = this.pageSizeOpts[0]
    this.loading = this.api ? true : false
    if (!this.firstLoadingData) {
      this.initOpCol();
      this.initFilters();
      if (this.autoLoadData) {
        this.loadListData();
      }
    } else {
      this.loadListData();
      this.initOpCol();
      this.initFilters();
    }

  },
  computed: {
    tableClass() {
      return [{ "table-fix-op": this.fixedOp }];
    },
    noDataText(){
      if(this.loading){
        return ' '
      }else{
        return  "暂无数据"
      }
    }
  },
  methods: {
    setHeight() {
      if (this.tableHeight) {
        this.selfHeight = this.tableHeight;
        return true;
      }
      if (!this.$refs.table) {
        this.selfHeight = this.getTableHeight() - 20;
        return false;
      }
      this.selfHeight =
        window.innerHeight -
        55 - // 底部分页和pading所占空间
        this.$refs.table.$el.getBoundingClientRect().top;
    },
    setSelfColumns() {
      let columns = TableUtil.initColumnsWidth({
        columns: this.columns,
        tableId: TableUtil.tableId.out_stock
      });
      this.selfColumns = columns;
    },
    /**
     * 初始化操作列
     */
    initOpCol() {
      this.columns.forEach(col => {
        /**
         * actionList格式 ［
         *     action: (rowParams) => {}, // 按钮操作函数
         *     confirm: "", // 是否需要确认操作
         *     name: "", //操作按钮名称
         *     type: "", // redirect|normal redirect: 页面跳转，normal: 普通操作, event: 事件
         *     eventName: "", //事件名， 仅type为event时有效
         *     url: "", // API地址｜跳转地址
         *     params: [], // 参数，如果是跳转作为query参数
         * ］
         */
        if (col.actionList && col.actionList.length > 0) {
          col.resizable = false;
          col.width || (col.width = 1);
          if (this.fixedOp) {
            col.width = 60;

            col.renderHeader = h => {
              return h("span", "");
              return h("Icon", {
                props: {
                  type: "grid",
                  size: 20
                },
                style: {
                  color: "#333"
                }
              });
            };
          }
          col.render = (h, params) => {
            let data = params.row;
            let actionList = col.actionList.map(action => {
              // 按钮名称
              let actionName = action.name;
              // 按钮类名
              let actionClass = {};
              // 按钮自定义属性
              let attrs = {};
              if (action.render) {
                return action.render(h, params);
              }
              // 按钮名称灵活定义
              if (action.name && typeof action.name === "function") {
                actionName = action.name(params);
              }
              if (action.class && typeof action.class === "function") {
                actionClass = action.class(params);
              } else {
                actionClass = action.class;
              }
              if (action.attrs) {
                if (typeof action.attrs === "function") {
                  attrs = action.attrs(params);
                } else {
                  attrs = action.attrs;
                }
              }
              return h(
                "span",
                {
                  class: actionClass,
                  attrs: attrs,
                  on: {
                    click: () => {
                      if (action.action) {
                        action.action(params); // todo
                      } else {
                        let actionParams = {};
                        if (action.params && action.params.length > 0) {
                          if (typeof action.params === "string") {
                            if (data[action.params]) {
                              actionParams[action.params] = data[action.params];
                            }
                          } else {
                            action.params.map(p => {
                              if (data[p]) {
                                actionParams[p] = data[p];
                              }
                            });
                          }
                        }
                        if (action.extraParams) {
                          let extraParams = {};
                          if (
                            action.extraParams &&
                            typeof action.extraParams === "function"
                          ) {
                            extraParams = action.extraParams(params, action);
                          }
                          actionParams = Object.assign(
                            {},
                            actionParams,
                            extraParams
                          );
                        }
                        switch (action.type) {
                          // 页面跳转
                          case "redirect": {
                            this.router.push({
                              path: action.url,
                              query: actionParams
                            });
                            break;
                          }
                          // 导出
                          case "export": {
                            get(action.url, actionParams).then(res => {
                              let { status, message, data } = res;
                              if (status) {
                                let url = data.url || data;
                                location.href = url;
                              } else {
                                this.modalError(message);
                              }
                            });
                            break;
                          }
                          // 事件
                          case "event": {
                            if (action.eventName) {
                              this.$emit(action.eventName, params);
                            }
                            break;
                          }
                          // 普通ajax操作
                          default: {
                            if (!action.url) {
                              return false;
                            }
                            const doRequest = (url, params) => {
                              get(url, params).then(res => {
                                let { status, message } = res;
                                if (status) {
                                  this.loadListData(false);
                                } else {
                                  this.modalError({
                                    content: message
                                  });
                                }
                              });
                            };
                            if (action.confirm) {
                              let confirm =
                                typeof action.confirm === "function"
                                  ? action.confirm(h, params)
                                  : action.confirm;
                              let modalConfig = {
                                content: "",
                                onOk: () => {
                                  doRequest(action.url, actionParams);
                                }
                              };
                              if (typeof confirm === "string") {
                                modalConfig.content = confirm;
                              } else {
                                modalConfig.render = () => {
                                  return confirm;
                                };
                              }
                              this.$Modal.confirm(modalConfig);
                            } else {
                              doRequest(action.url, actionParams);
                            }
                          }
                        }
                      }
                    }
                  }
                },
                actionName
              );
            });
            if (this.fixedOp) {
              return h(
                "Dropdown",
                {
                  class: ["op-wrap"],
                  props: {
                    transfer: true,
                    trigger: "hover",
                    placement: "bottom-end"
                  }
                },
                [
                  h("Icon", {
                    class: ["pointer"],
                    props: {
                      type: "ios-more",
                      size: 20
                    }
                  }),
                  h(
                    "div",
                    {
                      slot: "list",
                      class: ["op-list"]
                    },
                    actionList
                  )
                ]
              );
            } else {
              return h(
                "ul",
                {
                  class: {
                    "show-edit-btn": true,
                    //                    [this.rowBtnWrapClass]: true
                    "table-row-btn-wrap": true
                  },
                  style: {
                    "text-align": "left"
                  }
                },
                actionList
              );
            }
          };
        }
      });
      this.setSelfColumns();
    },
    initFilters() {
      this.initFilterData("listFilters", this.filterItems);
    },
    initAdvanceFilters() {
      this.advanceFilterItems.forEach(row => {
        this.initFilterData("advanceFilters", row.items);
      });
    },
    handleChangeFilter(filters) {
      this.listFilters = filters;
      this.$emit("on-filter", filters);
      this.loadListData();
    },
    closeAdvance() {
      this.selfShowAdvance = false;
    },
    /**
     * 重置高级筛选
     */
    resetAdvance() {
      this.$emit("on-advance-reset");
      this.initAdvanceFilters();
      this.closeAdvance();
    },
    /**
     * 高级筛选搜索
     */
    searchAdvance(filters) {
      this.advanceFilters = filters;
      this.loadListData();
      this.closeAdvance();
    },
    /**
     * 获取列表筛选参数
     */
    getFilters() {
      let filters = this.deepClone(this.filters);
      let listFilters = {
        ...this.listFilters,
        ...this.advanceFilters
      };
      filters = Object.assign(listFilters, filters);
      filters = this.excludeFilters(filters);
      return filters;
    },
    /**
     * 从服务端获取数据
     * @param resetPage 获取数据前是否重置分野
     */
    loadListData(resetPage) {
        this.loading = true;
        this.list = [];
        let params = this.getFilters();
        if (resetPage !== false) {
          this.resetPage();
        }

        params.page = this.page.currentPage;
        params.pageSize = this.page.pageSize;

        // 没有分页
        if (!this.showPagination) {
          delete params.page;
          delete params.pageSize;
        }

        // 列表数据渲染完成触发实践   TODO： 将来会废弃、用beforeRequest代替
        this.beforeLoadListData(params);
        // 发送筛选数据给父组建
        this.onSearchParams({
          searchKey: params.searchKey,
          receStyle: params.receStyle,
          area_id: params.area_id
        });

        params = this.beforeRequest(params);
        if (!params) {
          return false;
        }

        const request = this.requestType === "get" ? get : post;
        request(this.api, params).then(res => {
          this.loading = false;
          const { data, status, message } = res;
          if (status) {
            let { list, pageParams } = data;

            list.forEach(item => {
              item._disabled = false;
            });
            this.afterLoadListData(list, data);

            this.list = this.deepClone(list);
            if (this.showPagination) {
              this.page.totalPage = pageParams.total_page;
              this.page.total = parseInt(pageParams.count);
              this.onTotalSize(this.page.total);
            }
          } else {
            this.list = [];
            this.resetPage();
            this.errorNotice({ desc: message || '获取数据失败，请重试' })
          }
          // 列表数据渲染完成触发事件
          this.$nextTick(() => {
            this.$emit("after-render", this.list, res);
          });
        });
    },
    /**
     * 从服务端获取数据前的钩子函数
     * @param params 请求参数
     */
    beforeLoadListData(params) {
      this.beforeLoadList &&
        typeof this.beforeLoadList &&
        this.beforeLoadList(params);
    },
    /**
     * 从服务端获取数据后的钩子函数
     * @param list 服务器返回的列表数据
     */
    afterLoadListData(list, data) {
      this.afterLoadList &&
        typeof this.afterLoadList &&
        this.afterLoadList(list, data);
    },
    /**
     * 获取列表数据
     */
    getListData() {
      return this.list;
    },
    /**
     * 设置列表数据
     */
    setListData(list) {
      this.list = list;
    },
    // 更新某一列
    updateRow(index, row = {}) {
      this.list[index] = row
    },
    onSortChange(sortData) {
      let { column, key, order } = sortData;
      this.$emit("on-sort-change", column, key, order);
    },
    onSelect(selection, row) {
      this.$emit("on-select", selection, row);
    },
    onSelectAll(selection) {
      this.$emit("on-select-all", selection);
    },
    onSelectionChange(selection) {
      this.$emit("on-selection-change", selection);
    },
    onSelectCancel(selection, row) {
      this.$emit("on-select-cancel", selection, row);
    },
    onRowClick(row, index) {
      this.$emit("on-row-click", row, index);
    },
    onTotalSize(nums) {
      this.$emit("on-total-size", nums);
    },
    onSearchParams(params) {
      this.$emit("on-search-params", params);
    },
    resetPage() {
      this.page.totalPage = 0;
      this.page.total = 0;
      this.page.currentPage = 1;
    },
    changePage(page) {
      this.page.currentPage = page;
      this.loadListData(false);
    },
    changePageSize(pageSize) {
      this.page.pageSize = pageSize;
      this.loadListData();
    }
  }
};
</script>
<style scoped lang="less">
.list-table {
  .list-table-filter {
    padding: 8px 0;
  }
  .advance-filter {
    margin-top: 15px;
  }
  .filter-right {
    flex: 1;
  }
  .advanced-search-btn {
    cursor: pointer;
    position: absolute;
    display: inline-block;
    width: 115px;
    text-align: center;
    height: 45px;
    line-height: 35px;
    bottom: -30px;
    z-index: 2;
    &.active {
      background: #f3f5f2;
    }
  }
}
</style>
