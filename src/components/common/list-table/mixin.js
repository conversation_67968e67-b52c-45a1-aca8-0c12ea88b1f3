/**
 * Created by ddcoder on 19/2/27.
 */
import { getSlotName, filterType } from './util';
export default {
  data () {
    return {
      filterType,
      excludeFilterPrefix: '_exclude_'
    }
  },
  methods: {
    /**
     * 获取自定义筛选项的slot名称
     * @param filterItem 自定义筛选项信息
     */
    getSlotName(filterItem) {
      return getSlotName(filterItem);
    },
    /**
     * 获取不需要传给后端的字段名称, 传递参数的时候会对这些字段进行排除
     * @param filterItem
     * @returns {string}
     */
    getExcludeFilterKey(filterItem) {
      return Array.isArray(filterItem.key) ? this.excludeFilterPrefix + filterItem.key.join('_') : this.excludeFilterPrefix + filterItem.key;
    },
    /**
     * 排除不需要传递给后台的筛选项
     * @param filters
     */
    excludeFilters(filters) {
      let newFilters = {};
      for (let filterKey in filters) {
        if (!filterKey.startsWith(this.excludeFilterPrefix)) {
          newFilters[filterKey] = filters[filterKey];
        }
      }
      return newFilters;
    },
    /**
     * 将筛选数据初始化到data中以便完成数据双向绑定
     * @param dataKey
     * @param filterItems
     */
    initFilterData (dataKey, filterItems) {
      let filtersKeys = Object.keys(this[dataKey]);
      filterItems.map((filterCol) => {
        let filterKey = filterCol.key;
        let modelKey = filterKey;
        let defaultValue = filterCol.default !== undefined ? filterCol.default : '';
        // 设置响应式数据
        if (!filtersKeys.includes(modelKey)) {
          switch (filterCol.type) {
            // 日期区间格式
            case this.filterType.daterange:
            case this.filterType.datetimerange:
              modelKey = this.getExcludeFilterKey(filterCol);
              if (!defaultValue || !Array.isArray(defaultValue)) {
                defaultValue = [];
              }
              this.$set(this.$data[dataKey], filterCol.key[0], defaultValue[0] ? defaultValue[0] : '');
              this.$set(this.$data[dataKey], filterCol.key[1], defaultValue[1] ? defaultValue[1] : '');
              break;
            case this.filterType.date:
            case this.filterType.slot:
              modelKey = this.getExcludeFilterKey(filterCol);
              break;
            default:
              break;
          }
          this.$set(this.$data[dataKey], modelKey, defaultValue);
        }
        // 初始化筛选条件默认值
        this.$data[dataKey][modelKey] = defaultValue;
      });
    }
  }
}
