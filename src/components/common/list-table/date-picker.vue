<template>
  <DatePicker
    ref="date"
    :transfer="true"
    :value="value"
    :placeholder="placeholder"
    :type="type"
    :options="options"
    @on-change="onChange"
    v-bind="$attrs"
    @on-open-change="onOpenChange"
    ></DatePicker>
</template>
<script>
  export default {
    name: "ListTableDatePicker",
    components: {
    },
    props: {
      value: {
        type: [String, Array],
        default: ''
      },
      dataKey: {
        type: [String, Array],
        default: ''
      },
      type: {
        type: String,
        default: ''
      },
      placeholder: {
        type: String,
        default: ''
      },
      options: {
        type: Object,
        default: () => {}
      },
    },
    data() {
      return {
      }
    },
    created() {
    },
    methods: {
      onChange(date) {
        this.$emit('on-change', date, this.dataKey);
      },
      handleClear () {
      	this.$refs.date.handleClear();
      },
      onOpenChange(flag) {
        this.$emit('on-open-change', flag);
      }
    }
  };
</script>
<style scoped lang="less">
</style>
