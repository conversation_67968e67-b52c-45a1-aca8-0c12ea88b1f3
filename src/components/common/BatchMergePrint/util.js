/*
 * @Author: lizhiwei😊
 * @Description: 
 * @LastEditors: lizhiwei😊
 * @Date: 2022-03-29 10:17:28
 * @LastEditTime: 2022-03-31 09:37:30
 */
// 不支持012100012这种错误格式判断
export function arabicToChinese(s) {
    const z = '零一二三四五六七八九十'
    if (s >= 10) {
      let _s = s
      s = s + ''
      s = s.split('').join('十')
      if (_s < 20) {
        s = s.substr(1)
      }
    }
    s = s + ''
    return s.replace(/\d/g, (i, j) => ((j == 0 && i == 0) || (s.length - 1 === j && i == 0) ? '' : z[i]))
} 
