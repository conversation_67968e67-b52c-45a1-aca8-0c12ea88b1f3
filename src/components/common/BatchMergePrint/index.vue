<template>
  <div>
    <!-- 确认按照订单标签拆分打印订单 -->
    <PrintErrorHintModal
      v-model="errorModal.show"
      @on-ok="commoditySummaryPrint"
      :data="errorModal.data"
      :title="errorModal.title"
    />
    <!-- 编辑分组模态框 -->
    <PrintEditCateModal
      :title="cateModal.title"
      width="800"
      v-model="cateModal.show"
      :show="cateModal.show"
      :groupData="cateModal.data"
      @on-ok="saveCateGroup"
    />
    <!-- 抽屉 -->
    <PrintModeDrawer
      v-model="drawer.show"
      @on-close="handleDrawerClose"
      @on-click-drawer-preview="_handleClickDrawerPreview"
      @on-click-drawer-print="_handleClickDrawerPrint"
      @on-click-drawer-export="_handleClickDrawerExport"
      @on-edit-category="handleEditCate"
      @clickLook="clickLook"
      :drawer="drawer"
      :bill="bill"
      :templateData="templateData"
      :groupTemplateData="groupTemplateData"
      :groupData="drawer.cateData"
    />
    <PrintTemplateExportChoose  :filterOld="false" btnText="打印" title="选择打印模板" ref="printTemplateBatchChoose"/>
  </div>
</template>

<script>
const {
  ORDER,
  GOODS,
  ORDER_MERGE_GOODS,
  ORDER_REMARK_SPLIT, // 订单备注拆分打印
  ORDER_TAG, // 按订单标签拆分打印
  ORDER_TAG_SPLIT, // 按订单标签拆开打印
  CUSTOM_CATE, // 自定义分类分组打印
  CUSTOM_MERGE_CATE,
  CATE_LEVEL_1, // 按一级分类分组
  CATE_LEVEL_2, // 按二级分类分组
  CATE_LEVEL_3,
  PRINT_MODE_DRAWER, // 缓存配置
} = require('./constant.js').default;
import PrintTemplateExportChoose from '@components/print-template-export-choose';
import PrintErrorHintModal from "./components/printErrorHintModal";
import PrintModeDrawer from "./components/printModeDrawer";
import PrintEditCateModal from "./components/printEditCateModal";
import settings from "@api/settings.js";
import ConfigMixin from "@/mixins/config";
import printerConfig from "@api/printerConfig.js";
import { uniqBy } from "lodash-es"
export default {
  mixins: [ConfigMixin],
  components: {
    PrintModeDrawer,
    PrintEditCateModal,
    PrintErrorHintModal,
    PrintTemplateExportChoose
  },
  data() {
    return {
      is_preview:false,
      templateData:[],
      groupTemplateData:[],
      // 错误模态框
      errorModal: {
        show: false,
        data: [],
        title: "订单标签拆分",
      },
      // 编辑分组模态框
      cateLevel1Data: Object.freeze([]), // 二维数组, 分组数据
      cateLevel2Data: Object.freeze([]), // 二维数组, 分组数据
      cateModal: {
        title: "",
        cateLevel: CATE_LEVEL_1,
        show: false,
        data: [],
        allCate: [],
      },
      // 传进去进行双向绑定了
      drawer: {
        show: false, // 抽屉显示隐藏
        defaultNoShow: false, // 默认不显示勾选框
        lock: false, //
        config: {
          orderMergeGoods: false, // 订单合并商品 / 按订单拆分
          mergeGoodsType:'0',
          wayPrint: ORDER, // 默认订单合并打印类型, GOODS为按商品汇总打印, ORDER_MERGE_GOODS 为按商品合并打印数量
          goodsType: ORDER_TAG, // 按订单标签拆分打印
          cateLevel: CATE_LEVEL_1, // 按一级分类分组打印
        },
        cateData: [], // 分组数据
      },
      templates: [],
      isExportExcel: false,
    };
  },
  props: {
    requestUrl: {
      type: String,
    },
    bill:{
      type:String,
      default:''
    },
    config: {
      type: Object,
      required: true,
      default: () => {
        return {
          idKey: "order_id", // 请求打印数据参数id的key 具体看使用地方
          getPrintDataIdKey: "id", // 获取打印数据的id的key 具体看使用地方
          printFn: () => {}, // 打印函数
          getPrintData: () => {}, // 拿到打印数据方法
        };
      },
    },
    // 表格的ref 同步打印次数
    tableRef: {
      required: true,
    },
    // 选择的数据
    selectedRows: {
      type: Array,
      required: true,
    },
  },
  created() {
    this.getDrawerByLocalStorage();
    this.getCategoryGroupData();
    this.fetchTemplateData()
    this.fetchGroupTemplateData()
  },
  activated() {
    this.getDrawerByLocalStorage();
  },
  watch: {
    "drawer.config": {
      handler(n) {
        this.storage.setLocalStorage(
          PRINT_MODE_DRAWER,
          this.drawer.config
        );
      },
      deep: true,
    },
    "drawer.config.cateLevel": {
      handler(n) {
        this.drawer.cateData = (n === CATE_LEVEL_1 ? this.cateLevel1Data : (n === CATE_LEVEL_2?this.cateLevel2Data:[]));
      },
      deep: true,
    },
  },
  methods: {
      async fetchGroupTemplateData() {
      const res = await this.$request.get(this.apiUrl.getPrintTemplate, {
        type: 'GROUP_ORDER',
      });
      const { list } = res.data;
      this.groupTemplateData = Array.isArray(list) ? list : [];
      },
      async fetchTemplateData() {
      const res = await this.$request.get(this.apiUrl.getPrintTemplate, {
        type: 'ORDER',
      });
      const { list } = res.data;
      this.templateData = Array.isArray(list) ? list : [];
    },
    clickLook(){
      this.drawer.show = false
    },
    /**
     * 拿到is_merge参数
     */
    getIsMergeParams() {
      const { wayPrint, goodsType, orderMergeGoods, mergeGoodsType } = this.drawer.config;
      if (orderMergeGoods) {
        if (wayPrint === GOODS && goodsType === ORDER_TAG){
          return 4
          // 按商品别名合并
        }else if(mergeGoodsType == 1){
          return 5
        }else{
          return 2
        }
      }
      if (wayPrint === ORDER_REMARK_SPLIT) return 3
      return 1
    },
    onPrintSelected(params = {},batch) {
      const { isMerge = 1, template_id, printType, seqFields, rangeParams = {}} = params;
      const { record_print_times = true } = rangeParams;

      const { idKey, printFn } = this.config;
      const is_order_split = this.drawer.config.breakUpByOrder ? 1 : 0

      // const { wayPrint } = this.drawer.config;
      // const isMerge = wayPrint === ORDER ? 1 : 2;
      // 打印发货单
      let ids = this.selectedRows.map((item) => item[idKey]);
      record_print_times && this.asyncPrintTimes(ids);
      if(batch) {
        this.$refs.printTemplateBatchChoose.open((templateId, preview) => {
        printFn({
          requestUrl: this.requestUrl,
          id: ids.join(),
          record_print_times,
          is_preview: this.is_preview,
          is_merge: isMerge,
          template_id:templateId,
          is_export_excel: this.isExportExcel,
          printType,
          seqFields,
          rangeParams,
          is_order_split,
        });
      });
      } else {
        printFn({
          requestUrl: this.requestUrl,
          id: ids.join(),
          record_print_times,
          is_preview: this.is_preview,
          is_merge: isMerge,
          is_export_excel: this.isExportExcel,
          template_id,
          printType,
          seqFields,
          rangeParams,
          is_order_split,
        });
      }
    },
    /**
     * 处理批量打印点击按钮事件
     */
    executor() {
      if (!this.drawer.lock) {
        // 赋值一次就行了,因为this.openShowDeliveryBatchPrintSidebar 不会实时更新
        this.drawer.lock = true;
        this.drawer.defaultNoShow = !this.openShowDeliveryBatchPrintSidebar;
      }
      if (!this.drawer.defaultNoShow) {
        this.drawer.show = true;
      } else {
        this.isExportExcel = false;
        this.handleClickDrawerPrint();
      }
    },
    _handleClickDrawerPreview(template_id,printType,seqFields,rangeParams){
      this.is_preview = true
      // 校验是否可以预览
      this.isExportExcel = false;
      this.handleClickDrawerPrint(template_id,printType,seqFields,rangeParams)
    },
    _handleClickDrawerPrint(template_id, printType,seqFields,rangeParams) {
      this.is_preview = false
      this.isExportExcel = false;
      this.handleClickDrawerPrint(template_id,printType,seqFields,rangeParams)
    },
   async _handleClickDrawerExport(template_id, printType,seqFields,rangeParams) {
      this.isExportExcel = true;
      let defaultTemp = null
      if(printType=='3'){
        defaultTemp = await settings.getPrintTemplate({ type: "GROUP_ORDER", id:template_id, });
      }else{
        defaultTemp = await settings.getPrintTemplate({ type: "ORDER", id:template_id, });
      }
      // 错误处理
      if (!this.isPass(defaultTemp)) return;
      this.handleClickDrawerPrint(template_id,printType,seqFields,rangeParams)
    },
    /**
     * @description: 抽屉内点击打印事件
     */
    handleClickDrawerPrint(template_id,printType,seqFields,rangeParams) {
      this.drawer.show = false;
      // 如果选择的是默认类型,则走之前的打印逻辑
      // drawer.config 传进去drawer双向绑定了
      const { wayPrint, goodsType } = this.drawer.config;
      const isMerge = this.getIsMergeParams()
      if (!this.openShowDeliveryBatchPrintSidebar) {
        if(this.isCanChooseDeliveryOrderPrintTemplate){
           this.onPrintSelected({isMerge,seqFields,rangeParams},'batch');
        }else{
           this.onPrintSelected({isMerge,seqFields,rangeParams});
        }
        return;
      }
      if (wayPrint === GOODS) {
        this.errorModal.title = goodsType === ORDER_TAG ? "订单标签拆分" : (goodsType== CUSTOM_CATE  ? "自定义分类分开":'自定义分类合并');
      }
      // 按订单备注拆分
       if (wayPrint === ORDER_REMARK_SPLIT) {
        if (!this.isOpenOrderCommodityRemarkSplit) {
          this.errorMessage('请勾选打印类型')
          return
        } else {
          this.onPrintSelected({isMerge,template_id,printType,seqFields,rangeParams})
        }
      }
      // 按订单
      wayPrint === ORDER && this.onPrintSelected({isMerge,template_id,printType,seqFields,rangeParams});
      //按商品分类
      wayPrint === GOODS && this.checkTemplateType(template_id,printType,seqFields,rangeParams); // 检查模板类型
    },
    /**
     * 错误判断
     */
    isPass(defaultTemp) {
      if (!this.isEnableNewVersionPrint) {
        this.modalError("此功能仅支持新版打印,请前往设置页面开启新版打印!");
        return false;
      }
      if (!settings.isNewTemplate(defaultTemp)) {
        this.modalError(
          "此功能仅支持新版打印模板，请切换默认模板为新版打印模板再尝试!"
        );
        return false;
      }
      return true;
    },
    /**
     * 得到打印数据
     */
    async getPrintData(printType,defaultTempId,seqFields,rangeParams) {
      // mixin方法 获取打印数据
      const { idKey, getPrintDataIdKey, isMerge, getPrintData } = this.config;
      const { wayPrint, goodsType, cateLevel, orderMergeGoods, mergeGoodsType } = this.drawer.config;

      let params = {
        [getPrintDataIdKey]: this.selectedRows
          .map((item) => item[idKey])
          .join(),
        is_bill: 0,
        is_merge: isMerge,
      };
      params.is_merge = this.getIsMergeParams();
      // 按集团打印
      if(printType==3){
        params.merge_type = 2
        if(params.is_merge==2){
          params.select_prt_tpl_id = defaultTempId
        }
      }
      seqFields && (params.sequence_field = seqFields)
      if(rangeParams&&rangeParams.customize_scope_type==1){
        params.customize_scope_type = 1
        params.customize_scope_ids = rangeParams.customize_scope_ids
      }else if(rangeParams&&rangeParams.customize_scope_type==2){
        params.customize_scope_type = 2
        params.customize_scope_ids = rangeParams.customize_scope_ids
      }else if(rangeParams&&rangeParams.customize_scope_type=='custom'){
        params.print_scope_id = rangeParams.print_scope_id
      }
      // 自定义分组分类打印,需要拆分数据
      if (wayPrint === GOODS && (goodsType === CUSTOM_CATE || goodsType==CUSTOM_MERGE_CATE)) {
        params.group_entity_type = cateLevel === CATE_LEVEL_1 ? '1' : (cateLevel === CATE_LEVEL_2 ? '2': (printType==3?'4':'3'));
      }
      // 假如合并打印 group_mode 传2
      if(goodsType==CUSTOM_MERGE_CATE){
        params.group_mode = 2
      }
      // 按订单标签拆开打印
      if (goodsType === ORDER_TAG_SPLIT) {
        params.is_merge = 6;
        if (orderMergeGoods) {
          // 按商品名称合并
          if (mergeGoodsType === '0') {
            params.is_merge = 7;
          }
          // 按商品别名合并
          if (mergeGoodsType === '1') {
            params.is_merge = 8;
          }
        }
      }
      let { status, data, message } = await getPrintData(params);

      if (!status) {
        this.errorMessage(message);
        return false;
      }
      if (goodsType === ORDER_TAG_SPLIT) {
        data.order = data.order.filter(order => order.item && order.item.length > 0);
      }
      return data;

    },
    /**
     * @description: 检查模板类型是否符合要求
     */
    async checkTemplateType(template_id,printType,seqFields,rangeParams) {
      let defaultTemp = await settings.getPrintTemplate({ type: (printType=='3'?'GROUP_ORDER ': "ORDER"), id:template_id, }).catch(err=>{
        this.$Modal.confirm({
          title: '提示',
          content: '未配置打印模板，请先配置打印模板！点击确定，将跳转至打印配置页面。',
          onOk: () => {
            this.$router.push('/printConfig')
          },
          onCancel: () => {

          }
          })
      })
      if (!defaultTemp) return;
      let defaultTempId = defaultTemp.id;
      // 错误处理
      if (!this.isPass(defaultTemp)) return;
      // 拿到数据, 因为打印模板的id是放在订单信息的接口里面,所以只能先拿到数据接口,再去判断模板.
      // 因为这里已经拿到了打印数据,所以调用接口的时候可以直接传递数据, 打印mixin里面判断了,如果有data就直接用,没有就调用接口再次请求订单数据
      const data = await this.getPrintData(printType,defaultTempId,seqFields,rangeParams);
      if (!data) return;
      // 将获取到的data存起来. 等会调用打印mixin时传过去,这样mixin里面就不用再次调用获取获取数据接口.
      this.printData = data;
      if (!data.order || !Array.isArray(data.order)) return;
      const templateIds = [];
      // 打印模板id,对应的订单信息
      const tplIdOfOrderMap = {};
      data.order.forEach((order) => {
        if(printType!=3){
        if (order.prt_tpl_id && !templateIds.includes(order.prt_tpl_id)) {
          templateIds.push(order.prt_tpl_id);
        }
        // 把订单信息放进去
        order.prt_tpl_id = +order.prt_tpl_id ? order.prt_tpl_id : defaultTempId;
        Array.isArray(tplIdOfOrderMap[order.prt_tpl_id])
          ? tplIdOfOrderMap[order.prt_tpl_id].push(order)
          : (tplIdOfOrderMap[order.prt_tpl_id] = [order]);
        }

      });
      // 最后放一个默认的进去
      templateIds.push('')
      let templates = await Promise.all(
        templateIds.map((id) =>
          settings.getPrintTemplate({
            type: (printType=='3'?'GROUP_ORDER ': "ORDER"),
            id:template_id||id,
          })
        )
      );
      // 将获取到的打印模板存起来. 等会调用打印mixin时传过去,这样mixin里面就不用再次调用获取打印模板接口.
      this.templates = this.deepClone(templates);
      const templatesMap = {};
      templates.forEach((tpl) => {
        templatesMap[Number(tpl.id)] = tpl;
      });
      // 是否弹窗提示框
      let isShowModal = false;
      // 判断模板是否符合要求, 老版打印不行,分类汇总样式不行
      this.errorModal.data = [];
      for (const key in templatesMap) {
        if (Object.hasOwnProperty.call(templatesMap, key)) {
          const template = templatesMap[key];
          // 老版打印不行
          if (+template.version === 1) {
            isShowModal = true;
            const text = "老版打印模板不支持";
           tplIdOfOrderMap[key]&&tplIdOfOrderMap[key].forEach((order) => {
              this.errorModal.data.push({
                user: order.user_name,
                question: text,
              });
            });
          } else {
            // 新版打印分类汇总不行
            if (template.tpl_data.config.print_style === "multi_column_3") {
              isShowModal = true;
              const text =
                "新版打印模板仅支持默认单列样式, 双列样式,分类汇总样式,不支持按三列样式";
              // 如果取不到key就是默认模板
              if (tplIdOfOrderMap[key]) {
                tplIdOfOrderMap[key].forEach((order) => {
                  this.errorModal.data.push({
                    user: order.user_name,
                    question:
                      key == defaultTempId ? `【默认模板】- ${text}` : text,
                  });
                });
              }
            }
          }
        }
      }
      if (isShowModal) {
        // 去重
        this.errorModal.data = uniqBy(this.errorModal.data, 'user');
        this.errorModal.show= isShowModal;
      } else {
        this.commoditySummaryPrint(printType,seqFields,rangeParams);
      }
    },
    /**
     * @description: 按商品汇总合并打印
     */
    commoditySummaryPrint(printType,seqFields,rangeParams) {
      // 打印发货单
      const { idKey, printFn, isMerge } = this.config;
      let ids = this.selectedRows.map((item) => item[idKey]);
      this.asyncPrintTimes(ids);
      try {
        printFn({
          id: ids.join(","),
          is_preview: this.is_preview,
          is_merge: isMerge,
          way_print: this.drawer.config.goodsType, // 打印方式,按订单标签拆分打印,
          _templates: this.templates,
          _data: this.printData,
          is_export_excel: this.isExportExcel,
          printType,
          seqFields,
          rangeParams
        });
      } catch (e) {
        console.log(JSON.parse(JSON.stringify(e)), 'AAAAAAAAAAAAAAAAAAAAAAAAAA')
      }

      this.drawer.show = false;
    },
    /**
     * @description: 同步打印次数
     */
    asyncPrintTimes(ids) {
      const { idKey } = this.config;
      let list = this.tableRef.getData();
      list.forEach((item) => {
        if (ids.indexOf(item[idKey]) > -1) {
          item.print_times = +item.print_times + 1;
        }
      });
    },
    handleDrawerClose() {
      // 没有改变状态就不用保存
      if (!this.drawer.defaultNoShow) return;
      const params = [
        {
          key: "open_show_delivery_batch_print_sidebar",
          value: +!this.drawer.defaultNoShow,
        },
      ];
      settings.saveSystemConfig(params).then((res) => {
        let { message, status } = res;
        if (status) {
          this.successNotice("保存成功", 0);
        } else {
          this.errorNotice(message, 0);
        }
      });
    },
    getDrawerByLocalStorage() {
      // 拿到抽屉缓存配置
      const drawerConfigByLocalStorage = this.storage.getLocalStorage(
        PRINT_MODE_DRAWER
      );
      if (drawerConfigByLocalStorage) {
        this.drawer.config = drawerConfigByLocalStorage;
      }
    },
    /**
     * 得到分类分组数据
     */
    async getCategoryGroupData() {
      const params = {
        tpl_type: "ORDER",
        entity_type: 1 // 一级分类
      }
      const {data: cateLevel1} = await printerConfig.getTplGroup(params).catch((err) => {
        console.log(err);
      });
      this.cateLevel1Data = Object.freeze(cateLevel1);

      // ----------------------------------------------------------------
      params.entity_type = 2; // 二级分类
      const {data: cateLevel2} = await  printerConfig.getTplGroup(params).catch((err) => {
        console.log(err);
      });
      this.cateLevel2Data = Object.freeze(cateLevel2);
      this.updateDrawerCateData()
    },
    updateDrawerCateData() {
      this.drawer.cateData = this.drawer.config.cateLevel === CATE_LEVEL_1 ? this.cateLevel1Data : (this.drawer.config.cateLevel=== CATE_LEVEL_2?this.cateLevel2Data:[]);
    },
    /**
     * @description: 点击编辑按钮
     * @param {CATE_LEVEL_1|CATE_LEVEL_2} cateLevel
     */
    handleEditCate(cateLevel) {
      if (cateLevel === CATE_LEVEL_1) {
        this.cateModal.data = this.cateLevel1Data
        this.cateModal.title = '编辑一级分类'
      }
      if (cateLevel === CATE_LEVEL_2) {
        this.cateModal.data = this.cateLevel2Data
        this.cateModal.title = '编辑二级分类'
      }
      if (cateLevel === CATE_LEVEL_3) {
         this.cateModal.data =[]
      }
      this.cateModal.cateLevel = cateLevel
      this.cateModal.show = true
    },
    /**
     * 保存
     */
    async saveCateGroup(data,callback) {
      if (!data) return;
      const params = {
        tpl_type: "ORDER",
        entity_type: 1,
        record_list: JSON.stringify(data)
      }
      let result;
      if (this.cateModal.cateLevel === CATE_LEVEL_1) {
        this.cateLevel1Data = data
        params.entity_type = 1;
        result = await printerConfig.saveGroup(params)
      }

      if (this.cateModal.cateLevel === CATE_LEVEL_2) {
        this.cateLevel2Data = data
        params.entity_type = 2;
        result = await printerConfig.saveGroup(params)
      }
      if (result.status) {
        callback()
        this.updateDrawerCateData()
        this.successNotice('保存成功')
      } else {
        this.errorNotice(result.message)
      }
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
