<!--
 * @Author: lizhiwei😊
 * @Description: 编辑商品分类模态框
 * @LastEditors: lizhiwei😊
 * @Date: 2022-03-30 11:27:49
 * @LastEditTime: 2022-04-06 14:15:04
-->
<template>
    <div class="w">
      <div class="group" v-for="(list, index) in groupData" :key="index">
        <div class="pb10" >  {{list[0].category_name || '分组' + arabicToChinese(index + 1)}}</div>
        <div class="group-ul" >
          <Button
            class="group-ul-item"
            v-for="_ in list"
            :key="_.entity_id"
          >
            {{ _.entity_name }}
          </Button>
        </div>
      </div>
  </div>
</template>

<script>
import { arabicToChinese } from '../util.js';
export default {
  name: "drawerCateTable",
  props: {
    // groupData 二维数组,每一组的数据
    groupData: {
      type: Array,
      default: () => []
    },
  },
  methods: {
    arabicToChinese
  }
};
</script>

<style scoped>

.group {
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 3px;
}
.group-ul {
  display: flex;
  flex-wrap: wrap;
  min-height: 50px;
}
.group-ul-item {
  height: 40px;
  margin: 0 10px 10px 0;
}

.group-ul-item i {
  cursor: pointer;
}
</style>
