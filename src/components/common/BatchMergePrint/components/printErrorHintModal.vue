<template>
  <Modal
    v-bind="$attrs"
    v-on="$listeners"
    >
    <p slot="header" style="font-weight: bold;">
        <Icon color="#ff8d24" size="22" type="ios-information-circle"></Icon>
        <span>确认按照{{title}}打印订单?</span>
    </p>
    <p>{{title}}打印订单仅支持新版打印且应用【默认单列样式，双列样式，分类汇总样式】，当前订单中存在有打印样式不一致。</p>
    <p><span style="color: red">点击确认，将会按照打印配置中的默认模板默认单列样式进行继续打印；</span></p>
    <p><span style="color: red">点击取消，则取消该次打印，请手动修改模板。</span></p>
  <Table :columns="columns" :data="data" :height="300" />
</Modal>
</template>

<script>
//     v-model="drawer.modalShow"
    // @on-ok="commoditySummaryPrint"
  export default {
    name: 'PrintErrorHint',
    props: {
      title: {
        type: String,
        default: '自定义分类分开'
      },
      data: {
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
         columns: [
          {
            title: "客户名称",
            width: 200,
            key: "user",
          },
          {
            title: "存在问题",
            key: "question",
          },
        ],
      }
    }
  }
</script>

<style lang="scss" scoped>

</style>
