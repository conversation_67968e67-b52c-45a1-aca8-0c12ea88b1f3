<template>
  <Drawer class="print-drawer" width="400" v-bind="$attrs" v-on="$listeners"  :closable="false"	>
      <div slot="header" class="header">
        <div>
          <div class="header-title">选择打印模式</div>
          <div class="mt10"><Checkbox v-model="drawer.defaultNoShow" >默认不展示</Checkbox></div>
          <div><Checkbox @on-change="changeOrderMergeGoods" :disabled="drawer.config.wayPrint ===  ORDER_REMARK_SPLIT"  v-model="drawer.config.orderMergeGoods" >相同商品数量合并</Checkbox></div>
          <RadioGroup v-model="drawer.config.mergeGoodsType" v-if="drawer.config.orderMergeGoods" style="display:flex;">
            <Radio label="0">
                <span>按商品名称合并</span>
            </Radio>
            <Radio label="1">
                <span>按商品别名合并</span>
            </Radio>
          </RadioGroup>
          <div><Checkbox :disabled="drawer.config.wayPrint ===  ORDER_REMARK_SPLIT"  v-model="drawer.config.breakUpByOrder" >按订单拆分</Checkbox></div>
          <!-- <div><Checkbox @on-change="changeSplitOrder" :disabled="drawer.config.orderMergeGoods" v-model="drawer.config.splitOrder" >拆分打印</Checkbox></div> -->
        </div>
        <div class="btnAbsolute">
          <Button type="primary" @click="handleClickDrawerExport">导出</Button>
          <Button type="primary" @click="handleClickDrawerPrint">打印</Button>
          <Tooltip content="固定表尾模板不支持打印预览">
            <Button type="primary" :disabled="previewDisabled" @click="handleClickDrawerPrintPreview">打印预览</Button>
          </Tooltip>
        </div>
      </div>
      <div class="list_box">
        <div class="header-title">选择打印方式</div>
          <RadioGroup v-model="printType" vertical @on-change="printTypeChange">
          <Radio label="2">
              <span>按客户打印</span>
          </Radio>

          <Radio v-if="bill" disabled label="3">
              <span>按集团打印   </span>
                   <Tooltip
          :max-width="200"
          placement="top"
          transfer
          content="单据打印暂不支持按集团打印"
        >
          <i class="f20 iconfont icon-help" style="font-size:14px;" ></i>
        </Tooltip>
          </Radio>
           <Radio v-else label="3">
              <span>按集团打印</span>
          </Radio>
        </RadioGroup>
      </div>
      <div class="list_box">
        <div class="header-title">打印排序</div>
          <RadioGroup v-model="seqFields" vertical>
            <Radio label="default_code"> <span>按默认排序 </span>
              <Tooltip
          :max-width="200"
          placement="top"
          transfer
          content="默认订单合并打印与按订单标签拆单打印时打印顺序与发货单排序方式保持一致，自定义分类合并打印与分开打印时，排序方式与按照设置的分组顺序打印"
        >
          <i class="f20 iconfont icon-help" style="font-size:14px;" ></i>
        </Tooltip></Radio>
            <Radio label="commodity_code"> <span>按商品编码排序</span></Radio>
            <Radio label="commodity_sequence"> <span>按商品排序号排序</span></Radio>
            <Radio label="category_name"> <span>按商品分类名称排序</span></Radio>
            <Radio label="category_sequence"> <span>按商品分类排序号排序</span></Radio>
        </RadioGroup>
      </div>
      <div class="list_box">
        <div class="header-title">选择打印范围</div>
          <RadioGroup v-model="sortRange" vertical>
            <Radio label="all"> <span>打印全部分类</span></Radio>
            <Radio label="custom"> <span>打印指定分类</span> <span style="color:#00BA33;" @click.stop.prevent="setPrintRange">设置打印范围</span></Radio>
            <div class="cate_box" v-if="sortRange=='custom'">
             <div class="cate_item_box" :class="{'cate_item_box_active':item.checked}" @click="clickItem2(item)" v-for="item in rangeData" :key="item.id">{{item.name}}</div>
            </div>
            <Radio label="1"> <span>指定一级分类打印范围</span></Radio>
            <div class="cate_box" v-if="sortRange==1">
             <div class="cate_item_box" :class="{'cate_item_box_active':item.checked}" @click="clickItem(item)" v-for="item in cateLevel1Data" :key="item.entity_id">{{item.entity_name}}</div>
            </div>
            <Radio label="2"> <span>指定二级分类打印范围</span></Radio>
             <div class="cate_box" v-if="sortRange==2">
             <div class="cate_item_box" :class="{'cate_item_box_active':item.checked}" @click="clickItem(item)" v-for="item in cateLevel2Data" :key="item.entity_id">{{item.entity_name}}</div>
            </div>
        </RadioGroup>
      </div>
      <div class="list_box">
        <div class="header-title">选择打印模板</div>
          <RadioGroup v-model="ifTemplateCustom" vertical @on-change="radioChange">
          <Radio label="1">
              <span>按默认模板打印</span>
          </Radio>
          <Radio label="0">
              <span>自定义选择模板打印</span>
          </Radio>
        </RadioGroup>
        <div class="print_template_box" v-if="ifTemplateCustom==0">
            <div
            class="print_template_item"
            :class="{'print_template_item_active':currentTemplate==template.id}"
            :key="i"
            :title="template.name"
            @click="setCurrentTemplate(template)"
            v-for="(template, i) in realTemplateData"
            >{{ template.name }}</div>
            <div class="noData" v-if="realTemplateData&&realTemplateData.length==0">
              暂无模板数据
            </div>
        </div>
      </div>
      <div class="body" style="padding-bottom:50px;">
        <RadioGroup v-model="drawer.config.wayPrint" vertical>
          <Radio v-if="isOpenOrderCommodityRemarkSplit" :label="ORDER_REMARK_SPLIT" :disabled="drawer.config.orderMergeGoods">
              <span>订单按备注拆分打印</span>
          </Radio>
          <div v-if="isOpenOrderCommodityRemarkSplit" class="ml20">(使用该模式则同一订单下订单备注相同的订单商品拆分到另外张单进行打印）</div>
          <Radio :label="ORDER">
              <span>默认订单合并打印</span>
          </Radio>
          <Radio :label="GOODS">
              <span>按商品汇总合并打印</span>
          </Radio>
          <div class="ml20">(相同{{printType=='3'?'集团':'客户'}}订单按照商品汇总整单打印)</div>
        </RadioGroup>
        <!-- ---------------------------------- -->
        <RadioGroup  class="ml20" v-model="drawer.config.goodsType" vertical >
          <Radio :label="ORDER_TAG" :disabled="levelOneDisable">
              <span>按订单标签拆分打印</span>
          </Radio>
          <div class="ml20">(相同{{printType=='3'?'集团':'客户'}}订单按照商品汇总后按照不同订单标签整单打印)</div>
          <Radio :label="ORDER_TAG_SPLIT" :disabled="levelOneDisable">
            <span>按订单标签拆开打印</span>
          </Radio>
          <div class="ml20">(相同{{printType=='3'?'集团':'客户'}}订单将按照商品汇总后根据不同订单标签拆成一种订单标签一张单进行打印)</div>
          <Radio :label="CUSTOM_CATE" :disabled="levelOneDisable">
              <span>自定义分类分开打印</span>
          </Radio>
          <div class="ml20">(相同{{printType=='3'?'集团':'客户'}}订单将按照自定义分类的分组分开打印,一个分组一张单据；如果打印模板同时开启【按一级分类打印】则会优先按照分组进行分开打印）</div>
          <Radio :label="CUSTOM_MERGE_CATE" :disabled="levelOneDisable">
              <span>自定义分类合并打印</span>
          </Radio>
          <div class="ml20">(相同{{printType=='3'?'集团':'客户'}}订单将按照自定义分类的分组合并打印,一个{{printType=='3'?'集团':'客户'}}一张单据；如果打印模板同时开启【按一级分类打印】则会优先按照分组进行合并打印）</div>
        </RadioGroup>
          <!-- --------------------------------- -->
        <RadioGroup class="ml40" v-model="drawer.config.cateLevel" vertical >
             <Radio class="cateflex" :label="CATE_LEVEL_3" :disabled="levelTwoDisable">
            <div class="space-between w305">
              <span>按指定分组模板打印</span>
              <a @click.stop.prevent="editCate(CATE_LEVEL_3)" class="primary-color" :class="{disabled: levelTwoDisable}">查看指定分组模板</a>
            </div>
          </Radio>
          <Radio class="cateflex" :label="CATE_LEVEL_1" :disabled="levelTwoDisable">
            <div class="space-between w305">
              <span>按一级分类分组打印</span>
              <a @click.stop.prevent="editCate(CATE_LEVEL_1)" class="primary-color" :class="{disabled: levelTwoDisable}">编辑一级分类</a>
            </div>
          </Radio>
          <Radio class="cateflex" :label="CATE_LEVEL_2" :disabled="levelTwoDisable">
            <div class="space-between w305">
              <span>按二级分类分组打印</span>
              <a @click.stop.prevent="editCate(CATE_LEVEL_2)" class="primary-color" :class="{disabled: levelTwoDisable}">编辑二级分类</a>
            </div>
          </Radio>
        </RadioGroup>
      <DrawerCateTable :groupData="groupData" />
      </div>
    </Drawer>
</template>

<script>
const {
  ORDER_MERGE_GOODS, // 相同商品打印合计数量
  SPLIT_ORDER, // 拆分打印
  DEFAULT, // 默认
  ORDER, // 默认订单打印
  ORDER_REMARK_SPLIT, // 订单备注拆分打印
  GOODS, // 商品打印
  ORDER_TAG, // 按订单标签拆分打印
  ORDER_TAG_SPLIT, // 按订单标签拆开打印
  CUSTOM_CATE, // 自定义分类分组打印
  CUSTOM_MERGE_CATE,//自定义
  CATE_LEVEL_1, // 按一级分类分组
  CATE_LEVEL_2, // 按二级分类分组
  CATE_LEVEL_3,//指定分组
} = require('../constant.js').default;
import { get,post } from '@api/request.js';
import { api } from '@api/api.js';
import ConfigMixin from "@/mixins/config";
import DrawerCateTable from './drawerCateTable.vue'
  export default {
    name: 'PrintModeDrawer',
    mixins: [ConfigMixin],
    components: { DrawerCateTable },
    props: {
      // 骚操作
      drawer: {
        type: Object,
        required: true,
        default: () => {
          return {
            show: false, // 抽屉显示隐藏
            defaultNoShow: false, // 默认不显示勾选框
            lock: false, //
            config: {
              orderMergeGoods: false,
              breakUpByOrder:  false,
              mergeGoodsType:'0',
              wayPrint: ORDER, // 默认订单合并打印类型, GOODS为按商品汇总打印
              goodsType: ORDER_TAG, // 按订单标签拆分打印
              cateLevel: CATE_LEVEL_3, // 按一级分类分组打印
            },
          }
        },
      },
      bill:{
      type:String,
      default:''
      },
      templateData:{
        type:Array,
        default:() => {
          return []
        }
      },
      groupTemplateData:{
        type:Array,
        default:() => {
          return []
        }
      },
      groupData: {
        type: Array,
        required: true,
        default: () => {
          return []
        },
      },
    },
    data() {
      return {
        rangeData:[],
        cateLevel1Data:[],
        cateLevel2Data:[],
        sortRange:'all',
        seqFields:'default_code',
        printType:'2',
        ifTemplateCustom:'1',
        ORDER,
        ORDER_REMARK_SPLIT,
        ORDER_MERGE_GOODS,
        SPLIT_ORDER,
        DEFAULT,
        GOODS,
        ORDER_TAG,
        ORDER_TAG_SPLIT,
        CUSTOM_CATE,
        CATE_LEVEL_3,
        CATE_LEVEL_1,
        CATE_LEVEL_2,
        CUSTOM_MERGE_CATE,
        currentTemplate:'',
      }
    },
    computed: {
      levelOneDisable() {
        return  this.drawer.config.wayPrint !== GOODS
      },
      levelTwoDisable() {
        return (this.drawer.config.goodsType !== CUSTOM_MERGE_CATE && this.drawer.config.goodsType !== CUSTOM_CATE) || this.drawer.config.wayPrint !== GOODS
      },
      realTemplateData() {
        return  this.printType == 3?this.groupTemplateData:this.templateData
      },
      previewDisabled() {
        // 按默认模板打印，不做限制
        if (this.ifTemplateCustom === '1') {
          return false;
        }
        const template = this.realTemplateData.find((item) => item.id === this.currentTemplate);
        if (!template) {
          return false;
        }
        const tplData = JSON.parse(template.tpl_data);
        if (tplData.config && tplData.config.fixedFooter === 'Y') {
          return true;
        }
        return false
      }
    },
    watch: {

    },
    mounted(){
      this.getCategoryGroupData()
    },
    methods: {
      setCurrentTemplate(template) {
        this.currentTemplate = template.id;
      },
      getRange() {
        let print_scope_id =  ''
        let customize_scope_ids = ''
        let arr1 = []
        this.cateLevel1Data.forEach(e=>{
          if(e.checked){
            arr1.push(e.entity_id)
          }
        })
        let arr2 = []
        this.cateLevel2Data.forEach(e=>{
          if(e.checked){
            arr2.push(e.entity_id)
          }
        })
        if(this.sortRange==1){
          if(arr1&&arr1.length==0){
            this.warningMessage('请先选择一级分类！')
            return false
          }
          customize_scope_ids = arr1.join(',')
        }else if(this.sortRange==2){
          customize_scope_ids = arr2.join(',')
          if(arr2&&arr2.length==0){
            this.warningMessage('请先选择二级分类！')
            return false
          }
        }else if(this.sortRange=='custom'){
          let rangeItem = this.rangeData.find(res=>res.checked)
          if(rangeItem){
            print_scope_id = rangeItem.id
          }else{
            this.warningMessage('请先选择指定的打印分类')
            return false
          }
        }
        return {
          print_scope_id,
          customize_scope_ids,
          customize_scope_type:this.sortRange,
        }
      },
      clickItem2(item){
        if(item.checked){
          return
        }
        this.rangeData.forEach(res=>{
          res.checked = res.id == item.id
        })
      },
    clickItem(item) {
      item.checked = !item.checked
    },
      async getCategoryGroupData() {
        const {data: rangeData} = await get('/superAdmin/printerConfig/printScopeList',{is_paginate:0}).catch((err) => {
          console.log(err);
        });
        rangeData.forEach(res=>{
          res.checked = false
        })
        this.rangeData = rangeData || []

        const params = {
          tpl_type: "ORDER",
          entity_type: 1 // 一级分类
        }
        const {data: cateLevel1} = await get(api.delivery.template.getInitTplGroup ,params).catch((err) => {
          console.log(err);
        });
      cateLevel1[0].forEach(element => {
        element.checked = false
      });
      this.cateLevel1Data = cateLevel1[0]
        // ----------------------------------------------------------------
        params.entity_type = 2; // 二级分类
        const {data: cateLevel2} = await get(api.delivery.template.getInitTplGroup ,params).catch((err) => {
          console.log(err);
        });

      cateLevel2[0].forEach(element => {
        element.checked = false
      });
      this.cateLevel2Data = cateLevel2[0]
      },
      setPrintRange(e) {
        this.$emit("clickLook",false)
        this.$router.push({path:'/delivery/line/range'})
      },
      printTypeChange(e) {
        if(this.ifTemplateCustom==0){
          this.currentTemplate = this.realTemplateData[0]?this.realTemplateData[0].id:''
        }else{
           this.currentTemplate = ''
        }
      },
      radioChange(e){
        if(e==0){
          this.currentTemplate = this.realTemplateData[0]?this.realTemplateData[0].id:''
        }else{
           this.currentTemplate = ''
        }
      },
      changeOrderMergeGoods (value) {
      //   if (value) {
      //     this.drawer.config.goodsType = '';
      //   }
      },
      // changeSplitOrder (value) {
      //   if (value) {
      //     this.drawer.config.wayPrint = ORDER_REMARK_SPLIT;
      //   }
      // },
      handleClickDrawerPrintPreview() {
        let param = this.getRange()
        if(!param){
          return
        }
        this.$emit('on-click-drawer-preview', this.currentTemplate, this.printType,this.seqFields,param);
      },
      handleClickDrawerPrint() {
        let param = this.getRange()
        if(!param){
          return
        }
        this.$emit('on-click-drawer-print', this.currentTemplate, this.printType, this.seqFields,param);
      },
      handleClickDrawerExport() {
        let param = {
          ...this.getRange(),
          record_print_times: false
        }
        if(!param){
          return
        }
        this.$emit('on-click-drawer-export',this.currentTemplate, this.printType, this.seqFields,param);
      },
      editCate(cateLevel) {
        if(this.levelTwoDisable) return;
        if(cateLevel==CATE_LEVEL_3){
          this.$emit("clickLook",false)
          this.$router.push({path:'/delivery/line/template'})
          return
        }
        this.$emit('on-edit-category', cateLevel);
      }
    }
  }
</script>

<style lang="less" scoped>
.cate_box{
  display: flex;
  flex-wrap: wrap;
  padding: 10px;
  .cate_item_box{
    padding: 5px;
    border: 1px solid #d8d8d8;
    margin: 5px 3px;
    cursor: pointer;
  }
  .cate_item_box_active{
      border: 1px solid #03ac54;
      color: #03ac54;
  }
}
.print_template_box{
  display: flex;
  flex-wrap: wrap;
}
.print_template_item{
  height: 28px;
  line-height: 28px;
  font-size: 12px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  text-align: center;
  padding: 0 3px;
  width: calc(33.33% - 3px);
  cursor: pointer;
  border:1px solid #d8d8d8;
  margin: 5px;
}
.print_template_item_active{
  border: 1px solid var(--primary-color);
 color: var(--primary-color)
}
.list_box{
  padding-bottom: 10px;
  border-bottom: 1px solid #e8e8e8;
}
.header {
  display: flex;
  position: relative;
  justify-content: space-between;
}
.btnAbsolute{
  position: absolute;
  top: 10px;
  right: 10px;
}
.primary-color {
  color: var(--primary-color)
}
.disabled {
  color: #ccc;
  cursor: not-allowed;
}
.cateflex {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.space-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.w305 {
  width: 305px;
}
.noData{
  text-indent: 1.5em;
  font-size: 12px;
  color: #ccc;
}
</style>
