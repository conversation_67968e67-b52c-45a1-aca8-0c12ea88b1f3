<!--
 * @Author: lizhiwei😊
 * @Description: 编辑商品分类模态框
 * @LastEditors: lizhiwei😊
 * @Date: 2022-03-30 11:27:49
 * @LastEditTime: 2022-04-08 18:05:46
-->
<template>
  <Modal v-bind="$attrs" v-on="$listeners" ref="sdpmodal" class-name="vertical-center-modal"  @on-ok="handleOk">
    <div class="w" style="overflow: auto;" :style="{'max-height':(getViewportHeight() * 0.85 - 100)+'px'}">
      <slot name="formContent"></slot>  
      <div class="hint">
        <Icon size="16" type="ios-alert-outline" />
        <span>选择好菜品分类后，点击键盘回车键，将所选的分类自动存为新的分组</span>
      </div>
      <template  v-if="show">
        <div class="group" v-for="(list, index) in grouping" :key="index">
          <div v-if="groupingTitle&&groupingTitle.length>0" class="pb10" > 
            <Input :maxlength="12" placeholder="请输入分组名称,最大长度不超过12个字"  showWordLimit @input="vaildValue($event,index)" v-model="groupingTitle[index]"></Input>
            <!-- {{}}  -->
            </div>
          <!-- <div class="pb10" >分组{{arabicToChinese(index + 1)}} </div> -->
          
          <div class="group-ul" ref="list" >
            <Button
              :data-id="_.entity_id"
              :data-pid="index"
              class="group-ul-item"
              v-for="_ in list"
              :key="_.entity_id"
            >
              {{ _.entity_name }}
            </Button>
          </div>
        </div>
      </template>
  </div>
   <div slot="footer">
        <Button @click="handleCancel()">取消</Button>
        <Button type="primary" @click="handleOk()">确认</Button>
      </div>
  </Modal>
</template>

<script>
  import { Sortable, MultiDrag } from "sortablejs";
import { arabicToChinese } from '../util.js';
import { getViewportHeight } from '@/util/common';
try {
  Sortable.mount(new MultiDrag());
} catch (e) {
  console.error(e);
}
export default {
  name: "PrintEditCateModal",
  props: {
    // 绑定模态框的显示隐藏
    show: {
      type: Boolean,
      default: false
    },
    // groupData 二维数组,每一组的数据
    groupData: {
      type: Array,
      default: () => []
    },
  },
  data() {
    return {
      selected: [],
      instanceArr: [],
      grouping: [],
      groupingTitle:[]
    };
  },
  watch: {
    groupData:{
      handler(n){
        this.groupingTitle =[]
        this.grouping = this.deepClone(n);
        this.grouping.forEach((res,index)=>{
          if(res[0]&&res[0].category_name){
             this.groupingTitle.push(res[0].category_name)
          }else{
            this.groupingTitle.push('分组'+arabicToChinese(index+1))
          }
        })
        this.$nextTick(() => {
          this.initDrag();
        });
      }
    },
    // 每次都要重新渲染并且重新初始化drag
    show(val) {
      if (val) {
         this.groupingTitle =[]
        this.grouping = this.deepClone(this.groupData);
        this.grouping.forEach((res,index)=>{
             if(res[0]&&res[0].category_name){
             this.groupingTitle.push(res[0].category_name)
          }else{
            this.groupingTitle.push('分组'+arabicToChinese(index+1))
          }
        })
        this.$nextTick(() => {
          this.initDrag();
        });
      }
    }
  },
  mounted() {
    document.addEventListener("keydown", this.handleKeyDown, false);
    // this.initDrag();
  },
  beforeDestroy() {
    document.removeEventListener("keydown", this.handleKeyDown, false);
  },
  methods: {
      getViewportHeight,
    vaildValue(n,index){
      if(!n){
        return
      }
      let data = new Set(this.groupingTitle)
      if(data.size!=this.groupingTitle.length){
        this.warningMessage('分组名称不能重复')
        // this.groupingTitle[index] = ''
      }
    },
    handleCancel(){
      this.$refs.sdpmodal.cancel()
    },
     checkRepeat(arr,id) {
    var array = [];
    arr.forEach((item) => {
      if (item) {
       array.push(item[0][id]);
      }
    });
    if((new Set(array)).size != arr.length){
        console.log("数组有重复值");
        return true;
    }
     return false;
  },
    handleOk () {
      // 根据dom结构拿到最新的顺序, 没有在每次拖动的时候去更新数据,统一在保存的时候取.
      let arr = this.getGroupFormDom()
      arr.forEach((res,index)=>{
          res.forEach(e=>{
            e.category_name = this.groupingTitle[index]
          }) 
      })
       for(let i=0;i<this.groupingTitle.length;i++){
         if(this.groupingTitle[i]==""&&arr[i].length>0){
          this.warningMessage('请填写分组名称')
          return
         }
       }
      arr = arr.filter(_=>_.length)
      if(this.checkRepeat(arr,'category_name')){
        this.warningMessage('分组名称不能重复')
        return
      }
      this.$emit('on-ok', arr, this.handleCancel )
    },
    /**
     * @description: 根据dom结构取最新数据
     * @return {Array}
     */    
    getGroupFormDom() {
      const arr = []
      this.instanceArr.forEach(ins => {
        // ins.toArray() sortablejs 内部排序
        arr.push(ins.toArray())
      })
      return arr.map(list => list.map( id => {
        let item = {};
        this.grouping.map((group) => {
          group.map((_) => {
            if (_.entity_id === id) {
              item = _;
            }
          })
        })
        return item
      }))
    },
    /**
     * @description: 数字转中文
     */    
    arabicToChinese,
    /**
     * 初始化拖拽
     */
    initDrag() {
      this.instanceArr = [];
     this.$refs.list&&this.$refs.list.forEach(this.createSortable);
    },
    createSortable(dom, _index) {
      const res = Sortable.create(dom, {
        multiDrag: true, // Enable the plugin
        group: {
          name: "s" + _index, //组名为itxst
          pull: true, //是否允许拖入当前组
          put: true, //是否允许拖出当前组
        },
        selectedClass: "selected", // Class name for selected item that must be down for items to be selected
        supportPointer: false, // 不支持 pointer-events
        avoidImplicitDeselect: false, // true - if you don't want to deselect items on outside click
        onSelect: (evt) => {
          const { id, pid } = evt.item.dataset
          this.activeIndex = _index;
          const findItem = this.grouping[pid].find((item) => item.entity_id == id);
          findItem && this.selected.push(findItem);
        },
        // Called when an item is deselected
        onDeselect: (evt) => {
          const { id } = evt.item.dataset
          const findIndex = this.selected.findIndex((item) => item.entity_id == id);
          this.selected.splice(findIndex, 1);
        },
        onAdd: (evt) => {
          // const items = evt.items.length ? evt.items : [evt.item];
          setTimeout(() => {
            this.mockDocumentEvent()
          }, 100);
        },
      });
      this.instanceArr.push(res);
    },
    filterGrouping() {
      return this.grouping.filter((item, idx) => {
          const len = item.length
          !len && this.instanceArr.splice(idx, 1);
          return len
        });
    },
    handleKeyDown(e) {
      e.keyCode === 13 && this.selected.length > 0 && this.add();
    },
    // 模拟document鼠标抬起
    mockDocumentEvent() {
      const pointEvent = new PointerEvent("pointerup", {
        bubbles: true,
        cancelable: true,
        composed: true,
        pointerId: 42,
        pointerType: "pen",
        clientX: 300,
        clientY: 500,
      });
      document.dispatchEvent(pointEvent);

      let mouseEvent = document.createEvent("MouseEvents");
      mouseEvent.initEvent("mouseup", true, true);
      document.dispatchEvent(mouseEvent);
    },
    add() {
      this.grouping = this.getGroupFormDom()
      const origin = this.grouping[this.activeIndex];
      this.selected.forEach((item) => {
        const idx = origin.findIndex(({ entity_id }) => entity_id == item.entity_id);
        ~idx && origin.splice(idx, 1);
      });
      // this.grouping = this.filterGrouping()
      this.grouping.push(this.selected);
      this.groupingTitle.push('分组'+arabicToChinese(this.grouping.length))
      this.selected = [];
      this.$nextTick(() => {
        // console.log(JSON.parse(JSON.stringify(this.grouping)), 'AAAAAAAAAAAAAAAAAAAAAAAAAA')
        this.initDrag()
        // const _index = this.grouping.length - 1;
        // this.createSortable(this.$refs.list[_index], _index);
      });
    },
  }
};
</script>

<style scoped>


.selected {
  color: var(--primary-color) !important;
  background-color: rgba(3, 172, 84, 0.1)!important;
  border-color: var(--primary-color)!important;
}
.hint {
  padding-bottom: 0;
}
.group {
  border: 1px solid #ccc;
  margin: 10px 0;
  padding: 10px;
  border-radius: 3px;
}
.group-ul {
  display: flex;
  flex-wrap: wrap;
  min-height: 50px;
}
.group-ul-item {
  height: 40px;
  margin: 0 10px 10px 0;
  cursor: grab;
}

.group-ul-item i {
  cursor: pointer;
}
</style>
