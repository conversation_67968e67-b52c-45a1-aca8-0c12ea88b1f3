<template>
  <div class="select-and-input-wrap">
    <Select
      class="selectAndInput-select"
      v-model="selectValue"
      @on-change="changeData"
    >
      <Option
        v-for="item in selectData"
        :value="item.value"
        :key="item.value"
        >{{ item.label }}</Option
      >
    </Select>
    <Input
      ref="input"
      :placeholder="placeholder"
      @on-change="changeInputData"
      @on-enter="onEnter"
      v-model="inputValue"
      class="selectAndInput-input"
    />
  </div>
</template>
<script>
export default {
  name: 'SelectAndInput',
  props: {
    value: {
      type: [String, Number, Array],
      default: () => [],
    },
    selectData: {
      type: Array,
      required: true,
    },
    selectDefaultValue: {
      type: String,
      default: '',
    },
    inputDefaultValue: {
      type: String,
      default: '',
    },
    inputPlaceholder: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      selectValue: '',
      inputValue: '',
      timer: null,
      placeholder: '',
    };
  },
  watch: {
    selectValue(val) {
      // 如果selectData里面的对象传递了placeholder属性，则使用该属性
      const findIndex = this.selectData.findIndex((item) => item.value === val);
      if (findIndex !== -1) {
        this.placeholder =
          this.selectData[findIndex].placeholder || this.inputPlaceholder;
      }
    },
    value: {
      handler(val) {
        this.selectValue = val[0];
        this.inputValue = val[1];
      },
      immediate: true,
    },
  },
  created() {
    this.placeholder = this.inputPlaceholder;
    this.selectValue = this.selectDefaultValue;
    this.inputValue = this.inputDefaultValue;
  },
  methods: {
    changeInputData() {
      // 防抖触发changeData方法
      clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.changeDataEmit();
      }, 300);
    },
    onEnter() {
      this.$refs.input.focus({ cursor: 'all' });
      this.$emit('on-enter', [this.selectValue, this.inputValue]);
    },
    changeData() {
      // 切换类型时清空数据
      this.inputValue = '';
      this.changeDataEmit();
    },
    changeDataEmit() {
      this.$emit('on-change', [this.selectValue, this.inputValue]);
    },
    resetValue() {
      this.inputValue = '';
      this.selectValue = '1';
    },
  },
};
</script>

<style lang="less" scoped>
.select-and-input-wrap {
  display: flex;
  .selectAndInput-select {
    flex-basis: 0px;
  }
  .selectAndInput-input {
    flex: 2;
    // width: 232px;
  }
  /deep/.ivu-select-selection {
    border-color: transparent !important;
    width: 65px;
  }
  /deep/.ivu-select-selected-value {
    padding: 0 13px 0 0 !important;
    color: rgba(0, 0, 0, 0.85) !important;
    text-align: right;
  }
  /deep/.ivu-select-arrow {
    right: 0 !important;
    color: #606060;
  }
  /deep/.ivu-select-visible .ivu-select-selection {
    box-shadow: none;
  }
}
</style>
