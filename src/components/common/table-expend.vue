<template>
  <div class="batch-summary-expand">
    <Table
      v-if="dataList.length > 0"
      class="data-list-table"
      :columns="dataListCols"
      :data="dataList"
    ></Table>
    <div v-if="bindList.length > 0" class="collapseRow">
      <Collapse style="width: 100%">
        <Panel name="1" v-for="(list, index) in bindList" :key="index">
          <span class="title">采购类型：{{ list.purchaseType || '未知' }}</span>
          <span class="title"
            >供应商：{{ list.agenter || list.provider || '未设置' }}</span
          >
          <span class="title">订单数：{{ list.items.length || '未知' }}</span>
          <span class="title">汇总量：{{ list.convert_amount || '未知' }}</span>
          <span class="title">在途库存量：{{ list.transit_stock }}</span>
          <span class="title">待采购量：{{ list.purchases }}</span>
          <p slot="content">
            <Row class="expand-row" :gutter="30">
              <Col span="3">
                <strong class="expand-key">订单号 </strong>
              </Col>
              <Col span="3">
                <strong class="expand-key">客户名称 </strong>
              </Col>
              <Col span="3">
                <strong class="expand-key">客户编码 </strong>
              </Col>
              <Col span="3">
                <strong class="expand-key">采购类型 </strong>
              </Col>
              <Col span="4">
                <strong class="expand-key">采购员/供应商 </strong>
              </Col>
              <Col span="3">
                <strong class="expand-key">订单状态 </strong>
              </Col>
              <Col span="3">
                <strong class="expand-key">下单数 </strong>
              </Col>
              <Col class="flex-con">
                <strong class="expand-key">备注</strong>
              </Col>
            </Row>
            <Row
              :gutter="30"
              v-for="(order, index) in list.items"
              :key="index"
              class="expand-row"
            >
              <Col span="3">
                <span class="expand-value">{{ order.order_no || '未知' }}</span>
              </Col>
              <Col span="3">
                <span class="expand-value">{{
                  order.user_name || '未知'
                }}</span>
              </Col>
              <Col span="3">
                <span class="expand-value">{{
                  order.user_code || '未知'
                }}</span>
              </Col>
              <Col span="3">
                <span class="expand-value">{{ order.purchaseType }}</span>
              </Col>
              <Col span="4">
                <span class="expand-value">{{
                  order.agenter || list.provider || '未设置'
                }}</span>
              </Col>
              <Col span="3">
                <span
                  class="expand-value"
                  :class="{ redColor: order.mode_text === '待发货' }"
                  >{{ order.mode_text || '未知' }}</span
                >
              </Col>
              <Col span="3">
                <span class="expand-value">
                  {{ order.view_price }}
                </span>
              </Col>
              <Col class="flex-con">
                {{ order.remark }}
              </Col>
            </Row>
          </p>
        </Panel>
      </Collapse>
    </div>
    <Row v-if="dataList.length === 0 && bindList.length === 0">
      <Col span="24" align="center">
        <Spin v-if="!loading">暂无数据</Spin>
        <span fix v-if="loading" style="color: #03ac54">
          <Icon type="load-c" size="18" class="demo-spin-icon-load"></Icon>
          <div>加载中...</div>
        </span>
      </Col>
    </Row>
  </div>
</template>
<script>
import common from '@api/order.js';
import ConfigMixin from '@/mixins/config';
export default {
  mixins: [ConfigMixin],
  props: {
    requestData: Object
  },
  data() {
    return {
      loading: false,
      dataListCols: [
        {
          title: '订单号',
          key: 'order_no',
          width: 215
        },
        {
          title: '客户名称',
          key: 'user_name',
          tooltip: true
        },
        {
          title: '客户编码',
          key: 'user_code',
          tooltip: true
        },
        {
          title: '采购类型',
          key: 'purchaseType'
        },
        {
          title: '采购员/供应商',
          width: 160,
          render: (h, params) => {
            let { row } = params;
            return h('Tooltip', {
              props: {
                maxWidth: 300,
                transfer: true,
                content: row.agenter || row.provider || '未设置',
                placement: 'bottom'
              }
            }, [
              h('span', {
                style: {
                  'text-overflow': 'ellipsis',
                  display: 'box',
                  '-webkit-line-clamp': 2, //多行在这里修改数字即可
                  overflow: 'hidden',
                  /* autoprefixer: ignore next */
                  '-webkit-box-orient': 'vertical',
                }
              }, row.agenter || row.provider || '未设置')
            ])
          }
        },
        {
          title: '订单状态',
          render: (h, params) => {
            let { row } = params;
            return h(
              'span',
              {
                class: {
                  redColor: row.mode_text === '待发货'
                }
              },
              row.mode_text
            );
          }
        },
        {
          title: '基础商品',
          key: 'base_amount'
        },
        {
          title: '加工商品',
          key: 'raw_amount'
        },
        {
          title: '下单数',
          tooltip: true,
          key: 'view_price'
        },
        {
          title: '备注',
          key: 'remark',
          tooltip: true
        }
      ],
      dataList: [],
      bindList: [],
      orderUnit: ''
    };
  },
  filters: {
    unit(list) {
      let unit = '';
      if (list.unit !== list.unit_sell) {
        unit = `(${list.unit_num} ${list.unit_sell} * ${list.amount} ${list.unit})`;
      }
      return unit;
    }
  },
  created() {
    this.loading = true;
    let me = this;
    this.setCols();
    common.getCommodityOrderList(this.requestData.params).then(res => {
      if (res.status && res.data) {
        this.orderUnit = res.data.unit;
        if (res.data.order_list) {
          this.dataList = res.data.order_list.map(item => {
            this.mapVal(item);
            return item;
          });
          this.loading = false;
        } else {
          this.dataList = [];
        }
        if (res.data.bind_order_commoditys) {
          this.bindList = res.data.bind_order_commoditys.map(d => {
            me.mapVal(d);
            d.items.forEach(item => {
              me.mapVal(item);
              return item;
            });
            return d;
          });
          this.loading = false;
        } else {
          this.bindList = [];
        }
      }
    });
  },
  methods: {
    setCols() {
      this.commonService.getConfig().then(config => {
        // 未启用净菜加工
        if (!this.util.isEnableProcess(config)) {
          let excludeCols = ['base_amount', 'raw_amount'];
          excludeCols.forEach(colKey => {
            if (this.dataListCols.find(col => col.key === colKey)) {
              this.dataListCols.splice(
                this.dataListCols.findIndex(col => col.key === colKey),
                1
              );
            }
          });
        }
      });
    },
    mapVal(item) {
      item['purchaseType'] = '市场自采';
      if (parseInt(item.purchase_type) === 2) {
        item['purchaseType'] = '供应商直供';
      } else if (parseInt(item.purchase_type) === 3) {
        item['purchaseType'] = '指定供应商';
      }
      if(item.is_order_provider ==1 ) {
        item['purchaseType'] = '订单指定供应商';
      }
      if (parseInt(item.agent_id) > 0) {
        this.requestData.PurchaseType.agents.map(_item => {
          if (_item.id === item.agent_id) {
            item['agenter'] = _item.name;
          }
        });
      }
      if (parseInt(item.provider_id) > 0) {
        this.requestData.PurchaseType.providers.map(_item => {
          if (_item.id === item.provider_id) {
            item['provider'] = _item.name;
          }
        });
      }
    }
  }
};
</script>
<style scoped lang="less">
.title {
  padding-right: 80px;
  font-weight: bold;
}
.orangeredColor {
  color: #ff9a00;
}
.redColor {
  color: #e20000;
}
.greenColor {
  color: #03ac54;
}
.collapseRow {
  padding-top: 15px;
  margin-right: -15px;
  margin-left: -15px;
}
.expand-row {
  margin-bottom: 16px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dddddd;
}
.demo-spin-icon-load {
  animation: ani-demo-spin 1s linear infinite;
}
/deep/ .ivu-table-header thead tr th {
  padding: 0 !important;
  height: 42px !important;
  background-color: #f6f8f9 !important;
}
/deep/ .ivu-table-header thead tr {
  height: 42px;
}
</style>
<style lang="less">
.batch-summary-expand {
  .data-list-table {
    border: none;
    margin: -20px -15px 0;
    .ivu-table {
      background: transparent !important;
      th,
      td {
        background: transparent;
        height: auto;
        padding: 15px 0;
      }
      &:after {
        background: transparent;
      }
      &:before {
        background: transparent;
      }
    }
  }
}
</style>
