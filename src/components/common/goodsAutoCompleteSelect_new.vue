<template>
  <div class="wrap goods-search" :class="{ icon: icon }">
    <RemoteSelect
      ref="goodsInput"
      clearable
      filterable
      remote
      v-model="goodsId"
      :transfer="transfer"
      :placeholder="placeholder"
      :remote-method="remoteSearch"
      :loading="selectLoading"
      @on-query-change="onQueryChange"
      @on-change="updateValue"
    >
      <Option
        v-for="(option, index) in remoteList"
        :value="option[valueKey]"
        :label="option[labelKey]"
        :key="index"
      >
        <p>
          {{ option[labelKey] }} {{ option.unit }}
          <span v-if="option.summary">({{ option.summary }})</span>
        </p>
        <p style="color: #aaa">{{ option.commodity_code }}</p>
      </Option>
    </RemoteSelect>
    <Icon
      :type="icon"
      v-if="icon"
      class="icon"
      @click.native="handleClick"
    ></Icon>
    <slot></slot>
  </div>
</template>

<script>
export default {
  name: 'goodsAutoCompleteSelect',
  props: {
    value: {
      default: 0
    },
    transfer: {
      type: Boolean,
      default: true
    },
    placeholder: {
      type: String,
      default: '商品名/助记码/编码/别名/条形码'
    },
    icon: {
      type: String,
      default: ''
    },
    labelKey: {
      type: String,
      default: 'commodity_name'
    },
    valueKey: {
      type: String,
      default: 'commodity_id'
    },
    filters: {
      type: Object,
      default: () => {}
    },
    api: {
      type: String,
      default: '' // 搜索api地址
    },
    // 在商品编码后添加已配置的商品税率，编辑税率页面专用配置
    showTax: {
      type: Boolean,
      default: false
    },
    taxData: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  watch: {
    value (newValue) {
      this.goodsId = newValue
    }
  },
  data() {
    return {
      defaultApi: '',
      goodsId: '',
      remoteList: [],
      lastChange: '',
      selectLoading: false
    };
  },
  methods: {
    onQueryChange() {
      this.$emit('on-query-change');
    },
    remoteSearch(query) {
      query = query.trim();
      if (query !== '') {
        if (this.selectLoading) {
          this.lastChange = query;
          return false;
        }
        this.selectLoading = true;
        let api = this.api || this.apiUrl.searchGoods;
        this.$request.get(api, { query, ...this.filters }).then(res => {
          let { status, data } = res;
          this.selectLoading = false;
          if (status) {
            this.remoteList = data.commodities;
          } else {
            this.remoteList = [];
          }
          // 在商品编码后添加已配置的商品税率
          if (this.showTax) {
            this.remoteList.map(item => {
              let find = this.taxData.find(element => {
                return element.foreign_id === item.commodity_id;
              });
              if (find) {
                item.commodity_code += ` [${find.tax_rate}%]`;
              }
            });
          }
          if (this.lastChange) {
            this.remoteSearch(this.lastChange);
            this.lastChange = '';
          }
        });
      } else {
        this.goodsId = '';
        this.remoteList = [];
      }
    },
    setQuery(query) {
      if (!query) {
        this.$refs.goodsInput.clearSingleSelect();
      } else {
        this.$refs.goodsInput.setQuery(query);
      }
    },
    updateValue() {
      this.$emit('input', this.goodsId);
      this.$emit('on-change', this.goodsId);
    },
    handleClick() {
      this.$emit('on-click');
    }
  }
};
</script>

<style lang="less">
.goods-search {
  &.icon {
    .ivu-icon:not(.icon) {
      right: 30px !important;
    }
  }
}
</style>
<style lang="less" scoped>
.wrap {
  display: inline-block;
  width: 100%;
  position: relative;
  .icon {
    position: absolute;
    font-size: 20px;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    cursor: pointer;
  }
}
</style>
