<template>
  <Cascader
    v-model="selfValue"
    :data="cascaderData"
    :render-format="renderFormat"
    :disabled="disabled"
    :clearable="clearable"
    :placeholder="placeholder"
    :trigger="trigger"
    :change-on-select="changeOnSelect"
    filterable
    transfer
    @on-change="handleChange"
    style="width: 100%"
    :transfer-class-name="transferClassName"
  ></Cascader>
</template>

<script>
import storeRoom from '@api/storeRoom.js'

export default {
  name: 'ReservoirAreaLocation',
  props: {
    // 可选项的数据源
    data: {
      type: [Array],
      default: () => []
    },
    // 当前已选项的数据
    value: {
      type: [Array],
      default: () => []
    },
    // 选择后展示的函数，用于自定义显示格式
    renderFormat: {
      type: Function,
      default: label => label.join(' / ')
    },
    // 是否禁用选择器
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持清除
    clearable: {
      type: Boolean,
      default: true
    },
    // 输入框占位符
    placeholder: {
      type: String,
      default: '输入库位号'
    },
    // 次级菜单展开方式，click / hover
    trigger: {
      type: String,
      default: 'click'
    },
    // 当此项为 true 时，点选每级菜单选项值都会发生变化
    changeOnSelect: {
      type: Boolean,
      default: false
    },
    // 是否自动加载远程数据
    autoLoadData: {
      type: Boolean,
      default: false
    },
    // 库房id, 搭配remote使用
    storeId: {
      type: String,
      default: ''
    },
    transferClassName: { //此配置是给弹出层添加类名，一般用于修改浮层样式,在使用该组件的页面中添加本页面末尾的样式类似样式即可不要使用scoped
      type: String,
      default: ''
    },
  },
  data () {
    return {
      areaData: [], // 库区列表
      locationData: [], // 库位列表
      cascaderData: [], // 级联选择器数据
      selfValue: [],
      selectedItem: null
    }
  },
  watch: {
    data: {
      deep: true,
      handler (newValue) {
        this.cascaderData = newValue
      }
    },
    value (newValue) {
      this.selfValue = newValue
    },
    storeId (newValue) {
      this._getCascaderData(newValue)
    }
  },
  created () {
    this.selfValue = this.value
    if (this.autoLoadData && this.storeId) {
      this._getCascaderData()
    } else {
      this.cascaderData = this.data
    }
  },
  methods: {
    _getCascaderData () {
      const params = {
        store_id: this.storeId
      }
      storeRoom.getReservoirList(params).then(res => {
        const { status, data } = res
        if (status) {
          this.cascaderData = data
        }
      })
    },
    handleChange (value, selectedData) {
      this.$emit('input', value)
      this.$emit('on-change', value, selectedData)
      this.selectedItem = selectedData
    },
    getSelectedItem () {
      return this.selectedItem
    }
  }
};
</script>
 <style>
 /* transferClassName使用方法 reservoir-area-location为props的自定义类名*/
/* .reservoir-area-location .ivu-cascader-not-found-tip {
  width: 232px !important;
} */
</style>