<template>
  <div id="category-select">
    <Cascader
      v-bind="$attrs"
      v-model="selfValue"
      :data="categoryList"
      :change-on-select="changeOnSelect"
      clearable
      filterable
      :placeholder="placeholder"
      @on-change="changeCategory"
      ref="cascader"
      :style="{width}"
    >
    </Cascader>
  </div>
</template>

<script>
import goods from '@api/goods.js';
export default {
  name: 'PurchaseSelect',
  autoRegister: true,
  props: {
    value: {
      type: [Array, String],
      default: () => [],
    },
    placeholder: {
      default: '采购模式',
    },
    changeOnSelect: {
      type: Boolean,
      default: true,
    },
    level: {
      type: Array,
      default() {
        return [0, 1, 2];
      }, // 显示全部,市场自采,供应商直供
    },
    // 额外的查询参数
    params: {
      type: Object,
      default: () => ({}),
    },
    // 控制是否初始化数据
    shouldInitData: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: '232px'
    }
  },
  watch: {
    value() {
      this._filterCategoryList();
    },
    level: {
      deep: true,
      handler(n) {
        if (n === this.level) return
        this.level = n;
        this._filterCategoryList();
      },
    },
    shouldInitData(val) {
      if (val) {
        this._initData();
      }
    },
  },
  data() {
    return {
      timer: null,
      selectedItem: null,
      selfValue: [],
      originCategoryList: [],
      categoryList: [],
      dataInitialized: false,
    };
  },
  created() {
    if (this.shouldInitData) {
      this._initData();
    }
  },
  methods: {
    _formatLabel(str) {
      return str ? `(${str})` : '';
    },
    _filterCategoryList() {
      const originCategoryList = this.deepClone(this.originCategoryList);
      // 过滤
      originCategoryList.length > 0 &&
        (this.categoryList = originCategoryList.filter((c) =>
          this.level.includes(+c.value),
        ));
      this.$nextTick(() => {
        // 默认值可能回显空的问题
        this.selfValue = this.value;
        if (this.value && this.value.length > 0) {
          this.$refs.cascader.updateSelected(true);
        }
      });
    },
    async _initData() {
      const { status, data } = await goods.getPurchaseType(this.params);
      if (!status) return;
      this.originCategoryList = [
        {
          value: '0',
          label: '全部',
        },
        {
          value: '1',
          label: '市场自采',
          children: [],
        },
        {
          value: '2',
          label: '供应商直供',
          children: [],
        },
        {
          value: '3',
          label: '指定供应商',
          children: [],
        },
        {
          value: '5',
          label: '联营供应商',
          children: [],
        },
      ];
      this.originCategoryList[1].children = data.agents.map((item) => {
        return {
          value: item.id,
          label: item.name + this._formatLabel(item.agent_code),
        };
      });
      let providersArr = [];
      let jointProvidersArr = [];
      data.providers.forEach((item) => {
        providersArr.push({
          value: item.id,
          label: item.name + this._formatLabel(item.provider_code),
        });
        if (item.provider_type == 2) {
          jointProvidersArr.push({
            value: item.id,
            label: item.name,
          });
        }
      });
      this.originCategoryList[2].children =
        this.originCategoryList[3].children = providersArr;
      this.originCategoryList[4].children = jointProvidersArr;
      this.categoryList = this.deepClone(this.originCategoryList);
      this.$nextTick(() => {
        this._filterCategoryList();
      });
    },
    changeCategory(value, selectedData) {
      // this.$emit('input', value);
      if (this.timer) {
        clearTimeout(this.timer); //只要触发就清除
      }
      this.timer = setTimeout(() => {
        this.$emit('on-change', value, selectedData);
        this.selectedItem = selectedData;
      }, 500);
    },
  },
};
</script>

<style scoped></style>
