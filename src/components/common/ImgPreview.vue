<template>
  <Modal
    title="图片预览"
    className="vertical-center-modal modal-no-footer"
    @on-cancel="handleCancel"
    width="1000"
    v-model="showModal">
    <div :style="{maxHeight: getTableHeight() + 'px', overflowY: 'auto', overflowX: 'hidden'}">
      <img
        style="width: auto; max-width: 1000px; height: auto;"
        :src="src" alt="">
    </div>
  </Modal>
</template>

<script>
  export default {
  	autoRegister: true,
    name: "ImgPreview",
    components: {
    },
    data() {
      return {
        showModal: false,
        src: ''
      }
    },
    created () {
    },
    methods: {
    	show (src) {
    		this.src = src;
    		this.openModal();
      },
      openModal () {
    		this.showModal = true;
      },
      handleCancel () {
      	this.showModal = false;
      }
    },
  }
</script>

<style lang="less" scoped>
</style>
