<template>
  <div id="category-select">
    <Cascader
      v-if="level - 1 > 0"
      v-model="selfValue"
      :data="categoryList"
      change-on-select
      clearable
      filterable
      transfer
      :placeholder="placeholder"
      @on-change="changeCategory"
      :render-format="renderFormat"
      style="width: 100%"
    >
    </Cascader>
    <Select
      v-if="parseInt(level) === 1"
      v-model="selfValue"
      @on-change="changeFirsCategory"
    >
      <Option
        v-for="(item, index) in categoryList"
        :key="index"
        :value="item.value"
      >{{ item.label }}</Option>
    </Select>
  </div>
</template>

<script>
import goods from '@api/goods.js';
import { mapState } from 'vuex';
export default {
  name: 'CategorySelect',
  autoRegister: true,
  props: {
    value: {
      type: [Array, String],
      default: () => []
    },
    meta: {
      type: Object,
      default: null
    },
    level: {
      default: 2
    },
    placeholder: {
      default: '全部分类'
    },
    defaultAllCategory: {
      type: Boolean,
      default: true
    },
    renderFormat: {
      type: Function,
      default: label => label.join(' / ')
    },
    noThree: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '/superAdmin/categorySuper/tree'
    }
  },
  computed: {
    ...mapState({
				sysConfig: 'sysConfig'
			})
  },
  watch: {
    value (newValue) {
      this.selfValue = newValue;
    }
  },
  data () {
    return {
      selectedItem: null,
      selfValue: [],
      categoryList: []
    };
  },
  created(){
    this._initData();
  },
  activated () {
    this._initData();
  },
  deactivated () {
    this.categoryList = [];
  },
  methods: {
    _initData(){
      const convertToChildren = treeData => {
        if (Array.isArray(treeData)) {
          return treeData.map(convertToChildren);
        }

        if (typeof treeData === 'object' && treeData !== null) {
          const children = treeData.items
            ? convertToChildren(treeData.items)
            : [];
          return {
            ...treeData,
            value: treeData.id,
            label: treeData.name,
            children: children.map(convertToChildren)
          };
        }

        return treeData;
      };
      this.selfValue = this.value;
      this.$request.get(this.url).then(res => {
        let { status, data } = res;
        if (status) {
          if (+this.sysConfig.is_open_commodity_category_three === 1) {
						data.forEach(item => {
							if (item.items && item.items.length > 0) {
								item.items.forEach(item2 => {
									item2.items = item2.items || [];
									item2.items.unshift({
										// id: item2.id + '-' + '0',
										id: '0',
										name: '未设置',
										level: '3'
									});
								});
							}
						});
					}
          if(this.noThree) {
            data.forEach(item => {
							if (item.items && item.items.length > 0) {
								item.items.forEach(item2 => {
									item2.items = [];
								});
							}
						});
          }
          let cascaderData = convertToChildren(data)
          this.categoryList = cascaderData;
          if (this.defaultAllCategory) {
            this.categoryList.unshift({ value: '', label: '全部分类' });
          }
        }
      });
    },
    loadSubCategoryData (item, callback) {
      item.loading = true;
      goods.getGoodsCategory(item.value).then(res => {
        if (res.status) {
          item.children = res.data.map(_item => {
            return {
              value: _item.id,
              label: _item.name
            };
          });
          item.loading = false;
          callback();
        }
      });
    },
    changeCategory (value, selectedData) {
      this.$emit('input', value);
      this.$emit('on-change', value, selectedData, this.meta);
      this.selectedItem = selectedData;
    },
    changeFirsCategory () {
      this.selectedItem = this.categoryList.find(
        item => item.value === this.selfValue
      );
      this.$emit('input', this.selfValue);
      this.$emit('on-change', this.selfValue);
    },
    getSelectedItem () {
      return this.selectedItem;
    }
  }
};
</script>

<style scoped></style>
