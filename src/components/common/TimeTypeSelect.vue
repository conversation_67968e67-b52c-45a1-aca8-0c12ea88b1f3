<!--
 * @Author: <PERSON>
 * @Date: 2020-12-21 10:36:38
 * @LastEditors: fc <EMAIL>
 * @LastEditTime: 2023-08-24 16:27:41
 * @Description: 左侧下拉选择不同的label, 右侧选择日期范围
-->
<template>
  <div class="time__type">
    <Select class="time__type--select" v-model="selectType" @on-change="_changeDate">
      <Option v-for="item in data" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>

    <Tooltip
      v-if="tipContent"
      :transfer="true"
      maxWidth='200'
      :content='tipContent'
      >
      <Icon class="filter__tips" type="ios-alert-outline" />
    </Tooltip>

    <DatePicker
			:options="options || {}"
      class="time__type--date"
      :class="typeof type === 'function' && type(selectType)"
      :type="typeof type === 'function' ? type(selectType) : type"
      :value="datePicker"
      :format="selfFormat"
      :clearable="clearable"
      :transfer="transfer"
      :placeholder="placeholder"
      :defaultToEnd="defaultToEnd"
      @on-change="_changeDate"
    ></DatePicker>
  </div>
</template>
<script>
import moment from 'moment'
import DateUtil from '@util/date.js'

export default {
  name: 'TimeTypeSelect',
  props: {
    format:{
      type:String,
      default: "yyyy-MM-dd",
    },
    // 日期范围默认值
    value: {
      type: Array,
      required: true
    },
    // 可选日期类型数组
    data: {
      type: Array,
      required: true
    },
		options: {
			type: Object,
			default: () => {}
		},
    // 日期选择器类型type
    type: {
      type: [String, Function],
      default: 'daterange'
    },
    maxRange: {
      type: Number,
      default: undefined
    },
    maxRangeText: {
      type: String,
      default: ''
    },
    clearable: {
      type: Boolean,
      default: false
    },
    transfer: {
      type: Boolean,
      default: false
    },
    defaultToEnd: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择日期'
    },
  },
  data () {
    return {
			dateQuickOptions: DateUtil.dateQuickOptions,
      selectType: 1,
      datePicker: [],
      originDatePicker: [],
      defaultSelectType: '',
      defaultDatePicker: '',
      selfFormat: ''
    }
  },
  computed: {
    tipContent() {
      const row = this.data.find(k => k.value === this.selectType) || {}
      return row.tips
    }
  },
  watch: {
    value: {
      immediate: true, // 初次绑定时执行handler
      handler (newValue) {
        const [start_date, end_date, date_type] = newValue
        this.datePicker = [start_date, end_date]
        this.selectType = date_type
      }
    }
  },
  created () {
    this.selfFormat = this.format
    this.originDatePicker = this.deepClone(this.datePicker);
    this.defaultDatePicker = this.deepClone(this.datePicker);
    this.defaultSelectType = this.selectType;
  },
  methods: {
    /**
     * @description Select选项改变 或 DatePicker日期改变
     * @param {_param} 是否是类型改变(select改变不会传类型 1 / 2 ，date传参传数组['2020-8-10','2020-8-11'])
     */
    _changeDate (_param) {
      let value = []
      if (_param instanceof Array) {
        //日期改变
        if (this.maxRange && DateUtil.diffDay(_param[0], _param[1]) > this.maxRange) {
          // this.datePicker = this.originDatePicker  //原来这么写当开启maxRange时，如果连续选择的时超过的maxRange时，
          // 会导致第二次选择后日期this.datePicker = this.originDatePicker不会成功，因为iview的datepicker组件使用的是watch监听value
          this.datePicker = [_param[0], _param[1]]
          this.modalError({
            content: `最多支持${this.maxRangeText || `${this.maxRange}天`}，请重新选择`,
            onOk: () => {
              this.datePicker = this.originDatePicker
              value = [...this.datePicker, this.selectType]
              this.$emit('on-change', value)
            }
          })
          return
        } else {
          this.datePicker = _param
          this.originDatePicker = this.deepClone(_param)
        }
        value = [...this.datePicker, this.selectType]
      } else {
        //类型改变
        this.handleSelectTypeChange(_param)
        value = [...this.datePicker, _param]
      }
      this.$nextTick(() => this.$emit('on-change', value))
    },
    resetValue () {
      this.selectType = this.defaultSelectType;
      this.datePicker = this.defaultDatePicker;
      this.handleSelectTypeChange(this.selectType)
    },
    handleDate (date, index) {
      const item = this.data.find(i => i.value === this.selectType)
      if (!item) return date
      if (item.subtract) {
        return moment(date).subtract(item.subtract, 'days')
      } else if (item.default) {
        return moment(item.default[index])
      }
    },
    handleSelectTypeChange (val) {
      if (typeof this.type === 'string') return
      const type = this.type(val)
      // const stratDate = this.handleDate(this.defaultDatePicker[0], 0)
      // const endDate = this.handleDate(this.defaultDatePicker[1], 1)
      // 不懂，上面那个写法是干嘛，而且有问题？？？？？？
      const stratDate = this.defaultDatePicker[0]
      const endDate = this.defaultDatePicker[1]
      switch (type) {
        case 'datetimerange':
          this.selfFormat = 'yyyy-MM-dd HH:mm'
          this.datePicker = [
            moment(stratDate).format('yyyy-MM-DD HH:mm'),
            moment(endDate).endOf('day').format('yyyy-MM-DD HH:mm')
          ]
          break;
        default:
          this.datePicker = [
            moment(stratDate).format('yyyy-MM-DD'),
            moment(endDate).endOf('day').format('yyyy-MM-DD')
          ]
          this.selfFormat = 'yyyy-MM-dd'
          break;
      }
    }
  }
};
</script>

<style lang="less" scoped>
.time__type {
  width: 299px;
  display: flex;
  align-items: center;
  &--select {
    width: 72px;
  }
  &--date {
    width: 232px;
  }
  /deep/.ivu-select-selection {
    border-color: transparent !important;
  }
  /deep/.ivu-select-selected-value {
    padding: 0 13px 0 0 !important;
    color: rgba(0, 0, 0, 0.85) !important;
    white-space: normal;
    text-align: right;
    line-height: 15px;
    display: flex;
    align-items: center;
  }
  /deep/.ivu-select-arrow {
    right: 0 !important;
  }
  /deep/.ivu-select-visible .ivu-select-selection {
    box-shadow: none;
  }
  /deep/ .datetimerange .ivu-input {
    padding-left: 5px;
    padding-right: 20px;
    font-size: 12px;
  }
}
</style>
