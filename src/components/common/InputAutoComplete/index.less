.input-auto-complete {
  .dropdown {
    position: absolute;
    z-index: 255;
    background: #fff;
    box-shadow: 0px 0px 5px #ccc;
    max-height: 200px !important;
    min-height: 50px;
    min-width: 232px;
    max-width: 500px;
    overflow-y: auto;
    padding: 10px 0;
    margin: 5px 0;
    box-sizing: border-box;
    &-content {
      position: absolute;
      z-index: 199;
      left: 0;
      margin-top: 5px;
      padding-left: 10px;
      padding-right: 10px;
      background-color: #fff;
      border-radius: 2px;
      width: 100%;
    }
    .active {
      p {
        color: var(--primary-color);
      }
      color: var(--primary-color);
      background-color: #ebf7ff;
    }
    &-items {
      text-align: left;
      width: 100%;
      clear: both;
      color: #333;
      cursor: pointer;
      line-height: normal;
      list-style: none;
      margin: 0;
      padding: 7px 16px;
      -webkit-transition: background .2s ease-in-out;
      transition: background .2s ease-in-out;
      &:hover {
        cursor: pointer;
        background-color: #ebf7ff;
        color: var(--primary-color);
      }
      &-label {
        font-size: 13px;
        margin-right: 10px;
        line-height: 19.2px;
      }
    }
  }

  // .dropdown-items-span {
  //   font-size: 12px;
  //   color: #aaa;
  // }
  // .dropdown-items-p {
  //   height: 20px;
  //   line-height: 20px;
  //   font-size: 13px;
  //   color: #aaa;
  // }
}
