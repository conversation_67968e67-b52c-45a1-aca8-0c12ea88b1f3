<template>
  <div class="input-auto-complete">
    <Input
      ref="searchWord"
      :clearable="clearable"
      v-model="searchWord"
      :placeholder="placeholder"
      :disabled="disabled"
      @on-change="throttledInputChange"
      @on-keydown="handleKeydown"
      @on-enter="handleEnter"
      @on-focus="handleFocus"
      @on-blur="handleBlur"
      @on-clear="handleClear"
    />
    <div class="dropdown" id="dropdown-wrap" v-if="showDropDown">
      <div>
        <ul class="dropdown-list">
          <li
            :key="index"
            class="dropdown-items"
            style="height: auto !important"
            :class="
              index == activeIndex
                ? 'active sdp-no-replace-text'
                : 'sdp-no-replace-text'
            "
            v-for="(info, index) in remoteList"
            :data-index="index"
            @click="selectItem(info)"
          >
            <slot name="option" :option="info">
              <div class="dropdown-items-label">
                {{ info[labelKey] }}
              </div>
            </slot>
          </li>
        </ul>
      </div>
      <div class="dropdown-content" v-show="remoteList.length === 0">
        <p class="dropdown-empty">
          {{ this.selectLoading ? '加载中...' : '暂无数据' }}
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { throttle, debounce } from 'lodash-es';

export default {
  name: 'InputAutoComplete',
  props: {
    value: {
      default: '',
    },
    placeholder: {
      type: String,
      default: '请输入',
    },
    // 远程搜索api, 设置请求数据url, 支持函数返回Promise对象，return列表数组
    dataProvider: {
      type: [String, Function],
      required: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    labelKey: {
      type: String,
      required: true,
    },
    valueKey: {
      type: String,
      default: 'id',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    queryKey: {
      type: String,
      default: 'query',
    },
    otherParams: {
      type: Object,
      default: () => {},
    },
    pagination: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    value(newValue) {
      this.searchWord = newValue;
    },
  },
  data() {
    return {
      itemClass: 'dropdown-items',
      searchWord: '',
      goodsId: '',
      remoteList: [],
      lastChange: '',
      activeIndex: -1,
      showDropDown: false,
      selectLoading: false,
      timer: null,
    };
  },
  created() {
    this.searchWord = this.value;
    this.throttledInputChange = debounce(this.inputChange, 200);
  },
  methods: {
    inputChange(e) {
      this.emitChange(e);
      this.remoteSearch();
    },
    setQuery(query) {
      this.searchWord = query;
    },
    handleFocus() {
      this.$emit('on-focus');
    },
    handleEnter(e) {
      this.showDropDown = false;
      this.emitChange();
      this.emitEnter(
        this.remoteList[this.activeIndex]
          ? this.remoteList[this.activeIndex][this.valueKey]
          : '',
        e,
      );
    },
    handleBlur() {
      this.$emit('on-blur', {
        value: this.searchWord,
        callBack: (value) => {
          this.searchWord = value;
        },
      });
      setTimeout(() => {
        this.showDropDown = false;
      }, 300);
    },
    handleKeydown(event) {
      if (this.remoteList.length <= 0) {
        return false;
      }
      let self = this,
        length = self.remoteList.length,
        dropdownDom = document.querySelector('#dropdown-wrap');
      if (!dropdownDom) {
        return false;
      }
      let e = event || window.event || arguments.callee.caller.arguments[0];
      let activeItemSelector = `.${self.itemClass}[data-index="${self.activeIndex}"]`;
      let currentActiveItem = dropdownDom.querySelector(activeItemSelector);
      let scrollHeight = currentActiveItem
        ? currentActiveItem.offsetHeight
        : 50;
      scrollHeight *= 1;
      // 如果点击向上按钮
      if (e && e.keyCode === 38 && self.showDropDown) {
        dropdownDom.scrollTop -= scrollHeight;
        self.activeIndex--;
        if (self.activeIndex < 0) {
          self.activeIndex = -1;
        }
        self.setActiveItem();
      }
      // 如果点击向下按钮
      if (e && e.keyCode === 40 && self.showDropDown) {
        self.activeIndex++;
        if (self.activeIndex > length - 1) {
          self.activeIndex = length - 1;
        }
        if (self.activeIndex > 0) {
          dropdownDom.scrollTop += scrollHeight;
        }
        if (
          dropdownDom.scrollTop + dropdownDom.offsetHeight + 50 >
          dropdownDom.scrollHeight
        ) {
          dropdownDom.scrollTop = dropdownDom.scrollHeight;
        }
        self.setActiveItem();
      }
    },
    resetValue() {
      this.$emit('resetValue', '');
    },
    handleClear() {
      this.emitChange();
      this.emitEnter('');
    },
    selectItem(item) {
      this.searchWord = item[this.labelKey];
      this.showDropDown = false;
      this.emitChange();
      this.emitEnter(item[this.valueKey]);
    },
    emitChange(e) {
      this.$emit('input', this.searchWord);
      this.$emit('on-change', this.searchWord, e);
    },
    emitEnter(value = '', e) {
      this.$emit('on-enter', value, e);
    },
    remoteSearch() {
      let query = this.searchWord;
      query = query.trim();
      if (query) {
        this.selectLoading = true;
        // debounce(function () {
        console.log('AAA');
        this.getRemoteMethod(query).then(({ status, data }) => {
          this.selectLoading = false;
          if (status) {
            this.remoteList = this.pagination ? data.list : data;
          } else {
            this.remoteList = [];
          }
          this.showDropDown = true;
          if (this.lastChange) {
            this.remoteSearch(this.lastChange);
            this.lastChange = '';
          }
          this.activeIndex = -1;
          this.$nextTick(() => {
            // this.keyControl()
          });
        });
        // }, 300)
      } else {
        this.temp = '';
        this.remoteList = [];
      }
    },
    getRemoteMethod(query) {
      let remoteMethod = this.dataProvider;
      if (typeof remoteMethod === 'string') {
        return this.$request.get(this.dataProvider, {
          ...this.otherParams,
          [this.queryKey]: query,
        });
      } else if (typeof remoteMethod === 'function') {
        return this.dataProvider(query);
      }
    },
    setActiveItem() {
      if (this.remoteList[this.activeIndex]) {
        this.searchWord = this.remoteList[this.activeIndex][this.labelKey];
      }
    },
    keyControl() {},
  },
};
</script>
