<template>
  <div style="display: inline-block" @click="doExport">
    <slot>
      <Button style="margin: 0" :icon="icon" :type="type" :disabled="disabled">
        {{ loading ? '导出中...' : text }}
      </Button>
    </slot>
  </div>
</template>

<script>
import Button from '@components/button';
import { exportLoop } from './util';
import { formatUrlParams } from '@/util'

export default {
  name: 'ExportButton',
  autoRegister: true,
  components: {
    Button
  },
  props: {
    /**
     * 是否是离线导出
     */
    offline: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    /**
     * 导出api地址
     */
    api: {
      default: ''
    },
    /**
     * 导出api地址
     */
    loopApi: {
      default: ''
    },
    /**
     * 获取导出参数的方法
     */
    paramGetter: {
      type: Function,
      default: null
    },
    text: {
      default: '导 出'
    },
    type: {
      type: String,
      default: 'default'
    },
    icon: {
      type: String,
      default: ''
    },
    // 直接下载
    download: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      selfLoopApi: this.apiUrl.listCount
    };
  },
  created() {
    if (this.loopApi) {
      this.selfLoopApi = this.loopApi;
    }
  },
  methods: {
    handleSuccess() {
      this.$emit('on-success');
    },
    handleClick() {
      let res = true;
      this.$emit('on-click', val => {
        res = val;
      });
      return res;
    },
    doExport() {
      // 用于处理点击导出按钮后进行其他操作
      this.$emit('handle-click-export')
      let clickRes = this.handleClick();
      if (!clickRes) return;
      let params = {
        isApi: true
      };
      if (this.paramGetter) {
        if (this.paramGetter() === false) {
          return false
        }
        params = {
          ...params,
          ...this.paramGetter()
        };
      }
      // 直接下载
      if (this.download) {
        window.location = `${this.api}?${formatUrlParams(params)}`
        this.handleSuccess();
        return
      }
      if (!this.api) {
        return false;
      }
      if (this.loading) {
        return false;
      }
      this.loading = true;
      this.$request.post(this.api, params).then(
        res => {
          let { status, message, data } = res;
          this.loading = false;
          if (status) {
            // 离线导出
            if (this.offline) {
              // this.successMessage(
              //   message ? message : '操作成功，导出任务执行中'
              // );
              this.$store.commit('showTaskCenter', true);
              exportLoop(data.task_no);
              this.handleSuccess();
            } else {
              this.handleSuccess(data);
              let url = data.file || data.url || data.path || data;
              location.href = url;
            }
          } else {
            if (message) {
              this.errorNotice({
                title: '导出失败',
                desc: message
              });
            } else {
              this.errorNotice({
                title: '导出失败'
              });
            }
          }
        },
        res => {
          this.loading = false;
          let { message } = res;
          if (message) {
            this.errorMessage(message);
          } else {
            this.errorMessage('导出失败！');
          }
        }
      );
    }
  }
};
</script>

<style scoped></style>
