/*
 * @Date: 2022-09-29 11:22:41
 * @Desc: 全局-导出处理
 * @LastEditTime: 2023-03-16 20:03:42
 * @FilePath: /sdpbase-pro/src/components/common/export-btn/util.js
 */
/**
 * Created by ddcoder on 19/4/26.
 */

import store from '@/vuex'
import { api } from '@api/api';
import { get, post } from '@api/request';
import Bus from "@api/bus";
import SdpProgress from '@/plugins/iview/progress';

/**
 * 导出任务轮询
 * @param opts
 * @param opts {func} 轮询的函数
 * @param opts {maxLoopTIme} 最长轮询时间
 * @param opts {frequence} 轮询频率
 */
const Loop = (opts) => {
  const defaultOps = {
    func: () => {},
    autoStart: true,
    timer: null,
    time: 0, // 已轮训的时间
    maxLoopTime: 30 * 24 * 60 * 1000, // 最长轮训时间
    frequency: 1000, // 默认1s轮询一次
  };
  
  let loopOptions = Object.assign(defaultOps, opts);
  if(loopOptions.needProgress) {
    loopOptions.progressPercent = 0
  }
  const startLoop = () => {
    if (loopOptions.timer === null) {
      loopOptions.timer = setInterval(() => {
        if (loopOptions.time - loopOptions.maxLoopTime >= 0) {
          endLoop();
        }
        if(loopOptions.needProgress) {
          console.log('progressPercent', loopOptions.progressPercent)
          if (loopOptions.progressPercent < 99) {
            loopOptions.progressPercent += 1;
            loopOptions.SdpProgress.update(loopOptions.progressPercent);
          } 
        }
        loopOptions.time += loopOptions.frequency;
        loopOptions.func && loopOptions.func();
      }, loopOptions.frequency);
    }
  };
  const endLoop = () => {
    if (loopOptions.timer !== null) {
      clearInterval(loopOptions.timer);
      loopOptions.time = 0;
      loopOptions.timer = null;
      if(loopOptions.needProgress) {
        loopOptions.SdpProgress.update(100);
        loopOptions.SdpProgress.finish();
      }
    }
  };
  if (loopOptions.autoStart === true) {
    startLoop();
  }
  return {
    startLoop,
    endLoop
  }
};


/**
 * 导出轮询
 * @param task_no
 * @param vm
 * @param url
 */
const exportLoop = (task_no, vm, url, needProgress = false) => {
  if(needProgress) {
    SdpProgress.config({
      hideInfo: true,
      text: '生成中...'
    });
    SdpProgress.start();
  }
  url = url || api.taskResult;
  let loop = {};
  const request = () => {
    let params = {
      task_no
    };
    get(url, params).then((res) => {
      let { status,message } = res;
      if (status) {
        Bus.$emit('refresh-task-center');
        // 打开任务列表
        store.commit('showTaskCenter', true);
        loop.endLoop();
      }else{
        if(message!='正在导出中'){
          loop.endLoop();
          // 导出失败根据是否返回正在导出中判断
          Bus.$emit('task-error', message);//抛出异常
          Bus.$emit('refresh-task-center');
        }
      } 
    }, () => {
      loop.endLoop();
    })
  };
  if(needProgress) {
    loop = Loop({
      SdpProgress: SdpProgress,
      func: request,
      needProgress: needProgress,
    })
  } else {
    loop = Loop({
      func: request
    });
  }
};

export {
  Loop,
  exportLoop
}
