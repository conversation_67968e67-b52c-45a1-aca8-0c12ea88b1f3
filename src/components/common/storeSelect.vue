<template>
  <Select
    v-model="storeId"
    @on-change="updateValue"
    :filterable="filterable"
    :disabled="disabled"
    :placeholder="placeholder"
    :filter-by-label="filterByLabel"
  >
    <Option value="" v-if="showAll">全部仓库</Option>
    <Option :value="item.id" v-for="item in list" :key="item.id" :label="item.name">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
import store from '@api/storeRoom.js';
export default {
  autoRegister: true,
  name: 'storeSelect',
  props: {
    value: {
      default: ''
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    defaultFirst: {
      // 默认选中第一个库房
      type: Boolean,
      default: true
    },
    defaultChange: {
      // 默认选中第一个库房的同时触发on-change事件
      type: Boolean,
      default: true
    },
    showAll: {
      type: Boolean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      default: '选择仓库'
    },
    filterByLabel:{
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      storeId: '',
      list: []
    };
  },
  watch: {
    value(newValue) {
      this.storeId = newValue;
      if (
        !newValue &&
        this.defaultFirst === true &&
        this.list &&
        this.list.length > 0
      ) {
        this.storeId = this.list[0]['id'];
      }
    },
    storeId(val) {
      this.$emit('input', val);
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.list = [];
      store.getUserWarehouse().then(res => {
        if (res.status) {
          this.list = res.data;
          if (this.defaultFirst === true && !this.storeId) {
            this.storeId = this.list[0]['id'];
            if (this.defaultChange) {
              this.updateValue();
            }
          }
        }
      });
    },
    updateValue() {
      this.$emit('input', this.storeId);
      let currentStore = this.list.find(item => item.id === this.storeId);
      this.$emit('on-change', currentStore, this.storeId);
    },
    getSelectedItem() {
      return this.list.find(item => item.id === this.selfValue);
    }
  }
};
</script>

<style scoped></style>
