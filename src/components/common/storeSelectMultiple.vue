<template>
  <Select
    v-model="chosedIdS"
    :filterable="filterable"
    :disabled="disabled"
    :multiple="true"
    :filter-by-label="filterByLabel"
    :max-tag-count="1"
    :max-tag-placeholder="(num) => `+ ${num}`"
  >
    <Option
      @on-click-all="_onClickAll"
      :value="item.value"
      v-for="item in list"
      :key="item.value"
      :label="item.label"
      >{{ item.label }}</Option
    >
  </Select>
</template>

<script>
import { Option } from '@components/select-sp/index';
import store from '@api/storeRoom.js';

let timeout = null;

export default {
  autoRegister: true,
  name: 'storeSelectMultiple',
  components: {
    // Select,
    Option,
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      default: '请选择仓库',
    },
    isHint: {
      // 全都不选的时候是否给提示
      type: Boolean,
      default: true,
    },
    isEmpty: {
      // 全都不选的时候store_id 返回-1,以便让表格数据清空
      type: Boolean,
      default: true,
    },
    isDefaultChoiceAll: {
      // 是否默认全选
      type: Boolean,
      default: true,
    },
    filterByLabel: {
      type: Boolean,
      default: false,
    },
    requestCached: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chosedIdS: [], // 已选择的仓库id
      originIdS: [], // 所有的仓库id
      list: [],
    };
  },
  watch: {
    value(newValue) {
      this.chosedIdS = newValue.split(',');
    },
    chosedIdS(newValue, oldValue) {
      if (newValue.length === 0 && oldValue.length === 1) {
        this._hint();
      }

      if (newValue.length === 0 && this.isEmpty) {
        newValue = ['-1'];
      }
      this.$emit('on-change', newValue.join(','));
    },
  },
  computed: {
    isChoiceAll() {
      return this.chosedIdS.length === this.list.length - 1;
    },
  },
  created() {
    this._getList();
  },
  methods: {
    /**
     * @description: 提示
     */
    _hint() {
      if (this.isHint) {
        // 避免多次触发
        clearTimeout(timeout);
        timeout = setTimeout(() => {
          this.$Notice.warning({
            title: '请至少选择一个仓库！',
          });
        }, 200);
      }
    },
    /**
     * @description: 点击全选的情况
     */
    _onClickAll() {
      if (this.isChoiceAll) {
        this.chosedIdS = [];
        this._hint();
      } else {
        this.chosedIdS = this.deepClone(this.originIdS);
      }
    },
    /**
     * @description: 得到仓库数据
     */
    _getList() {
      store.getUserWarehouse({}, { cache: this.requestCached }).then((res) => {
        if (res.status) {
          const data = res.data;
          this.list = data.map((item) => {
            return {
              value: item.id,
              label: item.name,
            };
          });
          // 全选
          this.isDefaultChoiceAll &&
            (this.chosedIdS = data.map((item) => {
              return item.id;
            }));
          this.originIdS = this.deepClone(this.chosedIdS);
          !this.isDefaultChoiceAll && (this.chosedIdS = [this.list[0].value]);
          // if(this.isDefaultChoiceAll){
          this.list.unshift({
            value: '_spforall',
            label: '全部',
          });
          // }
        } else {
          this.$Notice.error({
            title: res.message,
          });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
/deep/ .ivu-select-selection {
  .ivu-tag-checked {
    // padding: 0 5px;
    max-width: 128px;
  }
}
</style>
