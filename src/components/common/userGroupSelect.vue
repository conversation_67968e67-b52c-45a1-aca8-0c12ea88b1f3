<template>
  <Select
    :multiple="multiple"
    style="width: auto"
    ref="sorter"
    v-model="groupId"
    @on-change="updateValue"
    :filterable="filterable"
    :clearable="clearable"
    :placeholder="placeholder"
  >
    <Option value="" v-if="showAll">全部集团</Option>
    <Option value="0" v-if="showNo">无集团</Option>
    <Option :value="item.id" :key="index" v-for="(item, index) in list">{{
      item.group_name
    }}</Option>
  </Select>
</template>

<script>
export default {
  name: 'userGroupSelect',
  props: {
    value: {
      default: '',
    },
    placeholder: {
      default: '选择集团',
    },
    // 默认选中第一个
    defaultFirst: {
      type: Boolean,
      default: false,
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    showNo: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    multiple: {
      type: <PERSON>olean,
      default: false,
    },
    remote: {
      type: <PERSON><PERSON><PERSON>,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      groupId: '',
      list: [],
    };
  },
  watch: {
    // value(newValue) {
    //   alert('newValue---', newValue)
    //   this.groupId = newValue;
    // },
    value: {
      immediate: true,
      handler(newValue) {
        this.groupId = newValue;
      },
    },
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      this.loading = true;
      this.list = [];
      let url = '/superAdmin/userGroup/list';
      let params = {
        pageSize: 10000,
      };
      this.$request.get(url, params, { cache: true }).then((res) => {
        let { data, status } = res;
        if (status && data.list.length > 0) {
          this.list = data.list;
          if (this.defaultFirst === true) {
            this.groupId = this.list[0].id;
          }
        } else {
          this.list = [];
        }
      });
    },
    updateValue() {
      this.$emit('input', this.groupId);
      this.$emit('on-change', this.groupId);
    },
  },
};
</script>

<style scoped></style>
