<template>
  <div class="import-box">
    <div class="import-box__dialog__body__import__description">
      <Upload
        ref="upload"
        class="import-box__dialog__body__import__upload__box"
        :action="post.url"
        :data="data"
        :show-upload-list="false"
        :on-format-error="onFormatError"
        :on-error="onError"
        :on-success="onSuccess"
        :on-exceeded-size="onExceededSize"
        :on-progress="onProgress"
        :before-upload="beforeUpload"
        :mask-closable="false"
        :accept="post.accept"
        :format="post.format"
        :max-size="post.maxSize"
      >
        <Button
          :disabled="importing"
          :loading="loadingStatus"
          class="import-box__dialog__body__select"
          :type="styleType ? 'default' : 'primary'"
          :styleType="styleType"
          >{{ importing ? "导入中..." : btnText }}</Button
        >
      </Upload>
      <span
        v-show="fileName"
        class="import-box__dialog__body__import__file__name ml12"
      >
        {{ fileName }}
        <span @click="removeFile">
          <Icon
            icon="solid-close"
            size="mini"
            class="import-box__dialog__body__delete__file__icon"
          />
        </span>
      </span>
    </div>
    <slot name="custom-error-tip" :error-table="errorTable">
      <div
        v-show="errorTip"
        class="import-box__dialog__body__error__description mt14 mb27"
      >
        <span class="import-box__dialog__body__download__text__left"></span>
        <span class="ml12">{{ errorTip }}</span>
      </div>
    </slot>
  </div>
</template>
<script>
import { importLoop } from "./util";
import { Upload } from "view-design";
import Button from "@components/button/index";
import Icon from "@components/icon/index";
import "./style.less";
import Bus from "@api/bus";
import { MINE_TYPE } from "@/util/const";

export default {
  name: "ImportButton",
  autoRegister: true,
  components: {
    Button,
    Upload,
    Icon,
  },
  props: {
    /**
     * 是否是离线导入
     */
    offline: {
      type: Boolean,
      default: false,
    },
		// 导入成功是否直接返回数据
		returnData: {
			type: Boolean,
			default: false,
		},
    // 确认按钮文字
    btnText: {
      type: String,
      default: "选择文件",
    },
    // 下载模板
    download: {
      required: false,
      type: Object,
      default: () => {
        return {
          url: "",
          text: "",
        };
      },
    },
    // 上传时附带的额外参数
    data: {
      type: Object,
      default: () => ({}),
    },
    // 上传相关
    post: {
      required: false,
      type: Object,
      default: () => {
        return {
          url: "",
          accept: MINE_TYPE.excel.join(","),
          maxSize: Infinity,
          format: ["csv", "xlsx"],
        };
      },
    },
    styleType: {
      type: String,
      default: "",
    }
  },
  data() {
    return {
      importSuccess: false,
      importing: false,
      errorTip: "",
      errorTable: [],
      fileName: "",
      file: "",
      loadingStatus: false,
      upload: () => {},
    };
  },
  beforeDestroy() {
    Bus.$off("on-import-completed", this._onOfflineCompleted);
  },
  methods: {
    // 点击确定
    confirmUpload() {
      if (!this.file) {
        this.errorTip = "请选择文件";
        return;
      }
      if (this.errorTip) {
        return;
      }
      this.loadingStatus = true;
      this.upload(); // resolve beforeUpload 开始上传
    },
    // 文件格式校验失败
    onFormatError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `文件格式校验失败，请上传 ${
        this.post.format + ""
      } 文件。`;
    },
    // 文件上传失败
    onError() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件失败，请重试。`;
    },
    // 文件超过大小限制
    onExceededSize() {
      this.importing = false;
      this.loadingStatus = false;
      this.errorTip = `上传文件超过大小限制，文件不能超过 ${this.post.maxSize}kb。`;
    },
    // 文件开始上传
    onProgress() {
      this.importing = true;
      this.importSuccess = false;
    },
    beforeUpload(file) {
      this.fileName = file.name;
      this.file = file;
      this.errorTip = "";
      this.errorTable = [];
      this.emitChange();
      Bus.$on("on-import-completed", this._onOfflineCompleted);
      return new Promise((resolve) => {
        this.upload = resolve;
      });
    },
    // 文件上传成功
    onSuccess(response) {
      this.importing = false;
      this.loadingStatus = false;
      const { status, message, data } = response;
      if (status) {
        // 离线导入
        if (this.offline) {
          this.infoMessage(message || "操作成功，导入任务执行中");
          importLoop(data.exec_no);
        } else {
          this.importSuccess = true;
          this.successMessage(message || "批量导入成功");
					if (this.returnData) {
						this.$emit("on-completed", data);
						this.show = false
						return;
					} else {
						this.$emit("on-completed", this.importSuccess);
					}
        }
        this.errorTip = "";
        this.errorTable = [];
        this.removeFile();
      } else {
        if (this.offline) {
          this.errorMessage(message || "导入失败");
        } else {
          this.errorTip = response.message || "";
          this.errorTable = response.data || [];
        }
        this.importSuccess = false;
        this.$emit("on-completed", this.importSuccess);
      }
    },
    /**
     * @description: 离线导入模式完成时
     * @param {Boolean} isSuccess 是否成功
     */
    _onOfflineCompleted(isSuccess) {
      this.importSuccess = isSuccess;
      this.$emit("on-completed", this.importSuccess);
    },
    removeFile() {
      this.fileName = "";
      this.file = "";
      this.errorTip = "";
      this.errorTable = [];
      this.loadingStatus = false;
      this.emitChange();
    },
    emitChange() {
      this.$emit('on-change', this.file)
    }
  },
};
</script>

<style lang="less" scoped>
@box-prefix: import-box;

.@{box-prefix} {
  display: inline-block;
}

  .margin20 {
    margin: 20px 0;
  }
  .marginT20 {
    margin-top: 20px;
  }
  .ml12 {
    margin-left: 12px;
  }
  .mt14 {
    margin-top: 14px;
  }
  .mb27 {
    margin-bottom: 27px;
  }
  .@{box-prefix}__dialog__header {
    font-size: 14px;
    color: #303030;
    text-align: left;
    line-height: 14px;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center;
    font-weight: 500;
  }
  .@{box-prefix}__dialog__border {
    width: 3px;
    height: 12px;
    background: #505050;
    border-radius: 0.5px;
    border-radius: 0.5px;
    display: inline-block;
    margin-left: 10px;
    margin-right: 6px;
  }
  .@{box-prefix}__dialog__delete {
    position: absolute;
    right: 25px;
  }
  .@{box-prefix}__dialog__delete__icon {
    font-size: 10px;
    cursor: pointer;
  }
  .@{box-prefix}__dialog__body {
    margin-top: -16px;
    font-weight: 500;
    &__import__description {
      width: 100%;
      display: inline-flex;
      align-items: center;
    }
    &__download__description {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      margin-bottom: 0px;
    }
    &__download__text__left {
      display: inline-block;
      width: 120px;
      text-align: right;
    }
    &__download__text {
      color: #03ac54;
      cursor: pointer;
    }
    &__import__upload__box {
      display: inline-block;
    }
    &__select {
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
    }
    &__import__file__name {
      font-size: 13px;
      color: #505050;
      text-align: left;
      line-height: 14px;
      display: inline-flex;
      align-items: center;
      width: 200px;
    }
    &__delete__file__icon {
      margin-left: 16px;
      color: rgba(0, 0, 0, 0.2);
      cursor: pointer;
    }
    &__error__description {
      font-size: 13px;
      color: red;
      text-align: left;
      line-height: 14px;
    }
  }
  .@{box-prefix}__dialog__footer {
    &__cancel {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      background: #ffffff;
      // border: 1px solid #d8d8d8;
      border-radius: 2px;
      border-radius: 2px;
      margin-right: 4px;
    }
    &__confirm {
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
    }
  }
</style>
