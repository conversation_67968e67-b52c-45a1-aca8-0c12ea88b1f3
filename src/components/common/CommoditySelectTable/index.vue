<!--
 * @Author: <PERSON>
 * @Date: 2022-02-22 22:07:52
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-31 10:19:35
 * @Description: 商品选择组件
-->

<template>
  <div>
    <Dropdown
      ref="dropdown"
      trigger="custom"
      :visible="showDropdown"
      transfer
      transfer-class-name="commodity-select-table"
      events-enabled
      placement="bottom-start"
      @on-clickoutside="_onBlur"
    >
      <!-- dropdown源码禁用了右键菜单，这里需求需要放开 -->
      <div @contextmenu.stop>
        <Input
          :autofocus="true"
          ref="input"
          v-model="selfInputValue"
          :placeholder="placeholder"
          :disabled="disabled"
          :maxlength="50"
          @on-change="getCommodity"
          @on-enter="_enterCommodity"
          @on-focus="_onFocus"
          @on-keydown="_onKeydown"
          :style="
            'width: 100%; border: ' +
            inputStyle.border +
            '; color: ' +
            inputStyle.color +
            ';'
          "
          v-bind="inputProps"
        ></Input>
        <!--  @on-blur="_onBlur" -->
      </div>
      <DropdownMenu
        v-show="selfInputValue"
        slot="list"
        ref="dropdownMenu"
        class="dropdown-commodity"
        :style="{ minWidth: width }"
      >
        <template v-if="selectType === 'normal'">
          <template v-if="commodityList.length > 0">
            <li
              v-for="item of commodityList"
              :key="item[commodityIdKey]"
              :class="{
                'dropdown-commodity-item': true,
                active: selectIndex === item._index,
                selected: item.is_selected,
              }"
              @click.stop="_clickCommodity(item)"
              @mousedown.prevent
            >
              <slot name="option" :option="item">
                <div v-if="slotType === 'default'" style="width: 280px">
                  <span class="dropdown-commodity-item-name">{{
                    item[commodityNameKey]
                  }}</span>
                  <span class="dropdown-commodity-item-unit">{{
                    item.unit
                  }}</span>
                  <span class="dropdown-commodity-item-code">{{
                    item.commodity_code
                  }}</span>
                </div>
                <div
                  v-else-if="slotType.includes('order')"
                  style="width: 280px"
                >
                  <div style="display: flex; align-items: center">
                    <SIcon
                      v-show="+Goods.isProtocolGoods(item)"
                      icon="xie"
                      :size="16"
                      class="mr5"
                    />
                    <span class="dropdown-commodity-item-name">{{
                      item[commodityNameKey]
                    }}</span>
                    <span class="dropdown-commodity-item-unit mr2">{{
                      item.unit
                    }}</span>
                    <SIcon
                      v-show="
                        slotType === 'order-is_online' && item.is_online === 'N'
                      "
                      icon="xiajia"
                      :size="16"
                      class="ml5"
                    />
                  </div>
                  <div v-if="!!item.store">
                    现有库存：{{ item.store.existing }}
                  </div>
                  <div class="dropdown-commodity-item-code">
                    <span>{{ item.commodity_code }}</span>
                    <StepPricingPoptip
                      v-if="Goods.isStepPricingGoods(item)"
                      :goods="item"
                    ></StepPricingPoptip>
                    <Rate
                      style="float: right; font-size: 15px"
                      :value="item.star"
                      disabled
                    ></Rate>
                  </div>
                </div>
                <div v-else-if="slotType === 'bill'" style="width: 280px">
                  <div style="display: flex; align-items: center">
                    <SIcon
                      v-show="+Goods.isProtocolGoods(item)"
                      icon="xie"
                      :size="16"
                      class="mr5"
                    />
                    <span class="dropdown-commodity-item-name">{{
                      item[commodityNameKey]
                    }}</span>
                  </div>
                  <div>
                    <span class="dropdown-commodity-item-unit mr2">{{
                      item.unit
                    }}</span>
                    <span class="dropdown-commodity-item-code">{{
                      item.commodity_code
                    }}</span>
                  </div>
                </div>
                <div v-else-if="slotType === 'purchase'" style="width: 280px">
                  <div style="display: flex; align-items: center">
                    <span class="dropdown-commodity-item-name">{{
                      item[commodityNameKey]
                    }}</span>
                    <span class="dropdown-commodity-item-unit mr2">{{
                      item.unit
                    }}</span>
                    <SIcon
                      v-show="+item.price_type === 1"
                      icon="xie"
                      :size="16"
                      class="ml5"
                    />
                    <SIcon
                      v-show="+item.central_purchase_flag === 1"
                      icon="central"
                      :size="16"
                      class="ml5"
                    />
                    <SIcon
                      v-show="item.is_online === 'N'"
                      icon="xiajia"
                      :size="16"
                      class="ml5"
                    />
                  </div>
                  <div class="dropdown-commodity-item-code">
                    {{ item.commodity_code }}
                  </div>
                  <div
                    v-show="item.summary"
                    class="dropdown-commodity-item-summary"
                  >
                    {{ item.summary }}
                  </div>
                </div>
              </slot>
            </li>
          </template>
          <div v-else class="dropdown-commodity__empty">暂无数据</div>
        </template>
        <template v-else-if="selectType === 'table'">
          <!--stop防止调整表头宽度的时候触发失焦事件关闭表格-->
          <div
            class="com-list-table-con"
            v-if="commodityList.length > 0"
            @click.stop
          >
            <EditableTable
              key="com-list-table"
              :row-class-name="rowClassName"
              table-id="com-list-table"
              :max-height="190"
              ref="com-list-table"
              style="width: 770px"
              outer-border
              :columns="columns"
              :data="commodityList"
              @on-row-click="_clickCommodity"
            >
              <template #after-table-left>
                <p></p>
              </template>
              <template #after-table>
                <span></span>
              </template>
            </EditableTable>
            <div v-if="isShowAddBtn || isShowTemporaryBtn" class="btn-box">
              <p>
                没有找到此商品，点击
                <span
                  class="text-primary"
                  v-if="isShowAddBtn"
                  @click.stop="openCreateGoodModal"
                  >{{ isShowTemporaryBtn ? '系统商品' : '快速创建' }}</span
                >
                <span
                  class="text-primary"
                  v-if="isShowTemporaryBtn"
                  @click.stop="handleCreateTemporaryGoods"
                  >临时商品</span
                >
              </p>
            </div>
          </div>
          <div class="table" v-else>
            <div class="dropdown-commodity__empty">
              <img
                src="@/assets/images/commoditySearch/commodity-search-default.png"
                alt=""
                srcset=""
              />
              <!-- @click.stop="openCreateGoodModal" -->
              <div v-if="isShowAddBtn || isShowTemporaryBtn" class="btn-box">
                <p>
                  没有找到此商品，点击
                  <span
                    class="text-primary"
                    v-if="isShowAddBtn"
                    @click.stop="openCreateGoodModal"
                    >{{ isShowTemporaryBtn ? '系统商品' : '快速创建' }}</span
                  >
                  <span
                    class="text-primary"
                    v-if="isShowTemporaryBtn"
                    @click.stop="handleCreateTemporaryGoods"
                    >临时商品</span
                  >
                </p>
              </div>
            </div>
          </div>
        </template>
      </DropdownMenu>
    </Dropdown>
    <CreateGoodModal
      @on-close="handleCancelCreateGoodsModal"
      v-if="renderModal"
      ref="createGoodModalRef"
      :searchName="selfInputValueCopy"
      :extraParams="params"
      @createGood="createGood"
    />
    <TemporaryGoodsModal
      ref="temporaryGoods"
      @on-finish="createGood"
    ></TemporaryGoodsModal>
  </div>
</template>

<script>
import Dropdown from '@/components/base/dropdown/dropdown.vue';
import StepPricingPoptip from '@/pages/order/components/StepPricingPoptip.vue';
import SIcon from '@components/icon';
import CreateGoodModal from '@/components/goods/CreateGoodModal';
import TemporaryGoodsModal from '@/components/temporaryGoodsModal';
import { debounce } from 'lodash-es';
import Goods from '@api/goods.js';
import authority from '@/util/authority.js';
import ConfigMixin from '@/mixins/config';
import ListTable from '@/components/list-table';
import EditableTable from '@/components/editable-table';
import linImg from '@/assets/images/appCenter/lin.png';

const { hasAuthority } = authority;

export default {
  name: 'CommoditySelect',
  mixins: [ConfigMixin],
  components: {
    EditableTable,
    ListTable,
    Dropdown,
    StepPricingPoptip,
    SIcon,
    CreateGoodModal,
    TemporaryGoodsModal,
  },
  props: {
    inputStyle: {
      type: Object,
      default: () => ({
        border: 'none', // 默认边框
        color: '#000', // 默认字体颜色
      }),
    },
    // 远程搜索时是否去除两边的空格
    trimInput: {
      type: Boolean,
      default: true,
    },
    // 输入框显示的商品名称
    commodityName: {
      type: String,
      default: '',
    },
    // 已选择的商品
    selectedData: {
      type: Array,
      default: () => [],
    },
    // 远程搜索的额外参数
    params: {
      type: Object,
      default: () => ({}),
    },
    // 远程搜索api, 设置请求数据url, 支持函数返回Promise对象，return列表数组
    dataProvider: {
      type: [String, Function],
      required: true,
    },
    // 远程搜索对应的查询字段参数名，默认'query'
    queryKey: {
      type: String,
      default: 'query',
    },
    // 返回数据的商品id字段名，默认'commodity_id'
    commodityIdKey: {
      type: String,
      default: 'commodity_id',
    },
    // 返回数据的商品id字段名，默认'commodity_name'
    commodityNameKey: {
      type: String,
      default: 'commodity_name',
    },
    // Input组件的props属性
    inputProps: {
      type: Object,
      default: () => ({}),
    },
    // placeholder占位符
    placeholder: {
      type: String,
      default: '商品名/编码/别名/关键字',
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    slotType: {
      type: String,
      default: 'default',
    },
    selectType: {
      type: String,
      default: 'normal',
    },
    isOpenCreated: {
      type: Boolean,
      default: false,
    },
    // 是否显示下单指数
    isShowOrderIndex: {
      type: Boolean,
      default: true,
    },
    // 是否显示临时商品
    isShowTemporary: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    isShowAddBtn() {
      return hasAuthority('A001001002') && this.isOpenCreated;
    },
    isShowTemporaryBtn() {
      return (
        Number(this.sysConfig.is_open_temp_c) === 1 &&
        this.isShowTemporary &&
        hasAuthority('APP_TEMPORARY_ADD')
      );
    },
  },
  watch: {
    commodityName: {
      immediate: true,
      handler(newValue) {
        this.selfInputValue = newValue;
        this.selectIndex = -1;
      },
    },
    showDropdown(newValue) {
      if (newValue) {
        this.lockEmitEnter = true;
      } else {
        this.lockEmitEnter = false;
      }
      this.$emit('on-show-dropdown', newValue, this.renderModal)
    },
  },
  data() {
    return {
      activeRowIndex: 0,
      lockEmitEnter: false,
      renderModal: false,
      width: '',
      selfInputValue: '',
      selfInputValueCopy: '',
      selectIndex: -1,
      commodityList: [],
      showDropdown: false,
      Goods,
      hasScroll: false,
      columns: [
        {
          type: 'titleCfg', // 表头设置配置
          width: 42,
          align: 'center',
          fixed: 'left',
          key: 'title',
          titleType: 'order_rate_commodity',
          render: (h, params) => {
            const { row } = params;
            return (
              <div>
                {+Goods.isProtocolGoods(row) !== 0 && (
                  <SIcon
                    icon="xie"
                    size={14}
                    class="mr5"
                    style="position: absolute; left: 24px; top: 11px"
                  />
                )}
                {Goods.isDiscountGoods(row) && (
                  <SIcon
                    icon="zhe"
                    size={14}
                    class="mr5"
                    style="position: absolute; left: 20px;color: #ff6600;top: 9px"
                  />
                )}
                {+row.is_temp_c === 1 && (
                  <img
                    src={linImg}
                    class="mr-5"
                    style=" display: inline-block;width: 14px;height: 14px;position: absolute; left: 20px;top: 9px"
                    alt=""
                  />
                )}
              </div>
            );
          },
        },
        {
          title: '商品名称',
          key: 'commodity_name',
          width: 180,
          style: {
            paddingLeft: '0px',
            paddingRight: '5px',
          },
          fixed: 'left',
          show: true,
          minWidth: 100,
          render: (h, params) => {
            const { row } = params;
            return (
              <div>
                <div style="display: flex; position: relative;padding-right: 35px;">
                  <p
                    class="line-clamp-3 commodity-name"
                    style="max-width: 160px"
                  >
                    {row.commodity_name}
                  </p>
                  {row.is_online === 'N' && (
                    <SIcon
                      style="vertical-align: middle;position: absolute;right: 0;"
                      icon="xiajia"
                      size="16"
                      class="ml5"
                    />
                  )}
                </div>
                <div>
                  {Goods.isStepPricingGoods(row) && (
                    <StepPricingPoptip
                      sIconClass=""
                      goods={row}
                    ></StepPricingPoptip>
                  )}
                </div>
              </div>
            );
          },
        },
        {
          title: '单位',
          width: 80,
          key: 'unit',
        },
        {
          title: '描述',
          style: {
            paddingRight: '5px',
          },
          width: 165,
          key: 'summary',
        },
        {
          title: '商品分类',
          width: 100,
          style: {
            paddingLeft: '10px',
            paddingRight: '10px',
          },
          key: 'category_name',
          render: (h, params) => {
            const { row } = params;
            const category =
              row.category_name +
              (row.category2_name ? `/${row.category2_name}` : '');
            return h('span', category || '--');
          },
        },
        {
          title: '现有库存',
          key: 'stock',
          width: 90,
          render: (h, params) => {
            const { row } = params;
            const stock = (!!row.store && row.store.existing) || '--';
            return h('span', stock);
          },
        },
        {
          title: '下单指数',
          width: 130,
          key: 'start',
          render: (h, params) => {
            const { row } = params;

            return h('Rate', {
              props: {
                value: row.star,
                disabled: true,
              },
              style: {
                float: 'right',
                fontSize: '15px',
              },
            });
          },
        },
        {
          title: '商品品牌',
          width: 90,
          key: 'brand',
        },
        {
          title: '商品别名',
          width: 160,
          key: 'alias',
        },
        {
          title: '商品产地',
          width: 80,
          key: 'product_place',
        },
        {
          title: '最近一次进价',
          width: 100,
          key: 'in_price',
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          width: 160,
        },
        {
          title: '客户商品别名',
          width: 160,
          key: 'user_commodity_alias_name',
        },
        {
          title: '客户商品别名编码',
          width: 160,
          key: 'user_commodity_alias_code',
        },
      ],
    };
  },
  created() {
    this.getCommodity = debounce(this.searchCommodity, 100);
  },
  mounted() {
    this.width = this.$refs.dropdown.$el.style.width;
  },
  methods: {
    hasAuthority,
    rowClassName(row, index) {
      return index === this.selectIndex ? 'table-line-active' : '';
    },
    handleRowClick(row, index) {
      this.selectIndex = index;
    },
    searchCommodity() {
      if (!this.selfInputValue || this.selfInputValue.trim() === '') {
        this.lockEmitEnter = false;
        return;
      }
      this.lockEmitEnter = true;
      this.selectIndex = -1; // 每次重新搜索必须清空
      const params = {
        [this.queryKey]: this.trimInput
          ? this.selfInputValue.trim()
          : this.selfInputValue,
        ...this.params,
        is_temp_c: this.isShowTemporaryBtn ? 2 : undefined,
      };

      let remoteMethod = this.dataProvider;
      if (typeof remoteMethod === 'string') {
        remoteMethod = async (params) => {
          const { status, data } = await this.$request.get(
            this.dataProvider,
            params,
          );
          let list = [];
          if (status) {
            list = data.commodities;
          }
          return list;
        };
      }
      remoteMethod(params).then((res) => {
        if (res) {
          let list = [];
          const { status, data } = res;
          if (status === undefined && Array.isArray(res)) {
            list = res;
          } else if (status && data) {
            list = data.commodities;
          }
          if (
            params[this.queryKey] !==
            (this.trimInput ? this.selfInputValue.trim() : this.selfInputValue)
          )
            return; // 连续输入时多次请求，旧的请求结果不需要
          this.commodityList = list;
          this.showDropdown = true;
          if (list.length) {
            // 加上排序
            this.commodityList.forEach((item, index) => (item._index = index));
            this._signSelected();

            let firstCom = list[0];
            this.selectIndex = firstCom._index; // 默认选中第一个商品
            this._setScrollTop();
          }
          this._updatePopper();
        }
      });
    },
    /**
     * @description: 遍历查询商品，如果已经被选中，则标记is_selected状态
     */
    _signSelected() {
      // 已经加入订单列表的商品设置属性区分
      if (this.selectedData.length) {
        this.commodityList.forEach((goods) => {
          goods.is_selected = this.selectedData.some(
            (item) => item[this.commodityIdKey] === goods[this.commodityIdKey],
          );
        });
      }
    },
    /**
     * @description: 更新Popper位置，解决Popper在上方且选项动态变少时的位置问题
     */
    _updatePopper() {
      if (this.$refs.dropdown && this.$refs.dropdown.$refs.drop) {
        this.$refs.dropdown.$refs.drop.update();
      }
    },
    _setScrollTop() {
      const dropdown = this.$refs.dropdownMenu.$parent.$el;
      this.$nextTick(() => {
        dropdown.scrollTop = 0;
      });
    },
    _onKeydown(event) {
      if (this.inputProps.type === 'textarea' && event.keyCode === 13) {
        event.preventDefault(); // 阻止textarea的回车换行
      }
      if (!this.selfInputValue || !this.showDropdown) return;
      event.stopPropagation();
      let e = event || window.event || arguments.callee.caller.arguments[0],
        checkId = this.selectIndex; // 选中的序号

      if (e.keyCode !== 38 && e.keyCode !== 40) return;

      let length = this.commodityList.length,
        first = 0,
        last = length - 1,
        dropdown = this.$refs.dropdownMenu.$parent.$el,
        itemElements = this.$refs.dropdownMenu.$el.querySelectorAll('li');
      if (this.selectType === 'table') {
        dropdown = this.$refs.dropdownMenu.$el.querySelector(
          '.sdp-table__content',
        );
        console.log(dropdown, 'jkjdklasjkldasj');
        itemElements = dropdown.querySelectorAll('tr');
      }
      console.log('出发了');
      // 如果点击向上按钮
      if (e && e.keyCode === 38) {
        if (checkId !== -1 && checkId > first) {
          this.selectIndex -= 1;
        }
        // 控制滚动条的滚动
        if (checkId < last) {
          dropdown.scrollTop = Math.ceil(
            dropdown.scrollTop - itemElements[checkId].clientHeight,
          );
        }
      }
      // 如果点击向下按钮
      if (e && e.keyCode === 40) {
        // 如果是首次点击向下
        if (checkId === -1) {
          this.selectIndex = first;
        }
        if (checkId !== -1 && checkId < last) {
          this.selectIndex += 1;
          if (checkId > 0) {
            dropdown.scrollTop = Math.ceil(
              dropdown.scrollTop + itemElements[checkId].clientHeight,
            );
          }
        }
      }
    },
    // table样式时, 选择商品会触发两次, 先记录下, 暂时不做调整, 就怕bug套bug, 某些功能在这此基础上能玩转...
    _clickCommodity(value) {
      this.selfInputValue = value[this.commodityNameKey];
      this.showDropdown = false;
      this.selectIndex = -1;
      this.$emit('on-change', value[this.commodityIdKey], value);
      setTimeout(() => (this.commodityList = []), 500);
    },
    /**
     * @description: 回车键确认商品
     * @param {*} event
     */
    _enterCommodity(event) {
      if (this.selectIndex >= 0) {
        const value = this.commodityList[this.selectIndex];
        this.selfInputValue = value[this.commodityNameKey];
        this.showDropdown = false;
        this.selectIndex = -1;
        this.$emit('on-change', value[this.commodityIdKey], value);
        setTimeout(() => (this.commodityList = []), 500);
      } else {
        if (this.lockEmitEnter) {
          return;
        }
        this.$emit('on-enter', event);
      }
    },
    _onFocus(event) {
      event.target.select();
      this.$emit('on-focus', event);
    },
    _onBlur() {
      this.selfInputValueCopy = this.selfInputValue;
      this.selfInputValue = this.commodityName; // 未选择时，恢复父组件传入的正确的商品名
      this.showDropdown = false;
      this.selectIndex = -1;
      setTimeout(() => (this.commodityList = []), 500);
    },
    outFocus() {
      this.$refs.input.focus();
    },
    openCreateGoodModal() {
      // 手动清空一下
      this._onBlur();
      this.renderModal = true;
      this.$nextTick(() => {
        this.$refs.createGoodModalRef.showModal();
      });
    },
    createGood(newGood) {
      console.log('newGood---', newGood);
      this.$emit('createGood', newGood);
    },
    handleCancelCreateGoodsModal() {
      this.renderModal = false
      this.$emit('on-show-dropdown', this.showDropdown, this.renderModal)
    },
    handleCreateTemporaryGoods() {
      // 手动清空一下
      this._onBlur();
      this.$refs.temporaryGoods.open({
        name: this.selfInputValueCopy,
      });
    },
  },
};
</script>

<style lang="less" scoped>
@deep: ~'>>>';
@{deep} .dropdown-commodity {
  // box-shadow: 0px 2px 14px 1px rgba(0,0,0,0.15) !important;
  border-radius: 4px;
  &-item {
    width: 100%;
    padding: 7px 10px;
    line-height: 20px;
    font-size: 12px;

    &-name {
      font-weight: 500;
      font-size: 13px;
      margin-right: 10px;
      &:extend(.line-clamp-2);
    }
    &-unit {
      font-size: 12px;
      color: #aaa;
    }
    &-code {
      font-size: 12px;
      color: #aaa;
    }
    &-summary {
      font-size: 12px;
      color: #aaa;
      line-height: 15px;
      &:extend(.line-clamp-2);
    }
    &.active {
      color: #03ac54;
      background-color: #ebf7ff;
    }
    &.selected {
      color: inherit !important;
      p {
        color: inherit !important;
      }
    }
    &:hover {
      cursor: pointer;
      background-color: #ebf7ff;
      color: #03ac54;
    }
  }

  &__empty {
    color: rgba(0, 0, 0, 0.15);
    text-align: center;
    img {
      margin: 48px 0 10px 0;
      width: 100px;
      height: 76px;
    }
    .btn-box {
      border-top: none !important;
      margin-bottom: 46px;
    }
  }
}

/deep/ .ivu-rate-star {
  margin-right: 0px;
}
.line-clamp-2 {
  display: -webkit-box;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.line-clamp-3 {
  display: -webkit-box;
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  line-clamp: 3;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
.table {
  width: 750px;
  margin-top: -10px;
  margin-bottom: -10px;
  .table-head {
    padding-left: 24px;
    background-color: #f6f8f9;
    font-size: 13px;
    font-weight: 500;
    line-height: 22px;
    display: flex;
    height: 36px;
    > span {
      padding-top: 11px;
      padding-bottom: 11px;
      text-align: left;
    }
  }
  .table-body {
    overflow-y: auto;
    max-height: 186px;
    .table-tr {
      padding: 6px 0 6px 24px;
      display: flex;
      font-size: 13px;
      border-bottom: 0.5px solid rgba(232, 232, 232, 0.3);
      > div {
        text-align: left;
      }
    }
  }
  .table-cell {
    padding-left: 8px;
    padding-right: 8px;
  }
  .icon-lin {
    display: inline-block;
    width: 16px;
    height: 16px;
  }
}
.btn-box {
  color: rgba(0, 0, 0, 0.6);
  border-top: 0.5px solid #e8e8e8;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  .text-primary {
    color: #03ac54;
    cursor: pointer;
  }
}
.list-table-con {
  padding: 0px;
}
</style>

<style lang="scss">
.commodity-select-table {
  .sdp-table__thead {
    z-index: 10 !important;
  }
  .sdp-table__th {
    background-color: #f2f4f6;
  }
  .sdp-table__content .sdp-table__tr {
    min-height: 39px;
    height: auto;
    &:hover {
      .commodity-name {
        color: #03ac54 !important;
      }
    }
  }
  .commodity-name {
    font-weight: bold;
  }
  .line-clamp-3 {
    display: -webkit-box;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: 3;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  td .sdp-table__cell {
    min-height: 51px;
    padding: 7px 18px;
    display: flex;
    align-items: flex-start;
    > div {
      -webkit-line-clamp: 3 !important;
    }
  }
  .table-line-active {
    .commodity-name {
      color: #03ac54 !important;
    }
    td {
      background: #e6f7ec !important;
    }
  }
}
</style>
