<template>
  <div class="input__type">
    <Select class="input__type--select" v-model="type" @on-change="_change">
      <Option v-for="item in data" :value="item.value" :key="item.value">{{ item.label }}</Option>
    </Select>
    <Input 
      class="input__type--input"
      v-model="search"
      :placeholder="placeholder"
      @on-change="_change"
    />
  </div>
</template>
<script>
import DateUtil from '@util/date.js'

export default {
  name: 'InputTypeSearch',
  props: {
    value: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择日期'
    }
  },
  data () {
    return {
      type: 1,
      search: '',
      default: null
    }
  },
  watch: {
    value: {
      immediate: true,
      handler (newValue) {
        if (!this.default) {
          this.default = this.deepClone(newValue);
        }
        this.type = newValue[0];
        this.search = newValue[1];
      }
    }
  },
  created () {
  },
  methods: {
    _change () {
      this.$emit('on-change', [this.type, this.search])
    },
    // 重置，父级组件会调用
    resetValue () {
      this.type = this.default[0];
      this.search = this.default[1];
    }
  }
};
</script>

<style lang="less" scoped>
.input__type {
  display: flex;
  &--select {
    flex-basis: 0px;
  }
  &--input {
    flex: 2;
    width: 232px;
  }
  /deep/.ivu-select-selection {
    border-color: transparent !important;
  }
  /deep/.ivu-select-selected-value {
    padding: 0 13px 0 0 !important;
    color: rgba(0, 0, 0, 0.85) !important;
  }
  /deep/.ivu-select-arrow {
    right: 0 !important;
  }
  /deep/.ivu-select-visible .ivu-select-selection {
    box-shadow: none;
  }
}
</style>
