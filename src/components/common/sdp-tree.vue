<template>
  <Tree
    :data="data"
    :children-key="childrenKey"
    :title-key="titleKey"
    :show-checkbox="showCheckbox"
    @on-check-change="handleCheckChange"
    @on-select-change='selectChange'
    :render="renderContent">
  </Tree>
</template>

<script>
  export default {
    name: "tree",
    props: {
      data: Array,
      childrenKey: {
        type: String,
        default: 'children'
      },
      titleKey: {
        type: String,
        default: 'title'
      },
      showIcon: {
        type: Boolean,
        default: false
      },
      showCheckbox: Boolean
    },
    methods: {
      renderContent(h, {root, node, data}) {
        let itemHtml = [];
        if (this.showIcon === true) {
          itemHtml.push(h('Icon', {
              props: {
                type: 'ios-folder-outline'
              },
              style: {
                marginRight: '8px',
              }
            }));
        }
        itemHtml.push(h('span', data[this.titleKey]));
        return h('span', {
          style: {
            display: 'inline-block',
            width: '100%',
            textAlign: 'left'
          }
        }, [
          h('span', itemHtml)
        ]);
      },
      handleCheckChange(checkedList) {
        this.$emit('on-check-change', checkedList);
      },
      selectChange (data) {
        this.$emit('on-select-change', data);

      }
    }
  }
</script>

<style scoped lang="less">
</style>
