<template>
  <div style="display: inline-block">
    <Select :disabled="disabled || disablePurchaseSelect" style="width:140px" :placeholder="placeholder" @on-change="updateValue" v-model="purchaseType.currentType">
      <Option value="" v-if="showEmpty">{{placeholder}}</Option>
      <Option :value="purchaseType.typeDic.typeAgent.value">{{purchaseType.typeDic.typeAgent.label}}</Option>
      <Option :value="purchaseType.typeDic.typeProviderDirect.value">{{purchaseType.typeDic.typeProviderDirect.label}}</Option>
      <Option :disabled="disablePurchaseSelectLaseOne" :value="purchaseType.typeDic.typeProvider.value" v-if="mode === 'all'">{{purchaseType.typeDic.typeProvider.label}}</Option>
    </Select>
    <Select :disabled="disabled" v-model="purchaseType.currentValue" :clearable="true" v-if="purchaseType.searchList.length > 0 || always"
            style="width: 200px" filterable @on-change="handlePurchaseSearch" :placeholder="subPlaceholder">
      <Option v-for="item in purchaseType.searchList" :value="item.value" :key="purchaseType.currentType + item.value">{{ item.label }}</Option>
    </Select>
  </div>
</template>

<script>
  import Util from '@api/util.js';
  import goods from '@api/goods.js';
  const mode = {
    all: 'all',
    agentProvider: 'agent_provider',
  };
  export default {
    name: "purchaseType",
    props: {
      disablePurchaseSelect: {
        type: Boolean,
        default: false
      },
      disablePurchaseSelectLaseOne: {
        type: Boolean,
        default: false
      },
      value: {
        default: ''
      },
      currentValue: {
        type: String,
        default: ''
      },
      placeholder: {
        default: '采购模式'
      },
      mode: {
        type: String,
        default: mode.agentProvider,
      },
      showEmpty: {
        type: Boolean,
        default:true
      },
      always: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      // 供应商和采购员过滤禁用
      isFilterDisabled: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      value(newValue) {
        this.purchaseType.currentType = newValue;
        this.handlePurchaseTypeChange();
        this.init = true;
      },
      currentValue(newValue) {
        this.purchaseType.currentValue = newValue;
      }
    },
    data() {
      return {
        init: true,
        subPlaceholder: '',
        purchaseType: {
          currentType: '',
          currentValue: '',
          typeDic: Util.purchaseType,
          searchList: [],
          agents: [],
          providers: []
        }
      }
    },
    created () {
      this.purchaseType.currentType = this.value;
      this.purchaseType.currentValue = this.currentValue;
      let params = {};
      // 供应商和采购员接口过滤禁用
      if (this.isFilterDisabled) {
        params = {
          filter_disable_provider: 1,
          filter_disable_agent: 1,
        }
      }
      goods.getPurchaseType(params).then((res) => {
        
        if (res.status) {
          let data = res.data;
          this.purchaseType.agents = data.agents.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
          this.purchaseType.providers = data.providers.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          });
        }
        this.handlePurchaseTypeChange();
      });
    },
    methods: {
      handlePurchaseTypeChange () {
        this.search_agent_selected = '';
        this.search_provider_selected = '';
        if (!this.init) {
          // this.purchaseType.currentValue = '';
        }
        this.purchaseType.searchList = [];
        if (this.purchaseType.currentType === this.purchaseType.typeDic.typeAgent.value) {
          this.purchaseType.searchList = this.purchaseType.agents;
          this.subPlaceholder = '请选择采购员';
        }
        else if (
          this.purchaseType.currentType === this.purchaseType.typeDic.typeProviderDirect.value ||
          this.purchaseType.currentType === this.purchaseType.typeDic.typeProvider.value
        ) {
          this.purchaseType.searchList = this.purchaseType.providers;
          this.subPlaceholder = '请选择供应商';
        }
        else {
          this.$emit('on-reset');
          this.purchaseType.searchList = [];
        }
        this.$emit('on-type-change');
        this.$nextTick(() => {
          this.purchaseType.currentValue = this.currentValue
          this.init = false;
        });
      },
      handlePurchaseSearch () {
      	let selectedItem = this.purchaseType.searchList.find(item => item.value === this.purchaseType.currentValue);
      	let label = selectedItem ? selectedItem.label : '';
        if (this.purchaseType.currentType === this.purchaseType.typeDic.typeAgent.value) {
          this.$emit('on-agent-change', this.purchaseType.currentValue, label);
        }
        else if (
          this.purchaseType.currentType === this.purchaseType.typeDic.typeProviderDirect.value ||
          this.purchaseType.currentType === this.purchaseType.typeDic.typeProvider.value
        ) {
          this.$emit('on-provider-change', this.purchaseType.currentValue, label);
        }
      },
      updateValue() {
        this.handlePurchaseTypeChange();
        this.$emit('input', this.purchaseType.currentType);
        this.$emit('on-change');
      },
      getSelectedItem () {
        let selectedItem = this.purchaseType.searchList.find(item => item.value === this.purchaseType.currentValue);
        if (selectedItem) {
        	selectedItem.is_provider = this.purchaseType.currentType === this.purchaseType.typeDic.typeProvider.value;
        }
        return selectedItem;
      },
    }
  }
</script>

<style scoped>

</style>
