<!--
 * @Author: <PERSON>
 * @Date: 2021-04-15 18:08:32
 * @LastEditors: hgj
 * @LastEditTime: 2022-10-28 15:10:08
 * @Description: 导出按钮（下拉多个按钮）
-->
<template>
  <div class="export-box">
    <Dropdown v-if="data.length > 1" slot="button">
      <Button class="export-more">
        {{ dropdownText }}
        <Icon size="mini" icon="arrow-down" />
      </Button>
      <DropdownMenu slot="list">
        <template v-for="(item, index) of data">
          <DropdownItem v-if="item.offline" :key="index" style="padding: 0">
            <ExportButton
              type="text"
              offline
              :text="item.text"
              :api="item.api"
              @on-success="handleSuccess"
              :param-getter="item.paramGetter"
              style="padding: 7px 16px; width: 100%"
            ></ExportButton>
          </DropdownItem>
          <DropdownItem v-else :key="index" @click.native="_handleExport(item)">
            <i v-if="item.set" class="ivu-icon ivu-icon-ios-settings"></i>
            {{ item.text }}</DropdownItem
          >
        </template>
      </DropdownMenu>
    </Dropdown>
    <Button v-else @click="_handleExport(data[0])">{{ dropdownText }}</Button>
  </div>
</template>

<script>
import ExportButton from '@components/common/export-btn';
import Button from '@components/button/index';
import Icon from '@components/icon/index';
import { get } from '@api/request.js';
import { exportLoop } from '@/components/common/export-btn/util';

export default {
  components: {
    ExportButton,
    Button,
    Icon,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    dropdownText: {
      type: String,
      default: '导出',
    },
  },
  methods: {
    handleSuccess(data) {
      this.$emit('on-success', true);
    },
    _handleExport(item) {
      console.log('item', item);
      if (item.onClick && typeof (item.onClick === 'function')) {
        const res = item.onClick();
        if (res === false) return;
      }
      const params =
        item.paramGetter && typeof item.paramGetter === 'function'
          ? item.paramGetter()
          : undefined;
      get(item.api, params).then(({ status, data, message }) => {
        if (status) {
          // 离线导出
          if (item.offline) {
            // this.successMessage(
            //   message ? message : '操作成功，导出任务执行中'
            // );
            console.log('导出任务-multi');
            this.$store.commit('showTaskCenter', true);
            exportLoop(data.task_no);
          } else {
            window.open(data.file || data.url || data, '_self');
            this.successNotice('导出成功');
          }
        } else {
          this.errorNotice({ title: '导出失败', desc: message });
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@deep: ~'>>>';
@box-prefix: export-box;
.@{box-prefix} {
  height: 30px;
  vertical-align: top;
  display: inline-block;

  @{deep} .export-more {
    overflow: hidden;
    display: inline-block;
    .sui-icon {
      transition: 0.3s transform;
      margin-left: 5px;
      margin-right: -5px;
    }
    &:hover {
      .sui-icon {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
