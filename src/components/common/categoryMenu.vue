<template>
  <div class="menu_panel">
    <!--<div class="menu_header" @click="hideMenu"><span>商品分类 </span>-->
    <!--<Icon type="chevron-up"></Icon>-->
    <!--</div>-->
    <div class="classification">
      <div
        class="menu_item"
        v-for="item in menu"
        :key="item.value"
        @mouseover="showSubMenu(item)"
        @click="selectItem"
      >
        <span class="sdp-no-replace-text">{{ item.label }}</span>
        <Icon
          type="ios-arrow-forward"
          v-if="item.children && item.children.length > 0"
        ></Icon>
      </div>
    </div>
    <transition name="slide-left">
      <div @mouseout="showMenu = false" @mouseover="showMenu = true">
        <div class="subMenu" v-show="showMenu">
          <div
            class="subMenu_item"
            v-for="subItem in subMenu"
            :key="subItem.value"
            @click="selectItem(subItem)"
          >
            <span class="sdp-no-replace-text">{{ subItem.label }}</span>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'category-menu',
  props: {
    menu: {
      type: Array,
      default: [],
    },
  },
  data() {
    return {
      showMenu: false,
      subMenu: [],
      parentCategory: '',
    };
  },
  methods: {
    hideMenu() {
      this.$emit('on-handleSelect', this.parentCategory, 'hide');
    },
    hideSubMenu() {
      if (this.showMenu) {
        this.showMenu = false;
      }
    },
    showSubMenu(menu) {
      this.parentCategory = menu;
      if (menu.children) {
        this.showMenu = true;
        this.subMenu = menu.children;
      }
    },
    selectItem(item) {
      // this.showMenu = false;
      this.$emit('on-handleSelect', this.parentCategory, item);
    },
  },
};
</script>

<style lang="scss" scoped>
.menu_panel {
  position: relative;
  background: #f5f6fa;
  max-height: 70vh;

  .classification {
    overflow-y: auto;
    display: inline-grid;
    width: 100%;
    max-height: 70vh;
  }
  .menu_header {
    padding: 10px;
    background: #e6e7ef;
    cursor: pointer;
    span {
      float: left;
    }
    i {
      float: right;
    }
  }
  .menu_item {
    cursor: pointer;
    padding: 15px 15px;
    display: inline-block;
    span {
      float: left;
    }
    i {
      float: right;
    }
    &:hover {
      background: #ffffff;
      span {
        color: #03ac54;
      }
      i {
        color: #03ac54;
      }
    }
  }
  .subMenu {
    position: absolute;
    background: #ffffff;
    z-index: 110;
    height: 100%;
    width: 100px;
    left: 99.9%;
    top: 0%;
    max-height: 70vh;
    overflow-y: auto;
    border: 1px solid #f9f9f9;
    .subMenu_item {
      cursor: pointer;
      padding: 15px 15px;
      &:hover {
        background: #eef1f9;
        span {
          color: #03ac54;
        }
      }
    }
  }
}
// .slide-left-enter,
// .slide-right-leave-active {
//   opacity: 0;
//   -webkit-transform: translate(100%, 0);
//   transform: translate(100%, 0);
// }

// .slide-left-leave-active,
// .slide-right-enter {
//   opacity: 0;
//   -webkit-transform: translate(-100%, 0);
//   transform: translate(-100% 0);
// }
</style>
