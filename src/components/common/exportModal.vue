<template>
  <s-modal
    ref="modal"
    type="default"
    :width="width || 780"
    :btns="0"
    title="自定义导出字段"
  >
    <div class="select-box">
      <div v-for="item in showList" :key="item.title" class="select-box__panel">
        <div class="title">{{ item.title }}</div>
        <div class="list">
          <CheckboxGroup
            @on-change="changeSeletion(item)"
            v-model="item.selected"
          >
            <Checkbox
              :value="item.checkAll"
              :indeterminate="item.indeterminate"
              @click.prevent.native="selectedAll(item)"
              label="all"
            >
              <span>全部</span>
            </Checkbox>
            <Checkbox
              :disabled="selection.disabled"
              v-for="(selection, index) in item.list"
              :key="index"
              :label="selection.key"
              >{{ selection.label }}
              <Tooltip
                :maxWidth="200"
                :content="selection.setTip"
                placement="top"
              >
                <i
                  class="sui-icon icon-tips"
                  v-if="selection.setTip"
                ></i> </Tooltip
            ></Checkbox>
          </CheckboxGroup>
        </div>
      </div>
    </div>
    <div class="bottom-box">
      <div class="left">
        <Checkbox true-value="1" false-value="0" v-model="notShowModalLastTime">
          <span>导出不再提示</span>
        </Checkbox>
      </div>
      <div class="right">
        <Button @click="cancel" style="margin-right: 10px">取消</Button>
        <Button @click="complete" type="primary">确定</Button>
      </div>
    </div>
  </s-modal>
</template>
<script>
import { SModal } from '@sdp/ui';

export default {
  name: 'exportModal',
  data() {
    return {
      showList: [],
      notShowModalLastTime: '0'
    };
  },
  props: {
		width: {
			type: Number,
			default: 780
		},
    groupList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    defaultDataList: {
      type: Array,
      default: () => {
        return [];
      }
    },
    exportDataList: {
      type: Object,
      default: () => {
        return {};
      }
    },
    // 合计字段 参考：/pages/purchase/order-list
    exportTotalFields: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  component: {
    SModal
  },
  methods: {
    complete() {
      let selected = [];
      let notSelected = [];
      this.showList.forEach(item => {
        selected = selected.concat(item.selected);
        item.keys.forEach(key => {
          if (item.selected.indexOf(key) === -1) {
            notSelected.push(key);
          }
        });
      });
      // 合计、小计入参
      const params = {};
      if (this.exportTotalFields.length) {
        this.exportTotalFields.forEach(item => {
          params[item.key] = selected.includes(item.key) ? 1 : 0;
        });
      }
      this.$emit(
        'complete',
        {
          selected,
          notSelected
        },
        params
      );
      window.localStorage.setItem(
        this.$route.path + '__showExportSeletedModalData',
        JSON.stringify({
          selected,
          notSelected
        })
      );
      window.localStorage.setItem(
        this.$route.path + '__notShowExportSeletedModal',
        this.notShowModalLastTime
      );
      this.$refs.modal.close();
    },
    cancel() {
      this.$refs.modal.close();
    },
    open(forceShow) {
      let shouldShow =
        +window.localStorage.getItem(
          this.$route.path + '__notShowExportSeletedModal'
        ) === 0;
      let cacheData = window.localStorage.getItem(
        this.$route.path + '__showExportSeletedModalData'
      );
      if (cacheData) {
        cacheData = JSON.parse(cacheData);
      }
      if (forceShow || shouldShow) {
        setTimeout(() => {
          this.generateShowList();
          this.$refs.modal.open();
        }, 100);
      } else {
        this.$emit('complete', {
          selected: cacheData.selected,
          notSelected: cacheData.notSelected
        });
      }
    },
    selectedAll(item) {
      if (item.selected.length === item.keys.length) {
        item.selected = this.defaultDataList || [];
        item.checkAll = false;
      } else {
        item.selected = this.deepClone(item.keys);
        item.checkAll = true;
      }
      item.indeterminate = false;
    },
    changeSeletion(item) {
      if (item.selected.length === item.keys.length) {
        item.indeterminate = false;
        // iview bug，在 CheckboxGroup 下嵌套一个想不受其控制的 Checkbox 时，需要延迟改变状态
        setTimeout(() => {
          item.checkAll = true;
        }, 10);
      } else {
        item.indeterminate = true;
        item.checkAll = false;
      }
      if (item.selected.length === 0) {
        item.indeterminate = false;
      }
    },
    generateShowList() {
      let showList = this.deepClone(this.groupList);
      // 取缓存的已选择项
      let cacheData = window.localStorage.getItem(
        this.$route.path + '__showExportSeletedModalData'
      );
      if (cacheData) {
        cacheData = JSON.parse(cacheData);
      } else {
        cacheData = {
          selected: []
        };
      }
      showList.forEach(item => {
        let list = [];
        item.selected = [];
        item.keys.forEach(key => {
          if (this.exportDataList[key]) {
            let keyObj = {
              key,
              label: this.exportDataList[key]
            };
            if (key == 'all_need') {
              keyObj.setTip =
                '新增、编辑采购单时，可供修改的数量，用于创建采购单时提前制定采购需求';
            }
            // 默认选择项
            if (this.defaultDataList.indexOf(key) > -1) {
              item.selected.push(key);
              keyObj.disabled = true;
            }
            // 有之前缓存的已选项
            else if (cacheData.selected.indexOf(key) > -1) {
              item.selected.push(key);
            }
            list.push(keyObj);
          }
        });
        item.indeterminate = false;
        item.checkAll = false;
        item.list = list;
        this.changeSeletion(item);
      });
      // 合计
      let exportTotalFields = [];
      if (this.exportTotalFields.length) {
        const selected = [];
        this.exportTotalFields.forEach(item => {
          const { key } = item;
          // 默认选择项
          if (this.defaultDataList.indexOf(item.key) > -1) {
            selected.push(key);
            item.disabled = true;
          }
          // 有之前缓存的已选项
          else if (cacheData.selected.indexOf(key) > -1) {
            selected.push(key);
          }
        });
        const rowItem = {
          title: '合计',
          list: this.exportTotalFields,
          keys: this.exportTotalFields.map(item => item.key),
          indeterminate: false,
          selected,
          checkAll: false
        };
        exportTotalFields = [rowItem];
        this.changeSeletion(rowItem);
      }
      this.showList = [...showList, ...exportTotalFields];
    }
  }
};
</script>
<style lang="less" scoped>
.sui-icon {
  font-size: 12px;
  cursor: pointer;
}
/deep/ .s-modal__type--default {
  padding: 14px 0;
}
/deep/ .s-modal__title {
  padding: 0 20px;
  margin-bottom: 10px;
}
.select-box {
  border-top: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  padding: 16px 24px 16px 24px;
  &__panel:first-child {
    margin-top: 0px;
  }
  &__panel {
    margin-top: 10px;
    .title {
      font-size: 13px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #303030;
      line-height: 14px;
    }
    .list {
      margin-top: 8px;
      /deep/ .ivu-checkbox-group {
        display: grid;
        grid-template-columns: repeat(5, 20%);
        // grid-template-rows: repeat(5, 20%);
      }
    }
  }
}
.bottom-box {
  padding: 10px 24px 0 24px;
  display: flex;
  justify-content: space-between;
}
</style>
