<template>
  <div class="advance-filter">
    <Row
      v-show="!filtered || allFilteredItems.length === 0"
      align="middle"
      type="flex">
      <Col class="flex-con">
      <slot></slot>
      </Col>
      <Col><slot name="trigger-button"><span class="pointer" @click="showModal">高级筛选</span></slot></Col>
    </Row>
    <Row
      class="margin-bottom"
      v-show="filtered && allFilteredItems.length > 0"
      align="middle"
      type="flex">
      <Col class="flex-con">
      <span class="filtered-label">筛选结果</span>
      <div
        v-for="filteredItem in filteredItems"
        class="filtered-item margin-left">
        <span>{{filteredItem.label}}：</span>
        <span>{{filteredItem.value_desc}}</span>
        <Icon
          class="remove"
          @click.native="removeFilteredItem(filteredItem)"
          type="android-close"></Icon>
      </div>
      <div
        v-for="filteredItem in extraFilteredItems"
        class="filtered-item margin-left">
        <span>{{filteredItem.label}}：</span>
        <span>{{filteredItem.value_desc}}</span>
        <Icon
          class="remove"
          @click.native="removeExtraFilteredItem(filteredItem.key)"
          type="android-close"></Icon>
      </div>
      </Col>
      <Col><slot name="trigger-button"><span class="pointer clear-all" @click="handleClearAll">清除筛选</span></slot></Col>
    </Row>
    <Modal
      className="vertical-center-modal"
      width="800"
      title="高级筛选"
      :closable="false"
      @on-cancel="handleCancel"
      v-model="selfShow">
      <Row
        v-for="(row, index) in selfRows"
        :key="index"
        class="filter-row"
        align="middle"
        type="flex">
        <template v-for="filterItem in row.cols">
          <Col
            class="filter-label"
            v-if="row.label">{{row.label}}</Col>
          <Col>
          <Select
            :filterable="filterItem.filterable !== undefined ? filterItem.filterable : true"
            :clearable="filterItem.clearable !== undefined ? filterItem.clearable : true"
            v-model="filters[filterItem.key]"
            v-if="filterItem.type === filterType.select"
            :style="filterItem.style"
            :placeholder="filterItem.placeholder">
            <template v-if="filterItem.data">
              <Option v-for="option in filterItem.data" :key="filterItem.key + option.value" :value="option.value">{{option.label}}</Option>
            </template>
          </Select>
          <CommonDatePicker
            :style="{ width: isRangeFilter(filterItem) ? '180px' : '100px' }"
            :type="filterItem.type"
            v-model="filters[getFilterKey(filterItem)]"
            v-if="isDateType(filterItem)"
            :meta="filterItem"
            @on-change="handleDateChange"></CommonDatePicker>
          <slot :name="getSlotName(filterItem)" v-if="filterItem.type === filterType.slot"></slot>
          <CheckboxGroup v-model="filters[filterItem.key]" v-if="filterItem.type === filterType.checkboxGroup">
            <Checkbox :label="checkBox.value" v-for="(checkBox, index) in filterItem.data" :key="index">
              <span>{{checkBox.label}}</span>
            </Checkbox>
          </CheckboxGroup>
          <RadioGroup v-model="filters[filterItem.key]" v-if="filterItem.type === filterType.radioGroup">
            <Radio :label="radio.value" v-for="(radio, index) in filterItem.data" :key="index">
              <span>{{radio.label}}</span>
            </Radio>
          </RadioGroup>
          <Input
            class="search-input"
            v-if="!filterItem.type || filterItem.type === filterType.input"
            v-model="filters[filterItem.key]"
            :style="filterItem.style"
            :placeholder="filterItem.placeholder">
          </Input>
          </Col>
        </template>
      </Row>
      <div slot="footer">
        <Button
          class="button-margin-left"
          @click="handleClear"
          >清除</Button>
        <Button
          class="button-margin-left"
          @click="handleCancel"
          >取消</Button>
        <Button
          class="button-margin-left"
          @click="handleFilter"
          type="primary">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import { filterType } from '../list-table/util';
  const rangeFilter = ['daterange', 'datetimerange'];
  const multipleFilter= ['checkboxGroup'];
  const isRangeFilter = (filter) => {
  	return rangeFilter.includes(filter.type);
  };
  const isMultipleFilter = (filter) => {
    return multipleFilter.includes(filter.type);
  };
  export default {
    name: "AdvanceFilter",
    autoRegister: true,
    props: {
    	rows: {
    		type: Array,
        default: () => []
      }
    },
    components: {
    },
    watch: {
      show (show) {
        this.selfShow = show;
        if (show) {
          this.initFilters();
        }
      },
      selfShow (show) {
        if (show) {
          this.initFilters();
        }
      },
      rows: {
      	deep: true,
        handler () {
          this.initFilters();
        }
      }
    },
    computed: {
    	allFilteredItems () {
    		return [
          ...this.filteredItems,
          ...this.extraFilteredItems,
        ]
      }
    },
    data() {
      return {
      	selfShow: false,
        filtered: false,
        filterType,
        excludeFilterPrefix: '_exclude_',
      	filters: {},
        defaultFilters: {},
        filteredItems: [],
        extraFilteredItems: [],
        selfRows: []
      }
    },
    created () {
    	if (this.show !== undefined) {
        this.selfShow = this.show;
      }
      this.initFilters();
    },
    methods: {
      isRangeFilter (filterItem) {
      	return isRangeFilter(filterItem);
      },
      isDateType (filterItem) {
      	const dateType = ['date', 'daterange', 'datetime', 'datetimerange'];
      	return dateType.includes(filterItem.type);
      },
      /**
       * 获取自定义筛选项的slot名称
       * @param filterItem 自定义筛选项信息
       */
      getSlotName(filterItem) {
        return filterItem.key;
      },
      /**
       * 获取不需要传给后端的字段名称, 传递参数的时候会对这些字段进行排除
       * @param filterItem
       * @returns {string}
       */
      getExcludeFilterKey(filterItem) {
        return Array.isArray(filterItem.key) ? this.excludeFilterPrefix + filterItem.key.join('_') : this.excludeFilterPrefix + filterItem.key;
      },
      getFilterKey(filterItem) {
      	if (Array.isArray(filterItem.key)) {
      		return this.getExcludeFilterKey(filterItem);
        }
        return filterItem.key;
      },
      getFilterDefaultValue(filterItem) {
      	if (isRangeFilter(filterItem)) {
      		return Array.isArray(filterItem.default) ? filterItem.default : ['', ''];
        }
        if (isMultipleFilter(filterItem)) {
          return Array.isArray(filterItem.default) ? filterItem.default : [];
        }
        return filterItem.default === undefined ? '' : filterItem.default;
      },
      getFilterValueDesc(filterItem) {
      	let label = '';
        let filterKey = this.getFilterKey(filterItem);
        let filterValue = this.filters[filterKey];
      	if (filterItem.data && Array.isArray(filterItem.data))  {
      		let chooseItem = filterItem.data.find(item => item.value === filterValue);
      		if (chooseItem) {
      			return chooseItem.label;
          }
        }
        if (Array.isArray(filterValue)) {
      		label = filterValue.join(',');
          return label;
        }
        return filterValue;
      },
      /**
       * 排除不需要传递给后台的筛选项
       * @param filters
       */
      excludeFilters(filters) {
        let newFilters = {};
        for (let filterKey in filters) {
          if (!filterKey.startsWith(this.excludeFilterPrefix)) {
            newFilters[filterKey] = filters[filterKey];
          }
        }
        return newFilters;
      },
      getFilters () {
      	return this.deepClone(this.filters);
      },
    	initFilters () {
        this.filtered = false;
    		this.formatRows();
    		let filterKeys = Object.keys(this.filters);
    		this.rows.forEach(row => {
    			row.cols.forEach(col => {
    				let filterKey = this.getFilterKey(col);
    				let defaultValue = this.getFilterDefaultValue(col);
    				this.defaultFilters[filterKey] = defaultValue;
    				if (!filterKeys.includes(filterKey)) {
    					if (isRangeFilter(col)) {
    						if (!Array.isArray(col.key)) {
    							console.error(`配置项${row.label}-${col.label}的key需要是数组格式`);
                } else {
                  this.$set(this.filters, filterKey, defaultValue);
                  this.$set(this.filters, col.key[0], defaultValue[0]);
                  this.$set(this.filters, col.key[1], defaultValue[1]);
                }
              } else {
                this.$set(this.filters, filterKey, defaultValue);
              }
            }
          });
        });
      },
      isFiltered (filterItem) {
      	let filterKey = this.getFilterKey(filterItem);
      	let defaultValue = this.getFilterDefaultValue(filterItem);
      	if (this.filters[filterKey] === defaultValue && filterItem.show_default === true) {
      		return true;
        }
      	return this.filters[filterKey] !== defaultValue;
      },
      setFilter (key, value) {
      	this.filters[key] = value;
      },
      setFilteredItems () {
      	this.filteredItems = [];
      	this.selfRows.forEach(row => {
      		row.cols.forEach(col => {
      			if (this.isFiltered(col) && !this.isExtraFilteredItemExist(col)) {
              let filterKey = this.getFilterKey(col);
      				let filteredItem = {
      					key: filterKey,
                label: col.label,
                value_desc: this.getFilterValueDesc(col),
              };
              this.addFilteredItem(filteredItem);
            }
          });
        });
      },
      /**
       * @param item{key}
       * @param item{label}
       * @param item{value_desc}
       */
      addExtraFilteredItem (item) {
      	if (!this.isExtraFilteredItemExist(item)) {
          this.extraFilteredItems.push(item);
        }
      },
      isExtraFilteredItemExist (item) {
        return this.extraFilteredItems.some(existItem => existItem.key === item.key)
      },
      /**
       * @param item{key}
       * @param item{label}
       * @param item{value_desc}
       */
      addFilteredItem (item) {
        if (!this.filteredItems.some(existItem => existItem.key === item.key)) {
          this.filteredItems.push(item);
        }
      },
      resetFilteredItems () {
      	this.filteredItems = [];
      },
      removeFilteredItem (filteredItem) {
      	this.filters[filteredItem.key] = this.defaultFilters[filteredItem.key];
      	this.setFilteredItems();
      },
      removeExtraFilteredItem (key) {
      	let index = this.extraFilteredItems.findIndex(item => item.key === key);
      	if (index !== -1) {
          this.extraFilteredItems.splice(index, 1);
          this.$emit('on-remove-filter', key);
        }
      },
      formatRows () {
        this.selfRows = this.rows.map(row => {
        	if (!row.cols) {
            row.cols = [row];
          }
          return row;
        });
      },
      handleDateChange (date, filterItem) {
    		let filterKey = filterItem.key;
    		if (isRangeFilter(filterItem.type)) {
    			this.filters[filterKey[0]] = date[0];
          this.filters[filterKey[1]] = date[1];
        } else {
        }
      },
      closeModal () {
      	this.selfShow = false;
      },
      showModal () {
        this.selfShow = true;
      },
      handleCancel () {
        this.closeModal();
        this.$emit('on-cancel');
      },
      handleClearAll () {
        this.closeModal();
        this.filtered = false;
        this.filters = this.defaultFilters;
        this.filteredItems = [];
        this.extraFilteredItems = [];
        this.$emit('on-clear');
      },
      handleClear () {
        this.closeModal();
        this.filtered = false;
        this.filters = this.defaultFilters;
        let filters = this.getFilters();
        filters = this.excludeFilters(filters);
        this.$emit('on-filter', filters);
      },
      handleFilter () {
      	this.filtered = true;
      	let filters = this.getFilters();
      	filters = this.excludeFilters(filters);
      	this.setFilteredItems();
        this.$emit('on-filter', filters);
        this.closeModal();
      }
    },
  }
</script>

<style lang="less" scoped>
  .filter-row {
    margin-bottom: 10px;
  }
  .filter-label {
    width: 90px;
  }
  .filtered-label {
    padding-right: 15px;
    &:after {
      content: "";
      position: relative;
      display: inline-block;
      width: 1px;
      height: 30px;
    }
  }
  .clear-all {
    position: relative;
    top: 5px;
  }
  .filtered-item {
    display: inline-block;
    border: 1px solid #dedede;
    padding: 5px 10px;
    .remove {
      cursor: pointer;
      margin-left: 10px;
    }
  }
</style>

