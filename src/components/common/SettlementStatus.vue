<template>
  <div>
    <Select :placeholder="placeholder" v-model="selected" @on-change="change">
      <Option v-for="item in list" :value="item.id" :key="item.id">{{ item.name }}</Option>
    </Select>
  </div>
</template>
<script>
import PurchaseAudit from '@api/PurchaseAudit';
let purchaseAuditService = new PurchaseAudit();
export default {
  name: 'InputTypeSearch',
  props: {
    placeholder: {
      type: String,
      default: '请选择日期'
    }
  },
  data () {
    return {
      selected: '',
      list: []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler (newValue) {
        this.selected = newValue;
      }
    }
  },
  created () {
    this.getList();
  },
  methods: {
    getList () {
      purchaseAuditService.getAuditListSearchConfig().then(res => {
        if (res.status) {
          this.list = res.data.status || [];
        }
      });
    },
    change () {
      this.$emit('on-change', this.selected)
    },
    resetValue () {
      this.selected = '';
    }
  }
};
</script>

<style lang="less" scoped>
</style>
