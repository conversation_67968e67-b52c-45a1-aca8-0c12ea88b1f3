<template>
  <Select
    ref="select"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :disabled="disabled"
    :clearable="true"
    :placeholder="placeholder"
  >
    <Option :value="item.value" v-for="item in list" :key="item.value">{{
      item.label
    }}</Option>
  </Select>
</template>

<script>
export default {
  name: 'OrderSource',
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    filterable: {
      type: Boolean,
      default: true
    },
    placeholder: {
      default: '选择订单来源'
    }
  },
  watch: {
    value(newValue) {
      this.selfValue = newValue;
    }
  },
  data() {
    return {
      selfValue: '',
      list: []
    };
  },
  created() {
    this.selfValue = this.value;
    this.getList();
  },
  methods: {
    getList() {
      this.list = [];
      this.$request.get(this.apiUrl.orderListSearchConfig).then(res => {
    			let { status, data } = res;
    			if (status) {
            let source = Object.keys(data.source).map(value => {
              return {
                value,
                label: data.source[value]
              };
            });

            // 过滤当前项目版本没有的功能
            source = source.filter(item => {
              if (
                item.label === '商城APP' &&
                !this.$hasModule('is_sync_user_center')
              ) {
                return false;
              }
              return true;
            });

            source.unshift({
              value: '0',
              label: '全部'
            });

            this.list = source;
          }
        });
    },
    updateValue() {
      this.$emit('input', this.selfValue);
      this.$emit('on-change', this.selfValue);
    }
  }
};
</script>

<style scoped></style>
