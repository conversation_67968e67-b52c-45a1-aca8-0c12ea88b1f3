<template>
  <div
    class="top-menu"
    style="overflow-x: visible"
    :class="{
      indexMenu:
        ['/index', '/indexNew'].includes($route.path) || isHideSecondMenu,
    }"
  >
    <Row
      class="layout-nav"
      :class="currentEdition > 101 ? 'layout-green-bg' : ''"
      type="flex"
      align="middle"
      style="padding-left: 0"
    >
      <Col
        ><a class="layout-nav__logo" style="margin-right: 0" href="/"
          ><img v-if="logo.path" :src="logo.path" alt="logo" /></a
      ></Col>
      <Col>
        <span class="company-name">{{ companyName }}</span>
        <Dropdown
          @on-visible-change="handleSiteListVisible"
          v-if="isMainSiteAccount"
        >
          <span class="sui-icon icon-switch"></span>
          <DropdownMenu slot="list" v-show="siteList.length">
            <DropdownItem
              @click.native="switchSite(item)"
              :key="index"
              v-for="(item, index) in siteList"
              >{{ item.name }}</DropdownItem
            >
          </DropdownMenu>
        </Dropdown>
      </Col>
      <Col v-if="versionMap[currentEdition]">
        <img class="icon-version" :src="versionMap[currentEdition]" />
      </Col>
      <Col class="menu-feature flex-con">
        <Row justify="end" align="middle" type="flex">
          <Col v-if="+sysConfig.is_open_custom_href === 1">
            <quick-links></quick-links>
          </Col>
          <Col>
            <global-search />
          </Col>
          <Col v-if="showAdminHelp && newVersionHelp">
            <Badge :offset="[20, 20]" :count="showNewVersionDot" dot>
              <s-button
                size="mini"
                @click.native="showNewVersionHelp()"
                style="
                  border-color: var(--primary-color);
                  color: var(--primary-color);
                  margin-right: 12px;
                "
                circle
                >新功能</s-button
              >
            </Badge>
          </Col>
          <Col v-if="showAdminHelp">
            <!-- <router-link to="/guide"> -->
            <s-button
              type="primary"
              size="mini"
              @click.native="openUrl('http://help.sdongpo.com')"
              circle
              >帮助中心</s-button
            >
            <!-- </router-link> -->
          </Col>
          <Col v-if="isDemoProject">
            <div class="layout-nav__item">
              <Tooltip content="视频演示" placement="bottom">
                <s-icon
                  @click.native="jumpsVideoPresentation"
                  icon="shipinbofang"
                  :size="18"
                />
              </Tooltip>
            </div>
          </Col>
          <Col v-if="isDemoProject">
            <div class="layout-nav__item">
              <Tooltip content="演示系统数据切换" placement="bottom">
                <s-icon
                  icon="shujutianchong"
                  @click.native="genBusinessDataModal = true"
                  :size="18"
                />
              </Tooltip>
            </div>
          </Col>
          <Col v-if="isDemoProject">
            <div class="layout-nav__item">
              <Tooltip content="版本切换" placement="bottom">
                <span
                  class="sui-icon icon-switch"
                  @click="getVersionList"
                ></span>
              </Tooltip>
            </div>
          </Col>
          <!-- </Col> -->
          <Col @click.native="showMessages()" class="notice-wrap">
            <div class="layout-nav__item">
              <Tooltip content="消息" placement="bottom">
                <Badge :count="noticeCount">
                  <span class="sui-icon icon-remind"></span>
                </Badge>
              </Tooltip>
            </div>
          </Col>
          <Col v-if="guides" style="position: relative">
            <div class="_poptip">
              <div class="_poptipTag"></div>
              <div class="_poptipContent">
                <p>点击"消息"，在公告通知列表中可以查看历史收到的公告通知</p>
              </div>
              <div @click="shutDown" class="_poptipButton">
                <p class="_poptipButtonContent">知道了</p>
              </div>
            </div>
          </Col>
          <Col class="setting-wrap">
            <Dropdown class="layout-nav__item">
              <span>
                <img
                  class="layout-nav__avatar"
                  :src="require('@/assets/avatar.png')"
                  alt="avatar"
                /><span>{{ name }}</span>
              </span>
              <DropdownMenu slot="list" style="height: '48px'">
                <DropdownItem
                  v-for="(info, index) in menu"
                  :key="index"
                  @click.native="toUrl(info)"
                  >{{ info.name }}</DropdownItem
                >
                <DropdownItem @click.native="logout">退出账号</DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </Col>
        </Row>
      </Col>
    </Row>

    <Modal v-model="genBusinessDataModal" :width="300" title="演示系统数据切换">
      <p>是否确定填充{{ versionName }}数据？</p>
      <div slot="footer">
        <Button @click="genBusinessDataModal = false">关闭</Button>
        <Button @click="genBusinessData" type="success">确定</Button>
      </div>
    </Modal>
    <div
      class="modal-mask"
      v-if="showRightModal"
      @click="showRightModal = false"
    ></div>
    <transition name="rightFade">
      <div
        style="
          padding: 15px 0;
          postion: relative;
          box-shadow: 7px 0 20px 6px rgb(0, 0, 0, 0.3);
        "
        class="rightModal"
        ref="noticeModal"
        v-if="showRightModal"
      >
        <span style="position: absolute; right: 10px; top: 10px; z-index: 999">
          <Icon
            style="cursor: pointer"
            @click.native="toggleNoticeRing"
            size="20"
            :type="noticeRing ? 'ios-notifications' : 'ios-notifications-off'"
          ></Icon>
        </span>
        <div class="modal-body">
          <Tabs
            style="margin: 0 20px"
            value="orderNotice"
            @on-click="switchTab"
          >
            <TabPane
              :label="label1"
              name="orderNotice"
              v-show="boxData.order_counter"
            >
              <div class="rightModalBtns">
                <Button
                  type="primary"
                  icon="checkmark"
                  size="small"
                  @click="readAll(1)"
                  :disabled="orderReadAll"
                  >全部已读</Button
                >
                <Button
                  type="primary"
                  icon="close"
                  size="small"
                  @click="cleanAll(1)"
                  >清 空</Button
                >
              </div>
              <div
                class="messages hoverColor"
                id="margom"
                v-for="(notice, index) in noticeData.orderNoticeList"
                :key="index"
              >
                <p
                  class="messagesContent"
                  style="margin: 0 0 20px 0"
                  :class="{ isRead: notice.is_read === '2' }"
                  @click="readMessage(notice.id, notice.type, notice.code)"
                >
                  <span style="display: block">{{ notice.create_time }}</span>

                  <!-- <span v-if="notice.is_read === '1'">[未读]</span> -->
                  <span v-if="notice.is_read !== '1'">[已读]</span>
                  <span v-html="notice.content"></span>
                </p>
                <div class="icon" v-if="notice.is_top === '1'">
                  <img
                    src="data:image/jpg;base64,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"
                    alt=""
                  />
                </div>
              </div>
              <div
                class="noMore"
                style="margin-top: 145px"
                v-if="noticeData.orderNoticeList.length === 0"
              >
                <img src="../../assets/message.png" class="NoMessages" />
                <p>暂时没有消息</p>
              </div>
              <div
                class="viewMore"
                @click="loadMore"
                v-if="!noticeNoMore && noticeData.orderNoticeList.length > 0"
              >
                加载更多...
              </div>
              <div class="noMore" v-if="noticeNoMore">没有更多了</div>
            </TabPane>
            <!-- 预警信息 -->
            <TabPane :label="label3" name="warningNotice">
              <div class="rightModalBtns">
                <Button
                  type="primary"
                  icon="checkmark"
                  size="small"
                  @click="readAll(14)"
                  :disabled="warningReadAll"
                  >全部已读</Button
                >
                <Button
                  type="primary"
                  icon="close"
                  size="small"
                  @click="cleanAll(14)"
                  >清 空</Button
                >
              </div>
              <div
                class="messages hoverColor"
                id="margom"
                v-for="(warning, index) in noticeData.warningNoticeList"
                :key="index"
              >
                <p
                  class="messagesContent"
                  style="margin: 0 0 20px 0"
                  :class="{ isRead: warning.is_read === '2' }"
                  @click="
                    readMessage(
                      warning.id,
                      warning.type,
                      warning.code,
                      0,
                      index,
                    )
                  "
                >
                  <span style="display: block">{{ warning.create_time }}</span>

                  <!-- <span v-if="warning.is_read === '1'">[未读]</span> -->
                  <span v-if="warning.is_read !== '1'">[已读]</span>
                  <span v-html="warning.content"></span>
                </p>
                <div class="icon" v-if="notice.is_top === '1'">
                  <img
                    src="data:image/jpg;base64,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"
                    alt=""
                  />
                </div>
              </div>
              <div
                class="noMore"
                style="margin-top: 145px"
                v-if="noticeData.warningNoticeList.length === 0"
              >
                <img src="../../assets/message.png" class="NoMessages" />
                <p>暂时没有消息</p>
              </div>
              <div
                class="viewMore"
                @click="loadMore"
                v-if="!noticeNoMore && noticeData.warningNoticeList.length > 0"
              >
                加载更多...
              </div>
              <div class="noMore" v-if="noticeNoMore">没有更多了</div>
            </TabPane>
            <!-- 公告提醒 -->
            <TabPane :label="label2" name="sysNotice">
              <div class="rightModalBtns">
                <Button
                  type="primary"
                  icon="checkmark"
                  size="small"
                  @click="readAll(2)"
                  :disabled="sysReadAll"
                  >全部已读</Button
                >
                <Button
                  type="primary"
                  icon="close"
                  size="small"
                  @click="cleanAll(2)"
                  >清 空</Button
                >
              </div>
              <div
                class="messages hoverColor"
                id="margom"
                v-for="(notice, index) in noticeData.sysNoticeList"
                :key="index"
              >
                <div
                  class="messagesContent"
                  :class="{ isRead: notice.is_read === '2' }"
                  @click="
                    readMessage(
                      notice.id,
                      notice.type,
                      notice.content,
                      notice.source,
                    )
                  "
                >
                  <Row type="flex" justify="space-between">
                    <Col>
                      <span class="notices">{{ notice.type_desc }}</span>
                    </Col>
                    <Col style="text-align: right">
                      <span class="notices">{{ notice.create_time }}</span>
                    </Col>
                  </Row>
                  <p
                    class="contents"
                    style="margin: 9px 0 20px"
                    :class="{ isRead: notice.is_read === '2' }"
                  >
                    <!-- <span v-if="notice.is_read === '1'">[未读]</span> -->
                    <span v-if="notice.is_read !== '1'">[已读]</span>
                    <span>{{ notice.title }}</span>
                  </p>
                  <div class="icon" v-if="notice.is_top === '1'">
                    <img
                      src="data:image/jpg;base64,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"
                      alt=""
                    />
                  </div>
                </div>
              </div>
              <div
                class="noMore"
                style="margin-top: 145px"
                v-if="noticeData.sysNoticeList.length === 0"
              >
                <img src="../../assets/message.png" class="NoMessages" />
                <p>暂时没有消息</p>
              </div>
              <div
                class="viewMore"
                @click="loadMore"
                v-if="!noticeNoMore && noticeData.sysNoticeList.length > 0"
              >
                加载更多...
              </div>
              <div class="noMore" v-if="noticeNoMore">没有更多了</div>
            </TabPane>
          </Tabs>
        </div>
      </div>
    </transition>

    <!--问题反馈开始-->
    <form-modal
      class-name="question-report"
      post-data-key="data"
      :width="620"
      :validateRules="questionModal.validateRules"
      :formData="questionModal.formData"
      :title="questionModal.title"
      :formColumns="questionModal.formColumns"
      :save-api="questionModal.saveApi"
      @on-cancel="closeQuestionModal"
      @on-success="saveQuestionSuccess"
      v-if="questionModal.show"
      :show-modal="questionModal.show"
    >
      <div slot="pics" class="upload-list">
        <div class="img-list">
          <div
            class="img-item"
            v-for="(item, index) in uploadList"
            :key="index"
          >
            <template v-if="item.status === 'finished'">
              <img :src="item.url" />
              <div class="img-modal">
                <Icon
                  type="ios-trash-outline"
                  @click.native="handleRemove(item)"
                ></Icon>
              </div>
            </template>
            <template v-else>
              <Progress
                v-if="item.showProgress"
                :percent="item.percentage"
                hide-info
              ></Progress>
            </template>
          </div>
          <div
            class="img-item upload-item"
            v-show="uploadList.length - questionModal.maxPicCount < 0"
          >
            <Upload
              ref="questionImg"
              multiple
              type="drag"
              :show-upload-list="false"
              :on-success="handleSuccess"
              :format="['jpg', 'jpeg', 'png']"
              :on-format-error="handleFormatError"
              :on-exceeded-size="handleMaxSize"
              :before-upload="handleBeforeUpload"
              :action="uploadApi"
            >
              <Row class="upload-wrap" type="flex" align="middle">
                <Col span="24">
                  <Icon
                    type="ios-cloud-upload"
                    size="48"
                    style="color: #3399ff"
                  ></Icon>
                </Col>
              </Row>
            </Upload>
          </div>
        </div>
      </div>
    </form-modal>
    <!--问题反馈-->
    <!--任务列表-->
    <task-center></task-center>
    <div
      v-if="showAdminHelp"
      @mouseenter="openCustomerService(true)"
      class="customer-service-btn"
    >
      <BaseIcon style="font-size: 20px" icon="kefuicon" />
      客服
      <div
        v-show="showCustomerServicePop"
        @mouseleave="closeCustomerService"
        class="customer-service__mask"
      >
        <div class="customer-service__pop">
          <p class="customer-service__tel">************</p>
          <div
            @click="openRemoteAssistanceCodeMode"
            class="customer-service__btn2"
          >
            授权码
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      @mouseenter="openCustomerService(true)"
      class="customer-service-btn"
    >
      <i class="ic_shouquan" />
      授权
      <div
        v-show="showCustomerServicePop"
        @mouseleave="closeCustomerService"
        class="customer-service__mask"
      >
        <div class="customer-service__pop">
          <p class="customer-service__tel">授权入口</p>
          <div
            @click="openRemoteAssistanceCodeMode"
            class="customer-service__btn2"
          >
            去授权
          </div>
        </div>
      </div>
    </div>
    <!--消息提醒-->
    <PlaySound :sound-type="soundType" :notice-ring="noticeRing" />

    <!-- 悬浮条通知公告 -->
    <template v-if="showSuspended">
      <Alert
        type="warning"
        closable
        class="article-suspended"
        @on-close="$_onCloseSuspended(false)"
      >
        [公告]{{ articleSuspended.title }} &nbsp;&nbsp;&nbsp;&nbsp;<span
          class="article-suspended__show-detail"
          @click="$_onCloseSuspended(true)"
          >查看详情</span
        >
      </Alert>
    </template>
    <!-- 版本切换 -->
    <VersionSelect
      v-model="showVersionSelect"
      :versionList="edition_list"
      :currentVersionId="curr_edition"
    />
    <!-- 授权登录弹窗 -->
    <Modal
      v-model="remoteAssistanceCodeMode.show"
      title="授权登录"
      :footer-hide="true"
      @on-ok="remoteAssistanceCodeMode.show = false"
    >
      <div style="display: flex; align-items: center">
        <Button
          type="primary"
          :disabled="remoteAssistanceCodeMode.authCodeText !== '获取授权码'"
          @click="getAuthCode"
        >
          {{ remoteAssistanceCodeMode.authCodeText }}
        </Button>
        <span style="margin-left: 10px">{{
          remoteAssistanceCodeMode.code
        }}</span>
      </div>
      <p style="color: #b1b1b1; margin-top: 10px">
        授权码一小时内可使用，超过一小时需要重新获取
      </p>
    </Modal>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import common from '@api/main.js';
import Notice from '@api/notice.js';
import Settings from '@api/settings.js';
import '@assets/images/icon/topMenu/topMenu.scss';
import formModal from '@/components/basic/formModal.vue';
import TaskCenter from '@/components/common/task-center';
import { api } from '../../api/api';
import apiUtil from '@/api/util';
import Bus from '../../api/bus';
import { getCurrentBreadcrumb } from '@/api/breadcrumb.js';
import PlaySound from './sound/index.vue';
import CommonUtil from '@/util/common';
import ConfigMixin from '@/mixins/config';
import MenuMixin from '@components/mixin/menu';
import Dropdown from '@components/dropdown';
import kefu from '../../assets/images/index/kefu.png';
import VersionSelect from '@/components/common/version-select';
import { loadJS } from '@/util/printJs';
const siteService = CommonUtil.getService('site');
import { get } from '@/api/request';
import Cookies from 'js-cookie';
import store from '@/api/storeRoom.js';
const AUTH_CODE_TEXT = '获取授权码';
import { hasModule } from '@/util/editionUtil';
import quickLinks from './quick-links.vue';

import iconVersion101 from './images/TopMenu/<EMAIL>';
import iconVersion102 from './images/TopMenu/<EMAIL>';
import iconVersion103 from './images/TopMenu/<EMAIL>';
import iconVersion104 from './images/TopMenu/<EMAIL>';
import iconVersion105 from './images/TopMenu/<EMAIL>';
import iconVersion106 from './images/TopMenu/<EMAIL>';

// 普通通知
const NOTICE_TYPE_NORMAL = 1;
// 升级引导通知
// const NOTICE_TYPE_GUIDE = 2;
const NOTICE_TYPE_MAP = {
  1: '公告通知',
  2: '系统升级',
  3: '创建订单',
  4: '取消订单',
  5: '系统消息',
  6: '送达',
  7: '签收',
  8: '促销活动',
  9: '东坡小报',
  10: '值班公告',
  62: '库存预警',
};

const versionMap = {
  101: iconVersion101,
  102: iconVersion102,
  103: iconVersion103,
  104: iconVersion104,
  105: iconVersion105,
  106: iconVersion106,
};

export default {
  name: 'top-menu',
  mixins: [ConfigMixin, MenuMixin],
  components: {
    formModal,
    TaskCenter,
    PlaySound,
    Dropdown,
    BaseIcon: () => import('@/components/icon'),
    VersionSelect,
    GlobalSearch: () => import('./global-search.vue'),
    quickLinks,
  },
  data() {
    return {
      remoteAssistanceCodeMode: {
        show: false,
        code: '',
        btnDisable: false,
        authCodeText: AUTH_CODE_TEXT,
        loading: false,
      },
      newVersionHelp: false,
      showNewVersionDot: 0,
      versionName: '',
      genBusinessDataModal: false,
      kefu,
      guides: false,
      showCustomerServicePop: false,
      MessageTime: {
        code: '',
      },
      siteList: [],
      noticeRing: false,
      notice_type: [],
      soundType: {
        create_order: false,
        cancel_order: false,
        system_upgrade: false,
      },
      currentSite: siteService.getCurrentSite(),
      PushNotification: 2,
      showAdminHelp: false,
      breadcrumbItems: [],
      label1: (h) => {
        return h('div', [
          h('span', '消息提醒'),
          h(
            'span',
            {
              props: {
                count: this.noticeData.order_counter,
                'class-name': 'notice-badge',
              },
            },
            '(' + this.noticeData.order_counter + ')',
          ),
        ]);
      },
      label3: (h) => {
        return h('div', [
          h('span', '预警信息'),
          h(
            'span',
            {
              props: {
                count: this.noticeData.warning_counter,
                'class-name': 'notice-badge',
              },
            },
            '(' + this.noticeData.warning_counter + ')',
          ),
        ]);
      },
      label2: (h) => {
        return h('div', [
          h('span', '公告通知'),
          h(
            'span',
            {
              props: {
                count: this.noticeData.sys_counter,
                'class-name': 'notice-badge',
              },
            },
            '(' + this.noticeData.sys_counter + ')',
          ),
        ]);
      },
      uploadApi: api.uploadImg,
      uploadList: [],
      notice: {},
      showRightModal: false,
      menu: [],
      name: '',
      url: {
        info: '/superAdmin/index/',
        logout: '/login',
      },
      companyName: '',
      questionModal: {
        show: false,
        title: '问题反馈',
        validateRules: {
          detail: [
            { required: true, message: '问题详情必填！', trigger: 'blur' },
          ],
        },
        formColumns: [
          {
            name: 'tel',
            label: '联系方式',
          },
          {
            name: 'title',
            label: '标题',
          },
          {
            name: 'detail',
            label: '问题详情',
            type: 'textarea',
            rows: 4,
          },
          {
            name: 'pics',
            label: '图片上传',
            type: 'slot',
          },
        ],
        formData: {
          tel: '',
          title: '',
          detail: '',
          pics: [],
        },
        uploadList: [],
        maxPicCount: 5,
        saveApi: api.saveQuestion,
      },
      upgrades: [],
      noticeCount: 0,
      noticeVoiceSrc: '',
      noticeData: {
        warningNoticeList: [],
        orderNoticeList: [],
        sysNoticeList: [],
        order_counter: 0,
        warning_counter: 0,
        sys_counter: 0,
      },
      noticeType: 1,
      noticePage: 1,
      noticeNoMore: false,
      orderReadAll: true,
      warningReadAll: true,
      sysReadAll: true,
      timer: null,
      boxData: '',
      showVersionSelect: false,
      edition_list: [],
      curr_edition: 0,
      ccode: '',
      showSuspended: false,
      articleSuspended: {
        content: '',
        id: '',
        title: '',
      },
      versionMap,
    };
  },
  created() {
    // 存储在本地的业务配置项
    const localBusinessConfig = this.storage.getLocalStorage('business_config');
    if (localBusinessConfig) {
      this.showAdminHelp = Number(localBusinessConfig.show_admin_help) === 1;
    }
    if (sessionStorage.getItem('logopath')) {
      this.logo.path = sessionStorage.getItem('logopath');
    }
    if (sessionStorage.getItem('siteList')) {
      this.siteList = JSON.parse(sessionStorage.getItem('siteList'));
    }
    // 存储在本地的系统配置
    const localSysConfig = this.storage.getLocalStorage('sys_config');
    if (localSysConfig) {
      this.name = localSysConfig.username;
      this.companyName = localSysConfig.company_name;
      this.menu = localSysConfig.sys_items;
    }

    this.$store.commit('setShowNotice', true);
    if (this.$route.path !== '/login' && this.$route.path !== '/unique-path') {
      this.getMessagesCount();
    }
    if (process.env.NODE_ENV !== 'development') {
      this.setLoop();
    }
    // this.announcement();
    this.ccode = Cookies.get('ccode') || '';
    if (this.ccode === '/') {
      this.ccode = '';
    }
    const isFromSiteAdmin = Boolean(
      document.referrer && document.referrer.includes('/siteAdmin/'),
    );
    // 本地存储有配置时延迟执行配置请求
    if (localBusinessConfig) {
      this.businessConfigTimer = setTimeout(() => {
        this.getSysConfig();
        this.initSysConfig(true);
      });
    } else {
      this.getSysConfig();
      this.initSysConfig(isFromSiteAdmin);
    }
    this.setInitInitWareHouseId();
    Bus.$on('refresh-config', () => {
      this.getSysConfig();
    });
    if (sessionStorage.getItem('change_site_reload') === 'Y') {
      // 清空订单离线数据
      this.storage.removeLocalStorage('ORDER_NEW_DATA');
      // 清空采购单离线数据
      this.storage.removeLocalStorage('PURCHASE_NEW_DATA');
      sessionStorage.removeItem('change_site_reload');
    }
  },
  mounted() {
    this.uploadList = this.$refs.questionImg && this.$refs.questionImg.fileList;
    // 基础版不展示新功能引导
    if (hasModule('newVersionHelp')) {
      this.newVersionHelp = true;
    }
    if (this.isMultiSite) {
      window.addEventListener('visibilitychange', this.visibleChange);
    }
  },
  computed: {
    ...mapState({
      sysConfig: 'sysConfig',
      logo: (state) => state.logo,
      showNotice: (state) => state.showNotice,
    }),
    // 演示项目
    isDemoProject() {
      const localSysConfig = this.storage.getLocalStorage('business_config');
      if (localSysConfig && localSysConfig.project_info) {
        const projectInfo = JSON.parse(localSysConfig.project_info);
        return Number(projectInfo.deploy_type || 0) !== 0;
      }
      return this.getProjectType !== 0;
    },
  },
  watch: {
    $route: function () {
      this.$store.commit('setShowNotice', true);
      this.breadcrumbItems = getCurrentBreadcrumb(this.$route.path);
    },
    isMainSiteAccount() {
      this.getSiteList();
    },
  },
  methods: {
    openRemoteAssistanceCodeMode() {
      this.$request.get(this.apiUrl.remoteAssistanceInfo).then((res) => {
        if (res.status) {
          this.remoteAssistanceCodeMode.code = res.data || '';
        }
      });
      this.remoteAssistanceCodeMode.show = true;
    },
    // 获取授权码10秒倒计时
    getAuthCodeCountDown() {
      let time = 10;
      this.remoteAssistanceCodeMode.authCodeText = `正在获取中(${time}s)`;
      let timer = setInterval(() => {
        time--;
        if (time <= 0) {
          clearInterval(timer);
          this.remoteAssistanceCodeMode.authCodeText = AUTH_CODE_TEXT;
        } else {
          this.remoteAssistanceCodeMode.authCodeText = `正在获取中(${time}s)`;
        }
      }, 1000);
      return () => {
        clearInterval(timer);
        this.remoteAssistanceCodeMode.authCodeText = AUTH_CODE_TEXT;
      };
    },
    /**
     * @description: 获取授权码
     */
    async getAuthCode() {
      if (this.remoteAssistanceCodeMode.authCodeText !== AUTH_CODE_TEXT) return;
      const resetFunc = this.getAuthCodeCountDown();
      const { status, data } = await this.$request
        .get(api.getRemoteAssistanceCode)
        .catch((err) => {
          this.errorMessage('授权码获取失败');
          resetFunc();
        });
      if (!status) {
        this.errorMessage('授权码获取失败');
        resetFunc();
        return;
      }
      this.successMessage('授权码获取成功');
      this.remoteAssistanceCodeMode.code = data.remote_assistance_code;
      resetFunc();
    },
    jumpsVideoPresentation() {
      this.$router.push({ path: '/video-presentation' });
    },
    genBusinessData() {
      this.$sloading.open();
      this.genBusinessDataModal = false;
      this.$request.get(this.apiUrl.generalGenBusinessData).then((res) => {
        if (res.status) {
          this.$Notice.success({ title: '生成成功！' });
          this.$sloading.close();
        } else {
          this.$Notice.error({ title: res.message });
          this.$sloading.close();
        }
      });
    },
    jumps(itm) {
      if ([21, 41].includes(parseInt(itm.type))) {
        return;
      }

      if (itm.content !== '') {
        console.log(2);
        window.open(itm.content);
        return;
      }
      // 通知类型
      const types = [3, 20, 40];
      const { id, type, code, soucre } = itm;
      let isPopup = 0;
      if (types.includes(type)) {
        isPopup = 1;
      } else {
        isPopup = 2;
      }
      this.readMessage(id, type + '', code, soucre, 0, isPopup);
    },
    shutDown() {
      this.guides = false;
    },
    openCustomerService(setStorage) {
      if (setStorage) {
        window.localStorage.setItem(
          'newVersionUrlForCustomerService',
          this.newVersionUrl,
        );
      }

      this.showCustomerServicePop = true;
      //鼠标移动到客服上面再加载客服代码
      if (typeof window.qimoChatClick === 'undefined') {
        let path =
          'https://webchat.7moor.com/javascripts/7moorInit.js?accessId=4b047f10-4f73-11e9-9aec-31ebf44e9977&autoShow=false&language=ZHCN';
        loadJS(path);
      }
    },
    closeCustomerService() {
      this.showCustomerServicePop = false;
    },
    handleSiteListVisible(visible) {
      if (visible) {
        this.getSiteList();
      }
    },
    getSiteList() {
      if (!this.isMainSiteAccount) {
        return false;
      }
      // 确保一个生命周期只调用一次
      if (this.siteListFetched) {
        return false;
      }
      this.$request
        .get(this.apiUrl.siteAdmin.site.list, {
          pageSize: 99999,
          status: 1,
          is_search_site_power: 1,
        })
        .then((res) => {
          if (res.status === 1) {
            this.siteListFetched = true;
            const siteList = [
              {
                id: 0,
                name: '总站点',
              },
            ];
            if (res.data.is_main_site_power) {
              this.siteList = siteList.concat(res.data.list);
            } else {
              this.siteList = res.data.list;
            }
            sessionStorage.setItem('siteList', JSON.stringify(this.siteList));
          }
        });
    },
    setLoop() {
      if (!this.timer) {
        this.timer = setInterval(() => {
          // 登录页不调用这个接口
          if (
            this.$route.path === '/login' ||
            this.$route.path === '/unique-path'
          ) {
            return;
          }
          !document.hidden && this.getMessagesCount();
        }, 60000);
      }
    },
    handleMaxSize() {},
    toggleNoticeRing() {
      let params = {
        type: this.noticeRing ? 0 : 1,
      };
      this.$request
        .post(this.apiUrl.common.toggleNoticeRing, params)
        .then((res) => {
          let { status, message } = res;
          if (status) {
            this.initSysConfig(true);
          } else {
            this.modalError(message ? message : '操作失败');
          }
        });
    },
    switchSite(site) {
      siteService.switchSite(site.id).then(async () => {
        // 清空记录的仓库
        sessionStorage.removeItem('storeId');
        this.storage.removeLocalStorage('init_ware_house_id');

        siteService.setCurrentSite(site.id);
        await this.initSysConfig(true, site.id);
        await this.setInitInitWareHouseId();
        if (siteService.isMainSite(site.id)) {
          siteService.goToMainSite();
        } else {
          sessionStorage.setItem('change_site_reload', 'Y');
          location.reload();
        }
        await this.$router.replace('/index');
      });
    },
    getDataConfig() {
      this.$request.get(this.apiUrl.getABConfig).then((res) => {
        let { status, data, message } = res;
        if (status) {
          this.$store.commit('setDataConfig', data || {});
        }
      });
    },
    getSysConfig() {
      this.commonService.getConfig().then((config) => {
        let {
          show_admin_help,
          project_info,
          help_version_url,
          consortium_platform,
        } = config;
        this.$store.commit('setSysConfig', config);
        if (consortium_platform == 1) {
          this.getDataConfig();
        }
        project_info = project_info ? JSON.parse(project_info) : {};
        Number(project_info.deploy_type || 0);
        this.showAdminHelp = Number(show_admin_help) === 1;
        this.newVersionUrl = help_version_url;
        if (
          window.localStorage.getItem('newVersionUrl') !== this.newVersionUrl
        ) {
          this.showNewVersionDot = 1;
        }
        if (Number(config.is_open_store_area_location)) {
          apiUtil.setIsOpenStoreMGT(1);
        } else {
          apiUtil.setIsOpenStoreMGT(0);
        }
        if (config.multi_warehouse_max > 1) {
          apiUtil.setIsMultiStore(1);
        } else {
          apiUtil.setIsMultiStore(0);
        }
      });
    },
    closeQuestionModal() {
      this.questionModal.show = false;
    },
    showQuestionModal() {
      this.questionModal.formData.title = '';
      this.questionModal.formData.tel = '';
      this.questionModal.formData.detail = '';
      this.questionModal.formData.pics = [];
      this.questionModal.show = true;
      this.$refs.questionImg.clearFiles();
      this.uploadList = this.$refs.questionImg.fileList;
    },
    saveQuestionSuccess() {
      this.closeQuestionModal();
    },
    handleRemove(file) {
      const fileList = this.$refs.questionImg.fileList;
      this.$refs.questionImg.fileList.splice(fileList.indexOf(file), 1);
      this.questionModal.formData.pics.splice(
        this.questionModal.formData.pics.indexOf(file.url),
        1,
      );
    },
    handleSuccess(res, file) {
      file.url = res.data.upyun_url;
      file.name = res.data.filename;
      this.questionModal.formData.pics.push(res.data.upyun_url);
    },
    handleFormatError(file) {
      this.modalError({
        content: '文件' + file.name + ' 格式不正确。',
      });
    },
    handleBeforeUpload() {
      const check = this.uploadList.length < this.questionModal.maxPicCount;
      if (!check) {
        this.modalError({
          content: `最多只能上传${this.questionModal.maxPicCount}张图片？`,
        });
      }
      return check;
    },
    switchTab(name) {
      this.noticePage = 1;
      switch (name) {
        case 'orderNotice':
          this.noticeType = 1;
          break;
        case 'warningNotice':
          this.noticeType = 14;
          break;
        default:
          this.noticeType = 2;
          break;
      }
      this.loadMessages();
    },
    loadMore() {
      this.noticePage += 1;
      this.loadMessages();
    },
    showSysNotice() {
      const UPGRADE_NOTICE_NAME = 'upgrade_notice';
      this.$Notice.warning({
        name: UPGRADE_NOTICE_NAME,
        duration: 0,
        title: '升级通知',
        render: (h) => {
          return h(
            'div',
            {
              style: {
                lineHeight: '18px',
              },
            },
            [
              h(
                'span',
                '尊敬的客户，您的系统即将升级，想了解更多内容，请点击上方',
              ),
              h(
                'span',
                {
                  class: ['text-green', 'pointer'],
                  on: {
                    click: () => {
                      this.showMessages();
                      this.$Notice.close(UPGRADE_NOTICE_NAME);
                    },
                  },
                },
                '“小铃铛”',
              ),
              h('span', '查看升级详情。'),
            ],
          );
        },
      });
    },
    getMessagesCount() {
      Notice.getNoticeCounter().then((res) => {
        let { data } = res;
        if (res.status) {
          if (res.data.notice.csm.length !== 0) {
            this.upgrades = res.data.notice.csm;
            const modalItem = this.upgrades[0] || {};
            this.$Modal.info({
              title: modalItem.title,
              okText: [21, 41, 74].includes(parseInt(modalItem.type))
                ? '知道了'
                : '查看详情',
              onOk: () => {
                this.jumps(modalItem);
              },
              closable: true,
              onCancel: () => {
                this.guides = true;
              },
              render: () => {
                return (
                  <div>
                    {modalItem.banner ? (
                      <div
                        class="_imgs"
                        onClick={() => this.jumps(modalItem)}
                        style="cursor:pointer;"
                      >
                        <img src={modalItem.banner} style="width: 100%" />
                      </div>
                    ) : null}
                    <div class="pointer" onClick={() => this.jumps(modalItem)}>
                      {modalItem.subtitle}
                    </div>
                  </div>
                );
              },
            });
          }

          // 悬浮条(这里后端为空的时候返回的是空数组，不为空返回的又是对象)
          if (data.notice.article_suspended.length !== 0) {
            this.articleSuspended = data.notice.article_suspended;
            this.showSuspended = true;
          }

          res.data.notice.csm.forEach((ite) => {
            ite.type_desc = NOTICE_TYPE_MAP[Number(ite.type)];
          });
          // this.announcement();
          let notice = res.data.notice;
          this.noticeCount = res.data.counter;
          this.soundType = {
            create_order: Number(notice.new_order) === 1,
            cancel_order: Number(notice.cancel_order) === 1,
            system_upgrade: Number(notice.sys) === 1,
          };
          if (this.soundType.system_upgrade) {
            this.showSysNotice();
          }
          // 导出任务完成
          if (data.offline_count * 1) {
            if (this.$store.state.showTaskCenter) {
              Bus.$emit('refresh-task-center', data.offline_count);
              return false;
            }
            let noticeName = 'exportNotice';
            this.$Notice.close(noticeName);
            this.$Notice.success({
              title: '成功',
              name: noticeName,
              duration: 0,
              onClose: () => {
                Bus.$emit('refresh-task-center');
              },
              render: (h) => {
                return h('div', [
                  h('span', '有'),
                  h(
                    'span',
                    {
                      style: 'color: red',
                    },
                    data.offline_count,
                  ),
                  h('span', '个导出任务完成'),
                  h(
                    'span',
                    {
                      style: 'color: #03ac54; cursor: pointer',
                      on: {
                        click: () => {
                          this.$Notice.close(noticeName);
                          this.showTaskCenter();
                        },
                      },
                    },
                    '点击',
                  ),
                  h('span', '查看'),
                ]);
              },
            });
          }
        }
      });
    },
    showMessages(loadMore) {
      this.showRightModal = true;
      if (this.noticePage > 1) {
        this.noticePage = 1;
      }
      this.loadMessages(loadMore);
    },
    loadMessages(loadMore) {
      let params = {
        type: this.noticeType,
        page: this.noticePage,
        pageSize: 10,
      };
      this.noticeNoMore = false;
      // const curDate = new Date();
      // let today = curDate.getTime();
      Notice.getNoticeBox(params).then((res) => {
        res.data.items.forEach((itm) => {
          itm.type_desc = NOTICE_TYPE_MAP[Number(itm.type)];
        });
        if (res.status) {
          this.boxData = res.data;
          if (this.noticePage > 1 && res.data.items.length === 0) {
            this.noticeNoMore = true;
          }
          if (this.noticePage === 1) {
            switch (this.noticeType) {
              case 1:
                this.noticeData.orderNoticeList = res.data.items;
                this.checkRead('order');
                break;
              case 14:
                this.noticeData.warningNoticeList = res.data.items;
                this.checkRead('warning');
                break;

              default:
                this.noticeData.sysNoticeList = res.data.items;
                this.checkRead('system');
                break;
            }
          } else {
            res.data.items.forEach((d) => {
              switch (this.noticeType) {
                case 1:
                  this.noticeData.orderNoticeList.push(d);
                  this.checkRead('order');
                  break;
                case 14:
                  this.noticeData.warningNoticeList.push(d);
                  this.checkRead('warning');
                  break;
                default:
                  this.noticeData.sysNoticeList.push(d);
                  this.checkRead('system');
                  break;
              }
            });
          }
          this.noticeData.order_counter = res.data.order_counter;
          this.noticeData.warning_counter = res.data.warn_counter;
          this.noticeData.sys_counter = res.data.sys_counter;
        }
        if (loadMore && this.noticeData.orderNoticeList.length) this.loadMore();
      });
    },
    checkRead(type) {
      switch (type) {
        case 'order':
          this.noticeData.orderNoticeList.forEach((item) => {
            if (item.is_read === '1') {
              this.orderReadAll = false;
            }
          });
          break;
        case 'warning':
          this.noticeData.warningNoticeList.forEach((item) => {
            if (item.is_read === '1') {
              this.warningReadAll = false;
            }
          });
          break;
        default:
          this.noticeData.sysNoticeList.forEach((item) => {
            if (item.is_read === '1') {
              this.sysReadAll = false;
            }
          });
          break;
      }
    },
    // isPopup是否是通过弹窗调用的
    readMessage(id, type, code, source, dataIndex, isPopup) {
      const types = [
        '20',
        '40',
        '30',
        '17',
        '15',
        '16',
        '60',
        '50',
        '31',
        '61',
        '80',
        '62',
        '73',
        '81',
        '82',
        '21',
        '41',
      ];
      const urls = [
        '/returnDetail',
        '/purchase/purchaseOrderDetail',
        '/agreementPrice/list',
        '/purchase/purchaseOrderDetail',
        '/storeRoom/existingStockList',
        '/storeRoom/existingStockList',
        '/goods/list', // 售卖库存变更为零
        '/user/list', // 新用户注册
        '/purchase/provider', //采购协议价即将过期预警
        '/appCenter/auto-purchase-order/list',
        '/basket/circulation-manage',
        '/storeRoom/existingStockList',
        '/finance/invoice/notMakeOutInvoice',
        '/storeRoom/existingStockList?tabName=batch_existing',
        '/purchase/provider', // 供应商资质到期
      ];
      Notice.updateNotice({ id }).then((res) => {
        if (res.status) {
          this.noticeCount = this.noticeCount > 0 ? this.noticeCount - 1 : 0;
          this.showRightModal = false;
          // 订单tab页通知
          // 隐藏弹窗
          this.$Modal.remove();
          const index = types.indexOf(type);
          // __MARK('跳转函数');
          // 如果有具体type值
          if (index !== -1) {
            if (type === '17') {
              code = this.noticeData.warningNoticeList[dataIndex].purchase_id;
            }
            // 通用筐和押金筐
            if (+type === 80) {
              const path_80 = '/basket/circulation-manage';
              const [tab, order_id] = code.split(',');
              this.router[this.$route.path === path_80 ? 'replace' : 'push']({
                path: path_80,
                query: {
                  tab,
                  order_id,
                  keep_scroll: 1,
                },
              });
              return;
            }
            this.router.push({
              path: urls[index],
              query: {
                id: code || '',
                keep_scroll: '1',
              },
            });
            return;
          }

          if (type === '32') {
            this.router.push('/user/list');
            return;
          }

          if (this.noticeType === 1 || isPopup === 1) {
            if (+type === 74) return; // 这种类型定位不到订单，后端建议不跳转
            this.router.push({
              path: '/orderDetail',
              query: { id: code },
            });

            return;
          }
          // 预警信息
          if (this.noticeType === 14 || isPopup === 2) {
            if (+type === 70) return; // 异地登录
            //保质期预警(过期、即将过期)
            this.router[this.$route.path === '/user/list' ? 'replace' : 'push'](
              {
                path: '/user/list',
                query: {
                  day_remind_status: +type === 13 ? '3' : undefined, // 跳转至客户档案页面时默认筛选条件
                  price_remind_status: +type === 12 ? '3' : undefined,
                },
              },
            );
            return false;
          }
          // 公告
          if (source == '2') {
            let tempwindow = window.open();
            tempwindow.location = code;
            this.loadMessages();
          } else if (type == Notice.type.upgrade) {
            this.showRightModal = false;
            this.$router.push({
              name: 'upgrade-guide',
              params: {
                index: 0,
              },
              query: {
                code: id,
              },
            });
          } else {
            this.$router.push({
              name: 'notice-detail',
              params: {
                index: 0,
              },
              query: {
                code: id,
              },
            });
          }
        } else {
          this.modalError(res.message);
          this.showRightModal = false;
        }
      });
    },
    readAll(type) {
      Notice.updateNotice({ type }).then((res) => {
        if (res.status) {
          this.getMessagesCount();
          let listName = 'sysNoticeList';
          switch (type) {
            case 1:
              listName = 'orderNoticeList';
              this.orderReadAll = true;
              break;
            case 14:
              listName = 'warningNoticeList';
              this.warningReadAll = true;
              break;
            default:
              this.sysReadAll = true;
              break;
          }
          this.noticeData[listName].forEach((item) => {
            item.is_read = '2';
          });
        }
      });
    },
    cleanAll(type) {
      Notice.cleanNotice({ type }).then((res) => {
        if (res.status) {
          this.$Message.success('清除成功');
          switch (type) {
            case 1:
              this.noticeData.orderNoticeList = [];
              break;
            case 14:
              this.noticeData.warningNoticeList = [];
              this.noticeData.warning_counter = 0;
              break;
            default:
              this.noticeData.sysNoticeList = [];
              break;
          }
        } else {
          this.modalError(res.message);
        }
      });
    },
    getConfigFromLocal() {
      const config = this.storage.getLocalStorage('sys_config');
      if (config) {
        this.companyName = config.company_name;
      }
    },
    async initSysConfig(refreshCache = false, siteId) {
      let config = this.storage.getLocalStorage('sys_config');
      if (!config || refreshCache) {
        let res = await common.getTopMenu();
        config = res.data;
        this.storage.setLocalStorage('sys_config', config);
      }
      if (config) {
        this.storage.setLocalStorage('user_info', {
          userid: config.userid,
          username: config.username,
        });
        this.companyName = config.company_name;
        if (!document.title) {
          document.title = config.company_name || '';
        }
        this.menu = config.sys_items;
        this.name = config.username;
        sessionStorage['userId'] = config.userid;
        sessionStorage['userName'] = config.username;
        localStorage['companyName'] = config.company_name;
        if (config.logo_path) {
          sessionStorage.setItem('logopath', config.logo_path);
        }
        this.$store.state.logo = {
          url: config.index_url,
          path: config.logo_path,
        };

        let accountInfo = {
          username: config.username,
          userid: config.userid,
        };
        this.$store.commit(
          this.globalConfig.store.mutationKey.updateAccountInfo,
          accountInfo,
        );
        this.noticeRing = Number(config.ring) === 1;
        if (siteId !== undefined) {
          config.site_id = siteId;
        }
        this.$store.commit('setCommonConfig', config);
      }
    },
    async getNotice() {
      let res = await Notice.getNotice();
      if (res.status) {
        let data = res.data;
        data.count || (data.count = 1);
        this.notice = res.data;
      }
    },
    toNotice() {
      // 普通通知
      if (this.notice.type == NOTICE_TYPE_NORMAL) {
        this.$router.push({
          name: 'notice-detail',
          query: {
            code: this.notice.code,
          },
        });
      }
      // 升级引导
      else {
        this.$router.push({
          name: 'upgrade-guide',
          params: {
            index: 0,
          },
          query: {
            code: this.notice.id,
          },
        });
      }
    },
    async logout() {
      let res = await common.logOut();
      if (res.status) {
        // 打印配置不能清除
        // 这里先取到所有已经设置的打印配置
        // 然后删除所有 localStorage 再重新赋值
        get(api.getPrintTemplateType).then((res) => {
          sessionStorage.clear();
          this.storage.removeLocalStorage('screen_select_storage_id');
          // 处理退出时不清除储存listTable表头的拖动位置
          for (var i = 0; i < localStorage.length; i++) {
            var key = localStorage.key(i); //获取本地存储的Key
            let isNoRemove =
              key.includes('&columnWidth') ||
              key.includes('title-') ||
              key.includes('delivery_map_show_username') ||
              key.includes('delivery_map_show_username_in_map') ||
              key.includes('order_export_column') ||
              key.includes('purchase_plan_export_summary_columns') ||
              key.includes('purchase_plan_export_detail_columns') ||
              key.includes('is_show_commodity_ceiling_price') ||
              key.includes('pageSize_') ||
              key.includes('export_commodity_') ||
              key.includes('/goods/list_showIntroGuide');;
            if (!isNoRemove) {
              localStorage.removeItem(key);
            }
          }
          siteService.resetCurrentSite();
          window.location.href = '/';

          if (!res.status) return;

          let printSettingArr = [];
          res.data.list.map((item) => {
            let key = this.ccode + item.PRINTER_KEY;
            let value = localStorage.getItem(key);
            value && printSettingArr.push({ key, value });
          });
          printSettingArr.map((item) => {
            localStorage.setItem(item.key, item.value);
          });
        });
      }
    },
    toUrl(info) {
      if (info.index_key) {
        const path = '/' + info.index_key.split('#/')[1];
        this.$router.push({ path });
      }
    },
    openUrl() {
      //window.open(url);
      this.$store.commit('SET_HELP_SHOW', true);
    },
    showNewVersionHelp() {
      window.open('https://www.yuque.com/sdongpo/news/1630');
      window.localStorage.setItem('newVersionUrl', this.newVersionUrl);
      // window.open(this.newVersionUrl);
      this.showNewVersionDot = 0;
    },
    getVersionList(val) {
      Settings.getVersionList().then((res) => {
        let {
          status,
          data: { curr_edition, edition_list },
        } = res;
        if (status) {
          this.curr_edition = Number(curr_edition);
          this.edition_list = edition_list;
          edition_list.forEach((item) => {
            if (item.id === this.curr_edition) {
              this.versionName = item.name;
            }
          });
        }
        if (val !== 1) {
          this.showVersionSelect = true;
        }
      });
    },
    chatClick() {
      window.qimoChatClick();
    },
    $_onCloseSuspended(_showDetail = false) {
      this.showSuspended = false;
      this._reqUpdateSuspended(_showDetail);
    },
    /**
     * @description: 更新悬浮条的信息(无法复用原来消息已读接口，后端不返回字段)
     * @param {String} _id
     * @author: lizi
     */
    _reqUpdateSuspended(_showDetail) {
      const { id, content } = this.articleSuspended;
      Notice.updateNotice({ id }).then((res) => {
        if (res.status) {
          _showDetail && window.open(content);
        } else {
          this.errorNotice(res.message);
        }
      });
    },
    getStoreListData() {
      return store.getUserWarehouse({}, { cache: true }).then((res) => {
        const { status, data } = res;
        if (status && data && data.length > 0) {
          return data[0].id;
        } else {
          return false;
        }
      });
    },
    async setInitInitWareHouseId() {
      const id = await this.getStoreListData();
      if (id) {
        this.storage.setLocalStorage('init_ware_house_id', id);
      }
    },
    visibleChange() {
      if (document.visibilityState == 'visible') {
        const localSysConfig = this.storage.getLocalStorage('sys_config');
        if (
          localSysConfig &&
          localSysConfig.company_name !== this.companyName
        ) {
          window.location.reload();
        }
      }
    },
  },
  beforeDestroy() {
    window.removeEventListener('visibilitychange', this.visibleChange);
  },
};
</script>

<style lang="scss" scoped>
.layout-nav {
  position: relative;
  padding-left: 0;
  height: 48px;
  &.layout-green-bg {
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 220px;
      height: 48px;
      background: linear-gradient(270deg, #ffffff 0%, rgba(192, 234, 212, 0.8) 100%);
    }
  }
  .ivu-select-dropdown {
    margin-top: 0;
  }
  .sui-icon {
    padding: 2px;
    border-radius: 6px;
    transition: 0.3s all;
    font-size: 18px;
    &:hover {
      background: rgba(246, 248, 249, 0.9);
    }
  }
  &__logo {
    width: 108px;
    padding: 8px 0;
    display: block;
    max-height: 48px;
    box-sizing: border-box;
    text-align: center;
    margin-right: 0;
    img {
      width: auto;
      height: 100%;
      max-height: 32px;
      max-width: 80px;
      vertical-align: top;
    }
  }
  &__title {
    color: #505050;
    max-height: 48px;
  }

  &__item {
    margin-left: 16px;
    color: #505050;
    cursor: pointer;
    transition: 0.3s color;

    .ivu-badge {
      line-height: 1;
    }
    &:hover {
      color: #303030;
    }

    span {
      vertical-align: middle;
    }
  }
  &__avatar {
    width: 24px;
    height: 24px;
    margin-right: 6px;
    border-radius: 999px;
    vertical-align: middle;
  }
}
.alerts {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.ClickToView {
  cursor: pointer;
  color: rgb(90, 171, 247);
  font-size: 15px;
}
.ClickToViewtitle {
  font-size: 15px;
}

.isRead {
  color: #999999;
  a {
    color: #999999;
  }
}
.offline-goods－stamp {
  position: relative;
  overflow: hidden;
  &:after {
    content: '已下架';
    position: absolute;
    width: 110px;
    height: 35px;
    line-height: 50px;
    text-align: center;
    background: red;
    color: #fff;
    top: -12px;
    right: -38px;
    transform: rotate(25deg);
    font-size: 12px;
  }
}
.question-report {
  .upload-list {
    $imgWith: 160px;
    .img-list {
      .upload-item {
        display: inline-block;
        width: $imgWith;
        height: $imgWith / 2;
      }
      .img-item:not(.upload-item) {
        display: inline-block;
        width: $imgWith;
        height: $imgWith / 2;
        text-align: center;
        line-height: 60px;
        border: 1px solid transparent;
        border-radius: 4px;
        overflow: hidden;
        background: #fff;
        position: relative;
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
        margin-right: 4px;
        img {
          width: 100%;
          height: 100%;
        }
        .img-modal {
          height: $imgWith / 2;
          line-height: $imgWith / 2;
          display: none;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.6);
          i {
            color: #fff;
            font-size: 20px;
            cursor: pointer;
            margin: 0 2px;
          }
        }
        &:hover {
          .img-modal {
            display: block;
          }
        }
      }
    }
    .ivu-upload {
      width: $imgWith;
      .upload-wrap {
        width: $imgWith;
        height: $imgWith / 2;
        text-align: center;
      }
    }
  }
}
</style>
<style lang="less" scoped>
@import '../../assets/less/variable';
.top-menu {
  position: fixed;
  z-index: 302;
  top: 0;
  left: @menu-width;
  right: 0;
  min-width: calc(~'1200px - @{menu-width}');
  height: @nav-height;
  line-height: @nav-height;
  background-color: #fff;
  /*color: #fff;*/
  font-size: 13px;
  padding: 0 @padding-base 0 0;
  &::after {
    content: '';
    position: absolute;
    height: 1px;
    background: #e8e8e8;
    transform: scaleY(0.5);
    width: 100%;
    z-index: 16;
    left: 0;
    bottom: 0;
  }
  .nav-item {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0 15px;
    .sui-icon {
      margin-right: 5px;
    }
    &.split-line:after {
      content: '';
      display: block;
      width: 1.5px;
      height: 14px;
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      background-color: #efefef;
    }
  }
  .setting-wrap {
    position: relative;
    height: 48px;
    .setting {
      display: none;
    }
    &:hover {
      .setting {
        display: block;
      }
    }
    .sui-icon {
      position: relative;
      top: 2px;
    }
    /deep/.ivu-dropdown-rel {
      height: 48px !important;
    }
  }

  .company-name {
    // margin-left: 18px;
    font-weight: normal;
    vertical-align: middle;
  }

  .notice-wrap {
    position: relative;
    cursor: pointer;
    .notice-icon {
      font-size: 20px;
      margin-right: 5px;
      color: #9c9d9d;
    }
    .ivu-badge {
      font-size: 14px;
      vertical-align: inherit; // 覆盖iview样式
    }
  }

  .setting-wrap {
    text-align: left;
    position: relative;
    cursor: pointer;
  }

  .help {
    position: relative;
    cursor: pointer;
    .help-list {
      display: none;
      position: absolute;
      right: 0;
      top: @nav-height;
      width: 100px;
      text-align: left;
      background: #fff;
      color: #7d8282;
      border: 1px solid #dedede;
      padding: 12px 8px 0;
      border-bottom: none;
      font-size: 12px;
      .help-item {
        padding-left: 23px;
        cursor: pointer;
        a {
          color: #7d8282;
          &:hover {
            color: #03ac54 !important;
          }
        }
        height: 40px;
        line-height: 40px;
        .ivu-icon {
          font-size: 16px;
          margin-right: 5px;
        }
        &:hover {
          color: #03ac54 !important;
        }
        .sui-icon {
          position: absolute;
          left: 10px;
        }
      }
      .service-info {
        line-height: 24px;
        background: #03ac54;
        color: #fff;
        margin: 0 -9px;
        text-align: center;
        padding: 5px 0;
        margin-top: 5px;
      }
    }
    &:hover {
      .help-list {
        display: block;
      }
    }
  }

  .setting {
    position: absolute;
    z-index: 13;
    top: @nav-height;
    right: 20px;
    width: 86px;
    font-size: 12px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
    color: #7d8282;
    ul {
      li {
        float: none;
        text-align: left;
        height: 40px;
        line-height: 40px;
        padding-left: 8px;
        &:hover {
          background: #e0e0e0;
          color: #03ac54;
          cursor: pointer;
        }
      }
    }

    .logout {
      height: 40px;
      line-height: 40px;
      text-indent: 8px;
      text-align: left;
      &:hover {
        background: #e0e0e0;
        color: #03ac54;
        cursor: pointer;
      }
    }
  }
}

// .indexMenu {
//   padding-left: @menu-width;
// }

.customer-service-btn {
  cursor: pointer;
  position: fixed;
  right: 0;
  bottom: 75px;
  color: #fff;
  line-height: 18px;
  font-size: 12px;
  width: 36px;
  height: 48px;
  border-radius: 2px 0px 0px 2px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(31, 36, 46, 0.8);
  transition: 0.3s background-color;
  &:hover {
    background-color: @theme-color;
  }
}
.customer-service {
  &__mask {
    position: absolute;
    right: 0;
    bottom: 0;
    padding-right: 190px;
    height: 100px;
  }
  &__pop {
    width: 140px;
    height: 80px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 8px 1px rgba(0, 0, 0, 0.08);
    border-radius: 4px;
    position: absolute;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: auto;
  }
  &__tel {
    font-size: 16px;
    // color: @theme-color;
    font-family: Avenir-Medium, Avenir;
    font-weight: 500;
    color: rgba(48, 48, 48, 1);
    line-height: 16px;
  }
  &__btn {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    background-color: @theme-color;
    width: 76px;
    height: 24px;
    line-height: 12px;
    border-radius: 12px;
    margin-top: 12px;
    cursor: pointer;
  }
  &__btn2 {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    border: 1px solid @theme-color;
    width: 76px;
    height: 24px;
    line-height: 12px;
    border-radius: 12px;
    margin-top: 12px;
    cursor: pointer;
    color: @theme-color;
  }
}

.rightModalBtns {
  text-align: center;
  margin-top: 5px;
  margin-bottom: 5px;
}
.modal-body {
  overflow: hidden;
}
// 消息提醒条数
.ivu-badge-count {
  right: -10px;
  top: 6px;
}

.MessageFont {
  align-content: center;
}

.titleMessage {
  margin: 20px 0;
  text-align: center;
  overflow: hidden;
  -webkit-line-clamp: 2;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.contents {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-height: 20px;
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}
.messages {
  position: relative;
  .icon {
    position: absolute;
    right: 0px;
    top: 0px;
  }
}
.hoverColor:hover {
  background-color: rgba(248, 248, 249, 1);
  .contents {
    color: rgb(92, 90, 90) !important;
  }
  .notices {
    color: rgb(92, 90, 90) !important;
  }
}
.NoMessages {
  width: 130px;
  height: 128px;
}

._imgs {
  padding: 0 24px;
  width: 100%;
  line-height: 0;
  margin-top: 16px;
  border: none;
  height: auto;
}

._fontClass {
  color: rgba(0, 0, 0, 0.7);
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.s-custom-modal {
  &__masks {
    position: fixed;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: 0.3s opacity;
    &.show {
      opacity: 1;
      visibility: visible;
    }
  }
  &__wrappers {
    position: fixed;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: 0.3s all;
    background: #fff;
    z-index: 1000;
    opacity: 0;
    display: flex;
    flex-direction: column;
    &.show {
      opacity: 1;
    }
  }
  &__ft {
    position: relative;
    margin-top: 20px;
  }
  &__ft:before {
    content: '';
    position: absolute;
    padding: 0;
    width: 100%;
    height: 1px;
    background: #e8e8e8;
    transform: scaleY(0.5);
  }
}

._buttonBox {
  display: flex;
  margin: 22px auto;
  border-radius: 2px;
  justify-content: center;
  width: 112px;
  align-items: center;
  cursor: pointer;
  height: 42px;
  background: #03ac54;
}
._buttonContent {
  color: var(--primary-color);
  text-align: center;
  cursor: pointer;
}
#margom {
  margin: 0 -34px;
  padding: 0 34px;
}
#margom {
  margin: 0 -34px;
  padding: 0 34px;
}
._poptip {
  position: absolute;
  padding: 20px 18px 16px;
  top: 26px;
  right: -22px;
  width: 232px;
  height: 117px;
  z-index: 9999999;
  border-radius: 2px;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
}

._poptipTag {
  width: 15px;
  height: 15px;
  background: rgba(255, 255, 255, 1);
  position: absolute;
  top: -5px;
  z-index: 9999999;
  right: 24px;
  transform: rotateZ(45deg);
}
._poptipContent {
  width: 196px;
  height: 40px;
  font-size: 13px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  line-height: 20px;
}

._poptipButton {
  width: 61px;
  height: 26px;
  margin-top: 12px;
  cursor: pointer;
  background: rgba(3, 172, 84, 1);
  float: right;
  border-radius: 2px;
  text-align: center;
  line-height: 26px;
}

._poptipButtonContent {
  font-size: 13px;
  font-family:
    PingFangSC-Regular,
    PingFang SC;
  font-weight: 400;
  color: rgba(255, 255, 255, 1);
}
.article-suspended {
  width: 40%;
  margin: auto;
  // top: -44px;
  top: 10px;
  color: #fc4d1d;
  z-index: 999999;
  text-align: center;
  &__show-detail {
    text-decoration: underline;
    cursor: pointer;
  }
}
.modal-mask {
  left: 176px;
}
.icon-version {
  height: 20px;
  margin-left: 12px;
  position: relative;
  top: 5px;
}
.ic_shouquan {
  display: inline-block;
  width: 20px;
  height: 20px;
  background: url('./images/TopMenu/ic_shouquan.png');
  background-size: 100% 100%;
}
</style>
