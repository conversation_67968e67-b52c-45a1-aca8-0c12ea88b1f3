<template>
  <div>
    <Modal
      v-model="settingModal"
      v-bind="$attrs"
      title="参数配置"
      @on-ok="saveConfig"
      @on-cancel="settingModal = false"
    >
      <div style="margin-top:10px;">
        <span style="margin-right: 20px;">采购下限取值</span>
        <RadioGroup v-model="configSetting.min_stock_limit_flag">
          <Radio label="1">
            <span>库存下限</span>
          </Radio>
          <Radio label="0">
            <span>下限备货天数</span>
          </Radio>
        </RadioGroup>
        <span style="margin-right: 20px;">
          <Input
            v-model="configSetting.min_pur_limit_days"
            style="width: 50px"
          />
          * 日均销量
        </span>
      </div>
      <div style="margin-top:10px;">
        <span style="margin-right: 20px;">采购上限取值</span>
        <RadioGroup v-model="configSetting.max_stock_limit_flag">
          <Radio label="1">
            <span>库存上限</span>
          </Radio>
          <Radio label="0">
            <span>上限备货天数</span>
          </Radio>
        </RadioGroup>
        <span style="margin-right: 20px;">
          <Input
            v-model="configSetting.max_pur_limit_days"
            style="width: 50px"
          />
          * 日均销量
        </span>
      </div>
      <div style="margin-top:10px;">
        <span style="margin-right: 20px">日均销量取值</span>
        <span
          >前
          <Input v-model="configSetting.last_few_days" style="width: 30px" />
          天日均销量</span
        >
      </div>
      <div v-if="!noRememberPreChoice" style="margin-top:10px;">
        <span style="margin-right: 20px">预采分配取值</span>
        <RadioGroup v-model="configSetting.remember_pre_choice">
          <Radio label="0">
            <span>默认采购员/供应商</span>
          </Radio>
          <Radio label="1">
            <span>记住上一次分配</span>
          </Radio>
        </RadioGroup>
      </div>
    </Modal>
  </div>
</template>
<script>
import settings from '@api/settings.js';
export default {
  props: {
    noRememberPreChoice: {
      type: Boolean,
      defualt: false
    }
  },
  data() {
    return {
      settingModal: false,
      configSetting: {
        min_stock_limit_flag: '0',
        min_pur_limit_days: 1,
        max_stock_limit_flag: '0',
        max_pur_limit_days: 3,
        last_few_days: 1,
        remember_pre_choice: '0'
      }
    };
  },
  created() {
    this.getConfig();
  },
  methods: {
    showModal() {
      this.settingModal = true;
    },
    saveConfig() {
      let data = [
        {
          key: 'pre_purchase_config',
          value: JSON.stringify(this.configSetting)
        }
      ];
      settings.saveSystemConfig(data).then(res => {
        let { message, status } = res;
        if (status) {
          this.$Message.success('保存成功');
          this.$emit('save-success');
          this.$emit('on-config-change', this.configSetting);
        } else {
          this.modalError(message, 0);
        }
      });
    },
    getConfig() {
      settings.getSystemConfig().then(res => {
        let { status, data } = res;
        if (status) {
          if (data && data.length) {
            data.map(item => {
              if (item.key === 'pre_purchase_config') {
                let value = JSON.parse(item.value);
                this.configSetting.max_stock_limit_flag = value.max_stock_limit_flag.toString();
                this.configSetting.min_stock_limit_flag = value.min_stock_limit_flag.toString();
                this.configSetting.max_pur_limit_days =
                  value.max_pur_limit_days;
                this.configSetting.min_pur_limit_days =
                  value.min_pur_limit_days;
                this.configSetting.last_few_days = value.last_few_days;
                this.configSetting.remember_pre_choice = value.remember_pre_choice.toString();
                this.$emit('on-config-change', this.configSetting);
              }
            });
          }
        }
      });
    }
  }
};
</script>
