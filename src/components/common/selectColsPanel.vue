<template>
  <div style="display: inline-block">
    <Button type="default" v-if="defaultTrigger" @click="showModal">表头设置</Button>
    <div class="modal-mask" @click="closeModal" v-if="show"></div>
    <transition name="panelFade">
      <div class="selectColsPanel" v-if="show">
        <div class="panelHeader">
          <div class="title">选择表头显示列</div>
          <div class="close" @click="closeModal">
            <Icon type="android-close"></Icon>
          </div>
        </div>
        <div class="panelBody" style="padding-bottom: 80px">
          <CheckboxGroup v-model="selectedCols">
            <Checkbox
              :label="item.key"
              disabled
              :key="item.key"
              v-for="item in checkBoxItems"
              :disabled="item.isDefault"
              >{{ item.title }}</Checkbox
            >
          </CheckboxGroup>
        </div>
        <div class="panelFooter">
          <Button @click="recovery">恢 复</Button>
          <Button @click="closeModal">取 消</Button>
          <Button type="primary" @click="updateCols">确 定</Button>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import basic from "@api/basic.js";
import ConfigMixin from '@/mixins/config';
export default {
  name: "SelectColsPanel",
  mixins: [ConfigMixin],
  autoRegister: true,
  props: {
    defaultTrigger: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: ""
    },
    showStatus: {
      type: [Boolean, String],
      default: false
    },
    hideFields: {
      type: Array,
      default: () => []
    },
  },
  created() {
    if (!this.defaultTrigger) {
      this.show = this.showStatus;
    }
    this.getFields();
  },
  data() {
    return {
      show: false,
      checkBoxItems: "",
      colsObj: "",
      selectedCols: []
    };
  },
  watch: {
    showStatus(currentValue) {
      if (!this.defaultTrigger) {
        this.show = currentValue;
      }
    },
    type() {
      this.getFields();
    },
    sysConfig() {
      this.handleChange();
    }
  },
  methods: {
    closeModal() {
      if (!this.defaultTrigger) {
        this.hideModal();
      } else {
        this.show = false;
      }
    },
    showModal() {
      this.show = true;
    },
    getFields() {
      basic.getFields({ type: this.type }).then(res => {
        if (res.status) {
          this.colsObj = res.data;
          let cols = this.cloneObj(res.data.selected_column);
          this.$emit("on-change", cols, this.colsObj);
          this.dealCols();
        } else {
          this.modalError(res.message);
        }
      });
    },
    recovery() {
      basic.initCustomExportColumn({ type: this.type }).then(res => {
        if (res.status) {
          this.selectedCols = res.data;
          this.closeModal();
          this.$emit("on-change", this.selectedCols, this.colsObj);
        } else {
          this.$Modal.error({
            title: "错误",
            content: res.message
          });
        }
      });
    },
    dealCols() {
      let newCols = [];
      let allCols = this.colsObj.all_column;
      let defaultCols = this.colsObj.default_column;
      this.selectedCols = this.colsObj.selected_column;
      Object.keys(allCols).forEach(key => {
        if (this.hideFields.includes(key)) {
          return
        }
        newCols.push({ title: allCols[key], key: key });
      });
      newCols.forEach(col => {
        defaultCols.forEach(item => {
          if (col.key === item) {
            col.isDefault = true;
          }
        });
      });
      this.checkBoxItems = this.cloneObj(newCols);
    },
    hideModal() {
      this.$parent.showSelectPanel = false;
    },
    handleChange() {
      this.$emit("on-change", this.selectedCols, this.colsObj);
    },
    updateCols() {
      let params = {
        type: this.type,
        selected_column: JSON.stringify(this.selectedCols)
      };
      basic.updateFields(params).then(res => {
        if (res.status) {
          this.closeModal();
          this.$emit("on-change", this.selectedCols, this.colsObj);
        } else {
          this.$Modal.error({
            title: "错误",
            content: res.message
          });
        }
      });
    }
  }
};
</script>

<style scoped></style>
