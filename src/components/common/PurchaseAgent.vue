<template>
  <CommonSelect
    v-model="selfValue"
    @on-change="onChange"
    url="/superAdmin/purchaseAgent/list"
    :multiple="multiple"
    :disabled="disabled"
    :all-label="allLabel"
    :all-value="allValue"
    :filterable="filterable"
    :clearable="false"
    :show-all="false"
    :shouldInitData="shouldInitData"
    :placeholder="placeholder"
  ></CommonSelect>
</template>

<script>
import SelectMixin from '@/mixins/select';
export default {
  name: 'NoticeSelect',
  autoRegister: true,
  mixins: [SelectMixin],
  props: {
    placeholder: {
      type: String,
      default: '请选择采购员',
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    // 控制是否初始化数据
    shouldInitData: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped></style>
