<template>
  <Row
    type="flex"
    align="middle">
    <RadioGroup
      v-model="quickItem"
      type="button"
      class="radioGroup"
      @on-change="changeQuickItem">
      <Radio label="week">本周</Radio>
      <Radio label="lastWeek">上周</Radio>
      <Radio label="month">本月</Radio>
      <Radio label="lastMonth">上月</Radio>
    </RadioGroup>
    <DatePicker
      style="margin-left: 10px; width: 200px"
      v-model="currentDate"
      class="date-picker"
      type="daterange"
			:clearable="clearable"
      @on-change="onChange"
      placeholder="选择日期"></DatePicker>
  </Row>
</template>

<script>
  import {
    getYesterDay,
    getWeekStartDate,
    getWeekEndDate,
    getLastWeekStartDate,
    getLastWeekEndDate,
    getMonthStartDate,
    getMonthEndDate,
    getLastMonthEndDate,
    getLastMonthStartDate
  } from '@assets/js/getDate.js';
  export default {
    name: "QuickDateRange",
    autoRegister: true,
    props: {
			clearable: {
				type: Boolean,
				default: true
			}
    },
    data() {
      return {
        quickItem: 'week',
        currentDate: '',
        startdatas: []
      }
    },
    created() {
      this.changeQuickItem('week');
    },
    methods: {
      changeQuickItem(val) {
        switch (val) {
          case 'yesterday':
            this.currentDate = [getYesterDay(), getYesterDay()];
            break;
          case 'week':
            this.currentDate = [getWeekStartDate(), getWeekEndDate()];
            this.quickItem = 'week'
            break;
          case 'lastWeek':
            this.currentDate = [getLastWeekStartDate(), getLastWeekEndDate()];
            break;
          case 'month':
            this.currentDate = [getMonthStartDate(), getMonthEndDate()];
            break;
          case 'lastMonth':
            this.currentDate = [getLastMonthStartDate(), getLastMonthEndDate()];
            break;
        }
        this.onChange(this.currentDate);
      },
      onChange(date) {
        this.$emit('on-change', date);
      }
    }
  }
</script>

<style scoped>
  .radioGroup .ivu-radio-wrapper-checked {
    background: #03ac54;
    color: #ffffff;
  }
</style>
