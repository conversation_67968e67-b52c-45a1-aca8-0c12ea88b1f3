<template>
  <div class="global-search">
    <Input
      ref="input"
      v-model="searchKey"
      type="text"
      class="global-search__input"
      @on-focus="$_onSearchMenu"
      @on-change="$_onSearchMenu"
      @on-enter="$_onSearchMenu"
      @click.native.stop
      element-id="search_id"
      placeholder="搜功能、搜应用"
    >
      <s-icon
        slot="suffix"
        icon="ic_search"
        :size="14"
        id="search_icon_id"
        class="global-search__icon"
        @click="$_onSearchMenu"
      />
    </Input>

    <template v-if="startLoading">
      <div class="global-search__panel" v-show="c_exit_menu || c_exit_app">
        <div v-show="c_exit_menu">
          <p>功能直达</p>
          <ul>
            <li
              v-for="fn in searchMenuData.menus"
              :key="fn.path"
              :class="{
                active: fn.index === activeIndex,
              }"
              @click.capture="$_onGoPage(fn.path)"
              v-html="fn.name"
            ></li>
          </ul>
        </div>
        <div v-show="c_exit_app">
          <p>应用直达</p>
          <ul>
            <li
              v-for="app in searchMenuData.apps"
              :class="{
                active: app.index === activeIndex,
              }"
              :key="`${app.name}_${app.config_key}`"
              @click.capture="$_onGoPage(app.path)"
              v-html="app.name"
            ></li>
          </ul>
        </div>
      </div>

      <div
        v-show="!c_exit_menu && !c_exit_app && searchKey"
        class="global-search__empty"
      >
        抱歉，没有找到合适的搜索结果
      </div>
    </template>
  </div>
</template>

<script>
import appCenterMixin from '@/mixins/appCenter.js';
import PATH_MAP from './global-search';
export default {
  name: 'gloabl-search',
  mixins: [appCenterMixin],
  data() {
    return {
      totalList: [],
      activeIndex: 0,
      totalLength: 0,
      searchKey: '',
      startLoading: false,
      localMenuData: [], // 存放本地存储的localStorage中的menuData
      // 菜单/功能搜索数据
      searchMenuData: {
        menus: [],
        apps: [],
      },
      // 菜单/功能原始数据
      originalSearchMenuData: {
        menus: [],
        apps: [],
      },
    };
  },
  computed: {
    c_exit_menu() {
      return this.searchMenuData.menus.length !== 0;
    },
    c_exit_app() {
      return this.searchMenuData.apps.length !== 0;
    },
  },
  created() {
    document.addEventListener('click', this.$_onCloseSearch);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.$_onCloseSearch);
  },
  watch: {
    startLoading() {
      if (this.startLoading) {
        this.activeIndex = 0;
        window.addEventListener('keydown', this.keyControllFn);
      } else {
        window.removeEventListener('keydown', this.keyControllFn);
      }
    },
  },
  methods: {
    keyControllFn(event) {
      const dropdown = document.getElementsByClassName(
        'global-search__panel',
      )[0];
      let e = event || window.event || arguments.callee.caller.arguments[0];
      // 如果点击向上按钮
      if (e && e.keyCode === 38) {
        dropdown.scrollTop = Math.ceil(dropdown.scrollTop - 30);
        if (this.activeIndex > 0) {
          this.activeIndex -= 1;
        }
      }

      // 如果点击向下按钮
      if (e && e.keyCode === 40) {
        dropdown.scrollTop = Math.ceil(dropdown.scrollTop + 30);
        if (this.totalLength > this.activeIndex) {
          this.activeIndex += 1;
        }
      }

      // 回车
      if (e && e.keyCode === 13) {
        this.startLoading = false;
        if (this.totalList[this.activeIndex]) {
          this.$_onGoPage(this.totalList[this.activeIndex].path);
        }
      }
    },
    $_onCloseSearch(_event) {
      if (_event && _event.target) {
        // 点击输入框会显示之前的搜索结果
        const targetId = _event.target.id;
        // 点击输入框和图标都显示结果
        if (
          ['search_id', 'search_icon_id'].includes(targetId) &&
          (this.c_exit_menu || this.c_exit_app)
        ) {
          this.startLoading = true;
          return;
        }
      }
      this.startLoading = false;
    },
    $_onSearchMenu() {
      this.startLoading = true;
      let _query = this.searchKey;
      if (_query === '') {
        // 为空的时候自动获取推荐数据
        // 根据当前路径获取所在模块
        this._getModuleOriginData().then(({ menus, apps }) => {
          this.searchMenuData = { menus, apps };
          this.$_onSearchMenuAssist();
        });
      } else {
        this._getOriginalData().then(
          ({ menus: originalMenus, apps: orginalApps }) => {
            // 搜索并替换菜单信息
            this.searchMenuData.menus = originalMenus
              .filter((item) => {
                return item.name.indexOf(_query) > -1;
              })
              .map((item) => {
                return this._replaceContent(_query, item);
              });
            // 搜索并替换应用信息
            this.searchMenuData.apps = orginalApps
              .filter((item) => {
                return item.name.indexOf(_query) > -1;
              })
              .map((item) => {
                return this._replaceContent(_query, item);
              });
            this.$_onSearchMenuAssist();
          },
        );
      }
    },
    $_onSearchMenuAssist() {
      // 给每列编号
      let index = 0;
      this.totalList = [];
      this.searchMenuData.menus.forEach((item) => {
        this.totalList.push(item);
        item.index = index;
        index += 1;
      });
      this.searchMenuData.apps.forEach((item) => {
        this.totalList.push(item);
        item.index = index;
        index += 1;
      });
      this.totalLength = index - 1;
    },
    _replaceContent(_query, _item) {
      // 内容替换(高亮显示)
      const reg = eval(`/${_query}/g`);
      const replaceContent = `<span style="color:var(--primary-color)">${_query}</span>`;
      _item.name = _item.name.replace(reg, replaceContent);
      return _item;
    },
    hasModule(menuItem, url) {
      let flag = false;
      menuItem.children &&
        menuItem.children.forEach((children) => {
          if (children.path === url) {
            flag = true;
          } else {
            // 没有路径的可能是分组了(循环三级菜单)
            children.children &&
              children.children.forEach((grandChildren) => {
                if (grandChildren.path === url) {
                  flag = true;
                }
              });
          }
        });
      return flag;
    },
    /**
     * @description: 获取当前模块应该显示的数据
     * @return {*}
     */
    async _getModuleOriginData() {
      const url = this.$route.path;
      if (!this.localMenuData || !this.localMenuData.length) {
        const localMenuData = localStorage.getItem('menuData');
        this.localMenuData = localMenuData ? JSON.parse(localMenuData) : [];
      }
      const menuData = this.localMenuData;
      // 菜单数据组装(只过滤二级菜单)
      const menus = [];
      let name = '';
      menuData.forEach((menuItem) => {
        if (menuItem.path === url || this.hasModule(menuItem, url)) {
          name = menuItem.name;
          //  Array.isArray(menuItem.children) && menus.push(...menuItem.children);
          const { children } = menuItem;
          if (Array.isArray(children) && children.length) {
            children.forEach((child) => {
              if (child.children && child.children.length) {
                menus.push(...child.children);
              } else {
                menus.push(child);
              }
            });
          }
        }
      });
      let { apps } = await this._getOriginalData();
      let filterApps = [];
      if (PATH_MAP[name] && PATH_MAP[name].length) {
        filterApps = apps.filter((app) => {
          return PATH_MAP[name].includes(app.name);
        });
      }
      return this.deepClone({ menus, apps: filterApps });
    },
    /**
     * @description: 获取原始数据(包含菜单信息、应用信息)
     * @return {Object}
     * @author: lizi
     */
    async _getOriginalData() {
      if (!this.originalSearchMenuData.menus.length) {
        if (!this.localMenuData || !this.localMenuData.length) {
          const localMenuData = localStorage.getItem('menuData');
          this.localMenuData = localMenuData ? JSON.parse(localMenuData) : [];
        }
        const menuData = this.localMenuData;
        // 菜单数据组装(只过滤二级菜单 + 三级菜单)
        let menus = [];
        menuData.forEach((menuItem) => {
          menuItem.children &&
            menuItem.children.forEach((children) => {
              if (children.path) {
                menus.push(children);
              } else {
                // 没有路径的可能是分组了(循环三级菜单)
                children.children &&
                  children.children.forEach((grandChildren) => {
                    if (grandChildren.path) {
                      menus.push(grandChildren);
                    }
                  });
              }
            });
        });
        this.originalSearchMenuData.menus = Object.freeze(menus);
      }
      if (!this.originalSearchMenuData.apps.length) {
        await this.setAppList();
        const appData = this.appList;
        // 应用数据组装(过滤每个分组中的单个应用，并且免费 + 付费(已获取的)))
        const apps = [];
        // 判断有应用中心模块的权限时才能看到应用直达
        if (
          this.localMenuData &&
          this.localMenuData.length &&
          ~this.localMenuData.findIndex((item) => item.name === '应用')
        ) {
          appData.forEach((appItem) => {
            appItem.apps &&
              appItem.apps.forEach((children) => {
                if (
                  children.is_free ||
                  (!children.is_free && children.is_get) ||
                  children.notGetButAccessible
                ) {
                  // 路径转换(应用中心使用的是link作为跳转路径、菜单是使用的path作为跳转路径；这里统一一下)
                  children.path = children.link;
                  apps.push(children);
                }
              });
          });
        }
        this.originalSearchMenuData.apps = Object.freeze(apps);
      }
      return this.deepClone(this.originalSearchMenuData);
    },
    $_onGoPage(_path) {
      if (_path) {
        this.searchKey = '';
        this._clearSearchResult();
        if (this.$refs.input) {
          this.$refs.input.$el.querySelector('input').blur();
        }
        if (typeof _path === 'string') {
          this.$router.push(_path);
        } else if (typeof _path === 'function') {
          _path.call(this);
        }
      }
    },
    _clearSearchResult() {
      this.searchMenuData.menus = [];
      this.searchMenuData.apps = [];
    },
  },
};
</script>
<style lang="less">
.global-search {
  width: 232px;
  background: #ffffff;
  position: relative;
  margin-right: 16px;
  height: auto;
  vertical-align: middle;
  margin-top: -1px;

  .ivu-input {
    border-radius: 15px;
    padding-left: 15px;
    background: #f2f3f5;
    border: 1px solid transparent;
    &:hover,
    &:focus {
      border: 1px solid #03ac54;
      box-shadow: 0px 0px 6px 0px rgba(23, 56, 39, 0.1);
      background: transparent;
    }
  }
  .ivu-input-suffix i {
    font-size: 19px;
  }
  &:hover {
    .ivu-input-suffix i {
      color: #03ac54;
    }
  }
  &__panel {
    width: 232px;
    max-height: 208px;
    background: #ffffff;
    box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
    border-radius: 4px;
    padding: 17px 0;
    line-height: initial;
    overflow-y: auto;
    position: absolute;
    z-index: 1000;
    &::-webkit-scrollbar {
      width: 7px;
    }
    &::-webkit-scrollbar-track {
      -webkit-box-shadow: inset 0 0 6px transparent;
      border-radius: 10px;
      margin: 2px 0;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 4px;
      background: rgba(0, 0, 0, 0.3);
      -webkit-box-shadow: inset 0 0 6px transparent;
      margin: 0 10px;
    }
    &::-webkit-scrollbar-thumb:window-inactive {
      background: rgba(0, 0, 0, 0.3);
    }
    p {
      font-size: 13px;
      font-family:
        PingFangSC-Semibold,
        PingFang SC;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 14px;
      position: relative;
      padding-left: 28px;
      padding-bottom: 4px;
      // 保留角标标识，UI后期可能会调整为角标
      // &::before {
      //   content: '';
      //   width: 10px;
      //   height: 10px;
      //   background: url(../../assets/images/index/icon_arrow.png) top center
      //     no-repeat;
      //   background-size: 100% 100%;
      //   position: absolute;
      //   left: 12px;
      //   top: 2px;
      // }

      &::before {
        content: '';
        width: 3px;
        height: 11px;
        background: #505050;
        position: absolute;
        left: 18px;
        top: 2px;
      }
    }
    ul {
      margin-bottom: 4px;
      li {
        padding: 9px 38px;
        position: relative;
        cursor: pointer;
        color: #000000;
        &::before {
          content: '';
          width: 4px;
          height: 4px;
          background: #03ac54;
          border-radius: 3px;
          position: absolute;
          left: 28px;
          top: 50%;
          transform: translateY(-50%);
        }
        &:hover {
          background-color: rgba(3, 172, 84, 0.05);
        }
      }
      .active {
        background-color: rgba(3, 172, 84, 0.05);
      }
    }
  }
  &__empty {
    padding: 12px;
    line-height: initial;
    width: 232px;
    background: #ffffff;
    box-shadow: 0px 2px 14px 1px rgba(15, 33, 27, 0.15);
    border-radius: 4px;
    text-align: center;
    position: absolute;
  }
  &__icon {
    left: -6px;
    position: relative;
    cursor: pointer;
  }
}
</style>
