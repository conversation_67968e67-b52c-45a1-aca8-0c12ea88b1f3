<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="onCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1100">
      <list-table
        ref="list"
        :table-height="tableHeight"
        :api="apiUrl.getGoodsList"
        :auto-load-data="false"
        :filter-items="filterItems"
        :filters="filters"
        :columns="cols"
        :row-class-name="rowClassName"
        :after-load-list="afterLoadList"
        :page-size-opts="pageSizeOpts"
        @after-render="afterRender"
        @on-select="onSelect"
        @on-row-click="onRowClick"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-selection-change="onSelectionChange">
        <category-select slot="category" @on-change="changeCategory" v-if="modal.show"></category-select>
        <Select slot="presell_status" style="width:200px" @on-change="changePresellStatus">
          <Option value="defualt" >商品预售状态</Option>
          <Option value="1" >预售商品</Option>
          <Option value="2" >非预售商品</Option>
        </Select>
      </list-table>
      <div slot="footer">
        <Button   @click="onCancel">{{cancelText}}</Button>
        <Button  type="primary"  @click="onOk" v-if="showOk">{{okText}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import CategorySelect from '@components/common/categorySelect';
  import ListTable from '@components/common/list-table/list'
  const LIST_KEY = 'commodity_id';
  export default {
    name: "GoodsModal",
    components: {
      CategorySelect,
      ListTable
    },
    props: {
      show: {
        type: Boolean,
        default: false
      },
      maxSelectCount: {
        type: Number,
        default: 0,
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      closable: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: '请选择商品'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      okText: {
        type: String,
        default: '确 定'
      },
      showOk: {
        type: Boolean,
        default: true
      },
      // 默认选中的上商品
      defaultSelectedGoods: {
        type: Array,
        default: () => []
      },
      disabledGoods: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      columns: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      filterParams: {
        type: Object,
        default: () => {}
      },
      showPresellStatusFilter: {
        type: Boolean,
        default: true
      },
      pageSizeOpts: {
        type: Array,
        default: () => [10, 20, 30, 40]
      }
    },
    watch: {
      show(newValue, oldValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.setFilters();
          this.selectedGoods = this.cloneObj(this.defaultSelectedGoods);
          this.loadGoods();
        }
      },
      defaultSelectedGoods: {
      	deep: true,
        handler(newValue) {
          this.selectedGoods = this.cloneObj(newValue);
        }
      }
    },
    computed: {
    },
    data() {
      return {
        modal: {
          show: false,
          className: 'vertical-center-modal common-goods-modal'
        },
        tableHeight: this.getTableHeight() * 0.8,
        filterItems: [
          {
            key: 'category',
            type: 'slot'
          },
          {
            key: 'searchValue',
            placeholder: '请输入商品名称/编码进行搜索',
            showIcon: true,
          }
        ],
        filters: {
          category_id: '',
          category_id2: '',
          pre_sale: ''
        },
        storeList: [],
        goodsList: [],
        selectedRow: [],
        selectedGoods: [],
        cols: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          },
          {
            title: "商品图片",
            render: (h, params) => {
              let obj = params.row;
              return h('img', {
                attrs: {
                  src: obj.logo + '!40x40'
                },
                style: {
                  background: obj._disabled ? '#000' : '',
                  opacity: obj._disabled ? '0.3' : 1
                }
              })
            }
          },
          {
            title: "商品编码",
            key: "commodity_code",
          },
          {
            title: "商品名称",
            key: "name",
          },
          {
            title: "商品分类",
            key: 'category_name'
          },
          {
            title: "单位",
            key: "unit",
          },
          {
            title: "描述",
            key: "summary",
          },
        ],
      }
    },
    created() {
      if (this.showPresellStatusFilter) {
        this.filterItems.splice(1, 0, {
          key: 'presell_status',
          type: 'slot'
        })
      }
      this.setFilters();
      this.modal.show = this.show;
      if (!this.showFooter) {
        this.modal.className += ' hide-footer';
      }
      if (this.columns && this.columns.length > 0) {
        this.cols = this.columns;
      }
      if (this.show) {
        this.$nextTick(() => {
          this.$refs.list.loadListData();
        });
      }
    },
    methods: {
      setFilters() {
        this.filters = {
          ...this.filters,
          ...this.filterParams
        };
      },
      afterRender() {
        this.setDisabledGoods();
        this.checkMaxSelectCount();
      },
      changeCategory(category) {
        this.filters.category_id = category[0];
        this.filters.category_id2 = category[1];
        this.filters.category_id3 = category[2];
        this.loadGoods();
      },
      changePresellStatus (status) {
        if (status === 'defualt') {
          this.filters.pre_sale = ''
        } else {
          this.filters.pre_sale = status;
        }
      },
      loadGoods() {
        // let selectedGoodsIdArr = this.defaultSelectedGoods.map((goods) => goods[LIST_KEY]);
        // this.filters.commodity_string = JSON.stringify(selectedGoodsIdArr);
        this.$refs.list.loadListData();
      },
      updateRow(row) {
      	let index = this.storeList.findIndex(item => item[LIST_KEY] === row[LIST_KEY]);
      	if (index !== -1) {
      		this.storeList[index] = this.deepClone(row);
        }
      },
      getList() {
        return this.$refs.list.getListData();
      },
      setList(list) {
        return this.$refs.list.setListData(list);
      },
      syncList() {
      	let list = this.deepClone(this.getList());
      	list = list.map(item => {
      		let storeItem = this.storeList.find(findItem => findItem[LIST_KEY] === item[LIST_KEY]);
      		if (storeItem) {
      			storeItem._checked = item._checked;
      			return storeItem;
          }
          return item;
        });
      	this.storeList = this.deepClone(list);
      	this.setList(list);
      },
      afterLoadList(list) {
        list.forEach((goods) => {
          goods._checked = false;
          if (this.isSelected(goods)) {
            goods._checked = true;
          }
        });
        this.$emit('after-load-list', list);
        this.syncList();
      },
      onSelectAll (selection) {
        selection.forEach((row) => {
          this.selectGoods(row);
        });
      },
      onSelect(selection, row) {
        this.selectGoods(row);
      },
      onSelectionChange (selection) {
        selection.forEach((goods) => {
          this.selectGoods(goods);
        });
        if (selection.length === 0) {
          this.$refs.list.list.forEach((item) => {
            this.cancelSelectGoods(item);
          });
        }
        this.checkMaxSelectCount();
      },
      onSelectCancel(selection, row) {
        this.cancelSelectGoods(row);
      },
      onRowClick(row, index) {
        if (this.isDisabled(row) || row._disabled) {
          return false;
        }
        if (this.isSelected(row)) {
          this.cancelSelectGoods(row);
          this.cancelCheckStatus(index);
        } else {
          this.selectGoods(row);
          this.setCheckStatus(index);
        }
        this.checkMaxSelectCount();
      },
      isSelected(goods) {
        return this.selectedGoods.some((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]);
      },
      isDisabled(goods) {
        return this.disabledGoods.some((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY]);
      },
      setDisabledGoods() {
        let list = this.getList();
        list.forEach((goods) => {
          if (this.disabledGoods.find((findGoods) => findGoods[LIST_KEY] === goods[LIST_KEY])) {
            goods._disabled = true;
          }
        });
        this.setList(list);
      },
      checkMaxSelectCount() {
        let list = this.getList();
        let selectedLen = this.selectedGoods.length;
        if (!this.maxSelectCount) {
          return false;
        }
        list.forEach((goods, index) => {
          let selected = this.isSelected(goods);
          if (selectedLen >= this.maxSelectCount && !selected) {
            goods._disabled = true;
          } else {
            goods._disabled = false;
          }
          goods._checked = selected;
        });
        this.setList(list);
      },
      /**
       * 设置选中效果
       * @param index 选中行的索引
       */
      setCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 设置选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom && !checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.add('ivu-checkbox-wrapper-checked');
          checkBoxDom && !checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.add('ivu-checkbox-checked');

          const inputDom = selectRow.querySelector('.ivu-input-number-input');
          if (inputDom) {
            inputDom.focus()
            inputDom.select()
          }
        }
      },
      /**
       * 取消选中效果
       * @param index 取消选中行的索引
       */
      cancelCheckStatus(index) {
        let bodyDom = this.$refs.list.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        // 取消选中样式
        if (selectRow) {
          let checkBoxWrapperDom = selectRow.querySelector('.ivu-checkbox-wrapper');
          let checkBoxDom = selectRow.querySelector('.ivu-checkbox');
          checkBoxWrapperDom.classList.contains('ivu-checkbox-wrapper-checked') && checkBoxWrapperDom.classList.remove('ivu-checkbox-wrapper-checked');
          checkBoxDom.classList.contains('ivu-checkbox-checked') && checkBoxDom.classList.remove('ivu-checkbox-checked');
        }
      },
      /**
       * 将商品添加到选中商品中
       * @param row
       */
      selectGoods(row) {
        if (!this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.push(row);
        }
      },
      /**
       * 从选中商品中删除商品
       * @param row
       */
      cancelSelectGoods(row) {
        if (this.selectedGoods.some((goods) => goods[LIST_KEY] === row[LIST_KEY])) {
          this.selectedGoods.splice(this.selectedGoods.findIndex((goods) => goods[LIST_KEY] === row[LIST_KEY]), 1);
        }
      },
      rowClassName (row, index) {
      },
      onCancel() {
        this.$emit('on-cancel');
      },
      onOk() {
        if (this.selectedGoods.length === 0) {
          this.errorNotice('请选择商品');
          return false;
        }
        if (this.maxSelectCount && this.maxSelectCount < this.selectedGoods.length) {
          this.errorNotice(`最多只能选择${this.maxSelectCount}条商品！`);
          return false;
        }
        this.syncList();
        this.$emit('on-ok', this.cloneObj(this.selectedGoods));
      }
    }
  }
</script>

<style lang="less">
  .common-goods-modal {
    .ivu-modal-body {
      padding-bottom: 0;
      .ivu-table-row {
        cursor: pointer;
      }
    }
  }
</style>
<style lang="less" scoped>
</style>
