<template>
  <div id="GoodsList">
    <article class="goods-list">
      <div class="goods-list__operation">
        <div class="goods-list__select-middle">
          <Cascader
            ref="cascaderRef"
            :data="categoryList"
            change-on-select
            clearable
            v-model="categoryData"
            placeholder="全部分类"
            @on-change="changeCategory"
          ></Cascader>
        </div>

        <div class="goods-list__left">
          <i-input
            placeholder="请输入商品编码\名称\助记码\别名"
            v-model="search_key"
            @on-enter="loadGoodsList(1)"
            clearable
          >
            <i-button
              slot="append"
              icon="ios-search"
              @click="loadGoodsList(1)"
              class="search-btn"
            ></i-button>
          </i-input>
        </div>
      </div>

      <div class="goods-list__table">
        <i-table
          :columns="columns"
          :height="440"
          :data="goodsList"
          @on-selection-change="getCheckGoods"
        ></i-table>
        <div style="height:40px;display:flex;align-items:center;">
             <Page
          :total="page.count"
          :current.sync="nowPage"
          :page-size="page.page_size"
          @on-change="getCurrentPage"
          @on-page-size-change="modifyPage"
          placement="top"
          show-elevator
          show-total
          show-sizer
        ></Page>
        </div>

        <div class="bottom-button tr"></div>
      </div>
    </article>
  </div>
</template>

<script>
import '@assets/scss/pagesCommon.scss';
import goods from '@api/goods.js';
import ConfigMixin from "@/mixins/config";

export default {
  name: 'goodsSelect',
  mixins: [ConfigMixin],
  props: {
    filter: {
      type: Object,
      default: () => {
        return {};
      },
      required: false
    },
    uid: {
      type: String
    },
    url: {
      type: String,
      default: ''
    },
    searchValueKey: {
      type: String,
      default: 'search_value'
    }
  },
  data() {
    return {
      params:{},
      columns: [
        {
          type: 'selection',
          width: 32,
          align: 'center',
          className: 'table-select'
        },
        {
          title: '商品图片',
          key: 'logo',
          align: 'center',
          render: (h, params) => {
            var obj = params.row;
            if (!obj.logo) {
              obj.logo =
                'https://img.shudongpoo.com/sdpdev/upload_pic/com_thumb_201712181022093575373e5a372651e750e.jpg';
            }
            return h('img', {
              attrs: {
                src: obj.logo + '!40x40'
              }
            });
          }
        },
        {
          title: '商品编码',
          key: 'commodity_code',
          align: 'left'
        },
        {
          title: '商品名称',
          key: 'name',
          align: 'left'
        },
        {
          title: '商品分类',
          key: 'category_name',
          align: 'left'
        },
        {
          title: '单位',
          width: 120,
          key: 'unit',
          align: 'left'
        },
        {
          title: '描述',
          width: 120,
          key: 'summary',
          align: 'left'
        },
        {
          title: '别名',
          width: 120,
          key: 'alias',
          align: 'left'
        }
      ],
      goodsList: [],
      disabledGoods: [],
      page: {
        currentPage: 1,
        pageSize: 20
      },
      pageSize: '',
      nowPage: 1,
      category: {
        first: '',
        second: ''
      },
      searchData: '',
      categoryData: [],
      categoryList: [],
      search_category_level_1_source: '', // 一级分类搜索条件数据源
      search_category_level_1_selected: '', // 选中一级分类搜索条件

      search_category_level_2_source: '', // 一级分类搜索条件数据源
      search_category_level_2_selected: '', // 选中一级分类搜索条件

      search_key: '' //搜索框输入字符
    };
  },
  created() {
    this.init();
    this.initSearchCombos();
    this.loadGoodsList();
  },
  methods: {
    init() {
      const convertToChildren = (treeData) => {
        if (Array.isArray(treeData)) {
          return treeData.map(convertToChildren);
        }

        if (typeof treeData === 'object' && treeData !== null) {
          const children = treeData.items
            ? convertToChildren(treeData.items)
            : [];
          return {
            ...treeData,
            value: treeData.id,
            label: treeData.name,
            children: children.map(convertToChildren),
          };
        }

        return treeData;
      };
      goods.getGoodsCategoryTree().then((res) => {
        if (res.status) {
          res.data.forEach((item) => {
            if (item.items && item.items.length > 0) {
              item.items.forEach((item2) => {
                if (this.isOpenCommodityCategoryThree) {
                  item2.items = item2.items || [];
                  item2.items.unshift({
                    id: '0',
                    name: '未设置',
                    level: '3',
                  });
                }
              });
            }
          });
          this.categoryList = convertToChildren(res.data);
          this.categoryList.unshift({ value: 0, label: '全部分类' });
        }
      });
    },
    changeCategory(e) {
      this.categoryData = e || [];
      this.loadGoodsList(1);
    },
    // 修改一页展示的条数
    async modifyPage() {
      this.pageSize = arguments[0];
      this.loadGoodsList();
    },
    getCurrentPage() {
      this.nowPage = arguments[0];
      this.loadGoodsList();
    },
    setDisableGoods(goodsIds) {
      if (Array.isArray(goodsIds)) {
        this.disabledGoods = goodsIds;
      }
      let disabledGoods = this.disabledGoods;
      if (Array.isArray(disabledGoods)) {
        let goodsList = this.cloneObj(this.goodsList);
        goodsList.forEach(goods => {
          // goods._disabled = disabledGoods.indexOf(goods.id + '') !== -1 ? true : false;
          // if(goods.selected == 1) {
          //   goods._disabled = true
          // }
          goods._disabled = goods.selected == 1 || disabledGoods.includes(goods.id);
        });
        this.goodsList = goodsList;
      }
    },
    toggleCheckAll(checked) {
      checked || (checked = false);
      this.goodsList.forEach(goods => {
        goods._checked = checked;
        goods._disabled = checked;
      });
    },
    getCheckGoods() {
      let value = arguments[0],
        data = {
          ids: [],
          detail: {}
        };
      if (Array.isArray(value)) {
        value.forEach(goods => {
          data.detail[goods.id] = goods;
          data.ids.push(goods.id);
        });
      }
      this.$emit('selectChange', data);
    },
    getSearchPara(a) {
      if (a) {
        this.nowPage = a;
      }
      let para = {
        // 获取商品列表的参数对象
        page: this.nowPage,
        pageSize: this.pageSize,
        [this.searchValueKey]: this.search_key, //搜索关键词
        category_id: this.categoryData[0], //一级分类id
        category_id2: this.categoryData[1], //二级分类id
        category_id3: this.categoryData[2], //三级分类id
        agent_id: this.search_agent_selected, //采购员id
        sortName: '', //排序字段
        sortOrder: '', //排序方式
        user_id: this.uid
      };
      return para;
    },
    async loadGoodsList(a) {
      let paras = this.getSearchPara(a);
      this.params = Object.assign(paras, this.filter);
      let res = {}
      if(this.url) {
        res = await this.$request.post(this.url, paras)
      } else {
        res = await goods.searchCommodity(paras);
      }

      if (!res.status) {
        this.modalError(res.message);
        this.$emit('afterLoadGoods', []);
        return;
      }
      let data = res.data;
      this.$emit('afterLoadGoods', data.list);
      data.list.forEach(goods => {
        goods._disabled = false;
        goods._checked = false;
      });
      this.goodsList = data.list;
      this.page = data.pageParams;
      this.page.count = parseInt(this.page.count);
      this.page.page_size = parseInt(this.page.page_size);
      this.setDisableGoods();
    },

    //加载搜索下拉框数据
    async initSearchCombos() {
      //一级分类
      this.search_category_level_1_source = [];
      let res = await goods.getGoodsCategory();
      if (res.status) {
        this.search_category_level_1_source = res.data;
      }
      this.search_category_level_1_source.unshift({
        id: 0,
        name: '一级分类'
      });
      //二级分类
      this.search_category_level_2_source = [];
      this.search_category_level_2_source.unshift({
        id: 0,
        name: '二级分类'
      });
    },
    async initCategoryLevel2Combo(a) {
      this.search_category_level_2_source = [];
      if (this.search_category_level_1_selected > 0) {
        let res = await goods.getGoodsCategory(
          this.search_category_level_1_selected
        );
        if (res.status) {
          this.search_category_level_2_source = res.data;
        }
      }
      this.search_category_level_2_source.unshift({
        id: 0,
        name: '二级分类'
      });
      this.search_category_level_2_selected = '';
      this.loadGoodsList(a);
    }
  }
};
</script>

<style lang="scss">
/* iview custom */

.ivu-icon-ios-search {
  font-size: 20px;
  color: #fff;
}

.goodslist__add-btn {
  position: absolute;
  top: 10px;
  right: 0;
}

.goods-list__btn {
  margin-right: 10px;
  float: left;
}

.ivu-input-hide-icon .ivu-input-icon {
  margin-right: 50px;
}
</style>
<style lang="scss" scoped>
.goods-list__select-middle {
  float: left;
  padding: 10px 0;
  margin-left: 10px;
}
.goods-list__table {
  margin-top: 20px;
}
.goods-list {
  padding:30px 10px;
  background-color: #fff;
}
.goods-list__operation {
  position: relative;
  width: 100%;
  border-bottom: 1px solid #d7dde4;
  &:after {
    content: ' ';
    display: block;
    background: red;
    clear: both;
  }
  /* iview custom */
  .ivu-input-wrapper {
    margin-top: -2px;
    width: 275px;
    /*padding: 10px 0;*/
  }
  .goods-list__left {
    float: left;
    padding: 10px;
  }
  .goods-list__right-but {
    padding: 10px 0;
    float: right;
  }
}
</style>
