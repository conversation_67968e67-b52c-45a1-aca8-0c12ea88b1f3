/**
 * Created by ddcoder on 19/4/26.
 */

import store from '@/vuex'
import { api } from '@api/api'
import { get } from '@api/request'
import Bus from "@api/bus"

let snoticeInstance = undefined

/**
 * 轮询
 * @param opts
 * @param opts {func} 轮询的函数
 * @param opts {maxLoopTIme} 最长轮询时间
 * @param opts {frequence} 轮询频率
 */
const Loop = (opts) => {
  const defaultOps = {
    func: () => { },
    autoStart: true,
    timer: null,
    time: 0, // 已轮训的时间
    maxLoopTime: 6 * 60 * 1000, // 最长轮训时间
    frequency: 1000, // 默认1s轮询一次
  }
  let loopOptions = Object.assign(defaultOps, opts)
  const startLoop = () => {
    if (loopOptions.timer === null) {
      loopOptions.timer = setInterval(() => {
        if (loopOptions.time - loopOptions.maxLoopTime >= 0) {
          endLoop()
        }
        loopOptions.time += loopOptions.frequency
        loopOptions.func && loopOptions.func()
      }, loopOptions.frequency)
    }
  }
  const endLoop = () => {
    if (loopOptions.timer !== null) {
      clearInterval(loopOptions.timer)
      loopOptions.time = 0
      loopOptions.timer = null
    }
  }
  if (loopOptions.autoStart === true) {
    startLoop()
  }
  return {
    startLoop,
    endLoop
  }
}


/**
 * 导入轮询
 * @param task_no
 * @param vm
 * @param url
 */
const importLoop = (task_no, vm, url) => {
  url = url || api.taskResult
  let loop = {}
  snoticeInstance = undefined
  const request = () => {
    let params = {
      task_no
    }
    get(url, params).then(res => {
      const { data, status } = res
      const { type_name, content, success_num, failure_num } = data

      if (status && !snoticeInstance) {
        Bus.$emit('refresh-task-center')
        let noticeName = task_no
        const isSuccess = content === 'success' // 通过content判断文件校验是否成功
        Bus.$emit('on-import-completed', isSuccess && Number(success_num) > 0) // 返回是否有数据成功导入

         // 协议单、采购协议价为整个单据成功或失败，没有部分成功的状态
        const wholeCompleteTypes = ['协议单', '采购协议单', '入库单', '出库单']
        let wholeComplete = false
        if (wholeCompleteTypes.includes(type_name)) {
          wholeComplete = true
        }

        const noticeConfig = {
          name: noticeName,
          type: isSuccess && Number(failure_num) === 0 ? 'success' : 'error',
          title: isSuccess && Number(failure_num) === 0 ? `${type_name}全部导入成功` : `${type_name}导入失败`,
          duration: 0,
          text: isSuccess ? (Number(failure_num > 0) ?
            `${!wholeComplete ? `${failure_num}个${type_name}导入失败，${success_num}个${type_name}导入成功，` : ''}请在任务列表中下载导入结果，检查失败原因后重新导入` :
            `${!wholeComplete ? `${success_num}个` : ''}${type_name}已导入成功`) : content,
          btnTxt: isSuccess ? '查看任务列表' : undefined,
          btnClick: () => {
            store.commit('showTaskCenter', true)
          }
        }

        snoticeInstance = Bus.$snotice(noticeConfig)

        loop.endLoop()
      }
    }, () => {
      loop.endLoop()
    })
  }
  loop = Loop({
    func: request
  })
}

export {
  Loop,
  importLoop
}

