<template>
  <div class="import-box">
    <!--导入按钮-->
    <Button @click="showModal" v-show="showTrigger">
      <slot>批量导入</slot>
    </Button>
    <!--导入弹出框-->
    <Modal
      :class-name="'modal-for-importBtn ' + modalClassName"
      :width="modalWidth"
      v-model="show"
      :closable="false"
      :title="title"
      @on-visible-change="onVisibleChange"
      @cancel="cancel"
      v-bind="modalProps"
    >
      <div class="import-box__dialog__header" slot="header">
        <div class="import-box__dialog__border"></div>
        {{ title }}
        <span class="import-box__dialog__delete" @click="cancel">
          <Icon icon="close" class="import-box__dialog__delete__icon" />
        </span>
      </div>
      <div class="import-box__dialog__body">
        <slot name="custom-area"></slot>
        <div
          v-if="
            this.$slots['custom-area-left'] || this.$slots['custom-area-right']
          "
          class="import-box__dialog__body__import__description marginT20"
        >
          <div class="import-box__dialog__body__download__text__left">
            <slot v-if="this.$slots['custom-area-left']" name="custom-area-left"></slot>
          </div>
          <div v-if="this.$slots['custom-area-right']">
            <slot name="custom-area-right"></slot>
          </div>
        </div>
        <div
          class="import-box__dialog__body__download__description margin20"
          v-if="download.text && (download.url || download['before-download'])"
        >
          <span class="import-box__dialog__body__download__text__left">下载模板：</span>
          <span class="ml12">点击下载</span>
          <span
            @click="downloadTemplate"
            class="import-box__dialog__body__download__text"
          >{{ download.text }}</span>
        </div>
        <div class="import-box__dialog__body__import__description margin20">
          <span class="import-box__dialog__body__download__text__left">选择上传文件：</span>
          <Upload
            ref="upload"
            class="import-box__dialog__body__import__upload__box ml12"
            :action="post.url"
            :data="data"
            :show-upload-list="false"
            :on-format-error="onFormatError"
            :on-error="onError"
            :on-success="onSuccess"
            :on-exceeded-size="onExceededSize"
            :on-progress="onProgress"
            :before-upload="beforeUpload"
            :mask-closable="false"
            :accept="post.accept"
            :format="post.format"
            :max-size="post.maxSize"
          >
            <Button
              :disabled="importing"
              :loading="loadingStatus"
              class="import-box__dialog__body__select"
              type="primary"
            >{{ importing ? '导入中...' : '选择文件' }}</Button>
          </Upload>
          <span v-show="fileName" class="import-box__dialog__body__import__file__name ml12">
            {{ fileName }}
            <span @click="removeFile">
              <Icon
                icon="solid-close"
                size="mini"
                class="import-box__dialog__body__delete__file__icon"
              />
            </span>
          </span>
        </div>
        <slot name="custom-error-tip" :error-table="errorTable">
          <div v-show="errorTip" class="import-box__dialog__body__error__description">
            <!-- <span class="import-box__dialog__body__download__text__left"></span> -->
            <span class="ml12" style="padding-left: 120px;display: inline-block;">{{ errorTip }}</span>
          </div>
        </slot>
      </div>
      <div class="import-box__dialog__footer" slot="footer">
        <slot name="footer">
          <Button @click="cancel" class="import-box__dialog__footer__cancel">取消</Button>
          <Button @click="confirm" class="import-box__dialog__footer__confirm" type="primary">{{confirmText}}</Button>
        </slot>
      </div>
    </Modal>
  </div>
</template>
<script>
import { importLoop } from './util'
import { exportLoop } from '@components/common/export-btn/util';
import { Modal, Upload } from 'view-design'
import Button from '@components/button/index'
import Icon from '@components/icon/index'
import './style.less'
import Bus from '@api/bus'

export default {
  name: 'ImportButton',
  autoRegister: true,
  components: {
    Button,
    Modal,
    Upload,
    Icon
  },
  props: {
    showTrigger: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否是离线导入
     */
    offline: {
      type: Boolean,
      default: false
    },
    /**
     * 是否是离线导出模板
     */
     offlineTpl: {
      type: Boolean,
      default: false
    },
		// 导入成功是否直接返回数据
		returnData: {
			type: Boolean,
			default: false,
		},
    // 弹出框标题
    title: {
      required: false,
      type: String,
      default: () => {
        return '导入'
      }
    },
    // 确认按钮文字
    confirmText: {
      type: String,
      default: '确定'
    },
    // 下载模板
    download: {
      required: false,
      type: Object,
      default: () => {
        return {
          url: '',
          text: ''
        }
      }
    },
    // 上传时附带的额外参数
    data: {
      type: Object,
      default: () => ({})
    },
    // 上传相关
    post: {
      required: true,
      type: Object,
      default: () => {
        return {
          url: '',
          accept: '',
          maxSize: ''
        }
      }
    },
    // 弹框自定义类名
    modalClassName: {
      required: false,
      type: String,
      default: () => ''
    },
    modalProps: {
      type: Object,
      default: () => ({})
    },
    onConfirm: {
      type: Function,
      default: () => {
        return true
      }
    },
    customErrorTip: {
      type: String,
      default: () => {
        return ''
      }
    },
    modalWidth: {
      type: String,
      default: '520'
    }
  },
  data () {
    return {
      createDownloadUrlFnExecuting: false,
      importSuccess: false,
      importing: false,
      show: false,
      errorTip: '',
      errorTable: [],
      fileName: '',
      file: '',
      loadingStatus: false,
      upload: () => { }
    }
  },
  beforeDestroy () {
    Bus.$off('on-import-completed', this._onOfflineCompleted)
  },
  watch: {
    customErrorTip (n) {
      this.errorTip = n
    }
  },
  methods: {
    // 显示弹出框
    showModal () {
      this.fileName = ''
      this.errorTip = ''
      this.errorTable = []
      this.show = true
    },
    // 下载模板
    async downloadTemplate() {
      if (!this.download.url && !this.download['before-download']) return
      let url = ''
      if (this.download['before-download']) {
        if (this.createDownloadUrlFnExecuting) return
        this.createDownloadUrlFnExecuting = true
        url = await new Promise(resolve => {
          this.download['before-download'](resolve)
        })
        this.createDownloadUrlFnExecuting = false
        if (!url) {
          return
        }
      } else {
        url = this.download.url
      }

      if (this.offlineTpl) {
        this.$request.get(this.download.url).then(res => {
          if (res.status) {
            this.$store.commit('showTaskCenter', true);
            exportLoop(res.data.task_no);
          }
        })
      } else {
        window.location.href = url
      }
    },
    // 点击确定
    confirm () {
      if (!this.file) {
        this.errorTip = '请选择文件'
        return
      }
      // if (this.errorTip) {
      //   return
      // }
      this.loadingStatus = true
      if (!this.onConfirm()) {
        return
      }
      this.upload() // resolve beforeUpload 开始上传
    },
    // 点击取消
    cancel () {
      if (this.importing) {
        return false
      }
      this.removeFile()
      this.show = false
      this.$emit('cancel', false)
    },
    // 文件格式校验失败
    onFormatError () {
      this.importing = false
      this.loadingStatus = false
      this.errorTip = `文件格式校验失败，请上传 ${this.post.format +
        ''} 文件。`
    },
    // 文件上传失败
    onError () {
      this.importing = false
      this.loadingStatus = false
      this.errorTip = `上传文件失败，请重试。`
    },
    // 文件超过大小限制
    onExceededSize () {
      this.importing = false
      this.loadingStatus = false
      this.errorTip = `上传文件超过大小限制，文件不能超过 ${this.post.maxSize}kb。`
    },
    // 文件开始上传
    onProgress () {
      this.importing = true
      this.importSuccess = false
    },
    beforeUpload (file) {
      this.fileName = file.name
      this.file = file
      this.errorTip = ''
      this.errorTable = []
      this.$emit('beforeUpload', {file, fileName: this.fileName})
      Bus.$on('on-import-completed', this._onOfflineCompleted)
      return new Promise(resolve => {
        this.upload = resolve
      })
    },
    // 文件上传成功
    onSuccess (response) {
      this.importing = false
      this.loadingStatus = false
      const { status, message, data } = response
      if (status) {
        // 离线导入
        if (this.offline) {
          this.infoMessage(message || '操作成功，导入任务执行中')
          importLoop(data.exec_no)
        } else {
          this.importSuccess = true
          this.successMessage(message || '批量导入成功')
					if (this.returnData) {
						this.$emit("on-completed", data);
						this.show = false
						return;
					} else {
						this.$emit("on-completed", this.importSuccess, data);
					}
        }
        this.errorTip = ''
        this.errorTable = []
        this.removeFile()
        this.show = false
      } else {
        if (this.offline) {
          this.errorMessage(message || '导入失败')
        } else {
          this.errorTip = response.message || ''
          this.errorTable = response.data || []
        }
        this.importSuccess = false
				this.removeFile(true, false)
        this.$emit('on-completed', this.importSuccess,  this.errorTip, data)
      }
    },
    /**
     * @description: 离线导入模式完成时
     * @param {Boolean} isSuccess 是否成功
     */
    _onOfflineCompleted (isSuccess) {
      this.importSuccess = isSuccess
      this.$emit('on-completed', this.importSuccess)
    },
    onVisibleChange () {
      this.removeFile()
    },
    removeFile (noClearTip = false, clearErrorTable = true) {
      this.fileName = ''
      this.file = ''
      !noClearTip && (this.errorTip = '')
      if (clearErrorTable) {
        this.errorTable = []
      }
      this.loadingStatus = false
      this.$emit('removeFile')
    },
  }
}
</script>

<style lang="less" scoped>
@box-prefix: import-box;

.@{box-prefix} {
  display: inline-block;
}

.ivu-modal-wrap {
  .margin20 {
    margin: 20px 0;
  }
  .marginT20 {
    margin-top: 20px;
  }
  .ml12 {
    margin-left: 12px;
  }
  .mt14 {
    margin-top: 14px;
  }
  .mb27 {
    margin-bottom: 27px;
  }
  .@{box-prefix}__dialog__header {
    font-size: 14px;
    color: #303030;
    text-align: left;
    line-height: 14px;
    display: flex;
    align-items: center;
    display: flex;
    align-items: center;
    font-weight: 500;
  }
  .@{box-prefix}__dialog__border {
    width: 3px;
    height: 12px;
    background: #505050;
    border-radius: 0.5px;
    border-radius: 0.5px;
    display: inline-block;
    margin-left: 10px;
    margin-right: 6px;
  }
  .@{box-prefix}__dialog__delete {
    position: absolute;
    right: 25px;
  }
  .@{box-prefix}__dialog__delete__icon {
    font-size: 10px;
    cursor: pointer;
  }
  .@{box-prefix}__dialog__body {
    margin-top: -16px;
    font-weight: 500;
    &__import__description {
      width: 100%;
      display: inline-flex;
      align-items: center;
    }
    &__download__description {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      margin-bottom: 0px;
    }
    &__download__text__left {
      display: inline-block;
      width: 120px;
      text-align: right;
    }
    &__download__text {
      color: #03ac54;
      cursor: pointer;
    }
    &__import__upload__box {
      display: inline-block;
    }
    &__select {
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
    }
    &__import__file__name {
      font-size: 13px;
      color: #505050;
      text-align: left;
      line-height: 14px;
      display: inline-flex;
      align-items: center;
      width: 200px;
      word-break: break-all;
    }
    &__delete__file__icon {
      margin-left: 16px;
      color: rgba(0, 0, 0, 0.2);
      cursor: pointer;
    }
    &__error__description {
      font-size: 13px;
      color: red;
      text-align: left;
      line-height: 14px;
    }
  }
  .@{box-prefix}__dialog__footer {
    &__cancel {
      font-size: 13px;
      color: #303030;
      text-align: left;
      line-height: 14px;
      background: #ffffff;
      // border: 1px solid #d8d8d8;
      border-radius: 2px;
      border-radius: 2px;
      margin-right: 4px;
    }
    &__confirm {
      font-size: 13px;
      color: #ffffff;
      text-align: left;
      line-height: 14px;
      background: #03ac54;
      border-radius: 2px;
      border-radius: 2px;
    }
  }
}
</style>
