<!--
 * @Description: 
 * @Autor: lizi
 * @Date: 2021-11-10 10:12:03
 * @LastEditors: lizi
 * @LastEditTime: 2022-04-26 16:11:39
 * @FilePath: \sdpbase-pro\src\components\common\store-select.vue
-->
<template>
  <Select
    v-bind="$attrs"
    v-on="$listeners"
    :data="list"
    :value="value"
    :clearable="false"
    :filterable="filterable"
    optionLabel="name"
    optionValue="id"
    @on-change="updateValue"
    :filter-by-label="filterByLabel"
  />
</template>

<script>
import Select from '@sdptest/base/lib/select/src/main';
import store from '@api/storeRoom.js';


export default {
  name: 'store-select',
  components: { Select },
  props: {
    defaultFirst: {
      type: Boolean,
      default: true
    },
    saveValue: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Number]
    },
    filterable:{
      type: <PERSON>olean,
      default: false
    },
    filterByLabel:{
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {
      list: [],
      ready: false
    };
  },
  watch: {},
  created() {
    this.getList();
  },
  methods: {
    getList() {
      store.getUserWarehouse().then(res => {
        if (res.status === 1) {
          this.list = res.data.map(item=>{
            item.label = item.name;
            return item;
          });
          const storeId = sessionStorage.getItem('storeId');
          if (this.saveValue && storeId) {
            this.updateValue(storeId);
          } else if (this.defaultFirst && this.list && this.list.length) {
            this.updateValue(this.list[0].id);
          }
        }
      });
    },
    updateValue(val) {
      if (!this.ready) {
        this.$emit('on-set-default-value', val);
        this.ready = true;
      }
      this.saveValue && sessionStorage.setItem('storeId', val);
      this.$emit('input', val);
      this.$emit('on-change', val);
    }
  }
};
</script>
