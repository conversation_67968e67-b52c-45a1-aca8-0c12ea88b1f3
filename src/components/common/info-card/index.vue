<template>
  <div class="info-card">
    <Row type="flex" align="middle">
      <Col>
      <Icon custom="ricon iconfont sdpicon-dian--" color="#03ac54"></Icon>
      <span class="title">{{title}}</span>
      </Col>
      <Col class="flex-con"><slot name="header"></slot></Col>
    </Row>
    <slot></slot>
  </div>
</template>

<script>
  import Bus from '@api/bus.js'
  export default {
    autoRegister: true,
    name: "InfoCard",
    props: {
      title: {
        type: String,
        default: '标题'
      }
    },
    data() {
      return {
      }
    },
  }
</script>

<style lang="less" scoped>
  .info-card {
    padding: 10px;
    .title {
      font-weight: bold;
      margin-left: 5px;
      &::before {
        content: '';
        display: inline-block;
        width: 3px;
        height: 11px;
        background: rgba(0, 0, 0, 0.7);
        margin-right: 6px;
        margin-bottom: -1px;
      }
    }
  }
</style>
