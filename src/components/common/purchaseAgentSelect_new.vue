<!--
 * @Description:
 * @Date: 2023-02-27 09:42:58
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-10 15:12:28
 * @FilePath: /sdpbase-pro/src/components/common/purchaseAgentSelect_new.vue
-->
<template>
  <Select
    :filterable="filterable"
    v-model="agentsId"
    @on-change="updateValue"
    :placeholder="placeholder"
    :disabled="disabled"
  >
    <Option :value="item.id" v-for="item in list" :key="item.id">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
import goods from '@api/goods.js';
export default {
  name: 'purchaseAgentSelect',
  props: {
    data: {
      type: Array,
    },
    value: {
      default: ''
    },
    filterable: {
      type: Boolean,
      default: false
    },
    placeholder: {
      default: '全部'
    },
    needAll: {
      type: Boolean,
      default: true
    },
    needNoSet: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    isFilterDisabled: {
      type: <PERSON>olean,
      default: false
    },
    clearDataOnDeactivated: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    value (newValue) {
      this.agentsId = newValue;
    },
    data: {
      deep: true,
      handler (val) {
        this.list = val || []
      }
    }
  },
  data() {
    return {
      agentsId: '',
      list: []
    };
  },
  created() {
    this.agentsId = this.value;
    if (!this.data) {
      this.getList();
    } else {
      this.list = this.data
    }
  },
  activated () {
    if (!this.data) {
      this.getList();
    }
  },
  deactivated(){
    if (this.clearDataOnDeactivated) {
      this.list = [];
    }
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        id: '',
        name: '全部'
      };
      goods.getPurchaseType().then(res => {
        if (res.status) {
          if(this.isFilterDisabled) {
            res.data.agents = res.data.agents.filter(item => +item.disable === 0)
          }
					console.log('成功获取数据')
          this.list = res.data.agents;
        }
        if(this.needAll) {
          this.list.splice(0, 0, defaultItem);
        }
        if(this.needNoSet) {
          this.list.splice(0, 0, {
            id: 'noSet',
            name: '不临时指定'
          })
        }

      });
    },
    updateValue() {
      let agentsId = ''
      if (this.needNoSet) {
        agentsId = this.agentsId === 'noSet' ? '' : this.agentsId
      } else {
        agentsId = this.agentsId
      }
      this.$emit('input', agentsId);
      this.$emit('on-change', agentsId);
    }
  }
};
</script>

<style scoped></style>
