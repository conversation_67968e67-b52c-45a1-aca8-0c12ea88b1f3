<template>
  <div class="quick-link-top">
    <ul class="quick-link-text">
      <li class="quick-link-item" v-for="item in list" :key="item.id" @click="handleGo(item)"><i class="icon-quicklink"></i><span>{{ item.href_name }}</span></li>
      <li v-if="data && data.length > 3" class="more-btn" @click="show = !show">
        <Icon icon="xialagengduo" style="font-size: 9px;transform: rotate(90deg);cursor: pointer;" :style="{ transform: `rotate(${show ? 180 : 0}deg)` }"></Icon>
      </li>
    </ul>
    <ul class="quick-link-modal" v-show="show" v-if="data && data.length > 3">
      <li class="quick-link-title">快捷链接</li>
      <li class="quick-link-item" v-for="item in data" :key="item.id" @click="handleGo(item)"><i class="icon-quicklink"></i><span>{{ item.href_name }}</span></li>
    </ul>
  </div>
</template>
<script>
import Icon from '@components/icon'
import {mapState} from 'vuex'

export default {
  name: 'quick-links',
  components: {
    Icon
  },
  data() {
    return {
      data: [],
      show: false
    }
  },
  watch: {
    'sysConfig.is_open_custom_href'() {
      this.getList();
    }
  },
  computed: {
    list() {
      this.data = this.$store.state.customHrefList
      if (this.data) {
        return this.data.slice(0,3)
      }
    },
    ...mapState({
      sysConfig: 'sysConfig'
    })
  },
  created() {
    this.getList();
  },
  methods: {
    getList() {
      if (+this.sysConfig.is_open_custom_href === 1) {
        this.$request.get(this.apiUrl.getCustomHrefList).then(res => {
          if (res.status) {
            this.data = res.data.list
            this.$store.commit('setCustomHrefList', res.data.list)
          } else {
            this.$Message.warning(res.message)
          }
        });
      }
    },
    handleGo(item) {
      this.show = false
      window.open(item.href_url)
    }
  }
}
</script>
<style lang="less" scoped>
  .quick-link-text {
    display: flex;
    margin-top: 1px;
    align-items: center;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0,0,0,0.65);
    padding-right: 40px;
    line-height: 1;
    .quick-link-item {
      display: flex;
      align-items: center;
      padding: 0 6px;
      height: 22px;
      cursor: pointer;
      &:hover {
        background: #F2F3F5;
        border-radius: 4px;
      }
      span {
        display: inline-block;
        max-width: 108px;
        white-space: nowrap;      /* 确保文本在一行内显示 */
        overflow: hidden;         /* 隐藏溢出的内容 */
        text-overflow: ellipsis;  /* 使用省略号表示文本溢出 */
      }
    }
    /deep/ .sui-icon:hover {
      background: transparent;
    }
    .more-btn {
      padding: 2px;
    }
  }
  .icon-quicklink {
    display: inline-block;
    flex: 0 0 12px;
    width: 12px;
    height: 12px;
    background: url(../../assets/images/appCenter/icon-quicklink.png);
    background-size: 100% 100%;
    margin-right: 4px;
  }
  .quick-link-modal {
    position: fixed;
    top: 48px;
    z-index: 20;
    width: 360px;
    height: 208px;
    overflow: auto;
    line-height: 1;
    background: #FFFFFF;
    box-shadow: 0px 2px 14px 1px rgba(15,33,27,0.15);
    border-radius: 4px;
    padding: 5px 0;
    .quick-link-title {
      height: 32px;
      line-height: 32px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 600;
      font-size: 13px;
      color: rgba(0,0,0,0.85);
      padding: 0 18px;
      &::before {
        display: inline-block;
        content: '';
        width: 3px;
        height: 11px;
        background: #505050;
        margin-right: 8px;
      }
    }
    .quick-link-item {
      display: flex;
      align-items: center;
      height: 32px;
      padding: 0 18px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 13px;
      color: rgba(0,0,0,0.85);
      cursor: pointer;
      &:hover {
        background: rgba(3,172,84,0.05);
        color: #03AC54;
      }
      /deep/ .sui-icon {
        margin-right: 2px;
        &:hover {
          background: transparent;
        }
      }
      span {
        display: inline-block;
        max-width: 345px;
        white-space: nowrap;      /* 确保文本在一行内显示 */
        overflow: hidden;         /* 隐藏溢出的内容 */
        text-overflow: ellipsis;  /* 使用省略号表示文本溢出 */
      }
    }
  }
</style>
