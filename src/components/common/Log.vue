<template>
  <div class="log-wrap">
    <p class="title" v-if="showTitle">
      <Icon type="record" color="#03ac54"></Icon>
      <span @click="toggleExpand" >
        单据操作历史
        <Icon type="md-arrow-dropdown" @click.native="toggleExpand" v-show="!expand"></Icon>
        <Icon type="arrow-up-b" @click.native="toggleExpand" v-show="expand"></Icon>
      </span>
    </p>
    <div v-show="expand">
      <table class="log-table">
        <colgroup>
          <col width="0.1%"></col>
          <col width="12%"></col>
          <col width="13.1%"></col>
          <col width="13.1%"></col>
          <col width="13.1"></col>
          <col width="13.1%"></col>
          <col width="13.1%"></col>
          <col width="13.1%"></col>
        </colgroup>
        <thead>
        <th></th>
        <th>操作员</th>
        <th>操作类型</th>
        <th>变更内容</th>
        <th>变更前</th>
        <th>变更后</th>
        <th>操作IP</th>
        <th>时间</th>
        </thead>
      </table>
      <div class="table-div">
        <table class="log-table">
          <colgroup>
            <col width="1%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
            <col width="13%"></col>
          </colgroup>
          <tbody>
          <tr>
            <td colspan="10" v-show="logList == ''" style="text-align: center">暂无数据</td>
          </tr>
          <tr v-for="log in logList">
            <td class="tdi" style="border-right: none !important; ">
              <a class="iconA" @click="more(log)" v-if="log.content_count > itemExpandCount && !log.expand"><Icon type="ios-arrow-forward" size="15"/></a>
              <a class="iconA" @click="more(log)" v-else-if="log.content_count > itemExpandCount && log.expand"><Icon type="ios-arrow-down" size="15"/></a>
            </td>
            <td class="op-name">{{log.operator_name}}</td>
            <td>{{log.opr_type_detail}}</td>
            <td colspan="3" class="sp">
              <Row type="flex" align="middle" v-show="log.content == ''">
                <Col :span="8" class="right-border"></Col>
                <Col :span="8" class="right-border"></Col>
                <Col :span="8" class="w"></Col>
              </Row>
              <Row type="flex" align="middle" class="row" v-for="(content, index) in log.content" :key="index">
                <Col :span="8" class="right-border">{{content.content}}</Col>
                <Col :span="8" class="right-border">{{content.before_update}}</Col>
                <Col :span="8" class="">{{content.after_update}}</Col>
              </Row>
            </td>
            <td>{{log.ip}}</td>
            <td>{{log.opr_time}}</td>
          </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: "log",
    props: {
      url: {
        type: String,
        default: ''
      },
      params: {
        type: Object,
        default: () => {}
      },
      showTitle: {
        type: Boolean,
        default: true
      },
    },
    watch: {
      params: {
        handler () {
          this.getLogList();
        },
        deep: true
      }
    },
    data() {
      return {
        expand: true,
        itemExpandCount: 3,
        selfParams: {},
        logList: [],
      }
    },
    created() {
      // this.getLogList();
    },
    methods: {
      toggleExpand() {
        this.expand = !this.expand;
      },
      getLogList() {
        let url = this.url ? this.url : this.apiUrl.orderLog;
        this.$request.get(url, this.params).then((res) => {
          let {status, data} = res;
          if (status && data) {
            // 后台接口data格式有时候是数据，有时候包裹的是list
            data = data.list ? data.list : data;
            data.forEach((item) => {
              if (item.content_count - this.itemExpandCount > 0) {
                item.hasMore = true;
              } else {
                item.hasMore = false;
              }
              item.expand = false;
            });
            this.logList = data;
          } else {
            this.logList = [];
          }
        });
      },
      more(log){
        log.expand = !log.expand;
        if (!log.hasMore) {
          return false;
        }
        this.$request.get(this.apiUrl.orderLogDetail, {log_id: log.id}).then((res) => {
          let {status, data} = res;
          if (status) {
            log.content = data;
            log.hasMore = false;
          }
        });
      },
    }
  }
</script>

<style lang="less" scoped>
  .log-wrap {
    .title {
      cursor: pointer;
      text-align: left;
      margin-bottom: 15px;
    }
    .log-table {
      width: 100%;
      border-collapse:collapse;
      font-size: 13px;
      tbody {
        height: 100%;
      };
      th {
        padding: 10px 15px;
        background-color: #f5f5f5;
      }
      td {
        padding: 10px 15px;
        border: 1px solid #f5f5f5;
        &.tdi {
          border-right: none !important;
          padding-left: 15px;
        }
        &.op-name {
          border-left: none;
          text-align: left;
        }
        &.sp {
          padding: 0;
        }
      }
      .row {
        border-bottom: 1px solid #f5f5f5;
        .ivu-col {
          padding: 15px;
        }
      }
      .right-border {
        border-right: 1px solid #f5f5f5;
      }
    }
  }
</style>
