<template>
  <Select
    v-model="storeId"
    ref="Select"
    @on-change="updateValue(false)"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
    :placeholder="placeholder"
    :multiple="multiple"
    :max-tag-count="maxTagCount"
    class="store-box"
    :filter-by-label="filterByLabel"
    v-bind="$attrs"
  >
    <Option
      :value="item.id"
      v-for="item in list"
      :key="item.id"
      :label="item.name"
    >
      <template v-if="checkFoodProcess && isOpenProcess && !isCVProcess">{{
        `${item.name}${item.type_name ? `(${item.type_name})` : ''}`
      }}</template>
      <template v-else>{{ item.name }}</template>
    </Option>
  </Select>
</template>

<script>
import store from '@/api/storeRoom.js';
import FoodProcessMixin from '@/mixins/food-process.js';

export default {
  name: 'storeSelect',
  autoRegister: true,
  mixins: [FoodProcessMixin],
  props: {
    storageKey: {
      type: String,
      default: '',
    },
    value: {
      default: '',
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    maxTagCount: {
      type: Number,
      default: 1,
    },
    initNoOnchange: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    defaultFirst: {
      // 默认选中第一个库房
      type: Boolean,
      default: true,
    },
    defaultChange: {
      // 默认选中第一个库房的同时触发on-change事件
      type: Boolean,
      default: true,
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      default: '选择仓库',
    },
    saveSession: {
      type: Boolean,
      default: false,
    },
    // 是否检测食品加工配置，从而显示仓库类型
    checkFoodProcess: {
      type: Boolean,
      default: false,
    },
    filterByLabel: {
      type: Boolean,
      default: false,
    },
    requestParams: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 从外部传入数据
    data: {
      type: Array,
    },
    requestCached: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      defaultStoreId: '',
      storeId: '',
      list: [],
    };
  },
  watch: {
    // value(newValue) {
    //   // 存在本地的不接受外部传入的值
    //   if (!this.storageKey) {
    //     this.storeId = newValue;
    //   }
    // },
    value: {
      immediate: true,
      handler(newValue) {
        // 存在本地的不接受外部传入的值
        if (!this.storageKey) {
          this.storeId = newValue;
        }
      },
    },
    storeId(val) {
      this.$emit('input', val);
    },
    data: {
      deep: true,
      handler() {
        this.getList();
      },
    },
  },
  created() {
    const storageValue = this.storage.getLocalStorage(this.storageKey);
    if (this.storageKey && storageValue) {
      this.storeId = storageValue;
      if (this.defaultChange) {
        this.updateValue(true);
      } else {
        this.$emit('input', this.storeId);
      }
    }
    this.getList();
  },
  methods: {
    getDefaultValue() {
      this.storeId = this.deepClone(this.defaultStoreId);
      this.saveSession && sessionStorage.setItem('storeId', this.storeId);
      return this.storeId;
    },
    getList() {
      // 从外部传入数据
      if (this.data) {
        const list = this.deepClone(this.data);
        if (
          this.showAll &&
          !list.some((item) => item.id === '' && item.name === '全部')
        ) {
          list.unshift({ id: '', name: '全部' });
        }
        this.list = list;
        return;
      }
      this.list = [];
      store
        .getUserWarehouse(this.requestParams, { cache: this.requestCached })
        .then((res) => {
          if (res.status) {
            this.list = res.data;
            const sessionStoreId = sessionStorage.getItem('storeId');
            if (
              this.saveSession &&
              sessionStoreId &&
              sessionStoreId !== 'undefined'
            ) {
              this.storeId = sessionStoreId;
              this.updateValue(true);
            } else if (this.defaultFirst === true && !this.storeId) {
              this.storeId = this.list[0]['id'];
              if (this.defaultChange) {
                this.updateValue(true);
              }
            } else {
              if (this.defaultChange) {
                this.updateValue(true);
              }
            }
            this.defaultStoreId = this.deepClone(this.storeId);

            if (this.showAll) {
              if (
                this.list &&
                this.list.find((v) => v.name === '全部' && v.id === '')
              ) {
                return;
              }
              this.list.unshift({ id: '', name: '全部' });
            }
          }
        });
    },
    updateValue(init) {
      this.storeId = this.storeId || ''; // iView Select选中空字符串值为undefined
      this.saveSession && sessionStorage.setItem('storeId', this.storeId);
      let currentStore = this.list.find((item) => item.id === this.storeId);
      this.$emit('input', this.storeId);
      if (this.storageKey) {
        this.storage.setLocalStorage(this.storageKey, this.storeId);
      }
      if (this.initNoOnchange) {
        if (!init) {
          this.$emit('on-change', this.storeId, currentStore);
        }
      } else {
        this.$emit('on-change', this.storeId, currentStore);
      }
    },
    getSelectedItem() {
      return this.list.find((item) => item.id === this.selfValue);
    },
  },
};
</script>

<style lang="less">
.store-box {
  .ivu-select-selection {
    .ivu-select-selected-value {
      line-height: 30px;
      padding-left: 10px;
      padding-right: 10px;
      color: rgba(0, 0, 0, 0.7);
    }
  }
}
</style>
