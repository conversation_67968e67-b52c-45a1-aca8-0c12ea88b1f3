<template>
  <div class="provider-select">
    <Select
      v-bind="$attrs"
      :transfer="transfer"
      :multiple="multiple"
      :disabled="disabled"
      :max-tag-count="maxTag"
      :filterable="filterable"
      :clearable="filterable"
      :show-all="showAll"
      v-model="providerId"
      @on-change="updateValue"
      :placeholder="placeholder"
    >
      <Option :value="item.id" v-for="item in list" :key="item.id">{{
          item.name
        }}</Option>
    </Select>
  </div>
</template>

<script>
import goods from '@api/goods.js';
export default {
  name: 'storeSelect',
  props: {
    value: {
      default: ''
    },
    // 过滤禁用供应商/采购员
    filterDisable: {
      type: Boolean,
      default: false
    },
    data: {
      type: Array,
      default: () => []
    },
    remote: {
      type: Boolean,
      default: true
    },
    multiple: {
      type: Boolean,
      default: false
    },
    showAll: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    placeholder: {
      default: '选择供应商'
    },
    maxTag: {
      type: Number
    },
    transfer: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    value(newValue) {
      this.providerId = newValue;
    },
    data: {
      deep: true,
      handler(newData) {
        if (!this.remote) {
          this.list = newData;
        }
      }
    }
  },
  data() {
    return {
      providerId: '',
      list: []
    };
  },
  created() {
    this.providerId = this.value;
    if (this.multiple) {
      this.providerId = this.providerId || [];
    }
    if (this.remote) {
      this.getList();
    } else {
      this.list = this.data;
    }
  },
  methods: {
    getList() {
      this.list = [];
      let defaultItem = {
        id: '',
        name: '全部供应商'
      };
      let params = {}
      if (this.filterDisable) {
        params = {
          // filter_disable_provider: 1,
          // filter_disable_agent: 1
        }
      }
      goods.getPurchaseType(params).then(res => {
        if (res.status) {
          this.list = res.data.providers;
        }
        if (this.showAll) {
          this.list.splice(0, 0, defaultItem);
        }
      });
    },
    updateValue() {
      this.$emit('input', this.providerId);
      let currentProvider = this.list.find(item => item.id === this.providerId);
      this.$emit('on-change', currentProvider, this.providerId);
    }
  }
};
</script>

<style lang="less" scoped>
.provider-select {
  display: inline-block;
  /deep/ .ivu-select-multiple .ivu-select-input {
    height: 30px!important;
    line-height: 30px!important;
  }
}
</style>
