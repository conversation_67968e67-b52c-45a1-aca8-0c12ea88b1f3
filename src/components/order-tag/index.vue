<template>
  <CheckboxGroup @on-change="handleChange" v-model="selectedTag">
    <Checkbox v-for="item in tagList" :key="item.id" :label="item.id">
      <span>{{ item.name }}</span>
    </Checkbox>
  </CheckboxGroup>
</template>

<script>
import orderService from "@/service/order";
export default {
  name: "OrderTag",
  autoRegister: true,
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      selectedTag: [],
      tagList: []
    };
  },
  watch: {
    value(newValue) {
      this.selectedTag = newValue;
    },
    selectedTag(value) {
      this.$emit("input", value);
    }
  },
  created() {
    this.selectedTag = this.value;
    this.getData();
  },
  methods: {
    handleChange(...args) {
      this.$emit("on-change", ...args);
    },
    getData() {
      orderService
        .getOrderTag()
        .then(res => {
          let { status, data } = res;
          if (status) {
            this.tagList = data;
          } else {
            this.tagList = [];
          }
        })
        .catch(() => {
          this.tagList = [];
        });
    }
  }
};
</script>

<style scoped></style>
