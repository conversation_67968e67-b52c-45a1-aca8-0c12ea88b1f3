<template>
  <div class="table__selection-panel" v-show="count > 0">
    <Icon
      @click.native="handleClosePanel"
      class="table__selection-panel__icon-close table__selection-panel__icon"
      icon="close"
    />
    <span class="table__selection-panel__selected">已选择{{ count }}项</span>
		<slot name="batch-checked"></slot>
    <slot class="table__selection-panel__extra" name="operation"></slot>
  </div>
</template>

<script>
import Icon from '../../icon';
export default {
  components: {
    Icon
  },
  props: {
    count: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  methods: {
    handleClosePanel() {
      this.$emit('cancel-selected');
    }
  },
  created() {},
  mounted() {}
};
</script>
