import DecimalSettingTable from './src/main';
import { floor, round } from 'lodash-es';

// 格式化精度
export const formatPrecision = (value, mode = LOGIC_ROUND, precision) => {
  if (Number(mode) === Number(LOGIC_ROUND)) {
    return round(value, precision);
  } else {
    return floor(value, precision);
  }
};

// 四舍五入
export const DECIMAL_LOGIC_ROUND = '0';

// 向下取整
export const DECIMAL_LOGIC_FLOOR = '1';

export default DecimalSettingTable;
