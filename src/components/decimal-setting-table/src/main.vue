<template>
  <Modal title="设置" v-model="show" width="800">
    <Form label-colon :label-width="120">
      <!-- 字段名称 -->
      <Table :columns="columns" :data="tableData" border>
        <!-- 小数点位数 -->
        <template #bits="{ row, index }">
          <div v-if="row.bit !== '-' && row.bit !== undefined">
            <RadioGroup
              v-model="row.bit"
              @on-change="handleDecimalChange(row, index)"
            >
              <Radio :label="'0'">0 (整数)</Radio>
              <Radio :label="'1'">0.0 (一位)</Radio>
              <Radio :label="'2'">0.00 (二位)</Radio>
            </RadioGroup>
          </div>
          <div v-else>{{ '' }}</div>
        </template>
        <!-- 取值逻辑 -->
        <template #logics="{ row, index }">
          <RadioGroup
            v-model="row.logic"
            @on-change="handleLogicChange(row, index)"
          >
            <Radio :label="DECIMAL_LOGIC_ROUND">四舍五入</Radio>
            <Radio :label="DECIMAL_LOGIC_FLOOR">向下取整</Radio>
          </RadioGroup>
        </template>
      </Table>
    </Form>
    <div slot="footer">
      <Button type="text" @click="handleCancel">取消</Button>
      <Button type="primary" @click="handleOk">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { DECIMAL_LOGIC_ROUND, DECIMAL_LOGIC_FLOOR } from '../index';
export default {
  name: 'DecimalSettingTable',
  data() {
    return {
      show: false,
      tableData: [
        { fieldName: '发货金额', bit: '-', logic: '' },
        { fieldName: '实付金额', bit: '0', logic: DECIMAL_LOGIC_ROUND },
      ],
      columns: [
        { title: '字段名称', key: 'fieldName' },
        { title: '小数点', key: 'bit', slot: 'bits', width: 300 },
        { title: '模式', key: 'logic', slot: 'logics', width: 250 },
      ],
      DECIMAL_LOGIC_FLOOR,
      DECIMAL_LOGIC_ROUND,
    };
  },
  methods: {
    handleOk() {
      if (this.confirmResolve) {
        this.confirmResolve(this.tableData);
      }
      this.handleCancel();
    },
    open(data) {
      this.tableData = data;
      this.show = true;
      return new Promise((resolve) => {
        this.confirmResolve = resolve;
      });
    },
    handleCancel() {
      this.show = false;
      this.tableData = [
        { fieldName: '发货金额', bit: '-', logic: '0' },
        { fieldName: '实付金额', bit: '0', logic: DECIMAL_LOGIC_ROUND },
      ];
    },
    handleDecimalChange(row, index) {
      // 在这里处理小数点位数的变化
      console.log('小数位数更新：', row.bit);
      this.tableData[index].bit = row.bit;
    },
    handleLogicChange(row, index) {
      // 在这里处理取值逻辑的变化
      console.log('取值逻辑更新：', row.logic);
      this.tableData[index].logic = row.logic;
    },
  },
};
</script>
