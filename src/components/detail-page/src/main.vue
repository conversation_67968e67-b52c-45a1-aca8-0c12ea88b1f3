<!--
 * @Author: <PERSON>
 * @Date: 2021-11-11 15:22:25
 * @LastEditors: hgj
 * @LastEditTime: 2023-05-31 19:47:49
 * @Description: 封装详情页页面级组件

 暂只封装页面头部及底部按钮，中间部分尚未确定固定模板，使用slot插入
 期待后续完善...
-->
<template>
  <div class="sdp-detail-page">
    <div class="sdp-detail-page__hd" v-if="showHeader">
      <base-detail-head :backTxt="backTxt" :backTo="backTo" :title="title">
        <template #right>
          <slot name="head-right"></slot>
        </template>
      </base-detail-head>
    </div>
    <div class="sdp-detail-page__bd" @scroll="onScroll">
      <slot></slot>
    </div>
    <div class="sdp-detail-page__ft" v-if="showFooter">
      <slot name="button-before"></slot>
      <Button @click="_quit" v-if="showCancelBtn">{{ isViewPage ? '返回' : '取消' }}</Button>
      <slot name="button-between"></slot>
      <Button
        v-if="!isViewPage && !customSaveBtn"
        type="primary"
        :disabled="disabledSave"
        @click="_clickSave"
      >{{  saving ? (okText ? okText + '中...' : '保存中...') : (okText || '保存')}}</Button>
      <slot name="button-after"></slot>
    </div>
  </div>
</template>

<script>
import BaseDetailHead from '@/components/base-detail-head/index.js'
import { getMinHeight } from '@/util/index.js'
import { debounce } from 'lodash-es'

export default {
  name: 'DetailPage',
  components: {
    BaseDetailHead
  },
  props: {
    // 页面类型（必传）： 查看、新增、编辑
    pageType: {
      type: String,
      validator (value) {
        return ['view', 'add', 'edit'].includes(value)
      },
      required: true
    },
    // 页面标题
    title: {
      type: String
    },
    // 定义返回行为，可自定义页面路由 / 函数
    backTo: {
      type: [String, Function],
      default: null
    },
    // 是否显示顶部导航栏header
    showHeader: {
      type: Boolean,
      default: true
    },
    showCancelBtn: {
      type: Boolean,
      default: true
    },
    backTxt: {
      type: String,
      default: '返回'
    },
    // 是否显示底部固定操作栏
    showFooter: {
      type: Boolean,
      default: true
    },
    // 自定义保存按钮，为true时将隐藏内置的保存按钮，请使用slot插入
    customSaveBtn: {
      type: Boolean,
      default: false
    },
    // 内置的保存按钮是否为禁用状态
    disabledSave: {
      type: Boolean,
      default: false
    },
    // 是否正在保存
    saving: {
      type: Boolean,
      default: false
    },
    okText: {
      type: String,
      default: '保存'
    }
  },
  computed: {
    isViewPage () {
      return this.pageType === 'view'
    },
    // isAddPage () {
    //   return this.pageType === 'add'
    // },
    // isEditPage () {
    //   return this.pageType === 'edit'
    // }
  },
  created () {
    this.$store.commit("setShowFootLogo", false) // 去掉所有footLogo的显示
  },
  methods: {
    onScroll(e){
      this.$emit('onScroll',e)
    },
    getMinHeight,
    _quit: debounce(function () {
      if ((history.length && history.length <= 1) || this.$route.query.back === 'close') {
        window.close();
      } else {
        this.$router.go(-1)
      }
      // if (window.opener) {
      //   // 如果是新开页面，则关闭当前页面
      //   window.close();
      // } else if (this.$router) {
      //   // 如果可以跳转上一页，则跳转上一页
      //   this.$router.go(-1);
      // } else {
      //   // 其他情况，例如只有一页或无法跳转上一页，可以执行其他操作
      //   window.close();
      //   }
    }, 500, { leading: true, trailing: false }),
    _clickSave: debounce(function () {
      this.$emit('on-save')
    }, 500, { leading: true, trailing: false })
  }
}
</script>
