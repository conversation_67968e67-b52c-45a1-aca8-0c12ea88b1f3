@prefix: sdp-detail-page;

.@{prefix} {
  height: calc(~"100vh - 68px");
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // 暂时没用到的样式先注释

  // .primary-color {
  //   color: var(--primary-color);
  // }
  // .warning-color {
  //   color: #ff9f00;
  // }
  .error-color {
    color: #f13130;
  }
  // .light-color {
  //   color: rgba(0, 0, 0, 0.5);
  // }
  // .text-color {
  //   color: rgba(0, 0, 0, 0.7);
  // }

  // 表单校验错误时的样式
  .error {
    .ivu-input,
    input,
    &.ivu-input-number {
      border-color: #f67f7f !important;
      box-shadow: none !important;
    }
  }
  .ivu-form-item-error {
    .ivu-cascader-label,
    .ivu-select-selection,
    input {
      border-color: #f67f7f !important;
      box-shadow: none !important;
    }
  }

  &__hd {
    height: 40px;
    flex: 0 0 40px;
    padding: 0 24px;
  }

  & &__bd .ivu-form .ivu-form-item-label {
    color: #303030;
  }

  &__bd {
    flex: 1;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 0 24px 20px 24px;
    color: #303030;

    .base-block__hd {
      align-items: center;
      padding-bottom: 12px;
    }

    .base-info,
    .base-info__detail {
      .base-block {
        &__bd {
          font-size: 0; // inline-blick间隙问题导致上下略微不对齐
          margin-bottom: -16px;
        }
      }
    }

    .base-info {
      .base-block {
        &__hd {
          padding-bottom: 12px;
        }
        &__bd {
          .ivu-form-item {
            margin: 0 36px 16px -2px;
          }
        }
      }
      .ivu-radio-wrapper {
        height: 30px;
        margin-right: 24px;
      }
      .ivu-cascader-label {
        line-height: 30px;
      }
    }
    .base-block{
      margin-top: 20px;
    }
    .base-info__detail {
      .base-block {
        &__hd {
          padding-bottom: 12px;
        }
        &__bd {
          .ivu-form-item {
            margin: -6px 5px 10px -2px;
          }
        }
      }
    }

    .ivu-form-inline {
      .ivu-form {
        &-item {
          width: 330px;
          margin: 0 5px 0 -2px;

          &-label {
            font-size: 13px;
            padding: 0 12px 0 0 !important;
            height: 30px;
            line-height: 15px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
          }

          &-required .ivu-form-item-label::before {
            vertical-align: middle;
          }

          &-content {
            font-size: 13px;
            min-height: 30px;
            line-height: normal;
            color: #303030;
            display: flex;
            flex-direction: column;
            justify-content: center;

            .ivu-btn {
              height: 30px;
              line-height: 30px;
              font-size: 13px;
              padding: 0 15px;
              font-weight: normal;
            }

            .ivu-radio-group,
            .ivu-radio-wrapper {
              font-size: 13px;
            }
          }

          &.span-2 {
            width: 640px;
          }

          .remote-more {
            position: absolute;
            right: 0;
            bottom: 0;
          }
        }
      }
    }

    .list-table .before-table-head {
      padding-top: 0;
    }

    .list-table,
    .editable-table,
    .s-vxe-editable-table {
      .sdp-table__container, .s-vxe-table {
        margin-top: 16px;
      }
      .editable-table {
        .sdp-table__container {
          margin-top: 15px;
        }
      }
      .before-table {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 1px;
        box-sizing: border-box;
      }
    }
  }

  &__ft {
    background-color: #fff;
    text-align: center;
    height: 46px;
    flex: 0 0 46px;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9; // 阴影被遮挡问题

    .s-button,
    .ivu-btn {
      margin: 0 6px;
      font-size: 13px;
      height: 30px;
      line-height: 30px;
      padding: 0 14px;
    }
  }

  .ivu-btn {
    font-weight: normal;
  }
}
