import { Dropdown } from 'view-design';
export default {
  extends: Dropdown,
  props: {
    delayTime: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleMouseenter() {
      if (this.trigger === 'custom') return false;
      if (this.trigger !== 'hover') {
        return false;
      }
      if (this.timeout) clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.currentVisible = true;
      }, this.delayTime);
    }
    // handleMouseleave() {
    //   if (this.trigger === 'custom') return false;
    //   if (this.trigger !== 'hover') {
    //     return false;
    //   }
    //   if (this.timeout) {
    //     clearTimeout(this.timeout);
    //     this.timeout = setTimeout(() => {
    //       this.currentVisible = false;
    //     }, this.delayTime);
    //   }
    // }
  }
};
