<template>
  <s-modal ref="modal" :type="type" :title="title" @ok="ok" @quit="quit" async>
    <div class="captcha-submit">
      <p>{{ text }}</p>
      <Input v-model="code" :maxlength="size" />
      <s-captcha v-bind="$attrs" ref="captcha" @change="changeCode" />
    </div>
  </s-modal>
</template>

<script>
// @ is an alias to /src
import { SModal, SCaptcha } from '@sdp/ui';
export default {
  name: 's-captcha-submit',
  components: {
    SModal,
    SCaptcha
  },
  props: {
    type: {
      type: String,
      default: 'warning'
    },
    title: {
      type: String
    },
    text: {
      type: String
    },
    width: {
      type: Number,
      default: 60
    },
    height: {
      type: Number,
      default: 30
    },
    size: {
      type: Number,
      default: 4
    }
  },
  data() {
    return {
      code: null,
      captchaCode: null
    };
  },

  watch: {},
  computed: {},
  methods: {
    open() {
      this.$refs.modal.open();
    },
    changeCode(code) {
      this.captchaCode = code;
    },
    reset() {
      this.code = '';
      this.$refs.captcha.draw();
    },
    ok(resolve) {
      if (this.code && this.code === this.captchaCode) {
        this.reset();
        this.$emit('ok');
        resolve();
      } else {
        this.$refs.captcha.draw();
        this.$smessage({ type: 'error', text: '请输入正确的验证码' });
      }
    },
    quit() {
      this.reset();
      this.$emit('quit');
    }
  }
};
</script>

<style lang="less" scoped>
.captcha-submit {
  margin: 12px 0;
  p {
    margin-bottom: 8px;
  }
  .ivu-input-wrapper {
    width: 200px;
    vertical-align: top;
  }
}
</style>
