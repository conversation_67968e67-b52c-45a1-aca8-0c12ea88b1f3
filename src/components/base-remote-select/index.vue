<template>
  <Select
    remote
    clearable
    filterable
    label-in-value
    :remote-method="remoteMethod"
    :loading="loading"
    :value="value"
    :placeholder="placeholder"
    @on-change="selectChange"
  >
    <Option
      :label="getLabel(item)"
      :key="index"
      v-for="(item, index) in data"
      :value="item[optionValue]"
    >
      <div
        class="option-wrap"
        v-html="renderOption ? renderOption(item) : getLabel(item)"
      ></div>
    </Option>
  </Select>
</template>

<script>
import { get } from '@api/request';
import { debounce } from 'lodash-es';
import Select from '@sdptest/base/lib/select/src/select.js'

export default {
  name: 'base-remote-select',
  components: { Select },
  props: {
    remote: {
      type: [String, Function],
    },
    optionValue: {
      type: String,
      default: 'value',
    },
    optionLabel: {
      type: [String, Function],
      default: 'label',
    },
    params: {
      type: [Function, Object],
    },
    dataKey: {
      type: String,
    },
    renderOption: {
      type: Function,
    },
    value: {
      type: [String, Number],
    },
    placeholder: {
      type: String,
    },
  },
  data() {
    return {
      loading: false,
      data: [],
    };
  },
  created() {
    this.$emit('on-ready', '');
  },
  watch: {},
  computed: {},
  methods: {
    getLabel(item) {
      return typeof this.optionLabel === 'function'
        ? this.optionLabel(item)
        : item[this.optionLabel];
    },
    getMethod(val) {
      // let params =
      //   (typeof this.params === 'function' ? this.params(val) : params) || val;

      if (typeof this.remote === 'string') {
        return get(this.remote, { params: { query: val } });
      }
      if (typeof this.remote === 'function') {
        return this.remote(val);
      }
    },
    remoteMethod: debounce(function(query) {
      if (query !== '') {
        this.loading = true;
        this.getMethod(query).then(res => {
          this.loading = false;
          if (res.status === 1) {
            this.data = this.dataKey ? res.data[this.dataKey] : res.data;
          }
        });
      } else {
        this.data = [];
      }
    }, 300),
    selectChange(item) {
      this.$emit('input', item);
      this.$emit('on-change', item ? item.value : item, item, this.data);
    },
  },
};
</script>
