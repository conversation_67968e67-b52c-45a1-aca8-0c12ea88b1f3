<template>
  <i
    class="sui-icon"
    :style="style"
    :class="['icon-' + icon, 'sui-icon__size--' + size]"
  ></i>
</template>

<script>
// @ is an alias to /src

export default {
  name: 'sui-icon',
  props: {
    icon: {
      type: String,
      required: true
    },
    size: {
      type: [String, Number],
      default: 'md'
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {},
  methods: {
    style() {
      return typeof this.size === 'number' ? `font-size:${this.size}px` : '';
    }
  }
};
</script>

<style lang="less"></style>
