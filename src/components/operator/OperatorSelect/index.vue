<template>
  <CommonSelect
    v-model="selfValue"
    @on-change="onChange"
    :url="apiUrl.getOperatorList"
    :filterable="filterable"
    :multiple="multiple"
    :disabled="disabled"
    :clearable="clearable"
    :show-all="isShowAll"
    label-key="user_name"
    value-key="id"
    :all-label="allLabel"
    :all-value="allValue"
    :placeholder="placeholder">
    <template v-slot="slotScope">{{`${slotScope.item.user_name}（${slotScope.item.cn_name || slotScope.item.user_name}）`}}</template>
  </CommonSelect>
</template>

<script>
  import SelectMixin from '@/mixins/select'
  export default {
    name: "OperatorSelect",
    autoRegister: true,
    mixins: [SelectMixin],
    props: {
      isShowAll: {
        type: Boolean,
        default: false,
      }
    },
    data () {
      return {}
    },
  }
</script>

<style scoped>

</style>
