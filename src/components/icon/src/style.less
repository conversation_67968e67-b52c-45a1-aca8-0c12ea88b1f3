@icon-prefix: sui-icon;

.@{icon-prefix} {
  font-family: "_iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1;
  display: inline-block;
  vertical-align: middle;

  &__size {
    &--md {
      font-size: 26px;
    }

    &--sm {
      font-size: 18px;
    }

    &--mini {
      font-size: 10px;
    }
  }
}

/* 项目业务中使用的特殊标签颜色 */

// 缺
.icon-out-stock {
  color: #f13130;
}
// 首
.icon-first-order {
  color: var(--common-green); // #03ac54 不被替换主题色
}
// 退
.icon-return {
  color: #FF9F00;
}
// 代付
.icon-daifu {
  color: #28A2FF;
}
// 单
.icon-dan {
  color: #00B3A7;
}
// 预
.icon-yu {
  color: #5367FF;
}
// 集
.icon-central {
  color: #0879FF;
}
// 同步
.icon-tongbudingdan {
  color: #0879ff;
}
// 加
.icon-jia {
  color: #00B3A7;
}
// 新
.icon-xin {
  color: var(--common-green);
}
// 替
.icon-ti {
  color: #0879FF;
}
// 协
.icon-xie {
  color: #5367FF;
}
// 变
.icon-bian {
  color: #00B3A7;
}
// 换
.icon-huan {
  color: #FF9F00;
}
// 默
.icon-mo {
  color: var(--common-green);
}
// 下架
.icon-xiajia {
  color: #F13130;
}
// 报价
.icon-baojia {
  color: #0879FF;
}
// 满赠
.icon-manzeng {
  color: #F13130;
}
// 赠品
.icon-zengpin {
  color: #FF9F00;
}
// 阶梯定价
.icon-jietidingjia {
  color: #0879FF;
}
// 未入库
.icon-congweiruku {
  color: #F13130;
}
// 删除
.icon-delete {
  color: #B2B2B2;
}
