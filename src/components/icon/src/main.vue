<!--
 * @Description:
 * @Autor: lizi
 * @Date: 2020-11-25 10:36:29
 * @LastEditors: lizi
 * @LastEditTime: 2021-03-12 15:23:53
 * @FilePath: \sdpbase-pro\src\components\icon\src\main.vue
-->
<template>
  <i v-if="!isSvg" class="sui-icon" :style="style" :class="classes" @click="clickHandle"></i>
	<svg :style="{width, height}" v-else class="icon" aria-hidden="true">
		<use v-bind:xlink:href="`#icon-${icon}`"></use>
	</svg>
</template>

<script>
// @ is an alias to /src

export default {
  name: 'sui-icon',
  props: {
    icon: {
      type: String,
      required: true
    },
		// 是否为svg,适用于使用多色图标
		isSvg: {
			type: Boolean,
			default: false
		},
		width: {
			type: [String, Number],
			default: '1em'
		},
		height: {
			type: [String, Number],
			default: '1em'
		},
    size: {
      type: [String, Number],
      default: 'md'
    }
  },
  data() {
    return {};
  },
  watch: {},
  computed: {
    style() {
      return typeof this.size === 'number' ? `font-size:${this.size}px` : '';
    },
    classes() {
      return [
        'icon-' + this.icon,
        this.style ? '' : 'sui-icon__size--' + this.size
      ];
    }
  },
  methods: {
    clickHandle(e) {
      this.$emit('click', e);
    }
  }
};
</script>

<style lang="less" scoped>
.icon {
	width: 1em; height: 1em;
	vertical-align: -0.15em;
	fill: currentColor;
	overflow: hidden;
}
</style>
