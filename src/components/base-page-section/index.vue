<template>
  <Col :class="$style['section']">
    <Row type="flex" align="middle">
      <Col
        ><span :class="$style.label">{{ label }}</span></Col
      >
      <Col><slot name="right"></slot></Col>
    </Row>
    <slot></slot>
  </Col>
</template>

<script>
export default {
  autoRegister: true,
  name: "BasePageSection",
  components: {},
  props: {
    label: {
      type: String,
      default: ""
    }
  },
  data() {
    return {};
  },
  created() {},
  methods: {}
};
</script>

<style lang="less" module>
@import url("../../assets/less/variable.less");
.label {
  position: relative;
  padding-left: 15px;
  font-size: 16px;
  color: #333;
}
.label:before {
  @width: 5px;
  content: "";
  background-color: @theme-color;
  display: block;
  width: @width;
  height: @width;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 0;
}
</style>
