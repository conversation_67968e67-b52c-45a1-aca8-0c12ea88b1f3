<template>
    <div id="sdp-user-handmade-order">
      <p class="sdp-user-handmade-order-title">
        <span @click="close">
          <Icon
            size="20"
            type="ios-arrow-forward"></Icon>
         </span>
        客户手工单
        <span class="spd-reload" @click="reload" v-show="uploadFile.upyun">重新上传</span>
      </p>
      <Upload
          type = "drag"
          :max-size="2048"
          action="/superAdmin/general/upload"
          :on-success="handleSuccess"
          :on-format-error="handleFormatError"
          :on-exceeded-size="handleMaxSize"
          :show-upload-list="false"
          v-show="!uploadFile.upyun">
        <div style="padding: 20px 0">
          <Icon type="ios-cloud-upload" size="52" style="color: #3399ff"></Icon>
          <p>支持上传、拖拽客户手工单图片</p>
        </div>
        </Upload>
        <div class="upload-wrap" v-show="uploadFile.upyun">
          <img :src="uploadFile.upyun">
        </div>
    </div>
</template>

<script>
  import common from '@api/order.js';
  import '@assets/scss/mixin.scss';

  export default {
    name: 'sdp-user-handmade-order',
    data () {
      return {
        uploadFile: {
          server: '',
          upyun: ''
        }
      }
    },
    created () {

    },
    mounted () {

    },
    methods: {
      handleSuccess(res, file) {
        var self = this;
        if (res.status) {
          var data = res.data;
          self.uploadFile.server = data.server_url;
          self.uploadFile.upyun = data.upyun_url;
        } else {
          self.modalError(res.sMsg);
        }
      },
      handleFormatError(file) {
        this.$Notice.warning({
          title: '文件格式不正确',
          desc: '文件 ' + file.name + ' 格式不正确，请上传 jpg 或 png 格式的图片。'
        });
      },
      handleMaxSize(file) {
        this.$Notice.warning({
          title: '超出文件大小限制',
          desc: '文件 ' + file.name + ' 太大，不能超过 2M。'
        });
      },
      reload: function() {
        this.uploadFile = { server: '', upyun: '' };
      },
      close: function () {
        this.$emit('close');
      }
    }
  }
</script>

<style lang="scss">
  #sdp-user-handmade-order {
    position: fixed;
    z-index: 1999;
    top: 0;
    right: 0;
    width: 500px;
    height: 100%;
    font-size: 16px;
    border-left: 1px solid #EEE;
    background-color: #FFF;
  }

  .sdp-user-handmade-order-title {
    width: 100%;
    padding: 10px;
    text-align: center;
    border-bottom: 1px solid #EEE;
  }
  .spd-reload { float: right; }
  .spd-reload:hover { cursor: pointer; }
  .sdp-user-handmade-order-title i {
    float: left;
  }

  .sdp-user-handmade-order-title i:hover { cursor: pointer; }
  #sdp-user-handmade-order .ivu-upload {
    margin-top: 20%;
    width: 100%;
    display: flex;
    display: -webkit-flex;
    justify-content: center;
    -webkit-justify-content: center;
    align-items: center;
    -webkit-align-items: center;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
  }
  #sdp-user-handmade-order .ivu-upload-drag {
    border: none;
  }
  #sdp-user-handmade-order .ivu-upload-drag:hover {
    border: none;
  }
  #sdp-user-handmade-order .ivu-icon-ios-cloud-upload {
    font-size: 100px !important;
    color: #EEE !important;
  }
  #sdp-user-handmade-order .ivu-upload-drag p {
    color: #CCC;
  }
  .upload-wrap {
    width: 100%;
    height: 100%;
  }
  .upload-wrap > img {
    width: 100%;
    height: auto;
  }
</style>
