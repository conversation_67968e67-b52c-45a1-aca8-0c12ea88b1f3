<template>
  <Select
    ref="productLineSelect"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :clearable="false"
    :disabled="disabled"
    :transfer="true"
    :placeholder="placeholder">
    <Option value="" v-if="showAll">全部</Option>
    <Option :value="item.id" :key="index" v-for="(item, index) in list">{{item.name}}</Option>
  </Select>
</template>

<script>
  export default {
    name: "productLineSelect",
    props: {
      value: {
        default: ''
      },
      placeholder: {
        type: String,
        default: '选择生产车间'
      },
      filterable: {
        type: <PERSON>olean,
        default: false
      },
      remote: {
        type: Boolean,
        default: false
      },
      disabled: {
        type: Boolean,
        default: false
      },
      // 是否启用缓存
      cache: {
        type: Boolean,
        default: false
      },
      transfer: {
        type: Boolean,
        default: false
      },
      showAll: {
        type: Boolean,
        default: true
      },
    },
    data() {
      return {
        selfValue: '',
        list: [],
      }
    },
    created() {
      this.selfValue = this.value;
      this.getList();
    },
    watch: {
      value(val) {
        this.selfValue = val;
      }
    },
    methods: {
      getList() {
        this.list = [];
        let params = {
          pageSize: 99999
        };
        if (this.cache) {
          // 启用缓存时优先从缓存中获取数据
          this.list = this.getDataFromCache();
          if (this.list && this.list.length > 0) {
            return true;
          }
        }
        this.$request.get(this.apiUrl.process.productLine.list, params).then((res) => {
          let {status, data} = res;
          if (status) {
            this.updateCache(data.list);
            this.list = data.list;
          } else {
             this.list = [];
          }
        });
      },
      /**
       * 启用缓存时从缓存中获取数据
       */
      getDataFromCache() {
        return this.$store.state[this.globalConfig.store.stateKey.productLineList];
      },
      /**
       * 启用缓存时更新缓存中的数据
       */
      updateCache(data) {
        this.$store.commit(this.globalConfig.store.mutationKey.updateProductLineList, data);
      },
      updateValue() {
        this.$emit('input', this.selfValue);
        this.$emit('on-change', this.selfValue);
      }
    }
  }
</script>

<style scoped>

</style>
