<template>
  <Select
    ref="workerSelect"
    v-model="selfValue"
    @on-change="updateValue"
    :filterable="filterable"
    :transfer="true"
    :clearable="false"
    :disabled="disabled"
    :placeholder="placeholder"
  >
    <Option value="">全部</Option>
    <Option :value="item.id" :key="index" v-for="(item, index) in list">{{
      item.name
    }}</Option>
  </Select>
</template>

<script>
export default {
  name: 'workerSelect',
  props: {
    value: {
      default: ''
    },
    placeholder: {
      type: String,
      default: '选择生产工人'
    },
    filterable: {
      type: Boolean,
      default: false
    },
    remote: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    firstDefault: {
      default: 0
    }
  },
  data() {
    return {
      selfValue: '',
      list: [],
    };
  },
  created() {
    this.selfValue = this.value;
    this.getList();
  },
  watch: {
    value(val) {
      this.selfValue = val;
    }
  },
  methods: {
    getList() {
      this.list = [];
      let params = {
        pageSize: 99999
      };
      this.$request.get(this.apiUrl.process.worker.list, params).then(res => {
        let { status, data } = res;
        if (status) {
          this.list = data.list;
          if (this.firstDefault === 1) {
            this.selfValue = data.list[0].id;
            this.updateValue();
          }
        } else {
          this.list = [];
        }
      });
    },
    updateValue() {
      this.$emit('input', this.selfValue);
      this.$emit('on-change');
    }
  }
};
</script>

<style scoped></style>
