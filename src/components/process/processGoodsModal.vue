<!--
 * @Descripttion: 添加加工商品
 * @url: http://0.0.0.0:8089/#/process/processOrder/edit
-->

<template>
  <div>
    <Modal
      v-model="modal.show"
      @on-cancel="handleCancel"
      :title="title"
      :mask-closable="false"
      :closable="closable"
      :class-name="modal.className"
      width="1100">
      <Row class="selectRow mb10" type="flex" justify="start" :gutter="10">
        <Col span="24">
          <category-select @on-change="changeCategory" class="category"></category-select>
          <Input class="search-word" placeholder="请输入商品名称/编码进行搜索" v-model="filters.no" @on-enter="loadGoodsList(1)" clearable>
            <Button slot="append" icon="ios-search" @click="loadGoodsList(1)"></Button>
          </Input>
        </Col>
      </Row>
      <Table
        :columns="cols"
        :data="goodsList"
        width="1070"
        ref="goodsTable"
        :loading="loading"
        :height="tableHeight"
        border
        @on-row-click="selectGoods"
        :row-class-name="rowClassName"
        @on-select="checkBoxCheck"
        @on-select-cancel="checkBoxCancel"
        @on-select-all="selectAll"
        @on-selection-change="cancelAll"></Table>
      <Page
        :total="totalPage"
        :current='currentPage'
        :page-size='pageSize'
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        placement="top"
        show-elevator
        show-total
        show-sizer>
      </Page>
      <div slot="footer">
        <Button   @click="handleCancel">{{cancelText}}</Button>
        <Button  type="primary"  @click="addConfirm">{{confirmText}}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
  import categorySelect from '@components/common/categorySelect';
  const LIST_KEY = 'commodity_id';
  export default {
    name: "goods-list-modal",
    components: {
      categorySelect
    },
    props: {
      show: {
        type: Boolean,
        default: false
      },
      /**
       * 是否显示多选
       */
      showSelect: {
        type: Boolean,
        default: true
      },
      showFooter: {
        type: Boolean,
        default: true
      },
      closable: {
        type: Boolean,
        default: true
      },
      title: {
        type: String,
        default: '请选择商品'
      },
      cancelText: {
        type: String,
        default: '取 消'
      },
      confirmText: {
        type: String,
        default: '确 认'
      },
      // 默认选中的上商品
      defaultSelectedGoods: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      columns: {
        type: Array,
        default: () => []
      },
      /**
       * 表格字段
       */
      filterParams: {
        type: Object,
        default: () => {}
      }
    },
    watch: {
      show(newValue, oldValue) {
        this.modal.show = newValue;
        if (newValue) {
          this.loadGoodsList();
        }
      }
    },
    data() {
      return {
        modal: {
          show: false,
          className: 'vertical-center-modal goods-modal'
        },
        tableHeight: this.getTableHeight() * 0.8,
        filters: {
          no: '',
          category1: '',
          category2: '',
        },
        goodsList: [],
        categoryList: [],
        selectedRow: [],
        loading: false,
        selectedGoods: [],
        totalPage: 0,
        currentPage: 1,
        pageSize: 10,
        cols: [
          {
            type: 'selection',
            width: 60,
            align: 'left'
          },
          {
            title: "商品名称",
            key: "name",
          },
          {
            title: "商品分类",
            render: (h, parmas) => {
              let {row} = parmas;
              return h('span', `${row.category1}／${row.category2}`);
            }
          },
          {
            title: "单位",
            key: "unit",
          },
          {
            title: "描述",
            key: "summary",
          },
          {
            title: "最近一次进价",
            key: "in_price",
          },
          {
            title: "市场价",
            key: "price",
          }
        ],
      }
    },
    created() {
      this.modal.show = this.show;
      if (this.show) {
        this.loadGoodsList();
      }
      if (!this.showFooter) {
        this.modal.className += ' hide-footer';
      }
      if (this.columns && this.columns.length > 0) {
        this.cols = this.columns;
      }
    },
    methods: {
      //全选取消状态处理
      cancelAll(selection) {
        if (this.addedNum === selection.length) {
          this.selectedRow.forEach((item, index) => {
            if (this.selectedGoods.length) {
              this.selectedGoods.forEach((_item, _index) => {
                if (item[LIST_KEY] === _item[LIST_KEY] && !item._disabled) {
                  this.selectedGoods.splice(_index, 1);
                }
              });
            }
          });
        }
      },
      // checkbox全选中状态
      selectAll(selection) {
        let me = this;
        this.selectedRow.forEach(item => {
          let selected = false;
          if (this.selectedGoods.length) {
            this.selectedGoods.forEach((_item, index) => {
              if (item[LIST_KEY] === _item[LIST_KEY]) {
                selected = true;
              }
            });
            setTimeout(function () {
              if (!selected && !item._disabled) {
                me.selectedGoods.push(item);
              }
            }, 100);
          } else {
            if (!selected && !item._disabled) {
              me.selectedGoods.push(item);
            }
          }
        });
      },
      // CheckBox 单个取消状态
      checkBoxCancel(selection, row) {
        if (this.selectedGoods.length) {
          this.selectedGoods.forEach((item, index) => {
            if (item[LIST_KEY] === row[LIST_KEY]) {
              this.selectedGoods.splice(index, 1);
            }
          });
        }
      },
      //CheckBox单个选中种状态
      checkBoxCheck(selection, row) {
        let index = this.getIndex(row);
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        if (!checked) {
          this.selectedGoods.push(this.selectedRow[index]);
        }
      },
      //获取行所在的索引
      getIndex(row) {
        let idx = '';
        this.goodsList.forEach((item, index) => {
          if (item[LIST_KEY] === row[LIST_KEY]) {
            idx = index;
          }
        });
        return idx;
      },
      // 单击行状态处理
      selectGoods(row, index) {
        if (!this.showSelect) {
          return;
        }
        if (row._disabled) return;
        let bodyDom = this.$refs.goodsTable.$el.querySelector('.ivu-table-tbody');
        let selectRow = bodyDom.querySelectorAll('.ivu-table-row')[index];
        let checked = selectRow.querySelector('.ivu-checkbox-checked');
        selectRow.querySelector('.ivu-checkbox-wrapper').className =
          checked ? "ivu-checkbox-wrapper" : "ivu-checkbox-wrapper ivu-checkbox-wrapper-checked";
        selectRow.querySelector('.ivu-checkbox').className = checked ? "ivu-checkbox" : "ivu-checkbox ivu-checkbox-checked";
        if (!checked) {
          this.selectedGoods.push(this.selectedRow[index]);
        } else {
          if (this.selectedGoods.length) {
            this.selectedGoods.forEach((item, index) => {
              if (item[LIST_KEY] === row[LIST_KEY]) {
                this.selectedGoods.splice(index, 1);
              }
            });
          }
        }
      },
      rowClassName (row, index) {
        if (row._disabled) {
          return 'table-close-row';
        }
        return 'normalRow';
      },
      changePage(pageNo) {
        this.currentPage = pageNo;
        this.loadGoodsList();
      },
      changePageSize(size) {
        this.pageSize = size;
        this.loadGoodsList();
      },
      changeCategory(value) {
        this.currentPage = 1;
        this.filters.category1 = value[0];
        this.filters.category2 = value[1];
        this.loadGoodsList();
      },
      loadGoodsList(page) {
        this.loading = true;
        let params = {
          page: this.currentPage,
          pageSize: this.pageSize,
          query: this.filters.no, //搜索关键词
          category_id: this.filters.category1, //一级分类id
          category_id2: this.filters.category2, //二级分类id
          showAll: true,
          ...this.filterParams
        };
        if (page) {
          params.page = page;
        }
        this.$request.get(this.apiUrl.process.goodsBoom.list, params).then((res) => {
          this.loading = false;
          this.goodsList = [];
          let {status, data} = res;
          if (!status) {
            this.selectedRow = [];
            this.currentPage = 1;
            this.totalPage = 0;
          }
          let Goods = data.list.map(item => {
            if (this.defaultSelectedGoods.length > 0) {
              item._checked = false;
              item._disabled = false;
              this.defaultSelectedGoods.forEach(d => {
                if (item[LIST_KEY] === d[LIST_KEY] || item[LIST_KEY] === d.commodity_id) {
                  item._checked = true;
                  item._disabled = true;
                }
              });
            }
            return item;
          });
          // 已经选择的置顶
          Goods.forEach(item => {
            // 筛选已经选取的商品
            this.selectedGoods.forEach(order => {
              if (item[LIST_KEY] === order[LIST_KEY]) {
                item._checked = true;
              }
            });
            if (item._disabled) {
              this.goodsList.unshift(item);
            } else {
              this.goodsList.push(item);
            }
          });
          this.selectedRow = this.cloneObj(this.goodsList);
          this.currentPage = parseInt(data.pageParams.page);
          this.totalPage = parseInt(data.pageParams.count);
        });
      },
      handleCancel() {
        this.$emit('on-cancel');
      },
      addConfirm() {
        if (this.selectedGoods.length) {
          this.$emit('on-confirm', this.selectedGoods);
        } else {
          this.modalError('请选择需要添加的商品');
        }
      }
    }
  }
</script>

<style lang="less">
  .ivu-modal-wrap {
    &.hide-footer {
      .ivu-modal-footer {
        display: none;
      }
    }
  }
  .goods-modal {
    .category {
      display: inline-block;
      width: 170px;
    }
    .search-word {
      width: 240px;
    }
  }
</style>
<style lang="less" scoped>
  ._tips {
    text-align: center;
    line-height: 30px;
    color: #03ac54;
  }
</style>
