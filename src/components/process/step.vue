<template>
  <div class="process-step">
    <Row type="flex" align="middle" class="steps">
      <Col v-for="(step, index) in steps" :key="index" class="step" :class="{active: index == current, done: index < current}">
        <Row type="flex" align="middle" justify="center">
          <Col class="icon" v-if="index !== steps.length - 1">{{index + 1}}</Col>
          <Col class="icon" v-if="index === steps.length - 1"><Icon type="ios-checkmark-empty"></Icon></Col>
          <Col class="title">{{step.title}}</Col>
        </Row>
      </Col>
    </Row>
  </div>
</template>

<script>
  export default {
    name: "processStep",
    props: {
      isEditProcessOrder: {
        type: Boolean,
        default: false
      },
      current: {
        type: Number,
        default: 0
      }
    },
    data() {
      return {
        steps: [
          {
            title: '新增加工单'
          },
          {
            title: '领料'
          },
          {
            title: '完成入库'
          },
          {
            title: '标记完成'
          },
          {
            title: '退料'
          },
          {
            title: '加工完成'
          }
        ]
      }
    },
    watch: {
      isEditProcessOrder(value) {
        this.setStepsTitle();
      }
    },
    created() {
      this.setStepsTitle();
    },
    methods: {
      setStepsTitle() {
        this.steps[0].title = this.isEditProcessOrder ? '编辑加工单' : '新增加工单';
      }
    }
  }
</script>

<style lang="less" scoped>
  .process-step {
    @activeColor: #03ac54;
    border-bottom: 2px solid #dedede;
    text-align: center;
    .steps {
      width: 1000px;
      margin: 0 auto;
      .step {
        width: 16.66666%;
        padding-bottom: 10px;
        @iconWidth: 25px;
        .icon {
          background: rgb(204, 204,204);
          width: @iconWidth;
          height: @iconWidth;
          line-height: @iconWidth;
          border-radius: 50%;
          color: #fff;
          font-size: 14px;
          margin-right: 10px;
          .ivu-icon {
            font-size: 24px;
          }
        }
        &.active {
          border-bottom: 2px solid @activeColor;
          margin-bottom: -2px;
          color: @activeColor;
          .icon {
            background: @activeColor;
          }
        }
        &.done {
          .icon {
            background: @activeColor;
          }
          color: @activeColor;
        }
      }
    }
  }
</style>
