/**
 * Created by ddcoder on 2019/7/2.
 */
export default {
  data () {
    return {
      filterGoodsList: [],
      goodsFilters: {
        category_id: '',
        category_id2: '',
        search_value: '',
      }
    }
  },
  methods: {
    /**
     * @param config {list}
     * @param config {category_id_key}
     * @param config {category_id2_key}
     * @param config {goods_name_key}
     * @param config {goods_code_key}
     */
    _filterGoodsList (config) {
      config = config || {};
      let filters = this.goodsFilters;
      let category_id_key = config.category_id_key || 'category_id';
      let category_id2_Key = config.category_id2_key || 'category_id2';
      let goods_name_key = config.goods_name_key || 'commodity_name';
      let goods_code_key = config.goods_code_key || 'commodity_code';
      let list = config.list || [];
      list = this.deepClone(list);
      if (filters.category_id) {
        list = list.filter(item => item[category_id_key] === filters.category_id);
      }
      if (filters.category_id2) {
        list = list.filter(item => item[category_id2_Key] === filters.category_id2);
      }
      if (filters.search_value) {
        list = list.filter(item => {
          item[goods_name_key] = item[goods_name_key] || '';
          item[goods_code_key] = item[goods_code_key] || '';
          return item[goods_name_key].includes(filters.search_value) ||
            item[goods_code_key].includes(filters.search_value);
        });
      }
      this.filterGoodsList = list;
    }
  }
}
