<template>
  <Cascader
    v-bind="$attrs"
    :data="areaData"
    v-model="area"
    @on-change="onChange"
  ></Cascader>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(val) {
        this.area = val || [];
      }
    }
  },
  data() {
    return {
      area: [],
      areaData: [],
    }
  },
  methods: {
    getFirstTwoLevels(data) {
      data.forEach(item => {
        if (item.children && item.children.length) {
          item.children.forEach(cItem => {
            if (cItem.children) {
              delete cItem.children;
            }
          })
        }
      })
    },
    getCityData() {
      this.$request.get(this.apiUrl.baseApi.getCityData).then(res => {
        this.getFirstTwoLevels(res || [])
        this.areaData = res;
      });
    },
    onChange(val, rows) {
      this.$emit('input', val);
      this.$emit('on-change', val, rows);
    },
  },
  created() {
    this.getCityData();
  }
}
</script>
