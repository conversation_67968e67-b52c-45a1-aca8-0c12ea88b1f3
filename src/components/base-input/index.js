import { Input } from "view-design";

/**
 * 继承Input组件解决内存泄露问题
 */
export default {
  extends: Input,
  methods: {
    destroyInput() {
      this._vnode.children.forEach(vnode => {
        if (
          (vnode.tag === "input" || vnode.tag === "textarea") &&
          vnode.data.on
        ) {
          Object.keys(vnode.data.on).forEach(event => {
            vnode.elm.removeEventListener(event, vnode.data.on[event]._wrapper);
          });
        }
        if (vnode.elm.remove) {
          vnode.elm.remove();
        }
      });
    }
  },
  beforeDestroy() {
    this.destroyInput();
    if (this.$el && this.$el.remove) {
      this.$el.remove();
    }
  }
};
