<template>
  <div @keydown="handleKeydown">
    <Select
      class="sdp-base-select"
      ref="select"
      :loading="loading"
      :not-found-text="loaded ? '暂无数据' : ''"
      label-in-value
      @on-open-change="handleOnOpenChange"
      @on-change="handleChange"
      @on-query-change="handleSearch"
      v-bind="{
        'max-tag-count': 1,
        clearable: true,
        filterable: true,
        ...$attrs
      }"
      v-on="$listeners"
    >
    <!-- v-if="visible" -->
      <template>
        <VirtualList
          ref="virtualList"
          style="max-height: 200px; overflow-y: auto;"
          class="scroll-wrap"
          data-key="value"
          :data-sources="filterOptionsList"
          :data-component="optionItems"
          :estimate-size="31"
          :keeps="20"
          :extra-props="{
            value: selfValue,
            preValue: preValue
          }"
        >
        </VirtualList>
        <Option disabled v-if="loaded && !optionsList.length" value="empty">暂无数据</Option>
        <Option disabled v-else-if="keywords && !filterOptionsList.length" value="empty">无匹配数据</Option>
      </template>
    </Select>
  </div>
</template>

<script>
import VirtualList from 'vue-virtual-scroll-list'
import item from './components/item'

export default {
  components: {
    VirtualList,
    item
  },
  props: {
    value: {
      default: '',
    },
    apiName: {
      type: String,
      default: '',
    },
    defaultValue: {
      type: String,
      default: ''
    },
    labelKey: {
      type: String,
    },
    valueKey: {
      type: String,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    defaultOptions: {
      type: Array,
      default: () => []
    },
    responseParse: {
      type: Function,
      default: (res) => {
        return res
      },
    },
  },
  watch: {
    value(val) {
      // 重置
      if (!val) {
        const { defaultFirst } = this.$attrs
        if (defaultFirst) {
          const { value } = this.handleSetFirst()
          this.$emit('on-update', value);
          return
        }
        this.keywords = ''
        // 会触发change事件, 此时selectToSearch为true, 再次搜索会被阻断
        this.handleSetDetault([])
        this.$nextTick(() => {
          this.selectToSearch = false
        })
      } else if (this.defaultValue === val) {
        // 有默认值且重置的情况下
        const value = this.handleGetDefault()
        this.handleSetDetault(value)
      }
    }
  },
  computed: {
    filterOptionsList() {
      const keywords = this.keywords ? this.keywords.toLowerCase() : '';
      const list = this.optionsList.filter(k =>
        !keywords ||
        k.label.toLowerCase().includes(keywords) ||
        k.value.toLowerCase().includes(keywords)
      );
      return list;
    },
    preValue() {
      const preValue = this.filterOptionsList[this.activeIndex]
      if (!preValue) return null
      return preValue
    }
  },
  data() {
    return {
      activeIndex: 0, // 当前选中项索引
      selfValue: null,
      selected: false,
      keywords: '',
      loaded: false,
      visible: false,
      loading: false,
      // 选中触发search
      selectToSearch: false,
      // 是否首次触发on-change
      isFirstChange: false,
      optionsList: [],
      optionItems: item,
    };
  },
  methods: {
    handleKeydown(event) {
      const { key } = event

      if (!this.visible) {
        if (key === 'ArrowDown' || key === 'ArrowUp') this.$refs.select.visible = true
        return
      }

      const lastIndex = this.filterOptionsList.length - 1

      // 上键：选中上一个
      if (key === 'ArrowUp') {
        const index = Math.max(this.activeIndex - 1, 0)

        // 如果一致的话, 回滚到最后一个
        this.activeIndex = index === this.activeIndex ? lastIndex : index
      }

      // 下键：选中下一个
      if (key === 'ArrowDown') {
        const index = Math.min(this.activeIndex + 1, lastIndex)

        // 如果一致的话, 回滚到第一个
        this.activeIndex = index === this.activeIndex ? 0 : index
      }

      // 回车
      if (key === 'Enter') {
        const { multiple } = this.$attrs
        const { label, value } = this.preValue
        let select = [{ label, value }]

        // 如果是多选, 把之前选中的值累加进去
        if (multiple) {
          const selected = this.cloneObj(this.$refs.select.values)
          // 是否存在
          const selectedIndex = selected.findIndex(k => k.value === value)
          if (selectedIndex > -1) {
            selected.splice(selectedIndex, 1)
            select = []
          } 

          select = [...selected, ...select]
        }
        this.handleSetDetault(select)
        
        // 关闭下拉
        if (!multiple) {
          this.$refs.select.visible = false
        }
      }
      // 滚动
      this.$refs.virtualList.scrollToIndex(this.activeIndex - 5)
    },
    handleSearch(e) {
      // 选中后会触发search, 判断下
      if (this.selectToSearch) {
        this.selectToSearch = false
        return
      }

      this.activeIndex = 0
      this.keywords = e.trim() // 去掉首位空格
    },
    handleChange(value) {
      const { multiple } = this.$attrs

      // filterable: true 的影响
      // iView 的 Select 组件在 filterable: true 时，默认会监听 input 事件触发搜索，即使 on-change 事件改变了 value，也可能导致 search 事件触发。
      if (!multiple) {
        // 多选选中不会触发search事件
        this.selectToSearch = true
      }

      this.selfValue = value

      // 选中下标
      const index = !value ? 0 : this.filterOptionsList.findIndex(k => k.value === value.value)
      if (index !== -1) {
        this.activeIndex = index
      }

      // 上报数据
      let emitValue

      // 如果 value 是对象
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        emitValue = value.value
      }
      // 如果 value 是数组
      if (Array.isArray(value)) {
        emitValue = value.map(val => val.value)
      }

      // 有默认值首次触发不上报
      if (this.isFirstChange && this.$refs.select.values) {
        this.isFirstChange = false
        return
      }
      this.$emit('on-update', emitValue);
    },
    handleOnOpenChange(isOpen) {
      this.visible = isOpen

      if (isOpen) {
        // 置空keywords, 还原初始数据继续供用户去选择
        this.keywords = ''

        if (!this.optionsList.length && !this.loaded) {
          this.handleGetOptionsList()
        }
      }
    },
    async handleGetOptionsList() {
      console.log('handleGetOptionsList')
      this.loading = true;
      const res = await this.$request
        .get(this.apiName, this.params)

        if (!res.status) {
          this.$Message.error(res.message)
          return
        }

      const parseLabelValue = (list) =>{
        const template = this.labelKey

        const getLabel = (val) => {
          if (template.includes('$')) {
            return template.replace(/\$(\w+)/g, (_, key) => val[key] || '')
          }
          return val[template]
        }

        list.map(val => {
          val.label = getLabel(val)
          val.value = `${val[this.valueKey]}`
        })

        return list
      }
      const { list } = this.responseParse(res.data)
      console.log('defaultOptions', this.defaultOptions);
      this.optionsList = [...this.defaultOptions, ...parseLabelValue(list)]

      this.loading = false
      this.loaded = true
    },
    handleGetDefault(defaultValue = this.defaultValue) {
      const rows = this.optionsList.filter(k => defaultValue.includes(k.value))
      const value = rows.map(({ label, value }) => {
        return {
          label,
          value,
        }
      })
      return value
    },
    handleSetDetault(value) {
      this.$refs.select.values = value
    },
    handleSetFirst() {
      const { label, value } = this.optionsList[0]
      this.handleSetDetault([{ label, value }])
      return { label, value }
    },
    async handleSetInit() {
      const { defaultFirst } = this.$attrs

      // 默认选中值
      if (this.defaultValue) {
        this.isFirstChange = true
        await this.handleGetOptionsList()
        const value = this.handleGetDefault()
        this.handleSetDetault(value)
      }
      // 默认选中第一个值
      else if (defaultFirst) {
        await this.handleGetOptionsList()
        this.handleSetFirst()
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.select) {
        // 关键, 重置内置的搜索与键盘事件
        this.$refs.select.validateOption = () => true;
        this.$refs.select.handleKeydown = () => {}

        this.handleSetInit()
      }
    })
  },
};
</script>

<style lang="less" scoped>
/* 可根据需求调整虚拟列表和 Option 样式 */
.sdp-base-select {
  &.ivu-select-multiple {
    /deep/ .ivu-select-selection > div {
      display: flex;
      align-items: center;
      width: 100%;
      overflow: hidden;
      .ivu-tag {
        flex-shrink: 0;
      }
      .ivu-select-input {
        flex: 1 1;
        width: inherit !important;
        padding-right: 5px;
        padding-left: 10px;
        line-height: 28px;
      }
    }
  }
  /deep/ .ivu-select-dropdown {
    overflow: hidden;
    max-height: unset;
    max-width: 232px;
    z-index: 99;
    .scroll-wrap {
      &::-webkit-scrollbar {
        width: 10px;
        height: 10px;
        display: block;
        position: absolute;
      }
      &::-webkit-scrollbar-thumb {
        box-shadow: inset 0 0 30px rgba(0, 0, 0, 0.3);
        border: 2px solid transparent;
        border-radius: 10px;
      }
    }
  }
}
</style>
