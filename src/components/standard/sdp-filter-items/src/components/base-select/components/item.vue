<template>
  <!-- 选中样式需要手动加上 -->
  <Option 
    :value="source.value"
    :class="{
      'ivu-select-item-selected ivu-select-item-focus': handleIsActive(source.value, value),
       'ivu-select-item-focus': handleIsActive(source.value, preValue)}">{{ source.label }}</Option>
</template>

<script>

export default {
  props: {
    // data-sources中的数据对象
    source: {
      type: Object,
      default () {
        return {}
      }
    },
    value: {
      type: [Object, Array, String, null],
      default: null,
    },
    preValue: {
      type: [Object, Array, String, null],
      default: null,
    },
  },
  methods: {
    handleIsActive(value, selected) {
      // 如果 value 是对象
      if (selected && typeof selected === 'object' && !Array.isArray(selected)) {
        return selected.value === value;
      }
      // 如果 value 是数组
      if (Array.isArray(selected)) {
        return selected.some((item) => item.value === value);
      }
      // 如果 value 是字符串
      return selected === value;
    },
  },
  mounted() {
  }
}
</script>

<style lang="less" scoped>
.ivu-select .ivu-select-item-focus {
  background: rgba(246, 248, 249, 0.8) !important;
}
</style>