<template>
  <sdpCheckbox
    :apiName="apiUrl.getUserTagList"
    :value="value"
    :defaultValue="defaultValue"
    storageKey="checkboxExpandStateUserTag"
    @on-update="handleUpdate"
    v-bind="$attrs">
  </sdpCheckbox>
</template>

<script>
import sdpCheckbox from './base-checkbox/index';

export default {
  name: 'userTag',
  components: {
    sdpCheckbox
  },
  props: {
    value: {
      default: '',
    },
    defaultValue: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleUpdate(value) {
      this.$emit('input', value);
      this.$emit('on-change', value);
    }
  },
}
</script>
<style lang="less"></style>
