<template>
  <div class="base-checkbox">
    <span class="loading" v-if="!loaded">加载中...</span>
    <div class="checkbox-container" ref="checkboxContainer">
      <CheckboxGroup 
        v-model="selected" 
        @on-change="handleChange" 
        class="tagCheckbox" 
        :class="{ 'collapsed': isCollapsed && hasMultipleLines }" 
        ref="checkboxGroup">
        <Checkbox 
          v-for="(item, index) in data" 
          :label="item[valueKey]" 
          :key="index" 
          :disabled="handleGetDisabledState(item)">
          <span>{{ item.name }}</span>
        </Checkbox>
      </CheckboxGroup>
      <span 
        v-if="hasMultipleLines && loaded" 
        class="expand-collapse" 
        @click="handleToggleCollapse"
      >
        <Icon :icon="isCollapsed ? 'zhankai3' : 'shouqi2'" size="16" />
      </span>
    </div>
  </div>
</template>

<script>
import Bus from '@api/bus'
import { Checkbox, CheckboxGroup } from 'view-design'
import { isElementInViewport } from '../../utils/helper'
import { debounce } from 'lodash'
import Icon from '@/components/icon'

export default {
  components: {
    Icon,
    Checkbox,
    CheckboxGroup
  },
  props: {
    value: {
      default: '',
    },
    defaultValue: {
      type: String,
      default: ''
    },
    apiName: {
      type: String,
      default: ''
    },
    keyName: {
      type: String,
      default: ''
    },
    valueKey: {
      type: String,
      default: 'id'
    },
    // localStorage存储键名
    storageKey: {
      type: String,
      default: 'checkboxExpandState'
    },
    // 是否在可视区域内加载接口, 使用列表中的高级筛选展开加载
    isVisibleArea: {
      type: Boolean,
      default: true
    },
    // 上报是不是数组类型
    emitArray: {
      type: Boolean,
      default: false
    },
    // 外部给进来的items, 兼容之前老代码, 减少改造成本
    checkboxItems: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabled: {
      type: [Boolean, Function], // 支持布尔值或函数
      default: false
    }
  },
  data() {
    return {
      loaded: false,
      data: [],
      selected: [],
      hasMultipleLines: false,
      isCollapsed: true, // 默认设置为收起状态
    };
  },
  inject: {
    handleUpdateFilterCol: { default: () => () => {} }
  },
  watch: {
    value: {
      handler (val) {
        this.handleInitValue(val)
      }
    },
    data() {
      this.$nextTick(() => {
        this.handleCheckMultipleLines()
      });
    },
    checkboxItems(val) {
      this.data = val
    }
  },
  computed: {},
  methods: {
    // 新增方法：获取存储的展开状态
    getStoredCollapseState() {
      try {
        const state = JSON.parse(localStorage.getItem(this.storageKey)) || {};
        const route = this.$route ? this.$route.path : window.location.pathname;
        return state[route] !== undefined ? state[route] : true; // 默认收起
      } catch (e) {
        return true; // 出错时默认收起
      }
    },
    // 新增方法：保存展开状态
    saveCollapseState() {
      try {
        const route = this.$route ? this.$route.path : window.location.pathname;
        const state = JSON.parse(localStorage.getItem(this.storageKey)) || {};
        state[route] = this.isCollapsed;
        localStorage.setItem(this.storageKey, JSON.stringify(state));
      } catch (e) {
        console.error('保存展开状态失败:', e);
      }
    },
    // 新增方法：获取禁用状态
    handleGetDisabledState(item) {
      if (typeof this.disabled === 'function') {
        // 如果是函数，传入当前item让调用方决定禁用状态
        return this.disabled(item)
      }
      // 否则直接返回布尔值或item自身的disabled状态
      return this.disabled || (item && item.disabled)
    },
    handleCheckMultipleLines() {
      this.$nextTick(() => {
        const container = this.$refs.checkboxContainer
        const checkboxGroup = this.$refs.checkboxGroup
        
        if (!container || !checkboxGroup) {
          return
        }
        
        const checkboxes = checkboxGroup.$el.querySelectorAll('.ivu-checkbox-wrapper')
        
        if (checkboxes.length === 0) {
          return
        }
        
        const firstTop = checkboxes[0].offsetTop
        const lastTop = checkboxes[checkboxes.length - 1].offsetTop
        
        this.hasMultipleLines = firstTop < lastTop
        
        if (!this.hasMultipleLines) {
          this.isCollapsed = false
        }
      });
    },
    hanldeInitResizeObserver() {
      window.addEventListener('resize', debounce(this.handleCheckMultipleLines, 100))
    },
    handleToggleCollapse() {
      this.isCollapsed = !this.isCollapsed
      this.saveCollapseState()
    },
    handleChange() {
      this.$emit('on-update', this.emitArray ? this.selected : this.selected.join(','))
    },
    handleVisibleArea() {
      const userTag = document.querySelector('.tagCheckbox')
      const wrap = document.querySelector('.base-filter-row')
      // 判断是否在可视区域内
      if (!isElementInViewport(userTag, wrap)) return false

      this.render()
      return true
    },
    onTableScroll() {
      this.handleVisibleArea();
    },
    handleOnBus() {
      Bus.$on('tableScroll', this.onTableScroll)
    },
    removeBusListener() {
      if (this.isVisibleArea) {
        Bus.$off('tableScroll', this.onTableScroll)
      }
    },
    handleInit() {
      const flag = this.handleVisibleArea()

      if (!flag) {
        this.handleOnBus()
      }
    },
    handleInitValue(val) {
      const value = val || []
      this.selected = Array.isArray(value) ? value : value.split(',')
    },
    render () {
      this.removeBusListener()

      this.$request.get(this.apiName).then(res => {
        if (res.status && res.data && res.data.length) {
          this.data = res.data
        } else {
          // 没有数据时, 不展示
          // 传递信息给上层父组件
          if (!this.keyName) return
          this.handleUpdateFilterCol && this.handleUpdateFilterCol({ key: this.keyName, attr: 'show', value: false })
        }
        this.loaded = true
      })
    }
  },
  mounted() {
    // 从存储中读取展开状态
    this.isCollapsed = this.getStoredCollapseState()

    // 默认给进来的是1,2,3,4.  可直接给list-table使用
    if (this.defaultValue) {
      this.selected = this.defaultValue.split(',')
    }

    // 如果有值的话
    if (this.value) {
      this.handleInitValue(this.value)
    }

    if (this.isVisibleArea) {
      this.handleInit()
    } else {
      this.data = this.checkboxItems
      this.loaded = true
    }

    this.hanldeInitResizeObserver()
  },
  activated() {
    if (this.isDeactivated) {
      this.handleOnBus()
    }
  },
  deactivated() {
    this.isDeactivated = true
    this.removeBusListener()
  },
  beforeDestroy() {
    this.removeBusListener()

    window.removeEventListener('resize', this.handleCheckMultipleLines)
  }
}
</script>

<style lang="less">
.base-checkbox {
  .loading {
    color: #888;
  }
  .checkbox-container {
    position: relative;
    padding-right: 50px; /* 为展开/收起按钮留出空间 */
  }
  .tagCheckbox {
    display: flex;
    flex-wrap: wrap;
    transition: max-height 0.3s ease;
    &.collapsed {
      max-height: 30px !important; /* 一行的高度 */
      overflow: hidden;
    }
  }
  .expand-collapse {
    position: absolute;
    right: 0;
    top: 0;
    cursor: pointer;
    font-size: 12px;
    white-space: nowrap;
    color: #b2b2b2;
    line-height: 30px;
    &:hover {
      opacity: 0.8;
      color: #03ac54;
    }
  }
}
</style>