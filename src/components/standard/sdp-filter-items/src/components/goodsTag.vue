<template>
  <sdpCheckbox
    :apiName="apiUrl.getGoodsTag"
    :value="value"
    :defaultValue="defaultValue"
    storageKey="checkboxExpandStateGoodsTag"
    @on-update="handleUpdate"
    valueKey="name"
    v-bind="$attrs">
  </sdpCheckbox>
</template>

<script>
import sdpCheckbox from './base-checkbox/index';

export default {
  name: 'goodsTag',
  components: {
    sdpCheckbox
  },
  props: {
    value: {
      default: '',
    },
    defaultValue: {
      type: String,
      default: ''
    }
  },
  methods: {
    handleUpdate(value) {
      this.$emit('input', value);
      this.$emit('on-change', value);
    }
  },
}
</script>
<style lang="less"></style>
