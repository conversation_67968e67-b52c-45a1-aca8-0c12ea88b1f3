<template>
  <Cascader
    v-model="selfValue"
    :data="treeData"
    :not-found-text="!loaded ? '加载中' : '暂无数据'"
    transfer-class-name="sdp-cascader-transferClassName"
    v-bind="{
      'change-on-select': true,
      clearable: true,
      filterable: true,
      transfer: true,
      ...$attrs
    }"
    @on-change="handleChange"
    @on-visible-change="handleVisibleChange"
  >
  </Cascader>
</template>

<script>
export default {
  props: {
    value: {
      type: [Array, String],
      default: () => []
    },
    apiName: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    responseParse: {
      type: Function,
      default: (res) => {
        return res
      },
    },
  },
  watch: {
    value (newValue) {
      this.selfValue = newValue || [];
    }
  },
  data () {
    return {
      treeData: [],
      loaded: false,
      selfValue: [],
    };
  },
  methods: {
    handleVisibleChange(isOpen) {
      if (isOpen) {
        if (!this.treeData.length && !this.loaded) {
          // 调接口
          this.$request.get(this.apiName, this.params).then(res => {
            this.treeData = this.responseParse(res)
            this.loaded = true
          })
        }
      }
    },
    handleChange (value) {
      this.$emit('on-update', value)
    },
  }
};
</script>

<style lang="less">
.sdp-cascader-transferClassName {
  .ivu-cascader-not-found-tip {
    width: 232px;
    padding: 0;
  }
  .ivu-cascader-dropdown {
    .ivu-select-dropdown-list {
      height: 170px;
    }
  }
}
</style>
