import Button from '../../button';
import Dropdown from '../../dropdown';
import Icon from '../../icon';
import { cloneDeep } from 'lodash-es';
const getActionItemName = ({ action = {}, params }) => {
  let actionItemName;
  if (typeof action.name === 'function') {
    actionItemName = action.name(params);
  } else {
    actionItemName = action.name;
  }
  return actionItemName;
};

const getActionItemClass = ({ action = {}, defaultClass = [], params }) => {
  let actionItemClass = [];
  if (action.class) {
    if (typeof action.class === 'function') {
      actionItemClass = action.class(params);
    } else {
      actionItemClass = action.class;
    }
    if (!Array.isArray(actionItemClass)) {
      actionItemClass = [actionItemClass];
    }
  }
  return [...defaultClass, ...actionItemClass];
};

const getActionItemStyle = ({ action = {}, defaultStyle = {}, params }) => {
  let actionItemStyle = {};
  if (action.style) {
    if (typeof action.style === 'function') {
      actionItemStyle = action.style(params);
    } else {
      actionItemStyle = action.style;
    }
  }
  return {
    ...defaultStyle,
    ...actionItemStyle
  };
};

const getActionItemAttrs = ({ action = {}, defaultAttrs = {}, params }) => {
  let actionItemAttrs = {};
  if (action.attrs) {
    if (typeof action.attrs === 'function') {
      actionItemAttrs = action.attrs(params);
    } else {
      actionItemAttrs = action.attrs;
    }
  }
  return {
    ...defaultAttrs,
    ...actionItemAttrs
  };
};

const getActionItemProps = ({ action = {}, defaultProps = {}, params }) => {
  let actionItemProps = {};
  if (action.props) {
    if (typeof action.attrs === 'function') {
      actionItemProps = action.props(params);
    } else {
      actionItemProps = action.props;
    }
  }
  return {
    ...defaultProps,
    ...actionItemProps
  };
};

const hasChildAction = action => action.children && action.children.length > 0;

const handleAction = (actionItem, params) => {
  if (!actionItem.action || typeof actionItem.action !== 'function') {
    new Error(`请为${actionItem.name}设置正确的处理函数`);
  }
  actionItem.action(params);
};

const formatActions = ({ actions = [], limit = 2 }) => {
  let formatActions = [];
  limit = Number(limit);
  if (actions.length <= limit + 1) {
    return cloneDeep(actions);
  }
  const moreActions = {
    name: '更多',
    children: []
  };
  formatActions = actions.slice(0, limit);
  moreActions.children = actions.slice(limit, actions.length);
  formatActions.push(moreActions);
  return formatActions;
};

const getActionRender = ({ h, params, actions = [] }) => {
  const actionCount = actions.length;
  return h(
    'div',
    {
      class: ['list-table__action']
    },
    actions.map((action, index) => {
      let actionConfirm = '';
      if (typeof action.confirm === 'function') {
        actionConfirm = action.confirm(params, h);
      } else {
        actionConfirm = action.confirm;
      }
      let actionItem = [
        h(Button, {
          style: getActionItemStyle({
            action,
            defaultStyle: {
              cursor: 'pointer',
              color: 'var(--primary-color)'
            },
            params
          }),
          class: getActionItemClass({
            action,
            params
          }),
          attrs: getActionItemAttrs({
            action,
            params
          }),
          props: getActionItemProps({
            action,
            params,
            defaultProps: {
              type: 'text',
              text: getActionItemName({ action, params }),
              confirm: !!actionConfirm,
              title: actionConfirm,
              placement: 'top-end',
              offset: '11'
            }
          }),
          on: {
            click: () => {
              if (hasChildAction(action)) {
                return false;
              }
              handleAction(action, params);
            }
          }
        })
      ];
      if (hasChildAction(action)) {
        actionItem = [
          h(
            Dropdown,
            {
              props: {
                transfer: true,
                trigger: 'hover',
                placement: 'left-start'
              }
            },
            [
              actionItem,
              h(Icon, {
                class: ['list-table__action-dropdown__icon'],
                props: {
                  icon: 'arrow-down'
                }
              }),
              h(
                'div',
                {
                  class: ['list-table__action-dropdown'],
                  slot: 'list'
                },
                action.children.map(childAction => {
                  return h(
                    'p',
                    {
                      style: getActionItemStyle({
                        action: childAction,
                        params
                      }),
                      class: getActionItemClass({
                        action: childAction,
                        defaultClass: ['list-table__action-dropdown__item'],
                        params
                      }),
                      attrs: getActionItemAttrs({
                        action: childAction,
                        params
                      }),
                      on: {
                        click: () => {
                          handleAction(childAction, params);
                        }
                      }
                    },
                    getActionItemName({ action: childAction, params })
                  );
                })
              )
            ]
          )
        ];
      }
      if (index < actionCount - 1) {
        actionItem.push(
          h('div', {
            class: ['list-table__action-divider']
          })
        );
      }
      return actionItem;
    })
  );
};

export {
  getActionItemClass,
  getActionItemStyle,
  getActionItemAttrs,
  getActionItemProps,
  hasChildAction,
  getActionItemName,
  getActionRender,
  formatActions
};
