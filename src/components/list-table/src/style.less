@prefix: list-table;
.@{prefix} {
  .ivu-icon-ios-close-circle {
    cursor: pointer;
  }
  &__selection-panel {
    padding: 8px 26px;
  }
  .operation__item {
    margin-right: 10px;
  }
  &__pagination-wrap {
    // padding: 10px 0;
    padding: 12px 0 16px;
    margin-right: -10px;
  }
  // .before-table {
  //   max-height: 46px;
  // }
  .before-table, .before-table-head {
    padding-top: 16px;
    &.nopd {
      padding-top: 0;
    }
  }
  // .filter--advance ~.before-table-head:empty {
  //   display: none;
  // }
  .sdp-table__header-top {
    .ivu-btn {
      font-size: 13px;
    }
  }
  &__action {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 13px;
    font-weight: 400;
    &-divider {
      display: block;
      width: 1px;
      height: 13px;
      background-color: rgba(233, 233, 233, 1);
      margin: 0 8px;
    }
    & &-dropdown__icon {
      &.sui-icon {
        font-size: 12px;
        color: var(--primary-color);
        margin-left: 3px;
        margin-bottom: 2px;
      }
    }

    &-dropdown {
      margin-bottom: -10px;
      min-width: 106px;
      &__item {
        display: block;
        padding: 7px 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.85);
        cursor: pointer;
        .ivu-poptip, .ivu-poptip-rel {
          display: block;
        }
        span {
          display: block;
        }
        &:hover {
          color: var(--primary-color);
          background-color: rgb(239, 250,244);
        }
      }
    }
  }
}
