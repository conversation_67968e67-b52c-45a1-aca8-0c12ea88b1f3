<template>
  <div data-perf="list-table" class="list-table list-table--new" ref="wrap">
    <ListFilter
      ref="filter"
      :tableId="tableId"
      v-show="isShowFilter"
      @filter="filterChange"
      @filterConfigChange="filterConfigChange"
      @reset="filterChange($event, true)"
      @on-ready="autoFetchData"
      @advanceChange="advanceChange"
      :useDefaultFilerOrder="useDefaultFilerOrder"
      :search-text="searchText"
      :advance-items="advanceItemsFilter"
      :lazy="filterLazy"
      :allFliter="allFliter"
      :showAdvance="showAdvance"
      :showAdvanceBtn="showAdvanceBtn"
      :afterFilterRender="afterFilterRender"
      :filter-items="filterItemsFilter"
      :filter-config="filterConfig"
      :isOpenCustom="isOpenCustom"
      :updateWidthOnAllFiltersChange="updateWidthOnAllFiltersChange"
    >
      <slot slot="button" name="button"></slot>
    </ListFilter>
    <div class="before-table" v-show="$slots['before-table']">
      <slot class="before-table" name="before-table"></slot>
    </div>
    <div
      v-show="beforeTableHead"
      class="before-table-head"
      :class="{ nopd: (selection.length && showTableSelectionPanel) || nopd }"
    >
      <slot name="before-table-head"></slot>
    </div>
    <slot name="content">
      <Table
        @on-selection-change="handleSelectionChange"
        @on-column-width-resize="_onColumnWidthResize"
        @on-sort-change="handleSortChange"
        ref="table"
        :rowKey="rowKey"
        :isOpenGroupingTableHead="isOpenGroupingTableHead"
        :loading="loading"
        :data="data"
        :needAnimation="false"
        :columns="columnsFilter"
        :virtualScroll="virtualScroll"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <div slot="header">
          <TableSelectionPanel
            v-if="showTableSelectionPanel"
            @cancel-selected="handleCancelSelect"
            class="list-table__selection-panel"
            :count="totalSelectionCount"
          >
            <slot slot="batch-checked" name="batch-checked"></slot>
            <slot slot="operation" name="batch-operation"></slot>
          </TableSelectionPanel>
        </div>
      </Table>
    </slot>
    <slot name="after-table"></slot>
    <Row
      type="flex"
      align="middle"
      justify="space-between"
      class="list-table__pagination-wrap"
      :style="{ marginRight: elevatorIsShow ? '0px' : '-10px' }"
      v-show="showPagination"
    >
      <Col class="flex-con">
        <slot name="page-left">
          <Row :gutter="12" type="flex" v-if="sumConfig">
            <Col v-if="sumConfig.title">{{ sumConfig.title }}：</Col>
            <Col :key="sum.key" v-for="sum in sumFields">{{ sum.title }}：{{ sumData[sum.key] }}</Col>
          </Row>
        </slot>
      </Col>
      <Col align="right">
        <Page
          :show-total="showTotal"
          show-sizer
          placement="top"
          class="js-after-table"
          :total="curPagination.total"
          :current="curPagination.currentPage"
          :page-size="curPagination.pageSize"
          :page-size-opts="pageSizeOpts"
          @on-change="handleChangePage"
          @on-page-size-change="handleChangePageSize"
          :show-elevator="elevatorIsShow"
        >
        </Page>
      </Col>
    </Row>
  </div>
</template>

<script>
import { get, post } from '@api/request.js';
import ListFilter from '../../list-filter';
import Button from '../../button';
import Icon from '../../icon';
import Table from '../../table';
import Dropdown from '../../dropdown';
import TableSelectionPanel from '../../list-table-selection-panel';
import { cloneDeep } from 'lodash-es';
import basic from '@api/basic.js';
import TitleCfg from '../../title-config';
import { LoadingBar, Tooltip } from 'view-design';
import {
  getActionItemClass,
  getActionItemAttrs,
  getActionItemStyle,
  getActionItemProps,
  hasChildAction,
  getActionItemName,
  formatActions,
} from './utils';
import { debounce } from 'lodash-es';
// import performanceLogger from '@util/performanceLogger';
import { initPage } from '@sdp/xiaodong-sdk';
import { mapState } from 'vuex';

const MIN_PAGE_SIZE = 20;
export default {
  name: 'ListTable',
  components: {
    ListFilter,
    Table,
    TableSelectionPanel,
  },
  // provide(){
  //   return {
  //     tableId: this.tableId
  //   }
  // },
  props: {
    useDefaultFilerOrder: {
      type: Boolean,
      default: false,
    },
    sumConfig: {
      type: [Object, Boolean],
      default: false,
    },
    updateWidthOnAllFiltersChange: {
      type: Boolean,
      default: false,
    },
    beforeTableHead: {
      type: Boolean,
      default: true,
    },
    selectionOtherPage: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: String,
    },
    showSet: {
      type: Boolean,
      default: true,
    },
    nopd: {
      type: Boolean,
      default: false,
    },
    // 多级表头
    isOpenGroupingTableHead: {
      type: Boolean,
      default: false,
    },
    // 是否显示跳转到哪一页
    elevatorIsShow: {
      type: Boolean,
      default: false,
    },
    // 默认参数
    initParams: {
      type: Object,
    },
    customSelect: {
      type: [String, Boolean],
      default: false,
    },
    /**
     * fetchData 使用了lodash的防抖,[options.leading=false] (boolean): 指定在延迟开始前调用;[options.trailing=true] (boolean): 指定在延迟结束后调用。
     */
    debounceOptions: {
      type: [Object, Boolean],
      default: () => ({
        leading: false,
        trailing: true,
      }),
    },
    /**
     * 列表接口地址配置, 设置请求数据url, 或者传入一个函数返回Promise
     */
    dataProvider: {
      type: [String, Function],
    },
    /**
     * 是否自动加载数据, 为false需要手动调用获取数据的方法才会向后段发送请求
     */
    autoLoadData: {
      type: Boolean,
      default: true,
    },
    /**
     * 是否展示分页栏
     */
    showPagination: {
      type: Boolean,
      default: true,
    },
    /**
     * 自定义分页栏配置,如{
        currentPage: 1,
        pageSize: 100
      }
      配合 pageSizeOpts: [
          100,
          200,
          300
        ],
     */
    customPagination: {
      type: Object,
    },
    // 请求方法
    apiMethods: {
      type: String,
      default: 'get',
    },
    /**
     * 获取数据的请求前置操作，return false 可以终止请求 ,
     */
    beforeRequest: {
      type: Function,
      default: (params) => params,
    },
    /** * 请求结束后的钩子函数、返回处理后的数据 */
    afterRequest: {
      type: Function,
      default: (data) => data,
    },
    /** 设置数据前钩子函数、返回处理后的数据;参数:data, resData */
    beforeSetData: {
      type: Function,
      default: (data) => data,
    },
    /**
     * 分页栏page改变前置操作，return false 阻止改变 ,
     */
    beforePageChange: {
      type: Function,
      default: () => true,
    },
    /**
     * 分页栏page改变前置操作，return false 阻止改变 ,
     */
    beforePageSizeChange: {
      type: Function,
      default: () => true,
    },
    /**
     * 控制list-filter组件显示隐藏
     */
    showFilter: {
      type: Boolean,
      default: true,
    },
    /**
     * 额外的请求参数
     */
    filters: {
      type: Object,
      default: () => {},
    },
    /*
     * 基础筛选组件
     */
    filterItems: {
      type: Array,
      default: () => [],
    },
    /**
     * 高级筛选组件
     */
    advanceItems: {
      type: Array,
      default: () => [],
    },
    /** 表头配置 */
    columns: {
      type: Array,
      default: () => [],
    },
    filterLazy: {
      type: Boolean,
      default: false,
    },
    /** 是否显示高级筛选 */
    showAdvance: {
      type: Boolean,
      default: false,
    },
    /** 是否显示高级筛选按钮 */
    showAdvanceBtn: {
      type: Boolean,
      default: true,
    },
    defaultPageSize: {
      type: Number,
      default: MIN_PAGE_SIZE,
    },
    pageSizeOpts: {
      type: Array,
      default: () => {
        return [MIN_PAGE_SIZE, 30, 40, 50];
      },
    },
    pageTotal: {
      type: Number,
      default: 0,
    },
    /**额外的勾选数 */
    extraSelectionCount: {
      type: Number,
      default: 0,
    },
    /** 是否显示勾选后的勾选数量 */
    showTableSelectionPanel: {
      type: Boolean,
      default: true,
    },
    /** 同一个路由中有多个listtable时,用这个区分.不然表头设置会混乱 */
    tableId: {
      type: [String, Number],
      default: '',
    },
    // 是否显示总条数
    showTotal: {
      type: Boolean,
      default: true,
    },
    // defer异步加载filter组件后执行的方法
    afterFilterRender: Function,
    customGetPageParams: {
      type: Function,
      default: undefined,
    },
    virtualScroll: Boolean,
    initChecked: {
      type: Boolean,
      default: true,
    },
    filterConfig: {
      type: Array,
      default: () => [],
    },
    // 是否开启自定义筛选功能
    isOpenCustom: {
      type: Boolean,
      default: false,
    },
    // 是否展示表头设置里的自定义字段
    hasCustomizeField: {
      type: Boolean,
      default: false,
    },
    // 是否请求列表数据
    fetch: {
      type: Boolean,
      default: true,
    },
    searchText: {
      type: String,
      default: '查询',
    },
    smartOnChange: {
      type: Boolean,
      default: false,
    },
    // 是否本地缓存用户当前pageSize设置, suffix有值的情况下，则视为开启
    pageSizeCache: {
      type: Object,
      default: () => ({
        suffix: false,
      }),
    },
  },
  provide() {
    return {
      smartOnChange: this.smartOnChange,
    };
  },
  data() {
    const _pageSizeCacheKey = this.pageSizeCache.suffix
      ? `pageSize_${this.$route.path}_${this.pageSizeCache.suffix}`
      : undefined;
    return {
      sumFields: [],
      checked: true,
      selectionAll: [],
      row: {},
      selection: [],
      loading: true,
      // pageSizeOpts: [MIN_PAGE_SIZE, 50, 100, 200],
      pagination: {
        currentPage: 1,
        total: 0,
        pageSize: MIN_PAGE_SIZE,
      },
      data: [],
      titleCol: [],
      allColKeys: [],
      copyColumns: [],
      hasTitleCfg: false,
      timer: null,
      pageSizeCacheKey: _pageSizeCacheKey,
    };
  },
  deactivated() {
    // this.data = [];
  },
  watch: {
    columns: {
      deep: true,
      handler() {
        this.initAction();
      },
    },
    selection: {
      deep: true,
      handler(selection) {
        this.$emit('on-selection-change', selection, '');
      },
    },
    pageTotal() {
      if (!this.customPagination) {
        this.pagination.total = this.pageTotal;
      }
    },
    allFliter: {
      deep: true,
      handler() {
        if (Number(this.sysConfig.is_open_ai_chat_dialog)) this._initPage()
      },
    },
  },
  computed: {
    ...mapState({
				sysConfig: 'sysConfig'
		}),
    sumData() {
      if (!this.sumConfig || !this.sumConfig.data) {
        return {};
      }
      return this.sumConfig.data;
    },
    selectionRowKeys() {
      if (!this.rowKey) {
        return [];
      }
      return this.selection.map((item) => item[this.rowKey]);
    },
    totalSelectionCount() {
      const selfSelectionCount =
        this.selectionAll && this.selectionAll.length > 0
          ? this.selectionAll.length
          : 0;
      return selfSelectionCount + this.extraSelectionCount;
    },
    curPagination() {
      return this.customPagination ? this.customPagination : this.pagination;
    },
    filterDefaultValues() {
      const defaultValues = {};
      let advanceItems = this.advanceItems;
      if (this.advanceItems.length > 0 && this.advanceItems[0].items) {
        advanceItems = this.advanceItems.flatMap((x) => x.items);
      }
      this.filterItems.forEach((item) => {
        defaultValues[item.key] = item.defaultValue;
      });
      advanceItems.forEach((item) => {
        defaultValues[item.key] = item.defaultValue;
      });
      return defaultValues;
    },
    allFliter() {
      let filters = this.filterItems.filter((item) => item.show !== false);
      let advanceItems = this.advanceItems;
      if (this.advanceItems.length > 0 && this.advanceItems[0].items) {
        advanceItems = this.advanceItems.flatMap((x) => x.items);
      }
      advanceItems = advanceItems.filter((itm) =>
        itm.show === 'function' ? itm.show() : itm.show !== false,
      );
      return [...filters, ...advanceItems];
    },
    filterItemsFilter() {
      return this.filterItems.filter((item) =>
        typeof item.show === 'function' ? item.show() : item.show !== false,
      );
    },
    advanceItemsFilter() {
      let advanceItems = this.advanceItems;
      if (this.advanceItems.length > 0 && this.advanceItems[0].items) {
        advanceItems = this.advanceItems.flatMap((x) => x.items);
      }
      return advanceItems.filter((itm) =>
        typeof itm.show === 'function' ? itm.show() : itm.show !== false,
      );
    },
    isFilter() {
      return this.allFliter && this.allFliter.length > 0;
    },
    isShowFilter() {
      return this.isFilter && this.showFilter;
    },
    showColumns() {
      return this.copyColumns.filter(
        (item) => this.isDefCol(item) || item.type,
      );
    },
    columnsFilter() {
      let result = this.hasTitleCfg
        ? this.showColumns
            .filter((item) => item.checked !== false)
            .sort((a, b) => a.index - b.index)
        : this.copyColumns;
      // column配置添加show api
      result = result.filter((item) => {
        if (item.show !== undefined) {
          return typeof item.show === 'function' ? item.show() : item.show;
        }
        return true;
      });
      return result;
    },
  },
  created() {
    this.allPageSelection = [];
    // 首次列表数据渲染完成
    this.firstDataRenderDone = false;
    // 外部传入data的情况下loading由外部控制
    if (!this.dataProvider || !this.autoLoadData) {
      this.loading = false;
    }
    if (this.debounceOptions) {
      this.fetchData = debounce(this._fetchData, 300, this.debounceOptions);
    } else {
      this.fetchData = this._fetchData;
    }
    const cachedPageSize = this.pageSizeCacheKey
      ? localStorage.getItem(this.pageSizeCacheKey)
      : null;
    this.pagination.pageSize = cachedPageSize
      ? parseInt(cachedPageSize, 10)
      : this.defaultPageSize;
    this.pagination.total = this.pageTotal;
    if (this.autoLoadData) {
      this.fetchData();
    }
    this.initAction();
    // 展开收起二级菜单重新初始化表格宽度
    this.$root.$on('menuModeChange', this.$_onMenuModeChange);
  },
  beforeDestroy() {
    removeTool('searchFilters')
    this.$root.$off('menuModeChange', this.$_onMenuModeChange);
  },
  mounted() {},
  methods: {
    _initPage() {
      let t = (this.deepClone(this.getallFliter())).filter(item => {
        return item.type !== 'custom' && item.type !== 'Select'
      })
      t.forEach(t => {
        if(t.component){
          delete t.component;// 删除component属性, AI那边接受不了，否则会报错
        }
      })
      console.log('_initPage', t);

      initPage({
        context: {
          // pageContext: `当前页面路由为"${this.$route.path}", 页面名称为"${this.$route.name}", 页面名称可能是英文也可能没有,
          // 如果你不了解你可以结合路由, pageContext属性是对页面的说明,filterItems是当前页面的一些筛选项
          // ,它是一个对象数组,每个对象有一些属性, key属性是用来筛选字段可能是一个字符串也可能是字符串数组, label是筛选项的名称,
          // type属性是筛选项的类型, 可能是日期可能是字符串,还有可能custom自定义的,你需要结合label以及当前上下文判断。但是你返回的参数
          // 的key是与筛选项的key是对应的, 不要试图编造。
          // 支持查询 search 操作`,
          // filterItems: t,
        },
        // actions: {
        //   search: (params, cb) => {
        //     console.log('search', params);
        //       // for (let key in params) {
        //       //   this.setValue(key, params[key]);
        //       // }
        //     cb();
        //   },
        // },
        tools: [
          {
            name: 'search',
            description: `
              页面查询工具，当用户执行查询语义操作时，使用此工具进行查询，
              当前页面支持的查询项的定义为：${JSON.stringify(t)}
            `,
            parameters: {
              type: 'object',
              properties: {
                params: {
                  type: 'record',
                  description: '查询的参数',
                },
              },
              returnType: {
                type: 'object',
                description: '查询结果',
              },
            },
          }
        ],
        toolCallback: async(toolName, payload) => {
          console.log('toolName', toolName, 'payload', payload);



          switch (toolName) {
            case 'getCustomerInputValue':
              return params;
            case 'search': {
              const { params = {} } = payload;
              const entries = Object.entries(params);
              for (let i = 0; i < entries.length; i++) {
                const [key, value] = entries[i];
                this.setValue(key, value, i !== entries.length - 1);
              }
              return `已根据参数 ${JSON.stringify(params, null, 2)} 进行查询`
            }

            default:
              return '没有对应工具方法';
          }
        },
      });
    },
    getallFliter(){
      return this.allFliter
    },
    handleSortChange(column, field, order) {
      // 外部没有处理排序时走公共排序逻辑
      if (!this.$listeners['on-sort-change']) {
        this.orderBy = order ? [field, order] : [];
        this.fetchData();
      }
    },
    _onColumnWidthResize() {
      let path = this.$route.path;
      let columnWidth = [];
      if (this.isOpenGroupingTableHead) {
        let copyColumns = this.deepClone(this.copyColumns);
        copyColumns.forEach(({ key, width }) => {
          columnWidth.push({ key, width });
        });
      } else {
        this.copyColumns.forEach(({ key, width }) => {
          columnWidth.push({ key, width });
        });
      }
      localStorage.setItem(
        path + '&columnWidth' + this.tableId,
        JSON.stringify(columnWidth),
      );
    },
    $_onMenuModeChange() {
      this.$nextTick(() => {
        this.$refs.table.handleResize();
      });
    },
    filterChange(e, isReset) {
      console.log('888888888888888888', e);
      this.$emit('filter-change', e, isReset);
      isReset && this.$emit('reset-change', e);
      this.fetchData();
    },
    filterConfigChange(filterConfig) {
      this.$emit('filterConfigChange', filterConfig);
    },
    advanceChange(e) {
      this.$emit('advanceChange', e);
    },
    /**
     * @vuese
     * 手动设置value值
     * @arg key: 需要设置的key值; value: 对应的value; stop: 设置完成后不抛出事件
     */
    setValue(key, value, stop) {
      if (this.$refs.filter) {
        this.$refs.filter.setValue(key, value, stop);
      }
    },
    /**
     * @vuese
     * 返回获取当前显示的columns
     * @arg
     */
    getFilterColumns() {
      return this.columnsFilter;
    },
    getTitleList() {
      const title = localStorage.getItem(
        `title-${this.$route.path}${this.tableId}`,
      );
      return title ? JSON.parse(title) : [];
    },
    getCustomTitleList() {
      const title = localStorage.getItem(
        `title-sum-${this.$route.path}${this.tableId}`,
      );
      return title ? JSON.parse(title) : title;
    },
    async getTitleData(col) {
      const titleData = this.getTitleList();
      if (col.titleType) {
        const res = await basic.getFields({ type: col.titleType });
        if (res.status) {
          // 设置自定义字段
          if (this.hasCustomizeField) {
            this.setCustomizeField(res.data);
          }
          this.formatColumns(res.data);
          titleData.length && this.formatTitle(titleData);
        }
      } else {
        this.formatColumns();
        titleData && this.formatTitle(JSON.parse(titleData));
      }
      this.hasTitleCfg = true;
    },
    colsChange(cols, customFields = []) {
      const data = cols.map((item) => item.key);
      const customData = customFields.map((item) => item.key);
      localStorage.setItem(
        `title-${this.$route.path}${this.tableId}`,
        JSON.stringify(data),
      );
      localStorage.setItem(
        `title-sum-${this.$route.path}${this.tableId}`,
        JSON.stringify(customData),
      );
      this.sumFields = customFields;
      this.formatTitle(data);
    },
    isDefCol(item) {
      return this.allColKeys.indexOf(item.key) !== -1;
    },
    formatTitle(curTitle) {
      let i = 0;
      this.showColumns.forEach((item, index) => {
        const colIdx = curTitle.indexOf(item.key);
        const isDefCol = this.isDefCol(item);
        if (colIdx !== -1) {
          item.index = colIdx + i;
        } else if (!isDefCol) {
          i++;
          item.index = index;
        } else {
          item.index = -1;
        }
        if (isDefCol) {
          item.checked = colIdx !== -1;
        }
      });
    },
    formatColumns(data = {}) {
      const { all_column, default_column, selected_column } = data;
      this.titleCol = [];
      this.allColKeys = Object.keys(all_column);
      this.showColumns.forEach((item, index) => {
        if (this.isDefCol(item)) {
          this.$set(
            item,
            'checked',
            selected_column ? selected_column.indexOf(item.key) !== -1 : true,
          );
          this.$set(
            item,
            'required',
            default_column ? default_column.indexOf(item.key) !== -1 : false,
          );
          this.$set(item, 'index', index);
          this.titleCol.push(item);
        }
      });
    },
    /**
     * @description 设置自定义字段
     * @param data
     */
    setCustomizeField(data) {
      const { all_column } = data;
      // 查找出所有的自定义字段key
      const customizeField = Object.keys(all_column).filter((key) =>
        key.includes('customize_field'),
      );

      customizeField.forEach((key) => {
        // 如果不存在则添加, 防止某些场景重复添加
        if (this.copyColumns.findIndex((item) => item.key === key) === -1) {
          this.copyColumns.push({
            title: all_column[key],
            key: key,
          });
        }
      });
      // 如果存在操作栏,则将操作栏放在最后
      const actionCol = this.copyColumns.find((item) => item.type === 'action');
      if (actionCol) {
        this.copyColumns = this.copyColumns.filter(
          (item) => item.type !== 'action',
        );
        this.copyColumns.push(actionCol);
      }
    },
    autoFetchData(_childrenParams = {}) {
      // 理论上autoLoadData为true, 不需要走这里逻辑的不知道以前为啥要这么写。为了确保新的优化方式autoLoadData为true时不走这里的逻辑，又不影响之前的逻辑，所以加了个判断
      if (this.autoLoadData && this.debounceOptions !== false) {
        this.fetchData(true, false, 'spinner', _childrenParams);
      }
    },
    initAction() {
      this.copyColumns = this.deepClone(this.columns);

      this.copyColumns.forEach((col) => {
        /**
         * 字段类型扩展
         */
        // 枚举字段
        if (col.enum) {
          col.render = (h, params) => {
            const { row } = params;
            const value = row[col.key];
            let enumItem = {};
            if (Array.isArray(col.enum)) {
              enumItem = col.enum.find((item) => item.value === value);
            } else {
              enumItem = col.enum[value];
            }
            return enumItem ? enumItem.label : '';
          };
        }

        // 解决有些字段没设置key，列表拖动列宽后操作栏宽度不一致问题
        if (col.type === 'selection' && !col.key) {
          col.key = '_selection';
        }

        // if (col.type === 'expand' && col.titleType) {
        if (col.titleType) {
          this.titleCol = [];
          this.getTitleData(col);
          let storeCustomFields = this.getCustomTitleList();
          const hasStoreCustomFields = storeCustomFields !== null;
          storeCustomFields = storeCustomFields || [];
          // 开启自定义合计字段
          if (col.sumConfig && col.sumConfig.fields) {
            // 做一层显示隐藏的控制
            col.sumConfig.fields = col.sumConfig.fields.filter(item => {
              if (item.show === undefined) {
                return true;
              }
              return typeof item.show === 'function' ? item.show() : item.show;
            });
            const sumFields = [];
            if (!hasStoreCustomFields) {
              storeCustomFields = col.sumConfig.fields.filter(item => item.defaultChecked !== false).map(item => item.key);
            }
            storeCustomFields.forEach((item) => {
              const field = col.sumConfig.fields.find((field) => field.key === item);
              if (field) {
                sumFields.push(field);
              }
            });
            this.sumFields = sumFields;
          }
          col.renderHeader = () => {
            console.log('col.title', col);
            if (col.tip) {
              return (
                <TitleCfg
                  data={this.titleCol}
                  sumConfig={col.sumConfig}
                  storeCustomFields={storeCustomFields}
                  hasStoreCustomFields={hasStoreCustomFields}
                  onChange={this.colsChange.bind(this)}
                >
                  <Tooltip
                    transfer-class-name="tooltip_custom"
                    content={col.tip}
                    placement="top"
                    transfer
                  >
                    <Icon style="font-size:16px;" icon="config" size="sm" />
                  </Tooltip>
                </TitleCfg>
              );
            } else {
              return (
                <TitleCfg
                  data={this.titleCol}
                  sumConfig={col.sumConfig}
                  storeCustomFields={storeCustomFields}
                  hasStoreCustomFields={hasStoreCustomFields}
                  onChange={this.colsChange.bind(this)}
                >
                  <Icon style="font-size:16px;" icon="config" size="sm" />
                </TitleCfg>
              );
            }
          };
        }

        if (col.type === 'action') {
          col.resizable = false;
          col.title = col.title || '操作';
          col.fixed = 'right';
          col.renderHeader = (h) => h('span', col.title);
          if (!col.width) {
            col.width = '170';
          }
          if (!col.key) {
            col.key = 'action';
          }
          col.render = (h, params) => {
            let actions =
              typeof col.actions === 'function'
                ? col.actions(params)
                : col.actions || [];
            const actionCountLimit =
              typeof col.actionCountLimit === 'function'
                ? col.actionCountLimit(params)
                : col.actionCountLimit;
            actions = formatActions({
              actions,
              limit: actionCountLimit || 3,
            });
            const actionCount = actions.length;
            const { row } = params;
            return h(
              'div',
              {
                class: ['list-table__action'],
              },
              actions.map((action, index) => {
                if (action.render && typeof action.render === 'function') {
                  return action.render(h, params);
                }
                let actionConfirm = '';
                if (typeof action.confirm === 'function') {
                  actionConfirm = action.confirm(params, h);
                } else {
                  actionConfirm = action.confirm;
                }
                let actionItem = [
                  h(Button, {
                    style: getActionItemStyle({
                      action,
                      defaultStyle: {
                        cursor: 'pointer',
                        color: 'var(--primary-color)',
                      },
                      params,
                    }),
                    class: getActionItemClass({
                      action,
                      params,
                    }),
                    attrs: getActionItemAttrs({
                      action,
                      params,
                    }),
                    props: getActionItemProps({
                      action,
                      params,
                      defaultProps: {
                        type: 'text',
                        text: getActionItemName({ action, params }),
                        confirm: !!actionConfirm,
                        title: actionConfirm,
                        placement: 'top-end',
                        offset: '11',
                        openPoptipOption: false,
                      },
                    }),
                    on: {
                      click: () => {
                        if (hasChildAction(action)) {
                          return false;
                        }
                        this.handleAction(action, params);
                      },
                      'on-cancel': () => {
                        if (
                          action.actionCancel &&
                          typeof action.actionCancel === 'function'
                        ) {
                          action.actionCancel(params);
                        }
                      },
                    },
                  }),
                ];
                if (hasChildAction(action)) {
                  if (!row._show_more_action) {
                    actionItem = [
                      h(
                        'div',
                        {
                          on: {
                            mouseover() {
                              row._show_more_action = true;
                            },
                          },
                        },
                        [
                          actionItem,
                          h(Icon, {
                            class: ['list-table__action-dropdown__icon'],
                            props: {
                              icon: 'arrow-down',
                            },
                          }),
                        ],
                      ),
                    ];
                  } else {
                    actionItem = [
                      h(
                        Dropdown,
                        {
                          props: {
                            transfer: true,
                            trigger: 'hover',
                            placement: 'bottom-end',
                          },
                          ref: 'dropdown-moreAction' + action.name,
                        },
                        [
                          actionItem,
                          h(Icon, {
                            class: ['list-table__action-dropdown__icon'],
                            props: {
                              icon: 'arrow-down',
                            },
                          }),
                          h(
                            'div',
                            {
                              class: ['list-table__action-dropdown'],
                              slot: 'list',
                            },
                            action.children.map((childAction) => {
                              let actionConfirm = '';
                              if (typeof childAction.confirm === 'function') {
                                actionConfirm = childAction.confirm(params, h);
                              } else {
                                actionConfirm = childAction.confirm;
                              }
                              return h(Button, {
                                props: {
                                  type: 'text',
                                  text: getActionItemName({
                                    action: childAction,
                                    params,
                                  }),
                                  confirm: !!actionConfirm,
                                  title: actionConfirm,
                                  placement: 'top-end',
                                  offset: childAction.offset || '-36',
                                  openPoptipOption: false,
                                },
                                style: getActionItemStyle({
                                  action: childAction,
                                  params,
                                  defaultStyle: {
                                    display: 'block',
                                  },
                                }),
                                class: getActionItemClass({
                                  action: childAction,
                                  defaultClass: [
                                    'list-table__action-dropdown__item',
                                  ],
                                  params,
                                }),
                                attrs: getActionItemAttrs({
                                  action: childAction,
                                  params,
                                }),
                                on: {
                                  click: () => {
                                    if (!actionConfirm) {
                                      actionItem[0].context.$refs[
                                        'dropdown-moreAction' + action.name
                                      ].currentVisible = false;
                                    }
                                    this.handleAction(childAction, params);
                                  },
                                },
                              });
                            }),
                          ),
                        ],
                      ),
                    ];
                  }
                }
                if (index < actionCount - 1) {
                  actionItem.push(
                    h('div', {
                      class: ['list-table__action-divider'],
                    }),
                  );
                }
                return actionItem;
              }),
            );
          };
        }
      });
      let path = this.$route.path;
      let columnWidth = [];
      columnWidth = JSON.parse(
        localStorage.getItem(path + '&columnWidth' + this.tableId),
      );
      // console.log('columnWidth', columnWidth)
      if (columnWidth !== null) {
        this.copyColumns.forEach((item) => {
          let data = columnWidth.find((val) => val.key === item.key);
          if (data && data.width) {
            this.$set(item, 'width', data.width);
          }
        });
      }
    },
    handleAction(actionItem, params) {
      let { action, param, success, error, type, url } = actionItem;
      let { row } = params;
      const getActionParams = () => {
        let actionParams = {};
        if (typeof param === 'string') {
          actionParams[param] = row[param];
        }
        if (Array.isArray(param)) {
          param.map((paramItem) => {
            actionParams[paramItem] = row[paramItem];
          });
        }
        if (typeof param === 'function') {
          actionParams = param(params);
        }
        return actionParams;
      };
      // action是函数，控制权交给函数
      if (action && typeof action === 'function') {
        action(params);
      }
      // action 是字符则当做请求api地址执行ajax请求
      if (typeof action === 'string') {
        this.$request
          .post(action, getActionParams())
          .then((res) => {
            let { status, message } = res;
            if (status) {
              if (typeof success === 'string') {
                this.successMessage(success);
                this._fetchData(false);
              }
              if (typeof success === 'function') {
                success(res);
              }
            } else {
              if (typeof error === 'string') {
                // this.errorNotice({ desc: message || error });
                this.errorMessage(message || error);
              }
              if (typeof error === 'function') {
                error(res);
              }
            }
          })
          .catch(() => {
            this.errorNotice({
              title: '操作失败',
              desc: '网络异常',
            });
          });
      }
      switch (type) {
        case 'export':
          if (!url) {
            return false;
          }
          get(url, getActionParams()).then((res) => {
            let { status, message, data } = res;
            if (status) {
              let url = data.url || data;
              location.href = url;
            } else {
              this.errorMessage(message);
            }
          });
          break;
        default:
          break;
      }
    },
    handleSelectionChange(selection, rebuild, row, checked) {
      this.row = row;
      this.checked = checked;

      let allSelectionData = [...selection];
      if (this.selectionOtherPage && this.rowKey) {
        allSelectionData.push(...this.allPageSelection);
        const currentDataRowKeys = this.data.map((item) => item[this.rowKey]);
        const otherPageSelection = this.selection.filter(
          (item) => !currentDataRowKeys.includes(item[this.rowKey]),
        );
        allSelectionData = otherPageSelection.concat(allSelectionData);
      }

      this.selectionAll = allSelectionData.filter((res) => !res._summary);
      this.selection = allSelectionData;
      console.log(this.selectionAll);
    },
    handleCancelSelect() {
      if (!this.initChecked) return;

      this.selection = [];
      this.selectionAll = [];

      let data = cloneDeep(this.getData());
      this.$emit('on-selection-change', [], '', { noall: true });
      if (!this.customSelect) {
        data.forEach((item) => {
          item._checked = false;
        });
        this.setData(data);
        this.row = null;
      }
    },
    // 表格滑动到最顶部
    scroll2top() {
      this.$refs.table.scroll2top();
    },
    handleChangePage(page) {
      if (!this.beforePageChange()) {
        return;
      }
      this.curPagination.currentPage = page;
      this.$emit('pageChange', page);
      this._fetchData(false, false);
    },
    handleChangePageSize(pageSize) {
      if (!this.beforePageSizeChange()) {
        return;
      }
      this.curPagination.pageSize = pageSize;
      // 将新的 pageSize 存储到 localStorage
      if (this.pageSizeCacheKey) {
        localStorage.setItem(this.pageSizeCacheKey, pageSize);
      }
      this.$emit('pageSizeChange', pageSize);
      this._fetchData();
    },
    resetPagination() {
      this.curPagination.totalPage = 0;
      this.curPagination.total = 0;
      this.curPagination.currentPage = 1;
      this.$emit('paginationReset');
    },
    /**
     * @vuese
     * 返回当前的请求参数,常用作导出excel时调用
     * @arg
     */
    getParams(_childrenParams = {}) {
      const { pageSize, currentPage } = this.pagination;
      const initParams = {
        ...this.filterDefaultValues,
        ...(this.initParams || {}),
      };
      let params = {
        pageSize,
        currentPage,
        ...this.filters,
      };
      if (this.isFilter) {
        let filters = _childrenParams;
        if (this.$refs.filter) {
          filters = this.$refs.filter.getFilters();
        }
        params = {
          ...initParams,
          ...filters,
          ...params,
        };
      }
      if (this.orderBy && this.orderBy.length > 0) {
        params.sort_field = this.orderBy[0];
        params.sort_type = this.orderBy[1];
      }
      return params;
    },
    /**
     * @vuese
     * 手动设置表格数据
     * @param data 需要设置的数据
     * @param resData 完整的接口返回数据
     * @param noNeedHook  是否需要触发beforeSetData钩子函数;
     * @param setPageParams  是否根据res的数据设置分页参数
     */
    setData(data, resData, noNeedHook, setPageParams = false) {
      if (noNeedHook) {
        this.data = data;
        return;
      }
      if (setPageParams && resData.pageParams) {
        this.pagination.totalPage = resData.pageParams.total_page;
        this.pagination.total = parseInt(resData.pageParams.count);
      }
      data = this.beforeSetData(data, resData) || [];
      data.forEach((item) => {
        // 初始化查看更多标识
        if (item._show_more_action === undefined) {
          item._show_more_action = false;
        }
        if (
          this.selectionOtherPage &&
          this.selectionRowKeys.includes(item[this.rowKey])
        ) {
          item._checked = true;
        }
      });
      this.data = data;
    },
    // 更新当前行的数据
    setRowData(index, row) {
      this.data.splice(index, 1, row)
    },
    /**
     * @vuese
     * 返回当前表格数据
     * @arg
     */
    getData() {
      return this.data;
    },
    /**
     * @description: 获取表格数据 rebuildData
     * @return {Array}
     */
    getRebuildData() {
      return (this.$refs.table && this.$refs.table.rebuildData) || [];
    },

    /**
     * @vuese
     * 重新发起请求,获取数据
     * @arg resetPage: 是否重置分页, 默认true;
     * @arg keepScroll: 保持滚动条位置,默认true;
     * @arg loadingType: 是否显示loading效果,默认: 'spinner',不显示
     * @arg _childrenParams: 立康补充下, 默认{}
     */
    async _fetchData(
      resetPage = true,
      keepScroll = true,
      loadingType = 'spinner',
      _childrenParams = {},
    ) {
      if (!this.fetch) return;
      console.log('dataProvider', this.dataProvider);
      let dataProvider = this.dataProvider;

      let params = this.getParams(_childrenParams);
      if (resetPage !== false) {
        this.resetPagination();
      }
      params.page = this.curPagination.currentPage;
      params.pageSize = this.curPagination.pageSize;
      // 没有分页
      if (!this.showPagination) {
        delete params.page;
        delete params.pageSize;
      }
      if (typeof dataProvider === 'string') {
        dataProvider = (params) => {
          const methods = { get, post };
          return methods[this.apiMethods](this.dataProvider, params);
        };
      }

      params = this.beforeRequest(params);
      // beforeRequest返回promise的场景
      if (typeof params.then === 'function') {
        params = await params;
      }
      if (params === false) {
        if (this.loading) this.loading = false;
        return false;
      }

      if (loadingType === 'spinner') {
        this.loading = true;
      }
      LoadingBar.config({
        duration: 100,
      });
      LoadingBar.start();
      LoadingBar.update(95);
      if (!this.selectionOtherPage) {
        this.allSelectionData = [];
      }
      dataProvider(params)
        .then((res) => {
          res = this.afterRequest(res);
          let { data, status, message } = res;
          if (status) {
            let { list, pageParams } = data;
            if (this.showPagination) {
              // 分页参数和列表分开调用
              if (this.customGetPageParams) {
                setTimeout(() => {
                  this.customGetPageParams(params).then((res) => {
                    this.pagination.totalPage = res.pageParams.total_page;
                    this.pagination.total = parseInt(res.pageParams.count);
                  });
                }, 0);
              } else {
                this.pagination.totalPage = pageParams.total_page;
                this.pagination.total = parseInt(pageParams.count);
              }
            } else if (Array.isArray(data)) list = data;
            if (resetPage || !keepScroll) {
              this.scroll2top();
            }
            this.setData(list, res);
          } else {
            this.data = [];
            this.resetPagination();
            this.setData([], res);
            if (message) {
              this.$smessage({
                type: 'error',
                text: message,
              });
            }
          }
          this.handleReady();
        })
        .catch((err) => {
          console.log('err', err);
          this.data = [];
          this.resetPagination();
        })
        .finally(() => {
          LoadingBar.finish();
          // this.selection = [];
          this.loading = false;
          this.handleFinish();
        });
    },
    resetFilter() {
      this.$refs.filter && this.$refs.filter.handleReset();
    },
    handleReady() {
      this.$nextTick(() => {
        this.$emit('on-ready');
        // try {
        //   performanceLogger.pageLoadEnd(this);
        // } catch (err) {
        //   console.error(err);
        // }
      });
      if (!this.firstDataRenderDone) {
        console.time('timeout');
        setTimeout(() => {
          this.firstDataRenderDone = true;
          this.$emit('first-data-render');
        }, 0);
        console.timeEnd('timeout');
      }
    },
    handleFinish() {
      this.$nextTick(() => {
        this.$emit('on-finish-data-load');
      });
    },
  },
};
</script>
