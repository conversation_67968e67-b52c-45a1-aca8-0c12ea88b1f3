<template>
	<div :class="{ 'PreviewImg': size == 'mini' }">
		<!-- 小图预览 -->
		<template v-if="size == 'mini'">
			<div v-for="(url, index) in urls" :key="index">
				<div class="items" @click="previewImage(urls, index)">
					<img :src="url + '!40x40'" />
					<div class="items-cover">
						<Icon type="ios-eye-outline" />
						<i
							v-if="isDel"
							class="delete sui-icon icon-solid-close"
							@click.stop="handleRemove(index)"
						/>
					</div>
				</div>
			</div>
		</template>
		<!-- 大图预览 -->
		<template v-else>
			<div class="list large" :class="[scene]">
				<div
					class="items"
					v-for="(url, index) in urls"
					:key="index"
				>
					<div class="img">
						<img :src="formatGoodsThumbnailPath(url)" />
					</div>
					<div class="cover">
						<i
							class="icon-img-view sui-icon view-icon"
							@click="previewImage(urls, index)"
						/>
					</div>
				</div>
			</div>
		</template>
	</div>
</template>

<script>
import { formatGoodsThumbnailPath } from '@/util/common';

export default {
	name: 'PreviewImgs',
	components: {},
	props: {
		// 小图预览还是大图预览
		size: {
			type: String,
			default: 'mini'
		},
		scene: {
			type: String,
			default: ''
		},
		isDel: {
			type: Boolean,
			default: false
		},
		urls: {
			type: Array,
			default: () => { return [] }
		},
	},
	data() {
		return {};
	},
	computed: {},
	watch: {},
	created() {},
	mounted() {},
	methods: {
		formatGoodsThumbnailPath,
		previewImage(images, _viewIndex = 0) {
			this.viewImage(images, _viewIndex);
		},
		handleRemove(index) {
			this.$emit('on-remove', index)
		}
	}
};
</script>
<style lang='less' scoped>
.delete {
	position: absolute;
	right: -4px;
	top: -4px;
	font-size: 12px;
}
.PreviewImg {
	display: flex;
	.items {
		cursor: pointer;
		text-align: center;
		border-radius: 4px;
		height: 40px;
		width: 40px;
		line-height: 40px;
		background: rgba(0, 0, 0, 0);
		position: relative;
		box-shadow: none;
		margin-right: 4px;
	}

	.items img {
		width: auto;
		height: auto;
	}
	.items:hover .items-cover {
		display: block;
	}
	.items-cover {
		display: none;
		position: absolute;
		color: #fff;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		border-radius: 4px;
		background: rgba(0, 0, 0, 0.4);
	}
}

.large {
  display: flex;
  flex-wrap: wrap;
	margin-bottom: -10px;
	&.modal {
		.items {
			width: 80px;
			height: 80px;
			margin-right: 12px;
			.view-icon {
				font-size: 18px
			}
		}
	}
  .items {
    position: relative;
    display: inline-block;
    width: 90px;
    height: 90px;
    line-height: 90px;
    margin: 0 16px 10px 0;
    padding: 2px;
    overflow: visible;
    border: 1px solid rgba(216, 216, 216, 0.8);
    border-radius: 2px;
    box-shadow: none;
    .img {
      width: 100%;
      height: 100%;
      overflow: hidden;
      & img {
        position: relative;
        top: 50%;
        width: 100%;
        height: auto;
        vertical-align: top;
        transform: translateY(-50%);
      }
    }
    &:hover .cover {
      visibility: visible;
      opacity: 1;
    }
    .view-icon {
      position: absolute;
      right: 12px;
      bottom: 20px;
      color: #fff;
      font-size: 22px;
      cursor: pointer;
    }
  }
}
</style>
