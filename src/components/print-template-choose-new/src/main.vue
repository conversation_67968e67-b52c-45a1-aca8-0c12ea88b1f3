<!--
 * @Description: 打印模版选择
 * @Date: 2023-08-21 17:52:07
 * @LastEditors: hgj
 * @LastEditTime: 2023-08-21 18:27:24
 * @FilePath: /sdpbase-pro/src/components/print-template-choose-new/src/main.vue
-->
<template>
  <div class="print__template-choose">
    <Modal v-model="show" :title="title">
      <template v-if="showChooseDefault">
        <Row align="middle" justify="space-between">
          <Col>按照默认指定模板:</Col>
          <Col v-if="showPreview">
            <Checkbox v-model="previewChecked" :disabled="previewDisabled">
              <span>打印前预览</span>
            </Checkbox>
            <Tooltip placement="top">
              <span slot="content">
                仅在选择自定义模板时，才能进行打印预览
              </span>
              <Icon style="cursor: pointer; vertical-align: baseline; margin-left: -8px" type="ios-help-circle"></Icon>
            </Tooltip>
          </Col>
        </Row>
        <RadioGroup v-model="currentTemplate" class="ml10 mt5">
            <Radio class="print__template-choose__item" label="">按照客户或者配置的默认打印模板进行{{btnText}}</Radio>
        </RadioGroup>
      </template>
      <div class="mt5">自定义选择模板:</div>
      <div class="print__template-choose__content template-items">
        <RadioGroup v-model="currentTemplate" class="ml10">
          <Tooltip
            v-for="(template, i) in templateData"
            :key="i"
            placement="bottom"
            max-width="300"
            transfer
            :content="template.name">
          <Radio
            class="print__template-choose__item"
            :label="template.id">
            <SIcon
              class="icon--new"
              icon="xin"
              v-if="isNewTemplate(template)"/>
              <span class="template-name">{{ template.name }}</span>
            </Radio>
          </Tooltip>
        </RadioGroup>
      </div>
      <Row slot="footer" type="flex" justify="center" :gutter="10" align="middle" >
        <Col><Button @click="closeModal">取消</Button></Col>
        <Col><Button :disabled="disabled" @click="print" type="primary">{{btnText}}</Button></Col>
      </Row>
    </Modal>
  </div>
</template>

<script>
import settings from "@api/settings";
import Button from "@components/button";
import SIcon from "@components/icon";
import ConfigMixin from '@/mixins/config';
export default {
  name: 'print-template-choose-new',
  components: {
    Button,
    SIcon,
  },
  props: {
    title: {
      type: String,
      default: "选择打印模板",
    },
    type: {
      type: String,
      default: "ORDER",
    },
    // 是否显示 按照默认指定模板
    showChooseDefault: {
      type: Boolean,
      default: false,
    },
    // 是否显示打印前预览勾选项
    showPreview: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    btnText: {
      type: String,
      default: "打印",
    },
    filterOld: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [ConfigMixin],
  data() {
    return {
      show: false,
      previewChecked: false,
      currentTemplate: "",
      templateData: [],
      callback: () => {},
    };
  },
  watch: {
    currentTemplate(val) {
      if(!val) {
        this.previewChecked = false;
        this.previewDisabled = true;
      } else {
        this.previewDisabled = false;
      }
    },
  },
  methods: {
    openModal() {
      this.show = true;
      this.currentTemplate = ''
    },
    closeModal() {
      this.show = false;
      this.currentTemplate = ''
    },
    isNewTemplate(template) {
      return settings.isNewTemplate(template);
    },
    open() {
      this.fetchTemplateData();
      this.openModal();
    },
    print() {
      this.$emit('print', this.currentTemplate)
      if (this.chooseResolve) {
        this.chooseResolve(this.currentTemplate);
        this.closeModal()
      }
    },
    choose() {
      this.open();
      return new Promise((resolve) => {
        this.chooseResolve = resolve;
      });
    },
    async fetchTemplateData() {
      const res = await this.$request.get(this.apiUrl.getPrintTemplate, {
        type: this.type,
      });
      const { list } = res.data;
      this.templateData = Array.isArray(list) ? list : [];
      // 过滤老模板
      if (this.filterOld) {
        this.templateData = this.templateData.filter((_) => {
          return settings.isNewTemplate(_);
        });
      }
      const currentTemplate = this.templateData.filter(item => +item.is_selected)
      this.currentTemplate = currentTemplate ? currentTemplate[0].id : ''
    },
  }
};
</script>
<style lang="less" scoped>
.print {
  &__template-choose {
    &__item {
      margin-right: 50px;
    }
  }
}
.template-items {
  .print__template-choose__item {
    width: 220px;
    margin-right: 0;
    display: flex;
  }
  .template-name {
    flex: 1;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    padding-right: 50px;
  }
}
.icon--new {
  font-size: 16px;
  margin-right: 5px;
  color: #ff9f00;
}
/deep/.ivu-tooltip-inner {
    max-width: inherit;
    box-shadow: none;
  }
  .print__template-choose__content{
      max-height: 500px;
      overflow-y: auto;
      line-height:1;
      padding:3px 0;
  }
  /deep/.ivu-radio{
      overflow: hidden;
  }
  .print__template-choose__item{
    overflow: hidden;
    display: inline-flex;
    flex-wrap: wrap;
    align-items: center;
  }
</style>
