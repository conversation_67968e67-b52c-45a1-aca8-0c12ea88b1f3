<template>
  <div id="form-modal">
    <Modal
      :width="width"
      :class-name="currentClassName"
      v-model="show"
      :title="title"
      :z-index="zIndex"
      @on-visible-change="handleVisibleChange"
      @on-cancel="handleCancel"
      @on-ok="save"
    >
      <Form
        class="modal-form"
        ref="form"
        :model="selfFormData"
        :rules="validateRules"
        :label-width="Number(labelWidth)"
        :style="selfFormStyle"
      >
        <FormItem
          v-show="isShow(item)"
          :prop="item.name"
          :label="item.label"
          :required="item.required"
          v-for="(item, index) in formColumns"
          :key="index"
        >
          <Row type="flex" align="middle">
            <Col style="flex: 1">
              <Select
                :transfer="true"
                :prop="item.name"
                v-model="selfFormData[item.name]"
                :disabled="item.disabled"
                v-if="item.type === 'select'"
                :placeholder="item.placeholder"
              >
                <Option
                  v-for="option in item.data"
                  :key="index + option.value"
                  :value="option.value"
                  >{{ option.label }}</Option
                >
              </Select>
              <CheckboxGroup v-if="item.type === 'checkbox'" v-model="selfFormData[item.name]"  @on-change="(data) => {checkAllGroupChange(item.name, data)}">
                <Checkbox v-for="option in item.data" :key="index + option.value" :label="option.value">{{option.name}}</Checkbox>
              </CheckboxGroup>
              <slot :name="item.name" v-else-if="item.type === 'slot'" ></slot>
              <Input
                v-model="selfFormData[item.name]"
                :type="item.type"
                :disabled="item.disabled"
                :rows="item.rows"
                :placeholder="item.placeholder"
                v-else-if="item.type"
              />
              <Input
                :showWordLimit="item.showWordLimit && !item.disabled"
                :maxlength="item.maxlength"
                v-model="selfFormData[item.name]"
                :disabled="item.disabled"
                :placeholder="item.placeholder"
                v-else
              />
							<p v-if="item.tips" style="color: #ccc;">{{ item.tips }}</p>
            </Col>
            <Col>
              <slot :name="`${item.name}_after`"></slot>
            </Col>
          </Row>
          <slot :name="`${item.name}_extra`" v-if="item.extra">
            <div class="extra">
              <p>{{ item.extra }}</p>
            </div>
          </slot>
        </FormItem>
      </Form>
      <div slot="footer">
        <Button @click="handleCancel()" v-if="showCancelBtn">{{
          cancelText
        }}</Button>
        <Button type="primary" @click="checkSave('form')">{{
          loading ? '保存中...' : okText
        }}</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { post } from '@api/request';
export default {
  name: 'formModal',
  props: {
    saveCallBack:{
      type:Function
    },
    showCancelBtn: {
      type: Boolean,
      default: false
    },
    validate: {
      type: Boolean,
      default: false
    },
    width: {
      type: [String, Number],
      default: 400
    },
    className: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '编辑'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    okText: {
      type: String,
      default: '保存'
    },
    labelWidth: {
      type: [Number, String],
      default: 80
    },
    height: {
      type: Number,
      default: 0
    },
    zIndex: {
      type: Number,
      default: 1000
    },
    saveApi: {
      type: String,
      default: ''
    },
    showModal: {
      type: Boolean,
      default: false
    },
    formColumns: {
      type: Array
    },
    formData: {
      type: Object
    },
    postDataKey: {
      type: String,
      default: ''
    },
    validateRules: {
      type: Object,
      default: () => {
        return {};
      }
    },
    formStyle: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      show: false,
      loading: false,
      selfFormData: {},
      selfFormStyle: {
        overflowY: 'auto'
      },
      currentClassName: ' vertical-center-modal body-no-padding '
    };
  },
  watch: {
    formData: {
      deep: true,
      handler(newValue) {
        this.selfFormData = newValue;
      }
    },
    showModal(newValue) {
      this.show = newValue;
      if (this.show) {
        this.loading = false;
      }
    },
    height(newValue) {
      this.selfFormStyle.maxHeight = newValue + 'px';
    },
    selfFormData: {
      deep: true,
      handler(formData) {
        this.$emit('on-change', formData);
      }
    }
  },
  created() {
    this.show = this.showModal;
    this.currentClassName += this.className;
    this.selfFormData = this.formData;
    this.setSelfFormStyle();
  },
  methods: {
    isShow(item) {
      if (item.show === undefined) {
        return true;
      }
      if (typeof item.show === 'function') {
        return item.show(this.selfFormData);
      }
      return item.show;
    },
    checkAllGroupChange (name, data) {
      this.selfFormData[name] = data;
    },
    resetFields() {
      this.$refs.form.resetFields();
    },
    setSelfFormStyle() {
      this.selfFormStyle.maxHeight =
        (this.height || this.getTableHeight()) + 'px';
      this.selfFormStyle = {
        ...this.selfFormStyle,
        ...this.formStyle
      };
    },
    handleCancel() {
      this.$refs.form.resetFields();
      this.$emit('on-cancel');
    },
    handleVisibleChange(visible) {
      this.$emit('on-visible-change', visible);
    },
    checkSave() {
      if (!this.validate) {
         if(typeof this.saveCallBack == 'function'){
            this.saveCallBack(()=>{
                 this.save();
            })
         }else{
            this.save();
         }
        return false;
      }
      this.$refs.form.validate(valid => {
        if (valid) {
          if(typeof this.saveCallBack == 'function'){
            this.saveCallBack(()=>{
                 this.save();
            })
         }else{
            this.save();
         }
        } else {
          this.errorMessage('信息填写不完整');
        }
      });
    },
    save() {
      //        this.$refs[ref].validate((valid) => {
      //          if (valid) {
      if (this.saveApi) {
        if (this.loading) {
          // return false;
        }
        this.loading = true;
        let postData = {};
        if (this.postDataKey) {
          postData[this.postDataKey] = this.selfFormData;
        } else {
          postData = this.selfFormData;
        }
        postData = this.deepClone(postData);
        this.formColumns.map(item => {
          if (item.type === 'checkbox' && postData[item.name] || item.paramsType === 'Array') {
            postData[item.name] = postData[item.name].join(',');
          }
        })
        post(this.saveApi, postData).then(
          res => {
            this.loading = false;
            if (res.status) {
              this.successMessage(
                res.message ? res.message : `${this.okText}成功`
              );
               this.resetFields();
              this.$emit('on-success', this.selfFormData);
            } else {
              this.errorMessage(res.message);
              this.$emit('on-error', this.selfFormData);
            }
          },
          () => {
            this.loading = false;
          }
        );
      } else {
        this.$emit('on-save', this.selfFormData);
      }
      // 清空校验状态
      //          } else {
      //            this.modalError('请正确填写信息', 0);
      //          }
      //        });
    }
  }
};
</script>
<style lang="less">
#form-modal {
}
.body-no-padding {
  .ivu-modal-body {
    padding: 0;
    overflow: hidden !important;
  }
  .modal-form {
    padding: 16px;
  }
}
</style>
<style lang="less" scoped="">
.extra {
  color: #999;
  line-height: 18px;
  margin-top: 15px;
}
</style>
