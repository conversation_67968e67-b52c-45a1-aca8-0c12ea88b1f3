<template>
  <div style="display: inline-block">
    <form onautocomplete="off" onsubmit="return false">
      <input
        :timestamp="timestamp"
        :ref="refs"
        name="number-input"
        :class="{ number_input: true, _dis: disabled }"
        @input="change"
        @enter="handleEnter"
        @change="change"
				@keyup.down="handleDown"
				@keyup.up="handleUp"
        @keyup.enter="handleEnter"
        @focus="handleFocus"
        @blur="handleBlur"
        @click="handleClick"
        autocomplete="off"
        spellcheck="true"
        :maxlength="maxlength"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :autofocus="autofocus"
        :editable="editable"
        :tab="tab"
        :max="max"
        :min="min"
        :value="currentValue"
      />
    </form>
  </div>
</template>

<script>
import { value } from 'lodash-es';
import Emitter from 'view-design/src/mixins/emitter';
export default {
  name: 'NumberInput',
  autoRegister: true,
  mixins: [Emitter],
  props: {
    max: {
      type: [Number, String],
      default: Infinity
    },
    min: {
      type: [Number, String],
      default: -Infinity
    },
		allowNegativeDecimal: {
			type: Boolean,
			default: false
		},
    // step: {
    //   type: Number,
    //   default: 1
    // },
    value: {
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autofocus: {
      type: Boolean,
      default: false
    },
    intOnly: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    editable: {
      type: Boolean,
      default: true
    },
    name: {
      type: [Number, String]
    },
    precision: {
      type: [Number, String]
    },
    elementId: {
      type: [Number, String]
    },
    refs: {
      type: [Number, String],
      default: ''
    },
    placeholder: {
      type: [Number, String],
      default: '请输入'
    },
    formatter: {
      type: Function
    },
    parser: {
      type: Function
    },
    required: {
      type: Boolean,
      default: false
    },
    tab: {
      type: Boolean,
      default: false
    },
    selectAll: {
      type: Boolean,
      default: true
    },
    // 原有输入框在清空输入框之后没有回传空值
    canEmpty:{
      type:Boolean,
      default:false
    },
    maxlength: {
      type: [Number, String],
      default: ''
    },
		uniqueKey: {
			type: [Number, String],
			default: ''
		},
		enterReturnThis: {
			type: Boolean,
			default: false
		}
  },
  data() {
    return {
      currentValue: this.value,
      timestamp: new Date().getTime() * Math.floor(Math.random() * 100)
    };
  },
  computed: {
    formatterValue() {
      if (this.formatter && this.precisionValue !== null) {
        return this.formatter(this.precisionValue);
      } else {
        return this.precisionValue;
      }
    }
  },
  created() {
  },
  beforeDestroy () {
    // 解绑input事件，解决内存泄露问题
    this._vnode.children.forEach(vnode => {
      if (vnode.tag === 'form') {
        vnode.children.forEach(childVnode => {
          if (childVnode.tag === 'input' && childVnode.data && childVnode.data.on) {
            Object.keys(childVnode.data.on).forEach(event => {
              childVnode.elm.removeEventListener(event, childVnode.data.on[event]._wrapper);
            });
            if (childVnode.elm.remove) {
              childVnode.elm.remove();
            }
          }
        })
      }
    });
  },
  methods: {
    setValue(val, event) {
      // 如果 step 是小数，且没有设置 precision，是有问题的
      if (!isNaN(this.precision) && !this.canEmpty)
        val = Number(Number(val).toFixed(this.precision));
      this.$nextTick(() => {
        this.currentValue = val;
        this.$emit('input', val);
        this.$emit('on-change', val);
        this.$emit('on-check', event)
        this.dispatch('FormItem', 'on-form-change', value);
      });
    },
    handleEnter(event) {
			if (this.enterReturnThis) {
				this.$emit('on-enter', { event, _this: this });
			} else {
				this.$emit('on-enter', event);
			}
    },
		handleDown(event) {
			this.$emit('on-down', { event, _this: this });
		},
		handleUp(event){
			this.$emit('on-up', { event, _this: this });
		},
    handleFocus(event) {
      if (this.selectAll) {
        event.target.select();
      }
      this.$emit('on-focus', event);
    },
    handleClick (event) {
      this.$emit('on-click', event);
    },
    handleBlur(event) {
      let val = event.target.value.trim();
      if(this.min === 0 && val === '') {
        val = 0
        // 0 和 '' vue组件不会更新必须强制更新
        this.$forceUpdate()
      }
      this.$emit('on-blur', event);
    },
    change(event) {
      const { min, max } = this;
      let val = event.target.value.trim();
      let docNum = val.split('.').length - 1;
      val = docNum > 1 ? `${val.split('.')[0]}.${val.split('.')[1]}` : val;
      event.target.value = val;
      if (val < min) {
        val = min;
        event.target.value = min; // 如果用户输入的号码较大，可以提前防止此事件。 否则，更改将处理此事件。
      }
      val = val.toString();
      if (event.type === 'input' && val.includes('.') && this.intOnly) {
        val = val.replace(/\./g, '');
        this.setValue(val, event);
        return;
      }
      if(this.canEmpty && val === ''){
        this.setValue('', event);
        return;
      }
			
			// 新改的功能, 防止影响到其他的输入框, 先使用配置项
			if (this.allowNegativeDecimal) {
				// 修改此处的正则表达式以允许负数和小数输入
				if (event.type === 'input' && val.match(/^\-?\d*\.?\d*$/)) return; // 匹配负数、小数或空输入
			}
			
      // eslint-disable-next-line
      if (event.type === 'input' && val.match(/^\-?\.?$|\.$/)) return; // 小数点后的错误事件阻断
      const isEmptyString = val.length === 0;
      val = Number(val);
      if (isEmptyString) {
        this.setValue(null, event);
        return;
      }
      if (event.type === 'change') {
        if (val === this.currentValue && val > min && val < max) return; // 已经为输入事件触发了更改
      }
      if (!isNaN(val) && !isEmptyString) {
        this.currentValue = val;
        if (val > max) {
          this.setValue(max, event);
        } else if (val < min) {
          this.setValue(min, event);
        } else {
          this.setValue(val, event);
        }
      } else {
        event.target.value = this.currentValue;
      }
    }
  },
  watch: {
    value(val) {
      this.currentValue = val;
    }
  }
};
</script>

<style lang="scss" scoped>
.number_input {
  display: inline-block;
  border-radius: 2px;
  width: 100%;
  padding-left: 10px;
  &:focus {
    border-color: #03ac54;
  }
  &:hover {
    border-color: #03ac54;
  }
  &::placeholder {
    color: #999;
  }
}
._dis {
  background: #f7f7f7;
  color: rgba(0,0,0,0.4);
}
._dis:hover {
  cursor: not-allowed;
  border-color: #e0e0e0;
}
</style>
