<template>
  <div :class="classes" v-clickoutside="handleClose">
    <div :class="[prefixCls + '-rel']" @click="toggleOpen" ref="reference">
      <input type="hidden" :name="name" :value="currentValue">
      <i-input @on-focus="handleFocus" @on-change="handleInput" :element-id="elementId" ref="input" class="text-ellipsis" :title="displayInputRender" :readonly="!filterable" :disabled="disabled" v-model="query" :size="size" :placeholder="inputPlaceholder"></i-input>
      <div :class="[prefixCls + '-label']" v-show="filterable && query === ''" @click="handleFocus">{{ displayRender }}</div>
      <Icon type="ios-close" :class="[prefixCls + '-arrow']" v-show="showCloseIcon" @click.native.stop="clearSelect"></Icon>
      <Icon type="arrow-down-b" :class="[prefixCls + '-arrow']"></Icon>
    </div>
    <transition name="slide-up">
      <div class="ivu-select-dropdown cascader-multi" v-show="visible" :class="{ [prefixCls + '-transfer']: transfer }" ref="drop" :data-transfer="transfer" v-transfer-dom v-if="!destroy">
        <div>
          <cas-multi-panel @handleClose="handleClose" :value="queryItem" v-if="(data.length && (!filterable || query === ''))" @handleGetSelected="selectedData" @clearQueryItem="queryItem = []" :data="data" :multiple="multiple"></cas-multi-panel>
          <div :class="[prefixCls + '-dropdown']" v-show="filterable && query !== '' && querySelections.length">
            <ul :class="[selectPrefixCls + '-dropdown-list']">
              <virtual-list
                data-key="value"
                :data-sources="querySelections"
                :data-component="itemComponent"
                style="height: 180px; overflow-y: auto;"
                :estimate-size="32"
                :extra-props="{
                  selectPrefixCls: selectPrefixCls
                }"></virtual-list>
            </ul>
          </div>
          <ul v-show="filterable && query !== '' && !querySelections.length" :class="[prefixCls + '-not-found-tip']">
            <li>暂无数据</li>
          </ul>
        </div>
      </div>
    </transition>
  </div>
</template>
<script>
import casMultiPanel from "./cascader-multi-panel.vue";
import clickoutside from "view-design/src/directives/clickoutside";
import TransferDom from "view-design/src/directives/transfer-dom";
import { oneOf } from 'view-design/src/utils/assist';
import Emitter from 'view-design/src/mixins/emitter';
import {
  getSelections,
  getSelectItem
} from '../utils';
import VirtualList from 'vue-virtual-scroll-list';
import selectItem from "./selectItem.vue";
import Bus from '@api/bus.js';

const prefixCls = "ivu-cascader";
const selectPrefixCls = "ivu-select";

export default {
  name: "cascaderMulti",
  mixins: [Emitter],
  directives: {
    clickoutside,
    TransferDom
  },
  components: {
    casMultiPanel,
    VirtualList
  },
  provide() {
    return {
      cascaderVm: this
    };
  },
  data() {
    return {
      prefixCls: prefixCls,
      selectPrefixCls: selectPrefixCls,
      // 可见
      visible: false,
      // 是否销毁,用于清除数据销毁组件重新渲染
      destroy: false,
      // 已选择项
      selected: [],
      // 搜索条件
      query: '',
      // 搜索后需要的数据
      queryItem: [],
      itemComponent: selectItem
    };
  },
  props: {
    // 默认已选择数据
    value: {
      type: Array,
      default () {
        return [];
      }
    },
    // 是否支持搜索
    filterable: {
      type: Boolean,
      default: true
    },
    // 绑定数据源数据
    data: {
      type: Array,
      default () {
        return [];
      }
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否支持清除
    clearable: {
      type: Boolean,
      default: true
    },
    // 尺寸大小
    size: {
      validator(value) {
        return oneOf(value, ["small", "large"]);
      }
    },
    // 是否将弹层放置于 body 内，在 Tabs、带有 fixed 的 Table 列内使用时，建议添加此属性，它将不受父级样式影响，从而达到更好的效果
    transfer: {
      type: Boolean,
      default: false
    },
    name: {
      type: String
    },
    // 给表单元素设置 id
    elementId: {
      type: String
    },
    // 输入框占位符
    placeholder: {
      type: String
    },
    // 是否多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 分隔符
    separate: {
      type: String,
      default: "/"
    }
  },
  computed: {
    classes() {
      return [
        `${prefixCls}`,
        {
          [`${prefixCls}-show-clear`]: this.showCloseIcon,
          [`${prefixCls}-size-${this.size}`]: !!this.size,
          [`${prefixCls}-visible`]: this.visible,
          [`${prefixCls}-disabled`]: this.disabled,
          [`${prefixCls}-not-found`]: this.filterable && this.query !== '' && !this.querySelections.length
        }
      ];
    },
    // 判断是否显示clear按钮
    showCloseIcon() {
      return (
        this.currentValue &&
        this.currentValue.length &&
        this.clearable &&
        !this.disabled
      );
    },
    // 格式化显示数据
    displayRender() {
      let label = [];
      for (let i = 0; i < this.selected.length; i++) {
        label.push(this.selected[i].label);
      }
      return label.join(this.separate);
    },
    displayInputRender() {
      return this.filterable ? '' : this.displayRender;
    },
    // 输入框占位符取值
    inputPlaceholder() {
      return this.currentValue.length ? null : this.placeholder;
    },
    // 当前选择项取所有id
    currentValue() {
      return this.selected.map((k, v) => {
        return k.value;
      });
    },
    // 生成搜索结果
    querySelections() {
      let selections = [];
      // 禁止搜索，则不需要生成搜索结果
      if (!this.filterable) return selections;
      getSelections(this.data, null, null, selections);
      selections = selections.filter(item => {
        return item.label ? item.label.indexOf(this.query) > -1 : false;
      }).map(item => {
        item.display = item.display.replace(new RegExp(this.query, 'g'), `<span>${this.query}</span>`);
        return item;
      });
      console.log(selections)
      return selections;
    }
  },
  methods: {
    // 点击搜索结果
    handleSelectItem(index) {
      const item = this.querySelections[index];
      if (item.item.disabled) return false;
      this.query = '';
      this.$refs.input.currentValue = '';
      let temp_data = [];
      getSelectItem(this.data, item.value.split(','), temp_data);
      this.selectedData(temp_data);
      this.queryItem = temp_data;
      this.handleClose();
    },
    handleInput(event) {
      this.query = event.target.value;
      this.onFocus();
    },
    handleFocus(event) {
      this.query = '';
      this.$refs.input.focus();
      // event.target.value = '';
    },
    // 子组件传值
    selectedData(val = []) {
      this.selected = val;
      this.$emit("input", this.currentValue);
      this.$emit("on-change", this.currentValue);
    },
    // 清除选择项
    clearSelect() {
      if (this.disabled) return false;
      this.selectedData();
      this.handleClose();
      // 销毁组件
      this.queryItem = [];
      this.destroy = true;
    },
    // 关闭dropDown
    handleClose() {
      this.visible = false;
    },
    // 页面input点击事件
    toggleOpen() {
      if (this.visible) {
        this.handleClose();
      } else {
        this.onFocus();
      }
    },
    // 显示dropDown
    onFocus() {
      // 禁用状态,不显示dropDown
      if (this.disabled) return;
      this.visible = true;
    },
    // 初始化数据
    init() {
      getSelectItem(this.data, this.value, this.queryItem);
      this.selectedData(this.queryItem);
    }
  },
  watch: {
    // 重新渲染组件
    destroy(val) {
      if (val) {
        this.$nextTick(_ => {
          this.destroy = false;
        });
      }
    },
    value(val) {
      // 绑定的value更改后,判断和当前currentValue是否一致,不一致重新渲染组件
      if (val.join(",") !== this.currentValue.join(",")) {
        this.clearSelect();
        this.init();
      };
    },
    data() {
      // 绑定数据源改变,清空选择项
      this.clearSelect();
    }
  },
  mounted() {
    this.init();
    this.$on('cascader-select-click', this.handleSelectItem);
  }
};
</script>
<style>
.w-full {
  width: 100%;
  user-select: none;
}

.text-ellipsis input {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding-right: 20px;
}

.cas-multi-select-input {
  position: relative;
}

.cascader-multi.ivu-select-dropdown {
  max-height: 230px;
}

.cascader-multi .ivu-cascader-menu {
  min-width: 150px;
  height: 230px;
}

.cas-multi-select-item {
  position: absolute;
  height: 200px;
  width: 80px;
  border: 1px solid #ccc;
}
</style>
