<template>
  <li
    sdp-no-replace
    class="ivu-cascader-menu-item"
    :key="source.id"
    :class="{ 'ivu-cascader-menu-item-active': selected == index }"
  >
    <!-- 多选checkbox -->
    <!-- 全局多选或者当前项为多选 -->
    <template v-if="multiple || source.multiple">
      <Checkbox
        :value="checkBoxGroup.indexOf(index) >= 0"
        @click.native.stop
        :label="index"
        class="w-full"
        >{{ source.label }}
        <!-- 子级箭头标记 -->
        <Icon
          type="ios-arrow-forward"
          v-if="source.children && source.children.length"
        />
      </Checkbox>
    </template>
    <!-- 单选p -->
    <template v-else>
      <p @click="handleClick(index)">
        {{ source.label }}
        <!-- 子级箭头标记 -->
        <Icon
          type="ios-arrow-forward"
          v-if="source.children && source.children.length"
        />
      </p>
    </template>
  </li>
</template>
<script>
export default {
  name: 'item-component',
  props: {
    index: {
      type: Number,
    },
    source: {
      type: Object,
      default() {
        return {};
      },
    },
    multiple: Boolean,
    selected: Number,
    checkBoxGroup: {
      type: Array,
      default: () => [],
    },
  },
  inject: ['panelVm'],
  mounted() {},
  methods: {
    handleClick(index) {
      if (this.panelVm) {
        this.panelVm.$emit('cascader-checkbox-click', index);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.item-component {
}
</style>
