<template>
  <li
    :key="source.label"
    sdp-no-replace
    :class="[
      selectPrefixCls + '-item',
      { [selectPrefixCls + '-item-disabled']: source.disabled },
    ]"
    @click="handleSelectItem(index)"
    v-html="source.display"
  ></li>
</template>
<script>
import Bus from '@api/bus.js';

export default {
  name: 'item-component',
  props: {
    index: {
      type: Number,
    },
    source: {
      type: Object,
      default() {
        return {};
      },
    },
    selectPrefixCls: String,
  },
  inject: ['cascaderVm'],
  mounted() {},
  methods: {
    handleSelectItem(index) {
      if (this.cascaderVm) {
        this.cascaderVm.$emit('cascader-select-click', index);
      }
    },
  },
};
</script>
<style lang="less" scoped>
.item-component {
}
</style>
