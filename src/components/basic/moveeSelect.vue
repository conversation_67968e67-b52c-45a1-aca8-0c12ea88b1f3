<template>
  <Select
    :placeholder="placeholder"
    @on-change="changeVal"
    v-model="currentModel"
    :disabled="disabled"
    :multiple="multiple"
    style="text-align: left"
    :filterable="filterable"
    v-bind="$attrs"
    :filter-by-label="filterByLabel"
  >
    <Option
      v-for="info in optionList"
      :value="info.id"
      :key="info.id"
      :label="info.name"
    >
      <slot v-bind:info="info">{{ info.name }}</slot>
    </Option>
  </Select>
</template>

<script>
import { get, post } from '@api/request.js';
export default {
  name: 'movee-select',
  props: {
    multiple: false,
    value: {
      default: '',
    },
    disabled: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    v_model: '',
    dataChildFields: {
      type: String,
      default: () => {
        return '';
      },
    },
    defaultVal: {
      type: [String, Number],
      default: () => {
        return '';
      },
    },
    url: {
      type: String,
      default: () => {
        return '';
      },
      required: false,
    },
    // 请求参数
    requestData: {
      type: Object,
      default: () => {
        return {};
      },
      required: false,
    },
    JsonData: {
      type: Array,
      default: () => {
        return [];
      },
      required: false,
    },
    _key: {
      type: String,
      default: () => {
        return 'id';
      },
      required: false,
    },
    _label: {
      type: String,
      default: () => {
        return 'name';
      },
      required: false,
    },
    noId: {
      type: Boolean,
      default: () => {
        return false;
      },
    },
    selectSecond: {
      type: String,
      default: () => {
        return '';
      },
      required: false,
    },
    selectFirst: {
      type: String,
      default: () => {
        return '';
      },
      placeholder: '',
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    needAll: {
      type: String,
      default: () => {
        return 'isTrue';
      },
      required: false,
    },
    // 添加全部的时候，选项里有全部的话，只选中全部，其他选项不选中
    needAllOnlyZero: {
      type: Boolean,
      default: false,
    },
    MultipleLabel: {
      type: Array,
      default: () => {
        return [];
      },
      required: false,
    },
    placeholder: '',
    filterByLabel: {
      type: Boolean,
      default: false,
    },
    resHandle: {
      type: Function,
      default: (res) => res,
    },
  },
  data() {
    return {
      currentModel: '',
      optionList: [],
    };
  },
  created() {
    this.optionList = this.JsonData || [];
    this.currentModel = this.defaultVal;
    if (this.url) {
      this.getList();
    }
  },
  methods: {
    async getList() {
      let data = { ...this.requestData };
      let me = this;
      data.token = ez.getCookie('token');
      let res = await get(this.url, data);
      if (res.status) {
        let resData = this.dataChildFields
          ? res.data[this.dataChildFields]
          : res.data; // 检查并获取的data数据tree结构
        this.optionList = this.resHandle(resData).map((item, index, array) => {
          if (this.MultipleLabel.length > 0) {
            return {
              ...item,
              id: item[me._key],
              name: `${item[this.MultipleLabel[0]]} - ${
                item[this.MultipleLabel[1]]
              }`,
            };
          } else if (this.noId) {
            return { ...item, id: item[me._label], name: item[me._label] };
          } else {
            return { ...item, id: item[me._key], name: item[me._label] };
          }
        });
        if (this.needAll === 'isTrue') {
          this.optionList.unshift({ id: 0, name: '全部' });
        }
        if (this.selectFirst === 'isTrue') {
          if (this.defaultVal && this.defaultVal !== '0') {
            this.currentModel = this.defaultVal;
          } else {
            this.currentModel =
              this.needAll === 'isTrue'
                ? this.optionList[1].id
                : this.optionList[0].id;
          }
          this.changeVal();
        }
        if (this.selectSecond === 'isTrue' && !this.selectFirst) {
          this.currentModel =
            this.optionList.length > 1
              ? this.optionList[1].id
              : this.optionList[0].id;
        }
      } else {
        this.$Modal.error({
          title: '错误',
          content: res.message,
        });
      }
    },
    changeVal(val) {
      if (this.needAll === 'isTrue' && this.needAllOnlyZero && val.includes(0)) {
        this.currentModel = [0];
      }
      this.$emit(
        'on-change',
        this.currentModel,
        this.optionList.find((_) => _.id === this.currentModel),
      );
      this.$emit(
        'changeEvent',
        this.currentModel,
        this.optionList.find((_) => _.id === this.currentModel),
      );
    },
  },
  watch: {
    value: {
      immediate: true,
      handler(newValue) {
        this.currentModel = newValue;
      },
    },
    currentModel(value) {
      this.$emit('input', value);
    },
    defaultVal(data) {
      this.currentModel = data;
    },
  },
};
</script>

<style scoped></style>
