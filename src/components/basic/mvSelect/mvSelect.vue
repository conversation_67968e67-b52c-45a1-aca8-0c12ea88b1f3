<template>
  <Select
    @on-change="changeVal"
    v-model="currentModel"
    style="text-align: left"
    @on-open-change="handleOpenChange"
    @keyup.enter.native="handleEnterkeyUP"
    v-bind="$attrs"
  >
    <Option v-for="info in optionList" :value="info.id" :key="info.id" :label="info.name">
      <slot v-bind:info="info">{{ info.name }}</slot>
    </Option>
  </Select>
</template>

<script>
import { get, post } from '@api/request.js';
export default {
  name: 'mv-select',
  props: {
    dataChildFields: {
      type: String,
      default: () => {
        return '';
      }
    },
    defaultVal: {
      type: [String, Number],
      default: () => {
        return '';
      }
    },
    url: {
      type: String,
      default: () => {
        return '';
      },
      required: false
    },
    // 请求参数
    requestData: {
      type: Object,
      default: () => {
        return {};
      },
      required: false
    },
    JsonData: {
      type: Array,
      default: () => {
        return [];
      },
      required: false
    },
    _key: {
      type: String,
      default: () => {
        return 'id';
      },
      required: false
    },
    _label: {
      type: String,
      default: () => {
        return 'name';
      },
      required: false
    },
  },
  data() {
    return {
      currentModel: '',
      optionList: []
    };
  },
  created() {
    this.optionList = this.JsonData || [];
    this.currentModel = this.defaultVal;
    if (this.url) {
      this.getList();
    }
  },
  methods: {
     resetValue () {
      this.currentModel = [];
    },
    async getList() {
      let data = { ...this.requestData };
      let me = this;
      data.token = ez.getCookie('token');
      let res = await get(this.url, data);
      if (res.status) {
        let resData = this.dataChildFields
          ? res.data[this.dataChildFields]
          : res.data; //检查并获取的data数据tree结构
        this.optionList = resData.map((item, index, array) => {
            return { ...item, id: item[me._key], name: item[me._label] };
        });
      } else {
        this.$Modal.error({
          title: '错误',
          content: res.message
        });
      }
    },
    changeVal() {
         this.$emit(
        'on-change',
        this.currentModel,
        this.optionList.find(_ => _.id === this.currentModel)
      );
    },
    handleOpenChange (isOpen) {
      this.$emit('on-open-change', isOpen)
    },
    handleEnterkeyUP (e) {
      this.$emit('on-enter-key-up')
    }
  },
  watch: {
    currentModel(value) {
      this.$emit('input', value);
    },
    defaultVal(data) {
      this.currentModel = data;
    },
    JsonData(data) {
      this.optionList = data;
    }
  }
};
</script>

<style lang="less" scoped>
/deep/.ivu-select-input {
  top: 0;
}
</style>le
