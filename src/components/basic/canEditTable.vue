<template>
  <div>
    <bigdata-table
      :append-num="1"
      :ref="refs" :columns="columnsList" :data="thisTableData"
      @on-selection-change="getSelectedRow" :border="border"
      @on-expand="expendRow" :height="tableHeight" :loading="loading"
      @on-row-click="currentRow" ellipsis></bigdata-table>
  </div>
</template>

<script>
  //定义鼠标hover时可编辑按钮出现的事件
  const incellEditBtn = (vm, h, param) => {
    if (vm.hoverShow) {
      return h('div', {
        'class': {
          'show-edit-btn': vm.hoverShow
        }
      }, [
        h('Button', {
          props: {
            type: 'text',
            icon: 'ios-create-outline'
          },
          on: {
            click: (event) => {
              vm.edittingStore[param.index].edittingCell[param.column.key] = true;
              vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
              // vm.thisTableData[param.index].edittingCell[param.column.key] = true;
            }
          }
        })
      ]);
    } else {
      return h('Button', {
        props: {
          type: 'text',
          size: 'large',
          icon: 'ios-create-outline'
        },
        style: {
          'font-size': '20px',
          'margin-top': '-7px'
        },
        on: {
          click: (event) => {
            vm.edittingStore[param.index].edittingCell[param.column.key] = true;
            vm.thisTableData[param.index].edittingCell[param.column.key] = true;
            // vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
          }
        }
      });
    }
  };

  // 定义确认按钮事件
  const saveIncellEditBtn = (vm, h, param) => {
    return h('Button', {
      props: {
        type: 'text',
        icon: 'md-checkmark'
      },
      on: {
        click: (event) => {
          vm.edittingStore[param.index].edittingCell[param.column.key] = false;
          vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
          vm.$emit('input', vm.handleBackdata(vm.thisTableData));
          vm.$emit('on-cell-change', vm.handleBackdata(vm.thisTableData), param.index, param.column.key);
        }
      }
    });
  };
  // 定义可编辑框类型 并初始化值和事件
  const cellInput = (vm, h, param, item) => {
    let inputType = item.editObj ? item.editObj.type : 'Input';
    switch (inputType) {
      case 'Input':
        return h(inputType, {
          props: {
            value: vm.edittingStore[param.index][item.key]
          },
          on: {
            'on-change' (event) {
              let key = item.key;
              vm.edittingStore[param.index][key] = event.target.value;
            },
            'on-blur'() {
              vm.thisTableData = JSON.parse(JSON.stringify(vm.edittingStore));
              vm.$emit('input', vm.handleBackdata(vm.thisTableData));
              vm.$emit('on-cell-change', vm.handleBackdata(vm.thisTableData), param.index, param.column.key);
            }
          }
        });
      case 'InputNumber':
        return h(inputType, {
          props: {
            value: Number(vm.edittingStore[param.index][item.key]),
            min: 0
          },
          style: {
            width: '100%'
          },
          on: {
            'on-change'(val) {
              let key = item.key;
              if (val !== vm.edittingStore[param.index][key]) {
                vm.edittingStore[param.index].changed = true;
              } else {
                vm.edittingStore[param.index].changed = false;
              }
              vm.edittingStore[param.index][key] = Number(val);
            },
            'on-focus'(event) {
              // event.target.select();
            },
            'on-blur'() {
              let key = item.key;
              if (vm.edittingStore[param.index].changed) {
                vm.thisTableData[param.index][key] = vm.edittingStore[param.index][key];
                vm.$emit('input', vm.handleBackdata(vm.thisTableData));
                vm.$emit('on-cell-change', vm.handleBackdata(vm.thisTableData), param.index, param.column.key);
              }
            }
          }
        });
      case 'Select':
        vm.edittingStore[param.index]['selectedVal'] = 1;
        item.editObj.data.map((_item) => {
          if(_item.label === param.row.name) {
            vm.edittingStore[param.index]['selectedVal'] = _item.value;
          }
        });
        return h(inputType, {
            props: {
              value: vm.edittingStore[param.index]['selectedVal'],
              'label-in-value': true
            },
            on: {
              'on-change' (val) {
                let key = item.key;
                vm.edittingStore[param.index]['selectedVal'] = val.value;
                vm.edittingStore[param.index][key] = val.label;
              }
            }
          },
          item.editObj.data.map(opt => {
            return h('Option', {
              props: {
                key: opt.value,
                value: opt.value
              }
            }, opt.label);
          }));
      case 'Cascader':
        // vm.edittingStore[param.index]['cascaderVal'] = param.row.categoryId;
        return h(inputType, {
          props: {
            transfer: true,
            data: item.editObj.data,
            value: vm.edittingStore[param.index]['cascaderVal'],
            // 'change-on-select': true
          },
          on: {
            'on-change' (val, selectedData) {
              let key = item.key;
              if (selectedData) {
                let len = selectedData.length;
                vm.edittingStore[param.index]['cascaderVal'] = val;
                vm.edittingStore[param.index]['categoryId'] = val;
                param.row.cascaderVal = val;
                param.row.categoryId = val;
                if (len > 1) {
                  vm.edittingStore[param.index][key] = selectedData[len - 1]['__label'];
                } else if(len === 1) {
                  vm.edittingStore[param.index][key] = selectedData[0].label
                }
              }
            }
          }
        });
    }
  };

  export default {
    name: 'canEditTable',
    components: {
    },
    props: {
      refs: String,
      border: {
        type: Boolean,
        default: false
      },
      loading: false,
      columnsList: Array,
      value: Array,
      url: String,
      tableHeight: Number,
      editIncell: {
        type: Boolean,
        default: false
      },
      hoverShow: {
        type: Boolean,
        default: false
      }
    },
    data () {
      return {
        columns: [],
        thisTableData: [],
        edittingStore: []
      };
    },
    created () {
      this.init();
    },
    methods: {
      //获取选择项
      checkSelect(row, selection) {
        this[row].forEach((item) => {
          item._checked = false;
          selection.forEach( d=> {
            if (item.commodity_id === d.commodity_id) {
              item._checked = true;
            }
          });
        });
      },
      getSelectedRow(selection) {
        this.checkSelect('edittingStore', selection);
        this.checkSelect('thisTableData', selection);
        this.$emit('selection-click', selection);
      },
      // 展开详情列
      expendRow(row, status, index) {
        this.$emit('expend-click', row, status, index);
      },
      currentRow(row, index) {
        this.$emit('currentRow-index', row, index);
      },
      // 父组件更新row
      updateRow(data, index) {
        this.thisTableData[index].order_num = data.order_num;
        this.thisTableData[index].convert_amount = data.convert_amount;
        this.thisTableData[index].transit_stock = data.transit_stock;
        this.thisTableData[index].purchases = data.purchases;
        this.thisTableData[index].stock = data.stock;
        this.thisTableData[index]._expanded = true;
      },
      // 初始化表
      init () {
        let vm = this;
        let editableCell = this.columnsList.filter(item => {
          if (item.editable) {
            if (item.editable === true) {
              return item;
            }
          }
        });
        let cloneData = JSON.parse(JSON.stringify(this.value));
        let res = [];
        res = cloneData.map((item, index) => {
          let isEditting = false;
          if (this.thisTableData[index]) {
            if (this.thisTableData[index].editting) {
              isEditting = true;
            } else {
              for (const cell in this.thisTableData[index].edittingCell) {
                if (this.thisTableData[index].edittingCell[cell] === true) {
                  isEditting = true;
                }
              }
            }
          }
          if (isEditting) {
            return this.thisTableData[index];
          } else {
            this.$set(item, 'editting', false);
            let edittingCell = {};
            editableCell.forEach(item => {
              edittingCell[item.key] = false;
            });
            this.$set(item, 'edittingCell', edittingCell);
            return item;
          }
        });
        res.forEach((item) => {
          item.cascaderVal = item.categoryId;
        });
        this.thisTableData = res;
        this.edittingStore = JSON.parse(JSON.stringify(this.thisTableData));
        this.columnsList.forEach(item => {
          if (item.editable) {
            item.render = (h, param) => {
              let currentRow = this.thisTableData[param.index];
              if (!currentRow.editting) {
                if (currentRow.canEdit) {
                  if (this.editIncell && currentRow.canEdit === 'isTrue') {
                    return h('div', {
                      style: {
                        display: 'flex',
                        textAlign: 'left'
                      }
                    }, [
                      h('div', {
                        style: {
                          flex: 4
                        }
                      }, [
                        !currentRow.edittingCell[param.column.key] ? h('span', {style: {lineHeight: '32px'}}, currentRow[item.key]) : cellInput(this, h, param, item)
                      ]),
                      h('div', [
                        currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                      ])
                    ]);
                  } else {
                    return h('span', currentRow[item.key]);
                  }
                } else {
                  if (this.editIncell) {
                    if (currentRow.notEdit) {
                      return h('span', currentRow[item.key]);
                    } else {
                      return h('div', {
                        style: {
                          display: 'flex',
                          textAlign: 'left'
                        }
                      }, [
                        h('div', {
                          style: {
                            flex: 4
                          }
                        }, [
                          cellInput(this, h, param, item)
                          // !currentRow.edittingCell[param.column.key] ? h('span', {style: {lineHeight: '32px'}}, currentRow[item.key]) : cellInput(this, h, param, item)
                        ]),
                        // h('div', [
                        //   currentRow.edittingCell[param.column.key] ? saveIncellEditBtn(this, h, param) : incellEditBtn(this, h, param)
                        // ])
                      ]);
                    }
                  } else {
                    return h('span', currentRow[item.key]);
                  }
                }
              } else {
                return h('Input', {
                  props: {
                    type: 'text',
                    value: currentRow[item.key]
                  },
                  on: {
                    'on-change' (event) {
                      let key = param.column.key;
                      vm.edittingStore[param.index][key] = event.target.value;
                    }
                  }
                });
              }
            };
          }
          // 处理定义的handle
          if (item.handle) {
            item.render = (h, param) => {
              let currentRowData = this.thisTableData[param.index];
              let children = [];
              item.handle.forEach(item => {
                if (item === 'edit') {
                  children.push(editButton(this, h, currentRowData, param.index));
                } else if (item === 'delete') {
                  children.push(deleteButton(this, h, currentRowData, param.index));
                }
              });
              return h('div', children);
            };
          }
        });
      },
      // 过滤掉处理数据加的字段,返回更新后的原始数据
      handleBackdata (data) {
        let clonedData = JSON.parse(JSON.stringify(data));
        clonedData.forEach(item => {
          delete item.editting;
          delete item.edittingCell;
          delete item.saving;
        });
        return clonedData;
      }
    },
    watch: {
      value (data) {
        this.init();
      }
    }
  };
</script>

<style lang="less" scoped>
  .show-edit-btn{
    display: none;
    margin-left: -10px;
  }
  .ivu-table-cell:hover .show-edit-btn{
    display: inline-block;
  }
</style>

