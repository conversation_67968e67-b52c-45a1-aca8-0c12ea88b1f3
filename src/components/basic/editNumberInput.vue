<template>
  <div style="display: inline-block">
    <form onautocomplete="off" onsubmit="return false">
      <div class="input-box">
        <input
        :timestamp="timestamp"
        :ref="refs"
        name="number-input"
        class="number_input"
        @input="change"
        @enter="handleEnter"
        @change="change"
        @keyup.enter="handleEnter"
        @focus="handleFocus"
        @blur="handleBlur"
        autocomplete="off"
        spellcheck="true"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :autofocus="autofocus"
        :editable='editable'
        :tab="tab"
        :max="max"
        :min="min"
        :value="currentValue">
        <div class="edit-box">
          <Icon @click.native="arrowUp" class="arrow-up" type="arrow-up-b"></Icon>
          <Icon @click.native="arrowDown" class="arrow-down" type="md-arrow-dropdown"></Icon>
        </div>
      </div>
    </form>
  </div>
</template>

<script>
  export default {
    name: 'editNumberInput',
    autoRegister: true,
    props: {
      max: {
        type: [Number, String],
        default: Infinity
      },
      min: {
        type: [Number, String],
        default: -Infinity
      },
      // step: {
      //   type: Number,
      //   default: 1
      // },
      value: {
        default: 1
      },
      disabled: {
        type: Boolean,
        default: false
      },
      autofocus: {
        type: Boolean,
        default: false
      },
      intOnly: {
        type: Boolean,
        default: false
      },
      readonly: {
        type: Boolean,
        default: false
      },
      editable: {
        type: Boolean,
        default: true
      },
      name: {
        type: [Number, String]
      },
      precision: {
        type: [Number, String]
      },
      elementId: {
        type: [Number, String]
      },
      refs: {
        type: [Number, String],
        default: ''
      },
      placeholder: {
        type: [Number, String],
        default: '请输入'
      },
      formatter: {
        type: Function
      },
      parser: {
        type: Function
      },
      required: {
        type: Boolean,
        default: false
      },
      tab: {
        type: Boolean,
        default: false
      },
      selectAll: {
        type: Boolean,
        default:  true
      }
    },
    data () {
      return {
        currentValue: this.value,
        timestamp: (new Date().getTime()) * Math.floor(Math.random()*100)
      }
    },
    computed: {
      formatterValue () {
        if (this.formatter && this.precisionValue !== null) {
          return this.formatter(this.precisionValue);
        } else {
          return this.precisionValue;
        }
      }
    },
    methods: {
      setValue (val) {
        // 如果 step 是小数，且没有设置 precision，是有问题的
        if (!isNaN(this.precision)) val = Number(Number(val).toFixed(this.precision));
        this.$nextTick(() => {
          this.currentValue = val;
          this.$emit('input', val);
          this.$emit('on-change', val);
        });
      },
      handleEnter (event) {
        this.$emit('on-enter', event);
      },
      handleFocus (event) {
        if (this.selectAll) {
          event.target.select();
        }
        this.$emit('on-focus', event);
      },
      handleBlur (event) {
        this.$emit('on-blur', event);
      },
      arrowUp() {
        if(this.currentValue&&this.currentValue>=this.max) {
            return false;
        }
        this.currentValue++
        this.$emit('input', this.currentValue);
        this.$emit('on-change', this.currentValue);
      },
      arrowDown() {
        if(this.currentValue <= this.min) {
            return false;
        }
        this.currentValue--
        this.$emit('input', this.currentValue);
        this.$emit('on-change', this.currentValue);
      },
      change (event) {
        const {min, max} = this;
        let val = event.target.value.trim();
        let docNum = (val.split('.')).length - 1;
        val = docNum > 1 ? `${val.split('.')[0]}.${val.split('.')[1]}` : val;
        event.target.value = val;
        if (val < min) {
          val = min;
          event.target.value = min; // 如果用户输入的号码较大，可以提前防止此事件。 否则，更改将处理此事件。
        }
        val = val.toString();
        if (event.type === 'input' && val.includes('.') && this.intOnly) {
          val = val.replace(/\./g, '');
          this.setValue(val);
          return;
        }
        if (event.type === 'input' && val.match(/^\-?\.?$|\.$/)) return; // 小数点后的错误事件阻断
        const isEmptyString = val.length === 0;
        val = Number(val);
        if(isEmptyString){
          this.setValue(null);
          return;
        }
        if (event.type === 'change'){
          if (val === this.currentValue && val > min && val < max) return; // 已经为输入事件触发了更改
        }
        if (!isNaN(val) && !isEmptyString) {
          this.currentValue = val;
          if (val > max) {
            this.setValue(max);
          } else if (val < min) {
            this.setValue(min);
          } else {
            this.setValue(val);
          }
        } else {
        //  console.log(this.currentValue)
          event.target.value = this.currentValue;
        }
      },
    },
    watch: {
      value (val) {
        this.currentValue = val;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .input-box {
    border-radius: 4px;
    width: 100%;
    height: 32px;
    // line-height: 32px;
    font-size: 12px;
    border: 1px solid #d8d8d8;
    border-radius: 4px;
    color: #333333;
    background-color: #fff;
    .number_input {
        // display: inline-block;
        float: left;
        width: 70%;
        height: 100%;
        border-style:none;
        // &:focus {
        // border-color: #03ac54;
        // }
        // &:hover {
        // border-color: #03ac54;
        // }
    }
    .edit-box{
        // display: inline-block;
        height: 100%;
        float: right;
        width: 20px;
        padding-top: 4px;
        // padding: 4px;
        //   text-align: center;
        .arrow-up {
          display: block;
          font-size: 16px;
          margin-bottom: -8px;
          cursor: pointer;
        // margin-top: 20px;
        }
        .arrow-down {
        //  margin-top: -10px;
          cursor: pointer;
          font-size: 16px;
        }
    }
  }
</style>
