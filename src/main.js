import Vue from 'vue';
import './public-path';
import InitComponent from './init/init-iview.js';
InitComponent(Vue);
import('./init/init-vxe-table.js');
import('./init/init-viewer.js');
import { handleRouteBeforeEnter } from 'prefetch-preload/esm/vue2';
import './assets/js/ez';
import('@/init/init-translate');

// import Vuex from 'vuex'
import App from './App';
import store from './vuex';
import router from './router';
import axios from 'axios';
import { Notice } from 'view-design';
import './util/Math';
import 'normalize.css';
import '@assets/scss/default.css';
import '@assets/scss/pagesCommon.scss';
import '@assets/scss/mixin.scss';
import '@assets/styles/all.less';
import './assets/font/iconfont/iconfont.css';
import './assets/less/global.less';
import './assets/less/basic-theme.less';
import { get, post } from './api/request';
import { api } from './api/api';
import globalConfig from './config/globalConfig';
import util from './api/util';
import commonService from './api/main';
import importDirective from './directive';
import CommonUtil from './util/common';
import Global from './util/global';
import logger from './util/logger';
import { SentryInit } from '@/init/init-sentry.js';
import { sdpReplaceText } from './replace';

import { initSdpComponent } from '@/init/init-sdp.js';
initSdpComponent();

import { initSyncComponent } from '@/init/init-local-auto-components';
initSyncComponent();

import { SLS_CLIENT } from '@aliyun-sls/web-browser';
import { init, addTools, removeTool, initPage } from '@sdp/xiaodong-sdk';
import '@sdp/xiaodong-sdk/lib/index.css';
import Cookies from 'js-cookie';

// 处理函数
function transformRoutes(routes) {
  return routes.map((route) => {
    let transformed = {};
    if (route.meta instanceof Array && route.meta.length) {
      const meta = route.meta[0] || { title: '' };
      transformed = {
        path: route.path || '',
        name: meta.title || route.name || '',
      };
    } else {
      transformed = {
        path: route.path || '',
        name: route.name || '',
      };
    }
    if (route.children && Array.isArray(route.children)) {
      transformed.children = transformRoutes(route.children);
    }
    return transformed;
  });
}

function cleanRoutesForAI(routes) {
  return routes.map((route) => {
    const { component, ...rest } = route;
    const cleanedRoute = { ...rest };
    if (cleanedRoute.children && Array.isArray(cleanedRoute.children)) {
      cleanedRoute.children = cleanRoutesForAI(cleanedRoute.children);
    }
    return cleanedRoute;
  });
}

setTimeout(() => {
  const isOpenAiChatDialog = Number(
    store.state.sysConfig.is_open_ai_chat_dialog,
  );
  // console.log('sysConfig', isOpenAiChatDialog,store.state.sysConfig.is_open_ai_chat_dialog);
  if (isOpenAiChatDialog) {
    let routes = cleanRoutesForAI(router.options.routes);
    removeTool('addOrder');
    init({
      url:
        process.env.NODE_ENV === 'development'
          ? 'http://localhost:3000'
          : 'https://ai-chat.movee.cn',
      // url: 'https://ai-chat.movee.cn',
      // url: 'https://ai.xiaodong.chat',
      // onRouteChange: (url) => history.pushState(null, '', url),
      options: {
        onRouteChange: (url) => router.push(url),
        systemContext: `当前系统路由信息：${JSON.stringify(routes)}`,
      },
      params: {
        admin_token: window.localStorage.getItem('admin_token'),
        admin_host: window.location.host,
        cookie: Cookies.get('ccode'),
      },
      toolCallback: async (toolName, params) => {
        switch (toolName) {
          case 'getCustomerInputValue':
            return params;
          case 'addOrder':
            console.log('addOrder123', params);
            const result = await Vue.prototype.$handleSetname(
              params.customerName,
              params.goodsInfo,
            );
            console.log('result', result);
            return result;
          default:
            return '没有对应工具方法';
        }
      },
    });
  }
}, 1000);

// 微前端模式下主应用中已经加了全链路
if (!window.__POWERED_BY_QIANKUN__) {
  SLS_CLIENT.init({
    host: 'cn-beijing.log.aliyuncs.com',
    project: 'sdp-trace',
    logstore: 'sdp-trace-raw',
    workspace: 'sdp-trace',
    env: process.env.NODE_ENV,
    service: 'sdpbase-pro',
    enableRuntimeError: true,
    enableResourcePerf: true,
    enableRequest: true,
    enablePerf: true,
    enableTrace: true,
  });
}
console.log('process.env.NODE_ENV', process.env.NODE_ENV);
const debugPrintEditor = false;
if (process.env.NODE_ENV === 'development' && debugPrintEditor) {
  window.printEditorUrl = 'https://localhost:8888/webprint/#/editor';
}
Vue.config.performance = true;
Vue.config.devtools = true;
// 将 logger 方法曝露到全局供打印 JS 使用
window.__loggerForPrint = logger.jsLogger.bind(logger);

import {
  globalErrorTrace,
  networkTrace,
} from './util/performanceAndErrorDetective';
import { addCommonlyUsed } from './service/commonlyUsed';
import storage from './util/storage';
import UI from '@sdp/ui';
Vue.use(UI);

// 初始化本地业务配置
const localBusinessConfig = storage.getLocalStorage('business_config');
if (localBusinessConfig) {
  store.commit('setSysConfig', localBusinessConfig);
}

// 初始化文案配置
const localEntityConfig = storage.getLocalStorage('sys_entity_config');

if (localEntityConfig) {
  store.commit('setSysEntityConfig', localEntityConfig);
}

// 自动注册autoRegister为true的组件
import smodal from '@/components/modal';

Vue.use(smodal);
importDirective(Vue);

globalErrorTrace();
networkTrace();

Vue.component('BigdataTable', (resolve) =>
  require(['./components/common/bigdata-table'], resolve),
);

// 挂载公共服务到Vue原型，方便调用
Vue.prototype.commonService = commonService;
Vue.prototype.showTaskCenter = function () {
  this.$store.commit('showTaskCenter', true);
};

Vue.prototype.util = util;
Vue.prototype.globalConfig = globalConfig;
Vue.prototype.addTools = addTools;
Vue.prototype.removeTool = removeTool;
Vue.prototype.initPage = initPage;

// 挂载ajax到Vue原型，方便调用
Vue.prototype.$request = {
  get,
  post,
};
Vue.config.productionTip = false;
Vue.prototype.apiUrl = api;

// 检测上次页面是否崩溃
let goodExit = window.localStorage.getItem('good_exit');
let stack = {};
if (window.performance) {
  stack = {
    memory: JSON.stringify(window.localStorage.getItem('lastPageMemory')),
  };
}
if (goodExit === '0') {
  logger.jsLogger({
    logType: 'page-crash',
    url: window.localStorage.getItem('lastPage'),
    stack,
  });
}

router.afterEach(() => {
  store.dispatch('setRouteLoading', false);
});

router.beforeEach(function (to, from, next) {
  // 菜单更换位置后， 点击原先菜单位置拦截页面， 并在men-item.vue进行提示
  const oldPage = [
    // 报表下的
    // 收获统计
    '/reports/receive-statistics/byGoodsOldPage',
    // 客户库存
    '/reports/inventory/customerInventoryOldPage',
    // 客户毛利
    '/reports/profit/customerMarginOldPage',
    // 计划统计
    '/reports/purchase/planOldPage',
  ];
  if (oldPage.indexOf(to.path) > -1) {
    next(false);
    return;
  }

  if (
    ['/finance/user/audit-list', '/finance/user/audit-list-new'].includes(
      to.path,
    )
  ) {
    const auditListType = storage.getLocalStorage('audit-list-type');
    if (auditListType === 1 && to.path === '/finance/user/audit-list') {
      next('/finance/user/audit-list-new');
    } else if (
      auditListType === 0 &&
      to.path === '/finance/user/audit-list-new'
    ) {
      next('/finance/user/audit-list');
    }
  }

  // 如果开启了微前端配置，当前不是运行在微前端模式下，则跳转微前端环境
  const isMicroMode =
    store.state.sysConfig &&
    +store.state.sysConfig.micro_front_end_enabled === 1;
  if (
    process.env.NODE_ENV !== 'development' &&
    isMicroMode &&
    !window.__POWERED_BY_QIANKUN__
  ) {
    const noRedirectPaths = [
      '/print-test',
      '/login',
      '/unique-login',
      '/auto-login',
      '/resetPass',
      '/auth-login',
      '/printEditor/printShare',
      '/order/export-commodity-trace',
      '/data-screen',
      '/trace-screen',
      '/distribution-screen',
      '/deliver-screen',
    ];
    if (!noRedirectPaths.includes(to.path)) {
      const extractCcCodePrefix = (url) => {
        const match = url.match(/^((\/cc_[^/]+)?)/);
        return match ? match[1] : null;
      };
      let ccodePrefix = extractCcCodePrefix(location.pathname);
      if (!ccodePrefix) {
        const ccode = Cookies.get('ccode');
        if (ccode) {
          ccodePrefix = '/cc_' + ccode;
        }
      }
      window.location.href = `${ccodePrefix}/superAdmin/viewCenter/v1${to.path}`;
      return;
    }
  }

  store.dispatch('setRouteLoading', true);
  handleRouteBeforeEnter(to, from, next, { get, post });
  // 全局处理Notice导致的内存泄露问题
  Notice.destroy();
  // 记录每次路由，及页面内存使用情况，用来在页面奔溃的时候上报
  window.localStorage.setItem('lastPage', window.location.href);
  if (window.performance) {
    window.localStorage.setItem('lastPageMemory', window.performance.memory);
  }

  console.log(to);

  addCommonlyUsed(to);
  // 百度统计跟踪路由页面
  if (to.path) {
    if (window._hmt) {
      window._hmt.push(['_trackPageview', '/#' + to.fullPath]);
    }
  }

  if (localStorage['admin_token'] && localStorage['login'] === 'isTrue') {
    const siteService = CommonUtil.getService('site');
    let exceptionPath = [
      '/data-screen',
      '/trace-screen',
      '/deliver-screen',
      '/distribution-screen',
      '/auto-login',
    ];
    if (
      siteService.currentSiteIsMainSite() &&
      exceptionPath.indexOf(to.path) === -1
    ) {
      // 判断是从总站点入口要跳转到弱密码页面
      if (to.path === '/resetPass') {
        next();
      } else {
        siteService.goToMainSite();
        next(false);
        return false;
      }
    }
    if (to.path == '/login' || to.path === '/unique-login') {
      // 已经登陆访问登录页面时跳转到分配给该账号的第一个页面
      let firstPage = localStorage['first_page'];
      if (firstPage) {
        if (firstPage.indexOf('#') && !firstPage.endsWith('#')) {
          next(firstPage.substr(firstPage.indexOf('#') + 1));
        } else {
          next();
        }
      } else {
        next();
      }
    } else {
      const cfg = localStorage.getItem('use_new_sort_commodity');
      const isNewVersion = cfg === 'true' || !cfg;

      // 判断库房页面新旧页面跳转路径 (/sort/commodity旧页面、/sorting/commodity新页面)
      if (to.path == '/sort/commodity') {
        if (isNewVersion) {
          // next("/sorting/commodity");
          // sort/commodity有跳转到新页面的逻辑，加载这里的话会影响客户页面的跳转
          // 所以先直接跳过去，然后由老页面跳转新页面
          next();
        } else {
          next();
        }
      } else if (to.path == '/sorting/commodity') {
        if (!isNewVersion) {
          next('/sort/commodity');
        } else {
          next();
        }
      } else {
        next();
      }
    }
  } else {
    next();
  }
});
Vue.prototype.router = router;
Vue.prototype.vue = Vue;
Vue.prototype.$http = axios;

Vue.prototype.back = function () {
  history.go(-1);
};
Vue.prototype.delGetParams = (url, params) => {
  let requestUrl = `${url}?token=${localStorage['admin_token']}`;
  if (params) {
    Object.keys(params).forEach((key) => {
      if (params[key] === 'undefined' || params[key] === undefined) {
        params[key] = '';
      }
      requestUrl += `&${key}=${params[key]}`;
    });
  }
  return requestUrl;
};
Vue.prototype.cloneObj = function (obj) {
  var str,
    newobj = obj.constructor === Array ? [] : {};
  if (typeof obj !== 'object') {
    return;
  } else if (window.JSON) {
    (str = JSON.stringify(obj)), (newobj = JSON.parse(str));
  } else {
    for (var i in obj) {
      newobj[i] = typeof obj[i] === 'object' ? this.cloneObj(obj[i]) : obj[i];
    }
  }
  return newobj;
};
Vue.prototype.json2queryString = function (obj) {
  if (!obj) {
    return '';
  }
  return Object.keys(obj)
    .map((key) => {
      if (obj[key] === undefined) return '';
      return encodeURIComponent(key) + '=' + encodeURIComponent(obj[key]);
    })
    .join('&');
};
Vue.prototype.getTableHeight = function () {
  let winHeight = 0;
  if (window.innerHeight) {
    winHeight = window.innerHeight;
  } else if (document.body && document.body.clientHeight) {
    winHeight = document.body.clientHeight;
  }
  return winHeight - 218;
};
Vue.prototype.getTableHeightNew = function (hasTab = true, hasFilter = true) {
  let winHeight = 0;
  if (window.innerHeight) {
    winHeight = window.innerHeight;
  } else if (document.body && document.body.clientHeight) {
    winHeight = document.body.clientHeight;
  }
  if (hasTab) {
    winHeight -= 40;
  }
  if (hasFilter && !hasTab) {
    winHeight -= 80;
  } else if (hasFilter && hasTab) {
    winHeight -= 75;
  }
  return winHeight - 218;
};
Vue.prototype.getWindowWidth = function () {
  let winWidth = 0;
  if (window.innerWidth) {
    winWidth = window.innerWidth;
  } else if (document.body && document.body.clientWidth) {
    winWidth = document.body.clientWidth;
  }
  return winWidth;
};
Vue.prototype.formatDate = function (dt) {
  // 转换中国时区
  let date = new Date(dt.valueOf() + 28800000);
  date = date.toISOString().slice(0, 10);
  return date;
};
Vue.prototype.getToday = function () {
  const curDate = new Date();
  let month =
    curDate.getMonth() < 9
      ? `0${curDate.getMonth() + 1}`
      : curDate.getMonth() + 1;
  let day =
    curDate.getDate() < 10 ? `0${curDate.getDate()}` : curDate.getDate();
  let currentDate = `${curDate.getFullYear()}-${month}-${day}`;
  return currentDate;
};
//初始化table列
Vue.prototype.initCols = (selectedCols, originCols) => {
  let newCols = [];
  let len = originCols.length - 1;
  if (originCols[0].key === 'selection') {
    newCols.push(originCols[0]);
  }
  if (originCols[0].key === 'expand') {
    newCols.push(originCols[0]);
  }
  originCols.forEach((col) => {
    col.isSelected = false;
    selectedCols.forEach((selected) => {
      if (col.key === selected) {
        col.isSelected = true;
      }
    });
    if (col.isSelected) {
      newCols.push(col);
    }
  });
  if (originCols[len].key === 'action') {
    newCols.push(originCols[len]);
  }
  return newCols;
};
// 获取从今天起往前推一个月的日期
Vue.prototype.getCurrentLastMonth = function () {
  let timestamp = Date.parse(new Date()) - 30 * 24 * 60 * 60 * 1000;
  let date = new Date(timestamp);
  let y = 1900 + date.getYear();
  let m = '0' + (date.getMonth() + 1);
  let d = '0' + date.getDate();
  return (
    y +
    '-' +
    m.substring(m.length - 2, m.length) +
    '-' +
    d.substring(d.length - 2, d.length)
  );
};
Vue.prototype.getCustomToday = function (h) {
  const curDate = new Date();
  let year = curDate.getFullYear();
  let hours = curDate.getHours();
  let month =
    curDate.getMonth() < 9
      ? `0${curDate.getMonth() + 1}`
      : curDate.getMonth() + 1;
  let day =
    curDate.getDate() < 10 ? `0${curDate.getDate()}` : curDate.getDate();
  let monthStartDate = new Date(year, curDate.getMonth(), 1);
  let monthEndDate = new Date(year, curDate.getMonth() + 1, 1);
  let days = (monthEndDate - monthStartDate) / (1000 * 60 * 60 * 24);
  if (hours > h) {
    day =
      curDate.getDate() < 9
        ? `0${curDate.getDate() + 1}`
        : curDate.getDate() + 1;
  }
  let currentDate = `${year}-${month}-${day}`;
  if (Number(day) > days) {
    let nextMon =
      curDate.getMonth() < 8
        ? `0${curDate.getMonth() + 2}`
        : curDate.getMonth() + 2;
    if (month - 12 >= 0) {
      nextMon = '01';
      year = year * 1 + 1;
    }
    currentDate = `${year}-${nextMon}-01`;
  }
  return currentDate;
};
Vue.prototype.hightlightRow = function (index, color, bold, fontSize, ref) {
  ref || (ref = 'orderTable');
  let TextColor = color || 'red';
  let bodyDom = this.$refs[ref].$el.querySelector('.ivu-table-tbody');
  let tabRows = bodyDom.querySelectorAll('.ivu-table-row');
  for (let i = 0; i < tabRows.length; i++) {
    tabRows[i].style['color'] = '#333333';
    tabRows[i].style['fontWeight'] = 'normal';
    tabRows[i].style['fontSize'] = 'inherit';
    tabRows[i].classList.remove('ivu-table-row-high-light');
  }
  tabRows[index].style.color = TextColor;
  tabRows[index].style.fontWeight = bold;
  tabRows[index].style.fontSize = fontSize;
  tabRows[index].classList.add('ivu-table-row-high-light');
};
Vue.prototype.goNextInput = function (index, listLength, event, scroll, ref) {
  ref || (ref = 'orderTable');
  let nextRowIndex = index + 1,
    currentInputIdx;
  let bodyDom = this.$refs[ref].$el.querySelector('.ivu-table-tbody'),
    tabRows = bodyDom.querySelectorAll('.ivu-table-row'),
    inputObjs = tabRows[index].querySelectorAll('.number_input');
  // 获取当前聚焦元素的索引值
  if (inputObjs.length > 0) {
    for (let idx = 0; idx < inputObjs.length; idx++) {
      if (
        event &&
        inputObjs[idx].attributes[1].value === event.target.attributes[1].value
      ) {
        currentInputIdx = idx;
      }
    }
    let nextInputIdx = currentInputIdx + 1;
    if (nextRowIndex < listLength) {
      if (nextInputIdx < inputObjs.length) {
        inputObjs[nextInputIdx].focus();
      } else {
        tabRows[nextRowIndex].querySelectorAll('.number_input')[0].focus();
      }
      if (scroll === true) {
        tabRows[nextRowIndex].scrollIntoView();
      }
    } else {
      if (scroll === true) {
        tabRows[listLength - 1].scrollIntoView();
      }
      if (nextInputIdx < inputObjs.length) {
        inputObjs[nextInputIdx].focus();
      }
    }
  }
};
Vue.prototype.goTableInput = function (
  index,
  listLength,
  event,
  goNext,
  scroll,
  ref,
) {
  ref || (ref = 'orderTable');
  if (goNext === true) {
    this.goNextInput(index, listLength, event);
  } else {
    let currentInputIdx = 0;
    let bodyDom = this.$refs[ref].$el.querySelector('.ivu-table-tbody');
    if (!bodyDom) {
      return false;
    }
    let tabRows = bodyDom.querySelectorAll('.ivu-table-row');
    if (!tabRows || tabRows.length === 0) {
      return false;
    }
    if (scroll === true && index > 0) {
      tabRows[index].scrollIntoView();
    }
    let inputObjs = tabRows[index].querySelectorAll('.number_input');
    // 获取当前聚焦元素的索引值
    if (inputObjs.length > 0) {
      for (let idx = 0; idx < inputObjs.length; idx++) {
        if (
          event &&
          inputObjs[idx].attributes[1].value ===
            event.target.attributes[1].value
        ) {
          currentInputIdx = idx;
        }
      }
      let nextInputIdx = currentInputIdx + 1;
      if (nextInputIdx < inputObjs.length) {
        inputObjs[nextInputIdx].focus();
      } else {
        tabRows[index].querySelectorAll('.number_input')[0].focus();
      }
    }
  }
};
Vue.prototype.modalSuccess = function (config, delay = 500) {
  let modalConfig = {};
  if (typeof config === 'object') {
    modalConfig = config;
  } else {
    modalConfig.content = config;
  }
  modalConfig.title || (modalConfig.title = '操作成功');
  if (delay) {
    setTimeout(() => {
      this.$Modal.success(modalConfig);
    }, delay);
  } else {
    this.$Modal.success(modalConfig);
  }
};

Vue.prototype.modalError = function (config, delay = 500) {
  let modalConfig = {};
  if (typeof config === 'object') {
    modalConfig = config;
  } else {
    modalConfig.content = config;
  }
  modalConfig.title || (modalConfig.title = '操作失败');
  if (delay) {
    setTimeout(() => {
      this.$Modal.error(modalConfig);
    }, delay);
  } else {
    this.$Modal.error(modalConfig);
  }
};

// 解决列表页点击确认删除狂后，错误提示框闪退问题
let IviewErrorModal = Vue.prototype.$Modal.error;
Vue.prototype.$Modal.error = function (config) {
  setTimeout(() => {
    IviewErrorModal(config);
  }, 300);
};

Vue.prototype.goBack = function () {
  if (history.length && history.length <= 1) {
    window.close();
  } else {
    history.go(-1);
  }
};

/**
 * 生产加工模式是否是客户模式
 * @returns {boolean}
 */
Vue.prototype.isUserModeProcess = function () {
  return (
    this.$store.state[this.globalConfig.store.stateKey.processMode] * 1 ===
    this.util.processMode.user.value * 1
  );
};

Vue.prototype.sysEntityText = function (text) {
  return text;
};

const sysEntityConfig = window.localStorage.getItem('sys_entity_config');
if (sysEntityConfig && sysEntityConfig !== 'null') {
  Vue.prototype.sysEntityText = function (textContent) {
    if (!textContent) return textContent;
    // 获取配置对象
    const sysEntityConfig = window.localStorage.getItem('sys_entity_config');
    if (sysEntityConfig && sysEntityConfig !== 'null') {
      const config = JSON.parse(sysEntityConfig) || {
        user: '客户',
        group: '集团',
      };
      return textContent
        .replaceAll('客户', config.user || '客户')
        .replaceAll('集团', config.group || '集团');
    } else {
      return textContent;
    }
  };

  Vue.mixin({
    beforeCreate() {
      const originalRender = this.$options.render;
      if (originalRender) {
        const that = this;
        that.$options.render = function (h) {
          return sdpReplaceText(h, originalRender, that);
        };
      }
    },
  });
}
/**
 * @description: 判断是否需要弹出http跳转https的提示
 */
Vue.prototype.isShowHttp2HttpsHint =
  location.protocol === 'http:' && process.env.NODE_ENV !== 'development';

// Vue全局配置
Vue.use(Global);

const originalFetch = window.fetch;

// 重写 fetch 函数
window.fetch = function (url, options) {
  // 添加你要设置的请求头信息
  const headers = {
    'X-Requested-With': 'XMLHttpRequest', // 后端需要这个请求头判断是否正确返回权限不足的提示
  };

  // 合并传入的选项和新的请求头
  const newOptions = Object.assign({}, options, { headers });

  // 调用原始的 fetch 函数并返回结果
  return originalFetch(url, newOptions);
};

// SentryInit(
//   {
//     userName: sessionStorage.getItem('userName'),
//     userId: sessionStorage.getItem('userId'),
//   },
//   router,
// );

// new Vue({
//   el: '#app',
//   router,
//   store,
//   template: '<App/>',
//   components: {
//     App,
//   },
// });

// 添加乾坤生命周期
let instance = null;
function render(props = {}) {
  if (props.container) {
    // 乾坤注入的容器选择器
    document.getElementById('app').innerHTML = ''; // 清空原有容器
  }

  if (typeof props.setLoading === 'function') {
    props.setLoading(false);
  }
  instance = new Vue({
    router,
    store,
    render: (h) => h(App),
  }).$mount('#app');
}

// 导出乾坤生命周期
export async function bootstrap() {
  console.log('[vue] sub app bootstraped');
}

export async function mount(props) {
  console.log('[vue] sub app mount', props);
  render(props);
}

export async function unmount(props) {
  console.log('[vue] sub app unmount', props);
  instance.$destroy();
  instance = null;
}

// 独立运行时直接渲染
if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

if (!window.AppAdminOld) {
  ((global) => {
    global.AppAdminOld = {
      bootstrap: () => {
        console.log('[vue] sub app bootstraped');
        return Promise.resolve();
      },
      mount: (props) => {
        console.log('[vue] sub app mount', props);
        render(props);
      },
      unmount: (props) => {
        console.log('[vue] sub app unmount', props);
        console.log('xxxxxxxxxxx', instance);
        instance.$destroy();
        instance = null;
      },
    };
  })(window);
}
