/*eslint-disable no-undef*/
'use strict';
// Template version: 1.2.8
// see http://vuejs-templates.github.io/webpack for documentation.
const yargs = require('yargs');
const path = require('path');
const productionMode = yargs.argv.productionMode || '';
const entryDir = yargs.argv.entryDir || path.resolve(__dirname, `../../common`);
const baseDir = yargs.argv.baseDir || path.resolve(__dirname, `../../sdpbase`);

// 环境,用命令行切换环境 ,避免因更改配置文件导致缓存失效
// npm run dev -- --env=spddev
// npm run dev -- --env=base1120
const env = yargs.argv.env || 'sdpdev'; // 请勿更改,遵守规范, 用命令行命令换环境
const branch = yargs.argv.branch;
const project = yargs.argv.project;

const target = `${env}.sdongpo.com`;
// eslint-disable-next-line no-undef
module.exports = {
  dev: {
    // Paths
    assetsSubDirectory: 'static',
    assetsPublicPath: '/',
    proxyTable: {
      '/superAdmin': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/commonApi': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/wapv3': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/wap': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/word': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/upload': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/api': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/siteAdmin': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/webprint': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/qyweixinApi': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
      '/area.json': {
        target: `http://${target}`,
        changeOrigin: true,
        secure: false,
      },
    },
    host: '0.0.0.0', // 请勿更改
    port: 8089, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    cacheBusting: true,
    cssSourceMap: false,
    devtool: 'eval',
  },

  build: {
    // Template for index.html
    index: `${entryDir}/core/modules/superAdmin/views/view/index${
      productionMode ? '-' + productionMode : ''
    }.php`,

    // Paths
    assetsRoot: baseDir + '/www/superadmin-static',
    assetsSubDirectory:
      'superadmin' + (productionMode ? '-' + productionMode : ''),
    assetsPublicPath: `https://base-oss.shudongpoo.com/static/`,

    // index: path.resolve(__dirname, '../dist/index.html'),
    // // Paths
    // assetsRoot: path.resolve(__dirname, '../dist'),
    // assetsSubDirectory: 'static',
    // assetsPublicPath: './',

    /**
     * Source Maps
     */
    productionSourceMap: +yargs.argv.sentry === 1,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: 'source-map',

    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: false,
    productionGzipExtensions: ['js', 'css'],
    env: env,
    branch: branch,
    project: project,
  },
};
