diff --git a/node_modules/view-design/src/components/cascader/cascader.vue b/node_modules/view-design/src/components/cascader/cascader.vue
index cc22775..7fd14b0 100644
--- a/node_modules/view-design/src/components/cascader/cascader.vue
+++ b/node_modules/view-design/src/components/cascader/cascader.vue
@@ -1,5 +1,5 @@
 <template>
-    <div :class="classes" v-click-outside="handleClose">
+    <div :class="classes" v-click-outside="handleClose" @keydown.stop="handleKeyDown" @keyup.stop="handleKeyUp">
         <div :class="[prefixCls + '-rel']" @click="toggleOpen" ref="reference">
             <input type="hidden" :name="name" :value="currentValue">
             <slot>
@@ -36,13 +36,16 @@
                         :data="data"
                         :disabled="itemDisabled"
                         :change-on-select="changeOnSelect"
-                        :trigger="trigger"></Caspanel>
+                        :trigger="trigger"
+                        :avoidUpdate="avoidUpdate"
+                        @close-panel="handleClose"></Caspanel>
                     <div :class="[prefixCls + '-dropdown']" v-show="filterable && query !== '' && querySelections.length">
-                        <ul :class="[selectPrefixCls + '-dropdown-list']">
+                        <ul :class="[selectPrefixCls + '-dropdown-list']" @keydown.stop.prevent="handleDropKeyDown">
                             <li
                                 :class="[selectPrefixCls + '-item', {
                                     [selectPrefixCls + '-item-disabled']: item.disabled
                                 }]"
+                                tabindex="-1"
                                 v-for="(item, index) in querySelections"
                                 @click="handleSelectItem(index)" v-html="item.display"></li>
                         </ul>
@@ -152,6 +155,10 @@
             },
             transferClassName: {
                 type: String
+            },
+            useHotkey: {
+                type: Boolean,
+                default: false
             }
         },
         data () {
@@ -165,7 +172,8 @@
                 currentValue: this.value,
                 query: '',
                 validDataStr: '',
-                isLoadedChildren: false    // #950
+                isLoadedChildren: false,    // #950
+                avoidUpdate: false
             };
         },
         computed: {
@@ -375,6 +383,74 @@
                 }
 
                 return data.map(item => deleteData(item));
+            },
+            handleKeyDown (e) {
+                const { target, keyCode } = e
+                switch (keyCode) {
+                    case 40:  // 下
+                        this.focusTargetItem(keyCode, this.visible)
+                        if (!this.visible && this.useHotkey) this.toggleOpen()
+                        e.preventDefault()
+                        break
+                    case 37:
+                    case 38:
+                    case 39:
+                        if (this.visible && this.currentValue.length) {
+                            this.focusTargetItem(keyCode, true)
+                            e.preventDefault()
+                        }
+                        break
+                    case 9:  // tab
+                        if (this.visible) e.preventDefault()
+                        break
+                }
+            },
+            handleKeyUp (e) {
+                if (this.query && !this.visible) this.toggleOpen()
+            },
+            focusTargetItem (keyCode, toNextStep) {
+                if (!this.useHotkey) return
+                this.avoidUpdate = true
+                this.$nextTick(() => {
+                    let targetItem = null
+                    if (this.filterable && this.query) {
+                        const targetMenu = this.$el.querySelector('.ivu-select-dropdown-list')
+                        targetItem = targetMenu.querySelector('.ivu-select-item')
+                        targetItem && targetItem.focus()
+                        return
+                    }
+                    const valueLength = this.currentValue.length
+                    const targetMenu = this.$refs.caspanel.$el.querySelectorAll('.ivu-cascader-menu')[(valueLength || 1) - 1]
+                    targetItem = targetMenu.querySelector(`.ivu-cascader-menu-item${valueLength ? '-active' : ''}[tabindex="-1"]`)
+                    if (!targetItem ) return
+                    if (valueLength && toNextStep) {
+                        const panel = this.$refs.caspanel.getPanel(valueLength - 1)
+                        panel && panel.handleKeyDown({ keyCode, target: targetItem })
+                    } else {
+                        targetItem.focus()
+                        !valueLength && targetItem.click()
+                    }
+                })
+            },
+            handleDropKeyDown (e) {
+                const { target, keyCode } = e
+                switch (keyCode) {
+                    case 38:  // 上
+                        const prev = target.previousElementSibling
+                        prev && prev.focus()
+                        break
+                    case 40:  // 下
+                        const next = target.nextElementSibling
+                        next && next.focus()
+                        break
+                    case 13:  // enter
+                        target.click()
+                        break
+                    case 27:
+                    case 9:
+                        this.handleClose()
+                        break
+                }
             }
         },
         created () {
@@ -420,6 +496,7 @@
                     }
                     this.broadcast('Drop', 'on-update-popper');
                 } else {
+                    this.avoidUpdate = false
                     if (this.filterable) {
                         this.query = '';
                         this.$refs.input.currentValue = '';
diff --git a/node_modules/view-design/src/components/cascader/casitem.vue b/node_modules/view-design/src/components/cascader/casitem.vue
index 848f178..1d2f6ca 100644
--- a/node_modules/view-design/src/components/cascader/casitem.vue
+++ b/node_modules/view-design/src/components/cascader/casitem.vue
@@ -1,5 +1,5 @@
 <template>
-    <li :class="classes">
+    <li :class="classes" tabindex="-1" :data-has-child="data.children && data.children.length">
         {{ data.label }}
         <Icon :type="arrowType" :custom="customArrowType" :size="arrowSize" v-if="showArrow" />
         <i v-if="showLoading" class="ivu-icon ivu-icon-ios-loading ivu-load-loop ivu-cascader-menu-item-loading"></i>
diff --git a/node_modules/view-design/src/components/cascader/caspanel.vue b/node_modules/view-design/src/components/cascader/caspanel.vue
index bcdd82d..20b8263 100644
--- a/node_modules/view-design/src/components/cascader/caspanel.vue
+++ b/node_modules/view-design/src/components/cascader/caspanel.vue
@@ -1,15 +1,15 @@
 <template>
     <span>
-        <ul v-if="data && data.length" :class="[prefixCls + '-menu']">
+        <ul v-if="data && data.length" :class="[prefixCls + '-menu']" @keydown.stop.prevent="handleKeyDown">
             <Casitem
-                v-for="item in data"
-                :key="getKey()"
+                v-for="(item, i) in data"
+                :key="getKey() + i"
                 :prefix-cls="prefixCls"
                 :data="item"
                 :tmp-item="tmpItem"
                 @click.native.stop="handleClickItem(item)"
                 @mouseenter.native.stop="handleHoverItem(item)"></Casitem>
-        </ul><Caspanel v-if="sublist && sublist.length" :prefix-cls="prefixCls" :data="sublist" :disabled="disabled" :trigger="trigger" :change-on-select="changeOnSelect"></Caspanel>
+        </ul><Caspanel ref="subpanel" v-if="sublist && sublist.length" :prefix-cls="prefixCls" :data="sublist" :disabled="disabled" :trigger="trigger" :avoidUpdate="avoidUpdate" :change-on-select="changeOnSelect" @close-panel="handleClose"></Caspanel>
     </span>
 </template>
 <script>
@@ -18,6 +18,20 @@
     import { findComponentUpward, findComponentDownward } from '../../utils/assist';
 
     let key = 1;
+    const getSibling = (el, distance) => {
+        const { parentNode } = el
+        if (parentNode) {
+            const siblings = parentNode.querySelectorAll('.ivu-cascader-menu-item[tabindex="-1"]')
+            const index = Array.prototype.indexOf.call(siblings, el)
+            return siblings[index + distance] || null
+        }
+        return null
+    }
+    const focusItem = el => {
+        if (!el) return
+        el.focus()
+        el.dataset.hasChild && el.click()
+    }
 
     export default {
         name: 'Caspanel',
@@ -33,7 +47,8 @@
             disabled: Boolean,
             changeOnSelect: Boolean,
             trigger: String,
-            prefixCls: String
+            prefixCls: String,
+            avoidUpdate: Boolean
         },
         data () {
             return {
@@ -135,7 +150,49 @@
                 }
             },
             getKey () {
+                if (this.avoidUpdate) return key
                 return key++;
+            },
+            handleKeyDown (e) {
+                const { target, keyCode } = e
+                switch (keyCode) {
+                    case 38:  // 上
+                        const prev = getSibling(target, -1)
+                        focusItem(prev)
+                        break
+                    case 40:  // 下
+                        const next = getSibling(target, 1)
+                        focusItem(next)
+                        break
+                    case 37:  // 左
+                        const targetItem = this.$parent.$el.querySelector('.ivu-cascader-menu-item-active[tabindex="-1"]')
+                        this.$emit('on-clear', true)
+                        focusItem(targetItem)
+                        break
+                    case 39:  // 右
+                        if (this.$refs.subpanel) {
+                            const firstItem = this.$refs.subpanel.$el.querySelector('.ivu-cascader-menu-item[tabindex="-1"]')
+                            focusItem(firstItem)
+                        }
+                        break
+                    case 13:  // enter
+                        if (!this.$refs.subpanel) target.click()
+                        break
+                    case 27:
+                    case 9:
+                        this.handleClose()
+                        break;
+                }
+            },
+            handleClose () {
+                this.$emit('close-panel')
+            },
+            getPanel (level) {
+                let panel = this
+                for (let i = 1; i <= level; i++) {
+                    panel = panel.$refs.subpanel || null
+                }
+                return panel
             }
         },
         mounted () {
diff --git a/node_modules/view-design/src/components/date-picker/panel/Date/date-panel-mixin.js b/node_modules/view-design/src/components/date-picker/panel/Date/date-panel-mixin.js
index f810eab..e2ce799 100644
--- a/node_modules/view-design/src/components/date-picker/panel/Date/date-panel-mixin.js
+++ b/node_modules/view-design/src/components/date-picker/panel/Date/date-panel-mixin.js
@@ -50,6 +50,10 @@ export default {
         focusedDate: {
             type: Date,
             required: true,
+        },
+        defaultToEnd: {
+            type: Boolean,
+            default: false
         }
     },
     computed: {
diff --git a/node_modules/view-design/src/components/date-picker/panel/Date/date-range.vue b/node_modules/view-design/src/components/date-picker/panel/Date/date-range.vue
index f7b4cf1..3ee1955 100644
--- a/node_modules/view-design/src/components/date-picker/panel/Date/date-range.vue
+++ b/node_modules/view-design/src/components/date-picker/panel/Date/date-range.vue
@@ -296,7 +296,9 @@
             },
             changePanelDate(panel, type, increment, updateOtherPanel = true){
                 const current = new Date(this[`${panel}PanelDate`]);
-                current[`set${type}`](current[`get${type}`]() + increment);
+                // current[`set${type}`](current[`get${type}`]() + increment);
+                if (type === 'FullYear') current[`set${type}`](current[`get${type}`]() + increment);
+                else current[`set${type}`](current[`get${type}`]() + increment, 1);
                 this[`${panel}PanelDate`] = current;
 
                 if (!updateOtherPanel) return;
@@ -353,6 +355,7 @@
                     if (this.currentView === 'time'){
                         this.dates = val;
                     } else {
+                        if (this.defaultToEnd) val.setHours(23, 59)
                         const [minDate, maxDate] = [this.rangeState.from, val].sort(dateSorter);
                         this.dates = [minDate, maxDate];
                         this.rangeState = {
diff --git a/node_modules/view-design/src/components/date-picker/picker.vue b/node_modules/view-design/src/components/date-picker/picker.vue
index 51bbb8c..6a36ce6 100644
--- a/node_modules/view-design/src/components/date-picker/picker.vue
+++ b/node_modules/view-design/src/components/date-picker/picker.vue
@@ -58,6 +58,7 @@
                         :picker-type="type"
                         :multiple="multiple"
                         :focused-date="focusedDate"
+                        :defaultToEnd="defaultToEnd"
 
                         :time-picker-options="timePickerOptions"
 
@@ -223,6 +224,10 @@
             },
             transferClassName: {
                 type: String
+            },
+            defaultToEnd: {
+                type: Boolean,
+                default: false
             }
         },
         data(){
@@ -799,6 +804,7 @@
             },
         },
         mounted () {
+            console.warn('---------debug---------1', this.value);
             const initialValue = this.value;
             const parsedValue = this.publicVModelValue;
             if (typeof initialValue !== typeof parsedValue || JSON.stringify(initialValue) !== JSON.stringify(parsedValue)){
diff --git a/node_modules/view-design/src/styles/components/cascader.less b/node_modules/view-design/src/styles/components/cascader.less
index 9320429..b270141 100644
--- a/node_modules/view-design/src/styles/components/cascader.less
+++ b/node_modules/view-design/src/styles/components/cascader.less
@@ -86,6 +86,10 @@
             max-height: 190px;
             box-sizing: border-box;
             overflow: auto;
+            .ivu-select-item:focus-visible {
+                background-color: @background-color-select-hover;
+                outline: none;
+            }
         }
     }
 
@@ -145,6 +149,10 @@
                 background-color: @background-color-select-hover;
                 color: @primary-color;
             }
+            &:focus-visible {
+                background-color: @background-color-select-hover;
+                outline: none;
+            }
         }
     }
 }
