## 概述 {docsify-ignore-all}
* 组件名称：业务员列表
* 组件类型：下拉列表
* 组件路径：components/user/sellerSelect.vue
* 接口地址：/superAdmin/salesSuper/getSalesList

## 代码示例
``` html
<seller v-model="sellerId"></seller>
<script>
import seller from '@components/user/sellerSelect';
export default {
  data() {
    return {
      sellerId: ''
    }
  },
  components: {
    seller
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
filterable|是否支持搜索|Boolean|false|
showAll|是否显示全部选项|Boolean|true|
placeholder|选择框默认文字|String|选择业务员|

### events
事件名|说明|返回值
-||-
on-change|选择变化时触发|无
