## 概述 {docsify-ignore-all}
* 组件名称：分拣员列表
* 组件类型：下拉列表
* 组件路径：components/common/sorterSelect.vue
* 接口地址：/superAdmin/sorterHistory/getSorterList

## 代码示例
``` html
<sorter v-model="sorterId"></sorter>
<script>
import sorter from '@components/common/sorterSelect';
export default {
  data() {
    return {
      sorterId: ''
    }
  },
  components: {
    sorter
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
disabled|是否禁用|Boolean|false|
filterable|是否支持搜索|Boolean|false|
defaultFirst|是否默认选中第一个|Boolean|false|
showAll|是否显示全部选项|Boolean|false|
placeholder|选择框默认文字|String|选择分拣员|

### events
事件名|说明|返回值
-||-
on-change|选择变化时触发|无
