## 概述 {docsify-ignore-all}
* 组件名称：司机列表
* 组件类型：下拉列表
* 组件路径：components/delivery/driverSelect.vue
* 接口地址：/superAdmin/driver/list

## 代码示例
``` html
<driver v-model="driver"></driver>
<script>
import driver from '@components/delivery/driverSelect';
export default {
  data() {
    return {
      driver: ''
    }
  },
  components: {
    driver
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
filterable|是否支持搜索|Boolean|false|
showAll|是否显示全部选项|Boolean|true|
disabled|是否禁用|Boolean|true|
placeholder|选择框默认文字|String|选择司机|

### events
事件名|说明|返回值
-||-
on-change|选择变化时触发|无
