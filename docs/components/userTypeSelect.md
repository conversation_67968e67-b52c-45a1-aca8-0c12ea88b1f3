## 概述 {docsify-ignore-all}
* 组件名称：客户类型列表
* 组件类型：下拉列表
* 组件路径：components/user/userTypeSelect.vue
* 接口地址：/superAdmin/receStyle/list

## 代码示例
``` html
<user-type v-model="userTypeId"></user-type>
<script>
import userType from '@components/common/userTypeSelect';
export default {
  data() {
    return {
      userTypeId: ''
    }
  },
  components: {
    userType
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
filterable|是否支持搜索|Boolean|false|
placeholder|选择框默认文字|String|选择客户类型|

### events
事件名|说明|返回值
-||-
on-change|选择变化时触发|无
