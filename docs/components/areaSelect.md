## 概述 {docsify-ignore-all}
* 组件名称：区域列表
* 组件类型：下拉列表
* 组件路径：components/delivery/areaSelect.vue
* 接口地址：/superAdmin/area/list

## 代码示例
``` html
<area v-model="areaId"></area>
<script>
import area from '@components/delivery/areaSelect';
export default {
  data() {
    return {
      areaId: ''
    }
  },
  components: {
    area
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
placeholder|选择框默认文字|String|选择区域|

### events
事件名|说明|返回值
-||-
on-change|选择变化时触发|无
