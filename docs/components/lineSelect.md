## 概述 {docsify-ignore-all}
* 组件名称：线路下拉列表组件
* 组件类型：下拉列表
* 组件路径：components/delivery/lineSelect.vue
* 接口地址：/superAdmin/line/list

## 代码示例
``` html
<line-select v-model="lineId"></line-select>
<script>
import lineSelect from '@components/delivery/lineSelect';
export default {
  data() {
    return {
      lineId: ''
    }
  },
  components: {
    lineSelect
  }
}
</script>
```

## API
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
disabled|是否禁用|Boolean|false|
filterable|是否支持搜索|Boolean|false|
showAll|是否显示全部选项|Boolean|false|
placeholder|选择框默认文字|String|选择线路|

### events
事件名|说明|返回值
-||-
on-change|线路变化时触发|无
