## 概述 {docsify-ignore-all}
* 组件名称：采购类型
* 组件类型：二级联动
* 组件路径：components/common/purchaseType.vue
* 接口地址：/superAdmin/commoditySuper/ajaxGetPurchaseType

## 代码示例
``` html
<purchase-type
  v-model="purchaseType"
  @on-agent-change="handleAgentChange"
  @on-provider-change="handleAgentChange"
  @on-change="handlePurchaseTypeChange"></purchase-type>
<script>
import purchaseType from '@components/common/purchaseType';
export default {
  data() {
    return {
      purchaseType: '',
    }
  },
  components: {
    purchaseType
  },
  methods: {
    handleAgentChange(agentId) {}
    handleProviderChange(providerId) {}
    handlePurchaseTypeChange(providerId) {}
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|采购类型(可以使用 v-model 双向绑定数据)|Number|空|
placeholder|选择框默认文字|String|采购模式|

### events
事件名|说明|返回值
-||-
on-change|采购类型变化时触发|无
on-agent-change|采购员变化时触发|采购员id
on-provider-change|供应商变化时触发|供应商id
