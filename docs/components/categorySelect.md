## 概述 {docsify-ignore-all}
* 组件名称：商品分类
* 组件类型：级联
* 组件路径：components/common/categorySelect.vue
* 接口地址：/superAdmin/categorySuper/tree

## 代码示例
``` html
<category v-model="category" @on-change="handleChangeCategory"></category>
<script>
import category from '@components/common/categorySelect';
export default {
  data() {
    return {
      category: [12, 13],
      category1: '',
      category2: '',
    }
  },
  components: {
    category
  },
  methods: {
    handleChangeCategory(category) {
       this.category1 = category[0];
       this.category2 = category[1];
    }
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Array|[]|
placeholder|选择框默认文字|String|全部分类|

### events
事件名|说明|返回值
-||-
on-change|选择分类时触发|value([一级分类id，二级分类id], selectedData)
