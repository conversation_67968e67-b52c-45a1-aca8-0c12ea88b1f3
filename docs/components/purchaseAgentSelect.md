## 概述 {docsify-ignore-all}
* 组件名称：采购员列表
* 组件类型：下拉列表
* 组件路径：components/common/purchaseAgentSelect.vue
* 接口地址：/superAdmin/commoditySuper/ajaxGetPurchaseType

## 代码示例
``` html
<purchase-agent v-model="agentId"></purchase-agent>
<script>
import purchaseAgent from '@components/common/purchaseAgentSelect';
export default {
  data() {
    return {
      agentId: ''
    }
  },
  components: {
    purchaseAgent
  }
}
</script>
```

## API
### props
属性|说明|类型|默认值
-|||-
value|指定选中项目的 value 值，可以使用 v-model 双向绑定数据|Number|空|
filterable|是否支持搜索|Boolean|false|
placeholder|选择框默认文字|String|选择采购员|

### events
事件名|说明|返回值
-||-
on-change|采购员变化时触发|无
