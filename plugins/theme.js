const fs = require('fs');
const globs = require('globs2');

const theme = {
  '--primary-color': ['#03ac54', '#10c0bc', '#00ba59'],
  '--primary-color-active': ['#03a350'],
  '--primary-color-second': ['#35bd76', '#35d59e', '#30d2a2'],
  '--primary-color-2': ['#eafbf2'],
  '--primary-color-5': ['#1dca7080', '#7ac99099'],
  '--primary-message-border': ['#7ac990'],
  '--primary-message-text': ['#33ac54'],
  '--primary-tr-hover': ['#e6f7ec']
};

globs(`${process.cwd()}/src/**/*.{js,less,css,scss,sass}`).then(res => {
  res.map(item => {
    let content = fs.readFileSync(item.file, 'utf8');
    for (let [cssVariable, colors] of Object.entries(theme)) {
      colors = typeof colors === 'string' ? [colors] : colors;
      for (let color of colors) {
        const reg1 = new RegExp(
          `([^-]color|background|background-color)\\s*:\\s*${color}`,
          'igm'
        );
        const matchRes = content.match(reg1);
        if (matchRes) {
          matchRes.map(item => {
            content = content.replace(item, () => {
              item = item.replace(color, `var(${cssVariable})`);
              return item;
            });
          });
        }
        // const matchRes = content.match(reg);
        // if (matchRes) {
        //   console.log(color, matchRes);
        // }
        // content = content.replace(
        //   new RegExp(color, 'igm'),
        //   `var(${cssVariable})`
        // );
      }
    }
    fs.writeFileSync(item.file, content);
  });
});
