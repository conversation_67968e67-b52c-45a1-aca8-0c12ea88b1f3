/* eslint-disable no-undef */
'use strict';
const path = require('path');
const SdpLibs = require('@sdp/library-v2/libs');
const utils = require('./utils');
const config = require('../config');
const { execSync } = require('child_process'); // 引入 child_process 模块
const webpack = require('webpack'); // 引入 webpack

const gitBranch = getGitBranch();

function resolve(dir) {
  return path.join(__dirname, '..', dir);
}

// 获取 Git 当前分支名称
function getGitBranch() {
  try {
    return execSync('git rev-parse --abbrev-ref HEAD', {
      encoding: 'utf-8',
    }).trim();
  } catch (error) {
    console.warn('无法获取 Git 分支名称，请确保已初始化 Git 仓库。');
    return 'unknown';
  }
}

module.exports = {
  context: path.resolve(__dirname, '../'),
  entry: {
    app: './src/main.js',
  },
  cache: {
    // https://webpack.docschina.org/configuration/cache/#cache
    type: 'filesystem',
    store: 'pack',
    cacheLocation:
      process.env.NODE_ENV === 'production'
        ? path.resolve('./node_modules', '.build-appcache')
        : path.resolve('./node_modules', '.dev-appcache'),
    buildDependencies: {
      // 当配置文件内容或配置文件依赖的模块文件发生变化时,缓存失效
      // eslint-disable-next-line no-undef
      config: [__filename],
    },
  },
  output: {
    path: config.build.assetsRoot,
    filename: '[name].js',
    publicPath:
      process.env.NODE_ENV === 'production'
        ? config.build.assetsPublicPath
        : config.dev.assetsPublicPath,
  },
  resolve: {
    extensions: ['.js', '.vue', '.json'],
    alias: {
      vue$: 'vue/dist/vue.esm.js',
      '@components': path.join(__dirname, '..', 'src', 'components'),
      '@api': path.join(__dirname, '..', 'src', 'api'),
      '@assets': path.join(__dirname, '..', 'src', 'assets'),
      '@util': resolve('src/util'),
      '@mixins': resolve('src/mixins'),
      'vxe-table-common': 'vxe-table/lib/index.common.js',
      '@': resolve('src'),
      path: 'path-browserify', // 添加 path-browserify 的 alias
    },
    fallback: {
      module: false,
      dgram: false,
      dns: false,
      fs: false,
      http2: false,
      net: false,
      tls: false,
      path: require.resolve('path-browserify'),
      buffer: require.resolve('buffer/'),
      child_process: false,
      setImmediate: false,
    },
  },
  externals: SdpLibs.getExternals(),
  module: {
    rules: [
      // {
      //   enforce: 'pre',
      //   test: /\.vue$/,
      //   use: [
      //     {
      //       loader: 'prefetch-preload/cjs/loaders/getClientDataLoader.js', // 使用模块中的函数
      //       options: {
      //         outputPath: utils.prefetchAsyncFnDataPath,
      //         dirPath: path.resolve('./src'),
      //       },
      //     },
      //   ],
      // },
      {
        test: /\.vue$/,
        use: [
          'thread-loader',
          {
            loader: 'vue-loader',
            options: {
              exposeFilename: true,
            },
          },
          {
            loader: 'iview-loader',
            options: {
              prefix: false,
            },
          },
        ],
      },
      {
        test: /\.js$/,
        use: ['thread-loader', 'babel-loader?cacheDirectory=true'],
        include: [
          resolve('src'),
          resolve('node_modules/@sdptest/base'),
          resolve('node_modules/view-design'),
        ],
      },
      {
        // 例如 icon-inline.svg
        test: /\-inline\.svg$/,
        use: [
          'vue-svg-loader'
        ]
      },
      {
        test: /\.(png|jpe?g|gif|mp4|webm|svg|ogg|mp3|wav|flac|aac|woff2?|eot|ttf|otf)(\?.*)?$/,
        type: 'asset',
        exclude: /-inline\.svg$/, // 关键：排除已处理的文件
        parser: {
          dataUrlCondition: {
            maxSize: 20 * 1024, // 10kb
          },
        },
        generator: {
          filename: utils.assetsPath('img/[name].[hash:7].[ext]'),
        },
      },
    ],
  },
  node: {
    global: false,
  },
  plugins: [
    // 注入 Git 分支名称到环境变量
    new webpack.DefinePlugin({
      'process.env.__version__': JSON.stringify(gitBranch),
      'process.env.__env__': JSON.stringify(config.build.env),
      'process.env.__project__': JSON.stringify(config.build.project),
    }),
  ],
  performance: {
    maxEntrypointSize: 10000000,
    maxAssetSize: 30000000,
  },
};
