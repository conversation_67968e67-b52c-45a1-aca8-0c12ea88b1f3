'use strict';
require('./check-versions')();
const yargs = require('yargs');

process.env.NODE_ENV = 'production';
const isOpenlog = !!yargs.argv.openlog;
const isOpenSentry = +yargs.argv.sentry === 1;
const ora = require('ora');
const rm = require('rimraf');
const path = require('path');
const chalk = require('chalk');
const webpack = require('webpack');
const config = require('../config');
const webpackConfig = require('./webpack.prod.conf');

const fs = require('fs');
// const glob = require('glob');

const spinner = ora('building for production...');
spinner.start();

rm(
  path.join(config.build.assetsRoot, config.build.assetsSubDirectory),
  (err) => {
    if (err) throw err;
    webpack(webpackConfig, (err, stats) => {
      spinner.stop();
      if (err) throw err;
      process.stdout.write(
        stats.toString({
          colors: true,
          modules: false,
          children: false, // If you are using ts-loader, setting this to true will make TypeScript errors show up during build.
          chunks: false,
          chunkModules: false,
        }) + '\n\n',
      );

      if (stats.hasErrors()) {
        console.log(chalk.red('  Build failed with errors.\n'));
        process.exit(1);
      }

      console.log(chalk.cyan('  Build complete.\n'));
      console.log(
        chalk.yellow(
          '  Tip: built files are meant to be served over an HTTP server.\n' +
            "  Opening index.html over file:// won't work.\n",
        ),
      );

      // 入口文件域名替换
      fs.readFile(config.build.index, (err, data) => {
        data += '';
        // var newData = data.replace(new RegExp(webpackConfig.output.publicPath, 'g'), `<?php echo $GLOBALS['COMMON']['S1_URL']['URL'] . $GLOBALS['COMMON']['S1_URL']['VERSION']?>`);
        var newData = data.replace(
          new RegExp('//inject-sdp-global-js', 'g'),
          `window.openlog = ${isOpenlog};window.openSentry=${isOpenSentry};window._version='${process.env.__version__}';`,
        );
        fs.writeFile(config.build.index, newData, (err) => {
          if (err) {
            throw err;
          }
          console.log(`文件${config.build.index}域名替换完成!`);
        });
        fs.writeFile(`${config.build.assetsRoot}/index.txt`, newData, (err) => {
          if (err) {
            throw err;
          }
          console.log(`${config.build.assetsRoot}/index.txt生成成功`);
        });
      });
    });
  },
);
