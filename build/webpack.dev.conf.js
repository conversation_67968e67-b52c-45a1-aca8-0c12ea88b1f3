'use strict';
const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const portfinder = require('portfinder');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const FriendlyErrorsPlugin = require('friendly-errors-webpack-plugin');
const ExtractHtmlPlugin = require('extract-html-plugin');
const CopyPlugin = require('copy-webpack-plugin');
const UnusedWebpackPlugin = require('unused-webpack-plugin');
const { codeInspectorPlugin } = require('code-inspector-plugin');
const crypto = require('crypto');
const env = require('../config/dev.env');

const {
  RouteMappingPlugin,
  PrefetchAsyncFnPlugin,
} = require('prefetch-preload');

// test
const utils = require('./utils');
const config = require('../config');
const baseWebpackConfig = require('./webpack.base.conf');

const HOST = process.env.HOST;
const PORT = process.env.PORT && Number(process.env.PORT);

const devWebpackConfig = merge(baseWebpackConfig, {
  stats: {
    assets: true,
  },
  mode: 'development',
  devtool: 'eval-cheap-module-source-map',
  // 缓存
  // 热加载
  watchOptions: {
    // https://webpack.docschina.org/configuration/watch/#watch
    // 当第一个文件更改，会在重新构建前增加延迟.避免不小心短时间内保存多次 编译多次,
    aggregateTimeout: 200,
    ignored: '**/node_modules',
  },
  target: 'web',
  devServer: {
    host: HOST || config.dev.host,
    port: PORT || config.dev.port,
    publicPath: config.dev.assetsPublicPath,
    proxy: config.dev.proxyTable,
    historyApiFallback: true,
    hot: true,
    https: true,
    open: false,
    overlay: { warnings: false, errors: true },
    quiet: true, // 打包页面变干净点.必须使用FriendlyErrorsPlugin
  },
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.dev.cssSourceMap,
      usePostCSS: true,
    }),
  },
  plugins: [
    new CopyPlugin([
      {
        from: path.resolve(
          __dirname,
          '../node_modules/@sdp/library-v2/vendor/static',
        ),
        to: './static',
      },
    ]),
    new webpack.DefinePlugin({
      'process.env.SECRET_KEY': JSON.stringify(env.SECRET_KEY),
    }),
    new webpack.HotModuleReplacementPlugin(),
    new HtmlWebpackPlugin(
      Object.assign(
        {
          filename: 'index.html',
          template: 'index.html',
          chunks: ['manifest', 'app', 'vendor'],
          inject: true,
        },
        ExtractHtmlPlugin({
          htmlPath: path.resolve(
            __dirname,
            '../node_modules/@sdp/library-v2/vendor/index.html',
          ),
        }),
      ),
    ),
    new RouteMappingPlugin({
      outputFile: 'route-mapping.[contenthash:8].js', // 输出的映射文件
    }),
    new PrefetchAsyncFnPlugin({
      outputPath: utils.prefetchAsyncFnDataPath,
      outputFile: 'prefetchAsyncFn.[contenthash:8].js',
    }),
    codeInspectorPlugin({
      bundler: 'webpack',
      escapeTags: [
        'style',
        'script',
        'template',
        'transition',
        'keepalive',
        'keep-alive',
        'component',
        'slot',
        'teleport',
        'transition-group',
        'transitiongroup',
        'suspense',
        'fragment',
      ],
      importClient: 'code',
      // ctrlKey、altKey、metaKey、shiftKey
      hotKeys: ['shiftKey'],
    }),
    // new UnusedWebpackPlugin({
    //   directories: [path.join(__dirname, '../src')]
    // })
  ],
});

// eslint-disable-next-line no-undef
module.exports = new Promise((resolve, reject) => {
  // 自动获取可用端口
  portfinder.basePort = process.env.PORT || config.dev.port;
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err);
    } else {
      process.env.PORT = port;
      const protocol = devWebpackConfig.devServer.https ? 'https' : 'http';
      devWebpackConfig.devServer.port = port;
      devWebpackConfig.plugins.push(
        new FriendlyErrorsPlugin({
          compilationSuccessInfo: {
            messages: [
              'App runing at: ',
              `- Local: ${protocol}://localhost:${port}`,
              `- Network: ${protocol}://${require('ip').address()}:${port}`,
            ],
          },
          onErrors: utils.createNotifierCallback(),
        }),
      );
      resolve(devWebpackConfig);
    }
  });
});
