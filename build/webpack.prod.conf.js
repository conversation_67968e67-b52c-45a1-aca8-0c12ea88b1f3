/* eslint-disable */
'use strict';
const path = require('path');
const webpack = require('webpack');
const merge = require('webpack-merge');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const ExtractHtmlPlugin = require('extract-html-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const SimpleProgressWebpackPlugin = require('simple-progress-webpack-plugin');
const AliOSSPlugin = require('webpack-alioss-plugin');
const SentryWebpackPlugin = require('@sentry/webpack-plugin');
const getOssConfig = require('@sdp/oss-config');
// prefetch --start
// const {
//   RouteMappingPlugin,
//   PrefetchAsyncFnPlugin,
// } = require('prefetch-preload');

// prefetch --end
const utils = require('./utils');
const config = require('../config');
const env = require('../config/prod.env');

const yargs = require('yargs');
// 可以用作区别测试环境base1010... 和 正式环境scm(上线环境)
const isFormalEnv = +yargs.argv.sentry === 1; // 是否是正式环境
const needBundle = +yargs.argv.bundle === 1;
const needUploadOSS = +yargs.argv.oss === 1;

const ossConfig = getOssConfig();
const SdpLibrary = ExtractHtmlPlugin({
  htmlPath: path.resolve(
    __dirname,
    '../node_modules/@sdp/library-v2/dist/index.html',
  ),
});
if (SdpLibrary.styleHtml) {
  SdpLibrary.styleHtml = SdpLibrary.styleHtml.map((html) => {
    const publicPath = `${config.build.assetsPublicPath}${config.build.assetsSubDirectory}`;
    return html.replace('/static/superadmin', publicPath);
  });
}
SdpLibrary.scriptHtml = SdpLibrary.scriptHtml.map((html) => {
  const publicPath = `${config.build.assetsPublicPath}${config.build.assetsSubDirectory}`;
  return html.replace('/static/superadmin', publicPath);
});

const baseWebpackConfig = require('./webpack.base.conf');
const webpackConfig = merge(baseWebpackConfig, {
  mode: 'production',
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.build.productionSourceMap,
      extract: true,
      usePostCSS: true,
    }),
  },
  devtool: config.build.productionSourceMap ? config.build.devtool : false,
  output: {
    path: config.build.assetsRoot,
    filename: utils.assetsPath('js/[name].[chunkhash:8].js'),
    chunkFilename: utils.assetsPath('js/[name].[chunkhash:8].js'),
    clean: true,
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 2 * 1024 * 1024,
      minChunks: 1,
    },
    runtimeChunk: { name: (entrypoint) => `runtime-${entrypoint.name}` },
    minimizer: [
      new CssMinimizerPlugin({
        parallel: true,
      }),
    ],
  },
  plugins: [
    new webpack.DefinePlugin({ 'process.env': env }),
    new CopyWebpackPlugin([
      {
        from: path.resolve(
          __dirname,
          '../node_modules/@sdp/library-v2/dist/static/superadmin',
        ),
        to: config.build.assetsSubDirectory,
        ignore: ['.*'],
      },
    ]),

    new MiniCssExtractPlugin({
      filename: '[name]/styles.[contenthash:7].css',
      chunkFilename: '[id]/styles.[contenthash:7].css',
    }),

    new HtmlWebpackPlugin(
      Object.assign(
        {
          filename: config.build.index,
          template: 'index.html',
          chunks: ['manifest', 'app', 'vendor'],
          minify: false,
          chunksSortMode: 'auto',
          inject: 'body',
        },
        SdpLibrary,
      ),
    ),
    // new RouteMappingPlugin({
    //   outputFile: 'route-mapping.[contenthash:8].js', // 输出的映射文件
    // }),
    // new PrefetchAsyncFnPlugin({
    //   outputPath: utils.prefetchAsyncFnDataPath,
    //   outputFile: 'prefetchAsyncFn.[contenthash:8].js',
    // }),
    new SimpleProgressWebpackPlugin(),
    new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn/),
  ],
});
// 是正式环境才加入
if (isFormalEnv) {
  webpackConfig.plugins.push(
    new SentryWebpackPlugin({
      // sentry-cli configuration
      authToken:
        '94b19080e0284d719a45880f040c23bf82f5b1c1b39c475f83aca92d45c1ebf4',
      org: 'shudongpo',
      project: 'base-front',
      release: process.env.npm_package_version,
      url: 'https://sentry.shudongpoo.com',
      // webpack specific configuration
      include: config.build.assetsRoot,
      urlPrefix: '~/static/',
      ignore: ['node_modules', 'webpack.config.js'],
    }),
  );
  webpackConfig.optimization.minimizer.push(
    new TerserPlugin({
      parallel: true,
      terserOptions: {
        // https://github.com/terser/terser#minify-options
        compress: {
          warnings: false, // 删除无用代码时是否给出警告
          drop_debugger: true, // 删除所有的debugger
          drop_console: true, // 删除所有的console.*
          pure_funcs: [''],
        },
      },
    }),
  );
}

if (config.build.productionGzip) {
  const CompressionWebpackPlugin = require('compression-webpack-plugin');
  webpackConfig.plugins.push(
    new CompressionWebpackPlugin({
      asset: '[path].gz[query]',
      algorithm: 'gzip',
      test: new RegExp(
        '\\.(' + config.build.productionGzipExtensions.join('|') + ')$',
      ),
      threshold: 10240,
      minRatio: 0.8,
    }),
  );
}

if (needUploadOSS) {
  webpackConfig.plugins.push(
    new AliOSSPlugin({
      auth: {
        accessKeyId: ossConfig.AccessKeyID, // 在阿里 OSS 控制台获取
        accessKeySecret: ossConfig.AccessKeySecret, // 在阿里 OSS 控制台获取
        region: 'oss-cn-beijing', // OSS 服务节点, 示例: oss-cn-hangzhou
        bucket: 'sdongpo-base-file', // OSS 存储空间, 在阿里 OSS 控制台获取
      },
      retry: 0,
      prefix: 'static',
      ossBaseDir: '',
      project: '', // 项目名(用于存放文件的直接目录)
      exclude: /.*\.php$|.*\.map$/,
      removeMode: false,
    }),
  );
}

// 打包分析
if (needBundle) {
  const BundleAnalyzerPlugin =
    require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
  webpackConfig.plugins.push(new BundleAnalyzerPlugin());
}
// 打包速度分析
// const smp = new SpeedMeasurePlugin();
// module.exports = smp.wrap(webpackConfig);

module.exports = webpackConfig;
